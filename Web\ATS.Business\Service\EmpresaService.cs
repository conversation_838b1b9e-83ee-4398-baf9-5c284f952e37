﻿using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Text.RegularExpressions;
using System.Web.Configuration;
using ATS.CrossCutting.Reports.Empresa.RelatorioListaEmpresa;
using ATS.Domain.DTO.Empresa;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class EmpresaService : ServiceBase, IEmpresaService
    {
        private readonly IEmpresaContaBancariaService _empresaContaBancariaService;
        private readonly IEmpresaRepository _repository;
        private readonly IEmpresaDapper _empresaDapper;
        private readonly IAutenticacaoAplicacaoRepository _autenticacaoAplicacaoRepository;
        private readonly IUsuarioService _usuarioService;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IEmpresaModuloService _empresaModuloService;

        public EmpresaService(IEmpresaRepository repository, IEmpresaContaBancariaService empresaContaBancariaService, IEmpresaDapper empresaDapper,
            IAutenticacaoAplicacaoRepository autenticacaoAplicacaoRepository, IUsuarioService usuarioService, IEmpresaModuloService empresaModuloService, IUsuarioRepository usuarioRepository)
        {
            _repository = repository;
            _empresaContaBancariaService = empresaContaBancariaService;
            _empresaDapper = empresaDapper;
            _autenticacaoAplicacaoRepository = autenticacaoAplicacaoRepository;
            _usuarioService = usuarioService;
            _empresaModuloService = empresaModuloService;
            _usuarioRepository = usuarioRepository;
        }

        public bool AtualizaUltimaExecucao(int idEmpresa)
        {
            return _empresaDapper.AtualizaUltimaExecucao(idEmpresa);
        }

        public Empresa GetAsNoTracking(int id)
        {
            return _repository.GetAsNoTracking(id);
        }

        

        public bool AnyById(int id)
        {
            return _repository.Where(c => c.IdEmpresa == id && c.Ativo).Any();
        }

        public bool EmpresaAgrupaProtocoloMesmoEvento(int id)
        {
            return _repository.All().Where(x => x.IdEmpresa == id).Select(c => c.AgrupaProtocoloMesmoEvento).FirstOrDefault();
        }

        public string GetLogoPorId(int idEmpresa)
        {
            byte[] logoBytes = _repository
               .FirstOrDefault(x => x.IdEmpresa == idEmpresa)?.Logo;

            if (logoBytes != null && logoBytes.Length > 0)
                return $"data:image/png;base64,{Convert.ToBase64String(logoBytes)}";

            return string.Empty;
        }

        public Empresa Get(int id, int? idUsuarioLogOn)
        {
            var validationResult = ValidarPermissaoUsuario(id, idUsuarioLogOn, EProcesso.Get);
            
            if (!validationResult.IsValid)
                throw new Exception(validationResult.ToString());

            return _repository.Get(id);
        }
        
        public Empresa Get(int id)
        {
            return _repository.Get(id);
        }
        
        public void CreateAuthenticationByCompany(Empresa empresa)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmssffff");
                
            _autenticacaoAplicacaoRepository.Add(new AutenticacaoAplicacao()
            {
                CNPJAplicacao = empresa.CNPJ,
                IdEmpresa = empresa.IdEmpresa,
                Ativo = true,
                Token = MD5Hash.Hash(timestamp)
            });
        }

        public ValidationResult Add(Empresa empresa)
        {
            try
            {
                FormatValues(empresa);
                var validationResult = IsValidToCrud(empresa, EProcesso.Create);
                if (!validationResult.IsValid)
                    return validationResult;

                var empresaTemp = _repository.Add(empresa);
                var contaBancaria = empresa.EmpresaContaBancaria;

                if (contaBancaria != null)
                {
                    contaBancaria.IdEmpresa = empresaTemp.IdEmpresa;
                    _empresaContaBancariaService.Inserir(contaBancaria);
                }

                CreateAuthenticationByCompany(empresa);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.ToString());
            }
        }

        public ValidationResult AddEmpresaIndicadores(EmpresaIndicadores empresaIndicadores)
        {
            try
            {
                _repository.AddEmpresaIndicadores(empresaIndicadores);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.ToString());
            }
        }

        public ValidationResult Update(Empresa empresa)
        {
            try
            {
                FormatValues(empresa);
                var validationResult = IsValidToCrud(empresa, EProcesso.Update);
                
                if (!validationResult.IsValid)
                    return validationResult;

                var contaBancaria = _empresaContaBancariaService.GetByEmpresaId(empresa.IdEmpresa);

                if (contaBancaria == null)
                {
                    contaBancaria = empresa.EmpresaContaBancaria;

                    if (contaBancaria != null)
                        _empresaContaBancariaService.Inserir(contaBancaria);
                }
                else
                {
                    contaBancaria = empresa.EmpresaContaBancaria ?? contaBancaria;
                    _empresaContaBancariaService.Update(contaBancaria);
                }

                empresa.DataAtualizacao = DateTime.Now;

                _repository.Update(empresa);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.ToString());
            }
        }

        public ValidationResult UpdateEmpresaIndicadores(EmpresaIndicadores empresaIndicadores)
        {
            try
            {
                _repository.UpdateEmpresaIndicadores(empresaIndicadores);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.ToString());
            }
        }

        public IQueryable<Empresa> Consultar(string razaoSocial, int? idUsuarioLogOn)
        {
            if (!idUsuarioLogOn.HasValue)
                return null;

            var usuarioLogOn = _usuarioRepository.GetWithRelationships((int)idUsuarioLogOn);
            var tipoContrato = _usuarioService.GetTipoContrato((int)idUsuarioLogOn, usuarioLogOn?.IdEmpresa);

            var perfil = _usuarioRepository.GetPerfil(idUsuarioLogOn.Value);
            
            if (perfil != EPerfil.Administrador && perfil != EPerfil.Empresa && tipoContrato != ETipoContrato.Terceiro)
                return null;

            var retEmpresas =
                _repository.Find(c => razaoSocial == null || c.RazaoSocial.Contains(razaoSocial));

            switch (perfil)
            {
                case EPerfil.Empresa:
                    if (usuarioLogOn?.IdEmpresa != null)
                        retEmpresas = retEmpresas.Where(t => t.IdEmpresa == usuarioLogOn.IdEmpresa.Value);
                    break;
            }

            return retEmpresas;
        }
        
        public List<ConsultaTodasEmpresasDto> ConsultarTodas(bool? ativo = null)
        {
            return _repository.ConsultarTodas(ativo);
        }

        public Empresa GetWithChilds(int id)
        {
            return _repository
                .Find(x => x.IdEmpresa == id && x.Ativo)
                .Include(x => x.Cidade)
                .Include(x => x.Estado)
                .Include(x => x.Layout)
                .Include(x => x.Modulos)
                .Include(x => x.Autorizacoes)
                .FirstOrDefault();
        }

        public IQueryable<Empresa> All()
        {
            return _repository.Find(x => x.Ativo);
        }

        public string GetLogoPorCnpj(string cnpjEmpresa)
        {
            var logoBytes = _repository.FirstOrDefault(x => x.CNPJ == cnpjEmpresa)?.Logo;

            if (logoBytes != null && logoBytes.Length > 0)
                return $"data:image/png;base64,{Convert.ToBase64String(logoBytes)}";

            return string.Empty;
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, List<int> idsEstabelecimento, int? empresaId)
        {
            var empresas = _repository.GetAll();

            empresas = string.IsNullOrWhiteSpace(order?.Campo)
                    ? empresas.OrderBy(x => x.IdEmpresa)
                    : empresas.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            if (idsEstabelecimento != null && idsEstabelecimento.Any())
            {
                empresas = empresas.Where(
                    x => x.Credenciamentos.Any(y => idsEstabelecimento.Contains(y.IdEstabelecimentoBase.Value) && y.Status == EStatusCredenciamento.Aprovado));
            }

            if (empresaId != null)
                empresas = empresas.Where(c => c.IdEmpresa == empresaId);

            empresas = empresas.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = empresas.Count(),
                items = empresas.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdEmpresa,
                    x.RazaoSocial,
                    CNPJ = x.CNPJ.ToCNPJFormato(),
                    x.Ativo
                })
            };
        }

        public object GetParametrosPorCnpj(string sCnpjEmpresa)
        {
            return _repository
                .Find(x => x.CNPJ == sCnpjEmpresa)
                .Select(x => new
                {
                    Geral = new
                    {
                        x.Telefone,
                        x.Ativo,
                        x.EmailSugestoes
                    },
                    OfertaDeCargas = new
                    {
                        x.RaioCooperado,
                        x.RaioTerceiro,
                        x.AlertaArea,
                        x.ObrigarValorTerceiro,
                        x.PrioridadeCooperadoCargas,
                        x.RoteirizarCarga,
                        x.PeriodoExpiracaoChat,
                        x.MinutosPrioridade,
                        EntradaRaio = x.DistanciaEntradaRaio,
                        SaidaRaio = x.DistanciaSaidaRaio,
                        x.DistanciaRaioGr
                    },
                    MonitoramentoTemperatura = new
                    {
                        x.ObrigarNumeroFrota,
                        x.TemperaturaInicial,
                        x.TemperaturaFinal
                    },
                    MonitoramentoVelocidade = new
                    {
                        x.AvisoSonoroForte,
                        x.AvisoSonoroFraco,
                        x.AvisoSonoroModerado
                    },
                    GestaoEntregas = new
                    {
                        x.ValidaRaio,
                        x.RaioEntrega,
                        x.PeriodoIniEnvCheckInCooperado,
                        x.PeriodoFimEnvCheckInCooperado,
                        x.PeriodoIniEnvCheckInTerceiro,
                        x.PeriodoFimEnvCheckInTerceiro
                    },
                    GestaoLogistica = new
                    {
                        x.TempoExecucaoServico,
                        x.TempoParada
                    },
                    CheckList = new
                    {
                        x.EmailsCheckList
                    },
                    Cartao = new
                    {
                        IdProdutoCartaoFretePadrao = x.IdProdutoCartaoFretePadrao ?? SistemaInfoConsts.ProdutoFretePadraoAdministradora
                    },
                    x.TokenMicroServices
                });
        }

        public int? GetDiasExpiracaoCompraPedagio(int idEmpresa)
        {
            var parametro = _repository.Find(o => o.IdEmpresa == idEmpresa)
                .Select(o => o.DiasExpiracaoSaldoPedagio).FirstOrDefault();

            return parametro;
        }

        public int? GetIdProdutoCartaoFretePadrao(int idEmpresa)
        {
            return _repository.GetIdProdutoCartaoFretePadrao(idEmpresa);
        }

        public int? GetIdByCnpj(string cnpjEmpresa)
        {
            return _repository
                .Where(x => x.CNPJ == cnpjEmpresa)
                .Select(c => c.IdEmpresa)
                .FirstOrDefault();
        }

        public string GetCnpjById(int empresaId)
        {
            return _repository
                .Where(x => x.IdEmpresa == empresaId)
                .Select(c => c.CNPJ)
                .FirstOrDefault();
        }

        public bool BloqueiaCancelamentoAbastecimento(int empresaId)
        {
            return _repository
                .Where(x => x.IdEmpresa == empresaId)
                .Select(c => c.Bloqueiacancelamentoabastecimentofalha)
                .FirstOrDefault();
        }

        public Empresa GetByCnpj(string cnpjEmpresa)
        {
            return _repository
                .Find(x => x.CNPJ == cnpjEmpresa)
                .FirstOrDefault();
        }

        public ValidationResult AlterarStatus(int idEmpresa)
        {
            try
            {
                var repository = _repository;
                var empresa = repository.FirstOrDefault(o => o.IdEmpresa == idEmpresa);

                if (empresa == null)
                    return new ValidationResult().Add("Empresa não encontrada.");

                empresa.Ativo = !empresa.Ativo;
                repository.Update(empresa);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object ConsultarGridEmpresa(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var empresas = GetDataToGridAndReport(idEmpresa, orderFilters, filters);

            return new
            {
                totalItems = empresas.Count(),
                items =
                    empresas.Skip((page - 1) * take)
                        .Take(take)
                        .ToList()
                        .Select(
                            o =>
                                new
                                {
                                    o.IdEmpresa,
                                    CNPJ = o.CNPJ.ToCNPJFormato(),
                                    o.RazaoSocial,
                                    Telefone = o.Telefone?.ToTelefoneFormato(),
                                    o.Ativo
                                })
            };
        }

        public byte[] GerarRelatorioGridEmpresas(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao)
        {
            var listaEmpresas = new List<RelatorioEmpresaDataType>();
            var empresas = GetDataToGridAndReport(idEmpresa, orderFilters, filters);

            foreach (var empresa in empresas)
            {
                listaEmpresas.Add(new RelatorioEmpresaDataType
                {
                    IdEmpresa = empresa.IdEmpresa.ToString(),
                    Celular = empresa.Celular?.ToTelefoneFormato(),
                    Cnpj = empresa.CNPJ?.ToCNPJFormato(),
                    Email = empresa.Email,
                    NomeFantasia = empresa.NomeFantasia,
                    RazaoSocial = empresa.RazaoSocial,
                    Telefone = empresa.Telefone?.ToTelefoneFormato(),
                    Endereco = FormatarEnderecoEmpresa(empresa)
                });
            }

            return new RelatorioEmpresa().GetReport(listaEmpresas, extensao, logo);
        }

        public TarifasMeioHomologadoModel GetTarifasMeioHomologado(string cnpj)
        {
            // 1 - Busca configuração da empresa
            // 2 - Se não houver, retorna o default da instancia do ATS em execução (SOTRAN ou Extratta)
            var rep = _repository;
            var result = rep.GetTarifasMeioHomologado(cnpj);

            // Buscar padrão da instância de meio homologado
            if (result.QuantidadeTotal == 0)
                result.QuantidadeTotal =
                    WebConfigurationManager.AppSettings["MH.Tarifas.QuantidadeTotal"].ToIntNullable() ?? 0;

            if (result.ValorTotal == 0)
                result.ValorTotal =
                    WebConfigurationManager.AppSettings["MH.Tarifas.ValorTotal"].ToDecimalSafe() ?? 0;

            //
            if (result.QuantidadeTotal == 0)
                result.QuantidadeTotal = 8;

            if (result.ValorTotal == 0)
                result.ValorTotal = 78.40m;

            return result;
        }

        public bool ValidaChaveBaixaEvento(int idEmpresa)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa)
                .Select(x => x.ValidaChaveBaixaEvento).FirstOrDefault();
        }

        public bool ValidaChaveMhBaixaEvento(int idEmpresa)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa)
                .Select(x => x.ValidaChaveMHBaixaEvento).FirstOrDefault();
        }
        
        public bool AnyRntrc(int idEmpresa)
        {
            return _repository.AnyRntrc(idEmpresa);
        }
        
        private ValidationResult IsValidToCrud(Empresa empresa, EProcesso processo)
        {
            var validationResult = new ValidationResult();

            validationResult.Add(AssertionConcern.AssertArgumentIsValidCNPJ(empresa.CNPJ, @"CNPJ inválido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidCEP(empresa.CEP, @"CEP deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(empresa.Email, @"E-mail deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidTelefone(empresa.Telefone, @"Telefone deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidLatitude(empresa.Latitude, @"Latitude deve possuir um valor válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidLongitude(empresa.Longitude, @"Longitude deve possuir um valor válido"));

            if (!string.IsNullOrWhiteSpace(empresa.Celular))
                validationResult.Add(AssertionConcern.AssertArgumentIsValidTelefone(empresa.Celular.OnlyNumbers(), @"Celular deve ser válido"));

            /*if (empresa.CriterioRanking1 == empresa.CriterioRanking2)
                validationResult.Add("Critério de Ranking Secundário deve ser diferente do Primário.");*/

            if (empresa.TemperaturaInicial.HasValue && empresa.TemperaturaFinal.HasValue
                && empresa.TemperaturaInicial > empresa.TemperaturaFinal)
                validationResult.Add("Temperatura inicial deve ser menor que temperatura final.");

            if ((empresa.ValidaRaio == EValidaRaio.Bloquear || empresa.ValidaRaio == EValidaRaio.Notificar) &&
                (!empresa.RaioEntrega.HasValue || empresa.RaioEntrega <= 0))
                validationResult.Add("É necessário informar um raio de entrega quando selecionado a validação de raio de entrega como Bloquear ou Notificar.");

            if (empresa.IntervaloConsultaFretesConcorrente < 0)
                validationResult.Add("O intervalo de dias para consulta de fretes da concorrência não pode ser negativo.");

            if (!string.IsNullOrWhiteSpace(empresa.EmailsCheckList))
            {
                var emails = empresa.EmailsCheckList.Split(';');
                foreach (var email in emails)
                {
                    if (string.IsNullOrWhiteSpace(email)) continue;
                    if (Regex.IsMatch(email, "[,;]"))
                    {
                        validationResult.Add("Deve-se usar \";\" como separador dos e-mails. ");
                        break;
                    }
                    var validationError = AssertionConcern.AssertArgumentIsValidEmail(email,
                        @"Endereços de e-mails do checklist devem ser válidos");
                    if (validationError == null) continue;
                    validationResult.Add(validationError);
                    break;
                }
            }

            #region Validações de Email

            if (!string.IsNullOrWhiteSpace(empresa.EmailNome) || !string.IsNullOrWhiteSpace(empresa.EmailEndereco) ||
                empresa.EmailPorta != null || !string.IsNullOrWhiteSpace(empresa.EmailServidor) ||
                !string.IsNullOrWhiteSpace(empresa.EmailUsuario) || !string.IsNullOrWhiteSpace(empresa.EmailSenha))
            {
                if (string.IsNullOrWhiteSpace(empresa.EmailNome))
                    validationResult.Add("Contato do e-mail não informado");

                if (string.IsNullOrWhiteSpace(empresa.EmailEndereco))
                    validationResult.Add("Endereço de e-mail não informado");

                validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(empresa.EmailEndereco, @"Endereço de e-mail deve ser válido"));

                if (empresa.EmailPorta == null)
                    validationResult.Add("Porta do e-mail não informado");

                if (string.IsNullOrWhiteSpace(empresa.EmailServidor))
                    validationResult.Add("Servidor de e-mail não informado");

                if (string.IsNullOrWhiteSpace(empresa.EmailUsuario))
                    validationResult.Add("E-mail do usuário não informado");

                validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(empresa.EmailUsuario, @"Endereço de e-mail do usuário deve ser válido"));

                if (string.IsNullOrWhiteSpace(empresa.EmailSenha))
                    validationResult.Add("Senha do usuário não informado");
            }

            #endregion

            #region CNPJ

            var cCnpj = empresa.CNPJ.OnlyNumbers();
            var numCnpj = _repository.Find(t => t.CNPJ == cCnpj).Count();
            
            if (processo == EProcesso.Create && numCnpj > 0)
                validationResult.Add("CNPJ informado já cadastrado para outra empresa!");

            if (processo == EProcesso.Update && cCnpj != _repository.GetCnpj(empresa.IdEmpresa))
                validationResult.Add("CNPJ não pode ser alterado!");

            #endregion

            #region RNTRC
            
            if (empresa.CNTRC > 0 && processo == EProcesso.Create && HasCompanyByRntrc(empresa.CNTRC.Value, null))
            {
                validationResult.Add("Existe outra empresa cadastrada com o mesmo RNTRC!");
            }
            else if (empresa.CNTRC > 0 && processo == EProcesso.Update && HasCompanyByRntrc(empresa.CNTRC.Value, empresa.IdEmpresa))
            {
                validationResult.Add("Existe outra empresa cadastrada com o mesmo RNTRC!");

            }
            #endregion

            #region email
            if (empresa.Email != null && processo == EProcesso.Create && HasCompanyByMail(empresa.Email, null))
            {
                validationResult.Add("E-mail de contato informado está sendo utilizado por outra empresa!");

            }
            else if (empresa.Email != null && processo == EProcesso.Update && HasCompanyByMail(empresa.Email, empresa.IdEmpresa))
            {
                validationResult.Add("E-mail de contato informado está sendo utilizado por outra empresa!");

            }
            #endregion

            #region Modulos

            validationResult.AddRange(_empresaModuloService.IsValid(empresa));

            #endregion

            return validationResult;
        }
        
        private bool HasCompanyByRntrc(int rntrc, int? idempresa)
        {
            var repository = _repository;
            var query = repository.Where(x => x.CNTRC == rntrc);

            if (idempresa != null)
                query = query.Where(x => x.IdEmpresa != idempresa);

            return query.Any();
        }
        
        private bool HasCompanyByMail(string email, int? idempresa)
        {
            var repository = _repository;
            var query = repository.Where(x => x.Email == email);

            if (idempresa != null)
                query = query.Where(x => x.IdEmpresa != idempresa);

            return query.Any();
        }
        
        private static void FormatValues(Empresa empresa)
        {
            empresa.CNPJ = empresa.CNPJ.OnlyNumbers();
            empresa.Telefone = empresa.Telefone.OnlyNumbers();
            empresa.CEP = empresa.CEP.OnlyNumbers();
            
            if (!string.IsNullOrEmpty(empresa.Celular))
                empresa.Celular = empresa.Celular.OnlyNumbers();
        }
        
        private ValidationResult ValidarPermissaoUsuario(int? id, int? idUsuarioLogOn, EProcesso processo)
        {
            if (!idUsuarioLogOn.HasValue)
                return new ValidationResult();

            var perfil = _usuarioRepository.GetPerfil(idUsuarioLogOn.Value);
            
            if (perfil != EPerfil.Administrador && perfil != EPerfil.Empresa)
                return new ValidationResult().Add("Perfil do usuário logado não permite manipulação destas informações.");

            if (processo != EProcesso.Create)
            {
                if (perfil == EPerfil.Empresa && id != _usuarioRepository.GetIdEmpresa(idUsuarioLogOn.Value))
                    return new ValidationResult().Add("Perfil do usuário logado não permite manipular o registro desejado.");
            }

            return new ValidationResult();
        }
        
        private static string FormatarEnderecoEmpresa(Empresa empresa)
        {
            var endereco = string.Empty;

            endereco += $"{empresa.Endereco}";

            if (empresa.Numero.HasValue)
                endereco += $", Nº {empresa.Numero}";

            if (!string.IsNullOrEmpty(empresa.Complemento))
                endereco += $", {empresa.Complemento}";

            if (!string.IsNullOrEmpty(empresa.Bairro))
                endereco += $", {empresa.Bairro}";

            if (!string.IsNullOrEmpty(empresa.Cidade?.Nome))
                endereco += $", {empresa.Cidade?.Nome}";

            if (!string.IsNullOrEmpty(empresa.Estado?.Sigla))
                endereco += $" - {empresa.Estado?.Sigla}";

            if (!string.IsNullOrEmpty(empresa.CEP))
                endereco += $", {empresa.CEP.ToCEPFormato()}";

            return endereco;
        }
        
        private IQueryable<Empresa> GetDataToGridAndReport(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var empresas = _repository.GetAll();

            if (idEmpresa.HasValue)
                empresas = empresas.Where(o => o.IdEmpresa == idEmpresa);

            empresas = empresas.AplicarFiltrosDinamicos(filters);

            empresas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? empresas.OrderByDescending(o => o.IdEmpresa)
                : empresas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            return empresas;
        }
        
        public object ConsultarGridEmpresasHubPedagio(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var empresas = _repository.GetAll();

            empresas = string.IsNullOrWhiteSpace(order?.Campo)
                ? empresas.OrderBy(x => x.IdEmpresa)
                : empresas.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            empresas = empresas.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = empresas.Count(),
                items = empresas.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdEmpresa,
                        x.RazaoSocial,
                        CNPJ = x.CNPJ.ToCNPJFormato(),
                        x.Ativo
                    })
            };
        }
    }
}

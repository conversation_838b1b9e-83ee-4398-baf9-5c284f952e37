﻿using ATS.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class UsuarioContato : ContatoBase
    {
        /// <summary>
        /// Código do usuário
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Código do contato
        /// </summary>
        public int IdContato { get; set; }

        [SkipTracking]
        public new string Email { get; set; }

        #region Referência

        public virtual Usuario Usuario { get; set; }

        #endregion
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IEspecieService : IService<Especie>
    {
        /// <summary>
        /// Buscar espécie
        /// </summary>
        /// <param name="id"><PERSON>ódigo da espécie</param>
        /// <returns>Objeto Especie</returns>
        Especie Get(int id);

        /// <summary>
        /// Adicionar a espécie a base de dados
        /// </summary>
        /// <param name="especie">Dados da espécie</param>
        /// <returns></returns>
        ValidationResult Add(Especie especie);

        Especie AddReturningObject(Especie especie);

        /// <summary>
        /// Atualizar o registro da espécie
        /// </summary>
        /// <param name="especie">Dad<PERSON> da espécie</param>
        /// <returns></returns>
        ValidationResult Update(Especie especie);

        Especie UpdateReturningObject(Especie especie);

        /// <summary>
        /// Retorna as espécies a partir dos dados de filtro
        /// </summary>
        /// <param name="descricao">Descrição da espécie</param>
        /// <returns>IQueryable de Especie</returns>
        IQueryable<Especie> Consultar(string descricao);

        /// <summary>
        /// Inativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser desativado</param>
        /// <returns></returns>
        ValidationResult Inativar(int idEspecie);

        /// <summary>
        /// Reativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser reativado</param>
        /// <returns></returns>
        ValidationResult Reativar(int idEspecie);

        /// <summary>
        /// Retorna todas as especies ativas
        /// </summary>
        /// <returns>IQueryable de Especie</returns>
        IQueryable<Especie> All();

        /// <summary>
        ///
        /// </summary>
        /// <param name="dataBase"></param>
        /// <returns></returns>
        List<Especie> GetEspeciesAtualizadas(DateTime dataBase);

        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        Especie GetPorDescricao(string especie);
        ValidationResult AlterarStatus(int idEspecie);
    }
}
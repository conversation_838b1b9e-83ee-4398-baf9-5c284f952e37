﻿using System;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ICteService : IService<Cte>
    {
        IQueryable<Cte> ConsultaCte(string cpfMotorista, DateTime dataInicial, DateTime? dataFinal);
        ValidationResult Update(Cte cte);
        ValidationResult Add(Cte cte);
        Cte ConsultaPorId(int idCte);
        int GetTotalCtes(string cPFCNPJUsuario);
    }
}

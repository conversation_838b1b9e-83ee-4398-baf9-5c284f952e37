﻿using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Viagem;
using ATS.WS.Models.Webservice.Request;
using ATS.WS.Services;
using ATS.WS.Services.ViagemServices;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class ViagemController : BaseApiController<IViagemApp>
    {
        private readonly SrvCargaAvulsa _srvCargaAvulsa;
        private readonly SrvViagem _srvViagem;
        private readonly BaixaEventoViagem _baixaEventoViagem;
        private readonly IntegracaoEventoViagem _integracaoEventoViagem;
        private readonly BloqueioEventoViagem _bloqueioEventoViagem;
        private readonly StatusEventoViagem _statusEventoViagem;
        private readonly PedagioViagem _pedagioViagem;
        private readonly ConsultasViagem _consultasViagem;
        private readonly ValoresViagem _valoresViagem;
        private readonly IntegracaoCheckViagem _integracaoCheckViagem;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvIP _srvIP;

        public ViagemController(BaseControllerArgs baseArgs, IViagemApp app, SrvCargaAvulsa srvCargaAvulsa, SrvViagem srvViagem,
            BaixaEventoViagem baixaEventoViagem, IntegracaoEventoViagem integracaoEventoViagem, BloqueioEventoViagem bloqueioEventoViagem, StatusEventoViagem statusEventoViagem,
            PedagioViagem pedagioViagem, ConsultasViagem consultasViagem, ValoresViagem valoresViagem, IntegracaoCheckViagem integracaoCheckViagem, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvIP srvIP) : base(baseArgs, app)
        {
            _srvCargaAvulsa = srvCargaAvulsa;
            _srvViagem = srvViagem;
            _baixaEventoViagem = baixaEventoViagem;
            _integracaoEventoViagem = integracaoEventoViagem;
            _bloqueioEventoViagem = bloqueioEventoViagem;
            _statusEventoViagem = statusEventoViagem;
            _pedagioViagem = pedagioViagem;
            _consultasViagem = consultasViagem;
            _valoresViagem = valoresViagem;
            _integracaoCheckViagem = integracaoCheckViagem;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvIP = srvIP;
        }

        /// <summary>
        /// Integrar a viagem
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Integrar(ViagemIntegrarRequestModel @params)
        {
            try
            {
                var ip = this.GetRealIp();

                if  (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(@params.CNPJEmpresa) ? @params.CNPJAplicacao : @params.CNPJEmpresa, ip))
                    return NaoAutorizado();

                var retorno = @params.IdViagem.HasValue
                    ? _srvViagem.Alterar(@params, true)
                    : _srvViagem.Integrar(@params, true);

                return Responde(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarEventos(ConsultaEventoAbonoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                var retorno = App.GetEventosViagem(@params.CNPJCPFProprietario, @params.CPFMotorista, @params.StatusEvento, @params.TipoEvento, @params.DataInicialViagem,
                    @params.DataFinalViagem);

                return Responde(new Retorno<object>(true, retorno));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(new Retorno<object>(false, e.Message, null));
            }
        }

        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarDetalhesEvento(ConsultarEventoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                var retorno =
                    App.ConsultarDetalheEvento(@params);

                return Responde(new Retorno<object>(true, retorno));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(new Retorno<object>(false, e.Message, null));
            }
        }

        /// <summary>
        ///     Consulta o custo de pedágios de uma rota
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarCustoPedagioRota(CustoPedagioRotaRequestModel @params)
        {
            try
            {
                var ip = this.GetRealIp();

                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(@params.CNPJEmpresa) ? @params.CNPJAplicacao : @params.CNPJEmpresa, ip))
                    return NaoAutorizado();

                //var result = Responde(new SrvViagem(App, _clienteApp, _parametrosApp, _ciotV2App, _ciotV3App, _proprietarioApp).ConsultarCustoPedagioRota(@params));
                var result = Responde(_pedagioViagem.ConsultarCustoPedagioRota(@params));
                return result;
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult BaixarViagem(string token, string cnpjAplicacao, string documentoUsuarioAudit, string nomeUsuarioAudit, int codigoViagem = 0, DateTime? dataEvento = null)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                if (codigoViagem < 1) throw new Exception("Código da viagem é obrigatório. ");

                // 5 = baixada
                return Responde(
                    _statusEventoViagem
                        .AlterarStatus(codigoViagem, EStatusViagem.Baixada, cnpjAplicacao, token, documentoUsuarioAudit, nomeUsuarioAudit, dataEvento));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult CancelarViagem(string token, string cnpjAplicacao, int codigoViagem = 0, DateTime? dataEvento = null,
            string documentoUsuarioAudit = null, string nomeUsuarioAudit = null,
            ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado comportamentoEstornoPedagioMoedeiro =
                ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                if (codigoViagem < 1) throw new Exception("Código da viagem é obrigatório. ");

                // 3 = Cancelada
                return Responde(
                    _statusEventoViagem.AlterarStatus(codigoViagem, EStatusViagem.Cancelada, cnpjAplicacao, token,
                        documentoUsuarioAudit, nomeUsuarioAudit, dataEvento));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        ///     Integrar o preenchimento da Check
        /// </summary>
        /// <param name="params">Parâmetros da check</param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string IntegrarCheck(ViagemCheckRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new Models.Mobile.Common.JsonResult().TokenInvalido();

                return new Models.Mobile.Common.JsonResult().Responde(_integracaoCheckViagem.IntegrarCheck(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new Models.Mobile.Common.JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        ///     Consultar viagem
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Consultar(ConsultarViagemRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                if (@params.DataLancamentoInicial.HasValue)
                    @params.DataLancamentoInicial = new DateTimeHelper().StartOfDay(@params.DataLancamentoInicial.Value);

                if (@params.DataLancamentoFinal.HasValue)
                    @params.DataLancamentoFinal = new DateTimeHelper().EndOfDay(@params.DataLancamentoFinal.Value);

                return BigJson(_consultasViagem.Consultar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult BaixarEvento(BaixarEventoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return Responde(BaixarEventoResponseModelErro("Token inválido", @params));

                var erroCamposChamada = @params.Valida();
                if (!string.IsNullOrWhiteSpace(erroCamposChamada))
                    return Responde(BaixarEventoResponseModelErro(erroCamposChamada, @params));

                var retorno = _baixaEventoViagem.BaixarEvento(@params, true);

                return Responde(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(BaixarEventoResponseModelErro($"Ocorreu um erro durante o processamento: {e.Message}", @params));
            }
        }

        private BaixarEventoResponseModel BaixarEventoResponseModelErro(string mensagem, BaixarEventoRequestModel @params)
        {
            return new BaixarEventoResponseModel
            {
                Sucesso = false,
                Mensagem = mensagem,
                IdViagem = @params.IdViagem ?? -1,
                NumeroControle = @params.NumeroControleViagem,
                IdViagemEvento = @params.IdViagemEvento ?? -1,
                NumeroControleEvento = @params.NumeroControleEvento,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = ERetornoOperacaoCartao.Erro,
                    Mensagem = string.Empty
                },
                Token = string.Empty
            };
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AlterarValoresViagem(AlterarValoresViagemRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                var retorno = _valoresViagem
                    .AlterarValoresViagem(@params, true); //new SrvViagem(App, _clienteApp, _parametrosApp, _ciotV2App, _ciotV3App, _proprietarioApp).AlterarValoresViagem(@params);
                return Responde(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AdicionarEventosViagem(AdicionarEventosViagemRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                var retorno = _integracaoEventoViagem.AdicionarEventosViagem(@params, true);
                return Responde(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AlterarStatusParcelas(AlterarStatusEventosViagemRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                var retorno =
                    _statusEventoViagem.AlterarStatusEventos(@params, true);
                return Responde(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.GetBaseException().Message);
            }
        }

        /// <summary>
        /// Obsolete: Usar carga CargaAvulsa/Carregar
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult CargaAvulsa(CargaAvulsaRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                return Responde(_srvCargaAvulsa.Add(request, this.GetRealIp()));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult BaixarEventoSaldo(BaixarEventoSaldoRequest request)
        {
            if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) &&
                !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                return TokenInvalido();

            return Json(_baixaEventoViagem.BaixarEventoSaldo(request));
        }

        /// <summary>
        /// Criado método WS para substituir o serviço do windows BloqueioPagamento
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult BloqueioEventosExpirados()
        {
            try
            { 
                //nao tem validacao de token ???????????
                BloqueioEventosExpiradosResponseDTO eventosBloqueados;
                var validation =
                    _bloqueioEventoViagem
                        .BloqueioEventosExpirados(out eventosBloqueados);

                return Responde(new
                {
                    success = validation.IsValid,
                    message = validation.ToString(),
                    data = eventosBloqueados
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarTiposCarga(string token, string cnpjAplicacao, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                var resposta = this.App.ConsultarTiposCarga(cnpjAplicacao);
                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Remove carretas da viagem e retifica o ciot caso necessário
        /// </summary>
        /// <param name="request">Request base, idviagem e lista de carretas para serem removidas</param>
        /// <returns>Carretas que ainda existem na viagem</returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult RemoverCarretas(ViagemRemoverCarretasRequest request)
        {
            try
            {
                //nao tem validacao de token
                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new Retorno<ViagemIntegrarResponseModel>(validacaoChamada.ToString()));

                return Responde(_srvViagem
                    .RemoverCarretas(request));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult ConsultarFilialEDadosPagamento(string ciot, string token)
        {
            try
            {
                //nao tem validacao de token ???????????
                var filialEDadosPagamento = App.ConsultarFilialEDadosPagamento(ciot);

                return BigJson(filialEDadosPagamento.Conteudo);
            }
            catch (Exception e)
            {
                return Responde(e);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao()]
        public FileResult ReciboPEF(int idViagem, string token, string cnpjAplicacao, string cnpjEmpresa, bool ListarParcelasCanceladas = true)
        {
            var ip = this.GetRealIp();
            if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, ip))
                throw new Exception("Integração não autorizada, entre em contato com o suporte.");

            if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                throw new Exception("Token inválido");

            var dadosRecibo = App.GetDadosReciboPef(idViagem, ListarParcelasCanceladas, string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa);

            if (dadosRecibo == null)
                throw new Exception("Não foram encontrados dados do recibo de pagamento");

            var impressaoComprovante = App.GetDadosReciboPefReport(dadosRecibo);

            return File(impressaoComprovante, ConstantesUtils.PdfMimeType, "Recibo de pagamento.pdf");
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao()]
        public FileResult ReciboVPO(int id, string token, string cnpjAplicacao, string cnpjEmpresa)
        {
            var ip = this.GetRealIp();
            if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, ip))
                throw new Exception("Integração não autorizada, entre em contato com o suporte.");

            if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                throw new Exception("Token inválido");

            ViagemDadosValePedagio dadosAtsValePedagio;
            var dadosComprovante = App.GetDadosValePedagio(id, out dadosAtsValePedagio, string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa);

            if (dadosAtsValePedagio == null)
                throw new Exception("Dados nao encontrado");

            if (dadosComprovante?.Status == ComprovanteValePedagioResponseStatus.Falha)
                throw new Exception("Recibo VPO com status erro");

            var impressaoComprovante = !string.IsNullOrWhiteSpace(dadosAtsValePedagio.ProtocoloEnvioValePedagio)
                ? App.GetDadosValePedagioReport(dadosComprovante, dadosAtsValePedagio.Fornecedor)
                : App.ConsultarReciboMoedeiro(id, dadosAtsValePedagio.IdEmpresa);

            return File(impressaoComprovante, ConstantesUtils.PdfMimeType, $"Comprovante de compra de pedágio.pdf");
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao()]
        public JsonResult ReciboPedago(int idViagem, string token, string cnpjAplicacao, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    throw new Exception("Token inválido");

                var dadosComprovante = App.ConsultarReciboPedagio(idViagem, string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa);

                var objetoRetorno = new PedagioReciboResponse
                {
                    Sucesso = true,
                    Mensagem = string.Empty,
                    Objeto = dadosComprovante
                };

                return Responde(objetoRetorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);

                var objetoRetorno = new PedagioReciboResponse
                {
                    Sucesso = false,
                    Mensagem = e.Message
                };

                return Responde(objetoRetorno);
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CheckInMap : EntityTypeConfiguration<CheckIn>
    {
        public CheckInMap()
        {
            ToTable("CHECKIN");

            HasKey(t => t.IdCheckIn);

            Property(t => t.IdCheckIn)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdUsuario)
                .IsOptional();

            Property(t => t.IdMotorista)
                .IsOptional();

            Property(t => t.IdViagem)
                .IsOptional();

            Property(t => t.DataHora)
                .IsRequired();
            
            Property(t => t.TipoEvento)
                .IsRequired();

            Property(t => t.Longitude)
                .IsRequired();

            Property(t => t.Latitude)
                .IsRequired();

            HasOptional(t => t.Usuario)
                .WithMany(t => t.CheckIns)
                .HasForeignKey(d => d.IdUsuario);

            HasOptional(t => t.Motorista)
              .WithMany(t => t.CheckIns)
              .HasForeignKey(d => d.IdMotorista);

            HasOptional(t => t.Viagem)
             .WithMany(t => t.CheckIns)
             .HasForeignKey(t => new { t.IdViagem, IdEmpresa = t.IdEmpresa });

            HasOptional(t => t.Cidade)
             .WithMany(t => t.Checkins)
             .HasForeignKey(t => t.IdCidadeCheckIn);
        }
    }
}
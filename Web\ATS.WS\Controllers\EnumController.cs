﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class EnumController: BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public EnumController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        /// <summary>
        /// Retorna as filiais cadastradas/atualizadas a partir da data base de filtro
        /// </summary>
        /// <param name="token"></param>
        /// <param name="cnpjAplicacao"></param>
        /// <param name="enumName"></param>
        /// <returns></returns>
        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(string token, string cnpjAplicacao, string enumName)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(new Retorno<object>(true, string.Empty,
                    new EnumApp().Consultar(enumName)));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
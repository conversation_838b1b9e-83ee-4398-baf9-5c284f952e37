using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Domain.Enum
{
    public enum EOrigemConsumoServicoExterno
    {
        [EnumMember, Description("Posicionamento motorista")]
        PosicionamentoMotorista = 1,
        
        [EnumMember, Description("Integrar estabelecimento")]
        IntegrarEstabelecimento = 2,
        
        [EnumMember, Description("Editar estabelecimento")]
        EditarEstabelecimento = 3,
        
        [EnumMember, Description("Consultar filiais atualizadas")]
        ConsultarFiliaisAtualizadas = 4,
        
        [EnumMember, Description("Consultar filial embarcador")]
        ConsultarFilialEmbarcador = 5,
        
        [EnumMember, Description("Não utilizado")]
        NaoUtilizado = 6,
        
        [EnumMember, Description("Enviar nota")]
        EnviarNota = 7,
        
        [EnumMember, Description("Importação planulha nota")]
        ImportacaoPlanilhaNota = 8,
        
        [EnumMember, Description("Alterar dados nota")]
        AlterarDadosNota = 9,
        
        [EnumMember, Description("Consulta latitude longitude por endereco")]
        ConsultaLatitudeLongitudePorEndereco = 10,
        
        [EnumMember, Description("Get id's de estabelecimentos no percurso")]
        GetIdsEstabelecimentoNoPercurso = 11,
        
        [EnumMember, Description("Cadstro ponto referência")]
        CadastroPontoReferencia = 12,
        
        [EnumMember, Description("Importação planilha ponto referência")]
        ImportacaoPlanilhaPontoReferencia = 13,
        
        [EnumMember, Description("Consultar entregas reltório")]
        ConsultarEntregasReltorio = 14,
        
        [EnumMember, Description("Consultar entregas")]
        ConsultarEntregas = 15,
    }
}
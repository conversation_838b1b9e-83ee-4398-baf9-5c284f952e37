﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class PontosRotaModeloMap : EntityTypeConfiguration<PontosRotaModelo>
    {
        public PontosRotaModeloMap()
        {
            ToTable("PONTOS_ROTA_MODELO");

            HasKey(t => new { t.Id<PERSON>otaModelo, t.IdPonto });

            Property(t => t.IdRotaModelo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdPonto)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .HasMaxLength(500)
                .IsRequired();
            
            Property(t => t.Ibge)
                .IsRequired();
            
            Property(t => t.Latitude)
                .HasPrecision(18, 7);
            
            Property(t => t.Longitude)
                .HasPrecision(18, 7);
        }
    }
}
using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Roteirizador
{
    public class RelatorioRoteirizador
    {
        public byte[] GetReport(List<RelatorioRoteirizadorFiltrosDataType> listaFiltros, List<RelatorioRoteirizadorPracasDataType> listaPracasPedagio, List<RelatorioRoteirizadorEntradasSaidasDataType> listaEntradasSaidas, List<RelatorioRoteirizadorPostosDataType> listaPostos, string tipoArquivo, string logo, string mapa)
        {
            mapa = mapa ?? string.Empty;
            
            var parametrizes = new Tuple<string, string, bool>[2];
            parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);
            parametrizes[1] = new Tuple<string, string, bool>("Mapa", mapa, true);

            var listaDataSources = new List<Tuple<object, string>>
            {
                new Tuple<object, string>(lista<PERSON><PERSON><PERSON>, "DtsFiltros"),
                new Tuple<object, string>(listaPracasPedagio, "DtsPracasPedagio"),
                new Tuple<object, string>(listaEntradasSaidas, "DtsEntradasSaidas"),
                new Tuple<object, string>(listaPostos, "DtsPostos"),
            };

            //var bytes = new Base.Reports().GetReport(listaDataSources, parametrizes, true, "ATS.CrossCutting.Reports.Roteirizador.RelatorioRoteirizador.rdlc", tipoArquivo);
            
            var bytes = new Base.Reports().GetReport(listaDataSources, parametrizes, true, "ATS.CrossCutting.Reports.Roteirizador.RelatorioRoteirizador.rdlc", tipoArquivo);

            return bytes;
        }
    }
}
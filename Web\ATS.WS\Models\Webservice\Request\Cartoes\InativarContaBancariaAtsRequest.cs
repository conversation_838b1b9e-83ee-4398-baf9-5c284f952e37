using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Models.Webservice.Request.Cartoes
{
    public class InativarContaBancariaAtsRequest : RequestBase
    {
        public string Documento
        {
            get { return _documento; }
            set {_documento = value.OnlyNumbers();}
        }
        private string _documento { get; set; }
        
        public string CodigoBacenBanco
        {
            get { return _codigoBacenBanco; }
            set {_codigoBacenBanco = value.OnlyNumbers();}
        }
        private string _codigoBacenBanco { get; set; }
        
        public string Agencia
        {
            get { return _agencia; }
            set {_agencia = value.OnlyNumbers();}
        }
        private string _agencia { get; set; }
        
        public string Conta
        {
            get { return _conta; }
            set {_conta = value.OnlyNumbers();}
        }
        private string _conta { get; set; }
        public InativarContaBancariaRequestTipoContaBancaria TipoContaBancaria { get; set; }
        
        public ValidationResult ValidaRequest()
        {
            ValidationResult validationResult = ValidaRequestBase();
            
            if (string.IsNullOrWhiteSpace(Documento))
                validationResult.Add("É obrigatório o envio do campo Documento");
            else if (Documento.Length != 11 && Documento.Length != 14)
                validationResult.Add("O campo Documento deve conter 11 ou 14 dígitos");
            
            if (string.IsNullOrWhiteSpace(CodigoBacenBanco))
                validationResult.Add("É obrigatório o envio do campo CodigoBacenBanco");
            
            if (string.IsNullOrWhiteSpace(Agencia))
                validationResult.Add("É obrigatório o envio do campo Agencia");
            
            if (string.IsNullOrWhiteSpace(Conta))
                validationResult.Add("É obrigatório o envio do campo Conta");
            
            if (TipoContaBancaria == InativarContaBancariaRequestTipoContaBancaria.Indefinido)
                validationResult.Add("É obrigatório o envio do campo TipoContaBancaria preenchido como 1 (Corrente) ou 2 (Poupanca)");

            return validationResult;
        }
    }
}
﻿using NLog;
using SistemaInfo.Cartoes.Repository.External.SistemaInfo.Auth.Api.Client;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Linq;
using System.Web;

namespace ATS.Data.Repository.External.SistemaInfo.Auth
{
    public class AuthExternalRepository : IDisposable
    {
        #region Classes

        private class WebAuthToken
        {
            /// <summary>
            /// Documento do usuário requisitando a sessão
            /// </summary>
            public string Documento { get; set; }

            /// <summary>
            /// Token para autenticção no serviço de login dinamico. É utilizado a adm x empresa desta credencial de acesso para gerar a sessão dinamica
            /// </summary>
            public string AuthToken { get; set; }

            /// <summary>
            /// Token da sessão gerada par ao usuário
            /// </summary>
            public string SessionToken { get; set; }
        }

        #endregion

        private const string ServicoIndisponivelResultMessage = "Serviço de autenticação/api indisponível.";

        /// <summary>
        /// Tokens de acesso a funções administrativas do ambiente web da plataforma de cartões
        /// </summary>
        private static readonly List<WebAuthToken> WebTokensCache = new List<WebAuthToken>();

        private EmpresasClient _empresaApiClient;
        private UsuariosClient _usuarioClient;

        public AuthExternalRepository()
        {
            TokenAdministradora = SistemaInfoConsts.TokenAdministradora;

            _empresaApiClient = new EmpresasClient(HttpContext.Current);
            _usuarioClient = new UsuariosClient(HttpContext.Current);

            _empresaApiClient.BaseUrl = SistemaInfoConsts.AuthApiUrl;
            _usuarioClient.BaseUrl = SistemaInfoConsts.AuthApiUrl;
        }

        public string TokenAdministradora { get; }

        public void Dispose()
        {
        }

        public IntegrarEmpresaApiResponse IntegrarEmpresa(string cnpj, string nome)
        {
            try
            {
                var request = new IntegrarEmpresaApiRequest
                {
                    Cnpj = cnpj,
                    Nome = nome
                };
                var result = _empresaApiClient.Integrar(request, TokenAdministradora);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new IntegrarEmpresaApiResponse
                {
                    Status = IntegrarEmpresaApiResponseStatus.Falha,
                    Mensagens = new ObservableCollection<ApiResponseValidation>
                    {
                        new ApiResponseValidation
                        {
                            Message = ServicoIndisponivelResultMessage,
                            Type = ApiResponseValidationType.Error
                        }
                    }
                };
            }
        }

        public GetOrGenerateAppTokenApiResponse GetOrGenerateAppToken(string cnpjEmpresa)
        {
            try
            {
                var appName = ConfigurationManager.AppSettings["TITULO_SISTEMA"];
                if (string.IsNullOrWhiteSpace(appName))
                    throw new AuthAtsException("Tag 'TITULO_SISTEMA' não definida no web.config. COnfigure seu valor para utilizar recursos de administradora de micro serviços.");

                var result = _empresaApiClient.GetOrGenerateAppToken(cnpjEmpresa, appName, TokenAdministradora);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new GetOrGenerateAppTokenApiResponse
                {
                    Status = GetOrGenerateAppTokenApiResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

        public LoginDinamicoApiResponse LoginDinamico(LoginDinamicoApiRequest request)
        {
            try
            {
                return _usuarioClient.LoginDinamico(request, TokenAdministradora);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new LoginDinamicoApiResponse
                {
                    Sucesso = false,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

        public string LoginDinamicoCacheable(string documento, string nome, string authToken)
        {
            var cache = WebTokensCache.FirstOrDefault(c => c.Documento == documento && c.AuthToken == authToken);
            if (cache != null)
                return cache.SessionToken;

            var request = new LoginDinamicoApiRequest
            {
                Documento = documento,
                Nome = nome,
                Token = authToken,
                AppType = LoginDinamicoApiRequestAppType.ATS
            };
            var session = LoginDinamico(request);
            if (session.Sucesso.HasValue && session.Sucesso.Value)
            {
                WebTokensCache.Add(new WebAuthToken
                {
                    Documento = documento,
                    AuthToken = authToken,
                    SessionToken = session.Sessao.Value.ToString()
                });
                return session.Sessao.Value.ToString();
            }

            throw new AuthAtsException("Erro ao iniciar sessão na plataforma de cartões. " + session.Mensagem);
        }
    }
}

﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using ATS.CrossCutting.IoC;
//using ATS.Domain.Entities;
//using ATS.Domain.Extensions;
//using ATS.Domain.Grid;
//using ATS.Domain.Helpers;
//using ATS.Domain.Interface.Database;
//using ATS.Domain.Interface.Service;
//using ATS.Domain.Service.Common;
//using ATS.Domain.Validation;
//using System.Linq.Dynamic;
//using System.Data.Entity;

//namespace ATS.Domain.Service
//{
//    public class ProdutoDadosCargaService : ServiceBase, IProdutoDadosCargaService
//    {
//        private readonly IProdutoDadosCargaRepository _produtoDadosCargaRepository;

//        public ProdutoDadosCargaService(IProdutoDadosCargaRepository produtoDadosCargaRepository)
//        {
//            _produtoDadosCargaRepository = produtoDadosCargaRepository;
//        }

//        public ValidationResult Add(ProdutoDadosCarga produtodc)
//        {
//            try
//            {
//                _produtoDadosCargaRepository.Add(produtodc);
//                return new ValidationResult();
//            }
//            catch (Exception e)
//            {
//                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
//            }
//        }

//        public ProdutoDadosCarga Get(int idProdutodc)
//        {
//            return _produtoDadosCargaRepository
//                .FirstOrDefault(o => o.IdDadosCarga == idProdutodc);
//        }

//        public ValidationResult Update(ProdutoDadosCarga produtodc)
//        {
//            try
//            {
//                _produtoDadosCargaRepository.Update(produtodc);
//                return new ValidationResult();
//            } catch (Exception e)
//            {
//                var mensagem = e.Message + e.InnerException?.Message ?? "";
//                return new ValidationResult().Add(mensagem);
//            }
//        }
//    }
//}

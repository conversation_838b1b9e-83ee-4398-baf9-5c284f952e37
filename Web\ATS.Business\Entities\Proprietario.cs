﻿using System;
using ATS.Domain.Enum;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Proprietario
    {
        public int IdProprietario { get; set; }

        /// <summary>
        /// ID do Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// CPF e CNPJ do Proprietário
        /// </summary>
        public string CNPJCPF { get; set; }

        /// <summary>
        /// Razão Social
        /// </summary>
        public string RazaoSocial { get; set; }

        /// <summary>
        /// Nome Fantasia
        /// </summary>
        public string NomeFantasia { get; set; }

        /// <summary>
        /// RG
        /// </summary>
        public string RG { get; set; }

        /// <summary>
        /// Orgão expedidor do documento de RG
        /// </summary>
        public string RGOrgaoExpedidor { get; set; }

        /// <summary>
        /// Inscrição Estadual
        /// </summary>
        public string IE { get; set; }

        /// <summary>
        /// Número de Registro Nacional de Empresas Rodoviárias de Cargas
        /// </summary>
        public string RNTRC { get; set; }

        /// <summary>
        /// Status da Integração com outros sistemas
        /// </summary>
        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Integrado;

        /// <summary>
        /// Tipo de contrato (Terceiro, Frota, Agregado...)
        /// </summary>
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Agregado;

        public DateTime? DataNascimento { get; set; }

        public string Endereco { get; set; }

        public string Inss { get; set; }

        public string Referencia1 { get; set; }

        public string Referencia2 { get; set; }

        /// <summary>
        /// Informa se o registro ainda esta ativo ou fora 'deletado'
        /// </summary>
        public bool Ativo { get; set; } = true;
        public bool TransferirEntreCartoes { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }

        #region Parametros

        #region Meio Homologado

        /// <summary>
        /// Comportamento padrão para carregamento de meio homologado
        /// </summary>
        public ETipoCarregamentoFrete TipoCarregamentoFrete { get; set; }

        /// <summary>
        /// Percentual de transferencia  para o motorista
        /// </summary>
        public decimal? PercentualTransferenciaMotorista { get; set; }

        /// <summary>
        /// Proprietário é equiparado a TAC
        /// </summary>
        public bool? EquiparadoTac { get; set; }

        /// <summary>
        /// Indica se viagens para o proprietário devem ter o contrato de agregado
        /// </summary>
        public bool HabilitarContratoCiotAgregado { get; set; }

        /// <summary>
        /// Método padrão para pagamento de frete
        /// </summary>
        public EFormaPagamentoFrete FormaPagamentoFretePadrao { get; set; } = EFormaPagamentoFrete.Cartao;

        /// <summary>
        /// Chave pix padrão caso <see cref="FormaPagamentoFrete"/> for <see cref="EFormaPagamentoFrete.Pix"/>
        /// </summary>
        public string ChavePixPadrao { get; set; }

        #endregion

        #endregion

        #region Navegação inversa

        /// <summary>
        /// Empresa vinculado
        /// </summary>
        public virtual Empresa Empresa { get; set; }

        /// <summary>
        /// Veiculos vinculados ao proprietário
        /// </summary>
        public virtual ICollection<Veiculo> Veiculos { get; set; }
        public virtual ICollection<Viagem> Viagens { get; set; }
        #endregion

        #region Tabelas filhas

        /// <summary>
        /// Formas de contatos do proprietário
        /// </summary>
        public virtual ICollection<ProprietarioContato> Contatos { get; set; }

        /// <summary>
        /// Endereços do proprietário
        /// </summary>
        public virtual ICollection<ProprietarioEndereco> Enderecos { get; set; }

        public virtual ICollection<ConjuntoEmpresa> Conjuntos { get; set; }

        public virtual ICollection<ConjuntoCarretaEmpresa> ConjuntosCarretas { get; set; }
        #endregion
    }
}

using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class UsuarioAplicativoGetInformacoesResponse
    {
        public int IdUsuario { get; set; }
        public string Nome { get; set; }
        public string Login { get; set; }
        public string CPFCNPJ { get; set; }
        public EPerfil Perfil { get; set; }
        public bool? Ativo { get; set; }
        public ETipoContrato? TipoContrato { get; set; }
        public bool? Carreteiro { get; set; }
        public bool? ReceberNotificacao { get; set; }
        public bool? ComTracao { get; set; }
        public int? IdTipoCavalo { get; set; }
        public int? IdTipoCarreta { get; set; }
        public ETipoRodagem? TipoRodagem { get; set; }
        public string CEP { get; set; }
        public int? Numero { get; set; }
        public string Endereco { get; set; }
        public string Bairro { get; set; }
        public int? IBGECidade { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
        public UsuarioAplicativoGetInformacoesAutenticacaoResponse Autenticacao { get; set; }
        public UsuarioAplicativoGetInformacoesEmpresaResponse Empresa { get; set; }
    }

    public class UsuarioAplicativoGetInformacoesEmpresaResponse
    {
        public string CNPJ { get; set; }
        public string RazaoSocial { get; set; }
        public bool? Ativo { get; set; }
        public bool? ObrigarValorTerceiro { get; set; }
        public string EmailSugestoes { get; set; }
        public string CEP { get; set; }
        public string Bairro { get; set; }
        public int? IdCidade { get; set; }
        public int? IdEstado { get; set; }
        public string Email { get; set; }
    }

    public class UsuarioAplicativoGetInformacoesAutenticacaoResponse
    {
        public string Token { get; set; }
        public string CNPJAplicacao { get; set; }
    }
}
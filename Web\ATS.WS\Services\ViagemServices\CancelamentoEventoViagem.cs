using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Services.ViagemServices
{
    public class CancelamentoEventoViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemApp _viagemApp;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly SrvViagem _srvViagem;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IViagemEventoApp _viagemEventoApp;

        public CancelamentoEventoViagem(IVersaoAnttLazyLoadService versaoAntt, IViagemApp viagemApp, IClienteApp clienteApp, IParametrosApp parametrosApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IProprietarioApp proprietarioApp, ICadastrosApp cadastrosApp, IEmpresaRepository empresaRepository, SrvViagem srvViagem, ITransacaoCartaoApp transacaoCartaoApp, IEmpresaApp empresaApp, IViagemEventoApp viagemEventoApp)
        {
            _versaoAntt = versaoAntt;
            _viagemApp = viagemApp;
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _empresaRepository = empresaRepository;
            _srvViagem = srvViagem;
            _transacaoCartaoApp = transacaoCartaoApp;
            _empresaApp = empresaApp;
            _viagemEventoApp = viagemEventoApp;
        }

        public CancelarEventoResponseModel CancelarEvento(CancelarEventoRequestModel @params, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return CancelarEventoV2(@params, isApi);
                case EVersaoAntt.Versao3:
                    return CancelarEventoV3(@params);
                default:
                    return CancelarEventoV2(@params, isApi);
            }
        }
        
        private CancelarEventoResponseModel CancelarEventoV2(CancelarEventoRequestModel @params, bool isApi = false)
        {
            var empresaApp = _empresaApp;
            var empresaId = empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            
            var queryViagemEvento = _viagemEventoApp.Find(x => x.IdEmpresa == (empresaId ?? 0));

            if (@params.IdViagem.HasValue)
                queryViagemEvento = queryViagemEvento.Where(x => x.IdViagem == @params.IdViagem && x.IdViagemEvento == @params.IdViagemEvento);
            else
                queryViagemEvento = queryViagemEvento.Where(x => x.Viagem.NumeroControle == @params.NumeroControleViagem && x.NumeroControle == @params.NumeroControleEvento);

            var viagemEvento = queryViagemEvento.Select(c => new
            {
                c.Status,
                c.HabilitarPagamentoCartao,
                c.Viagem.DataAtualizacao
            }).FirstOrDefault();
            
            /*if(viagemEvento?.DataAtualizacao.HasValue == true && @params.DataAtualizacao.HasValue && 
               @params.DataAtualizacao < viagemEvento.DataAtualizacao)
                return new CancelarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };*/
            
            if (viagemEvento?.Status == EStatusViagemEvento.Cancelado)
            {
                var mensagemCartaoViagemAtual = string.Empty;
                var statusOperacaoCartaoAtual = ERetornoOperacaoCartao.NaoHabilitado;
                
                if (viagemEvento.HabilitarPagamentoCartao)
                {
                    var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == @params.IdViagemEvento).OrderByDescending(x => x.IdTransacaoCartao);

                    var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                    mensagemCartaoViagemAtual = transacaoCartao?.MensagemProcessamentoWs;
                    
                    if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Erro;
                    else
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Sucesso;
                }
                
                return new CancelarEventoResponseModel
                {
                    Sucesso = true,
                    Mensagem = "",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = statusOperacaoCartaoAtual,
                        Mensagem = mensagemCartaoViagemAtual
                    },
                    Token = string.Empty
                };
            }
            
            var cancelarEvento = queryViagemEvento
                .Select(x => new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = @params.CNPJEmpresa,
                    CNPJAplicacao = @params.CNPJAplicacao,
                    Token = @params.Token,
                    DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                    NomeUsuarioAudit = @params.NomeUsuarioAudit,
                    IdViagem = x.IdViagem,
                    NumeroControle = x.Viagem.NumeroControle,
                    PesoSaida = x.Viagem.PesoSaida,
                    PedagioBaixado = x.Viagem.PedagioBaixado,
                    ValorPedagio = x.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = x.Viagem.HabilitarDeclaracaoCiot,
                    NaturezaCarga = x.Viagem.NaturezaCarga,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>
                    {
                        new ViagemEventoIntegrarModel
                        {
                            IdViagemEvento = x.IdViagemEvento,
                            NumeroControle = x.NumeroControle,
                            CpfUsuario = @params.DocumentoUsuarioAudit,
                            NomeUsuario = @params.NomeUsuarioAudit,
                            TipoEvento = x.TipoEventoViagem,
                            IdViagem = x.IdViagem,
                            Status = EStatusViagemEvento.Cancelado,
                            HabilitarPagamentoCartao = x.HabilitarPagamentoCartao,
                            HabilitarPagamentoPix = x.HabilitarPagamentoPix
                        }
                    },
                    //DataAtualizacao = @params.DataAtualizacao
                }).FirstOrDefault();

            if (cancelarEvento == null || cancelarEvento.IdViagem <= 0 || cancelarEvento.ViagemEventos[0].IdViagemEvento <= 0)
                return new CancelarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = ERetornoOperacaoCartao.Erro,
                        Mensagem = string.Empty
                    },
                    Token = string.Empty
                };

            var retorno = _srvViagem.Alterar(cancelarEvento, isApi);

            var statusOperacaoCartao = ERetornoOperacaoCartao.NaoHabilitado;
            var mensagemProcessamentoWs = string.Empty;
            var idViagemEvento = cancelarEvento.ViagemEventos[0].IdViagemEvento.Value;

            //TODO: para não precisar ir no banco de novo para o retorno teria que ter um DTO do retorno da viagem, pois não da pra fazer select nas propriedades de objeto anônimo fora do método de declaração dele
            if (cancelarEvento.ViagemEventos[0].HabilitarPagamentoCartao)
            {
                var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                    .OrderByDescending(x => x.IdTransacaoCartao);

                var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                mensagemProcessamentoWs = transacaoCartao?.MensagemProcessamentoWs;
                if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                    statusOperacaoCartao = ERetornoOperacaoCartao.Erro;
                else
                    statusOperacaoCartao = ERetornoOperacaoCartao.Sucesso;
            }

            var viagemEventoStatusToken = _viagemEventoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                .Select(x => new
                {
                    x.Status,
                    x.Token
                }).First();

            return new CancelarEventoResponseModel
            {
                Sucesso = retorno.Sucesso && viagemEventoStatusToken.Status == EStatusViagemEvento.Cancelado && statusOperacaoCartao != ERetornoOperacaoCartao.Erro,
                Mensagem = retorno.Mensagem,
                IdViagem = cancelarEvento.IdViagem ?? 0,
                NumeroControle = cancelarEvento.NumeroControle,
                IdViagemEvento = cancelarEvento.ViagemEventos[0].IdViagemEvento ?? 0,
                NumeroControleEvento = cancelarEvento.ViagemEventos[0].NumeroControle,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = statusOperacaoCartao,
                    Mensagem = mensagemProcessamentoWs
                },
                Token = viagemEventoStatusToken.Token
            };
        }
        
        private CancelarEventoResponseModel CancelarEventoV3(CancelarEventoRequestModel @params)
        {
            var empresaApp = _empresaApp;
            var empresaId = empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            var viagemEventoApp = _viagemEventoApp;
            var queryViagemEvento = viagemEventoApp.Find(x => x.IdEmpresa == (empresaId ?? 0));

            if (@params.IdViagem.HasValue)
                queryViagemEvento = queryViagemEvento.Where(x => x.IdViagem == @params.IdViagem && x.IdViagemEvento == @params.IdViagemEvento);
            else
                queryViagemEvento = queryViagemEvento.Where(x => x.Viagem.NumeroControle == @params.NumeroControleViagem && x.NumeroControle == @params.NumeroControleEvento);

            var viagemAtual = queryViagemEvento.Select(c => new
            {
                c.Status,
                c.HabilitarPagamentoCartao,
                c.Viagem.FormaPagamento,
                c.Viagem.DataAtualizacao
            }).FirstOrDefault();
            
            if(viagemAtual?.DataAtualizacao.HasValue == true && @params.DataAtualizacao.HasValue && 
               @params.DataAtualizacao < viagemAtual.DataAtualizacao)
                return new CancelarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };
            
            if (viagemAtual?.Status == EStatusViagemEvento.Cancelado)
            {
                var mensagemCartaoViagemAtual = string.Empty;
                var statusOperacaoCartaoAtual = ERetornoOperacaoCartao.NaoHabilitado;
                
                if (viagemAtual.HabilitarPagamentoCartao)
                {
                    var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == @params.IdViagemEvento).OrderByDescending(x => x.IdTransacaoCartao);

                    var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                    mensagemCartaoViagemAtual = transacaoCartao?.MensagemProcessamentoWs;
                    
                    if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Erro;
                    else
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Sucesso;
                }
                
                return new CancelarEventoResponseModel
                {
                    Sucesso = true,
                    Mensagem = "",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = statusOperacaoCartaoAtual,
                        Mensagem = mensagemCartaoViagemAtual
                    },
                    Token = string.Empty,
                };
            }
            
            var cancelarEvento = queryViagemEvento
                .Select(x => new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = @params.CNPJEmpresa,
                    CNPJAplicacao = @params.CNPJAplicacao,
                    Token = @params.Token,
                    DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                    NomeUsuarioAudit = @params.NomeUsuarioAudit,
                    IdViagem = x.IdViagem,
                    NumeroControle = x.Viagem.NumeroControle,
                    PesoSaida = x.Viagem.PesoSaida,
                    PedagioBaixado = x.Viagem.PedagioBaixado,
                    ValorPedagio = x.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = x.Viagem.HabilitarDeclaracaoCiot,
                    NaturezaCarga = x.Viagem.NaturezaCarga,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>
                    {
                        new ViagemEventoIntegrarModel
                        {
                            IdViagemEvento = x.IdViagemEvento,
                            NumeroControle = x.NumeroControle,
                            CpfUsuario = @params.DocumentoUsuarioAudit,
                            NomeUsuario = @params.NomeUsuarioAudit,
                            TipoEvento = x.TipoEventoViagem,
                            IdViagem = x.IdViagem,
                            Status = EStatusViagemEvento.Cancelado,
                        }
                    },
                    DataAtualizacao = @params.DataAtualizacao
                }).FirstOrDefault();

            if (cancelarEvento == null || cancelarEvento.IdViagem <= 0 || cancelarEvento.ViagemEventos[0].IdViagemEvento <= 0)
                return new CancelarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = ERetornoOperacaoCartao.Erro,
                        Mensagem = string.Empty
                    },
                    Token = string.Empty
                };

            var retorno = _srvViagem.Alterar(cancelarEvento);

            var statusOperacaoCartao = ERetornoOperacaoCartao.NaoHabilitado;
            var mensagemProcessamentoWs = string.Empty;
            var idViagemEvento = cancelarEvento.ViagemEventos[0].IdViagemEvento.Value;

            //TODO: para não precisar ir no banco de novo para o retorno teria que ter um DTO do retorno da viagem, pois não da pra fazer select nas propriedades de objeto anônimo fora do método de declaração dele
            if (viagemAtual.FormaPagamento == EViagemFormaPagamento.Cartao)
            {
                var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                    .OrderByDescending(x => x.IdTransacaoCartao);

                var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                mensagemProcessamentoWs = transacaoCartao?.MensagemProcessamentoWs;
                if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                    statusOperacaoCartao = ERetornoOperacaoCartao.Erro;
                else
                    statusOperacaoCartao = ERetornoOperacaoCartao.Sucesso;
            }

            var viagemEvento = viagemEventoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                .Select(x => new
                {
                    x.Status,
                    x.Token
                }).First();

            return new CancelarEventoResponseModel
            {
                Sucesso = retorno.Sucesso && viagemEvento.Status == EStatusViagemEvento.Cancelado && statusOperacaoCartao != ERetornoOperacaoCartao.Erro,
                Mensagem = retorno.Mensagem,
                IdViagem = cancelarEvento.IdViagem ?? 0,
                NumeroControle = cancelarEvento.NumeroControle,
                IdViagemEvento = cancelarEvento.ViagemEventos[0].IdViagemEvento ?? 0,
                NumeroControleEvento = cancelarEvento.ViagemEventos[0].NumeroControle,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = statusOperacaoCartao,
                    Mensagem = mensagemProcessamentoWs
                },
                Token = viagemEvento.Token,
                Avisos = retorno.Objeto?.Avisos
            };
        }
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO.Pix
{
    public class SolicitacaoChavePixGridResponse
    {
        public int totalItems { get; set; }
        public List<SolicitacaoChavePixProprietarioGridResponseItem> items { get; set; }
    }

    public class SolicitacaoChavePixProprietarioGridResponseItem
    {
        public int Codigo { get; set; }
        public string DocumentoTitular { get; set; }
        public string NomeTitular { get; set; }
        public ETipoChavePix TipoEnum { get; set; }
        public string Tipo { get; set; }
        public string Chave { get; set; }
        public ESolicitacaoChavePixStatus StatusEnum { get; set; }
        public string Status { get; set; }
        public DateTime DataCadastroDateTime { get; set; }
        public string DataCadastro { get; set; }
        public string DocumentoUsuarioCadastro { get; set; }
        public string NomeUsuarioCadastro { get; set; }
    }
}
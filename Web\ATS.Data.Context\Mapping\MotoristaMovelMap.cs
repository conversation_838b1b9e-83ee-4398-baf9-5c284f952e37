﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class MotoristaMovelMap : EntityTypeConfiguration<MotoristaMovel>
    {
        public MotoristaMovelMap()
        {
            ToTable("MOTORISTA_MOVEL");

            HasKey(t => new { t.IdMotorista, t.IdMovel });

            Property(t => t.IdMotorista)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdMovel)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IMEI)
                .IsRequired()
                .HasMaxLength(15)
                .IsFixedLength();
        }
    }
}
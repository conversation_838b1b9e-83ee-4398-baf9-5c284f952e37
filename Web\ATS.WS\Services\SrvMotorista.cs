﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Grid;
using Sistema.Framework.Util.Extension;
using Motorista = ATS.Domain.Entities.Motorista;
using ATS.Domain.Interface.Service;
using MassTransit;
using ValidationResult = ATS.Domain.Validation.ValidationResult;

namespace ATS.WS.Services
{
    public class SrvMotorista : SrvBase
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly SrvUtils _srvUtils;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IUsuarioDocumentoApp _usuarioDocumentoApp;
        private readonly ITipoDocumentoApp _tipoDocumentoApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ILimiteTransacaoPortadorApp _limiteTransacaoPortadorApp;
        private readonly ISerproApp _serproApp;
        private readonly IUsuarioService _usuarioService;
        private readonly IPublishEndpoint _publisher;

        public SrvMotorista(IUsuarioApp usuarioApp, SrvUtils srvUtils, IMotoristaApp motoristaApp, IEmpresaApp empresaApp,
            ICidadeApp cidadeApp, IUsuarioDocumentoApp usuarioDocumentoApp, ITipoDocumentoApp tipoDocumentoApp, IParametrosEmpresaService parametrosEmpresa,
            ILimiteTransacaoPortadorApp limiteTransacaoPortadorApp, ISerproApp serproApp, IUsuarioService usuarioService, IPublishEndpoint publisher)
        {
            _usuarioApp = usuarioApp;
            _srvUtils = srvUtils;
            _motoristaApp = motoristaApp;
            _empresaApp = empresaApp;
            _limiteTransacaoPortadorApp = limiteTransacaoPortadorApp;
            _cidadeApp = cidadeApp;
            _usuarioDocumentoApp = usuarioDocumentoApp;
            _tipoDocumentoApp = tipoDocumentoApp;
            _parametrosEmpresa = parametrosEmpresa;
            _serproApp = serproApp;
            _usuarioService = usuarioService;
            _publisher = publisher;
        }

        public Retorno<MotoristaModel> Integrar(MotoristaIntegrarRequestModel @params)
        {
            try
            {
                var validationResultUsuario = new Retorno<MotoristaModel>();
                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (!idEmpresa.HasValue)
                    return new Retorno<MotoristaModel>(false, @"Empresa informado é inválido", null);

                var empresa = _empresaApp.GetQuery(idEmpresa.Value).Select(c => new
                {
                    c.Email, c.EmailSugestoes, c.IdEmpresa, c.IntegrarComoUsuario
                }).First();

                var emailEmpresa =  string.IsNullOrWhiteSpace(empresa.Email) ? empresa.EmailSugestoes: empresa.Email;

                //Validações
                if (string.IsNullOrEmpty(@params.Celular) || @params.Celular.Length > 11 || @params.Celular.Length < 10)
                {
                    var celularEmpresa = _empresaApp.Get(@params.CNPJEmpresa).Telefone;
                    @params.Celular = string.IsNullOrWhiteSpace(celularEmpresa) ? "9999999999" : celularEmpresa;
                }

                if (!string.IsNullOrEmpty(@params.Email))
                    @params.Email = @params.Email.Length > 100 ? @params.Email.Substring(0, 100): @params.Email;

                if (!string.IsNullOrWhiteSpace(@params.Email) && @params.Email.Contains(";"))
                {
                    string[] listaEmail = @params.Email.Split(';');

                    foreach (var email in listaEmail)
                    {
                        if (!AssertionConcern.IsValidEmail(email))
                        {
                            @params.Email = string.IsNullOrWhiteSpace(emailEmpresa) ? "<EMAIL>" : emailEmpresa;
                        }
                    }
                }

                if (string.IsNullOrEmpty(@params.Email) || !AssertionConcern.IsValidEmail(@params.Email))
                    @params.Email = string.IsNullOrWhiteSpace(emailEmpresa) ? "<EMAIL>" : emailEmpresa;

                if (@params.BacenPais <= 0 || _srvUtils.GetIdPaisPorBACEN(@params.BacenPais) == 0 || _srvUtils.GetIdPaisPorBACEN(@params.BacenPais) == null)
                    @params.BacenPais = 1058;

                var motorista = Mapper.Map<MotoristaIntegrarRequestModel, Motorista>(@params);

                motorista.Nome = new Regex("[*'\",_&#^@]").Replace(motorista.Nome, string.Empty);

                var motoristaExiste = _motoristaApp.Any(@params.Cpf, idEmpresa.Value, true);

                if (motoristaExiste)
                    return Update(@params, idEmpresa);

                /*if (@params.Cpf.OnlyNumbers().Length == 11 && _parametrosEmpresa.GetPermiteCadastrarMotoristaComCpfFicticio(empresa.IdEmpresa) == false)
                {
                    var validacaoSerpro = _serproApp.ValidarPortador(@params.Cpf);
                    if (!validacaoSerpro.IsValid)
                        return new Retorno<MotoristaModel>(false, validacaoSerpro.Errors.FirstOrDefault()?.Message, null);
                }*/
                if (@params.BacenPais > 0)
                {
                    var idRetPais = _srvUtils.GetIdPaisPorBACEN(@params.BacenPais);

                    if (idRetPais.HasValue && idRetPais > 0)
                        motorista.IdPais = idRetPais.GetValueOrDefault();
                }

                if (@params.IbgeEstado > 0)
                {
                    var idRetEstado = _srvUtils.GetIdEstadoPorIBGE(@params.IbgeEstado);

                    if (idRetEstado.HasValue && idRetEstado > 0)
                        motorista.IdEstado = idRetEstado.GetValueOrDefault();
                }

                var idRetCidade = _srvUtils.GetIdCidadePorIBGE(@params.IbgeCidade);

                if (idRetCidade == null)
                    return new Retorno<MotoristaModel>(false, @"Cidade não cadastrada!", null);

                if (idRetCidade > 0)
                {
                    motorista.IdCidade = idRetCidade.GetValueOrDefault();
                    if (motorista.IdEstado == 0 || motorista.IdPais == 0)
                    {
                        var cidade = _cidadeApp.GetWithAllChilds(motorista.IdCidade);
                        motorista.IdEstado = cidade.IdEstado;
                        motorista.IdPais = cidade.Estado.IdPais;
                    }
                }

                if (@params.ValidadeCnh.HasValue)
                {
                    var usuario = _usuarioApp.GetPorCNPJCPF(StringExtension.OnlyNumbers(motorista.CPF));

                    if (usuario != null)
                    {
                        var documentosUsuario = _usuarioDocumentoApp.GetDocumentos(usuario.IdUsuario);
                        if (!documentosUsuario.Any(x => x.TipoDocumento.Descricao == "CNH"))
                        {
                            usuario.Documentos = new List<UsuarioDocumento>();

                            var tpDocCnh = _tipoDocumentoApp.All().FirstOrDefault(x => x.Descricao == "CNH");

                            if (tpDocCnh != null)
                                usuario.Documentos.Add(new UsuarioDocumento
                                {
                                    Validade = @params.ValidadeCnh.Value,
                                    IdTipoDocumento = tpDocCnh.IdTipoDocumento
                                });
                            _usuarioApp.Update(usuario, usuario.IdUsuario);
                        }
                    }
                    else
                        motorista.ValidadeCNH = @params.ValidadeCnh;
                }

                motorista.Sexo = string.IsNullOrEmpty(motorista.Sexo) ? "M" : motorista.Sexo.Trim().ToUpper();

                if (!string.IsNullOrWhiteSpace(@params.FormularioCnh))
                    motorista.FormularioCnh = @params.FormularioCnh;

                var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CpfcnpjUsuario);
                motorista.IdEmpresa = idEmpresa.GetValueOrDefault();

                var validationResult = _motoristaApp.Add(motorista, idUsuario.GetValueOrDefault(), true);

                if (!validationResult.IsValid)
                    return new Retorno<MotoristaModel>(false, validationResult.ToFormatedMessage(), null);

                //Integração como usuário caso parametro na empresa marcado e integração sucesso
                if (empresa.IntegrarComoUsuario && validationResult.IsValid || motorista.TipoContrato == ETipoContrato.Terceiro)
                    _publisher.Publish(@params);

                return new Retorno<MotoristaModel>(true, validationResultUsuario.Mensagem ?? "",
                    Mapper.Map<Motorista, MotoristaModel>(motorista));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception(e.Message);
            }
        }

        public ValidationResult AlterarStatus(int idMotorista)
        {
            var motoristaApp = _motoristaApp;
            var validationResult = motoristaApp.AlterarStatus(idMotorista);
            return validationResult;
        }

        public Retorno<MotoristaModel> Update(MotoristaIntegrarRequestModel @params, int? idEmpresa = null)
        {
            try
            {
                if(!idEmpresa.HasValue)
                    idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (!idEmpresa.HasValue)
                    return new Retorno<MotoristaModel>(false, @"Empresa informada é inválido", null);

                var empresa = _empresaApp.Get(@params.CNPJEmpresa);
                var emailEmpresa =  string.IsNullOrWhiteSpace(empresa.Email) ? empresa.EmailSugestoes: empresa.Email;
                var idMotorista = _motoristaApp.GetIdPorCpf(@params.Cpf, idEmpresa);

                if (!idMotorista.HasValue)
                    return new Retorno<MotoristaModel>(false, @"Motorista não encontrado para a empresa informada!", null);

                /*if (@params.Cpf.OnlyNumbers().Length == 11 &&
                    _parametrosEmpresa.GetPermiteCadastrarMotoristaComCpfFicticio(empresa.IdEmpresa) == false)
                {
                    var validacaoSerpro = _serproApp.ValidarPortador(@params.Cpf);
                    if (!validacaoSerpro.IsValid)
                        return new Retorno<MotoristaModel>(false, validacaoSerpro.Errors.FirstOrDefault()?.Message, null);
                }*/
                //Validações
                if (string.IsNullOrEmpty(@params.Celular) || @params.Celular.Length > 11 || @params.Celular.Length < 10)
                {
                    var celularEmpresa = _empresaApp.Get(@params.CNPJEmpresa).Telefone;
                    @params.Celular = string.IsNullOrWhiteSpace(celularEmpresa) ? "9999999999" : celularEmpresa;
                }

                if (!string.IsNullOrEmpty(@params.Email))
                {
                    @params.Email = @params.Email.Length > 100 ? @params.Email.Substring(0, 100): @params.Email;
                }

                if (!string.IsNullOrWhiteSpace(@params.Email) && @params.Email.Contains(";"))
                {
                    string[] listaEmail = @params.Email.Split(';');

                    foreach (var email in listaEmail)
                    {
                        if (!AssertionConcern.IsValidEmail(email))
                        {
                            @params.Email = string.IsNullOrWhiteSpace(emailEmpresa) ? "<EMAIL>" : emailEmpresa;
                        }
                    }
                }

                if (string.IsNullOrEmpty(@params.Email) || !AssertionConcern.IsValidEmail(@params.Email))
                {
                    @params.Email = string.IsNullOrWhiteSpace(empresa.Email) ? "<EMAIL>" : empresa.Email;
                }

                if (@params.BacenPais <= 0 || _srvUtils.GetIdPaisPorBACEN(@params.BacenPais) == 0 ||
                    _srvUtils.GetIdPaisPorBACEN(@params.BacenPais) == null)
                {
                    @params.BacenPais = 1058;
                }

                var motorista = _motoristaApp.Get(idMotorista.Value);

                if (string.IsNullOrWhiteSpace(@params.Sexo))
                    @params.Sexo = motorista.Sexo;

                Mapper.Map(@params, motorista);

                motorista.Nome = new Regex("[*'\",_&#^@]").Replace(motorista.Nome,String.Empty);
                if (@params.TipoContrato != ETipoContrato.Terceiro)
                {
                    var veiculos = _motoristaApp.GetVeiculosMotorista(motorista.CPF, motorista.IdEmpresa);

                    if (veiculos != null)
                    {
                        foreach (var veiculo in veiculos)
                        {
                            ValidationResult validationResultVeiculo = _motoristaApp.DesvincularMotoristaVeiculo(motorista.IdEmpresa.ToInt(), motorista.CPF, veiculo.Placa);
                        }
                    }
                }
                var idRetPais = _srvUtils.GetIdPaisPorBACEN(@params.BacenPais);

                if (idRetPais.HasValue && idRetPais > 0)
                    motorista.IdPais = idRetPais.GetValueOrDefault();

                var idRetEstado = _srvUtils.GetIdEstadoPorIBGE(@params.IbgeEstado);

                if (idRetEstado.HasValue && idRetEstado > 0)
                    motorista.IdEstado = idRetEstado.GetValueOrDefault();

                var idRetCidade = _srvUtils.GetIdCidadePorIBGE(@params.IbgeCidade);

                if (idRetCidade == null)
                    return new Retorno<MotoristaModel>(false, @"Cidade não cadastrada!", null);

                if (idRetCidade > 0)
                    motorista.IdCidade = idRetCidade.GetValueOrDefault();

                if (@params.IbgeCidade == 0)
                    return new Retorno<MotoristaModel>(false, @"Código IBGE da Cidade não cadastrado!", null);

                if (motorista.IdEstado == 0)
                {
                    var cidade = _cidadeApp.Get(idRetCidade.Value);
                    motorista.IdEstado = cidade.IdEstado;
                }

                #region Validade CNH

                if (@params.ValidadeCnh.HasValue)
                {
                    Usuario usuario;
                    usuario = _usuarioApp.GetPorCNPJCPF(StringExtension.OnlyNumbers(motorista.CPF), true, idEmpresa) ?? _usuarioApp.GetPorCNPJCPF(StringExtension.OnlyNumbers(motorista.CPF));

                    if (usuario != null)
                    {
                        if (usuario.Documentos.Any())
                        {
                            usuario.Documentos.ToList().ForEach(x =>
                            {
                                if (x.TipoDocumento.Descricao == "CNH")
                                    x.Validade = @params.ValidadeCnh.Value;
                            });

                            _usuarioApp.Update(usuario, usuario.IdUsuario);
                        }
                    }
                    else
                    {
                        motorista.ValidadeCNH = @params.ValidadeCnh.Value;
                    }
                }

                #endregion

                motorista.Sexo = motorista.Sexo?.Trim();

                if (!string.IsNullOrWhiteSpace(@params.FormularioCnh))
                    motorista.FormularioCnh = @params.FormularioCnh;

                var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CpfcnpjUsuario);

                var validationResult = _motoristaApp.Update(motorista, idUsuario.GetValueOrDefault(), true);

                //Integração como usuário caso parametro na empresa marcado e integração sucesso
                if (empresa.IntegrarComoUsuario && validationResult.IsValid || motorista.TipoContrato == ETipoContrato.Terceiro)
                    _publisher.Publish(@params);

                if (!validationResult.IsValid)
                    return new Retorno<MotoristaModel>(false, validationResult.ToFormatedMessage(), null);

                return new Retorno<MotoristaModel>(true, string.Empty, Mapper.Map<Motorista, MotoristaModel>(motorista));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception(e.Message);
            }
        }

        public byte[] GerarRelatorioGridMotoristas(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao)
        {
            var report = _motoristaApp.GerarRelatorioGridMotoristas(idEmpresa, take, page, order, filters, GetLogo(idEmpresa), extensao);
            return report;
        }

        public Retorno<MotoristaModel> IntegrarUsuarioMotorista(MotoristaIntegrarRequestModel @params)
        {
            var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            var motorista = Mapper.Map<MotoristaIntegrarRequestModel, Motorista>(@params);
            var idUsuarioLogon = _usuarioApp.GetIdPorCNPJCPF(@params.CpfcnpjUsuario) ?? 0;

            motorista.Nome = new Regex("[*'\",_&#^@]").Replace(motorista.Nome,String.Empty);

            Usuario usuario = null;
            if(idEmpresa.HasValue)
                usuario = _usuarioApp.GetPorCNPJCPF(motorista.CPF, true, idEmpresa);

            if(usuario == null)
                usuario = _usuarioApp.GetPorCNPJCPF(motorista.CPF);

            ValidationResult validationResult;

            if (@params.IbgeCidade == 0)
                return new Retorno<MotoristaModel>(false, @"Código IBGE da Cidade não cadastrado!", null);

            var cidade = _cidadeApp.GetCidadeByIBGE(@params.IbgeCidade);

            //Trata quando ja existe um usuário na base de dados...
            if (usuario != null)
            {
                usuario.IdEmpresa = idEmpresa;

                usuario.Nome = motorista.Nome;
                usuario.CNH = motorista.CNH;
                usuario.ValidadeCNH = _usuarioApp.GetDocumentoCnhPorCpfMot(motorista.CPF).Validade;
                usuario.CNHCategoria = motorista.CNHCategoria;
                usuario.RG = motorista.RG;
                usuario.RGOrgaoExpedidor = motorista.RGOrgaoExpedidor;
                usuario.Referencia1 = @params.Referencia1;
                usuario.Referencia2 = @params.Referencia2;
                usuario.NomeMae = @params.NomeMae;
                usuario.NomePai = @params.NomePai;
                usuario.DataNascimento = @params.DataNascimento;

                // Endereço
                var endereco = usuario.Enderecos.FirstOrDefault();
                if (endereco == null)
                {
                    endereco = new UsuarioEndereco();
                    usuario.Enderecos.Add(endereco);
                }
                endereco.CEP =  motorista.CEP;
                endereco.Bairro = motorista.Bairro;
                endereco.Endereco = motorista.Endereco;
                endereco.IdCidade = cidade.IdCidade;
                endereco.IdPais = cidade.Estado.IdPais;
                endereco.IdEstado= cidade.IdEstado;

                // Contato
                var contato = usuario.Contatos.FirstOrDefault();
                if (contato == null)
                {
                    contato = new UsuarioContato();
                    usuario.Contatos.Add(contato);
                }
                contato.Email = motorista.Email;
                contato.Telefone = motorista.Celular;
                contato.Celular = motorista.Celular;


                validationResult = _usuarioApp.Update(usuario, idUsuarioLogon);

                if (!validationResult.IsValid)
                    return new Retorno<MotoristaModel>(false, validationResult.ToFormatedMessage(), null);
            }
            else
            {
                usuario = new Usuario()
                {
                    Nome = motorista.Nome,
                    CPFCNPJ = StringExtension.OnlyNumbers(motorista.CPF),
                    CNH = motorista.CNH,
                    CNHCategoria = motorista.CNHCategoria,
                    ValidadeCNH = _usuarioApp.GetDocumentoCnhPorCpfMot(motorista.CPF).Validade,
                    DataCadastro = DateTime.Now,
                    Perfil = EPerfil.Motorista,
                    Login = motorista.CPF,
                    RG = motorista.RG,
                    RGOrgaoExpedidor = motorista.RGOrgaoExpedidor,
                    Referencia1 = @params.Referencia1,
                    Referencia2 = @params.Referencia2,
                    Carreteiro = false,
                    Senha = motorista.CPF,
                    NomeMae = motorista.NomeMae,
                    NomePai = motorista.NomePai,
                    DataNascimento = motorista.DataNascimento,
                    Enderecos = new List<UsuarioEndereco>()
                      {
                          new UsuarioEndereco()
                          {
                             CEP =  motorista.CEP,
                             Bairro = motorista.Bairro,
                             Endereco = motorista.Endereco,
                             IdCidade = cidade.IdCidade,
                             IdPais = cidade.Estado.IdPais,
                             IdEstado= cidade.IdEstado
                          }
                      },
                    Contatos = new List<UsuarioContato>()
                      {
                          new UsuarioContato()
                          {
                              Email= motorista.Email,
                              Telefone = motorista.Celular,
                              Celular = motorista.Celular,
                          }
                      },
                    Ativo = true
                };

                _limiteTransacaoPortadorApp.LimitarValoresPadrão(ETipoPessoa.Fisica, usuario.CPFCNPJ);

                usuario.Senha = _usuarioService.GerarSenhaAleatoria(usuario.Perfil);

                validationResult = _usuarioApp.Add(usuario, idUsuarioLogon);

                if (!validationResult.IsValid)
                    return new Retorno<MotoristaModel>(false, validationResult.ToString(), null);
            }

            usuario = _usuarioApp.GetPorCNPJCPF(motorista.CPF);

            //Seta dados atualizados no motorista para retorno.
            motorista.IdMotorista = usuario.IdUsuario;
            motorista.Pais = usuario.Enderecos.FirstOrDefault()?.Pais;
            motorista.IdPais = usuario.Enderecos.FirstOrDefault()?.IdPais ?? 0;
            motorista.Estado = usuario.Enderecos.FirstOrDefault()?.Estado;
            motorista.IdEstado = usuario.Enderecos.FirstOrDefault()?.IdEstado ?? 0;
            motorista.Cidade = usuario.Enderecos.FirstOrDefault()?.Cidade;
            motorista.IdCidade = usuario.Enderecos.FirstOrDefault()?.IdCidade ?? 0;

            return new Retorno<MotoristaModel>(true, string.Empty, Mapper.Map<Motorista, MotoristaModel>(motorista));
        }

        private Retorno<MotoristaModel> IntegrarMotoristaUsuario(MotoristaIntegrarRequestModel @params)
        {
            var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            var motorista = Mapper.Map<MotoristaIntegrarRequestModel, Motorista>(@params);
            var idUsuarioLogon = _usuarioApp.GetIdPorCNPJCPF(@params.CpfcnpjUsuario) ?? 0;

            motorista.Nome = new Regex("[*'\",_&#^@]").Replace(motorista.Nome,String.Empty);

            Usuario usuario = null;
            if(idEmpresa.HasValue)
                usuario = _usuarioApp.GetPorCNPJCPF(motorista.CPF, true, idEmpresa);

            if(usuario == null)
                usuario = _usuarioApp.GetPorCNPJCPF(motorista.CPF);

            ValidationResult validationResult;

            if (@params.IbgeCidade == 0)
                return new Retorno<MotoristaModel>(false, @"Código IBGE da Cidade não cadastrado!", null);

            var cidade = _cidadeApp.GetCidadeByIBGE(@params.IbgeCidade);

            //Trata quando ja existe um usuário na base de dados...
            if (usuario != null)
                return new Retorno<MotoristaModel>(true, "Motorista integrado com sucesso. Usuário não integrado devido a já existir no sistema.", null);

            usuario = new Usuario()
            {
                Nome = motorista.Nome,
                CPFCNPJ = StringExtension.OnlyNumbers(motorista.CPF),
                CNH = motorista.CNH,
                CNHCategoria = motorista.CNHCategoria,
                ValidadeCNH = _usuarioApp.GetDocumentoCnhPorCpfMot(motorista.CPF).Validade,
                DataCadastro = DateTime.Now,
                Perfil = EPerfil.Motorista,
                IdEmpresa = idEmpresa,
                Login = StringExtension.OnlyNumbers(motorista.CPF),
                RG = motorista.RG,
                RGOrgaoExpedidor = motorista.RGOrgaoExpedidor,
                Referencia1 = @params.Referencia1,
                Referencia2 = @params.Referencia2,
                Carreteiro = false,
                Senha = StringExtension.OnlyNumbers(motorista.CPF),
                NomeMae = motorista.NomeMae,
                NomePai = motorista.NomePai,
                DataNascimento = motorista.DataNascimento,
                Enderecos = new List<UsuarioEndereco>()
                  {
                      new UsuarioEndereco()
                      {
                         CEP =  motorista.CEP,
                         Bairro = motorista.Bairro,
                         Endereco = motorista.Endereco,
                         IdCidade = cidade.IdCidade,
                         IdPais = cidade.Estado.IdPais,
                         IdEstado= cidade.IdEstado
                      }
                  },
                Contatos = new List<UsuarioContato>()
                  {
                      new UsuarioContato()
                      {
                          Email= motorista.Email,
                          Telefone = motorista.Celular,
                          Celular = motorista.Celular,
                      }
                  },
                Ativo = true
            };

            usuario.Senha = _usuarioService.GerarSenhaAleatoria(usuario.Perfil);

            validationResult = _usuarioApp.Add(usuario, idUsuarioLogon);

            if (!validationResult.IsValid)
                return new Retorno<MotoristaModel>(false, "Motorista integrado com sucesso e usuário não devido a/o " + validationResult, null);

            return new Retorno<MotoristaModel>(true, "Motorista e usuário integrados com sucesso.", null);
        }

        public Retorno<MotoristaPorCpfModel> ConsultarMotoristaPorCpf(MotoristaPorCpfRequestModel @params)
        {
            try
            {
                var retorno = new Retorno<MotoristaPorCpfModel>() { Sucesso = true };

                if (@params.CpfMotorista.Length < 11)
                    return new Retorno<MotoristaPorCpfModel>(false, @"CPF inválido.", null);

                var motorista = _motoristaApp.GetFromAllTables(@params.CpfMotorista);
                if (motorista != null)
                {
                    retorno.Objeto = Mapper.Map<Motorista, MotoristaPorCpfModel>(motorista);
                    retorno.Objeto.Placa = motorista.Veiculos?.FirstOrDefault()?.Placa?.ToPlacaFormato();
                    retorno.Objeto.DataValidade = _usuarioApp.GetDocumentoCnhPorCpfMot(motorista.CPF.RemoveSpecialCharacters())?.Validade;
                }
                else
                    return new Retorno<MotoristaPorCpfModel>(false, @"CPF informado não cadastrado.", null);

                return retorno;
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception(e.Message + Environment.NewLine + e.Message);
            }
        }

        public Retorno<List<MotoristaModel>> Consultar(MotoristaConsultarRequestModel @params)
        {
            try
            {
                int? idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                if (!idEmpresa.HasValue)
                    return new Retorno<List<MotoristaModel>>(false, @"Empresa inválido.", null);

                return new Retorno<List<MotoristaModel>>(true,
                    Mapper.Map<List<Motorista>, List<MotoristaModel>>(_motoristaApp.ConsultarMotoristasAtualizados(idEmpresa.Value, @params.DataBase).ToList()));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<MotoristaModel>>($"{nameof(Consultar)} >> {e.Message}");
            }
        }
    }
}

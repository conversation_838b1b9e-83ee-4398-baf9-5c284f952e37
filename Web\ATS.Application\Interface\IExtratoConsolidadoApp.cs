using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IExtratoConsolidadoApp : IBaseApp
    {
        ExtratoConsolidadoDTOResponse ExtratoConsolidadoGrid(ExtratoConsolidadoDTORequest request);
        ExtratoConsolidadoPortadorGridDTOResponse ConsultarPortadorGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        byte[] GerarRelatorioExtratoConsolidadoGrid(ExtratoConsolidadoDTORequest request);
    }
}
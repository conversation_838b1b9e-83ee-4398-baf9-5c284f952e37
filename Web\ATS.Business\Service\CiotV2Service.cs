using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity;
using System.IO;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Data.Repository.External.SistemaInfo.Ciot;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Data.Repository.External.SistemaInfo.Infra;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;
using Microsoft.Practices.EnterpriseLibrary.Common.Utility;
using Newtonsoft.Json;
using NLog;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using SistemaInfo.MicroServices.Rest.Infra.ApiClient;

namespace ATS.Domain.Service
{
    public class CiotV2Service : BaseService<IDeclaracaoCiotRepository>, ICiotV2Service
    {
        private CiotAuthRequestParams _ciotAuthRequestParams;
        private CiotExternalRepository _ciotExternalRepository;
        
        private readonly IViagemRepository _viagemRepository;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IParametrosService _parametrosService;
        private readonly IContratoCiotAgregadoRepository _contratoCiotAgregadoRepository;
        private readonly IVeiculoDapper _veiculoDapper;
        private readonly IVeiculoRepository _veiculoRepository;

        public CiotV2Service(IDeclaracaoCiotRepository repository, IUserIdentity sessionUser, IViagemRepository viagemRepository, 
            IParametrosService parametrosService, IVeiculoDapper veiculoDapper, IVeiculoRepository veiculoRepository, 
            IContratoCiotAgregadoRepository contratoCiotAgregadoRepository, IEmpresaRepository empresaRepository) : base(repository, sessionUser)
        {
            _viagemRepository = viagemRepository;
            _parametrosService = parametrosService;
            _veiculoDapper = veiculoDapper;
            _veiculoRepository = veiculoRepository;
            _contratoCiotAgregadoRepository = contratoCiotAgregadoRepository;
            _empresaRepository = empresaRepository;
        }

        public CiotExternalRepository CiotRepository => _ciotExternalRepository;
        
        public void GerarToken(int idEmpresa)
        {
            var cnpjEmpresa = _empresaRepository.GetCnpj(idEmpresa);
            var tokenEmpresa = _empresaRepository.GetTokenMicroServices(cnpjEmpresa);
            
            _ciotAuthRequestParams = new CiotAuthRequestParams
            {
                CnpjEmpresa = cnpjEmpresa,
                Token = tokenEmpresa
            };
            
            _ciotExternalRepository = new CiotExternalRepository(_ciotAuthRequestParams);
        }

        public List<Viagem> GetViagensNaoCanceladas(int idDeclaracaoCiot)
        {
            var viagens = _viagemRepository.GetAll()
                .Include(v => v.ClienteOrigem.Cidade)
                .Include(v => v.ClienteDestino.Cidade)
                .Include(v => v.ClienteOrigem.Estado)
                .Include(v => v.ClienteDestino.Estado)
                .Include(v => v.ViagemEventos)
                .Where(v => v.IdDeclaracaoCiot == idDeclaracaoCiot 
                            && v.StatusViagem != EStatusViagem.Cancelada)
                .ToList();

            return viagens;
        }
        
        public IQueryable<DeclaracaoCiot> GetQueryDeclaracaoCiotByViagem(int idViagem, int idEmpresa)
        {
            var idDeclaracaoCiotViagem = _viagemRepository.Find(x => x.IdViagem == idViagem && x.IdEmpresa == idEmpresa)
                .Select(x => x.IdDeclaracaoCiot).FirstOrDefault();

            return idDeclaracaoCiotViagem.HasValue 
                ? Repository.Where(o => o.IdDeclaracaoCiot == idDeclaracaoCiotViagem).Include(o => o.ViagensVinculadas) 
                : null;
        }     

        public DeclararCiotResult EncerrarCiot(ContratoCiotAgregado contratoCiotAgregado,
            IContratoCiotAgregadoRepository contratoCiotAgregadoRepository, List<Viagem> viagens)
        {
            try
            {
                if (contratoCiotAgregado == null)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = "Não foi encontrado nenhum contrato para o identificador informado."
                    };
                if (contratoCiotAgregado.Status != EStatusContratoAgregado.Vigente)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = $"Não é possível encerrar um contrato já {contratoCiotAgregado.Status.ToString()}" 
                    };

                if (viagens == null || viagens.Count == 0)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = $"Contrato {contratoCiotAgregado.IdContratoCiotAgregado} deve ser cancelado pois não possui nenhum viagem vinculada"
                    };

                var ciotResponse = EncerrarCiotInternal(contratoCiotAgregado, viagens);
                if (!ciotResponse.Sucesso.GetValueOrDefault(false))
                {
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = ciotResponse.Excecao?.Mensagem
                    };
                }

                contratoCiotAgregado.Encerrar(contratoCiotAgregadoRepository);
                return contratoCiotAgregado.DeclaracaoCiot.GetCiotResultV2();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, $"Erro ao encerrar CIOT para viagem");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = msg
                };
            }
        }

        public ContratoCiotAgregado GetContratoAgregado(int contratoAgregadoId)
        {
            var agregadoRepository = _contratoCiotAgregadoRepository;
            var agregado = agregadoRepository.GetAll()
                .Include(c => c.DeclaracaoCiot)
                .Include(c => c.DeclaracaoCiot.ViagemOrigem)
                .Include(c => c.Proprietario)
                .Include(c => c.Empresa)
                .Include(c => c.ContratoCiotAgregadoVeiculos)
                .Include(c => c.ContratoCiotAgregadoVeiculos.Select(v=> v.Veiculo))
                .FirstOrDefault(c => c.IdContratoCiotAgregado == contratoAgregadoId);
            return agregado;
        }

        public ValoresViagemModel CalcularValoresViagem(List<Viagem> viagens)
        {
            var viagensEventos = new List<ViagemEvento>();
            foreach (var viagem in viagens.Where(v => v.ViagemEventos.Any()))
                viagem.ViagemEventos.Where(ve => ve.Status != EStatusViagemEvento.Cancelado)
                    .ToList().ForEach(ve => viagensEventos.Add(ve));

            var tarifasAntt = viagensEventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.TarifaAntt).ToList();
            decimal valorTarifas = tarifasAntt.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento);
            int qtdTarifas = tarifasAntt.Count;

            var valoresViagemModel = new ValoresViagemModel
            {
                PesoTotal = viagens.Sum(v => v.PesoSaida ?? 0),
                ValorPedagioTotal = viagens.Where(v => v.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraSolicitada || 
                                                       v.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada).Sum(v => v.ValorPedagio),
                ValorSESTSENAT = viagens.Sum(v => v.SESTSENAT),
                ValorINSS = viagens.Sum(v => v.INSS),
                ValorIRRF = viagens.Sum(v => v.IRRPF),
                ValorFrete = viagensEventos.Where(ve => ve.TipoEventoViagem != ETipoEventoViagem.TarifaAntt && ve.TipoEventoViagem != ETipoEventoViagem.Abastecimento)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                ValorCombustivel = viagensEventos.Where(ve => ve.TipoEventoViagem == ETipoEventoViagem.Abastecimento)
                    .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                ValorTarifas = valorTarifas,
                QtdTarifas = qtdTarifas
            };

            valoresViagemModel.ValorTotalImpostos = valoresViagemModel.ValorSESTSENAT + valoresViagemModel.ValorINSS + valoresViagemModel.ValorIRRF;
            valoresViagemModel.ValorDespesas = valoresViagemModel.ValorFrete + valoresViagemModel.ValorCombustivel + valoresViagemModel.ValorTarifas + valoresViagemModel.ValorPedagioTotal; 

            return valoresViagemModel;
        }

        public CancelarCiotResult CancelarCiot(Viagem viagem)
        {
            try
            {     
                if (_ciotAuthRequestParams == null)
                    throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
                
                var declaracaoCiot = Repository
                    .FirstOrDefault(dc => dc.IdViagem == viagem.IdViagem && dc.IdEmpresa == viagem.IdEmpresa);

                if (declaracaoCiot != null && declaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Agregado)
                    return new CancelarCiotResult
                    {
                        Sucesso = true,
                        Cancelado = true,
                        Mensagem = "Não é possível realizar o cancelamento de um contrato do tipo TAC-Agregado."
                    };

                if (declaracaoCiot != null && declaracaoCiot.DataCancelamento == null)
                {
                    var request = new CancelarOperacaoTransporteRequest
                    {
                        Ciot = declaracaoCiot.Ciot,
                        MotivoCancelamento = "Cancelamento da viagem " + viagem.IdViagem,
                        SenhaAlteracao = declaracaoCiot.Senha
                    };
                    var cancelarReponse = _ciotExternalRepository.CancelarOperacaoTransporte(request);
                    if (cancelarReponse.Sucesso.HasValue)
                    {
                        var cancelado = cancelarReponse.Sucesso.Value;
                        if (!cancelado)
                            cancelado = cancelarReponse.Excecao?.Codigo ==
                                        "NEG019"; // Operação de transporte já cancelada
                        if (cancelado)
                        {
                            declaracaoCiot.DataCancelamento = DateTime.Now;
                            declaracaoCiot.MotivoCancelamento = request.MotivoCancelamento;
                            declaracaoCiot.ProtocoloCancelamento = cancelarReponse.ProtocoloCancelamento;
                            declaracaoCiot.Status = EStatusDeclaracaoCiot.Cancelado;
                            Repository.Update(declaracaoCiot);

                            return new CancelarCiotResult
                            {
                                Sucesso = true,
                                Cancelado = true,
                                Mensagem = request.MotivoCancelamento
                            };
                        }
                    }

                    return new CancelarCiotResult
                    {
                        Sucesso = false,
                        Cancelado = false,
                        Mensagem = cancelarReponse.ExceptionMessage ?? 
                                   cancelarReponse.Excecao?.Mensagem
                    };
                }

                // Não necessita cancelar
                return new CancelarCiotResult
                {
                    Sucesso = true,
                    Cancelado = declaracaoCiot?.DataCancelamento != null
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao cancelar CIOT da viagem id: " + viagem.IdViagem);


                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;

                // Erro no processo
                return new CancelarCiotResult
                {
                    Sucesso = false,
                    Cancelado = true,
                    Mensagem = msg
                };
            }
        }

        public ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request)
        {
            if (_ciotAuthRequestParams == null)
                throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
            
            return _ciotExternalRepository.ConsultarFrotaTransportador(request);
        }

        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request)
        {
            if (_ciotAuthRequestParams == null)
                throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
            
            return _ciotExternalRepository.ConsultarSituacaoTransportador(request);
        }

        public ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario)
        {
            if (_ciotAuthRequestParams == null)
                throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
            
            return _ciotExternalRepository.EquiparadoTac(cpfCnpjProprietario, rntrcProprietario);
        }

        public void GerarToken(string cnpjEmpresa)
        {
            var tokenEmpresa = _empresaRepository.GetTokenMicroServices(cnpjEmpresa);
            
            _ciotAuthRequestParams = new CiotAuthRequestParams
            {
                CnpjEmpresa = cnpjEmpresa,
                Token = tokenEmpresa
            };
            
            _ciotExternalRepository = new CiotExternalRepository(_ciotAuthRequestParams);
        }

        public bool IsDeclaracaoTacAgregado(string placa, int idEmpresa, bool habilitarContratoCiotAgregado)
        {
            var veiculoService = _veiculoRepository;
            var veiculo = veiculoService.GetVeiculoPorPlacaWithoutIncludes(placa, idEmpresa);

            return veiculo?.HabilitarContratoCiotAgregado ?? habilitarContratoCiotAgregado;
        }

        public void EnviarEmailAviso(DadosSituacaoCiotDto dados)
        {
            var emails = _parametrosService.GetEmailsAlertaCiotAgregado(dados.IdEmpresa);
            if (string.IsNullOrWhiteSpace(emails)) return;
            
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var notificacao = new NotificacaoEmailApiRequest {Aplicacao = "Extratta"};

            var colecaoEnvio = new ObservableCollection<NotificacaoEmailAddressApiRequest>();
            var listaEnviar = emails.Split(';');
            foreach (var email in listaEnviar)
                colecaoEnvio.Add(new NotificacaoEmailAddressApiRequest(){Address = email});
               
            using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\comunicado-situacao-ciot.html"))
            {
                var html = ms.ReadToEnd();
                html = html.Replace("{CiotCancelarDinamico}", dados.MensagensCancelar);
                html = html.Replace("{CiotEncerrarDinamico}", dados.MensagensEncerrar);
                notificacao.Message = new NotificacaoEmailMessageApiRequest()
                {
                    Body = html,
                    IsBodyHtml = true,
                    Subject = "Comunicado de Situação de CIOT's Agregado",
                    HiddenCopy = colecaoEnvio
                };
            }

            var emailFrom = Properties.Resources.ATSEmail;
            var password = Properties.Resources.Senha;
            var porta = Convert.ToInt32(Properties.Resources.Porta);
            var smtp = Properties.Resources.SMTPGoogle;
            notificacao.Smtp = new NotificacaoEmailSmtpApiRequest()
            {
                Password = password,
                Host = smtp,
                UserName = emailFrom,
                EnableSsl = false,
                Port = porta
            };
            notificacao.Message.From = new NotificacaoEmailAddressApiRequest {Address = emailFrom};
            notificacao.PoliticaFalha = new NotificacaoPoliticaFalhaApiRequest {MinutosParaMonitorar = 5, QuantidadeMaximaTentativasReenvio = 5};
            GerarToken(dados.cnpjEmpresa);
            new InfraExternalRepository(_ciotAuthRequestParams.Token, _ciotAuthRequestParams.AuditUserDoc, null).EnviarEmail(notificacao);
        }

        public void EnviarEmailCancelamento(string ciotsCancelados, int idEmpresa)
        {
            var emails = _parametrosService.GetEmailsAlertaCiotAgregado(idEmpresa);
            var empresaCnpj = _empresaRepository.GetCnpj(idEmpresa);
            
            if (string.IsNullOrWhiteSpace(emails)) return;

            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var notificacao = new NotificacaoEmailApiRequest {Aplicacao = "Extratta"};

            var colecaoEnvio = new ObservableCollection<NotificacaoEmailAddressApiRequest>();
            var listaEnviar = emails.Split(';');
            foreach (var email in listaEnviar)
                colecaoEnvio.Add(new NotificacaoEmailAddressApiRequest(){Address = email});
               
            using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\comunicado-cancelamento-ciot.html"))
            {
                var html = ms.ReadToEnd();
                html = html.Replace("{CiotCancelarDinamico}", ciotsCancelados);
                notificacao.Message = new NotificacaoEmailMessageApiRequest()
                {
                    Body = html,
                    IsBodyHtml = true,
                    Subject = "Comunicado de Situação de CIOT's Agregado cancelados",
                    HiddenCopy = colecaoEnvio
                };
            }

            var emailFrom = Properties.Resources.ATSEmail;
            var password = Properties.Resources.Senha;
            var porta = Convert.ToInt32(Properties.Resources.Porta);
            var smtp = Properties.Resources.SMTPGoogle;
            notificacao.Smtp = new NotificacaoEmailSmtpApiRequest()
            {
                Password = password,
                Host = smtp,
                UserName = emailFrom,
                EnableSsl = false,
                Port = porta
            };
            notificacao.Message.From = new NotificacaoEmailAddressApiRequest {Address = emailFrom};
            notificacao.PoliticaFalha = new NotificacaoPoliticaFalhaApiRequest {MinutosParaMonitorar = 5, QuantidadeMaximaTentativasReenvio = 5};
            GerarToken(empresaCnpj);
            new InfraExternalRepository(_ciotAuthRequestParams.Token, _ciotAuthRequestParams.AuditUserDoc, null).EnviarEmail(notificacao);
        }

        public DeclararCiotResult GetCiotResult(int idViagem, int idEmpresa)
        {
            var declaracaoCiot = Repository
                .AsNoTracking()
                .FirstOrDefault(dc => dc.IdViagem == idViagem && dc.IdEmpresa == idEmpresa);

            if (declaracaoCiot == null)
                return null;

            return declaracaoCiot.GetCiotResultV2();
        }

        /// <summary>
        /// Armazear resultado da declaraçao do CIOT no banco de dados do ATS
        /// </summary>
        /// <param name="ciot"></param>
        /// <param name="viagem"></param>
        /// <param name="contratoAgregado"></param>
        /// <returns>Entidade que representa o registro inserido no banco de dados. Objeto não vinculado a conexão do entity framework.</returns>
        public DeclaracaoCiot InserirRegistroDeclaracaoCiot(DeclararOperacaoTransporteReponse ciot, Viagem viagem,
            ContratoCiotAgregado contratoAgregado)
        {
            try
            {
                var dec = new DeclaracaoCiot
                {
                    IdEmpresa = viagem?.IdEmpresa ?? contratoAgregado?.IdEmpresa ??
                                throw new Exception("Nenhuma empresa vinculada a viagem/contrato"),
                    IdViagem = viagem?.IdViagem,
                    IdContratoCiotAgregado = contratoAgregado?.IdContratoCiotAgregado,
                    Ciot = ciot.Ciot,
                    Verificador = ciot.CodigoVerificador,
                    AvisoTransportador = ciot.AvisoTransportador,
                    EmContigencia = ciot.EmContingencia.HasValue && ciot.EmContingencia.Value,
                    ProtocoloErro = ciot.ProtocoloErro,
                    Senha = ciot.SenhaAlteracao,
                    DataDeclaracao = DateTime.Now,
                    TipoDeclaracao = contratoAgregado != null ? ETipoDeclaracao.Agregado : ETipoDeclaracao.Padrao
                };
                dec.Status = dec.EmContigencia ? EStatusDeclaracaoCiot.DeclaradoContigencia : EStatusDeclaracaoCiot.Declarado;

                if (viagem != null && viagem.ViagemEventos != null
                                   && viagem.ViagemEventos.Any(c => ETipoEventoViagem.TarifaAntt == c.TipoEventoViagem && EStatusViagemEvento.Baixado == c.Status))
                    dec.TarifaPaga = true;


                Repository.Add(dec);
//                Repository.Detach(dec);
                return dec;
            }
            catch (Exception e)
            {
                var ciotApiResponse = JsonConvert.SerializeObject(ciot);
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, $"Erro ao inserir declaracao CIOT para viagem Id: {viagem?.IdViagem} / Empresa: {viagem?.IdEmpresa}. CIOT API Response: {ciotApiResponse}");
                throw;
            }
        }

        /// <summary>
        /// Registrar CIOT na ANTT e armazenar registro na tabela "DeclaracaoCiot" em caso de sucesso
        /// </summary>
        /// <param name="request">Requisição para enviar a ANTT</param>
        /// <param name="viagem">Viagem que originou a declaração do CIOT</param>
        /// <param name="contratoAgregado">Contrato de agregado que originou a declaração do CIOT</param>
        /// <returns></returns>
        public DeclararOperacaoTransporteModel DeclararOperacaoTransporte(DeclararOperacaoTransporteRequest request, Viagem viagem,
            ContratoCiotAgregado contratoAgregado)
        {
            var ciotResult = CiotRepository.DeclararOperacaoTransporte(request);
            
            if (!ciotResult.Sucesso.GetValueOrDefault(false))
            {
                return new DeclararOperacaoTransporteModel
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Mensagem = ciotResult.ExceptionMessage ?? ciotResult.Excecao?.Mensagem
                    }
                };
            }
                
            // Registrando declaração do CIOT no banco de dados
            var declaracaoCiot = InserirRegistroDeclaracaoCiot(ciotResult, viagem, contratoAgregado);
            
            // Retornar dados
            var result = Mapper.Map<DeclararOperacaoTransporteModel>(ciotResult);
            result.DeclaracaoCiot = declaracaoCiot;

            if (request.Frete.TipoViagem != null) 
                result.TipoViagem = request.Frete.TipoViagem.Value;
            if (result.Veiculos != null && result.Veiculos.Count > 0)
                result.Veiculos = request.Veiculos.ToList();

            return result;
        }

        public VeiculoRntrcDTO ObterVeiculoPorPlaca(ViagemCarreta carreta, int idEmpresa)
        {
            return _veiculoDapper.GetVeiculoRntrc(carreta.Placa, idEmpresa);
        }

        public void CancelarDeclaracaoCiot(DeclaracaoCiot declaracaoCiot, CancelarOperacaoTransporteRequest request,
            CancelarOperacaoTransporteReponse cancelarReponse)
        {
            declaracaoCiot.DataCancelamento = DateTime.Now;
            declaracaoCiot.MotivoCancelamento = request.MotivoCancelamento;
            declaracaoCiot.ProtocoloCancelamento = cancelarReponse.ProtocoloCancelamento;
            declaracaoCiot.Status = EStatusDeclaracaoCiot.Cancelado;
            Repository.Update(declaracaoCiot);
            //Repository.Detach(declaracaoCiot);
        }

        public CancelarOperacaoTransporteReponse CancelarOperacaoTranspote(CancelarOperacaoTransporteRequest request,
            DeclaracaoCiot declaracaoCiot)
        {
            if (_ciotAuthRequestParams == null)
                throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
            
            var cancelarReponse = _ciotExternalRepository.CancelarOperacaoTransporte(request);
            if (!cancelarReponse.Sucesso.GetValueOrDefault(false) &&
                cancelarReponse.Excecao?.Codigo != DeclaracaoCiotCodigos.OperacaoJaCancelada)
                return cancelarReponse;

            CancelarDeclaracaoCiot(declaracaoCiot, request, cancelarReponse);

            return cancelarReponse;
        }

        public DeclararCiotResult RetificarCiot(IContratoCiotAgregadoService contratoCiotAgregadoService, ContratoCiotAgregado contratoCiotAgregado,
            IContratoCiotAgregadoRepository contratoCiotAgregadoRepository, ICollection<VeiculoModelAgregado> veiculos)
        {
            try
            {
                if (contratoCiotAgregado == null)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = "Não foi encontrado nenhum contrato para o identificador informado."
                    };
                if (contratoCiotAgregado.DeclaracaoCiot == null)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = "Não foi encontrado nenhuma declaração de CIOT para o contrato informado."
                    };
                if (contratoCiotAgregado.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = "Não é possível retificar um contrato já encerrado"
                    };

                var ciotResponse = RetificarCiotInternal(contratoCiotAgregadoService, contratoCiotAgregado, veiculos);
                if (!ciotResponse.Sucesso.HasValue || !ciotResponse.Sucesso.Value)
                {
                    return new DeclararCiotResult
                    {
                        Resultado = EResultadoDeclaracaoCiot.Erro,
                        Declarado = false,
                        Mensagem = ciotResponse.Excecao?.Mensagem
                    };
                }

                return contratoCiotAgregado.DeclaracaoCiot.GetCiotResultV2();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, $"Erro ao encerrar CIOT para viagem");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = msg
                };
            }
        }

        public ConsultarSituacaoCiotReponse ConsultarSituacaoCiot(ConsultarSituacaoCiotRequest ciotRequest)
        {
            if (_ciotAuthRequestParams == null)
                throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
            
            return _ciotExternalRepository.ConsultarSituacaoCiot(ciotRequest);
        }
        
        /// <summary>
        /// Executa o encerramento do CIOT
        /// </summary>
        private EncerrarOperacaoTransporteReponse EncerrarCiotInternal(ContratoCiotAgregado contratoCiotAgregado,
            List<Viagem> viagens)
        {
            try
            {
                if (_ciotAuthRequestParams == null)
                    throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
                
                var request = new EncerrarOperacaoTransporteRequest
                {
                    Ciot = contratoCiotAgregado.DeclaracaoCiot.Ciot,
                    SenhaAlteracao = contratoCiotAgregado.DeclaracaoCiot.Senha,
                };

                #region Viagens
                var viagensRequestList =
                    new ObservableCollection<EncerrarOperacaoTransporteViagemRequest>();
                
                viagens.GroupBy(v => new {IdCidadeOrigem = v.ClienteOrigem.IdCidade, IdCidadeDestino = v.ClienteDestino.IdCidade, v.NaturezaCarga})
                    .ToList().ForEach(grp =>
                    {
                        var viagensAgrupadas = viagens.Where(x =>
                            x.ClienteOrigem.IdCidade == grp.Key.IdCidadeOrigem &&
                            x.ClienteDestino.IdCidade == grp.Key.IdCidadeDestino &&
                            x.NaturezaCarga == grp.Key.NaturezaCarga).ToList();

                        var pesoCarga = Math.Floor(viagensAgrupadas.Sum(x => x.PesoSaida) ?? 0);
                        
                        viagensRequestList.Add(new EncerrarOperacaoTransporteViagemRequest
                        {
                            CodigoMunicipioOrigem = viagensAgrupadas.Where(v => v.ClienteOrigem?.Cidade != null)
                                .Select(v => v.ClienteOrigem.Cidade.IBGE).First(),
                            CodigoMunicipioDestino = viagensAgrupadas.Where(v => v.ClienteDestino?.Cidade != null)
                                .Select(v => v.ClienteDestino.Cidade.IBGE).First(),
                            CodigoNaturezaCarga = grp.Key.NaturezaCarga.ToString(),
                            QuantidadeViagens = viagensAgrupadas.Count(),
                            PesoCarga = pesoCarga > 1 ? pesoCarga : 1,
                        });
                    });
                
                request.ViagensOperacaoTransporte = viagensRequestList;

                #endregion

                #region Valores efetivos

                var calculoValoresViagem = CalcularValoresViagem(viagens);

                var somaPesoViagens = request.ViagensOperacaoTransporte.Sum(c => c.PesoCarga) ?? 1;
                
                request.PesoCarga = somaPesoViagens;
                request.ValoresEfetivos = new ValoresFreteRequest
                {
                    TotalPegadio = calculoValoresViagem.ValorPedagioTotal,
                    TotalImposto = calculoValoresViagem.ValorTotalImpostos,
                    ValorSESTSENAT = calculoValoresViagem.ValorSESTSENAT,
                    ValorINSS = calculoValoresViagem.ValorINSS,
                    ValorIRRF = calculoValoresViagem.ValorIRRF,
                    ValorCombustivel = calculoValoresViagem.ValorCombustivel,
                    ValorDespesas = calculoValoresViagem.ValorDespesas,
                    ValorFrete = calculoValoresViagem.ValorFrete,
                    ValorFretePago = calculoValoresViagem.ValorFretePago,
                    ValorTarifas = calculoValoresViagem.ValorTarifas,
                    QuantidadeTarifas = calculoValoresViagem.QtdTarifas
                };

                #endregion

                // Declarar CIOT
                var result = _ciotExternalRepository.EncerrarOperacaoTransporte(request);
                if (!result.Sucesso.GetValueOrDefault(false))
                {
                    if (result.Excecao?.Codigo == DeclaracaoCiotCodigos.OperacaoJaEncerrada)
                        result.Sucesso = true;
                    else
                        return new EncerrarOperacaoTransporteReponse
                        {
                            Sucesso = false,
                            Excecao = new ExcecaoResponse
                            {
                                Mensagem = result.ExceptionMessage ?? 
                                           "Erro ao encerrar CIOT: " +
                                           result.Excecao?.Codigo + " - " +
                                           result.Excecao?.Mensagem
                            }
                        };
                }

                if (contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                    AtualizarSituacaoCiot(contratoCiotAgregado.IdDeclaracaoCiot.Value, EStatusDeclaracaoCiot.Encerrado);

                var resultMapped = Mapper.Map<EncerrarOperacaoTransporteReponse>(result);
//                resultMapped.TipoViagem = request.Frete.TipoViagem.Value;
//                if (resultMapped.Veiculos != null && resultMapped.Veiculos.Count > 0)
//                    resultMapped.Veiculos = request.Veiculos.ToList();

                return resultMapped;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, $"Erro ao encerrar CIOT da viagem");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao declarar CIOT: " + e.Message;

                return new EncerrarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = msg,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    }
                };
            }
        }
        
        private void AtualizarSituacaoCiot(int idDeclaracaoCiot, EStatusDeclaracaoCiot situacaoCiot)
        {                        
            var declaracaoCiotExistente = Repository.Get(idDeclaracaoCiot);                
            if (declaracaoCiotExistente != null)
            {
                declaracaoCiotExistente.Status = situacaoCiot;
                Repository.Update(declaracaoCiotExistente);
            }
        }
        
        public void EnviarEmail(NotificacaoEmailApiRequest request, string cnpjEmpresa, int administradora)
        {
            GerarToken(cnpjEmpresa);
            new InfraExternalRepository(_ciotAuthRequestParams.Token, _ciotAuthRequestParams.AuditUserDoc, null).EnviarEmail(request);
        }
        
        public ValidationResult AtualizarVeiculosContratoAgregado(IContratoCiotAgregadoService contratoCiotAgregadoService, ContratoCiotAgregado contratoCiotAgregado,
            ICollection<VeiculoModelAgregado> veiculos)
        {
            return contratoCiotAgregadoService.AtualizarVeiculosContratoAgregado(contratoCiotAgregado, veiculos);
        }
        
        private RetificarOperacaoTransporteReponse RetificarCiotInternal(IContratoCiotAgregadoService contratoCiotAgregadoService, ContratoCiotAgregado contratoCiotAgregado,
            ICollection<VeiculoModelAgregado> veiculos)
        {
            try
            {
                if (_ciotAuthRequestParams == null)
                    throw new Exception("Necessário gerar o Token para o CIOT. Método CiotService.GerarToken");
                
                var request = new RetificarOperacaoTransporteRequest()
                {
                    Ciot = contratoCiotAgregado.DeclaracaoCiot.Ciot,
                    SenhaAlteracao = contratoCiotAgregado.DeclaracaoCiot.Senha,
                    ValorTarifas = contratoCiotAgregado.ValorTarifas,
                    QuantidadeTarifas = contratoCiotAgregado.QuantidadeTarifas,
                    ValorCombustivel = contratoCiotAgregado.ValorCombustivel,
                    ValorPedagio = contratoCiotAgregado.ValorPedagio,
                    ValorFrete = contratoCiotAgregado.ValorFrete,
                    ValorFretePago = contratoCiotAgregado.ValorFretePago, 
                };

                if (veiculos?.Any(v => v.InclusoCiot) == true)
                {
                    request.Veiculos = new ObservableCollection<VeiculoRequest>();
                    var veiculoRepository = _veiculoRepository;
                    
                    veiculos.Where(v => v.InclusoCiot).ForEach(v =>
                    {
                        var rntrcPlacaCavaloCadastrada = veiculoRepository.GetRntrcProprietarioVeiculo(v.IdVeiculo);
                        request.Veiculos.Add(new VeiculoRequest
                        {
                            Placa = v.Placa.RemoveSpecialCaracter(),
                            Rntrc = !string.IsNullOrWhiteSpace(rntrcPlacaCavaloCadastrada) ? rntrcPlacaCavaloCadastrada : contratoCiotAgregado.Proprietario.RNTRC
                        });
                    });
                }

                // Declarar CIOT
                var result = _ciotExternalRepository.RetificarOperacaoTransporte(request);
                if (!result.Sucesso.GetValueOrDefault(false))
                    return new RetificarOperacaoTransporteReponse
                    {
                        Sucesso = false,
                        Excecao = new ExcecaoResponse
                        {
                            Mensagem = result.ExceptionMessage ?? result.Excecao?.Mensagem
                        }
                    };

                if (contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                    AtualizarVeiculosContratoAgregado(contratoCiotAgregadoService, contratoCiotAgregado, veiculos);

                var resultMapped = Mapper.Map<RetificarOperacaoTransporteReponse>(result);
                return resultMapped;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, $"Erro ao retificar CIOT da viagem");

                var msg = e is CiotAtsException
                    ? e.Message
                    : "Erro ao retificar CIOT: " + e.Message;

                return new RetificarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = msg,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    }
                };
            }
        }
        
        public DadosAtualizacaoCiotDto GetDeclaracaoCiotIncludeViagens(int idDeclaracaoCiot)
        {
            return Repository.GetDeclaracaoCiotIncludeViagens(idDeclaracaoCiot);
        }
        
        public AtualizarCiotResponse AtualizarCiot(AtualizarCiotRequest request)
        {
            return _ciotExternalRepository.AtualizarOperacaoTransorte(request);
        }
    }
}
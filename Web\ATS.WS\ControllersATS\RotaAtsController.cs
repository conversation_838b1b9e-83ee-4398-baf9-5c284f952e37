﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class RotaAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IRotaApp _rotaApp;

        public RotaAtsController(IUserIdentity userIdentity, IEstabelecimentoApp estabelecimentoApp, IRotaApp rotaApp)
        {
            _userIdentity = userIdentity;
            _estabelecimentoApp = estabelecimentoApp;
            _rotaApp = rotaApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, bool? listarInativos, 
            string descricao, int Take, int Page, OrderFilters Order, List<QueryFilters> Filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && _userIdentity.IdEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa.Value;

                return ResponderSucesso(_rotaApp.ConsultaGrid(idEmpresa, listarInativos, descricao, Take, Page, Order, Filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int IdRota)
        {
            try
            {
                _rotaApp.Inativar(IdRota);

                return ResponderSucesso("Rota inativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Excluir(int IdRota)
        {
            try
            {
                _rotaApp.Excluir(IdRota);                
                return ResponderSucesso("Rota excluida com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int IdRota)
        {
            try
            {
                _rotaApp.Reativar(IdRota);

                return ResponderSucesso("Rota reativa   da com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }


        /*[HttpPost]
        [Autorizar]
        public JsonResult CadastrarAtualizar(RotaModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");

                if (@params.IdRota.HasValue)
                {
                    _rotaApp.Atualizar(@params);
                    return ResponderSucesso($"Rota atualizada com sucesso!", @params.IdRota);
                }
                else
                {
                    var idRota = _rotaApp.Cadastrar(@params);
                    return ResponderSucesso($"Rota inserida com sucesso!", idRota);
                }
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return ResponderErro(e);
            }
        }*/

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int IdRota)
        {
            try
            {
                var rota = _rotaApp.Get(IdRota);                
                /*var desvios = new List<DesvioTempoRota>();                
                foreach (var item in rota.Desvios) desvios.Add(new DesvioTempoRota
                {
                    Endereco = item.Endereco,
                    Latitude = item.Latitude,
                    Longitude = item.Longitude,
                    IdRotaDesvio = item.IdRotaDesvio,
                    Parada = item.Parada
                });*/
                                
                //var ocorrencias = new List<OcorrenciaRotaModel>();
                //var ocorrenciasMapa = new List<OcorrenciaRotaModel>();
                if (rota.RotaTrajeto != null && rota.RotaTrajeto.Any())
                {
                    foreach (var trajeto in rota.RotaTrajeto)
                    {
                        decimal raio = trajeto.DistanciaViagemMetros / 1000;
                        raio = raio + raio * Convert.ToDecimal(0.3);
                        if (raio == 0) raio = 5;
                        if (!trajeto.Latitude.HasValue || !trajeto.Longitude.HasValue) continue;
                        //var rotaOcorrencias = new RotaOcorrenciaApp().GetPorRotaOcorrenciaitem(trajeto.Latitude.Value, trajeto.Longitude.Value, raio, true, IdRota);
                        //var rotaOcorrenciasMapa = new RotaOcorrenciaApp().GetPorRota(trajeto.Latitude.Value, trajeto.Longitude.Value, raio, true);
                        /*foreach (var item in rotaOcorrencias)
                        {
                            if (!item.Latitude.HasValue || !item.Longitude.HasValue) continue;
                            
                            var existe = ocorrencias
                                .Any(o => 
                                    o.IdRotaOcorrencia == item.IdRotaOcorrencia ||
                                    o.IdTipoOcorrencia == item.IdTipoOcorrencia && 
                                    o.Latitude == item.Latitude &&
                                    o.Longitude == item.Longitude);

                            if (!existe)
                            {
                                
                                ocorrencias.Add(new OcorrenciaRotaModel
                                {
                                    Latitude = item.Latitude.Value,
                                    Longitude = item.Longitude.Value,
                                    IdTipoOcorrencia = item.IdTipoOcorrencia,
                                    Descricao = item.Descricao,
                                    Referencia = item.Referencia,
                                    IdRotaOcorrencia = item.IdRotaOcorrencia,
                                    Selecionada = rota.RotaOcorrenciaItems != null 
                                                  && rota.RotaOcorrenciaItems.Any(x => x.IdRotaOcorrencia == item.IdRotaOcorrencia) 
                                                  && (item.DataFim == null || item.DataFim >= DateTime.Now)
                                                  && (item.DataInicio == null || item.DataInicio <= DateTime.Now)
                                });                                      
                            }
                                                                                                                                           
                        }*/
                        
                        /*foreach (var item in rotaOcorrenciasMapa)
                        {
                            if (!item.Latitude.HasValue || !item.Longitude.HasValue) continue;
                            
                            var existe = ocorrenciasMapa
                                .Any(o => 
                                    o.IdRotaOcorrencia == item.IdRotaOcorrencia ||
                                    o.IdTipoOcorrencia == item.IdTipoOcorrencia && 
                                    o.Latitude == item.Latitude &&
                                    o.Longitude == item.Longitude);

                            if (!existe)
                            {
                                
                                ocorrenciasMapa.Add(new OcorrenciaRotaModel
                                {
                                    Latitude = item.Latitude.Value,
                                    Longitude = item.Longitude.Value,
                                    IdTipoOcorrencia = item.IdTipoOcorrencia,
                                    Descricao = item.Descricao,
                                    Referencia = item.Referencia,
                                    IdRotaOcorrencia = item.IdRotaOcorrencia,
                                    Selecionada = rota.RotaOcorrenciaItems != null 
                                                  && rota.RotaOcorrenciaItems.Any(x => x.IdRotaOcorrencia == item.IdRotaOcorrencia) 
                                                  && (item.DataFim == null || item.DataFim >= DateTime.Now)
                                                  && (item.DataInicio == null || item.DataInicio <= DateTime.Now)
                                });                                      
                            }
                                                                                                                                           
                        }*/
                    }
                }

                //var filiaisRota = new List<FilialRotaModel>();
                /*foreach (var filial in filiais)
                {
                    filiaisRota.Add(new FilialRotaModel
                    {
                        Latitude = filial.Latitude.GetValueOrDefault(0),
                        Longitude = filial.Longitude.GetValueOrDefault(0)
                    });
                }*/
                
                //var estabalecimentosRota = new List<EstabelecimentoRotaModel>();                
                var estabelecimentos = _estabelecimentoApp.EstabelecimentosDaRota(rota.IdRota);
                if (estabelecimentos != null && estabelecimentos.Any())
                {
                    foreach (var estabelecimento in estabelecimentos)
                    {                        
                        if (estabelecimento != null)
                        {
                            /*estabalecimentosRota.Add(new EstabelecimentoRotaModel
                            {
                                Latitude = estabelecimento.Latitude.GetValueOrDefault(0),
                                Longitude = estabelecimento.Longitude.GetValueOrDefault(0),
                                Bairro = estabelecimento.Bairro,
                                Cidade = estabelecimento.Cidade.Nome,
                                Estado = estabelecimento.Estado.Sigla,
                                Descricao = estabelecimento.Descricao,
                                Logradouro = estabelecimento.Logradouro,
                                TokenIcone = estabelecimento.TipoEstabelecimento.Icone.TokenIcone
                            });*/
                        }                            
                    }
                }                                

                //var tiposOcorrencias = new List<TipoOcorrenciaModel>();
                /*foreach (var item in  ocorrenciasMapa.Select(o => o.IdTipoOcorrencia))
                {
                    /*var tipoOcorrenciaRotograma = new TipoOcorrenciaRotogramaApp().Get(item);
                    var icone = !string.IsNullOrWhiteSpace(tipoOcorrenciaRotograma?.Icone?.TokenIcone)
                        ? _dataMediaServerApp.GetMedia(tipoOcorrenciaRotograma?.Icone?.TokenIcone).Data
                        : string.Empty;

                    tiposOcorrencias.Add(new TipoOcorrenciaModel { Icone = icone, IdTipoOcorrencia = item, IconeDescricao = tipoOcorrenciaRotograma?.Descricao });#1#
                }*/

                //ponto mapa
                /*tiposOcorrencias.Add(new TipoOcorrenciaModel {
                    Icone = "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",
                    IdTipoOcorrencia = 0,
                    IconeDescricao = "Endereço" });*/

                /*RotaModel r = new RotaModel
                {
                    Descricao = rota.Descricao,
                    IdRota = rota.IdRota,
                    IdEmpresa = rota.IdEmpresa,
                    RazaoSocialEmpresa = rota.Empresa.RazaoSocial,
                    Inicial = new LocLatLngEnd
                    {
                        Endereco = rota.From,
                        Latitude = rota.FromLatitude,
                        Longitude = rota.FromLongitude
                    },
                    Final = new LocLatLngEnd
                    {
                        Endereco = rota.To,
                        Latitude = rota.ToLatitude,
                        Longitude = rota.ToLongitude
                    },
                    TotalKm = rota.TotalKm,
                    TotalTempo = new TotalTempoRotaModel
                    {
                        StrValue = rota.TotalTempoViagem,
                        Type = "segundos",
                        Value = rota.TotalSegundos
                    },
                    Ocorrencias = ocorrencias,
                    OcorrenciasMapa = ocorrenciasMapa,
                    TiposOcorrencia = tiposOcorrencias,      
                    Filiais = filiaisRota,
                    Estabelecimentos = estabalecimentosRota
                };*/


                return ResponderSucesso(new {});
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ClienteProdutoEspecieMap : EntityTypeConfiguration<ClienteProdutoEspecie>
    {
        public ClienteProdutoEspecieMap()
        {
            ToTable("CLIENTE_PRODUTO_ESPECIE");

            HasKey(x => new { x.IdClienteProdutoEspecie, x.IdCliente });

            Property(t => t.IdClienteProdutoEspecie)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(x => x.Cliente)
                .WithMany(x => x.ClienteProdutoEspecie)
                .HasForeignKey(x => x.IdCliente);

            HasRequired(x => x.Produto)
                .WithMany(x => x.ClienteProdutoEspecie)
                .HasForeignKey(x => x.IdProduto);

            HasRequired(x => x.Especie)
                .WithMany(x => x.ClienteProdutoEspecie)
                .HasForeignKey(x => x.IdEspecie);
        }
    }
}

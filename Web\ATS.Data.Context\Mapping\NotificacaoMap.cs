using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class NotificacaoMap : EntityTypeConfiguration<Notificacao>
    {
        public NotificacaoMap()
        {
            ToTable("NOTIFICACAO");

            HasKey(t => t.IdNotificacao);

            Property(t => t.IdNotificacao)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Tipo)
                .HasColumnAnnotation("defaultValue", 0)
                .IsRequired();

            Property(t => t.<PERSON>)
                .IsRequired()
                .HasColumnType("text")
                .IsMaxLength();

            Property(t => t.DataHoraEnvio)
                .IsRequired();

            HasRequired(t => t.usuario)
                .WithMany(t => t.Notificacoes)
                .HasForeignKey(t => t.IdUsuario);

            HasRequired(x => x.TipoNotificacao)
                .WithMany(x => x.Notificacao)
                .HasForeignKey(x => x.IdTipoNotificacao);

        }
    }
}
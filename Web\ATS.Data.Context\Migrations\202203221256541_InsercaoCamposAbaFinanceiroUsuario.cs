﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class InsercaoCamposAbaFinanceiroUsuario : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.BLOQUEIO_FINANCEIRO_TIPO",
                c => new
                    {
                        idbloqueiofinanceirotipo = c.Int(nullable: false),
                        descricao = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idbloqueiofinanceirotipo);
            
            CreateTable(
                "dbo.USUARIO_PERMISSAO_FINANCEIRO",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idbloqueiogestortipo = c.Int(nullable: false),
                        desbloquearfinanceiro = c.<PERSON>(nullable: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idbloqueiogestortipo })
                .ForeignKey("dbo.BLOQUEIO_FINANCEIRO_TIPO", t => t.idbloqueiogestortipo)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idbloqueiogestortipo, name: "IX_IdBloqueioGestorTipo");
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.USUARIO_PERMISSAO_FINANCEIRO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_PERMISSAO_FINANCEIRO", "idbloqueiogestortipo", "dbo.BLOQUEIO_FINANCEIRO_TIPO");
            DropIndex("dbo.USUARIO_PERMISSAO_FINANCEIRO", "IX_IdBloqueioGestorTipo");
            DropIndex("dbo.USUARIO_PERMISSAO_FINANCEIRO", "IX_IdUsuario");
            DropTable("dbo.USUARIO_PERMISSAO_FINANCEIRO");
            DropTable("dbo.BLOQUEIO_FINANCEIRO_TIPO");
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.AtendimentoPortador;
using AutoMapper;

namespace ATS.Domain.Service
{
    public class AtendimentoPortadorService : IAtendimentoPortadorService
    {
        private readonly IAtendimentoPortadorRepository _repository;
        private readonly IAtendimentoPortadorTramiteRepository _tramiteRepository;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IAtendimentoPortadorTramiteRepository _atendimentoPortadorTramiteRepository;
        private readonly IAtendimentoDapper _atendimentoDapper;

        public AtendimentoPortadorService(IAtendimentoPortadorRepository repository, IAtendimentoPortadorTramiteRepository tramiteRepository, 
            IParametrosEmpresaService parametrosEmpresaService, IEmpresaRepository empresaRepository, IParametrosUsuarioService parametrosUsuarioService,
            IAtendimentoPortadorTramiteRepository atendimentoPortadorTramiteRepository, IAtendimentoDapper atendimentoDapper)
        {
            _repository = repository;
            _tramiteRepository = tramiteRepository;
            _parametrosEmpresaService = parametrosEmpresaService;
            _empresaRepository = empresaRepository;
            _parametrosUsuarioService = parametrosUsuarioService;
            _atendimentoPortadorTramiteRepository = atendimentoPortadorTramiteRepository;
            _atendimentoDapper = atendimentoDapper;
        }

        public AtendimentoPortadorResultModel IniciarAtendimento(int idUsuario, int? idEmpresa)
        {
            var atendimentoPendente = ConsultaAtendimentoPendente(idUsuario, idEmpresa);
            if (atendimentoPendente.AtendimentoPortador != null)
                return atendimentoPendente;
            
            var atendimentoPortador = new AtendimentoPortador
            {
                IdUsuario = idUsuario,
                IdEmpresa = idEmpresa
            };
            
            atendimentoPortador.Abrir(_repository);
            
            InserirTramite(new AtendimentoPortadorTramiteRequest()
            {
                IdAtendimentoPortador = atendimentoPortador.IdAtendimentoPortador,
                Tipo = ETipoTramiteAtendimentoPortador.InicioAtendimento,
                Operacao = "Atendimento iniciado"
            });
            
            return new AtendimentoPortadorResultModel
            {
                Sucesso = true,
                Mensagem = "Atendimento iniciado com sucesso!",
                AtendimentoPortador = new {atendimentoPortador.Protocolo}
            };
        }

        public void AtualizaDocumento(string documento, int idUsuario)
        {
            var atendimentoPortador = _repository.Find(x => x.IdUsuario == idUsuario 
                                                            && x.Status == EStatusAtendimentoPortador.Pendente 
                                                            && string.IsNullOrEmpty(x.CNPJCPF))
                .FirstOrDefault();

            if (atendimentoPortador == null)
                return;

            atendimentoPortador.CNPJCPF = documento;
            
            _repository.Update(atendimentoPortador);
            
            InserirTramite(new AtendimentoPortadorTramiteRequest()
            {
                IdAtendimentoPortador = atendimentoPortador.IdAtendimentoPortador,
                Tipo = ETipoTramiteAtendimentoPortador.ConfirmouDocumento,
                Operacao = $"Atendimento confirmado para o documento {documento}"
            });
        }

        public AtendimentoPortadorResultModel Finalizar(FinalizarAtendimentoDTORequest request, int idUsuario)
        {
            var atendimentoPortador = _repository.Where(x => x.IdUsuario == idUsuario && x.Status == EStatusAtendimentoPortador.Pendente)
                .OrderByDescending(x => x.DataInicio).First();
            
            if(atendimentoPortador == null)
                return new AtendimentoPortadorResultModel
                {
                    Sucesso = false,
                    Mensagem = "O atendimento já está finalizado.",
                    AtendimentoPortador = null
                };
            
            atendimentoPortador.Finalizar(_repository, request.Observacao, request.IdMotivoFinalizacaoAtendimento);
            
            InserirTramite(new AtendimentoPortadorTramiteRequest()
            {
                IdAtendimentoPortador = atendimentoPortador.IdAtendimentoPortador,
                Tipo = ETipoTramiteAtendimentoPortador.FinalAtendimento,
                Operacao = string.Concat($"Atendimento finalizado com o motivo '{request.MotivoFinalizacaoAtendimento}'", 
                           string.IsNullOrEmpty(request.Observacao) ?  string.Empty : $" com a observação '{request.Observacao}'"),
                IdMotivo = request.IdMotivoFinalizacaoAtendimento
            });
            
            return new AtendimentoPortadorResultModel
            {
                Sucesso = true,
                Mensagem = "Atendimento finalizado com sucesso!",
                AtendimentoPortador = atendimentoPortador
            };
        }

        public AtendimentoPortadorResultModel ConsultaAtendimentoPendente(int idUsuario, int? idEmpresa)
        {
            var atendimentoPendente = _repository.Find(x => x.IdUsuario == idUsuario && x.Status == EStatusAtendimentoPortador.Pendente);

            if (atendimentoPendente.Any())
            {
                var atendimentoPortador = atendimentoPendente.First();
                
                InserirTramite(new AtendimentoPortadorTramiteRequest()
                {
                    IdAtendimentoPortador = atendimentoPortador.IdAtendimentoPortador,
                    Tipo = ETipoTramiteAtendimentoPortador.AtendimentoRetomado,
                    Operacao = $"Atendimento já iniciado para este atendente foi retomado"
                });

                return new AtendimentoPortadorResultModel
                {
                    Sucesso = true,
                    Mensagem = "Foi retomado um atendimento pendente",
                    AtendimentoPortador = new { atendimentoPortador.CNPJCPF, atendimentoPortador.Protocolo, atendimentoPortador.DataInicio }
                };
            }
            else
                return new AtendimentoPortadorResultModel
                {
                    Sucesso = true,
                    Mensagem = "Não foi encontrado um atendimento pendente",
                    AtendimentoPortador = null
                };
        }

        public PermissoesEmpresaAtendimentoPortadorResultModel ConsultaPermissoesUsuario(int idUsuario, EPerfil perfil, int? idEmpresa)
        {
            if (perfil != EPerfil.Empresa || !idEmpresa.HasValue)
                return new PermissoesEmpresaAtendimentoPortadorResultModel("Funcionalidade disponível apenas para usuários de perfil empresa.");


            var tokenMicroServico = _parametrosEmpresaService.GetTokenMicroServicoCentralAtendimento(idEmpresa.Value);
            if (string.IsNullOrWhiteSpace(tokenMicroServico))
                tokenMicroServico = _empresaRepository.GetTokenMicroServices(idEmpresa.Value);
            
            if(string.IsNullOrWhiteSpace(tokenMicroServico))
                return new PermissoesEmpresaAtendimentoPortadorResultModel("Empresa não configurada para realizar atendimentos. Favor entrar em contato com o suporte para maiores detalhes");
            
            var permissoes = _parametrosUsuarioService.GetPermissoesAtendimentoCartao(idUsuario, idEmpresa.Value);

            return new PermissoesEmpresaAtendimentoPortadorResultModel { Permissoes = permissoes };
        }

//        public int ConsultaQuantidadeAtendimentoPortador(DateTime dataInicial, DateTime dataFinal)
//        {
//            var count = _checkListRepository
//                .Find(x => x.DataHoraInicio >= dataInicial && x.DataHoraInicio <= dataFinal).Count();
//
//            return count;
//        }
  
        public List<ItensGraficoPorEmpresa> ConsultaQuantidadeAtendimentoPortador(DateTime dataInicial, DateTime dataFinal, DateTime? dataSelecinada)
        {
            var primeiraHoraDoDiaSelecionado = dataSelecinada;
            var ultimaHoraDoDiaSelecionado = dataSelecinada;
            
            //Datas para os indicadores com filtro do dia selecinado.
            var dataSelecionadaDia = Convert.ToDateTime(dataSelecinada);

            if (dataSelecinada != null)
            {
                primeiraHoraDoDiaSelecionado = new DateTime(dataSelecionadaDia.Year, dataSelecionadaDia.Month, dataSelecionadaDia.Day, 0, 0, 0);
                ultimaHoraDoDiaSelecionado = new DateTime(dataSelecionadaDia.Year, dataSelecionadaDia.Month, dataSelecionadaDia.Day, 23, 59, 59);
            }
            
            //Quantia de Atendimentos do dia do filtro
            var atendimentosDia = _atendimentoPortadorTramiteRepository
                .Find(x => x.AtendimentoPortador.DataInicio >= (primeiraHoraDoDiaSelecionado ?? DateTime.Today) &&
                           x.AtendimentoPortador.DataInicio <= (ultimaHoraDoDiaSelecionado ?? DateTime.Now))
                .Include(x => x.AtendimentoPortador);
            
            var atendimentosTramites = _atendimentoPortadorTramiteRepository
                .Find(x => x.AtendimentoPortador.DataInicio >= dataInicial &&
                           x.AtendimentoPortador.DataInicio <= dataFinal)
                .Include(x => x.AtendimentoPortador);
            
            var arrayAtendimentosTramites = atendimentosTramites.GroupBy(x => x.Tipo).ToList()
                .Select(c => new ItensGraficoPorEmpresa
                {
                    QuantidadeItensPorTipo = c.Count(),
                    QuantidadeTotalItens = atendimentosTramites.Count(),
                    QuantidadeItensPorDia = atendimentosDia.Count(),
                    DescricaoTipoAtendimento = c.Key.GetDescription()
                }).ToList();
            
            return arrayAtendimentosTramites;
        }

        public IQueryable<AtendimentoPortador> Find(Expression<Func<AtendimentoPortador, bool>> predicate, bool @readonly = false)
        {
            return _repository.Find(predicate, @readonly);
        }

        public ConsultarAtendimentoResponseDTO ConsultarAtendimento(ConsultarAtendimentoRequestDTO request, int Take, int Page,
            OrderFilters Order, List<QueryFilters> filters, int idEmpresa)
        {
            var atendimentoProtocolo = _repository.Where(x => x.IdEmpresa == idEmpresa);
            
            if (request.DataInicio != default(DateTime) || request.DataFim != default(DateTime))
                atendimentoProtocolo = atendimentoProtocolo.Where(x => x.DataInicio >= request.DataInicio && x.DataFinal <= request.DataFim);
                
            if (request.Protocolo != null)
               atendimentoProtocolo = atendimentoProtocolo.Where(x => x.Protocolo == request.Protocolo);

            if (request.Portador != 0)
                atendimentoProtocolo = atendimentoProtocolo.Where(x => x.IdAtendimentoPortador == request.Portador);

            if (request.Atendente != 0)
                atendimentoProtocolo = atendimentoProtocolo.Where(x => x.IdUsuario == request.Atendente);
            
            var lista = atendimentoProtocolo.Select(c => new
            {
                c.DataInicio,
                c.DataFinal,
                c.Protocolo,
                DescricaoMotivo = c.Motivo.Descricao,
                IdUsuario = c.Usuario.IdUsuario,
                NomeUsuario = c.Usuario.Nome,
                c.AtendimentoPortadorTramite,
                c.IdAtendimentoPortador,
                c.IdMotivoFinalizacaoAtendimento,
                c.CNPJCPF,
                c.Observacao,
                c.Status
            }).ToList();

            var queryLista = lista.AsQueryable();

            var resultado = queryLista.Select(c => new ListaAtendimentoDTO
            {
                DataInicio = c.DataInicio.ToString("dd/MM/yyyy HH:mm:ss"),
                DataFinal = c.DataFinal.Value.ToString("dd/MM/yyyy HH:mm:ss"),
                DataInicioFT = c.DataInicio,
                DataFinalFT = c.DataFinal,
                IdAtendimentoPortador = c.IdAtendimentoPortador, 
                Cnpjcpf  = c.CNPJCPF,
                Observacao = c.Observacao,
                IdUsuario = c.IdUsuario,
                UsuarioNome = c.NomeUsuario,
                Status = c.Status,
                StatusNome = EnumHelpers.GetDescription(c.Status),
                Protocolo = c.Protocolo,
                IdMotivoFinalizacaoAtendimento = c.IdMotivoFinalizacaoAtendimento,
                DescricaoMotivoAtendimento = c.DescricaoMotivo,
                TotalData = (c.DataFinal - c.DataInicio).Value.TotalMilliseconds
            });

            var query = resultado.AsQueryable();
            
            query = query.AplicarFiltrosDinamicos(filters);
            
            query = string.IsNullOrWhiteSpace(Order?.Campo)
                ? query.OrderByDescending(x => x.IdAtendimentoPortador)
                : query.OrderBy($"{Order.Campo} {Order.Operador.DescriptionAttr()}");
            
            return new ConsultarAtendimentoResponseDTO
            {
               ConsultaAtendimento = query.ToList()
            };
        }
        
        public ConsultarHistoricoAtendimentoResponseDTO ConsultarHistoricoAtendimento(ConsultarHistoricoAtendimentoRequestDTO request, int Take, int Page,
            OrderFilters Order, List<QueryFilters> filters, int idEmpresa)
        {
            var atendimentoProtocolo = _repository.Where(x => x.IdEmpresa == idEmpresa && x.IdAtendimentoPortador == request.Portador).FirstOrDefault();

            var historicos = _tramiteRepository
                .Where(x => x.IdAtendimentoPortador == atendimentoProtocolo.IdAtendimentoPortador);

            var lista = historicos.Select(c => new
            {
                c.Motivo,
                c.IdMotivo,
                c.IdAtendimentoPortador,
                c.Operacao,
                c.Permanente,
                c.Sequencial,
                c.Tipo,
                c.AtendimentoPortador,
                c.DataTramite,
                c.IdentificadorCartao,
                c.ProdutoCartao
            }).ToList();

            var queryLista = lista.AsQueryable();

            var resultado = queryLista.Select(c => new ListaHistoricoAtendimentoDTO
            {
                IdAtendimentoPortador = c.IdAtendimentoPortador,
                Sequencial = c.Sequencial,
                DataTramite = c.DataTramite.ToString("dd/MM/yyyy HH:mm:ss"),
                DataTramiteFT = c.DataTramite,
                IdentificadorCartao = c.IdentificadorCartao,
                ProdutoCartao = c.ProdutoCartao,
                Operacao = c.Operacao,
                Tipo = c.Tipo,
                IdMotivo = c.IdMotivo,
                Permanente = c.Permanente
            }).ToList();
            
            var query = resultado.AsQueryable();
            
            query = query.AplicarFiltrosDinamicos(filters);
            
            query = string.IsNullOrWhiteSpace(Order?.Campo)
                ? query.OrderByDescending(x => x.Sequencial)
                : query.OrderBy($"{Order.Campo} {Order.Operador.DescriptionAttr()}");
            
            return new ConsultarHistoricoAtendimentoResponseDTO 
            {
               ConsultaHistoricoAtendimento = query.ToList()
            };
        }

        public IList<ConsultaAtendimentoInternalResponseDTO> RelatorioConsultaAtendimentos(ConsultaRelatorioAtendimentoDTO request, int Take, int Page, OrderFilters Order,
            List<QueryFilters> filters)
        {
            var atendimentoRepository = _atendimentoDapper;

            var resultado = atendimentoRepository.GetAtendimentos(request);

            return resultado;
        }

        public void InserirTramite(AtendimentoPortadorTramiteRequest atendimentoTramitePortadorDto)
        {
            var atendimentoTramite = Mapper.Map<AtendimentoPortadorTramiteRequest, AtendimentoPortadorTramite>(atendimentoTramitePortadorDto);

            atendimentoTramite.Sequencial = _tramiteRepository.Find(x => x.IdAtendimentoPortador == atendimentoTramite.IdAtendimentoPortador)
                .OrderByDescending(x => x.Sequencial)
                .Select(x => x.Sequencial)
                .FirstOrDefault() + 1;
            
            _tramiteRepository.Add(atendimentoTramite);
        }
        
    }
}
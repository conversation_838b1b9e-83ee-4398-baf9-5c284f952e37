﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IEspecieApp : IAppBase<Especie>
    {
        List<Especie> GetEspeciesAtualizadas(DateTime dataBase);

        /// <summary>
        /// Buscar espécie
        /// </summary>
        /// <param name="id"><PERSON><PERSON><PERSON> da espécie</param>
        /// <returns>Objeto Especie</returns>
        Especie Get(int id);

        /// <summary>
        /// Adicionar a espécie a base de dados
        /// </summary>
        /// <param name="especie">Dados do espécie</param>
        /// <returns></returns>
        ValidationResult Add(Especie especie);

        /// <summary>
        /// Atualizar o registro da espécie
        /// </summary>
        /// <param name="especie">Dad<PERSON> da espécie</param>
        /// <returns></returns>
        ValidationResult Update(Especie especie);

        /// <summary>
        /// Retorna as espécies a partir dos dados de filtro
        /// </summary>
        /// <param name="descricao">Descrição da espécie</param>
        /// <returns>IQueryable de Especie</returns>
        IQueryable<Especie> Consultar(string descricao);
        

        /// <summary>
        /// Inativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser desativado</param>
        /// <returns></returns>
        ValidationResult Inativar(int idEspecie);

        /// <summary>
        /// Reativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser desativado</param>
        /// <returns></returns>
        ValidationResult Reativar(int idEspecie);

        /// <summary>
        /// Retorna todas as especies ativas
        /// </summary>
        /// <returns>IQueryable de Especie</returns>
        IQueryable<Especie> All();

        object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult AlterarStatus(int idEspecie);
        Especie GetPorDescricao(string especie);
        Especie AddReturningObject(Especie especie);
        Especie UpdateReturningObject(Especie especie);
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Models.AtendimentoPortador;

namespace ATS.WS.Models.Webservice.Request.Usuario
{
    public class CadastrarAtualizarCls
    {
        public int? idUsuario { get; set; }
        public int? idEmpresa { get; set; }
        public int? idGrupoUsuario { get; set; }
        public int? idHorario { get; set; }
        public int? idPonto { get; set; }
        public int? veiculoAnoFabricacao { get; set; }
        public int? veiculoAnoModelo { get; set; }
        public ETipoRodagem veiculoTipoRodagem { get; set; }
        public int? veiculoIdTipoCavalo { get; set; }
        public int? veiculoIdTipoCarreta { get; set; }
        public int? veiculoIdTecnologiaRastreamento { get; set; }
        public string referencia1 { get; set; }
        public string referencia2 { get; set; }
        public bool receberRelatorioOC { get; set; } = false;
        public bool VisualizaTodosChecklists { get; set; }
        public bool VisualizaChecklistTodosGrupos { get; set; }

        public int enderecoIdPais { get; set; }
        public int enderecoIdEstado { get; set; }
        public int enderecoIdCidade { get; set; }
        public int veiculoQtdeEixos { get; set; }
        public EPerfil perfil { get; set; }
        public ETipoCliente tipoCliente { get; set; }
        public ETipoPessoa tipoPessoa { get; set; }


        public string nome { get; set; }
        public string imagemPerfilB64 { get; set; }
        public string cpfcnpj { get; set; }
        public string login { get; set; }
        public string senha { get; set; }
        public string categoriaCNH { get; set; }
        public string numeroCNH { get; set; }
        public string telefone { get; set; }
        public string celular { get; set; }
        public string email { get; set; }
        public string cep { get; set; }
        public string enderecoBairro { get; set; }
        public string endereco { get; set; }
        public string enderecoNumero { get; set; }
        public string enderecoComplemento { get; set; }
        public string veiculoPlaca { get; set; }
        public string veiculoRenavam { get; set; }
        public string veiculoChassi { get; set; }
        public string veiculoMarca { get; set; }
        public string veiculoModelo { get; set; }

        public bool veiculoTracao { get; set; }
        public bool carreteiro { get; set; }
        public bool alertaPorEmail { get; set; }
        public bool alertaPopup { get; set; }
        public bool permitirResponderChat { get; set; }
        public bool receberNotificacoes { get; set; }
        public bool alertaListagem { get; set; }
        public bool gestor { get; set; }
        public bool vistoriador { get; set; }
        public bool? usuarioMasterEstabelecimento { get; set; }
        public int? idEstabelecimentoBase { get; set; }
        
        public bool EnviarSenha { get; set; } = false;

        #region Permissão de Gestão

        public PermissaoGestao[] permissoes { get; set; }
        
        #endregion

        #region Permissão de Financeiro

        public PermissaoFinanceiro[] PermissaoFinanceiro { get; set; }
        
        #endregion

        #region Tag
        public List<permissaoTag> permissaoTag { get; set; }

        #endregion


        public PermissaoCartao[] PermissaoCartao { get; set; }
        public List<int> clientesMarcados { get; set; } = new List<int>();
        public List<int> clientesDesmarcados { get; set; } = new List<int>();
        public List<int> filiaisAdicionadas { get; set; } = new List<int>();
        public List<int> filiaisRemovidas { get; set; } = new List<int>();
        public List<int> operacoesAdicionadas { get; set; } = new List<int>();
        public List<int> operacoesRemovidas { get; set; } = new List<int>();
        public List<DocsAdds> documentosAdicionados { get; set; } = new List<DocsAdds>();
        public List<int> documentosRemovidos { get; set; } = new List<int>();
        public bool VistoriadorMaster { get; set; }
        public bool? Matriz { get; set; } = false;
        public bool PermiteAlterarLimiteEmpresa { get; set; }
        public bool PermiteAlterarLimiteFilial { get; set; }
        public bool RecebeEmailGestao { get; set; }
        public bool PermiteAcessarExtratoDetalhado { get; set; }
        public PermissoesUsuarioAtendimentoPortador PermissoesAtendimentoCartao { get; set; }
        public bool AplicativoPermiteRealizarTransferenciaBancaria { get; set; }
        public bool AplicativoPermiteRealizarTransferenciaCartoes { get; set; }
        public List<PortadorLimitesValor> LimitesPortador { get; set; }
        public bool PermiteAprovarSolicitacaoAdiantamentoApp { get; set; }
        public bool PermiteSolicitarAdiantamentoApp { get; set; }
        public bool PermiteEfetuarCargaSolicitacaoAdiantamentoApp { get; set; }
        public decimal? LimiteDiarioPagamentoPix { get; set; }
        public decimal? LimiteUnitarioPagamentoPix { get; set; }
        public bool? PermitirEdicaoDadosAdministrativosPix { get; set; }
        public bool? PermiteRealizarPagamentoPix { get; set; }
        public bool? PermitirCadastroChavePix { get; set; }
        public bool? PermiteSolicitarAlteracoesLimitePix { get; set; }
        public bool? GestorAlcadasPix { get; set; }
    }
}
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Common.Request;
using Autofac;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services.ViagemServices
{
    public class ValoresViagem
    {
        private readonly ILifetimeScope _scope;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemApp _viagemApp;
        private readonly IViagemEventoApp _viagemEventoApp;

        private SrvViagem _srvViagem => _scope.Resolve<SrvViagem>();

        public ValoresViagem(ILifetimeScope scope, IVersaoAnttLazyLoadService versaoAntt, IViagemApp viagemApp,
            IViagemEventoApp viagemEventoApp)
        {
            _scope = scope;
            _versaoAntt = versaoAntt;
            _viagemApp = viagemApp;
            _viagemEventoApp = viagemEventoApp;
        }

        public AlterarValoresViagemResponseModel AlterarValoresViagem(AlterarValoresViagemRequestModel @params, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return AlterarValoresViagemV2(@params, isApi);
                case EVersaoAntt.Versao3:
                    return AlterarValoresViagemV3(@params);
                default:
                    return AlterarValoresViagemV2(@params, isApi);
            }
        }

        public void AjustarValoresViagem(Viagem viagem)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    AjustarValoresViagemV2(viagem);
                    break;
                case EVersaoAntt.Versao3:
                    AjustarValoresViagemV3(viagem);
                    break;
                default:
                    AjustarValoresViagemV2(viagem);
                    break;
            }
        }
        
        private AlterarValoresViagemResponseModel AlterarValoresViagemV2(AlterarValoresViagemRequestModel @params, bool isApi = false)
        {
            var viagemEventoApp = _viagemEventoApp;

            var viagemQuery = !string.IsNullOrWhiteSpace(@params.NumeroControle)
                ? _viagemApp.Find(v => v.NumeroControle == @params.NumeroControle)
                : _viagemApp.Find(v => v.IdViagem == @params.IdViagem);

            var viagem = viagemQuery
                .Select(v => new
                {
                    v.IdViagem,
                    v.NumeroControle,
                    v.PesoSaida,
                    v.PedagioBaixado,
                    v.HabilitarDeclaracaoCiot,
                    v.NaturezaCarga
                })
                .FirstOrDefault();

            if (viagem == null)
                return new AlterarValoresViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma viagem encontrado com a IdViagem/NumeroControle informado."
                };

            if (!string.IsNullOrWhiteSpace(@params.NumeroControle) && @params.IdViagem.HasValue &&
                (@params.NumeroControle != viagem.NumeroControle || @params.IdViagem != viagem.IdViagem))
                return new AlterarValoresViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Numero de controle {0} e id viagem {1} não pertencem ao mesmo registro."
                        .FormatEx(@params.NumeroControle, @params.IdViagem)
                };

            //
            var alterarViagem = new ViagemIntegrarRequestModel
            {
                CNPJEmpresa = @params.CNPJEmpresa,
                CNPJAplicacao = @params.CNPJAplicacao,
                Token = @params.Token,
                DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                NomeUsuarioAudit = @params.NomeUsuarioAudit,
                IdViagem = @params.IdViagem,
                NumeroControle = @params.NumeroControle,
                PesoSaida = viagem.PesoSaida,
                PedagioBaixado = viagem.PedagioBaixado,
                HabilitarDeclaracaoCiot = viagem.HabilitarDeclaracaoCiot,
                NaturezaCarga = viagem.NaturezaCarga
            };


            alterarViagem.ViagemEventos = new List<ViagemEventoIntegrarModel>();

            var numControleEventosParams = @params.ViagemEventos.Select(x => x.NumeroControle).ToList();

            var eventos = viagemEventoApp
                .Find(x => x.IdViagem == viagem.IdViagem &&
                           numControleEventosParams.Contains(x.NumeroControle))
                .Select(ve => new
                {
                    ve.TipoEventoViagem,
                    ve.HabilitarPagamentoCartao,
                    ve.HabilitarPagamentoPix,
                    ve.NumeroControle,
                    ve.IdViagemEvento,
                    ve.Status
                })
                .ToList();

            foreach (var viagemEventoParams in @params.ViagemEventos)
            {
                var dadosEventoDb = eventos
                    .FirstOrDefault(ve => ve.NumeroControle == viagemEventoParams.NumeroControle ||
                                          ve.IdViagemEvento == viagemEventoParams.IdViagemEvento);

                if (dadosEventoDb != null && !string.IsNullOrWhiteSpace(viagemEventoParams.NumeroControle) && viagemEventoParams.IdViagemEvento != null &&
                    (viagemEventoParams.NumeroControle != dadosEventoDb.NumeroControle || viagemEventoParams.IdViagemEvento != dadosEventoDb.IdViagemEvento))
                    return new AlterarValoresViagemResponseModel
                    {
                        Mensagem = "Numero de controle da parcela {0} e id viagem evento {1} não pertencem ao mesmo registro."
                            .FormatEx(viagemEventoParams.NumeroControle, viagemEventoParams.IdViagemEvento)
                    };

                if ((viagemEventoParams.TipoEvento == null || viagemEventoParams.HabilitarPagamentoCartao == null) &&
                    dadosEventoDb == null)
                    return new AlterarValoresViagemResponseModel
                    {
                        Mensagem = "Tipo de evento ou HabilitarPagamentoCartao não informado para parcela com número de controle: " +
                                   viagemEventoParams.NumeroControle
                    };

                var evento = new ViagemEventoIntegrarModel
                {
                    IdViagemEvento = viagemEventoParams.IdViagemEvento ?? dadosEventoDb?.IdViagemEvento,
                    NumeroControle = viagemEventoParams.NumeroControle ?? dadosEventoDb?.NumeroControle,
                    ValorPagamento = viagemEventoParams.ValorPagamento,
                    ValorBruto = viagemEventoParams.ValorPagamento,
                    TipoEvento = viagemEventoParams.TipoEvento ?? dadosEventoDb.TipoEventoViagem,
                    HabilitarPagamentoCartao = viagemEventoParams.HabilitarPagamentoCartao ??
                                               dadosEventoDb.HabilitarPagamentoCartao,
                    HabilitarPagamentoPix = viagemEventoParams.HabilitarPagamentoCartao == true ? false : dadosEventoDb.HabilitarPagamentoPix,
                    Status = viagemEventoParams?.Status ?? dadosEventoDb?.Status ?? EStatusViagemEvento.Aberto
                };

                alterarViagem.ViagemEventos.Add(evento);
            }

            var retorno = _srvViagem.Alterar(alterarViagem, isApi);

            return new AlterarValoresViagemResponseModel
            {
                Sucesso = retorno.Sucesso,
                Mensagem = retorno.Mensagem
            };
        }
        
        private AlterarValoresViagemResponseModel AlterarValoresViagemV3(AlterarValoresViagemRequestModel @params)
        {
            var viagemEventoApp = _viagemEventoApp;

            var viagemQuery = !string.IsNullOrWhiteSpace(@params.NumeroControle)
                ? _viagemApp.Find(v => v.NumeroControle == @params.NumeroControle)
                : _viagemApp.Find(v => v.IdViagem == @params.IdViagem);

            var viagem = viagemQuery
                .Select(v => new
                {
                    v.IdViagem,
                    v.NumeroControle,
                    v.PesoSaida,
                    v.PedagioBaixado,
                    v.HabilitarDeclaracaoCiot,
                    v.NaturezaCarga
                })
                .FirstOrDefault();

            if (viagem == null)
                return new AlterarValoresViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma viagem encontrado com a IdViagem/NumeroControle informado."
                };

            if (!string.IsNullOrWhiteSpace(@params.NumeroControle) && @params.IdViagem.HasValue &&
                (@params.NumeroControle != viagem.NumeroControle || @params.IdViagem != viagem.IdViagem))
                return new AlterarValoresViagemResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Numero de controle {0} e id viagem {1} não pertencem ao mesmo registro."
                        .FormatEx(@params.NumeroControle, @params.IdViagem)
                };

            //
            var alterarViagem = new ViagemIntegrarRequestModel
            {
                CNPJEmpresa = @params.CNPJEmpresa,
                CNPJAplicacao = @params.CNPJAplicacao,
                Token = @params.Token,
                DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                NomeUsuarioAudit = @params.NomeUsuarioAudit,
                IdViagem = @params.IdViagem,
                NumeroControle = @params.NumeroControle,
                PesoSaida = viagem.PesoSaida,
                PedagioBaixado = viagem.PedagioBaixado,
                HabilitarDeclaracaoCiot = viagem.HabilitarDeclaracaoCiot,
                NaturezaCarga = viagem.NaturezaCarga
            };


            alterarViagem.ViagemEventos = new List<ViagemEventoIntegrarModel>();

            var numControleEventosParams = @params.ViagemEventos.Select(x => x.NumeroControle).ToList();

            var eventos = viagemEventoApp
                .Find(x => x.IdViagem == viagem.IdViagem &&
                           numControleEventosParams.Contains(x.NumeroControle))
                .Select(ve => new
                {
                    ve.TipoEventoViagem,
                    ve.HabilitarPagamentoCartao,
                    ve.NumeroControle,
                    ve.IdViagemEvento,
                    ve.Status
                })
                .ToList();

            foreach (var viagemEventoParams in @params.ViagemEventos)
            {
                var dadosEventoDb = eventos
                    .FirstOrDefault(ve => ve.NumeroControle == viagemEventoParams.NumeroControle ||
                                          ve.IdViagemEvento == viagemEventoParams.IdViagemEvento);

                if (dadosEventoDb != null && !string.IsNullOrWhiteSpace(viagemEventoParams.NumeroControle) &&
                    viagemEventoParams.IdViagemEvento != null &&
                    (viagemEventoParams.NumeroControle != dadosEventoDb.NumeroControle ||
                     viagemEventoParams.IdViagemEvento != dadosEventoDb.IdViagemEvento))
                    return new AlterarValoresViagemResponseModel
                    {
                        Mensagem =
                            "Numero de controle da parcela {0} e id viagem evento {1} não pertencem ao mesmo registro."
                                .FormatEx(viagemEventoParams.NumeroControle, viagemEventoParams.IdViagemEvento)
                    };

                if ((viagemEventoParams.TipoEvento == null || viagemEventoParams.HabilitarPagamentoCartao == null) &&
                    dadosEventoDb == null)
                    return new AlterarValoresViagemResponseModel
                    {
                        Mensagem =
                            "Tipo de evento ou HabilitarPagamentoCartao não informado para parcela com número de controle: " +
                            viagemEventoParams.NumeroControle
                    };

                var evento = new ViagemEventoIntegrarModel
                {
                    IdViagemEvento = viagemEventoParams.IdViagemEvento ?? dadosEventoDb?.IdViagemEvento,
                    NumeroControle = viagemEventoParams.NumeroControle ?? dadosEventoDb?.NumeroControle,
                    ValorPagamento = viagemEventoParams.ValorPagamento,
                    ValorBruto = viagemEventoParams.ValorPagamento,
                    TipoEvento = viagemEventoParams.TipoEvento ?? dadosEventoDb.TipoEventoViagem,
                    Status = viagemEventoParams?.Status ?? dadosEventoDb?.Status ?? EStatusViagemEvento.Aberto
                };

                alterarViagem.ViagemEventos.Add(evento);
            }

            var retorno = _srvViagem.Alterar(alterarViagem);

            return new AlterarValoresViagemResponseModel
            {
                Sucesso = retorno.Sucesso,
                Mensagem = retorno.Mensagem
            };
        }

        private void AjustarValoresViagemV2(Viagem viagem)
        {
            if (!string.IsNullOrWhiteSpace(viagem.Placa))
                viagem.Placa = viagem.Placa.Replace("-", "");

            if (viagem.ViagemCarretas != null && viagem.ViagemCarretas.Any())
                foreach (var viagemCarreta in viagem.ViagemCarretas)
                    if (!string.IsNullOrWhiteSpace(viagemCarreta.Placa))
                        viagemCarreta.Placa = viagemCarreta.Placa.Replace("-", "");
        }

        private void AjustarValoresViagemV3(Viagem viagem)
        {
            if (!string.IsNullOrWhiteSpace(viagem.Placa))
                viagem.Placa = viagem.Placa.Replace("-", "");

            if (viagem.ViagemCarretas != null && viagem.ViagemCarretas.Any())
                foreach (var viagemCarreta in viagem.ViagemCarretas)
                    if (!string.IsNullOrWhiteSpace(viagemCarreta.Placa))
                        viagemCarreta.Placa = viagemCarreta.Placa.Replace("-", "");
        }
    }
}
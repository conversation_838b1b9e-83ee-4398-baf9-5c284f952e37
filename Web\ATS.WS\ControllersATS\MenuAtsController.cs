﻿using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Menu;

namespace ATS.WS.ControllersATS
{
    public class MenuAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IMenuApp _menuApp;

        public MenuAtsController(IUserIdentity userIdentity, IMenuApp menuApp)
        {
            _userIdentity = userIdentity;
            _menuApp = menuApp;
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetMenusDisponiveisPorEmpresaModulo(int idEmpresa, int idModulo, int? idGrupoUsuario)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                if (idEmpresa <= 0)
                    throw new ArgumentException("Id Empresa inválido.");

                var menus = _menuApp
                    .GetMenusPorModulo(idModulo, idGrupoUsuario ?? 0, idEmpresa, _userIdentity.Perfil)
                    .Where(i => i.IdModulo != null);

                return ResponderSucesso(menus);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idEmpresa, int idModulo,
            bool verificarAutorizacaoEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                if (idEmpresa <= 0)
                    throw new ArgumentException("Id Empresa inválido.");

                var menus = _menuApp
                    .GetMenusDisponiveisPorEmpresaModuloAutorizacao(idModulo, idEmpresa, verificarAutorizacaoEmpresa);

                return ResponderSucesso(menus);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetMenusUsuario()
        {
            try
            {
                return ResponderSucesso(_menuApp.GetMenusPermitidos(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                return ResponderSucesso(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var menus = _menuApp.ConsultaGrid(take, page, order, filters);
                return ResponderSucesso(menus);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int id)
        {
            try
            {
                var validationResult = _menuApp.AlterarStatus(id);

                return !validationResult.IsValid ? 
                    ResponderErro(validationResult.Errors.FirstOrDefault()?.Message) : 
                    ResponderSucesso("Status alterado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetMenusPai()
        {
            try
            {
                return ResponderSucesso(_menuApp.GetMenusPai());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(MenuCadastroRequest request)
        {
            try
            {
                var response = _menuApp.Cadastrar(request);
                
                return response.IsValid ? 
                    ResponderSucesso("Cadastro realizado com sucesso") :
                    ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ObterParaEditar(int id)
        {
            try
            {
                return ResponderSucesso(_menuApp.ObterParaEditar(id));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ModuloMap : EntityTypeConfiguration<Modulo>
    {
        public ModuloMap()
        {
            ToTable("MODULO");

            HasKey(t => t.IdModulo);

            Property(t => t.IdModulo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Sequencia)
                .IsRequired();

            Property(t => t.Descricao)
                .IsRequired()
                .HasMaxLength(100);
        }
    }
}
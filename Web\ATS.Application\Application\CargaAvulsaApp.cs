﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Mail;
using System.Transactions;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.CargaAvulsa;
using NLog;

namespace ATS.Application.Application
{
    public class CargaAvulsaApp : BaseApp<ICargaAvulsaService>, ICargaAvulsaApp
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly IEmailApp _emailApp;
        private readonly IUsuarioPermissaoFinanceiroApp _usuarioPermissaoFinanceiroApp;
        private readonly IUserIdentity _userIdentity;
        
        public CargaAvulsaApp(ICargaAvulsaService service, IParametrosApp parametrosApp, IEmailApp emailApp, IUsuarioPermissaoFinanceiroApp usuarioPermissaoFinanceiroApp, IUserIdentity userIdentity) : base(service)
        {
            _parametrosApp = parametrosApp;
            _emailApp = emailApp;
            _usuarioPermissaoFinanceiroApp = usuarioPermissaoFinanceiroApp;
            _userIdentity = userIdentity;
        }
        
        public CargaAvulsaAddResponseModel Add(CargaAvulsa cargaAvulsa, string cnpjEmpresa = null, string ipv4 = null, EBloqueioOrigemTipo? origem = null, bool? ignorarValidacaoDuplicada = null)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var response = Service.Add(cargaAvulsa, cnpjEmpresa, ipv4, origem, ignorarValidacaoDuplicada);

                    if (response.ValidationResult.IsValid)
                        transaction.Complete();

                    return response;
                }
            }
            catch (Exception ex)
            {
                LogManager.GetCurrentClassLogger().Error(ex,$"Falha na carga avulsa / {ex.Message}");
                return new CargaAvulsaAddResponseModel(null, null, new ValidationResult<EValidationCargaAvulsa>().Add(ex.Message, EFaultType.Error));
            }
        }
        
        public decimal GetTransacoesDiaria(int? idEmpresa, int? idFilial)
        {
            return Service.GetTransacoesDiaria(idEmpresa,idFilial);
        }

        public object ConsultaGrid(int? idEmpresa, int idUsuario, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor)
        {
            return Service.ConsultaGrid(idEmpresa,idUsuario, take, page, orderFilters, filters,apenasLiberacaoGestor);
        }
        
        public object ConsultaGridPlanilha(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor)
        {
            return Service.ConsultaGridPlanilha(idEmpresa, take, page, orderFilters, filters,apenasLiberacaoGestor);
        }
        
        public IQueryable<CargaAvulsa> Find(Expression<Func<CargaAvulsa, bool>> predicate)
        {
            return Service.Find(predicate);
        }
        
        public ValidationResult ReprocessarCargaAvulsa(int cargaAvulsaId)
        {
            return Service.ReprocessarCargaAvulsa(cargaAvulsaId);
        }

        public EstornarCargaAvulsaResponseModel EstornarCargaAvulsa(int? idCargaAvulsa, string numeroControle, string cnpjEmpresa)
        {
            return Service.EstornarCargaAvulsa(idCargaAvulsa, numeroControle, cnpjEmpresa);
        }
        
        public byte[] GerarRelatorioGridCargaAvulsa(OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa,bool apenasLiberacaoGestor)
        {
            return Service.GerarRelatorioGridCargaAvulsa(orderFilters, filters, tipoArquivo, logo, idEmpresa,apenasLiberacaoGestor);
        }

        public ValidationResult AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa status)
        {
            return Service.AlterarStatusCargaAvulsa(cargaAvulsaId, status);
        }

        public byte[] GerarReciboCargaAvulsa(int cargaAvulsaId, int? empresaid, string usuarioNome, string usuarioDocumento)
        {
            return Service.GerarReciboCargaAvulsa(cargaAvulsaId, empresaid, usuarioNome, usuarioDocumento);
        }

        public ValidationResult Aprovar(int cargaAvulsaId, string cnpjEmpresa)
        {
            return Service.Aprovar(cargaAvulsaId, cnpjEmpresa);
        }

        public BusinessResult GestorAprovar(int cargaAvulsaId)
        {
            try
            {
                var permissaoUsuario =  _usuarioPermissaoFinanceiroApp
                    .GetParametroPermissaoFinanceiro(_userIdentity.IdUsuario, EBloqueioFinanceiroTipo.permiteAprovarDesaprovarCargaAvulsa)?.DesbloquearFinanceiro ?? false;
                
                if(!permissaoUsuario)
                    return BusinessResult.Error("Usuário não possui permissão para aprovar / reprovar carga avulsa!");

                var cargaAvulsa = Service
                    .GetAll()
                    .Include(x => x.Empresa)
                    .Where(x => x.IdCargaAvulsa == cargaAvulsaId && x.Empresa != null)
                    .Select(x => new 
                    {
                        x.Empresa.CNPJ
                    }).FirstOrDefault();
                
                if(cargaAvulsa == null)
                    return BusinessResult.Error("Carga Avulsa não localizada!");

                var result = Aprovar(cargaAvulsaId,cargaAvulsa.CNPJ);
                
                return !result.IsValid 
                    ? BusinessResult.Error($"Falha ao aprovar carga avulsa: {result.Errors.FirstOrDefault()}") 
                    : BusinessResult.Valid();
                
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao aprovar carga avulsa: {e.Message}");
            }
        }
        
        public BusinessResult GestorReprovar(int cargaAvulsaId,string motivoRejeicao,bool validarPermissao,bool origemPortal = false)
        {
            try
            {
                if (validarPermissao)
                {
                    var permissaoUsuario =  _usuarioPermissaoFinanceiroApp
                        .GetParametroPermissaoFinanceiro(_userIdentity.IdUsuario, EBloqueioFinanceiroTipo.permiteAprovarDesaprovarCargaAvulsa)?.DesbloquearFinanceiro ?? false;
                
                    if(!permissaoUsuario)
                        return BusinessResult.Error("Usuário não possui permissão para aprovar / reprovar carga avulsa!");
                }

                var result = Service.Reprovar(cargaAvulsaId,motivoRejeicao,origemPortal);
                
                return !result.Success 
                    ? BusinessResult.Error($"Falha ao reprovar carga avulsa: {result.Messages.FirstOrDefault()}") 
                    : BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao reprovar carga avulsa: {e.Message}");
            }
        }
        
        public BusinessResult GestorAprovarPlanilha(string codImportacao)
        {
            try
            {
                var permissaoUsuario =  _usuarioPermissaoFinanceiroApp
                    .GetParametroPermissaoFinanceiro(_userIdentity.IdUsuario, EBloqueioFinanceiroTipo.permiteAprovarDesaprovarCargaAvulsa)?.DesbloquearFinanceiro ?? false;
            
                if(!permissaoUsuario)
                    return BusinessResult.Error("Usuário não possui permissão para aprovar / reprovar carga avulsa!");

                var result = Service.GestorAprovarPlanilha(codImportacao);

                if (!result.Success)
                    return BusinessResult.Error(result.Messages.FirstOrDefault());

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao aprovar todas as carga avulsa: {e.Message}");
            }
        }
        
        public BusinessResult GestorReprovarPlanilha(string codImportacao)
        {
            try
            {
                var permissaoUsuario =  _usuarioPermissaoFinanceiroApp
                    .GetParametroPermissaoFinanceiro(_userIdentity.IdUsuario, EBloqueioFinanceiroTipo.permiteAprovarDesaprovarCargaAvulsa)?.DesbloquearFinanceiro ?? false;
            
                if(!permissaoUsuario)
                    return BusinessResult.Error("Usuário não possui permissão para aprovar / reprovar carga avulsa!");
                
                var result = Service.GestorReprovarPlanilha(codImportacao);

                if (!result.Success)
                    return BusinessResult.Error(result.Messages.FirstOrDefault());

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao reprovar todas as carga avulsa: {e.Message}");
            }
        }

        public ValidationResult Processar(int cargaAvulsaId, string cnpjEmpresa)
        {
            return Service.Processar(cargaAvulsaId, cnpjEmpresa);
        }

        public bool PertenceAEmpresa(int empresaId, int cargaAvulsaId)
        {
            return Service.Find(c => c.IdCargaAvulsa == cargaAvulsaId && c.IdEmpresa == empresaId).Any();
        }
        
        public BusinessResult ProvisionarValor(ProvisionarRequest request)
        {
            try
            {
                var cnpjProvisionamento = _parametrosApp.GetTagExtrattaCnpjProvisionamento();
                
                if (cnpjProvisionamento == null)
                {
                    var resultEmail = EnviarEmailFalhaProvisionamento(request,EErrroProvisionamento.Outros, "CNPJ do provisionamento nulo");
                    
                    if (!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Info($"Falha ao notificar provisionamento TAXA viagem {request.IdViagem}");

                    return BusinessResult.Valid();
                }
                
                //Evitar pagamentos duplicados para mesma viagem ou passagem
                if(request.Origem == OrigemProvisionamento.Pedagio)
                {
                    var anyPassagemRegistrada = Service
                        .GetByEmpresa(request.IdEmpresa)
                        .Any(x => (x.StatusCargaAvulsa == EStatusCargaAvulsa.ProcessamentoEfetuadoComComSucesso 
                                  || x.StatusCargaAvulsa == EStatusCargaAvulsa.ProcessamentoEfetuadoComTransacaoPendente)
                                  && x.EventoSaldoTagId == request.EventoSaldoTagId && x.Valor == request.Valor);

                    if (anyPassagemRegistrada)
                        return BusinessResult.Valid();
                }
                
                var valorTagRequest = new CargaAvulsa()
                {
                    CPFCNPJUsuario = cnpjProvisionamento,
                    CPFMototista = cnpjProvisionamento,
                    NomeMotorista = "Extratta",
                    Observacao = request.Origem == OrigemProvisionamento.ValePedagio ? 
                        $"Valor VPO Recibo {request.Recibo} - IdViagem {request.IdViagem} - Fornecedor {request.Fornecedor.GetDescription()}"
                        : $"Registro de passagem na praça {request.Praca} - Placa {request.Placa.FormatarPlaca()}",
                    Valor = request.Valor,
                    TipoCarga = ETipoCarga.Provisionamento,
                    IdEmpresa = request.IdEmpresa,
                    IdUsuariocadastro = _parametrosApp.GetIdUsuarioGenericoWS() ?? 0,
                    IdViagemProvisionada = request.IdViagem,
                    EventoSaldoTagId = request.EventoSaldoTagId,
                    PlacaCavalo = request.Placa
                };
                
                var resultVpo = Add(valorTagRequest, request.CnpjEmpresa);

                if (!resultVpo.ValidationResult.IsValid)
                {
                    var resultEmail = EnviarEmailFalhaProvisionamento(request,EErrroProvisionamento.FalhaVpo,resultVpo.ValidationResult.Errors.FirstOrDefault()?.Message);
                    
                    if (!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Info($"Falha ao notificar provisionamento VPO viagem {request.IdViagem}: {resultVpo.ValidationResult.Errors.FirstOrDefault()?.Message}");
                }
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"Falha ao notificar provisionamento: {nameof(CargaAvulsaApp)} / {e.Message}");
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult ProvisionarTaxa(ProvisionarRequest request)
        {
            try
            {
                decimal taxaCargaAvulsa = 0;
                decimal valorTaxa = 0;
                var cnpjProvisionamento = _parametrosApp.GetTagExtrattaCnpjProvisionamento();

                if (request.ValorTaxa.HasValue)
                    valorTaxa = Math.Round(request.ValorTaxa ?? 0,2);
                else
                {       
                    switch (request.Fornecedor)
                    {
                        case FornecedorEnum.MoveMais:
                            taxaCargaAvulsa = _parametrosApp.GetMoveMaisExtrattaTaxaVpo(request.IdEmpresa);
                            break;

                        case FornecedorEnum.ExtrattaTag:
                            taxaCargaAvulsa = _parametrosApp.GetTagExtrattaTaxaVpo(request.IdEmpresa);
                            break;

                        case FornecedorEnum.ViaFacil:
                            taxaCargaAvulsa = _parametrosApp.GetViaFacilExtrattaTaxaVpo(request.IdEmpresa);
                            break;

                        case FornecedorEnum.Veloe:
                            taxaCargaAvulsa = _parametrosApp.GetVeloeExtrattaTaxaVpo(request.IdEmpresa);
                            break;
                        
                        case FornecedorEnum.ConectCar:
                            taxaCargaAvulsa = _parametrosApp.GetConectCarExtrattaTaxaVpo(request.IdEmpresa);
                            break;
                        
                        case FornecedorEnum.TaggyEdenred:
                            taxaCargaAvulsa = _parametrosApp.GetTaggyEdenredExtrattaTaxaVpo(request.IdEmpresa);
                            break;

                    }

                    valorTaxa = Math.Round(request.Valor * taxaCargaAvulsa / 100,2);
                }
                
                if (cnpjProvisionamento == null)
                {
                    var resultEmail = EnviarEmailFalhaProvisionamento(request,EErrroProvisionamento.Outros,"CNPJ do provisionamento nulo");
                    
                    if (!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Info($"Falha ao notificar provisionamento TAXA viagem {request.IdViagem}");

                    return BusinessResult.Valid();
                }
                
                //Evitar pagamentos duplicados para mesma viagem ou passagem
                if(request.Origem == OrigemProvisionamento.Pedagio)
                {
                    var anyPassagemRegistrada = Service
                        .GetByEmpresa(request.IdEmpresa)
                        .Any(x => (x.StatusCargaAvulsa == EStatusCargaAvulsa.ProcessamentoEfetuadoComComSucesso 
                                  || x.StatusCargaAvulsa == EStatusCargaAvulsa.ProcessamentoEfetuadoComTransacaoPendente)
                                  && x.EventoSaldoTagId == request.EventoSaldoTagId && x.Valor == valorTaxa);

                    if (anyPassagemRegistrada)
                        return BusinessResult.Valid();
                }
                
                var taxaTagRequest = new CargaAvulsa()
                {
                    CPFCNPJUsuario = cnpjProvisionamento,
                    CPFMototista = cnpjProvisionamento,
                    NomeMotorista = "Extratta",
                    Observacao = request.Origem == OrigemProvisionamento.ValePedagio ? 
                        $"Taxa VPO Recibo {request.Recibo} - IdViagem {request.IdViagem} - Fornecedor {request.Fornecedor.GetDescription()}"
                        : $"Taxa do registro de passagem na praça {request.Praca} - Placa {request.Placa.FormatarPlaca()}",
                    Valor = valorTaxa,
                    TipoCarga = ETipoCarga.Provisionamento,
                    IdEmpresa = request.IdEmpresa,
                    IdViagemProvisionada = request.IdViagem,
                    IdUsuariocadastro = _parametrosApp.GetIdUsuarioGenericoWS() ?? 0,
                    EventoSaldoTagId = request.EventoSaldoTagId,
                    PlacaCavalo = request.Placa
                };
                
                var resultTaxa = Add(taxaTagRequest, request.CnpjEmpresa);

                if (!resultTaxa.ValidationResult.IsValid)
                {
                    var resultEmail = EnviarEmailFalhaProvisionamento(request,EErrroProvisionamento.FalhaTaxa,resultTaxa.ValidationResult.Errors.FirstOrDefault()?.Message);
                    
                    if (!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Info($"Falha ao notificar provisionamento TAXA viagem {request.IdViagem}: {resultTaxa.ValidationResult.Errors.FirstOrDefault()?.Message}");
                }
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"Falha ao notificar provisionamento TAXA viagem: {nameof(CargaAvulsaApp)} / {e.Message}");
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult EstornarProvisionamentoValePedagio(int? idViagem,int? idEmpresa)
        {
            if(idViagem == null)
                return BusinessResult.Error("Falha no estorno do provisionamento: Informe o IdViagem");
            
            if(idEmpresa == null)
                return BusinessResult.Error("Falha no estorno do provisionamento: Informe o idEmpresa");

            var cargasVpo = Service
                .GetByEmpresa(idEmpresa)
                .Include(x => x.Empresa)
                .Where(x => x.IdViagemProvisionada == idViagem
                            && x.TipoCarga == ETipoCarga.Provisionamento
                            && x.StatusCargaAvulsa == EStatusCargaAvulsa.ProcessamentoEfetuadoComComSucesso)
                .Select(x => new {x.IdCargaAvulsa,x.Empresa.CNPJ})
                .ToList();

            if (cargasVpo.Any())
            {
                foreach (var itemEstorno in cargasVpo)
                {
                    var resultEstorno = EstornarCargaAvulsa(itemEstorno.IdCargaAvulsa, null, itemEstorno.CNPJ);

                    if (!resultEstorno.Sucesso)
                    {
                        var requestEmail = new ProvisionarRequest()
                        {
                            IdEmpresa = (int)idEmpresa,
                            IdViagem = idViagem
                        };
                        
                        EnviarEmailFalhaProvisionamento(requestEmail,EErrroProvisionamento.FalhaEstorno, resultEstorno.Mensagem);
                        return BusinessResult.Error($"Falha no estorno do provisionamento: {resultEstorno.Mensagem}");
                    }
                }
            }
            
            return BusinessResult.Valid();
        }

        public BusinessResult EstornarProvisionamentoPedagio(long? eventoSaldoTagId)
        {
            try
            {
                var cargasVpo = Service
                    .GetAll()
                    .Include(x => x.Empresa)
                    .Where(x => x.EventoSaldoTagId == eventoSaldoTagId && x.TipoCarga == ETipoCarga.Provisionamento && x.StatusCargaAvulsa == EStatusCargaAvulsa.ProcessamentoEfetuadoComComSucesso)
                    .Select(x => new {x.IdCargaAvulsa,x.Empresa.CNPJ})
                    .ToList();

                if (cargasVpo.Any())
                {
                    foreach (var itemEstorno in cargasVpo)
                    {
                        var resultEstorno = EstornarCargaAvulsa(itemEstorno.IdCargaAvulsa, null, itemEstorno.CNPJ);

                        if (!resultEstorno.Sucesso)
                            return BusinessResult.Error($"Falha no estorno do provisionamento TAG Extratta: {resultEstorno.Mensagem}");
                    }
                }

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(CargaAvulsaApp)} / {e.Message}");
                return BusinessResult.Error(e.Message);
            }
        }

        private BusinessResult EnviarEmailFalhaProvisionamento(ProvisionarRequest request,EErrroProvisionamento falha,string mensagem)
        {
            try
            { 
                using (var streamReader = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\falha-provisionamento-tag-extratta.html"))
                {
                    var html = streamReader.ReadToEnd();
                    
                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    string corpo;

                    if (falha == EErrroProvisionamento.FalhaEstorno)
                    {
                        corpo = $"Falha ao estornar vale pedágio {request.IdViagem} da empresa cód {request.IdEmpresa} / Motivo: {mensagem}";
                    }
                    else
                    {
                        corpo = falha == EErrroProvisionamento.FalhaTaxa 
                            ? $"Falha ao provisionar a taxa na viagem {request.IdViagem} no valor de ${request.Valor} no VPO {request.Recibo} da empresa {request.CnpjEmpresa.ToCnpjFormato()} / Motivo: {mensagem}"
                            : falha == EErrroProvisionamento.FalhaVpo 
                                ? $"Falha ao provisionar o VPO na viagem {request.IdViagem} no valor de ${request.Valor} no VPO {request.Recibo} da empresa {request.CnpjEmpresa.ToCnpjFormato()} / Motivo: {mensagem}"
                                : $"Nenhuma conta encontrada para o provisionamento da {request.Fornecedor.GetDescription()} / Motivo: {mensagem}";
                    }
                   
                    
                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{titulo}", request.Fornecedor.GetDescription());
                    html = html.Replace("{ambiente}", WebConfigurationManager.AppSettings.Get("AMBIENTE")?.ToUpper() ?? string.Empty);
                    html = html.Replace("{body}", corpo);
                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    var linkWebNovo = WebConfigurationManager.AppSettings.Get("EmailNotificacaoFalhaProvisionamento");
                    
                    if(linkWebNovo == null)
                        return BusinessResult.Error("Nenhum email configurado prara notificar erro no provisionamento.");

                    var destinatario = linkWebNovo.Split(';').ToList();
                    
                    var emailModel = new EmailModel
                    {
                        Assunto = $"{falha.GetDescription()} {request.Fornecedor.GetDescription()}",
                        Destinatarios = destinatario,
                        Prioridade = MailPriority.High,
                        AlternateView = view
                    };

                    var result = _emailApp.EnviarEmail(emailModel);
                
                    if(!result.IsValid)
                        return BusinessResult.Error(result.Errors.FirstOrDefault()?.Message);
                    
                    return BusinessResult.Valid();
                }
                
            }
            catch (Exception e)
            { 
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(CargaAvulsaApp)} / {e.Message}");
                return BusinessResult.Error(e.Message);
            }
        }
    }
}

﻿using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class FilialController : BaseController
    {
        private readonly SrvFilial _srvFilial;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IFilialApp _filialApp;
        private readonly IEmpresaApp _empresaApp;

        public FilialController(BaseControllerArgs baseArgs, SrvFilial srvFilial, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IFilialApp filialApp, IEmpresaApp empresaApp) : base(baseArgs)
        {
            _srvFilial = srvFilial;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _filialApp = filialApp;
            _empresaApp = empresaApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Integrar(FilialIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvFilial.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string AtivarFilial(FilialRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var filialApp = _filialApp;
                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa) ?? 0;

                var listaEmpresasComAcesso =
                    _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(@params.CNPJAplicacao, @params.Token)
                        .Select(x => x.IdEmpresa)
                        .ToList();

                if (!listaEmpresasComAcesso.Contains(idEmpresa))
                    return new JsonResult().Responde(new
                    {
                        Sucesso = false,
                        Mensagem = "Você não possui permissão para alterar registros desta empresa!"
                    });

                var idFilial = !@params.IdFilial.HasValue ? (filialApp.GetIdPorCnpjTodos(@params.Cnpj) ?? 0) : @params.IdFilial ?? 0;

                var filial = filialApp.GetFilialPorEmpresa(idEmpresa, idFilial);

                if (filial == null)
                    return new JsonResult().Responde(new
                    {
                        Sucesso = false,
                        Mensagem = "Filial não pertence a empresa informada!"
                    });

                var retorno = _filialApp.Reativar(idFilial);

                if (!retorno.IsValid)
                    throw new Exception(retorno.Errors.FirstOrDefault()?.Message);

                return new JsonResult().Responde(new
                {
                    Sucesso = true,
                    Mensagem = ""
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string DesativarFilial(FilialRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var filialApp = _filialApp;
                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa) ?? 0;

                var listaEmpresasComAcesso =
                    _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(@params.CNPJAplicacao, @params.Token)
                        .Select(x => x.IdEmpresa)
                        .ToList();

                if (!listaEmpresasComAcesso.Contains(idEmpresa))
                    return new JsonResult().Responde(new
                    {
                        Sucesso = false,
                        Mensagem = "Você não possui permissão para alterar registros desta empresa!"
                    });

                var idFilial = !@params.IdFilial.HasValue ? (filialApp.GetIdPorCnpjTodos(@params.Cnpj) ?? 0) : @params.IdFilial ?? 0;

                var filial = filialApp.GetFilialPorEmpresa(idEmpresa, idFilial);

                if (filial == null)
                    return new JsonResult().Responde(new
                    {
                        Sucesso = false,
                        Mensagem = "Filial não pertence a empresa informada!"
                    });

                var retorno = _filialApp.Inativar(idFilial);

                if (!retorno.IsValid)
                    throw new Exception(retorno.Errors.FirstOrDefault()?.Message);

                return new JsonResult().Responde(new
                {
                    Sucesso = true,
                    Mensagem = ""
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarAtualizadas(FilialConsultaRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvFilial.ConsultarAtualizadas(@params.CNPJEmpresa, @params.DataBase));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public string IntegrarFilialFila(FilialIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvFilial.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

    }
}
﻿using ATS.Domain.Interface.Triggers;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Data.Context.Trigger
{
    public abstract class TriggerContext : DbContext, ITriggerContext
    {
        public Guid? TriggerSessionId { get; set; }

        public int SaveChanges<TEntity>(TEntity entity) where TEntity : class
        {
            try
            {
                //TODO create new feature to identify changes in attached objects to run trigger. 
                var state = Entry(entity).State;

                var currentOperation_ = TriggerManager.GetCurrentOperationByState(state);

                var triggerManager = TriggerManager.Get<ITrigger<TEntity>>(typeof(TEntity), TriggerSessionId, currentOperation_);

                if (triggerManager == null)
                    return SaveChanges();

                var old_ = (state == EntityState.Modified) ? (TEntity)Entry(entity).OriginalValues.ToObject() : null;

                #region Execute before statement

                triggerManager.ExecuteBeforeTrigger(currentOperation_, entity, old_);

                #endregion

                var returnStatement = SaveChanges();

                triggerManager.ExecuteAfterTrigger(currentOperation_, entity, old_);

                return returnStatement;

            }
            catch (Exception e)
            {
                throw e;
            }
        }
    }
}

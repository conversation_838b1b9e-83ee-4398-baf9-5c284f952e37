﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface ICheckInService : IService<CheckIn>
    {
        ValidationResult Add(CheckIn checkIn);
        IQueryable<CheckIn> GetCheckInsHoje(int? idTipoCavalo, int? idtipoCarreta);
        CheckIn GetLast(int idUsuario);
        Tuple<decimal?, decimal?> GetLastPosition(int idUsuario);
        IQueryable<CheckIn> GetChekinsPeriodo(DateTime dataInicial, DateTime? dataFinal = null);
        List<CheckIn> GetChekinsPeriodoTelao(DateTime dataInicial, DateTime dataFinal);
        IQueryable<CheckIn> GetByPlaca(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa);
        ValidationResult AddCheckinPreCadastro(CheckIn checkIn);

        List<CheckIn> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa);
        IQueryable<CheckIn> GetByEmpresa(int IdEmpresa);
        List<CheckIn> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf);
        IQueryable<CheckIn> GetById(int Id, bool withIncludes = true);


        /// <summary>
        /// Retorna os dados do último CheckIn realizado pelo usuário, buscando pelo id do motorista
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        CheckIn GetLastByIdMotorista(int idMotorista);

        CheckIn GetLastByCpf(DateTime aDataInicial, DateTime aDataFinal, string cpf);
    }
}
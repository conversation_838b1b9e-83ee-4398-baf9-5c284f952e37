﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IViagemDocumentoFiscalService : IService<ViagemDocumentoFiscal>
    {
        ValidationResult Add(ViagemDocumentoFiscal viagemDocumentoFiscal);

        ValidationResult Update(ViagemDocumentoFiscal viagemDocumentoFiscal);

        List<object> GetByViagem(int idViagem);
    }
}

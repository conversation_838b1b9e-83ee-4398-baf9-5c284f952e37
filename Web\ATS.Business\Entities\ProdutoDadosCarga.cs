﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ProdutoDadosCarga
    {
        public int IdDadosCarga { get; set; }

        public int? IdProduto { get; set; } //acho que vou retirar esse valor

        /// <summary>
        /// Número do pedido informado pelo Embarcador
        /// </summary>
        public string NumeroPedido { get; set; }

        /// <summary>
        /// Ordem da compra informado pelo Embarcador
        /// </summary>
        public string OrdemCompra { get; set; }

        /// <summary>
        /// Protocolo informado pelo Embarcador
        /// </summary>
        public string Protocolo { get; set; }

        /// <summary>
        /// Formula informado pelo Embarcador
        /// </summary>
        public string Formula { get; set; }

        /// <summary>
        /// Quantidade informado pelo Embarcador
        /// </summary>
        public string Quantidade { get; set; }

        /// <summary>
        /// Armazém informado pelo Embarcador
        /// </summary>
        public string Armazem { get; set; }

        #region Navegação Inversa

        /// <summary>
        /// Produto vinculado
        /// </summary>
        public virtual Produto Produto { get; set; }
        
        #endregion
    }
}

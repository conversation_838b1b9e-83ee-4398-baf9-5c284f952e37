﻿using ATS.Domain.Enum;

namespace Extratta.Tag.Application.Events.Events
{
    public class EventoSaldoCobrancaValePedagioTagEvent 
    {
        public string Placa { get; set; }
        public int? ViagemId { get; set; }
        public string Recibo { get; set; }
        public decimal ValorPedagio { get; set; }
        public int? IdUsuario { get; set; }
        public int? IdEmpresa { get; set; }
        public EPerfil Perfil { get; set; } 
        public decimal? Taxa { get; set; }
        public FornecedorEnum Fornecedor { get; set; }
    }   
}

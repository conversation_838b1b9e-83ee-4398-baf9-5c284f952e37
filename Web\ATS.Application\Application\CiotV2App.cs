using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Data.Entity;
using System.Linq;
using System.Text;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Validation;
using AutoMapper;
using NLog;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Application.Application
{
    public class CiotV2App: BaseApp<ICiotV2Service>, ICiotV2App
    {
        private readonly IProprietarioService _proprietarioService;
        private readonly IViagemRepository _viagemRepository;
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly IEmpresaApp _empresaApp;
        private readonly IContratoCiotAgregadoService _contratoCiotAgregadoService;
        private readonly IVeiculoService _veiculoService;
        private readonly IContratoCiotAgregadoRepository _contratoCiotAgregadoRepository;
        private readonly IParametrosApp _parametrosApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IUserIdentity _userIdentity;
        private readonly ViagemActionDependencies _viagemActionDependencies;
        private readonly ContratoCiotAgregadoActionDependencies _contratoCiotAgregadoActionDependencies;

        public CiotV2App(ICiotV2Service service, IProprietarioService proprietarioService, IViagemRepository viagemRepository, IDeclaracaoCiotRepository declaracaoCiotRepository,
            IEmpresaApp empresaApp, IContratoCiotAgregadoService contratoCiotAgregadoService, IVeiculoService veiculoService, IContratoCiotAgregadoRepository contratoCiotAgregadoRepository, 
            IParametrosApp parametrosApp, IEmpresaRepository empresaRepository, ViagemActionDependencies viagemActionDependencies,
            ContratoCiotAgregadoActionDependencies contratoCiotAgregadoActionDependencies, IUserIdentity userIdentity) : base(service)
        {
            _proprietarioService = proprietarioService;
            _viagemRepository = viagemRepository;
            _declaracaoCiotRepository = declaracaoCiotRepository;
            _empresaApp = empresaApp;
            _contratoCiotAgregadoService = contratoCiotAgregadoService;
            _veiculoService = veiculoService;
            _contratoCiotAgregadoRepository = contratoCiotAgregadoRepository;
            _parametrosApp = parametrosApp;
            _empresaRepository = empresaRepository;
            _viagemActionDependencies = viagemActionDependencies;
            _contratoCiotAgregadoActionDependencies = contratoCiotAgregadoActionDependencies;
            _userIdentity = userIdentity;
        }

        public bool IsDeclaracaoTacAgregado(string placa, int idEmpresa, bool habilitarContratoCiotAgregado)
        {
            return Service.IsDeclaracaoTacAgregado(placa, idEmpresa, habilitarContratoCiotAgregado);
        }

        /// <summary>
        /// Executado a partir do serviço de integração de viagem.
        /// Rotina valida a obrigatoriedade de declararação de CIOT para o proprietário (EquiparadoTAC = Obrigatório), e avaliar se o sistema já declarou a operação para esta viagem.
        /// Esta rotina não deve executar exceptions que abortem a thread principal que retornará os dados da viagem.
        /// </summary>
        /// <param name="viagem"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <param name="habilitarDeclaracaoCiot">Indicado recepcionado do consumidor da API de integração da carta frete, indicando se a plataforma ATS está habilitada a genrenciar o CIOT desta viagem</param>
        /// <param name="retificarCiot"></param>
        /// <param name="forcarCiotNaoEquiparado"></param>
        /// <param name="gerarCiotTacAgregado"></param>
        public DeclararCiotResult DeclararCiotFromIntegracaoViagem(Viagem viagem, string cnpjEmpresa, bool habilitarDeclaracaoCiot,
            bool retificarCiot, bool forcarCiotNaoEquiparado, bool? gerarCiotTacAgregado)
        {
            try
            {
                Service.GerarToken(cnpjEmpresa);

                return viagem.DeclararCiotV2(_contratoCiotAgregadoService, _veiculoService, Service, _proprietarioService, _viagemRepository,
                    _declaracaoCiotRepository, _viagemActionDependencies, cnpjEmpresa, retificarCiot, forcarCiotNaoEquiparado,
                    habilitarDeclaracaoCiot, gerarCiotTacAgregado);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e,
                        "Erro ao declarar CIOT para viagem: {0} - Proprietário: {1}".FormatEx(viagem.IdViagem,
                            viagem.CPFCNPJProprietario));

                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = e.Message
                };
            }
        }

        public AbrirContratoCiotAgregadoResultModel AbrirContratoCiotAgregado(ContratoAberturaModel requestModel, int idEmpresa)
        {
            var possuiRntrc = _empresaApp.AnyRntrc(idEmpresa);
            if (!possuiRntrc)
                return new AbrirContratoCiotAgregadoResultModel
                {
                    Sucesso = false,
                    Mensagem = "Para empresa que não possui RNTRC só é permitido emitir CIOT do tipo padrão"
                };

            requestModel.IdEmpresa = idEmpresa;
            requestModel.DataInicio = DateTime.Today;
            
            return _contratoCiotAgregadoService.AbrirContratoCiotAgregado(requestModel);
        }

        public DeclararCiotResult EncerrarCiotAgregado(int idEmpresa, EncerrarContratoAgregadoModel requestModel)
        {
            try
            {
                Service.GerarToken(idEmpresa);
                var ciotAgregadoRepository = _contratoCiotAgregadoRepository;
                var contratoCiotAgregado = Service.GetContratoAgregado(requestModel.ContratoAgregadoId);
                if (contratoCiotAgregado?.IdDeclaracaoCiot == null)
                    return new DeclararCiotResult
                    {
                        Declarado = false,
                        Mensagem = "Erro ao encontrato contrado de agregado",
                        Resultado = EResultadoDeclaracaoCiot.Erro
                    };

                var viagens = Service.GetViagensNaoCanceladas(contratoCiotAgregado.IdDeclaracaoCiot.Value);
                return Service.EncerrarCiot(contratoCiotAgregado, ciotAgregadoRepository, viagens);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e, "Erro ao encerrar CIOT para viagem");
                return new DeclararCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    Declarado = false,
                    Mensagem = e.Message
                };
            }
        }

        public ValidationResult Encerrar(string cnpjEmpresa, string ciot)
        {
            var validacao = new ValidationResult();
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);
                if (!idEmpresa.HasValue)
                    return validacao.Add("Empresa não encontrada");
                
                var idContratoCiotAgregado = _declaracaoCiotRepository.GetIdContratoAgregadoPorCiot(idEmpresa.Value, ciot);
                if (!idContratoCiotAgregado.HasValue || idContratoCiotAgregado == 0)
                    return validacao.Add("Contrato agregado de CIOT não encontrado");
                
                var result = EncerrarCiotAgregado(idEmpresa.Value, new EncerrarContratoAgregadoModel {ContratoAgregadoId = idContratoCiotAgregado.Value} );

                if (result.Resultado == EResultadoDeclaracaoCiot.Erro)
                    validacao.Add(result.Mensagem);
                else if(result.Resultado == EResultadoDeclaracaoCiot.NaoObrigatorio)
                    validacao.Add(result.Mensagem, EFaultType.Alert);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e, "Erro ao encerrar CIOT.");
                validacao.Add(e.GetBaseException().Message);
            }

            return validacao;
        }

        public void Avisar()
        {
            var contratoCiotAgregadoCancelarList = _contratoCiotAgregadoService.GetContratosSemViagensParaCancelamentoFuturo().GroupBy(x => x.IdEmpresa).ToList();
            var contratoCiotAgregadoEncerrarList = _contratoCiotAgregadoService.GetContratosExpiradosParaEncerramentoFuturo().GroupBy(x => x.IdEmpresa).ToList();
            
            var dadosSituacaoCiot = new List<DadosSituacaoCiotDto>();
            
            foreach (var empresa in contratoCiotAgregadoCancelarList)
            {
                var ciotCancelar = string.Empty;
                var cnpjEmpresa = string.Empty;
                foreach (var contratoCiotAgregado in empresa)
                {
                    cnpjEmpresa = contratoCiotAgregado.Empresa.CNPJ;

                    ciotCancelar += $"<li>CIOT {contratoCiotAgregado.DeclaracaoCiot.Ciot}/{contratoCiotAgregado.DeclaracaoCiot.Senha} será cancelado automaticamente em {(DateTime.Now.Date - contratoCiotAgregado.DataCadastro.Value.Date.AddDays(5)).Days*-1} dia(s) por não" +
                                    $" possuir uma Viagem vinculada. Necessário vincular uma viagem para evitar o cancelamento (Previsão de Cancelamento: {contratoCiotAgregado.DataCadastro.Value.AddDays(5):d}) </li>";
                }
                dadosSituacaoCiot.Add(new DadosSituacaoCiotDto
                {
                    MensagensCancelar = ciotCancelar,
                    IdEmpresa = empresa.Key,
                    cnpjEmpresa = cnpjEmpresa
                });
            }
            
            foreach (var empresa in contratoCiotAgregadoEncerrarList)
            {
                var cnpjEmpresa = string.Empty;
                var ciotEncerrar = string.Empty;
                foreach (var contratoCiotAgregado in empresa)
                {
                    cnpjEmpresa = contratoCiotAgregado.Empresa.CNPJ;

                    ciotEncerrar += $"<li>CIOT {contratoCiotAgregado.DeclaracaoCiot.Ciot}/{contratoCiotAgregado.DeclaracaoCiot.Senha} será encerrado automaticamente em {(DateTime.Now.StartOfDay().AddDays(-24) - contratoCiotAgregado.DataFinal.Value.Date).Days} dia(s)" +
                                    $" (Previsão de Encerramento: {contratoCiotAgregado.DataFinal.Value.AddDays(30)}) </li>";
                }

                if (dadosSituacaoCiot.Any(x => x.IdEmpresa == empresa.Key))
                {
                    var dadosCiotEmpresa = dadosSituacaoCiot.FirstOrDefault(x => x.IdEmpresa == empresa.Key);
                    if(dadosCiotEmpresa != null)
                        dadosCiotEmpresa.MensagensEncerrar = ciotEncerrar;
                }
                else
                    dadosSituacaoCiot.Add(new DadosSituacaoCiotDto
                    {
                        MensagensEncerrar = ciotEncerrar,
                        IdEmpresa = empresa.Key,
                        cnpjEmpresa = cnpjEmpresa
                    });
            }

            foreach (var dadosEmpresa in dadosSituacaoCiot)
            {
                try
                {
                    Service.EnviarEmailAviso(dadosEmpresa);
                }
                catch (Exception e)
                {
                    LogManager.GetCurrentClassLogger().Error(e, "Erro ao enviar email, id empresa: " + dadosEmpresa.IdEmpresa);
                }
            }
        }

        public EncerradoCanceladoCiotResult EncerrarCancelarCiotAgregadoExpirados()
        {
            try
            {
                var contratoCiotAgregadoCancelarList = _contratoCiotAgregadoService.GetContratosSemViagensParaCancelar();

                //CANCELAMENTO DE CONTRATOS SEM VIAGEM
                foreach (var contrato in contratoCiotAgregadoCancelarList)
                {
                    contrato.CanceladoAutomaticamente = true;
                    
                    Service.GerarToken(contrato.IdEmpresa);
                    contrato.CancelarCiotAgregado("Cancelamento automático", _declaracaoCiotRepository,
                        _contratoCiotAgregadoRepository, Service, null, EVersaoAntt.Versao2);
                }

                var contratoCiotAgregadoEncerrarList = _contratoCiotAgregadoService.GetContratosExpiradosParaEncerrar();
                var ciotCancelar = string.Empty;
                //ENCERRAMENTO DE CONTRATOS EXPIRADOS
                foreach (var contrato in contratoCiotAgregadoEncerrarList)
                {
                    Service.GerarToken(contrato.IdEmpresa);

                    Service.EncerrarCiot(contrato, _contratoCiotAgregadoRepository,
                        contrato.DeclaracaoCiot.ViagensVinculadas.ToList());

                    if (contrato.IdDeclaracaoCiot.HasValue)
                    {
                        ciotCancelar += $"<li>CIOT {contrato.DeclaracaoCiot.Ciot}/{contrato.DeclaracaoCiot.Senha}</li>";

                        try
                        {
                            Service.EnviarEmailCancelamento(ciotCancelar, contrato.IdEmpresa);
                        }
                        catch (Exception e)
                        {
                            LogManager.GetCurrentClassLogger()
                                .Fatal(e, "Erro ao enviar e-mail de cancelamento de CIOT Agregado.");
                        }
                    }
                }

                return new EncerradoCanceladoCiotResult
                {
                    EncerradoCancelado = true,
                    Mensagem = "Processo de encerramento de contratos concluído com sucesso",
                    Resultado = EResultadoDeclaracaoCiot.Sucesso,
                    QtdCancelados = contratoCiotAgregadoCancelarList.Count,
                    QtdEncerrados = contratoCiotAgregadoEncerrarList.Count
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao encerrar ou cancelar automaticamente o CIOT expirado.");
                return new EncerradoCanceladoCiotResult
                {
                    Resultado = EResultadoDeclaracaoCiot.Erro,
                    EncerradoCancelado = false,
                    Mensagem = e.Message
                };
            }
        }

        public RetificarContratoAgregadoResponseDto RetificarCiotAgregadoPorTela(int idEmpresa, RetificarContratoAgregadoModel requestModel)
        {
            try
            {
                Service.GerarToken(idEmpresa);

                var ciotAgregadoRepository = _contratoCiotAgregadoRepository;
                var contratoCiotAgregado = Service.GetContratoAgregado(requestModel.ContratoAgregadoId);
                if (contratoCiotAgregado == null)
                    return new RetificarContratoAgregadoResponseDto("Não foi encontrado o contrado de agregado na base de dados");

                // Se existir registro na tabela de contrato, que não possua CIOT gerado, tenta declara o CIOT para seguir o processo (Pode acontecer isso caso ANTT esteja fora durante o processo de declaração
                if (contratoCiotAgregado.Status == EStatusContratoAgregado.Vigente &&
                    contratoCiotAgregado.IdDeclaracaoCiot == null)
                {
                    var resultDeclaracaoCiot = contratoCiotAgregado.VerificarEDeclararCiotAntt(
                        _contratoCiotAgregadoService.Repository, Service, null, EVersaoAntt.Versao2,
                        _declaracaoCiotRepository, _contratoCiotAgregadoActionDependencies);

                    if (!resultDeclaracaoCiot.Sucesso.GetValueOrDefault(false))
                        return new RetificarContratoAgregadoResponseDto(resultDeclaracaoCiot.Excecao?.Mensagem);
                }

                if (requestModel.Veiculos?.Any() != true)
                    return new RetificarContratoAgregadoResponseDto("Informe ao menos um veículo para retificar contrato de CIOT agregado.");

                foreach (var veiculo in requestModel.Veiculos)
                    veiculo.Placa = StringExtension.RemoveSpecialCaracter(veiculo.Placa);

                var placasPersistidas = contratoCiotAgregado.ContratoCiotAgregadoVeiculos.Select(x => x.Veiculo.Placa).Distinct().ToList();
                var placasRequest = requestModel.Veiculos.Select(x => x.Placa).Distinct().ToList();
                
                var placasNovas = placasRequest.Except(placasPersistidas).ToList();
                var placasRemovidas = placasPersistidas.Except(placasRequest).ToList();

                foreach (var placaNova in placasNovas)
                {
                    var consultaFrota = ConsultarFrotaTransportador(new ConsultarFrotaTransportadorRequest
                    {
                        Placa = new ObservableCollection<string> {placaNova},
                        RntrcTransportador = contratoCiotAgregado.Proprietario.RNTRC,
                        CpfCnpjTransportador = contratoCiotAgregado.Proprietario.CNPJCPF,
                        CpfCnpjInteressado = contratoCiotAgregado.Empresa.CNPJ
                    });
            
                    var veiculoRequest = requestModel.Veiculos.First(v => v.Placa == placaNova);
                        
                    //Foi aberto um cartão no asana para avaliar a contingencia
                    if (consultaFrota.Sucesso != true || consultaFrota.FalhaComunicacaoAntt == true || !consultaFrota.VeiculoTransportador.Any())
                        veiculoRequest.InclusoCiot = true;
                    else if (consultaFrota.VeiculoTransportador.First().SituacaoVeiculoFrotaTransportador == true)
                        veiculoRequest.InclusoCiot = true;
                    else
                    {
                        // A placa salva no banco e não é enviada na retificação
                        veiculoRequest.InclusoCiot = false;
                    }
                }
                
                var retificaPorPlaca = false;

                if (placasNovas.Any() && requestModel.Veiculos.Where(x => placasNovas.Contains(x.Placa)).Any(v => v.InclusoCiot))
                    //Se tem alguma placa nova que vai para o CIOT
                    retificaPorPlaca = true;
                
                if (placasRemovidas.Any() && contratoCiotAgregado.ContratoCiotAgregadoVeiculos.Where(v => placasRemovidas.Contains(v.Veiculo.Placa)).Any(v => v.InclusoCiot))
                    //Ou se tem alguma placa que vai ser removida no CIOT
                    retificaPorPlaca = true;
                
                if (retificaPorPlaca)
                {
                    var ciotResult = Service.RetificarCiot(_contratoCiotAgregadoService, contratoCiotAgregado, ciotAgregadoRepository, requestModel.Veiculos);
                    return ciotResult.Resultado == EResultadoDeclaracaoCiot.Sucesso 
                        ? new RetificarContratoAgregadoResponseDto{Sucesso = true, Mensagem = "Contrato retificado com sucesso!"} 
                        : new RetificarContratoAgregadoResponseDto(ciotResult.Mensagem);
                }
                
                string mensagemAviso;
                if (placasNovas.Any() || placasRemovidas.Any())
                {
                    var validationResult = Service.AtualizarVeiculosContratoAgregado(_contratoCiotAgregadoService, contratoCiotAgregado, requestModel.Veiculos);
                    
                    if(!validationResult.IsValid)
                        return new RetificarContratoAgregadoResponseDto(validationResult.ToString());
                    
                    mensagemAviso = "A placa não pertencente ao proprietário não é necessário retificar na ANTT";
                }
                else
                    mensagemAviso = "Não houve alteração de placa e não é necessário retificar na ANTT";

                return new RetificarContratoAgregadoResponseDto
                {
                    Sucesso = true, 
                    Mensagem = "Contrato salvo",
                    MensagemAviso = mensagemAviso
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e, "Erro ao encerrar CIOT para viagem");
                return new RetificarContratoAgregadoResponseDto(e.GetBaseException().Message);
            }
        }

        public CancelarCiotAgregadoViagensResult CancelarCiotAgregado(int idEmpresa, int idContrato)
        {
            try
            {
                var contratoCiotAgregado = Service.GetContratoAgregado(idContrato);
                if (contratoCiotAgregado == null)
                    return new CancelarCiotAgregadoViagensResult
                    {
                        Mensagem = "Erro ao encontrar o contrato de agregado",
                        Sucesso = false
                    };
                
                if (!contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                    return new CancelarCiotAgregadoViagensResult
                    {
                        Mensagem = "Erro ao encontrar o contrato de agregado",
                        Sucesso = false
                    };
                
                if (contratoCiotAgregado.IdEmpresa != _userIdentity.IdEmpresa)
                    return new CancelarCiotAgregadoViagensResult
                    {
                        Mensagem = "Usuário não autenticado.",
                        Sucesso = false
                    };
            
                var diasCancelamento = _parametrosApp.GetDiasCancelamentoViagem(idEmpresa);
                var viagens = Service.GetViagensNaoCanceladas(contratoCiotAgregado.IdDeclaracaoCiot.Value);
                var retorno = new CancelarCiotAgregadoViagensResult
                {
                    Mensagem = "",
                    Sucesso = false,
                    Cancelado = false,
                };
                foreach (var viagem in viagens)
                {
                    if (diasCancelamento != 0 && viagem.DataLancamento.HasValue && DateTime.Now > viagem.DataLancamento.Value.AddDays(diasCancelamento))
                    {
                        return new CancelarCiotAgregadoViagensResult
                        {
                            Mensagem = $"Não é possível cancelar o CIOT, pois uma das viagens atingiu o prazo de cancelamento. Viagem {viagem.IdViagem}",
                            Sucesso = false
                        };
                    }
                    retorno.IdViagens.Add(viagem.IdViagem);
                }
                
                Service.GerarToken(idEmpresa);
                contratoCiotAgregado.CanceladoAutomaticamente = false;

                var resultado = contratoCiotAgregado.CancelarCiotAgregado("Solicitação feita via ATS",
                    _declaracaoCiotRepository, _contratoCiotAgregadoRepository,
                    Service, null, EVersaoAntt.Versao2);
                
                retorno.Sucesso = resultado.Sucesso;
                retorno.Cancelado = resultado.Cancelado;

                if (string.IsNullOrWhiteSpace(retorno.Mensagem))
                    retorno.Mensagem = resultado.Mensagem;
                    
                return retorno;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao cancelar CIOT");

                return new CancelarCiotAgregadoViagensResult
                {
                    Sucesso = false,
                    Cancelado = false,
                    Mensagem = e.Message
                };
            }
            
        }

        public object ConsultarContratosAgregado(DateTime dataInicio, DateTime dataFinal, int take, int page, 
            OrderFilters order, List<QueryFilters> filters, int? idEmpresa)
        {
            var contratoRepository = _contratoCiotAgregadoRepository;
            
            var contratoCiotAgregados = contratoRepository
                .ConsultarContratos(dataInicio, dataFinal, idEmpresa);
            
            var filtroAviso = filters?.FirstOrDefault(f => f.Campo == "AvisoContratoAgregadoDescricao");
            var filtroSituacao = filters?.FirstOrDefault(f => f.Campo == "Status");

            if (filtroAviso != null) filters.Remove(filtroAviso);
            if (filtroSituacao != null) filters.Remove(filtroSituacao);
            
            var contratosFiltrados = contratoCiotAgregados
                .AplicarFiltrosDinamicos(filters)
                .AplicarOrderByDinamicos(order);
            
            //Essa validacao de filtro de aviso esta aqui pq esse nao é um valor salvo no banco e sim uma informacao
            //criada a partir dele
            if (filtroAviso?.Valor != null && !string.IsNullOrEmpty(filtroAviso.Valor)) {
                var dataHoje = DateTime.Now.StartOfDay();
                var dataHojeMenos5 = DateTime.Now.StartOfDay().AddDays(-5);
                var dataHojeMenos30 = DateTime.Now.StartOfDay().AddDays(-30);

                //Se bater nos tres filtros ou seja o usuario só quer procurar os que tenham aviso
                if ("CIOT será encerrado automaticamente em dias".Contains(filtroAviso.Valor) &&
                    "CIOT será encerrado automaticamente em 1 dia".Contains(filtroAviso.Valor) &&
                    "CIOT será encerrado automaticamente hoje".Contains(filtroAviso.Valor))
                {
                    contratosFiltrados = contratosFiltrados
                        .Where(c => c.Status == EStatusContratoAgregado.Vigente && 
                                    c.DataInicio.HasValue && c.DataFinal.HasValue && c.IdDeclaracaoCiot.HasValue &&
                                    ((!c.DeclaracaoCiot.ViagensVinculadas.Any() ||
                                      c.DeclaracaoCiot.ViagensVinculadas.All(v =>
                                          v.StatusViagem == EStatusViagem.Cancelada) &&
                                      DbFunctions.DiffDays(dataHojeMenos5, c.DataInicio) >= 0)
                                     ||
                                     (c.DeclaracaoCiot.ViagensVinculadas.Any(v =>
                                          v.StatusViagem != EStatusViagem.Cancelada) &&
                                      c.DataFinal < dataHoje &&
                                      DbFunctions.DiffDays(dataHojeMenos30, c.DataFinal) >= 0)));
                }
                //Se o usuario digitou "dia"
                else if ("CIOT será encerrado automaticamente em dias".Contains(filtroAviso.Valor) &&
                    "CIOT será encerrado automaticamente em 1 dia".Contains(filtroAviso.Valor))
                {
                    contratosFiltrados = contratosFiltrados
                        .Where(c => c.Status == EStatusContratoAgregado.Vigente && 
                                    c.DataInicio.HasValue && c.DataFinal.HasValue && c.IdDeclaracaoCiot.HasValue &&
                                    ((!c.DeclaracaoCiot.ViagensVinculadas.Any() ||
                                            c.DeclaracaoCiot.ViagensVinculadas.All(v =>
                                                v.StatusViagem == EStatusViagem.Cancelada) &&
                                            DbFunctions.DiffDays(dataHojeMenos5, c.DataInicio) >= 1)
                                        ||
                                        (c.DeclaracaoCiot.ViagensVinculadas.Any(v =>
                                             v.StatusViagem != EStatusViagem.Cancelada) &&
                                         c.DataFinal < dataHoje &&
                                         DbFunctions.DiffDays(dataHojeMenos30, c.DataFinal) >= 1)));
                }
                //Se o usuario digitou o numero de dias ou "hoje" pra filtrar separadamente
                else
                {
                    bool digitouNumero = int.TryParse(filtroAviso.Valor, out _);
                    switch (filtroAviso.Valor)
                    {
                        case { } when digitouNumero:
                        case { } s when "CIOT será encerrado automaticamente em dias".Contains(s):
                        {
                            contratosFiltrados = contratosFiltrados
                                .Where(c => c.Status == EStatusContratoAgregado.Vigente && 
                                           c.DataInicio.HasValue && c.DataFinal.HasValue && c.IdDeclaracaoCiot.HasValue && 
                                           ((!c.DeclaracaoCiot.ViagensVinculadas.Any() ||
                                             c.DeclaracaoCiot.ViagensVinculadas.All(v =>
                                                 v.StatusViagem == EStatusViagem.Cancelada) &&
                                             DbFunctions.DiffDays(dataHojeMenos5, c.DataInicio) > 1)
                                            ||
                                            (c.DeclaracaoCiot.ViagensVinculadas.Any(v =>
                                                 v.StatusViagem != EStatusViagem.Cancelada) &&
                                             c.DataFinal < dataHoje &&
                                             DbFunctions.DiffDays(dataHojeMenos30, c.DataFinal) > 1)));
                        }
                            break;
                        case { } s when "CIOT será encerrado automaticamente em 1 dia".Contains(s):
                        {
                            contratosFiltrados = contratosFiltrados
                                .Where(c => c.Status == EStatusContratoAgregado.Vigente && 
                                           c.DataInicio.HasValue && c.DataFinal.HasValue && c.IdDeclaracaoCiot.HasValue && 
                                           ((!c.DeclaracaoCiot.ViagensVinculadas.Any() ||
                                             c.DeclaracaoCiot.ViagensVinculadas.All(v =>
                                                 v.StatusViagem == EStatusViagem.Cancelada) &&
                                             DbFunctions.DiffDays(dataHojeMenos5, c.DataInicio) == 1)
                                            ||
                                            (c.DeclaracaoCiot.ViagensVinculadas.Any(v =>
                                                 v.StatusViagem != EStatusViagem.Cancelada) &&
                                             c.DataFinal < dataHoje &&
                                             DbFunctions.DiffDays(dataHojeMenos30, c.DataFinal) == 1)));
                        }
                            break;
                        case { } s when "CIOT será encerrado automaticamente hoje".Contains(s):
                        {
                            contratosFiltrados = contratosFiltrados
                                .Where(c => c.Status == EStatusContratoAgregado.Vigente && 
                                           c.DataInicio.HasValue && c.DataFinal.HasValue && c.IdDeclaracaoCiot.HasValue && 
                                           ((!c.DeclaracaoCiot.ViagensVinculadas.Any() ||
                                             c.DeclaracaoCiot.ViagensVinculadas.All(v =>
                                                 v.StatusViagem == EStatusViagem.Cancelada) &&
                                             DbFunctions.DiffDays(dataHojeMenos5, c.DataInicio) == 0)
                                            ||
                                            (c.DeclaracaoCiot.ViagensVinculadas.Any(v =>
                                                 v.StatusViagem != EStatusViagem.Cancelada) &&
                                             c.DataFinal < dataHoje &&
                                             DbFunctions.DiffDays(dataHojeMenos30, c.DataFinal) == 0)));
                        }
                            break;
                    }
                }
            }
            
            //Essa validacao de filtro de statsu esta aqui pq o campo status do contrato ciot não é atualizado
            //como deveria no banco seguindo as condicoes a baixo, entao é manualmente feito a mudança de 
            //descricoes de status
            if (filtroSituacao?.Valor != null && !string.IsNullOrEmpty(filtroSituacao.Valor)) {
                var statusParaFiltro = (EStatusContratoAgregado) filtroSituacao.Valor.ToInt();
                var dataHoje = DateTime.Now.StartOfDay();
                switch (statusParaFiltro)
                {
                    case EStatusContratoAgregado.Vigente:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso &&
                                        c.Status == EStatusContratoAgregado.Vigente &&
                                        (c.DeclaracaoCiot == null ||
                                         c.DeclaracaoCiot.ViagensVinculadas.Any(v => v.StatusViagem != EStatusViagem.Cancelada)) &&
                                        c.DataFinal >= dataHoje);
                    }
                        break;
                    case EStatusContratoAgregado.AguardandoViagem:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso &&
                                        c.Status == EStatusContratoAgregado.Vigente &&
                                        c.DeclaracaoCiot != null &&
                                        (!c.DeclaracaoCiot.ViagensVinculadas.Any() || c.DeclaracaoCiot.ViagensVinculadas.All(v => v.StatusViagem == EStatusViagem.Cancelada)));

                    }
                        break;
                    case EStatusContratoAgregado.AguardandoEncerramento:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso &&
                                c.Status == EStatusContratoAgregado.Vigente &&
                                (c.DeclaracaoCiot == null ||
                                 c.DeclaracaoCiot.ViagensVinculadas.Any(v => v.StatusViagem != EStatusViagem.Cancelada)) &&
                                c.DataFinal < dataHoje);
                    }
                        break;

                    case EStatusContratoAgregado.Encerrado:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso &&
                                        c.Status == EStatusContratoAgregado.Encerrado);
                    }
                        break;
                    case EStatusContratoAgregado.CanceladoAutomaticamente:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => (c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso || c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Erro || c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.NaoHabilitado) &&
                                        c.Status == EStatusContratoAgregado.Cancelado &&
                                        c.CanceladoAutomaticamente);
                    }
                        break;
                    case EStatusContratoAgregado.Cancelado:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => (c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Sucesso || c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Erro || c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.NaoHabilitado) &&
                                        c.Status == EStatusContratoAgregado.Cancelado &&
                                        !c.CanceladoAutomaticamente);
                    }
                        break;
                    case EStatusContratoAgregado.NaoAprovado:
                    {
                        contratosFiltrados = contratosFiltrados
                            .Where(c => c.ResultadoDeclaracaoCiot != EResultadoDeclaracaoCiot.Sucesso && 
                                        (c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Erro || 
                                         c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.NaoHabilitado) &&
                                        c.Status != EStatusContratoAgregado.Cancelado);
                    }
                        break;
                }
            }
            
            var countTotalItems = contratosFiltrados.ToList().Count;

            contratosFiltrados = contratosFiltrados.Skip((page - 1) * take).Take(take);
            
            var contratosList = contratosFiltrados.ToList();

            var contratos = Mapper.Map<List<ConsultaContratoAgregadoModel>>(contratosList);

            return new
            {
                totalItems = countTotalItems,
                items = contratos.ToList()
            };
        }
        
        private string GetAvisoContratoAgregado(ContratoCiotAgregado c)
        {
            switch (c.Status)
            {
                case EStatusContratoAgregado.Vigente:
                    if (!c.DataInicio.HasValue || !c.DataFinal.HasValue)
                        return null;
                    
                    if (!c.IdDeclaracaoCiot.HasValue)
                        return null;

                    var dataHoje = DateTime.Now.StartOfDay();

                    if (!c.DeclaracaoCiot.ViagensVinculadas.Any() || c.DeclaracaoCiot.ViagensVinculadas.All(v => v.StatusViagem == EStatusViagem.Cancelada))
                    {
                        var diasParaCancelar = (c.DataInicio.Value.AddDays(5) - dataHoje).Days;
                        if (diasParaCancelar > 1)
                            return $"CIOT será cancelado automaticamente em {diasParaCancelar} dias";
                        
                        if (diasParaCancelar == 1)
                            return $"CIOT será cancelado automaticamente em 1 dia";
                        
                        if(diasParaCancelar == 0)
                            return $"CIOT será cancelado automaticamente hoje";
                    }

                    if (c.DeclaracaoCiot.ViagensVinculadas.Any(v => v.StatusViagem != EStatusViagem.Cancelada) && c.DataFinal < dataHoje)
                    {
                        var diasParaEncerrar = (c.DataFinal.Value.AddDays(30) - dataHoje).Days;
                        if (diasParaEncerrar > 1)
                            return $"CIOT será encerrado automaticamente em {diasParaEncerrar} dias";
                        
                        if (diasParaEncerrar == 1)
                            return $"CIOT será encerrado automaticamente em 1 dia";
                        
                        if(diasParaEncerrar == 0)
                            return $"CIOT será encerrado automaticamente hoje";
                    }
                    
                    break;
            }

            return null;
        }
        
        public CarregarContratoAgregadoModel CarregarContratoAgregado(int idContratoAgregado, int idEmpresa)
        {
            var contratoCiotAgregado = _contratoCiotAgregadoService.GetWithAllIncludes(idContratoAgregado);

            var contratoMapped = Mapper.Map<CarregarContratoAgregadoModel>(contratoCiotAgregado);
            foreach (var veiculoPersistido in contratoCiotAgregado.ContratoCiotAgregadoVeiculos)
            {
                var contratoVeiculoMapped = contratoMapped.Veiculos.FirstOrDefault(x => x.IdVeiculo == veiculoPersistido.IdVeiculo);
                if (contratoVeiculoMapped == null) continue;
                contratoVeiculoMapped.InclusoCiot = veiculoPersistido.InclusoCiot;
            }
            
            Service.GerarToken(idEmpresa);

            if (!contratoCiotAgregado.IdDeclaracaoCiot.HasValue)
                return contratoMapped;
            
            var viagens = Service.GetViagensNaoCanceladas(contratoCiotAgregado.IdDeclaracaoCiot.Value);
            ICollection<ViagemModelAgregado> viagensModel = Mapper.Map<Collection<ViagemModelAgregado>>(viagens);
            
            if(viagensModel == null)
                throw new Exception("Ocorreu um erro ao carregar as viagens do contrato");
            
            contratoMapped.TotalViagens = viagensModel.Count;
            contratoMapped.Viagens = new List<ViagemModelAgregado>();
            viagensModel.GroupBy(v => new {ClienteOrigem = v.MunicipioOrigem, ClienteDestino = v.MunicipioDestino, v.NaturezaCarga})
                .ToList().ForEach(grp =>
                {
                    var viagensAgrupadas = viagensModel.Where(x =>
                        x.MunicipioOrigem == grp.Key.ClienteOrigem &&
                        x.MunicipioDestino == grp.Key.ClienteDestino &&
                        x.NaturezaCarga == grp.Key.NaturezaCarga).ToList();

                    contratoMapped.Viagens.Add(new ViagemModelAgregado
                    {
                        MunicipioOrigem = grp.Key.ClienteOrigem,
                        MunicipioDestino = grp.Key.ClienteDestino,
                        NaturezaCarga = grp.Key.NaturezaCarga,
                        PesoSaida = Math.Floor(viagensAgrupadas.Sum(x => x.PesoSaida) ?? 0),
                        Quantidade = viagensAgrupadas.Count(),
                        ValorMercadoria = viagensAgrupadas.Sum(x => x.ValorMercadoria),
                        ValorImpostos = viagensAgrupadas.Sum(x => x.ValorImpostos)
                    });
                });
            
            contratoMapped.Valores = Service.CalcularValoresViagem(viagens);

            return contratoMapped;
        }

        public CancelarCiotResult CancelarCiot(Viagem viagem)
        {
            try
            {
                var empresaRepository = _empresaRepository;
                var empresa = empresaRepository.Get(viagem.IdEmpresa);

                Service.GerarToken(empresa.CNPJ);
                return Service.CancelarCiot(viagem);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e,
                        "Erro ao cancelar CIOT para viagem: {0}".FormatEx(viagem.IdViagem));

                return new CancelarCiotResult
                {
                    Sucesso = false,
                    Cancelado = false,
                    Mensagem = e.Message
                };
            }
        }

        public CancelarCiotResult CancelarCiotAgregado(CancelarContratoAgregadoModel requestModel, int idEmpresa)
        {
            try
            {
                Service.GerarToken(idEmpresa);

                var contratoCiotAgregado = Service.GetContratoAgregado(requestModel.ContratoAgregadoId);
                if (contratoCiotAgregado == null)
                    return new CancelarCiotResult
                    {
                        Mensagem = "Erro ao encontrato contrado de agregado",
                        Sucesso = false
                    };

                return contratoCiotAgregado.CancelarCiotAgregado(requestModel.MotivoCancelamento,
                    _declaracaoCiotRepository, _contratoCiotAgregadoRepository,
                    Service, null, EVersaoAntt.Versao2);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao cancelar CIOT para viagem");

                return new CancelarCiotResult
                {
                    Sucesso = false,
                    Cancelado = false,
                    Mensagem = e.Message
                };
            }
        }

        public string LinkComprovanteContrato(ImprimirComprovanteContratoAgregadoModel requestModel)
        {
            try
            {
                var urlBuilder = new StringBuilder();
                urlBuilder.Append(ConfigurationManager.AppSettings["MS_URL.Externo"].SetEndChars("/"));
                urlBuilder.Append("ciot/ServicesFull/Reports/DeclaracaoTransporte/");
                urlBuilder.Append(requestModel.Ciot);
                urlBuilder.Append("/");
                urlBuilder.Append(requestModel.Senha);

                return urlBuilder.ToString();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao resolver link para o comprovante do contrato");

                return null;
            }
        }

        /// <summary>
        /// Gerar tag de retorno ao consumidor com os dados referentes ao CIOT
        /// </summary>
        /// <param name="idViagem"></param>
        /// <param name="idEmpresa"></param>
        /// <returns>Null se não houver CIOT vinculado a viagem</returns>
        public DeclararCiotResult GetCiotResult(int idViagem, int idEmpresa)
        {
            Service.GerarToken(idEmpresa);
            return Service.GetCiotResult(idViagem, idEmpresa);
        }

        public ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request)
        {
            Service.GerarToken(request.CpfCnpjInteressado);
            return Service.ConsultarFrotaTransportador(request);
        }

        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request)
        {
            Service.GerarToken(request.CpfCnpjInteressado);
            return Service.ConsultarSituacaoTransportador(request);
        }

        public ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario,
            string cnpjEmpresa)
        {
            Service.GerarToken(cnpjEmpresa);
            return Service.EquiparadoTac(cpfCnpjProprietario, rntrcProprietario);
        }

        public VeiculoRntrcDTO ObterVeiculoPorPlaca(ViagemCarreta carreta, int idEmpresa)
        {
            return Service.ObterVeiculoPorPlaca(carreta, idEmpresa);
        }
        
        public bool AlgumContratoAberto(int idProprietario, int idEmpresa)
        {
            return _contratoCiotAgregadoService.AnyContratoAberto(idProprietario, idEmpresa);
        }
        
        public List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa)
        {
            return _contratoCiotAgregadoService.GetPlacasContratoAberto(idProprietario, idEmpresa);
        }
        
        public AtualizarCiotResponse AtualizarCiot(int idDeclaracaoCiot)
        {
            var declaracaoCiot = Service.GetDeclaracaoCiotIncludeViagens(idDeclaracaoCiot);

            if (declaracaoCiot == null || declaracaoCiot.ViagensVinculadas?.Any() != true)
                return new AtualizarCiotResponse {Sucesso = true};

            Service.GerarToken(declaracaoCiot.IdEmpresa);

            var eventosFrete = new List<DadosAtualizacaoCiotViagemEventoDto>();
            foreach (var viagem in declaracaoCiot.ViagensVinculadas)
            {
                if (viagem.StatusViagem == EStatusViagem.Cancelada || viagem.ViagemEventos?.Any(ve => 
                    ve.Status != EStatusViagemEvento.Cancelado && !ve.TipoEventoViagem.In(ETipoEventoViagem.TarifaAntt, ETipoEventoViagem.Abastecimento)) != true)
                    continue;
                
                eventosFrete.AddRange(viagem.ViagemEventos.Where(ve => 
                    ve.Status != EStatusViagemEvento.Cancelado && !ve.TipoEventoViagem.In(ETipoEventoViagem.TarifaAntt, ETipoEventoViagem.Abastecimento)));
            }

            var request = new AtualizarCiotRequest
            {
                Ciot = declaracaoCiot.Ciot,
                SenhaAlteracao = declaracaoCiot.Senha.ToInt(),
                Valores = new AtualizarCiotValorRequest
                {
                    ValorFrete = eventosFrete.Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                    ValorFretePago = eventosFrete.Where(ve => ve.Status == EStatusViagemEvento.Baixado)
                        .Sum(ve => ve.ValorTotalPagamento ?? ve.ValorPagamento),
                    ValorInss = declaracaoCiot.ViagensVinculadas.Sum(v => v.INSS),
                    ValorIrrf = declaracaoCiot.ViagensVinculadas.Sum(v => v.IRRPF),
                    ValorSestSenat = declaracaoCiot.ViagensVinculadas.Sum(v => v.SESTSENAT)
                }
            };

            if (declaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Padrao && !string.IsNullOrWhiteSpace(declaracaoCiot.ViagensVinculadas.First().DocumentoCliente))
                request.Frete = new AtualizarCiotFreteRequest {DocumentoCliente = declaracaoCiot.ViagensVinculadas.First().DocumentoCliente};

            return Service.AtualizarCiot(request);
        }
        
        public ValidationResult DesvincularCiot(Viagem viagem,Usuario usuario)
        {
            var validation = new ValidationResult();

            var declaraco = _declaracaoCiotRepository.ConsultarCiot(viagem.IdViagem);

            if (declaraco == null)
                return validation.Add($"Viagem não possui ciot vinculado!");
                
            //Update para nulo
            declaraco.IdViagemCiotDesvinculado = declaraco.IdViagem;
            declaraco.IdViagem = null;
            declaraco.DataDesvinculo = DateTime.Now;
            declaraco.Usuario = usuario;
            viagem.DeclaracaoCiot = null;
            
            _declaracaoCiotRepository.Update(declaraco);
            _viagemRepository.Update(viagem);

            return validation;
        }
    }
}
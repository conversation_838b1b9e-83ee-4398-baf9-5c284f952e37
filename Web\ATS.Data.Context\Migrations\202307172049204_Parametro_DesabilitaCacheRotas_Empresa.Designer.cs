﻿// <auto-generated />
namespace ATS.Data.Context.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.2.0-61023")]
    public sealed partial class Parametro_DesabilitaCacheRotas_Empresa : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(Parametro_DesabilitaCacheRotas_Empresa));
        
        string IMigrationMetadata.Id
        {
            get { return "202307172049204_Parametro_DesabilitaCacheRotas_Empresa"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}

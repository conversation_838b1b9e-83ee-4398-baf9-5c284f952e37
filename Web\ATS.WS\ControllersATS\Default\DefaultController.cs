﻿using ATS.Application.Application;
using ATS.Domain.Service;
using ATS.WS.Attributes;
using ATS.WS.Models.Webservice;
using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Web.Configuration;
using System.Web.Mvc;
using System.Web.Routing;
using System.Web.Script.Serialization;
using ATS.Application.Interface;
using ATS.Application.Interface.Common;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using Autofac;

namespace ATS.WS.ControllersATS.Default
{
    public abstract class BaseAtsController<TEntityApp> : DefaultController where TEntityApp : IBaseApp
    {
        public readonly IUserIdentity SessionUser;
        public readonly TEntityApp App;

        protected BaseAtsController(TEntityApp app, IUserIdentity userIdentity)
        {
            App = app;
            SessionUser = userIdentity;
        }
    }
    
    public class DefaultController : Controller
    {
        public static Logger Logger = LogManager.GetCurrentClassLogger();

        public virtual JsonResult BigJson(object data)
        {
            return new JsonResult
            {
                Data = data,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                MaxJsonLength = 2147483644
            };
        }

        public static string GetLogo(int idEmpresa)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var empresaApp = scope.Resolve<IEmpresaApp>();
                var logoEmpresa = empresaApp.GetQuery(idEmpresa).Select(x => x.Logo).FirstOrDefault();
                var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);

                return logo;
            }
        }


        #region Overrides
        protected override void Initialize(RequestContext requestContext)
        {
            base.Initialize(requestContext);

            if (Request.HttpMethod.ToUpper() == "OPTIONS") return;

            // Validar se o método possui a configuração especifica para ignorar a validação de permissão.
            var memberInfo = GetType().GetMethods().FirstOrDefault(m => Request.CurrentExecutionFilePath.ToLower().Contains(m.Name.ToLower()));

            if (memberInfo != null)
            {
                if (memberInfo.GetCustomAttributes(typeof(Autorizar)).Any())
                {
                }
            }
        }
        #endregion

        #region Enderecos

        [HttpGet]
        public JsonResult ConsultaLatitudeLongitudePorEndereco(string endereco, string bairro, string cidade, string estado, string pais, int numero)
        {
            try
            {
                decimal latitude;
                decimal longitude;

                new AdressService().SetCoordinatesObject(numero, endereco, bairro, cidade, estado, pais, out latitude, out longitude);

                return ResponderSucesso(string.Empty, new
                {
                    latitude,
                    longitude
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        public JsonResult ConsultaEnderecoViaCep(string cep)
        {
            try
            {
                using (var scope = IoC.Container.BeginLifetimeScope())
                {
                    var estadoApp = scope.Resolve<IEstadoApp>();
                    var request = (HttpWebRequest)WebRequest.Create("http://viacep.com.br/ws/" + cep + "/json/");
                    request.Method = "GET";
                    request.ContentType = "application/json";


                    var response = (HttpWebResponse)request.GetResponse();
                    var responseString = new StreamReader(response.GetResponseStream()).ReadToEnd();
                    var endereco = new JavaScriptSerializer().Deserialize<EnderecoViaCep>(responseString);

                    endereco.estado = estadoApp.GetEstadoBySigla(endereco.uf)?.Nome;

                    return ResponderSucesso(string.Empty, endereco);
                }
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        #endregion

        #region Retornos padrões

        private static readonly DefaultController Controller = new DefaultController();

        public string GetCurrentControllerName()
        {
            return ControllerContext.Controller.ToString().Split('.')[2];
        }

        public static JsonResult ResponderSucesso(string msg)
        {
            return Controller.Responder(true, msg);
        }

        public static JsonResult ResponderSucesso(List<MultipleMessageReturnTyped> msgs, string mainMessage = "")
        {
            return Controller.Responder(true, msgs, mainMessage);
        }

        public static JsonResult ResponderSucesso(string msg, object data)
        {
            return Controller.Responder(true, msg, data);
        }

        public static JsonResult ResponderSucesso(object data)
        {
            return Controller.Responder(true, "", data);
        }

        public static JsonResult ResponderErro(string message)
        {
            return Controller.Responder(false, message);
        }

        public static JsonResult ResoinderErro(string msg, object data)
        {
            return Controller.Responder(false, msg, data);
        }

        public static JsonResult ResponderErro(Exception ex)
        {
            Logger.Error(ex);

            var msg = ex.Message;
            if (!string.IsNullOrWhiteSpace(ex.InnerException?.Message) && !msg.Contains(ex.InnerException.Message))
                msg += " / " + ex.InnerException.Message;
            
            var mensagemBase = ex.GetBaseException().Message;
            if (!string.IsNullOrEmpty(mensagemBase) && !msg.Contains(mensagemBase))
                msg += " / " + mensagemBase;

            return Controller.Responder(false, msg);
        }

        public static JsonResult ResponderPadrao(bool sucesso, string msg = "", object data = null)
        {
            return Controller.Responder(sucesso, msg, data);
        }

        private JsonResult Responder(bool sucesso, string msg, bool warningRequired)
        {
            return Json(new
            {
                message = msg ?? "",
                success = sucesso,
                warning = warningRequired
            }, JsonRequestBehavior.AllowGet);
        }

        private JsonResult Responder(bool sucesso, string msg)
        {
            return Json(new
            {
                message = msg ?? "",
                success = sucesso,
                warning = false
            }, JsonRequestBehavior.AllowGet);
        }

        private JsonResult Responder(bool sucesso, List<MultipleMessageReturnTyped> msgs, string mainMsg = "")
        {
            return Json(new
            {
                multipleMessagesType = true,
                messages = msgs,
                message = mainMsg,
                success = sucesso,
                warning = false
            }, JsonRequestBehavior.AllowGet);
        }

        public JsonResult Responder(bool sucesso, string msg, object data, string error = null)
        {
            return BigJson(new
            {
                message = msg ?? "",
                success = sucesso,
                warning = false,
                data,
                error
            });
        }

        public static JsonResult ResponderAtencao(bool sucesso, string msg = "")
        {
            return Controller.Responder(sucesso, msg, true);
        }


        #endregion

        #region Métodos privados
        public bool ValidarTokenWebConfig(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            string tokenFromWebConfig = WebConfigurationManager.AppSettings["Token"];
            if (token.ToLower() == tokenFromWebConfig.ToLower())
                return true;

            return false;
        }

        #endregion

        public static string GetLogo(int? idEmpresa)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                if (!idEmpresa.HasValue)
                    return ConstantesUtils.SemImagem;

                var empresaApp = scope.Resolve<IEmpresaApp>();
                var logoEmpresa = empresaApp.GetQuery(idEmpresa.Value).Select(x => x.Logo).FirstOrDefault();
                var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);

                return logo;
            }
        }
    }

    public class MultipleMessageReturnTyped
    {
        public string message { get; set; }
        public ETypeReturnMessage type { get; set; }
    }

    public enum ETypeReturnMessage
    {
        Success = 0,
        Alert = 1,
        Error = 2,
        Information = 3,
        Exclamation = 4,
        Custom = 5
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Triggers;
using ATS.Domain.Service;
using ATS.Domain.Trigger.Base;

namespace ATS.Domain.Trigger
{
    public class CargaAvulsaTrigger : Trigger<CargaAvulsa>, ICargaAvulsaTrigger
    {
        public CargaAvulsaTrigger()
        {
            /*this.RegisterAfterTrigger(EOperationTrigger.Insert, (d,e) => new CargaAvulsaService().CreateTransaction(d,e), "CreateTransaction");*/
        }
    }
}
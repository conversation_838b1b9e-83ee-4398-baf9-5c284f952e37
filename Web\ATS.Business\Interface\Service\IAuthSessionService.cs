﻿using ATS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Interface.Service
{
    public interface IAuthSessionService
    {
         AuthSession GetByToken(string Token);
         AuthSession Gerar(int idUsuario);
         void AtualizaDataUltimaRequisicao(string token);
         void InvalidarSessao(string sessionKey);
         void InvalidarSessoesAtivasParaUsuario(int idUsuario);
         int GetTotalRequisicoes();
         int GetIdUsuario(string Token);
    }
}

﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IProdutoApp : IAppBase<Produto>
    {
        ValidationResult Add(Produto produto);
        ValidationResult Update(Produto produto);
        ValidationResult Inativar(int idProduto);
        ValidationResult Reativar(int idproduto);

        object ConsultaGrid(int? idEmpresa, int? idProduto, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters);

        List<Produto> ConsultarPorEmpresa(int emp);
        Produto GetPorId(int value);
    }
}

﻿using System.Collections;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Cartoes.TransferenciaContasBancarias
{
    public class RelatorioTransferenciaCotnasBancariasFull
    {
        public IList<RelatorioTransferenciaContasBancariasDataType> DataType { get; set; }
        public ResumoTransferenciaContaBancaria Resumo { get; set; }
    }
    public class RelatorioTransferenciaContasBancariasDataType
    {
        public int? TransacaoId { get; set; }
        public string IdentificadorCartao { get; set; }
        public string CpfCnpjPortador { get; set; }
        public string NomePortador { get; set; }
        public string Conta { get; set; }
        public string TipoTransacao { get; set; }
        public long? ProtocoloRequisicao { get; set; }
        public string Valor { get; set; }
        public string Status { get; set; }
        public string StatusExportacao { get; set; }
        public string MensagemExportacao { get; set; }
        public string NomeContaBancaria { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataExportacao { get; set; }
        public string DataAlteracaoExportacao { get; set; }
    }

    public class ResumoTransferenciaContaBancaria
    {
        public string ValorTotalNaoExportados { get; set; }
        public string ValorTotalExportados { get; set; }
        public string ValorTotalErros { get; set; }
        public string ValorTotalSucessos { get; set; }
        public string ValorTotalItems { get; set; }
        public string NumNaoExportados { get; set; }
        public string NumExportados { get; set; }
        public string NumErros { get; set; }
        public string NumSucessos { get; set; }
        public string NumTotalItems { get; set; }
    };
}
using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.PedagioRota;

namespace ATS.Application.Application
{
    public class PedagioRotaApp : BaseApp<IPedagioRotaService>, IPedagioRotaApp
    {
        public PedagioRotaApp(IPedagioRotaService service) : base(service)
        {
        }

        public PedagioRotaSalvarResponse Salvar(PedagioRotaSalvarRequest request)
        {
            return Service.Salvar(request);
        }

        public IList<PedagioRotaGridResponse> ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarGrid(take, page, order, filters);
        }

        public PedagioRotaDetalheResponse ConsultarDetalhes(int idRota)
        {
            return Service.ConsultarDetalhes(idRota);
        }
    }
}
﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class CredenciamentoRepository : Repository<Credenciamento>, ICredenciamentoRepository
    {
        public CredenciamentoRepository(AtsContext context) : base(context)
        {
        }
        
        public bool EstabelecimentoCredenciado(int idEstabelecimento, int idEmpresa)
        {
            return Any(x => x.IdEstabelecimento == idEstabelecimento && x.IdEmpresa == idEmpresa);
        }
    }
}

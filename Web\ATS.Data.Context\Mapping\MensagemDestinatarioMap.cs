﻿using System.ComponentModel.DataAnnotations.Schema;
using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class MensagemDestinatarioMap : EntityTypeConfiguration<MensagemDestinatario>
    {
        public MensagemDestinatarioMap()
        {
            ToTable("MENSAGEM_DESTINATARIO");

            HasKey(x => x.IdMensagemDestinatario);
                
            Property(x => x.IdMensagemDestinatario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

        //    HasRequired(x => x.MensagemDestino)
        //        .WithMany(x => x.Destinatario).HasForeignKey(x => x.IdMensagem);

            Property(x => x.IdMensagem).IsRequired();

            HasRequired(x => x.UsuarioDestinatario)
                .WithMany(x => x.Destinatarios)
                .HasForeignKey(x => x.IdUsuarioDestinatario);

            HasRequired(x => x.MensagemDestino)
                .WithMany(x => x.MensagemDestinatario).HasForeignKey(x => x.IdMensagem);
        }
    }
}

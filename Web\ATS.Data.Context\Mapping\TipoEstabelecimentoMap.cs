﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TipoEstabelecimentoMap : EntityTypeConfiguration<TipoEstabelecimento>
    {
        public TipoEstabelecimentoMap()
        {
            ToTable("TIPO_ESTABELECIMENTO");

            HasKey(x => x.IdTipoEstabelecimento);

            Property(x => x.DataUltimaAtualizacao)
                .IsOptional();

            Property(x => x.IdEmpresa)
                .IsOptional();

            HasOptional(x => x.Empresa)
                .WithMany(x => x.TipoEstabelecimentos)
                .HasForeignKey(x => x.IdEmpresa);
            
            HasOptional(x => x.Icone)
                .WithMany(x => x.TipoEstabelecimentos)
                .HasForeignKey(x => x.IdIcone);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioClienteRepository : Repository<UsuarioCliente>, IUsuarioClienteRepository
    {
        public UsuarioClienteRepository(AtsContext context) : base(context)
        {
        }
        
        public ValidationResult Remove(UsuarioCliente usuarioCliente)
        {
            if (usuarioCliente != null)
                Delete(usuarioCliente, false);

            return new ValidationResult();
        }
    }
}

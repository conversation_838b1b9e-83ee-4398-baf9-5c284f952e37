using System;
using ATS.WS.Models.Common.Request.Base;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Common.Request
{
    public class EventoRequestModel : RequestBase
    {
        public new string DocumentoUsuarioAudit 
        {
            get { return!string.IsNullOrWhiteSpace(_documentoUsuarioAudit) ? _documentoUsuarioAudit : CpfUsuario.OnlyNumbers(); }
            set {_documentoUsuarioAudit = value.OnlyNumbers();}
        }
        private string _documentoUsuarioAudit { get; set; }
        [JsonIgnore]
       // [Obsolete("Utilizar os campos de auditoria da request base")]
        public string CpfUsuario { get; set; }

        public new string NomeUsuarioAudit 
        {
            get { return !string.IsNullOrWhiteSpace(_nomeUsuarioAudit) ? _nomeUsuarioAudit : NomeUsuario.RemoveAccents().RemoveSpecialCaracter(true); }
            set {_nomeUsuarioAudit = value.RemoveAccents().RemoveSpecialCaracter(true);}
        }
        private string _nomeUsuarioAudit { get; set; }

        [JsonIgnore]
        //[Obsolete("Utilizar os campos de auditoria da request base")]
        public string NomeUsuario { get; set; }
        public int? IdViagem { get; set; }
        public int? IdViagemEvento { get; set; }
        public string NumeroControleViagem { get; set; }
        public string NumeroControleEvento { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }

    public class CancelarEventoRequestModel: EventoRequestModel
    {
    }

    public class BloquearEventoRequestModel : EventoRequestModel
    {
        
    }

    public class DesbloquearEventoRequestModel : EventoRequestModel
    {
        
    }
}
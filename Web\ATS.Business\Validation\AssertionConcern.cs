﻿using ATS.Domain.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using ATS.CrossCutting.IoC;
using Microsoft.Ajax.Utilities;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using NLog;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Validation
{
    public class AssertionConcern
    {
        /// <summary>
        /// Validar se os valores são iguais. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="object"></param>
        /// <param name="object1"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentEquals(object @object, object object1, string message)
        {
            if (!@object.Equals(object1))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o tamanho (Length) do valor não esta superior ao valor especificado. Estando, exibe mensagem.
        /// </summary>
        /// <param name="stringValue"></param>
        /// <param name="maximum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentLength(string stringValue, int maximum, string message)
        {
            int length = stringValue.Trim().Length;
            if (length > maximum)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o tamanho (Length) do valor esta dentro do intervalo especificado. Não estando, exibe mensagem.
        /// </summary>
        /// <param name="stringValue"></param>
        /// <param name="minimum"></param>
        /// <param name="maximum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentLength(string stringValue, int minimum, int maximum, string message)
        {
            if (string.IsNullOrEmpty(stringValue))
                stringValue = string.Empty;

            int length = stringValue.Trim().Length;
            if (length < minimum || length > maximum)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor confere com a expressão Regex de teste. Não se adequando ao teste, exibe a mensagem.
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stringValue"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentMatches(string pattern, string stringValue, string message)
        {
            if (!new Regex(pattern).IsMatch(stringValue))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é diferente de string vazia. Sendo igual, exibe a mensagem.
        /// </summary>
        /// <param name="stringValue"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentNotEmpty(string stringValue, string message)
        {
            if (string.IsNullOrEmpty(stringValue))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor não é null (nulo) ou uma string contendo espaço em branco. Sendo uma das situações, exibe a mensagem.
        /// </summary>
        /// <param name="stringValue"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentNotNullOrWhiteSpace(string stringValue, string message)
        {
            if (string.IsNullOrWhiteSpace(stringValue))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se os valores não são iguais. Sendo, exibe a mensagem
        /// </summary>
        /// <param name="object"></param>
        /// <param name="object2"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentNotEquals(object @object, object object2, string message)
        {
            if (@object.Equals(object2))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor não é nulo (null). Sendo, exibe a mensagem.
        /// </summary>
        /// <param name="object"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentNotNull(object @object, string message)
        {
            if (@object == null)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é nulo (null). Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="stringValue"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsNull(string stringValue, string message)
        {
            if (stringValue != null)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor esta dentro do intervalo especificado. Não estando, exibe a mensagem.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="minimum"></param>
        /// <param name="maximum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentRange(double value, double minimum, double maximum, string message)
        {
            if (value < minimum || value > maximum)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor esta dentro do intervalo especificado. Não estando, exibe a mensagem.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="minimum"></param>
        /// <param name="maximum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentRange(float value, float minimum, float maximum, string message)
        {
            if (value < minimum || value > maximum)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor esta dentro do intervalo especificado. Não estando, exibe a mensagem.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="minimum"></param>
        /// <param name="maximum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentRange(int value, int minimum, int maximum, string message)
        {
            if (value < minimum || value > maximum)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor esta dentro do intervalo especificado. Não estando, exibe a mensagem.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="minimum"></param>
        /// <param name="maximum"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentRange(long value, long minimum, long maximum, string message)
        {
            if (value < minimum || value > maximum)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é um falso (false). Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="boolValue"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertStateFalse(bool boolValue, string message)
        {
            if (boolValue)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é verdadeiro (true). Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="boolValue"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertStateTrue(bool boolValue, string message)
        {
            if (!boolValue)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é uma senha válida. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="pswd"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertPassword(string pswd, string message)
        {
            if (string.IsNullOrWhiteSpace(pswd) || pswd.Length < 5)
                return new ValidationError($"Senha deve possuir no mínimo 5 caracteres.");

            return null;
        }

        /// <summary>
        /// Validar se o valor é um CNPJ válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="cpf"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidCPF(string cpf, string message)
        {
            if (string.IsNullOrEmpty(cpf))
                return new ValidationError(message);

            int[] mt1 = new int [9] {10, 9, 8, 7, 6, 5, 4, 3, 2};
            int[] mt2 = new int[10] {11, 10, 9, 8, 7, 6, 5, 4, 3, 2};
            string TempCPF;
            string Digito;
            int soma;
            int resto;

            cpf = cpf.OnlyNumbers()?.Trim();

            if (cpf?.Length != 11)
                return new ValidationError(message);

            TempCPF = cpf.Substring(0, 9);
            soma = 0;
            for (int i = 0; i < 9; i++)
                soma += int.Parse(TempCPF[i].ToString()) * mt1[i];

            resto = soma % 11;
            if (resto < 2)
                resto = 0;
            else
                resto = 11 - resto;

            Digito = resto.ToString();
            TempCPF = TempCPF + Digito;
            soma = 0;

            for (int i = 0; i < 10; i++)
                soma += int.Parse(TempCPF[i].ToString()) * mt2[i];

            resto = soma % 11;
            if (resto < 2)
                resto = 0;
            else
                resto = 11 - resto;

            Digito = Digito + resto;
            if (!cpf.EndsWith(Digito))
                return new ValidationError(message);

            if (cpf[0] == cpf[1] && cpf[0] == cpf[2] && cpf[0] == cpf[3] && cpf[0] == cpf[4] && cpf[0] == cpf[5] &&
                cpf[0] == cpf[6] && cpf[0] == cpf[7] && cpf[0] == cpf[8] && cpf[0] == cpf[9] && cpf[0] == cpf[10])
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é um CPF válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="cnpj"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidCNPJ(string cnpj, string message)
        {
            cnpj = cnpj.OnlyNumbers();

            if (cnpj.Length != 14)
                return new ValidationError(message);

            int[] digitos, soma, resultado;
            int nrDig;
            string ftmt;
            bool[] CNPJOk;

            ftmt = "6543298765432";
            digitos = new int[14];
            soma = new int[2];
            soma[0] = 0;
            soma[1] = 0;
            resultado = new int[2];
            resultado[0] = 0;
            resultado[1] = 0;
            CNPJOk = new bool[2];
            CNPJOk[0] = false;
            CNPJOk[1] = false;

            for (nrDig = 0; nrDig < 14; nrDig++)
            {
                digitos[nrDig] = int.Parse(cnpj.Substring(nrDig, 1));

                if (nrDig <= 11)
                    soma[0] += (digitos[nrDig] * int.Parse(ftmt.Substring(nrDig + 1, 1)));

                if (nrDig <= 12)
                    soma[1] += (digitos[nrDig] * int.Parse(ftmt.Substring(nrDig, 1)));
            }

            for (nrDig = 0; nrDig < 2; nrDig++)
            {
                resultado[nrDig] = (soma[nrDig] % 11);
                if ((resultado[nrDig] == 0) || (resultado[nrDig] == 1))
                    CNPJOk[nrDig] = (digitos[12 + nrDig] == 0);
                else
                    CNPJOk[nrDig] = (digitos[12 + nrDig] == (11 - resultado[nrDig]));
            }

            if (!(CNPJOk[0] && CNPJOk[1]))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é CEP válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="cep"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidCEP(string cep, string message, bool valueRequired = true)
        {
            if (cep.IsNullOrWhiteSpace())
            {
                if (valueRequired)
                    return new ValidationError("CEP não informado");

                return null;
            }

            if (!Regex.IsMatch(cep.OnlyNumbers(), "[0-9]{8}"))
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor é e-mail válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="email"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidEmail(string email, string message)
        {
            if (!RegexUtilities.IsValidEmail(email))
                return new ValidationError(message);

            return null;
        }
        /// <summary>
        /// Validar se o valor é e-mail válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="email"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static bool IsValidEmail(string email)
        {
            var trimmedEmail = email.Trim();

            if (trimmedEmail.EndsWith(".")) {
                return false; // suggested by @TK-421
            }
            try {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == trimmedEmail;
            }
            catch {
                return false;
            }
        }

        /// <summary>
        /// Validar se o valor é um telefone válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="telefone"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidTelefone(string telefone, string message)
        {
            if (telefone.Length > 11 || telefone.Length < 10)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o objeto possui algum elemento. Não possuindo, exibe a mensagem.
        /// </summary>
        /// <param name="object"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentHasElement(IEnumerable<object> @object, string message)
        {
            if (!@object.Any())
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se a latitude é um valor válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="latitude"></param>
        /// <param name="message"></param>
        /// <param name="mandatory"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidLatitude(decimal? latitude, string message,
            bool mandatory = false)
        {
            if (!latitude.HasValue)
            {
                if (mandatory)
                    return new ValidationError(message);

                return null;
            }

            if (latitude < -90 || latitude > 90)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se a longitude é um valor válido. Não sendo, exibe a mensagem.
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="message"></param>
        /// <param name="mandatory"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidLongitude(decimal? longitude, string message,
            bool mandatory = false)
        {
            if (!longitude.HasValue)
            {
                if (mandatory)
                    return new ValidationError(message);

                return null;
            }

            if (longitude < -180 || longitude > 180)
                return new ValidationError(message);

            return null;
        }

        /// <summary>
        /// Validar se o valor possui caracterer especial. Possuindo, exibe a mensagem.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ValidationError AssertArgumentNotHasSpecialCaracter(string value, string message)
        {
            return AssertArgumentMatches(@"^[a-zA-Z0-9 ]*$", value, message);
        }

        /// <summary>
        /// Validar se o valor informado e um login válido para o sistema. Não sendo, exibe a mensagem
        /// </summary>
        /// <param name="value">Login</param>
        /// <param name="message">Mensagem de erro</param>
        /// <returns></returns>
        public static ValidationError AssertArgumentIsValidLogin(string value, string message)
        {
            // Caso o login termine com "_" irá retornar como inválido.
            if (value.EndsWith("_"))
                return new ValidationError(message);

            // Validar de acordo com o Regex
            if (!new Regex(@"^(?=[a-zA-Z0-9])[-\w.]{0,23}([a-zA-Z\d]|(?<![-.])_)$").IsMatch(value))
                return new ValidationError(message);

            return null;
        }

        public static ValidationError AssertArgumentIsValidPlaca(IEmpresaRepository empresaRepository, string value, string message, int idEmpresa)
        {
            var empresaPossuiPlacaEspeciais = empresaRepository
                .Where(x => x.IdEmpresa == idEmpresa && x.UtilizaPlacasEspeciais == true).Any();

            if (string.IsNullOrWhiteSpace(value))
                return new ValidationError(message);

            Regex regex = Veiculo.RegexBrasil;
            Regex regexMercosul = new Regex("");


            if (!empresaPossuiPlacaEspeciais)
            {
                regexMercosul = Veiculo.RegexMercosul;
            }

            if (!regex.IsMatch(value) && !regexMercosul.IsMatch(value))
                return new ValidationError(message);

            return null;
        }
        
        public static ValidationError AssertArgumentIsValidPlaca(string value, string message)
        {
            if (string.IsNullOrEmpty(value))
                return new ValidationError(message);
            
            if (value.Length > 7)
                return new ValidationError(message);
            
            var regex = new Regex(@"[A-Z]{3}[0-9]{1}[A-Z]{1}[0-9]{2}|[A-Z]{3}[0-9]{4}", RegexOptions.IgnoreCase);
            
            return !regex.IsMatch(value) ? new ValidationError(message) : null;
        }

        public static ValidationError AssertArgumentIsValidCnh(string value, string message, bool validarSomenteQuantidadeCaracteres = false)
        {
            try
            {
                if (value.IsNullOrWhiteSpace())
                    return new ValidationError(message);
                
                if (value.Length != 11)
                    return new ValidationError(message);

                if (validarSomenteQuantidadeCaracteres)
                    return null;

                if (value.Equals("11111111111") || value.Equals("22222222222") || value.Equals("33333333333")
                    || value.Equals("44444444444") || value.Equals("55555555555") || value.Equals("66666666666")
                    || value.Equals("77777777777") || value.Equals("88888888888") || value.Equals("99999999999")
                    || value.Equals("00000000000"))
                {
                    return new ValidationError(message);
                }

                var fracao = new int[9];
                var acumulador = 0;
                var inc = 2;
                for (var i = 0; i < 9; i++)
                {
                    fracao[i] = (Math.Abs(int.Parse(value.Substring(i, 1)))) * inc;
                    acumulador += fracao[i];
                    inc++;
                }

                var resto = acumulador % 11;
                var digito1 = 0;
                if (resto > 1)
                {
                    digito1 = 11 - resto;
                }

                acumulador = digito1 * 2;
                inc = 3;
                for (var i = 0; i < 9; i++)
                {
                    fracao[i] = (Math.Abs(int.Parse(value.Substring(i, 1)))) * inc;
                    acumulador += Math.Abs(fracao[i]);
                    inc++;
                }

                resto = acumulador % 11;
                var digito2 = 0;
                if (resto > 1)
                {
                    digito2 = 11 - resto;
                }

                if (digito1 == Math.Abs(int.Parse(value.Substring(9, 1)))
                    && digito2 == Math.Abs(int.Parse(value.Substring(10, 1))))
                {
                    return null;
                }

                return new ValidationError(message);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return new ValidationError(message);
            }
        }
    }
}
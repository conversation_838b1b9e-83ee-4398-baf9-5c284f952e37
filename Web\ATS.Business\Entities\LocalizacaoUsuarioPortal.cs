using System;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    /// <summary>
    /// Usado para guardar localizacoes distintas em que o usuario fez login
    /// </summary>
    [TrackChanges]
    public class LocalizacaoUsuarioPortal
    {
        public int Id { get; set; }
        
        public int IdUsuario { get; set; }
        
        public string Ip { get; set; }
        
        public decimal Latitude { get; set; }
        
        public decimal Longitude { get; set; }
        
        public string Cidade { get; set; }
        
        public string Provedor { get; set; }
        
        public string Estado { get; set; }
        
        public DateTime DataCadastro { get; set; }
        
        #region Referências

        /// <summary>
        /// Empresa vinculado ao usuário
        /// </summary>
        public virtual Usuario Usuario { get; set; }
        
        #endregion
    }
}

﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using Newtonsoft.Json;
using ATS.WS.Models.Webservice.Request.TipoCavalo;
using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class TipoCavaloModel : RequestBase
    {
        public int IdTipoCavalo { get; set; }
        public string Nome { get; set; }
        public ECategoriaTipoCavalo Categoria { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CategoriaDescricao { get; set; }
        public int? NumeroEixos { get; set; }
        public int? Capacidade { get; set; }
        public bool Ativo { get; set; } = true;
        public List<TipoCavaloClienteRequest> TipoCavaloCliente { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;
using ATS.Domain.Models;
using System;
using ATS.Domain.DTO;
using ATS.Domain.Enum;

namespace ATS.Domain.Interface.Dapper
{
    public interface IPagamentoDapper : IRepositoryDapper<ViagemEvento>
    {
        IEnumerable<TotalEstabelecimentoModel> ConsultarTotalCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF, int page_, int take_ , int idempresa);
        int CountTotalCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, int idempresa);
        decimal SumValorTotalABC(DateTime dataInicio_, DateTime dataFim_, string UF_, double take_, double skip_, int idempresa);
        List<TotalEventoModel> GetTotalPagamento(DateTime dataInicio_, DateTime dataFinal_, int? UF_, EStatusViagemEvento status_, int HabilitarPagamentoCartao, int? idEstabelecimento, int? idEmpresa);
        IList<FaturamentoGridDto> RelatorioFaturamento(DateTime dataInicial, DateTime datafinal, int? empresaFiltro);
    }
}
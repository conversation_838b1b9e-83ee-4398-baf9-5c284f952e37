﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Cartoes.TransferenciaContasBancarias
{
    public class RelatorioTransferenciaContasBancarias
    {
        public byte[] GetReport(string tipo, RelatorioTransferenciaCotnasBancariasFull listaDados, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;
                
                var parametros = new ReportParameter[11];

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaDados.DataType,
                    Name = "DtsTransferenciasBancarias"
                });

                var path = ReportUtils.CreateLogo(logo);
                parametros[0] = new ReportParameter("Logo", "file:///" + path);
                parametros[1] = new ReportParameter("ValorTotalNaoExportados",listaDados.Resumo.ValorTotalNaoExportados);
                parametros[2] = new ReportParameter("ValorTotalExportados",listaDados.Resumo.ValorTotalExportados);
                parametros[3] = new ReportParameter("ValorTotalErros",listaDados.Resumo.ValorTotalErros);
                parametros[4] = new ReportParameter("ValorTotalSucessos",listaDados.Resumo.ValorTotalSucessos);
                parametros[5] = new ReportParameter("ValorTotalItems",listaDados.Resumo.ValorTotalItems);
                parametros[6] = new ReportParameter("NumNaoExportados",listaDados.Resumo.NumNaoExportados);
                parametros[7] = new ReportParameter("NumExportados",listaDados.Resumo.NumExportados);
                parametros[8] = new ReportParameter("NumErros",listaDados.Resumo.NumErros);
                parametros[9] = new ReportParameter("NumSucessos",listaDados.Resumo.NumSucessos);
                parametros[10] = new ReportParameter("NumTotalItems",listaDados.Resumo.NumTotalItems);

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Cartoes.TransferenciaContasBancarias.RelatorioTransferenciaContasBancarias.rdlc";
                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }
    }
}

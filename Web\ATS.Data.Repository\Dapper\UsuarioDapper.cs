using System;
using System.Data;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using <PERSON>pper;

namespace ATS.Data.Repository.Dapper
{
    public class UsuarioDapper : DapperFactory<Usuario>, IUsuarioDapper
    {
        public ConsultaInformacoesMobileModel ConsultarInformacoesMobile(int itensPorPagina, int pagina, int? empresaId, string documento)
        {
            var whereClause = string.Empty;

            if (empresaId.HasValue)
                whereClause += $" WHERE U.idempresa = @EmpresaId ";

            if (!string.IsNullOrEmpty(documento))
                whereClause += string.IsNullOrEmpty(whereClause)
                    ? $" WHERE U.cpfcnpj = @Documento "
                    : $" AND U.cpfcnpj = @Documento ";
            
            var queryTotal = $@"SELECT COUNT(U.idusuario)
                                FROM USUARIO U
                                       LEFT JOIN CHECKIN_RESUMO CR on U.idusuario = CR.idusuario
                                       LEFT JOIN USUARIO_PERMISSOES_CONCEDIDAS_MOBILE UPCM on U.idusuario = UPCM.idusuario
                                {whereClause}";

            var queryItems = $@"DECLARE
                                  @ItensPorPagina INT = {itensPorPagina},
                                  @Pagina INT = {pagina}

                                SELECT U.idusuario                              AS UsuarioId,
                                       U.nome                                   AS UsuarioNome, 
                                       U.dataultimoacessoaplicativo             AS DataUltimoLogin,
                                       U.dataultimaaberturaaplicativo           AS DataUltimoAcesso,
                                       CR.datahora                              AS DataUltimoCheckin,
                                       UPCM.permitelercalendario                AS PermiteLerCalendario,
                                       UPCM.permiteescrevercalendario           AS PermiteLerCalendario,
                                       UPCM.permiteusarcamera                   AS PermiteUsarCamera,
                                       UPCM.permitelercontatos                  AS PermiteLerContatos,
                                       UPCM.permiteescrevercontatos             AS PermiteEscreverContatos,
                                       UPCM.permitebuscarcontatos               AS PermiteBuscarContatos,
                                       UPCM.permiteacessolocalizacao            AS PermiteAcessoLocalizacao,
                                       UPCM.permitegravaraudio                  AS PermiteGravarAudio,
                                       UPCM.permitelerestadotelefone            AS PermiteLerEstadoTelefone,
                                       UPCM.permiterealizarchamada              AS PermiteRealizarChamada,
                                       UPCM.permitelerlogchamada                AS PermiteLerLogChamada,
                                       UPCM.permiteescreverlogchamada           AS PermiteEscreverLogChamada,
                                       UPCM.permiteadicionarcorreiovoz          AS PermiteAdicionarCorreioVoz,
                                       UPCM.permiteusarsip                      AS PermiteUsarSip,
                                       UPCM.permiteprocessarchamadasaida        AS PermiteProcessarChamadaSaida,
                                       UPCM.permiteusarsensorescomporais        AS PermiteUsarSensoresComporais,
                                       UPCM.permiteenviarsms                    AS PermiteEnviarSms,
                                       UPCM.permiterecebermms                   AS PermiteReceberSms,
                                       UPCM.permitelersms                       AS PermiteLerSms,
                                       UPCM.permitereceberwappush               AS PermiteReceberWapPush,
                                       UPCM.permiterecebermms                   AS PermiteReceberMms,
                                       UPCM.permitelerarmazenamentoexterno      AS PermiteLerArmazenamentoExterno,
                                       UPCM.permiteescreverarmazenamentoexterno AS PermiteEscreverArmazenamentoExterno,
                                       UPCM.permiteacessarinformacoesrede       AS PermiteAcessarInformacoesRede,
                                       UPCM.permiteinternet                     AS PermiteInternet,
                                       UPCM.permiteforeground                   AS PermiteForeground,
                                       UPCM.permitejanelasoverlay               AS PermiteJanelasOverlay,
                                       UPCM.permiteimpedirbloqueio              AS PermiteImpedirBloqueio,
                                       UPCM.permiteignorarotimizacoesbateria    AS PermiteIgnorarOtimizacoesBateria,
                                       UPCM.permitereceberconfirmacaoinicio     AS PermiteReceberConfirmacaoInicio,
                                       UPCM.permitelerperfil                    AS PermiteLerPerfil
                                FROM USUARIO U
                                       LEFT JOIN CHECKIN_RESUMO CR on U.idusuario = CR.idusuario
                                       LEFT JOIN USUARIO_PERMISSOES_CONCEDIDAS_MOBILE UPCM on U.idusuario = UPCM.idusuario
                                {whereClause}
                                ORDER BY U.idusuario
                                  OFFSET (@Pagina - 1) * @ItensPorPagina ROWS
                                  FETCH NEXT @ItensPorPagina ROWS ONLY";
            
            using (var connection = new DapperContext().GetConnection)
            {
                var parameters = new
                {
                    EmpresaId = empresaId, 
                    Documento = documento
                };
                
                var totalRegistros = connection.Query<int>(queryTotal, parameters).FirstOrDefault();
                var registros = connection.Query<ConsultaInformacoesMobileItemsModel>(queryItems, parameters).ToList();
                
                return new ConsultaInformacoesMobileModel { TotalRegistros = totalRegistros, Registros = registros };
            }
        }
        
        public ValidationResult UpdatePush(int idUsuario, string idPush, ESistemaOperacional? sistemaOperacional = null)
        {
            var validationResult = new ValidationResult();
            try
            {
                var parameters = new DynamicParameters();
                parameters.Add("idUsuario", idUsuario);
                parameters.Add("idPush", idPush);
                parameters.Add("sistemaOperacional", sistemaOperacional);

                var sql = $"UPDATE USUARIO SET idPush = @idPush, sistemaOperacional = @sistemaOperacional  WHERE idUsuario = @idUsuario";
                using (IDbConnection dbConnection = this.GetConnection())
                    dbConnection.Execute(sql, parameters);
            }
            catch (Exception e)
            {
                validationResult.Add(e.Message);
            }

            return validationResult;
        }
    }
}
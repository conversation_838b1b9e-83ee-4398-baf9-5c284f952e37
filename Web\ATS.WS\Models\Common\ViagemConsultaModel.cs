﻿using ATS.Domain.Enum;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class ViagemConsultaModel
    {
        public int IdViagem { get; set; }

        public int IdEmpresa { get; set; }

        public string RazaoSocialEmpresa { get; set; }

        public string PaisClienteOrigem { get; set; }

        public string UFClienteOrigem { get; set; }

        public string CidadeClienteOrigem { get; set; }

        public string PaisClienteDestino { get; set; }

        public string UFClienteDestino { get; set; }

        public string CidadeClienteDestino { get; set; }

        public string PaisClienteTomador { get; set; }
        public string UFClienteTomador { get; set; }
        public string CidadeClienteTomador { get; set; }

        public int QuantidadeCargas { get; set; }

        public string NumeroDocumento { get; set; }

        public string DataEmissao { get; set; }

        public string Produto { get; set; }

        public EUnidadeMedida Unidade { get; set; }

        public decimal Quantidade { get; set; }
        public string CPFCNPJClienteTomador { get; set; }

        // Impostos
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal IRRPF { get; set; } = 0;
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal INSS { get; set; } = 0;
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal SESTSENAT { get; set; } = 0;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EStatusCheckViagem? StatusUltimoCheckViagem { get; set; } = null;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EStatusViagem StatusViagem { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string TokenViagem { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? PesoChegada { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? PesoSaida { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? PesoDiferenca { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorMercadoria { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorDifFreteMotorista { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorQuebraMercadoria { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DataLancamento { get; set; }

        public List<ViagemRegrasConsultaModel> ViagemRegras { get; set; }
        public List<ViagemEstabelecimentoConsultaModel> ViagemEstabelecimentos { get; set; }
        public List<ViagemEventoConsultaModel> ViagemEventos { get; set; }

    }

    public class ViagemRegrasConsultaModel
    {
        public decimal? TaxaAntecipacao { get; set; }
        public decimal? ToleranciaPeso { get; set; }
        public decimal? TarifaTonelada { get; set; }
        public ETipoQuebraMercadoria TipoQuebraMercadoria { get; set; }
    }

    public class ViagemEstabelecimentoConsultaModel
    {
        public int IdViagemEstabelecimento { get; set; }
        public int IdEstabelecimento { get; set; }
        public int IdViagem { get; set; }
        public ETipoEventoViagem TipoEventoViagem { get; set; }
    }

    public class ViagemEventoConsultaModel
    {
        public int IdViagemEvento { get; set; }
        public int? IdProtocolo { get; set; }
        public int? IdEstabelecimentoBase { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public decimal ValorPagamento { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public decimal ValorBruto { get; set; }
        public string DataPagamento { get; set; }
        public string HoraPagamento { get; set; }
        public string DataValidade { get; set; }
        public string NumeroRecibo { get; set; }
        public string Instrucao { get; set; }
        public string Token { get; set; }
        public EStatusViagemEvento Status { get; set; }

        public List<ViagemDocumentoConsultaModel> ViagemDocumentos { get; set; }
        public List<ViagemValorAdicionalConsultaModel> ViagemOutrosDescontos { get; set; }
        public List<ViagemValorAdicionalConsultaModel> ViagemOutrosAcrescimos { get; set; }
        public decimal SESTSENAT { get; internal set; }
        public decimal IRRPF { get; internal set; }
        public decimal INSS { get; internal set; }
        public EOrigemIntegracao OrigemPagamento { get; set; }
    }

    public class ViagemDocumentoConsultaModel
    {
        public int IdDocumento { get; set; }
        public int IdEvento { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public string Descricao { get; set; }
        public ETipoDocumento TipoDocumento { get; set; }
        public int NumeroDocumento { get; set; }
        public bool ObrigaAnexo { get; set; }
    }

    public class ViagemValorAdicionalConsultaModel
    {
        public int IdViagemOutrosDescontos { get; set; }
        public int NumeroDocumento { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
    }
}
﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;

namespace ATS.Application.Interface
{
    public interface ITipoDocumentoApp : IAppBase<TipoDocumento>
    {
        TipoDocumento Get(int id);
        ValidationResult Add(TipoDocumento entity);
        ValidationResult Update(TipoDocumento entity);
        ValidationResult Inativar(int idTipoDocumento);
        ValidationResult Reativar(int idTipoDocumento);
        IQueryable<TipoDocumento> All();
        int? GetCnh();
    }
}
﻿using System;
using System.IO;
using System.Threading;
using System.Web;

namespace ATS.CrossCutting.IoC.Utils
{
    public static class ReportUtils
    {
        public static string CreateLogo(string logo)
        {
            var folder = HttpContext.Current != null
                ? new DirectoryInfo(HttpContext.Current.Server.MapPath("/") + "Relatorios\\images")
                : new DirectoryInfo(Path.Combine(Environment.GetEnvironmentVariable("LocalAppData"), "Ats", "Imagens"));

            if (!folder.Exists)
                folder.Create();

            var fileName = folder.FullName + "\\Logo.png";
            var logoBase64 = Convert.FromBase64String(logo);
            
            if (logoBase64.Length == 0)
                return string.Empty;

            for (var i = 0; i < 2; i++)
            {
                try
                {
                    var lFileStream = new FileStream(fileName, FileMode.Create);

                    try
                    {
                        lFileStream.Write(logoBase64, 0, logoBase64.Length);
                    }
                    finally
                    {
                        lFileStream.Close();
                    }

                    break;
                }
                catch
                {
                    if (i >= 1)
                        throw;

                    Thread.Sleep(3000);
                }
            }

            return fileName;
        }
        
        public static string CreateLogoCheckList(string logo, int idEmpresa)
        {
            var folder = HttpContext.Current != null
                ? new DirectoryInfo(HttpContext.Current.Server.MapPath("/") + "Relatorios\\images")
                : new DirectoryInfo(Path.Combine(Environment.GetEnvironmentVariable("LocalAppData"), "Ats", "Imagens"));

            if (!folder.Exists)
                folder.Create();

            var fileName = folder.FullName + $"\\Logo{idEmpresa}.png";
            var logoBase64 = Convert.FromBase64String(logo);
            
            if (logoBase64.Length == 0)
                return string.Empty;

            for (var i = 0; i < 2; i++)
            {
                try
                {
                    var lFileStream = new FileStream(fileName, FileMode.Create);

                    try
                    {
                        lFileStream.Write(logoBase64, 0, logoBase64.Length);
                    }
                    finally
                    {
                        lFileStream.Close();
                    }

                    break;
                }
                catch
                {
                    if (i >= 1)
                        throw;

                    Thread.Sleep(3000);
                }
            }

            return fileName;
        }
    }
}

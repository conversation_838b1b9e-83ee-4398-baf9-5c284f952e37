﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class EstabelecimentoContaBancariaApp : AppBase, IEstabelecimentoContaBancariaApp
    {
        private readonly IEstabelecimentoContaBancariaService _service;

        public EstabelecimentoContaBancariaApp(IEstabelecimentoContaBancariaService service)
        {
            _service = service;
        }

        public List<object> GetContasBancariasByEstabalecimento(int idEstabelecimento)
        {
            return _service.GetContasBancariasByEstabalecimento(idEstabelecimento);
        }
    }
}

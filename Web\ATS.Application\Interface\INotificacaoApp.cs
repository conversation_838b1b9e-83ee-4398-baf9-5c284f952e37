﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Application.Interface
{
    public interface INotificacaoApp : IAppBase<Notificacao>
    {
        /// <summary>
        /// Adicionar a mensagem
        /// </summary>
        /// <param name="notificacao">Informações sobre a notificação</param>
        /// <returns></returns>
        Notificacao Add(Notificacao notificacao);

        IQueryable<Notificacao> ObterNotificacoesNaoLidas(int idUsuario);
        IQueryable<Notificacao> GetNotificacoesPeloUsuario(int idUsuario);
        ValidationResult SetRecebidoNovo(List<int> ids);
        ValidationResult SetWithLido(int id);
    }
}
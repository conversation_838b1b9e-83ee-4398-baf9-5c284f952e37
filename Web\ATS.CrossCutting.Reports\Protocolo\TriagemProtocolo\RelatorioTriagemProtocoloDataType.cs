﻿namespace ATS.CrossCutting.Reports.Protocolo.TriagemProtocolo
{
    public class RelatorioTriagemProtocoloDataType
    {
        public string Codigo { get; set; }

        public string Estabelecimento { get; set; }

        public string DataGeracao { get; set; }

        public string DataPagamento { get; set; }

        public string DataPrevisaoPagamento { get; set; }

        public string DataAprovacao { get; set; }

        public string ValorProtocolo { get; set; }

        public string Status { get; set; }

        public string Pago { get; set; }

        public string NumeroEventos { get; set; }

        public string PagamentoAntecipado { get; set; }

        public string CnpjEstabelecimento { get; set; }
    }
}

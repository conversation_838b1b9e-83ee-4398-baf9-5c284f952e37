﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net48" />
  <package id="Autofac" version="8.2.0" targetFramework="net48" />
  <package id="AutoMapper" version="4.2.1" targetFramework="net48" />
  <package id="BouncyCastle.NetFramework" version="1.8.5.2" targetFramework="net48" />
  <package id="ClosedXML" version="0.95.4" targetFramework="net48" />
  <package id="DnsClient" version="1.6.1" targetFramework="net48" />
  <package id="DocumentFormat.OpenXml" version="2.7.2" targetFramework="net48" />
  <package id="EntityFramework" version="6.2.0" targetFramework="net48" />
  <package id="EntityFramework.MappingAPI" version="6.1.0.9" targetFramework="net48" />
  <package id="ExcelDataReader" version="3.6.0" targetFramework="net48" />
  <package id="ExcelDataReader.DataSet" version="3.6.0" targetFramework="net48" />
  <package id="ExcelNumberFormat" version="1.0.10" targetFramework="net48" />
  <package id="FluentValidation" version="6.2.1.0" targetFramework="net48" />
  <package id="Google.Apis" version="1.68.0" targetFramework="net48" />
  <package id="Google.Apis.Auth" version="1.68.0" targetFramework="net48" />
  <package id="Google.Apis.Core" version="1.68.0" targetFramework="net48" />
  <package id="MassTransit.Abstractions" version="8.2.0-develop.1655" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.2" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="8.6.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens" version="8.6.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.Memory" version="9.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.TimeProvider" version="8.0.1" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Abstractions" version="8.6.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Logging" version="8.6.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.1.0" targetFramework="net48" />
  <package id="MongoDB.Driver" version="3.2.0" targetFramework="net48" />
  <package id="Microsoft.Win32.Registry" version="5.0.0" targetFramework="net48" />
  <package id="MongoDB.Bson" version="3.2.0" targetFramework="net48" />
  <package id="MongoDB.Driver.Core" version="2.7.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NLog" version="4.7.2" targetFramework="net48" />
  <package id="QRCoder" version="1.3.2" targetFramework="net48" />
  <package id="SharpCompress" version="0.30.1" targetFramework="net48" />
  <package id="Snappier" version="1.0.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.CodeDom" version="7.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.FileSystem.Primitives" version="4.0.1" targetFramework="net48" />
  <package id="System.IO.Packaging" version="4.0.0" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="8.6.0" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net48" />
  <package id="System.Text.Encoding.CodePages" version="5.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.2" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.2" targetFramework="net48" />
  <package id="System.Linq.Dynamic" version="1.0.7" targetFramework="net48" />
  <package id="System.Management" version="7.0.2" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.2" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="TrackerEnabledDbContext" version="3.6.1" targetFramework="net48" />
  <package id="TrackerEnabledDbContext.Common" version="3.6.1" targetFramework="net48" />
  <package id="WebGrease" version="1.6.0" targetFramework="net48" />
  <package id="ZstdSharp.Port" version="0.7.3" targetFramework="net48" />
</packages>
﻿using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Models;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Email;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.WS.Services
{
    public class SrvEmail : SrvBase
    {
        private readonly ILayoutApp _layoutApp;
        private readonly IEmailApp _emailApp;
        private readonly IEmpresaApp _empresaApp;

        public SrvEmail(ILayoutApp layoutApp, IEmailApp emailApp, IEmpresaApp empresaApp)
        {
            _layoutApp = layoutApp;
            _emailApp = emailApp;
            _empresaApp = empresaApp;
        }


        public Retorno<object> EnviarEmail(EnviarEmailRequest @params)
        {
            try
            {
                var empresa = _empresaApp.Get(@params.CNPJEmpresa);


                if (string.IsNullOrEmpty(@params.Corpo))
                    return new Retorno<object>(false, "O corpo do e-mail não foi informado", null);

                if (string.IsNullOrEmpty(@params.NomeUsuarioApp))
                    return new Retorno<object>(false, "O nome do usuário do aplicativo não foi informado", null);

                if (empresa == null)
                    return new Retorno<object>(false, "Empresa não encontrada", null);

                if (string.IsNullOrEmpty(empresa.EmailSugestoes))
                    return new Retorno<object>(false, "O endereço de e-mail de sujestões não está cadastrado.", null);

                var nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;

                var layout = _layoutApp.GetPorEmpresa(empresa.IdEmpresa);
                if (layout != null && !string.IsNullOrWhiteSpace(layout.NomeAplicativo))
                    nomeAplicativo = layout.NomeAplicativo;

                using (var streamReader = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\sugestao-motorista.html"))
                {
                    var body = @params.Corpo;

                    var logoAts =
                        new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-ats-login.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoFacebook =
                        new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\facebook.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var html = streamReader.ReadToEnd();

                    html = html.Replace("{0}", logoAts.ContentId);
                    html = html.Replace("{LogoFace}", logoFacebook.ContentId);
                    html = html.Replace("{Empresa}", empresa.NomeFantasia);
                    html = html.Replace("{NomeMotorista}", @params.NomeUsuarioApp);
                    html = html.Replace("{BodyMsg}", body);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoAts);
                    view.LinkedResources.Add(logoFacebook);

                    var emailModel = new EmailModel
                    {
                        Assunto = "Envio de sugestão do motorista " + @params.NomeUsuarioApp,
                        Destinatarios = new List<string>
                        {
                            empresa.EmailSugestoes
                        },
                        Prioridade = MailPriority.High,
                        AlternateView = view
                    };

                    var resultado = _emailApp.EnviarEmail(emailModel);

                    return !resultado.IsValid
                        ? new Retorno<object>(false, resultado.Errors.FirstOrDefault()?.Message, null)
                        : new Retorno<object>(true);
                }
            }
            catch (Exception e)
            {
                return new Retorno<object>(false, e.Message, null);
            }
        }
    }
}
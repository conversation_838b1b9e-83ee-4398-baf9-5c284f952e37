using System;
using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class CheckinResumoConsultaModel
    {
        public int ResumosTotal { get; set; }
        
        public List<CheckinResumoItensConsultaModel> Resumos { get; set; }
    }

    public class CheckinResumoItensConsultaModel
    {
        public int CheckinId { get; set; }

        public int? MotoristaId { get; set; }

        public string MotoristaCpf { get; set; }

        public string MotoristaNome { get; set; }

        public string UsuarioCpfCnpj { get; set; }

        public string UsuarioNome { get; set; }

        public int CidadeIbge { get; set; }

        public string CidadeNome { get; set; }

        public string Uf { get; set; }

        public string Placa { get; set; }

        public DateTime DataHora { get; set; }

        public int TipoEvento { get; set; }

        public decimal Latitude { get; set; }

        public decimal Longitude { get; set; }
    }
}
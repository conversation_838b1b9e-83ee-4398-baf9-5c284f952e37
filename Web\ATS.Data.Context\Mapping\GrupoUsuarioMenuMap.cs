﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class GrupoUsuarioMenuMap : EntityTypeConfiguration<GrupoUsuarioMenu>
    {
        public GrupoUsuarioMenuMap()
        {
            ToTable("GRUPO_USUARIO_MENU");

            HasKey(t => new { t.IdGrupoUsuario, t.IdMenu });

            Property(t => t.IdGrupoUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdMenu)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
        }
    }
}
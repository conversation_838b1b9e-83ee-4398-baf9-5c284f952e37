using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ContratoCiotAgregadoMap : EntityTypeConfiguration<ContratoCiotAgregado>
    {
        public ContratoCiotAgregadoMap()
        {
            ToTable("CONTRATO_CIOT_AGREGADO");

            HasKey(t => t.IdContratoCiotAgregado);

            Property(x => x.DataInicio)
                .HasColumnName("datainicio")
                .IsRequired()
                .HasColumnType("datetime");

            Property(x => x.DataFinal)
                .HasColumnName("datafinal")
                .IsRequired()
                .HasColumnType("datetime");

            Property(x => x.DataCadastro)
                .HasColumnName("datacadastro")
                .IsRequired()
                .HasColumnType("datetime");

            Property(x => x.IdUsuario)
                .HasColumnName("idusuario")
                .IsOptional();
            
            Property(x => x.IdDeclaracaoCiot)
                .HasColumnName("iddeclaracaociot")
                .IsOptional();

            Property(x => x.IdProprietario)
                .HasColumnName("idproprietario")
                .IsRequired();
            
            Property(x => x.Status)
                .HasColumnName("status")
                .IsRequired();

            Property(x => x.CanceladoAutomaticamente)
                .HasColumnName("canceladoautomaticamente")
                .IsRequired();

            Property(x => x.ResultadoDeclaracaoCiot).IsOptional();

            Property(x => x.MensagemDeclaracaoCiot).IsOptional().HasMaxLength(2000);
            
            HasRequired(t => t.Empresa)
                .WithMany(t => t.ContratoCiotAgregados)
                .HasForeignKey(t => t.IdEmpresa);

            HasRequired(t => t.Proprietario)
                .WithMany()
                .HasForeignKey(t => new {t.IdProprietario, t.IdEmpresa});

            HasOptional(t => t.Usuario)
                .WithMany(t => t.ContratoCiotAgregados)
                .HasForeignKey(t => t.IdUsuario);

            HasOptional(t => t.DeclaracaoCiot)
                .WithMany()
                .HasForeignKey(t => t.IdDeclaracaoCiot);

            HasMany(t => t.ContratoCiotAgregadoVeiculos)
                .WithRequired(d => d.ContratoCiotAgregado)
                .HasForeignKey(d => d.IdContratoCiotAgregado);

            Property(o => o.OrigemEncerramento)
                .IsOptional();
        }
    }
}

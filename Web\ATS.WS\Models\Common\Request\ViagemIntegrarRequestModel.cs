﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;
using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using Newtonsoft.Json;

namespace ATS.WS.Models.Common.Request
{
    public class ViagemIntegrarRequestModel : RequestBase
    {
        public int? IdViagem { get; set; }
        public int? IdViagemComplementada { get; set; }
        public string CNPJFilial { get; set; }
        public string CodigoFilial { get; set; }
        public string CPFCNPJClienteDestino { get; set; }
        public string CPFCNPJClienteOrigem { get; set; }
        public string CPFMotorista { get; set; }
        public string CPFCNPJProprietario { get; set; }
        public string CPFCNPJClienteTomador { get; set; }
        public string NomeProprietario { get; set; }
        public int? RNTRC { get; set; }
        public int? IdCarga { get; set; }
        public string RazaoSocialFilial { get; set; }

        public string Placa { get; set; }
        public List<string> Carretas { get; set; }
        public DateTime? DataColeta { get; set; }
        public DateTime? DataPrevisaoEntrega { get; set; }
        public EStatusViagem StatusViagem { get; set; }
        public EStatusIntegracao StatusIntegracao { get; set; }
        public string DocumentoCliente { get; set; }
        public string Coleta { get; set; }
        public string Entrega { get; set; }

        public String NumeroNota { get; set; }
        public String NumeroDocumento { get; set; }
        public string NumeroDocumentoComplementado { get; set; }
        public DateTime? DataEmissao { get; set; }
        public string Produto { get; set; }
        public int? NaturezaCarga { get; set; }
        public EUnidadeMedida Unidade { get; set; }
        public decimal Quantidade { get; set; }
        public decimal? ValorPedagio { get; set; } = 0;

        /// <summary>
        /// Indica se o pedágio já é considerado pago ou não.
        /// Influencia no valor que é instruido o posto para trocar a carta frete do motorista.
        /// Caso não for token de adiantamento, não pagando no cartão:
        /// -  PedagioBaixado = false, incluir o valor do pedágio no pagamento do adiantamento do posto. O protocolo gerado posteriormente também deve respeitar a adição do valor do pedágio no adiantamento.
        /// - PedagioBaixado = true, paga apenas o valor do adiantamento. O protoclo gerado posteriormente deve ter apenas o valor do adiantamento.
        ///  Utilizar função "DeveIncluirPedagioJuntoComPagamentoDoEvento" na classe "PagamentoFreteService" para auxiliar na utilização desta regra.
        /// </summary>
        public bool? PedagioBaixado { get; set; } = false;

        public decimal? PesoSaida { get; set; }
        public decimal? ValorMercadoria { get; set; }
        public DateTime DataLancamento { get; set; }
        public string NumeroCartao { get; set; }

        // Impostos
        public decimal IRRPF { get; set; }
        public decimal INSS { get; set; }
        public decimal SESTSENAT { get; set; }

        public List<ViagemRegraIntegrarModel> ViagemRegra { get; set; }
        public List<ViagemEventoIntegrarModel> ViagemEventos { get; set; } = new List<ViagemEventoIntegrarModel>();
        public List<ViagemEstabelecimentoIntegrarModel> ViagemEstabelecimentos { get; set; }

        /// <summary>
        /// Indica que a nossa plataforma deve realizar a gestão do CIOT, veriricar a obrigatoriedade e declarar a operação caso positivo.
        /// O cliente consumidor da plataforma possui a opção de enviar este campo como falso, pois o mesmo pode ter realizado o CIOT por outro fornecedor, e estar consumindo nosso serviço apenas para o pagamento de cartão.
        /// </summary>
        public bool HabilitarDeclaracaoCiot { get; set; }

//        Pedagio
        public PedagioModel Pedagio { get; set; }

        public Dictionary<string, WebHookIntegrarRequest> Webhook { get; set; }

        public string NumeroControle { get; set; }

        public List<ViagemDocumentoFiscalModel> DocumentosFiscais { get; set; }

        public bool ForcarCiotNaoEquiparado { get; set; } = false;

        public string CepOrigem { get; set; }

        public string CepDestino { get; set; }

        public int? CodigoTipoCarga { get; set; }

        public int? DistanciaViagem { get; set; }

        public ViagemDadosPagamentoIntegrarModel DadosPagamento { get; set; }

        public ViagemDadosAnttIntegrarModel DadosAntt { get; set; }

        public List<ViagemCarretasIntegrarModel> CarretasViagemV2 { get; set; }
        public List<ViagemCarretasV2IntegrarModel> CarretasV2 { get; set; }
        public bool? GerarCiotTacAgregado { get; set; }
        public DateTime? DataAtualizacao { get; set; }
        public ViagemIntegrarDadosBancarioSemCartao DadosBancarioPagamentoSemCartao { get; set; }
    }

    public class ViagemRegraIntegrarModel
    {
        public int? IdViagemRegra { get; set; }
        public decimal? TaxaAntecipacao { get; set; }
        public decimal? ToleranciaPeso { get; set; }
        public decimal? TarifaTonelada { get; set; }
        public decimal? TotalFrete { get; set; }
        public bool FreteLotacao { get; set; } = false;
        public ETipoQuebraMercadoria? TipoQuebraMercadoria { get; set; }
        public string UnidadeMedida { get; set; } = "KG";
    }

    public class ViagemEstabelecimentoIntegrarModel
    {
        public int? IdViagemEstabelecimento { get; set; }
        public int? IdEstabelecimento { get; set; }
        public int IdViagem { get; set; }
        public ETipoEventoViagem? TipoEventoViagem { get; set; }
    }

    public class ViagemEventoIntegrarModel
    {
        public bool HabilitarPagamentoCartao { get; set; } = false;
        /// <summary>
        /// Possui valor true ou false quando a integração vem do Portal, possui null quando vem da api de integração
        /// </summary>
        public bool? HabilitarPagamentoPix { get; set; } = null;
        public int? IdViagemEvento { get; set; }
        public int IdViagem { get; set; }
        public string NumeroControle { get { return _numeroControle;} set { _numeroControle = string.IsNullOrEmpty(value) ? null : value?.Trim(); } }
        private string _numeroControle { get; set; }
        public ETipoEventoViagem? TipoEvento { get; set; }
        public decimal? ValorPagamento { get; set; }
        public DateTime? DataValidade { get; set; }
        public string NumeroRecibo { get; set; }
        public string Instrucao { get; set; }

        public EStatusViagemEvento? Status { get; set; }

        public decimal? ValorBruto { get; set; }
        public int? IdMotivo { get; set; }

        // Impostos
        public decimal? IRRPF { get; set; }
        public decimal? INSS { get; set; }
        public decimal? SESTSENAT { get; set; }

        public string MotivoBloqueio { get; set; }

        public ViagemEventoIntegrarAbastecilmentoModel DadosAbastecimento { get; set; }

        public List<ViagemDocumentoIntegrarModel> ViagemDocumentos { get; set; }
        public List<ViagemValorAdicionalIntegrarModel> ViagemOutrosDescontos { get; set; }
        public List<ViagemValorAdicionalIntegrarModel> ViagemOutrosAcrescimos { get; set; }

        /// <summary>
        /// Campo de auditoria
        /// </summary>
        public string CpfUsuario { get; set; }

        /// <summary>
        /// Campo de auditoria
        /// </summary>
        public string NomeUsuario { get; set; }

        /// <summary>
        /// Quando o evento nasce e já vai ser pago, ele ainda não tem um id no banco, então essa propriedade vai identificar o evento para não fazer um FirstOrDefault.
        /// Visto que o ATS pode receber parcelas do mesmo tipo de evento, por exemplo entrar 3 eventos de adiantamento com data de pagamento para d, d+15 e d+30.
        /// Também podem existir parcelas pagas de integrações anteriores da mesma viagem, e posteriormente adicionado um novo evento do mesmo tipo.
        /// </summary>
        [JsonIgnore]
        public string Token { get; set; }

        /// <inheritdoc cref="ViagemEvento.DataAgendamentoPagamento"/>
        public DateTime? DataAgendamentoPagamento { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class ViagemEventoIntegrarAbastecilmentoModel
    {
        public EFornecedorPedagio Fornecedor { get; set; } = EFornecedorPedagio.TicketLog;
        public int? CodigoCredito { get; set; }
        public long CodigoProduto { get; set; }
        public string NumeroCartao { get; set; }
        public DateTime? DataValidade { get; set; }
        public DateTime? DataLiberacao { get; set; }
    }

    public class ViagemDocumentoIntegrarModel
    {
        public int? IdViagemDocumento { get; set; }
        public int IdEvento { get; set; }
        public ETipoEventoViagem? TipoEvento { get; set; }
        public string Descricao { get; set; }
        public ETipoDocumento? TipoDocumento { get; set; }
        public int NumeroDocumento { get; set; }
        public bool? ObrigaAnexo { get; set; }
        public bool? ObrigaAnexoMatriz { get; set; }
        public bool? ObrigaAnexoFilial { get; set; }
        public bool? ObrigaDocOriginal { get; set; }
    }

    public class ViagemValorAdicionalIntegrarModel
    {
        public int? IdViagemValorAdicional { get; set; }
        public int NumeroDocumento { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
        public long? CodigoERP { get; set; }
    }

    public class PedagioModel
    {
        public FornecedorEnum? Fornecedor { get; set; }
        public ETipoVeiculoPedagioEnum? TipoVeiculo { get; set; }
        public int QtdEixos { get; set; }
        public List<LocalizacaoDTO> Localizacoes { get; set; }
        public List<PracasDTO> Pracas { get; set; }
        public decimal? ValorPedagio { get; set; }
        public Guid? IdentificadorHistorico { get; set; }
        public string NomeRota { get; set; }
        public int? IdRotaModelo { get; set; }
        public int? CodPolyline { get; set; }
        public long? TagSerialNumber { get; set; }

        public bool Istag()
        {
            return Fornecedor == FornecedorEnum.Outros || Fornecedor == FornecedorEnum.Veloe || Fornecedor == FornecedorEnum.MoveMais || Fornecedor == FornecedorEnum.ViaFacil || Fornecedor == FornecedorEnum.ViaFacil || Fornecedor == FornecedorEnum.ExtrattaTag;
        }

        public ValidationResult ValidarEntrada()
        {
            if (Fornecedor.HasValue)
                if (!Enum.IsDefined(typeof(FornecedorEnum), Fornecedor))
                    return new ValidationResult().Add("Fornecedor inválido.", EFaultType.Error);
            
            if (TipoVeiculo.HasValue)
                if (!Enum.IsDefined(typeof(ETipoVeiculoPedagioEnum), TipoVeiculo))
                    return new ValidationResult().Add("Tipo de veículo inválido.", EFaultType.Error);

            if (ValorPedagio.HasValue && ValorPedagio.Value < 0)
                return new ValidationResult().Add("Valor do pedágio não pode ser negativo.", EFaultType.Error);

            return new ValidationResult();
        }
    }

    public class ViagemDocumentoFiscalModel
    {
        public int? IdViagemDocumentoFiscal { get; set; }

        public decimal NumeroDocumento { get; set; }

        public string Serie { get; set; } = "0";

        public decimal PesoSaida { get; set; } = 1;

        public decimal? Valor { get; set; } = 1;

        public int? IdClienteOrigem { get; set; }

        public int? IdClienteDestino { get; set; }

        public ETipoDocumento TipoDocumento { get; set; }
        public string Chave { get; set; } 
    }

    public class ViagemCarretasIntegrarModel
    {
        public string Placa { get; set; }
        public string Rntrc { get; set; }
        public string CPFCNPJProprietario { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (!string.IsNullOrEmpty(Placa))
                if (Placa.Length > 7)
                    return new ValidationResult().Add("Placa não pode conter mais que 7 caracteres.", EFaultType.Error);

            if (string.IsNullOrEmpty(Rntrc))
                return new ValidationResult().Add("RNTRC não foi preenchido.", EFaultType.Error);

            if (!string.IsNullOrEmpty(Rntrc))
                if (Rntrc.Length > 9)
                    return new ValidationResult().Add("RNTRC não pode conter mais que 9 caracterres.", EFaultType.Error);

            return new ValidationResult();
        }
    }

    public class ViagemCarretasV2IntegrarModel
    {
        public string CPFCNPJProprietario { get; set; }
        public string Placa { get; set; }
        public string Rntrc { get; set; }
        public string Chassi { get; set; }
        public int? AnoFabricacao { get; set; }
        public int? AnoModelo { get; set; }
        public string Marca { get; set; }
        public string Modelo { get; set; }
        public bool Comtracao { get; set; }
        public int? IdTipoCarreta { get; set; }
        public ETipoContrato TipoContrato { get; set; }
        public int QuantidadeEixos { get; set; }
        public int? IdTipoCavalo { get; set; }
        public string TecnologiaRastreamento { get; set; }
        public long? NumeroFrota { get; set; }
        public int? CodigodaOperacao { get; set; }
        public string Municipio { get; set; }
        public string Renavam { get; set; }
        public int? IBGECidade { get; set; }
        public string CNPJFilial { get; set; }
        public string CorVeiculo { get; set; }
        public ETipoRodagem TipoRodagem { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (!string.IsNullOrEmpty(Placa))
                if (Placa.Length > 7)
                    return new ValidationResult().Add("Placa não pode conter mais que 7 caracteres.", EFaultType.Error);

            if (string.IsNullOrEmpty(Rntrc))
                return new ValidationResult().Add("RNTRC não foi preenchido.", EFaultType.Error);

            if (!string.IsNullOrEmpty(Rntrc))
                if (Rntrc.Length > 9)
                    return new ValidationResult().Add("RNTRC não pode conter mais que 9 caracterres.", EFaultType.Error);

            return new ValidationResult();
        }
    }

    public class ViagemDadosPagamentoIntegrarModel
    {
        public EViagemFormaPagamento FormaPagamento { get; set; } = EViagemFormaPagamento.Outros;

        public string CodigoBacen { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }
    }

    public class ViagemDadosAnttIntegrarModel
    {
        public bool? AltoDesempenho { get; set; }

        public bool? DestinacaoComercial { get; set; }

        public bool? FreteRetorno { get; set; }

        public string CepRetorno { get; set; }

        public int? DistanciaRetorno { get; set; }
    }

    public class ViagemIntegrarDadosBancarioSemCartao
    {
        public string ContaCorrente { get; set; }
        public string Agencia { get; set; }
        public EViagemFormaPagamento FormaPagamentoSemCartao { get; set; }
        public int IdBanco { get; set; }
        public ETipoConta? TipoConta { get; set; }
        public string DescricaoBanco { get; set; }

    }
}

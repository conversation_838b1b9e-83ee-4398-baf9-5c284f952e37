namespace ATS.Domain.Entities
{
    public class UsoTipoEstabelecimento
    {
        public int IdUsoTipoEstabelecimento { get; set; }
        public int IdTipoEstabelecimento { get; set; }
        public EUsoTipoEstabelecimento Uso { get; set; }

        #region Relacionamentos
        public virtual TipoEstabelecimento TipoEstabelecimento { get; set; }
        #endregion
    }
    
    public enum EUsoTipoEstabelecimento
    {
        Todos = 0,
        Credenciamento_Estabelecimento = 1
    }
}
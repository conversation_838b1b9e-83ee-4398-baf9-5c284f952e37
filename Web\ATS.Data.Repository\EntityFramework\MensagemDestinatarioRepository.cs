﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Data.Context;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Data.Repository.EntityFramework.Common;

namespace ATS.Data.Repository.EntityFramework
{
    public class MensagemDestinatarioRepository : Repository<MensagemDestinatario>, IMensagemDestinatarioRepository
    {
        public MensagemDestinatarioRepository(AtsContext context) : base(context)
        {
        }
    }
}

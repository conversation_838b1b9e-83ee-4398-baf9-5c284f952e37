﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Grid;

namespace ATS.Domain.DTO
{
    public class ExtratoConsolidadoDTORequest
    {
        public int Page { get; set; } = 1;
        public int Take { get; set; } = 15;
        public DateTime DataInicial { get; set; }
        public DateTime DataFinal { get; set; }
        public OrderFilters Order { get; set; } = new();
        public List<QueryFilters> Filters { get; set; } = new();
        public List<string> DocumentosPortadores { get; set; } = new();
        public EExtensaoArquivoRelatorio Extensao { get; set; }
        public bool Relatorio { get; set; }
        public EModoConsultaExtratoConsolidado ModoConsulta { get; set; }
    }
    
    public enum EModoConsultaExtratoConsolidado
    {
        ExcluirPortadoresSelecionados = 0,
        IncluirPortadoresSelecionados = 1
    }
}
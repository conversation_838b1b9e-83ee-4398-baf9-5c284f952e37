﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.Enum;

namespace ATS.Application.Interface
{
    public interface ITipoCavaloApp : IAppBase<TipoCavalo>
    {
        TipoCavalo Get(int id);
        List<TipoCavalo> GetTodos(int? aIdEmpresa = null);
        ValidationResult Add(TipoCavalo entity);
        ValidationResult Update(TipoCavalo entity);
        IQueryable<TipoCavaloGrid> Consultar(string nome, int? idEmpresa);
        ValidationResult Inativar(int idTipoCavalo);
        ValidationResult Reativar(int idTipoCavalo);
        IQueryable<TipoCavalo> GetPorCategoria(ECategoriaTipoCavalo categoria);
        IQueryable<TipoCavalo> List(Expression<Func<TipoCavalo, bool>> where);
        IEnumerable<TipoCavalo> GetRegistrosAtualizados(DateTime dataBase, List<int> idsEmpresa);
        IQueryable<TipoCavalo> All();
        

        IQueryable<TipoCavalo> GetQuery(int id);

        /// <summary>
        /// Retorna apenas o objeto de tipo cavalo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        TipoCavalo GetTipoCavalo(int id);

        object ConsultarSemEmpresa();

        TipoCavalo GetPorDescricao(string nome, int idEmpresa);
        List<TipoCavalo> GetTiposCavaloPorEmpresa(int? aIdEmpresa = null);
        int? GetCapacidadePorPlaca(string placa);
        object ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ValidationResult AlterarStatus(int tipoCavaloId);
    }
}
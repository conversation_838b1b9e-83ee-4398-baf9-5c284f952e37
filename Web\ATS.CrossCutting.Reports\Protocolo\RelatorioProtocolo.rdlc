<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.5cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>5.49256cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>5.26275cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.67911cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>4.19477cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>4.18623cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.15617cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.15617cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.15617cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.67438cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.881cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.6cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox38">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Protocolo</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox38</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox13">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Estabelecimento</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox13</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox14">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Associação</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox14</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox15">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Valor protocolo</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox15</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox16">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data geração</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox16</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox17">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data trânsito</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox17</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox18">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data recebimento</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox18</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox19">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data aprovação</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox19</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox28">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Prev. pagamento</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox28</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox30">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data rejeição</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox30</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox34">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Status</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox34</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <BackgroundColor>WhiteSmoke</BackgroundColor>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.6cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="IdProtocolo1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!IdProtocolo.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>IdProtocolo1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Estabelecimento1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Estabelecimento.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Estabelecimento1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Associacao">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Associacao.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Associacao</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ValorProtocolo">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ValorProtocolo.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ValorProtocolo</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataGeracao">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataGeracao.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataGeracao</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataTransito1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataTransito.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataTransito1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataRecebimento">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataRecebimento.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataRecebimento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataAprovacao">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataAprovacao.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataAprovacao</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataPrevisaoPagamento">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataPrevisaoPagamento.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataPrevisaoPagamento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataRejeicao">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataRejeicao.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataRejeicao</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Status">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Status.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Status</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>ProtocoloListModel</DataSetName>
        <Left>0.03175cm</Left>
        <Height>1.2cm</Height>
        <Width>40.33931cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>3.78166cm</Height>
    <Style>
      <Border>
        <Style>None</Style>
      </Border>
    </Style>
  </Body>
  <Width>40.40866cm</Width>
  <Page>
    <PageHeader>
      <Height>2.04727cm</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <ReportItems>
        <Textbox Name="Textbox66">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>RELATÓRIO DE PROTOCOLO</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>11pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox66</rd:DefaultName>
          <Top>0.5067cm</Top>
          <Left>0.03175cm</Left>
          <Height>1.52604cm</Height>
          <Width>34.13689cm</Width>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <LeftBorder>
              <Style>None</Style>
            </LeftBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox3">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>="Data: " &amp; Now().ToString("dd/MM/yyyy HH:mm:ss")</Value>
                  <Style>
                    <FontSize>8pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox3</rd:DefaultName>
          <Top>1.2919cm</Top>
          <Left>34.16281cm</Left>
          <Height>0.74084cm</Height>
          <Width>6.20826cm</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BottomBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </LeftBorder>
            <RightBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </RightBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox2">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Página: </Value>
                  <Style>
                    <FontSize>8pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>=Globals!PageNumber</Value>
                  <Style>
                    <FontSize>8pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value> de </Value>
                  <Style>
                    <FontSize>8pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>=Globals!TotalPages</Value>
                  <Style>
                    <FontSize>8pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox2</rd:DefaultName>
          <Top>0.50614cm</Top>
          <Left>34.16281cm</Left>
          <Height>0.78576cm</Height>
          <Width>6.20827cm</Width>
          <ZIndex>2</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <TopBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </TopBorder>
            <LeftBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </LeftBorder>
            <RightBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </RightBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <PageHeight>21cm</PageHeight>
    <PageWidth>42cm</PageWidth>
    <LeftMargin>0.5cm</LeftMargin>
    <RightMargin>0.5cm</RightMargin>
    <TopMargin>0.5cm</TopMargin>
    <BottomMargin>0.5cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsProtocolo">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>680cd15d-2331-479a-83c1-0372f4e4874e</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsProtocolo1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>e46f438a-4f1c-4c41-93ee-8898275546d3</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsProtocolo2">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c3a751dd-7b35-40b6-a82a-07c0a8afa826</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsProtocolo3">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c9044d60-3b34-486e-a538-8172ca6aeef1</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="ProtocoloModel">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsProtocolo</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataGeracao">
          <DataField>DataGeracao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Logo">
          <DataField>Logo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PerfilEmpresa">
          <DataField>PerfilEmpresa</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="TotalValorProtocolo">
          <DataField>TotalValorProtocolo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="UsuarioGeracaoProtocolo">
          <DataField>UsuarioGeracaoProtocolo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Protocolo</rd:DataSetName>
        <rd:TableName>ProtocoloModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Protocolo.ProtocoloModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="ProtocoloListModel">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsProtocolo3</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Associacao">
          <DataField>Associacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataAprovacao">
          <DataField>DataAprovacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataGeracao">
          <DataField>DataGeracao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataPagamento">
          <DataField>DataPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataPrevisaoPagamento">
          <DataField>DataPrevisaoPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataRecebimento">
          <DataField>DataRecebimento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataRejeicao">
          <DataField>DataRejeicao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataTransito">
          <DataField>DataTransito</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Estabelecimento">
          <DataField>Estabelecimento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IdProtocolo">
          <DataField>IdProtocolo</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Status">
          <DataField>Status</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValorProtocolo">
          <DataField>ValorProtocolo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Protocolo</rd:DataSetName>
        <rd:TableName>ProtocoloRelatorioModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Protocolo.ProtocoloRelatorioModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <EmbeddedImages>
    <EmbeddedImage Name="sotranLogo1">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAZAAAACFCAYAAAB1065QAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiIAAC4iAari3ZIAAAAZdEVYdFNvZnR3YXJlAHBhaW50Lm5ldCA0LjAuMTczbp9jAABJIklEQVR4Xu2dB3hUVdrHCaT3TG/JJJNI7733EiABQgpF176u67rurq762da1rGtFKSqKioKIiCIoKL2DoFKUooiKoIhUAVFqZr7/OzmXndyczG0zSQbv/3l+zyQz97znPeeee95zbjm3ni5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTp0qVLly5dunTpuuRVUlKSUFJyRc6oUaP6jR49+ipw16hRY8aNHj1mKj7n4HPRqFFjV+LzY7CJsRYsx+/z8dsb+HwePIS0f8X/I8vKLu9QVHSFpVevXtEsmzqn0tLSZJS5CfwtgO9/rvB/zAv4fxb+/lBcZny3CiwGc/DbFHw+Am4uKxtTOGbMmMZUj8x0JSGPhmVlY3v+XkC9plG5S0v/kMX7vSbA/uhYUnJ5U7RHa3X7pa7phhtuiOGVRS5XXXVVPDOlWWrb7NixYz3MRJ0W2kYjnv/BQFvqxJL/fpWfnx+HimhfVkYd/ejX0PntxN9n0BH6woAXtk8gjw3IazLyvbG0dGy7UDZ0uaKDE3m3hg+3wJfX4dPX8O0cx2ctUD1uBZNKS0eXoeMyU94URNGhDcH3CLZjzgNe2ksFLzoRN5UbQfclzu+1Ae2X77G/38fnv9EZ9C8ouCGRfKxLgm+3Mn9VgTY9hJnSLNjCoJGfTzCwz/dEQsCGr8vEvkszej1L/vsSdWTo0K4tw8gZB9FJfPpqmd/KRo1eWTpqzH0U1alzZ66GVJdffnkq
DoTRpaNGz0S5j3H8CDfnkf9qzFZuQKeaQT7RyI4CGBrkhaoN9JLgKIoZVVFW/6yNt02tg/bwK/bNjJIxY3oJ/tam0C4y4dMpnq9yQfpHmTlNwgyyAewdFduXC/y4n5mqq4qCn4rLh/YyiaW/9FXRCEYVlGLUhY7srKhjq2OMPoJGNxU7aIjWU173339/fYww+8DWG7CNQMXLrxZAh4VA9uzIkRVTfATONvj/I+62kUzZmKVUPprpou2d425T5xi9vqysrDP5XVtCvc3h+yaf0rIx65g5TcLAywV7XrF9uWC//0oBkZmrc4Jv2Ty/pSgZNeo6ZuLSlf/ALaVTRaO+wY6knRlJnL/iiiuSWFEUCQEzFmW+uqRs1A6O3TpDSdnos/BzAs1IKMiXjBpzOzqws7xtIxEEyidofxRTgOT8XlfBfrkA/kMDEH+DqkGxgR7XL4WcKSgo0HxqrjgE/qAu32Tm6pxKSkYX8XyWpHRsa2biklQUKqakuGTUt9zCRwDo/L9kZZEtOuCLS0uvKImwgFlSWvbTyLKyQioDRr/d4P9B3naRRknJqLFUJjplyvu9roP98HZN3vhBA6aS0tF7eL6ooaTEf0pOk7AP/8WzrQTUoxftuiczWaeE4PYgz+dgIM1vdfmGIE0ajukiOqQPsNNox0UsxaWjZrIiyRJG8O0QMDfybEUI3pKSsieoYWIUmouy7OVsE0mgPCV5tG+KS8omcn6PCNAOp/gbWA0I+/9Rng9qge/3MdOqhb5kLs+2UtCet9Asm5mtM0KAXMDzNyglozax5JeWRo4sKxxZXHoMByx2WGQzsqTsDlasoKLTdNj2UZS7nGcn0kBZ3qE700aOHOkpLindz9smQjghjNJQpnWc3yMFL2aHxf7GFkYVFZU1R17nRXlro7hsMTOvSv4ZfUnZj1XsqmRkyag/M9N1QlS+kcVlB3i+SvASM3HJKKq4uPRO4AVoOJEPgmF/VrZqRSNcbLtJnDbSGVlc
Mo8636KysrZo4L9xGnDdp7h0Le2jioO09BR3m4ih9Du6ruZvdGGQv6MuLlkjbgdaGTmy5Bctp1qKioosoRyYwdZh4Q7EuiDsUxvPT0mKS//CTFwSQvAoeVzceCIcen4gaENDgBmE7X4WpbuUmEDlRJD8E+e3SMDv/4gRI5pwfotErqDyhEPo6K/j5BcSRowo6ciyUSy0vQE8m1oYObL0WWa+1lVcXDyY56MUqJcuzETkC43vQUCjjUuI4j2seFwVFZVcX1RUfIGf9tIBDbzMP4IfWbqS93tdBvvoGtpX+Hus+LdIBOVZ6G98IRY6IzPa8lFeniHiNpaVYiHtHSJboeDc8OElLVgWtSrU+90c/6Q4j30WEasZSGrEiOKrUAlegAZ+6YByzWFFrCL8/g9wyZW5Gg4XFBSYiorKWuHviAqYOMia0v7C30+If4tEUP+n6HqbvxGGUCNGjHwtYH+HgZFzWVaKhYHcTF5daAV+LYf5Wn9gE4OCd3j+BQO+72DJI1tFRUXN0fhOV24sIeMMOvFvYH8ZPqfh/6fw97/Arfj7FlT8n+iT/gf3gkfBZHz3NrZfi889+P83fKrq6LGj7mXFrKThw4v+jt/DFjzg8zkccN+CRSjHFHz38PDhI+/AJ8pafCN+95cXB9bT+H82vt8CfqW0YeQFKjvtB+R9Sg0cm7Lg2ZLJIeGaAfxezrMdiWBG2IzKFCqNGDGiF+oqrIMh2hfISlVnjfR7xPZCR1EJy6a2FIXjfC/ft+pBe57B0keuaLkPNIwtgBqIZtAx46Aveg9//w07tm0oHkCiDmT48OGZ6ICHwP6d+JwJvsbfF8T5i6E0zMxF4WC7Ar95xdtqAb6Uo9yb8fnwsGEj+wwbNiyFZSdbNCpFnXUmG+ArXj5agM2zqMdslp1iwTc7z64MfqbTZ8yMKlF62DkmsisLlPvvhYWjrWoYPnxUJjr7rrDzZxzw71Idiu2rY8RIVjTNonYDv76omkfoQbv2
zwaVaOjQsRnwT/JY1UCtrpOFvA3wAcc/17dqQZ38k5mIXNHBBagwWtmAA+yKUAQMuUKHZhk2rKgEO2My8v9O5I8f6vTY5n7hwO2O79GRVt1WJYfAI4WFhf7nFEKo+ghE/WF7TUBemkFd+S9IqxHqemjgASAX5Ou/i0qLKPDxbMth2LDiNsyMZlX4UbScl49CbmAmNQv1e3fgPg4nw4aNuJFlK1t0zHHKH2pqbZ0sGjBy/JGE0jETkamhQ4dmoFP4mddQ5DPii8LConxmsjYVVVhY3BIN/EH49SXz7QD7zS+Mnhz0XWX/1YF6O1JYOPy2GgiYUTRj0r6fBEYcVXv+HXV7L99mcOD7RGZCtWCjiGdbCvgc8id9qf7gz2pefnIpLBxxMzOnSWgbuSjjaUBllYsX/q/hfC8J/H6dZS1byOsWXh2EmF8LCkqzWJY1KtTLrRx/pPBS/8tMRKbQAd4nbiDKGP4cDs4aX0JdjhAsOmG0+Cf2L50GawB/l/HLoQgv6u3lgQNLDMx0jYhmOPD/K44/ikG9qHqYDZ3HHJ49KZDf1cyEaqHOH+LZlgI+b2YmQqqhQ4tawT46Yn6+MlA8kucoCm3iQ45tCYbPKigYMZj/W3CwH75jecsW0k0V2wkH2NezWZY1KgqqPH8k+JYlj0zROUM0hkOcgkmCdL6CgmH/x0xFhODvrbyyKAHlPorR1DBmssY1ZEipraBg+Lc835RQWDhsGjOpRNRZ7eXZkwLBryWzoVo4SBfwbEuBffYKMxFywbbqgI7yjGFmVAuDpFI6FhVyomImPjwdf58X/SYLpFW0Ii7azXZeHYQa+OaFb71ZtjUm5KumHbzNkkemVDY+P+iMn2ZmIkI0eofPp3llkc+wr8JwnUOxcIC0QFl+4/soD6TfB1OK7qahGRfS0uyLazMIv9Lsj5lRJbqAjsD5E8e2JChr2J70RZt4l5enHHD8aVoQEHWahjrZXwBbiigYdjszgUHV8M3cbaQo
GP4HZkJS8DMZeV7g1UE4QF6fh/qUZTDl5+enIl/FgRh+3sNMRKaGovFzG4ckw7aF60VN4RJ8Xsgvi1yGbR1QVGRh5mpdQwuH38H3Ux5DC4aV0yiUmZOlwsKivjxbkhQM/4SZUC3/zItnWw4FI8L2pC/qcRY3T2m8g0Q3dygV8p7AsRuUoQXDdwR2rmhH43jbSVJQ+CIzISm0s/ZcG2FkaGFhSK4vyRG1L54PkhSMGMxMRJ7YqOAMoEioiCFDhg1lZiJCQ4cOG8Yrh1xwoO7GzMPKzNUJ0UVc+Pa92FclDMEwiJmTJXQ2t3EPBCkKhsvubKoTHWxc21IUDAvrk76wv4KbrwRoUz9rmZVRpzx0aGF54P6UgrbHsVvprh8cG0W8baUp3MlMSGpo4YgbeHUQVgqGHR0xYoSRuRBWoX3fxPUhGAXDvEOGDLExE5EnHJADKzcIeaDB7UbyWn/qU65otEWNnVcWmRwbPHhEQ2auTgkdwm0cf2UzZEjhncyULKEeX+fZkQJ+al41FTbu5tmWRn5Hp1QVp9WGHauapzQozypmRrGoTQ8dWvAJbJAd2WB/V3kRE61MgN+84m2lGDKk4EK/fvI6aGw/mVcHwUA/cxj77mveb3JBvs8zF8Iq+DqFl39wCn9kySNTqNyHhMagEP9b4SJF8Pcqkf9KoNvsipipOqdBgwbZcSCXc/yWBToUJQdYFDqt3Tw7UmDU24nZUC3k/Q7PthQoo+JbTuUKnW9j5KG482U8zMwo1uDBQ2/GfqdOXAn+C+fMRCXht+2ibWVSKOtBSJT1Y1HZZTDsQ9TvCP5vsjmfn6/95g0poW1+xslbivkseWQKDWBh1QYhTX5+gf9Nd5EgOkUAn3eJy6CAl5mpOiv4+JHIZwUUvsvMSIouFCLNec6BEBSkOReC27yjYGff//yWD3xQvfiflFB/d/LylAMGJqpWtR00qIgGDSfE9qRA0Kn2fTj4/Tnx9nIYPLhA8kYaOtWKbc+K
24UU8Pe/SI79XriC97tckPcKsuN3JgyiZ8BUlu8hZiIiFYUCHBYagjKGuJmNOi/4OpJfBmlwcBzo3780jZmqs6IDjee/HFDG1cyMpFCXXXk2pEAe25gJ1Ro4cKAB5Szn2Zdi8ODCvsxMSEWnkYYMGbqbl6cUqJNv1C7rkp8/5E3UBWwootKFc7Gwb8dw0kiSnz/0U2aiWg0ePLgFrw6kQIAtpfT5+cPaIK8LvG3kItgKhxAg2/DylKIun9mQFB2QaIjl4gYhBdJcoHvHmZm6LgTJIWt45ZDDoEGDr2d26rTQGY3i+S+PIbJfpYmO4C98G5JMZyZUCx1cH45dOdAzAWFpr+g8L+fkJ5e7mRlFQmc1EMcgHYdK8GLfBQ2isOvipJNk0KAhZwYMGJDEzHCFsl4pKrssBg7833MmqOtXeNvIBem/C8EsmCvYvpaXpwTewPJFnAYMGNqcUyhJ0GjKKfgwM3VaVEb4i4OHX5bgDNmh5Q6ZmhRGMu34ZZAG9SN7djBo0NCXeDakQB7/YCZUC8H8Vp5tKZB3WJ70RZ1nwPZ+Xp5SIN1xNUGN7iTLzx+8G+nJhgIGz2Imggp1/A0/fXAGQcwEVziWnubVQzBg90jg8UfX+vDdSd62CniAmQup4NdETl5BofIhacTciFRFNCKhna+GgQOHdGVm6rRwQIzj+S+P/MuZmTqv/v2HOeCzt2oZ5DBYQQAZ8jnfhhT5vZgJ1cK+nM63LcXgd5iJUCoKdfE2Pz9pUJZ/V5hRpoED8x9CWkqvgPyTNLtgJoIK9qfybUgS9GYA/L6KVw/BQP2uZMkvCnbu5m0rF6T/taCgIOTrZMHXj3j5BQNplrHkkSk0qiK28xWDhvYMM1NnhdFL7MCBgw/x/Jcmf2+kzD5I/fv3T8M+KeeXRYp8yXPYJLpQiPo8w7cRFC/5x8yoFuzsFtmVBeol1E/6RsHmY7y85IA6VLXkOOqwyYABg84Cn0KqvXAuVv/+A6/hpJdk
4MBB1d6OTMcRyn1KXA8yqHJxnk5Boe6/42yrgPy3mbmQCP1oHOz+VjUfKfKfZCYiUwMG5BdhZ9ABpobf+vbND/utcVqE8g3k+C2XiFpegA4sHMgXOOWQBPUk6yJ6v375bXjppcCBQs8MaRIO0lTYOi+2LYcBAwaH7Elf6gxRz0/w8pED0pajLAOZOdmii+39+w9YgQ7ep5Ad7dq1k71aBA6YPI4NSfr1G3iKBmzMTCX17z/0Ml5dSIEROneZFATgUbztFeBFOUO2fDpsNebkIYOBY5mJyBQ6jkJhBKGG/v0HHUTDoeXb6+R5PPj3As9vKXBAXOjbt6+TmYkI0UVM8ptXHimQ7j1mJqjQ4K/lpZdm4FvMhGr175/fmW9bEm+vXqF50hflN6As8zh5KEHV2nH9+vW7sl+/AeioFeHt21dxRxnVt2//Hzm2JOnTZyD3tDaOwzJOPUgyaNCgRsyEWFGwuZaXRj4DQ7ZOFvrAsfw8guLt0yc/l5mITNEO5zUE5fTfiEZ3Gxp5b+Dp1avARAebGrBT06kzVDJq4onSw6dDfH8lWcrMRIy6dx+aAb/LReWQi6xVarGfJ3HSSoIApXm1Zti5SWxXDtQZMhNaFIV2PRTl/56Xh1z69h2wQs1pUeRt7NOn72GATloJfWYyE4pE6fj2pOhzFzNRSSj7o+K6kMGJYHXVu/eATthGbXv3g7YRknWyYOsJsW0p0BaOhyqA1ZpolN23bz+MUvpTZdYRyJ9+5/r06XcKn9+jYa7o3bvvC717974FB1KXzp07yzp3jG078O1Lg7w1L7lR00JjdPHKIgfU1X+YmaBCvdBAgWsjGH36DFB8ykYs5D0F7QH2FLOAmVAlHCMt0Qbnc+wqok+f/jtogMTMKlLPnr2n9OrVx6eQk/3791e0SKYgpP2zyJZMen/ATFQSyr+I1y6CQW2NJa9WsDuNl1YuyOMoBWdmTrXgxzKe/eD0W8eSR67ovGrv3n2OVh5F1G3g
768IKPMw2hkTLJgg4NzNSy8DBLDIOn1F6tmzXxtOWWTSRzJg0oVCbPtr1bSSlHfpMkDz6sXY51s5tiVBOjVP+tbv0aNPT3SKc9Heynl2lQAb36htU9279+7Wo0evcgQRn0JUP3mPwUhzjj1J4Ocx3qgadaB49oQ6k1xep0ePgZnY9jdxWmX0mczMqRKVFzZO8G0Ho4/mN3PWCWHHzxQ3hEgBDfYIPu/hPcSE71cEbiuXHj16a35iujbUs2fPQl555NC9ey/J1xDDfhNeWhl8z0yoFt39BTtnRXZl0lPuk75R6AyykeZOtIGdVe2og2x17apuJtC0adPY7t17fN69e0/sI0Vs13IKmE4ddevW8xjHriSdO/dswcz41aNHj0xevUjTR9Z74tEH/JufXh5IfwH7vTUzp1jdu3f3wI5XbFeaPtcyE5EtHGAjUYlUkRELGu73aKgXT5PQzATfneZtK4PxzExECeW9hVMWOXjpIGdmqhXayVhOWjnIukAfTF279mrNsSsJ6sTbpUufapfcoVkVOoBe3br1eBjbbqXteXbUAnur0BZVP3DbpUu3f3bt2t2nkPKuXXtqvsOoa9ce73JsS9KlS/dbmAm/0G6G8epGiu7d+7RjJoIKnX8ytt8vTq8ENAHZS/mIpbb/hN/NmYnIFh1EGG3sQ2OnBh/JlKMj8F+spQ4Rf5/hbCOD7iX+iokwwfdJVcsii5NyRqvY7glROllgP2h+8hf75BqebSmQ91Ek998hSKP5Ll26NEfHeDm+HwfWgtO8dCHAC9vPaZkFwFd3ly5df0UQQaesiCpLtatR585d/8GxLUnnzt3eZib8Ql3cL6obSWi/0P5iJiSF/utKnh0loM8YxcwpEtI+JLYlBcp3ivpdZiLyhZHDFeKRRKTSrVu3+1iZ/ij+TQYYvXVVdbqhttW1a7eVnPJIghHjFmYiqLDdcl56aXpqfm888p7Ity1Ft4t30+HvF/jbhJpux9GGNK9g0Llz
l3kAHbIiToSq/Xbq1Kkdx74knTp1prveLi4QiX03j19Pwej2GUsuS3TKDek+rWpHPgh++xDwE5lJ2YKv83n2JJC99lykqD4qcF7gSCJy6ert2LErvWEPZeq6mL9NdXQ9UFEdkSU6gOD7CX6ZJJnKzFQrulCIkeVJTloJupZ37txZ8w0J6JjWwxbZUwRG0RffWQMbn/C2CSXIYzk63myWpWp17NhxRIcOnXxKad++4z+ZCc3Ky8uLg81T4jxk4G3fvr2HmUEA6bqP3zaC0fVVlly2EDh7IJ2Xb08eaC9Kb7iIQprDPFsSTGHpLx2h0aZ26tRlkzCSiHAOoEGldOnSpQ3+RifG3YbHYlYdEaUOHTo05JRFJp3/ysxUK7SNHH7a4GA0egjJNT1kSh1Zx46dfoMtsqcIlM3/pC+dSsL/Z8S/h5AjnTp1/SOy0vxAbdOmvZLbteu4D8GAAoJskGY7BXpmJiSCzQ95eUnRtm0H/wrW3bt3N/PahRTohypdR5Er7O/ZPHtyQb6/UVtn5iRFgyOk84rtSIF8bmImLi21atUqvUOHjos5o4pIxL9YXfv2nZZyfquGjnV+fS+ecND+gV8eOXRoz8xUK9ThSH5aKTpqDsjt2nVpzLctibdt267+J31Rxtac3zWDej8PXkSAMvmdDYHatm3/ZLt2HRAQ5IM05W3btg3Z0hyCYPcuXn5SIN1rlB6d8UBevUmBWVx3vwMKRZ0/2twZnk35dJS98CaOi8F8G8Fp166zqpeIRYTo2RAcFLejIZyk0UTk0mE/jcjQeYzl/87lL6waIkoo60ucskhC+1jOxUps+5A4rUweZSZUC6PgMYGdk3zanxBG5G3atL+Gv41qylHn89u27VzpllWtatOmTcvWrducb9OmrU8JrVu3VfXEuZQwoOzGy0+aNv7l81FPN3LaRFCQ5nzLli2DvlskmGDjv2KbCsHAo6Osl49h27tFaSVB+U6jXYblnSR1Si1bdrGgsE9g
OnoYn4EHT8SAhtCDRocYEV3g/V6VdkNY8SNK6Cz38MsTHOzbKstl84T6m89LLwU6blV3tgSqTZt2jyN/GtUqpN3FJ33x/8Sqv6uiHHW9sE2bDl2Y6VCqfqtWrdcjgCAgyAdpjmP2YWc2Qiq6qAz7Z3j5BgNpyhFEHPCrE69dBANt8kuWvSrRqXjso4M823KBD9vknA7Edm/z0gen/Q6W/PchOgfdunW74RhZvISRzm40EMUjpNqjDZ3GisLfX1X9rSotW7aPuHuzUcYmvLLIAfvzX8xMMNXHPj/CSy+BFyNJ2eeTqxPyXsaxLQnKdvFJX9TRDN42SoAfm9AhtmUmQ64WLVrd0LJlK7RBpbS8+MR58+bNrS1atLgjlMD+j/x8g4O0mDm2i0HdnRLXZTCw3zTfhgwb1/NsKwE2/sbMVSu0ib28tMFp8zpL/vsUGkVaq1btOqIirgB3oKIfa9269fOozKn4e7pK3oSNpbCxB5QDOmBDwRzyuVWrNrM5v4mhDk/zkhs1rVatWt3OKYssWrZs24mZqVaw78S2XnFaGfysZuHAQFF6GmEDGtUqomXLNhef9MV+bYPvvOJtFPJ9Z5nrsCkVdfzNm7f4uUWLluh45YM0lS6cN2/e8lXedrUBfHuOfEK90XHNax9ccKze6S+MBrF28xnPvnxaH+3QoUO162TRmQ1sp/i4QJ95KzOhKxyiHYNO6+qWLVtvCTh41bKZbOLzKdH3VcCo6RxNf/1ORJBQTxt55ZEC5T0kp4Nv0aL1YF56aVqpfrpXEGxkw08Edv4oNxhI24yZ8Qv19C5vO2W01LyqME9Nmzab3rRpc59CvM2aNevNTNRr3Lh5F/pOtE1t4l8SCPV+P799VEcrzQtvkrCv+sKepkEDfH+Bmasi+NmHl0aaVhf3ma7wqj5GVHdhNOMVj27k0+I7MoQR3n383ytxtmnTpsn+nCNEOEgacsohkxbTmJmgwnZ389NL0ULzkjDNm7cq4tsO
Dka/p+jUKzPjF/xpi980tCWy2/IY7GQwkyFRw4ZN+zZp0tTbpEkzn0LeYCb8I+7GjZt+wtmmFml6AceTgTpyXl1WAwJgU9VLv4iF/TWPk4ds0I7ON2vWuhUzV0mwfRsvTTDIXqNGjVKYCV01IXT+D6PiqfIV06xZC/+DgbBxG+93EREXQFC+RznlkAU6QnrYUlIYSb6NfKguFdG0aYurmAnVgp0HRaNamTTjPumL7+fyt5cPOsenmDnNcrvd8Y0bN/kS+BRyEh3RxSfOGzZsch1nm1oHPhbShXjU2zlx+6iGvaxIIVGTJq0vg82zojwUAd/pVb1Vnu/Bb6+Lt5UCx8TXLLmumhI1QBy0xwIPYgX4ZyBIfy/nt0pgm/MeTzvN7+2uKblcnRPQIR7mlUWKivqUt9ZQkybN9/FsSNGsWbMmzIRqYRT7PnxlI1olNOU+6du4cYu2+F3NaD+AprROUxYzqUkNGza8r2HDRj6lXHZZo4sXzmlGhP8P87arfRr6VwJAva3ntZGqNJvrL1QIhbzH8fOST+PGzUYzcxeF42Inb9tgoO1UWidMVw2pUaMmM3kjHGkab6X0+HyM/3tl8vLyXP4MI0Ao0w28Msij8cvMTFChPszY3ls1fXAaNWr8CwK/pjdKQlGwdVBsWx6Nq33SF7+9xU8jH7RH/4NyWpSTk9PwsssangEICEq4rNJS7dhH4/nb1T55eQ39L4VCe5B1/GHf3O8vVAjVqlWrdOyvI/z85AH/97UMeDaFzlTg+3LxdlLAj7uZCV01KUyFH8FOpB2plA9Z+lc5v1UBIyZVT8DWtGj20LBh4z28MsijEffd1WJhuz789JJ8zEyoFvJ2YBTrrTqqlUPDDsxMFeG3ltimvGoaRZTDPy0PEdb3eHIX5+bm+RRSnpube/EirMfjaY7vLoi2qTOgjOesVmsSAkMBp41waKR54U2ecKzcxM9PPrBx8a2dKE8X3jZSoO1JvndHVxiEA/YR0QEs
C0zt/bcS4u9V4t94YHRH6xnVeV12WeObef7LAXVCK53KWrMJDf5Wng1pGmp6yxsJNgbzRrUyOO1yuYLebottZovSKAaja9WvynW7PWNycjw+5eRcvHAOReH/5fzt6g4Icv0w4DGgziQHA+E6A0AzNtjfKc5PCfD/NNqk/7kmfP6Ft00wkB4zlsaaX5+rS4XQsF7Ly7sMDUwZubmX0Uvz6+Pvg+LfeGB7yddo1rbotBJ8PSr2XS4oo6w3vZGw7es8G1IgEP+JmVAt5H03z7YMtjMT1Sonp2FLbFcuSqeYwNmAXGVlZWVkZbl/cruzfUpAmhNIe/GJ86ysnFLednUPt39NOrTbz3l1GMBBf8HCJI+n4WBOngrJ81+jQdt8if979SANvZlT82KbulTI48nbzpsiS5PbBaM0K/72Vv2tKphya1pGoQYUhRHdNJ7vMvkJB4HsF9kgry9RJ1QvikC9a35iOzvbM5s3opUC6aYzE0Gl1r6Ij2BKUaeQmemehGBAAUER6IgvXji32+2JmZlZe3nb1TXg53LyGW1pEq+t/A/PIn/hwic6dj7k5y0bL/qTAdjvmzi/BSUnJ3c+80NXTYpGXdhh2HHcAzgYv9JtktjhBZzfqsOLNI1Z1nVOKEsx+SjyWQE5dzFTkmrUqFFKdnZOOd9O9SDNb0qCVHWCnW8B2VOE250j60nfrKy8ptj+gji9UjATKGYmJeVyuTqiQy0H1LEqYVu9ev974jwzM/NBzjZ1lVN0Cik7O3sUr70IoC7/y4oXNmFg0wz5XODlLxek/wKc5f0WnJwHmRu6alLo0G/iT40lWUPps7Kyn+L8FgT3xZcQ1SU5nZ7LMKI7zvdZGqQ9aDabZT/ngnrozLMjjftzZkK1HA6HkW9bGnRUvZgZSaFtvMGzoQTY+AKm5CzZEu10OjcBn0LKEXh6Mhv1aB+ifubj+6U1BfLbhk+eb3Lp7HTmuVBfGKDx6zEzM7uMFTGswv56lpd/+HGP
ZC7oqkE1wAjmi8ApsXyy7kX6+vjcxf+dD/I7TuepK7KvG6IOFX59yfNXPlmKlqp3udw38e1IkaX4bXJiOZ3u3mz0qgiXK/OckiAJX5si3QWxHeVkSl7zsdvtt2A/+lQwg5moNcGH1iKflHI72UFd7eG3GbcXdeh/d0u4RdcQ4ccJjg9hBUE0Yh4RuGSETuzaygeqbLwOR3YjNMpmnN9kkDmBuVDrwujT4HJlfcr3Ux7oWHcEngKRI6czcwrqAemVgXSSq5hKCQfbP1Bu+K0MpFP8pC/8ncmzpQTk+wM+q73zy2AwOG0220ngU8hxBEQbM1ObirZabUc4/snlfTKC9vGKuL0QaJ/H8fPF96iHW8jzVrEP4QTloxsE9AvoNSmr1ZqNkcvPODjpAFXKFpiIQvr/cn6TQzlGjAMqPKk9YYRshy9bRL4ppdIpELlCOjWnW3yotx7MhGphv03n2ZYC6WYzE7KFzq0p0l3g2VMCbNzDTFaRxWKZbbFYfcqx1JmVW81m6xt8H6Uxmy1HYIKOx2uqqTtaLqTGRNfokOfXPF/CxFKWta6aEDohE9iGnSxMgZXyF9ZIDoq+lw3yP4ogVmlF15oUOo/O8OF7nm8KUfxMBtUd8j7LsRUUpDkHEpkZ1YKNHYDsKUX2TQKBQrppIjuKQSD6GYG6ykKAJpNpiMlk9qmAriVpWg4/lEI5ruX4KJuMDGtzdKSXoZ14xe0GdVfjr5LGPhsu9iNcIK86eV1VlWhkDwrkroVU08KUPRcVrrYDocZ4BOVLwmcJ73eFHIQtyXdmhFLUecP3+5D3eZEvioGdr6gumGnZMpsdrZGW0itlFzOhWuSv1Wq7AHxKQf6qnvSljg35nufZVIb1aWbSL+yDRKPR+K3RaPIppBwdtuJZYziVnp7uhl9ekZ8KMNI1uPrYRwcD2ovAlRW51KiikO8KkR9hAe1gDMsz8mUyWa9l08pDGOU+i0bfmzot9nNtKhr+/BG+nQyc/irH
ch8Zw45rj/+9VX9XBurpDDuVoHVtJyk1QD7DkecusQ9qgN+/mUz2dsy2IgltRDmWWcyEasFGZ75tKSx0qk71UuBI/xrfrnxQ56fRWVxcaBGd7n8NBoNPBXXxrXVRGRkZX3N8lYu/bVAbEdcbjlXNC2+qEQarrZH/BbE/oQbBsxHLMvJlMlkmoqFTYw/kBHgLo54bsKNpZ4a7s7wouviICh6Lae5nIp/U8AON+pjpKPy/UfS7auDfdjQ4uuc/pHWDsqfA7jUhKr+AF0HgOpaFYqEd8NqIDMya3yZnNJr/LJz2UMg+JFd9oRJlvgw2zotsqsDkf5AxJSWlIQLIOeBTQlpa2gnYCMs7zrUKvk3i+SwHpPXvH7SRmwPbDOrsFL6vtbMh8OelQH9CDcp3AtlcOhfQMZ1cV7nBczmORrwEPGo0Wq7AZzs0ALq1VdGdPNWoPnaazWAwF6OzmIK8jony1oBpFMvDL/xfwN9OE/th9wnQKyBYKVGD9HSbG+W/EuWfhf3xKycPjZg0nXNFUFtb+fSDPAwGi+YbDzCAmYL8YU8ZSOe/00eLYOdVsV2lGAzG8+gwW9LxkpKSdjQ1Nc2nBKSps688TU5OG8HzWS6ok2wc+60qtxmT5oU3taiiLzKdDPQpxKxjWV0SaoBp6C9sSqmUcvAz2A4+gJ2p+HwS3ANuwf/X4/NKfJbhs5h9XgVuBv8Ck/HdYvAj/vYCXh6qgV3eWvv18dsa8bYh5Az4DLyO/P+Dz1vA1azsCJDGP+DvG/H3vYDKvwyfhwHPVqig0x9abomsDz/VtJEL6CDSmQ3VQt6bOLYlQTrNT/rC/xzYOcezrwQEEP8q0JiF3JacnOJTAK3jFYpBWlhEQRE+nhf5LBvUx9UwE4M6PhFQX5MrrNee4M//BfgTUmB7Issm8oUOrTEKRIW61PiGGjcrZiXRaBC/nxNtf0mCBkvnmTV1QNRGhNMOSkD908u7tE7VY2HrtNi2HJD/CGZDk2Br
iti2CryAnoiPT0pK2gd8MvCCOv++bPi4McBnpfjfQ4O6mS/UFdqt5oU3tcrtdsfDlz2CT6EE7fIalk3kCztrDK+QEc7PIOi7GfD73QHbX6rQwal59JqamjoGjZ4aviJSU9PmMROqlZiY3gr5w5ZyEhISLr7iVYtQlhzYOye2r4INMBcVHx9/JXwj/6QIXKq9zgp+PiTyWwn+u/RQx3cEtB3NC2+GQthfZQE+hZLWLIvIV3Jy6hMpKamYSl4qpJxMTk6W8xKo+ij7+3wbEU85Gv+/UMaQXKhDPT3OyUMSpNP8Njnsz2t4pz5k8BOSh+xCJfyYwslDBcl000V9BJHPgS8IJxITE+vkhXOx4Gsvke9KKEedmFC/XVi7OQ2TQd/dUoOKgj9rhfYcIs7Abo3dkBR2Yect4zf0yCMpKfkIGqKsN+uRzGYzip/8Ec9WpII6OIYyheTUjaDExORlsEu2FYF9UchMqBY60Qmc0x6SIN0SZiIkiouL88DueXE+SoFfNOKOiY2NHQqbvurA73+vyDkiBJfjfgn0XwkoaylsJKB+TqOtaV54M5RKSEjtgLbsFbdt9SR9ykxfEqqPBn00sIFHKijHdnRYl7FyyZbBYEhF2nU8m5EGyrECIzo3K1qoVD8hIeEobJN9pWgeQSPv9QGnO5TwODMRMqE8z3PyUcOfyV6DBjGroqNjfGIaNIimN0TW2QvnPMHvD8TlkAvqwX9RGfW7EnWjeeHNUAvH1msBbVoTKN8UZjbyheifE9CoI5VydJovojhalsuAmYRZATYjCpT/KD5vRDlCvvgc2ki2OD85wKcDSK71FFIM7PwCAk95yKXS7duhEGxmgbMBeVQiCWSg7BaQlZDoa5SY5GudmOzrkpTi6wMGJ6f6ipLTfFenpB2Ym2F98PFUw4Kb8P/fk9N9dyRn+O5LyfA9mGrwvZhu2j3fYFu/IMO2YYHBtg1sX2Cw7iU+MNj2f2i0HVsYAP7/eaHRfp5YZHKc
X2x0/LLYZD+2JBCj46elJsfepSbn3mUm5xfLTY5ty03OT8H6FWbHshUm5yIwb4XZ+eYKk+O1lWbH5NVm19OrzK7/rDQ57lllcv59tcn5x9Um15g1RkfhSqOzz1pjZvvVJkfDVaYsuys29t74BtEIBg0UU79+A1qnjg7CB4DmhTdDLfjkAr9Suw4BNzGzkS8UZiTvQIggdqKD68eKo1VRqI+/wOYpUR51mTNgImZeYXuvcmxsbJFwqkEJSLeQmVAt2GjMsy2TPGZGsXxoC9Os1qR3kqyWWcmGpjOTjL3fTDMUv5Vq/NMfE1N3XpOU6vsrOv67UtJ9/0GnPyHV6Hst1eSblWb2zU43+94Gcxjvplv8zGXMyxCw+nmP8T5jvqECBI1KIHD4+dBo94NgUYlFjMUmh58lDASNiyBw+EHQqMBcAYKGn5WMVRdx+UEg8bNGxFpLBessmb41lkzvavy9DN+/i7xeQRnGo5wPpZl8dyAo3oggORb1NiQh2dctPtHXPDbe54qJ8aUhgMTWr38W1Z6CfdYP7VnO9csaF/y6X9S+VIE+pjMzGflCgR4UFzASQMfyHT5pxB3yaT7sZoM5oJzyqqOcQh08h0Z9cYmMcCkmJuZB5EV1rgik0/w2ufr1Y8bwTnnIgJYCrzL7eatevQbTDYbUaanmvNeT03vOSM0YPSMl49Y3kg2Pz0g2TH8jxbjkjRTDF/g8PjPVeO7NVKMX+GYx3kKQ8INOkZgN3kbQIN4RQNAgfm8BhFgv4iPGBsZGS1YlPrZm+TYASrvRknlwlTnzs9WWzAWfWjNf2GTNfGCTxX3jFot7xGabq8NnTqdrK4I6BXe2O2taSTExsT/w2roCzsOO5oVF64xwkDdr0CDmyejo6K9w0HlFB2Fdoxy+rofPl8P1sJ8fRp10Qp7zwYUAH2oT7J/onYBexFNjL7dCvgtEfsgCnb/mt8k1aNDg8Qb+0yLSRIOE6GifBXm3ion7clpS+uXTktPv
wufzYAHYCU5OS8648HqKwUfMYCBoMIx+EDz8UPDQGkCAFwHk3Ltp5tPg1NwM81EEkh8QPL6em2H9DMHkk3np1vUIHksRSJbMz7DOQwCZtyDD+g4CxxsCCB6VWAg+NNhm4nMeschgnw+WLjbZVywy2T9abLRvRgDZvtTo+GaJ0X5gqcl+fJnRcQqcQRA5jwDirQsBhPgkgE8Zm6xuP5sD2GTJOrvZ4j662Zq1eas1a84Wm3v8Fmv27Vut7tGfW9xdtrhczj1udzyaTliCDNr0Fby2Lhe0U3oo9JJUFEa1eeicbgHvoqCHgFc4OGuRC+hENsOn+xG9GzJfa1Sol1wErkfgyzegpusEQTN6B/J/FIGzDdyp6dFXfegAQEBQBtJ6KkyoV1RU1LJAm9EguUEDnwcHY6eYWF9hbLzvmrhE3/8lJPueTkz1vZSU7nsVvJZcAYJFJaYz1AQQfF7A52kEkP1vpRq34nPp7DTjmwgkz89OMz/8dprxtrfTTNchmBTPTrP0eyfN1PbdNGv2nBSHca7RmDK1njueZkCsaLWiFRgPrHe5EpZkeNJW2O2mFWZX3nKTvd0Ki6M/AknxKpP9upUmx60IHg+vNDknIZi8gc9Fq83OT1abnHsQRE7i73P49NZGACG2MLbaBLL9fCZgzT79mdW9b5ste9nntuyXttnc93xmcY/dYc3utNXqsfgUvkRNpAZoh58EtkkloD3710T7PYguxuai0Jej0E+Ahfh7LzgvVEYY8IJfwUbkNwmfo+GDxe9N3RB13s3g1+3wbz4+DwHymVcWtZwGm2B/Mj6vQH4heRBOg2zwxQuo8SvhGNKqCnb+6w/1rEnPJSa26dQg5nhBTJzvutgE333xSb4JCSm+lxEoXklM8zM1qQIKGoEoDCBecPaNZMPeGcnGdTNSjG8iWDw1M8X4z1kpxiveSDUMmJaQ0mlyYmLbidEJ7SdFx/ecFJs4ZFJMfNmkuMSrn41LuOm52KTbno9P
vOf5uKT/TE5IemxyfPL4F+OTJ06JT3nJT0Lqyy8npMysIG0mfPcDX6eCl14DNFN6LTlj/PRkw5PTUzIemZFsuO+N5Iw7kP9fZyRnXI+ANhb+DJuVZuz7Tqqxw8xkQ5N3Ek329+z2xPsRGEDY395H+2aF2Zy81pjpWGdytFltdRQggFy/1pJ5DwLGpHXmzDnrzZkfIZh8v97sOovvvDUdQBA0/CCA+NleBfcJfG7bYc9+e4ct+79fOHKu/dLu6f61P7jIqsNuaN9qjgk6vuvsmmY1JTp/R8sQDwLXA1omnV71SmstLQCrAd3nvA3sBt8GQNO3zYC2oSeUXwGPALorYTDIAZF06yJ1kPRO44GA3mvwGKARBl08Xg/EdUD3/9PtmbSQGq2LNA3Qraa3AHpegm5BrmsPGNFS6PTif6X0B5J6oV5G2uT4lC6TE5OvfyEh+Sl0uh9OSUje+2JCyoUpCBYv0ayCQYFDQEUA8U5LTj+ATnoN/n51WrLh/tdTjH+Ynpze48WkpGYT6yU4sPMc4+LiPONiYpqNj4lp93R0dJcJ0fE9xjWI6zs+Or7r+JjEds/GxLR8Niap5STM1sdjZirwXGxKoyn4nng+IbXjC/EpXRFwBqIMhS/HJ5VNSUy55pWE1JunJKbehQDyyNTEtGdfTUx7Hb5/8GpS2nr4tWtaUsZR8lMIcP+bGf1vNnTxNFrA6TM6bYaZz5l30k2H5qSbdr+bbt44N838/twMy9R56bbH38uw3v5+uu2q+Rnm/A/SHG3mm822mpgJ0WyH7s5aZ3N12GDOKtloybx1gzlz/EZz1rsIJlvw//HaCCAIGn522nP8fMH4krDl/LzLnv3JLnvOtN32nLt32Twjd9ncTT5t1058XNI7ZnjtXoraHhDq0hV5QocVOzkmqcXz8UmXY5T+6OT4pAXPxyXvxUjdi8DhI15kUOAQUBFAzk1NTD+Gz90Y1W8E8zGqfw3BZBw652fQOb+GjnrO68kZ
izHSX4+OeitG919itL9nRkrGfnTahwh02EdmphhOYRZyfGaq4dCbAJ33T2+lGPe8lWr8Cp34tllppo34XD471TRvdpp5Bjrx59Gp//edVPOddCrrnTRL0ZwMc/e5KcZG89Oy6JqV5IyMZhLTMauYlpjeakZqxiD4cDV8uxufz8KHebNSDJvhx2FxAAm87iJcsBcu1AsX6EUX5s9/QLcDG+wfLzTa5iwy2iYsMjpuX2hyjFpscHZeanA5ZY7GNWm9y2XYaMts/6k1s+xja+Zdn1gyX/7EkrUKgWQ/goi3xgMIQPDw81Ugtuxz+Ny5G7MWBJYHdttySr+y5TX1VQ0sunTp0iKaVUyKj+8+KTbhludiE155Li5hy7NxiecQPHwEggcj2U8IA8gZhtJTWGykH/wayMXRv8q7sDAzODs33fwNZgar56WbZ6CTf/S9dNtN72fYhswx2JosUvCWSDptNSvd0uKddOOwd9JMf0fAmoQZyELwNV2wlwogCB5V7uYS7uAS7txaYrKfXWpyfrnc6FiwzOScuNzk/BsYuszoaLSi4kJ1WEV3XG2yZ7XbbHGPQSB5AJ+zNlmzPsfnqRoPIACBw8/X4BuHpwJ7zrlv7J6t3zo808Ht39qzB+1yXeaE+zV9rVKXrsjTS/XqpUyIju82MTbh7wgY0yfFJXw5KS7Ri4DhE3iOEd4AknYOnJ6amHb81cT0n19NTv+JoNNWmIF8J4Ag8t305PS901MyfiIw6zj4erLhOGYhvyCQnAlnAKFO/X8de0XnHngbL/C+n27dPz/DuhSd/XPglvcM5oELMxyZSk45vVCvXgzsNptnMJciv3+9n2558710y473MyynlQQQ4bZf4XZf4S4tdofWhVUm5+5VZtd7q03Ox9eaM69eY3F2phkFcyNsopnRdrMrb4vdXbTFlnXvFpv7za3W7B3gdE0HEAQNP3uqkHP4O4dn4V5HzqN7HbmjvnVkN+KcBtOl6/cjOr3ydGxs
kwkxcVePj42fPDE2/jNwAcHDRyB4+AkMHjICyG/4+9ALCUlfvRiftOHF+OQPpiQkvzElPmXSiwkpj0yJT77jpYTUG19KSr385cSUgqlxSb3xf/sXYpMbv5hgcE1NS0snv6iDJehCMkEXewWY+5UU+LuQhqWPpudEXk0wOGckGxu/mWLoPDPVMHBWmqEEAeOGt5KNd8xKNT2OYPLKrDTjfASQj2enmva9nWr6hXc6SUkACfIciHdBhu2XBQbrGnT8kxaY7H9cbLS1f8tsTmbFkSUKLB+kW1rCxlULMqxPf2iwr/zQaDuiLoBUvbW34o6sTO96i2s/mA/+u8HiGrPRmtmsJmYsn9ZrF7PF7G79mdV99Var+5ltVvfKbdbsw7URQBA8/OytzK/7HJ7VPzg84/a5ci7fb2/YWD8FpuuS1aR69ZKfjo7v+XR07F3PxMTNByfGx8T5JsTG+0HwYFQKIDT7OPVsbOJ+zEQ+fTYu4f1nYxOm4PuHn49LuhkBpAyfvemi80v1jCnUYVfXyUeSKPjMT0vLeDfN1A6BpHh2mvmfc9JMk8CHCB5fv5tm/k1DAPFDs4bAmQM4v9Bo+2yhwfbCIoP9ugXplhZv1Wuq6DWwqPv689KsOQggYxcZbc8sNtnX0zIoagNI4K28wt1XdLfVBnPm2Y8tWZ9+Ys18YaMl88ZPrK6OdIqKuRE2Ufm2OXIzt9tyShA0HkFQWbzd6j6Ev701GUAQOPx8z/jBmcvwnEJAWYG/H/vRmVO83+7JIp+Z+7p0RY7G16uXOq5BXP9xDWIeHhcTux4B4yzwCSB4ePH5K2YgeyfExq2ZGBv3xsSYuMcmxCXcPCE2YQTdpTSlXqqBRrrMpC4m6tjpYvq8VFPZuxnmh97LMM+dm2H5bm6G9bzaACLMHAJnEAgkv2AWsRiziPsW+teqMqYwF2SLRvL0bMgSk+0fy4yO2ctNjh+WmZ1eLQFEuGU38G6rTdasc59as7ZutmY9v8nq
vmazLavp9qbKAqAaUQe9HR31Trt7DALJ0zttOeu+sGf/UhsBZH9VvPudnkMHHLlzDrg8d/zoyu55xJCXylzXpavuCKP/+HHR0T2ebBDzwFMNYtaPi449C06Pi475HgFk1TPRsVMx+/jX0zHxV46Lju/xeL1Euv0z7Af470hRdHfWvAxzPmYi/34/3boIAeRnTQEE0Gko4VRUxYVw+7qlRvuDS4zO3nTxneUtW3R6b4XVmr3S5LgWTEMQ2YsgUq41gAi36gbeXbXV5j65xZa99DOr+99b7dmDPs/y370WdlHg2mnParvLnn3LLlvOm7ts2XsRRMprOoD8yDjA+Ik+HZ5zB52ezQgqzxxy5JYdcea5EAQbHDY2SjnmbNjyUGbuqMPO3H8fc+U+yoqjS1fohUYX9XS9etmP1Y+5FgHjqSfrR89G8Bj/VHT0LU81aDDk6Xpx2U/VqzMv8vldip6dmJ9mbj0/w3r7ggzrogUZtlPaAsj/rmf4MTp+W2p0Llpmcv5jqcHWVM3pEmpHq6xZOWtMzhsQON5aY3IdCmEAuXhHFbsQfh5s32FzT9hhyyndbnbbKH/mSthEeXyRmenYhVkKgsqzX9mzPwfnayOAEAcZhyo+vQedeT/j73OHXXk+4gigQMLc16Ur5Ip6ol69HAoS4+vVi2Pf6arjmuVyJSCIDF5gsE7+IMO2X2sAoWsaF69rVEBLuz+70uQcTEucsGwVia7/rDLZ266xuO5dZ3GtRfA4F8IAIr74Xb7Tlr37S3vO8zus2WWfW3KscKFGrqHtcbvTv3LkFH5tzx6HIPIpAsj52gggfihgsODBWF8TgVWXLl0RKuqoF2TYu31osD/7odF2OCQBxFwBXd/A/78sNznmrLY4r1hT8eCjKlFaBJIxH5kzXwdHQxxALl74ZtcsyjFD2AbG7bJ5hhyogQvzgiigfIOAAp751p7zOQJHeW0EEHx6jzjzLp0l4iNY/wS0HMs4EFFvg9P1+xJd
lF9ksBchcHwALoQigAgXyStwnFtpcixcZXJdt8ThUP2uGf/ijSZXr43mrHEbrZnfhDqABF7s9p9esmWfwexgxdd2z517HO7WNTkqP2D1WPbYPWMQTF5B8DhQYzMQZ96bzAVdtahO4AL4CJjoC126IkGLKm7DfQocD1UAqbjTquJuK3AefLDa7PzDBo13BG00u1t/Ys188BNL1vawBBAgXJugmcC3ds+P4OXvbZ6RxzyeNOZGyEQLMO625eYjj1sQPCYhz8V7HDl78emtkQDizPvtmC071K+w1qVC9E7mqeDSedmLrt+VPjAYUhcb7HcvNjoOhTiAXLxld7XJ+dsas/ONNSbnYLrdl2WtSpts7iYIIA9stmR9Ea4AIpxOqujIc87ts3uWfI/Ofm9mXi5zQ5Mww2mw25HbBUHjnj32nKXfOnJOCXnWRAA54srV/NI2LaKLqeQA3f7Vgb4IocjeM2AN2AFWgYeBDWgVvWeCbC0DZJtW/6UX8fcAakW+vgyK/f8pE02ThwJaXXcToFV4aUVeWqFXzUH2D0C+BINW8FWimwGlu83/nzq1BlRPtJowray8CNwAQnG6bxgQytaOvtClTivM5mTMRB6gBwJDHUCEW3b9mJw/rbE4H19ty2rKslarKJqZbLJmPbnVmrU3XAEksBNH5+39weHZjk77gf323LYIBPXpdBvdInvY6el70JX7l0POvImHnbmLwR+Yn5Ki24H3OHP7IJg8BNbttXtOhy2AOPN+Omhuqmg1glCrDUDd+QnVe4gpKL0IvECwHchh0ByoEd1yeBc4B3i2ywG9rVCpaP0ewYbsxsJEnSct4R7oRyC0ZL0SUTD6EfBsBaL0nu9vAKWjmZZSURB8GlS3T2cDLeea08FBINi7F+jSqA9NWXbMRGYgcJSHJYAAunV3jcXlXWvO/IjWw9qucHkVsbDz62Mm0mer1f3q5zb38TAGEHGn/RM66l+BV+igK64v5H6t5eHGfS5Xwh6b
ZzACyRP7HDmffm/PORuqAALfaPBWq7oO0AFLHa/iJ1Y5ogXf5gCySdcTaEQ+AtDa+c8DoQPaANR0OE8CSk92KB96ZWo/8CCgdw3Tb/uAUttkg9ISSkdTNLKndPQObvq7PaD3g3wB6PuzQEkDpM76z4DekULQe0WEcgnfEUr8pABJ+5jsXENfKBDV5WuA0lLgHg9of44B9K4T+p72h5ZZA920QHYE3gO6QqTlRke/JUbHnnAFEOH5jwpcJ9aaXeM3aJ+V0FLvCQggVyJ4rMDnhXAGEKGzvthJswByxHkZvcMjZPrKkJe6z+4pwuxnAvzADMhzXlUAceZ9jgOlVt9uSXoW0AFLnV0oRB0f2aPOaix9IRKdnhB+V3oqizotIQDxTt/QS62oE6cOV+l1DHrHONk9CZQ+WLURUFoqW6BodEAvkKIXaGlZSE6w/4b/P3WiU3tkg1B6YFOQpnRU9/TSq0DRrYM0UNgD6IVgatQEUGAi+0Kw/B5omdHoEomWNFlickxHEPGGM4AIDxGC8nXmzBVrLZnDtV4rIX1pzcpBIPkPAsgPNRVA8Deddg9rO/zKlmdGMBn9o8PzIj6/hi8XpAIIPr0HM/NokFrr+hho7ZwEUSP5AZA9OqXBE11foN+JjvSFTNFOpGsLlG4JfcERnTpTey5+JiDbdAeWUtEbBiktzTReAPSmxlCdl6Qy/QrIPt1irFZ/B2TjF6BkNkTBlK51UNp36QuRqL7pQS4tomtFZJ9mlDQ7or8pKJmBrhBricl5wzKj40wNBBA/9DDhR2bXdxssmf9YkeamU5WqREFoky3bvdOaU7DDnrMy3AEEnxd+zMqt6WtxUd8781wIJFcfcHimHXB69sEvb5UA4sj9gG1fqwpV5ySIrqGQLWIAfcFRKRC2ode3yhWNUoV0gY/r02159HrdQNTc0kYzBbJNMzKl+isQfBM4DahDVHutRxDNFgSbfegLlRJOQdGAQYkC81dzc4GU6MI52T4D
8gCd/hPyE892dIVIy0yunstMjmM1FkAAPZEOTm4wZz21rvrbTistv08PTtIqutus7lXbbO5favIU1k8ODx0ztSqqC8w0cuHLjWA2fDt4yOE5e8SWp/n0YChEnZtwsGrpnAQJp4HodER111P+DWgbClxKTutcCwRfs+gLpvuB8L3AO0CJyFfh+gBdE1IqavB04Z5mSMIpNgE6JablHm26oE92aESuevQGCddjaIakRH8ElI7KZacvQija/18Dsk93dpHoGQMqK333AH2hKzxaanG2XGZ0HqzhAOJ/Ih2f5zaYM6d/bMnq/7E56w+fWLP+A+Z8YslcxltO5au8vDgEkT47rNlP7rRm70AA8YY3gHh+OZzZqM69uxwHRf2jjtxM9m+t60pAByp1nqFY8VK4nkKdJk/U0W4BtM1y+kKB7gGUjgi8vkEd4neALjALvyu9g6cXENLSbapaRJ3sVWAtEGzS6SO1Ei4uf+v/T52oUxYCpNK7NugWb0pHNyjwzgVreTCL7qYT6oiuX9HgghBmxbQigK4waoXZ0XqZ2XGipgNIILS8CcP7sc0t6zoaLcW+y559wy5bzrv4PBnqAAKoHeqSEN2WSQcq3d4ZCk0CQmfDC0jDgdBh0IVZJQo8TdSYvhCpGRB+p4vtSvQ3QOmo41JyDcUF6HoMXSPoSV8EiDpWwR+6M0uthEBU3TUlOaIL3YIvbekLBXoEUDqaFYhvTKAR2ilAz4QoXYeH6o7SCn7xoFuZeUFLVwi1xOzKRwC5UAcCiKpTRvRGvy+tnr67bTmP7bZlf4Yg4tUWQDz79qtY8v73qFB0ToG6HggHP80MAm8xo4fsTgD6bQVQeqdTFyDYpmsLdP1GEI36hecw1Fx8pVuNKS09jKhE9DCj4JNwCoZEnZ4wYyJ/6LqMGtHFbrroTXbupi9Uim75JRsUIJXezz4aCGWkU5SCqI4pcND3h4DStZLopg1KSzMrep4lEArK9BvNmkJ92kwXR0tN
jvtrM4BstGb+GKr3fuxyuZzf2rP/+I0jZ863ds8xFTMQpc+B/S5FHYkwAjwKdnKg5zeUiE6VCHdhETSzmQuEu3iIz4Gau2so4NAtdYIdGp2SberEhHPmBD1folRqrw9QgKTTZ0LedAvqLEBPoQvf0SkotWoIBDtabtkTbp3e6v9PmWgkFlhGugg/HwiBjW4WoLvOlIhuKRauFdFFdLHoNIaQXxF9oSu8ooUZl5scn9dGANlozfJutLjDsp/pyfJ9tuyeexw5j35n92xFICkPGkAcuR+j0Skd3P4uFdg5VUcLoFR0Kkm4rTUQ6nDoATQtt7fSKHceEM7nC1BnRJ02jbSV3sZL/ggPIN5IXyhUV8B7apyuA9EFfi2nYITRv5pZVaAocJAd8XMqckV3wNGAIrB8BA0GlK5eQPtHuA5GM1GenEAIMHQKTVcYRLfFLsmwZy0z2wcuNzlvWW52rKiVAGLJqrGVZL+x5Fj3OXKuRRCZvd/pORIYQED5QbsnVKtxXPKiO4/aSKC286MITvdPXw3oLh4aoYZyjX66C4uuoVCHXwKygVpf6VSYUF61q4ySDSojXaCm8tJsIRTnUC2A/Grl/0+9WgKyo2UNMtqndHBR+egmAfJJTZ3TnVdCfVe34jHlRfZpG9q3ukKsFWa3bbnReScCx+JlJsfPtXYR3Zx5aLMtr1ae96HZyX67p9uPDs8jPzo9m4G+JLouXbp0KRF1pCstrhYrzPabEUhmrjA59yKQlNdEANlgcdFyOHVC9MwJ+1OXLl26dKmRDzPLVaYs+wqTfcxqk3MigsfmlSbn2ZAHEHMWXcPUpUuXLl2Xsmh5+NUWR7/VJtf9CCqL15idx7UFkMxjm4yZde5BPV26dOnSFWbRaa/1FlcLBJCb19LLpcyufWstmV65AeRjs0vpitC6dOnSpetS1QaD07XWmjkKQWTCOotrE4LHeV4A2WDJWkinyVgyXbp06dKlq7LotNd6S1Z/zFTuX292
Lf7I4jqOQHLiU7sncC07Xbp06dKlK7gw66i/0ewOxeusdenSpUuXLl26dOnSpUuXLl26dOnSpUuXLl26dOnSpUuXLl26dOnSpUuXLl26dOnSpUuXLl26dOn6Hahevf8Hcy6+S6p3q4MAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="logoatslogin">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAuFwAALhcBXGSJOgAAAAd0SU1FB98HBhI6LyktE6gAAFGtSURBVHhe7Z0HWBTn2vdXBexiARt2sVCkSK9L79IUFRWx95JEE1M0gCnGKKDpgC09ISaxxZhogrLLFlg6Kpq8J+fLe/rJacl7TpKTOPd337PPwrAMsJTFNv/r+l8L7Mwzw8zzm/vpI5PU8wKZrK9igMek0gG+8tJBfhmKYQGPKkcEHFaMCnxPYRt0STkmsFIxPvhr5YTgP5VNkn+vnCr/WTldzpXNDAHV7FAocwy9VeYc+pPKJfSfarew36s9whrLvMK1Kt/w82r/8DfUgRHPa+QRD6hDouZrIyI8teHho9ihJUm6o9SnROYz5ZLMN+WyzDfrSh+/4iv9/OtLrQL+oxgQAIpBgaAYirYOBOWIIFDaoEcHg3IsenwwlE2UQ9lk9LQQKLNHOGaFgsoB7RwGKtdwUM+NALVXJKh9okDjHw2aQHQwOgQdho6IAW1UNGijozltTPTfNDFRKm101BH8fKAiOjqkWi4fzs5TkiTzq1g2YeCn/TxCPrPw2P1ZX69PLvX1/ssXfX24kr6+cLmPH5T28YdSC7QVwkGADBYAMpIBMobgQCgmIhRTMFpMD4OymeEIBsLgHAVqV/TcGNB4xYLWLw60gQmglc+D8rAkKI9KhoqYFKiITwVd4nzQpaZB5QL0woVQuXgRVC5aCLqFC0C3IBUqUnD7xIRb5QlxN7SxUW8gRBvQThjd+rB/R5Kk7ikLi0qnLNw9z1i5P3HOcm7JJ5YeP35q6QkXLLzg837ecKmfDyAgQIBg9GgJyEAGyDD0CITCBj1aDspxCMcEhGMyRompaHuEYxbC4RgJGheMCm4IhwfaG+Hwj4dyBKQ8JBHKw5OgIjoFdHEIx7wFUJlCcCyCqsXpUL10KVQvz4Dq
FcuhZuUKqFm9EmrXrGpyzaoV+P0yhGghVMxP/nNF4rz3NDExqzUx8gnsX5UkyTSdl9n3/8DKJeHDAa5HPurv+sdT/d0BAYGzVnMBAYF2AelLgARAaX9WvBoShNEDzQOCYIxBj0M4JmDk4AFBOGa0AYgPAyQIowcPCEYPHhCMHDwgGDUWLNYDsgwBycyAmhWZPAw8GOvWQN36dVC/aSPUb9kM9du2QMP2rdCwbSvUb97Ib1O5NJ3Tpc2vrkial6ONi3Rjl0CSpJYqkcks3h7kEPfuQKc33x84518fDHQBBAQ+HuAGBkDOtQHIlwRIHwEglgQIwiEEZCQCYssAGS8AZJoAECcGiDsBEtsSkFAEJEIASCIDJM0AyDIBICx6ECAbGCBbN/NgNDy4Ha4+9CBc3fEQ/9nw4AMIzTYeGAKrKn3RV5Wpyc+UxUc6s0sj6X7WG4NnO785yCHvrUGOf3pnkBO8N8gZigfOgSZA+osAYuEJn7UAxLcZkH4MkAEMkKFtADLRCJDZBAhWxA2AeBoAweKVEBCsfxgDUp0uAGRlO4Bg9LiKQPBwGLmBYCE/sB2jzSban6tatqQKo8s2qYXsPtNhLEIdHzoz48SQ2WWvD3Hg3hziCG8PdoJ3EQ4xQE7zgMxtBcjFtgCxQjCEgAwnQLDuQYCMbQZEJQaIa0tAygmQYCNA4hkgqe0Bshrq1q01GRBjN8GyeROl92PV0iVvVaSmBrBLKOle1CuDp40uHDoj5+jQmX86NnQWnBg6G94Y4gAtAZnDA3JSFBAPON8GINSC1WlAphgAiRAHxNcIkMi2AFkC1RltAbKeLz51FpBmGyLLNqjbuJ6rXpFZVZWevrwhLc2KXVZJd7tesZ407VXr6a8WDpvxnyLrmXB02Cw43huA8E28AkBGiwAyXQCIswggAQZAkloCkpSmB2ShEBBqwUJAqAVrLQNkowEQfQX96kOdBURgVmehCj8WwX5XnZHxsDIxcSi7zJLuNr00
bIL9y9bTT7wyfPp/XxtuD4XWM6BoWGtA3hIA8n4bgFAL1jmEwxgQasFqBgThoBYsISDDGCCjEAweEISDAJkkAGQmAsL6QHhADH0gQkCoD4QAidUDUkmAzF8EVQTIko4A2cQA2dZcQe+mGxA0SrNm7aq/1WQs3aNLS7Nml13Sna7DI+0mHB45tfDFEVP/+/KIaYCAQGtAZjcDMtgYEBc4OcAFPhIFhFqwTABkYDuA2LUDiJsIIKyTsIIHJBV0CeKA1Aj7QAiQ9c2ANBAgWEzqKUAMpuIXNR/XrF3zt+rly3fpEhIGsdsg6U7T4ZEjh+WNmvTsoZFT/v3CyKnwIsJBgLwqAOSIAJDXhzo0AfJOK0BceUBO9QQg1AdiAIT6QJoAQTjaA8QvvmNAFmEFnQBZbgwI9YEIAKE+EDMAYrABlLq1a35Xu2L5muK0tH7stki63aLe7gM2E1YfHDXpT/mjJsOhUVOgGZDpnQaEWrDEAKEWLD0gXvC5ABDDMJMmQKgPhAcE4WgPEOokpF50BETtEAnqOQiHMSDUi24AJCoZdAZAkoWA6HvRmwFZ1QRI/cYNfLOtuQExmAdlK9VRVtdgRAllt0jS7dJ+m3Fz99tM0BywmQi5NpNACMhLAkAKxABBOLoMSL/WgOiHmRgBItaLzgOCxSvDMBNDBJkTBWoEpGkcFhaxtAHxoA1ChyIkEYlQEY1RJA7rIfMQlJQFULkAK+qLEJSlFEUyoFpYB6EIsql3AeGNx6DKfN2mjVztqpXvISh27HZJ6i0dGDNm8D7b8fnP2dr98rztRGgByEhjQOyhwLoZkGMCQKgFywAI9YG8z/pA9IBQJ6Eb30koBsiljgChXvSmCILmR/HKQWmHoFBH4ZSQf5dNDfu6zD5MWTYr/COVU3ih2jliv8Yt8jH13Ojtaq+ojVqv6A1qn8hNWp/Ih1T+UVnawMh8jTzyLXVo9Jea8JivNJHRP2tjY6E8AaNN0jyoSMEokzYfqpZg
3SQzg1qb9MUsaubtLUCYm5qH16/7V9XKzG2QldWX3T5J5tS+0eMj9tna/ea50RPgOdsJoAdkUhMghzsNiFMrQKgFSwyQlsNMmgHhm3h5QNBsoGLp4ECudGjgnzGKXFaMDCxQjAraqRgdkHLZJtj94nivHumdBizrq6Ki7NXREYnamKgnNDFRH2pior/FT04bFwPl8+KhIjUZKtMX85GFmnypNYue8L0Di74fhQCtWb1CVbV0qSM7dUk9rc8iIwefdPd4af/oCbd4OASAHOQBmdwKkFeaAJkBRW0AQi1YbQOiH2YiDkjzQEWMIFxpH7/fXOnn936plf8jl/sHRJbJ/EazU+91aWJiJmiiI9I1MZGFCMv/8MDERvPA0FD46oyl9GTXD2BEWCgTi2fwHjIfTbZD7YZ1P2Jke1iKJj0smjV3OSykMXf8ZK4Jjm4AQk28YoC0GGbS7kBFr1uX+vrUfNHH+9BlmU9qicxrLDvVO1La2PCZCMuDCEoJRppfCBaKMDRnpGrZEqilCj01BZs5sgiiyZXKJUsms9OT1A31UcdG7dBER/706rSZLeFA7xcAkodwGAB50QAIVtCbAZnZNiCmDFS08PjrpxYeb17o67HkM5nLbYsO3VVVTIwtgrJOGxP9JcLyKw9LfCxfFKN6C1Xs9cWw7froIjTBIzQWocRAaNe4H9VNatau/nvV0sUL2GlJ6qwUgYEjNNFRZ+gGvu82t7lY1S4gU1oBQk28BkCOEiCsF10ICLVgiQFy2srt29OW7ofOWbgHF8tk91zbPk2YQkh2ISzXeVCoGJYQBzQrsWpJOlQtZcYow/e1sBYy/UhhrPzzc0028cNYqAjVAiIxOASm3vi6jRu4qoylL0ljuzopTVSUqzY26msqBlySB9/aP2ZCq+hBNgaEmngPIxxtAzKrCRBq4m0JiH6YyQcDXP7+kZXLax9bzAm6n6anqmMiAjUJsW9Wbt/4n6odm6HyoU1QuW39Ld3G1VzF
qgyufMlCTpuSyGniovn6DEUdgqk8MYGPPgaoaFYjDxDBwzcv68ERg8ZQ5KpakaEtT0qayE5FUntSRUcs1MRG/x81W2riYrkXJ00XjR5kAuRAG4C0GmbSDiBYxOLeG+hcUjxgzpLjsskD2Kncl7p5s9T2xvXSJ242Kn5/s1EJQt+4ruBuNFzmrmvPc1cvFnMNHxzl6grzuOrnnuQqd27lKjKXcNqEWH2DAMJDzc40zZf6aAgavleftaLpGwb0rVwUgapXrvhz5cLUYHYakkTUB8vGWfhkusXPncan0nuu7m3CQe4UIFi80ncSCgAZ7PDDO4McX37TylFqfjTSzZvn+yMkqxGMG8agtGcC6FrJR1z9269xNc9nc7pt6/nIw0PDWtGwWKUHZgv1z2CEIUiwXoKQ/Kydn7yWnYIkgxocHa2wIv4WtapQOZfKuyVhoVi0mihatDKYB8SmJSDUgmUYhyUOyGw4MWT2n94c5PB4kWzCSHYKktpQcXFxv8ZG5bIbnQRF6BvXSnlo6o4d5qqe2MGVL15A/TQclRLoYUjTfWlyF/X+Vy1fxlUkJx6gIUTsFO5vaWJihmEl8YvK9EX8U4SeKniBuNemzfxVDAqh92P0MPSitwaEWrCaAaEWrKNDZ/7h2OBZ22n5HnZ4SSaqpKTEAkFZc/O64ndiEHTGVFS7duVjLJ7lcpUPbuK08+I5PrrMT+WBofpMeUL8++djYvqzw9+fUoeFjdHGxVbRE6Rxz25o3P0439R41seX4Gg3epBNB8T+uyNDZ+zMlcDotnQ63SDM5E9iJv8/44zfVTfWfsk1FB/hqvbs5LSpSZyhAUAbG/OlJsZ7GDv0/aXyqKiJtKgZtXbc2JsDX+17BmjGmwpDb77dlHbrHrxZC1a7AxWtp/302jD7A8etJw9nh5XUQ/qq/spELHYVUzQQy/RdNdVhGk4e46oef4jTJiZwWC+tKJHLbdhh7w8pwsOnlScmfEO9tzef3gtfPfcs
3MjOAipmYcW8w6IV7yZAmsdhCQYqci9bTzv70rDp9uyQksykrxpLYxCS34hl9u66seYLru7Ei1zV9o116nlhY9gh720RHFgZ/y21g3/17NM8HF89+ww/kE4RGX7rwNhJHUcPcluADJ/y7QvWk5PZ4ST1gmpqPhuM0eQQgnJLLKP3hK8pTl/Xxsnv6CE93ZYuImJSRWrKN9ce2ckXqXg40I27n+Cbdd90mmNa9CAzQKgFi43DunV45JRX98tGSQsJ3CbdvFYqv9Go+EYsg3fXVJSr3LSmXnevFreoQl6RknzTAMfXCMbX+/dhEespfpmayyY067awEJBRk77NGzkpgh1K0m3UzZuaYQjJWz1dNyHXHTlEdRLdPVdxx8hhrUtNqTaGg6LH9Ud38cMVXndwNj16oPlhJjYTuQOjJhXvs540gh1K0h2im9fLViIk/xbL6F11Y9VFfZNwfNwX90wTMP0jCEeJMRyG6EFNvFfCQn99ro3xVm3axu7H/aPGb2CHkXQHqrGx1AUz9k3jjN4dVz36AMeP/0pOeveuHytH/0B5UuI7V3dShbwlHMLo8UZn6h7ofbZ2v91nM86DHUbSHaxvvikZfvO68rxYZu+KG04e5bQJcfoOxfkp+9hh7k6Vz4t/ippyqbXKAIbBfPRYuQIUEeG3nh8z0bSWKzTCcfmZIWNt2SEk3QWi4SpY3MrriXoJ9ZNQZyI/737Fcq5y4cKV7DB3lzTz4tLrNm/kCASKFkI4+OjxmD56vOvqZnL02Gdjd6xAJrNkh5B0l6nxWukmzOS/GGf6zrrqyUc4GvRIC1LUrF75U9WiRf7sEHeHymNi3GvXrP73zadyWsHBA/LM0/zEG3V05K3c8ZM7jB4YNbh9NuOfZMlLuov1VaMyCSPJf8Qyvqlu+PA4P3aLAOFnJ65Z/cfKxYvHs0Pc2VJFRY2sWr7sG+oZF4UD/9a4R9/vcdrLu8PogXD8ipFjHUte0j2gGzfKgm9cV/5T
LPOb4hv1WMxKnsdRA49+qPx2iiRlunXr7uzSBVXKK5cs+uT67sdF4eAB2afvNdfERnMvTbFvFxCE47/P2YxfzJKXdA+psfGy141Gxd/EADDFlbu2c/TSUoKDZivWb9tKLaL5LPk7UxXzU3Zd3blD35wrAge1YvFjrhalwaXg4F/3tTNil+B4xtZOmtR/D+vm1cvumNm/M878prju9Zf4+SQ0J56fwouQ8Ks5rlmRxJK/s6RLjPOp37zpvzexfiEKB5qiCjX50nDmNxzbadq1tfsFAUljSUu6h3X9usID6yT/EIOgPV/XfspPuqK6rGG+OxW3atev+652zZo76+29DXL5EAxvX93Ym81D0JapRYvme1Dl/OC4NgYl2trdes52fCZLWtJ9oOvXr/gjJJ2eX1KxahlHq67wi0LwC0FgfeTB7VC3fs2lO2pxOjzJwutPPAY3n32G7xAUM313/fHHoALD4lkfH9HowbdWjRq/gyUr6T5SY2NpDGb6n40haM/V+/Zw1NhDb/DVA6IvalF9pHbdugdY0rdXlSkpMfXbt3E3n9rLR4g2jd/TqhY0eb/IfnYbgIw/zJKVdB/qxjVFZmc6E2lVFepVp8XumgBBU1EL6yP/rt2wYRZL+vboemLi0Jo1q75tfHIPApDTrmkb6v1URoaLr3NlO/4cVjqkF6/c5/rqRlm2GAxibqz8nK+H0EIfBEULSLDYVbdpfeltLWpVpS966dquR+BGdjbcyGnf1x7eCeXzEuCUp0jfh63d1ayRI+/PuceSjNXn5g3Fe2JAiLlixVKOX/CjqR7CjMBQ0at+04aNLN3elS4x0ad+06ZfG/fsgRtZT7Zrih60mrg2OhoKjYpXWO/41/5R429vKJR0R4kWhmi8rqgWA8LYVdm79PUQekmpEBA0RRUs1v/9+sMP924vO61TW52RUXl91y5o3E2rkbTv67sehcqFi0AVHtFiUhRVyp+xGb+QJStJUpMaG69MvWlCRyKt
tUXDTqh+a2jubQEJRpb6LZvfYsn2jiqT529o2LIVGh973CRf3f4AlMfHwzkv3xZNu1gpf40lKUlSK928rpiHELQ7z52WQKWGH3qFg3E9hDdFka2bb117+EE5S9a8qpbLh9dmZP7l+sOP8JFB78fa9iOPQt0aLF5FRMHrs5ybAMHocT1LNk56bbCkdnXjuiJPDAyD+XFZ8bEcLZotCgiaL2pt2ayDkhILlqz5pEtJO9iwdTtWuneZ5Ks7HsbiVTqowyO5/PFsvStbu1+eGznGmyUpSVKbamhosMJI0m59pGLlMo4mUFEnoRggZBr12/DgNvPOHakMjZtck7Hyx6sP7oRrOx4xyQ1bqXiVBF/4y5ujh43dfpakJEkd6tq1Eucb15U/isFBrnrsQVZRF3QYGpmiSN2Wzd/+ubh4CEu256VLWnCifuNWuPYAAmKCr6Lr1m4ATXgMfOjiyQOyb7Td19ISoJI6q8ZGxS4xOMg1h57h56nTus5icBisf1fJ9l0syZ5VmTxqds2yFb80bMVy3jY8oCnGbauXZoJGHglHpzvcolar52zs4lmSkiSZLIDifjcaFRVigNS//Sr/Vl+aQNXea+H0dZFNf/nm+PGeX4pWlzj/7fp1m6Fh8wNwdTOexJaO3bBpG1QmLwR1cASXN34yAjL+E5acJEmdFg2Px6JWqym7Vy+8p2/JEozsbct8FHlg2x6WZM9IGxg+s3pRxi8NG7CisxEPYKLr126G8pgkuOyL9Q++Yj7WgSUpSVKXhIDkGwNyveICD0jNisyOAdG3aP3l7wUF1izJ7ksXm3KkbuUGqF+PgJho2rZ2xTrQhsbCGVdvAuQoS06SpC6LVm5EKP4oBIQGONKCcvSyUXo5qBgYQvOtXQ8++DBLsntS+keMr0pd+lP9ms0YEbZ0yjXpK0ETGAVvzXT5+SnrMVNZkpIkdUvs1XAtokj5skWsqdcEQDCKNGzd8i3odN3vh6sIT3imdtlqqF+9qVOu
W7URqpLTQeMfAa/bO7/OkpMkqdsCgL4YNWqFgNDbeHVpC/Rz1EWgMDZtd/2RHd3rF1H5+g7UxS34a23meqhbsbFTrl22FsojEkHtF869bu/gzpKUJKlHdOOqMk44d6Ty4W0d9oUIzddFtm2tRNi6PsVCHRi1smrBcqhdvgHqTHXmBtx+PVSlLkM4IkHlG/4lS06SpB7VzetKhQGQahrVm5zIv5udr6h3UFknN2zfxl3PeiKEJdd5lYcnaWvSV2M0WNcp0z7lYYmg8gnnNN5hQSw5SZJ6VF83loUZokj1/qzmVU6oHkKVdQMobcBCUQS3exejSOcXwVZ6Rbrp4hdxNelroXaJ6a5BVyYtBbVPJKi9wy+z5CRJMoswiigJkNrDz94Srraob6kii8BiAAY/MeL855vjxzv/5ipNQPQLVakZULN4dadcnbYSykOx7uEVDiqv8BiWnCRJZtHNm2UJPCCvHODfjssPe8d6CG8CxRgWHphmUKjV69rOnQ+x5EyTzsPDUhsy76/V81dATdqq9r1QYPy9KpFFD8+wOkzq7n5/g6Q7XlQ8utGovFpXlH+LB2TtGmjAeojeDBTeDBa0HhY9MAQIfleF6Zg+FF7lHjZPF50GPCALVpps2r4iPAXUHhg9PMJWs+QkSTKrsB6yvv7oYX5BQnqdX/3WzdTPoXcHsFB0wWIWd3PvXtNbWtU+Me9UJSyF6pQVnXLVvGWg8Y0BBOw71QRfacSupF4RvWW3rjDvH9q4WFrEmq+o6725DVjQQljw89qD202bgkEZuzwg4fuqxAyoTs402VVoXWQaqN0xeriF5bLkel2v5j5uV5CXs7MwL/uTorzsrwvzcv5RmJ/9PX7+Ef9WXpiX9UrRoZxIc72+q+Bg1uyC3D2ht9tH8vf2yEIYBVlZg/B/CizKz95YkJedi9fw3cL8nM/wsww/tXiNNfhdKX5ewM8P8NoXFuZm78NtHyzIzVpM+7588ImJaWlpZl3SqXLb
xsPa2BiozlxOa2JBPXmzAZQOYMHoUrd541cmFbM0c0KSKkJTMRogIJ1wVcIy0PongMo1jFM7Bff6oMTDh7f2L8rLeR5v1E9F+TnQkfEGVxUd3NOjr3EryM0JxUxzS+x4vW39eWR1qY3/pZeyhhTl7V2DD5aLeJ1Mup4dGdP6Dz6kavH+vI6fPd70Xx4Z6ayJiqI3TkHd+vVQv3EDmoFigEUIDMIiBIbeONC451EvllzbUrlHHNdFLoSq+GWdsi56MajnRoLKJVTFkuo10VMOb+RlsRvTnummFeVmxbFkui16yood53YZn+Cb2KmZJHrIFORnP4Zw/U0svZ4yRXU8XI9HcE1kZHl5QgLUrloFdevWQT2BsoFAMYJlkwgs+IlFrWdYUuLCYkdfjWfMn3VRi6Eybqnpjl0KFSGpCEcYKJ1Ce32hLoTjTbEbYYrxZv1QeDBrJkuqW7qbATl2aO8MvI51Yun0tPE4v2aZYcVDbXjUVm1kNFQvXQZ1a9ZC3VryumZY1iMoPDAIiXF0QUDqNq6vxmJW2+elcgzz1vomgC4qHSqjl5hs2l7jEw9lTqH/LZkpt2HJ9YqoGIEXnBO7ESY7L/s8S65bulsBOXIoyxmv4V/E0jCHzQWIOixsjCY86hddynyoXbkaalevgbrVCIkxLOsIltbRpXbd2lvXs7KmsORaq8wxdE95YApWtjGCRCIkJliHrgjFyrkrFq8cQy+wpHpNeLE/EbsJnTEBVpC7ew5Lssu6GwF5Zd+jI/D//63Y/uayuQAhqcOivtDGxEN1RqYeEvKqNQwWdBMsaIRFWBSjukvD5o1tv+5P5Rx+pSJ4PujCF7V2RBvG7wgqlVMYIGC9+i5BrD+MxCLSL2I3obPGdJ5myXZZdyMgRbnZBWL7mtPmBEQTErlZExYNVYuWQk3maqhZoXcLWNBNsBhFF/z5A5ZUS+nGeQxSu0T8VCFfALrQhaY7ZCFovbB45RByS+so7/yYlm6oIDdn
vtgNEJqiA3522LKE25WzZLusgvysVWJp3y7T+bBTE1XBoaxJWLz8r9i+ZvYv5gJEERAxSR0SwekS52MUWQk1y1chKGQxWPTACGGpWbnyT6DTtX4ZaNnMoDCNWwxUBCMg8jSTXRG0AItXUaCcHdLtDNZZ4VP/FZGL39J5WWuO7H94aEfFiEK8acfzs4azpLukAwd2DD6Sl/NsUV7OEVON53Va7HxambYT2b8t47V5ls6HnZqoCvKznxA9lojpQUMPkYK8rOcw6qzEY8wryM+JJfM/48MKv19amJu9lu8Dyc95Es85F6/rMfofC/Ozlfh5E//2d4y04k/pHpI6OKKmPCoBqtMzoGbZCr3bgKXGAIshuqxYCXXbtrmwpJpVNjN0j9Yjns/wumDM/CZ5AZT7Y/HKMRyUs0L2sqR6TXjBG8VupsH4/a/5LNPzHYQi2wiNT9xkPuFeVMGhHC+xc2nl3KweX4kSM2qp6LGMjNfxekH+Hh+22x0vdVDEfk0oFrMWLIGaJZlQs5TMQGmCRQCMAJaaFasQklWti6aqGSHny70SoSJgPlQEmu5y70RQzQwF5YzgXp33Qb2zeOPabb3C76vY5lgcy1osto2RX2Cb95puFyAAsj40wkD0WALjNfzLS89n9WrRubtSBYZHaIIiQZeYBtWLl2MkISMkBAsPjBEsLaLLKqjKWNFqNfg+qllhfy33TsKIgJneVPulgsYtDsrsQ/6vWOZoxdLqFWEYzxS7oULjzW16pRsNQekYqJyrbPNe0+0C5Ojhx2xFj2NkLCL1esmgu9J5YH06AOvT0UlQtTADqhchIGSChQcmE6rFYFmmh6VqSeaNFpOorkwKmlo2K5zTEiC+KSZb65MMKiesf0yXX2JJ9ZqwLPu62A0V+sihnPlsc164z9di2xlMAL24P6tXX7ZyuwDB9OxFj2PkgoNZiWyXu0pq/4gybWgcVKUugeo0hIRAEcJiAKYFLHpgqhdn/Hrt0UdHsaSw
5j8tOFU1OwK0XgiId7LJ1nomgWoW1j+mBvf6UwYrhv8rdkMN5qPFM4+NYZvzwqhzXGxboXG/pWzzXtHtAuTV/btniB7HyAV5OXflUrFqv7DnNUFRUJm0CKrnL4XqBcv0JlgMwCwSACMoiiEgULN4RRhLCgGZKs9WOUTxGb7cEzO/SU4Cjfs8LF5h/WNacI+NZzJFNGJW7GYa+TrbvEl864v4tk3GYtYxtnmv6HYBUpD3+DjR4xgZi1i72S53ldQ+4fMxioAufgFUpSzBSIKQkAmW+SKwCKJLFYJTmbbkQZaUTKacEvyB2jEatHOTOmWNSzyUTQ3hysb4jWZJ9YrwKb9Z7GYKTcOu2eZNomJFh/WQ/Ozfss17RbcLkOLitH5FNFhT7FgC4/X60ysHsnr1/vaEytxDJ6t9I6A8KgWjSDpUJiMkBEorWNDC6IKgUL0Fi2bNK4EqJ4fUqx1jQeuW2ClrnGJBOVn+O5ZMrwlv2odiN7OFD2UtY5u3EO77e9HtmQkgKn6wzc2u2wUICf9XreixWrvybmvJIqm9w/+mDU0A3bzFekgMJlgMwLQBCwKiH5VOI3iVk4J/VDvGgNY1AT3PJGtwW/WsKCibJO+RgX6miibedDQcmzL5kQNPTGa7tBAWod4T20fogvzs9Wxzs+s2A5IjeiwR43X7PZ7DXbUIh9or/LImMA50cQuhMgEhIVASyW3AkrK0CZjKxEXf8S1ZinEBkzCTg9oBAZmDgJhoDVo1IxKUE4MOsvPpFRUd3ushdgOFbq+YVJCbs0lsH6ExMxSzzc2u2wnIK3lZ0zBtk8ey0YOnKC/nncLDWRNYEne0VJ5hr2j8okEXswAq4xZBZTzCQTYBFt28hZw2OWOUrGxCUHDZpBDQzMYillO8wAhCO9Y4xoNqWjgo7QLXsvPpFeENekTs5gmNN/JNtnkr0ahdsX2Exv3/Sh1pbBez6nYCQsL/9ajo8dpzXs6/C/Kyn6KZhyyZO1Jqj/Bt
au9IqIhMBV0sRhEyRRMeFgZMC1jQDBbdPPw+drGnrNROnlE2MQSLS7GgcYjvhONANSUMAQkOZefTKyrMzf5M9KYJnZfV5qhiGiSHmeI70f2Y6UlZmJ/lxnYxq243IK+/+OworKz/TvSYHTr7DzT2qtjM8827KrVbaBytsFMRlgK0So8uhrkjWNC6BPxb9PxULGIFPVo2AQGZGYNRJM5kE1Blk8KgZIxP2xNMeliHt27tj8Wn/xO/WXpT5qZmYLaLqHCbjgcI5mbvYJubVbcbEFJhbpZvR9e1PeO+dVgvjGbJ3TFSuwc7qOeGQ7k8CXSRC0AXhXCQW8CS1hIWVhTTITTlEanbZYqxQYeVCIhqRjRCEmuyVTNiQDlB/muJTG7+d08zYf1BLnaDhMbM/yfctN3iUdGh7B1i+wqN6fTK6+LuBEBIRw7lhOP//IPosU0wPZgQkk9pdiJL8rarxFE+ROUWxpUHzgNd+HzQRSAkZGNYDMAgLHx0QVjoszw8+TkEJPA9pR0CYo+AYKY31arpUaAcJ/8DO5deERZ79ordHKHxRp1km7cpymxi+wqNT8XvCwrWtZ4X0MO6UwAhHTm42x2v3zeixzfdvxTkZRUWHXqmxSiG2yWVS+g/tf7xWMzCekgYTQYkM1AMsEQiGMawoCtCk4/LFGOCLinHIyDTEJDpmPlNtGoqAjJW3jRatjeEmVYpckNaGCuP29nmbapg3TpLAkBsf6FpTSe2i9l0JwFCojkxeLy3KCK0OodOuDAv5190L8w1McpUlbmENGq8Y6EiJAUzPJlAaQMWHhgGSxT+HJZ8TqYYHVipHIeATEVACBITrZqCgIyRf87Ow+yiSU9FJsx+O3LQtCUkMQN8Lra/kZ9km5tNdxogBuHxYvAa3RQ9l06YHmqFB564ba/fK3MOVag9Y6A8KBnKg1OwPoKQtAkLWgBLRUiSSqawCfpaORYBmRwF6imY+U20alIkKEfL32fnYXbRoDmxGyA0
PrX+YWqLCo0xEktDaMwgV9jmZtOdCgiJX4wvP/sJzORdrsCT+Y7dg3siWbK9KpVT2Gn13GjQBiIgBEkQQkKgCGHhgUllwCAkDJjy4KSrMsWowD9hJOAzfAsjMO16YgSU2Qa3Gu9kLtH0TbGLLzRmaJMr1qZU+PGYP3U0bbW7upMBMejIgacmY3Hpo+4UuxCSnwvzshayJHtNZY6hb6rdERD/JCgnSAw2wGIAhmAxii7lgYn/T6YYGfg9RgJQ2UWAagKCYaLL7MJBaRucz87D7MKbUyN24Vs4L+tRtnmHys19cCBGnB9F0xHY3M2XdwMgBlGxC8/lRqtzM9VYRMYKfBRLrldUNjvkVbVbFGj9EqGcIAkgIxzkFrCQGSwMGI1/wncyxYjAn5U2cigbj4AQJCa6bBwCMjJ4HzsPs+ro4Wdo9luHK5Ngpdqf7WKSEDqFWDotnJfzPNvcLLqbACHxfVF5OY93udiVl/1d4XO9N1SlbFZIntoFAfGZB+W+CAmBYoBFCEyr6JIMGt/4H2SK4YGcclQwlI0NB9U4zPwmmrZXjgjslYlSFJpFL7bQeTn/Ls7K6tS0XyxfPyOalsCYESrZ5mbR3QaIQWzJoJNdKXbhPh+yZMyuslny/eo5kaD1QkC80T4IB7kFLOTWsGi843+SKayDQDEiGCvcYVA2BjO+iabtFSOCcth5mFWYkTtc3AwveqffpsuKDKLpGYzp/vris4Lplz2suxUQgwoP5kTjNWp3KrOxCaojhzvxwppuqGym/Bm1MwLimYCex4PSBIs3gmGAxQCMABaNZ9wvMsWwQFAMD8L6BAJCkIg6vJVpe4SrVwDBesBXYhdaaLzonT6Xw4ezhmHaHY5mpTWf2C49rrsdEFKBfnX9lynji567mHOzX2W7m1Vl9giIIwIyNx60HgSJwQgKA0Yclnmg8YhFQIYG3CJIlDYhUEaQmGilTSjgvt1errMjvbova4opF/7IwZxwtkunhMUEnVh6
QtOaWmzzHte9AIhBhblZy6kiLnr+xs7L+V+2m1lVNl2+X+UQARr3ONAgJGTtXASkXVjwZ6yzaNxjfpQphgT8pBiKgIxEQDDTm2rlqBAoHRJ4gJ2H2WTKcp7UhNjV5tiCvOx8sTSFRkAb2eY9rnsJEBIWh01en7g3KutlU0PyaUESjWssv0QVb4LF3QgWY2AQFo1bzPcyxeCAfyqGUDFLjpBg5jfRyhEhgPu+zM7DbMLM+bbYxW3hvGw127zTKszNSRFNU2CKYOaaJHSvAYLqU5iXUy/6P7Ry196A1RmVTZW/ppqFgMxBQFwQDLIrsxEsPDACWNQu0X+VKQYG/EExCAGxRkAw0ytHUObv2LR96eDA19l5mEt9aM6B+MUVOC+ry02xh595zNaUIlxh/t7lbJce1T0IiEmdumQsui5gu5hNZVPkb6lmRYLGGQEhz0EgDO4AFrVT5G8REP8bCAnWJ4JBaY2AmGjFMARkQMBpdh5mUWHebiexC2vswkNZCWyXLgmfeFfF0m3hvGyzPAzuRUAw4z8u+j8YuTA/O53tYjYpJ8vPqWYiII4xaATEiYHCwyIAxhgWtMoxokFW2t+/XDEAARmM9RDM9KZaMSQYFP39lew8zKKC3KxtYhdWaHz6/0ovgWG7dEl4Q18TS7uFzVSpvBcB4Ve4F/sfjEzFW7aL2VQ2Ua5SzYgAtUM0v+4CLU5CboLFAIwRLGqXWFDPDlfKFFb+FxT9EZCBCAhmeuUQBMAEKwbzgNxg52EWYfHqlNiFFRqfQjVs8y4LM98SsbSFpmJYRzMVu6J7M4KY9koHmsnIdjGblBODv1LZIyCzEJDZzAZYGDA8LEbRRe0cA2Uzws/ISq0C3kJjZkcPwkw/mDK/CR4YBLjf9+w8ely0sBmNzhW7sELjzXiR7dJlmbJSPNnUd/51RvcaIPgQsTFlGEphfs6t7r6PxRSVTQj+QTUdAZkZpTeB0goWcktY6GfV9LBjsiv9/HJLLQOA
90AEZBBm/g4dDKX9A+GKpT93UeZhzc6lR1VwYI+P2IU19pFDOWlsl24J6xi/EUtfaISox4dI3EuA0JplBbnZH4iev5HxWtay3cwm3QgP67IJck41LRLU9gjHDIE7ggV/V00L2SdT9PXbUWrhD6UWCEj/IFAMQEhMcKlVIO7jD5dlvt1++aWY6H3dYhdWaHrqH++hFf8KTFgtHp96f+vpGXJ3AiBFhx4bQ6Ob2a9d0mt5WZ54P74UPXcR47ZZbFezSTU6eI7STg6qqRGY2aP4aeI8KGKwGIBhsFDFvmxS8DbZlb4+i0r7ISBkjCIKjAwKAqVdIxwUcXCfK/38k9j59KjwAl4Su7BG7rE6UGFu9mqR9Fv74F4PtkuP6HYDghXl3fSgwWPcoiItFo++wt+1tLwS/l5cRK9zy885VJSb/QzWGXbj3x8uzM96oCAvayd+/zRu+zZ+T/uYPswkL/un3ugkvDI6KEk5Xg5lUxCQqZF6YzThTbAYgBGBhYplCrvgVJlC5u1b2hfhIPdDQCwRkA5cSiagcJ8rMr+d7Hx6TMezsgbgjepwYWW6eWyXbqvwYNZM0WMYGTPGI2yXHtHtBqQoP/ui6PHM60Ps8GZV2ejgHbTeQtnkcFBNRkh4UERgwejSBAsDRjU1HBTjAj1kl2Q+Y6708eNK+2CGJ2NRS2GBILRjvjhGQNH2Mr8ey6QGFeZlhYlc1FbGJ1mPdt6Z1imZ3aPvgr/9gOT0LiB52V/T+gLs8GaVwjawiF+QZCICMgnBIBMoHcCimk7Fq1BONcF3JJ/QZZnfvxASuIIZ/gpGER6Adkzb8NvSPjK/Lg/zaEsUzkUvrsAU0nt6MQC+SCFyLKGphYYmDbFduq37CRC8Z/8oyN1rljqrmBQ2QSrluFAom4CAkHlQBLAYgCFYBMWwMgRHaRfyN5YMAtLHt6IEMzv5MhadLlPlG0Fo07QNbcvb9wesbfVoxRUvpEbsAguNGfVbtnmPqehQzhax
Yxm7IHePnO3Sbd0vgODD549UkWeHNbsoTyptgv7Fr9gzPgxUdgiGwQZYhJHFAAuaimSKcXL96w9IX/bxfaOkry/w7oeZngekbZcgIE3bk2V+PdaBVvDcLmu8mB3O0aDKIdulx/Rq7m5XsWMZG4/dY/Ng7nVAKNJTsfTIi7377sfSoYEzlaOCoGws1kEoiownh/FW2QmAaQVLOJRNDAPl2KDmF+h8KfN95EvM6E1GSEos2jZ9L9z+C5l3Bkuq2+JfTC9yoVu759/hwS9s3cG7R8gIcAnbpdsyFZBXzQRIQV7WEbHj9YQxylcWHOz9d8+TFCMClihHIiBjEIyxCMZY/CRQDG4DFiqOKREm5ejA5lewfSnzir7U1wfIX1CGJwAIhDZM33/Bb6v3pT7ePTbs/Xh+1hS8uNepvNqONd0df9WWaDVAPP5fRY5p8HcF+dlP6LfuvqjCimm2//bd/Jyv9pupYsuv7nIoezVCfwzPowY/O1zppS3j/hz6Gp5vHj7ofNghbouuWAe8oBxJU8kRBoKkCZR2YKFPBIbWiSsbFdT8Es/PZC6jL/b15i728wHyJQtfvS39Whv/3rQd88W+Xt0eD3U/CyOXBb0HsC3T92xTs4uOdTQva9prB/eE8zMEc7N3FFAfSF724aK8nEIsLh1HAN5gQL2IzqHXTRTk5oS++KL55u53VgrrQB2/1oJNCChtMSKQCRYDME2woIWwoJWjg2+phrEWLIMQkP/3eT/vpsx/kUBAIFpZAEiT+3r/ekFmlKAkSbdJNPxJMTTgFx6QUQgIQUKzYMlisIwRwDImBBSjgr5mSTUL4SgmQJps4dO2hdsxfyrzTGVJSZJ0W6UYGDCPppErhiMgI+V6SAzmYWHACGAxFMWUtrj9iMDWDUAX+no+cAEzepMtyD4ibt7mM4Hx915ZpUKSpI50ZVDAC/w0cmsEhKaSj2AmWEYKYGkCxgAL/oz1FsXwgC0sqWZ9LnP3/NTCC1rYEiOD
sY23Yb7Qz/MblpQkSbdVpQMDbuonACIgNJWcTKB0BAualsAqGeTf+vV7WTKZxfl+nv88j5md3JT5WwAi+Lux+3lyn8g8eq2X9F5QUW7WMqz0PogV4QIaBEg/U18MfYe/7yqg79D0iZXnAfT3I/k5fvwAw/ychwrzsx6i+Re0yqFx5yVWnpfSJ7VSYfqnXspqfuEmppdBQ9Pp52MHn5qIx9hDlXH6pLT4jVCGNISi1fMNgxVpLeRXc7Pi2FcyWtmyIC9nJ50vb7aeGE1JEB7/SF5OPDU+sF97VF9Y+c9CQDgeEJpGTpDQLFgDKG3CIucXUCwdGvg3ejU6S66lPunrcea8hScCIjBC0WTj74z8aV+P3SwpSZ0QZsTNwuHm9IIfyoDs1yYV5+YORDBaLfdKMx2P5LfsbyDg6JN/IQ5mfDzGVv4LFGVsQ8tYYV7Ws4ZXRvCZPy/nWfqZZEhDKHqHPL0+mv1K49c2vpqfxb+nUg/z4+P4LwSiv1FLF/uV/l+zDXUvtfJ/hJ9CPggBGRwEyiHMHcGCplVGEZCPWFKtdc7CY+s5zOhNtjTYq/ln4fcCf8LbQ2ru7YJaAVKwzpKe6OzXJtErGxCGVgt0dwxI1hqaX2NYO8wACC2pKsy4JH5bjEr0cxuAPMZ+5EX9UQjZBvoZYWnxnVDUd0RR69W8PQGvdXGRP1OksPLXNE0hH4RgECQGt4KlGRiqr1DFvnSI/0aWVGt9InOZedbCgztr6QHN9mQW/q0N475nZB49Pm/7XpcYIIX52e/iEzmTjMWVTHqZDT7dk2hYPtusSQQI9UvQE9xgTJNviWkCBJ/iBblZfOXTAMirh/bOKMrPatH6SAspvLJ/9yz9zyKAtB7u3weLTHy0K8rLafH6CTxG02LitEINpU3FQ/anHpda5jOl1NL/Fj+FfABW0gciDGQCpT1Y0IqhGD0GB3KK/oFN0VFUZyzm3jyDmV1oURja8Jl+Hr2y4vu9JFFAsBhi6Cik
1z/Q39sDpKMIQj/jE/4JiiIGQPRzYEQAye8cIFTvoB+Mo4vx/vj7ftzfbO8HudI34DF+xDlN5qOJfwOC9EZIWsEiBAZBoToL1l2usaTa1ul+c3PPWM5FMLpoi7m/wUpOu69hltRSphaxqHdbbNCiqYAc2Z81nqKIARCCD7dbTd8ZhMddaXhDrRggxlGCDZXZTD8bf9cKECyKmWvIDKm0r981fiIfQUJTwg2zYw2gtAELv3DiwAC40t9vP0uqbX1sMSfolNVc6I4RsuZxLJI6lCggh1oDcmT//qEUBfS/NctUQEi0PxbZniJA2O/P8F8wCX/HYl5rQPKztr0kWAeAJq3R8fmf8Zj5eDz+C1RvAnJZ5uXHT/xjM2NLaWIfzXwlUISwCIFhsPDFMaq3WAR0vAwRjaP/2NLt9x9buUNXfcrC7T2WnCQTVHRo7zKqY7BfeRXl52RjtNhGi+chQFsxQ/PNvAUH9sTyc8Pzsx7CIhLfzFt44KmpRQdzWrwkE4syLGrQE7753YAURTA9BabHN2UW5e91wN+zCjBi0SfVFfgNUfS74RwMq8fQeVKFm1rZKBLhd4v5jVHUlIv1H77JmD+33KwWbwlG4NLpVQns1x7VFZnvMf0kPgZIP4SCn/3KbJgqbgRLKcLB11ks/b81ueTzkaXbCx9hRu+yLd1+LpY59shqI5IkdSSFbM6Iy338/k0T+HhI+mJxiWa98qAYjCCIwULGItmVfn55LLmO9ZGFs9+HVm6caOY30Set3MzW1i1JklAlMr+dJX3ZDFf6pBmv6CtNbgsW/JmHw59Tyvw7N9vxw/6uN0/2d4Ou+kMr1z8el03miwWSJJlLJTKZxZd9fH7bPMPVj5/xaoCkQ1j4xUf8Om69MlaxlevuDzCjd8uWrmtZcpIkmUVfyLyWCGe38rNhW7gdWGjZKiySKWR+u1hyputD2ZwJxVYuvxT3d4Wu2+VmsUzGD2OQJKmnRZXqL/p6137RzzC7VQ+I
0O3CQkUymd9/r8g8Wg2PMUnv93c9+/4AV+jIxe34fUvXZSw5SZJ6VJdk3smGWa1kHhQLmjJucEtYWgFDxbE+Pm2PvepIecOnJL050OnWewNcoKt+v/+cGzRSmCUpSVKPiKLHxb7e1Rf70SxY/UxYHhSaKs7cLix8fQW3k/m0aB7vlNKweLTXdvJv3hjkdOtdzOxddn+XFr21kiR1Vxf6ei763KJ5RqsBkosW5GZIxGFh9ZU+vte6Peoja/yknc+Ot7/1+mCnW+8MmANdcn/n/y2WTWjqKZYkqTvSyTwsP+3n+dVnFl5A5kFpsgGSdmBhxbFLfb34Ucjd0nMjplk/O2X2v/ZPceCOD3W69fbAOdCW32nHbw1wkuaKSOoRnbfwfOACgmGwAZTPEJDPLH3gc4NbwdJcX7nU1/svKplvzzy0n5s8+/l8Z0/Im+XGHbV2uvXWQGfotAc4/3BCNsOOJSlJUpd0RuZh86mF598/tfQE/GyCpCUsBIreQlh4YAzFsb4+e1iS3dfTNpPGHXb0+s8rXnJ4wdmHKxrlfOtNzPSd9Vv9nd5hSUqS1CWds/AoOo9wkHlIBDaGRQ+MABZDMayf17/Oyeb07MKDL8yc+0KRfyQcCYiCV9wCuYKxLrew8s69MdAJOmHuzf4OZptNJune1jkLN/9PLD1uoYFsAEUUGFFYmPt5tRjB3CPaP3HW+MK5If95PSQBjgXGQOFcObwy0Y07MdiRe32QExgb4RH16wMdb+ZKFXZJnVSxzNHqnMXc+nNWHkA2QNJpWPp5/fOCzNE8ixwWOPgffFM+D94MTYTXA+LgqGc4vDbdizs6zIk7gZnfZA907HhiiiRJAp21dM8+azUXyAZIugLLBQvPFkPwe1Qvjvca9bp31D/ei1gA74Qkw5sB8XDCKxKKZvtzR0bNuXV8kCOHhg490OGX4wNnm/0d2ZLuDZ2ydHE/Y+X+8xkGiNCdgsXC40+nZLPM+3arow6Bj7wfmgrF
kQvhvZAUeDsgAd70joHjznIoGufGHRviyB0b7AgdepDDjTdkY/hVNiRJakslsskDTlm5N5zu7w5neM8FAqVLsFh4tL1iSU8pC0/4rblRv/kwOh1ORi6C4pBUeC8gEd7xjoU3XMPh6FQf7oi1E3d0sAMYfKwNHx3oUMSSlSRJVB9bub10CsEgQIQ2hqVDYCzmNtDQeJasefX6jIDUD4JSuFOxS+HjSAQlZAEUByTBe97x8I57FLwxM4g7YuvKHRnsQIY2PWg2VzTAYRFLVpKkFjpl5ZrycX83Dg0EidCiwCAUorBYzuVO93Pr+pirrugd5/DPTkekw5mYDDgduQQ+lqfBh/7J8IF3ArzvHgPvOIbBiYnefDQpGowgDJ4NbfhfhVZTWy1lI+n+1kf9naZ/2N/tHx8NcIOPDUZQ9LC0BKYjWE5bup1kyfae3p7mM+Ok17wfz0VnwCcxy+Fs5FI4LV8IH/unwofe8+AD91h43zkS3p4exB23deeKhszmCofMBjEXDZpdJ9VHJBn0hsxl8Mn+LjUfDnAF8ke83Xh3GhYr9+8/ljlNZEn3rt6fGbrnrHwRnI/ORC+HTyKXwVn5YjjtPx8+9kqCD93i4aRzNLw/MwzenuDLHRs+ByGZRUYwWrpg8MwPMElpPS1JfRCO908OdIWTDBChhbAYAyMGyykrt+0s3d5XgczD8iPX+LpPIzLgQvQKuBC1Aj6NQFDk6XDWfwGc9kqGU24J8JFzDJycHQnF0+Tw9hgv7sgwR+41BAUNLTx4Ro+9PVbS3akP+s/J/mCgC5BP8kZQugALbys3NS1jxZK+PfpgotzznHfqfz9HOC5Gr4SLUSvh84hMuCBfAuf9F8I5zxQ44zYPTjvHwcezo+HD6eHwwcQgeGuUB1c01IF7dchMEJh7ddCM5SxpSfeZigc4Zbw/cA5XzAARurOwfNTf9cePrJwcWNK3V6dnROd8FrQELiEcl6JW6T8jVsDn8mVwwR+LYJ7z
4ZxbEpx1joczs2PglH0EfDw5FE6ODeDeGuHOFQ6dzb0ydCbwHjLj55cH2Ztt/VZJd6aK+ztHvjdwzs/vD5oDxQMNduFtDIspwHxo5dLq9RG3TVTUOucYX/5FWCZ8Gbmq2REIijwDPvdbDBc8F8CnbsnwiXMCnJsdC2fto+D0lHA4NUEOH432594ergfl5SEzyD+8MtBe6mm/T1Q80NH7vYHO37+HcJAJEqGFsJgSXU4OcCm57UUrY521C57xufv87y+Hr4LLkaubjb9/GbwcLvktgYueafCZawpccEqE87Pi4JPp0XBucgScnRAKp8cGw6lR/tx7w+byRa+Xhtj//cVB0+ey5CXdo3pnsLPLu4Ocvnt3kDOQ3+OtB6VtWNqOLgjH394dcJtarTrSp1OiM770SedKw9dAaYTA+Pvl4BVQ4rcUvvBcBBdd58PnTknw2awEuDA9Fs5PjoJPJoTDuXEhcNY2GM6M9IeTwzy5E4OdvysYNFuC5B7VO5YOTu8McvozQgLkd8kMlGZYmoHpCJbiAXO4k1bOLRbxvuP0+YyEI6WBmaAMXwvKCIEJGvlKuOK3DEo8F8OXrgvgkmMyXJw5Dz6fFgefTY6GC3YR8OnYMDg/OgQ+sQmCc8P94aPBnn99ZridC0te0j2idwbPdHl7kOOf3x7sBOR3WrhrsLw/wDmfJX/nqljmO/DLWUmVZfJVoApfB6oIgfH3suBVoPBbDlc80+GyaxqUOKbCFzMT4dK0eLg4KQY+t4uCz8aGw4XRoXDBRg7nrQPg5RHT//K0zTgpktwjemOgg89bgx2/e2uII7zFABG6I1iagdHDUjzMhfvMwV+h8/CwZIe4s/XpmOgppXMW/lUTshY04etbOmw9qIPXQJkfRhmPpVDqsgiuOMyHyzOSoWRqAnw5KQ6+sIuGS2Mj4KJtGHw+MgQvwFzuOVu7fz5jM7bFm1wl3X06MWh21JuDHb9/E+EwmAeFwdI+MKwo1gwKd3aq
L6eOjv2dLjq6a6sj3i5dtosLVbqm/7c8dD2Uh20w8nrQBiM8vitA5bEMylwWg8JhAZTOSIErUxPh8qR4KLGLgS/HRsEXtuFwyTqYOzRqKrfP1u7H52zGN73rQtLdJYRj+etDZv/8xhAH0LsZks7C8oGtO3clLI7TLUr7UZeY6MMOcXdJMWXeOo3Hck4XugF0oRtbuSJ4HZT7rgKNRwaoXdJB5bAQymakgnJqEpROSoDS8XFwZUw0XB4VAcXWGEVGTwCMJLeetRnf6i1Lku5o9TkxeFbOiSGzudeHOgBvBIQshMUYGDFY3h02h7voE8lVr1rJVa/I5HRpaUvYMe5OKScnHtD5rIKqkE1QFWpk/Ftl8HrQ+a6Gco9M0LosBY3jIlDPWACqqclQNjERlOPjQTEmBi4PD+PybaboIUFjNHl3hzTA8Y4XDUI9PmRW8fGhs+FEkx14dwqWoU7cJy4hXPWatdy1h3dA/ZbNUJWx1HzTZ3tLtLSjdvr8d6t810ANAlETurml8W/VQRugymcNVM5dARUuy6DcYTFoZ6SBZkoqqCcmgXpcApTZxsJJa48mQPSQTKjdb2M3gx1K0h2momHT7Y8NnVV7bNhsIECE7gQs3GmHQE6XuYpr3LMbbj79FFx/bBdUr1h+hB3m7leDzNEKIblY7b+Wh6JWxDXyjUDfV3quBJ1rBlQ4pkP5zIWgnZYKmknJoLabB8pRkS2iiB4Su39hvaTpPXmS7gwdGWI//+jQmf84OmwWHGvh9mDRA8Ng4U7NDuC0SzMRjCfgq2efhq+eexZu5GRB/aaNp0vk8ntrQfQSW/mQcodF6hr5eqgNRygithgZ/xZG0WQdRpNVGE2Wg27OEowmi6B8xnzQTk0B9YR58NEorxaAMEi4fTZ2xx4eNcq8E/IldSha0unI0BmvHBk2k0MDASJ0S1hmtYZlmAN3ek4wV0ERY3czGDwce7OhYeumkm8yM+/NN5cpJsWP0LmmV9Vgpb02
agvUGZn+VhuBxTA5QuKPkHgiJG5LoMIZIZm9ALT2KaAaH8u9YDutFSQMlP/ZbzMuiB1OUi+ryHq6R9GwGVeLrGcC+QgZITHYGBYhMG+McuLOY+W7au06rvHJ3U1gfL1/H/95c28O1G/dolImJt7bD8GScQk2CElNTfhGqI1BMGK2tjD9rTYai1xh66E6cDVU+mSCzmMp1k0WQ7ljGtZNkuHc2ADuOdsJbUHyK9ZNDmfZ2g5hh5RkZhXLZFYF1tP3Flrb/7fQegYYbAClCRgBLEf0gHDvTp7LfRmdxNU/+CB3Iycbvtr3TBMYzXBkIxybtSVJSU3vYL+npZuJkMxdUlUTiZDEIRgiro3dAjURG6BajpV3/xWg81oGFe4IyRysvNsncq+NniUKiMEIym/3jRo/jx1Skpn00pBpga9Z2zcUDJ8BvBEMshAUY1hOjHbiznqFcdqMFdz1xx7lbj61Vw/Fc3ooWsKRg8WqLarqzMz7Aw6DqLhV4bFYXRO5AeoSEIqEba0dj6BEY+U9dC1UBa2ESt8M0HmmQ4VrGlyaEs7tHy0eRQzm6ya2dqf3WY+exg4rqYf0yuBpo18dNv3oq9bTb7063B5eE1gMlqM2DtxHroGcImUR17DjIe5GdhYWo1pGC2M49HWOLSX3fLGqLVHFXee+8POaqPVQl7gV6hO3tTL9vTZuI0aTtRhNEJIAhMQbK+8u8+FNO9d2ATEYIflxn83457JGjhzGDi2pi8rC4tTL1lMfemX49H+ggUyACM1A4Y6Nc+I+nhvMKeYv1kOR9SR89QyrWxhFC6Hpe6qHNGzZfEaVlnZ/r+NMTcA6z0Xv1ESvg7okhCQZwTByXTJCkoCVd9ymOgwr70FYefdbAmVO87i8MS2bfdu1rd1fnrOxe+CwTNafHV6SiaIJSC+OmLbkpeHT/uflEdPB4FcMRlAKbLFO4eDNXQiL47SZK7lrux7GSPEk3GyCQjxaCE11kOtPPA6169Ycveea
crsq6kwsnzt/f3XUaq4uBSFJRTCMXIeuTcLKeyxW3iMQkhCsvPulw1n7IALEdEjQ+20mfPv8qIkbt0qgdCi6Ny9aT53/4ogptS+NmAYGIxzc0QlOXLGLL3chPI5TL1vOXd25g2+Buvk01imMKtummPa59ugjXO3a1dns8JKE0nktWFMVsfLn+lSEZAGCsWC7kRGUlC0YTdZjNFkNVWEIie9CrmiCY+cAsZ0Az9tOhIM2E//34IiJD+2X+k9aqUAmszw8fErGiyOn1heMn8W9McOdO+kZyF2IiOWUi5ZwNVu2cNcf3QVUbBIC0VkoyLQPRRkE7Me61aul14a3p3LPZHllaMZf6lI3Q/3C7dBgZPpb3QKEJGkD1MStgaqITFDMnccdHDPZZEiaAZkEuTaTIX/U5L8fGjn5+XzryVPYady30oYnj6pMWbRLlZT2/9QL0rma1Wu4+s2b4eqOh+D6Y49i3WAP37LUVMHuAhBC83A8lQMN27f9oW5Vhh87DUntSeeRMKkqdGlFbepGqF+MYKQ/0ML0t7qFWC9Jwcp7wlqoilwOpx2CuH1t9I0YWwQQODRqChweOeVXLEqcOTx86rximawfO537QrqY5MDKeYter16c+Z+6NRuhYfN2uPrAQ3Bt58Nw/VEEY/cT0IjRglqWaDzUV/u6BwaZ4LiRtQfqt25SVS5ePJ6diiRTdNM+pn9F0MKCmqS1XH06QrIU4RC4npyO9ZIFCEnSWqgMX8odnTKnm4BMBSxS8GXsV4ZP+/0rw6YdeMl6shs7pXtOKt8oe11kyp7Keek3qheugLrM9VC/dgs0bNwGDVseREB2tAGIvkgllulNMYFBEejao7u42vVrX2hIS7NipySps6qQp6VXx6/4V336VmjIQDgyHhSYQKFosglqkteCOngBlzduaoeQmAIItdDwTZbW9lyhtf31oqH2TxUNmu6Bp3RXL4+q8Y5xLA9KfqwiLFVXGbeYq56fCTXpa6B2+Xqo
W4kRuyNAcroHiKFIdfWhB/5etWblAnZakrqj8sDUaVUxS1V1i/AGLkdIMhGOJiMkGQhJ+maoSV0Dn3tHEyDtQmIA5AAPyCTIaxOQ6XybvqEnmB9PNHTW744NnXXkxOCZC48Osbdlp3jHSuUYNVLlGpOi9Z73ckVg8v9UhKVxlbFLoDppOVTPXwk1i1ZD7ZK1IoA80BKQJwSAUM83RgAxANozQUWtXPWbN5VWLlkymZ2ipJ5QiUxuURm6cHd18qqf65fjDVyJoKxEQJjrVzwAdUu3QHXKavjA2feWGBgGdwcQwwhUGpb9xhCHW28Ocqx/e5Djq+8Mcsx808rR8XbWXejY6skRDqoZUcs1jrEva13jarReib9WBMwHXchC0EWmQ2XsUqhKyEBAMHIQIAs7AGQHArJrFwNkT5cA4aMGRp2rO3f8VLN65aPFaWn3Vf2uV1UVmuJaGb+0qm4pViRXISSrERCD8ff65VugKimTK5rm3GYUaQ+QFzoHCD/bzTBXml9tY+CcH4oHzNF8MGDOEVr+8iMrt6SPLefMKZZNs2b/QrelGuY7sswm2F09NjS1zC5sV9mk8ONl0yK16hkx/6dxjAOt6zzQeiZDuW8qVAQsAF0wwhG2CCrbAySDAbIGAdnAANmOlXQhIE8SIFlNgFDGFwPCYL6uYYgamzZW6FYsmcP+BUnmFPWwVsYs2oU399/1q/BmrsXy8tqHeDeswWiSuQW08encoQnTRSHpLCA0nshUQPRrNOmXw9Qvqqxflv90f3furNXc789Zetz8xNKj7Lyl59lPLTzf+tzS57Uv+vnkX+rn8/yX/Xz3Xe7nv6+0n/8BRb+Aw6VWgUWKgUHvKgcHnVdYy7XKESG/UY4K+bdyTDioxkeAalIUqKfFgHpGLKhnx4PWGcFwTYTyuUlQ7pXSNiDx7QGyuQ1AHu8UIIa6xrWdD/1fzdpVO0GKGr0vTWTa1KqkZWfrMjZwDWvxhq5HSNAN6xGSlVvg
SlQKd2DspNaQmAgIDZ9oG5DZ7QJCy2B+OFC/0vipAe5wesBcONvfA87194Tz/b3gQn9v+Ax90coHLln5wRfoEqsAuGwVCKVWQaDoHwyKQXJQDgkBpXUolI0MgzLbcCgbEwFl4yNBNTEa1FPQ02NBMzMONEJA3EUACWWAxAgBWYGArILadGNAtnYZED5qPPs09ZlwdevXnZbqGneAdHGL46vSVtyoW0k3F4taGxESdN2qzXAhWKTS3iYgUzoEhCb5dAzInGZABrq3BGSAlwAQH/gczQPS3x9K+iMg/QPhCg8IwkGADA2BMiNAVMaAzNADoiFAXASA+LQFyDI9IKlCQNZB3QoBIJuNAHmcAZItDogeDFac2rqlsWrFijh2eyTdCSp2dLSqSly0vXbpqr82rNsKVzdhNNmMkOBT8ZRPMFXamyFpAkTf1EuA5HcIyIw2AaElaUwB5EybgPj2DCBOHQASIQRkuQCQNQyQDeKAPGIMSE7TqFze+57Fvz9JveF/rVm9ettds8Lh/ShdRJp11fwlT9dkrP6BytJXNz8ItZlruXddvZtbtjoA5EUTAaHVNzoPiL6I9akJgBiKWKKATEA4JgsAmWUEiKcBkPkCQBa3DcgyBshqBGQ9AbIdrm4zAPKIOCAExt5sGobyQ936tU/p0tJ6rCFCkplVFZNmW5m2+PnqjJU/1K5aB1XpGdwJR7dfzQ0Iv1ZsDwBSKgaIDQNknAAQrKQ3A5LQGhB/cUCqDICktQakXgjIQ0JAdvOAUFMvwXKNwNi4/vmqlWl3fH+QpDakTU4eVZm2MKdyYfp35cnzuSMzHX/tDCDUm06AUFNv24A4tgMIvTfPNEC+bAMQvpI+ggEy2gBIlMmAVASlgS5EAEgcApKIgKQwQBa3B8hOuIaAXH/8MWjc/TgNR4e6LZv+VrtuzV5deroNu8yS7nbVREYOLk9I2KyJjWsstJ/9634CBN02IDQeqy1AZpkECL3xqBUgrBXLJEAG
mgGQ6GZAqtsDZBMB8qAekB074eoD26Fuw7qbNZkZW0vS0qQFMe5V0QSgK6GhUXljJn2EEeSXZkAmwyEDICPbB+RYNwA5azIgwc2ADDMFELSjEJDkNgBZ0jYgmQTIJgRkC9Rv3Ab1G7ZA3ep1v1RnZp6tSk+Pu+NeaybJvHp65AS73JETH8u1mdSIgHDU1NtZQGgNWQKEX1i5i4Bc7GlA3MQAWdgmIDUEyNJ1UJu5nh/RW5OxmqtevPyryoVLdpcnJd2ZrzOT1LvKHTnB+9CIKfmHR0z5HQFCTb0015oHZLh5AKGm3iZArPw6D8gkEUDmMEA8EBBvI0DChYBk8IBUL1gJ1WnoBZlQlbrkD1WJi16ojE6WJi5JEhcVwQ6PmOT/8vCpz788fFojAsIZAKHOQh4QdNuAOMP7HQDySTuAlBgDMtgACHqUABA7BshU/VATvjddBJByASA6AiRqCVbU0bH0uZjTRaXd1EWk5GrCkoKkIpSkTqtgmP3014ZN34yfp4qsZ/xdFBB0R4Cc4gGh3vT2AKGmXhMAGWsEiL0QkHktAfFDQAIWICTkVNAGJv8TfUYbOG+r1it2Jvs3JUnqvmg4+ZFB9u5Hh87admLIrHdPDJn9mzeGOHDtAUJNvT0PCNoIEDUB4kDjsageQmOy5oHGLYHTuMX/VuMe/75mbswDSvcIT5o2wP4dSZLMr7dlM23eHuwQgcWsHe8MdDr+3gBnDQLydwSE4wEZQIDoByyeQUhaAWJlBIgVA8QKARmAHoyQDAuBsuEECNZD+N50NN+bjp6EnhzFqaZG/qNseqRWbR91QjMjaqfSPjKq1D5Q6sCTdGeKwPnQwsnrQ6s5Cz62cn3gVH+3/Wes3E+ctfQ4d87KQ/WJpefVTy29v71g6f3dRUufHy5Z+v78haXvL19a+P1yxTLgp1KLwB9KrQK/w0jyrWJQ8FXFELlKaS0/pxwhP1E2Ur5fYSN/oMxWvqBsbLCXbpxc6rAzi2Sy
/w8gmj/GeiV2tQAAAABJRU5ErkJggg==</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>5b59bfed-4e14-4764-ad3e-20e20a69e9a1</rd:ReportID>
</Report>
﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia
{
    public class ReciboTransferencia
    {
        public byte[] GetReport(ReciboTransferenciaDataType reciboTransferencia)
        {
            try
            {
                var parametrizes = Array.Empty<Tuple<string, string, bool>>();

                var dataSources = new List<Tuple<object, string>>
                {
                    new (new List<ReciboTransferenciaDataType> {reciboTransferencia}, "ReciboTransferenciaDts"), 
                };

                var caminhoRelatorio = reciboTransferencia.Descricao == "Transferência para Conta Bancária"
                    ? "ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia.ReciboTransferenciaBancaria.rdlc"
                    : "ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia.ReciboTransferencia.rdlc";
                
                var bytes = 
                    new Base.Reports().GetReport(dataSources, parametrizes, true, caminhoRelatorio, ConstantesUtils.FormatoPdfMinusculo);
        
                return bytes;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
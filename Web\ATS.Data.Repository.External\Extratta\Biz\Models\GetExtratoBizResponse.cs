﻿using System;
using System.Collections.Generic;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class GetExtratoBizResponse
    {
        public List<GetExtratoBizItemResponse> Itens { get; set; }
        public int PaginaAtual { get; set; }
        public int TotalItensPorPagina { get; set; }
        public int TotalPaginas { get; set; }
        public int TotalItens { get; set; }
    }

    public class GetExtratoBizGridResponse
    {
        public List<GetExtratoBizItemGridResponse> Itens { get; set; }
        public int PaginaAtual { get; set; }
        public int TotalItensPorPagina { get; set; }
        public int TotalPaginas { get; set; }
        public int TotalItens { get; set; }
    }

    public class GetExtratoBizItemResponse
    {
        public string CodigoConta { get; set; }
        public int NumeroConta { get; set; }
        public int CodigoFilial { get; set; }
        public int NumeroCorrelativo { get; set; }
        public string NumeroCartao { get; set; }
        public string DocumentoPortador { get; set; }
        public int TipoDocumentoPortador { get; set; }
        public decimal ValorTransacaoDolar { get; set; }
        public int CodigoMoedaDolar { get; set; }
        public string DescricaoMoedaDolar { get; set; }
        public string SimboloMoedaDolar { get; set; }
        public decimal CotacaoDolarnoDia { get; set; }
        public string NomeEmbossadoCartao { get; set; }
        public string Id { get; set; }
        public int CodigoEmissor { get; set; }
        public decimal ValorLocal { get; set; }
        public int CodigoMoedaLocal { get; set; }
        public string DescricaoMoedaLocal { get; set; }
        public string SimboloMoedaLocal { get; set; }
        public int Mcc { get; set; }
        public string DescricaoMcc { get; set; }
        public string DescricaoGrupoMcc { get; set; }
        public int IdGrupoMcc { get; set; }
        public string NomeEstabelecimento { get; set; }
        public string DescricaoMti { get; set; }
        public int Mti { get; set; }
        public decimal ValorOrigem { get; set; }
        public int MoedaOrigem { get; set; }
        public string DescricaoMoedaOrigem { get; set; }
        public string SimboloMoedaOrigem { get; set; }
        public int NumeroParcelas { get; set; }
        public int CodigoProduto { get; set; }
        public string CodigoRespostaTransacao { get; set; }
        public string DescricaoCodigoRespostaTransacao { get; set; }
        public string CodigoRespostaEmissor { get; set; }
        public string DescricaoCodigoRespostaEmissor { get; set; }
        public string DescricaoPlanoVendas { get; set; }
        public int PlanoVendas { get; set; }
        public int DataTransacao { get; set; }
        public DateTime DataHoraTransacao { get; set; }
        public int HoraTransacao { get; set; }
        public int IdTransacao { get; set; }
        public int CodigoStatusTransacao { get; set; }
        public string DescricaoCodigoStatusTransacao { get; set; }
        public int EntryMode { get; set; }
        public string TerminalId { get; set; }
        public string RetrievalReferenceNumber { get; set; }
        public int CodigoAdquirente { get; set; }
        public string CodigoLoja { get; set; }
        public int Nsu { get; set; }
        public string CodigoAutorizacaoEmissor { get; set; }
        public string CodigoAutorizacaoTransacao { get; set; }
        public int CodigoProcessamentoTransacao { get; set; }
    }

    public class GetExtratoBizItemGridResponse
    {
        public string DataHoraTransacao { get; set; }
        public string ValorTransacao { get; set;}
        public string DescricaoPlanoVendas { get; set; }
        public string NumeroCartao { get; set; }
        public string DocumentoPortador { get; set; }
        public int NumeroConta { get; set; }
        public int Mcc { get; set; }
        public string DescricaoMcc { get; set; }
        public string DescricaoGrupoMcc { get; set; }
        public string ValorTransacaoDolar { get; set; }
        public int CodigoMoedaDolar { get; set; }
        public string CotacaoDolarnoDia { get; set; }
        public string ValorLocal { get; set; }
        public int CodigoMoedaLocal { get; set; }
        public string TerminalId { get; set; }
    }
}
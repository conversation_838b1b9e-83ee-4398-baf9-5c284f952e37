﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Response.Notificacao;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.WS.Services
{
    public class SrvNotificacao : SrvBase
    {
        private readonly INotificacaoApp _notificacaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly ITipoNotificacaoApp _tipoNotificacaoApp;

        public SrvNotificacao(INotificacaoApp notificacaoApp, IEmpresaApp empresaApp, ITipoNotificacaoApp tipoNotificacaoApp)
        {
            _notificacaoApp = notificacaoApp;
            _empresaApp = empresaApp;
            _tipoNotificacaoApp = tipoNotificacaoApp;
        }

        public List<Notificacao> Consultar(int idUsuario)
        {
            IQueryable<Notificacao> notificacoes;

            notificacoes = _notificacaoApp.GetNotificacoesPeloUsuario(idUsuario);

            return notificacoes.ToList<Notificacao>();
        }
        public Notificacao AdicionarNotificacao(NotificacaoRequestModel notificacaoParam)
        {

            /*Mapper.CreateMap<NotificacaoRequestModel, Notificacao>()
                .ForMember(c => c.usuario, option => option.Ignore());

            var notificacao = Mapper.Map<NotificacaoRequestModel, Notificacao>(notificacaoParam);

            notificacao = _notificacaoApp.Add(notificacao);*/

            return new Notificacao();

        }

        public List<ConsultarMobileResponse> ConsultarPorUsuario(int idUsuario, int? idTipoNotificacao, DateTime dataBase)
        {
            try
            {
                var notificacoes = _notificacaoApp.GetNotificacoesPeloUsuario(idUsuario)
                    .Where(x => x.DataHoraEnvio >= dataBase && x.Tipo == 4);

                if (idTipoNotificacao.HasValue)
                    notificacoes = notificacoes.Where(x => x.IdTipoNotificacao == idTipoNotificacao);

                foreach (var notificacao in notificacoes)
                {
                    notificacao.TipoNotificacao.Notificacao = null;
                    notificacao.usuario = null;
                }

                return Mapper.Map<List<Notificacao>, List<ConsultarMobileResponse>>(notificacoes.ToList());
            }
            catch (Exception e)
            {
                throw e;
            }

        }

        public Retorno<List<TipoNotificacaoResponse>> ConsultarTipoNotificacao(DateTime? dataBase, string cnpjEmpresa)
        {
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);
                if (!idEmpresa.HasValue)
                    throw new Exception("Empresa não identificada.");

                var retorno = _tipoNotificacaoApp.GetPorDataBase(dataBase, idEmpresa.Value).ToList();

                return new Retorno<List<TipoNotificacaoResponse>>(true, Mapper.Map<List<TipoNotificacao>, List<TipoNotificacaoResponse>>(retorno));
            }
            catch (Exception e)
            {
                return new Retorno<List<TipoNotificacaoResponse>>(false, e.Message, null);
            }
        }
    }
}
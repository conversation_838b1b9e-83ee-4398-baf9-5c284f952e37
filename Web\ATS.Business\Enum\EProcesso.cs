﻿using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Domain.Enum
{
    public enum EProcesso
    {
        /// <summary>
        /// Para métodos que inserem/criar um novo registro
        /// </summary>
        [EnumMember, Description("Create")]
        Create = 1,

        /// <summary>
        /// Para métodos que atualizam o registro
        /// </summary>
        [EnumMember, Description("Update")]
        Update = 2,

        /// <summary>
        /// Para métodos que deletam o registro (desativar)
        /// </summary>
        [EnumMember, Description("Delete")]
        Delete = 3,

        /// <summary>
        /// Para métodos que retornam um único objeto e de forma completa.
        /// <para>Maioria das vezes indicado para o processo de editar o registro</para>
        /// </summary>
        [EnumMember, Description("Get")]
        Get = 4,

        /// <summary>
        /// Para métodos que retornam um ou mais de um objeto.
        /// <para><PERSON><PERSON> das vezes indicado para a consulta de registros por busca genéricas</para>
        /// </summary>
        [EnumMember, Description("List")]
        List = 4,
    }
}
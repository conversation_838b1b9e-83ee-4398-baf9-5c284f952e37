﻿using System;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class ConsultarRemessaResponseItemDTO
    {
        public int Lote { get; set; }
        public string DocumentoOrigem { get; set; }
        public string NomeOrigem { get; set; }
        public string DocumentoDestino { get; set; }
        public string NomeDestino { get; set; }
        public string DataCadastro { get; set; }
        public string DataAlteracao { get; set; }
        public string DataBaixa { get; set; }
        public string RemessaOriginalId { get; set; }
        public ConsultarRemessaResponseStatus Status { get; set; }
        public string DocumentoOrigemFormatado { get; set; }
        public string DocumentoDestinoFormatado { get; set; }
        public string StatusBaixa { get; set; }
        public string NomeUsuarioBaixa { get; set; }
        public string NomeUsuarioCadastro { get; set; }
    }
}

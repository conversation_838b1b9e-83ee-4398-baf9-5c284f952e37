﻿using System;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using FluentValidation;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Mobile.Request
{
    public class DespesaCriarAplicativoModel : RequestBase
    {
        public int IdUsuario { get; set; }
        public DespesaCriarAplicativoItemModel Despesa { get; set; }
        public DespesaCriarLatLongAplicativoModel Localizacao { get; set; }
    }

    public class DespesaCriarAplicativoItemModel
    {
        public int IdCategoria { get; set; }
        public string Descricao { get; set; }
        public string LinkNota { get; set; }
        public string FotoNotaBase64 { get; set; }
        public string HashId { get; set; }
    }

    public class DespesaCriarLatLongAplicativoModel
    {
        public string Latitude { get; set; }
        public string Longitude { get; set; }
    }
}
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class BloqueioFinanceiroTipoService : ServiceBase, IBloqueioFinanceiroTipoService
    {
        private readonly IBloqueioFinanceiroTipoRepository _bloqueioFinanceiroTipoRepository;

        public BloqueioFinanceiroTipoService(IBloqueioFinanceiroTipoRepository bloqueioFinanceiroTipoRepository)
        {
            _bloqueioFinanceiroTipoRepository = bloqueioFinanceiroTipoRepository;
        }

        public IQueryable<BloqueioFinanceiroTipo> GetAll()
        {
            return _bloqueioFinanceiroTipoRepository.GetAll();
        }
    }
}
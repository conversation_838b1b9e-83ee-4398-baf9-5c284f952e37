﻿using ATS.MongoDB.Context.Entities;
using ATS.Domain.FileUploader;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class FileUploaderAtsController : DefaultController
    {
        public readonly SrvDataMediaServer AppLayer;
        private readonly IDataMediaServerApp _dataMediaServerApp;

        public FileUploaderAtsController(SrvDataMediaServer appLayer, IDataMediaServerApp dataMediaServerApp)
        {
            AppLayer = appLayer;
            _dataMediaServerApp = dataMediaServerApp;
        }


        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Upload(string base64, string fileName)
        {
            try
            {
                var arquivo = new Base64File(base64);

                var upload = AppLayer.Add((int)arquivo.MimeType.Tipo, arquivo.Content, fileName, arquivo.MimeType.Descricao);

                return ResponderSucesso(new { token = upload.ToString() });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult RemoveFile(string token)
        {
            try
            {
                AppLayer.DeleteByToken(token);

                return ResponderSucesso("Arquivo de mídia removida com sucesso");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(string token)
        {
            try
            {
                if (token == null) throw new Exception("É necessário informar o token");

                if (token.Length <= 0) throw new Exception("O token informado é inválido");

                Media retorno = AppLayer.GetMedia(token);

                if (retorno == null) throw new Exception($"Nenhum dado de mídia foi encontrado para o token {token}");

                var data = string.Empty;
                if (retorno.Data.Contains(","))
                {
                    var splitted = retorno.Data.Split(',');
                    data = splitted[1];
                }


                return ResponderSucesso(new
                {
                    Token = retorno._id.ToString(),
                    Data = data,
                    retorno.Type,
                    BlobType = Domain.Helpers.EnumHelpers.GetDescription(retorno.Type),
                    retorno.FileName
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Visualizar(string token)
        {
            try
            {
                return ResponderSucesso(string.Empty, _dataMediaServerApp.VisualizarMedia(token));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
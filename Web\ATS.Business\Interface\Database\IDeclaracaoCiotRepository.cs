﻿using ATS.Domain.DTO.Ciot;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IDeclaracaoCiotRepository : IRepository<DeclaracaoCiot>
    {
        DeclaracaoCiot ConsultarCiot(int viagemid);
        int? GetIdContratoAgregadoPorCiot(int idEmpresa, string ciot);
        DadosAtualizacaoCiotDto GetDeclaracaoCiotIncludeViagens(int idDeclaracaoCiot);
        DeclaracaoCiot GetByCiotDesvinculo(int idViagemDesnvinculada);
        string GetCiot(int idviagem, string separadorVerificador = "/");
        DeclaracaoCiot GetDeclaracaoCiot(int idviagem);
    }
}
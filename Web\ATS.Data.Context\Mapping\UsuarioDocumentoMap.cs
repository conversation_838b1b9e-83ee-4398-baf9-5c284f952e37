﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioDocumentoMap : EntityTypeConfiguration<UsuarioDocumento>
    {
        public UsuarioDocumentoMap()
        {
            ToTable("USUARIO_DOCUMENTO");

            HasKey(t => new { t.IdUsuario, t.IdTipoDocumento });

            Property(t => t.IdUsuarioDocumento).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdTipoDocumento).HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdUsuario).HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.Validade)
                .IsOptional();

            HasRequired(t => t.TipoDocumento)
                .WithMany(t => t.Documentos)
                .HasForeignKey(t => t.IdTipoDocumento);

            HasRequired(t => t.Usuario)
                .WithMany(t => t.Documentos)
                .HasForeignKey(t => t.IdUsuario);

            Property(t => t.AvisoValidade)
                .IsOptional();
        }
    }
}
using System;
using System.Collections.Generic;
using System.Transactions;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class ResgateCartaoAtendimentoApp : BaseApp<IResgateCartaoAtendimentoService>, IResgateCartaoAtendimentoApp
    {
        
        public ResgateCartaoAtendimentoApp(IResgateCartaoAtendimentoService service) : base(service)
        {
        }

        public ValidationResult Add(ResgateCartaoAtendimento resgatarCartao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                   
                    ValidationResult validationResult = Service.Add(resgatarCartao);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(ResgateCartaoAtendimento resgatarCartao)
        {
            try
            {
                using (TransactionScope transaction
                    = new TransactionScope(TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                  ValidationResult validationResult = Service.Update(resgatarCartao);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                }

            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);

            }
            return new ValidationResult();
        }

        public object ConsultarGrid(ConsultarResgateValorDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarGrid(request, take, page, order, filters);
        }

        public ValidationResult Alterar(ResgateCartaoAtendimento resgatar, string motivoEstorno, int usuario)
        {
            try
            {
                return Service.Alterar(resgatar, motivoEstorno, usuario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object GetResgate(int idResgate)
        {
            return Service.GetResgate(idResgate);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class PrestacaoContas 
    {
        public int Id { get; set; }
        
        public int IdEmpresa { get; set; }
        
        public int IdUsuarioPrestacao { get; set; }
        
        public int? IdUsuarioCadastro { get; set; }
        
        public int? IdUsuarioAtualizacao { get; set; }
        
        public string Observacao { get; set; }
        
        public decimal Valor { get; set; }
        
        public EStatusPrestacaoContas Status { get; set; }
        
        public DateTime DataCadastro { get; set; }
        
        public DateTime? DataConclusao { get; set; }
        
        #region Propriedades de Navegacao
        
        public virtual Empresa Empresa { get; set; }
        
        public virtual Usuario UsuarioPrestacao { get; set; }
        
        public virtual Usuario UsuarioCadastro { get; set; }
        
        public virtual Usuario UsuarioAtualizacao { get; set; }
        
        public virtual ICollection<DespesaUsuario> DespesasUsuario { get; set; }
        
        #endregion
    }
}

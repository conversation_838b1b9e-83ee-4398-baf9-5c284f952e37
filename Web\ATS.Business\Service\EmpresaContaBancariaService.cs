﻿using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class EmpresaContaBancariaService : ServiceBase, IEmpresaContaBancariaService
    {
        private IEmpresaContaBancariaRepository _empresaContaBancariaRepository;

        public EmpresaContaBancariaService(IEmpresaContaBancariaRepository empresaContaBancariaRepository)
        {
            _empresaContaBancariaRepository = empresaContaBancariaRepository;
        }

        public IQueryable<EmpresaContaBancaria> GetAll()
        {
            return _empresaContaBancariaRepository.GetAll();
        }

        public EmpresaContaBancaria Inserir(EmpresaContaBancaria contaBancaria)
        {
            return _empresaContaBancariaRepository.Add(contaBancaria);
        }

        public void Update(EmpresaContaBancaria contaBancaria)
        {
             _empresaContaBancariaRepository.Update(contaBancaria);
        }

        public EmpresaContaBancaria GetByEmpresaId(int idEmpresa)
        {
            return _empresaContaBancariaRepository.GetAll().FirstOrDefault(x => x.IdEmpresa == idEmpresa);
        }
    }
}
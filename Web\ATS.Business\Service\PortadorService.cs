using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.DTO;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class PortadorService : IPortadorService
    {
        private readonly IPortadorDapper _portadorDapper;

        public PortadorService(IPortadorDapper portadorDapper)
        {
            _portadorDapper = portadorDapper;
        }

        public object ConsultarGridPortadores(int? idEmpresa, int take, int page, List<QueryFilters> filters)
        {
            var documento = string.Empty;
            var nome = string.Empty;
            
            var filtroDocumento = filters?.FirstOrDefault(o => o.Campo == "Documento");
            var filtroNome = filters?.FirstOrDefault(o => o.Campo == "Nome");

            if (filtroDocumento != null)
                documento = filtroDocumento.Valor;

            if (filtroNome != null)
                nome = filtroNome.Valor;
            
            var portadores = _portadorDapper
                .ConsultarPortadoresPaginado(idEmpresa, documento, nome, take, page);
            
            return new
            {
                totalItems = portadores.TotalItens,
                items = portadores.Itens.Select(o => new
                    {
                        Documento = o.Documento?.ToCpfOrCnpj(), 
                        o.Nome
                    })
            };
        }
    }
}
﻿using System.Collections.Generic;

namespace ATS.Domain.DTO.Veiculo
{
    public class VeiculoGridResponse
    {
        public ParametrosGridVeiculo Parametros { get; set; }
        public List<VeiculoItemGridResponse> items { get; set; }
    
        public int totalItems { get; set; }
    }
    
    public class VeiculoItemGridResponse
    {
        public int IdVeiculo { get; set; }
        public string Placa { get; set; }
        public string Marca { get; set; }
        public string Modelo { get; set; }
        public bool ComTracao { get; set; }
        public int QtdEixos { get; set; }
        public string RazaoSocial { get; set; }
        public string RazaoSocialFilial { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public bool Ativo { get; set; }
        public long? NumeroFrota { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string RNTRC { get; set; }
        public string CiotAgregado { get; set; }
        public string TipoVeiculo { get; set; }
        public string SerialNumber { get; set; }
        public string Status { get; set; }
    }
    public class ParametrosGridVeiculo
    {
        public bool UtilizaExtrattaTag { get; set; }
    }
}
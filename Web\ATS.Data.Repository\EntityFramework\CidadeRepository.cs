﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class CidadeRepository : Repository<Cidade>, ICidadeRepository
    {
        public CidadeRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna todas as cidades dado os ids das mesmas
        /// </summary>
        /// <param name="ids">Array das IDs das cidades</param>
        /// <returns></returns>
        public IQueryable<Cidade> GetCidadesIn(int[] ids)
        {
            return (from cidade in All().Include(x => x.Estado)
                 where ids.Contains(cidade.IdCidade)
                select cidade);
        }

        /// <summary>
        /// Retorna a cidade contendo os objetos filhos
        /// </summary>
        /// <param name="id">ID da Cidade</param>
        /// <returns></returns>
        public Cidade GetWithAllChilds(int id)
        {
            return (from cidade in All()
                    .Include(x => x.Estado)
                    .Include(x => x.Estado.Pais)
                 where cidade.IdCidade == id
                select cidade)?.FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para consultar Cidade.
        /// </summary>
        /// <param name="nome">Nome da Cidade</param>
        /// <returns>IQueryable de Cidade</returns>
        public IQueryable<CidadeGrid> Consultar(string nome)
        {
            var resultado = from cidade in All()
                            where cidade.Nome.Contains(nome)
                            orderby cidade.IdCidade descending
                            select new CidadeGrid
                            {
                                IdCidade = cidade.IdCidade,
                                Nome = cidade.Nome,
                                NomeEstado = cidade.Estado.Nome,
                                Ativo = cidade.Ativo,
                                IdEstado = cidade.IdCidade
                            };

            return resultado;
        }

        /// <summary>
        /// Retorna o registro da cidade
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public override Cidade Get(int id)
        {
            return (from cidade in All()
                        .Include(c => c.Estado)
                        .Include(c => c.Estado.Pais)
                  where cidade.IdCidade == id
                 select cidade)?.FirstOrDefault();

        }
    }
}
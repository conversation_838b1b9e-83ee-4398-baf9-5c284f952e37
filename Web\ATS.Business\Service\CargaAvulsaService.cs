﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Web;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using ATS.CrossCutting.Reports.CargaAvulsa;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Interface.Dapper;
using NLog;
using ConfigurationManager = System.Configuration.ConfigurationManager;
using ATS.CrossCutting.Reports.CargaAvulsa.ReciboCargaAvulsa;
using ATS.Domain.Models.CargaAvulsa;
using ExcelDataReader;
using Extratta.CargaAvulsaWorkerService.Events;
using MassTransit;
using Sistema.Framework.Util.Extension;
using ValidationResult = ATS.Domain.Validation.ValidationResult;

namespace ATS.Domain.Service
{
    public class CargaAvulsaService : BaseService<ICargaAvulsaRepository>, ICargaAvulsaService
    {
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ICargaAvulsaDapper _cargaAvulsaDapper;
        private readonly IPushService _pushService;
        private readonly ITransacaoCartaoRepository _transacaoCartaoRepository;
        private readonly IUsuarioService _usuarioService;
        private readonly IFilialService _filialService;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        private readonly IBloqueioGestorValorService _bloqueioGestorValorService;
        private readonly ITransacaoCartaoService _transacaoCartaoService;
        private readonly IUsuarioPermissaoFinanceiroService _usuarioPermissaoFinanceiroService;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IWhiteListIPService _whiteListIPService;
        private readonly IAutenticacaoAplicacaoRepository _autenticacaoAplicacaoRepository;
        private readonly IPublishEndpoint _publisher;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosEmpresaService _parametrosServiceEmpresa;
        private readonly IEmailService _emailService;
        private readonly IUsuarioContatoRepository _usuarioContatoRepository;
        private readonly IAvaliacaoPlanilhaGestorCargaAvulsaRepository _avaliacaoPlanilhaGestorCargaAvulsaRepository;
        private readonly IMotivoService _motivoService;
        private static readonly List<ETipoCarga> CargasAvulsaValidacaoLimite = new()
        {
            ETipoCarga.Integracao,
            ETipoCarga.Lote,
            ETipoCarga.Rapida
        };

        public CargaAvulsaService(ICargaAvulsaRepository repository, IUserIdentity sessionUser,
            IEmpresaRepository empresaRepository, ICargaAvulsaDapper cargaAvulsaDapper,
            IPushService pushService, ITransacaoCartaoRepository transacaoCartaoRepository,
            IUsuarioService usuarioService,
            IFilialService filialService, CartoesServiceArgs cartoesServiceArgs,
            IBloqueioGestorValorService bloqueioGestorValorService,
            ITransacaoCartaoService transacaoCartaoService,
            IUsuarioPermissaoFinanceiroService usuarioPermissaoFinanceiroService,
            IParametrosGenericoService parametrosGenericoService, IWhiteListIPService whiteListIPService,
            IPublishEndpoint publisher, IUserIdentity userIdentity, IParametrosEmpresaService parametrosServiceEmpresa, IEmailService emailService, IUsuarioContatoRepository usuarioContatoRepository, IAutenticacaoAplicacaoRepository autenticacaoAplicacaoRepository, IAvaliacaoPlanilhaGestorCargaAvulsaRepository avaliacaoPlanilhaGestorCargaAvulsaRepository, IMotivoService motivoService) : base(repository, sessionUser)
        {
            _empresaRepository = empresaRepository;
            _cargaAvulsaDapper = cargaAvulsaDapper;
            _pushService = pushService;
            _transacaoCartaoRepository = transacaoCartaoRepository;
            _usuarioService = usuarioService;
            _filialService = filialService;
            _cartoesServiceArgs = cartoesServiceArgs;
            _bloqueioGestorValorService = bloqueioGestorValorService;
            _transacaoCartaoService = transacaoCartaoService;
            _usuarioPermissaoFinanceiroService = usuarioPermissaoFinanceiroService;
            _parametrosGenericoService = parametrosGenericoService;
            _whiteListIPService = whiteListIPService;
            _publisher = publisher;
            _userIdentity = userIdentity;
            _parametrosServiceEmpresa = parametrosServiceEmpresa;
            _emailService = emailService;
            _usuarioContatoRepository = usuarioContatoRepository;
            _autenticacaoAplicacaoRepository = autenticacaoAplicacaoRepository;
            _avaliacaoPlanilhaGestorCargaAvulsaRepository = avaliacaoPlanilhaGestorCargaAvulsaRepository;
            _motivoService = motivoService;
        }

        public CargaAvulsaAddResponseModel Add(CargaAvulsa cargaAvulsa, string cnpjEmpresa = null, string ipv4 = null, EBloqueioOrigemTipo? origem = null, bool? ignorarValidacaoDuplicada = null)
        {
            try
            {
                var validations = IsValidToCrud(cargaAvulsa, origem);

                if (!validations.IsValid)
                    return new CargaAvulsaAddResponseModel(null, null, validations);

                if (SessionUser.IdUsuario != 0
                    && !_usuarioPermissaoFinanceiroService.PossuiPermissao(SessionUser.IdUsuario, EBloqueioFinanceiroTipo.pagamentoCargaAvulsa)
                    && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                {
                    throw new InvalidOperationException("Usuário sem permissão para efetuar carga avulsa.");
                }
                
                if(cargaAvulsa.IdEmpresa == null)
                    throw new InvalidOperationException("Empresa não informada.");
                
                if(string.IsNullOrWhiteSpace(cargaAvulsa.CPFMototista))
                    throw new InvalidOperationException("Motorista não informado.");
                
                var empresa = _empresaRepository
                    .Query(cargaAvulsa.IdEmpresa.Value)
                    .FirstOrDefault();
                
                if (empresa == null)
                    throw new InvalidOperationException("Empresa não encontrada.");

                var mesmoNroControle = _cargaAvulsaDapper.HasCargaAvulsaMesmoNroControle(empresa.IdEmpresa, cargaAvulsa.NroControleIntegracao);
               
                if (mesmoNroControle && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                    throw new Exception($"Carga avulsa de nro controle {cargaAvulsa.NroControleIntegracao} já cadastrada.");
                
                #region Validacao de carga avulsa duplicada
                
                // Inicialmente (ao iniciar o cadastro) o campo ignorarValidacaoDuplicada vem false do portal (por integracao nunca cai aqui pois vem null)
                // Se o usuario confirmar a carga pela modal de confirmacao o campo virá true, ignorando essa validacao

                var empresaValidaCargaDuplicada = _parametrosServiceEmpresa.GetBloqueiaCargaAvulsaDuplicada(empresa.IdEmpresa);

                if (empresaValidaCargaDuplicada && ignorarValidacaoDuplicada == false)
                {
                    var periodoValidacaoDuplicatasHoras = _parametrosServiceEmpresa
                        .GetHorasBloqueioCargaAvulsaDuplicada(empresa.IdEmpresa).ToInt();
                    var data = DateTime.Now.AddHours(-periodoValidacaoDuplicatasHoras);
                    var cargasDuplicadas = Repository.Where(c => 
                            c.CPFMototista == cargaAvulsa.CPFMototista && c.Valor == cargaAvulsa.Valor && c.IdEmpresa == empresa.IdEmpresa &&
                            c.DataCadastro >= data).AsNoTracking()
                        .Select(c => new CargaAvulsaAddItemDuplicadoResponseModel
                        {
                            Codigo = c.IdCargaAvulsa,
                            TipoTitulo = c.Motivo.Descricao,
                            ValorDecimal = c.Valor,
                            MotoristaDocumento = c.CPFMototista,
                            DataCadastroDateTime = c.DataCadastro,
                            MotoristaNome = c.NomeMotorista,
                            CnpjFilial = c.Filial.CNPJ,
                        }).ToList();
                    if (cargasDuplicadas.Any())
                    {
                        return new CargaAvulsaAddResponseModel()
                        {
                            ValidationResult = new ValidationResult<EValidationCargaAvulsa>().Add(EValidationCargaAvulsa.CargaDuplicada, EFaultType.Error),
                            CargaAvulsaDuplicada = true,
                            CargasAvulsasDuplicadas = cargasDuplicadas,
                            PeriodoCargaAvulsaDuplicada = periodoValidacaoDuplicatasHoras,
                        };
                    }
                }

                #endregion
                
                var cartaoService = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices, cargaAvulsa.CPFCNPJUsuario, empresa.NomeFantasia);
                var produtoId = new List<int> { cartaoService.GetIdProdutoCartaoFretePadrao() };
                var cartaoMotorista = cartaoService.GetCartoesVinculados(cargaAvulsa.CPFMototista, produtoId, false, buscarCartoesBloqueados: true);
                if (cartaoMotorista == null)
                    throw new Exception($"Não foi possível obter os cartões vinculados ao motorista.");
                if (!cartaoMotorista.Cartoes.Any())
                    throw new Exception("Motorista não possui cartão vinculado.");

                if (cargaAvulsa.StatusCargaAvulsa != EStatusCargaAvulsa.AguardandoLiberacao)
                    cargaAvulsa.StatusCargaAvulsa = EStatusCargaAvulsa.PendenteDeProcessamento;

                cargaAvulsa.DataCadastro = DateTime.Now;

                Repository.Add(cargaAvulsa);

                if (cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao)
                {
                    var resultEmail = EnviarEmailAlertaGestor(cargaAvulsa.IdEmpresa ?? 0);

                    if(!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Error($"Falha ao notificar gestor carga avulsa {cargaAvulsa.IdCargaAvulsa}: {resultEmail.Messages.FirstOrDefault()}");

                    return new CargaAvulsaAddResponseModel(cargaAvulsa.IdCargaAvulsa, 0, new ValidationResult<EValidationCargaAvulsa>());
                }

                CreateTransaction(cargaAvulsa, cnpjEmpresa, ipv4, cartaoMotorista.Cartoes.Last());

                if (cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.PendenteAprovacao)
                {
                    _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa, EStatusCargaAvulsa.PendenteAprovacao, null, cargaAvulsa.MensagemAntiFraude);
                    return new CargaAvulsaAddResponseModel(cargaAvulsa.IdCargaAvulsa, 0, new ValidationResult<EValidationCargaAvulsa>());
                }

                var transacao = _transacaoCartaoRepository.FirstOrDefault(o => o.IdCargaAvulsa == cargaAvulsa.IdCargaAvulsa); //_transacaoCartaoService.GetStatusTransacaoByIdCargaAvulsa(cargaAvulsa.IdCargaAvulsa);

                if (transacao == null)
                    _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa, EStatusCargaAvulsa.PendenteDeProcessamento, null, null);
                else
                {
                    AlterarStatusCargaAvulsaDeAcordoComStatusTransacao(transacao.StatusPagamento, cargaAvulsa.IdCargaAvulsa, transacao.MensagemProcessamentoWs);

                    if (transacao.StatusPagamento == EStatusPagamentoCartao.Baixado && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                    {
                        var razaoSocialEmpresa = _empresaRepository.GetQuery(cargaAvulsa.IdEmpresa ?? 0).Select(o => o.RazaoSocial).FirstOrDefault();
                        _pushService.EnviarPorDocumento(cargaAvulsa.CPFMototista, "Carga avulsa", $"Olá. Foi efetuada uma carga em seu cartão no valor de R$ {cargaAvulsa.Valor:N} por parte da {razaoSocialEmpresa?.ToUpper()}");
                    }
                }

                return new CargaAvulsaAddResponseModel(cargaAvulsa.IdCargaAvulsa, transacao?.StatusPagamento == EStatusPagamentoCartao.Baixado ? 2 : 0, new ValidationResult<EValidationCargaAvulsa>())
                {
                    CargaAvulsaId = cargaAvulsa.IdCargaAvulsa,
                    StatusCargaAvulsa = transacao?.StatusPagamento == EStatusPagamentoCartao.Baixado ? 2 : 0,
                    StatusPagamentoCartao = transacao?.StatusPagamento,
                    MensagemProcessamentoWs = transacao?.MensagemProcessamentoWs,
                    ValidationResult = new ValidationResult<EValidationCargaAvulsa>()
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"Falha na carga avulsa / {e.Message}");
                return new CargaAvulsaAddResponseModel(null, null, new ValidationResult<EValidationCargaAvulsa>().Add(e.Message, EFaultType.Error));
            }
        }

        private ValidationResult<EValidationCargaAvulsa> IsValidToCrud(CargaAvulsa cargaAvulsa, EBloqueioOrigemTipo? origem = null)
        {
            var validations = new ValidationResult<EValidationCargaAvulsa>();

            if (cargaAvulsa.IdUsuariocadastro == 0)
                validations.Add(EValidationCargaAvulsa.UsuarioDeCadastroNaoInformado, EFaultType.Error);
            else if (!_usuarioService.Find(x => x.IdUsuario == cargaAvulsa.IdUsuariocadastro).Any())
                validations.Add(EValidationCargaAvulsa.PerfilInvalidoParaCadastrarCargaAvulsa, EFaultType.Error);

            if (string.IsNullOrEmpty(cargaAvulsa.NomeMotorista) && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                validations.Add(EValidationCargaAvulsa.NomeMotoristaNaoInformado, EFaultType.Error);

            if (cargaAvulsa.Valor <= 0)
                validations.Add(EValidationCargaAvulsa.ValorNegativoOuZerado, EFaultType.Error);

            if (CargasAvulsaValidacaoLimite.Contains(cargaAvulsa.TipoCarga))
            {
                var limiteDiarioEmpresa = _bloqueioGestorValorService.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorMaximoCargaAvulsaDiaria, cargaAvulsa.IdEmpresa ?? 0, origem) ?? 0;
                var limiteUnitarioEmpresa = _bloqueioGestorValorService.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorMaximoCargaAvulsaUnitaria, cargaAvulsa.IdEmpresa ?? 0, origem) ?? 0;

                var mensagemUnitarioError = EValidationCargaAvulsa.ValorExcedidoUnitarioEmpresa;
                var mensagemDiarioError = EValidationCargaAvulsa.ValorExcedidoDiarioEmpresa;

                var transacaoDoDia = GetTransacoesDiaria(cargaAvulsa.IdEmpresa, cargaAvulsa.IdFilial);

                if (cargaAvulsa.Valor > limiteUnitarioEmpresa && limiteUnitarioEmpresa != 0)
                    validations.Add(mensagemUnitarioError, EFaultType.Error);

                if ((transacaoDoDia + cargaAvulsa.Valor) > limiteDiarioEmpresa && limiteDiarioEmpresa != 0)
                    validations.Add(mensagemDiarioError, EFaultType.Error);

                if (cargaAvulsa.IdFilial.HasValue)
                {
                    var limiteDiarioFilial = _bloqueioGestorValorService.ValorLimiteConfiguradoFilial(EBloqueioGestorTipo.ValorMaximoCargaAvulsaDiaria,cargaAvulsa.IdEmpresa ?? 0, cargaAvulsa.IdFilial,EBloqueioOrigemTipo.Portal) ?? 0;
                    var limiteUnitarioFilial = _bloqueioGestorValorService.ValorLimiteConfiguradoFilial(EBloqueioGestorTipo.ValorMaximoCargaAvulsaUnitaria,cargaAvulsa.IdEmpresa ?? 0, cargaAvulsa.IdFilial,EBloqueioOrigemTipo.Portal) ?? 0;
                    mensagemUnitarioError = EValidationCargaAvulsa.ValorExcedidoUnitarioFilial;
                    mensagemDiarioError = EValidationCargaAvulsa.ValorExcedidoDiarioFilial;

                    if (cargaAvulsa.Valor > limiteUnitarioFilial && limiteUnitarioFilial != 0)
                        validations.Add(mensagemUnitarioError, EFaultType.Error);

                    if ((transacaoDoDia + cargaAvulsa.Valor) > limiteDiarioFilial && limiteDiarioFilial != 0)
                        validations.Add(mensagemDiarioError, EFaultType.Error);
                }
            }
            
            return validations;
        }
        
        public decimal GetTransacoesDiaria(int? idEmpresa, int? idFilial)
        {
            var dados = GetDataTrasacoesDiaria(idEmpresa, idFilial);

            var retorno = dados.ToList().Select(x => x.Valor).Sum();

            return retorno;
        }
        
        public object ConsultaGrid(int? idEmpresa, int idUsuario, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor)
        {
            var dados = GetDataToGridAndReport(orderFilters, filters, idEmpresa,apenasLiberacaoGestor);

            var retorno = new CargaAvulsaResponseDTO
            {
                totalItems = dados.Count(),
                desabilitarBotoes = idEmpresa.HasValue && !_usuarioPermissaoFinanceiroService.PossuiPermissao(idUsuario, 
                    EBloqueioFinanceiroTipo.realizarEstornoCargaAvulsa),
                items = dados.Skip((page - 1) * take)
                    .Take(take)
                    .ToList()
                    .Select(x => new CargaAvulsaResponseItemDTO
                    {
                        IdCargaAvulsa = x.IdCargaAvulsa,
                        StatusCargaAvulsaStr = System.Enum.IsDefined(typeof(EStatusCargaAvulsa), x.StatusCargaAvulsa)
                            ? x.StatusCargaAvulsa.DescriptionAttr()
                            : string.Empty,
                        StatusCargaAvulsa = x.StatusCargaAvulsa,
                        NroControleIntegracao = x.NroControleIntegracao,
                        DataCadastroDateTime = x.DataCadastro,
                        Nome = x.UsuarioCadastro.Nome,
                        ValorStr = $"{x.Valor:N}",
                        CPF = x.CPFMototista,
                        NomeMotorista = x.NomeMotorista,
                        Observacao = x.Observacao,
                        StatusPagamentoEnum = x.TransacaoCartao.Select(t => t.StatusPagamento).FirstOrDefault(),
                        Processamento =
                            string.IsNullOrEmpty(x.TransacaoCartao.Select(t => t.MensagemProcessamentoWs)
                                .FirstOrDefault())
                                ? x.MensagemProcessamento
                                : x.TransacaoCartao.OrderByDescending(o => o.IdTransacaoCartao).Select(t => t.MensagemProcessamentoWs).FirstOrDefault(),
                        TipoCargaEnum = x.TipoCarga,
                        CodigoPlanilhaImportada = x.CodigoPlanilhaImportada,
                        MotivoDescricao = x.Motivo?.Descricao,
                        IdentificadorCartao = x.TransacaoCartao.Where(c => c.IdentificadorCartao.HasValue).Select(c =>  c.IdentificadorCartao).FirstOrDefault()
                    }).ToList()
            };

            return retorno;
        }

        public object ConsultaGridPlanilha(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor)
        {
            var lPlanilhas = Repository.GetAll().Include(x => x.Empresa).GroupBy(g => new
             {
                 g.CodigoPlanilhaImportada,
                 g.IdEmpresa,
                 g.StatusCargaAvulsa,
                 g.Empresa.CNPJ,
                 g.Empresa.RazaoSocial
             })
            .Select(x => new
            {
                Descricao = x.Key.CodigoPlanilhaImportada,
                x.Key.IdEmpresa,
                x.Key.StatusCargaAvulsa,
                x.Key.CNPJ,
                x.Key.RazaoSocial,
                ValorTotal = x.Sum(x => x.Valor)
            });

            if (idEmpresa.HasValue)
                lPlanilhas = lPlanilhas.Where(x => x.IdEmpresa == idEmpresa);

            if(apenasLiberacaoGestor)
                lPlanilhas = lPlanilhas.Where(x => x.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao);

            lPlanilhas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? lPlanilhas.OrderBy(x => x.StatusCargaAvulsa).ThenByDescending(o => o.Descricao)
                : lPlanilhas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            lPlanilhas = lPlanilhas.AplicarFiltrosDinamicos(filters);

            var lPlanilhasResult = lPlanilhas
             .Skip((page - 1) * take)
             .Take(take)
             .ToList();
             

            return new
            {
                totalItems = lPlanilhas.Count(),
                items = lPlanilhasResult.Select(x => new
                {
                    x.Descricao,
                    x.IdEmpresa,
                    x.StatusCargaAvulsa,
                    x.CNPJ,
                    x.RazaoSocial,
                    ValorTotal = $"R$ {x.ValorTotal:N}"
                })
             .ToList()
        };
        }

        public CargaAvulsa Get(int idCargaAvulsa)
        {
            return Repository
                .Include(p => p.UsuarioCadastro)
                .FirstOrDefault(o => o.IdCargaAvulsa == idCargaAvulsa);
        }
        
        public IQueryable<CargaAvulsa> GetByEmpresa(int? idEmpresa)
        {
            return Repository
                .Include(p => p.UsuarioCadastro)
                .Where(p => p.IdEmpresa == idEmpresa);
        }
        
        public IQueryable<CargaAvulsa> GetAll()
        {
            return Repository.GetAll();
        }

        public BusinessResult EnviarEmailAlertaReprovarPlanilha(int idUsuario, string codigoPlanilha)
        {
            try
            {
                using (var streamReader = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\reprovacao-carga-avulsa-lote.html"))
                {
                    var html = streamReader.ReadToEnd();

                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{1}", codigoPlanilha);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                    var lEmails = _usuarioContatoRepository
                        .Where(x => x.IdUsuario == idUsuario && (x.Email != null || x.Email != string.Empty))
                        .Select(x => x.Email)
                        .ToList();

                    if (!lEmails.Any())
                        return BusinessResult.Error("Nao foi encontrado usuario que registrou a planilha!");

                    var emailModel = new EmailModel
                    {
                        Assunto = $"Lote de carga avulsa reprovada pelo gestor",
                        Destinatarios = lEmails,
                        Prioridade = MailPriority.High,
                        AlternateView = view
                    };

                    var result = _emailService.EnviarEmail(emailModel);

                    if (!result.IsValid)
                        return BusinessResult.Error(result.Errors.FirstOrDefault()?.Message);

                    return BusinessResult.Valid();
                }
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult GestorReprovarPlanilha(string codImportacao)
        {
            try
            {
                var planilha = _avaliacaoPlanilhaGestorCargaAvulsaRepository
                    .Where(x => x.CodigoPlanilhaImportada == codImportacao)
                    .OrderByDescending(x => x.DataCadastro)
                    .FirstOrDefault();

                if(planilha != null && planilha.DataCadastro.AddHours(1) > DateTime.Now)
                    return BusinessResult.Error("Planilha já reprovada aguarde o processamento!");

                var lCargaAvulsa = GetAll()
                    .Where(x => x.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao && x.CodigoPlanilhaImportada == codImportacao);

                var token = string.Empty;
                int? empresaIdToken ;

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    lCargaAvulsa = lCargaAvulsa.Where(x => x.IdEmpresa == _userIdentity.IdEmpresa);
                    empresaIdToken = _userIdentity.IdEmpresa;
                }
                else
                    empresaIdToken = GetAll().FirstOrDefault(x => x.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao && x.CodigoPlanilhaImportada == codImportacao)?.IdEmpresa;

                if (empresaIdToken.HasValue && empresaIdToken != 0)
                    token = _autenticacaoAplicacaoRepository.FirstOrDefault(x => x.IdEmpresa == empresaIdToken && x.Ativo)?.Token;

                var lResultCargaAvulsa = lCargaAvulsa
                    .Select(x => new ReprovarCargaAvulsaEvent
                    {
                        IdCargaAvulsa = x.IdCargaAvulsa,
                        Token = token,
                        CNPJAplicacao = x.Empresa.CNPJ
                    })
                    .ToList();

                if(string.IsNullOrWhiteSpace(token) && lResultCargaAvulsa.Any())
                    return BusinessResult.Error("Falha ao obter autorização empresa!");

                var idUsuarioPlanilha = lCargaAvulsa.Select(x => x.IdUsuariocadastro).Distinct().FirstOrDefault();

                var resultEmail = EnviarEmailAlertaReprovarPlanilha(idUsuarioPlanilha ?? 0, codImportacao);

                if (!resultEmail.Success)
                    LogManager.GetCurrentClassLogger().Error($"Falha ao notificar usuario que registrou a planilha {codImportacao}: {resultEmail.Messages.FirstOrDefault()}");

                if (lResultCargaAvulsa.Any())
                {
                    _publisher.PublishBatch(lResultCargaAvulsa);

                    _avaliacaoPlanilhaGestorCargaAvulsaRepository.Add(new AvaliacaoPlanilhaGestorCargaAvulsa()
                    {
                        DataCadastro = DateTime.Now,
                        Resultado = EAvaliacaoGestor.Reprovado,
                        CodigoPlanilhaImportada = codImportacao
                    });
                }

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao reprovar todas as carga avulsa: {e.Message}");
            }
        }

        public BusinessResult GestorAprovarPlanilha(string codImportacao)
        {
            try
            {
                var planilha = _avaliacaoPlanilhaGestorCargaAvulsaRepository
                    .Where(x => x.CodigoPlanilhaImportada == codImportacao)
                    .OrderByDescending(x => x.DataCadastro)
                    .FirstOrDefault();

                if(planilha != null && planilha.DataCadastro.AddHours(1) > DateTime.Now)
                    return BusinessResult.Error("Planilha já aprovada aguarde o processamento!");

                var lCargaAvulsa = GetAll()
                    .Where(x => x.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao && x.CodigoPlanilhaImportada == codImportacao);

                var token = string.Empty;
                int? empresaIdToken ;

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    lCargaAvulsa = lCargaAvulsa.Where(x => x.IdEmpresa == _userIdentity.IdEmpresa);
                    empresaIdToken = _userIdentity.IdEmpresa;
                }
                else
                   empresaIdToken = GetAll().FirstOrDefault(x => x.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao && x.CodigoPlanilhaImportada == codImportacao)?.IdEmpresa;

                if (empresaIdToken.HasValue && empresaIdToken != 0)
                    token = _autenticacaoAplicacaoRepository.FirstOrDefault(x => x.IdEmpresa == empresaIdToken && x.Ativo)?.Token;

                var lResultCargaAvulsa = lCargaAvulsa
                    .Select(x => new AprovarCargaAvulsaEvent()
                    {
                        IdCargaAvulsa = x.IdCargaAvulsa,
                        Token = token,
                        CNPJAplicacao = x.Empresa.CNPJ
                    })
                    .ToList();

                if(string.IsNullOrWhiteSpace(token) && lResultCargaAvulsa.Any())
                    return BusinessResult.Error("Falha ao obter autorização empresa!");

                if (lResultCargaAvulsa.Any())
                {
                    _publisher.PublishBatch(lResultCargaAvulsa);

                    _avaliacaoPlanilhaGestorCargaAvulsaRepository.Add(new AvaliacaoPlanilhaGestorCargaAvulsa()
                    {
                        DataCadastro = DateTime.Now,
                        Resultado = EAvaliacaoGestor.Aprovado,
                        CodigoPlanilhaImportada = codImportacao
                    });
                }

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao aprovar todas as carga avulsa: {e.Message}");
            }
        }

        public EstornarCargaAvulsaResponseModel EstornarCargaAvulsa(int? idCargaAvulsa, string numeroControle, string cnpjEmpresa)
        {
            try
            {
                var empresa = _empresaRepository.All(c => c.CNPJ == cnpjEmpresa).FirstOrDefault();

                if (empresa == null)
                    return new EstornarCargaAvulsaResponseModel(false, "Empresa não encontrada na base");
                
                var cargaAvulsa = idCargaAvulsa.HasValue
                    ? Repository.FirstOrDefault(o => o.IdCargaAvulsa == idCargaAvulsa && o.IdEmpresa == empresa.IdEmpresa)
                    : Repository.Where(o => o.NroControleIntegracao == numeroControle && o.IdEmpresa == empresa.IdEmpresa)
                        .OrderByDescending(o => o.IdCargaAvulsa)
                        .FirstOrDefault();
                
                if (cargaAvulsa == null)
                    return new EstornarCargaAvulsaResponseModel(false, "Registro de carga avulsa não encontrado na base");
                
                if (cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.Aprovado || cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.PendenteAprovacao)
                    return new EstornarCargaAvulsaResponseModel(false, "A carga avulsa não pode ser cancelada pois ainda está aguardando aprovação. Tente novamente em instantes.");
                
                if (SessionUser.IdUsuario != 0 && !_usuarioPermissaoFinanceiroService.PossuiPermissao(SessionUser.IdUsuario, EBloqueioFinanceiroTipo.realizarEstornoCargaAvulsa) && cargaAvulsa.TipoCarga != ETipoCarga.Provisionamento)
                    return new EstornarCargaAvulsaResponseModel(false, "Usuário sem permissão para efetuar estorno.");
                
                var cartaoService = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices,
                    cargaAvulsa.CPFCNPJUsuario, empresa.NomeFantasia);

                var transacaoOriginal = _transacaoCartaoService.Find(o =>
                        o.IdCargaAvulsa == cargaAvulsa.IdCargaAvulsa &&
                        o.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAvulsa &&
                        (o.StatusPagamento == EStatusPagamentoCartao.Pendente ||
                         o.StatusPagamento == EStatusPagamentoCartao.Baixado))
                    .FirstOrDefault();

                if (transacaoOriginal == null)
                {
                    cargaAvulsa.StatusCargaAvulsa = EStatusCargaAvulsa.Cancelado;
                    Repository.Update(cargaAvulsa);

                    return new EstornarCargaAvulsaResponseModel(true, "Estorno realizado com sucesso.");
                }
                
                var transacaoEstornada = _transacaoCartaoService.Find(o =>
                        o.IdCargaAvulsa == cargaAvulsa.IdCargaAvulsa && o.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoCargaAvulsa &&
                        (o.StatusPagamento == EStatusPagamentoCartao.Pendente || o.StatusPagamento == EStatusPagamentoCartao.Baixado))
                    .FirstOrDefault();
                
                if (transacaoEstornada != null)
                    return new EstornarCargaAvulsaResponseModel(false, "Carga estornada anteriormente.", cargaAvulsa.IdCargaAvulsa, transacaoEstornada);
                
                var protocolo = cartaoService.ConsultarProcotolo(transacaoOriginal.IdTransacaoCartao);
                if (protocolo.Status.In(ConsultarProtocoloResponseStatus.Erro))
                {
                    return new EstornarCargaAvulsaResponseModel(false, protocolo.Mensagem, cargaAvulsa.IdCargaAvulsa);
                }
                
                var cartao = new IdentificadorCartao()
                {
                    Identificador = protocolo.CartaoDestino.Identificador,
                    Produto = protocolo.CartaoDestino.Produto
                };
                
                var consultarSaldoCartaoRequest = new ConsultarSaldoCartaoRequest() { Cartao = cartao }; 
                
                var saldoPortador = cartaoService.ConsultarSaldoCartao(consultarSaldoCartaoRequest, null, null);

                if (saldoPortador.Sucesso == false)
                {
                    return new EstornarCargaAvulsaResponseModel(false, "Não foi possível consultar o saldo do cartão do cartão do portador!");
                }

                if (cargaAvulsa.Valor > saldoPortador.Objeto.ValorSaldoDisponivel)
                {
                    return new EstornarCargaAvulsaResponseModel(false, "Não foi possível realizar o estorno devido ao portador não ter saldo suficiente em sua conta!");
                }
                
                // Esta rotina é resiliente quanto a requests duplicadas e possui os tratamentos adequados internamente
                var resultadoEstorno = CreateTransactionEstorno(cargaAvulsa, transacaoOriginal, cnpjEmpresa);

                if (resultadoEstorno.Sucesso)
                {
                    cargaAvulsa.StatusCargaAvulsa = EStatusCargaAvulsa.Estornado;
                    Repository.Update(cargaAvulsa);
                }
                
                return resultadoEstorno;
            }
            catch (Exception e)
            {
                return new EstornarCargaAvulsaResponseModel(false, e.GetBaseException().Message);
            }
        }

        public IQueryable<CargaAvulsa> Find(Expression<Func<CargaAvulsa, bool>> predicate)
        {
            return Repository.Find(predicate);
        }

        public byte[] GerarRelatorioGridCargaAvulsa(OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa,bool apenasLiberacaoGestor)
        {
            var listaDados = new List<RelatorioCargaAvulsaDataType>();
            var dadosCargaAvulsa = GetDataToGridAndReport(orderFilters, filters, idEmpresa,apenasLiberacaoGestor);

            foreach (var item in dadosCargaAvulsa)
                listaDados.Add(new RelatorioCargaAvulsaDataType
                {
                    Data = item.DataCadastro.ToString("dd/MM/yyyy HH:mm"),
                    NomeMotorista = item.NomeMotorista,
                    Usuario = item.UsuarioCadastro?.Nome,
                    Valor = $"R$ {item.Valor:N}",
                    Tipo = item.TipoCarga.DescriptionAttr(),
                    Status = item.StatusCargaAvulsa.DescriptionAttr(),
                    Processamento = item.MensagemProcessamento,
                    CpfPortador = item.CPFMototista,
                    NumeroIntegracao = item.NroControleIntegracao,
                    Observacao = item.Observacao,
                    MotivoDescricao = item.Motivo?.Descricao,
                    IdentificadorCartao = item.TransacaoCartao.Where(c => c.IdentificadorCartao.HasValue).Select(c => c.IdentificadorCartao).FirstOrDefault()?.ToString(),
                });

            return new RelatorioCargaAvulsa().GetReport(listaDados, tipoArquivo, logo);
        }
        
        public ValidationResult CadastrarCargaAvulsaPlanilha(CargaAvulsaValidacaoPlanilha cargaAvulsa)
        {
            if (!cargaAvulsa.IdEmpresa.HasValue)
                throw new Exception("Id Empresa não informado");

            if (!string.IsNullOrEmpty(cargaAvulsa.CnpjFilial))
            {
                var filialId = _filialService.GetIdPorCnpj(cargaAvulsa.CnpjFilial);
                if (filialId != null &&
                    !_filialService.GetQuery(_userIdentity.IdEmpresa).Any(c => c.IdFilial == filialId))
                    throw new Exception("Filial não pertence à empresa.");
            }
            var cargaAvulsaToAdd = new CargaAvulsa
            {
                IdEmpresa = cargaAvulsa.IdEmpresa,
                DataCadastro = DateTime.Now,
                Arquivo = string.Empty,
                TipoCarga = ETipoCarga.Lote,
                NomeMotorista = cargaAvulsa.Nome,
                CPFMototista = cargaAvulsa.Cpf.OnlyNumbers(),
                IdFilial = !string.IsNullOrEmpty(cargaAvulsa.CnpjFilial) ? _filialService.GetIdPorCnpj(cargaAvulsa.CnpjFilial) : null,
                Valor = cargaAvulsa.Valor,
                PlacaCavalo = string.Empty,
                PlacaCarreta1 = string.Empty,
                PlacaCarreta2 = string.Empty,
                PlacaCarreta3 = string.Empty,
                IdUsuariocadastro = cargaAvulsa.UsuarioCadastroId,
                CPFCNPJUsuario = cargaAvulsa.UsuarioCadastroCpf,
                StatusCargaAvulsa = EStatusCargaAvulsa.PendenteDeProcessamento,
                CodigoPlanilhaImportada = cargaAvulsa.CodigoPlanilhaImportada,
                Observacao = cargaAvulsa.Observacao ?? "Adicionado por meio de importação de planilha!",
                IdMotivo = cargaAvulsa.IdMotivo
            };

            var aprovacaoGestor = _parametrosServiceEmpresa.GetSolicitaAprovacaoGestorCargaAvulsaLote(cargaAvulsa.IdEmpresa ?? 0);

            if (aprovacaoGestor)
            {
                cargaAvulsaToAdd.StatusCargaAvulsa = EStatusCargaAvulsa.AguardandoLiberacao;

                if (cargaAvulsa.UltimoRegistro)
                {
                    var resultEmail = EnviarEmailAlertaGestor(cargaAvulsa.IdEmpresa ?? 0);

                    if(!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Error($"Falha ao notificar gestor planilha {cargaAvulsa.CodigoPlanilhaImportada}: {resultEmail.Messages.FirstOrDefault()}");
                }
            }

            Repository.Add(cargaAvulsaToAdd);

            if(aprovacaoGestor)
                return new ValidationResult();

            try
            {
                var empresa = _empresaRepository.GetWithIncludeAutenticacao(cargaAvulsa.IdEmpresa);
                
                if (empresa == null)
                    throw new Exception("Empresa não encontrada!");
                
                var autenticacao = empresa.AutenticacaoAplicacao.FirstOrDefault(x => x.Ativo);

                if (autenticacao == null)
                    throw new Exception("Autenticação da empresa não encontrada!");
                
                var processarCargaAvulsaEvent = new ProcessarCargaAvulsaEvent()
                {
                    IdCargaAvulsa = cargaAvulsaToAdd.IdCargaAvulsa,
                    Token = autenticacao.Token,
                    CNPJAplicacao = autenticacao.CNPJAplicacao
                };
                
                _publisher.Publish(processarCargaAvulsaEvent);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Info($"Erro ao enviar a carga avulsa {cargaAvulsaToAdd.IdCargaAvulsa} a fila de processamento: {e.Message}");
            }
            
            return new ValidationResult();
        }
        
        public ValidationResult AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa status)
        {
            var retorno = new ValidationResult();
            
            if (!System.Enum.IsDefined(typeof(EStatusCargaAvulsa), status))
                throw new Exception("Status inválido");

            _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsaId, status, null, null);
            
            return retorno;
        }
        
        public ValidationResult Aprovar(int cargaAvulsaId, string cnpjEmpresa)
        {
            var retorno = new ValidationResult();
            var empresa = _empresaRepository.GetWithIncludeAutenticacao(cnpjEmpresa);
            
            if (empresa == null)
                throw new Exception("Empresa não localizada!");

            var cargaAvulsa = Repository.Get(cargaAvulsaId);
            
            if (cargaAvulsa == null)
                throw new Exception("Carga avulsa não localizada!");

            if (empresa.IdEmpresa != cargaAvulsa.IdEmpresa)
                throw new Exception("Carga avulsa não pertence a empresa informada!");

            if (cargaAvulsa.StatusCargaAvulsa != EStatusCargaAvulsa.PendenteAprovacao && cargaAvulsa.StatusCargaAvulsa != EStatusCargaAvulsa.AguardandoLiberacao)
                throw new Exception("Carga avulsa possui status diferente de pendente de aprovação ou aguardando liberação!");

            var statusAprovado = EStatusCargaAvulsa.Aprovado;
            
            if(cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao)
                statusAprovado = EStatusCargaAvulsa.AprovadoGestor;

            AlterarStatusCargaAvulsa(cargaAvulsaId, statusAprovado);

            try
            {
                var autenticacao = empresa.AutenticacaoAplicacao.FirstOrDefault(x => x.Ativo);

                if (autenticacao == null)
                    throw new Exception("Autenticação da empresa não encontrada!");
                
                var processarCargaAvulsaEvent = new ProcessarCargaAvulsaEvent()
                {
                    IdCargaAvulsa = cargaAvulsaId,
                    Token = autenticacao.Token,
                    CNPJAplicacao = autenticacao.CNPJAplicacao
                };
                
                _publisher.Publish(processarCargaAvulsaEvent);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Info($"Erro ao enviar a carga avulsa {cargaAvulsaId} a fila de processamento: {e.Message}");
                retorno.Add($"Falha ao colocar a carga avulsa cód {cargaAvulsaId} na fila de pagamentos.");
            }

            return retorno;
        }

        public ValidationResult Processar(int cargaAvulsaId, string cnpjEmpresa)
        {
            var retorno = new ValidationResult();
            var cargaAvulsa = Repository.Get(cargaAvulsaId);

            if (cargaAvulsa == null)
                return retorno.Add($"Carga avulsa com o cód {cargaAvulsaId} não encontrada!");
            
            var empresa = _empresaRepository.Get(cnpjEmpresa);

            if (empresa == null)
                return retorno.Add("Empresa não localizada!");

            if (empresa.IdEmpresa != cargaAvulsa.IdEmpresa)
                return retorno.Add("Carga avulsa não pertence a empresa informada!");
            
            var resultadoTransacao = CreateTransaction(cargaAvulsa);

            if (!resultadoTransacao.Value.IsValid)
            {
                retorno.Add(resultadoTransacao.Value.Errors.FirstOrDefault()?.Message);
            }
            // para o processo se a carga foi barrada pelo antifraude (pq aí nao gerou transacao cartao e o resto desse metodo perde sentido)
            else if (cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.PendenteAprovacao)
            {
                _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa, EStatusCargaAvulsa.PendenteAprovacao, null, cargaAvulsa.MensagemAntiFraude);
                return retorno.Add(cargaAvulsa.MensagemAntiFraude);
            }

            var cargaAvulsarTransacaoModel = _cargaAvulsaDapper.GetCargaAvulsaProcessadaPorId(cargaAvulsa.IdCargaAvulsa);
            
            AlterarStatusCargaAvulsaDeAcordoComStatusTransacao(cargaAvulsarTransacaoModel.StatusPagamento, cargaAvulsarTransacaoModel.IdCargaAvulsa, cargaAvulsarTransacaoModel.MensagemWebService);

            if (cargaAvulsarTransacaoModel.StatusPagamento == EStatusPagamentoCartao.Baixado)
                _pushService.EnviarPorDocumento(cargaAvulsa.CPFMototista, "Carga avulsa",
                    $"Olá. Foi efetuada uma carga em seu cartão no valor de R$ {cargaAvulsa.Valor:N} por parte da {cargaAvulsarTransacaoModel.RazaoSocialEmpresa?.ToUpper()}");

            return retorno;
        }

        /// <summary>
        /// Método responsável por processar pagamentos de cargas avulsas que estão pendentes
        /// </summary>
        public void RealizarCargasAvulsaPendentes()
        {
            try
            {
                _cargaAvulsaDapper.SetarStatusProcessamentoEmAndamento();
                var numeroRegistros = GetNumeroRegistrosParaProcessarCargaAvulsas();
                
                LogManager.GetCurrentClassLogger().Info($"Processará {numeroRegistros} registros.");
                var cargasAvulsas = _cargaAvulsaDapper.GetCargaAvulsasParaProcessar(numeroRegistros);

                if (!cargasAvulsas.Any())
                    return;

                foreach (var cargaAvulsa in cargasAvulsas)
                {
                    try
                    {
                        var horaInicial = DateTime.Now;
                        var resultadoTransacao = CreateTransaction(cargaAvulsa);
                        
                        if (!resultadoTransacao.Value.IsValid)
                            throw new Exception(resultadoTransacao.Value.Errors.FirstOrDefault()?.Message);
                        
                        if (cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.PendenteAprovacao)
                        {
                            _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa, EStatusCargaAvulsa.PendenteAprovacao, null, cargaAvulsa.MensagemAntiFraude);
                        }
                        
                        var horaFinal = DateTime.Now;
                        LogManager.GetCurrentClassLogger()
                            .Info(
                                $"Consumo do WS no processamento da carga avulsa levou: {horaFinal.Subtract(horaInicial).TotalSeconds} segundos.");
                    }
                    catch (Exception e)
                    {
                        _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa,
                            EStatusCargaAvulsa.ProcessamentoEfetuadoComErroNaTransacao, e.Message, null);
                    }
                }

                var cargasAvulsasProcessadas = _cargaAvulsaDapper.GetCargasAvulsasProcessadasPorId(cargasAvulsas.Select(o => o.IdCargaAvulsa).ToList());

                if (!cargasAvulsasProcessadas.Any())
                    return;

                foreach (var cargaAvulsa in cargasAvulsasProcessadas)
                {
                    AlterarStatusCargaAvulsaDeAcordoComStatusTransacao(cargaAvulsa.StatusPagamento, cargaAvulsa.IdCargaAvulsa, cargaAvulsa.MensagemWebService);

                    if (cargaAvulsa.StatusPagamento == EStatusPagamentoCartao.Baixado)
                        _pushService.EnviarPorDocumento(cargaAvulsa.CPFMototista, "Carga avulsa",
                            $"Olá. Foi efetuada uma carga em seu cartão no valor de R$ {cargaAvulsa.Valor:N} por parte da {cargaAvulsa.RazaoSocialEmpresa?.ToUpper()}");
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
            }
        }
        
        public ValidationResult ReprocessarCargaAvulsa(int cargaAvulsaId)
        {
            var cargaAvulsa = Repository.FirstOrDefault(o => o.IdCargaAvulsa == cargaAvulsaId);
            
            if (cargaAvulsa == null)
                return new ValidationResult().Add($"Carga avulsa id {cargaAvulsaId} não encontrado na base de dados.");

            CreateTransaction(cargaAvulsa);
            
            if (cargaAvulsa.StatusCargaAvulsa == EStatusCargaAvulsa.PendenteAprovacao)
            {
                _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa, EStatusCargaAvulsa.PendenteAprovacao, null, cargaAvulsa.MensagemAntiFraude);
                return new ValidationResult().Add("Carga avulsa pendente de aprovação!");
            }

            var transacao = _transacaoCartaoRepository
                .Where(o => o.IdCargaAvulsa == cargaAvulsa.IdCargaAvulsa).OrderByDescending(o => o.IdTransacaoCartao)
                .FirstOrDefault();

            if (transacao == null)
                _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsa.IdCargaAvulsa, EStatusCargaAvulsa.PendenteDeProcessamento, null, null);
            else
                AlterarStatusCargaAvulsaDeAcordoComStatusTransacao(transacao.StatusPagamento, cargaAvulsaId, transacao.MensagemProcessamentoWs);

            return transacao?.StatusPagamento != EStatusPagamentoCartao.Baixado
                ? new ValidationResult().Add("Operação realizada, porém existem pendências na transação")
                : new ValidationResult();
        }

        public byte[] GerarReciboCargaAvulsa(int cargaAvulsaId, int? empresaId, string usuarioNome, string usuarioDocumento)
        {
            var cargaAvulsa = _cargaAvulsaDapper.ConsultarDadosReciboCargaAvulsa(cargaAvulsaId);
            
            if (cargaAvulsa == null)
                throw new Exception($"Carga avulsa Id {cargaAvulsaId} não encontrada na base de dados.");

            if (empresaId.HasValue && empresaId.Value != cargaAvulsa.EmpresaId)
                throw new Exception("Não é permitida a impressão de carga avulsa pertencente a outra empresa");
            
            var numeroCartao = string.Empty;
            var token = !empresaId.HasValue ? _empresaRepository.GetTokenMicroServices(cargaAvulsa.EmpresaId) : _empresaRepository.GetTokenMicroServices(empresaId.Value);
            
            var protocolo = new CartoesService(_cartoesServiceArgs, empresaId, token, usuarioDocumento, usuarioNome).ConsultarProcotolo(new ConsultarProtocoloRequest { ProtocoloRequisicao = cargaAvulsa.IdTransacaoCartao });
            
            if (protocolo.Status == ConsultarProtocoloResponseStatus.Sucesso)
                if (protocolo.CartaoDestino != null)
                    numeroCartao = protocolo.CartaoDestino.Identificador.ToString();
            
            var relatorioReciboCargaAvulsaDto = new RelatorioReciboCargaAvulsaDto(cargaAvulsa.RazaoSocialEmpresa,
                cargaAvulsa.CnpjEmpresa, cargaAvulsa.EnderecoEmpresa, cargaAvulsa.NumeroEmpresa,
                cargaAvulsa.BairroEmpresa, cargaAvulsa.CepEmpresa, cargaAvulsa.NomeCidadeEmpresa,
                cargaAvulsa.SiglaEstadoEmpresa, cargaAvulsa.TelefoneEmpresa, cargaAvulsa.NomeMotorista,
                cargaAvulsa.CpfMotorista, numeroCartao, cargaAvulsa.CelularMotorista, cargaAvulsa.PlacaCavalo,
                cargaAvulsa.PlacaCarreta1, cargaAvulsa.PlacaCarreta2, cargaAvulsa.PlacaCarreta3,
                cargaAvulsa.IdCargaAvulsa, cargaAvulsa.NumeroControleIntegracao, cargaAvulsa.Valor,
                cargaAvulsa.DataCadastro, cargaAvulsa.StatusCargaAvulsa.DescriptionAttr(),
                cargaAvulsa.DataConfirmacaoMeioHomologado, cargaAvulsa.Observacao);
            
            var relatorioReciboCargaAvulsaRodapeDto = new RelatorioReciboCargaAvulsaRodapeDto(numeroCartao,
                cargaAvulsa.NomeMotorista, cargaAvulsa.CpfMotorista, cargaAvulsa.Valor);

            var logo = cargaAvulsa.LogoEmpresa == null
                ? ConstantesUtils.SemImagem
                : Convert.ToBase64String(cargaAvulsa.LogoEmpresa);
            
            return new RelatorioReciboCargaAvulsa(
                new List<RelatorioReciboCargaAvulsaDto> { relatorioReciboCargaAvulsaDto }, 
                new List<RelatorioReciboCargaAvulsaRodapeDto> { relatorioReciboCargaAvulsaRodapeDto }, usuarioNome, logo)
                .GetReport();
        }
        
        private int GetNumeroRegistrosParaProcessarCargaAvulsas()
        {
            var numeroCargasAvulsasParaProcessar =
                !string.IsNullOrWhiteSpace(ConfigurationManager.AppSettings["NUMERO_CARGAS_AVULSAS_PROCESSAR"])
                    ? Convert.ToInt32(ConfigurationManager.AppSettings["NUMERO_CARGAS_AVULSAS_PROCESSAR"])
                    : 10;

            return numeroCargasAvulsasParaProcessar;
        }

        private void AlterarStatusCargaAvulsaDeAcordoComStatusTransacao(EStatusPagamentoCartao statusPagamentoCartao, int cargaAvulsaId, string mensagem)
        {
            switch (statusPagamentoCartao)
            {
                case EStatusPagamentoCartao.Baixado:
                    _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsaId, EStatusCargaAvulsa.ProcessamentoEfetuadoComComSucesso, mensagem, null);
                    break;
                case EStatusPagamentoCartao.Pendente:
                    _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsaId, EStatusCargaAvulsa.ProcessamentoEfetuadoComTransacaoPendente, mensagem, null);
                    break;
                case EStatusPagamentoCartao.Erro:
                    _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsaId, EStatusCargaAvulsa.ProcessamentoEfetuadoComErroNaTransacao, mensagem, null);
                    break;
                case EStatusPagamentoCartao.Aberto:
                    _cargaAvulsaDapper.AlterarStatusCargaAvulsa(cargaAvulsaId, EStatusCargaAvulsa.PendenteDeProcessamento, mensagem, null);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
        
        private IQueryable<CargaAvulsa> GetDataToGridAndReport(OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa,bool apenasLiberacaoGestor)
        {
            var cargasAvulsas = Repository.GetAll()
                .Where(x => x.TipoCarga != ETipoCarga.Provisionamento)
                .Include(x => x.UsuarioCadastro)
                .Include(x => x.TransacaoCartao)
                .Include(x => x.Motivo);

            if (idEmpresa.HasValue)
                cargasAvulsas = cargasAvulsas.Where(o => o.IdEmpresa == idEmpresa);

            if(apenasLiberacaoGestor)
                cargasAvulsas = cargasAvulsas.Where(o => o.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao);

            var filtroCancelado = filters?.FirstOrDefault(o => o.Campo == "StatusCargaAvulsa");

            if (filtroCancelado == null)
                cargasAvulsas = cargasAvulsas.Where(o => o.StatusCargaAvulsa != EStatusCargaAvulsa.Estornado && o.StatusCargaAvulsa != EStatusCargaAvulsa.Cancelado);

            cargasAvulsas = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? cargasAvulsas.OrderBy(x => x.StatusCargaAvulsa).ThenByDescending(o => o.IdCargaAvulsa)
                : cargasAvulsas.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            if (filters != null && filters.Any())
                foreach (var filter in filters)
                    if (filter.Campo == "CPFOnlyNumbers")
                        filter.Valor = StringExtension.OnlyNumbers(filter.Valor);

            var filtroDocumento = filters?.FirstOrDefault(o => o.Campo == "CPFMototista");

            if (filtroDocumento != null)
            {
                var cpfSemMascara = StringExtension.OnlyNumbers(filtroDocumento.Valor);
                cargasAvulsas = cargasAvulsas.Where(o => o.CPFMototista == cpfSemMascara);

                filters.Remove(filtroDocumento);
            }

            var filtroIdentificador = filters?.FirstOrDefault(o => o.Campo == "IdentificadorCartao");
            if (filtroIdentificador != null)
            {
                var identificadorInt = int.Parse(filtroIdentificador.Valor.OnlyNumbers());
                cargasAvulsas = cargasAvulsas.Where(o =>
                    o.TransacaoCartao.Any(c => c.IdentificadorCartao.HasValue && c.IdentificadorCartao == identificadorInt));

                filters.Remove(filtroIdentificador);
            }

            var filtroValor = filters?.Find(f => f.Campo.Equals("Valor"));

            if (filtroValor != null)
            {
                filtroValor.Valor = Regex.Replace(filtroValor.Valor, "[^0-9.,]+", "");
                
                if (filtroValor.Valor.Contains(",") && !filtroValor.Valor.EndsWith(","))
                {
                    filtroValor.Valor = filtroValor.Valor.Replace(".", "");
                    filtroValor.Valor = filtroValor.Valor.Replace(",", ".");
                    cargasAvulsas = cargasAvulsas.Where(d =>
                        d.Valor.ToString(CultureInfo.CurrentCulture).StartsWith(filtroValor.Valor));
                }
                else
                {
                    var valor = int.Parse(StringExtension.OnlyNumbers(filtroValor.Valor));
                    cargasAvulsas = cargasAvulsas.Where(d => Math.Truncate(d.Valor) == valor);
                }
                
                filters.Remove(filtroValor);
            }
            
            var filtroObservacao = filters?.Find(f => f.Campo.Equals("Observacao"));

            if (filtroObservacao != null)
            {
                cargasAvulsas = cargasAvulsas.Where(o => o.Observacao.Contains(filtroObservacao.Valor));
                filters.Remove(filtroObservacao);
            }

            cargasAvulsas = cargasAvulsas.AplicarFiltrosDinamicos(filters);

            return cargasAvulsas;
        }
        
        private IQueryable<CargaAvulsa> GetDataTrasacoesDiaria(int? idEmpresa, int? idFilial)
        {
            var dataInicial = DateTime.Now.StartOfDay();
            var dataFinal = DateTime.Now.EndOfDay();
            
            var cargasAvulsas = Repository.GetAll()
                .Include(x => x.UsuarioCadastro)
                .Include(x => x.TransacaoCartao);
            
            if (idFilial.HasValue && idEmpresa.HasValue)
            {
                cargasAvulsas = cargasAvulsas.Where(o =>
                    o.IdFilial == idFilial && o.IdEmpresa == idEmpresa && (o.DataCadastro >= dataInicial && o.DataCadastro <= dataFinal) && o.StatusCargaAvulsa != EStatusCargaAvulsa.Estornado && o.TipoCarga != ETipoCarga.Provisionamento);
            }
            else if (idEmpresa.HasValue)
            {
                cargasAvulsas = cargasAvulsas.Where(o =>
                    o.IdEmpresa == idEmpresa && (o.DataCadastro >= dataInicial && o.DataCadastro <= dataFinal) && o.StatusCargaAvulsa != EStatusCargaAvulsa.Estornado && o.TipoCarga != ETipoCarga.Provisionamento);
            }
            else if (idFilial.HasValue)
            {
                cargasAvulsas = cargasAvulsas.Where(o =>
                    o.IdFilial == idFilial && (o.DataCadastro >= dataInicial && o.DataCadastro <= dataFinal) && o.StatusCargaAvulsa != EStatusCargaAvulsa.Estornado && o.IdEmpresa == null && o.TipoCarga != ETipoCarga.Provisionamento);
            }

            return cargasAvulsas;
        }

        #region Triggers

        public KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>> CreateTransaction(CargaAvulsa newEntity, string cnpjEmpresa = null, string ipv4 = null, CartaoVinculadoPessoaResponse cartao = null)
        {
            var validationResult = new ValidationResult<EValidationCargaAvulsa>();
            if (newEntity.IdEmpresa == null)
                return new KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>>(newEntity, validationResult.Add(EValidationCargaAvulsa.CargaAvulsaSemEmpresaVinculada, EFaultType.Error));
            
            var empresa = _empresaRepository
                .Query(newEntity.IdEmpresa.Value)
                .Select(e => new { e.IdEmpresa, e.NomeFantasia, e.TokenMicroServices, e.CNPJ })
                .FirstOrDefault();

            if (empresa == null)
                return new KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>>(newEntity, validationResult.Add(EValidationCargaAvulsa.EmpresaInvalida, EFaultType.Error));

            if (newEntity.CPFCNPJUsuario == null && newEntity.IdUsuariocadastro.HasValue)
                newEntity.CPFCNPJUsuario = empresa.CNPJ;

            var cartaoService = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices,
                newEntity.CPFCNPJUsuario, empresa.NomeFantasia);

            var layoutPadraoCartao = _empresaRepository.GetLayoutCartaoPadrao(empresa.IdEmpresa);
            
            if (layoutPadraoCartao == null)
                throw new Exception("Layout padrão da empresa não configurado.");

            if (cartao == null)
            {
                var produtoId = new List<int> { cartaoService.GetIdProdutoCartaoFretePadrao() };
                var cartaoMotorista = cartaoService.GetCartoesVinculados(newEntity.CPFMototista, produtoId, false, buscarCartoesBloqueados: true);
                if (cartaoMotorista == null)
                    throw new Exception($"Não foi possível obter os cartões vinculados ao motorista.");
                
                if (!cartaoMotorista.Cartoes.Any())
                {
                    validationResult.Add(EValidationCargaAvulsa.MotoristaNaoPossuiCartaoVinculado, EFaultType.Error);
                    return new KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>>(newEntity, validationResult);
                }

                cartao = cartaoMotorista.Cartoes.Last();
            }

            var antiFraudeDesativo = _parametrosGenericoService.GetParametro<bool?>(GLOBAL.DesativaAntiFraude, 0) ?? false;

            #region ANALISE FRAUDE

            if (newEntity.StatusCargaAvulsa != EStatusCargaAvulsa.Aprovado && !antiFraudeDesativo && newEntity.TipoCarga != ETipoCarga.Provisionamento)
            {
                var passouFraude = ValidoAntiFraude(newEntity, empresa.CNPJ, cartao,  ipv4, cartaoService);

                if (!passouFraude.Item1)
                {
                    newEntity.StatusCargaAvulsa = EStatusCargaAvulsa.PendenteAprovacao;
                    newEntity.MensagemAntiFraude = passouFraude.Item2;
                    return new KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>>(newEntity, validationResult);
                }
            }

            #endregion

            if (newEntity.StatusCargaAvulsa == EStatusCargaAvulsa.AguardandoLiberacao)
                return new KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>>(newEntity, validationResult);

            var retorno = cartaoService.EfetuarCargaAvulsa(newEntity, empresa.IdEmpresa, newEntity.CPFMototista,
                layoutPadraoCartao.Value, empresa.CNPJ, EOrigemTransacaoCartao.Avulso, cartao);

            if (retorno.Key.Status == CarregarValorResponseStatus.Erro)
                validationResult.Add(retorno.Key.Mensagem, EFaultType.Error);

            return new KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>>(newEntity, validationResult);
        }
        
        private Tuple<bool, string> ValidoAntiFraude(CargaAvulsa carga, string cnpjEmpresa, CartaoVinculadoPessoaResponse cartao, string ipv4, CartoesService cartaoService)
        {
            var idEmpresa = carga.IdEmpresa ?? 0;
            
            if (!string.IsNullOrWhiteSpace(ipv4))
            {
                var isIpWhiteList = _whiteListIPService.IPLiberado(ipv4, idEmpresa);

                if (isIpWhiteList)
                    return new Tuple<bool, string>(true, "Aprovado em antifraude.");
            }

            // PARAMETROS
            var dataLocal = DateTime.Now;
            var periodoNoturnoInicio = _parametrosGenericoService.GetParametro<int?>(GLOBAL.PeriodoNoturnoInicio, 0) ?? 22;
            var periodoNoturnoFim = _parametrosGenericoService.GetParametro<int?>(GLOBAL.PeriodoNoturnoFim, 0) ?? 7;
            
            var valorMaximoUnitarioNoturno = _parametrosGenericoService.GetParametro<decimal?>(GLOBAL.CargaValorMaximoUnitarioNoturno, 0) ?? 500m;
            var valorMaximoUnitarioDiurno = _parametrosGenericoService.GetParametro<decimal?>(GLOBAL.CargaValorMaximoUnitarioDiurno, 0) ?? 2000m;
            var valorMaximoPrimeiraTransacao = _parametrosGenericoService.GetParametro<decimal?>(GLOBAL.CargaValorMaximoPrimeiraTransacao, 0) ?? 500m;

            var intervaloHorasVinculoCartao = _parametrosGenericoService.GetParametro<int?>(GLOBAL.CargaIntervaloHorasVinculoCartao, 0) ?? 12;
            var intervaloHorasOutrasCargas = _parametrosGenericoService.GetParametro<int?>(GLOBAL.CargaIntervaloHorasOutrasCargas, 0) ?? 4;

            // PERÍODO NOTURNO
            if (dataLocal.TimeOfDay.Hours >= periodoNoturnoInicio || dataLocal.TimeOfDay.Hours <= periodoNoturnoFim)
            {
                if (carga.Valor >= valorMaximoUnitarioNoturno)
                    return new Tuple<bool, string>(false, "Valor maior que permitido para período norturno.");
            }
            
            // PERÍODO DIURNO
            if (dataLocal.TimeOfDay.Hours >= periodoNoturnoFim || dataLocal.TimeOfDay.Hours <= periodoNoturnoInicio)
            {
                if (carga.Valor >= valorMaximoUnitarioDiurno)
                    return new Tuple<bool, string>(false, "Valor maior que permitido para período diurno.");
            }

            var cargasDiarias = _cargaAvulsaDapper.HasCargaAvulsaMesmoDocumento(idEmpresa, carga.CPFMototista, intervaloHorasOutrasCargas);
            
            if (cargasDiarias)
                return new Tuple<bool, string>(false, $"Outra carga realizada para o mesmo documento em menos de {intervaloHorasOutrasCargas} horas.");

            if (cartao.DataVinculo.HasValue)
            {
                TimeSpan diferenca = DateTime.Now - cartao.DataVinculo.Value;

                // Verifique se a diferença é menor que 12 horas
                if (diferenca.TotalHours < intervaloHorasVinculoCartao)
                    return new Tuple<bool, string>(false, $"Cartão vinculado há menos de {intervaloHorasVinculoCartao} horas.");
            }
            
            if (cnpjEmpresa != cartao.CnpjEmpresaVinculo)
                return new Tuple<bool, string>(false, "Cartão foi vinculado por uma empresa diferente daquela que está realizando a carga.");

            if (cartao.Identificador != null && carga.Valor >= valorMaximoPrimeiraTransacao)
            {
                var primeiraTransacao = cartaoService.ConsultarPrimeiraTransacao(cartao.Identificador.Value, cartao.Produto.Id);
                if(primeiraTransacao.Existe == false)
                    return new Tuple<bool, string>(false, "Valor não permitido para primeira carga do cartão.");
            }

            var mesmoValor = _cargaAvulsaDapper.HasCargaAvulsaMesmoValor(idEmpresa, carga.Valor, carga.CPFMototista, carga.IdCargaAvulsa);
            if (mesmoValor)
                return new Tuple<bool, string>(false, $"Possível duplicidade: Valor R$ {carga.Valor.ToString("N2")} com mesmo documento já utilizado em outra carga avulsa. Valide com o cliente!");
            
            return new Tuple<bool, string>(true, "Aprovado em antifraude.");
        }

        private EstornarCargaAvulsaResponseModel CreateTransactionEstorno(CargaAvulsa cargaAvulsa, TransacaoCartao transacaoCartao, string cnpjEmpresa = null)
        {
            try
            {
                if (cargaAvulsa.IdEmpresa == null)
                    return new EstornarCargaAvulsaResponseModel(false, $"Carga avulsa sem empresa para realizar estorno. IdCargaAvulsa: {cargaAvulsa.IdCargaAvulsa}");

                var empresaService = _empresaRepository;
                var empresa = empresaService
                    .Query(cargaAvulsa.IdEmpresa.Value)
                    .AsNoTracking()
                    .First();

                var cartaoService = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices, cargaAvulsa.CPFCNPJUsuario, empresa.NomeFantasia); 
                var retorno = cartaoService.EstornarCargaAvulsa(cargaAvulsa, transacaoCartao, empresa, cargaAvulsa.CPFMototista, cnpjEmpresa);
                
                if (retorno.Key.Status != EstornarCargaCartaoResponseStatus.Sucesso && retorno.Key.Status != EstornarCargaCartaoResponseStatus.Pendente)
                    return new EstornarCargaAvulsaResponseModel(false, retorno.Key.Mensagem);
                
                return new EstornarCargaAvulsaResponseModel(true, "Estorno realizado com sucesso.", cargaAvulsa.IdCargaAvulsa, retorno.Value);
            }
            catch (Exception e)
            {
                return new EstornarCargaAvulsaResponseModel(false, e.GetBaseException().Message);
            }
        }

        #endregion

        #region Validação planilha

        public CargaAvulsaResultadoValidacaoPlanilha ValidarLinhasPlanilha(HttpPostedFileBase file, int idEmpresa)
        {
            if (!ValidarTipoArquivo(file.FileName))
                throw new Exception("Arquivo inválido.");
            
            if (file.ContentLength > 0)
            {
                var retorno = new CargaAvulsaResultadoValidacaoPlanilha();
                var stream = file.InputStream;
                var isCsv = false;
                IExcelDataReader reader = null;

                if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelBinaryExtension))
                    reader = ExcelReaderFactory.CreateBinaryReader(stream);
                
                if (file.FileName.ToLower().EndsWith(ConstantesUtils.ExcelXmlExtention))
                    reader = ExcelReaderFactory.CreateOpenXmlReader(stream);

                if (file.FileName.ToLower().EndsWith(ConstantesUtils.CsvExtention))
                {
                    isCsv = true;
                    reader = ExcelReaderFactory.CreateCsvReader(stream, new ExcelReaderConfiguration
                    {
                        AutodetectSeparators = new[] { ';', '|', '#' }
                    });
                }

                if (reader != null)
                {
                    var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
                    {
                        ConfigureDataTable = _ => new ExcelDataTableConfiguration
                        {
                            UseHeaderRow = true
                        },
                    });
                    reader.Close();

                    ValidarHeader(dataSet, isCsv);
                    var resultadoValidacao = ValidarLinhas(dataSet, idEmpresa);

                    retorno.Total = resultadoValidacao.Count;
                    retorno.TotalInvalidos = resultadoValidacao.Count(o => !o.Valido);
                    retorno.TotalValidos = resultadoValidacao.Count(o => o.Valido);
                    retorno.CodigoPlanilhaImportada = CriarCodigoImportacaoPlanilha();
                    retorno.Registros = resultadoValidacao;
                    retorno.SomatorioValoresCargaAvulsa = resultadoValidacao.Sum(o => o.Valor);

                    return retorno;
                }
            }
            else
            {
                throw new Exception("Planilha vazia.");
            }

            return new CargaAvulsaResultadoValidacaoPlanilha();
        }

        public void ValidarHeader(DataSet dataSet, bool isCsv)
        {
            if (dataSet != null)
            {
                var sheets = dataSet.Tables;

                foreach (DataTable sheet in sheets)
                {
                    if (sheet.Columns.Count != 4 && sheet.Columns.Count != 5 && sheet.Columns.Count != 6)
                        throw new Exception(
                            $"A planilha {sheet.TableName} não contém as colunas esperadas pelo layout padrão.");
                    
                    if (isCsv)
                        if (sheet.Columns.Count == 1)
                            if (sheet.Columns[0].ColumnName.Contains(","))
                                throw new Exception("O separador de arquivos CSV não pode ser uma vírgula, pois o valor a ser pago deve conter uma vírgula. Os seguintes separadores são aceitos '; | #'");

                    if (sheet.Columns[0].ColumnName != "CPF")
                        throw new Exception("A primeira coluna não se refere ao CPF do motorista, realize o download do layout padrão e preencha.");

                    if (sheet.Columns[1].ColumnName != "MOTORISTA")
                        throw new Exception("A segunda coluna não se refere ao nome do motorista, realize o download do layout padrão e preencha.");

                    if (sheet.Columns[2].ColumnName != "CNPJFILIAL")
                        throw new Exception("A terceira coluna não se refere ao CNPJ da filal, realize o download do layout padrão e preencha.");

                    if (sheet.Columns[3].ColumnName != "VALOR")
                        throw new Exception("A quarta coluna não se refere ao valor, realize o download do layout padrão e preencha.");

                    if (sheet.Columns.Count >= 5 && sheet.Columns[4].ColumnName != "OBSERVACAO")
                        throw new Exception("A quinta coluna não se refere a observação, realize o download do layout padrão e preencha.");

                    if (sheet.Columns.Count >= 6 && sheet.Columns[5].ColumnName != "TIPO DE TITULO")
                        throw new Exception("A sexta coluna não se refere ao tipo de título, realize o download do layout padrão e preencha.");
                }
            }
            else
            {
                throw new Exception("Planilha inválida, realize o download do layout padrão e preencha.");
            }
        }

        public List<CargaAvulsaValidacaoPlanilha> ValidarLinhas(DataSet dataSet, int idEmpresa)
        {
            var retorno = new List<CargaAvulsaValidacaoPlanilha>();
            var sheets = dataSet.Tables;
            var listaValorFilial = new Dictionary<string, decimal>();
            var filial = _filialService;
            decimal valorTotalEmpresa = 0;
            var bloqueio = _bloqueioGestorValorService;
            decimal transacaoDoDia;
            bool valido;
            
            try
            {
                foreach (DataTable sheet in sheets)
                {
                    var numeroLinha = 1;

                    try
                    {
                        var valorBloqueioEmpresaUnitario = bloqueio.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorMaximoCargaAvulsaUnitaria,idEmpresa,EBloqueioOrigemTipo.Portal);
                        var valorBloqueioEmpresaDiario = bloqueio.ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo.ValorMaximoCargaAvulsaDiaria,idEmpresa,EBloqueioOrigemTipo.Portal);
                        var validarCargaAvulsaDuplicada = _parametrosServiceEmpresa.GetBloqueiaCargaAvulsaDuplicada(idEmpresa);

                        foreach (DataRow row in sheet.Rows)
                        {
                            numeroLinha++;
                            try
                            {
                                var validacao = ValidarLinha(row, idEmpresa, validarCargaAvulsaDuplicada);
                                valido = validacao.Key;

                                if (validacao.Key)
                                {
                                    var idFilial = filial.GetIdPorCnpj(validacao.Value.CnpjFilial);
                                    transacaoDoDia = GetTransacoesDiaria(idEmpresa, idFilial);

                                    var valorBloqueioFilialUnitario = bloqueio.ValorLimiteConfiguradoFilial(EBloqueioGestorTipo.ValorMaximoCargaAvulsaUnitaria, idEmpresa, idFilial,EBloqueioOrigemTipo.Portal);
                                    var valorBloqueioFilialDiario = bloqueio.ValorLimiteConfiguradoFilial(EBloqueioGestorTipo.ValorMaximoCargaAvulsaDiaria, idEmpresa, idFilial,EBloqueioOrigemTipo.Portal);
                                    
                                    if (listaValorFilial.ContainsKey(validacao.Value.CnpjFilial))
                                        listaValorFilial[validacao.Value.CnpjFilial] += validacao.Value.Valor;
                                    
                                    else 
                                    
                                        listaValorFilial.Add(validacao.Value.CnpjFilial, validacao.Value.Valor);

                                    if (idFilial.HasValue)
                                    {
                                        if (valorBloqueioFilialUnitario < validacao.Value.Valor && valorBloqueioFilialUnitario != null && valorBloqueioFilialUnitario != 0)
                                        {
                                            validacao.Value.Mensagem += "O valor ultrapassa o limite unitário para esta filial </br>";
                                            valido = false;
                                        }

                                        if (valorBloqueioFilialDiario < listaValorFilial[validacao.Value.CnpjFilial] +
                                            transacaoDoDia && valorBloqueioFilialDiario != null &&
                                            valorBloqueioFilialDiario != 0)
                                        {
                                            validacao.Value.Mensagem +=
                                                "O valor ultrapassa o limite diário para esta filial </br>";
                                            valido = false;
                                        }
                                    }
                                    
                                    
                                    if (valorBloqueioEmpresaUnitario < validacao.Value.Valor && valorBloqueioEmpresaUnitario != null && valorBloqueioEmpresaUnitario != 0)
                                    {
                                        validacao.Value.Mensagem += "O valor ultrapassa o limite unitário para esta empresa </br>";
                                        valido = false;
                                    }
                                    
                                    valorTotalEmpresa += validacao.Value.Valor;
                                    transacaoDoDia = GetTransacoesDiaria(idEmpresa, null);

                                    if (valorBloqueioEmpresaDiario < valorTotalEmpresa + transacaoDoDia && valorBloqueioEmpresaDiario != null && valorBloqueioEmpresaDiario != 0)
                                    {
                                        validacao.Value.Mensagem += "O valor ultrapassa o limite diário para esta empresa";
                                        valido = false;
                                    }
                                }
                                
                                retorno.Add(new CargaAvulsaValidacaoPlanilha
                                {
                                    Valido = valido,
                                    Cpf = validacao.Value.Cpf,
                                    CnpjFilial = validacao.Value.CnpjFilial,
                                    Linha = numeroLinha,
                                    Nome = validacao.Value.Nome,
                                    NomeSheet = sheet.TableName,
                                    Valor = validacao.Value.Valor,
                                    Observacao = validacao.Value.Observacao,
                                    IdMotivo = validacao.Value.IdMotivo,
                                    MensagemValidacao = string.IsNullOrEmpty(validacao.Value.Mensagem)
                                        ? "Registro válido"
                                        : validacao.Value.Mensagem
                                });
                            }
                            catch (Exception e)
                            {
                                retorno.Add(new CargaAvulsaValidacaoPlanilha
                                {
                                    Valido = false, Cpf = string.Empty, Linha = numeroLinha,
                                    Nome = row.ItemArray[1].ToString(), NomeSheet = sheet.TableName,
                                    Valor = Convert.ToDecimal(row[3]), MensagemValidacao = e.GetBaseException().Message
                                });
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }
            }
            catch (Exception)
            {
                // ignored
            }

            return retorno;
        }

        public KeyValuePair<bool, CargaAvulsaValidacaoPlanilhaItem> ValidarLinha(DataRow row, int idEmpresa, bool validarCargaAvulsaDuplicada)
        {
            try
            {
                var mensagem = string.Empty;
                var obj = new CargaAvulsaValidacaoPlanilhaItem();

                #region Validação do CPF

                if (string.IsNullOrEmpty(row.ItemArray[0].ToString()))
                {
                    mensagem += "CPF não preenchido; ";
                    obj.Cpf = string.Empty;
                }
                else
                {
                    var cpf = StringExtension.OnlyNumbers(row.ItemArray[0].ToString()).PadLeft(11, '0');

                    if (!StringUtil.IsValidCPF(cpf))
                        mensagem += "CPF inválido; ";

                    obj.Cpf = cpf.ToCpfOrCnpj();
                }

                #endregion
                
                #region Validação do CNPJ da Filial e se o valor ultrapassa o limite
                
                var cnpj = StringExtension.OnlyNumbers(row.ItemArray[2].ToString()).PadLeft(14, '0');
                var filial = _filialService;

                if (!string.IsNullOrEmpty(row.ItemArray[2].ToString()))
                {
                    //Validação se é um CNPJ válido
                    if (!StringUtil.IsValidCNPJ(cnpj))
                        mensagem += "CNPJ inválido; ";
                    
                    // //Validação se o CNPJ pertence a uma filial
                    else if (filial.GetListaFilialPorEmpresa(idEmpresa).FirstOrDefault(a => a.CNPJ == cnpj) == null)
                    {
                        mensagem += "Esta filial não foi encontrada; ";
                    }

                    obj.CnpjFilial = row.ItemArray[2].ToString();
                }
                else
                {
                    obj.CnpjFilial = string.Empty;
                }

                #endregion

                #region Validação do nome

                if (string.IsNullOrEmpty(row.ItemArray[1].ToString()))
                {
                    mensagem += "Nome não preenchido; ";
                    obj.Nome = string.Empty;
                }
                else
                {
                    if (row.ItemArray[1].ToString().Length > 100)
                        mensagem += "O nome não pode conter mais de 100 caracteres; ";
                    else
                        obj.Nome = row.ItemArray[1].ToString();
                }

                #endregion

                #region Validação do valor

                if (!decimal.TryParse(row.ItemArray[3].ToString(), out _))
                {
                    mensagem += "Valor inválido; ";
                    obj.Valor = 0;
                }
                else if (Convert.ToDecimal(row.ItemArray[3].ToString()) <= 0)
                {
                    mensagem += "Valor inválido; ";
                    obj.Valor = Convert.ToDecimal(row.ItemArray[3].ToString());
                }
                else
                    obj.Valor = Convert.ToDecimal(row.ItemArray[3].ToString());

                #endregion

                #region Validação da observação

                if (row.ItemArray.Length >= 5)
                {
                    var observacao = row.ItemArray[4].ToString();
                    if (string.IsNullOrWhiteSpace(observacao))
                        obj.Observacao = null;
                    else if (observacao.Length > 255)
                    {
                        mensagem += "Observação não pode conter mais de 255 caracteres; ";
                        obj.Observacao = null;
                    }
                    else
                        obj.Observacao = observacao;
                }

                #endregion

                #region Validação do tipo de titulo (motivo)

                if (row.ItemArray.Length >= 6)
                {
                    var motivoInformado = row.ItemArray[5]?.ToString();
                    if (!string.IsNullOrWhiteSpace(motivoInformado))
                    {
                        var motivoCadastrado = _motivoService
                            .GetAtivos(idEmpresa).ToList()
                            .FirstOrDefault(c => c.Descricao.EqualsIgnoreCase(motivoInformado));
                        if (motivoCadastrado == null)
                        {
                            mensagem += $"Tipo de Título '{motivoInformado}' não encontrado; ";
                            obj.IdMotivo = null;
                        }
                        else
                        {
                            obj.IdMotivo = motivoCadastrado.IdMotivo;
                        }
                    }
                }

                #endregion

                #region Validação de carga avulsa duplicada

                if (validarCargaAvulsaDuplicada)
                {
                    var cpf = row.ItemArray[0]?.ToString();
                    var valor = Convert.ToDecimal(row.ItemArray[3]?.ToString());
                    if (!string.IsNullOrWhiteSpace(cpf) && valor > 0)
                    {
                        var periodoValidacaoDuplicatasHoras = _parametrosServiceEmpresa
                            .GetHorasBloqueioCargaAvulsaDuplicada(idEmpresa).ToInt();
                        var data = DateTime.Now.AddHours(-periodoValidacaoDuplicatasHoras);
                        var cargasDuplicadas = Repository
                            .Where(c => c.CPFMototista == cpf && c.Valor == valor && c.DataCadastro >= data && c.IdEmpresa == idEmpresa)
                            .AsNoTracking().Any();
                        if (cargasDuplicadas)
                        {
                            mensagem += periodoValidacaoDuplicatasHoras == 1 
                                ? $"Carga com mesmo valor para o documento detectada na ultima hora; " 
                                : $"Carga com mesmo valor para o documento detectada nas ultimas {periodoValidacaoDuplicatasHoras} horas; ";
                        }
                    }
                }

                #endregion
                
                var valido = string.IsNullOrEmpty(mensagem);
                obj.Mensagem = mensagem;

                return new KeyValuePair<bool, CargaAvulsaValidacaoPlanilhaItem>(valido, obj);
            }
            catch (Exception e)
            {
                return new KeyValuePair<bool, CargaAvulsaValidacaoPlanilhaItem>(false,
                    new CargaAvulsaValidacaoPlanilhaItem
                    {
                        Cpf = string.Empty, Mensagem = e.GetBaseException().Message, Nome = string.Empty, Valor = 0,
                        CnpjFilial = string.Empty
                    });
            }
        }

        private static string CriarCodigoImportacaoPlanilha()
        {
            var randomNumber = new Random().Next(1, 9);
            var dataHoraAtual = DateTime.Now;

            var codigoText =
                $"{randomNumber}{dataHoraAtual.Day.ToString().PadLeft(2, '0')}{dataHoraAtual.Month.ToString().PadLeft(2, '0')}{dataHoraAtual.Year.ToString().Substring(2)}{dataHoraAtual.Hour.ToString().PadLeft(2, '0')}{dataHoraAtual.Minute.ToString().PadLeft(2, '0')}{dataHoraAtual.Second.ToString().PadLeft(2, '0')}";

            return codigoText;
        }
        
        private static bool ValidarTipoArquivo(string arquivo)
        {
            return
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.ExcelBinaryExtension ||
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.ExcelXmlExtention ||
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.CsvExtention;
        }

        public BusinessResult EnviarEmailAlertaGestor(int idEmpresa)
        {
            try
            {
                using (var streamReader = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\liberacao-gestor-carga-avulsa.html"))
                {
                    var html = streamReader.ReadToEnd();

                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    html = html.Replace("{0}", logoEmail.ContentId);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                    var usuarioGestor = _usuarioPermissaoFinanceiroService
                        .GetByEmpresa(idEmpresa)
                        .Where(x => x.IdBloqueioGestorTipo == (int)EBloqueioFinanceiroTipo.permiteAprovarDesaprovarCargaAvulsa
                                    && x.DesbloquearFinanceiro)
                        .Select(x => x.IdUsuario)
                        .ToList();

                    var lEmails = _usuarioContatoRepository
                        .Where(x => usuarioGestor.Contains(x.IdUsuario) && (x.Email != null || x.Email != string.Empty))
                        .Select(x => x.Email)
                        .ToList();

                    if(!lEmails.Any())
                        return BusinessResult.Error("Nenhum Gestor encontrado na emopresa informada!");

                    var emailModel = new EmailModel
                    {
                        Assunto = $"Carga avulsas estão aguardando a verificação e liberação no nosso portal",
                        Destinatarios = lEmails,
                        Prioridade = MailPriority.High,
                        AlternateView = view
                    };

                    var result = _emailService.EnviarEmail(emailModel);

                    if(!result.IsValid)
                        return BusinessResult.Error(result.Errors.FirstOrDefault()?.Message);

                    return BusinessResult.Valid();
                }
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }
        #endregion

        public BusinessResult EnviarEmailAlertaReprovar(int idUsuario, string nomePortador, string documentoPortador, string motivo)
        {
            try
            {
                using (var streamReader = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\reprovacao-carga-avulsa.html"))
                {
                    var html = streamReader.ReadToEnd();

                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    html = html.Replace("{0}", logoEmail.ContentId);
                    html = html.Replace("{1}", nomePortador);
                    html = html.Replace("{2}", documentoPortador);
                    html = html.Replace("{3}", motivo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                    var lEmails = _usuarioContatoRepository
                        .Where(x => x.IdUsuario == idUsuario && (x.Email != null || x.Email != string.Empty))
                        .Select(x => x.Email)
                        .ToList();

                    if (!lEmails.Any())
                        return BusinessResult.Error("Nao foi encontrado usuario que registrou a carga avulsa!");

                    var emailModel = new EmailModel
                    {
                        Assunto = $"Carga avulsa reprovada pelo gestor",
                        Destinatarios = lEmails,
                        Prioridade = MailPriority.High,
                        AlternateView = view
                    };

                    var result = _emailService.EnviarEmail(emailModel);

                    if (!result.IsValid)
                        return BusinessResult.Error(result.Errors.FirstOrDefault()?.Message);

                    return BusinessResult.Valid();
                }
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult Reprovar(int id,string motivoRejeicao,bool origemPortal)
        {
            try
            {
                var cargaAvulsa = Repository.Get(id);

                if(cargaAvulsa == null)
                    return BusinessResult.Error("Falha ao salvar carga avulsa: Carga avulsa não encontrada!");

                if(origemPortal)
                {
                    var resultEmail = EnviarEmailAlertaReprovar(cargaAvulsa.IdUsuariocadastro ?? 0, cargaAvulsa.NomeMotorista, cargaAvulsa.CPFMototista, motivoRejeicao);

                    if (!resultEmail.Success)
                        LogManager.GetCurrentClassLogger().Error($"Falha ao notificar usuario que registrou a carga avulsa {cargaAvulsa.IdCargaAvulsa}: {resultEmail.Messages.FirstOrDefault()}");
                }

                // Reprovacao
                cargaAvulsa.StatusCargaAvulsa = EStatusCargaAvulsa.Cancelado;
                cargaAvulsa.MotivoRejeicaoGestor = motivoRejeicao;

                Repository.Update(cargaAvulsa);
                Repository.SaveChanges();

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error($"Falha ao salvar carga avulsa {e.Message}");
            }
        }
    }
}
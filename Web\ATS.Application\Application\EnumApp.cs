﻿using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;

namespace ATS.Application.Application
{
    public class EnumApp 
    {
        public object Consultar(string sEnum)
        {
            try
            {
                var retorno = new EnumService().Consultar(sEnum);
                return retorno;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

        }
    }
}

﻿using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;

namespace ATS.Application.Interface
{
    public interface IEstabelecimentoBaseDocumentoApp : IAppBase<EstabelecimentoBaseDocumento>
    {
        string Upload(string dataBase64, string fileName);

        IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null);
        
        object Download(string token);
        
        List<EstabelecimentoBaseDocumento> MergeDocumentosInformadosComExigidos(List<EstabelecimentoBaseDocumento> estabelecimentoDocumentos, int idEstabelecimento, List<int> idsEmpresa);
        List<EstabelecimentoBaseDocumento> GetDocumentos(int idEstabelecimentoBase);
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using System.Web.Configuration;
using ATS.Application.Application.Common;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO.PrestacaoContas;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;

namespace ATS.Application.Application
{
    public class PrestacaoContasApp : AppBase, IPrestacaoContasApp
    {
        private readonly IPrestacaoContasService _service;
        private readonly IPrestacaoContasRepository _prestacaoContasRepository;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IUserIdentity _userIdentity;
        private readonly IEmailService _emailService;
        private readonly IAdministradoraPlataformaRepository _administradoraPlataformaRepository;
        private readonly IParametrosAdministradoraPlataformaService _parametrosAdministradora;
        private readonly IEmpresaDapper _empresaDapper;

        public PrestacaoContasApp(IPrestacaoContasService service, IUsuarioApp usuarioApp, IEmpresaRepository empresaRepository, IUserIdentity userIdentity, IPrestacaoContasRepository prestacaoContasRepository, IEmailService emailService, IParametrosAdministradoraPlataformaService parametrosAdministradora, IAdministradoraPlataformaRepository administradoraPlataformaRepository, IEmpresaDapper empresaDapper)
        {
            _service = service;
            _usuarioApp = usuarioApp;
            _empresaRepository = empresaRepository;
            _userIdentity = userIdentity;
            _prestacaoContasRepository = prestacaoContasRepository;
            _emailService = emailService;
            _parametrosAdministradora = parametrosAdministradora;
            _administradoraPlataformaRepository = administradoraPlataformaRepository;
            _empresaDapper = empresaDapper;
        }

        public ValidationResult Novo(PrestacaoContasNovoRequest request)
        {
            var idEmpresa = _userIdentity.IdEmpresa ?? _empresaRepository.GetIdPorCnpj(request.CNPJAplicacao);

            if (idEmpresa == null)
                throw new InvalidOperationException("Empresa não encontrada.");
            
            if(!_usuarioApp.PertenceAEmpresa(idEmpresa.Value, request.IdUsuario))
                throw new InvalidOperationException("Usuário não pertence à empresa.");
            
            if(!string.IsNullOrWhiteSpace(request.Observacao) && request.Observacao.Length > 200)
                throw new InvalidOperationException("Observação deve ter no máximo 200 caracteres.");

            if(request.Valor <= 0)
                throw new InvalidOperationException("Valor inválido.");

            return _service.Novo(request, idEmpresa.Value);
        }

        public ValidationResult AlterarStatus(int prestacaoContasId, EStatusPrestacaoContas status, string observacao = null)
        {
            if (!_service.PertenceAEmpresa(prestacaoContasId, _userIdentity.IdEmpresa))
                throw new Exception("Registro não encontrado.");
            return _service.AlterarStatus(prestacaoContasId, status, observacao);
        }

        public PrestacaoContasGridResponse ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, EStatusPrestacaoContas[] status = null)
        {
            return _service.ConsultarGrid(take, page, order, filters, status);
        }

        public BusinessResult<bool> HasAberta()
        {
            try
            {
                return BusinessResult<bool>.Valid(_prestacaoContasRepository
                    .Any(x => x.Status == EStatusPrestacaoContas.Aberto 
                                                        && x.IdUsuarioPrestacao == _userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
               return BusinessResult<bool>.Error(e.Message);
            }
        }
        
        public BusinessResult<string> SolicitarEncerramento()
        {
            try
            {
                var prestacao = _prestacaoContasRepository
                    .Include(x => x.Empresa)
                    .Include(x => x.UsuarioPrestacao)
                    .Where(x => x.Status == EStatusPrestacaoContas.Aberto 
                                && x.IdUsuarioPrestacao == _userIdentity.IdUsuario)
                    .Select(x => new
                    {
                        x.IdEmpresa,
                        x.Empresa.RazaoSocial,
                        x.Empresa.CNPJ,
                        x.UsuarioPrestacao.Nome,
                        x.UsuarioPrestacao.CPFCNPJ
                    })
                    .FirstOrDefault();
                
                if(prestacao == null)
                    return BusinessResult<string>.Error("Este usuário não possui prestações de contas em aberto no momento.");

                var resultEnvioEmail = EnviarEmailSolicitarEncerramento(prestacao.IdEmpresa, prestacao.RazaoSocial,prestacao.Nome,prestacao.CPFCNPJ,prestacao.CNPJ);
                
                if(!resultEnvioEmail.Success)
                    return BusinessResult<string>.Error($"Falha ao enviar email: {resultEnvioEmail.Messages.FirstOrDefault()}");
                
                return BusinessResult<string>.Error("Solicitação enviada com sucesso.");
            }
            catch (Exception e)
            {
                return BusinessResult<string>.Error(e.Message);
            }
        }

        private BusinessResult EnviarEmailSolicitarEncerramento(int idempresa, string nomeEmp, string nomeUserPrestacao, string cpfUserPrestacao, string cnpjEmp)
        {
            try
            {
                using (var streamReader = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\solicitacao-encerramento-prestacao-compras.html"))
                {
                    var html = streamReader.ReadToEnd();

                    var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                    logoEmail.ContentId = Guid.NewGuid().ToString();

                    html = html.Replace("{0}", nomeEmp);
                    html = html.Replace("{1}", cnpjEmp.FormatarCpfCnpj());
                    html = html.Replace("{2}", cpfUserPrestacao.FormatarCpfCnpj());
                    html = html.Replace("{3}", nomeUserPrestacao);
                    html = html.Replace("{4}", DateTime.Now.FormatDateTimeBr());

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                    var lEmails = _empresaDapper.GetEmailsFinanceiro(idempresa);

                    var emailModel = new EmailModel
                    {
                        Assunto = $"Encerramento de Prestação de Contas",
                        Destinatarios = lEmails.ToList(),
                        Prioridade = MailPriority.High,
                        AlternateView = view
                    };

                    var admin = _administradoraPlataformaRepository.FirstOrDefault();
                    var configuracao = _parametrosAdministradora.GetConfiguracaoEmail(admin?.IdAdministradoraPlataforma ?? 1);

                    _emailService.EnviarEmailAsync(emailModel, configuracao);

                    return BusinessResult.Valid();
                }
            }
            catch (Exception)
            {
                return BusinessResult.Error($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail");
            }
        }
    }
}
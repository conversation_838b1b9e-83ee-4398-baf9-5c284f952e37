using System;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Models;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Services
{
    public class SrvCheckinResumo
    {
        private readonly ICheckinResumoApp _checkinResumoApp;
        private readonly IEmpresaApp _empresaApp;

        public SrvCheckinResumo(ICheckinResumoApp checkinResumoApp, IEmpresaApp empresaApp)
        {
            _checkinResumoApp = checkinResumoApp;
            _empresaApp = empresaApp;
        }

        public Retorno<CheckinResumoConsultaModel> ConsultarCheckinResumosPaginado(string cnpjAplicacao, int? itensPorPagina, int? pagina, DateTime? dataInicio, DateTime? dataFim)
        {
            try
            {
                var empresaId = _empresaApp.GetIdPorCnpj(cnpjAplicacao);
                var resumos = _checkinResumoApp.ConsultarCheckinResumosPaginado(empresaId ?? 0, itensPorPagina, pagina,dataInicio, dataFim);
                return new Retorno<CheckinResumoConsultaModel>(true, string.Empty, resumos);
            }
            catch (Exception e)
            {
                return new Retorno<CheckinResumoConsultaModel>(false, e.Message, null);
            }
        }
    }
}
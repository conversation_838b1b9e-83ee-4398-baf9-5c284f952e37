using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Common.Request
{
    public class ViagemRemoverCarretasRequest : RequestBase
    {
        public int IdViagem { get; set; }
        public List<string> Carretas { get; set; }

        public ValidationResult ValidaRequest()
        {
            var validacao = ValidaRequestBase(false);
            
            if (string.IsNullOrWhiteSpace(CNPJEmpresa))
                validacao.Add("É obrigatório o envio do campo CNPJEmpresa");

            if (IdViagem <= 0)
                validacao.Add("O campo IdViagem está inválido");
            
            if(Carretas == null || !Carretas.Any())
                validacao.Add("É obrigatório o envio de ao menos um registro no campo Carretas");
            else
                for (var i = 0; Carretas.Count > i; i++)
                {
                    Carretas[i] = Carretas[i].RemoveSpecialCharacters().Trim().ToUpper();
                    if (Carretas[i].Length != 7)
                        return validacao.Add("Os itens de Carretas devem conter 7 caracteres");
                }

            return validacao;
        }
    }
}
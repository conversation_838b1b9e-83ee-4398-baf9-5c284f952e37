using System;
using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.ViagemV2.Request
{
    public class ViagemV2CompraPedagioRequestModel : RequestBase
    {
        public int? ViagemId { get; set; }

        public decimal? ValorPedagio { get; set; }

        public int? QuantidadeEixos { get; set; }
        
        public FornecedorEnum? Fornecedor { get; set; }

        public ETipoVeiculoPedagioEnum? TipoVeiculoPedagio { get; set; }

        public Guid? IdentificadorHistorico { get; set; }
        
        public IList<LocalizacaoDTO> Localizacoes { get; set; }

        public ValidationResult ValidarEntrada()
        {
            var validation = new ValidationResult();

            if (!ViagemId.HasValue || ViagemId <= 0)
                return validation.Add("Viagem Id não informada.", EFaultType.Error);

            if (ValorPedagio > 0 == false && !IdentificadorHistorico.HasValue && Localizacoes == null)
                return validation.Add("É obrigatório informar ao menos um dos campos: ValorPedagio, IdentificadorHistorico ou Localizacoes.", EFaultType.Error);

            if (!QuantidadeEixos.HasValue || QuantidadeEixos <= 0)
                return validation.Add("Quantidade de eixos não informada.", EFaultType.Error);
            
            return validation;
        }
    }
}
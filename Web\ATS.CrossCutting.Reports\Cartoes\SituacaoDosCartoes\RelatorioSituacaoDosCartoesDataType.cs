﻿namespace ATS.CrossCutting.Reports.Cartoes.SituacaoDosCartoes
{
    public class RelatorioSituacaoDosCartoesDataType
    {
        public int? Identificador { get; set; }

        public string Produto { get; set; }

        public string Documento { get; set; }

        public string Portador { get; set; }

        public string Status { get; set; }
        
        public string StatusPedagio { get; set; }

        public string DataVinculo { get; set; }

        public string DataDesvinculo { get; set; }

        public string MotivoDesvinculo { get; set; }

        public string Administradora { get; set; }

        public string CnpjEmpresa { get; set; }

        public string NomeEmpresa { get; set; }

        public string DocumentoPontoDistribuicao{ get; set; }

        public string PontoDistribuicao { get; set; }

        public string DataRecepcao { get; set; }

        public string DataCadastro { get; set; }

        public string DataAlteracao { get; set; }
    }
}
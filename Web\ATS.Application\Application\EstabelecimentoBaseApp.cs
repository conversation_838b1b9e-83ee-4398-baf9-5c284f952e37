﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class EstabelecimentoBaseApp : AppBase, IEstabelecimentoBaseApp
    {
        private readonly IEstabelecimentoBaseService _service;
        public EstabelecimentoBaseApp(IEstabelecimentoBaseService service)
        {
            _service = service;
        }
        public ValidationResult Add(EstabelecimentoBase estabelecimento)
        {
            var validationResult = new ValidationResult();
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    validationResult.Add(_service.Add(estabelecimento));
                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return validationResult.Add(e.GetBaseException().Message);
            }
                
            return validationResult;
        }
        
        public EstabelecimentoBase GetWithDocumentos(int idEstabelecimento)
        {
            return _service.GetWithDocumentos(idEstabelecimento);
        }

        public ValidationResult Update(EstabelecimentoBase estabelecimento, List<EstabelecimentoBaseProduto> produtos, List<EstabelecimentoBaseContaBancaria> contasBancarias, int administradoraPlataforma,
            List<int> idsProdutosBaseExcluir = null, List<int> idsContasBancariasExcluir = null, List<EstabelecimentoBaseDocumento> documentos = null)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _service.Update(estabelecimento, produtos, contasBancarias, administradoraPlataforma, idsProdutosBaseExcluir, idsContasBancariasExcluir, documentos);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Inativar(int idEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _service
                        .Inativar(idEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _service.Reativar(idEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object ConsultaGrid(int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _service.ConsultaGrid(idTipoEstabelecimento, descricao, take, page, orderFilters, filters);
        }

        public EstabelecimentoBase Get(int idEstabelecimento)
        {
            return _service.Get(idEstabelecimento);
        }

        public EstabelecimentoBase Get(string cnpj)
        {
            return _service.Get(cnpj);
        }

        public IQueryable<EstabelecimentoBase> GetQueryByCnpj(string cnpj)
        {
            return _service.GetQueryByCnpj(cnpj);
        }

        public IQueryable<EstabelecimentoBase> GetQueryById(int id)
        {
            return _service.GetQueryById(id);
        }

        public IQueryable<EstabelecimentoBase> GetEstabelecimentosJSL()
        {
            return _service.GetEstabelecimentosJSL();
        }

        public async Task<ValidationResult> AutorizarEstabelecimentoJSLEmpresas(int idEstabelecimento, int administradoraPlataforma)
        {
            var estabelecimentoBase = GetQueryById(idEstabelecimento).Include(c => c.EstabelecimentoBaseProdutos).First();
            
            var validationResult = await _service.AutorizarEstabelecimentoJslEmpresas(estabelecimentoBase);
            
            validationResult.Add(await _service.CredenciaEstabelecimentosJsl(estabelecimentoBase, administradoraPlataforma));

            return validationResult;
        }

        public ValidationResult AutorizarEstabelecimentoJSLParaEmpresa(int idEstabelecimento, int idempresa, int administradoraPlataforma)
        {
            return _service.AutorizarEstabelecimentoJSLParaEmpresa(idEstabelecimento, idempresa, administradoraPlataforma);
        }

        public ValidationResult IntegrarEstabelecimentoJSL(EstabelecimentoBase estabelecimentoBase)
        {
            return _service.IntegrarEstabelecimentoJSL(estabelecimentoBase);
        }

        public ValidationResult RemoverCombustiveisJSL(IList<int> idprodutobase)
        {
            return _service.RemoverCombustiveisJSL(idprodutobase);
        }
        
        public int GetIdByCnpj(string cnpj)
        {
            return _service.GetIdByCnpj(cnpj);
        }
    }
}

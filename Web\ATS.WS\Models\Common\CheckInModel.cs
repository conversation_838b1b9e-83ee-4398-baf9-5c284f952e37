﻿using ATS.Domain.Enum;
using System;

namespace ATS.WS.Models.Common
{
    public class CheckInModel
    {
        public int IdCheckIn     { get; set; }
        public int IdMotorista   { get; set; }
        public DateTime DataHora { get; set; }
        public ETipoEvento TipoEvento   { get; set; }
        public decimal Latitude  { get; set; }
        public decimal Longitude { get; set; }
        public string CPFMotorista { get; set; }
        public string NomeMotorista { get; set; }
        public string CPFUsuario { get; set; }
        public string NomeUsuario { get; set; }
        public int IdViagem { get; set; }
        public string CNPJEmpresa { get; set; } 
        public string RazaoSocial { get; set; }
        public int IBGECidade { get; set; }
        public string SiglaUf { get; set; }

        public string Placa { get; set; }

        public string DescricaoCidade { get; set; }
    }
}
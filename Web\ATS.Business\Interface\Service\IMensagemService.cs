﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface IMensagemService : IService<Mensagem>
    {
        Mensagem Get(int id);
        Mensagem GetWithAllChilds(int id);
        Mensagem Add(Mensagem mensagem);
        IEnumerable<MensagemDestinatario> GetDestinatariosFromMensagemID(int mensagemId);
        IQueryable<Mensagem> GetPorDataEnvio(DateTime dataBase);
        IQueryable<Mensagem> ConsultarRecebidos(int idUsuario, DateTime dataInicial, DateTime dataFinal, string assunto);

        /// <summary>
        /// Retorna lista de mensagens novas
        /// </summary>
        /// <param name="idUsuarioDestinatario">Id do Usuario Destinatario</param>
        /// <returns></returns>
        ICollection<Mensagem> GetMensagensNovas(int idUsuarioDestinatario, DateTime? dataInicial);

        int GettotalMensagensNaoLidasPorUsuario(int idUsuario);

        /// <summary>
        /// Retorna todas as mensagens para o idusuario pelo filtro de data
        /// </summary>
        /// <param name="idUsuarioDestinatario"></param>
        /// <param name="dataFiltro"></param>
        /// <returns></returns>
        ICollection<Mensagem> GetMensagensPeloUsuario(int idUsuarioDestinatario, DateTime? dataFiltro);

        Mensagem Update(Mensagem mensagem);

        IQueryable<Mensagem> ConsultarEnviados(int idRemetente, DateTime dataInicial, DateTime dataFinal, string assunto);
        MensagemDestinatario AdicionarDestinatariosMensagem(MensagemDestinatario mensagemDestinatario);
        IEnumerable<MensagemGrupoUsuario> GetGruposDestinatariosFromMensagemID(int mensagemId);
        void RemoveGruposUsuario(int idMensagem);
        void RemoveDestinatarios(int idMensagem);

        /// <summary>
        /// Reativar a mensagem
        /// </summary>
        /// <param name="idMensagem">Código da mensagem a ser reativado</param>
        /// <returns></returns>
        ValidationResult Reativar(int idMensagem);

        /// <summary>
        /// Inativar a mensagem
        /// </summary>
        /// <param name="idMensagem">Código da mensagem a ser reativado</param>
        /// <returns></returns>
        ValidationResult Inativar(int idMensagem);

        ValidationResult SendBroadcast();
    }
}
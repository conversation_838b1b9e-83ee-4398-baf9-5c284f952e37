﻿using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ProtocoloAnexo
    {
        /// <summary>
        /// Código de anexo do protocolo
        /// </summary>
        public int IdProtocoloAnexo { get; set; }

        /// <summary>
        /// Código do protocolo
        /// </summary>
        public int IdProtocolo   { get; set; }

        /// <summary>
        /// Código do documento
        /// </summary>
        public int IdDocumento { get; set; }

        /// <summary>
        /// Token do protocolo
        /// </summary>
        public string Token { get; set; }

        #region Virtual Fields
        public virtual Protocolo Protocolo { get; set; }
        public virtual Documento Documento { get; set; }
        #endregion
    }
}

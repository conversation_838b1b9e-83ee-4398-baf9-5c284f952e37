using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using System;
using System.Linq;
using System.Web.Http;
using ATS.Data.Repository.External.Serpro.DTO;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Mobile.Common;
using Newtonsoft.Json;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class AplicativoController : BaseController
    {
        private readonly IRecursoMobileApp _app;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IRsaCryptoService _rsaCryptoService;
        private readonly ISerproApp _serproApp;

        public AplicativoController(BaseControllerArgs baseArgs, IRecursoMobileApp app, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IRsaCryptoService rsaCryptoService, ISerproApp serproApp) : base(baseArgs)
        {
            _app = app;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _rsaCryptoService = rsaCryptoService;
            _serproApp = serproApp;
        }

        [System.Web.Mvc.HttpGet]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [System.Web.Mvc.AllowAnonymous]
        public JsonResult GetRecursos(string cnpjAplicacao, string token)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                var retorno = _app.GetRecursoMobileData();

                if (!retorno.Success)
                    return Responde(new RecursosMobileModel
                    {
                        Sucesso = false,
                        Mensagem = retorno.ToString()
                    });

                return Responde(new RecursosMobileModel
                {
                    Sucesso = true,
                    Objeto = retorno.Value
                });
            }
            catch (Exception e)
            {
                return Responde(new RecursosMobileModel
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [System.Web.Mvc.HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [System.Web.Mvc.AllowAnonymous]
        public JsonResult ConsultarSerpro([FromBody] string dados, [FromBody] string fotob64)
        {
            try
            {
                ValidacaoSerproRequest req;
                
                try
                {
                    dados = _rsaCryptoService.Decrypt(dados);
                    req = JsonConvert.DeserializeObject<ValidacaoSerproRequest>(dados);
                    req.PhotoBase64 = fotob64;
                }
                catch (Exception e)
                {
                    Logger.Error(e);
                    return Responde(new Retorno<ValidacaoSerproResponse>(false, "Dados inválidos.", null));
                }
                
                var retornoSerpro = _serproApp.ValidarPortador(req);

                var msg = retornoSerpro.Success ? "Validação efetuada." : retornoSerpro.Messages.FirstOrDefault();
                
                return Responde(new Retorno<ValidacaoSerproResponse>(retornoSerpro.Success, msg, retornoSerpro.Value));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(new Retorno<ValidacaoSerproResponse>(false, e.ToString(), null));
            }
        }

    }
}
﻿using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Domain.Enum
{
    public enum EConsultaPagamentosModelTipo
    {
        [EnumMember, Description("Adiantamento")]
        Adiantamento = 0,

        [EnumMember, Description("Saldo")]
        Saldo = 1,

        [EnumMember, Description("Estadia")]
        Estadia = 2,

        [EnumMember, Description("RPA")]
        RPA = 3,

        [EnumMember, Description("TarifaAntt")]
        TarifaAntt = 4,

        [EnumMember, Description("Abastecimento")]
        Abastecimento = 5,

        [EnumMember, Description("Abono")]
        Abono = 6,

        [EnumMember, Description("Pedagio")]
        Pedagio = 7,

        [EnumMember, Description("Carga Avulsa")]
        CargaAvulsa = 99
    }
}
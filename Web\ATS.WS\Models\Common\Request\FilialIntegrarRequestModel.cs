﻿using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Common.Request
{
    public class FilialIntegrarRequestModel : RequestBase
    {
        public int? IdFilial { get; set; }
        public int CodigoIbgeCidade { get; set; }
        public int CodigoIbgeEstado { get; set; }
        public int CodigoBacenPais { get; set; }
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Sigla { get; set; }
        public string Cep { get; set; }
        public string CodigoFilial { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public short? Numero { get; set; }
        public string Bairro { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public int IdEmpresa { get; set; }
        public bool Ativo { get; set; } = true;
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool? PontoReferencia { get; set; } = false;

        public BloqueioGestorValorDto AlcadasBloqueioGestorValor { get; set; }
        
        public List<FilialContatos> Contatos { get; set; }
        public int? IdCidade { get; set; }
        public int? IdEstado { get; set; }
        public int? IdPais { get; set; }

        #region Parâmetros de servidor de e-mail

        public bool EmailSsl { get; set; }
        public string EmailUsuario { get; set; }
        public string EmailSenha { get; set; }
        public string EmailServidor { get; set; }
        public decimal? EmailPorta { get; set; }
        public string EmailEndereco { get; set; }
        public string EmailNome { get; set; }

        #endregion
    }

    public class FilialIntegrarResponseModel  
    {
        public int? IdFilial { get; set; }
        public string CnpjEmpresa { get; set; }
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Sigla { get; set; }
        public string Cep { get; set; }
        public string CodigoFilial { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public short? Numero { get; set; }
        public string Bairro { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public int IdEmpresa { get; set; }
        public bool Ativo { get; set; } = true;
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public int? IdFilialMae { get; set; }
        public bool PontoApoio { get; set; } = false;

        public List<FilialContatos> Contatos { get; set; }
        public int? IdCidade { get; set; }
        public int? IdEstado { get; set; }
        public int? IdPais { get; set; }

        #region Parâmetros de servidor de e-mail

        public bool EmailSsl { get; set; }
        public string EmailUsuario { get; set; }
        public string EmailSenha { get; set; }
        public string EmailServidor { get; set; }
        public decimal? EmailPorta { get; set; }
        public string EmailEndereco { get; set; }
        public string EmailNome { get; set; }

        #endregion
    }
}
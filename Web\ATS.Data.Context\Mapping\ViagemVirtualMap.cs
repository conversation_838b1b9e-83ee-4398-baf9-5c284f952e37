﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemVirtualMap : EntityTypeConfiguration<ViagemVirtual>
    {
        public ViagemVirtualMap()
        {
            ToTable("VIAGEM_VIRTUAL");

            HasKey(o => o.IdViagemVirtual);

            Property(o => o.IdViagemVirtual)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
        }
    }
}

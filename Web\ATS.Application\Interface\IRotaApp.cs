﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IRotaApp
    {
        Rota Get(int id);
        List<Rota> ConsultarRotas(int idEmpresa, DateTime? dataBase);
        void Reativar(int idRota);
        void Inativar(int idRota);

        object ConsultaGrid(int? idEmpresa, bool? listarInativos, string descricao, int take, int page, 
            OrderFilters order, List<QueryFilters> filters);

        void Excluir(int idRota);
    }
}
﻿using System;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using ATS.Domain.Enum;

namespace ATS.Domain.Service
{
    public class TransacaoCartaoService : ServiceBase, ITransacaoCartaoService
    {
        private readonly ITransacaoCartaoRepository _transacaoCartaoRepository;

        public TransacaoCartaoService(ITransacaoCartaoRepository transacaoCartaoRepository)
        {
            _transacaoCartaoRepository = transacaoCartaoRepository;
        }

        public TransacaoCartao Add(TransacaoCartao transacaoCartao)
        {
            return _transacaoCartaoRepository.Add(transacaoCartao);
        }

        public void Update(TransacaoCartao transacaoCartao)
        {
            _transacaoCartaoRepository.Update(transacaoCartao);
        }

        public int GetCountByIdEvento(int idevento)
        {
            return _transacaoCartaoRepository.GetCountByIdEvento(idevento);
        }

        public int GetCountByIdCarga(int idCarga)
        {
            return _transacaoCartaoRepository.All().Count(x => x.IdCargaAvulsa == idCarga);
        }

        public IList<TransacaoCartao> GetByIdEvento(int idevento)
        {
            return _transacaoCartaoRepository.GetByIdEvento(idevento);
        }

        public IList<TransacaoCartao> GetByIdCarga(int idCarga)
        {
            return _transacaoCartaoRepository.GetByIdCarga(idCarga);
        }

        public TransacaoCartao GetByIdEventoAndTipoProcesso(int idevento, ETipoProcessamentoCartao tipoProcessamentoCartao)
        {
            return _transacaoCartaoRepository.GetByIdEventoAndTipoProcessamento(idevento, tipoProcessamentoCartao);
        }

        public TransacaoCartao GetByIdCargaAndTipoProcesso(int idCarga, ETipoProcessamentoCartao tipoProcessamentoCartao)
        {
            return _transacaoCartaoRepository.All()
                .FirstOrDefault( x => x.IdCargaAvulsa == idCarga && 
                                      x.TipoProcessamentoCartao == tipoProcessamentoCartao &&
                                      x.StatusPagamento != EStatusPagamentoCartao.Erro);
        }

        public bool ExistsByIdEvento(int idevento)
        {
            var eventoexistente = _transacaoCartaoRepository.GetByIdEvento(idevento);
            return eventoexistente.Any();
        }

        public TransacaoCartao GetById(int transacaoCartaoId)
        {
            return _transacaoCartaoRepository.GetById(transacaoCartaoId);
        }
        
        public TransacaoCartao GetByResgate(int resgateId)
        {
            return _transacaoCartaoRepository.GetByResgate(resgateId);
        }
        
        public IQueryable<TransacaoCartao> GetQuery()
        {
            return _transacaoCartaoRepository.All();
        }
        
        public EStatusPagamentoCartao? GetStatusTransacaoByIdCargaAvulsa(int cargaAvulsaId)
        {
            var transacao = _transacaoCartaoRepository
                .Select(o => new TransacaoCartao {StatusPagamento = o.StatusPagamento})
                .FirstOrDefault(o => o.IdCargaAvulsa == cargaAvulsaId);

            return transacao?.StatusPagamento;
        }

        public IQueryable<TransacaoCartao> GetQuery(int transacaoCartaoId)
        {
            return _transacaoCartaoRepository.Where(t => t.IdTransacaoCartao == transacaoCartaoId);
        }

        public TransacaoCartao GetByIdIncludeViagem(int idtransacaoCartao)
        {
            return _transacaoCartaoRepository.GetByIdIncludeViagem(idtransacaoCartao);
        }

        public bool AnyByTransacao(int idtransacaoCartao)
        {
            return _transacaoCartaoRepository.Any(c => c.IdTransacaoCartao == idtransacaoCartao);
        }

        public TransacaoCartao GetTransferenciaManualAdiantamento(string cpfCnpjOrigem, string cpfCnpjDestino, DateTime dataBaixaPrimeiroEventoViagem)
        {
            var transacoes = _transacaoCartaoRepository.Where(o =>
                o.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaAdiantamento &&
                o.IdViagemEvento == null && o.CnpjCpfOrigem == cpfCnpjOrigem && o.CnpjCpfDestino == cpfCnpjDestino &&
                o.DataCriacao > dataBaixaPrimeiroEventoViagem).OrderByDescending(o => o.DataCriacao).ToList();

            return transacoes.FirstOrDefault();
        }
        
        public IQueryable<TransacaoCartao> Find(Expression<Func<TransacaoCartao, bool>> predicate, bool @readonly = false)
        {
            return _transacaoCartaoRepository.Find(predicate, @readonly);
        }
    }
}

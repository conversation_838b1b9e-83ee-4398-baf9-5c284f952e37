﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ATS.WS.Models.Webservice
{
    public class GoogleMatrixResponse
    {
        public string Status { get; set; }
        public List<Result> Results { get; set; }
        public List<string> destination_addresses { get; set; }
        public List<string> origin_addresses { get; set; }
        public List<Row> rows { get; set; }

    }

    public class Row
    {
        public List<Element> elements { get; set; }
    }

    public class Element 
    {
        public TextProperty distance { get; set; }
        public TextProperty duration { get; set; }
        public string status { get; set; }
    }
 
    public class TextProperty
    {
        public decimal? value { get; set; }
        public string text { get; set; }
    }

}
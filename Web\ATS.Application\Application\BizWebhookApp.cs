﻿using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Webservice.Request.BizWebhook;

namespace ATS.Application.Application
{
    public class BizWebhookApp : IBizWebhookApp
    {
        private readonly IBizWebhookService _bizWebhookService;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        
        public BizWebhookApp(IBizWebhookService bizWebhookService, IParametrosGenericoService parametrosGenericoService)
        {
            _bizWebhookService = bizWebhookService;
            _parametrosGenericoService = parametrosGenericoService;
        }

        public void PushCreatedAccount(BizWebhookCreatedAccountRequest request)
        {
            _bizWebhookService.PushCreatedAccount(request);
        }

        public void PushTransactionOccurred(BizWebhookTransactionOcurredRequest request)
        {
            if(_parametrosGenericoService.GetParametro<bool?>(GLOBAL.HabilitarPushWebhookTransacao, 0) ?? false)
                _bizWebhookService.PushTransactionOccurred(request);
        }
    }
}
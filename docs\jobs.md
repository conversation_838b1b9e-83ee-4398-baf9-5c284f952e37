# Jobs

O sistema suporta agendamento de jobs em backgroud através do framework [Quartz.NET](https://quartz-scheduler.net/) em conjunto com [Dependency Injection](dependency-injection.md).

Os jobs ficam na pasta [Web\ATS.WS\Jobs](../Web/ATS.WS/Jobs/).

- [JobManager.cs](../Web/ATS.WS/Jobs/JobManager.cs): Responsabilidade única de agendar os serviços no método `Start()`.
    - Execute o método `ScheduleJob<TJob>(jobSettingsKey)` para agendar a classe executadora do job (TJob) e a chave de configuração no web.config (jobSettingsKey)
    - No [Web.config](../Web/ATS.WS/Web.config) é necessário habilitar a execução e informar uma expressão cron para agendamento
    - O site [cronmaker](http://www.cronmaker.com) pode lhe auxiliar na construção da expressão
- [ProcessarPagamentoAgendadoJob.cs](../Web/ATS.WS/Jobs/ProcessarPagamentoAgendadoJob.cs): Classe que será acionada no momento agendado

## Configurações no web.config

O processo agendado é hosteado no próprio ATS.WS e deve ser evitado a concorrencia, portante só um servidor deve ter o job ativo.

As chaves no web.config devem segui o padrão:

```xml
<add key="Jobs:(nome-definido-no-código):Enabled" value="true" />
<add key="Jobs:(nome-definido-no-código):Cron" value="0/2 * * * * ?" />
```

Exemplo:

```xml
<add key="Jobs:ProcessarPagamentoAgendado:Enabled" value="true" />
<add key="Jobs:ProcessarPagamentoAgendado:Cron" value="0/2 * * * * ?" />
```

> ***Obs.: "0/2 * * * * ?" é uma expressão de alta recorrência (a cada 2 segundos) somente de exemplo para facilitar testes do desenvolvedor. Nunca use algo tão baixo em produção/homologação.***
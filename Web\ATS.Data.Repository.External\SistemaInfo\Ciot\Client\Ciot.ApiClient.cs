﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v********* (NJsonSchema v******** (Newtonsoft.Json v11.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

using System.Web;
using ATS.Data.Repository.External.SistemaInfo;

#pragma warning disable 108 // Disable "CS0108 '{derivedDto}.To<PERSON>son()' hides inherited member '{dtoBase}.ToJson()'. Use the new keyword if hiding was intended."
#pragma warning disable 114 // Disable "CS0114 '{derivedDto}.RaisePropertyChanged(String)' hides inherited member 'dtoBase.RaisePropertyChanged(String)'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword."
#pragma warning disable 472 // Disable "CS0472 The result of the expression is always 'false' since a value of type 'Int32' is never equal to 'null' of type 'Int32?'
#pragma warning disable 1573 // Disable "CS1573 Parameter '...' has no matching param tag in the XML comment for ...
#pragma warning disable 1591 // Disable "CS1591 Missing XML comment for publicly visible type or member ..."
#pragma warning disable 8073 // Disable "CS8073 The result of the expression is always 'false' since a value of type 'T' is never equal to 'null' of type 'T?'"

namespace SistemaInfo.MicroServices.Rest.Ciot.ApiClient
{
    using System = global::System;

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v11.0.0.0))")]
    public partial class AnttClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Ciot/Api";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public AnttClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(CreateSerializerSettings);
        }
    
        private Newtonsoft.Json.JsonSerializerSettings CreateSerializerSettings()
        {
            var settings = new Newtonsoft.Json.JsonSerializerSettings();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }
    
        public string BaseUrl
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
    
    
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
        /// <summary>Consulta os tipos de cargas válidas para a ANTT realizar uma declaração de CIOT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarTiposCargaResponse> ConsultarTiposCargaAsync(string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarTiposCargaAsync(x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consulta os tipos de cargas válidas para a ANTT realizar uma declaração de CIOT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarTiposCargaResponse ConsultarTiposCarga(string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarTiposCargaAsync(x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Consulta os tipos de cargas válidas para a ANTT realizar uma declaração de CIOT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<ConsultarTiposCargaResponse> ConsultarTiposCargaAsync(string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/ConsultarTiposCarga");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ConsultarTiposCargaResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar dados do transportador na ANTT.
        /// Por este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSituacaoTransportadorReponse> ConsultarSituacaoTransportadorAsync(ConsultarSituacaoTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarSituacaoTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar dados do transportador na ANTT.
        /// Por este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarSituacaoTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Consultar dados do transportador na ANTT.
        /// Por este método é possível verificar a data de validade e situação RNTRC, tipo do transportador (TAC, ETC ou CTC [Cooperativa]) e se o mesmo é equiparado a TAC (Obrigatório declarar CIOT)</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<ConsultarSituacaoTransportadorReponse> ConsultarSituacaoTransportadorAsync(ConsultarSituacaoTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/ConsultarSituacaoTransportador");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ConsultarSituacaoTransportadorReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarFrotaTransportadorReponse> ConsultarFrotaTransportadorAsync(ConsultarFrotaTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarFrotaTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarFrotaTransportadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Consultar na ANTT se a placa está registrada para o proprietário com CNPJ e RNTRC indicado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<ConsultarFrotaTransportadorReponse> ConsultarFrotaTransportadorAsync(ConsultarFrotaTransportadorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/ConsultarFrotaTransportador");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ConsultarFrotaTransportadorReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Registrar operação de transporte na ANTT.
        /// Em caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<DeclararOperacaoTransporteReponse> DeclararOperacaoTransporteAsync(DeclararOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return DeclararOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Registrar operação de transporte na ANTT.
        /// Em caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public DeclararOperacaoTransporteReponse DeclararOperacaoTransporte(DeclararOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await DeclararOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Registrar operação de transporte na ANTT.
        /// Em caso de indisponibilidade da ANTT, a operação é efetivada, gerando um número de CIOT válido, e os 4 ultimos dígitos (verificadores) constituidos por XXXX.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<DeclararOperacaoTransporteReponse> DeclararOperacaoTransporteAsync(DeclararOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/DeclararOperacaoTransporte");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<DeclararOperacaoTransporteReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CancelarOperacaoTransporteReponse> CancelarOperacaoTransporteAsync(CancelarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CancelarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CancelarOperacaoTransporteReponse CancelarOperacaoTransporte(CancelarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CancelarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Cancelar operação de transporte na ANTT. Operações encerradas ou consultadas pela ANTT não permitem o cancelamento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<CancelarOperacaoTransporteReponse> CancelarOperacaoTransporteAsync(CancelarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/CancelarOperacaoTransporte");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<CancelarOperacaoTransporteReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<RetificarOperacaoTransporteReponse> RetificarOperacaoTransporteAsync(RetificarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return RetificarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public RetificarOperacaoTransporteReponse RetificarOperacaoTransporte(RetificarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await RetificarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Retificar operação de transporte na ANTT. Operações canceladas ou consultadas pela ANTT não permitem a retificação.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<RetificarOperacaoTransporteReponse> RetificarOperacaoTransporteAsync(RetificarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/RetificarOperacaoTransporte");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<RetificarOperacaoTransporteReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Encerrar a operação de transporte na ANTT.
        /// Operações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.
        /// Operações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.
        /// Operações canceladas não permitem o encerramento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<EncerrarOperacaoTransporteReponse> EncerrarOperacaoTransporteAsync(EncerrarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EncerrarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Encerrar a operação de transporte na ANTT.
        /// Operações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.
        /// Operações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.
        /// Operações canceladas não permitem o encerramento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public EncerrarOperacaoTransporteReponse EncerrarOperacaoTransporte(EncerrarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EncerrarOperacaoTransporteAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Encerrar a operação de transporte na ANTT.
        /// Operações do tipo padrão não possuem esta obrigação, pois são automaticamente encerradas pela ANTT ao atigir a data prevista de fim do frete.
        /// Operações do tipo contrato de TAC/Agregado são obrigadas a ser encerradas pelo embarcador, caso contrário o proprietário TAC/Agregado ficará impossibilidade de declarar CIOT's para outros emcarcadores, e ao atigir 60 dias sem encerrar o contrato, o embarcador ficará impossibilidade pela ANTT de registrar novos contrato de TAC/Agregado.
        /// Operações canceladas não permitem o encerramento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<EncerrarOperacaoTransporteReponse> EncerrarOperacaoTransporteAsync(EncerrarOperacaoTransporteRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/EncerrarOperacaoTransporte");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<EncerrarOperacaoTransporteReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Reenviar ciot que está em contingencia a ANTT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<DeclararOperacaoTransporteReponse>> ReenviarContingenciaAsync(string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ReenviarContingenciaAsync(x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Reenviar ciot que está em contingencia a ANTT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<DeclararOperacaoTransporteReponse> ReenviarContingencia(string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ReenviarContingenciaAsync(x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Reenviar ciot que está em contingencia a ANTT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<DeclararOperacaoTransporteReponse>> ReenviarContingenciaAsync(string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Antt/ReenviarContingencia");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<System.Collections.Generic.List<DeclararOperacaoTransporteReponse>>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }
    
            public T Object { get; }
    
            public string Text { get; }
        }
    
        public bool ReadResponseAsString { get; set; }
        
        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }
        
            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new SwaggerException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    using (var streamReader = new System.IO.StreamReader(responseStream))
                    using (var jsonTextReader = new Newtonsoft.Json.JsonTextReader(streamReader))
                    {
                        var serializer = Newtonsoft.Json.JsonSerializer.Create(JsonSerializerSettings);
                        var typedBody = serializer.Deserialize<T>(jsonTextReader);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new SwaggerException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }
        
            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }
        
                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v11.0.0.0))")]
    public partial class CiotClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Ciot/Api";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public CiotClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(CreateSerializerSettings);
        }
    
        private Newtonsoft.Json.JsonSerializerSettings CreateSerializerSettings()
        {
            var settings = new Newtonsoft.Json.JsonSerializerSettings();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }
    
        public string BaseUrl
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
    
    
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
        /// <summary>Atulizar dados CIOT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<AtualizarCiotResponse> AtualizarAsync(AtualizarCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return AtualizarAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Atulizar dados CIOT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public AtualizarCiotResponse Atualizar(AtualizarCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await AtualizarAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Atulizar dados CIOT</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<AtualizarCiotResponse> AtualizarAsync(AtualizarCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Ciot/Atualizar");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("PATCH");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<AtualizarCiotResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }
    
            public T Object { get; }
    
            public string Text { get; }
        }
    
        public bool ReadResponseAsString { get; set; }
        
        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }
        
            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new SwaggerException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    using (var streamReader = new System.IO.StreamReader(responseStream))
                    using (var jsonTextReader = new Newtonsoft.Json.JsonTextReader(streamReader))
                    {
                        var serializer = Newtonsoft.Json.JsonSerializer.Create(JsonSerializerSettings);
                        var typedBody = serializer.Deserialize<T>(jsonTextReader);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new SwaggerException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }
        
            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }
        
                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v11.0.0.0))")]
    public partial class ConsultasClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Ciot/Api";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public ConsultasClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(CreateSerializerSettings);
        }
    
        private Newtonsoft.Json.JsonSerializerSettings CreateSerializerSettings()
        {
            var settings = new Newtonsoft.Json.JsonSerializerSettings();
            UpdateJsonSerializerSettings(settings);
            return settings;
        }
    
        public string BaseUrl
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
    
    
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
        /// <summary>Consultar situação da declaração de transporte na base de dados do meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSituacaoCiotReponse> SituacaoCiotAsync(ConsultarSituacaoCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return SituacaoCiotAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar situação da declaração de transporte na base de dados do meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSituacaoCiotReponse SituacaoCiot(ConsultarSituacaoCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await SituacaoCiotAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Consultar situação da declaração de transporte na base de dados do meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<ConsultarSituacaoCiotReponse> SituacaoCiotAsync(ConsultarSituacaoCiotRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Consultas/SituacaoCiot");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ConsultarSituacaoCiotReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarOperacaoTacAgregadoReponse> EncerramentosPendentesAsync(ConsultarOperacaoTacAgregadoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EncerramentosPendentesAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarOperacaoTacAgregadoReponse EncerramentosPendentes(ConsultarOperacaoTacAgregadoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EncerramentosPendentesAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Consultar contratos de TAC/Agregados pendentes de encerramento no meio homologado</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<ConsultarOperacaoTacAgregadoReponse> EncerramentosPendentesAsync(ConsultarOperacaoTacAgregadoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Consultas/EncerramentosPendentes");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ConsultarOperacaoTacAgregadoReponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar ciots</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarListaCiotsResponse> ConsultarCiotsAsync(ConsultarListaCiotsRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarCiotsAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar ciots</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarListaCiotsResponse ConsultarCiots(ConsultarListaCiotsRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarCiotsAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        /// <summary>Consultar ciots</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public async System.Threading.Tasks.Task<ConsultarListaCiotsResponse> ConsultarCiotsAsync(ConsultarListaCiotsRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Consultas/ConsultarCiots");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            var disposeClient_ = true;
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
    
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
    
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    var disposeResponse_ = true;
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = (int)response_.StatusCode;
                        if (status_ == 200)
                        {
                            var objectResponse_ = await ReadObjectResponseAsync<ConsultarListaCiotsResponse>(response_, headers_, cancellationToken).ConfigureAwait(false);
                            if (objectResponse_.Object == null)
                            {
                                throw new SwaggerException("Response was null which was not expected.", status_, objectResponse_.Text, headers_, null);
                            }
                            return objectResponse_.Object;
                        }
                        else
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false);
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + status_ + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (disposeResponse_)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (disposeClient_)
                    client_.Dispose();
            }
        }
    
        protected struct ObjectResponseResult<T>
        {
            public ObjectResponseResult(T responseObject, string responseText)
            {
                this.Object = responseObject;
                this.Text = responseText;
            }
    
            public T Object { get; }
    
            public string Text { get; }
        }
    
        public bool ReadResponseAsString { get; set; }
        
        protected virtual async System.Threading.Tasks.Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(System.Net.Http.HttpResponseMessage response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Threading.CancellationToken cancellationToken)
        {
            if (response == null || response.Content == null)
            {
                return new ObjectResponseResult<T>(default(T), string.Empty);
            }
        
            if (ReadResponseAsString)
            {
                var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    var typedBody = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(responseText, JsonSerializerSettings);
                    return new ObjectResponseResult<T>(typedBody, responseText);
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
                    throw new SwaggerException(message, (int)response.StatusCode, responseText, headers, exception);
                }
            }
            else
            {
                try
                {
                    using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
                    using (var streamReader = new System.IO.StreamReader(responseStream))
                    using (var jsonTextReader = new Newtonsoft.Json.JsonTextReader(streamReader))
                    {
                        var serializer = Newtonsoft.Json.JsonSerializer.Create(JsonSerializerSettings);
                        var typedBody = serializer.Deserialize<T>(jsonTextReader);
                        return new ObjectResponseResult<T>(typedBody, string.Empty);
                    }
                }
                catch (Newtonsoft.Json.JsonException exception)
                {
                    var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
                    throw new SwaggerException(message, (int)response.StatusCode, string.Empty, headers, exception);
                }
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value == null)
            {
                return "";
            }
        
            if (value is System.Enum)
            {
                var name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value != null ? attribute.Value : name;
                        }
                    }
        
                    var converted = System.Convert.ToString(System.Convert.ChangeType(value, System.Enum.GetUnderlyingType(value.GetType()), cultureInfo));
                    return converted == null ? string.Empty : converted;
                }
            }
            else if (value is bool) 
            {
                return System.Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            var result = System.Convert.ToString(value, cultureInfo);
            return result == null ? "" : result;
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarTiposCargaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private System.Collections.ObjectModel.ObservableCollection<DadosTipoCargaResponse> _tipos;
        private bool? _falhaComunicacaoAntt;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<DadosTipoCargaResponse> Tipos
        {
            get { return _tipos; }
            set
            {
                if (_tipos != value)
                {
                    _tipos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("falhaComunicacaoAntt", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? FalhaComunicacaoAntt
        {
            get { return _falhaComunicacaoAntt; }
            set
            {
                if (_falhaComunicacaoAntt != value)
                {
                    _falhaComunicacaoAntt = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarTiposCargaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarTiposCargaResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ExcecaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ExcecaoResponseTipo _tipo;
        private string _codigo;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ExcecaoResponseTipo Tipo
        {
            get { return _tipo; }
            set
            {
                if (_tipo != value)
                {
                    _tipo = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Codigo
        {
            get { return _codigo; }
            set
            {
                if (_codigo != value)
                {
                    _codigo = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set
            {
                if (_mensagem != value)
                {
                    _mensagem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ExcecaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ExcecaoResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class DadosTipoCargaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _codigo;
        private string _descricao;
    
        [Newtonsoft.Json.JsonProperty("codigo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Codigo
        {
            get { return _codigo; }
            set
            {
                if (_codigo != value)
                {
                    _codigo = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set
            {
                if (_descricao != value)
                {
                    _descricao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static DadosTipoCargaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DadosTipoCargaResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarSituacaoTransportadorRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjInteressado;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjInteressado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjInteressado
        {
            get { return _cpfCnpjInteressado; }
            set
            {
                if (_cpfCnpjInteressado != value)
                {
                    _cpfCnpjInteressado = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarSituacaoTransportadorRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoTransportadorRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarSituacaoTransportadorReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
        private string _nomeRazaoSocialTransportador;
        private bool? _rntrcAtivo;
        private System.DateTime? _dataValidadeRNTRC;
        private string _tipoTransportador;
        private bool? _equiparadoTAC;
        private bool? _falhaComunicacaoAntt;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocialTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocialTransportador
        {
            get { return _nomeRazaoSocialTransportador; }
            set
            {
                if (_nomeRazaoSocialTransportador != value)
                {
                    _nomeRazaoSocialTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcAtivo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? RntrcAtivo
        {
            get { return _rntrcAtivo; }
            set
            {
                if (_rntrcAtivo != value)
                {
                    _rntrcAtivo = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataValidadeRNTRC", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataValidadeRNTRC
        {
            get { return _dataValidadeRNTRC; }
            set
            {
                if (_dataValidadeRNTRC != value)
                {
                    _dataValidadeRNTRC = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoTransportador
        {
            get { return _tipoTransportador; }
            set
            {
                if (_tipoTransportador != value)
                {
                    _tipoTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("equiparadoTAC", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? EquiparadoTAC
        {
            get { return _equiparadoTAC; }
            set
            {
                if (_equiparadoTAC != value)
                {
                    _equiparadoTAC = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("falhaComunicacaoAntt", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? FalhaComunicacaoAntt
        {
            get { return _falhaComunicacaoAntt; }
            set
            {
                if (_falhaComunicacaoAntt != value)
                {
                    _falhaComunicacaoAntt = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarSituacaoTransportadorReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoTransportadorReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarFrotaTransportadorRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjInteressado;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
        private System.Collections.ObjectModel.ObservableCollection<string> _placa;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjInteressado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjInteressado
        {
            get { return _cpfCnpjInteressado; }
            set
            {
                if (_cpfCnpjInteressado != value)
                {
                    _cpfCnpjInteressado = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<string> Placa
        {
            get { return _placa; }
            set
            {
                if (_placa != value)
                {
                    _placa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarFrotaTransportadorRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarFrotaTransportadorRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarFrotaTransportadorReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _cpfCnpjTransportador;
        private string _rntrcTransportador;
        private string _nomeRazaoSocialTransportador;
        private bool? _rntrcAtivo;
        private System.Collections.ObjectModel.ObservableCollection<VeiculoFrotaTransportadorResponse> _veiculoTransportador;
        private bool? _falhaComunicacaoAntt;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcTransportador
        {
            get { return _rntrcTransportador; }
            set
            {
                if (_rntrcTransportador != value)
                {
                    _rntrcTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocialTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocialTransportador
        {
            get { return _nomeRazaoSocialTransportador; }
            set
            {
                if (_nomeRazaoSocialTransportador != value)
                {
                    _nomeRazaoSocialTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcAtivo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? RntrcAtivo
        {
            get { return _rntrcAtivo; }
            set
            {
                if (_rntrcAtivo != value)
                {
                    _rntrcAtivo = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("veiculoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<VeiculoFrotaTransportadorResponse> VeiculoTransportador
        {
            get { return _veiculoTransportador; }
            set
            {
                if (_veiculoTransportador != value)
                {
                    _veiculoTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("falhaComunicacaoAntt", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? FalhaComunicacaoAntt
        {
            get { return _falhaComunicacaoAntt; }
            set
            {
                if (_falhaComunicacaoAntt != value)
                {
                    _falhaComunicacaoAntt = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarFrotaTransportadorReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarFrotaTransportadorReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class VeiculoFrotaTransportadorResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private string _placaVeiculo;
        private bool? _situacaoVeiculoFrotaTransportador;
    
        [Newtonsoft.Json.JsonProperty("placaVeiculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PlacaVeiculo
        {
            get { return _placaVeiculo; }
            set
            {
                if (_placaVeiculo != value)
                {
                    _placaVeiculo = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("situacaoVeiculoFrotaTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? SituacaoVeiculoFrotaTransportador
        {
            get { return _situacaoVeiculoFrotaTransportador; }
            set
            {
                if (_situacaoVeiculoFrotaTransportador != value)
                {
                    _situacaoVeiculoFrotaTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static VeiculoFrotaTransportadorResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VeiculoFrotaTransportadorResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class DeclararOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private FreteRequest _frete;
        private ContratanteRequest _contratante;
        private DestinatarioRequest _destinatario;
        private ConsignatarioRequest _consignatario;
        private RemetenteRequest _remetente;
        private System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> _veiculos;
        private ValoresFreteRequest _valores;
        private PagamentoRequest _pagamento;
        private InformacoesPagamentoRequest _informacoesPagamento;
        private string _ciotAjuste;
    
        [Newtonsoft.Json.JsonProperty("frete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public FreteRequest Frete
        {
            get { return _frete; }
            set
            {
                if (_frete != value)
                {
                    _frete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contratante", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ContratanteRequest Contratante
        {
            get { return _contratante; }
            set
            {
                if (_contratante != value)
                {
                    _contratante = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("destinatario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DestinatarioRequest Destinatario
        {
            get { return _destinatario; }
            set
            {
                if (_destinatario != value)
                {
                    _destinatario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("consignatario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ConsignatarioRequest Consignatario
        {
            get { return _consignatario; }
            set
            {
                if (_consignatario != value)
                {
                    _consignatario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("remetente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public RemetenteRequest Remetente
        {
            get { return _remetente; }
            set
            {
                if (_remetente != value)
                {
                    _remetente = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("veiculos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> Veiculos
        {
            get { return _veiculos; }
            set
            {
                if (_veiculos != value)
                {
                    _veiculos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ValoresFreteRequest Valores
        {
            get { return _valores; }
            set
            {
                if (_valores != value)
                {
                    _valores = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PagamentoRequest Pagamento
        {
            get { return _pagamento; }
            set
            {
                if (_pagamento != value)
                {
                    _pagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("informacoesPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public InformacoesPagamentoRequest InformacoesPagamento
        {
            get { return _informacoesPagamento; }
            set
            {
                if (_informacoesPagamento != value)
                {
                    _informacoesPagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciotAjuste", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CiotAjuste
        {
            get { return _ciotAjuste; }
            set
            {
                if (_ciotAjuste != value)
                {
                    _ciotAjuste = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static DeclararOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DeclararOperacaoTransporteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class FreteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _codigoMunicipioOrigem;
        private int? _codigoMunicipioDestino;
        private string _cepOrigem;
        private string _cepDestino;
        private int? _distanciaPercorrida;
        private int? _codigoTipoCarga;
        private System.DateTime? _dataInicioFrete;
        private System.DateTime? _dataTerminoFrete;
        private string _dadosComplementares;
        private ProprietarioRequest _proprietario;
        private MotoristaRequest _motorista;
        private string _codigoNaturezaCarga;
        private decimal? _pesoCarga;
        private int? _tipoViagem;
        private bool? _subContratacao;
        private string _ciotPrincipal;
        private bool? _altoDesempenho;
        private bool? _destinacaoComercial;
        private FreteRetornoRequest _freteRetorno;
        private string _documentoCliente;
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioOrigem
        {
            get { return _codigoMunicipioOrigem; }
            set
            {
                if (_codigoMunicipioOrigem != value)
                {
                    _codigoMunicipioOrigem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioDestino
        {
            get { return _codigoMunicipioDestino; }
            set
            {
                if (_codigoMunicipioDestino != value)
                {
                    _codigoMunicipioDestino = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cepOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CepOrigem
        {
            get { return _cepOrigem; }
            set
            {
                if (_cepOrigem != value)
                {
                    _cepOrigem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cepDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CepDestino
        {
            get { return _cepDestino; }
            set
            {
                if (_cepDestino != value)
                {
                    _cepDestino = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("distanciaPercorrida", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? DistanciaPercorrida
        {
            get { return _distanciaPercorrida; }
            set
            {
                if (_distanciaPercorrida != value)
                {
                    _distanciaPercorrida = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoTipoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoTipoCarga
        {
            get { return _codigoTipoCarga; }
            set
            {
                if (_codigoTipoCarga != value)
                {
                    _codigoTipoCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioFrete
        {
            get { return _dataInicioFrete; }
            set
            {
                if (_dataInicioFrete != value)
                {
                    _dataInicioFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataTerminoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataTerminoFrete
        {
            get { return _dataTerminoFrete; }
            set
            {
                if (_dataTerminoFrete != value)
                {
                    _dataTerminoFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dadosComplementares", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DadosComplementares
        {
            get { return _dadosComplementares; }
            set
            {
                if (_dadosComplementares != value)
                {
                    _dadosComplementares = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("proprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProprietarioRequest Proprietario
        {
            get { return _proprietario; }
            set
            {
                if (_proprietario != value)
                {
                    _proprietario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motorista", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public MotoristaRequest Motorista
        {
            get { return _motorista; }
            set
            {
                if (_motorista != value)
                {
                    _motorista = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoNaturezaCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoNaturezaCarga
        {
            get { return _codigoNaturezaCarga; }
            set
            {
                if (_codigoNaturezaCarga != value)
                {
                    _codigoNaturezaCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoViagem
        {
            get { return _tipoViagem; }
            set
            {
                if (_tipoViagem != value)
                {
                    _tipoViagem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("subContratacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? SubContratacao
        {
            get { return _subContratacao; }
            set
            {
                if (_subContratacao != value)
                {
                    _subContratacao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciotPrincipal", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CiotPrincipal
        {
            get { return _ciotPrincipal; }
            set
            {
                if (_ciotPrincipal != value)
                {
                    _ciotPrincipal = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("altoDesempenho", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? AltoDesempenho
        {
            get { return _altoDesempenho; }
            set
            {
                if (_altoDesempenho != value)
                {
                    _altoDesempenho = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("destinacaoComercial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? DestinacaoComercial
        {
            get { return _destinacaoComercial; }
            set
            {
                if (_destinacaoComercial != value)
                {
                    _destinacaoComercial = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("freteRetorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public FreteRetornoRequest FreteRetorno
        {
            get { return _freteRetorno; }
            set
            {
                if (_freteRetorno != value)
                {
                    _freteRetorno = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoCliente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoCliente
        {
            get { return _documentoCliente; }
            set
            {
                if (_documentoCliente != value)
                {
                    _documentoCliente = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static FreteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<FreteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ContratanteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _rntrc;
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("rntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rntrc
        {
            get { return _rntrc; }
            set
            {
                if (_rntrc != value)
                {
                    _rntrc = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set
            {
                if (_endereco != value)
                {
                    _endereco = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ContratanteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ContratanteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class DestinatarioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set
            {
                if (_endereco != value)
                {
                    _endereco = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static DestinatarioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DestinatarioRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsignatarioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set
            {
                if (_endereco != value)
                {
                    _endereco = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsignatarioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsignatarioRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class RemetenteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set
            {
                if (_endereco != value)
                {
                    _endereco = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static RemetenteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RemetenteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class VeiculoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _placa;
        private string _rntrc;
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Placa
        {
            get { return _placa; }
            set
            {
                if (_placa != value)
                {
                    _placa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rntrc
        {
            get { return _rntrc; }
            set
            {
                if (_rntrc != value)
                {
                    _rntrc = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static VeiculoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VeiculoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ValoresFreteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private decimal? _valorFrete;
        private decimal? _valorFretePago;
        private decimal? _valorCombustivel;
        private decimal? _valorDespesas;
        private decimal? _totalImposto;
        private decimal? _valorIRRF;
        private decimal? _valorINSS;
        private decimal? _valorSESTSENAT;
        private decimal? _totalPegadio;
        private int? _quantidadeTarifas;
        private decimal? _valorTarifas;
    
        [Newtonsoft.Json.JsonProperty("valorFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFrete
        {
            get { return _valorFrete; }
            set
            {
                if (_valorFrete != value)
                {
                    _valorFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFretePago", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFretePago
        {
            get { return _valorFretePago; }
            set
            {
                if (_valorFretePago != value)
                {
                    _valorFretePago = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorCombustivel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorCombustivel
        {
            get { return _valorCombustivel; }
            set
            {
                if (_valorCombustivel != value)
                {
                    _valorCombustivel = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorDespesas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorDespesas
        {
            get { return _valorDespesas; }
            set
            {
                if (_valorDespesas != value)
                {
                    _valorDespesas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalImposto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalImposto
        {
            get { return _totalImposto; }
            set
            {
                if (_totalImposto != value)
                {
                    _totalImposto = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorIRRF", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorIRRF
        {
            get { return _valorIRRF; }
            set
            {
                if (_valorIRRF != value)
                {
                    _valorIRRF = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorINSS", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorINSS
        {
            get { return _valorINSS; }
            set
            {
                if (_valorINSS != value)
                {
                    _valorINSS = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSESTSENAT", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorSESTSENAT
        {
            get { return _valorSESTSENAT; }
            set
            {
                if (_valorSESTSENAT != value)
                {
                    _valorSESTSENAT = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalPegadio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalPegadio
        {
            get { return _totalPegadio; }
            set
            {
                if (_totalPegadio != value)
                {
                    _totalPegadio = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeTarifas
        {
            get { return _quantidadeTarifas; }
            set
            {
                if (_quantidadeTarifas != value)
                {
                    _quantidadeTarifas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTarifas
        {
            get { return _valorTarifas; }
            set
            {
                if (_valorTarifas != value)
                {
                    _valorTarifas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ValoresFreteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ValoresFreteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class PagamentoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _formaPagmento;
        private System.Collections.ObjectModel.ObservableCollection<ParcelaPagamentoRequest> _parcelas;
        private bool? _parcelaUnica;
        private string _infoPagamento;
        private string _bancoPagamento;
        private string _agenciaPagamento;
        private string _contaPagamento;
    
        [Newtonsoft.Json.JsonProperty("formaPagmento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? FormaPagmento
        {
            get { return _formaPagmento; }
            set
            {
                if (_formaPagmento != value)
                {
                    _formaPagmento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parcelas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<ParcelaPagamentoRequest> Parcelas
        {
            get { return _parcelas; }
            set
            {
                if (_parcelas != value)
                {
                    _parcelas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parcelaUnica", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ParcelaUnica
        {
            get { return _parcelaUnica; }
            set
            {
                if (_parcelaUnica != value)
                {
                    _parcelaUnica = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("infoPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoPagamento
        {
            get { return _infoPagamento; }
            set
            {
                if (_infoPagamento != value)
                {
                    _infoPagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bancoPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BancoPagamento
        {
            get { return _bancoPagamento; }
            set
            {
                if (_bancoPagamento != value)
                {
                    _bancoPagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agenciaPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AgenciaPagamento
        {
            get { return _agenciaPagamento; }
            set
            {
                if (_agenciaPagamento != value)
                {
                    _agenciaPagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contaPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContaPagamento
        {
            get { return _contaPagamento; }
            set
            {
                if (_contaPagamento != value)
                {
                    _contaPagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static PagamentoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PagamentoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class InformacoesPagamentoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private InformacoesPagamentoRequestFormaPagamento? _formaPagamento;
        private DadosBancariosRequest _dadosBancarios;
    
        [Newtonsoft.Json.JsonProperty("formaPagamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public InformacoesPagamentoRequestFormaPagamento? FormaPagamento
        {
            get { return _formaPagamento; }
            set
            {
                if (_formaPagamento != value)
                {
                    _formaPagamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dadosBancarios", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DadosBancariosRequest DadosBancarios
        {
            get { return _dadosBancarios; }
            set
            {
                if (_dadosBancarios != value)
                {
                    _dadosBancarios = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static InformacoesPagamentoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<InformacoesPagamentoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ProprietarioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _rntrc;
        private string _nomeRazaoSocial;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private PessoaEnderecoRequest _endereco;
        private string _tipoPessoa;
    
        [Newtonsoft.Json.JsonProperty("rntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rntrc
        {
            get { return _rntrc; }
            set
            {
                if (_rntrc != value)
                {
                    _rntrc = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRazaoSocial", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRazaoSocial
        {
            get { return _nomeRazaoSocial; }
            set
            {
                if (_nomeRazaoSocial != value)
                {
                    _nomeRazaoSocial = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEnderecoRequest Endereco
        {
            get { return _endereco; }
            set
            {
                if (_endereco != value)
                {
                    _endereco = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ProprietarioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProprietarioRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class MotoristaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _cpfCnpj;
        private string _numeroCNH;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set
            {
                if (_nome != value)
                {
                    _nome = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroCNH", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NumeroCNH
        {
            get { return _numeroCNH; }
            set
            {
                if (_numeroCNH != value)
                {
                    _numeroCNH = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static MotoristaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<MotoristaRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class FreteRetornoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cepRetorno;
        private int? _distanciaRetorno;
    
        [Newtonsoft.Json.JsonProperty("cepRetorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CepRetorno
        {
            get { return _cepRetorno; }
            set
            {
                if (_cepRetorno != value)
                {
                    _cepRetorno = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("distanciaRetorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? DistanciaRetorno
        {
            get { return _distanciaRetorno; }
            set
            {
                if (_distanciaRetorno != value)
                {
                    _distanciaRetorno = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static FreteRetornoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<FreteRetornoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class PessoaEnderecoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cep;
        private int? _codigoMunicipio;
        private string _logradouro;
        private string _numero;
        private string _complemento;
        private string _bairro;
        private string _telefone;
        private string _email;
    
        [Newtonsoft.Json.JsonProperty("cep", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Cep
        {
            get { return _cep; }
            set
            {
                if (_cep != value)
                {
                    _cep = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipio
        {
            get { return _codigoMunicipio; }
            set
            {
                if (_codigoMunicipio != value)
                {
                    _codigoMunicipio = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("logradouro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Logradouro
        {
            get { return _logradouro; }
            set
            {
                if (_logradouro != value)
                {
                    _logradouro = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numero", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Numero
        {
            get { return _numero; }
            set
            {
                if (_numero != value)
                {
                    _numero = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("complemento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Complemento
        {
            get { return _complemento; }
            set
            {
                if (_complemento != value)
                {
                    _complemento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bairro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Bairro
        {
            get { return _bairro; }
            set
            {
                if (_bairro != value)
                {
                    _bairro = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("telefone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Telefone
        {
            get { return _telefone; }
            set
            {
                if (_telefone != value)
                {
                    _telefone = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("email", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Email
        {
            get { return _email; }
            set
            {
                if (_email != value)
                {
                    _email = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static PessoaEnderecoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaEnderecoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ParcelaPagamentoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _codigoParcela;
        private decimal? _valorParcela;
        private System.DateTime? _vencimento;
    
        [Newtonsoft.Json.JsonProperty("codigoParcela", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoParcela
        {
            get { return _codigoParcela; }
            set
            {
                if (_codigoParcela != value)
                {
                    _codigoParcela = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorParcela", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorParcela
        {
            get { return _valorParcela; }
            set
            {
                if (_valorParcela != value)
                {
                    _valorParcela = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("vencimento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? Vencimento
        {
            get { return _vencimento; }
            set
            {
                if (_vencimento != value)
                {
                    _vencimento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ParcelaPagamentoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ParcelaPagamentoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class DadosBancariosRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjConta;
        private string _codigoBACEN;
        private string _agencia;
        private string _conta;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjConta
        {
            get { return _cpfCnpjConta; }
            set
            {
                if (_cpfCnpjConta != value)
                {
                    _cpfCnpjConta = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoBACEN", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoBACEN
        {
            get { return _codigoBACEN; }
            set
            {
                if (_codigoBACEN != value)
                {
                    _codigoBACEN = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Agencia
        {
            get { return _agencia; }
            set
            {
                if (_agencia != value)
                {
                    _agencia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("conta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Conta
        {
            get { return _conta; }
            set
            {
                if (_conta != value)
                {
                    _conta = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static DadosBancariosRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DadosBancariosRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class DeclararOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private string _codigoVerificador;
        private string _avisoTransportador;
        private bool? _emContingencia;
        private string _protocoloErro;
        private string _senhaAlteracao;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoVerificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoVerificador
        {
            get { return _codigoVerificador; }
            set
            {
                if (_codigoVerificador != value)
                {
                    _codigoVerificador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("avisoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AvisoTransportador
        {
            get { return _avisoTransportador; }
            set
            {
                if (_avisoTransportador != value)
                {
                    _avisoTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("emContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? EmContingencia
        {
            get { return _emContingencia; }
            set
            {
                if (_emContingencia != value)
                {
                    _emContingencia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloErro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProtocoloErro
        {
            get { return _protocoloErro; }
            set
            {
                if (_protocoloErro != value)
                {
                    _protocoloErro = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static DeclararOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DeclararOperacaoTransporteReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class CancelarOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _motivoCancelamento;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motivoCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotivoCancelamento
        {
            get { return _motivoCancelamento; }
            set
            {
                if (_motivoCancelamento != value)
                {
                    _motivoCancelamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static CancelarOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarOperacaoTransporteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class CancelarOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private System.DateTime? _dataCancelamento;
        private string _protocoloCancelamento;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCancelamento
        {
            get { return _dataCancelamento; }
            set
            {
                if (_dataCancelamento != value)
                {
                    _dataCancelamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProtocoloCancelamento
        {
            get { return _protocoloCancelamento; }
            set
            {
                if (_protocoloCancelamento != value)
                {
                    _protocoloCancelamento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static CancelarOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarOperacaoTransporteReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class RetificarOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _senhaAlteracao;
        private System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> _veiculos;
        private int? _quantidadeTarifas;
        private decimal? _valorTarifas;
        private decimal? _valorCombustivel;
        private decimal? _valorPedagio;
        private string _documentoCliente;
        private decimal? _valorFrete;
        private decimal? _valorFretePago;
        private string _codigoNaturezaCarga;
        private decimal? _pesoCarga;
        private System.DateTime? _dataInicioViagem;
        private System.DateTime? _dataFimViagem;
        private int? _codigoMunicipioOrigem;
        private int? _codigoMunicipioDestino;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("veiculos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<VeiculoRequest> Veiculos
        {
            get { return _veiculos; }
            set
            {
                if (_veiculos != value)
                {
                    _veiculos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeTarifas
        {
            get { return _quantidadeTarifas; }
            set
            {
                if (_quantidadeTarifas != value)
                {
                    _quantidadeTarifas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTarifas
        {
            get { return _valorTarifas; }
            set
            {
                if (_valorTarifas != value)
                {
                    _valorTarifas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorCombustivel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorCombustivel
        {
            get { return _valorCombustivel; }
            set
            {
                if (_valorCombustivel != value)
                {
                    _valorCombustivel = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorPedagio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorPedagio
        {
            get { return _valorPedagio; }
            set
            {
                if (_valorPedagio != value)
                {
                    _valorPedagio = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoCliente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoCliente
        {
            get { return _documentoCliente; }
            set
            {
                if (_documentoCliente != value)
                {
                    _documentoCliente = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFrete
        {
            get { return _valorFrete; }
            set
            {
                if (_valorFrete != value)
                {
                    _valorFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFretePago", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFretePago
        {
            get { return _valorFretePago; }
            set
            {
                if (_valorFretePago != value)
                {
                    _valorFretePago = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoNaturezaCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoNaturezaCarga
        {
            get { return _codigoNaturezaCarga; }
            set
            {
                if (_codigoNaturezaCarga != value)
                {
                    _codigoNaturezaCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioViagem
        {
            get { return _dataInicioViagem; }
            set
            {
                if (_dataInicioViagem != value)
                {
                    _dataInicioViagem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimViagem
        {
            get { return _dataFimViagem; }
            set
            {
                if (_dataFimViagem != value)
                {
                    _dataFimViagem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioOrigem
        {
            get { return _codigoMunicipioOrigem; }
            set
            {
                if (_codigoMunicipioOrigem != value)
                {
                    _codigoMunicipioOrigem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioDestino
        {
            get { return _codigoMunicipioDestino; }
            set
            {
                if (_codigoMunicipioDestino != value)
                {
                    _codigoMunicipioDestino = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static RetificarOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RetificarOperacaoTransporteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class RetificarOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _codigoRetorno;
        private string _ciot;
        private System.DateTime? _dataRetificacao;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoRetorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoRetorno
        {
            get { return _codigoRetorno; }
            set
            {
                if (_codigoRetorno != value)
                {
                    _codigoRetorno = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataRetificacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRetificacao
        {
            get { return _dataRetificacao; }
            set
            {
                if (_dataRetificacao != value)
                {
                    _dataRetificacao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static RetificarOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RetificarOperacaoTransporteReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class EncerrarOperacaoTransporteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private decimal? _pesoCarga;
        private System.Collections.ObjectModel.ObservableCollection<EncerrarOperacaoTransporteViagemRequest> _viagensOperacaoTransporte;
        private ValoresFreteRequest _valoresEfetivos;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("viagensOperacaoTransporte", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<EncerrarOperacaoTransporteViagemRequest> ViagensOperacaoTransporte
        {
            get { return _viagensOperacaoTransporte; }
            set
            {
                if (_viagensOperacaoTransporte != value)
                {
                    _viagensOperacaoTransporte = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valoresEfetivos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ValoresFreteRequest ValoresEfetivos
        {
            get { return _valoresEfetivos; }
            set
            {
                if (_valoresEfetivos != value)
                {
                    _valoresEfetivos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static EncerrarOperacaoTransporteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class EncerrarOperacaoTransporteViagemRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _codigoMunicipioOrigem;
        private int? _codigoMunicipioDestino;
        private string _cepOrigem;
        private string _cepDestino;
        private string _codigoNaturezaCarga;
        private decimal? _pesoCarga;
        private int? _quantidadeViagens;
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioOrigem
        {
            get { return _codigoMunicipioOrigem; }
            set
            {
                if (_codigoMunicipioOrigem != value)
                {
                    _codigoMunicipioOrigem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoMunicipioDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoMunicipioDestino
        {
            get { return _codigoMunicipioDestino; }
            set
            {
                if (_codigoMunicipioDestino != value)
                {
                    _codigoMunicipioDestino = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cepOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CepOrigem
        {
            get { return _cepOrigem; }
            set
            {
                if (_cepOrigem != value)
                {
                    _cepOrigem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cepDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CepDestino
        {
            get { return _cepDestino; }
            set
            {
                if (_cepDestino != value)
                {
                    _cepDestino = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoNaturezaCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoNaturezaCarga
        {
            get { return _codigoNaturezaCarga; }
            set
            {
                if (_codigoNaturezaCarga != value)
                {
                    _codigoNaturezaCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeViagens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeViagens
        {
            get { return _quantidadeViagens; }
            set
            {
                if (_quantidadeViagens != value)
                {
                    _quantidadeViagens = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static EncerrarOperacaoTransporteViagemRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteViagemRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class EncerrarOperacaoTransporteReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private string _protocoloEncerramento;
        private System.DateTime? _dataEncerramento;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloEncerramento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProtocoloEncerramento
        {
            get { return _protocoloEncerramento; }
            set
            {
                if (_protocoloEncerramento != value)
                {
                    _protocoloEncerramento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataEncerramento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataEncerramento
        {
            get { return _dataEncerramento; }
            set
            {
                if (_dataEncerramento != value)
                {
                    _dataEncerramento = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static EncerrarOperacaoTransporteReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EncerrarOperacaoTransporteReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class AtualizarCiotRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private int? _senhaAlteracao;
        private AtualizarCiotFreteRequest _frete;
        private AtualizarCiotValorRequest _valores;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("frete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public AtualizarCiotFreteRequest Frete
        {
            get { return _frete; }
            set
            {
                if (_frete != value)
                {
                    _frete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public AtualizarCiotValorRequest Valores
        {
            get { return _valores; }
            set
            {
                if (_valores != value)
                {
                    _valores = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static AtualizarCiotRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AtualizarCiotRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class AtualizarCiotFreteRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _documentoCliente;
    
        [Newtonsoft.Json.JsonProperty("documentoCliente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoCliente
        {
            get { return _documentoCliente; }
            set
            {
                if (_documentoCliente != value)
                {
                    _documentoCliente = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static AtualizarCiotFreteRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AtualizarCiotFreteRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class AtualizarCiotValorRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private decimal? _valorIrrf;
        private decimal? _valorInss;
        private decimal? _valorSestSenat;
        private decimal? _valorFrete;
        private decimal? _valorFretePago;
    
        [Newtonsoft.Json.JsonProperty("valorIrrf", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorIrrf
        {
            get { return _valorIrrf; }
            set
            {
                if (_valorIrrf != value)
                {
                    _valorIrrf = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorInss", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorInss
        {
            get { return _valorInss; }
            set
            {
                if (_valorInss != value)
                {
                    _valorInss = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSestSenat", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorSestSenat
        {
            get { return _valorSestSenat; }
            set
            {
                if (_valorSestSenat != value)
                {
                    _valorSestSenat = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFrete
        {
            get { return _valorFrete; }
            set
            {
                if (_valorFrete != value)
                {
                    _valorFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFretePago", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFretePago
        {
            get { return _valorFretePago; }
            set
            {
                if (_valorFretePago != value)
                {
                    _valorFretePago = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static AtualizarCiotValorRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AtualizarCiotValorRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class AtualizarCiotResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static AtualizarCiotResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AtualizarCiotResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarSituacaoCiotRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _senhaAlteracao;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senhaAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SenhaAlteracao
        {
            get { return _senhaAlteracao; }
            set
            {
                if (_senhaAlteracao != value)
                {
                    _senhaAlteracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarSituacaoCiotRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoCiotRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarSituacaoCiotReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private string _ciot;
        private string _codigoVerificador;
        private int? _situacao;
        private bool? _enviadoEmContingencia;
        private ExcecaoResponse _erroContingencia;
        private string _nomeProprietario;
        private string _cpfCnpjProprietario;
        private string _rntrcProprietario;
        private System.DateTime? _dataInicioFrete;
        private System.DateTime? _dataTerminoFrete;
        private decimal? _valorFrete;
        private decimal? _valorEfetivoFrete;
        private decimal? _valorDespesas;
        private decimal? _totalImposto;
        private decimal? _totalPegadio;
        private int? _tipoViagem;
        private bool? _encerrado;
        private string _avisoTransportador;
        private string _situacaoDeclaracao;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoVerificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoVerificador
        {
            get { return _codigoVerificador; }
            set
            {
                if (_codigoVerificador != value)
                {
                    _codigoVerificador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("situacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Situacao
        {
            get { return _situacao; }
            set
            {
                if (_situacao != value)
                {
                    _situacao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("enviadoEmContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? EnviadoEmContingencia
        {
            get { return _enviadoEmContingencia; }
            set
            {
                if (_enviadoEmContingencia != value)
                {
                    _enviadoEmContingencia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("erroContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse ErroContingencia
        {
            get { return _erroContingencia; }
            set
            {
                if (_erroContingencia != value)
                {
                    _erroContingencia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeProprietario
        {
            get { return _nomeProprietario; }
            set
            {
                if (_nomeProprietario != value)
                {
                    _nomeProprietario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjProprietario
        {
            get { return _cpfCnpjProprietario; }
            set
            {
                if (_cpfCnpjProprietario != value)
                {
                    _cpfCnpjProprietario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcProprietario
        {
            get { return _rntrcProprietario; }
            set
            {
                if (_rntrcProprietario != value)
                {
                    _rntrcProprietario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioFrete
        {
            get { return _dataInicioFrete; }
            set
            {
                if (_dataInicioFrete != value)
                {
                    _dataInicioFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataTerminoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataTerminoFrete
        {
            get { return _dataTerminoFrete; }
            set
            {
                if (_dataTerminoFrete != value)
                {
                    _dataTerminoFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorFrete
        {
            get { return _valorFrete; }
            set
            {
                if (_valorFrete != value)
                {
                    _valorFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorEfetivoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorEfetivoFrete
        {
            get { return _valorEfetivoFrete; }
            set
            {
                if (_valorEfetivoFrete != value)
                {
                    _valorEfetivoFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorDespesas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorDespesas
        {
            get { return _valorDespesas; }
            set
            {
                if (_valorDespesas != value)
                {
                    _valorDespesas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalImposto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalImposto
        {
            get { return _totalImposto; }
            set
            {
                if (_totalImposto != value)
                {
                    _totalImposto = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalPegadio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalPegadio
        {
            get { return _totalPegadio; }
            set
            {
                if (_totalPegadio != value)
                {
                    _totalPegadio = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoViagem
        {
            get { return _tipoViagem; }
            set
            {
                if (_tipoViagem != value)
                {
                    _tipoViagem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("encerrado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Encerrado
        {
            get { return _encerrado; }
            set
            {
                if (_encerrado != value)
                {
                    _encerrado = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("avisoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AvisoTransportador
        {
            get { return _avisoTransportador; }
            set
            {
                if (_avisoTransportador != value)
                {
                    _avisoTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("situacaoDeclaracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SituacaoDeclaracao
        {
            get { return _situacaoDeclaracao; }
            set
            {
                if (_situacaoDeclaracao != value)
                {
                    _situacaoDeclaracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarSituacaoCiotReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSituacaoCiotReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarOperacaoTacAgregadoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjContratante;
        private string _cpfCnpjTransportador;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjContratante", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjContratante
        {
            get { return _cpfCnpjContratante; }
            set
            {
                if (_cpfCnpjContratante != value)
                {
                    _cpfCnpjContratante = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjTransportador
        {
            get { return _cpfCnpjTransportador; }
            set
            {
                if (_cpfCnpjTransportador != value)
                {
                    _cpfCnpjTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarOperacaoTacAgregadoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarOperacaoTacAgregadoRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarOperacaoTacAgregadoReponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private System.Collections.ObjectModel.ObservableCollection<ConsultaOperacaoTacAgregadoViagemResponse> _viagensPendentes;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("viagensPendentes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<ConsultaOperacaoTacAgregadoViagemResponse> ViagensPendentes
        {
            get { return _viagensPendentes; }
            set
            {
                if (_viagensPendentes != value)
                {
                    _viagensPendentes = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarOperacaoTacAgregadoReponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarOperacaoTacAgregadoReponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultaOperacaoTacAgregadoViagemResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _codigoVerificador;
        private System.DateTime? _dataTerminoViagem;
        private System.DateTime? _dataDeclaracao;
        private int? _quantidadeTarifas;
        private decimal? _valorTarifas;
        private string _avisoTransportador;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoVerificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoVerificador
        {
            get { return _codigoVerificador; }
            set
            {
                if (_codigoVerificador != value)
                {
                    _codigoVerificador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataTerminoViagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataTerminoViagem
        {
            get { return _dataTerminoViagem; }
            set
            {
                if (_dataTerminoViagem != value)
                {
                    _dataTerminoViagem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDeclaracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDeclaracao
        {
            get { return _dataDeclaracao; }
            set
            {
                if (_dataDeclaracao != value)
                {
                    _dataDeclaracao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeTarifas
        {
            get { return _quantidadeTarifas; }
            set
            {
                if (_quantidadeTarifas != value)
                {
                    _quantidadeTarifas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTarifas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTarifas
        {
            get { return _valorTarifas; }
            set
            {
                if (_valorTarifas != value)
                {
                    _valorTarifas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("avisoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AvisoTransportador
        {
            get { return _avisoTransportador; }
            set
            {
                if (_avisoTransportador != value)
                {
                    _avisoTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultaOperacaoTacAgregadoViagemResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaOperacaoTacAgregadoViagemResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarListaCiotsRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpjContratante;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
        private System.Collections.ObjectModel.ObservableCollection<CiotStruct> _ciot;
        private System.Collections.ObjectModel.ObservableCollection<Tipos> _tipos;
        private string _cnpjProprietario;
        private string _rntrcProprietario;
        private string _cpfMotorista;
        private string _placa;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpjContratante", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpjContratante
        {
            get { return _cpfCnpjContratante; }
            set
            {
                if (_cpfCnpjContratante != value)
                {
                    _cpfCnpjContratante = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set
            {
                if (_dataFim != value)
                {
                    _dataFim = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<CiotStruct> Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore, ItemConverterType = typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public System.Collections.ObjectModel.ObservableCollection<Tipos> Tipos
        {
            get { return _tipos; }
            set
            {
                if (_tipos != value)
                {
                    _tipos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cnpjProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CnpjProprietario
        {
            get { return _cnpjProprietario; }
            set
            {
                if (_cnpjProprietario != value)
                {
                    _cnpjProprietario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rntrcProprietario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string RntrcProprietario
        {
            get { return _rntrcProprietario; }
            set
            {
                if (_rntrcProprietario != value)
                {
                    _rntrcProprietario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfMotorista", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfMotorista
        {
            get { return _cpfMotorista; }
            set
            {
                if (_cpfMotorista != value)
                {
                    _cpfMotorista = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Placa
        {
            get { return _placa; }
            set
            {
                if (_placa != value)
                {
                    _placa = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarListaCiotsRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarListaCiotsRequest>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class CiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private string _ciot;
        private string _verificador;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("verificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Verificador
        {
            get { return _verificador; }
            set
            {
                if (_verificador != value)
                {
                    _verificador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static CiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultarListaCiotsResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private ExcecaoResponse _excecao;
        private int? _quantidadeCiots;
        private int? _ciotsAbertos;
        private int? _ciotsEmContingencia;
        private System.Collections.ObjectModel.ObservableCollection<ConsultaCiot> _ciots;
        private string _exceptionMessage;
        private string _exceptionStackTrace;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set
            {
                if (_sucesso != value)
                {
                    _sucesso = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("excecao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ExcecaoResponse Excecao
        {
            get { return _excecao; }
            set
            {
                if (_excecao != value)
                {
                    _excecao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeCiots", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeCiots
        {
            get { return _quantidadeCiots; }
            set
            {
                if (_quantidadeCiots != value)
                {
                    _quantidadeCiots = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciotsAbertos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CiotsAbertos
        {
            get { return _ciotsAbertos; }
            set
            {
                if (_ciotsAbertos != value)
                {
                    _ciotsAbertos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciotsEmContingencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CiotsEmContingencia
        {
            get { return _ciotsEmContingencia; }
            set
            {
                if (_ciotsEmContingencia != value)
                {
                    _ciotsEmContingencia = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ciots", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<ConsultaCiot> Ciots
        {
            get { return _ciots; }
            set
            {
                if (_ciots != value)
                {
                    _ciots = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionMessage
        {
            get { return _exceptionMessage; }
            set
            {
                if (_exceptionMessage != value)
                {
                    _exceptionMessage = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exceptionStackTrace", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ExceptionStackTrace
        {
            get { return _exceptionStackTrace; }
            set
            {
                if (_exceptionStackTrace != value)
                {
                    _exceptionStackTrace = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultarListaCiotsResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarListaCiotsResponse>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ConsultaCiot : System.ComponentModel.INotifyPropertyChanged
    {
        private CiotStruct _ciot;
        private System.DateTime? _dataEmissao;
        private System.DateTime? _dataInicioFrete;
        private System.DateTime? _dataFimFrete;
        private ConsultaCiotStatus? _status;
        private bool? _encerrado;
        private string _avisoAoTransportador;
        private ValoresCiotStruct _valores;
        private ModalCiotStruct _modal;
        private RotaCiotStruct _rota;
        private ClientesCiotStruct _clientes;
    
        [Newtonsoft.Json.JsonProperty("ciot", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CiotStruct Ciot
        {
            get { return _ciot; }
            set
            {
                if (_ciot != value)
                {
                    _ciot = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataEmissao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataEmissao
        {
            get { return _dataEmissao; }
            set
            {
                if (_dataEmissao != value)
                {
                    _dataEmissao = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioFrete
        {
            get { return _dataInicioFrete; }
            set
            {
                if (_dataInicioFrete != value)
                {
                    _dataInicioFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimFrete
        {
            get { return _dataFimFrete; }
            set
            {
                if (_dataFimFrete != value)
                {
                    _dataFimFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaCiotStatus? Status
        {
            get { return _status; }
            set
            {
                if (_status != value)
                {
                    _status = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("encerrado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Encerrado
        {
            get { return _encerrado; }
            set
            {
                if (_encerrado != value)
                {
                    _encerrado = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("avisoAoTransportador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AvisoAoTransportador
        {
            get { return _avisoAoTransportador; }
            set
            {
                if (_avisoAoTransportador != value)
                {
                    _avisoAoTransportador = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ValoresCiotStruct Valores
        {
            get { return _valores; }
            set
            {
                if (_valores != value)
                {
                    _valores = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("modal", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ModalCiotStruct Modal
        {
            get { return _modal; }
            set
            {
                if (_modal != value)
                {
                    _modal = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rota", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public RotaCiotStruct Rota
        {
            get { return _rota; }
            set
            {
                if (_rota != value)
                {
                    _rota = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("clientes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ClientesCiotStruct Clientes
        {
            get { return _clientes; }
            set
            {
                if (_clientes != value)
                {
                    _clientes = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ConsultaCiot FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCiot>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ValoresCiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private decimal? _valorTotalFrete;
        private decimal? _valorTotalPedagio;
        private decimal? _valorTotalImpostos;
        private decimal? _pesoCarga;
    
        [Newtonsoft.Json.JsonProperty("valorTotalFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTotalFrete
        {
            get { return _valorTotalFrete; }
            set
            {
                if (_valorTotalFrete != value)
                {
                    _valorTotalFrete = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTotalPedagio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTotalPedagio
        {
            get { return _valorTotalPedagio; }
            set
            {
                if (_valorTotalPedagio != value)
                {
                    _valorTotalPedagio = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorTotalImpostos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorTotalImpostos
        {
            get { return _valorTotalImpostos; }
            set
            {
                if (_valorTotalImpostos != value)
                {
                    _valorTotalImpostos = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pesoCarga", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PesoCarga
        {
            get { return _pesoCarga; }
            set
            {
                if (_pesoCarga != value)
                {
                    _pesoCarga = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ValoresCiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ValoresCiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ModalCiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private string _motoristaCpf;
        private string _motoristaNome;
        private string _proprietarioCnpjCpf;
        private string _proprietarioNome;
        private string _proprietarioRntrc;
        private System.Collections.ObjectModel.ObservableCollection<string> _placas;
    
        [Newtonsoft.Json.JsonProperty("motoristaCpf", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotoristaCpf
        {
            get { return _motoristaCpf; }
            set
            {
                if (_motoristaCpf != value)
                {
                    _motoristaCpf = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motoristaNome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotoristaNome
        {
            get { return _motoristaNome; }
            set
            {
                if (_motoristaNome != value)
                {
                    _motoristaNome = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("proprietarioCnpjCpf", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProprietarioCnpjCpf
        {
            get { return _proprietarioCnpjCpf; }
            set
            {
                if (_proprietarioCnpjCpf != value)
                {
                    _proprietarioCnpjCpf = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("proprietarioNome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProprietarioNome
        {
            get { return _proprietarioNome; }
            set
            {
                if (_proprietarioNome != value)
                {
                    _proprietarioNome = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("proprietarioRntrc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ProprietarioRntrc
        {
            get { return _proprietarioRntrc; }
            set
            {
                if (_proprietarioRntrc != value)
                {
                    _proprietarioRntrc = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("placas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<string> Placas
        {
            get { return _placas; }
            set
            {
                if (_placas != value)
                {
                    _placas = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ModalCiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ModalCiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class RotaCiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private LocalCiotStruct _origem;
        private LocalCiotStruct _destino;
    
        [Newtonsoft.Json.JsonProperty("origem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public LocalCiotStruct Origem
        {
            get { return _origem; }
            set
            {
                if (_origem != value)
                {
                    _origem = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("destino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public LocalCiotStruct Destino
        {
            get { return _destino; }
            set
            {
                if (_destino != value)
                {
                    _destino = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static RotaCiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<RotaCiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class ClientesCiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private DadosClienteCiotStruct _contratante;
        private DadosClienteCiotStruct _remetente;
        private DadosClienteCiotStruct _destinatario;
        private DadosClienteCiotStruct _consignatario;
    
        [Newtonsoft.Json.JsonProperty("contratante", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DadosClienteCiotStruct Contratante
        {
            get { return _contratante; }
            set
            {
                if (_contratante != value)
                {
                    _contratante = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("remetente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DadosClienteCiotStruct Remetente
        {
            get { return _remetente; }
            set
            {
                if (_remetente != value)
                {
                    _remetente = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("destinatario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DadosClienteCiotStruct Destinatario
        {
            get { return _destinatario; }
            set
            {
                if (_destinatario != value)
                {
                    _destinatario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("consignatario", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public DadosClienteCiotStruct Consignatario
        {
            get { return _consignatario; }
            set
            {
                if (_consignatario != value)
                {
                    _consignatario = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static ClientesCiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ClientesCiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class LocalCiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cidade;
        private string _estado;
        private string _pais;
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Cidade
        {
            get { return _cidade; }
            set
            {
                if (_cidade != value)
                {
                    _cidade = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Estado
        {
            get { return _estado; }
            set
            {
                if (_estado != value)
                {
                    _estado = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Pais
        {
            get { return _pais; }
            set
            {
                if (_pais != value)
                {
                    _pais = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static LocalCiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<LocalCiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public partial class DadosClienteCiotStruct : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cpfCnpj;
        private string _nome;
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set
            {
                if (_nome != value)
                {
                    _nome = value;
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public static DadosClienteCiotStruct FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DadosClienteCiotStruct>(data, new Newtonsoft.Json.JsonSerializerSettings());
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public enum ExcecaoResponseTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = @"Autorizacao")]
        Autorizacao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Validacao")]
        Validacao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Negocio")]
        Negocio = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Aplicacao")]
        Aplicacao = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public enum InformacoesPagamentoRequestFormaPagamento
    {
        [System.Runtime.Serialization.EnumMember(Value = @"Cartao")]
        Cartao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = @"ContaCorrente")]
        ContaCorrente = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = @"ContaPoupanca")]
        ContaPoupanca = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = @"ContaPagamento")]
        ContaPagamento = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Outros")]
        Outros = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public enum Tipos
    {
        [System.Runtime.Serialization.EnumMember(Value = @"Padrao")]
        Padrao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Fracionado")]
        Fracionado = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = @"TacAgregado")]
        TacAgregado = 2,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v11.0.0.0)")]
    public enum ConsultaCiotStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = @"CiotGerado")]
        CiotGerado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Contingencia")]
        Contingencia = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = @"Cancelado")]
        Cancelado = 2,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v11.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException)
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + ((response == null) ? "(null)" : response.Substring(0, response.Length >= 512 ? 512 : response.Length)), innerException)
        {
            StatusCode = statusCode;
            Response = response;
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v11.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.IReadOnlyDictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException)
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

#pragma warning restore 1591
#pragma warning restore 1573
#pragma warning restore  472
#pragma warning restore  114
#pragma warning restore  108
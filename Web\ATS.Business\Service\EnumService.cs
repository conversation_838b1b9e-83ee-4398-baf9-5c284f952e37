﻿using ATS.Domain.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.ComponentModel;

namespace ATS.Domain.Service
{
    public class EnumService : ServiceBase
    {

        public Type GetEnum(string EnumName)
        {

            EnumName = "ATS.Domain.Enum." + EnumName;
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                var type = assembly.GetType(EnumName);

                if (type == null)
                    continue;

                if (type.IsEnum)
                    return type;
            }
            return null;
        }

        public object Consultar(string Enum)
        {
            var tEnum = GetEnum(Enum);
            List<testeOrdenacao> retorno = new List<testeOrdenacao>();
            var uEnum = tEnum != null ? System.Enum.GetUnderlyingType(tEnum) : null;
            if (tEnum == null)
                return new object();
            Array values = System.Enum.GetValues(tEnum);

            var fields = tEnum.GetFields();

            foreach (var field in tEnum.GetFields())
            {
                var attributes = field.GetCustomAttributes()
                    .Where(x => x.GetType() == typeof(DescriptionAttribute)).FirstOrDefault();

                if (attributes != null)
                {
                    var valueDoEnum = (int)field.GetValue(field);
                    retorno.Add(new testeOrdenacao
                    {
                        label = (attributes as DescriptionAttribute).Description,
                        value = valueDoEnum
                    });
                }
            }

            return retorno.OrderBy(x => x.label);
        }
    }

    public class testeOrdenacao
    {
        public string label { get; set; }
        public int value { get; set; }
    }
}
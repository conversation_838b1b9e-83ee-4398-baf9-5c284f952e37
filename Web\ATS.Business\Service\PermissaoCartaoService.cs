using System;
using System.Globalization;
using System.Linq;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.PermissaoCartao;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using ATS.WS.Models.Webservice.Request.BizWebhook;
using AutoMapper;
using NLog;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.Service
{
    public class PermissaoCartaoService : ServiceBase, IPermissaoCartaoService
    {
        private readonly IExtrattaBizApiClient _extrattaBizApiClient;
        public PermissaoCartaoService(IExtrattaBizApiClient extrattaBizApiClient)
        {
            _extrattaBizApiClient = extrattaBizApiClient;
        }
        
        public BusinessResult<PermissaoCartaoModelResponse> ConsultarPermissoes(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.GetPermissaoCartao(identificador,produto);

                if (!result.Success) 
                    return BusinessResult<PermissaoCartaoModelResponse>.Error(result.Messages);
                
                return BusinessResult<PermissaoCartaoModelResponse>.Valid(Mapper.Map<PermissaoCartaoModelResponse>(result.Value));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha na consulta de permissões:" + e);
                return BusinessResult<PermissaoCartaoModelResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult SaqueHabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutSaqueHabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao habilitar saque:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult SaqueDesabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutSaqueDesabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao desabilitar saque:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CompraOnlineHabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutCompraOnlineHabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao habilitar compra online:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CompraOnlineDesabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutCompraOnlineDesabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao desabiilitar compra online:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CompraInternacionalHabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutCompraInternacionalHabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao habilitar compra internacional:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CompraInternacionalDesabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutCompraInternacionalDesabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao desabilitar compra internacional:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CompraFisicaHabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutCompraFisicaHabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao habilitar compra fisica:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CompraFisicaDesabilitar(int identificador,int produto)
        {
            try
            {
                var result = _extrattaBizApiClient.PutCompraFisicaDesabilitar(identificador,produto);

                if (!result.Success) 
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Falha ao desabilitar compra fisica:" + e);
                return BusinessResult.Error(e.Message);
            }
        }
    }
}
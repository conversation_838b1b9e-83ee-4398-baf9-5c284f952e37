﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class TriagemProtocoloModel
    {
        public int IdProtocolo { get; set; }
        public string Placa { get; set; }
        public string Motorista { get; set; }
        public string TipoEventoViagem { get; set; }
        public decimal? ValorPagamento { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public int IdProtocoloEvento { get; set; }
        public int IdViagemEvento { get; set; }
        public ETipoEventoViagem? TipoEventoViagemInt { get; set; }
        public bool Aprovado { get; set; }
        public EStatusProtocolo StatusInt { get; set; }
        public string ViagemToken { get; set; }
        public string NumeroDocumento { get; set; }
        public string RazaoSocialFilial { get; set; }
        public int IdEmpresa { get; set; }
        public bool EventoAnalisado { get; set; }

        public string <PERSON>go<PERSON><PERSON>ao { get; set; }
        public bool? HabilitarPagamentoCartao { get; internal set; }

        public string EventoReincidente { get; set; }
    }
}

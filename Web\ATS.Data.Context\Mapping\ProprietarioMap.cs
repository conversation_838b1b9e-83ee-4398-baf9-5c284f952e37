﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class ProprietarioMap : EntityTypeConfiguration<Proprietario>
    {
        public ProprietarioMap()
        {
            ToTable("PROPRIETARIO");

            HasKey(t => new { t.IdProprietario, IdEmpresa = t.IdEmpresa });

            Property(t => t.IdProprietario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdEmpresa)
                .HasIndex("IX_PROPRIETARIO_CNPJCPF_IDEMPRESA", true, 2)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.CNPJCPF)
                .HasIndex("IX_PROPRIETARIO_CNPJCPF_IDEMPRESA", true, 0)
                .IsRequired()
                .HasMaxLength(14);

            Property(t => t.Ativo)
                .HasIndex("IX_PROPRIETARIO_CNPJCPF_IDEMPRESA", true, 1);

            Property(t => t.RazaoSocial)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.NomeFantasia)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.RG)
                .IsOptional()
                .HasMaxLength(20);

            Property(t => t.RGOrgaoExpedidor)
                .IsOptional()
                .HasMaxLength(100);

            Property(t => t.IE)
                .HasMaxLength(15);

            Property(t => t.TransferirEntreCartoes)
                .IsRequired();

            Property(t => t.TipoContrato)
                .IsRequired();

            Property(t => t.RNTRC)
                .IsRequired();

            Property(t => t.NomeMae)
                .IsOptional()
                .HasMaxLength(100);

            Property(t => t.NomePai)
                .IsOptional()
                .HasMaxLength(100);

            #region Parametros: Meio Homologado

            Property(t => t.TipoCarregamentoFrete)
                .IsRequired();

            Property(t => t.PercentualTransferenciaMotorista)
                .IsOptional();

            Property(t => t.EquiparadoTac)
                .IsOptional();

            Property(t => t.FormaPagamentoFretePadrao)
                .IsRequired();

            Property(t => t.ChavePixPadrao)
                .IsOptional()
                .HasMaxLength(32);

            #endregion

            HasRequired(a => a.Empresa)
                .WithMany(b => b.Proprietarios)
                .HasForeignKey(c => c.IdEmpresa);
        }
    }
}

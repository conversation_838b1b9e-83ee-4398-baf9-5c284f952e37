﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Data.Repository.EntityFramework
{
    public class MenuRepository : Repository<Menu>, IMenuRepository
    {
        public MenuRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado para consultar Menu.
        /// </summary>
        /// <param name="descricao">Descrição de Menu</param>
        /// <returns>IQueryable de MenuGrid</returns>
        public IQueryable<MenuGrid> Consultar(string descricao)
        {
            return (from menu in All()
                    where menu.Descricao.Contains(descricao)
                    orderby menu.IdMenu descending
                    select new MenuGrid
                    {
                        IdMenu = menu.IdMenu,
                        Descricao = menu.Descricao,
                        Link = menu.Link,
                        Ativo = menu.Ativo
                    });
        }

        /// <summary>
        /// Método utilizado para listar Menu por Grupo de Usuario.
        /// </summary>
        /// <param name="idGrupoUsuario">Id de Grupo de Usuario</param>
        /// <returns>IQueryable de Menu</returns>
        public IQueryable<Menu> GetPorIdGrupoUsuario(int idGrupoUsuario)
        {
            return from mnu in ((AtsContext)Context).Menu.Include(m => m.GruposDoMenu)
                   where
                       mnu.Ativo &&
                       mnu.GruposDoMenu.Count(m => m.IdMenu == mnu.IdMenu && m.IdGrupoUsuario == idGrupoUsuario) > 0
                   select mnu;
        }

        /// <summary>
        /// Retorna a lista de menus permitidos de acordo com a especificação do do grupo de usuário e perfil do usuário
        /// </summary>
        /// <param name="idGrupoUsuario"></param>
        /// <returns></returns>
        public IQueryable<Menu> GetMenusPermissao(int? idGrupoUsuario)
        {
            return (from mnu in All(true)
                .Include(m => m.MenuLista)
                .Include(m => m.ModuloMenus)
                .Include(m => m.AutorizacaoEmpresaLista)
                    where
                        mnu.Ativo &&
                        ((idGrupoUsuario == null ||
                         mnu.GruposDoMenu.Count(m => m.IdMenu == mnu.IdMenu && m.IdGrupoUsuario == idGrupoUsuario) > 0)
                         || mnu.IsMenuPai)
                    select mnu).Distinct();
        }

        /// <summary>
        /// Retorna os itens do menu organizados na hierarquia 
        /// </summary>
        /// <param name="idGrupoUsuario"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuarioMenuGrid> GetArvoreMenuPorIdGrupoUsuario(int idGrupoUsuario)
        {
            return (from men in ((AtsContext)Context).Menu
                    from jmen in ((AtsContext)Context).Menu.Where(m => m.IdMenu == men.IdMenuPai).DefaultIfEmpty()
                    from grm in
                        ((AtsContext)Context).GrupoUsuarioMenu.Where(
                            m => m.IdMenu == men.IdMenu && m.IdGrupoUsuario == idGrupoUsuario).DefaultIfEmpty()
                        //from mod in ((ATSContext)Context).Modulo.Where(m => m.IdModulo == men.IdModulo).DefaultIfEmpty()
                    where men.Ativo.Equals(true)
                    select new GrupoUsuarioMenuGrid
                    {
                        IdGrupoUsuario = grm.IdGrupoUsuario,
                        IdMenu = men.IdMenu,
                        Menu = men.Descricao,
                        IdMenuPai = men.IdMenuPai,
                        Pai = jmen != null ? jmen.Descricao : null,
                        //IdModulo = mod != null ? mod.IdModulo : (int?)null,
                        //Modulo = mod != null ? mod.Descricao : null,
                        Ativo = grm != null
                    }).OrderBy(p => p.Pai);
        }

        /// <summary>
        /// Retorna os itens do menu organizados na hierarquia por empresa
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public IQueryable<AutorizacaoEmpresaMenuGrid> GetArvoreMenuPorIdEmpresa(int idEmpresa)
        {

            var autorizacaoEmpresaSelect = (from empresaModulo in ((AtsContext)Context).EmpresaModulo
                                            from empresa in ((AtsContext)Context).Empresa
                                            from moduloMenu in ((AtsContext)Context).ModuloMenu
                                            from modulo in ((AtsContext)Context).Modulo
                                            from menu in ((AtsContext)Context).Menu
                                            where moduloMenu.IdModulo == empresaModulo.IdModulo
                                                  && modulo.IdModulo == empresaModulo.IdModulo
                                                  && menu.IdMenu == moduloMenu.IdMenu
                                                  && empresaModulo.IdEmpresa == idEmpresa
                                                  && empresa.IdEmpresa == idEmpresa
                                                  && empresa.Ativo
                                                  && !menu.IsMenuMobile
                                            select new AutorizacaoEmpresaMenuGrid
                                            {
                                                IdEmpresa = empresaModulo.IdEmpresa,
                                                IdMenu = menu.IdMenu,
                                                Menu = menu.Descricao,
                                                IdModulo = modulo != null ? modulo.IdModulo : (int?)null,
                                                Modulo = modulo != null ? modulo.Descricao : null,
                                                Ativo = menu != null
                                            }).Distinct().OrderBy(p => p.Modulo).ThenBy(p => p.Modulo);

            var countModulo = 0;
            var retorno = new List<AutorizacaoEmpresaMenuGrid>();
            var t = autorizacaoEmpresaSelect.GroupBy(x => x.IdModulo).ToList();
            foreach (var autorizacaoEmpresaMenuGrids in t)
            {
                ++countModulo;
                var countMenu = countModulo;
                var group = autorizacaoEmpresaMenuGrids.ToList();
                foreach (var autorizacaoEmpresaMenuGrid in group)
                {
                    var permissaoEmpresa = from autorizacao in ((AtsContext)Context).AutorizacaoEmpresa
                                           where autorizacao.IdEmpresa == idEmpresa
                                           select autorizacao;

                    var autorizacaoPorMenu = permissaoEmpresa.FirstOrDefault(x => x.IdMenu == autorizacaoEmpresaMenuGrid.IdMenu);
                    if (autorizacaoPorMenu != null)
                        autorizacaoEmpresaMenuGrid.HasPermissao =
                        (permissaoEmpresa?.FirstOrDefault(x => x.IdMenu == autorizacaoEmpresaMenuGrid.IdMenu) != null
                         && autorizacaoPorMenu.HasPermissao);
                    else
                        autorizacaoEmpresaMenuGrid.HasPermissao = false;

                    autorizacaoEmpresaMenuGrid.IdTreeViewModulo = countModulo;
                    autorizacaoEmpresaMenuGrid.IdTreeViewMenu = ++countMenu;
                    retorno.Add(autorizacaoEmpresaMenuGrid);
                }
                countModulo = countMenu;
            }

            return retorno.AsQueryable();
        }

        public IQueryable<GrupoUsuarioMenuGrid> GetMenusPorModulo(int idModulo, int idGrupoUsuario, int idEmpresa, int perfil)
        {
            var retorno = (from modulo in ((AtsContext)Context).Modulo.Where(x => x.IdModulo == idModulo)
                           from moduloMenu in ((AtsContext)Context).ModuloMenu.Where(x => x.IdModulo == modulo.IdModulo)
                           from menu in ((AtsContext)Context).Menu.Where(x => x.IdMenu == moduloMenu.IdMenu && (x.Perfis.Contains(perfil.ToString()) || perfil == 1))
                           from menuPai in ((AtsContext)Context).Menu.Where(x => x.IdMenu == menu.IdMenuPai).DefaultIfEmpty()
                           from autorizacaoEmpresa in ((AtsContext)Context).AutorizacaoEmpresa
                           where menu.Ativo.Equals(true) && menu.IdMenuPai != null
                           && autorizacaoEmpresa.IdMenu == menu.IdMenu && (idEmpresa <= 0 || autorizacaoEmpresa.IdEmpresa == idEmpresa)
                           select new GrupoUsuarioMenuGrid
                           {
                               IdMenu = menu.IdMenu,
                               Menu = menu.Descricao,
                               IdMenuPai = menu.IdMenuPai,
                               Pai = menuPai != null ? menuPai.Descricao : null,
                               IdModulo = modulo != null ? modulo.IdModulo : (int?)null,
                               Modulo = modulo != null ? modulo.Descricao : null,
                               Ativo = false

                           }).OrderBy(x => x.Modulo).ThenBy(y => y.Pai).ToList();

            if (idGrupoUsuario > 0)
            {
                foreach (var grupoUsuarioMenuGrid in retorno)
                {
                    var grupoUsuarioMenu = ((AtsContext)Context).GrupoUsuarioMenu.Where(
                            m => m.IdMenu == grupoUsuarioMenuGrid.IdMenu && m.IdGrupoUsuario == idGrupoUsuario);

                    if (grupoUsuarioMenu.Any())
                        grupoUsuarioMenuGrid.Ativo = true;
                }
            }

            List<GrupoUsuarioMenuGrid> listaGrupoUsuarioMenuGrid = new List<GrupoUsuarioMenuGrid>();
            foreach (var menu in retorno)
            {
                var menuId = listaGrupoUsuarioMenuGrid.FirstOrDefault(x => x.IdMenu == menu.IdMenu);
                if (menuId == null)
                    listaGrupoUsuarioMenuGrid.Add(menu);
            }

            return listaGrupoUsuarioMenuGrid.AsQueryable();
        }

        public List<GrupoUsuarioMenuGrid> GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idModulo, int idEmpresa, bool verificarAutorizacaoEmpresa)
        {
            var retorno = (from modulo in ((AtsContext)Context).Modulo.Where(x => x.IdModulo == idModulo)
                           from moduloMenu in ((AtsContext)Context).ModuloMenu.Where(x => x.IdModulo == modulo.IdModulo)
                           from menu in ((AtsContext)Context).Menu.Where(x => x.IdMenu == moduloMenu.IdMenu)
                           from menuPai in ((AtsContext)Context).Menu.Where(x => x.IdMenu == menu.IdMenuPai).DefaultIfEmpty()
                           where menu.Ativo.Equals(true) && menu.IdMenuPai != null
                           select new GrupoUsuarioMenuGrid
                           {
                               IdMenu = menu.IdMenu,
                               Menu = menu.Descricao,
                               IdMenuPai = menu.IdMenuPai,
                               Pai = menuPai != null ? menuPai.Descricao : null,
                               IdModulo = modulo != null ? modulo.IdModulo : (int?)null,
                               Modulo = modulo != null ? modulo.Descricao : null
                           }).OrderBy(x => x.Modulo)
                           .ThenBy(y => y.Pai)
                           .ToList();

            List<GrupoUsuarioMenuGrid> listaGrupoUsuarioMenuGrid = new List<GrupoUsuarioMenuGrid>();
            foreach (var menu in retorno)
                if (listaGrupoUsuarioMenuGrid.FirstOrDefault(x => x.IdMenu == menu.IdMenu) == null)
                {
                    var hasPerm = ((AtsContext)Context).AutorizacaoEmpresa.FirstOrDefault(x => 
                                x.IdMenu == menu.IdMenu && x.IdEmpresa == idEmpresa);
                    if (hasPerm != null)
                        menu.Ativo = hasPerm.HasPermissao;

                    listaGrupoUsuarioMenuGrid.Add(menu);
                }
            return listaGrupoUsuarioMenuGrid.Where(i => i.IdModulo != null).ToList();
        }
    }
}
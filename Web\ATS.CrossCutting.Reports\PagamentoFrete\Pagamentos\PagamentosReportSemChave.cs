﻿using System.Collections.Generic;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos
{
    public class PagamentosSemChaveReport
    {
        /// <summary>
        /// Retorna um relatorio renderizado
        /// </summary>
        /// <param name="pagamentosModel">Dados para o relatório</param>
        /// <param name="tipoArquivo">Tipo de renderização:</param>
        /// <para>1 = PDF, 2 = Excel</para>
        /// <returns></returns>
        public byte[] Relatorio(List<PagamentosModel> pagamentosModel, PagamentosHeaderModel pagamentosHeaderModel, int tipoArquivo)
        {
            var localReport = new LocalReport();
            try
            {
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = pagamentosModel,
                    Name = "pagamentosModel"
                });
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = new List<PagamentosHeaderModel> { pagamentosHeaderModel },
                    Name = "pagamentosHeaderModel"
                });

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos.PagamentosSemChave.rdlc";
                localReport.Refresh();

                switch (tipoArquivo)
                {
                    case 1:
                        return localReport.Render("PDF");
                    case 2:
                        return localReport.Render("Excel");
                    default:
                        return localReport.Render("PDF");
                }
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }
    }
}
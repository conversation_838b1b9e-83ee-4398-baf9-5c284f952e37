﻿using System.Net;
using System;
using Newtonsoft.Json;

namespace ATS.Domain.Models
{
    public class KeycloakScopeModel
    {
        [<PERSON>sonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("protocol")]
        public string Protocol { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }
    }
}
/*
     
        {
        "name": "test_scope",
        "description": "OpenID Connect built-in scope: offline_access",
        "protocol": "openid-connect",
        "attributes": {
            "consent.screen.text": "${offlineAccessScopeConsentText}",
            "display.on.consent.screen": "true"
        }
 */

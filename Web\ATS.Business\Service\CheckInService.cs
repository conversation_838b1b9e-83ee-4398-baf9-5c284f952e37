﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Data.Entity;
using System.Linq;
using ATS.Domain.Helpers;
using System.Collections.Generic;
using System.Linq.Dynamic;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using Microsoft.Ajax.Utilities;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class CheckInService : ServiceBase, ICheckInService
    {
        private readonly IViagemService _viagemService;
        private readonly ICidadeDapper _cidadeDapper;
        private readonly IEstadoRepository _estadoRepository;
        private readonly ICheckInRepository _checkInRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IVeiculoRepository _veiculoRepository;
        private readonly ICheckinResumoRepository _checkinResumoRepository;
        private readonly IMotoristaService _motoristaService;

        public CheckInService(IViagemService viagemService, ICidadeDapper cidadeDapper, IEstadoRepository estadoRepository, ICheckInRepository checkInRepository,
            IVeiculoRepository veiculoRepository, ICheckinResumoRepository checkinResumoRepository, IMotoristaService motoristaService, IUsuarioRepository usuarioRepository)
        {
            _viagemService = viagemService;
            _cidadeDapper = cidadeDapper;
            _estadoRepository = estadoRepository;
            _checkInRepository = checkInRepository;
            _veiculoRepository = veiculoRepository;
            _checkinResumoRepository = checkinResumoRepository;
            _motoristaService = motoristaService;
            _usuarioRepository = usuarioRepository;
        }
        
        public ValidationResult AddCheckinPreCadastro(CheckIn checkIn)
        {

            //Pega cidade do Checkin 
            var cidade = _cidadeDapper.GetCidadeMaisProxima(checkIn.Latitude, checkIn.Longitude);

            if (cidade != null)
            {
                var estado = _estadoRepository.Get(cidade.IdEstado);

                checkIn.IdCidadeCheckIn = cidade.IdCidade;
                checkIn.RegiaoBrasil = new MapHelper().GetRegiaoFromUf(estado.Sigla);
            }

            _checkInRepository.Add(checkIn);

            return new ValidationResult();
        }

        /// <summary>
        /// Adicionar o CheckIn
        /// </summary>
        /// <param name="checkIn">Dados do CheckIn</param>
        /// <returns></returns>
        public ValidationResult Add(CheckIn checkIn)
        {
            try
            {
                // Irá verificar se o CPF/CNPJ do usuário se encontra registrado
                // como motorista para o empresa da carga. Encontrando, configura o valor do campo IdMotorista
                string nCPFCNPJ = checkIn.IdUsuario != null ? _usuarioRepository.GetCNPJCPF(checkIn.IdUsuario.Value) : null;

                //Action para que este bloco seja chamado duas vezes
                Action lCidade = () =>
                {
                    //Pega cidade do Checkin 
                    var cidade = _cidadeDapper.GetCidadeMaisProxima(checkIn.Latitude, checkIn.Longitude);

                    if (cidade != null)
                    {
                        var estado = _estadoRepository.Get(cidade.IdEstado);

                        checkIn.IdCidadeCheckIn = cidade.IdCidade;
                        checkIn.RegiaoBrasil = new MapHelper().GetRegiaoFromUf(estado.Sigla);
                    }

                    if (checkIn.IdUsuario != null)
                        checkIn.CpfCnpjUsuario = _usuarioRepository.GetCNPJCPF(checkIn.IdUsuario ?? 0);
                    
                    _checkInRepository.Add(checkIn);
                };


                if (!string.IsNullOrWhiteSpace(nCPFCNPJ))
                {
                    //// Será verificado se existe alguma viagem em aberto, caso sim, iremos..
                    //// definir o idViagem do checkin e inserir um checkin para cada viagem.. 
                    //// em aberto, caso contrário irá inserir um checkin sem idViagem, apenas.

                    

                    int? idMotorista = _motoristaService.GetIdPorCpf(nCPFCNPJ.OnlyNumbers());
                    if (idMotorista.HasValue && idMotorista.Value > 0)
                        checkIn.IdMotorista = idMotorista;

                    Usuario usuario = _usuarioRepository.GetWithRelationships(checkIn.IdUsuario.Value);
                    IList<Viagem> viagensEmViagem = _viagemService.GetViagensEmViagemPorCPF(usuario.CPFCNPJ).ToList();
                    if (usuario.IdEmpresa != null)
                    {
                        var cidade = _cidadeDapper.GetCidadeMaisProxima(checkIn.Latitude, checkIn.Longitude);

                        if (cidade != null)
                        {
                            var estado = _estadoRepository.Get(cidade.IdEstado);

                            checkIn.IdCidadeCheckIn = cidade.IdCidade;
                            checkIn.RegiaoBrasil = new MapHelper().GetRegiaoFromUf(estado.Sigla);
                        }

                        checkIn.IdEmpresa = usuario.IdEmpresa;
                        _checkInRepository.Add(checkIn);
                    }
                    else if (viagensEmViagem.Any())
                    {
                        foreach (Viagem viagem in viagensEmViagem)
                        {

                            //Pega cidade do Checkin 
                            var cidade = _cidadeDapper.GetCidadeMaisProxima(checkIn.Latitude, checkIn.Longitude);

                            CheckIn chkIn = new CheckIn();
                            chkIn = checkIn;
                            chkIn.IdViagem = viagem.IdViagem;
                            chkIn.IdEmpresa = viagem.IdEmpresa;

                            if (cidade != null)
                            {
                                var estado = _estadoRepository.Get(cidade.IdEstado);

                                chkIn.IdCidadeCheckIn = cidade.IdCidade;
                                chkIn.RegiaoBrasil = new MapHelper().GetRegiaoFromUf(estado.Sigla);
                            }

                            _checkInRepository.Add(chkIn);
                        }
                    }
                    else
                    {
                        //Pega cidade do Checkin
                        var cidade = _cidadeDapper.GetCidadeMaisProxima(checkIn.Latitude, checkIn.Longitude);

                        if (cidade != null)
                        {
                            var estado = _estadoRepository.Get(cidade.IdEstado);

                            checkIn.IdCidadeCheckIn = cidade.IdCidade;
                            checkIn.RegiaoBrasil = new MapHelper().GetRegiaoFromUf(estado.Sigla);
                        }

                        _checkInRepository.Add(checkIn);
                    }
                }
                else
                {
                    lCidade();
                }

            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna os check-ins realizados em determinado período
        /// </summary>
        /// <param name="dataInicial"></param>
        /// <param name="dataFinal"></param>
        /// <returns></returns>
        public List<CheckIn> GetChekinsPeriodoTelao(DateTime dataInicial, DateTime dataFinal)
        {
            try
            {
                return (from chkIn in _checkInRepository
                           .Include(c => c.Usuario)
                           .Include(c => c.Usuario.Veiculos)
                           .Include(v => v.Usuario.CheckIns)
                           .Include(c => c.Motorista)
                           .Include(c => c.Motorista.Veiculos)
                           .Include(v => v.Motorista.CheckIns)
                        where chkIn.DataHora >= dataInicial && chkIn.DataHora <= dataFinal && chkIn.Latitude != 0 && chkIn.Longitude != 0
                        select chkIn).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"{ex.Message} - {ex.InnerException}");
            }
        }

        /// <summary>
        /// Retorna os check-ins realizados em determinado período e placa
        /// </summary>
        /// <param name="dataInicial"></param>
        /// <param name="dataFinal"></param>
        /// /// <param name="placa"></param>
        /// <returns></returns>
        public IQueryable<CheckIn> GetByPlaca(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa)
        {
            var checkins = _checkInRepository
                .Find(x => x.DataHora >= dataInicial && 
                           x.DataHora <= dataFinal && x.Viagem != null && 
                           (x.Viagem.IdEmpresa == IdEmpresa) && 
                           (x.Viagem.Placa == placa))
                .Include(x => x.Viagem)
                .Include(x => x.Cidade)
                .Include(x => x.Motorista)
                .Include(x => x.Viagem)
                .Include(x => x.Usuario)
                .Include(x => x.Empresa)
                .Include(x => x.Cidade.Estado);

            

            if (!string.IsNullOrEmpty(placa))
                checkins = checkins.Where(x => x.Viagem.Placa == placa);

            return checkins;
        }

        
        public List<CheckIn> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa)
        {
            var veiculo = _veiculoRepository.FirstOrDefault(x => x.Placa == placa);
            if (veiculo != null)
            {
                var checkins = _checkInRepository
                    .Find(x => x.DataHora >= dataInicial && 
                               x.DataHora <= dataFinal && 
                               x.IdMotorista == veiculo.IdMotorista && 
                               x.IdEmpresa == IdEmpresa)
                    .Include(x => x.Viagem)
                    .Include(x => x.Cidade)
                    .Include(x => x.Motorista)
                    .Include(x => x.Viagem)
                    .Include(x => x.Usuario)
                    .Include(x => x.Empresa)
                    .Include(x => x.Cidade.Estado);
                
                return checkins.ToList();
            }
            
            return new List<CheckIn>();
        }

        public IQueryable<CheckIn> GetByEmpresa(int IdEmpresa)
        {
            var teste= _checkinResumoRepository
                .Find(x => x.IdEmpresa == IdEmpresa && x.IdMotorista != null)
                .Include(x => x.Viagem)
                .Include(x => x.Cidade)
                .Include(x => x.Motorista)
                .Include(x => x.Viagem)
                .Include(x => x.Usuario)
                .Include(x => x.Empresa)
                .Include(x => x.Cidade.Estado);
            
            var checkins = _checkInRepository
                .Find(x => x.IdEmpresa == IdEmpresa && x.IdMotorista != null)
                .Include(x => x.Viagem)
                .Include(x => x.Cidade)
                .Include(x => x.Motorista)
                .Include(x => x.Viagem)
                .Include(x => x.Usuario)
                .Include(x => x.Empresa)
                .Include(x => x.Cidade.Estado);

            return checkins;
        }


        public List<CheckIn> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf)
        {
            var checkins = _checkInRepository
                .Find(x => x.DataHora >= dataInicial && x.DataHora <= dataFinal && 
                           ((x.Motorista != null && x.Motorista.CPF == cpf) || (x.Usuario != null &&
                                                                                x.Usuario.CPFCNPJ == cpf)))
                .Include(x => x.Viagem)
                .Include(x => x.Cidade)
                .Include(x => x.Motorista)
                .Include(x => x.Motorista.Veiculos)
                .Include(x => x.Viagem)
                .Include(x => x.Usuario)
                .Include(x => x.Empresa)
                .Include(x => x.Cidade.Estado);

            if(!checkins.Any())
                 checkins = _checkInRepository
                    .Find(x => x.DataHora >= dataInicial && x.DataHora <= dataFinal && 
                               x.Viagem != null && (x.Viagem.IdEmpresa == IdEmpresa) &&
                               ((x.Motorista != null && x.Motorista.CPF == cpf) || (x.Usuario != null && x.Usuario.CPFCNPJ == cpf)))
                    .Include(x => x.Viagem)
                    .Include(x => x.Cidade)
                    .Include(x => x.Motorista)
                    .Include(x => x.Motorista.Veiculos)
                    .Include(x => x.Viagem)
                    .Include(x => x.Usuario)
                    .Include(x => x.Empresa)
                    .Include(x => x.Cidade.Estado);

            return checkins.ToList();
        }

        public IQueryable<CheckIn> GetById(int Id, bool withIncludes = true)
        {
            var checkins = _checkInRepository
                .Find(x => x.IdCheckIn == Id);

            if (withIncludes) { 
                checkins = checkins
                .Include(x => x.Viagem)
                .Include(x => x.Cidade)
                .Include(x => x.Motorista)
                .Include(x => x.Viagem)
                .Include(x => x.Usuario)
                .Include(x => x.Empresa)
                .Include(x => x.Cidade.Estado);
            }

            return checkins;
        }
        /// <summary>
        /// Retorna os check-ins realizados em determinado período
        /// </summary>
        /// <param name="dataInicial"></param>
        /// <param name="dataFinal"></param>
        /// <returns></returns>
        public IQueryable<CheckIn> GetChekinsPeriodo(DateTime dataInicial, DateTime? dataFinal = null)
        {
            try
            {
                if (dataFinal.HasValue)
                    return _checkInRepository
                        .Find(p => p.DataHora >= dataInicial && p.DataHora <= dataFinal)
                        .Include(p => p.Motorista)
                        .Include(p => p.Motorista.Veiculos.Select(x => x.TipoCarreta))
                        .Include(p => p.Motorista.Veiculos.Select(x => x.TipoCavalo))
                        .Include(p => p.Motorista.Cidade)
                        .Include(p => p.Motorista.Estado)
                        .Include(p => p.Motorista.Pais)
                        .Include(p => p.Usuario.Filiais)
                        .Include(p => p.Usuario.Veiculos)
                        .Include(p => p.Usuario.Contatos)
                        .Include(p => p.Usuario.Enderecos)
                        .Include(p => p.Usuario.Enderecos.Select(x => x.Cidade))
                        .Include(p => p.Usuario.Enderecos.Select(x => x.Estado))
                        .Include(p => p.Usuario.Enderecos.Select(x => x.Pais))
                        .Include(p => p.Usuario.Veiculos.Select(x => x.TipoCarreta))
                        .Include(p => p.Usuario.Veiculos.Select(x => x.TipoCavalo));

                return _checkInRepository
                    .Find(p => p.DataHora <= dataInicial)
                    .Include(p => p.Motorista)
                    .Include(p => p.Motorista.Veiculos.Select(x => x.TipoCarreta))
                    .Include(p => p.Motorista.Veiculos.Select(x => x.TipoCavalo))
                    .Include(p => p.Motorista.Cidade)
                    .Include(p => p.Motorista.Estado)
                    .Include(p => p.Motorista.Pais)
                    .Include(p => p.Usuario.Filiais)
                    .Include(p => p.Usuario.Veiculos)
                    .Include(p => p.Usuario.Contatos)
                    .Include(p => p.Usuario.Enderecos)
                    .Include(p => p.Usuario.Enderecos.Select(x => x.Cidade))
                    .Include(p => p.Usuario.Enderecos.Select(x => x.Estado))
                    .Include(p => p.Usuario.Enderecos.Select(x => x.Pais))
                    .Include(p => p.Usuario.Veiculos.Select(x => x.TipoCarreta))
                    .Include(p => p.Usuario.Veiculos.Select(x => x.TipoCavalo));

            }
            catch (Exception ex)
            {
                throw new Exception($"{ex.Message} - {ex.InnerException}");
            }
        }

        /// <summary>
        /// Retorna os Check-in realizados hoje.
        /// </summary>
        /// <returns></returns>
        public IQueryable<CheckIn> GetCheckInsHoje(int? idTipoCavalo, int? idTipoCarreta)
        {
            try
            {
                DateTime dtIniCheckInsHoje = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, hour: 0, minute: 0, second: 0);
                DateTime dtFimCheckInsHoje = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);

                return _checkInRepository
                    .Find(p => p.DataHora >= dtIniCheckInsHoje && p.DataHora <= dtFimCheckInsHoje)
                    .Include(p => p.Motorista)
                    .Include(p => p.Motorista.Veiculos.Select(x => x.TipoCarreta))
                    .Include(p => p.Motorista.Veiculos.Select(x => x.TipoCavalo))
                    .Include(p => p.Motorista.Cidade)
                    .Include(p => p.Motorista.Estado)
                    .Include(p => p.Motorista.Pais)
                    .Include(p => p.Usuario.Filiais)
                    .Include(p => p.Usuario.Veiculos)
                    .Include(p => p.Usuario.Contatos)
                    .Include(p => p.Usuario.Enderecos)
                    .Include(p => p.Usuario.Enderecos.Select(x => x.Cidade))
                    .Include(p => p.Usuario.Enderecos.Select(x => x.Estado))
                    .Include(p => p.Usuario.Enderecos.Select(x => x.Pais))
                    .Include(p => p.Usuario.Veiculos.Select(x => x.TipoCarreta))
                    .Include(p => p.Usuario.Veiculos.Select(x => x.TipoCavalo))
                       .Where(
                        p => (p.Motorista == null || !idTipoCavalo.HasValue || p.Motorista.Veiculos.Select(x => x.IdTipoCavalo).Contains(idTipoCavalo.Value)) &&
                             (p.Motorista == null || !idTipoCarreta.HasValue || p.Motorista.Veiculos.Select(x => x.IdTipoCarreta).Contains(idTipoCarreta.Value)) &&
                             (p.Usuario == null || !idTipoCavalo.HasValue || p.Usuario.Veiculos.Select(x => x.IdTipoCavalo).Contains(idTipoCavalo.Value)) &&
                             (p.Usuario == null || !idTipoCarreta.HasValue || p.Usuario.Veiculos.Select(x => x.IdTipoCarreta).Contains(idTipoCarreta.Value)));
            }
            catch (Exception ex)
            {
                throw new Exception($"{ex.Message} - {ex.InnerException}");
            }
        }

        /// <summary>
        /// Retorna os dados do último CheckIn realizado pelo usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public CheckIn GetLast(int idUsuario)
        {
            return _checkInRepository
                .Find(x => x.IdUsuario == idUsuario)
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemChecks)
                .OrderByDescending(x => x.IdCheckIn).FirstOrDefault();
        }

        /// <summary>
        /// Retorna os dados do último CheckIn realizado pelo usuário, buscando pelo id do motorista
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public CheckIn GetLastByIdMotorista(int idMotorista)
        {
            return _checkInRepository
                .Find(x => x.IdMotorista == idMotorista)
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemChecks)
                .OrderByDescending(x => x.IdCheckIn).FirstOrDefault();
        }

        public CheckIn GetLastByCpf(DateTime aDataInicial, DateTime aDataFinal, string cpf)
        {
            return _checkInRepository
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemChecks)
                .Include(x => x.Motorista)
                .Include(x => x.Usuario)
                .Include(x => x.Cidade)
                .Include(x => x.Cidade.Estado)
                .Where(x => (x.DataHora >= aDataInicial && x.DataHora <= aDataFinal) &&
                    ((x.Motorista != null && x.Motorista.CPF == cpf) || (x.Usuario != null && x.Usuario.CPFCNPJ == cpf)))
                .OrderByDescending(x => x.IdCheckIn).FirstOrDefault();
        }

        /// <summary>
        /// Retorna a última latitude e longetude do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public Tuple<decimal?, decimal?> GetLastPosition(int idUsuario)
        {
            CheckIn checkIn = GetLast(idUsuario);
            if (checkIn != null)
                return new Tuple<decimal?, decimal?>(checkIn.Latitude, checkIn.Longitude);

            return null;
        }
    }
}
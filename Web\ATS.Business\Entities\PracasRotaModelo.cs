﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class PracasRotaModelo
    {
        public int IdPraca { get; set; }
        public int IdRotaModelo { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
        public decimal ValorTag { get; set; }

        #region Referências
        
        [ForeignKey("IdRotaModelo")]
        public virtual RotaModelo RotaModelo { get; set; }
        
        #endregion
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;

namespace ATS.WS.Services
{
    public class SrvTipoCavalo : SrvBase
    {
        private readonly ITipoCavaloApp _tipoCavaloApp;
        private readonly IEmpresaApp _empresaApp;

        public SrvTipoCavalo(ITipoCavaloApp tipoCavaloApp, IEmpresaApp empresaApp)
        {
            _tipoCavaloApp = tipoCavaloApp;
            _empresaApp = empresaApp;
        }

        public TipoCavalo Get(int idTipoCavalo)
        {
            try
            {
                return _tipoCavaloApp.Get(idTipoCavalo);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Consultar)} >> {e.Message}");
            }
        }

        public Retorno<List<TipoCavaloModel>> Consultar()
        {
            try
            {
                return new Retorno<List<TipoCavaloModel>>(true, string.Empty,
                    Mapper.Map<List<TipoCavalo>, List<TipoCavaloModel>>(
                        _tipoCavaloApp.GetTodos().ToList()));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Consultar)} >> {e.Message}");
            }
        }

        public Retorno<object> Integrar(TipoCavaloModel @params)
        {

            var idEmpresaInformada = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            if (!idEmpresaInformada.HasValue)
                throw new Exception("Não foi possível identificar a empresa.");
            if (string.IsNullOrWhiteSpace(@params.Nome))
                throw new Exception("Campo Nome é obrigatório para integração de um tipo de veículo.");

            if (@params.Categoria <= 0)
                throw new Exception("Campo Categoria é obrigatório.");

            var tipoCavaloExistente = _tipoCavaloApp.GetPorDescricao(@params.Nome, idEmpresaInformada.Value);
            if (tipoCavaloExistente != null)
                return new Retorno<object>(true, tipoCavaloExistente.IdTipoCavalo);

            var tipoCavalo = new TipoCavalo
            {
                IdEmpresa = idEmpresaInformada,
                Categoria = @params.Categoria,
                DataHoraUltimaAtualizacao = DateTime.Now,
                NumeroEixos = @params.NumeroEixos,
                Nome = @params.Nome,
                Capacidade = @params.Capacidade
            };

            if (@params.TipoCavaloCliente != null && @params.TipoCavaloCliente.Any())
            {
                tipoCavalo.TipoCavaloCliente = new List<TipoCavaloCliente>();
                foreach (var item in @params.TipoCavaloCliente)
                {
                    if (string.IsNullOrWhiteSpace(item.Nome))
                        return new Retorno<object>(false, "Nome para o cliente do tipo de cavalo não informado.");

                    var tipoCavaloModel = new TipoCavaloCliente
                    {
                        IdCliente = item.IdCliente,
                        Nome = item.Nome
                    };

                    tipoCavalo.TipoCavaloCliente.Add(tipoCavaloModel);
                }
            }

            var result = _tipoCavaloApp.Add(tipoCavalo);
            if (!result.IsValid)
                throw new Exception(result.ToFormatedMessage());

            return new Retorno<object>(true, tipoCavalo.IdTipoCavalo);
        }

        internal object ConsultarSemEmpresa()
        {
            return _tipoCavaloApp.ConsultarSemEmpresa();
        }
    }
}
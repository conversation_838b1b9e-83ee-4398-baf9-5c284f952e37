﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;

namespace ATS.Domain.Service
{
    public class ModuloService : ServiceBase, IModuloService
    {
        private readonly IModuloRepository _moduloRepository;
        private readonly IEmpresaModuloService _empresaModuloService;

        public ModuloService(IModuloRepository moduloRepository, IEmpresaModuloService empresaModuloService)
        {
            _moduloRepository = moduloRepository;
            _empresaModuloService = empresaModuloService;
        }

        /// <summary>
        /// Método utilizado para buscar Módulo.
        /// </summary>
        /// <param name="id">Id de Módulo</param>
        /// <returns>Entidade Modulo</returns>
        public Modulo Get(int id)
        {
            return _moduloRepository.Get(id);
        }

        /// <summary>
        /// Método utilizado para incluir Módulo.
        /// </summary>
        /// <param name="modulo">Entidade de Módulo</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Modulo modulo)
        {
            try
            {
                _moduloRepository.Add(modulo);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para alterar Módulo.
        /// </summary>
        /// <param name="modulo">Entidade de Módulo</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Modulo modulo)
        {
            try
            {
                _moduloRepository.Update(modulo);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para listar todos os Módulos ativos.
        /// </summary>
        /// <returns>IQueryable de Modulo</returns>
        public IQueryable<Modulo> All()
        {
            return _moduloRepository.All().Where(x => x.Ativo);
        }

        /// <summary>
        /// Método utilizado para consultar Módulo.
        /// </summary>
        /// <param name="descricao">Descrição do Módulo</param>
        /// <returns>IQueryable de Módulo</returns>
        public IQueryable<Modulo> Consultar(string descricao)
        {
            if (descricao == null)
                descricao = string.Empty;

            return _moduloRepository.Consultar(descricao);
        }

        /// <summary>
        /// Inativa um módulo na base de dados
        /// </summary>
        /// <param name="id">ID da cidade</param>
        /// <returns></returns>
        public ValidationResult Inativar(int id)
        {
            try
            {
                IModuloRepository repository = _moduloRepository;

                Modulo modulo = repository.Get(id);
                if (!modulo.Ativo)
                    return new ValidationResult().Add($"Módulo já inativo na base de dados.");

                modulo.Ativo = false;

                repository.Update(modulo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa um módulo na base de dados
        /// </summary>
        /// <param name="id">ID da cidade</param>
        /// <returns></returns>
        public ValidationResult Reativar(int id)
        {
            try
            {
                IModuloRepository repository = _moduloRepository;

                Modulo modulo = repository.Get(id);
                if (modulo.Ativo)
                    return new ValidationResult().Add($"Módulo já ativo na base de dados.");

                modulo.Ativo = true;

                repository.Update(modulo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna os módulos do transportador
        /// </summary>
        /// <param name="idTransportador"></param>
        /// <returns></returns>
        public List<Modulo> GetModulosPorEmpresa(int idEmpresa)
        {

            var modulosEmpresa = _empresaModuloService.ListarModuloPorIdEmpresa(idEmpresa);

            var modulos = new List<Modulo>();

            foreach (var modulo in modulosEmpresa)
            {
                modulos.Add(modulo.Modulo);
            }

            return modulos;
        }

        public object GetModulosPorUsuario(int idUsuario)
        {
            return _moduloRepository.GetModulosUsuario(idUsuario);
        }

        public object ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var modulos = _moduloRepository.GetAll();

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                modulos = modulos.OrderBy(x => x.IdModulo);
            else
                modulos = modulos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            modulos = modulos.AplicarFiltrosDinamicos<Modulo>(filters);

            return new
            {
                totalItems = modulos.Count(),
                items = modulos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdModulo,
                    x.Sequencia,
                    x.Ativo,
                    x.Descricao
                })
            };
        }
    }
}
﻿using System.ComponentModel;
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Infrastructure.Annotations;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;

namespace ATS.Data.Context.Mapping
{
    public class EmpresaIndicadoresMap : EntityTypeConfiguration<EmpresaIndicadores>
    {
        public EmpresaIndicadoresMap()
        {
            ToTable("EMPRESA_INDICADORES");

            HasKey(t => t.Id);

            Property(t => t.Id)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdEmpresa);

            Property(x => x.DataInicioOperacao)
                .HasColumnName("datainiciooperacao")
                .IsOptional()
                .HasColumnType("datetime2");

            Property(x => x.TipoCliente)
                .HasColumnName("tipocliente")
                .IsOptional()
                .HasColumnType("varchar")
                .HasMaxLength(2);

            Property(x => x.PrevistoMovimentacaoFinanceiraFrete)
               .HasColumnName("previstomovimentacaofinanceirafrete")
               .IsRequired()
               .HasColumnType("decimal")
               .HasPrecision(17, 2);
            

            Property(x => x.PrevistoMovimentacaoFinanceiraVPO)
              .HasColumnName("previstomovimentacaofinanceiravpo")
              .IsRequired()
              .HasColumnType("decimal")
              .HasPrecision(17, 2);

            Property(x => x.PrevistoQuantidadeViagens)
              .HasColumnName("previstoquantidadeviagens")
              .IsRequired()
              .HasColumnType("int");

            Property(x => x.ComercialConta)
                .HasColumnName("comercialconta")
                .IsOptional()
                .HasColumnType("varchar")
                .HasMaxLength(200);

            Property(x => x.TMS)
               .HasColumnName("tms")
               .IsOptional()
               .HasColumnType("varchar")
               .HasMaxLength(200);

            HasRequired(x => x.Empresa)
                .WithMany()
                .HasForeignKey(x => x.IdEmpresa);
        }
    }
}
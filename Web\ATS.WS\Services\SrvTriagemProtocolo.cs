﻿using ATS.Application.Application;
using ATS.CrossCutting.Reports.Protocolo;
using ATS.Data.Context.Mapping;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Service;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.WS.Services
{
    public class SrvTriagemProtocolo : SrvBase
    {
        private readonly IAuthSessionService _authSessionService;
        private readonly IProtocoloService _protocoloService;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IMotivoApp _motivoApp;

        public SrvTriagemProtocolo(IAuthSessionService authSessionService, IProtocoloService protocoloService, IEstabelecimentoApp estabelecimentoApp, IProtocoloApp protocoloApp,
            IViagemEventoApp viagemEventoApp, IMotivoApp motivoApp)
        {
            _authSessionService = authSessionService;
            _protocoloService = protocoloService;
            _estabelecimentoApp = estabelecimentoApp;
            _protocoloApp = protocoloApp;
            _viagemEventoApp = viagemEventoApp;
            _motivoApp = motivoApp;
        }

        public byte[] GerarRelatorioProtocolosComOcorrencia(string sessionKey, List<int> idsProtocolo)
        {
            var idEstabelecimentoBase = new List<int>();
            var protocolosListModel = new List<RelatorioProtocoloComOcorrencia>();
            var protocoloHeader = new ProtocoloModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy") };
            decimal valorProtocolo = 0;

            #region Header do protocolo

            var usuario = _authSessionService.GetByToken(sessionKey)?.Usuario;
            protocoloHeader.PerfilEmpresa = usuario != null && usuario?.Perfil == EPerfil.Empresa;
            protocoloHeader.UsuarioGeracaoProtocolo = usuario?.Nome;
            if (usuario?.Perfil == EPerfil.Estabelecimento)
                idEstabelecimentoBase = usuario.UsuarioEstabelecimentos?.Select(x => x.IdEstabelecimento)
                    .ToList();

            var protocolos = _protocoloService
                .GetQuery(idsProtocolo)
                .Select(c => new
                {
                    c.IdProtocolo,
                    Nome = c.ProtocoloEventos.Where(x => x.IdUsuarioCadOcorrencia != null)
                                            .Select(x => x.UsuarioOcorrencia.Nome).FirstOrDefault(),
                    c.IdEstabelecimentoBase,
                    c.Empresa,
                    c.DataGeracao,
                    c.EstabelecimentoBase,
                    c.ValorProtocolo,
                    c.ProtocoloEventos

                });

            var idsEstabelecimentoBaseAssociacao = new List<int>();
            foreach (var item in idEstabelecimentoBase)
            {
                var estabelecimentosEmpresas = _estabelecimentoApp.GetIdEstabelecimentosAssociadosLiberacaoProtocolo(item);
                if (estabelecimentosEmpresas != null && estabelecimentosEmpresas.Any())
                    idsEstabelecimentoBaseAssociacao.AddRange(estabelecimentosEmpresas.Select(x => x.Key));
            }

            idEstabelecimentoBase.AddRange(idsEstabelecimentoBaseAssociacao);
            if (idEstabelecimentoBase != null && idEstabelecimentoBase.Any())
            {
                protocolos = protocolos.Where(x => idEstabelecimentoBase.Contains(x.IdEstabelecimentoBase));
                    //.ToList();
            }

            if (protocoloHeader.PerfilEmpresa)
            {
                var protocolo = protocolos.FirstOrDefault();
                protocoloHeader.Logo = protocolo?.Empresa?.Logo == null
                    ? string.Empty
                    : Convert.ToBase64String(protocolo.Empresa.Logo);
            }

            #endregion

            #region Itens do protocolo

            foreach (var item in protocolos)
            {
                var model = new RelatorioProtocoloComOcorrencia
                {
                    DataOcorrencia = item.DataGeracao, 
                    Estabelecimento = protocoloHeader.PerfilEmpresa ?  item.EstabelecimentoBase?.Descricao :   "", 
                    NomeFantasia = !protocoloHeader.PerfilEmpresa ? item.Empresa?.NomeFantasia : "", 
                    IdProtocolo= item.IdProtocolo, 
                    ValorProtocolo = item.ValorProtocolo,
                    UsuarioOcorrencia = item.Nome
                };
                protocolosListModel.Add(model);

                valorProtocolo += item.ValorProtocolo;
                //var antecipacao = item.ProtocoloAntecipacoes?.FirstOrDefault()?.ValorPagamentoAntecipado;
                //if (antecipacao.HasValue)
                //    totalPagamentoAntecipado += antecipacao.Value;
            }

            #endregion

            //protocoloHeader.TotalValorPagamentoAntecipado = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", totalPagamentoAntecipado, CultureInfo.InvariantCulture);
            protocoloHeader.TotalValorProtocolo = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", valorProtocolo);

            var report = new ProtocoloReport().GerarRelatorioProtocolosComOcorrencia(protocolosListModel);
            return report;
        }

        public byte[] GerarRelatorioProtocolosEventosVinculados(int idProtocolo, int take, int page, OrderFilters order,
        List<QueryFilters> filters, string tipo)
        {
            var idEstabelecimentoBase = new List<int>();
            var protocolosListModel = new List<RelatorioEventosVinculados>();
            var protocoloHeader = new CrossCutting.Reports.Protocolo.ProtocoloModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy") };

            #region Header do protocolo

            var protocolos = _protocoloApp
                .ConsultarEventosOriginalReport(idProtocolo, take, page, order, filters)
                .ToList();


            #endregion

            //protocoloHeader.TotalValorPagamentoAntecipado = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", totalPagamentoAntecipado, CultureInfo.InvariantCulture);
            //protocoloHeader.TotalValorProtocolo = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", valorProtocolo);

            var report = new ProtocoloReport().GerarRelatorioEventosVinculados(protocolos, tipo);
            return report;
        }


        public byte[] GerarRelatorioProtocolosEventosOcorrencia(int? idProtocolo, int take, int page, OrderFilters order,
         List<QueryFilters> filters, string tipo)
        {
            var idEstabelecimentoBase = new List<int>();
            var protocolosListModel = new List<RelatorioProtocoloComOcorrencia>();
            var protocoloHeader = new ProtocoloModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy") };
            decimal valorProtocolo = 0;

            #region Header do protocolo
            
            var protocolos = _protocoloApp
                .ConsultarTriagemAntecipacaoOcorrenciaProtocoloRelatorio( idProtocolo, take, page, order, filters)
                .ToList();
       

            #endregion

            //protocoloHeader.TotalValorPagamentoAntecipado = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", totalPagamentoAntecipado, CultureInfo.InvariantCulture);
            protocoloHeader.TotalValorProtocolo = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", valorProtocolo);

            var report = new ProtocoloReport().GerarRelatorioProtocolosEventosOcorrencia(protocolos, tipo);
            return report;
        }


        public byte[] GerarRelatorio(string sessionKey, List<int> idsProtocolo)
        {
            var idEstabelecimentoBase = new List<int>();
            var protocolosListModel = new List<ProtocoloListModel>();
            var protocoloHeader = new ProtocoloModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy") };
            decimal valorProtocolo = 0;

            #region Header do protocolo

            var usuario = _authSessionService.GetByToken(sessionKey)?.Usuario;
            protocoloHeader.PerfilEmpresa = usuario != null && usuario?.Perfil == EPerfil.Empresa;
            protocoloHeader.UsuarioGeracaoProtocolo = usuario?.Nome;
            if (usuario?.Perfil == EPerfil.Estabelecimento)
                idEstabelecimentoBase = usuario.UsuarioEstabelecimentos?.Select(x => x.IdEstabelecimento)
                    .ToList();

            var protocolos = _protocoloService
                .Get(idsProtocolo)
                .ToList();

            var idsEstabelecimentoBaseAssociacao = new List<int>();
            foreach (var item in idEstabelecimentoBase)
            {
                var estabelecimentosEmpresas = _estabelecimentoApp.GetIdEstabelecimentosAssociadosLiberacaoProtocolo(item);
                if (estabelecimentosEmpresas != null && estabelecimentosEmpresas.Any())
                    idsEstabelecimentoBaseAssociacao.AddRange(estabelecimentosEmpresas.Select(x => x.Key));
            }

            idEstabelecimentoBase.AddRange(idsEstabelecimentoBaseAssociacao);
            if (idEstabelecimentoBase != null && idEstabelecimentoBase.Any())
            {
                protocolos = protocolos.Where(x => idEstabelecimentoBase.Contains(x.IdEstabelecimentoBase))
                    .ToList();
            }

            if (protocoloHeader.PerfilEmpresa)
            {
                var protocolo = protocolos.FirstOrDefault();
                protocoloHeader.Logo = protocolo?.Empresa?.Logo == null
                    ? string.Empty
                    : Convert.ToBase64String(protocolo.Empresa.Logo);
            }

            #endregion

            #region Itens do protocolo

            foreach (var item in protocolos)
            {
                var model = new ProtocoloListModel
                {
                    IdProtocolo = item.IdProtocolo,
                    Status = EnumHelpers.GetDescription(item.ProtocoloEventos
                        ?.FirstOrDefault(y => y.Status != EStatusProtocoloEvento.Rejeitado)?.Status),
                    Estabelecimento = item.EstabelecimentoBase?.Descricao,
                    ValorProtocolo = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", item.ValorProtocolo),
                    DataGeracao = item.DataGeracao.ToString("dd/MM/yyyy"),
                    DataPagamento = item.DataPagamento?.ToString("dd/MM/yyyy") ?? string.Empty,
                    Empresa = item.Empresa?.NomeFantasia,
                    DataAprovacao = item.DataAprovacao?.ToString("dd/MM/yyyy"),
                    DataPrevisaoPagamento = item.DataPrevisaoPagamento?.ToString("dd/MM/yyyy"),
                    NumeroEventos = Convert.ToString(item.ProtocoloEventos?.Count ?? 0),
                    Pago = item.StatusProtocolo == EStatusProtocolo.Pago ? "Sim" : "Não"
                    //DataPagamentoAntecipado = item.ProtocoloAntecipacoes?.FirstOrDefault()?.DataPagamentoAntecipado
                    //                              .ToString("dd/MM/yyyy") ?? string.Empty,
                    //ValorProtocoloAntecipado = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", item.ProtocoloAntecipacoes?.FirstOrDefault()?.ValorPagamentoAntecipado),
                    //Antecipado = item.ProtocoloAntecipacoes?.Any(y => y.Status == EStatusProtocoloAntecipacao.Aprovada) == true ? "Sim" : "Não",
                    //ReceitaAntecipacao = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", (item.ValorProtocolo - (item.ProtocoloAntecipacoes?.FirstOrDefault()?.ValorPagamentoAntecipado??0)))
                };
                protocolosListModel.Add(model);

                valorProtocolo += item.ValorProtocolo;
                //var antecipacao = item.ProtocoloAntecipacoes?.FirstOrDefault()?.ValorPagamentoAntecipado;
                //if (antecipacao.HasValue)
                //    totalPagamentoAntecipado += antecipacao.Value;
            }

            #endregion

            //protocoloHeader.TotalValorPagamentoAntecipado = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", totalPagamentoAntecipado, CultureInfo.InvariantCulture);
            protocoloHeader.TotalValorProtocolo = string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", valorProtocolo);

            var report = new ProtocoloReport().GerarRelatorio(protocoloHeader, protocolosListModel);
            return report;
        }


        public object AddOcorrencia(int idProtocoloEvento, int idMotivo, string descricao, Usuario UsuarioLogado)
        {
            var protocolo_ = _protocoloApp.AddOcorrencia(idProtocoloEvento, idMotivo, descricao, UsuarioLogado);
            var motivo = _motivoApp.Get(idMotivo);

            return new
            {
                IdProtocoloEvento = idProtocoloEvento,
                IdMotivo = idMotivo,
                Detalhamento = descricao, 
                Descricao = motivo.Descricao,
                IdUsuarioLogado = UsuarioLogado.IdUsuario
            };
        }


        /// <summary>
        /// Retorna um base64 com o qrcode gerado a partir da viagem evento
        /// </summary>
        /// <param name="idViagemEvento"></param>
        /// <returns></returns>
        public object GerarQRCodeViagemEvento(int idViagemEvento)
        {
            var viagemEvento = _viagemEventoApp.Get(idViagemEvento);

            if (viagemEvento == null)
                throw new Exception("Não foi possível identificar o evento da viagem para gerar o QR Code");

            string qrCodeBase64 = "data:image/png;base64,";
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            if (new FileInfo(caminhoAplicacao + @"\Content\Image\logo-ats-login.png").Exists)
            {
                var image = (Bitmap)Image.FromFile(caminhoAplicacao + @"\Content\Image\logo-ats-login.png");
                qrCodeBase64 += new QRCodeHelper().GerarQRCode(image, viagemEvento.Token);
            }
            else
                qrCodeBase64 += new QRCodeHelper().GerarQRCode(null, viagemEvento.Token);

            return new { qrCode = qrCodeBase64, viagemEvento.Token };
        }
    }
}
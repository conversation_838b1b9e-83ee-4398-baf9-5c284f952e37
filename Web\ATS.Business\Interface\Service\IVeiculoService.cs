﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IVeiculoService : IService<Veiculo>
    {
        Veiculo GetWithAllChilds(int idVeciulo);
        ValidationResult Add(Veiculo veiculo);
        ValidationResult Update(Veiculo veiculo);
        Veiculo Get(int idVeiculo);
        Veiculo GetVeiculoPorPlaca(string placa, int? idEmpresa);
        Veiculo GetVeiculoPorPlacaPorEmpresa(string placa, int idEmpresa);
        IQueryable<Veiculo> GetVeiculosPorNumerosFrotas(List<long> numeroFrota);
        string ConvertToPadraoMercosul(string placa);
        string ConvertToPadraoBrasil(string placaMercosul);
        ConsultaPlacaDTO PlacaExistente(string placa, int? idEmpresa);
        object ConsultarGrid(int? idEmpresa, bool comTracao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultaDadosVeiculoResponseDTO ConsultaDadosVeiculo(int idVeiculo);
        IQueryable<Veiculo> QueryById(int idVeiculo);
        string GetRntrcProprietarioVeiculo(string placa, int idEmpresa);
        string GetRntrcProprietarioVeiculo(int idVeiculo);
        int? GetIdPorPlaca(string nPlaca, int? idEmpresa);

        TipoCarreta GetTipoCarreta(int idTipoCarreta);
        TipoCavalo GetTipoCavalo(int idTipoCavalo);
        VeiculoDTO Get(string placa, int? idEmpresa = null, bool? ativo = true, bool somenteTerceiros = false);
        Veiculo GetVeiculoPorEmpresaMotorista(int idEmpresa, int idMotorista);
        List<string> GetPlacasPorEmpresa(int idEmpresa, bool incluirTerceiros = false, int? idOperacao = null);
        IQueryable<Veiculo> Query(string placa, int? idEmpresa = null, bool? ativo = true, bool somenteTerceiros = false);
        IQueryable<Veiculo> GetVeiculosByListIdVeiculos(List<int> lIdVeiculo, int? idEmpresa = null);

        /// <summary>
        /// Retorna um veículo e filial a partir de sua placa
        /// </summary>
        /// <param name="placa"></param>
        /// <param name="idEmpresa"></param>
        /// <returns>Veiculo</returns>
        Veiculo GetCodigoFilialVeiculo(string placa, int? idEmpresa = null);
        
        bool VeiculoValidoIntegracao(string placa, int? idEmpresa = null);
        Veiculo GetVeiculoTerceiro(string placa);
        IQueryable<Veiculo> GetVeiculosPorPlaca(List<string> placas);
        IQueryable<Veiculo> GetTodosVeiculosPorPlaca(string placa, int idEmpresa);
        ValidationResult AlterarStatus(int idVeiculo);
        byte[] GerarRelatorioGridVeiculos(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao);
        object ConsultarGridVeiculoEmpresa(int? idEmpresa, int? idFilial, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool marcarTodos,
            int apertou);
        IQueryable<GridDadosVeiculoResponseDTO> GetDataToGridAndReport(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters);
        IQueryable<Veiculo> GetAll();
    }
}
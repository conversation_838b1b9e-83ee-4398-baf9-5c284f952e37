﻿using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IGrupoUsuarioMenuService
    {
        IQueryable<GrupoUsuarioMenu> GetAllMenusPorGrupoUsuario(int idGrupoUsuario);
        IQueryable<GrupoUsuarioMenu> GetAllPorIdMenu(int idMenu);
        ValidationResult DeletePorIdGrupoUsuario(int idGrupoUsuario);
        bool HasMenuLiberado(int idUsuario, EMenu idMenu);
    }
}
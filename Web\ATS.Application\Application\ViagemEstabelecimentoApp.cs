﻿using ATS.Domain.Entities;
using ATS.Domain.Service;
using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class ViagemEstabelecimentoApp : AppBase, IViagemEstabelecimentoApp
    {
        private readonly IViagemEstabelecimentoService _viagemEstabelecimentoService;

        public ViagemEstabelecimentoApp(IViagemEstabelecimentoService viagemEstabelecimentoService)
        {
            _viagemEstabelecimentoService = viagemEstabelecimentoService;
        }

        public List<ViagemEstabelecimento> GetPorViagem(int idViagem)
        {
            return _viagemEstabelecimentoService.GetPorViagem(idViagem);
        }
    }
}

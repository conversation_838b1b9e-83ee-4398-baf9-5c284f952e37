﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Webservice.Request.Estabelecimento
{
    public class EstabelecimentoCrud
    {
        public int? IdEstabelecimento { get; set; }
        public string Descricao { get; set; }
        public string RazaoSocial { get; set; }
        public double TaxaAntecipacao { get; set; } = 0;
        public int? IdTipoEstabelecimento { get; set; }
        public string CEP { get; set; }
        public int? IdPais { get; set; }
        public int? IdEmpresa { get; set; }
        public List<int> IdsEmpresa { get; set; } = new List<int>();
        public int? IdEstado { get; set; }
        public int? IdCidade { get; set; }
        public string Logradouro { get; set; }
        public string CNPJEstabelecimento { get; set; }
        public string Email { get; set; }
        public string EmailProtocolo { get; set; }
        public string Bairro { get; set; }
        public int? Numero { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public DateTime? DataUltimaAtualizacao { get; set; }
        public List<EstabelecimentoProdutoCrud> Produtos { get; set; }
        public string Complemento { get; set; }
        public List<int> IdsExcluidos { get; set; } = new List<int>();
        public List<int> IdsContasBancariasExcluir { get; set; } = new List<int>();
        public bool Associacao { get; set; } = false;
        public bool LiberaProtocolos { get; set; }
        public bool PermiteAlterarDocumentosPesoChegada { get; set; }
        public bool PagamentoAntecipado { get; set; }
        public string Telefone { get; set; }
        public bool PontoReferencia { get; set; }


        /// <summary>
        /// Parâmetro que obriga o anexo de documentos na tela de pagamento de frete
        /// </summary>
        public bool ObrigaDocumentosPagamento { get; set; } = true;

        public bool RealizarCredenciamento { get; set; }

        public List<Associados> Associacoes { get; set; }

        public List<ContasBancarias> EstabelecimentoContasBancarias { get; set; }

        public TimeSpan? HoraInicialSemValidarChave { get; set; }

        public TimeSpan? HoraFinalSemValidarChave { get; set; }

        public bool RealizaPagamentoComCheque { get; set; }

        public int? IdFilialProcessoCaixaTms { get; set; }

        public List<Documento> Documentos { get; set; }

        public int AdministradoraPlataforma { get; set; }
        public bool? ValidarChavePagamento { get; set; }
    }

    public class EstabelecimentoProdutoCrud
    {
        public int IdProduto { get; set; }
        public int IdProdutoBase { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public string Descricao { get; set; }
        public string UnidadeMedida { get; set; }
        public string PrecoUnidade { get; set; }
        public string PrecoPromocional { get; set; }
        public bool Contrato { get; set; }
        //public bool Modificado { get; set; } = false;
    }

    public class Associados
    {
        public int IdEstabelecimento { get; set; }
    }

    public class ContasBancarias
    {
        public int IdEstabelecimentoBaseContaBancaria { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public string NomeConta { get; set; }
        public string CodigoBanco { get; set; }
        public string NomeBanco { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string DigitoConta { get; set; }
        public ETipoContaBancaria TipoConta { get; set; }
        public string CnpjTitular { get; set; }
        public string NomeTitular { get; set; }
    }

    public class Documento
    {
        public int IdEstabelecimentoBaseDocumento { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public int? IdDocumento { get; set; }
        public string Token { get; set; }
        public bool PermiteEditarData { get; set; }
        public int? DiasValidade { get; set; }
        public string DataValidade { get; set; }
        public string Descricao { get; set; }
        public bool Anexou { get; set; } = false;
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Common;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.WS.Models.Webservice.Request.Cte;
using ATS.Domain.Service;
using AutoMapper;

namespace ATS.WS.Services
{
    public class SrvCte : SrvBase
    {
        private readonly ICteApp _cteApp;
        private readonly IEmpresaApp _empresaApp;

        public SrvCte(ICteApp cteApp, IEmpresaApp empresaApp)
        {
            _cteApp = cteApp;
            _empresaApp = empresaApp;
        }

        public Retorno<List<Cte>> Consultar(ConsultarCteRequest request)
        {
            var validoParaConsulta = ValidarConsulta(request);

            if (!validoParaConsulta.IsValid)
                return new Retorno<List<Cte>>(false, validoParaConsulta.ToString(), null);

            //var idEmpresa = _empresaApp.GetIdPorCNPJ(request.CNPJEmpresa);

            //if (!idEmpresa.HasValue)
            //    return new Retorno<List<Cte>>(false, "Não foi possível identificar a empresa. ", null);

            var result = _cteApp.Consultar(request.CpfMotorista, 
                request.DataInicial, request.DataFinal);

            return new Retorno<List<Cte>>(true, result.ToList());
        }

        public Retorno<Cte> Integrar(IntegrarCteRequest @params)
        {
            Cte cte = Mapper.Map<IntegrarCteRequest, Cte>(@params);
            var result = _cteApp.Add(cte);

            var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            if (!idEmpresa.HasValue)
                return new Retorno<Cte>(false, "Não foi possível identificar a empresa. ", null);

            if (string.IsNullOrEmpty(@params.XmlCte))
                return new Retorno<Cte>(false, "Xml CTE não informado. ", null);

            if (string.IsNullOrEmpty(@params.CpfMotorista))
                return new Retorno<Cte>(false, "CPF do motorista não informado. ", null);

            if (result.IsValid)
                return new Retorno<Cte>(true, "Cte integrado com sucesso. ", cte);

            return new Retorno<Cte>(false, "Falha ao integrar Cte", null);
        }
        

        public Retorno<Cte> Alterar(AlterarCteRequest @params)
        {
            if (!string.IsNullOrEmpty(@params.CNPJEmpresa))
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (!idEmpresa.HasValue)
                    return new Retorno<Cte>(false, "Não foi possível identificar a empresa. ", null);
            }
            

            if (@params.Status == null)
                return new Retorno<Cte>(false, "Status não informado. ", null);

            if (@params.IdCte == 0)
                return new Retorno<Cte>(false, "Código Cte não informado. ", null);

            var cte = _cteApp.ConsultaPorId(@params.IdCte);
            if (cte == null)
                return new Retorno<Cte>(false, "Cte não encontrado. ", null);

            cte.Status = @params.Status ?? 0;

            _cteApp.Update(cte);

            return new Retorno<Cte>(true, "Cte alterado com sucesso. ", null);
        }



        private static ValidationResult ValidarConsulta(ConsultarCteRequest request)
        {
            var validationResult = new ValidationResult();

            

            if (request.DataFinal.HasValue)
            {
                if(request.DataFinal < request.DataInicial)
                    validationResult.Add("Data final precisa ser superior a data inicial. ");
            }

            //if (string.IsNullOrWhiteSpace(request.CNPJEmpresa))
            //{
            //    validationResult.Add("CNPJ da empresa é obrigatório. ");
            //}

            if (string.IsNullOrWhiteSpace(request.CpfMotorista))
            {
                validationResult.Add("CPF do motorista é obrigatório. ");
            }

            return validationResult;
        }
    }
}
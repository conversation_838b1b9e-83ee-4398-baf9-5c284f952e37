﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Web.Mvc;

namespace ATS.WS.Controllers
{
    public class PreCadastroController : BaseController
    {
        
        [HttpPost]
        public JsonResult Integrar(PreUsuarioModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !new AutenticacaoAplicacaoApp().AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();
                
                var retorno = new SrvPreUsuario().Integrar(@params);

                return Responde(retorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        public JsonResult VerificarCelular(PreCadastroCelularRequestModel @params)
        {
            if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !new AutenticacaoAplicacaoApp().AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                return TokenInvalido();

            return Responde(new SrvPreUsuario().VerificarCelular(@params));
        }

        [HttpGet]
        public JsonResult ValidarPin(PreCadastroValidarPINRequestModel @params)
        {
            if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !new AutenticacaoAplicacaoApp().AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                return TokenInvalido();

            return Responde(new SrvPreUsuario().VerificarPin(@params));
        }

        /// <summary>
        /// Altera a senha do usuário
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult AlterarSenha(SenhaRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !new AutenticacaoAplicacaoApp().AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return  TokenInvalido();

                return Responde(
                    new SrvPreUsuario().AlterarSenha(@params));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return Mensagem(e.Message);
            }
        }
    }
}
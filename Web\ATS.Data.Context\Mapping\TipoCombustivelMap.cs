using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class TipoCombustivelMap : EntityTypeConfiguration<TipoCombustivel>
    {
        public TipoCombustivelMap()
        {
            ToTable("TIPO_COMBUSTIVEL");

            HasKey(t => t.IdTipoCombustivel);

            Property(t => t.IdTipoCombustivel)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .IsRequired()
                .HasMaxLength(100);
        }
    }
}
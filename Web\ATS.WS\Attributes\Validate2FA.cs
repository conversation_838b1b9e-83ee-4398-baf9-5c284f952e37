﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.Webservice.Request.Usuario;
using ATS.WS.Services;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class Validate2FA : Attribute
    {
        public static void ExecutarValidacao(ActionExecutingContext filterContext, IUserIdentity userIdentity)
        {
            #region Casos em que a validação não é necessária

            if (UsuarioParametrizadoParaNaoUtilizar2Fa(userIdentity))
                return;

            if (SalvarViagemSemPagamentos(filterContext))
                return;

            if (BaixarViagemEventoSemCartao(filterContext))
                return;

            if (SalvarUsuarioMotoristaOuProprietario(filterContext))
                return;

            if (SalvarProprietarioSemPermissaoPix(filterContext))
                return;

            #endregion

            var sessionKey = filterContext.HttpContext.Request.Headers["SessionKey"];
            var codigo2Fa = filterContext.HttpContext.Request.Headers["Codigo2FA"];
            if (string.IsNullOrWhiteSpace(sessionKey) || string.IsNullOrWhiteSpace(codigo2Fa))
            {
                filterContext.Result = new JsonResult
                {
                    Data = new
                    {
                        message = "Falha na validação de dois fatores.",
                        success = false,
                        warning = false
                    },
                    JsonRequestBehavior = JsonRequestBehavior.AllowGet
                };
                return;
            }

            var srvUsuario = DependencyResolver.Current.GetService<SrvUsuario>();
            var requer2FA = srvUsuario.GetItemPermissaoFinanceiro(userIdentity.IdUsuario);
            if (!requer2FA.DesbloquearFinanceiro)
                return;

            var keycloak = DependencyResolver.Current.GetService<KeycloakHelper>();
            var valid = keycloak.Validate2fa(sessionKey, codigo2Fa);
            if (valid == "OK")
                return;

            filterContext.Result = new JsonResult
            {
                Data = new
                {
                    message = "Código inválido.",
                    success = false,
                    warning = false
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        #region Métodos de validação onde 2fa não é necessário

        /// <summary>
        /// Quando o usuário está com o parâmetro exigeAutenticação2FA false
        /// </summary>
        /// <param name="userIdentity"></param>
        /// <returns></returns>
        private static bool UsuarioParametrizadoParaNaoUtilizar2Fa(IUserIdentity userIdentity)
        {
            try
            {
                var usuarioPermissaoFinanceiro =
                    DependencyResolver.Current.GetService<IUsuarioPermissaoFinanceiroApp>();

                var item = usuarioPermissaoFinanceiro.GetParametroPermissaoFinanceiro(userIdentity.IdUsuario,
                    EBloqueioFinanceiroTipo.exigeAutenticação2FA);

                if (item == null) return false;

                //se tá true é pq exige 2fa então esse método tem q retornar false
                if (item.DesbloquearFinanceiro)
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Quando o usuário está integrando uma viagem ou atualizando uma já existente com as parcelas pagas sem cartao nem pix
        /// </summary>
        /// <param name="filterContext"></param>
        /// <returns></returns>
        private static bool SalvarViagemSemPagamentos(ActionExecutingContext filterContext)
        {
            try
            {
                var metodo = filterContext.ActionDescriptor.ActionName;
                var controller = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName;

                if (controller != "ViagemAts")
                    return false;

                if (metodo != "Salvar")
                    return false;

                var request = (ViagemIntegrarRequest)filterContext.ActionParameters.First().Value;

                //Validar 2fa se tá integrando uma viagem nova com algum fornecedor
                if (request.Pedagio != null && request.Pedagio.Fornecedor != FornecedorEnum.Desabilitado &&
                    request.IdViagem == null)
                    return false;

                //Validar 2fa se tá integrando parcelas novas baixadas pra pagar com cartão ou pix
                if (request.Parcelas != null && request.Parcelas.Any(c =>
                        c.IdViagemEvento == null && (c.FormaPagamento == EViagemEventoFormaPagamento.Cartao || c.FormaPagamento == EViagemEventoFormaPagamento.Pix) && c.Status == EStatusViagemEvento.Baixado))
                    return false;

                //Validar 2fa se tá marcando uma parcela existente para baixada e pra pagar com cartão ou pix
                var viagemEventoRepository = DependencyResolver.Current.GetService<IViagemEventoRepository>();
                var parcelasExistentesMarcadasParaBaixa = request.Parcelas?.Where(c =>
                        c.IdViagemEvento != null && c.Status == EStatusViagemEvento.Baixado && (c.FormaPagamento == EViagemEventoFormaPagamento.Cartao || 
                            c.FormaPagamento == EViagemEventoFormaPagamento.Pix))
                    .Select(c => c.IdViagemEvento.Value).ToList() ?? new List<int>();
                if (parcelasExistentesMarcadasParaBaixa.Any() &&
                    viagemEventoRepository.AnyParcelaNaoBaixada(parcelasExistentesMarcadasParaBaixa))
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Quando o usuário está baixando uma parcela marcada com pagamento sem cartao
        /// </summary>
        /// <param name="filterContext"></param>
        /// <returns></returns>
        private static bool BaixarViagemEventoSemCartao(ActionExecutingContext filterContext)
        {
            try
            {
                var metodo = filterContext.ActionDescriptor.ActionName;
                var controller = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName;

                if (controller != "ViagemAts")
                    return false;

                if (metodo != "BaixarEvento")
                    return false;

                var request = (ViagemBaixarEventoRequest)filterContext.ActionParameters.First().Value;

                if (request.FormaPagamento == EViagemEventoFormaPagamento.Cartao || request.FormaPagamento == EViagemEventoFormaPagamento.Pix)
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Quando o usuário está está tentando salvar/cadastrar um usuario Motorista ou Proprietário (usuário pro app)
        /// </summary>
        /// <param name="filterContext"></param>
        /// <returns></returns>
        private static bool SalvarUsuarioMotoristaOuProprietario(ActionExecutingContext filterContext)
        {
            try
            {
                var metodo = filterContext.ActionDescriptor.ActionName;
                var controller = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName;

                if (controller != "UsuarioAts")
                    return false;

                if (metodo != "CadastrarAtualizar" && metodo != "CadastrarParaNovoEstabelecimento")
                    return false;

                var request = (CadastrarAtualizarCls)filterContext.ActionParameters.First().Value;

                //Validar 2fa se tá cadastrando um usuário que não seja motorista nem proprietário
                if (request.perfil != EPerfil.Motorista && request.perfil != EPerfil.Proprietario)
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Quando o usuário está está tentando salvar/cadastrar um usuario Motorista ou Proprietário (usuário pro app)
        /// </summary>
        /// <param name="filterContext"></param>
        /// <returns></returns>
        private static bool SalvarProprietarioSemPermissaoPix(ActionExecutingContext filterContext)
        {
            try
            {
                var metodo = filterContext.ActionDescriptor.ActionName;
                var controller = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName;

                if (controller != "ProprietarioAts")
                    return false;

                if (metodo != "Cadastrar")
                    return false;

                var request = (ProprietarioCreateModel)filterContext.ActionParameters.First().Value;

                //Validar 2fa se tá cadastrando um proprietário que tem permissao pix 
                var parametrosProprietarioService = DependencyResolver.Current.GetService<IParametrosProprietarioService>();
                if (!parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(request.CNPJCPF, request.IdEmpresa))
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        #endregion
    }
}
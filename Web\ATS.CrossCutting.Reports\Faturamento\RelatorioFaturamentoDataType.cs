﻿using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Faturamento
{
    public class RelatorioFaturamentoDataType
    {
        public List<EmpresaFaturamento> DataGrid { get; set; }
    }

    public class EmpresaFaturamento
    {
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string QtdViagens { get; set; }
        public string Adiantamento { get; set; }
        public string Saldo { get; set; }
        public string TarifaANTT { get; set; }
        public string Estadias { get; set; }
        public string Abastecimento { get; set; }
        public string Pedagio { get; set; }
        public string CargaAvulsa { get; set; }
        public string Total { get; set; }
        public string <PERSON><PERSON><PERSON>s { get; set; }
        public string StatusEmpresa { get; set; }
    }
}

﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

using System.Web;
using ATS.Data.Repository.External.SistemaInfo;

namespace SistemaInfo.MicroServices.Rest.Cartao.WebClient
{
    #pragma warning disable // Disable all warnings

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class AcessoPortadorClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public AcessoPortadorClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<AcessarPaginaSaldoExtratoResponse> AcessarPaginaSaldoExtratoAsync(AcessarPaginaSaldoExtratoRequest request)
        {
            return AcessarPaginaSaldoExtratoAsync(request, System.Threading.CancellationToken.None);
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public AcessarPaginaSaldoExtratoResponse AcessarPaginaSaldoExtrato(AcessarPaginaSaldoExtratoRequest request)
        {
            return System.Threading.Tasks.Task.Run(async () => await AcessarPaginaSaldoExtratoAsync(request, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<AcessarPaginaSaldoExtratoResponse> AcessarPaginaSaldoExtratoAsync(AcessarPaginaSaldoExtratoRequest request, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/AcessoPortador/AcessarPaginaSaldoExtrato");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(AcessarPaginaSaldoExtratoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<AcessarPaginaSaldoExtratoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(AcessarPaginaSaldoExtratoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consulta um extrato do cartão</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<DataSourceResult> ConsultarExtratoAsync(object gridRequest, int cartao_Identificador, int cartao_Produto, System.DateTime dataInicio, System.DateTime dataFim, string tipo, bool exibirMetadados, string x_web_auth_token)
        {
            return ConsultarExtratoAsync(gridRequest, cartao_Identificador, cartao_Produto, dataInicio, dataFim, tipo, exibirMetadados, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consulta um extrato do cartão</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public DataSourceResult ConsultarExtrato(object gridRequest, int cartao_Identificador, int cartao_Produto, System.DateTime dataInicio, System.DateTime dataFim, string tipo, bool exibirMetadados, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarExtratoAsync(gridRequest, cartao_Identificador, cartao_Produto, dataInicio, dataFim, tipo, exibirMetadados, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consulta um extrato do cartão</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<DataSourceResult> ConsultarExtratoAsync(object gridRequest, int cartao_Identificador, int cartao_Produto, System.DateTime dataInicio, System.DateTime dataFim, string tipo, bool exibirMetadados, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (cartao_Identificador == null)
                throw new System.ArgumentNullException("cartao_Identificador");
    
            if (cartao_Produto == null)
                throw new System.ArgumentNullException("cartao_Produto");
    
            if (dataInicio == null)
                throw new System.ArgumentNullException("dataInicio");
    
            if (dataFim == null)
                throw new System.ArgumentNullException("dataFim");
    
            if (exibirMetadados == null)
                throw new System.ArgumentNullException("exibirMetadados");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/AcessoPortador/ConsultarExtrato?");
            if (gridRequest != null) 
            {
                urlBuilder_.Append("gridRequest=").Append(System.Uri.EscapeDataString(ConvertToString(gridRequest, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            urlBuilder_.Append("Cartao.Identificador=").Append(System.Uri.EscapeDataString(ConvertToString(cartao_Identificador, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Append("Cartao.Produto=").Append(System.Uri.EscapeDataString(ConvertToString(cartao_Produto, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Append("DataInicio=").Append(System.Uri.EscapeDataString(dataInicio.ToString("s", System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Append("DataFim=").Append(System.Uri.EscapeDataString(dataFim.ToString("s", System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (tipo != null) 
            {
                urlBuilder_.Append("Tipo=").Append(System.Uri.EscapeDataString(ConvertToString(tipo, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            urlBuilder_.Append("ExibirMetadados=").Append(System.Uri.EscapeDataString(ConvertToString(exibirMetadados, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(DataSourceResult); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<DataSourceResult>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(DataSourceResult);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Retorna o saldo inicial do período da consulta</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ConsultarSaldoAsync(ConsultarExtratoRequest request, string x_web_auth_token)
        {
            return ConsultarSaldoAsync(request, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Retorna o saldo inicial do período da consulta</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void ConsultarSaldo(ConsultarExtratoRequest request, string x_web_auth_token)
        {
            System.Threading.Tasks.Task.Run(async () => await ConsultarSaldoAsync(request, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Retorna o saldo inicial do período da consulta</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ConsultarSaldoAsync(ConsultarExtratoRequest request, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/AcessoPortador/ConsultarSaldo");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar o saldo do cartão</summary>
        /// <param name="request">Objeto de consulta de saldo</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSaldoCartaoResponse> ConsultarSaldoCartaoAsync(ConsultarSaldoCartaoRequest request, string x_web_auth_token)
        {
            return ConsultarSaldoCartaoAsync(request, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar o saldo do cartão</summary>
        /// <param name="request">Objeto de consulta de saldo</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSaldoCartaoResponse ConsultarSaldoCartao(ConsultarSaldoCartaoRequest request, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarSaldoCartaoAsync(request, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar o saldo do cartão</summary>
        /// <param name="request">Objeto de consulta de saldo</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarSaldoCartaoResponse> ConsultarSaldoCartaoAsync(ConsultarSaldoCartaoRequest request, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/AcessoPortador/ConsultarSaldoCartao");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarSaldoCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultarSaldoCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class AtualizarClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public AtualizarClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<string> GetScriptAsync()
        {
            return GetScriptAsync(System.Threading.CancellationToken.None);
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public string GetScript()
        {
            return System.Threading.Tasks.Task.Run(async () => await GetScriptAsync(System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<string> GetScriptAsync(System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/GetScript");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(string); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<string>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(string);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ExecutarAsync()
        {
            return ExecutarAsync(System.Threading.CancellationToken.None);
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void Executar()
        {
            System.Threading.Tasks.Task.Run(async () => await ExecutarAsync(System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ExecutarAsync(System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/Executar");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ExecutarSeedAsync()
        {
            return ExecutarSeedAsync(System.Threading.CancellationToken.None);
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void ExecutarSeed()
        {
            System.Threading.Tasks.Task.Run(async () => await ExecutarSeedAsync(System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ExecutarSeedAsync(System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/ExecutarSeed");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<bool> PossuiAtualizacaoPendenteAsync()
        {
            return PossuiAtualizacaoPendenteAsync(System.Threading.CancellationToken.None);
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public bool PossuiAtualizacaoPendente()
        {
            return System.Threading.Tasks.Task.Run(async () => await PossuiAtualizacaoPendenteAsync(System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<bool> PossuiAtualizacaoPendenteAsync(System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/PossuiAtualizacaoPendente");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(bool); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<bool>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(bool);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class AuthClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public AuthClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<LoginWebResponse> LoginAsync(string usuario, string senha, bool administradoraRequired, bool empresaRequired)
        {
            return LoginAsync(usuario, senha, administradoraRequired, empresaRequired, System.Threading.CancellationToken.None);
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public LoginWebResponse Login(string usuario, string senha, bool administradoraRequired, bool empresaRequired)
        {
            return System.Threading.Tasks.Task.Run(async () => await LoginAsync(usuario, senha, administradoraRequired, empresaRequired, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<LoginWebResponse> LoginAsync(string usuario, string senha, bool administradoraRequired, bool empresaRequired, System.Threading.CancellationToken cancellationToken)
        {
            if (usuario == null)
                throw new System.ArgumentNullException("usuario");
    
            if (senha == null)
                throw new System.ArgumentNullException("senha");
    
            if (administradoraRequired == null)
                throw new System.ArgumentNullException("administradoraRequired");
    
            if (empresaRequired == null)
                throw new System.ArgumentNullException("empresaRequired");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Auth/Login/{usuario}/{senha}?");
            urlBuilder_.Replace("{usuario}", System.Uri.EscapeDataString(ConvertToString(usuario, System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Replace("{senha}", System.Uri.EscapeDataString(ConvertToString(senha, System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Append("administradoraRequired=").Append(System.Uri.EscapeDataString(ConvertToString(administradoraRequired, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Append("empresaRequired=").Append(System.Uri.EscapeDataString(ConvertToString(empresaRequired, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(LoginWebResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<LoginWebResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(LoginWebResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class Client : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public Client(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Busca cartão através do identificador e produto.
        /// Esta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.
        /// Ou seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultaCartaoResponse> CartoesGetAsync(int identificador, int produto, string x_web_auth_token)
        {
            return CartoesGetAsync(identificador, produto, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca cartão através do identificador e produto.
        /// Esta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.
        /// Ou seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultaCartaoResponse CartoesGet(int identificador, int produto, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CartoesGetAsync(identificador, produto, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca cartão através do identificador e produto.
        /// Esta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.
        /// Ou seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultaCartaoResponse> CartoesGetAsync(int identificador, int produto, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (identificador == null)
                throw new System.ArgumentNullException("identificador");
    
            if (produto == null)
                throw new System.ArgumentNullException("produto");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/{identificador}/{produto}");
            urlBuilder_.Replace("{identificador}", System.Uri.EscapeDataString(ConvertToString(identificador, System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Replace("{produto}", System.Uri.EscapeDataString(ConvertToString(produto, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultaCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ == "204") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("N\u00e3o existe cart\u00e3o com os dados indicados", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultaCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Buscar todos os cartões da administradora</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<CartaoResponse>> CartoesGetAsync(System.DateTime? data, string x_web_auth_token)
        {
            return CartoesGetAsync(data, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar todos os cartões da administradora</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<CartaoResponse> CartoesGet(System.DateTime? data, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CartoesGetAsync(data, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar todos os cartões da administradora</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<CartaoResponse>> CartoesGetAsync(System.DateTime? data, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes?");
            if (data != null) 
            {
                urlBuilder_.Append("data=").Append(System.Uri.EscapeDataString(data.Value.ToString("s", System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<CartaoResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<CartaoResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<CartaoResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca todas as pessoas cadastradas (Somente para testes)</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<ConsultarPessoaResponse>> PessoasAsync(string x_web_auth_token)
        {
            return PessoasAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todas as pessoas cadastradas (Somente para testes)</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<ConsultarPessoaResponse> Pessoas(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await PessoasAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todas as pessoas cadastradas (Somente para testes)</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<ConsultarPessoaResponse>> PessoasAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<ConsultarPessoaResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<ConsultarPessoaResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<ConsultarPessoaResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca todas as processadoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<ProcessadoraDTOResponse>> ProcessadoraGetAsync(string x_web_auth_token)
        {
            return ProcessadoraGetAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todas as processadoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<ProcessadoraDTOResponse> ProcessadoraGet(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProcessadoraGetAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todas as processadoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<ProcessadoraDTOResponse>> ProcessadoraGetAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<ProcessadoraDTOResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<ProcessadoraDTOResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<ProcessadoraDTOResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Exclui uma Processadora</summary>
        /// <param name="id">Id da processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebActionResponse> ProcessadoraDeleteAsync(int id, string x_web_auth_token)
        {
            return ProcessadoraDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Exclui uma Processadora</summary>
        /// <param name="id">Id da processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebActionResponse ProcessadoraDelete(int id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProcessadoraDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Exclui uma Processadora</summary>
        /// <param name="id">Id da processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebActionResponse> ProcessadoraDeleteAsync(int id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (id == null)
                throw new System.ArgumentNullException("id");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora?");
            urlBuilder_.Append("id=").Append(System.Uri.EscapeDataString(ConvertToString(id, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("DELETE");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebActionResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebActionResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(WebActionResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca uma processadora por Id</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ProcessadoraDTOResponse> ProcessadoraGetAsync(int id, string x_web_auth_token)
        {
            return ProcessadoraGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca uma processadora por Id</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ProcessadoraDTOResponse ProcessadoraGet(int id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProcessadoraGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca uma processadora por Id</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ProcessadoraDTOResponse> ProcessadoraGetAsync(int id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (id == null)
                throw new System.ArgumentNullException("id");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora/{id}");
            urlBuilder_.Replace("{id}", System.Uri.EscapeDataString(ConvertToString(id, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ProcessadoraDTOResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ProcessadoraDTOResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ProcessadoraDTOResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca todos os Tipos de Transação cadastrados</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<TipoTransacaoDTOResponse>> TipoTransacaoGetAsync(string x_web_auth_token)
        {
            return TipoTransacaoGetAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todos os Tipos de Transação cadastrados</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<TipoTransacaoDTOResponse> TipoTransacaoGet(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await TipoTransacaoGetAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todos os Tipos de Transação cadastrados</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<TipoTransacaoDTOResponse>> TipoTransacaoGetAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<TipoTransacaoDTOResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<TipoTransacaoDTOResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<TipoTransacaoDTOResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Remove um Tipo de Transacao</summary>
        /// <param name="id">Id da transacao a ser removida</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebActionResponse> TipoTransacaoDeleteAsync(int? id, string x_web_auth_token)
        {
            return TipoTransacaoDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Remove um Tipo de Transacao</summary>
        /// <param name="id">Id da transacao a ser removida</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebActionResponse TipoTransacaoDelete(int? id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await TipoTransacaoDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Remove um Tipo de Transacao</summary>
        /// <param name="id">Id da transacao a ser removida</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebActionResponse> TipoTransacaoDeleteAsync(int? id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(id, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("DELETE");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebActionResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebActionResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(WebActionResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca um Tipo de Transacao com base no Id</summary>
        /// <param name="id">Id do tipo de transacao</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<TipoTransacaoDTOResponse> TipoTransacaoGetAsync(int id, string x_web_auth_token)
        {
            return TipoTransacaoGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca um Tipo de Transacao com base no Id</summary>
        /// <param name="id">Id do tipo de transacao</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public TipoTransacaoDTOResponse TipoTransacaoGet(int id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await TipoTransacaoGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca um Tipo de Transacao com base no Id</summary>
        /// <param name="id">Id do tipo de transacao</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<TipoTransacaoDTOResponse> TipoTransacaoGetAsync(int id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (id == null)
                throw new System.ArgumentNullException("id");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao/{id}");
            urlBuilder_.Replace("{id}", System.Uri.EscapeDataString(ConvertToString(id, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(TipoTransacaoDTOResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoDTOResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(TipoTransacaoDTOResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class CartoesClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public CartoesClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Cadastra um novo cartão</summary>
        /// <param name="cartaoCadastrarRequest">DTO de integração contendo dados de pessoa e cartão</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CartaoCadastrarResponse> CadastrarAsync(CartaoCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return CadastrarAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cadastra um novo cartão</summary>
        /// <param name="cartaoCadastrarRequest">DTO de integração contendo dados de pessoa e cartão</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CartaoCadastrarResponse Cadastrar(CartaoCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CadastrarAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Cadastra um novo cartão</summary>
        /// <param name="cartaoCadastrarRequest">DTO de integração contendo dados de pessoa e cartão</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CartaoCadastrarResponse> CadastrarAsync(CartaoCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/Cadastrar");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(cartaoCadastrarRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CartaoCadastrarResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoCadastrarResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(CartaoCadastrarResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.</summary>
        /// <param name="cartaoCadastrarRequest">cartao a ser cadastrado</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CartaoVirtualCadastrarResponse> CadastrarCartaoVirtualAsync(CartaoVirtualCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return CadastrarCartaoVirtualAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.</summary>
        /// <param name="cartaoCadastrarRequest">cartao a ser cadastrado</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CartaoVirtualCadastrarResponse CadastrarCartaoVirtual(CartaoVirtualCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CadastrarCartaoVirtualAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.</summary>
        /// <param name="cartaoCadastrarRequest">cartao a ser cadastrado</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CartaoVirtualCadastrarResponse> CadastrarCartaoVirtualAsync(CartaoVirtualCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/CadastrarCartaoVirtual");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(cartaoCadastrarRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CartaoVirtualCadastrarResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVirtualCadastrarResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(CartaoVirtualCadastrarResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Método responsável por importar cartoes a  partir de um arquivo</summary>
        /// <param name="file">Upload File</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ImportarCartoesAsync(int administradoraId, FileParameter file, string x_web_auth_token)
        {
            return ImportarCartoesAsync(administradoraId, file, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Método responsável por importar cartoes a  partir de um arquivo</summary>
        /// <param name="file">Upload File</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void ImportarCartoes(int administradoraId, FileParameter file, string x_web_auth_token)
        {
            System.Threading.Tasks.Task.Run(async () => await ImportarCartoesAsync(administradoraId, file, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Método responsável por importar cartoes a  partir de um arquivo</summary>
        /// <param name="file">Upload File</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ImportarCartoesAsync(int administradoraId, FileParameter file, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (administradoraId == null)
                throw new System.ArgumentNullException("administradoraId");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/ImportarCartoes?");
            urlBuilder_.Append("administradoraId=").Append(System.Uri.EscapeDataString(ConvertToString(administradoraId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var boundary_ = System.Guid.NewGuid().ToString();
                    var content_ = new System.Net.Http.MultipartFormDataContent(boundary_);
                    content_.Headers.Remove("Content-Type");
                    content_.Headers.TryAddWithoutValidation("Content-Type", "multipart/form-data; boundary=" + boundary_);
                    if (file == null)
                        throw new System.ArgumentNullException("file");
                    else
                        content_.Add(new System.Net.Http.StreamContent(file.Data), "file", file.FileName ?? "file");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Buscar todas as administradoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<Administradora>> GetAdministradorasAsync(string x_web_auth_token)
        {
            return GetAdministradorasAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar todas as administradoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<Administradora> GetAdministradoras(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await GetAdministradorasAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar todas as administradoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<Administradora>> GetAdministradorasAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/GetAdministradoras");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<Administradora>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<Administradora>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<Administradora>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Realiza a consulta dos dados na processadora a partir de um Hash de 16 dígitos criptografado</summary>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarCartaoProcessadoraResponse> ConsultarCartaoProcessadoraAsync(ConsultarCartaoProcessadoraRequest request)
        {
            return ConsultarCartaoProcessadoraAsync(request, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Realiza a consulta dos dados na processadora a partir de um Hash de 16 dígitos criptografado</summary>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarCartaoProcessadoraResponse ConsultarCartaoProcessadora(ConsultarCartaoProcessadoraRequest request)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarCartaoProcessadoraAsync(request, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Realiza a consulta dos dados na processadora a partir de um Hash de 16 dígitos criptografado</summary>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarCartaoProcessadoraResponse> ConsultarCartaoProcessadoraAsync(ConsultarCartaoProcessadoraRequest request, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/ConsultarCartaoProcessadora");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarCartaoProcessadoraResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarCartaoProcessadoraResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultarCartaoProcessadoraResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class DashboardClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public DashboardClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<IndicadorTransacaoSemanaResponse> IndicadoresTransacoesSemanaAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return IndicadoresTransacoesSemanaAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public IndicadorTransacaoSemanaResponse IndicadoresTransacoesSemana(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IndicadoresTransacoesSemanaAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<IndicadorTransacaoSemanaResponse> IndicadoresTransacoesSemanaAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (offsetWeeks == null)
                throw new System.ArgumentNullException("offsetWeeks");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Dashboard/IndicadoresTransacoesSemana?");
            urlBuilder_.Append("offsetWeeks=").Append(System.Uri.EscapeDataString(ConvertToString(offsetWeeks, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (administradoraId != null) 
            {
                urlBuilder_.Append("administradoraId=").Append(System.Uri.EscapeDataString(ConvertToString(administradoraId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            if (empresaId != null) 
            {
                urlBuilder_.Append("empresaId=").Append(System.Uri.EscapeDataString(ConvertToString(empresaId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            if (produtoId != null) 
            {
                urlBuilder_.Append("produtoId=").Append(System.Uri.EscapeDataString(ConvertToString(produtoId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(IndicadorTransacaoSemanaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(IndicadorTransacaoSemanaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<IndicadorTransacaoSemanaGraficoResponse> IndicadoresTransacoesSemanaGraficoAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return IndicadoresTransacoesSemanaGraficoAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public IndicadorTransacaoSemanaGraficoResponse IndicadoresTransacoesSemanaGrafico(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IndicadoresTransacoesSemanaGraficoAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<IndicadorTransacaoSemanaGraficoResponse> IndicadoresTransacoesSemanaGraficoAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (offsetWeeks == null)
                throw new System.ArgumentNullException("offsetWeeks");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Dashboard/IndicadoresTransacoesSemanaGrafico?");
            urlBuilder_.Append("offsetWeeks=").Append(System.Uri.EscapeDataString(ConvertToString(offsetWeeks, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (administradoraId != null) 
            {
                urlBuilder_.Append("administradoraId=").Append(System.Uri.EscapeDataString(ConvertToString(administradoraId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            if (empresaId != null) 
            {
                urlBuilder_.Append("empresaId=").Append(System.Uri.EscapeDataString(ConvertToString(empresaId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            if (produtoId != null) 
            {
                urlBuilder_.Append("produtoId=").Append(System.Uri.EscapeDataString(ConvertToString(produtoId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            }
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(IndicadorTransacaoSemanaGraficoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaGraficoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(IndicadorTransacaoSemanaGraficoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar saldo atual da empresa na BIZ</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSaldoEmpresaResponse> GetSaldoEmpresaAsync(string x_web_auth_token)
        {
            return GetSaldoEmpresaAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar saldo atual da empresa na BIZ</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSaldoEmpresaResponse GetSaldoEmpresa(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await GetSaldoEmpresaAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar saldo atual da empresa na BIZ</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarSaldoEmpresaResponse> GetSaldoEmpresaAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Dashboard/GetSaldoEmpresa");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarSaldoEmpresaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoEmpresaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultarSaldoEmpresaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class EmpresasClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public EmpresasClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Consultar saldo existente na conta de adiantamneto da empresa na processadora de cartões</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSaldoEmpresaResponse> ConsultarSaldoEmpresaAsync(string cnpj, string x_web_auth_token)
        {
            return ConsultarSaldoEmpresaAsync(cnpj, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar saldo existente na conta de adiantamneto da empresa na processadora de cartões</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSaldoEmpresaResponse ConsultarSaldoEmpresa(string cnpj, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarSaldoEmpresaAsync(cnpj, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar saldo existente na conta de adiantamneto da empresa na processadora de cartões</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarSaldoEmpresaResponse> ConsultarSaldoEmpresaAsync(string cnpj, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (cnpj == null)
                throw new System.ArgumentNullException("cnpj");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Empresas/ConsultarSaldoEmpresa/{cnpj}");
            urlBuilder_.Replace("{cnpj}", System.Uri.EscapeDataString(ConvertToString(cnpj, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarSaldoEmpresaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoEmpresaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultarSaldoEmpresaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class ProcessadoraClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public ProcessadoraClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Integra a Processadora</summary>
        /// <param name="processadora">Objeto da Processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(ProcessadoraDTORequest processadora, string x_web_auth_token)
        {
            return IntegrarAsync(processadora, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Integra a Processadora</summary>
        /// <param name="processadora">Objeto da Processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebIntegrationResponse Integrar(ProcessadoraDTORequest processadora, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IntegrarAsync(processadora, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Integra a Processadora</summary>
        /// <param name="processadora">Objeto da Processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(ProcessadoraDTORequest processadora, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora/Integrar");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(processadora, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebIntegrationResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebIntegrationResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(WebIntegrationResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class TipoTransacaoClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Cartoes/Web";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public TipoTransacaoClient(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Integra um Tipo de Transação</summary>
        /// <param name="tipoTransacao">Objeto do tipo de transação</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(TipoTransacaoDTORequest tipoTransacao, string x_web_auth_token)
        {
            return IntegrarAsync(tipoTransacao, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Integra um Tipo de Transação</summary>
        /// <param name="tipoTransacao">Objeto do tipo de transação</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebIntegrationResponse Integrar(TipoTransacaoDTORequest tipoTransacao, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IntegrarAsync(tipoTransacao, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Integra um Tipo de Transação</summary>
        /// <param name="tipoTransacao">Objeto do tipo de transação</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(TipoTransacaoDTORequest tipoTransacao, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao/Integrar");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_web_auth_token == null)
                        throw new System.ArgumentNullException("x_web_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(tipoTransacao, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebIntegrationResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebIntegrationResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(WebIntegrationResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class AcessarPaginaSaldoExtratoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _documento;
        private string _senha;
    
        [Newtonsoft.Json.JsonProperty("documento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Documento
        {
            get { return _documento; }
            set 
            {
                if (_documento != value)
                {
                    _documento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Senha
        {
            get { return _senha; }
            set 
            {
                if (_senha != value)
                {
                    _senha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static AcessarPaginaSaldoExtratoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AcessarPaginaSaldoExtratoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class AcessarPaginaSaldoExtratoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private AcessarPaginaSaldoExtratoResponseStatus _status;
        private string _mensagem;
        private string _tokenSessao;
        private System.Collections.Generic.List<IdentificadorCartaoWeb> _cartoes;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public AcessarPaginaSaldoExtratoResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tokenSessao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TokenSessao
        {
            get { return _tokenSessao; }
            set 
            {
                if (_tokenSessao != value)
                {
                    _tokenSessao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartoes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<IdentificadorCartaoWeb> Cartoes
        {
            get { return _cartoes; }
            set 
            {
                if (_cartoes != value)
                {
                    _cartoes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static AcessarPaginaSaldoExtratoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AcessarPaginaSaldoExtratoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ApiProcessingStateOnServer : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServerState _state;
        private string _errorMessage;
    
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ApiProcessingStateOnServerState State
        {
            get { return _state; }
            set 
            {
                if (_state != value)
                {
                    _state = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errorMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set 
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ApiProcessingStateOnServer FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ApiProcessingStateOnServer>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IdentificadorCartaoWeb : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _codigoProduto;
        private string _nomeProduto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoProduto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CodigoProduto
        {
            get { return _codigoProduto; }
            set 
            {
                if (_codigoProduto != value)
                {
                    _codigoProduto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeProduto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeProduto
        {
            get { return _nomeProduto; }
            set 
            {
                if (_nomeProduto != value)
                {
                    _nomeProduto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IdentificadorCartaoWeb FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IdentificadorCartaoWeb>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DataSourceRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _page;
        private int? _pageSize;
        private System.Collections.Generic.List<SortDescriptor> _sorts;
        private System.Collections.Generic.List<object> _filters;
        private System.Collections.Generic.List<GroupDescriptor> _groups;
        private System.Collections.Generic.List<AggregateDescriptor> _aggregates;
    
        [Newtonsoft.Json.JsonProperty("page", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Page
        {
            get { return _page; }
            set 
            {
                if (_page != value)
                {
                    _page = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pageSize", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PageSize
        {
            get { return _pageSize; }
            set 
            {
                if (_pageSize != value)
                {
                    _pageSize = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sorts", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<SortDescriptor> Sorts
        {
            get { return _sorts; }
            set 
            {
                if (_sorts != value)
                {
                    _sorts = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("filters", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<object> Filters
        {
            get { return _filters; }
            set 
            {
                if (_filters != value)
                {
                    _filters = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("groups", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<GroupDescriptor> Groups
        {
            get { return _groups; }
            set 
            {
                if (_groups != value)
                {
                    _groups = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aggregates", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<AggregateDescriptor> Aggregates
        {
            get { return _aggregates; }
            set 
            {
                if (_aggregates != value)
                {
                    _aggregates = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DataSourceRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DataSourceRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class SortDescriptor : System.ComponentModel.INotifyPropertyChanged
    {
        private string _member;
        private SortDescriptorSortDirection? _sortDirection;
    
        [Newtonsoft.Json.JsonProperty("member", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Member
        {
            get { return _member; }
            set 
            {
                if (_member != value)
                {
                    _member = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sortDirection", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public SortDescriptorSortDirection? SortDirection
        {
            get { return _sortDirection; }
            set 
            {
                if (_sortDirection != value)
                {
                    _sortDirection = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static SortDescriptor FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<SortDescriptor>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class GroupDescriptor : System.ComponentModel.INotifyPropertyChanged
    {
        private string _memberType;
        private object _displayContent;
        private System.Collections.Generic.List<AggregateFunction> _aggregateFunctions;
        private string _member;
        private GroupDescriptorSortDirection? _sortDirection;
    
        [Newtonsoft.Json.JsonProperty("memberType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MemberType
        {
            get { return _memberType; }
            set 
            {
                if (_memberType != value)
                {
                    _memberType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("displayContent", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public object DisplayContent
        {
            get { return _displayContent; }
            set 
            {
                if (_displayContent != value)
                {
                    _displayContent = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aggregateFunctions", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<AggregateFunction> AggregateFunctions
        {
            get { return _aggregateFunctions; }
            set 
            {
                if (_aggregateFunctions != value)
                {
                    _aggregateFunctions = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("member", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Member
        {
            get { return _member; }
            set 
            {
                if (_member != value)
                {
                    _member = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sortDirection", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public GroupDescriptorSortDirection? SortDirection
        {
            get { return _sortDirection; }
            set 
            {
                if (_sortDirection != value)
                {
                    _sortDirection = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static GroupDescriptor FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<GroupDescriptor>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class AggregateDescriptor : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Collections.Generic.List<AggregateFunction> _aggregates;
        private string _member;
    
        [Newtonsoft.Json.JsonProperty("aggregates", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<AggregateFunction> Aggregates
        {
            get { return _aggregates; }
            set 
            {
                if (_aggregates != value)
                {
                    _aggregates = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("member", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Member
        {
            get { return _member; }
            set 
            {
                if (_member != value)
                {
                    _member = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static AggregateDescriptor FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AggregateDescriptor>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class AggregateFunction : System.ComponentModel.INotifyPropertyChanged
    {
        private string _aggregateMethodName;
        private string _caption;
        private string _sourceField;
        private string _functionName;
        private string _memberType;
        private string _resultFormatString;
    
        [Newtonsoft.Json.JsonProperty("aggregateMethodName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AggregateMethodName
        {
            get { return _aggregateMethodName; }
            set 
            {
                if (_aggregateMethodName != value)
                {
                    _aggregateMethodName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("caption", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Caption
        {
            get { return _caption; }
            set 
            {
                if (_caption != value)
                {
                    _caption = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sourceField", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SourceField
        {
            get { return _sourceField; }
            set 
            {
                if (_sourceField != value)
                {
                    _sourceField = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("functionName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FunctionName
        {
            get { return _functionName; }
            set 
            {
                if (_functionName != value)
                {
                    _functionName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("memberType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MemberType
        {
            get { return _memberType; }
            set 
            {
                if (_memberType != value)
                {
                    _memberType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("resultFormatString", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ResultFormatString
        {
            get { return _resultFormatString; }
            set 
            {
                if (_resultFormatString != value)
                {
                    _resultFormatString = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static AggregateFunction FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AggregateFunction>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DataSourceResult : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Collections.Generic.List<object> _data;
        private int? _total;
        private System.Collections.Generic.List<AggregateResult> _aggregateResults;
        private object _errors;
    
        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<object> Data
        {
            get { return _data; }
            set 
            {
                if (_data != value)
                {
                    _data = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("total", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Total
        {
            get { return _total; }
            set 
            {
                if (_total != value)
                {
                    _total = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aggregateResults", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<AggregateResult> AggregateResults
        {
            get { return _aggregateResults; }
            set 
            {
                if (_aggregateResults != value)
                {
                    _aggregateResults = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errors", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public object Errors
        {
            get { return _errors; }
            set 
            {
                if (_errors != value)
                {
                    _errors = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DataSourceResult FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DataSourceResult>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class AggregateResult : System.ComponentModel.INotifyPropertyChanged
    {
        private object _value;
        private string _member;
        private object _formattedValue;
        private int? _itemCount;
        private string _caption;
        private string _functionName;
        private string _aggregateMethodName;
    
        [Newtonsoft.Json.JsonProperty("value", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public object Value
        {
            get { return _value; }
            set 
            {
                if (_value != value)
                {
                    _value = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("member", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Member
        {
            get { return _member; }
            set 
            {
                if (_member != value)
                {
                    _member = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("formattedValue", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public object FormattedValue
        {
            get { return _formattedValue; }
            set 
            {
                if (_formattedValue != value)
                {
                    _formattedValue = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("itemCount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ItemCount
        {
            get { return _itemCount; }
            set 
            {
                if (_itemCount != value)
                {
                    _itemCount = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("caption", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Caption
        {
            get { return _caption; }
            set 
            {
                if (_caption != value)
                {
                    _caption = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("functionName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FunctionName
        {
            get { return _functionName; }
            set 
            {
                if (_functionName != value)
                {
                    _functionName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aggregateMethodName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AggregateMethodName
        {
            get { return _aggregateMethodName; }
            set 
            {
                if (_aggregateMethodName != value)
                {
                    _aggregateMethodName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static AggregateResult FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<AggregateResult>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarExtratoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
        private string _tipo;
        private bool? _exibirMetadados;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set 
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set 
            {
                if (_dataFim != value)
                {
                    _dataFim = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exibirMetadados", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ExibirMetadados
        {
            get { return _exibirMetadados; }
            set 
            {
                if (_exibirMetadados != value)
                {
                    _exibirMetadados = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarExtratoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarExtratoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IdentificadorCartao : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _produto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IdentificadorCartao FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IdentificadorCartao>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSaldoCartaoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSaldoCartaoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoCartaoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSaldoCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarSaldoCartaoResponseStatus _status;
        private string _mensagem;
        private decimal? _valorLimiteCredito;
        private decimal? _valorSaldoDisponivel;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarSaldoCartaoResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorLimiteCredito", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorLimiteCredito
        {
            get { return _valorLimiteCredito; }
            set 
            {
                if (_valorLimiteCredito != value)
                {
                    _valorLimiteCredito = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSaldoDisponivel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorSaldoDisponivel
        {
            get { return _valorSaldoDisponivel; }
            set 
            {
                if (_valorSaldoDisponivel != value)
                {
                    _valorSaldoDisponivel = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSaldoCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class LoginWebResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool _sucesso;
        private string _mensagem;
        private string _cnpjEmpresa;
        private string _sessionToken;
        private string _urlImagemCartao;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Always)]
        public bool Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cnpjEmpresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CnpjEmpresa
        {
            get { return _cnpjEmpresa; }
            set 
            {
                if (_cnpjEmpresa != value)
                {
                    _cnpjEmpresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sessionToken", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SessionToken
        {
            get { return _sessionToken; }
            set 
            {
                if (_sessionToken != value)
                {
                    _sessionToken = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("urlImagemCartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UrlImagemCartao
        {
            get { return _urlImagemCartao; }
            set 
            {
                if (_urlImagemCartao != value)
                {
                    _urlImagemCartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static LoginWebResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<LoginWebResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebProcessingStateOnServer : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServerState _state;
        private string _errorMessage;
    
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public WebProcessingStateOnServerState State
        {
            get { return _state; }
            set 
            {
                if (_state != value)
                {
                    _state = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errorMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set 
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebProcessingStateOnServer FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebProcessingStateOnServer>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private System.DateTime? _dataDesvinculo;
        private CartaoStatus _status;
        private bool? _contaVirtual;
        private string _motivoDesvinculo;
        private Pessoa _pessoa;
        private ProdutoResponse _produto;
        private System.Collections.Generic.List<CartaoProcessadoraResponse> _cartaoProcessadora;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesvinculo
        {
            get { return _dataDesvinculo; }
            set 
            {
                if (_dataDesvinculo != value)
                {
                    _dataDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CartaoStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contaVirtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ContaVirtual
        {
            get { return _contaVirtual; }
            set 
            {
                if (_contaVirtual != value)
                {
                    _contaVirtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motivoDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotivoDesvinculo
        {
            get { return _motivoDesvinculo; }
            set 
            {
                if (_motivoDesvinculo != value)
                {
                    _motivoDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoProcessadora", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<CartaoProcessadoraResponse> CartaoProcessadora
        {
            get { return _cartaoProcessadora; }
            set 
            {
                if (_cartaoProcessadora != value)
                {
                    _cartaoProcessadora = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoStatus : System.ComponentModel.INotifyPropertyChanged
    {
        private string _abreviado;
        private string _descricao;
        private CartaoStatusId _id;
    
        [Newtonsoft.Json.JsonProperty("abreviado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Abreviado
        {
            get { return _abreviado; }
            set 
            {
                if (_abreviado != value)
                {
                    _abreviado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CartaoStatusId Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoStatus FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoStatus>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Pessoa : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private string _tipoPessoa;
        private string _sexo;
        private int? _cidadeId;
        private bool? _ativo;
        private System.DateTime? _dataAtivacao;
        private System.DateTime? _dataDesativacao;
        private Cidade _cidade;
        private PessoaEndereco _endereco;
        private PessoaInfo _info;
        private System.Collections.Generic.List<PessoaContaBancaria> _contasBancarias;
        private System.Collections.Generic.List<PessoaTipo> _tipos;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sexo
        {
            get { return _sexo; }
            set 
            {
                if (_sexo != value)
                {
                    _sexo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidadeId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CidadeId
        {
            get { return _cidadeId; }
            set 
            {
                if (_cidadeId != value)
                {
                    _cidadeId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ativo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Ativo
        {
            get { return _ativo; }
            set 
            {
                if (_ativo != value)
                {
                    _ativo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAtivacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAtivacao
        {
            get { return _dataAtivacao; }
            set 
            {
                if (_dataAtivacao != value)
                {
                    _dataAtivacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesativacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesativacao
        {
            get { return _dataDesativacao; }
            set 
            {
                if (_dataDesativacao != value)
                {
                    _dataDesativacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Cidade Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaEndereco Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("info", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public PessoaInfo Info
        {
            get { return _info; }
            set 
            {
                if (_info != value)
                {
                    _info = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contasBancarias", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<PessoaContaBancaria> ContasBancarias
        {
            get { return _contasBancarias; }
            set 
            {
                if (_contasBancarias != value)
                {
                    _contasBancarias = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<PessoaTipo> Tipos
        {
            get { return _tipos; }
            set 
            {
                if (_tipos != value)
                {
                    _tipos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Pessoa FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Pessoa>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProdutoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int _id;
        private string _nome;
        private bool _isMultiplasContas;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("isMultiplasContas", Required = Newtonsoft.Json.Required.Always)]
        public bool IsMultiplasContas
        {
            get { return _isMultiplasContas; }
            set 
            {
                if (_isMultiplasContas != value)
                {
                    _isMultiplasContas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProdutoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProdutoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoProcessadoraResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _cartaoId;
        private string _key;
        private int? _valor;
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("key", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Key
        {
            get { return _key; }
            set 
            {
                if (_key != value)
                {
                    _key = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoProcessadoraResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoProcessadoraResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Cidade : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private int? _ibge;
        private int? _estadoId;
        private Estado _estado;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibge", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Ibge
        {
            get { return _ibge; }
            set 
            {
                if (_ibge != value)
                {
                    _ibge = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estadoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? EstadoId
        {
            get { return _estadoId; }
            set 
            {
                if (_estadoId != value)
                {
                    _estadoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Estado Estado
        {
            get { return _estado; }
            set 
            {
                if (_estado != value)
                {
                    _estado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Cidade FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Cidade>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaEndereco : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _pessoaId;
        private string _logradouro;
        private int? _numero;
        private string _bairro;
        private string _cep;
        private string _complemento;
        private int? _cidadeId;
        private Cidade _cidade;
        private Pessoa _pessoa;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("pessoaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PessoaId
        {
            get { return _pessoaId; }
            set 
            {
                if (_pessoaId != value)
                {
                    _pessoaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("logradouro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Logradouro
        {
            get { return _logradouro; }
            set 
            {
                if (_logradouro != value)
                {
                    _logradouro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numero", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Numero
        {
            get { return _numero; }
            set 
            {
                if (_numero != value)
                {
                    _numero = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bairro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Bairro
        {
            get { return _bairro; }
            set 
            {
                if (_bairro != value)
                {
                    _bairro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cep", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Cep
        {
            get { return _cep; }
            set 
            {
                if (_cep != value)
                {
                    _cep = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("complemento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Complemento
        {
            get { return _complemento; }
            set 
            {
                if (_complemento != value)
                {
                    _complemento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidadeId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CidadeId
        {
            get { return _cidadeId; }
            set 
            {
                if (_cidadeId != value)
                {
                    _cidadeId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Cidade Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaEndereco FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaEndereco>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaInfo : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _pessoaId;
        private string _rg;
        private string _nomeMae;
        private string _nomePai;
        private System.DateTime? _dataNascimento;
        private Pessoa _pessoa;
    
        [Newtonsoft.Json.JsonProperty("pessoaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PessoaId
        {
            get { return _pessoaId; }
            set 
            {
                if (_pessoaId != value)
                {
                    _pessoaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("rg", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Rg
        {
            get { return _rg; }
            set 
            {
                if (_rg != value)
                {
                    _rg = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeMae", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeMae
        {
            get { return _nomeMae; }
            set 
            {
                if (_nomeMae != value)
                {
                    _nomeMae = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomePai", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomePai
        {
            get { return _nomePai; }
            set 
            {
                if (_nomePai != value)
                {
                    _nomePai = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataNascimento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataNascimento
        {
            get { return _dataNascimento; }
            set 
            {
                if (_dataNascimento != value)
                {
                    _dataNascimento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaInfo FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaInfo>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaContaBancaria : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _pessoaId;
        private string _nomeConta;
        private string _codigoBacenBanco;
        private string _agencia;
        private string _conta;
        private int? _digitoConta;
        private PessoaContaBancariaTipoConta? _tipoConta;
        private Pessoa _pessoa;
        private Banco _banco;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("pessoaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PessoaId
        {
            get { return _pessoaId; }
            set 
            {
                if (_pessoaId != value)
                {
                    _pessoaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeConta
        {
            get { return _nomeConta; }
            set 
            {
                if (_nomeConta != value)
                {
                    _nomeConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoBacenBanco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoBacenBanco
        {
            get { return _codigoBacenBanco; }
            set 
            {
                if (_codigoBacenBanco != value)
                {
                    _codigoBacenBanco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Agencia
        {
            get { return _agencia; }
            set 
            {
                if (_agencia != value)
                {
                    _agencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("conta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Conta
        {
            get { return _conta; }
            set 
            {
                if (_conta != value)
                {
                    _conta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("digitoConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? DigitoConta
        {
            get { return _digitoConta; }
            set 
            {
                if (_digitoConta != value)
                {
                    _digitoConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public PessoaContaBancariaTipoConta? TipoConta
        {
            get { return _tipoConta; }
            set 
            {
                if (_tipoConta != value)
                {
                    _tipoConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("banco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Banco Banco
        {
            get { return _banco; }
            set 
            {
                if (_banco != value)
                {
                    _banco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaContaBancaria FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaContaBancaria>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaTipo : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _pessoaId;
        private int? _administradoraId;
        private int? _empresaId;
        private PessoaTipoTipoPessoaId? _tipoPessoaId;
        private Pessoa _pessoa;
        private Administradora _administradora;
        private Empresa _empresa;
        private TipoPessoa _tipoPessoa;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("pessoaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PessoaId
        {
            get { return _pessoaId; }
            set 
            {
                if (_pessoaId != value)
                {
                    _pessoaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("administradoraId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AdministradoraId
        {
            get { return _administradoraId; }
            set 
            {
                if (_administradoraId != value)
                {
                    _administradoraId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("empresaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? EmpresaId
        {
            get { return _empresaId; }
            set 
            {
                if (_empresaId != value)
                {
                    _empresaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public PessoaTipoTipoPessoaId? TipoPessoaId
        {
            get { return _tipoPessoaId; }
            set 
            {
                if (_tipoPessoaId != value)
                {
                    _tipoPessoaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("administradora", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Administradora Administradora
        {
            get { return _administradora; }
            set 
            {
                if (_administradora != value)
                {
                    _administradora = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("empresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Empresa Empresa
        {
            get { return _empresa; }
            set 
            {
                if (_empresa != value)
                {
                    _empresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public TipoPessoa TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaTipo FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaTipo>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Estado : System.ComponentModel.INotifyPropertyChanged
    {
        private string _sigla;
        private string _nome;
        private int? _ibge;
        private int? _paisId;
        private Pais _pais;
        private System.Collections.Generic.List<Cidade> _cidades;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("sigla", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sigla
        {
            get { return _sigla; }
            set 
            {
                if (_sigla != value)
                {
                    _sigla = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibge", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Ibge
        {
            get { return _ibge; }
            set 
            {
                if (_ibge != value)
                {
                    _ibge = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("paisId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PaisId
        {
            get { return _paisId; }
            set 
            {
                if (_paisId != value)
                {
                    _paisId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pais Pais
        {
            get { return _pais; }
            set 
            {
                if (_pais != value)
                {
                    _pais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidades", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Cidade> Cidades
        {
            get { return _cidades; }
            set 
            {
                if (_cidades != value)
                {
                    _cidades = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Estado FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Estado>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Banco : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _url;
        private string _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url
        {
            get { return _url; }
            set 
            {
                if (_url != value)
                {
                    _url = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        public string Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Banco FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Banco>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Administradora : System.ComponentModel.INotifyPropertyChanged
    {
        private string _urlImagemCartao;
        private string _nome;
        private bool? _ativo;
        private int _id;
    
        [Newtonsoft.Json.JsonProperty("urlImagemCartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UrlImagemCartao
        {
            get { return _urlImagemCartao; }
            set 
            {
                if (_urlImagemCartao != value)
                {
                    _urlImagemCartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ativo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Ativo
        {
            get { return _ativo; }
            set 
            {
                if (_ativo != value)
                {
                    _ativo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Administradora FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Administradora>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Empresa : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _cnpj;
        private int _id;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Cnpj
        {
            get { return _cnpj; }
            set 
            {
                if (_cnpj != value)
                {
                    _cnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Empresa FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Empresa>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoPessoa : System.ComponentModel.INotifyPropertyChanged
    {
        private string _abreviado;
        private string _descricao;
        private TipoPessoaId _id;
    
        [Newtonsoft.Json.JsonProperty("abreviado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Abreviado
        {
            get { return _abreviado; }
            set 
            {
                if (_abreviado != value)
                {
                    _abreviado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TipoPessoaId Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoPessoa FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoPessoa>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Pais : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _sigla;
        private int? _bacen;
        private string _prefixoTelefone;
        private System.Collections.Generic.List<Estado> _estados;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sigla", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sigla
        {
            get { return _sigla; }
            set 
            {
                if (_sigla != value)
                {
                    _sigla = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bacen", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Bacen
        {
            get { return _bacen; }
            set 
            {
                if (_bacen != value)
                {
                    _bacen = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("prefixoTelefone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PrefixoTelefone
        {
            get { return _prefixoTelefone; }
            set 
            {
                if (_prefixoTelefone != value)
                {
                    _prefixoTelefone = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estados", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Estado> Estados
        {
            get { return _estados; }
            set 
            {
                if (_estados != value)
                {
                    _estados = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Pais FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Pais>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private System.DateTime? _dataDesvinculo;
        private string _portador;
        private ProdutoResponse _produto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesvinculo
        {
            get { return _dataDesvinculo; }
            set 
            {
                if (_dataDesvinculo != value)
                {
                    _dataDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("portador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Portador
        {
            get { return _portador; }
            set 
            {
                if (_portador != value)
                {
                    _portador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoCadastrarRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _produtoId;
        private System.Collections.Generic.Dictionary<string, string> _parametros;
        private int? _administradoraId;
        private string _agrupadorProdutos;
        private string _conteudoImportacao;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produtoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProdutoId
        {
            get { return _produtoId; }
            set 
            {
                if (_produtoId != value)
                {
                    _produtoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parametros", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> Parametros
        {
            get { return _parametros; }
            set 
            {
                if (_parametros != value)
                {
                    _parametros = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("administradoraId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? AdministradoraId
        {
            get { return _administradoraId; }
            set 
            {
                if (_administradoraId != value)
                {
                    _administradoraId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agrupadorProdutos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AgrupadorProdutos
        {
            get { return _agrupadorProdutos; }
            set 
            {
                if (_agrupadorProdutos != value)
                {
                    _agrupadorProdutos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("conteudoImportacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ConteudoImportacao
        {
            get { return _conteudoImportacao; }
            set 
            {
                if (_conteudoImportacao != value)
                {
                    _conteudoImportacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoCadastrarRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoCadastrarRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoCadastrarResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private string _mensagem;
        private int? _cartaoId;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoCadastrarResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoCadastrarResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoVirtualCadastrarRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _produtoId;
        private System.Collections.Generic.Dictionary<string, string> _parametros;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produtoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProdutoId
        {
            get { return _produtoId; }
            set 
            {
                if (_produtoId != value)
                {
                    _produtoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parametros", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> Parametros
        {
            get { return _parametros; }
            set 
            {
                if (_parametros != value)
                {
                    _parametros = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoVirtualCadastrarRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVirtualCadastrarRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoVirtualCadastrarResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private string _mensagem;
        private int? _cartaoId;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoVirtualCadastrarResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVirtualCadastrarResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IFormFile : System.ComponentModel.INotifyPropertyChanged
    {
        private string _contentType;
        private string _contentDisposition;
        private System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<string>> _headers;
        private long? _length;
        private string _name;
        private string _fileName;
    
        [Newtonsoft.Json.JsonProperty("contentType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContentType
        {
            get { return _contentType; }
            set 
            {
                if (_contentType != value)
                {
                    _contentType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contentDisposition", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContentDisposition
        {
            get { return _contentDisposition; }
            set 
            {
                if (_contentDisposition != value)
                {
                    _contentDisposition = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("headers", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<string>> Headers
        {
            get { return _headers; }
            set 
            {
                if (_headers != value)
                {
                    _headers = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("length", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? Length
        {
            get { return _length; }
            set 
            {
                if (_length != value)
                {
                    _length = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name
        {
            get { return _name; }
            set 
            {
                if (_name != value)
                {
                    _name = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("fileName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FileName
        {
            get { return _fileName; }
            set 
            {
                if (_fileName != value)
                {
                    _fileName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IFormFile FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IFormFile>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarCartaoProcessadoraRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cnpjEmpresa;
        private string _hashCartao;
    
        [Newtonsoft.Json.JsonProperty("cnpjEmpresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CnpjEmpresa
        {
            get { return _cnpjEmpresa; }
            set 
            {
                if (_cnpjEmpresa != value)
                {
                    _cnpjEmpresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("hashCartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HashCartao
        {
            get { return _hashCartao; }
            set 
            {
                if (_hashCartao != value)
                {
                    _hashCartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarCartaoProcessadoraRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarCartaoProcessadoraRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarCartaoProcessadoraResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarCartaoProcessadoraResponseStatus _status;
        private string _mensagem;
        private IdentificadorCartao _cartao;
        private IdentificadorCartao _cartaoRelacionado;
        private string _documentoPortador;
        private string _nomePortador;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarCartaoProcessadoraResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoRelacionado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao CartaoRelacionado
        {
            get { return _cartaoRelacionado; }
            set 
            {
                if (_cartaoRelacionado != value)
                {
                    _cartaoRelacionado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoPortador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoPortador
        {
            get { return _documentoPortador; }
            set 
            {
                if (_documentoPortador != value)
                {
                    _documentoPortador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomePortador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomePortador
        {
            get { return _nomePortador; }
            set 
            {
                if (_nomePortador != value)
                {
                    _nomePortador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarCartaoProcessadoraResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarCartaoProcessadoraResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IndicadorTransacaoSemanaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool _sucesso;
        private string _mensagem;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
        private System.Collections.Generic.List<IndicadorItem> _indicadores;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Always)]
        public bool Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set 
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set 
            {
                if (_dataFim != value)
                {
                    _dataFim = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("indicadores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<IndicadorItem> Indicadores
        {
            get { return _indicadores; }
            set 
            {
                if (_indicadores != value)
                {
                    _indicadores = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IndicadorTransacaoSemanaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IndicadorItem : System.ComponentModel.INotifyPropertyChanged
    {
        private string _indicador;
        private IndicadorItemTipo? _tipo;
        private decimal? _segunda;
        private decimal? _terca;
        private decimal? _quarta;
        private decimal? _quinta;
        private decimal? _sexta;
        private decimal? _sabado;
        private decimal? _domingo;
        private decimal? _totalSemana;
        private decimal? _totalMes;
        private string _segundaFormatted;
        private string _tercaFormatted;
        private string _quartaFormatted;
        private string _quintaFormatted;
        private string _sextaFormatted;
        private string _sabadoFormatted;
        private string _domingoFormatted;
        private string _totalSemanaFormatted;
        private string _totalMesFormatted;
    
        [Newtonsoft.Json.JsonProperty("indicador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Indicador
        {
            get { return _indicador; }
            set 
            {
                if (_indicador != value)
                {
                    _indicador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public IndicadorItemTipo? Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("segunda", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Segunda
        {
            get { return _segunda; }
            set 
            {
                if (_segunda != value)
                {
                    _segunda = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("terca", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Terca
        {
            get { return _terca; }
            set 
            {
                if (_terca != value)
                {
                    _terca = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quarta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Quarta
        {
            get { return _quarta; }
            set 
            {
                if (_quarta != value)
                {
                    _quarta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quinta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Quinta
        {
            get { return _quinta; }
            set 
            {
                if (_quinta != value)
                {
                    _quinta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Sexta
        {
            get { return _sexta; }
            set 
            {
                if (_sexta != value)
                {
                    _sexta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sabado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Sabado
        {
            get { return _sabado; }
            set 
            {
                if (_sabado != value)
                {
                    _sabado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("domingo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Domingo
        {
            get { return _domingo; }
            set 
            {
                if (_domingo != value)
                {
                    _domingo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalSemana", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalSemana
        {
            get { return _totalSemana; }
            set 
            {
                if (_totalSemana != value)
                {
                    _totalSemana = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalMes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? TotalMes
        {
            get { return _totalMes; }
            set 
            {
                if (_totalMes != value)
                {
                    _totalMes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("segundaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SegundaFormatted
        {
            get { return _segundaFormatted; }
            set 
            {
                if (_segundaFormatted != value)
                {
                    _segundaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tercaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TercaFormatted
        {
            get { return _tercaFormatted; }
            set 
            {
                if (_tercaFormatted != value)
                {
                    _tercaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quartaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string QuartaFormatted
        {
            get { return _quartaFormatted; }
            set 
            {
                if (_quartaFormatted != value)
                {
                    _quartaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quintaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string QuintaFormatted
        {
            get { return _quintaFormatted; }
            set 
            {
                if (_quintaFormatted != value)
                {
                    _quintaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sextaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SextaFormatted
        {
            get { return _sextaFormatted; }
            set 
            {
                if (_sextaFormatted != value)
                {
                    _sextaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sabadoFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SabadoFormatted
        {
            get { return _sabadoFormatted; }
            set 
            {
                if (_sabadoFormatted != value)
                {
                    _sabadoFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("domingoFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DomingoFormatted
        {
            get { return _domingoFormatted; }
            set 
            {
                if (_domingoFormatted != value)
                {
                    _domingoFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalSemanaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TotalSemanaFormatted
        {
            get { return _totalSemanaFormatted; }
            set 
            {
                if (_totalSemanaFormatted != value)
                {
                    _totalSemanaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalMesFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TotalMesFormatted
        {
            get { return _totalMesFormatted; }
            set 
            {
                if (_totalMesFormatted != value)
                {
                    _totalMesFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IndicadorItem FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorItem>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IndicadorTransacaoSemanaGraficoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool _sucesso;
        private string _mensagem;
        private System.DateTime? _dataInicioSemanaAtual;
        private System.DateTime? _dataFimSemanaAtual;
        private System.DateTime? _dataInicioSemanaAnterior;
        private System.DateTime? _dataFimSemanaAnterior;
        private System.Collections.Generic.List<TransacaoDiaGraficoitem> _indicadores;
        private System.Collections.Generic.List<TransacaoDiaGraficoitemPeriodo> _indicadoresPeriodo;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Always)]
        public bool Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioSemanaAtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioSemanaAtual
        {
            get { return _dataInicioSemanaAtual; }
            set 
            {
                if (_dataInicioSemanaAtual != value)
                {
                    _dataInicioSemanaAtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimSemanaAtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimSemanaAtual
        {
            get { return _dataFimSemanaAtual; }
            set 
            {
                if (_dataFimSemanaAtual != value)
                {
                    _dataFimSemanaAtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioSemanaAnterior
        {
            get { return _dataInicioSemanaAnterior; }
            set 
            {
                if (_dataInicioSemanaAnterior != value)
                {
                    _dataInicioSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimSemanaAnterior
        {
            get { return _dataFimSemanaAnterior; }
            set 
            {
                if (_dataFimSemanaAnterior != value)
                {
                    _dataFimSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("indicadores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TransacaoDiaGraficoitem> Indicadores
        {
            get { return _indicadores; }
            set 
            {
                if (_indicadores != value)
                {
                    _indicadores = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("indicadoresPeriodo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TransacaoDiaGraficoitemPeriodo> IndicadoresPeriodo
        {
            get { return _indicadoresPeriodo; }
            set 
            {
                if (_indicadoresPeriodo != value)
                {
                    _indicadoresPeriodo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IndicadorTransacaoSemanaGraficoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaGraficoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransacaoDiaGraficoitem : System.ComponentModel.INotifyPropertyChanged
    {
        private string _dia;
        private string _periodo;
        private decimal? _valorSemanaAtual;
        private decimal? _valorSemanaAnterior;
        private decimal? _valorMesmaHoraSemanaAnterior;
    
        [Newtonsoft.Json.JsonProperty("dia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Dia
        {
            get { return _dia; }
            set 
            {
                if (_dia != value)
                {
                    _dia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("periodo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Periodo
        {
            get { return _periodo; }
            set 
            {
                if (_periodo != value)
                {
                    _periodo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSemanaAtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorSemanaAtual
        {
            get { return _valorSemanaAtual; }
            set 
            {
                if (_valorSemanaAtual != value)
                {
                    _valorSemanaAtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorSemanaAnterior
        {
            get { return _valorSemanaAnterior; }
            set 
            {
                if (_valorSemanaAnterior != value)
                {
                    _valorSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorMesmaHoraSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorMesmaHoraSemanaAnterior
        {
            get { return _valorMesmaHoraSemanaAnterior; }
            set 
            {
                if (_valorMesmaHoraSemanaAnterior != value)
                {
                    _valorMesmaHoraSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransacaoDiaGraficoitem FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransacaoDiaGraficoitem>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransacaoDiaGraficoitemPeriodo : System.ComponentModel.INotifyPropertyChanged
    {
        private string _dia;
        private string _periodo;
        private decimal? _valor;
    
        [Newtonsoft.Json.JsonProperty("dia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Dia
        {
            get { return _dia; }
            set 
            {
                if (_dia != value)
                {
                    _dia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("periodo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Periodo
        {
            get { return _periodo; }
            set 
            {
                if (_periodo != value)
                {
                    _periodo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransacaoDiaGraficoitemPeriodo FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransacaoDiaGraficoitemPeriodo>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSaldoEmpresaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarSaldoEmpresaResponseStatus _status;
        private string _mensagem;
        private decimal? _saldoConta;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarSaldoEmpresaResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("saldoConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? SaldoConta
        {
            get { return _saldoConta; }
            set 
            {
                if (_saldoConta != value)
                {
                    _saldoConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSaldoEmpresaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoEmpresaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarPessoaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private string _nome;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private string _sexo;
        private bool? _ativo;
        private int? _cidade;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sexo
        {
            get { return _sexo; }
            set 
            {
                if (_sexo != value)
                {
                    _sexo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ativo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Ativo
        {
            get { return _ativo; }
            set 
            {
                if (_ativo != value)
                {
                    _ativo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarPessoaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarPessoaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProcessadoraDTORequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _nome;
        private string _url;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url
        {
            get { return _url; }
            set 
            {
                if (_url != value)
                {
                    _url = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProcessadoraDTORequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProcessadoraDTORequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebIntegrationResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private WebIntegrationResponseStatus _status;
        private System.Collections.Generic.List<WebResponseValidation> _mensagens;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public WebIntegrationResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<WebResponseValidation> Mensagens
        {
            get { return _mensagens; }
            set 
            {
                if (_mensagens != value)
                {
                    _mensagens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebIntegrationResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebIntegrationResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebResponseValidation : System.ComponentModel.INotifyPropertyChanged
    {
        private WebResponseValidationType? _type;
        private string _message;
        private string _field;
    
        [Newtonsoft.Json.JsonProperty("type", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public WebResponseValidationType? Type
        {
            get { return _type; }
            set 
            {
                if (_type != value)
                {
                    _type = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message
        {
            get { return _message; }
            set 
            {
                if (_message != value)
                {
                    _message = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("field", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Field
        {
            get { return _field; }
            set 
            {
                if (_field != value)
                {
                    _field = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebResponseValidation FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebResponseValidation>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebActionResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool _sucesso;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Always)]
        public bool Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebActionResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebActionResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProcessadoraDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _nome;
        private string _url;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url
        {
            get { return _url; }
            set 
            {
                if (_url != value)
                {
                    _url = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProcessadoraDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProcessadoraDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoDTORequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _descricao;
        private int? _transacaoContrariaId;
        private TipoTransacaoDTORequestTipoTransacaoApiSuportada? _tipoTransacaoApiSuportada;
        private TipoTransacaoDTORequestPoliticaFalha? _politicaFalha;
        private System.Collections.Generic.List<TipoTransacaoItemDTORequest> _itens;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("transacaoContrariaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TransacaoContrariaId
        {
            get { return _transacaoContrariaId; }
            set 
            {
                if (_transacaoContrariaId != value)
                {
                    _transacaoContrariaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoTransacaoApiSuportada", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TipoTransacaoDTORequestTipoTransacaoApiSuportada? TipoTransacaoApiSuportada
        {
            get { return _tipoTransacaoApiSuportada; }
            set 
            {
                if (_tipoTransacaoApiSuportada != value)
                {
                    _tipoTransacaoApiSuportada = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TipoTransacaoDTORequestPoliticaFalha? PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("itens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TipoTransacaoItemDTORequest> Itens
        {
            get { return _itens; }
            set 
            {
                if (_itens != value)
                {
                    _itens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoDTORequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoDTORequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoItemDTORequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _tipoTransacaoItem;
    
        [Newtonsoft.Json.JsonProperty("tipoTransacaoItem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoTransacaoItem
        {
            get { return _tipoTransacaoItem; }
            set 
            {
                if (_tipoTransacaoItem != value)
                {
                    _tipoTransacaoItem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoItemDTORequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoItemDTORequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _descricao;
        private int? _transacaoContrariaId;
        private string _apiSuportada;
        private string _politicaFalha;
        private System.Collections.Generic.List<TipoTransacaoItemDTOResponse> _itens;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("transacaoContrariaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TransacaoContrariaId
        {
            get { return _transacaoContrariaId; }
            set 
            {
                if (_transacaoContrariaId != value)
                {
                    _transacaoContrariaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("apiSuportada", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ApiSuportada
        {
            get { return _apiSuportada; }
            set 
            {
                if (_apiSuportada != value)
                {
                    _apiSuportada = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("itens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TipoTransacaoItemDTOResponse> Itens
        {
            get { return _itens; }
            set 
            {
                if (_itens != value)
                {
                    _itens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoItemDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _lineId;
        private int? _tipoTransacaoItem;
    
        [Newtonsoft.Json.JsonProperty("lineId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? LineId
        {
            get { return _lineId; }
            set 
            {
                if (_lineId != value)
                {
                    _lineId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoTransacaoItem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoTransacaoItem
        {
            get { return _tipoTransacaoItem; }
            set 
            {
                if (_tipoTransacaoItem != value)
                {
                    _tipoTransacaoItem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoItemDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoItemDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum AcessarPaginaSaldoExtratoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Pendente")]
        Pendente = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ApiProcessingStateOnServerState
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ok")]
        Ok = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum SortDescriptorSortDirection
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ascending")]
        Ascending = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Descending")]
        Descending = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum GroupDescriptorSortDirection
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ascending")]
        Ascending = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Descending")]
        Descending = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarSaldoCartaoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum WebProcessingStateOnServerState
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ok")]
        Ok = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum CartaoStatusId
    {
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoRemessaParaCliente")]
        AguardandoRemessaParaCliente = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoRemessaParaPontoDistribuicao")]
        AguardandoRemessaParaPontoDistribuicao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "EmTransitoParaPontoDistribuicao")]
        EmTransitoParaPontoDistribuicao = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "EmTransitoRemessaRejeitada")]
        EmTransitoRemessaRejeitada = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoVinculacao")]
        AguardandoVinculacao = 4,
    
        [System.Runtime.Serialization.EnumMember(Value = "Vinculado")]
        Vinculado = 5,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoExtravio")]
        CanceladoExtravio = 6,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoTrocaCartao")]
        CanceladoTrocaCartao = 7,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoManual")]
        CanceladoManual = 8,
    
        [System.Runtime.Serialization.EnumMember(Value = "RejeitadoPontoDistribuicao")]
        RejeitadoPontoDistribuicao = 9,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum PessoaContaBancariaTipoConta
    {
        [System.Runtime.Serialization.EnumMember(Value = "Indefinido")]
        Indefinido = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Corrente")]
        Corrente = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Poupanca")]
        Poupanca = 2,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum PessoaTipoTipoPessoaId
    {
        [System.Runtime.Serialization.EnumMember(Value = "PortadorCartao")]
        PortadorCartao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "PontoDistribuicao")]
        PontoDistribuicao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Estabelecimento")]
        Estabelecimento = 2,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TipoPessoaId
    {
        [System.Runtime.Serialization.EnumMember(Value = "PortadorCartao")]
        PortadorCartao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "PontoDistribuicao")]
        PontoDistribuicao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Estabelecimento")]
        Estabelecimento = 2,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarCartaoProcessadoraResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Pendente")]
        Pendente = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum IndicadorItemTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Informacao")]
        Informacao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "TotalizadorCargaCredito")]
        TotalizadorCargaCredito = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "TotalizadorEstornoCargaCredito")]
        TotalizadorEstornoCargaCredito = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "TotalizadorSaldo")]
        TotalizadorSaldo = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Agrupador")]
        Agrupador = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarSaldoEmpresaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum WebIntegrationResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum WebResponseValidationType
    {
        [System.Runtime.Serialization.EnumMember(Value = "Information")]
        Information = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Warning")]
        Warning = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Validation")]
        Validation = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TipoTransacaoDTORequestTipoTransacaoApiSuportada
    {
        [System.Runtime.Serialization.EnumMember(Value = "Indefinida")]
        Indefinida = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "CargaCartao")]
        CargaCartao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "OnUs")]
        OnUs = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Agrupador")]
        Agrupador = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Moedeiro")]
        Moedeiro = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TipoTransacaoDTORequestPoliticaFalha
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoAplicado")]
        NaoAplicado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Estornar")]
        Estornar = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Reenviar")]
        Reenviar = 2,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class FileParameter
    {
        public FileParameter(System.IO.Stream data) 
            : this (data, null)
        {
        }

        public FileParameter(System.IO.Stream data, string fileName)
        {
            Data = data;
            FileName = fileName;
        }

        public System.IO.Stream Data { get; private set; }

        public string FileName { get; private set; }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message, innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}
﻿using System;
using System.Collections.Generic;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class GetTransacaoBizResponse
    {
        public string CodigoConta { get; set; }
        public int? NumeroConta { get; set; }
        public int? CodigoFilial { get; set; }
        public int? NumeroCorrelativo { get; set; }
        public string NumeroCartao { get; set; }
        public string DocumentoPortador { get; set; }
        public int? TipoDocumentoPortador { get; set; }
        public decimal? ValorTransacaoDolar { get; set; }
        public int? CodigoMoedaDolar { get; set; }
        public string DescricaoMoedaDolar { get; set; }
        public string SimboloMoedaDolar { get; set; }
        public decimal? CotacaoDolarnoDia { get; set; }
        public string NomeEmbossadoCartao { get; set; }
        public string Id { get; set; }
        public int? CodigoEmissor { get; set; }
        public decimal? ValorLocal { get; set; }
        public int? CodigoMoedaLocal { get; set; }
        public string DescricaoMoedaLocal { get; set; }
        public string SimboloMoedaLocal { get; set; }
        public int? Mcc { get; set; }
        public string DescricaoMcc { get; set; }
        public string DescricaoGrupoMcc { get; set; }
        public int? IdGrupoMcc { get; set; }
        public string NomeEstabelecimento { get; set; }
        public string DescricaoMti { get; set; }
        public int? Mti { get; set; }
        public decimal? ValorOrigem { get; set; }
        public int? MoedaOrigem { get; set; }
        public string DescricaoMoedaOrigem { get; set; }
        public string SimboloMoedaOrigem { get; set; }
        public int? NumeroParcelas { get; set; }
        public int? CodigoProduto { get; set; }
        public string CodigoRespostaTransacao { get; set; }
        public string DescricaoCodigoRespostaTransacao { get; set; }
        public string CodigoRespostaEmissor { get; set; }
        public string DescricaoCodigoRespostaEmissor { get; set; }
        public string DescricaoPlanoVendas { get; set; }
        public int? PlanoVendas { get; set; }
        public int? DataTransacao { get; set; }
        public DateTime DataHoraTransacao { get; set; }
        public int? HoraTransacao { get; set; }
        public int? IdTransacao { get; set; }
        public int? CodigoStatusTransacao { get; set; }
        public string DescricaoCodigoStatusTransacao { get; set; }
        public int? EntryMode { get; set; }
        public string TerminalId { get; set; }
        public string RetrievalReferenceNumber { get; set; }
        public int? AcquirerCode { get; set; }
        public string MerchantCode { get; set; }
        public int? Nsu { get; set; }
        public string CodigoAutorizacaoEmissor { get; set; }
        public string CodigoAutorizacaoTransacao { get; set; }
        public int? CodigoProcessamentoTransacao { get; set; }
        public bool? SaldoCompartilhado { get; set; }
        public int? CodigoCriterioSelecaoCarteira { get; set; }
        public string IdTransacaoCliente { get; set; }
        public bool? Combinada { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Webservice.Request.Usuario;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Text.RegularExpressions;
using System.Web.Configuration;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Models.Parametro;
using Usuario = ATS.Domain.Entities.Usuario;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services
{
    public class SrvUsuario : SrvBase
    {
        private const string Md5HashPassword = "|2d331cca-f6c0-40c0-bb43-6e32989c2881";
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IAdministradoraPlataformaApp _administradoraPlataformaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IMensagemApp _mensagemApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IVeiculoApp _veiculoApp;
        private readonly IFilialApp _filialApp;
        private readonly IGrupoUsuarioApp _grupoUsuarioApp;
        private readonly ITipoCarretaApp _tipoCarretaApp;
        private readonly ITipoCavaloApp _tipoCavaloApp;
        private readonly ITipoDocumentoApp _tipoDocumentoApp;
        private readonly IUsuarioPermissaoFinanceiroApp _usuarioPermissaoFinanceiroApp;
        private readonly IUsuarioPermissaoGestorApp _usuarioPermissaoGestorApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmailApp _emailApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IUsuarioDocumentoApp _usuarioDocumentoApp;
        private readonly IBloqueioFinanceiroTipoApp _bloqueioFinanceiroTipoApp;
        private readonly IBloqueioCartaoTipoApp _bloqueioCartaoTipoApp;
        private readonly IUsuarioPermissaoCartaoApp _usuarioPermissaoCartaoApp;
        private readonly IConjuntoApp _conjuntoApp;
        private readonly IBloqueioGestorTipoApp _bloqueioGestorTipoApp;
        private readonly IUsuarioService _service;
        private readonly ILimiteTransacaoPortadorApp _limiteTransacaoPortadorApp;
        private readonly KeycloakHelper _keycloak;
        private readonly IDespesaUsuarioApp _despesaUsuarioApp;
        private readonly ITagExtrattaApp _tagExtrattaApp;
        private readonly IParametrosAdministradoraPlataformaService _parametrosAdministradora;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;

        public SrvUsuario(IParametrosApp parametrosApp, IProprietarioApp proprietarioApp, IAdministradoraPlataformaApp administradoraPlataformaApp, IUsuarioApp usuarioApp,
            IMotoristaApp motoristaApp, IMensagemApp mensagemApp, IEmpresaApp empresaApp, IVeiculoApp veiculoApp, IFilialApp filialApp, IGrupoUsuarioApp grupoUsuarioApp,
            ITipoCarretaApp tipoCarretaApp, ITipoCavaloApp tipoCavaloApp, ITipoDocumentoApp tipoDocumentoApp, IUsuarioPermissaoFinanceiroApp usuarioPermissaoFinanceiroApp,
            IUsuarioPermissaoGestorApp usuarioPermissaoGestorApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmailApp emailApp, ICidadeApp cidadeApp,
            IUsuarioDocumentoApp usuarioDocumentoApp, IBloqueioFinanceiroTipoApp bloqueioFinanceiroTipoApp, IConjuntoApp conjuntoApp, IBloqueioGestorTipoApp bloqueioGestorTipoApp
            ,IUsuarioService service, KeycloakHelper keycloak, ILimiteTransacaoPortadorApp limiteTransacaoPortadorApp, IDespesaUsuarioApp despesaUsuarioApp, IParametrosAdministradoraPlataformaService parametrosAdministradora, ITagExtrattaApp tagExtrattaApp, IBloqueioCartaoTipoApp bloqueioCartaoTipoApp, IUsuarioPermissaoCartaoApp usuarioPermissaoCartaoApp, IParametrosUsuarioService parametrosUsuarioService)
        {
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _administradoraPlataformaApp = administradoraPlataformaApp;
            _usuarioApp = usuarioApp;
            _motoristaApp = motoristaApp;
            _mensagemApp = mensagemApp;
            _empresaApp = empresaApp;
            _veiculoApp = veiculoApp;
            _filialApp = filialApp;
            _grupoUsuarioApp = grupoUsuarioApp;
            _tipoCarretaApp = tipoCarretaApp;
            _tipoCavaloApp = tipoCavaloApp;
            _tipoDocumentoApp = tipoDocumentoApp;
            _usuarioPermissaoFinanceiroApp = usuarioPermissaoFinanceiroApp;
            _usuarioPermissaoGestorApp = usuarioPermissaoGestorApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _emailApp = emailApp;
            _cidadeApp = cidadeApp;
            _usuarioDocumentoApp = usuarioDocumentoApp;
            _bloqueioFinanceiroTipoApp = bloqueioFinanceiroTipoApp;
            _conjuntoApp = conjuntoApp;
            _bloqueioGestorTipoApp = bloqueioGestorTipoApp;
            _service = service;
            _limiteTransacaoPortadorApp = limiteTransacaoPortadorApp;
            _despesaUsuarioApp = despesaUsuarioApp;
            _parametrosAdministradora = parametrosAdministradora;
            _tagExtrattaApp = tagExtrattaApp;
            _bloqueioCartaoTipoApp = bloqueioCartaoTipoApp;
            _usuarioPermissaoCartaoApp = usuarioPermissaoCartaoApp;
            _parametrosUsuarioService = parametrosUsuarioService;
            _keycloak = keycloak;
        }

        /// <summary>
        /// Abstrai a forma que o login será feito.
        /// </summary>
        /// <returns></returns>
        public Usuario GetDadosUsuario(LoginRequestModel @params)
        {
            if (!string.IsNullOrEmpty(@params.Usuario))
                return _usuarioApp.ValidarUsuarioPorUsuario(@params.Usuario, @params.Senha);

            if (!string.IsNullOrEmpty(@params.TokenFirebase))
                return _usuarioApp.ValidarUsuarioPorToken(@params.TokenFirebase);

            return _usuarioApp.ValidarUsuarioPorCPF(@params.CPFCNPJ, @params.Senha);

        }

        /// <summary>
        /// Verificar se o usuário é válido
        /// </summary>
        /// <param name="params">Dados para login</param>
        /// <returns></returns>
        public Retorno<UsuarioModel> Login(LoginRequestModel @params)
        {
            try
            {
                #region Recupera o parâmetro de versão do aplicativo ATS

                int versaoAplicativo = 1;
                if (!string.IsNullOrWhiteSpace(WebConfigurationManager.AppSettings["VersaoAplicativo"]))
                    versaoAplicativo = Convert.ToInt32(WebConfigurationManager.AppSettings["VersaoAplicativo"]);

                #endregion

                Usuario dadosUsuario = GetDadosUsuario(@params);

                //snirfar a senha do usuario do Aplicativo, e criar no Keycloak
                if (dadosUsuario != null)
                {
                    string userName = @params.Usuario;
                    if (string.IsNullOrEmpty(userName)) userName = @params.CPFCNPJ;
                    var token = _keycloak.GetUserAccessToken(userName, @params.Senha, "");
                    if (token.StatusCode != HttpStatusCode.OK)
                    {
                        var contato = dadosUsuario.Contatos.FirstOrDefault();

                        _keycloak.CreateOrUpdateUser(userName, dadosUsuario.Nome, dadosUsuario.Contatos.FirstOrDefault().Email, @params.Senha, true,
                            new Dictionary<string, object>()
                            {
                                { "CPFCNPJ", new List<string> { dadosUsuario.CPFCNPJ } },
                                { "Perfil", new List<string> { Convert.ToString((int)dadosUsuario.Perfil) } },
                                { "IdUsuario", new List<string> { dadosUsuario.IdUsuario.ToString() } },
                                { "IdEmpresa", new List<string> { dadosUsuario.IdEmpresa.ToString() } },
                                { "Nome", new List<string> { dadosUsuario.Nome.ToString() } },
                                { "Modified", new List<string> { "login at " + DateTime.Now.ToString() + " by " + dadosUsuario.IdUsuario.ToString() } },
                            }, false);
                    }
                }

                //Verifica pré cadastro se o usuário for embarcador loga pelo pré cadastro também
                //var preCadastrto = new PreUsuarioApp().GetByCPF(@params.CPFCNPJ);
                //var hasPreCadastro = preCadastrto != null;

                if (dadosUsuario != null)
                {
                    if (dadosUsuario.Empresa != null && !dadosUsuario.Empresa.Ativo)
                        return new Retorno<UsuarioModel>(false, $"Não foi possível realizar login. Entre em contato com {dadosUsuario.Empresa.NomeFantasia}", null);

                    var motorista = _motoristaApp.Get(dadosUsuario.CPFCNPJ , true);

                    if (motorista?.IdEmpresa != null)
                        motorista.Empresa = _empresaApp.Get(motorista.IdEmpresa.Value, null);

                    bool enviarMensagens;
                    bool.TryParse(WebConfigurationManager.AppSettings["MensagensUsuario"], out enviarMensagens);

                    //Recupera as mensagens deste usuário
                    var mensagens = new List<Mensagem>();

                    if (enviarMensagens)
                        mensagens = _mensagemApp.GetMensagensPeloUsuario(dadosUsuario.IdUsuario, @params.DataDasMensagens).ToList();

                    var usuario = Mapper.Map<Usuario, UsuarioModel>(dadosUsuario);
                    usuario.CPFCNPJ = dadosUsuario.CPFCNPJ;

                    if (@params.VersaoAplicativo != null && @params.VersaoAplicativo < versaoAplicativo)
                    {
                        usuario.PermiteVersaoAplicativo = false;
                        return new Retorno<UsuarioModel>(true, string.Empty, usuario);
                    }

                    // Se o usuário está inativo encerra o processo e retorna o objeto usuario com status ativo = false.
                    if (!usuario.Ativo)
                        return new Retorno<UsuarioModel>(false, "Usuário não está mais ativo no " + WebConfigurationManager.AppSettings["TITULO_SISTEMA"], usuario);

                    usuario.Foto = @params.RetornarFoto && dadosUsuario.Foto != null
                        ? Convert.ToBase64String(dadosUsuario.Foto)
                        : null;

                    if (usuario.Carreteiro)
                        usuario.Carreteiro = true;
                    else if (motorista?.Veiculos.Count > 0 && usuario.Carreteiro)
                        usuario.Carreteiro = true;
                    else
                        usuario.Carreteiro = false;

                    if (motorista != null)
                        usuario.TipoContrato = motorista.TipoContrato;

                    #region Veiculo

                    // Primeiro irá carregar o veículo vinculado ao motorista.
                    VeiculoModel veiculoMobile = null;
                    if (motorista?.IdEmpresa != null)
                        veiculoMobile = Mapper.Map<Veiculo, VeiculoModel>(_veiculoApp.GetVeiculoPorEmpresaMotorista(motorista.IdEmpresa.GetValueOrDefault(), motorista.IdMotorista));

                    // Não havendo veículo vinculado ao usuário e posteriormente ao Empresa, irá buscar o do cadastro do usuário
                    if (veiculoMobile == null && dadosUsuario.Veiculos != null)
                        veiculoMobile = Mapper.Map<Veiculo, VeiculoModel>(
                            dadosUsuario.Veiculos?.FirstOrDefault(v => v.Ativo));

                    if (veiculoMobile != null)
                    {
                        usuario.Placa = veiculoMobile.Placa;
                        usuario.ComTracao = veiculoMobile.ComTracao;
                        usuario.Marca = veiculoMobile.Marca;
                        usuario.Modelo = veiculoMobile.Modelo;
                        usuario.Chassi = veiculoMobile.Chassi;
                        usuario.RENAVAM = veiculoMobile.RENAVAM;
                        usuario.AnoModelo = veiculoMobile.AnoModelo;
                        usuario.AnoFabricacao = veiculoMobile.AnoFabricacao;
                        usuario.IdTipoCavalo = veiculoMobile.IdTipoCavalo;
                        usuario.IdTipoCarreta = veiculoMobile.IdTipoCarreta;
                        usuario.TipoRodagem = veiculoMobile.TipoRodagem;
                        usuario.TecRastreamento = veiculoMobile.TecnologiaRastreamento;
                    }

                    #endregion

                    #region Endereço

                    if (motorista != null)
                    {
                        usuario.CEP = motorista.CEP;
                        usuario.Endereco = motorista.Endereco;
                        usuario.Numero = !string.IsNullOrWhiteSpace(motorista.Numero) ? (int?)Convert.ToInt32(motorista.Numero) : null;
                        usuario.Bairro = motorista.Bairro;
                        usuario.IBGECidade = motorista.Cidade?.IBGE;
                    }
                    else
                    {
                        if (dadosUsuario.Enderecos != null)
                        {
                            usuario.CEP = dadosUsuario.Enderecos.FirstOrDefault()?.CEP;
                            usuario.Endereco = dadosUsuario.Enderecos.FirstOrDefault()?.Endereco;
                            usuario.Numero = dadosUsuario.Enderecos.FirstOrDefault()?.Numero;
                            usuario.Bairro = dadosUsuario.Enderecos.FirstOrDefault()?.Bairro;
                            usuario.IBGECidade = dadosUsuario.Enderecos.FirstOrDefault()?.Cidade?.IBGE;
                        }
                    }

                    #endregion

                    #region Filiais

                    var usuarioFilial = dadosUsuario.Filiais?.FirstOrDefault();
                    if (usuarioFilial != null)
                    {
                        usuario.IdFilial = usuarioFilial.IdFilial;
                        usuario.NomeFilial = _filialApp.Get(usuarioFilial.IdFilial)?.NomeFantasia;
                    }

                    #endregion

                    #region Contato

                    var usuarioContato = dadosUsuario.Contatos?.FirstOrDefault();

                    if (usuarioContato != null)
                    {
                        usuario.Telefone = usuarioContato.Telefone;
                        usuario.Celular = usuarioContato.Celular;
                        usuario.Email = usuarioContato.Email;
                    }

                    #endregion

                    #region Horários

                    if (dadosUsuario.HorariosCheckIn != null && dadosUsuario.HorariosCheckIn.Any())
                        usuario.HorariosCheckIn = dadosUsuario.HorariosCheckIn.Select(h => h.Horario).ToList();

                    #endregion

                    #region Atualizar Push

                    var validationResult = new ValidationResult();
                    if (!string.IsNullOrWhiteSpace(@params.IdPush) && @params.IdPush.Length > 20)
                    {
                        if (!validationResult.IsValid)
                            return new Retorno<UsuarioModel>(false, validationResult.ToFormatedMessage(false), null);
                    }

                    #endregion

                    #region Atualizar o Id do projeto firebase dos aplicativos, pelo aplicativo que o usuário está utilizando

                    validationResult = _parametrosApp.SetIdProjetoFireBase(usuario.IdUsuario, @params.ProjetoFirebase ?? 1);

                    if (!validationResult.IsValid)
                        return new Retorno<UsuarioModel>(false, validationResult.ToFormatedMessage(false), null);

                    #endregion

                    #region Atualizar data do último acesso

                    var validation = new ValidationResult();

                    if (!validation.IsValid)
                        return new Retorno<UsuarioModel>(false, validation.ToFormatedMessage(false), null);

                    #endregion

                    #region Mensagens

                    foreach (var mensagem in mensagens)
                    {
                        var mensagemRetorno = new MensagemModel
                        {
                            Assunto = mensagem.Assunto,
                            Conteudo = mensagem.Conteudo,
                            DataHoraEnvio = mensagem.DataHoraEnvio,
                            IdMensagem = mensagem.IdMensagem,
                            IdUsuarioRemetente = mensagem.IdUsuarioRemetente
                        };

                        var usuarioRemetente = _usuarioApp.Get(mensagem.IdUsuarioRemetente);

                        mensagemRetorno.Remetente = usuarioRemetente.Nome;
                        mensagemRetorno.RazaoSocialEmpresa = usuarioRemetente.IdEmpresa != null
                            ? _empresaApp.Get(usuarioRemetente.IdEmpresa.Value, null).RazaoSocial
                            : string.Empty;

                        usuario.Mensagens.Add(mensagemRetorno);
                    }

                    #endregion

                    #region Grupo de usuário

                    if (dadosUsuario.GrupoUsuario != null)
                    {
                        var grupoUsuario = _grupoUsuarioApp.Get(dadosUsuario.GrupoUsuario.IdGrupoUsuario);
                        usuario.GrupoUsuario = Mapper.Map<GrupoUsuario, GrupoUsuarioModel>(grupoUsuario);
                    }
                    #endregion

                    if (dadosUsuario.Empresa != null)
                    {
                        usuario.Empresa = new EmpresaModel();
                        usuario.Empresa.IdEmpresa = dadosUsuario.IdEmpresa.Value;
                        usuario.Empresa.ObrigarValorTerceiro = dadosUsuario.Empresa.ObrigarValorTerceiro;
                        usuario.Empresa.PrioridadeCooperadoCargas = dadosUsuario.Empresa.PrioridadeCooperadoCargas;
                        usuario.Empresa.RaioCooperado = dadosUsuario.Empresa.RaioCooperado;
                        usuario.Empresa.PeriodoIniEnvCheckInCooperado = dadosUsuario.Empresa.PeriodoIniEnvCheckInCooperado;
                        usuario.Empresa.PeriodoFimEnvCheckInCooperado = dadosUsuario.Empresa.PeriodoFimEnvCheckInCooperado;
                        usuario.Empresa.RaioTerceiro = dadosUsuario.Empresa.RaioTerceiro;
                        usuario.Empresa.PeriodoIniEnvCheckInTerceiro = dadosUsuario.Empresa.PeriodoIniEnvCheckInTerceiro;
                        usuario.Empresa.PeriodoFimEnvCheckInTerceiro = dadosUsuario.Empresa.PeriodoFimEnvCheckInTerceiro;
                        usuario.Empresa.RoteirizarCarga = dadosUsuario.Empresa.RoteirizarCarga;
                        usuario.Empresa.CNPJ = dadosUsuario.Empresa.CNPJ;
                        usuario.Empresa.EmailSugestoes = dadosUsuario.Empresa.EmailSugestoes;
                        usuario.Empresa.Logo = dadosUsuario.Empresa.Logo != null ? Convert.ToBase64String(dadosUsuario.Empresa.Logo) : string.Empty;
                    }

                    return new Retorno<UsuarioModel>(true, string.Empty, usuario);
                }

                return new Retorno<UsuarioModel>("Usuário ou senha inválida");
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<UsuarioModel>($"{nameof(Login)} >> {e.Message}");
            }
        }

        public ValidationResult InserirParaNovoEstabelecimento(CadastrarAtualizarCls prm)
        {
            var nUsu = new Usuario();
            var validation = PreencheNovoUsuario(prm, nUsu);

            validation.Add(_usuarioApp.Add(nUsu, null));

            return validation;
        }

        public ValidationResult InserirNovo(CadastrarAtualizarCls prm, IUserIdentity userIdentity)
        {
            var nUsu = new Usuario();
            var validation = PreencheNovoUsuario(prm, nUsu);

            if(prm.perfil != EPerfil.Motorista && prm.perfil != EPerfil.Proprietario && !_parametrosApp.GetPermitirEdicaoDadosAdministrativosUsuario(userIdentity.IdUsuario))
                return validation.Add("Usuário sem permissão.");

            #region Veículo
            if ((prm.perfil == EPerfil.Motorista || prm.perfil == EPerfil.Proprietario) && prm.carreteiro)
            {
                if (prm.veiculoAnoFabricacao == 0
                || prm.veiculoAnoModelo == 0
                || prm.veiculoTipoRodagem == 0
                || prm.veiculoQtdeEixos == 0
                || prm.veiculoPlaca == null
                || prm.veiculoRenavam == null
                || prm.veiculoChassi == null
                || prm.veiculoMarca == null
                || prm.veiculoModelo == null
                || !prm.veiculoIdTipoCavalo.HasValue
                || !prm.veiculoIdTipoCarreta.HasValue)
                    return validation.Add("É necessário informar todos os campos obrigatórios do veículo quando carreteiro!");

                // Validação da duplicidade de motoristas terceiros..
                var veiculoPlaca = _veiculoApp.GetVeiculosPorPlaca(new List<string> { prm.veiculoPlaca });
                if (veiculoPlaca != null && veiculoPlaca.Any(x => !x.IdEmpresa.HasValue && x.IdUsuario.HasValue && x.Ativo))
                    return validation.Add($"O veículo {prm.veiculoPlaca.ToPlacaFormato()} não está disponível! Já está sendo utilizado por outro motorista/empresa!");

                var veic = new Veiculo();
                veic.AnoFabricacao = prm.veiculoAnoFabricacao;
                veic.AnoModelo = prm.veiculoAnoModelo;
                veic.TipoRodagem = prm.veiculoTipoRodagem;
                veic.QuantidadeEixos = prm.veiculoQtdeEixos;
                veic.Placa = prm.veiculoPlaca;
                veic.RENAVAM = prm.veiculoRenavam;
                veic.Chassi = prm.veiculoChassi;
                veic.Marca = prm.veiculoMarca;
                veic.Modelo = prm.veiculoModelo;
                veic.ComTracao = prm.veiculoTracao;

                if (prm.veiculoIdTipoCarreta > 0)
                {
                    var tipoCarreta = _tipoCarretaApp.Get(prm.veiculoIdTipoCarreta.Value);
                    if (tipoCarreta != null)
                        veic.IdTipoCarreta = tipoCarreta.IdTipoCarreta;
                }

                if (prm.veiculoIdTipoCavalo > 0)
                {
                    var tpCavalo = _tipoCavaloApp.Get(prm.veiculoIdTipoCavalo.Value);
                    if (tpCavalo != null)
                        veic.IdTipoCavalo = tpCavalo.IdTipoCavalo;
                }

                nUsu.Veiculos = new List<Veiculo>();
                nUsu.Veiculos.Add(veic);
            }
            #endregion

            #region Filiais
            if (prm.filiaisAdicionadas.Any())
            {
                nUsu.Filiais = new List<UsuarioFilial>();
                prm.filiaisAdicionadas.ForEach(fil => nUsu.Filiais.Add(new UsuarioFilial
                {
                    IdFilial = fil
                }));
            }
            #endregion

            #region Documentos
            if (prm.documentosAdicionados.Any())
            {
                var tpDocCnh = _tipoDocumentoApp.GetCnh();
                var hasCnhDoc = prm.documentosAdicionados.Any(x => tpDocCnh != null && x.IdTipoDocumento == tpDocCnh.Value);
                if (!hasCnhDoc && prm.perfil == EPerfil.Motorista)
                    return validation.Add("É necessário informar ao menos um documento do tipo CNH!");

                nUsu.Documentos = new List<UsuarioDocumento>();
                prm.documentosAdicionados.ForEach(doc => nUsu.Documentos.Add(new UsuarioDocumento
                {
                    IdTipoDocumento = doc.IdTipoDocumento,
                    Validade = Convert.ToDateTime(doc.Validade)
                }));
            }
            else if (prm.perfil == EPerfil.Motorista)
                return validation.Add("É necessário informar ao menos um documento do tipo CNH!");
            #endregion

            #region Clientes
            if (prm.clientesMarcados.Any())
            {
                nUsu.Clientes = new List<UsuarioCliente>();
                prm.clientesMarcados.ForEach(cli => nUsu.Clientes.Add(new UsuarioCliente
                {
                    IdCliente = cli,
                    IsChecked = true
                }));
            }
            #endregion

            if (prm.perfil == EPerfil.Cliente)
                nUsu.Login = prm.cpfcnpj.OnlyNumbers();

            #region Validações
            if (prm.perfil == EPerfil.Estabelecimento)
            {
                if (userIdentity.Perfil != (int)EPerfil.Estabelecimento)
                    return validation.Add("Apenas usuários de perfil Estabelecimento podem realizar este cadastro.");

                nUsu.UsuarioEstabelecimentos = new List<UsuarioEstabelecimento>();
                var estabelecimentosUsuarioLogado = _usuarioApp.GetAllChilds(userIdentity.IdUsuario);
                estabelecimentosUsuarioLogado.UsuarioEstabelecimentos.ToList().ForEach(estab => nUsu.UsuarioEstabelecimentos.Add(new UsuarioEstabelecimento
                {
                    IdEstabelecimento = estab.IdEstabelecimento
                }));
            }

            if (userIdentity.Perfil != (int) EPerfil.Administrador && prm.perfil == EPerfil.Administrador)
                return validation.Add("Usuário não autenticado.");

            // Sendo motorista e o mesmo estiver setado como 'Carreteiro: Não'
            // as informações de veículo nunca irão existir, sendo o objeto apenas inativado.
            if (prm.perfil == EPerfil.Motorista && !prm.carreteiro)
                nUsu.Veiculos = null;

            // Validação da duplicidade de motoristas terceiros..
            if (nUsu.Perfil == EPerfil.Motorista)
            {
                var placa = nUsu.Veiculos?.FirstOrDefault()?.Placa;
                var veiculoSemEmpresaExiste = nUsu.Veiculos?.FirstOrDefault() != null && _usuarioApp.HasVeiculoCadastrado(placa);

                if (veiculoSemEmpresaExiste)
                    return validation.Add($"Já existe um veículo com a mesma placa cadastrado!");
            }

            #endregion

            if (string.IsNullOrWhiteSpace(prm.senha))
            {
                prm.senha = _service.GerarSenhaAleatoria(nUsu.Perfil);
                nUsu.Senha = prm.senha;
            }

            validation.Add(_usuarioApp.Add(nUsu, userIdentity.IdUsuario, ""));

            if (!validation.IsValid)
                return validation;

            #region Parametros

            if (prm.EnviarSenha)
            {
                var usuarioRequisicao = _service.Get(userIdentity.IdUsuario, true);
                _service.EnviarEmailRecuperacaoSenha(nUsu, usuarioRequisicao, prm.senha, null, "usuarionovo");
            }

            var permissaoUsuario = new PermissaoUsuarioAlterarLimiteAlcadas
            {
                PermiteEmpresa = prm.PermiteAlterarLimiteEmpresa,
                PermiteFilial = prm.PermiteAlterarLimiteFilial
            };

            validation.Add(_parametrosApp.SetPermitirAcessoExtratoDetalhadoUsuario(nUsu.IdUsuario, prm.PermiteAcessarExtratoDetalhado));
            validation.Add(_parametrosApp.SetPermissaoUsuarioAlterarLimiteAlcadas(permissaoUsuario, nUsu.IdUsuario));
            validation.Add(_parametrosApp.SetAplicativoPermiteRealizarTransferenciaBancaria(nUsu.IdUsuario, prm.AplicativoPermiteRealizarTransferenciaBancaria));
            validation.Add(_parametrosApp.SetAplicativoPermiteRealizarTransferenciaCartoes(nUsu.IdUsuario, prm.AplicativoPermiteRealizarTransferenciaCartoes));
            validation.Add(_parametrosApp.SetPermiteAprovarSolicitacaoAdiantamentoApp(nUsu.IdUsuario, prm.PermiteAprovarSolicitacaoAdiantamentoApp));
            validation.Add(_parametrosApp.SetPermiteSolicitarAdiantamentoApp(nUsu.IdUsuario, prm.PermiteSolicitarAdiantamentoApp));
            validation.Add(_parametrosApp.SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(nUsu.IdUsuario, prm.PermiteEfetuarCargaSolicitacaoAdiantamentoApp));

            if (prm.PermissaoFinanceiro != null)
            {
                foreach (var permissao in prm.PermissaoFinanceiro)
                {
                    var enumTipo = (EBloqueioFinanceiroTipo)permissao.IdBloqueioFinanceiro;
                    _usuarioPermissaoFinanceiroApp.Integrar(nUsu.IdUsuario, enumTipo, permissao.DesbloquearFinanceiro);
                }
            }

            if (prm.PermissaoCartao != null)
            {
                foreach (var permissao in prm.PermissaoCartao)
                {
                    var enumTipo = (EBloqueioCartaoTipo)permissao.IdBloqueioCartao;
                    _usuarioPermissaoCartaoApp.Integrar(nUsu.IdUsuario, enumTipo, permissao.DesbloquearCartao);
                }
            }

            if (prm.permissaoTag != null)
            {
                var request = Mapper.Map<BloqueiosTagUsuarioRequest>(prm.permissaoTag);

                foreach (var item in request.BloqueiosTag)
                    item.UsuarioId = nUsu.IdUsuario;

                _tagExtrattaApp.CadastrarBloqueios(request);
            }

            var usuarioPermissaoGestorApp = _usuarioPermissaoGestorApp;

            if (prm.permissoes != null)
                foreach (var permissao in prm.permissoes)
                    validation.Add(usuarioPermissaoGestorApp.Integrar(nUsu.IdUsuario, (EBloqueioGestorTipo)permissao.idBloqueioGestorTipo,
                        permissao.habilitarPorEmpresa, permissao.habilitarPorFilial));

            if (prm.LimitesPortador != null)
            {
                foreach (var limite in prm.LimitesPortador)
                {
                    var enumTipo = (ETipoLimiteTransacaoPortador)limite.Id;
                    _limiteTransacaoPortadorApp.LimitarValor(nUsu.CPFCNPJ, enumTipo, limite.Valor);
                }
            } 
            else
            {
                var perfisValidos = new List<EPerfil> { EPerfil.Empresa, EPerfil.Motorista, EPerfil.Proprietario };

                if (perfisValidos.Contains(prm.perfil))
                    _limiteTransacaoPortadorApp.LimitarValoresPadrão(prm.tipoPessoa, nUsu.CPFCNPJ);
 
            }

            if(prm.PermissoesAtendimentoCartao != null)
                validation.Add(_parametrosApp.SetPermissoesUsuarioAtendimentoCartao(nUsu.IdUsuario, prm.PermissoesAtendimentoCartao));


            var limiteDiarioPix = prm.LimiteDiarioPagamentoPix <= 0 ? null : prm.LimiteDiarioPagamentoPix;
            validation.Add(_parametrosApp.SetLimiteDiarioPagamentoPixUsuario(nUsu.IdUsuario, limiteDiarioPix));
            var limiteUnitarioPix = prm.LimiteUnitarioPagamentoPix <= 0 ? null : prm.LimiteUnitarioPagamentoPix;
            validation.Add(_parametrosApp.SetLimiteUnitarioPagamentoPixUsuario(nUsu.IdUsuario, limiteUnitarioPix));
            validation.Add(_parametrosUsuarioService.SetPermitirEdicaoDadosAdministrativosPix(nUsu.IdUsuario, prm.PermitirEdicaoDadosAdministrativosPix ?? false));
            validation.Add(_parametrosUsuarioService.SetPermiteRealizarPagamentoPix(nUsu.IdUsuario, prm.PermiteRealizarPagamentoPix ?? false));
            validation.Add(_parametrosUsuarioService.SetPermitirCadastroChavePix(nUsu.IdUsuario, prm.PermitirCadastroChavePix ?? false));
            validation.Add(_parametrosUsuarioService.SetPermiteSolicitarAlteracoesLimitePix(nUsu.IdUsuario, prm.PermiteSolicitarAlteracoesLimitePix ?? false));
            validation.Add(_parametrosUsuarioService.SetGestorAlcadasPix(nUsu.IdUsuario, prm.GestorAlcadasPix ?? false));
            
            #endregion

            return validation;
        }

        private ValidationResult PreencheNovoUsuario(CadastrarAtualizarCls prm, Usuario nUsu)
        {
            var validation = new ValidationResult();

            nUsu.IdEmpresa = prm.idEmpresa;
            nUsu.IdGrupoUsuario = prm.idGrupoUsuario;
            nUsu.IdHorario = prm.idHorario;
            nUsu.IdPonto = prm.idPonto;
            nUsu.CNH = prm.numeroCNH;
            nUsu.CNHCategoria = prm.categoriaCNH;
            nUsu.Nome = prm.nome;
            nUsu.CPFCNPJ = prm.cpfcnpj;
            nUsu.Senha = prm.senha;
            nUsu.TipoCobranca = prm.tipoPessoa == ETipoPessoa.Fisica ? ETipoCobranca.PessoaFisica : ETipoCobranca.PessoaJuridica;
            nUsu.Login = prm.login;
            nUsu.Carreteiro = prm.carreteiro;
            nUsu.ReceberNotificacao = prm.receberNotificacoes;
            nUsu.TipoCliente = prm.tipoCliente;
            nUsu.Perfil = prm.perfil;
            nUsu.PermiteResponderChat = prm.permitirResponderChat;
            nUsu.Gestor = prm.gestor;
            nUsu.Referencia1 = prm.referencia1;
            nUsu.Referencia2 = prm.referencia2;
            nUsu.ReceberRelatorioOC = prm.receberRelatorioOC;
            nUsu.Nome = new Regex("[*'\",_&#^@]").Replace(nUsu.Nome, String.Empty);
            nUsu.Matriz = prm.Matriz;
            nUsu.RecebeEmailGestao = prm.RecebeEmailGestao;
            nUsu.UsuarioMasterEstabelecimento = prm.usuarioMasterEstabelecimento;
            if (prm.idEstabelecimentoBase.HasValue)
                nUsu.UsuarioEstabelecimentos = new List<UsuarioEstabelecimento> {new UsuarioEstabelecimento {IdEstabelecimento = prm.idEstabelecimentoBase.Value}};

            if (prm.imagemPerfilB64 != null)
                nUsu.Foto = Convert.FromBase64String(prm.imagemPerfilB64);

            #region Endereço
            if (prm.enderecoBairro == null
                || prm.enderecoIdPais == 0
                || prm.enderecoIdEstado == 0
                || prm.enderecoIdCidade == 0
                || prm.cep == null
                || prm.endereco == null)
                return validation.Add("É necessário informar todos os campos obrigatórios do endereço!");

            nUsu.Enderecos = new List<UsuarioEndereco>();
            nUsu.Enderecos.Add(new UsuarioEndereco
            {
                Bairro = prm.enderecoBairro,
                CEP = prm.cep,
                IdCidade = prm.enderecoIdCidade,
                IdEstado = prm.enderecoIdEstado,
                IdPais = prm.enderecoIdPais,
                Complemento = prm.enderecoComplemento,
                Endereco = prm.endereco,
                Numero = !string.IsNullOrEmpty(prm.enderecoNumero) ? Convert.ToInt32(StringExtension.OnlyNumbers(prm.enderecoNumero)) : 0
            });
            #endregion

            #region Contato

            if (!string.IsNullOrWhiteSpace(prm.celular) || !string.IsNullOrWhiteSpace(prm.email) || !string.IsNullOrWhiteSpace(prm.telefone))
            {
                nUsu.Contatos = new List<UsuarioContato>();
                nUsu.Contatos.Add(new UsuarioContato
                {
                    Celular = prm.celular,
                    Email = prm.email,
                    Telefone = prm.telefone
                });

            }
            #endregion

            return validation;
        }

        #region Gerar Hash

        public Retorno<object> GerarKeyCodeTransaction(int? idUsuario, string cpf)
        {
            if (!idUsuario.HasValue)
                return new Retorno<object>(false, "Id do usuário não informado", null);
            if (string.IsNullOrEmpty(cpf))
                return new Retorno<object>(false, "CPF não informado", null);

            var response = _usuarioApp.GerarKeyCodeTransaction(idUsuario.Value, cpf);

            if (!response.Key.IsValid)
                return new Retorno<object>(false, response.Key.Errors.FirstOrDefault()?.Message, null);

            return new Retorno<object>(true, "Key Gerada com sucesso", new { KeyCodeTransaction = response.Value });
        }

        #endregion

        public ValidationResult AtualizarExistente(CadastrarAtualizarCls @params, IUserIdentity userIdentity)
        {
            var validation = new ValidationResult();

            if (!@params.idUsuario.HasValue)
                return validation.Add("É necessário informar o idUsuario para edita-lo!");

            var userEdit = _usuarioApp.GetAllChilds(@params.idUsuario.Value);

            if (userEdit == null)
                return validation.Add($"Usuário não encontrado para o id {@params.idUsuario.Value}!");

            // Validação da duplicidade de motoristas terceiros..
            if (userEdit.Perfil == EPerfil.Motorista)
            {
                var veiculoPlaca = _veiculoApp.GetVeiculosPorPlaca(new List<string> { @params.veiculoPlaca });
                if (veiculoPlaca != null && veiculoPlaca.Any(x => !x.IdEmpresa.HasValue && x.IdUsuario.HasValue && x.IdUsuario != userEdit.IdUsuario && x.Ativo))
                    return validation.Add($"O veículo { @params.veiculoPlaca.ToPlacaFormato()} não está disponível! Já está sendo utilizado por outro motorista/empresa!");
            }

            //Validacoes de campos que não podem ser editados
            if(userEdit.Perfil != EPerfil.Administrador && @params.perfil == EPerfil.Administrador ||
               userEdit.CPFCNPJ != @params.cpfcnpj ||
               userEdit.Login != @params.login)
                return validation.Add("Usuário não autenticado.");

            var permiteEditarDadosAdm = _parametrosApp.GetPermitirEdicaoDadosAdministrativosUsuario(userIdentity.IdUsuario);

            //Validacoes de campos apenas admin
            if (userIdentity.Perfil != (int)EPerfil.Administrador || (userIdentity.Perfil == (int)EPerfil.Administrador && !permiteEditarDadosAdm))
            {
                if(userEdit.Perfil != @params.perfil ||
                   userEdit.IdGrupoUsuario != @params.idGrupoUsuario ||
                   userEdit.IdEmpresa != @params.idEmpresa ||
                   ((!string.IsNullOrEmpty(@params.senha) || @params.EnviarSenha) && userEdit.Perfil != EPerfil.Motorista && userEdit.Perfil != EPerfil.Proprietario) ||
                   (userEdit.Contatos.Any() && userEdit.Contatos.Where(c => !string.IsNullOrWhiteSpace(c.Email)).All(c => c.Email != @params.email) && userEdit.Perfil != EPerfil.Motorista && userEdit.Perfil != EPerfil.Proprietario))
                    return validation.Add("Usuário não autenticado.");
            }

            userEdit.IdEmpresa = @params.idEmpresa;
            userEdit.IdGrupoUsuario = @params.idGrupoUsuario;
            userEdit.IdHorario = @params.idHorario;
            userEdit.IdPonto = @params.idPonto;
            userEdit.CNH = @params.numeroCNH;
            userEdit.CNHCategoria = @params.categoriaCNH;
            userEdit.Nome = @params.nome;
            userEdit.TipoCobranca = @params.tipoPessoa == ETipoPessoa.Juridica ? ETipoCobranca.PessoaJuridica : ETipoCobranca.PessoaFisica;
            userEdit.CPFCNPJ = @params.cpfcnpj;
            if (!string.IsNullOrEmpty(@params.senha))
                userEdit.Senha = @params.senha;
            userEdit.Login = @params.login;
            userEdit.Carreteiro = @params.carreteiro;
            userEdit.ReceberNotificacao = @params.receberNotificacoes;
            userEdit.TipoCliente = @params.tipoCliente;
            userEdit.Perfil = @params.perfil;
            userEdit.PermiteResponderChat = @params.permitirResponderChat;
            userEdit.Gestor = @params.gestor;
            userEdit.Matriz = @params.Matriz;
            userEdit.RecebeEmailGestao = @params.RecebeEmailGestao;

            //if (!string.IsNullOrWhiteSpace(@params.senha))
            //    userEdit.Senha = @params.senha;

            if (!string.IsNullOrWhiteSpace(@params.imagemPerfilB64))
                userEdit.Foto = Convert.FromBase64String(@params.imagemPerfilB64);

            userEdit.Referencia1 = @params.referencia1;
            userEdit.Referencia2 = @params.referencia2;
            userEdit.ReceberRelatorioOC = @params.receberRelatorioOC;

            #region Endereço
            if (userEdit.Enderecos.Any())
            {
                if (@params.enderecoBairro == null
                || @params.enderecoIdPais == 0
                || @params.enderecoIdEstado == 0
                || @params.enderecoIdCidade == 0
                || @params.cep == null
                || @params.endereco == null)
                    return validation.Add("É necessário informar todos os campos obrigatórios do endereço!");

                var endereco = userEdit.Enderecos.FirstOrDefault();
                if (endereco != null)
                {
                    endereco.Bairro = @params.enderecoBairro;
                    endereco.CEP = @params.cep;
                    endereco.IdCidade = @params.enderecoIdCidade;
                    endereco.IdEstado = @params.enderecoIdEstado;
                    endereco.IdPais = @params.enderecoIdPais;
                    endereco.Complemento = @params.enderecoComplemento;
                    endereco.Endereco = @params.endereco;
                    endereco.Numero = !string.IsNullOrEmpty(@params.enderecoNumero) ? Convert.ToInt32(StringExtension.OnlyNumbers(@params.enderecoNumero)) : 0;
                }
            }
            #endregion

            #region Veículo
            if ((@params.perfil == EPerfil.Motorista || @params.perfil == EPerfil.Proprietario) && @params.carreteiro)
            {
                if (@params.veiculoAnoFabricacao == 0
                    || @params.veiculoAnoModelo == 0
                    || @params.veiculoTipoRodagem == 0
                    || @params.veiculoQtdeEixos == 0
                    || @params.veiculoPlaca == null
                    || @params.veiculoRenavam == null
                    || @params.veiculoChassi == null
                    || @params.veiculoMarca == null
                    || @params.veiculoModelo == null
                    || !@params.veiculoIdTipoCavalo.HasValue
                    || !@params.veiculoIdTipoCarreta.HasValue)
                    return validation.Add("É necessário informar todos os campos obrigatórios do veículo quando carreteiro!");

                var veicEdit = userEdit.Veiculos.FirstOrDefault(x => x.Ativo);
                if (veicEdit != null && @params.veiculoPlaca == veicEdit.Placa)
                {
                    veicEdit.AnoFabricacao = @params.veiculoAnoFabricacao;
                    veicEdit.AnoModelo = @params.veiculoAnoModelo;
                    veicEdit.TipoRodagem = @params.veiculoTipoRodagem;
                    veicEdit.QuantidadeEixos = @params.veiculoQtdeEixos;
                    veicEdit.Placa = @params.veiculoPlaca;
                    veicEdit.RENAVAM = @params.veiculoRenavam;
                    veicEdit.Chassi = @params.veiculoChassi;
                    veicEdit.Marca = @params.veiculoMarca;
                    veicEdit.Modelo = @params.veiculoModelo;
                    veicEdit.ComTracao = @params.veiculoTracao;
                    veicEdit.IdTipoCarreta = @params.veiculoIdTipoCarreta;
                    veicEdit.IdTipoCavalo = @params.veiculoIdTipoCavalo;

                    #region tratamento de FK
                    if (@params.veiculoIdTipoCarreta > 0)
                    {
                        var tipoCarreta = _tipoCarretaApp.Get(@params.veiculoIdTipoCarreta.Value);
                        if (tipoCarreta != null)
                            veicEdit.IdTipoCarreta = tipoCarreta.IdTipoCarreta;
                    }

                    if (@params.veiculoIdTipoCavalo > 0)
                    {
                        var tpCavalo = _tipoCavaloApp.Get(@params.veiculoIdTipoCavalo.Value);
                        if (tpCavalo != null)
                            veicEdit.IdTipoCavalo = tpCavalo.IdTipoCavalo;
                    }

                    #endregion
                }
                else
                {
                    // Remove o vínculo e cadastra um novo
                    userEdit.Veiculos = new List<Veiculo>();
                    var newVeic = new Veiculo()
                    {
                        AnoFabricacao = @params.veiculoAnoFabricacao,
                        AnoModelo = @params.veiculoAnoModelo,
                        TipoRodagem = @params.veiculoTipoRodagem,
                        QuantidadeEixos = @params.veiculoQtdeEixos,
                        Placa = @params.veiculoPlaca,
                        RENAVAM = @params.veiculoRenavam,
                        Chassi = @params.veiculoChassi,
                        Marca = @params.veiculoMarca,
                        Modelo = @params.veiculoModelo,
                        ComTracao = @params.veiculoTracao,
                        IdTipoCavalo = @params.veiculoIdTipoCavalo,
                        IdTipoCarreta = @params.veiculoIdTipoCarreta
                    };

                    userEdit.Veiculos.Add(newVeic);
                }
            }
            #endregion

            #region Contato
            var contatoEdit = userEdit.Contatos.FirstOrDefault();
            if (contatoEdit == null)
                userEdit.Contatos.Add(new UsuarioContato
                {
                    Celular = @params.celular,
                    Email = @params.email,
                    Telefone = @params.telefone
                });
            else
            {
                contatoEdit.Celular = @params.celular;
                contatoEdit.Email = @params.email;
                contatoEdit.Telefone = @params.telefone;
            }

            #endregion

            #region Filiais
            if (@params.filiaisAdicionadas.Any() || @params.filiaisRemovidas.Any())
            {
                if (!userEdit.Filiais.Any())
                {
                    userEdit.Filiais = new List<UsuarioFilial>();
                    @params.filiaisAdicionadas.ForEach(fil => userEdit.Filiais.Add(new UsuarioFilial
                    {
                        IdFilial = fil
                    }));
                }
                else
                {
                    // Inserção de novas
                    var idsFiliaisExistentes = userEdit.Filiais.Select(x => x.IdFilial);

                    var idsToAdd = @params.filiaisAdicionadas
                        .Where(filial => !idsFiliaisExistentes.Contains(filial))
                        .ToArray();

                    if (idsToAdd.Any())
                    {
                        foreach (var id in idsToAdd)
                        {
                            var toAdd = new UsuarioFilial { IdFilial = id };
                            userEdit.Filiais.Add(toAdd);
                        }
                    }

                    // Remoção
                    var filiaisToRemove = userEdit.Filiais
                        .Where(uf => @params.filiaisRemovidas.Contains(uf.IdFilial))
                        .ToArray();

                    if (filiaisToRemove.Any())
                        foreach (var usuarioFilial in filiaisToRemove)
                            userEdit.Filiais.Remove(usuarioFilial);
                }
            }
            #endregion

            #region Documentos
            if (@params.documentosAdicionados.Any() || @params.documentosRemovidos.Any())
            {
                var tpDocCnh = _tipoDocumentoApp.GetCnh();
                if (!tpDocCnh.HasValue)
                    return validation.Add("É necessário informar ao menos um documento do tipo CNH!");

                var hasCnhDoc = @params.documentosAdicionados.Any(x => x.IdTipoDocumento == tpDocCnh.Value);

                if (!hasCnhDoc && @params.perfil == EPerfil.Motorista)
                    return validation.Add("É necessário informar ao menos um documento do tipo CNH!");

                if (userEdit.Documentos != null && !userEdit.Documentos.Any())
                {
                    userEdit.Documentos = new List<UsuarioDocumento>();
                    if (@params.documentosAdicionados.Any())
                        @params.documentosAdicionados.ForEach(doc => userEdit.Documentos.Add(new UsuarioDocumento
                        {
                            IdTipoDocumento = doc.IdTipoDocumento,
                            Validade = Convert.ToDateTime(doc.Validade)
                        }));
                }
                else
                {
                    // Inserção de novas
                    var idsDocumentosExistentes = userEdit.Documentos.Select(x => x.IdUsuarioDocumento);
                    var idsToAdd = from p in idsDocumentosExistentes
                                   where !@params.documentosAdicionados.Select(x => x.IdUsuarioDocumento).Contains(p)
                                   && !@params.documentosRemovidos.Contains(p)
                                   select p;

                    idsToAdd?.ToList().ForEach(item => userEdit.Documentos.Add(new UsuarioDocumento { IdTipoDocumento = item }));

                    // Remoção
                    (from p in userEdit.Documentos where @params.documentosRemovidos.Contains(p.IdUsuarioDocumento) select p)
                     .ToList()
                     .ForEach(item =>
                            userEdit.Documentos.Remove(
                                userEdit.Documentos.FirstOrDefault(x => x.IdUsuarioDocumento == item.IdUsuarioDocumento))
                    );
                }
            }
            #endregion

            #region Clientes
            if (@params.clientesMarcados.Any() || @params.clientesDesmarcados.Any())
            {
                if (!userEdit.Clientes.Any(x => x.IsChecked))
                {
                    userEdit.Clientes = new List<UsuarioCliente>();
                    if (@params.clientesMarcados.Any())
                        @params.clientesMarcados.ForEach(cliMarcado => userEdit.Clientes.Add(new UsuarioCliente
                        {
                            IdCliente = cliMarcado,
                            IsChecked = true
                        }));
                }
                else
                {
                    // Inserção de novas
                    var idsClientesMarcados = userEdit.Clientes.Select(x => x.IdCliente);
                    var idsToAdd = from p in idsClientesMarcados
                                   where !@params.clientesMarcados.Contains(p)
                                   && !@params.clientesDesmarcados.Contains(p)
                                   select p;

                    idsToAdd?.ToList().ForEach(item => userEdit.Clientes.Add(new UsuarioCliente
                    {
                        IsChecked = true,
                        IdCliente = item
                    }));

                    // Remoção
                    (from p in userEdit.Clientes where @params.clientesDesmarcados.Contains(p.IdCliente) select p)
                     .ToList()
                     .ForEach(item =>
                            userEdit.Clientes.Remove(
                                userEdit.Clientes.FirstOrDefault(x => x.IdCliente == item.IdCliente))
                    );
                }
            }
            #endregion

            //LogManager.GetCurrentClassLogger().Info(userEdit.Senha);

            if (@params.EnviarSenha || !string.IsNullOrWhiteSpace(@params.senha))
            {
                if (string.IsNullOrWhiteSpace(@params.senha))
                {
                    @params.senha = _service.GerarSenhaAleatoria(userEdit.Perfil);
                    _service.ResetarSenha(userEdit.IdUsuario, userIdentity.IdUsuario, @params.senha);
                }

                userEdit.Senha = MD5Hash.Hash($"{@params.senha}{Md5HashPassword}");
            }

            //atualizar o usuario no keycloak
            var contato = userEdit.Contatos.FirstOrDefault();
            var email = contato?.Email ?? string.Empty;
            var tempPass = (userEdit.Perfil != EPerfil.Motorista && userEdit.Perfil != EPerfil.Proprietario);
            _keycloak.CreateOrUpdateUser(userEdit.Login, userEdit.Nome, email, @params.senha ?? "", userEdit.Ativo, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { userEdit.CPFCNPJ } },
                        { "Perfil", new List<string> { Convert.ToString((int)userEdit.Perfil) } },
                        { "IdUsuario", new List<string> { userEdit.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { userEdit.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { userEdit.Nome.ToString() } },
                        { "Modified", new List<string> { "updated at " + DateTime.Now.ToString() + " by " + userIdentity.IdUsuario.ToString() } },
                    }, false);

            validation = _usuarioApp.Update(userEdit, userIdentity.IdUsuario, "");

            if (!validation.IsValid)
                return validation;

            //Se o usuario q ta editando nao é adm entao para aqui
            if(userIdentity.Perfil != (int) EPerfil.Administrador)
                return validation;

            //Qualquer adm sem permissoes especiais (N1) pode editar esses 2
            validation.Add(_parametrosApp.SetAplicativoPermiteRealizarTransferenciaBancaria(userEdit.IdUsuario, @params.AplicativoPermiteRealizarTransferenciaBancaria));
            validation.Add(_parametrosApp.SetAplicativoPermiteRealizarTransferenciaCartoes(userEdit.IdUsuario, @params.AplicativoPermiteRealizarTransferenciaCartoes));
            validation.Add(_parametrosApp.SetPermitirAcessoExtratoDetalhadoUsuario(userEdit.IdUsuario, @params.PermiteAcessarExtratoDetalhado));

            //Se nao tem permissão sendo adm entao nao faz nada a partir daqui
            //Se necessario, podemos extrair as edições dos parâmetros para outro metodo
            //Outras lógicas não adm devem ser criadas acima dessa validação
            if(userIdentity.Perfil == (int) EPerfil.Administrador && !permiteEditarDadosAdm)
                return validation;

            #region Parametros

            var permissaoUsuario = new PermissaoUsuarioAlterarLimiteAlcadas
            {
                PermiteEmpresa = @params.PermiteAlterarLimiteEmpresa,
                PermiteFilial = @params.PermiteAlterarLimiteFilial
            };

            validation.Add(_parametrosApp.SetPermissaoUsuarioAlterarLimiteAlcadas(permissaoUsuario, userEdit.IdUsuario));
            validation.Add(_parametrosApp.SetPermiteAprovarSolicitacaoAdiantamentoApp(userEdit.IdUsuario, @params.PermiteAprovarSolicitacaoAdiantamentoApp));
            validation.Add(_parametrosApp.SetPermiteSolicitarAdiantamentoApp(userEdit.IdUsuario, @params.PermiteSolicitarAdiantamentoApp));
            validation.Add(_parametrosApp.SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(userEdit.IdUsuario, @params.PermiteEfetuarCargaSolicitacaoAdiantamentoApp));

            if (@params.permissoes != null)
            {
                foreach (var permissao in @params.permissoes)
                {
                    var enumTipo = (EBloqueioGestorTipo)permissao.idBloqueioGestorTipo;
                    _usuarioPermissaoGestorApp.Integrar(userEdit.IdUsuario, enumTipo, permissao.habilitarPorEmpresa, permissao.habilitarPorFilial);
                }
            }

            if (@params.PermissaoFinanceiro != null)
            {
                foreach (var permissao in @params.PermissaoFinanceiro)
                {
                    var enumTipo = (EBloqueioFinanceiroTipo)permissao.IdBloqueioFinanceiro;
                    _usuarioPermissaoFinanceiroApp.Integrar(userEdit.IdUsuario, enumTipo, permissao.DesbloquearFinanceiro);
                }
            }
            if (@params.permissaoTag != null)
            {
                var request = Mapper.Map<BloqueiosTagUsuarioRequest>(@params.permissaoTag);

                foreach (var item in request.BloqueiosTag)
                    item.UsuarioId = userEdit.IdUsuario;

                _tagExtrattaApp.CadastrarBloqueios(request);
            }

            if (@params.PermissaoCartao != null)
            {
                foreach (var permissao in @params.PermissaoCartao)
                {
                    var enumTipo = (EBloqueioCartaoTipo)permissao.IdBloqueioCartao;
                    _usuarioPermissaoCartaoApp.Integrar(userEdit.IdUsuario, enumTipo, permissao.DesbloquearCartao);
                }
            }

            if (@params.LimitesPortador != null)
            {
                foreach (var limite in @params.LimitesPortador)
                {
                    var enumTipo = (ETipoLimiteTransacaoPortador)limite.Id;
                    _limiteTransacaoPortadorApp.LimitarValor(userEdit.CPFCNPJ, enumTipo, limite.Valor);
                }
            }

            if (@params.PermissoesAtendimentoCartao != null)
                validation.Add(_parametrosApp.SetPermissoesUsuarioAtendimentoCartao(@params.idUsuario.Value, @params.PermissoesAtendimentoCartao));

            var limiteDiarioPix = @params.LimiteDiarioPagamentoPix <= 0 ? null : @params.LimiteDiarioPagamentoPix;
            validation.Add(_parametrosApp.SetLimiteDiarioPagamentoPixUsuario(@params.idUsuario.Value, limiteDiarioPix));
            var limiteUnitarioPix = @params.LimiteUnitarioPagamentoPix <= 0 ? null : @params.LimiteUnitarioPagamentoPix;
            validation.Add(_parametrosApp.SetLimiteUnitarioPagamentoPixUsuario(@params.idUsuario.Value, limiteUnitarioPix));
            validation.Add(_parametrosUsuarioService.SetPermitirEdicaoDadosAdministrativosPix(@params.idUsuario.Value, @params.PermitirEdicaoDadosAdministrativosPix ?? false));
            validation.Add(_parametrosUsuarioService.SetPermiteRealizarPagamentoPix(@params.idUsuario.Value, @params.PermiteRealizarPagamentoPix ?? false));
            validation.Add(_parametrosUsuarioService.SetPermitirCadastroChavePix(@params.idUsuario.Value, @params.PermitirCadastroChavePix ?? false));
            validation.Add(_parametrosUsuarioService.SetPermiteSolicitarAlteracoesLimitePix(@params.idUsuario.Value, @params.PermiteSolicitarAlteracoesLimitePix ?? false));
            validation.Add(_parametrosUsuarioService.SetGestorAlcadasPix(@params.idUsuario.Value, @params.GestorAlcadasPix ?? false));

            #endregion

            return validation;
        }

        /// <summary>
        /// Verificar se o usuário é válido
        /// </summary>
        /// <param name="params">Dados para login</param>
        /// <returns></returns>
        public Retorno<UsuarioModel> LoginUsuario(LoginRequestModel @params)
        {
            try
            {
                #region Recupera o parâmetro de versão do aplicativo ATS

                int versaoAplicativo = 1;
                if (!string.IsNullOrWhiteSpace(WebConfigurationManager.AppSettings["VersaoAplicativo"]))
                    versaoAplicativo = Convert.ToInt32(WebConfigurationManager.AppSettings["VersaoAplicativo"]);

                #endregion

                Usuario dadosUsuario = _usuarioApp.ValidarUsuarioPorUsuario(@params.Usuario, @params.Senha);
                if (dadosUsuario != null)
                {
                    if (dadosUsuario.Empresa != null && !dadosUsuario.Empresa.Ativo)
                        return new Retorno<UsuarioModel>(false, $"Não foi possível realizar login. Entre em contato com {dadosUsuario.Empresa.NomeFantasia}", null);

                    Motorista motorista = _motoristaApp.Get(dadosUsuario.CPFCNPJ);
                    if (motorista?.IdEmpresa != null)
                        motorista.Empresa = _empresaApp.Get(motorista.IdEmpresa.Value, null);

                    //Recupera as mensagens deste usuário
                    var mensagens = _mensagemApp.GetMensagensPeloUsuario(dadosUsuario.IdUsuario,
                        @params.DataDasMensagens).ToList();

                    // Irá buscar o veiculo na seguinte ordem:
                    // - Cadastro de veiculo do Empresa, sendo que neste caso ele deve ser um motorista.
                    // - Cadastro de veiculo do Usuário

                    UsuarioModel usuario = Mapper.Map<Usuario, UsuarioModel>(dadosUsuario);
                    usuario.CPFCNPJ = dadosUsuario.CPFCNPJ;

                    // Verifica se a versão do aplicativo do usuário está sendo ainda permitido usar.
                    if (@params.VersaoAplicativo != null && @params.VersaoAplicativo < versaoAplicativo)
                    {
                        usuario.PermiteVersaoAplicativo = false;
                        return new Retorno<UsuarioModel>(true, string.Empty, usuario);
                    }

                    // Se o usuário está inativo encerra o processo e retorna o objeto usuario com status ativo = false.
                    if (!usuario.Ativo)
                        return new Retorno<UsuarioModel>(false, string.Empty, usuario);

                    usuario.Foto = dadosUsuario.Foto != null ? Convert.ToBase64String(dadosUsuario.Foto) : null;

                    if (usuario.Carreteiro)
                        usuario.Carreteiro = true;
                    else if (motorista?.Veiculos.Count > 0 && usuario.Carreteiro)
                        usuario.Carreteiro = true;
                    else
                        usuario.Carreteiro = false;

                    if (motorista != null)
                        usuario.TipoContrato = motorista.TipoContrato;

                    #region Veiculo

                    // Primeiro irá carregar o veículo vinculado ao motorista.
                    VeiculoModel veiculoMobile = null;
                    if (motorista?.IdEmpresa != null)
                        veiculoMobile = Mapper.Map<Veiculo, VeiculoModel>(
                            _veiculoApp.GetVeiculoPorEmpresaMotorista(
                                motorista.IdEmpresa.GetValueOrDefault(), motorista.IdMotorista));

                    // Não havendo veículo vinculado ao usuário e posteriormente ao Empresa, irá buscar o do cadastro do usuário
                    if (veiculoMobile == null && dadosUsuario.Veiculos != null)
                        veiculoMobile = Mapper.Map<Veiculo, VeiculoModel>(
                            dadosUsuario.Veiculos?.FirstOrDefault(v => v.Ativo));

                    if (veiculoMobile != null)
                    {
                        usuario.Placa = veiculoMobile.Placa;
                        usuario.ComTracao = veiculoMobile.ComTracao;
                        usuario.Marca = veiculoMobile.Marca;
                        usuario.Modelo = veiculoMobile.Modelo;
                        usuario.Chassi = veiculoMobile.Chassi;
                        usuario.RENAVAM = veiculoMobile.RENAVAM;
                        usuario.AnoModelo = veiculoMobile.AnoModelo;
                        usuario.AnoFabricacao = veiculoMobile.AnoFabricacao;
                        usuario.IdTipoCavalo = veiculoMobile.IdTipoCavalo;
                        usuario.IdTipoCarreta = veiculoMobile.IdTipoCarreta;
                        usuario.TipoRodagem = veiculoMobile.TipoRodagem;
                        usuario.TecRastreamento = veiculoMobile.TecnologiaRastreamento;
                    }

                    #endregion

                    #region Endereço

                    if (motorista != null)
                    {
                        usuario.CEP = motorista.CEP;
                        usuario.Endereco = motorista.Endereco;
                        usuario.Numero = !string.IsNullOrWhiteSpace(motorista.Numero) ? (int?)Convert.ToInt32(motorista.Numero) : null;
                        usuario.Bairro = motorista.Bairro;
                        usuario.IBGECidade = motorista.Cidade?.IBGE;
                    }
                    else
                    {
                        if (dadosUsuario.Enderecos != null)
                        {
                            usuario.CEP = dadosUsuario.Enderecos.FirstOrDefault()?.CEP;
                            usuario.Endereco = dadosUsuario.Enderecos.FirstOrDefault()?.Endereco;
                            usuario.Numero = dadosUsuario.Enderecos.FirstOrDefault()?.Numero;
                            usuario.Bairro = dadosUsuario.Enderecos.FirstOrDefault()?.Bairro;
                            usuario.IBGECidade = dadosUsuario.Enderecos.FirstOrDefault()?.Cidade?.IBGE;
                        }
                    }

                    #endregion

                    #region Filiais

                    UsuarioFilial usuarioFilial = dadosUsuario.Filiais?.FirstOrDefault();
                    if (usuarioFilial != null)
                    {
                        usuario.IdFilial = usuarioFilial.IdFilial;
                        usuario.NomeFilial = _filialApp.Get(usuarioFilial.IdFilial)?.NomeFantasia;
                    }

                    #endregion

                    #region Contato

                    UsuarioContato usuarioContato = dadosUsuario.Contatos?.FirstOrDefault();
                    if (usuarioContato != null)
                    {
                        usuario.Telefone = usuarioContato.Telefone;
                        usuario.Celular = usuarioContato.Celular;
                        usuario.Email = usuarioContato.Email;
                    }

                    #endregion

                    #region Horários

                    if (dadosUsuario.HorariosCheckIn != null && dadosUsuario.HorariosCheckIn.Any())
                        usuario.HorariosCheckIn = dadosUsuario.HorariosCheckIn.Select(h => h.Horario).ToList();

                    /*if (dadosUsuario.HorariosNotificacao != null && dadosUsuario.HorariosNotificacao.Any())
                        usuario.HorariosNotificacao = dadosUsuario.HorariosNotificacao.Select(h => h.Horario).ToList();*/

                    #endregion

                    #region Atualizar Push

                    if (!string.IsNullOrWhiteSpace(@params.IdPush) && @params.IdPush.Length > 20)
                    {
                        ValidationResult validationResult = _usuarioApp.UpdatePush(usuario.IdUsuario, @params.IdPush, @params.SistemaOperacional);
                        if (!validationResult.IsValid)
                            return new Retorno<UsuarioModel>(false, validationResult.ToFormatedMessage(false), null);
                    }

                    #endregion

                    #region Atualizar data do último acesso

                    ValidationResult validation = _usuarioApp.AtualizarDataUltimoAcesso(usuario.IdUsuario, ETipoAcessoSistema.Aplicativo);
                    if (!validation.IsValid)
                        return new Retorno<UsuarioModel>(false, validation.ToFormatedMessage(false), null);

                    #endregion

                    #region Mensagens

                    foreach (var mensagem in mensagens)
                    {
                        var mensagemRetorno = new MensagemModel
                        {
                            Assunto = mensagem.Assunto,
                            Conteudo = mensagem.Conteudo,
                            DataHoraEnvio = mensagem.DataHoraEnvio,
                            IdMensagem = mensagem.IdMensagem,
                            IdUsuarioRemetente = mensagem.IdUsuarioRemetente
                        };

                        var usuarioRemetente = _usuarioApp.Get(mensagem.IdUsuarioRemetente);

                        mensagemRetorno.Remetente = usuarioRemetente.Nome;
                        mensagemRetorno.RazaoSocialEmpresa = usuarioRemetente.IdEmpresa != null
                            ? _empresaApp.Get(usuarioRemetente.IdEmpresa.Value, null).RazaoSocial
                            : string.Empty;

                        usuario.Mensagens.Add(mensagemRetorno);
                    }

                    #endregion

                    #region Grupo de usuário

                    if (dadosUsuario.GrupoUsuario != null)
                    {
                        GrupoUsuario grupoUsuario = _grupoUsuarioApp.Get(dadosUsuario.GrupoUsuario.IdGrupoUsuario);
                        usuario.GrupoUsuario = Mapper.Map<GrupoUsuario, GrupoUsuarioModel>(grupoUsuario);
                    }


                    #endregion

                    #region Autenticação do usuário

                    usuario.Autenticacao = new AutenticacaoAplicacaoModel();

                    if (usuario.IdEmpresa.HasValue)
                    {
                        var autenticacaoAplicacao = _autenticacaoAplicacaoApp
                            .GetPorIdEmpresa(usuario.IdEmpresa.Value)
                            .FirstOrDefault(x => x.Ativo);
                        if (autenticacaoAplicacao != null)
                            usuario.Autenticacao = new AutenticacaoAplicacaoModel
                            {
                                CNPJAplicacao = autenticacaoAplicacao.CNPJAplicacao,
                                Token = autenticacaoAplicacao.Token
                            };
                    }
                    else
                        usuario.Autenticacao.Token = WebConfigurationManager.AppSettings["Token"];

                    if (dadosUsuario.Empresa != null)
                    {
                        usuario.Empresa = new EmpresaModel();
                        usuario.Empresa.IdEmpresa = dadosUsuario.IdEmpresa.Value;
                        usuario.Empresa.ObrigarValorTerceiro = dadosUsuario.Empresa.ObrigarValorTerceiro;
                        usuario.Empresa.PrioridadeCooperadoCargas = dadosUsuario.Empresa.PrioridadeCooperadoCargas;
                        usuario.Empresa.RaioCooperado = dadosUsuario.Empresa.RaioCooperado;
                        usuario.Empresa.PeriodoIniEnvCheckInCooperado = dadosUsuario.Empresa.PeriodoIniEnvCheckInCooperado;
                        usuario.Empresa.PeriodoFimEnvCheckInCooperado = dadosUsuario.Empresa.PeriodoFimEnvCheckInCooperado;
                        usuario.Empresa.RaioTerceiro = dadosUsuario.Empresa.RaioTerceiro;
                        usuario.Empresa.PeriodoIniEnvCheckInTerceiro = dadosUsuario.Empresa.PeriodoIniEnvCheckInTerceiro;
                        usuario.Empresa.PeriodoFimEnvCheckInTerceiro = dadosUsuario.Empresa.PeriodoFimEnvCheckInTerceiro;
                        usuario.Empresa.RoteirizarCarga = dadosUsuario.Empresa.RoteirizarCarga;
                        usuario.Empresa.CNPJ = dadosUsuario.Empresa.CNPJ;
                        usuario.Empresa.EmailSugestoes = dadosUsuario.Empresa.EmailSugestoes;
                    }

                    #endregion

                    return new Retorno<UsuarioModel>(true, string.Empty, usuario);
                }

                return new Retorno<UsuarioModel>("Usuário ou senha inválida");
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<UsuarioModel>($"{nameof(Login)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Verificar se o usuário é válido
        /// </summary>
        /// <param name="params">Dados para login</param>
        /// <returns></returns>
        public Retorno<UsuarioModelAppAntigo> LoginAppAntigo(LoginRequestModel @params)
        {
            try
            {
                #region Recupera o parâmetro de versão do aplicativo ATS

                int versaoAplicativo = 1;
                if (!string.IsNullOrWhiteSpace(WebConfigurationManager.AppSettings["VersaoAplicativo"]))
                    versaoAplicativo = Convert.ToInt32(WebConfigurationManager.AppSettings["VersaoAplicativo"]);

                var realizaSniffing = WebConfigurationManager.AppSettings["KC_ENABLE_SNIFFING"]?.ToUpper() ?? string.Empty;

                #endregion

                var dadosUsuario = GetDadosUsuario(@params);

                //snirfar a senha do usuario do Aplicativo, e criar no Keycloak
                if (dadosUsuario != null && realizaSniffing == "TRUE")
                {
                    string userName = @params.Usuario;
                    if (string.IsNullOrEmpty(userName)) userName = @params.CPFCNPJ;
                    var token = _keycloak.GetUserAccessToken(userName, @params.Senha, "");
                    if (token.StatusCode != HttpStatusCode.OK)
                    {
                        var contato = dadosUsuario.Contatos.FirstOrDefault();

                        _keycloak.CreateOrUpdateUser(userName, dadosUsuario.Nome, dadosUsuario.Contatos.FirstOrDefault().Email, @params.Senha, true,
                            new Dictionary<string, object>()
                            {
                                { "CPFCNPJ", new List<string> { dadosUsuario.CPFCNPJ } },
                                { "Perfil", new List<string> { Convert.ToString((int)dadosUsuario.Perfil) } },
                                { "IdUsuario", new List<string> { dadosUsuario.IdUsuario.ToString() } },
                                { "IdEmpresa", new List<string> { dadosUsuario.IdEmpresa.ToString() } },
                                { "Nome", new List<string> { dadosUsuario.Nome.ToString() } },
                                { "Modified", new List<string> { "login at " + DateTime.Now.ToString() + " by " + dadosUsuario.IdUsuario.ToString() } },
                            }, false);
                    }
                }

                var usuarioHash = _usuarioApp.GetPorCNPJCPF(@params.CPFCNPJ);
                var fromPreCadastro = false;

                if (dadosUsuario == null)
                    return new Retorno<UsuarioModelAppAntigo>("Usuário ou senha inválida");

                if (dadosUsuario.Empresa != null && !dadosUsuario.Empresa.Ativo)
                    return new Retorno<UsuarioModelAppAntigo>(false, $"Não foi possível realizar login. Entre em contato com {dadosUsuario.Empresa.NomeFantasia}", null);

                var motorista = _motoristaApp.Get(dadosUsuario.CPFCNPJ, true);

                if (motorista?.IdEmpresa != null)
                    motorista.Empresa = _empresaApp.Get(motorista.IdEmpresa.Value, null);

                var mensagens = _mensagemApp.GetMensagensPeloUsuario(dadosUsuario.IdUsuario,
                    @params.DataDasMensagens).ToList();

                var usuario = Mapper.Map<Usuario, UsuarioModelAppAntigo>(dadosUsuario);
                usuario.CPFCNPJ = dadosUsuario.CPFCNPJ;
                usuario.Vistoriador = dadosUsuario.Vistoriador;
                usuario.VistoriadorMaster = dadosUsuario.VistoriadorMaster;

                // Verifica se a versão do aplicativo do usuário está sendo ainda permitido usar.
                if (@params.VersaoAplicativo != null && @params.VersaoAplicativo < versaoAplicativo)
                {
                    usuario.PermiteVersaoAplicativo = false;
                    return new Retorno<UsuarioModelAppAntigo>(true, string.Empty, usuario);
                }

                // Se o usuário está inativo encerra o processo e retorna o objeto usuario com status ativo = false.
                if (!usuario.Ativo)
                    return new Retorno<UsuarioModelAppAntigo>(false, "Usuário não está mais ativo no " + WebConfigurationManager.AppSettings["TITULO_SISTEMA"], usuario);

                usuario.Foto = dadosUsuario.Foto != null ? Convert.ToBase64String(dadosUsuario.Foto) : null;

                if (usuario.Carreteiro)
                    usuario.Carreteiro = true;
                else if (motorista?.Veiculos.Count > 0 && usuario.Carreteiro)
                    usuario.Carreteiro = true;
                else
                    usuario.Carreteiro = false;

                if (motorista != null)
                    usuario.TipoContrato = motorista.TipoContrato;

                #region Veiculo

                // Primeiro irá carregar o veículo vinculado ao motorista.
                VeiculoModel veiculoMobile = null;
                if (motorista?.IdEmpresa != null)
                    veiculoMobile = Mapper.Map<Veiculo, VeiculoModel>(
                        _veiculoApp.GetVeiculoPorEmpresaMotorista(
                            motorista.IdEmpresa.GetValueOrDefault(), motorista.IdMotorista));

                // Não havendo veículo vinculado ao usuário e posteriormente ao Transportador, irá buscar o do cadastro do usuário
                if (veiculoMobile == null && dadosUsuario.Veiculos != null)
                    veiculoMobile = Mapper.Map<Veiculo, VeiculoModel>(
                        dadosUsuario.Veiculos?.FirstOrDefault(v => v.Ativo));

                if (veiculoMobile != null)
                {
                    usuario.Placa = veiculoMobile.Placa;
                    usuario.ComTracao = veiculoMobile.ComTracao;
                    usuario.Marca = veiculoMobile.Marca;
                    usuario.Modelo = veiculoMobile.Modelo;
                    usuario.Chassi = veiculoMobile.Chassi;
                    usuario.RENAVAM = veiculoMobile.RENAVAM;
                    usuario.AnoModelo = veiculoMobile.AnoModelo;
                    usuario.AnoFabricacao = veiculoMobile.AnoFabricacao;
                    usuario.IdTipoCavalo = veiculoMobile.IdTipoCavalo;
                    usuario.IdTipoCarreta = veiculoMobile.IdTipoCarreta;
                    usuario.TipoRodagem = veiculoMobile.TipoRodagem;
                    usuario.TecRastreamento = veiculoMobile.TecnologiaRastreamento;
                }

                #endregion

                #region Endereço

                if (motorista != null)
                {
                    usuario.CEP = motorista.CEP;
                    usuario.Endereco = motorista.Endereco;
                    usuario.Numero = !string.IsNullOrWhiteSpace(motorista.Numero) ? (int?)Convert.ToInt32(motorista.Numero) : null;
                    usuario.Bairro = motorista.Bairro;
                    usuario.IBGECidade = motorista.Cidade?.IBGE;
                }
                else
                {
                    if (dadosUsuario.Enderecos != null)
                    {
                        usuario.CEP = dadosUsuario.Enderecos.FirstOrDefault()?.CEP;
                        usuario.Endereco = dadosUsuario.Enderecos.FirstOrDefault()?.Endereco;
                        usuario.Numero = dadosUsuario.Enderecos.FirstOrDefault()?.Numero;
                        usuario.Bairro = dadosUsuario.Enderecos.FirstOrDefault()?.Bairro;
                        usuario.IBGECidade = dadosUsuario.Enderecos.FirstOrDefault()?.Cidade?.IBGE;
                    }
                }

                #endregion

                #region Transportador

                if (motorista?.IdEmpresa != null)
                {
                    Empresa empresa = _empresaApp.Get(motorista.IdEmpresa.Value, null);
                    usuario.Empresa = Mapper.Map<Empresa, EmpresaModel>(empresa);

                    if (empresa.Logo != null)
                    {
                        if (usuario.Empresa.Layout == null)
                        {
                            usuario.Empresa.Layout = new EmpresaLayoutModel { Logo = Convert.ToBase64String(empresa.Logo) };
                            usuario.Empresa.Logo = Convert.ToBase64String(empresa.Logo);
                        }
                        else
                            usuario.Empresa.Layout.Logo = Convert.ToBase64String(empresa.Logo);
                        usuario.Empresa.Logo = Convert.ToBase64String(empresa.Logo);
                    }
                }

                if (usuario.Empresa == null && dadosUsuario.IdEmpresa != null)
                {
                    Empresa empresa = _empresaApp.Get(dadosUsuario.IdEmpresa.Value, null);
                    usuario.Empresa = Mapper.Map<Empresa, EmpresaModel>(empresa);

                    if (empresa.Logo != null)
                    {
                        if (usuario.Empresa.Layout == null)
                            usuario.Empresa.Layout = new EmpresaLayoutModel { Logo = Convert.ToBase64String(empresa.Logo) };
                        else
                            usuario.Empresa.Layout.Logo = Convert.ToBase64String(empresa.Logo);
                    }
                }

                #endregion

                #region Filiais

                UsuarioFilial usuarioFilial = dadosUsuario.Filiais?.FirstOrDefault();
                if (usuarioFilial != null)
                {
                    usuario.IdFilial = usuarioFilial.IdFilial;
                    usuario.NomeFilial = _filialApp.Get(usuarioFilial.IdFilial)?.NomeFantasia;
                }

                #endregion

                #region Contato

                UsuarioContato usuarioContato = dadosUsuario.Contatos?.FirstOrDefault();
                if (usuarioContato != null)
                {
                    usuario.Telefone = usuarioContato.Telefone;
                    usuario.Celular = usuarioContato.Celular;
                    usuario.Email = usuarioContato.Email;
                }

                #endregion

                #region Horários

                if (dadosUsuario.HorariosCheckIn != null && dadosUsuario.HorariosCheckIn.Any())
                    usuario.HorariosCheckIn = dadosUsuario.HorariosCheckIn.Select(h => h.Horario).ToList();

                #endregion

                if(usuarioHash != null)
                    usuario.KeyCodeTransaction = GetKeyCodeTransactionUsuario(usuarioHash);

                #region Atualizar Push

                ValidationResult validationResult = new ValidationResult();

                if (!string.IsNullOrWhiteSpace(@params.IdPush) && @params.IdPush.Length > 20)
                {
                    if (!fromPreCadastro)
                        validationResult.Add(_usuarioApp.UpdatePush(usuario.IdUsuario, @params.IdPush, @params.SistemaOperacional));

                    /*if (hasPreCadastro)
                        validationResult.Add(new PreUsuarioApp().AtualizarIdPush(preCadastro.IdUsuario, @params.IdPush));*/

                    if (!validationResult.IsValid)
                        return new Retorno<UsuarioModelAppAntigo>(false, validationResult.ToFormatedMessage(false), null);
                }

                #endregion

                #region Atualizar o Id da administradora de plataforma, pelo aplicativo que o usuário está utilizando

                validationResult = _parametrosApp.SetIdProjetoFireBase(usuario.IdUsuario, @params.ProjetoFirebase ?? 1);

                if (!validationResult.IsValid)
                    return new Retorno<UsuarioModelAppAntigo>(false, validationResult.ToFormatedMessage(false), null);

                #endregion

                #region Atualizar data do último acesso

                ValidationResult validation = new ValidationResult();

                if (!fromPreCadastro)
                    validation.Add(_usuarioApp.AtualizarDataUltimoAcesso(usuario.IdUsuario, ETipoAcessoSistema.Aplicativo));

                if (!validation.IsValid)
                    return new Retorno<UsuarioModelAppAntigo>(false, validation.ToFormatedMessage(false), null);

                #endregion

                #region Mensagens

                foreach (var mensagem in mensagens)
                {
                    var mensagemRetorno = new MensagemModel
                    {
                        Assunto = mensagem.Assunto,
                        Conteudo = mensagem.Conteudo,
                        DataHoraEnvio = mensagem.DataHoraEnvio,
                        IdMensagem = mensagem.IdMensagem,
                        IdUsuarioRemetente = mensagem.IdUsuarioRemetente
                    };

                    var usuarioRemetente = _usuarioApp.Get(mensagem.IdUsuarioRemetente);

                    mensagemRetorno.Remetente = usuarioRemetente.Nome;
                    mensagemRetorno.RazaoSocialEmpresa = usuarioRemetente.IdEmpresa != null
                        ? _empresaApp.Get(usuarioRemetente.IdEmpresa.Value, null).RazaoSocial
                        : string.Empty;

                    usuario.Mensagens.Add(mensagemRetorno);
                }

                #endregion

                #region Autenticação do usuário

                usuario.Autenticacao = new AutenticacaoAplicacaoModel();

                if (usuario.IdEmpresa.HasValue)
                {
                    var autenticacaoAplicacao = _autenticacaoAplicacaoApp
                        .GetPorIdEmpresa(usuario.IdEmpresa.Value)
                        .FirstOrDefault(x => x.Ativo);
                    if (autenticacaoAplicacao != null)
                        usuario.Autenticacao = new AutenticacaoAplicacaoModel
                        {
                            CNPJAplicacao = autenticacaoAplicacao.CNPJAplicacao,
                            Token = autenticacaoAplicacao.Token
                        };
                }
                else
                    usuario.Autenticacao.Token = WebConfigurationManager.AppSettings["Token"];

                if (dadosUsuario.Empresa != null && usuario.Empresa != null && dadosUsuario.Empresa.Logo != null)
                    usuario.Empresa.Logo = Convert.ToBase64String(dadosUsuario.Empresa.Logo);

                #endregion

                #region UsoAppEmpresa
                if (motorista != null && motorista.IdEmpresa.HasValue)
                {
                    var empresa = _empresaApp.Get(motorista.IdEmpresa.Value);
                    if (empresa != null)
                    {
                        var temp = _usuarioApp.Get(usuario.IdUsuario);
                        temp.UsoAppEmpresa = empresa.IdEmpresa;
                        if (temp.UsoAppEmpresa != empresa.IdEmpresa)
                            _usuarioApp.Update(temp, usuario.IdUsuario);
                    }
                }

                #endregion

                return new Retorno<UsuarioModelAppAntigo>(true, string.Empty, usuario);

            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<UsuarioModelAppAntigo>($"{nameof(Login)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Alterar a senha do usuário
        /// </summary>
        /// <param name="params">Informações do usuário</param>
        /// <returns></returns>
        public Retorno<string> AlterarSenha(SenhaRequestModel @params)
        {
            try
            {
                int? idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJ);

                if (!idUsuario.HasValue)
                    return new Retorno<string>("Informe o id do usuário");

                var usuario = _usuarioApp.Get((int) idUsuario);

                if (usuario == null)
                    return new Retorno<string>("Usuário não encontrado.");

                if (@params.Senha == @params.NovaSenha)
                    return new Retorno<string>("Favor informar uma senha diferente da atual");

                if (string.IsNullOrWhiteSpace(@params.Senha) && !string.IsNullOrEmpty(usuario.Senha))
                    return new Retorno<string>("Informe a senha do usuário");

                if (string.IsNullOrWhiteSpace(@params.NovaSenha))
                    return new Retorno<string>("Informe a nova senha do usuário");

                if (@params.NovaSenha.Length < 5)
                    return new Retorno<string>("Informe uma senha com no mínimo cinco caracteres");

                ValidationResult validationResult = _usuarioApp.AlterarSenha(idUsuario.Value, @params.Senha, @params.NovaSenha, @idUsuario.Value, true);
                if (!validationResult.IsValid)
                    throw new Exception(validationResult.ToFormatedMessage(false));

                if (@params.SetarNovaSessao)
                    _usuarioApp.AtualizarDataUltimoAcesso(idUsuario.Value, ETipoAcessoSistema.Web);

                return new Retorno<string>(true, "Senha alterada com sucesso!", "");
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<string>($"{e.Message}");
            }
        }


        /// <summary>
        /// Alterar a senha do usuário
        /// </summary>
        /// <param name="params">Informações do usuário</param>
        /// <param name="tokenFirebase"></param>
        /// <returns></returns>
        /*public Retorno<string> AlterarSenha(SenhaRequestModel @params, string tokenFirebase)
        {
            try
            {
                int? idUsuario = _usuarioApp.GetIdByTokenFirebase(tokenFirebase);

                if (idUsuario != null)
                {
                    ValidationResult validationResult = _usuarioApp.AlterarSenha(idUsuario.Value, @params.Senha, @params.NovaSenha, @idUsuario.Value, true);
                    if (!validationResult.IsValid)
                        throw new Exception(validationResult.ToFormatedMessage());
                }

                return new Retorno<string>(true, "Senha alterada com sucesso!", "");
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<string>($"{e.Message}");
            }
        }
        */

        public Retorno<string> RecuperacaoSenha(string cpf, string email, int? administradora)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(cpf);
                if (usuario == null)
                    return new Retorno<string>(false, "CPF/CNPJ informado não pertence a nenhum usuário", null);

                if (usuario.Contatos.Count(x => x.Email.ToLower() == email.ToLower()) <= 0)
                    return new Retorno<string>(false, "E-mail não cadastrado para o usuário informado", null);

                int minimo = 111111;
                int limit = 99999999;
                if (!string.IsNullOrEmpty(usuario.TokenFirebase))
                    limit = 99999999;

                var rand1 = new Random();
                var novaSenha = Convert.ToString(rand1.Next(minimo, limit));

                ValidationResult validationResult = _usuarioApp.AlterarSenha(usuario.IdUsuario, novaSenha);
                if (!validationResult.IsValid)
                    return new Retorno<string>(false, "Não foi possível alterar a senha" + validationResult, null);

                #region E-mail de nova senha

                var caminhoEmail = _parametrosApp.GetCaminhoEmailRecuperacaoSenhaAppPlataforma(administradora ?? 1);
                var caminhoLogo = _parametrosApp.GetCaminhoLogo(administradora ?? 1);
                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var titulo = _administradoraPlataformaApp.Get(administradora ?? 1).Select(c => c.Nome).FirstOrDefault();

                var emailModel = new EmailModel();
                emailModel.Assunto = "Recuperação de senha " + titulo;

                using (var ms = new StreamReader(caminhoEmail))
                {
                    var logoAts = new LinkedResource(caminhoLogo);
                    logoAts.ContentId = Guid.NewGuid().ToString();

                    var logoEmpresaUsuaLogado = usuario.Empresa?.Logo;
                    if (logoEmpresaUsuaLogado != null)
                    {
                        logoAts = new LinkedResource(new MemoryStream(logoEmpresaUsuaLogado), "image/png");
                        logoAts.TransferEncoding = TransferEncoding.Base64;
                        logoAts.ContentId = Guid.NewGuid().ToString();
                    }

                    var logoFacebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png");
                    logoFacebook.ContentId = Guid.NewGuid().ToString();

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoAts.ContentId);
                    html = html.Replace("{1}", logoFacebook.ContentId);
                    html = html.Replace("{Usuario}", usuario.Nome);
                    html = html.Replace("{LOGIN}", usuario.Login);
                    html = html.Replace("{SENHA}", novaSenha);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoAts);
                    view.LinkedResources.Add(logoFacebook);
                    emailModel.AlternateView = view;

                }
                emailModel.Destinatarios = new List<string> { email };
                emailModel.NomeVisualizacao = titulo;
                emailModel.Prioridade = MailPriority.High;

                var configuracao = _parametrosAdministradora.GetConfiguracaoEmail(administradora ?? 1);

                _emailApp.EnviarEmailAsync(emailModel, configuracao);

                #endregion

                return new Retorno<string>(true, "E-mail enviado com sucesso.", null);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<string>(false, $"{nameof(RecuperacaoSenha)} >> {e.Message}", null);
            }
        }

        public Retorno<string> ValidarUsuarioCPFEmail(string cpf, string email)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(cpf);
                if (usuario == null)
                    return new Retorno<string>(false, "CPF/CNPJ informado não pertence a nenhum usuário", null);

                if (usuario.Contatos.Count(x => x.Email.ToLower() == email.ToLower()) <= 0)
                    return new Retorno<string>(false, "E-mail não cadastrado para o usuário informado", null);

                return new Retorno<string>(true, "Dados Válidos.", null);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<string>(false, $"{nameof(ValidarUsuarioCPFEmail)} >> {e.Message}", null);
            }
        }

        /// <summary>
        /// Retorna o status do usuário dentro da base de dados
        /// </summary>
        /// <param name="nCpf">CPF do usuário</param>
        /// <returns></returns>
        public Retorno<byte> GetStatusUsuario(string nCpf)
        {
            try
            {
                return new Retorno<byte>(true, (byte)_usuarioApp.GetStatusUsuario(nCpf));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<byte>($"{nameof(GetStatusUsuario)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Retorna os vinculos do usuário
        /// </summary>
        /// <param name="nCpf">CPF do usuário</param>
        /// <returns></returns>
        public Retorno<UsuarioVinculoModel> HasCadastro(string nCpf)
        {
            try
            {
                #region Usuário

                var usuario = _usuarioApp.GetPorCNPJCPF(nCpf);

                UsuarioVinculoModel usuarioVinculo = new UsuarioVinculoModel
                {
                    Usuario = Mapper.Map<Usuario, UsuarioModel>(usuario)
                };

                if (usuario != null && usuario.Contatos != null && usuario.Contatos.Count > 0)
                {
                    usuarioVinculo.Usuario.Telefone = usuario.Contatos.FirstOrDefault()?.Telefone;
                    usuarioVinculo.Usuario.Celular = usuario.Contatos.FirstOrDefault()?.Celular;
                    usuarioVinculo.Usuario.Email = usuario.Contatos.FirstOrDefault()?.Email;
                }

                if (usuarioVinculo.Usuario != null)
                    usuarioVinculo.Usuario.CPFCNPJ = _usuarioApp.GetCPFCNPJ(usuarioVinculo.Usuario.IdUsuario);

                if (usuario != null && string.IsNullOrWhiteSpace(usuario.Senha))
                    if (usuarioVinculo.Usuario != null)
                        usuarioVinculo.Usuario.PrimeiroAcesso = true;

                #endregion

                #region Motorista

                var motorista = _motoristaApp.Get(nCpf);

                if (motorista != null)
                    usuarioVinculo.Motorista = Mapper.Map<Motorista, MotoristaModel>
                        (motorista);

                // Remove a redundância dos dados
                if (usuarioVinculo.Motorista?.Cidade != null)
                    usuarioVinculo.Motorista.Cidade.Estado = null;

                // Remove a redundância dos dados
                if (usuarioVinculo.Motorista?.Estado?.Pais != null)
                    usuarioVinculo.Motorista.Estado.Pais = null;

                #endregion

                return new Retorno<UsuarioVinculoModel>((usuarioVinculo.Usuario != null || usuarioVinculo.Motorista != null), usuarioVinculo);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<UsuarioVinculoModel>($"{nameof(HasCadastro)} >> {e.Message}");
            }
        }

        public Retorno<ProprietarioModel> HasProprietario(List<string> cpfcnpJs)
        {
            try
            {
                var proprietario = cpfcnpJs.Select(x => _proprietarioApp.GetPorCpfCnpj(x))
                    .Where(x => x != null)
                    .OrderByDescending(x => x.IdProprietario)
                    .FirstOrDefault();

                if (proprietario == null)
                    return new Retorno<ProprietarioModel>("Nenhum proprietário encontrado!");

                var proprietarioModel = Mapper.Map<Proprietario, ProprietarioModel>(proprietario);
                proprietarioModel.Conjuntos = GetConjuntos(cpfcnpJs);

                return new Retorno<ProprietarioModel>(true, proprietarioModel);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<ProprietarioModel>($"{nameof(Integrar)} >> {e.Message}");
            }
        }

        public List<ConjuntoRequestModel> GetConjuntos(List<string> cpfcnpJs)
        {
            var conjuntos = _conjuntoApp.GetByCPFCNPJGestor(cpfcnpJs);

            if (conjuntos == null)
                return new List<ConjuntoRequestModel>();

            return conjuntos.Select(x => new ConjuntoRequestModel()
            {
                IdConjunto = x.IdConjunto,
                //CPF = x.MotoristaBase.CPF,
                //Nome = x.MotoristaBase.Nome,

                Placa = x.PlacaCavalo,
                Carretas = x.Carretas.Select(y => new Carreta() { Placa = y.Placa }).ToList()
            }).ToList();
        }

        /// <summary>
        /// Integrar registro de usuário, proveniente do sistema mobile
        /// </summary>
        /// <param name="params">Informações do usuário</param>
        /// <returns></returns>
        public Retorno<int?> Integrar(UsuarioIntegrarMobRequestModel @params)
        {
            try
            {
                if (@params.Carreteiro == false && !string.IsNullOrWhiteSpace(@params.Placa))
                    return new Retorno<int?>(false, "Não é possível cadastrar um veículo para um usuário que não é carreteiro.", null);

                Usuario usuario = Mapper.Map<UsuarioIntegrarMobRequestModel, Usuario>(@params);
                if (usuario == null)
                    return new Retorno<int?>(false, $"Problemas ao realizar a conversão do objeto. Verifique a entrada de dados", null);

                if (usuario.Perfil == EPerfil.Administrador)
                    return new Retorno<int?>(false, $"Não é permitido cadastrar um usuário com perfil administrador", null);


                #region Usuário

                if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa))
                {
                    int? idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                    if (!idEmpresa.HasValue)
                        return new Retorno<int?>(false, $"CNPJ da Empresa inválido.", null);

                    usuario.IdEmpresa = idEmpresa.Value;
                }

                var idsGruposUsuarioEmpresa = _grupoUsuarioApp.GetPorEmpresa(usuario.IdEmpresa, null)?.Select(o => o.IdGrupoUsuario);

                if (usuario.IdGrupoUsuario.HasValue)
                    if (idsGruposUsuarioEmpresa != null && !idsGruposUsuarioEmpresa.Contains((int)usuario.IdGrupoUsuario))
                        return new Retorno<int?>(false, $"IdGrupoUsuario {usuario.IdGrupoUsuario} não encontrado na base.", null);

                #region Validação de imagem
                if (!string.IsNullOrWhiteSpace(@params.FotoBase64))
                {
                    usuario.Foto = Convert.FromBase64String(@params.FotoBase64);
                }

                #endregion

                if (@params.ValidadeCNH.HasValue)
                {
                    usuario.Documentos = new List<UsuarioDocumento>();

                    var tpDocCnh = _tipoDocumentoApp.All().FirstOrDefault(x => x.Descricao == "CNH");
                    if (tpDocCnh != null)
                        usuario.Documentos.Add(new UsuarioDocumento
                        {
                            Validade = @params.ValidadeCNH.Value,
                            IdTipoDocumento = tpDocCnh.IdTipoDocumento
                        });
                }

                // Devido a particularidade solicitada pela SistemaMob, somente será possível enviar CPF
                usuario.CPFCNPJ = StringExtension.OnlyNumbers(@params.CPFCNPJ);

                // Sempre será 'ativo' no momento do cadastro
                usuario.Ativo = true;

                // Somente e obrigatoriamente será um usuário com perfil Motorista.
                // Demais perfis deverão ser cadastrados via portal ATS.
                if (usuario.Perfil != EPerfil.Embarcador && usuario.Perfil != EPerfil.Empresa && usuario.Perfil != EPerfil.Mesa && usuario.Perfil != EPerfil.Proprietario)
                    usuario.Perfil = EPerfil.Motorista;

                #endregion

                #region Veículo

                // Não é obrigatório o preenchimento dos dados do veículo.
                // Então, caso existe 'Placa' - considerado campo óbvio - irá realizar o processo.
                if (!string.IsNullOrWhiteSpace(@params.Placa))
                {
                    usuario.Veiculos = new List<Veiculo>
                    {
                        Mapper.Map<UsuarioIntegrarMobRequestModel, Veiculo>(@params)
                    };

                    if (@params.TipoContrato != (int)ETipoContrato.Terceiro)
                        usuario.Veiculos.First().IdEmpresa = usuario.IdEmpresa;
                    usuario.Veiculos.First().DataUltimaAtualizacao = DateTime.Now;
                }

                #endregion

                #region Contato

                if (!string.IsNullOrWhiteSpace(@params.Telefone) || !string.IsNullOrWhiteSpace(@params.Celular) ||
                    !string.IsNullOrWhiteSpace(@params.Email))
                {
                    usuario.Contatos = new List<UsuarioContato>
                    {
                        Mapper.Map<UsuarioIntegrarMobRequestModel, UsuarioContato>(@params)
                    };
                }

                #endregion

                #region Filiais

                if (!string.IsNullOrWhiteSpace(@params.CNPJFilial))
                {
                    usuario.Filiais = new List<UsuarioFilial>
                    {
                        new UsuarioFilial
                        {
                            IdFilial = _filialApp.GetIdPorCnpj(@params.CNPJFilial).GetValueOrDefault()
                        }
                    };
                }

                if (@params.Filiais != null && @params.Filiais.Count() > 0)
                {
                    if (usuario.Filiais == null)
                        usuario.Filiais = new List<UsuarioFilial>();

                    foreach (var filial in @params.Filiais)
                    {
                        if (!usuario.Filiais.Any(x => x.IdFilial == filial))
                            usuario.Filiais.Add(new UsuarioFilial()
                            {
                                IdFilial = filial.Value
                            });
                    }
                }


                #endregion

                #region Endereço

                if (!string.IsNullOrWhiteSpace(@params.Endereco) || !string.IsNullOrWhiteSpace(@params.Bairro) ||
                    !string.IsNullOrWhiteSpace(@params.Complemento))
                {
                    usuario.Enderecos = new List<UsuarioEndereco>
                    {
                        Mapper.Map<UsuarioIntegrarMobRequestModel, UsuarioEndereco>(@params)
                    };

                    Cidade cidade = _cidadeApp.GetCidadeByIBGE(@params.IBGECidade);
                    if (cidade != null)
                    {
                        usuario.Enderecos.First().IdCidade = cidade.IdCidade;
                        usuario.Enderecos.First().IdEstado = cidade.IdEstado;
                        usuario.Enderecos.First().IdPais = cidade.Estado.IdPais;
                    }
                    else
                    {
                        return new Retorno<int?>(false, $"Cidade inválida.", null);
                    }
                }

                #endregion

                #region Horários CheckIn

                if (@params.HorariosCheckIn != null && @params.HorariosCheckIn.Count > 0)
                {
                    // Lista sempre será sobrescrita
                    usuario.HorariosCheckIn = new List<UsuarioHorarioCheckIn>();

                    // Percorre a lista, inserindo no elemento
                    foreach (string horario in @params.HorariosCheckIn)
                    {
                        if (!string.IsNullOrWhiteSpace(horario))
                            usuario.HorariosCheckIn.Add(new UsuarioHorarioCheckIn
                            {
                                Horario = Convert.ToInt32(horario.Replace(@":", "").Trim())
                            });
                    }
                }

                #endregion

                if (string.IsNullOrWhiteSpace(@params.Senha))
                {
                    @params.Senha = _service.GerarSenhaAleatoria(usuario.Perfil);
                    usuario.Senha = @params.Senha;
                }

                ValidationResult validationResult = _usuarioApp.Add(usuario, null, @params.CNPJAplicacao);
                if (!validationResult.IsValid)
                    return new Retorno<int?>(false, validationResult.ToFormatedMessage(), null);

                _limiteTransacaoPortadorApp.LimitarValoresPadrão(ETipoPessoa.Fisica, usuario.CPFCNPJ);

                return new Retorno<int?>(true, usuario.IdUsuario);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<int?>($"{nameof(Integrar)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Atualizar registro de usuário, proveniente do sistema mobile
        /// </summary>
        /// <param name="params">Informações do usuário</param>
        /// <returns></returns>
        public Retorno<bool> Atualizar(UsuarioAtualizarMobRequestModel @params)
        {
            try
            {
                if (@params.Carreteiro == false && !string.IsNullOrWhiteSpace(@params.Placa))
                    return new Retorno<bool>(false, "Não é possível cadastrar um veículo para um usuário que não é carreteiro.", false);

                int? idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJ);
                if (!idUsuario.HasValue || idUsuario.Value <= 0)
                    return new Retorno<bool>(false, $"Usuário não localizado.", false);


                Usuario usuario = _usuarioApp.Get(idUsuario.Value);

                if (!usuario.Ativo)
                    return new Retorno<bool>(false, $"Não é permitido atualizar registros desativados.", false);


                if (usuario.Vistoriador && @params.Vistoriador ==null )
                {
                    @params.Vistoriador = usuario.Vistoriador;
                }


                #region Usuário

                var idGrupoUsuarioAtual = usuario.IdGrupoUsuario;
                var idGrupoUsuarioNovo = @params.IdGrupoUsuario;

                Mapper.Map(@params, usuario);

                usuario.Nome = new Regex("[*'\",_&#^@]").Replace(usuario.Nome, String.Empty);

                if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa))
                {
                    int? idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                    if (!idEmpresa.HasValue)
                        return new Retorno<bool>(false, $"CNPJ do Empresa inválido.", false);

                    usuario.IdEmpresa = idEmpresa.Value;
                }

                var idsGruposUsuarioEmpresa = _grupoUsuarioApp.GetPorEmpresa(usuario.IdEmpresa, null)?.Select(o => o.IdGrupoUsuario);

                if (idGrupoUsuarioNovo.HasValue)
                {
                    if (idsGruposUsuarioEmpresa != null && idsGruposUsuarioEmpresa.Contains((int)idGrupoUsuarioNovo))
                    {
                        usuario.IdGrupoUsuario = idGrupoUsuarioNovo;
                    }
                    else
                    {
                        return new Retorno<bool>(false, $"IdGrupoUsuario {idGrupoUsuarioNovo} não encontrado na base.",
                            false);
                    }
                }
                else if (idGrupoUsuarioAtual.HasValue)
                {
                    if (idsGruposUsuarioEmpresa != null && idsGruposUsuarioEmpresa.Contains((int)idGrupoUsuarioAtual))
                    {
                        usuario.IdGrupoUsuario = idGrupoUsuarioAtual;
                    }
                    else
                    {
                        return new Retorno<bool>(false, $"IdGrupoUsuario {idGrupoUsuarioAtual} não encontrado na base.",
                            false);
                    }
                }

                usuario.Foto = !string.IsNullOrWhiteSpace(@params.FotoBase64)
                    ? Convert.FromBase64String(@params.FotoBase64) : usuario.Foto;

                usuario.Ativo = true;

                #endregion

                #region Veículo

                var usuarioVeiculo = usuario.Veiculos?.FirstOrDefault(v => v.Ativo);
                if (usuarioVeiculo != null)
                {
                    // Caso a placa do veículo seja alterada, o registro anterior será desativado
                    // E será inserido um novo registro na base de dados
                    if (!string.IsNullOrWhiteSpace(@params.Placa) && usuarioVeiculo.Placa != @params.Placa.Replace("-", ""))
                    {
                        usuarioVeiculo.Ativo = false;
                        usuario.Veiculos.Add(Mapper.Map<UsuarioIntegrarMobRequestModel, Veiculo>(@params));
                        usuario.Veiculos.Last().Ativo = true;
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(@params.Placa))
                        {
                            Mapper.Map(@params, usuarioVeiculo);
                            usuarioVeiculo.Ativo = true;
                        }
                    }
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(@params.Placa))
                    {
                        usuario.Veiculos = new List<Veiculo> { Mapper.Map<UsuarioIntegrarMobRequestModel, Veiculo>(@params) };
                        usuario.Veiculos.Last().Ativo = true;
                    }
                }

                #endregion

                #region Contato

                var usuarioContato = usuario.Contatos?.FirstOrDefault();
                if (usuarioContato != null)
                {
                    if (!(string.IsNullOrWhiteSpace(@params.Telefone) &&
                        string.IsNullOrWhiteSpace(@params.Email)))
                        Mapper.Map(@params, usuarioContato);
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(@params.Telefone) ||
                        !string.IsNullOrWhiteSpace(@params.Celular))
                    {
                        usuario.Contatos
                            = new List<UsuarioContato>
                                {
                                Mapper.Map<UsuarioIntegrarMobRequestModel, UsuarioContato>(@params)
                                };
                    }

                }

                #endregion

                #region Filiais

                var usuarioFilial = usuario.Filiais?.FirstOrDefault();
                if (usuarioFilial != null)
                {
                    if (!string.IsNullOrWhiteSpace(@params.CNPJFilial)) {
                        usuario.Filiais
                            = new List<UsuarioFilial>
                            {
                                new UsuarioFilial
                                {
                                    IdFilial = _filialApp.GetIdPorCnpj(@params.CNPJFilial).GetValueOrDefault()
                                }
                            };
                    }
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(@params.CNPJFilial))
                    {
                        usuario.Filiais
                            = new List<UsuarioFilial>
                                {
                                    new UsuarioFilial
                                    {
                                        IdFilial = _filialApp.GetIdPorCnpj(@params.CNPJFilial).GetValueOrDefault()
                                    }
                                };
                    }
                }

                if (@params.Filiais != null && @params.Filiais.Count() > 0)
                {
                    var filiais = new List<UsuarioFilial>();
                    foreach (var filial in @params.Filiais)
                    {
                        if (filial.HasValue)
                            filiais.Add(new UsuarioFilial() { IdFilial = filial.Value, IdUsuario = usuario.IdUsuario });
                    }
                    usuario.Filiais = filiais;
                }

                #endregion

                #region Endereço

                var usuarioEndereco = usuario.Enderecos?.FirstOrDefault();
                if (usuarioEndereco != null)
                {
                    if (!(string.IsNullOrWhiteSpace(@params.Endereco) &&
                        string.IsNullOrWhiteSpace(@params.CEP)))
                        Mapper.Map(@params, usuarioEndereco);
                }
                else
                {
                    if (@params.IBGECidade > 0)
                    {
                        usuario.Enderecos
                            = new List<UsuarioEndereco>
                                {
                                Mapper.Map<UsuarioIntegrarMobRequestModel, UsuarioEndereco>(@params)
                                };
                    }
                }

                if (usuario.Enderecos.Any() && @params.IBGECidade != 0)
                {
                    Cidade cidade = _cidadeApp.GetCidadeByIBGE(@params.IBGECidade);
                    if (cidade == null)
                        return new Retorno<bool>(false, $"Código IBGE da Cidade é inválido.", false);

                    usuario.Enderecos.First().IdCidade = cidade.IdCidade;
                    usuario.Enderecos.First().IdEstado = cidade.IdEstado;
                    usuario.Enderecos.First().IdPais = cidade.Estado.IdPais;
                }

                #endregion

                #region Horários CheckIn

                if (@params.HorariosCheckIn != null && @params.HorariosCheckIn.Count > 0)
                {
                    // Lista sempre será sobrescrita
                    usuario.HorariosCheckIn = new List<UsuarioHorarioCheckIn>();

                    // Percorre a lista, inserindo no elemento
                    foreach (string horario in @params.HorariosCheckIn)
                    {
                        if (!string.IsNullOrWhiteSpace(horario))
                            usuario.HorariosCheckIn.Add(new UsuarioHorarioCheckIn
                            {
                                Horario = Convert.ToInt32(horario.Replace(@":", "").Trim())
                            });
                    }
                }

                #endregion

                #region Horários Notificações

                if (@params.HorariosNotificacao != null && @params.HorariosNotificacao.Count > 0)
                {
                    // Lista sempre será sobrescrita
                    /*usuario.HorariosNotificacao = new List<UsuarioHorarioNotificacao>();

                    // Percorre a lista, inserindo no elemento
                    foreach (string horario in @params.HorariosNotificacao)
                    {
                        if (!string.IsNullOrWhiteSpace(horario))
                            usuario.HorariosNotificacao.Add(new UsuarioHorarioNotificacao
                            {
                                Horario = Convert.ToInt32(horario.Replace(@":", "").Trim())
                            });
                    }*/
                }

                #endregion


                #region Documentos
                if (@params.ValidadeCNH.HasValue)
                {
                    if (usuario.Documentos != null && usuario.Documentos.Any())
                    {
                        var docsUsu = _usuarioDocumentoApp.GetDocumentos(usuario.IdUsuario);
                        var temCnhDoc = docsUsu.FirstOrDefault(x => x.TipoDocumento.Descricao == "CNH") != null;
                        if (temCnhDoc)
                            foreach (var item in usuario.Documentos.ToList())
                                if (item.TipoDocumento.Descricao == "CNH")
                                    item.Validade = @params.ValidadeCNH.Value;
                    }
                }
                #endregion

                if (@params.IdHorario != null)
                {
                    usuario.IdHorario = @params.IdHorario;
                }

                ValidationResult validationResult = _usuarioApp.Update(usuario, idUsuario.Value);
                if (!validationResult.IsValid)
                    return new Retorno<bool>(false, validationResult.ToFormatedMessage(), true);

                return new Retorno<bool>(true, true);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<bool>($"{nameof(Atualizar)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Integrar os horários de checkin
        /// </summary>
        /// <param name="params">Parâmetros</param>
        /// <returns></returns>
        public Retorno<bool> IntegrarHorariosCheckIn(IntegrarHorarioCheckInRequestModel @params)
        {
            try
            {
                int? idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJ);
                if (!idUsuario.HasValue || idUsuario.Value <= 0)
                    return new Retorno<bool>(false, $"Usuário não localizado.", false);

                Usuario usuario = _usuarioApp.Get(idUsuario.Value);

                // Lista sempre será sobrescrita
                usuario.HorariosCheckIn = new List<UsuarioHorarioCheckIn>();

                // Percorre a lista, inserindo no elemento
                foreach (string horario in @params.Horarios)
                {
                    if (!string.IsNullOrWhiteSpace(horario))
                        usuario.HorariosCheckIn.Add(new UsuarioHorarioCheckIn
                        {
                            Horario = Convert.ToInt32(horario.Replace(@":", "").Trim())
                        });
                }

                ValidationResult validationResult = _usuarioApp.Update(usuario, idUsuario.Value);
                if (!validationResult.IsValid)
                    return new Retorno<bool>(false, validationResult.ToFormatedMessage(), true);

                return new Retorno<bool>(true, true);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<bool>($"{nameof(IntegrarHorariosCheckIn)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Integrar os horários de notificação
        /// </summary>
        /// <param name="params">Parâmetros</param>
        /// <returns></returns>
        public Retorno<bool> IntegrarHorariosNotificacao(IntegrarHorariosNotificacaoRequestModel @params)
        {
            try
            {
                int? idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJ);
                if (!idUsuario.HasValue || idUsuario.Value <= 0)
                    return new Retorno<bool>(false, $"Usuário não localizado.", false);

                Usuario usuario = _usuarioApp.Get(idUsuario.Value);

                ValidationResult validationResult = _usuarioApp.Update(usuario, idUsuario.Value);
                if (!validationResult.IsValid)
                    return new Retorno<bool>(false, validationResult.ToFormatedMessage(), true);

                return new Retorno<bool>(true, true);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<bool>($"{nameof(IntegrarHorariosNotificacao)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Cria um novo motorista e vincula o usuário com o empresa
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public object VincularUsuarioComEmpresa(UsuarioMotoristaVinculoRequestModel @params)
        {
            EProcesso processoMotorista = EProcesso.Create;
            if (string.IsNullOrWhiteSpace(@params.CpfUsuario))
                return new { Sucesso = false, Mensagem = $"CPF do motorista não foi informado." };

            var usuario = _usuarioApp.GetPorCNPJCPF(@params.CpfUsuario);

            if (usuario == null)
                return new { Sucesso = false, Mensagem = "Usuário não encontrado pelo CPF informado." };

            if (usuario.IdEmpresa.HasValue)
                return new { Sucesso = false, Mensagem = $"Motorista já vinculado a um empresa." };

            Motorista motorista;
            var idMotorista = _motoristaApp.GetIdPorCpf(usuario.CPFCNPJ);
            var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            if (idEmpresa == null || idEmpresa <= 0)
                return new { Sucesso = false, Mensagem = $"CNPJ Empresa não informado ou inválido. " };


            if (idMotorista == null || idMotorista <= 0)
            {
                if (string.IsNullOrWhiteSpace(@params.Sexo))
                    return new Retorno<bool>(false, $"Sexo do motorista não informado.", false);
                if (string.IsNullOrWhiteSpace(@params.Rg))
                    return new Retorno<bool>(false, $"RG do motorista não foi informado", false);
                if (string.IsNullOrWhiteSpace(@params.OrgaoExpeditor))
                    return new Retorno<bool>(false, "Órgão expedidor do motorista não informado", false);
                if (string.IsNullOrWhiteSpace(@params.Cnh))
                    return new Retorno<bool>(false, "CNH do motorista não informada", false);
                if (string.IsNullOrWhiteSpace(@params.CnhCategoria))
                    return new Retorno<bool>(false, "A da CNH não foi informada.", false);

                motorista = new Motorista
                {
                    Ativo = true,
                    Bairro = usuario.Enderecos.FirstOrDefault()?.Bairro,
                    Celular = usuario.Contatos.FirstOrDefault()?.Celular,
                    CEP = usuario.Enderecos.FirstOrDefault()?.CEP,
                    IdCidade = usuario.Enderecos.FirstOrDefault()?.IdCidade ?? 0,
                    CNH = @params.Cnh,
                    CNHCategoria = @params.CnhCategoria,
                    CPF = usuario.CPFCNPJ,
                    Email = usuario.Contatos.FirstOrDefault()?.Email,
                    DataHoraUltimaAtualizacao = DateTime.Now,
                    Endereco = usuario.Enderecos.FirstOrDefault()?.Endereco,
                    IdEstado = usuario.Enderecos.FirstOrDefault()?.IdEstado ?? 0,
                    Foto = usuario.Foto,
                    IdPais = usuario.Enderecos.FirstOrDefault()?.IdPais ?? 0,
                    IdEmpresa = idEmpresa,
                    Nome = usuario.Nome,
                    Numero = Convert.ToString(usuario.Enderecos.FirstOrDefault()?.Numero),
                    RG = @params.Rg,
                    RGOrgaoExpedidor = @params.OrgaoExpeditor,
                    TipoContrato = ETipoContrato.Frota,
                    Sexo = @params.Sexo,

                };
            }
            else
            {
                motorista = _motoristaApp.Get(idMotorista.Value);
                motorista.IdEmpresa = idEmpresa;
                processoMotorista = EProcesso.Update;
            }

            if (_empresaApp.Get(idEmpresa.Value, null) == null)
                return new { Sucesso = false, Mensagem = $"Empresa não informado ou não foi cadastrado." };

            usuario.IdEmpresa = idEmpresa;
            var validationUsuario = _usuarioApp.VincularUsuarioComEmpresa(motorista, processoMotorista, usuario);

            if (!validationUsuario.IsValid)
                throw new Exception(validationUsuario.Errors.FirstOrDefault()?.Message);

            return new { Sucesso = true, Mensagem = "Motorista vinculado com sucesso." };
        }

        /// <summary>
        /// Realiza o desvinculo do usuário com o empresa
        /// </summary>
        /// <param name="cpfUsuario"></param>
        /// <returns></returns>
        public object DesvincularUsuarioComEmpresa(string cpfUsuario)
        {
            var validationResult = new ValidationResult();
            var idMotorista = _motoristaApp.GetIdPorCpf(cpfUsuario);
            var idUsuario = _usuarioApp.GetIdPorCNPJCPF(cpfUsuario);
            if (!(idMotorista.HasValue && idUsuario.HasValue))
            {
                return new { Sucesso = false, Mensagem = "Não foi possível encontrar o usuário pelo CPF" + cpfUsuario };
            }

            if (!validationResult.IsValid)
                return new { Sucesso = false, Mensagem = validationResult.Errors.FirstOrDefault()?.Message };

            return new { Sucesso = true, Mensagem = "Motorista desvinculado com sucesso." };

        }

        public List<UsuarioPreferenciasModel> GetPreferenciasUsuarioPrefixLike(int idUsuario, string prefix)
        {
            var result = _usuarioApp.GetPreferenciasUsuarioPrefixLike(idUsuario, prefix);
            return Mapper.Map<List<UsuarioPreferencias>, List<UsuarioPreferenciasModel>>(result);
        }

        public List<UsuarioPreferenciasModel> GetUsuarioPreferencias(int idUsuario, string campo = null)
        {
            var result = _usuarioApp.GetUsuarioPreferencias(idUsuario, campo);
            return Mapper.Map<List<UsuarioPreferencias>, List<UsuarioPreferenciasModel>>(result);
        }

        public Retorno<bool> SaveUsuarioPreferencias(List<UsuarioPreferenciasRequestModel> @params)
        {
            var usuarioPreferencias = @params.Select(item => new UsuarioPreferencias { IdUsuario = item.IdUsuario, Campo = item.Campo, Valor = item.Valor }).ToList();
            var validationResult = _usuarioApp.SaveUsuarioPreferencias(usuarioPreferencias);
            return !validationResult.IsValid ? new Retorno<bool>(false, validationResult.ToFormatedMessage(), true) : new Retorno<bool>(true, true);
        }

        public byte[] GerarRelatorioGridUsuarios(int? idEmpresa, EPerfil perfilUsuarioLogado, int idUsuario, bool listarTerceiros, OrderFilters orderFilters, List<QueryFilters> filters, string extensao)
        {
            return _usuarioApp.GerarRelatorioGridUsuarios(idEmpresa, orderFilters, filters, perfilUsuarioLogado,
                idUsuario, listarTerceiros, extensao, GetLogo(idEmpresa));
        }

        private string GetKeyCodeTransactionUsuario(Usuario usuarioHash)
        {
            if (!string.IsNullOrEmpty(usuarioHash.KeyCodeTransaction))
                return usuarioHash.KeyCodeTransaction;

            var result = _usuarioApp.GerarKeyCodeTransaction(usuarioHash.IdUsuario, usuarioHash.CPFCNPJ);

            return result.Key.IsValid ? result.Value : usuarioHash.KeyCodeTransaction;
        }

        public List<BloqueioGestorTipoResponseModel> GetTiposGestorBloqueio(int idUsuario, EPerfil? perfil = null)
        {
            var tipos = PegarTiposGestorBloqueio();
            if (idUsuario != 0)
            {
                var usuarioPermissaoGestorApp = _usuarioPermissaoGestorApp;
                foreach (var tipo in tipos)
                {
                    var item = usuarioPermissaoGestorApp.GetParametroPermissaoGestor(idUsuario, (EBloqueioGestorTipo)tipo.IdBloqueioGestorTipo);
                    if (item != null)
                    {
                        tipo.HabilitarPorEmpresa = perfil == EPerfil.Empresa ? true : item.DesbloquearEmpresa;
                        tipo.HabilitarPorFilial = item.DesbloquearFilial;
                    }
                    else
                    {
                        tipo.HabilitarPorEmpresa = false;
                        tipo.HabilitarPorFilial = false;
                        usuarioPermissaoGestorApp.Integrar(idUsuario, (EBloqueioGestorTipo)tipo.IdBloqueioGestorTipo, false, false);
                    }
                }
            }
            return tipos;
        }

        public List<FinanceiroPermissaoResponseModel> GetPermissaoFinanceiro(int idUsuario, EPerfil? perfil = null)
        {
            var tipos = PegarTiposFinanceiroBloqueio();

            int[] tiposParaExcluirDaTela = {(int)EBloqueioFinanceiroTipo.exigeAutenticação2FA};

            tipos = tipos.Where(c => !tiposParaExcluirDaTela.Contains(c.IdBloqueioFinanceiro)).ToList();

            if (idUsuario != 0)
            {
                var usuarioPermissaoFinanceiroApp = _usuarioPermissaoFinanceiroApp;
                foreach (var tipo in tipos)
                {
                    var item = usuarioPermissaoFinanceiroApp.GetParametroPermissaoFinanceiro(idUsuario, (EBloqueioFinanceiroTipo)tipo.IdBloqueioFinanceiro);
                    if (item != null) // Usuário possui permissões
                    {
                        tipo.DesbloquearFinanceiro = item.DesbloquearFinanceiro;
                    }
                    else // Usuário não possui permissões ou está criando um novo usuário
                    {
                        var valorDefault = false;
                        var bloqueio = (EBloqueioFinanceiroTipo)tipo.IdBloqueioFinanceiro;

                        if (bloqueio == EBloqueioFinanceiroTipo.exigeAutenticação2FA)
                            valorDefault = true;

                        usuarioPermissaoFinanceiroApp.Integrar(idUsuario,bloqueio, valorDefault);
                        tipo.DesbloquearFinanceiro = valorDefault;
                    }
                }
            }
            return tipos;
        }

        public List<CartaoPermissaoResponseModel> GetPermissaoCartao(int idUsuario, EPerfil? perfil = null)
        {
            var tipos = PegarTiposCartaoBloqueio();

            if (idUsuario != 0)
            {
                foreach (var tipo in tipos)
                {
                    var item = _usuarioPermissaoCartaoApp.GetParametroPermissaoCartao(idUsuario, (EBloqueioCartaoTipo)tipo.IdBloqueioCartao);
                    if (item != null)
                        tipo.DesbloquearCartao = item.DesbloquearCartao;
                    else
                    {
                        _usuarioPermissaoCartaoApp.Integrar(idUsuario,(EBloqueioCartaoTipo)tipo.IdBloqueioCartao, false);
                        tipo.DesbloquearCartao = false;
                    }
                }
            }

            return tipos;
        }

        public FinanceiroPermissaoResponseModel GetItemPermissaoFinanceiro(int idUsuario)
        {  
            var item = _usuarioPermissaoFinanceiroApp.GetParametroPermissaoFinanceiro(idUsuario, EBloqueioFinanceiroTipo.exigeAutenticação2FA);
            if(item == null)
                _usuarioPermissaoFinanceiroApp.Integrar(idUsuario, EBloqueioFinanceiroTipo.exigeAutenticação2FA, true);

            return new FinanceiroPermissaoResponseModel()
            {
                IdBloqueioFinanceiro = EBloqueioFinanceiroTipo.exigeAutenticação2FA.GetHashCode(),
                DesbloquearFinanceiro = item?.DesbloquearFinanceiro ?? true
            };
        }

        public List<BloqueioGestorTipoResponseModel> PegarTiposGestorBloqueio()
        {
            var response = new List<BloqueioGestorTipoResponseModel>();

            foreach (var tipo in _bloqueioGestorTipoApp.GetAll().ToList())
            {
                response.Add(new BloqueioGestorTipoResponseModel
                {
                    IdBloqueioGestorTipo = tipo.IdBloqueioGestorTipo,
                    Descricao = tipo.Descricao,
                    HabilitarPorEmpresa = tipo.HabilitarPorEmpresa,
                    HabilitarPorFilial = tipo.HabilitarPorFilial
                });
            }

            return response;
        }

        public List<FinanceiroPermissaoResponseModel> PegarTiposFinanceiroBloqueio()
        {
            var response = new List<FinanceiroPermissaoResponseModel>();

            foreach (var tipo in _bloqueioFinanceiroTipoApp.GetAll().ToList())
            {
                response.Add(new FinanceiroPermissaoResponseModel
                {
                    IdBloqueioFinanceiro = tipo.IdBloqueioFinanceiroTipo,
                    Descricao = tipo.Descricao,
                });
            }
            
            return response;
        }

        public List<CartaoPermissaoResponseModel> PegarTiposCartaoBloqueio()
        {
            var response = new List<CartaoPermissaoResponseModel>();

            foreach (var tipo in _bloqueioCartaoTipoApp.GetAll().ToList())
            {
                response.Add(new CartaoPermissaoResponseModel
                {
                    IdBloqueioCartao = tipo.IdBloqueioCartaoTipo,
                    Descricao = tipo.Descricao,
                });
            }
            
            return response;
        }

        public Retorno<object> AtualizarDataUltimaAberturaAplicativo(int? usuarioId)
        {
            try
            {
                if (!usuarioId.HasValue)
                    throw new Exception("Usuario Id não informado.");

                var resultado = _usuarioApp.AtualizarDataUltimaAberturaAplicativo(usuarioId.Value);

                return resultado.IsValid
                    ? new Retorno<object>(true, "Operação realizada com sucesso.", null)
                    : new Retorno<object>(false, resultado.Errors.FirstOrDefault()?.Message, null);
            }
            catch (Exception e)
            {
                return new Retorno<object>(false, e.Message, null);
            }
        }

        public Retorno<ConsultaInformacoesMobileModel> ConsultarInformacoesMobile(string cnpjAplicacao, int itensPorPagina, int pagina, string documento)
        {
            try
            {
                var empresaId = _empresaApp.GetIdPorCnpj(cnpjAplicacao);

                return new Retorno<ConsultaInformacoesMobileModel>(true, null,
                    _usuarioApp.ConsultarInformacoesMobile(itensPorPagina, pagina, empresaId, documento));
            }
            catch (Exception e)
            {
                return new Retorno<ConsultaInformacoesMobileModel>(false, e.Message, null);
            }
        }

        public void ExportarUsuariosKeycloak()
        {
            var usuarios = _service.GetTodos();
            foreach (var usuario in usuarios)
            {
                if (usuario.Login != null)
                {
                    var contato = usuario.Contatos.FirstOrDefault();
                    var email = contato?.Email ?? string.Empty;
                    var tempPass = (usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario);
                    _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, "", usuario.Ativo, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                        { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                        { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { usuario.Nome.ToString() } },
                    }, false);
                }
            }
        }

        public void AtualizarUsuariosKeycloak()
        {
            List<KeycloakUserModel> lista = _keycloak.GetAllUsers();
            foreach (var userKC in lista)
            {
                Usuario usuario = _service.GetByLogin(userKC.username);
                if (usuario != null)
                {
                    var contato = usuario.Contatos.FirstOrDefault();
                    var email = contato?.Email ?? string.Empty;
                    try
                    {
                        var tempPass = (usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario);
                        _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, "", usuario.Ativo, new Dictionary<string, object>()
                        {
                            { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                            { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                            { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                            { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                            { "Nome", new List<string> { usuario.Nome.ToString() } },
                        }, false);
                    } catch (Exception ex)
                    {
                        _logger.Error(ex);
                    }
                }
            }
        }

        public BusinessResult<RecursosUsuarioMobileItemModel> GetRecursos(int idusuario)
        {
            try
            {
                var transferenciaCartao = _parametrosApp.GetAplicativoPermiteRealizarTransferenciaCartoes(idusuario);
                var transferenciaBancaria = _parametrosApp.GetAplicativoPermiteRealizarTransferenciaBancaria(idusuario);
                var solicitarAdiantamento = _parametrosApp.GetPermiteSolicitarAdiantamentoApp(idusuario);

                var retorno = new RecursosUsuarioMobileItemModel
                {
                    TransferenciaCartao = transferenciaCartao,
                    TransferenciaTED = transferenciaBancaria,
                    SolicitarAdiantamento = solicitarAdiantamento,
                };

                return BusinessResult<RecursosUsuarioMobileItemModel>.Valid(retorno);
            }
            catch (Exception e)
            {
                return BusinessResult<RecursosUsuarioMobileItemModel>.Error(e.GetBaseException().Message);
            }
        }

        public bool GetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario)
        {
            return _parametrosApp.GetAplicativoPermiteRealizarTransferenciaCartoes(idusuario);
        }

        public bool GetPermitirAcessoExtratoDetalhadoUsuario(int idusuario)
        {
            return _parametrosApp.GetPermitirAcessoExtratoDetalhadoUsuario(idusuario);
        }

        public bool GetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario)
        {
            return _parametrosApp.GetAplicativoPermiteRealizarTransferenciaBancaria(idusuario);
        }

        public IList<PortadorLimitesValor> GetLimitesPortador(string documento)
        {
            return _limiteTransacaoPortadorApp.GetLimites(documento);
        }

        public DespesaUsuarioAnexoModelResponse GetAnexoByUsuario(int idUsuario,string hashId)
        {
            return _despesaUsuarioApp.GetLastAnexo(idUsuario,hashId);
        }

        public BloqueiosTagUsuarioResponse GetBloqueioTag(int idUsuario)
        {
            var result = _tagExtrattaApp.GetBloqueios(idUsuario);

            if (!result.Success)
                throw new Exception(result.Messages.FirstOrDefault());

            return result.Value;
        }
    }
}

﻿using ATS.WS.Models.Webservice;
using ATS.WS.Models.Webservice.Request;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net;
using System.Web.Configuration;
using System.Web.Script.Serialization;
using ATS.WS.Models.Common;
using System.Xml;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;

namespace ATS.WS.Helpers
{
    public class ReturnCityFromCordinate
    {
        public string Cidade { get; set; }
        public string SiglaEstado { get; set; }
        public string Pais { get; set; }

    }
    public class WebServiceHelperWs
    {
        private readonly ICidadeApp _cidadeApp;

        public WebServiceHelperWs(ICidadeApp cidadeApp)
        {
            _cidadeApp = cidadeApp;
        }

        private static string GetKeyGoogle()
        {
            try
            {
                return WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");
            }
            catch (Exception)
            {
                return "";
            }
        }

        public string GetAdressFromCoordinate(string aLatitude, string aLongitude, string keGoogle)
        {
            if (string.IsNullOrWhiteSpace(aLatitude) || string.IsNullOrWhiteSpace(aLongitude) || aLatitude == "0" || aLongitude == "0")
            {
                return "";
            }

            aLongitude = aLongitude.Replace(',', '.');
            aLatitude = aLatitude.Replace(',', '.');

            var latLng = (aLatitude + "," + aLongitude).Trim();
            var lApiUrl = string.Format("https://maps.googleapis.com/maps/api/geocode/xml?latlng=" + latLng + "&sensor=true&key="+ keGoogle);

            XmlDocument doc = new XmlDocument();
            
            try
            {
                doc.Load(lApiUrl);
                XmlNode element = doc.SelectSingleNode("//GeocodeResponse/status");
                if (element.InnerText == "ZERO_RESULTS")
                {
                    return ("");
                }
                var enderecoXml = doc.SelectSingleNode("//GeocodeResponse/result/formatted_address");
                if (enderecoXml != null)
                {
                    var endereco = enderecoXml.InnerText;
                    return endereco;
                }
            }
            catch
            {
                return "";
            }

            return "";
        }

        public GoogleMatrixRespose GetDadosRota( string enderecoOrigem, int? idCidadeOrigem, string enderecoDestino, int? idCidadeDestino)
        {

            if (!idCidadeOrigem.HasValue)
                throw new Exception("Nenhuma cidade de origem informada!");

            if (!idCidadeDestino.HasValue)
                throw new Exception("Nenhuma cidade de destino informada!");

            var cidadeOrigem = _cidadeApp.Get(idCidadeOrigem.Value);
            var cidadeDestino = _cidadeApp.Get(idCidadeDestino.Value);

            var enderecoOrigemCidade = $"{cidadeOrigem.Nome}|{cidadeOrigem.Estado.Nome}";
            var enderecoDestinoCidade = $"{cidadeDestino.Nome}|{cidadeDestino.Estado.Nome}";

            if (!string.IsNullOrEmpty(enderecoOrigem))
                enderecoOrigemCidade = $"{enderecoOrigem}+{enderecoOrigemCidade}";
            
            if (!string.IsNullOrEmpty(enderecoDestino))
                enderecoDestinoCidade = $"{enderecoDestino}+{enderecoDestinoCidade}";

            var path = WebConfigurationManager.AppSettings.Get("URL_API_GOOGLE");
            var key = WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");
            var pathUriCompleta = $"{path}json?origins={enderecoOrigemCidade}&destinations={enderecoDestinoCidade}&mode=driving&language=pr-BR&key={key}";
            
            var lClient = new WebClient();
            string lGeoCodeInfo;
            try
            {
                var lUri = new Uri(pathUriCompleta);
                lGeoCodeInfo = lClient.DownloadString(lUri);
            }
            catch
            {
                var uri = new Uri(pathUriCompleta);
                lGeoCodeInfo = lClient.DownloadString(uri);
            }

            if (!string.IsNullOrEmpty(lGeoCodeInfo))
            {
                var json = new JavaScriptSerializer().Deserialize<GoogleMatrixResponse>(lGeoCodeInfo);

                if (json.Status.ToUpper() == "OK" && json.rows != null)
                {
                    var queryElements = new List<Element>();
                    foreach ( var row in json.rows)
                    {
                        foreach ( var element in row.elements)
                        {
                            if (element.distance.value > 0 )
                                queryElements.Add( element);  
                        }
                    }

                    var lower = queryElements.Min(o => o.duration.value);
                    
                    var lowerPath = queryElements.FirstOrDefault( x => x.status == "OK" && x.duration.value == lower);

                    return new GoogleMatrixRespose()
                    {
                        Distancia = lowerPath.distance.value, 
                        TempoDuracao = lowerPath.duration.value
                    };
                }
            }

            return null;
        }

        public GoogleMatrixRespose GetDistanciaPorLatitudeLongitude(string latLngOrigem, int? latLngDestino)
        {

            var path = WebConfigurationManager.AppSettings.Get("URL_API_GOOGLE");
            var key = WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");
            var pathUriCompleta = $"{path}json?origins={latLngOrigem}&destinations={latLngDestino}&mode=driving&language=pr-BR&key={key}";

            var lClient = new WebClient();
            string lGeoCodeInfo;
            try
            {
                var lUri = new Uri(pathUriCompleta);
                lGeoCodeInfo = lClient.DownloadString(lUri);
            }
            catch
            {
                var uri = new Uri(pathUriCompleta);
                lGeoCodeInfo = lClient.DownloadString(uri);
            }

            if (!string.IsNullOrEmpty(lGeoCodeInfo))
            {
                var json = new JavaScriptSerializer().Deserialize<GoogleMatrixResponse>(lGeoCodeInfo);

                if (json.Status.ToUpper() == "OK" && json.rows != null)
                {
                    var queryElements = new List<Element>();
                    foreach (var row in json.rows)
                    {
                        foreach (var element in row.elements)
                        {
                            if (element.distance.value > 0)
                                queryElements.Add(element);
                        }
                    }

                    var lower = queryElements.Min(o => o.duration.value);

                    var lowerPath = queryElements.FirstOrDefault(x => x.status == "OK" && x.duration.value == lower);

                    return new GoogleMatrixRespose()
                    {
                        Distancia = lowerPath.distance.value,
                        TempoDuracao = lowerPath.duration.value
                    };
                }
            }

            return null;
        }

        public Coordinate GetCoordinateFromAdress(string aAdress)
        {
            try
            {
                var lApiUrl = @"https://maps.googleapis.com/maps/api/geocode/json?key=" + GetKeyGoogle() + "&sensor=false&address=" + aAdress + "&sensor=false";

                var lClient = new WebClient();
                string lGeoCodeInfo;
                try
                {
                    var lUri = new Uri(lApiUrl);
                    lGeoCodeInfo = lClient.DownloadString(lUri);
                }
                catch
                {
                    var uri = new Uri(lApiUrl);
                    lGeoCodeInfo = lClient.DownloadString(uri);
                }

                if (!string.IsNullOrEmpty(lGeoCodeInfo))
                {
                    var json = new JavaScriptSerializer().Deserialize<GoogleGeoCodeResponse>(lGeoCodeInfo);
                    if (json.Status == "OK")
                    {
                        string lLatitude = json.Results[0].Geometry.Location.Lat;
                        string lLongitude = json.Results[0].Geometry.Location.Lng;

                        return new Coordinate()
                        {
                            Status = true,
                            Latitude = decimal.Parse(lLatitude, CultureInfo.InvariantCulture),
                            Longitude = decimal.Parse(lLongitude, CultureInfo.InvariantCulture)
                        };
                    }
                }
                return new Coordinate()
                {
                    Status = false,
                    Latitude = 0,
                    Longitude = 0
                };
            }
            catch (Exception)
            {
                return new Coordinate()
                {
                    Status = false,
                    Latitude = 0,
                    Longitude = 0
                };
            }
        }
    }
}
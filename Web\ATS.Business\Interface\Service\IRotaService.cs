﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IRotaService
    {
        Rota Get(int id);
        List<Rota> ConsultarRotas(int idEmpresa, DateTime? dataBase);
        void Reativar(int idRota);
        void Inativar(int idRota);

        object ConsultarGrid(int? idEmpresa, bool? listarInativos, string descricao, int take, 
            int page, OrderFilters orderFilters, List<QueryFilters> filters);

        void Excluir(int idRota);
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class GestorUsuarioMap : EntityTypeConfiguration<GestorUsuario>
    {
        public GestorUsuarioMap()
        {
            ToTable("GESTOR_USUARIO");

            HasKey(x => x.Id);

            HasRequired(x => x.UsuarioCadastro)
                .WithMany()
                .HasForeign<PERSON>ey(x => x.IdUsuarioCadastro);

            HasRequired(x => x.UsuarioGestor)
                .WithMany()
                .HasForeignKey(x => x.IdGestor); 

            HasRequired(x => x.UsuarioSubordinado)
                .WithMany()
                .Has<PERSON><PERSON><PERSON><PERSON><PERSON>(x => x.IdSubordinado); 
        }
    }
}

﻿using ATS.Data.Repository.External.Extratta.Abastecimento.Client.Models;
using ATS.Data.Repository.External.Extratta.Models;

namespace ATS.Data.Repository.External.Extratta.Abastecimento.Client.Interfaces
{
    public interface IAbastecimentoApiClient
    {
        IntegracaoResult<InserirCreditoTicketLogResponse> InserirTicket(InserirCreditoTicketLogRequest request);
        IntegracaoResult CancelarTicket(int abastecimentoId, CancelarCreditoTicketLogRequest request);
    }
}
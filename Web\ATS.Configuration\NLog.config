﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <targets async="false">
    
    <target
        name="file"
        xsi:type="File"
        layout="${longdate}|${callsite}|${level}: ${message}${onexception:${newline}${newline}Exception\: ${exception:format=ToString}${newline}${newline}Stack Trace\: ${exception:format=StackTrace}${newline}}"
        fileName="${basedir}/log/ATS.CrossCutting.IoC.log"
        archiveFileName="${basedir}/log/ATS.CrossCutting.IoC_{#}.log"
        archiveDateFormat="yyyy-MM-dd"
        archiveEvery="Day"
        archiveNumbering="Date"
        maxArchiveFiles="30"
        encoding="utf-8"
        concurrentWrites="true"
        concurrentWriteAttempts="1"
        concurrentWriteAttemptDelay="10"
        lineEnding="CRLF" />

    <target
        name="fatal"
        xsi:type="File"
        layout="############################################################# FATAL EXCEPTION ######################################################${newline}${longdate}|${callsite}|${level}: ${message}${onexception:${newline}${newline}Exception\: ${exception:format=ToString}${newline}${newline}Stack Trace\: ${exception:format=StackTrace}${newline}}************************************************************************************************************************************"
        fileName="${basedir}/log/ATS.CrossCutting.IoC.log"
        archiveFileName="${basedir}/log/ATS.CrossCutting.IoC_{#}.log"
        archiveDateFormat="yyyy-MM-dd"
        archiveEvery="Day"
        archiveNumbering="Date"
        maxArchiveFiles="30"
        encoding="utf-8"
        concurrentWrites="true"
        concurrentWriteAttempts="1"
        concurrentWriteAttemptDelay="10"
        lineEnding="CRLF" />

    <target
        name="fileLine"
        xsi:type="File"
        layout="${longdate} ------------------------------------------------------------------------------------------------------------"
        fileName="${basedir}/log/ATS.CrossCutting.IoC.log"
        archiveFileName="${basedir}/log/ATS.CrossCutting.IoC_{#}.log"
        archiveDateFormat="yyyy-MM-dd"
        archiveEvery="Day"
        archiveNumbering="Date"
        maxArchiveFiles="30"
        encoding="utf-8"
        concurrentWrites="true"
        concurrentWriteAttempts="1"
        concurrentWriteAttemptDelay="10"
        lineEnding="CRLF"/>

  </targets>
  <rules>
    <logger name="FileLine" minlevel="Debug" writeTo="fileLine" final="true" />
    <logger name="*" level="Fatal" writeTo="fatal" />
    <logger name="*" levels="Trace,Debug,Info,Warn,Error" writeTo="file" />
  </rules>
</nlog>
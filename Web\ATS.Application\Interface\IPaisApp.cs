﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IPaisApp : IAppBase<Pais>
    {
        Pais Get(int id);
        ValidationResult Add(Pais entity);
        ValidationResult Update(Pais entity);
        IQueryable<Pais> Consultar(string nome);
        Pais GetPaisPorBACEN(int ibge);
        Pais Get(string sigla);
        ValidationResult Inativar(int idPais);
        ValidationResult Reativar(int idPais);
        object ConsultaGrid(string nome, string sigla, int? bacen, int take, int page, OrderFilters order, List<QueryFilters> filters);

        Pais BuscarBrasil();

        Pais GetPorNome(string pais);
        bool VerificarBacenCadastrado(int codigo);
    }
}
using System;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class UsuarioPermissaoCartaoService : ServiceBase, IUsuarioPermissaoCartaoService
    {
        private readonly IUsuarioPermissaoCartaoRepository _usuarioPermissaoCartaoRepository;

        public UsuarioPermissaoCartaoService(IUsuarioPermissaoCartaoRepository usuarioPermissaoCartaoRepository)
        {
            _usuarioPermissaoCartaoRepository = usuarioPermissaoCartaoRepository;
        }

        public ValidationResult Integrar(int idUsuario, EBloqueioCartaoTipo idBloqueioGestorTipo, bool bloqueioFinanceiro)
        {
            try
            {
                var repository = _usuarioPermissaoCartaoRepository;
                var permissionUpdate = repository.GetAll().FirstOrDefault(g => g.IdUsuario == idUsuario && g.IdBloqueioCartaoTipo == (int) idBloqueioGestorTipo);
                if (permissionUpdate != null)
                {
                    permissionUpdate.DesbloquearCartao = bloqueioFinanceiro;
                    repository.Update(permissionUpdate);
                    return new ValidationResult();
                }

                var newPermission = new UsuarioPermissaoCartao()
                {
                    IdUsuario = idUsuario,
                    IdBloqueioCartaoTipo = (int) idBloqueioGestorTipo,
                    DesbloquearCartao = bloqueioFinanceiro
                };
            
                repository.Add(newPermission);
            
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public UsuarioPermissaoCartao GetParametroPermissaoCartao(int idUsuario, EBloqueioCartaoTipo idBloqueioFinanceiroTipo)
        {
            var repository = _usuarioPermissaoCartaoRepository;
            var permissionUpdate = repository.GetAll().FirstOrDefault(g => g.IdUsuario == idUsuario && g.IdBloqueioCartaoTipo == (int) idBloqueioFinanceiroTipo);
            return permissionUpdate;
        }

        public bool PossuiPermissao(int idUsuario, EBloqueioCartaoTipo bloqueioFinanceiroTipo)
        {
            var permissao =
                _usuarioPermissaoCartaoRepository
                    .GetAll()
                    .FirstOrDefault(g =>
                        g.IdUsuario == idUsuario && g.IdBloqueioCartaoTipo == (int) bloqueioFinanceiroTipo);

            if (permissao == null)
                return false;

            return permissao.DesbloquearCartao;
        }
    }
}
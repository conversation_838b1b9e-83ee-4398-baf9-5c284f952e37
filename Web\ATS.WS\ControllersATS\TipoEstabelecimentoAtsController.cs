﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class TipoEstabelecimentoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ITipoEstabelecimentoApp _tipoEstabelecimentoApp;
        private readonly IDataMediaServerApp _dataMediaServerApp;

        public TipoEstabelecimentoAtsController(IUserIdentity userIdentity, ITipoEstabelecimentoApp tipoEstabelecimentoApp, IDataMediaServerApp dataMediaServerApp)
        {
            _userIdentity = userIdentity;
            _tipoEstabelecimentoApp = tipoEstabelecimentoApp;
            _dataMediaServerApp = dataMediaServerApp;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters, string descricao = null)
        {
            try
            {
                bool apenasTiposBase = false;
                EUsoTipoEstabelecimento? uso = null;
                if (filters != null)
                {
                    try
                    {
                        var row = filters.FirstOrDefault(x => x.Campo == "apenasTiposBase");
                        if (row != null)
                        {
                            apenasTiposBase = Convert.ToBoolean(row.Valor);
                            filters.Remove(row);
                        }
                        row = filters.FirstOrDefault(x => x.Campo.ToLower() == "idempresa");
                        if (row != null)
                        {
                            idEmpresa = row.Valor.ToIntNullable();
                            filters.Remove(row);
                        }

                        row = filters.FirstOrDefault(x => x.Campo == "uso");
                        if (row != null)
                        {
                            uso = (EUsoTipoEstabelecimento?) Convert.ToInt32(row.Valor);
                            filters.Remove(row);
                        }

                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }

                var tiposEstabelecimentos = _tipoEstabelecimentoApp
                    .ConsultaGrid(idEmpresa, descricao, take, page, order, filters, apenasTiposBase, uso);

                return ResponderSucesso(tiposEstabelecimentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(TipoEstabelecimentoCrud tipoEstabelecimentoCrud)
        {
            try
            {
                if (!tipoEstabelecimentoCrud.IdEmpresa.HasValue && _userIdentity.Perfil != (int)EPerfil.Administrador)
                    return ResponderErro("Empresa é obrigatória.");
                if (string.IsNullOrWhiteSpace(tipoEstabelecimentoCrud.Descricao))
                    return ResponderErro("Descrição é obrigatória.");
                if (!tipoEstabelecimentoCrud.IdIcone.HasValue)
                    return ResponderErro("Ícone é obrigatório.");

                if (tipoEstabelecimentoCrud.UsoTipoEstabelecimento == null)
                    tipoEstabelecimentoCrud.UsoTipoEstabelecimento = _userIdentity.Perfil != (int) EPerfil.Administrador 
                        ? new List<EUsoTipoEstabelecimento> {EUsoTipoEstabelecimento.Todos} 
                        : new List<EUsoTipoEstabelecimento>();

                var usoList = new List<UsoTipoEstabelecimento>();
                tipoEstabelecimentoCrud.UsoTipoEstabelecimento.ForEach(uso => usoList.Add(new UsoTipoEstabelecimento{ Uso = uso }));
                
                var tipoEstabelecimento = new TipoEstabelecimento
                {
                    Descricao = tipoEstabelecimentoCrud.Descricao,
                    IdIcone = tipoEstabelecimentoCrud.IdIcone.Value,
                    UsoTipoEstabelecimento = usoList
                };


                if (tipoEstabelecimentoCrud.IdEmpresa.HasValue)
                    tipoEstabelecimento.IdEmpresa = tipoEstabelecimentoCrud.IdEmpresa.Value;

                var validationResult = _tipoEstabelecimentoApp.Add(tipoEstabelecimento);
                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());

                return ResponderSucesso("Cadastro realizado com sucesso!");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(TipoEstabelecimentoCrud tipoEstabelecimentoCrud)
        {
            try
            {
                if (!tipoEstabelecimentoCrud.IdTipoEstabelecimento.HasValue)
                    return ResponderErro("Id do tipo de estabelecimento é obrigatório.");
                if (!tipoEstabelecimentoCrud.IdEmpresa.HasValue && _userIdentity.Perfil != (int)EPerfil.Administrador)
                    return ResponderErro("Empresa é obrigatória.");
                if (string.IsNullOrWhiteSpace(tipoEstabelecimentoCrud.Descricao))
                    return ResponderErro("Descrição é obrigatória.");
                if (!tipoEstabelecimentoCrud.IdIcone.HasValue)
                    return ResponderErro("Icone é obrigatório.");

                var tipoEstabelecimento = _tipoEstabelecimentoApp.Get(tipoEstabelecimentoCrud.IdTipoEstabelecimento.Value);

                ReflectionHelper.CopyProperties(tipoEstabelecimentoCrud, tipoEstabelecimento);

                _tipoEstabelecimentoApp.Update(tipoEstabelecimento, tipoEstabelecimentoCrud.UsoTipoEstabelecimento);

                return ResponderSucesso("Dados alterados com sucesso!");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idTipoEstabelecimento)
        {
            try
            {
                var tipoEstabelecimento = _tipoEstabelecimentoApp.Get(idTipoEstabelecimento);
                var tokenIcone = tipoEstabelecimento.Icone?.TokenIcone;
                string imagem = string.Empty;

                if (!string.IsNullOrWhiteSpace(tokenIcone))
                    imagem = "data:image/png;base64," + _dataMediaServerApp
                        .GetMedia(tokenIcone)?.Data;

                var retorno = new TipoEstabelecimentoConsulta
                {
                    IdEmpresa = tipoEstabelecimento.IdEmpresa,
                    RazaoSocialEmpresa = tipoEstabelecimento.Empresa?.RazaoSocial,
                    IdTipoEstabelecimento = tipoEstabelecimento.IdTipoEstabelecimento,
                    Descricao = tipoEstabelecimento.Descricao,
                    UsoTipoEstabelecimento = tipoEstabelecimento.UsoTipoEstabelecimento.Select(x => x.Uso).ToList(),
                    IdIcone = tipoEstabelecimento.IdIcone,
                    Imagem = imagem
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.GetBaseException().Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idTipoEstabelecimento)
        {
            try
            {
                var validationResult = _tipoEstabelecimentoApp.Inativar(idTipoEstabelecimento);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Tipo de estabelecimento inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idTipoEstabelecimento)
        {
            try
            {
                var validationResult = _tipoEstabelecimentoApp.Reativar(idTipoEstabelecimento);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Tipo de estabelecimento reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int? idEmpresa)
        {
            try
            {
                var tiposCheckList = _tipoEstabelecimentoApp.GetTiposEstabelecimentoPorEmpresa(idEmpresa)
                    .Select(x => new
                    {
                        x.IdTipoEstabelecimento,
                        x.Descricao
                    }).ToList();

                return ResponderSucesso(tiposCheckList);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
    }
}
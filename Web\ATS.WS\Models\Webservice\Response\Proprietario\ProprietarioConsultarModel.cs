﻿using ATS.Domain.Enum;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class ProprietarioConsultarModel
    {
        public int IdProprietario { get; set; }
        public int IdEmpresa { get; set; }
        public string CNPJCPF { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string IE { get; set; }
        public string RNTRC { get; set; }
        public EStatusIntegracao StatusIntegracao { get; set; }
        public ETipoContrato TipoContrato { get; set; }

        public List<ProprietarioContatoConsultarModel> Contatos { get; set; }
        public List<ProprietarioEnderecoConsultarModel> Enderecos { get; set; }
    }

    public class ProprietarioContatoConsultarModel
    {
        public int IdContato { get; set; }
        public string Telefone { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
    }

    public class ProprietarioEnderecoConsultarModel
    {
        public int IdEndereco { get; set; }
        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public int? Numero { get; set; }
        public string Bairro { get; set; }
        public int? CodigoIBGECidade { get; set; }
        public int? CodigoIBGEEstado { get; set; }
        public int? CodigoBACENPais { get; set; }
    }
}
﻿using System;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class PrestacaoContasEvento 
    {
        public int Id { get; set; }
        
        public int IdPrestacaoContas { get; set; }
        
        public int? IdUsuarioCadastro { get; set; }
        
        public EStatusPrestacaoContasEvento Status { get; set; }
        
        public DateTime DataCadastro { get; set; }
        
        #region Propriedades de Navegacao
        
        public virtual PrestacaoContas PrestacaoContas { get; set; }
        public virtual Usuario UsuarioCadastro { get; set; }
        
        #endregion
    }
}

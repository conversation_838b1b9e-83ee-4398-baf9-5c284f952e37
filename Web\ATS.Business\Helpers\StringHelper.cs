﻿using System;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using ATS.Domain.Enum;
using Microsoft.Ajax.Utilities;

namespace ATS.Domain.Helpers
{
    public static class StringHelper
    {
        /// <summary>
        /// Formatar o valor da placa
        /// </summary>
        /// <param name="placa">Placa a ser formatada</param>
        /// <returns></returns>
        public static string FormatarPlaca(string placa)
        {
            return $"{placa.Substring(0, 3)}-{placa.Substring(3, 4)}";
        }
        
        public static string CensorEmail(this string email)
        {
            if (string.IsNullOrEmpty(email) || !email.Contains('@'))
                return email;

            var parts = email.Split('@');
            var localPart = parts[0];
            var domainPart = parts[1];
            var lengthToCensor = localPart.Length / 2;
            var visiblePart = localPart.Substring(0, localPart.Length - lengthToCensor);
            var censoredPart = new string('*', lengthToCensor);

            return $"{visiblePart}{censoredPart}@{domainPart}";
        }

        public static string ToPlacaFormato(this string placa)
        {
            placa = placa.RemoveSpecialCharacters();

            if (placa.IsNullOrWhiteSpace())
                return placa;

            if (placa.Length < 7)
                return placa;
            return $"{placa.Substring(0, 3)}-{placa.Substring(3, 4)}";
        }

        //https://stackoverflow.com/questions/444798/case-insensitive-containsstring
        public static bool Contains(this string source, string toCheck, StringComparison comp)
        {
            if (string.IsNullOrWhiteSpace(source))
                return false;

            if (string.IsNullOrWhiteSpace(toCheck))
                return false;

//            var valor1 = source.ToLower();
//            var valor2 = toCheck.ToLower();
//
//            var contain = valor1.Contains(valor2);
//
//            return contain;

            if (string.IsNullOrWhiteSpace(source))
                return false;

            return source.IndexOf(toCheck, comp) >= 0;
        }

        public static string ToCNPJFormato(this string cnpj)
        {
            return Convert.ToUInt64(cnpj).ToString(@"00\.000\.000\/0000\-00");
        }

        public static string ToCPFFormato(this string cnpj)
        {
            return !string.IsNullOrWhiteSpace(cnpj) ? Convert.ToUInt64(cnpj).ToString(@"000\.000\.000\-00") : "";
        }

        public static string ToTelefoneFormato(this string numero)
        {
            long numeroLong = 0;
            if (string.IsNullOrWhiteSpace(numero) || !long.TryParse(numero, out numeroLong)) return numero;

            if (numero.Length > 10)
                return String.Format("{0:(##) #####-####}", numeroLong);
            else
                return String.Format("{0:(##) ####-####}", numeroLong);
        }

        public static string ToCEPFormato(this string cep)
        {
            if (string.IsNullOrEmpty(cep))
                return "";

            return Convert.ToUInt64(cep).ToString(@"00000\-000");
        }

        public static string ToTELFormato(this string tel)
        {
            if (string.IsNullOrWhiteSpace(tel))
                return tel;

            switch (tel.Length)
            {
                case 8:
                    return Convert.ToUInt64(tel).ToString(@"0000\-0000");
                case 9:
                    return Convert.ToUInt64(tel).ToString(@"00000\-0000");
                case 10:
                    return Convert.ToUInt64(tel).ToString(@"\(00\)0000\-0000");
                case 11:
                    return Convert.ToUInt64(tel).ToString(@"\(00\)00000\-0000");
                default:
                    return Convert.ToUInt64(tel).ToString(@"\(00\)00000\-0000");
            }
        }

        public static string ToTELFormatoWithSpace(this string tel)
        {
            if (string.IsNullOrWhiteSpace(tel))
                return tel;

            switch (tel.Length)
            {
                case 8:
                    return Convert.ToUInt64(tel).ToString(@"0000\-0000");
                case 9:
                    return Convert.ToUInt64(tel).ToString(@"00000\-0000");
                case 10:
                    return Convert.ToUInt64(tel).ToString(@"\(00\) 0000\-0000");
                case 11:
                    return Convert.ToUInt64(tel).ToString(@"\(00\) 00000\-0000");
                default:
                    return Convert.ToUInt64(tel).ToString(@"\(00\) 00000\-0000");
            }
        }

        public static bool Contem(this string str, string valor)
        {
            if (!str.IsNullOrWhiteSpace())
                return str.StartsWith(valor) || str.EndsWith(valor) || str.Contains(valor);

            return true;
        }

        /// <summary>
        /// Formatar string para a máscara de CPF ou CNPJ
        /// </summary>
        /// <param name="CpfCnpj">String contendo o valor a ser formatado</param>
        /// <param name="exceptionIfUnexpected">Se o valor indicador possuir menos de 11 caracteres, executa exceção</param>
        /// <returns></returns>
        public static string FormatarCpfCnpj(this string CpfCnpj, bool exceptionIfUnexpected = true, bool ignorarErro = false)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(CpfCnpj))
                {
                    CpfCnpj = CpfCnpj.Replace("-", "").Replace(".", "").Replace(@"/", "");
                    if (CpfCnpj.Length < 11 && exceptionIfUnexpected)
                        throw new Exception("CNPJ/CPF com valor inválido.");

                    return string.Format(CpfCnpj.Length < 14
                        ? @"{0:000\.000\.000\-00}"
                        : @"{0:00\.000\.000\/0000\-00}", long.Parse(CpfCnpj));
                }
            }
            catch (Exception)
            {
                if (ignorarErro)
                    return string.Empty;

                throw;
            }

            return string.Empty;
        }

        public static string FormatMaskCpfCnpj(this string input)
        {
            try
            {
                return input.Length switch
                {
                    11 => $"***.{input.Substring(3,3)}.{input.Substring(6,3)}-**",
                    14 => $"**.***.{input.Substring(5,3)}/{input.Substring(8,4)}-**",
                    _ => input
                };
            }
            catch (Exception)
            {
                return input;
            }
        }
        
        public static string ParaFormatoBrasileiroStr(this DateTime dt)
        {
            return dt.ToString("dd/MM/yyyy HH:mm");
        }

        public static string ParaFormatoBrasileiroStr(this DateTime? dt, string defaultValue = "")
        {
            if (dt == null)
                return defaultValue;

            return dt.Value.ParaFormatoBrasileiroStr();
        }

        public static string RemoveDiacritics(this string s)
        {
            var normalizedString = s.Normalize(NormalizationForm.FormD);
            StringBuilder stringBuilder = new StringBuilder();

            foreach (char c in normalizedString)
            {
                if (CharUnicodeInfo.GetUnicodeCategory(c) != UnicodeCategory.NonSpacingMark)
                    stringBuilder.Append(c);
            }

            return stringBuilder.ToString();
        }

        public static double ConvertStringTimeSpanToMinutes(this string time)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(time))
                    return 0;

                double minuto = 0;
                var splitHoraExtra = time.Split(':');
                double hora = Convert.ToInt32(splitHoraExtra[0]);
                minuto += Convert.ToInt32(splitHoraExtra[1]);

                minuto += hora * 60;

                return minuto;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public static string ToCpfOrCnpj(this string value)
        {
            switch (value?.Length)
            {
                case 11:
                    return ToCPFFormato(value);
                case 14:
                    return ToCNPJFormato(value);
            }

            return string.Empty;
        }

        public static string ValueLimited(this string aValue, int aMax)
        {
            if (aValue == null || aMax >= aValue.Length)
                return aValue;

            return aValue.Substring(0, aMax);
        }

        /// <summary>
        /// Formatar dinheiro R$ 0.000,00
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string FormatMoney(this decimal value)
        {
            return value.ToString("C2", CultureInfo.GetCultureInfo("pt-BR"));
        }

        /// <summary>
        /// Formatar dinheiro R$ 0.000,00
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string FormatMoney(this decimal? value)
        {
            return value?.FormatMoney();
        }

        public static int[] GetShuffleExchanges(int size, int key)
        {
            int[] exchanges = new int[size - 1];
            var rand = new RandomHelper(Convert.ToUInt64(key));
            for (int i = size - 1; i > 0; i--)
            {
                int n = rand.NextInt(i + 1);
                exchanges[size - 1 - i] = n;
            }

            return exchanges;
        }

        public static string Shuffle(this string toShuffle, int key)
        {
            int size = toShuffle.Length;
            char[] chars = toShuffle.ToArray();
            var exchanges = GetShuffleExchanges(size, key);
            for (int i = size - 1; i > 0; i--)
            {
                int n = exchanges[size - 1 - i];
                char tmp = chars[i];
                chars[i] = chars[n];
                chars[n] = tmp;
            }

            return new string(chars);
        }

        public static string DeShuffle(this string shuffled, int key)
        {
            int size = shuffled.Length;
            char[] chars = shuffled.ToArray();
            var exchanges = GetShuffleExchanges(size, key);
            for (int i = 1; i < size; i++)
            {
                int n = exchanges[size - i - 1];
                char tmp = chars[i];
                chars[i] = chars[n];
                chars[n] = tmp;
            }

            return new string(chars);
        }

        public static string GetAutonomiaBateriaFormatada(TimeSpan? autonomia)
        {
            var tempoFormatado = string.Empty;

            if (!autonomia.HasValue)
                return string.Empty;

            if (autonomia.Value.Days > 0)
                if (autonomia.Value.Days == 1)
                    tempoFormatado += $"{autonomia.Value.Days} dia";
                else
                    tempoFormatado += $"{autonomia.Value.Days} dias";

            if (autonomia.Value.Hours > 0)
                if (autonomia.Value.Hours < 10)
                    tempoFormatado += $" 0{autonomia.Value.Hours}h";
                else
                    tempoFormatado += $" {autonomia.Value.Hours}h";
            else
                tempoFormatado += " 00h";

            if (autonomia.Value.Minutes > 0)
                if (autonomia.Value.Minutes < 10)
                    tempoFormatado += $"0{autonomia.Value.Minutes}min.";
                else
                    tempoFormatado += $"{autonomia.Value.Minutes}min.";
            else
                tempoFormatado += "00min.";

            return tempoFormatado;
        }

        public static int ObterDigitoVerificadorModulo11(long chave)
        {
            int[] intPesos = {2, 3, 4, 5, 6, 7, 8, 9, 2, 3, 4, 5, 6, 7, 8, 9};
            var strText = chave.ToString();

            if (strText.Length > 16)
                throw new Exception("Número não suportado pela função!");

            var intSoma = 0;
            var intIdx = 0;

            for (var intPos = strText.Length - 1; intPos >= 0; intPos--)
            {
                intSoma += Convert.ToInt32(strText[intPos].ToString()) * intPesos[intIdx];
                intIdx++;
            }

            var intResto = (intSoma * 10) % 11;
            var intDigito = intResto;

            if (intDigito >= 10)
                intDigito = 0;

            return intDigito;
        }

        public static string DecodificarBase64(this string base64)
        {
            var encodedDataAsBytes = Convert.FromBase64String(base64);
            return Encoding.ASCII.GetString(encodedDataAsBytes);
        }
        
        public static string ToProtocoloAnttFormat(this string protocolo,FornecedorEnum fornecedorPedagio)
        {
            if (string.IsNullOrWhiteSpace(protocolo))
                return protocolo;
            
            if(!Regex.IsMatch(protocolo, @"^\d+$"))
                return protocolo;

            switch (fornecedorPedagio)
            {
                case FornecedorEnum.Outros:
                case FornecedorEnum.Desabilitado:
                case FornecedorEnum.Moedeiro:
                    return protocolo;
                case FornecedorEnum.ViaFacil:
                case FornecedorEnum.Veloe:
                case FornecedorEnum.ConectCar:
                    if(protocolo.Length < 20)
                        protocolo = protocolo.PadLeft(20, '0');
                    break;
                case FornecedorEnum.MoveMais:
                case FornecedorEnum.ExtrattaTag:
                    if (protocolo.Length == 20 && protocolo.StartsWith("00"))
                        protocolo = protocolo.Substring(6);
                    if (protocolo.Length <= 14)
                    {
                        if(protocolo.Length != 14)
                            protocolo =  protocolo.PadLeft(14, '0');
                    }
                    break;
                case FornecedorEnum.TaggyEdenred:
                    if (protocolo.Length <= 20)
                    {
                        if (protocolo.Length != 20)
                            protocolo = protocolo.PadLeft(20, '0');
                    }
                    else
                        protocolo = protocolo.Substring(8);
                    break;
            }

            return protocolo;
        }
    }
}
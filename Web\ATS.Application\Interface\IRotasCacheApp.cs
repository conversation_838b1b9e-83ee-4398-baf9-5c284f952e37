﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Interface
{
    public interface IRotasCacheApp : IAppBase<RotaModelo>
    {
        DataModel<object> ConsultarGrid(DateTime dataInicio,DateTime dataFim, int take, int page, 
            OrderFiltersPedagio order, List<QueryFiltersPedagio> filters);
        void DeletarCache(Guid guidRota);
    }
}
﻿using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class VeiculoConjuntoRepository : Repository<VeiculoConjunto>, IVeiculoConjuntoRepository
    {
        public VeiculoConjuntoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<VeiculoConjunto> GetByIdVeiculoWithAllChilds(int idVeiculo)
        {
            return null;
            /*
            return from VeiculoConjunto in All().Where(x => x.IdVeiculo == idVeiculo).Include(x => x.Veiculo)
                   select VeiculoConjunto;*/
        }
    }
}
using System.Collections.Generic;
using ATS.Domain.Entities.Base;

namespace ATS.Domain.Entities
{
    public class PedagioRota : Entidade
    {
        public int IdPedagioRota { get; set; }
        public string Descricao { get; set; }
        public int IdEmpresa { get; set; }
        
        public virtual IList<PedagioRotaPonto> Pontos { get; set; }
        public virtual Empresa Empresa { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoBaseAssociacaoMap : EntityTypeConfiguration<EstabelecimentoBaseAssociacao>
    {
        public EstabelecimentoBaseAssociacaoMap()
        {
            ToTable("ESTABELECIMENTO_BASE_ASSOCIACAO");

            HasKey(x => new { x.IdEstabelecimento, x.IdAssociacao });

            HasRequired(x => x.EstabelecimentoBase)
                .WithMany(x => x.AssociacoesBaseEstabelecimento)
                .HasForeignKey(x => x.IdEstabelecimento);

            HasRequired(x => x.Associacao)
                .WithMany()
                .HasForeignKey(x => x.IdAssociacao);
        }
    }
}

using System;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Data.Repository.EntityFramework;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using AutoMapper;
using Sistema.Framework.Util.Helper;

namespace ATS.Application.Application
{
    public class UsuarioPermissoesConcedidasMobileApp : AppBase, IUsuarioPermissoesConcedidasMobileApp
    {
        private readonly IUsuarioPermissoesConcedidasMobileRepository _permissoesConcedidasMobileRepository;

        public UsuarioPermissoesConcedidasMobileApp(IUsuarioPermissoesConcedidasMobileRepository permissoesConcedidasMobileRepository)
        {
            _permissoesConcedidasMobileRepository = permissoesConcedidasMobileRepository;
        }

        public ValidationResult SalvarOuEditar(UsuarioPermissoesConcedidasMobile permissoes)
        {
            try
            {
                var permissoesCadastradas = _permissoesConcedidasMobileRepository.ConsultarPorUsuario(permissoes.IdUsuario);

                if (permissoesCadastradas != null)
                {
                    ReflectionHelper.CopyProperties(permissoes, permissoesCadastradas, new []{"IdUsuarioPermissaoConcediadMobile"});
                    return _permissoesConcedidasMobileRepository.Editar(permissoesCadastradas);
                }

                return _permissoesConcedidasMobileRepository.Salvar(permissoes);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public UsuarioPermissoesConcedidasMobileModel ConsultarPorUsuario(int usuarioId)
        {
            return Mapper.Map<UsuarioPermissoesConcedidasMobile, UsuarioPermissoesConcedidasMobileModel>(
                _permissoesConcedidasMobileRepository.ConsultarPorUsuario(usuarioId));
        }
    }
}
﻿using System;
using System.Threading.Tasks;
using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using ATS.WS.Services;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using NLog;
using System.Web.Http;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class EstabelecimentoController : BaseController
    {
        private readonly SrvEstabelecimento _srvEstabelecimento;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;

        public EstabelecimentoController(BaseControllerArgs baseArgs, SrvEstabelecimento srvEstabelecimento, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmpresaApp empresaApp, IEstabelecimentoApp estabelecimentoApp) : base(baseArgs)
        {
            _srvEstabelecimento = srvEstabelecimento;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _empresaApp = empresaApp;
            _estabelecimentoApp = estabelecimentoApp;
        }

        /// <summary>
        /// Realiza a consulta dos estabelecimentos cadastrados no ATS, retornando também os inativos (Consulta Mobile)
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Consultar(EstabelecimentoConsulta @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvEstabelecimento.Consultar(@params));
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Realiza a consulta dos estabelecimentos cadastrados no ATS, retornando também os inativos (Consulta Mobile)
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Buscar(EstabelecimentoConsulta @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvEstabelecimento.Consultar(@params));
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Realiza a integração ou alteração de um estabelecimento
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Integrar(EstabelecimentoIntegracaoRequest @params)
        {
            try
            {
                //nao tem validacao de token aqui ????????????
                var empresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                if (!empresa.HasValue)
                    return Mensagem("Empresa informada não identificada. ");

                var estabelecimentoCadastrado = _estabelecimentoApp.Get(@params.CNPJEstabelecimento, empresa.Value);
                if (estabelecimentoCadastrado != null)
                    return Responde(_srvEstabelecimento.Editar(@params, estabelecimentoCadastrado.IdEstabelecimento));
                else
                    return Responde(_srvEstabelecimento.Integrar(@params));
            }
            catch (Exception e)
            {
                return Mensagem($"{e.Message} {e.InnerException}");
            }
        }

        [System.Web.Mvc.HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarEstabelecimentosRota([FromBody] ConsultarEstabelecimentosRotaRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvEstabelecimento.ConsultarEstabelecimentosRota(@params));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(
                        e,
                        $"Erro ao consultar estabelcimento por rota. " +
                        $"CNPJEmpresa: {@params.CNPJEmpresa} - " +
                        $"Origem: {@params.LatitudeOrigem}, {@params.LongitudeOrigem} - " +
                        $"Destino: {@params.LatitudeDestino}, {@params.LongitudeDestino}");
                return Mensagem(e.Message);
            }
        }

        [System.Web.Mvc.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public async Task<JsonResult> IntegrarWebhookJsl([FromBody] EstabelecimentoIntegracaoRequest request)
        {
            try
            {
                var resposta = await _srvEstabelecimento.IntegrarEstabelecimentoJsl(request);

                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Mensagem(e.GetBaseException().Message);
            }
        }

        [System.Web.Mvc.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult IntegrarCombustivelWebhookJsl([FromBody] CombustivelWebHookJSLRequest request)
        {
            try
            {
                //nao tem validacao de token aqui ?????????
                var resposta = _srvEstabelecimento.IntegrarCombustivelJsl(request);

                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Mensagem($"{e.Message} - {e.InnerException}");
            }
        }
    }
}
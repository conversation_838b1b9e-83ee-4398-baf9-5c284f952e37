﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class RecebimentoProtocoloAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IProtocoloApp _protocoloApp;

        public RecebimentoProtocoloAtsController(IUserIdentity userIdentity, IProtocoloApp protocoloApp)
        {
            _userIdentity = userIdentity;
            _protocoloApp = protocoloApp;
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int) EPerfil.Empresa)
                    throw new Exception("Apenas usuários de perfil empresa podem acessar este recurso!");
                
                if (filters == null)
                    filters = new List<QueryFilters>();

                if(_userIdentity.Perfil != (int)EPerfil.Administrador)
                    filters.Add(new QueryFilters {
                        Campo = "IdEmpresa",
                        CampoTipo = EFieldTipo.Number,
                        Operador = EOperador.Exact,
                        Valor = Convert.ToString(_userIdentity.IdEmpresa)
                    });
                filters.Add(new QueryFilters
                {
                    Campo = "IdEmpresa",
                    CampoTipo = EFieldTipo.Number,
                    Operador = EOperador.Exact,
                    Valor = Convert.ToString(_userIdentity.IdEmpresa)
                });

                var protocolos = _protocoloApp.ConsultarRecebimentoProtocolos(take, page, order, filters ?? new List<QueryFilters>());

                return ResponderSucesso(protocolos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Receber(string idsProtocolo)
        {
            try
            {
                var ids = JsonConvert.DeserializeObject<List<int>>(idsProtocolo);

                if (_userIdentity.Perfil != (int) EPerfil.Empresa)
                    throw new Exception("Apenas usuários de perfil empresa podem acessar este recurso!");

                if (ids == null || !ids.Any())
                    throw new Exception("É necessário selecionar no mínimo um protocolo para continuar!");

                var response = _protocoloApp.Receber(ids);

                return response.IsValid
                    ? ResponderSucesso("Protocolos recebidos com sucesso!")
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
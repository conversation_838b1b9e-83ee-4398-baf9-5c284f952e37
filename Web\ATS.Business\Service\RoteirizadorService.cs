using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Roteirizador;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Models;

namespace ATS.Domain.Service
{
    public class RoteirizadorService
    {
        public byte[] GerarRelatorio(RelatorioRoteirizadorModel relatorioRoteirizadorModel, string logo, string mapa)
        {
            var listaFiltros = new List<RelatorioRoteirizadorFiltrosDataType>();
            var listaPracasPedagio = new List<RelatorioRoteirizadorPracasDataType>();
            var listaEntradasSaidas = new List<RelatorioRoteirizadorEntradasSaidasDataType>();
            var listaPostos = new List<RelatorioRoteirizadorPostosDataType>();
            
            if (relatorioRoteirizadorModel.Filtros != null && relatorioRoteirizadorModel.Filtros.Any())
                foreach (var filtro in relatorioRoteirizadorModel.Filtros)
                {
                    listaFiltros.Add(new RelatorioRoteirizadorFiltrosDataType {Local = filtro.Local, Sequencia = filtro.Sequencia});
                }
            
            if (relatorioRoteirizadorModel.PracasPedagio != null && relatorioRoteirizadorModel.PracasPedagio.Any())
                foreach (var pracaPedagio in relatorioRoteirizadorModel.PracasPedagio)
                {
                    listaPracasPedagio.Add(new RelatorioRoteirizadorPracasDataType {PracaPedagio = pracaPedagio.Praca, Preco = $"R$ {pracaPedagio.Preco:N}",PrecoTag = $"R$ {pracaPedagio.PrecoTag:N}"});
                }
            
            if (relatorioRoteirizadorModel.Postos != null && relatorioRoteirizadorModel.Postos.Any())
                foreach (var posto in relatorioRoteirizadorModel.Postos)
                {
                    listaPostos.Add(new RelatorioRoteirizadorPostosDataType {IdEstabelecimento = posto.IdEstabelecimento, Preco = $"R$ {posto.Preco:N}", Produto = posto.NomeProduto, Endereco = posto.Endereco, PrecoPromocional = $"R$ {posto.PrecoPromocional:N}", NomeEstabelecimento = posto.NomeEstabelecimento, TipoEstabelecimento = posto.TipoEstabelecimento});
                }

            listaEntradasSaidas.Add(new RelatorioRoteirizadorEntradasSaidasDataType {ModoViagem = ((ETipoVeiculoPedagioEnum) relatorioRoteirizadorModel.ModoViagem).GetDescription(), NumeroEixos = relatorioRoteirizadorModel.NumeroEixos, DistanciaTotal = $"{relatorioRoteirizadorModel.Distancia:N} KM", CustoTotalPedagio = $"R$ {relatorioRoteirizadorModel.PrecoTotalPedagio:N}",CustoTotalTagPedagio = $"R$ {relatorioRoteirizadorModel.PrecoTotalTagPedagio:N}"});

            if (listaFiltros.Count() > listaPracasPedagio.Count)
            {
                var diferenca = listaFiltros.Count() - listaPracasPedagio.Count;

                for (var i = 0; i < diferenca; i++)
                    listaPracasPedagio.Add(new RelatorioRoteirizadorPracasDataType {Preco = ".", PracaPedagio = ".",PrecoTag = "."});
            }

            if (listaPracasPedagio.Count() > listaFiltros.Count)
            {
                var diferenca = listaPracasPedagio.Count - listaFiltros.Count();

                for (var i = 0; i < diferenca; i++)
                    listaFiltros.Add(new RelatorioRoteirizadorFiltrosDataType {Local = ".", Sequencia ="."});
            }
            
            var report = new RelatorioRoteirizador().GetReport(listaFiltros, listaPracasPedagio, listaEntradasSaidas,
                listaPostos, ConstantesUtils.FormatoPdfMinusculo, logo, mapa);

            return report;
        }
    }
}
﻿using ATS.Domain.Models;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Web;

namespace ATS.Domain.Service.Common
{
    public class ServiceBase : IDisposable
    {
        #region Propriedades

        /// <summary>
        /// Propriedades que identificam o usuário logado e que esta realizando a modificação
        /// </summary>
        //private readonly IdentityUserModel IdentityUser;

        public static Logger _logger = LogManager.GetCurrentClassLogger();

        #endregion

        #region Métodos

        /// <summary>
        /// Retorna se existem diferenças entre as propriedades públicas de dois objetos.
        /// </summary>
        /// <typeparam name="T">Tipo de objeto</typeparam>
        /// <param name="self">Primeiro objeto</param>
        /// <param name="to">Segundo objeto</param>
        /// <param name="ignore">Campos a serem ignorados</param>
        /// <returns></returns>
        public bool PublicPropertiesEqual<T>(T self, T to, params string[] ignore) where T : class
        {
            if (self != null && to != null)
            {
                Type type = typeof(T);
                List<string> ignoreList = new List<string>(ignore);
                foreach (System.Reflection.PropertyInfo pi in type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance))
                {
                    if (pi.GetType().IsClass)
                        continue;

                    if (!ignoreList.Contains(pi.Name))
                    {
                        object selfValue = type.GetProperty(pi.Name).GetValue(self, null);
                        object toValue = type.GetProperty(pi.Name).GetValue(to, null);

                        if (selfValue != toValue && (selfValue == null || !selfValue.Equals(toValue)))
                            return false;
                    }
                }

                return true;
            }

            return self == to;
        }

        public void Dispose()
        {
        }
        #endregion
    }
}
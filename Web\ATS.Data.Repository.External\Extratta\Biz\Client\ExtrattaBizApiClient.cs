﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.Extratta.Models;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;

namespace ATS.Data.Repository.External.Extratta.Biz.Client
{
    public class ExtrattaBizApiClient : IExtrattaBizApiClient
    {
        private string BaseUrl { get; }

        private readonly HttpClient _client;
        private readonly IUserIdentity _userIdentity;

        public ExtrattaBizApiClient(IUserIdentity userIdentity)
        {
            _userIdentity = userIdentity;
            _client = new HttpClient();
            BaseUrl = ConfigurationManager.AppSettings["EXTRATTABIZ_URL"];
        }

        public IntegracaoResult<GetExtratoBizResponse> GetExtrato(int identificador, int produto, string datainicio, string dataFim, int pagina = 0, int totalItens = 15)
        {
            return Task.Run(async () => await GetExtratoAsync(identificador, produto, datainicio, dataFim, pagina, totalItens, CancellationToken.None)).GetAwaiter().GetResult();
        }
        public IntegracaoResult<GetTransacaoBizResponse> GetTransacao(int identificador, int produto, int transacaoId)
        {
            return Task.Run(async () => await GetTransacaoAsync(identificador, produto, transacaoId, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult<ConsultarPermissaoModelResponse> GetPermissaoCartao(int identificador, int produto)
        {
            return Task.Run(async () => await GetPermissaoAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<ConsultarBancosModelResponse> GetBancos()
        {
            return Task.Run(async () => await GetBancosAsync(CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult GetRelatorioDespesasViagemOfx(int identificador, int produto, DateTime datainicio, DateTime dataFim, decimal saldo, List<string> email)
        {
            return GetRelatorioDespesasViagemOfxAsync(identificador, produto, datainicio, dataFim, saldo, email, CancellationToken.None);
        }

        public IntegracaoResult PostDadosBancariosPix(IntegrarDadosBancariosPixModelRequest request)
        {
            return Task.Run(async () => await PostDadosBancariosPixAsync(request, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<TransferenciaPixModelResponse> PostTransferenciaPix(string documentoOrigem, string documentoDestino, decimal valor, int transacaoPixPortalId, string mensagem)
        {
            return Task.Run(async () => await PostTransacaoPixAsync(documentoOrigem, documentoDestino, valor, transacaoPixPortalId, mensagem, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<GerarQrCodeEstaticoPixModelResponse> GetGerarQrCodeEstaticoPix(string documento)
        {
            return Task.Run(async () => await GetGerarQrCodeEstaticoPixAsync(documento, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<ConsultarDadosBancariosPixModelResponse> GetDadosBancariosPix(string documentoEmpresa, string documentoProprietario)
        {
            return Task.Run(async () => await GetDadosBancariosPixAsync(documentoEmpresa, documentoProprietario, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<TransferenciaPixGridTimelineResponse> GetAccountTimelinePix(string documento, int page, int take, DateTime dataInicial, DateTime dataFinal)
        {
            return Task.Run(async () => await GetAccountTimelinePixAsync(documento, page, take, dataInicial, dataFinal, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<TransferenciaPixComprovanteResponse> GetComprovanteTransferenciaPix(string documento, string endToEndId, ETipoTransferenciaPix tipo)
        {
            return Task.Run(async () => await GetComprovanteTransferenciaPixAsync(documento, endToEndId, tipo, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<TransferenciaPixComprovanteResponse> GetComprovanteTransferenciaPixPorTransacao(string documento, int idTransacaoMsCartao)
        {
            return Task.Run(async () => await GetComprovanteTransferenciaPixPorTransacaoAsync(documento, idTransacaoMsCartao, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult<ContaPixResponse> GetContaPix(string documento)
        {
            return Task.Run(async () => await GetContaPixAsync(documento, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult CadastrarChavePix(string documento, string chave, ETipoChavePix tipo)
        {
            return Task.Run(async () => await CadastrarChavePixAsync(documento, chave, tipo, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult DeletarChavePix(string documento, string chave)
        {
            return Task.Run(async () => await DeletarChavePixAsync(documento, chave, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult<ChavesPixResponse> ConsultarChavesPix(string documento)
        {
            return Task.Run(async () => await ConsultarChavesPixAsync(documento, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult VerificarDonoChave(string documentoEmpresa, string documentoTitular, ETipoChavePix tipo, string chave)
        {
            return Task.Run(async () => await VerificarDonoChaveAsync(documentoEmpresa,  documentoTitular, tipo, chave, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<LimitesPixAlterarModelResponse> AlterarLimite(string documento, LimitesPixAlterarModelRequest request)
        {
            return Task.Run(async () => await AlterarLimitePixAsync(documento, request, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<LimitesPixConsultarModelResponse> ConsultarLimites(string documento)
        {
            return Task.Run(async () => await ConsultarLimitesPixAsync(documento, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult<ExtratoConsolidadoModelResponse> ExtratoConsolidado(ExtratoConsolidadoModelRequest request)
        {
            return Task.Run(async () => await ExtratoConsolidadoAsync(request, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult PutCompraFisicaDesabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutCompraFisicaDesabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutCompraFisicaHabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutCompraFisicaHabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutCompraInternacionalDesabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutCompraInternacionalDesabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutCompraInternacionalHabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutCompraInternacionalHabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutCompraOnlineDesabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutCompraOnlineDesabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutCompraOnlineHabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutCompraOnlineHabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutSaqueDesabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutSaqueDesabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }
        
        public IntegracaoResult PutSaqueHabilitar(int identificador, int produto)
        {
            return Task.Run(async () => await PutSaqueHabilitarAsync(identificador, produto, CancellationToken.None)).GetAwaiter().GetResult();
        }

        #region PRIVADO

        private async Task<IntegracaoResult<GetExtratoBizResponse>> GetExtratoAsync(int identificador, int produto, string datainicio, string dataFim, int pagina = 0, int totalItens = 15, CancellationToken cancellationToken = default)
        {
            
            var url = BaseUrl + $"cartao/{identificador}/{produto}/transacoes?dataInicio={datainicio}&dataFim={dataFim}&pagina={pagina}&totalItens={totalItens}";
            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
            {
                return IntegracaoResult<GetExtratoBizResponse>.Error(result.Messages);
            }

            if (string.IsNullOrWhiteSpace(result.Value))
                return IntegracaoResult<GetExtratoBizResponse>.Error("O objeto retornou vazio!");

            var content = JsonConvert.DeserializeObject<GetExtratoBizResponse>(result.Value);

            if (content == null)
                return IntegracaoResult<GetExtratoBizResponse>.Error("O objeto retornou vazio!");

            return IntegracaoResult<GetExtratoBizResponse>.Valid(content);
        }

        private async Task<IntegracaoResult<GetTransacaoBizResponse>> GetTransacaoAsync(int identificador, int produto, int transacaoId, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"cartao/{identificador}/{produto}/transacoes/{transacaoId}";
            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
            {
                return IntegracaoResult<GetTransacaoBizResponse>.Error(result.Messages);
            }

            if (string.IsNullOrWhiteSpace(result.Value))
                return IntegracaoResult<GetTransacaoBizResponse>.Error("O objeto retornou vazio!");

            var content = JsonConvert.DeserializeObject<GetTransacaoBizResponse>(result.Value);

            if (content == null)
                return IntegracaoResult<GetTransacaoBizResponse>.Error("O objeto retornou vazio!");

            return IntegracaoResult<GetTransacaoBizResponse>.Valid(content);
        }
        
        private async Task<IntegracaoResult<ConsultarPermissaoModelResponse>> GetPermissaoAsync(int identificador, int produto, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}";
            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult<ConsultarPermissaoModelResponse>.Error(result.Messages);
            
            if (string.IsNullOrWhiteSpace(result.Value))
                return IntegracaoResult<ConsultarPermissaoModelResponse>.Error("O objeto retornou vazio!");

            var content = JsonConvert.DeserializeObject<ConsultarPermissaoModelResponse>(result.Value);

            if (content == null)
                return IntegracaoResult<ConsultarPermissaoModelResponse>.Error("O objeto retornou vazio!");

            return IntegracaoResult<ConsultarPermissaoModelResponse>.Valid(content);
        }
        
        private async Task<IntegracaoResult<ConsultarBancosModelResponse>> GetBancosAsync(CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"banco";
            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult<ConsultarBancosModelResponse>.Error(result.Messages);
            
            if (string.IsNullOrWhiteSpace(result.Value))
                return IntegracaoResult<ConsultarBancosModelResponse>.Error("O objeto retornou vazio!");

            var content = JsonConvert.DeserializeObject<ConsultarBancosModelResponse>(result.Value);

            if (content == null)
                return IntegracaoResult<ConsultarBancosModelResponse>.Error("O objeto retornou vazio!");

            return IntegracaoResult<ConsultarBancosModelResponse>.Valid(content);
        }
        
        private IntegracaoResult GetRelatorioDespesasViagemOfxAsync(int identificador, int produto, DateTime datainicio, 
            DateTime dataFim, decimal saldo, List<string> email, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"cartao/{identificador}/{produto}/transacoes/ofx";

            var req = new RelatorioExtratoOfxBizRequest()
            {
                Identificador = identificador,
                Produto = produto,
                Saldo = saldo,
                DataFim = dataFim,
                DataInicio = datainicio,
                Emails = email
            };

            var body = JsonConvert.SerializeObject(req);
            
            PostWithoutAwait(url, string.Empty, string.Empty, body, cancellationToken);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutCompraFisicaDesabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/compra-fisica/desabilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PostDadosBancariosPixAsync(IntegrarDadosBancariosPixModelRequest request, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/dados";

            var body = JsonConvert.SerializeObject(request);
            
            var result = await PostAsync(url, string.Empty, string.Empty, body, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult<TransferenciaPixModelResponse>> PostTransacaoPixAsync(string documentoOrigem, string documentoDestino, decimal valor, int transacaoPixPortalId, string mensagem, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/transferencia";

            var req = new TransferenciaPixModelRequest
            {
                Valor = valor,
                DocumentoOrigem = documentoOrigem,
                DocumentoDestino = documentoDestino,
                TransacaoPixPortalId = transacaoPixPortalId,
                Mensagem = mensagem
            };

            var body = JsonConvert.SerializeObject(req);
            
            var result = await PostAsync(url, string.Empty, string.Empty, body, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<TransferenciaPixModelResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<TransferenciaPixModelResponse>(result.Value);

            return IntegracaoResult<TransferenciaPixModelResponse>.Valid(desserializedResult);
        }
        
        
        private async Task<IntegracaoResult<GerarQrCodeEstaticoPixModelResponse>> GetGerarQrCodeEstaticoPixAsync(string documento, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/qrcode/{documento}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<GerarQrCodeEstaticoPixModelResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<GerarQrCodeEstaticoPixModelResponse>(result.Value);

            return IntegracaoResult<GerarQrCodeEstaticoPixModelResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<TransferenciaPixGridTimelineResponse>> GetAccountTimelinePixAsync(string documento, int page, int take, DateTime dataInicial, DateTime dataFinal, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/timeline/{documento}?page={page}&take={take}&dataInicial={dataInicial:s}&dataFinal={dataFinal:s}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<TransferenciaPixGridTimelineResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<TransferenciaPixGridTimelineResponse>(result.Value);

            return IntegracaoResult<TransferenciaPixGridTimelineResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<TransferenciaPixComprovanteResponse>> GetComprovanteTransferenciaPixAsync(
            string documento, string endToEndId, ETipoTransferenciaPix tipo, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/transferencia/{documento}/{endToEndId}/{(int)tipo}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<TransferenciaPixComprovanteResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<TransferenciaPixComprovanteResponse>(result.Value);

            return IntegracaoResult<TransferenciaPixComprovanteResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<TransferenciaPixComprovanteResponse>> GetComprovanteTransferenciaPixPorTransacaoAsync(
            string documento, int idTransacaoMsCartao, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/transferencia/{documento}/transacao/{idTransacaoMsCartao}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<TransferenciaPixComprovanteResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<TransferenciaPixComprovanteResponse>(result.Value);

            return IntegracaoResult<TransferenciaPixComprovanteResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<ContaPixResponse>> GetContaPixAsync(string documento, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/conta/{documento}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<ContaPixResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<ContaPixResponse>(result.Value);

            return IntegracaoResult<ContaPixResponse>.Valid(desserializedResult);
        }

        private async Task<IntegracaoResult> CadastrarChavePixAsync(string documento, string chave, ETipoChavePix tipo,
            CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/chave/{documento}";

            var request = new CadastrarChavePixRequest
            {
                Chave = chave,
                Tipo = tipo
            };
            var body = JsonConvert.SerializeObject(request);

            var result = await PostAsync(url, string.Empty, string.Empty, body, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }

        private async Task<IntegracaoResult> DeletarChavePixAsync(string documento, string chave, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/chave/{documento}/{chave}";

            var result = await DeleteAsync(url, string.Empty, string.Empty, null, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);
            
            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult<ChavesPixResponse>> ConsultarChavesPixAsync(string documento, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/chave/{documento}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<ChavesPixResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<ChavesPixResponse>(result.Value);

            return IntegracaoResult<ChavesPixResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<LimitesPixAlterarModelResponse>> AlterarLimitePixAsync(string documento, LimitesPixAlterarModelRequest request, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/limites/{documento}";

            var body = JsonConvert.SerializeObject(request);
            
            var result = await PostAsync(url, string.Empty, string.Empty, body, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<LimitesPixAlterarModelResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<LimitesPixAlterarModelResponse>(result.Value);

            return IntegracaoResult<LimitesPixAlterarModelResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult> VerificarDonoChaveAsync(string documentoEmpresa, string documentoTitular, ETipoChavePix tipo, string chave, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/chave/{documentoEmpresa}/{documentoTitular}/{tipo.ToInt()}/{chave}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);
            
            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult<LimitesPixConsultarModelResponse>> ConsultarLimitesPixAsync(string documento, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/limites/{documento}";

            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<LimitesPixConsultarModelResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<LimitesPixConsultarModelResponse>(result.Value);

            return IntegracaoResult<LimitesPixConsultarModelResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<ExtratoConsolidadoModelResponse>> ExtratoConsolidadoAsync(ExtratoConsolidadoModelRequest request, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"cartao/transacoes/consolidado";

            var result = await PostAsync(url, string.Empty, string.Empty, JsonConvert.SerializeObject(request), cancellationToken);

            if (!result.Success)
                return new IntegracaoResult<ExtratoConsolidadoModelResponse>(false, result.Messages);
            
            var desserializedResult = JsonConvert.DeserializeObject<ExtratoConsolidadoModelResponse>(result.Value);

            return IntegracaoResult<ExtratoConsolidadoModelResponse>.Valid(desserializedResult);
        }
        
        private async Task<IntegracaoResult<ConsultarDadosBancariosPixModelResponse>> GetDadosBancariosPixAsync(string documentoEmpresa, string documentoProprietario, CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"pix/dados/{documentoEmpresa}/{documentoProprietario}";
            var result = await GetAsync(url, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult<ConsultarDadosBancariosPixModelResponse>.Error(result.Messages);
            
            if (string.IsNullOrWhiteSpace(result.Value))
                return IntegracaoResult<ConsultarDadosBancariosPixModelResponse>.Error("O objeto retornou vazio!");

            var content = JsonConvert.DeserializeObject<ConsultarDadosBancariosPixModelResponse>(result.Value);

            if (content == null)
                return IntegracaoResult<ConsultarDadosBancariosPixModelResponse>.Error("O objeto retornou vazio!");

            return IntegracaoResult<ConsultarDadosBancariosPixModelResponse>.Valid(content);
        }
        
        private async Task<IntegracaoResult> PutCompraFisicaHabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/compra-fisica/habilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutCompraInternacionalDesabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/compra-internacional/desabilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutCompraInternacionalHabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/compra-internacional/habilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutCompraOnlineDesabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/compra-online/desabilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutCompraOnlineHabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/compra-online/habilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutSaqueDesabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/saque/desabilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }
        
        private async Task<IntegracaoResult> PutSaqueHabilitarAsync(int identificador, int produto,CancellationToken cancellationToken = default)
        {
            var url = BaseUrl + $"permissao/{identificador}/{produto}/saque/habilitar";
            var result = await PutAsync(url, string.Empty, string.Empty, string.Empty, cancellationToken);

            if (!result.Success)
                return IntegracaoResult.Error(result.Messages);

            return IntegracaoResult.Valid();
        }

        #endregion

        #region Auxiliar

        private async Task<IntegracaoResult<string>> GetAsync(string url, string tokenType, string token, CancellationToken cancellationToken = default)
        {
            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            AddCustomHeaders();

            var httpResponse = await _client.GetAsync(url, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private async Task<IntegracaoResult<string>> DeleteAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);
            
            AddCustomHeaders();
            
            var request = new HttpRequestMessage(HttpMethod.Delete, url);

            if (!string.IsNullOrWhiteSpace(body))
            {
                var content = new StringContent(body, Encoding.UTF8, "application/json");
                request.Content = content;
            }

            var httpResponse = await _client.SendAsync(request, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private async Task<IntegracaoResult<string>> PostAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);
            
            AddCustomHeaders();

            var httpResponse = await _client.PostAsync(url, content, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private IntegracaoResult PostWithoutAwait(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);
            
            AddCustomHeaders();

            _ = _client.PostAsync(url, content, cancellationToken);
            
            return IntegracaoResult.Valid();
        }

        private async Task<IntegracaoResult<string>> PutAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            AddCustomHeaders();

            var httpResponse = await _client.PutAsync(url, content, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private void AddCustomHeaders()
        {
            if (_userIdentity == null) return;
            
            if (!string.IsNullOrWhiteSpace(_userIdentity.CnpjEmpresa) && _userIdentity.CnpjEmpresa.Length == 14 && _userIdentity.CnpjEmpresa.OnlyNumbers().Length == 14)
                _client.DefaultRequestHeaders.Add("EmpresaCnpj", _userIdentity.CnpjEmpresa);
            
            if (!string.IsNullOrWhiteSpace(_userIdentity.Nome))
                _client.DefaultRequestHeaders.Add("UsuarioPortalNome", _userIdentity.Nome);

            if (!string.IsNullOrWhiteSpace(_userIdentity.CpfCnpj) && ((_userIdentity.CpfCnpj.Length == 11 && _userIdentity.CpfCnpj.OnlyNumbers().Length == 11) ||
                                                                      (_userIdentity.CpfCnpj.Length == 14 && _userIdentity.CpfCnpj.OnlyNumbers().Length == 14)))
                _client.DefaultRequestHeaders.Add("UsuarioPortalCpfCnpj", _userIdentity.CpfCnpj);
            
            if (_userIdentity.Perfil != 0)
                _client.DefaultRequestHeaders.Add("UsuarioPortalPerfil", _userIdentity.Perfil.ToString());
        }

        #endregion
    }
}
﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Services;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.ControllersATS
{
    public class PagamentoFreteAtsController : DefaultController
    {
        private readonly IPagamentoFreteApp _pagamentoFreteApp;
        private readonly IViagemApp _viagemApp;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosApp _parametrosApp;
        private readonly SrvPagamentoFrete _srvPagamentoFrete;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IProtocoloApp _protocoloApp;
        private readonly IEstabelecimentoBaseContaBancariaApp _estabelecimentoBaseContaBancariaApp;
        private readonly IEstadoApp _estadoApp;

        public PagamentoFreteAtsController(IUserIdentity userIdentity, IViagemApp viagemApp, IParametrosApp parametrosApp, SrvPagamentoFrete srvPagamentoFrete, 
            IMotoristaApp motoristaApp, IUsuarioApp usuarioApp, IEstabelecimentoApp estabelecimentoApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies,
            IEstabelecimentoBaseContaBancariaApp estabelecimentoBaseContaBancariaApp, IProtocoloApp protocoloApp, IEstadoApp estadoApp, IPagamentoFreteApp pagamentoFreteApp)
        {
            _userIdentity = userIdentity;
            _viagemApp = viagemApp;
            _parametrosApp = parametrosApp;
            _srvPagamentoFrete = srvPagamentoFrete;
            _motoristaApp = motoristaApp;
            _usuarioApp = usuarioApp;
            _estabelecimentoApp = estabelecimentoApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _estabelecimentoBaseContaBancariaApp = estabelecimentoBaseContaBancariaApp;
            _protocoloApp = protocoloApp;
            _estadoApp = estadoApp;
            _pagamentoFreteApp = pagamentoFreteApp;
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTotalPorEstabelecimentoCurvaAbc(DateTime? dataInicial, DateTime? dataFinal, int? uF, double? a, double? b, double? c, int page, int take)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Funcionalidade habilitada apenas para usuários do perfil empresa");

                var dataInicio = dataInicial?.StartOfDay() ?? DateTime.Now.StartOfDay();
                var dataFim = dataFinal?.EndOfDay() ?? DateTime.Now.EndOfDay();

                var uf = uF == 0 ? null : _estadoApp.Get(uF ?? 0)?.Sigla;

                var dataGrid = _pagamentoFreteApp.ConsultarTotalPagamentosCurvaABC(dataInicio, dataFim, uf, a ?? 0, b ?? 0, c ?? 0,
                    page, take, _userIdentity.IdEmpresa.Value);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGrid(string json)
        {
            if (!_userIdentity.IdEmpresa.HasValue)
                return null;

            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioCurvaAbcDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var uf = filtroGridModel.UF == 0 ? null : _estadoApp.Get(filtroGridModel.UF ?? 0)?.Sigla;

            var datainicio = filtroGridModel.DataInicial?.StartOfDay() ?? DateTime.Now.StartOfDay();
            var dataFim = filtroGridModel.DataFinal?.EndOfDay() ?? DateTime.Now.EndOfDay();

            var report = _srvPagamentoFrete.GerarRelatorioGrid(datainicio, dataFim, uf, filtroGridModel.A, filtroGridModel.B, filtroGridModel.C, _userIdentity.IdEmpresa.Value, filtroGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de Curva ABC.{filtroGridModel.Extensao}");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object GetPagamentosPorViagem(DateTime dataInicial, DateTime dataFinal, int take = 10, int page = 1, OrderFilters order = null, List<QueryFilters> filters = null)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if ((dataFinal - dataInicial).TotalDays > 31)
                    return ResponderErro("Período máximo é de 30 dias.");

                var dataGrid = _viagemApp.GetPagamentosPorViagem(_userIdentity.IdEmpresa.Value, dataInicial, dataFinal, take, page, order, filters);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult EfetuarPagamento(PagamentoFreteModel pagamentoFrete)
        {
            try
            {
                var usuario = _usuarioApp.Get(_userIdentity.IdUsuario, true);
                switch ((EPerfil)_userIdentity.Perfil)
                {
                    case EPerfil.Administrador:
                        throw new Exception("Funcionalidade não habilitada para o perfil administrador!");
                    case EPerfil.Estabelecimento:
                    case EPerfil.Empresa:
                        {
                            var result = _pagamentoFreteApp.EfetuarPagamento(pagamentoFrete, usuario, true, pagamentoFrete.AdministradoraPlataforma, usuario.UsuarioEstabelecimentos?.Select(s => s.IdEstabelecimento).ToList());

                            if (result.GetFaults().Any())
                            {
                                if (result.GetFaults().First().Code == "9000")
                                {
                                    return ResponderAtencao(true, $"{result.GetFaults().First().Message}");
                                }
                            }

                            if (!result.IsValid)
                                return ResponderErro(result.ToFormatedMessage());

                            if (result.IsValid && result.Alerts.Any())
                                return ResponderSucesso(result.Alerts.Select(x => new MultipleMessageReturnTyped
                                {
                                    type = ETypeReturnMessage.Alert,
                                    message = x.Message
                                }).ToList(), "Pagamento efetuado com sucesso!");

                            if (result.IsValid && !result.Alerts.Any())
                                return ResponderSucesso("Pagamento efetuado com sucesso!");

                            return ResponderErro("Ocorreu um erro, por favor tente novamente!");
                        }
                    case EPerfil.Motorista:
                        throw new Exception("Funcionalidade não habilitada para o perfil motorista!");
                    case EPerfil.Cliente:
                        throw new Exception("Funcionalidade não habilitada para o perfil cliente!");
                    case EPerfil.Proprietario:
                        throw new Exception($"Funcionalidade não habilitada para o perfil {WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]}!");
                    default:
                        throw new Exception("Funcionalidade não habilitada para este usuário!");
                }
            }
            catch (Exception e)
            {
                return ResponderErro(e.GetBaseException().Message);
            }
        }


        [HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarRelatorioPagamentosPorViagem(int idEmpresa, DateTime dataInicial, DateTime dataFinal)
        {
            try
            {
                var dataGrid = _viagemApp.GetRelatorioPagamentosPorViagem(idEmpresa, dataInicial, dataFinal);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GerarRecibo(string token, int? idViagemEvento, string usuario)
        {
            try
            {
                Response.AddHeader("Content-Disposition", $"inline; filename=Recibo-{token}.pdf");
                return File(_pagamentoFreteApp.GerarRecibo(token, idViagemEvento, usuario), "application/pdf");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpStatusCodeResult(System.Net.HttpStatusCode.BadRequest, e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CalcularValores(string token, bool habilitaPagamentoCartao, decimal? pesoChegada, int? numeroSacas, EUnidadeMedida unidadeInformada, bool quebraAbonada)
        {
            try
            {
                if (pesoChegada.HasValue && pesoChegada < 0)
                    throw new Exception("Não é possível informar valor negativo para o peso de chegada.");
                if (numeroSacas.HasValue && numeroSacas < 0)
                    throw new Exception("Não é possível informar um valor negativo para a quantidade de sacas.");

                if (!pesoChegada.HasValue && !numeroSacas.HasValue)
                    return ResponderSucesso(new PagamentoFreteEventoModel());
                return ResponderSucesso(_pagamentoFreteApp.CalcularValoresViagem(token, habilitaPagamentoCartao, pesoChegada, numeroSacas, unidadeInformada, quebraAbonada));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorToken(string token, int? idEstabelecimento)
        {
            try
            {
                var usuario = _usuarioApp.Get(_userIdentity.IdUsuario, true);
                switch ((EPerfil)_userIdentity.Perfil)
                {
                    case EPerfil.Administrador:
                        return ResponderSucesso(_pagamentoFreteApp.ConsultarPorToken(token, usuario.CPFCNPJ, usuario.Nome));
                    case EPerfil.Estabelecimento:
                        return ResponderSucesso(_pagamentoFreteApp.ConsultarPorToken(token, usuario.CPFCNPJ,
                            usuario.Nome, usuario.UsuarioEstabelecimentos.Select(s => s.IdEstabelecimento).ToList()));
                    case EPerfil.Empresa:
                        if (!idEstabelecimento.HasValue)
                            return ResponderErro("Informe o estabelecimento!");

                        return ResponderSucesso(_pagamentoFreteApp.ConsultarPorToken(token, usuario.CPFCNPJ,
                            usuario.Nome, null, idEstabelecimento.Value));
                    case EPerfil.Motorista:
                        throw new Exception("Funcionalidade não habilitada para o perfil motorista!");
                    case EPerfil.Cliente:
                        throw new Exception("Funcionalidade não habilitada para o perfil cliente!");
                    case EPerfil.Proprietario:
                        throw new Exception($"Funcionalidade não habilitada para o perfil {WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]}!");
                    default:
                        throw new Exception("Funcionalidade não habilitada para este usuário!");
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao buscar token " + token);
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarEvento(string token)
        {
            try
            {
                return ResponderSucesso(_pagamentoFreteApp.ConsultarEvento(token));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarOutrosDescontos(string token)
        {
            try
            {
                return ResponderSucesso(_pagamentoFreteApp.ConsultarOutrosDescontos(token));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarOutrosAcrescimos(string token)
        {
            try
            {
                return ResponderSucesso(_pagamentoFreteApp.ConsultarOutrosAcrescimos(token));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult CheckToken(string token, int? idEstabelecimentoLogado)
        {
            try
            {
                var row = _pagamentoFreteApp.CheckToken(token);
                if (row == null)
                    throw new Exception($"EVENT_NOT_FOUND");

                if (row.LiberarPagtoSemChave)
                    return ResponderSucesso("PAG_AUTOR_SEM_CHAVE", new { Validar = false });

                if (row.StatusViagemEvento == EStatusViagemEvento.Bloqueado)
                    throw new Exception("TOK_BLOCKED");

                if (_userIdentity.Perfil == (int)EPerfil.Empresa && _userIdentity.IdEmpresa.HasValue)
                    return ResponderSucesso(new { Validar = false });

                if (row.EmpresaValidaChaveMHBaixaEvento)
                    if (_estabelecimentoApp.ValidarChaveTokenPorHorarario(idEstabelecimentoLogado ?? 0, _userIdentity.IdEmpresa ?? row.IdEmpresa))
                    {
                        return ResponderSucesso(new { Validar = false });
                    }
                    else
                    {
                        var estabelecimentoValidaChavePagamento = _parametrosApp.GetEstabelecimentoValidarChavePagamento(idEstabelecimentoLogado ?? 0);
                        return ResponderSucesso(new { Validar = estabelecimentoValidaChavePagamento ?? row.EmpresaValidaChaveMHBaixaEvento });
                    }

                return ResponderSucesso(new { Validar = false });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAnexos(string token)
        {
            try
            {
                var response = _pagamentoFreteApp.ConsultarAnexos(token, _userIdentity?.IdUsuario);

                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult VincularAnexo(string tokenMidia, int idViagemDocumento)
        {
            try
            {
                _pagamentoFreteApp.VincularAnexo(tokenMidia, idViagemDocumento);

                return ResponderSucesso(true);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ValidarIntegridadeChave(string token, string chave)
        {
            try
            {
                var evento = _pagamentoFreteApp.ConsultarEvento(token);
                if (evento == null)
                    throw new Exception($"Nenhum evento encontrado para o token {token}!");

                if (evento.ChaveToken != chave)
                    throw new Exception($"A chave {chave} é inválida! Tente novamente!");

                if (DateTime.Now > evento.DataHoraLimiteValidadeToken)
                    throw new Exception($"Esta chave não é mais válida!");

                return ResponderSucesso("Chave validada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult SetMotivoRejeicaoAbono(int idViagemEvento, int? idMotivo, string detalhamentoMotivo, int? idUsuario)
        {
            try
            {
                var validation = _pagamentoFreteApp.SetMotivoRejeicaoAbono(idViagemEvento, idMotivo, detalhamentoMotivo, idUsuario);

                if (!validation.IsValid)
                    throw new Exception(validation.Errors.FirstOrDefault()?.Message);

                return ResponderSucesso("Abono rejeitado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult EnviarSmsValidacao(string token, string cpf, string cnpj, string celular, bool isPessoaJuridica = false)
        {
            try
            {
                _pagamentoFreteApp.EnviarSMSValidacao(token, cpf, cnpj, celular, isPessoaJuridica);

                return ResponderSucesso(true);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult RemoverAnexo(int idViagemDocumento)
        {
            try
            {
                _pagamentoFreteApp.RemoverAnexo(idViagemDocumento);

                return ResponderSucesso(true);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult RemoverAnexoByToken(string token)
        {
            try
            {
                var response = _pagamentoFreteApp.RemoverAnexo(token);

                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult AlterarSaldo(decimal? valorSaldo, decimal pesoChegada, decimal quebraMercadoria, decimal difFreteMotorista, string token)
        {
            try
            {
                if (!valorSaldo.HasValue)
                    return ResponderErro("Não foi possível identificar o valor do saldo. ");

                if (string.IsNullOrWhiteSpace(token))
                    return ResponderErro("Não foi possível identificar o evento.");

                var validationResult = _pagamentoFreteApp.AlterarSaldo(valorSaldo.Value, pesoChegada, quebraMercadoria, difFreteMotorista, token);

                return validationResult.IsValid
                    ? ResponderSucesso("Saldo atualizado com sucesso!")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult AbonarEvento(string tokenEvento, string tokenDocumento)
        {
            try
            {
                _pagamentoFreteApp.AbonarViagemEvento(tokenEvento, tokenDocumento);
                return ResponderSucesso("Abono solicitado com sucesso!");

            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult SolicitarAbono(string token, int? idMotivo, string detalhamento)
        {
            try
            {
                _pagamentoFreteApp.SolicitarAbono(token, idMotivo, detalhamento, _userIdentity.IdUsuario);
                return ResponderSucesso("Abono solicitado com sucesso!");

            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult CancelarSolicitacaoAbono(string token)
        {
            try
            {
                _pagamentoFreteApp.CancelarAbono(token);
                return ResponderSucesso("Solicitação cancelada com sucesso!");

            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult BuscarCartaoPortador(string token, string documentoPortador)
        {
            try
            {
                var usuario = _usuarioApp.GetAllChilds(_userIdentity.IdUsuario);
                var motorista = _motoristaApp.GetPorCpf(documentoPortador);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();

                if (cartaoProdutosList == null)
                    throw new Exception($"Nenhum cartão vinculado ao portador com documento {documentoPortador}!");

                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();

                var cartoes = cartoesApp.GetCartoesVinculados(motorista.CPF, cartaoIdArray).Cartoes.Last();

                return Responder(true, string.Empty, cartoes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult RelatorioPagamentos(string sessionKey, DateTime dataInicial, DateTime dataFinal, ETipoArquivoPagamento? tipoRelatorio)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Nenhuma empresa vinculada a este usuário.");

                if ((dataFinal - dataInicial).TotalDays > 31)
                    return ResponderErro("Período máximo é de 30 dias.");

                var report = _viagemApp.GerarRelatorioPagamentos(_userIdentity.IdEmpresa.Value, dataInicial, dataFinal, tipoRelatorio);

                switch (tipoRelatorio)
                {
                    case ETipoArquivoPagamento.PDF:
                        Response.AddHeader("Content-Disposition", $"inline; filename=Pagamentos-ATS.pdf");
                        return File(report, ConstantesUtils.PdfMimeType);
                    case ETipoArquivoPagamento.Excel:
                        Response.AddHeader("Content-Disposition", $"attachment; filename=Pagamentos.xlsx");
                        return File(report, ConstantesUtils.ExcelMimeType);
                    case ETipoArquivoPagamento.Detalhado:
                        Response.AddHeader("Content-Disposition", $"attachment; filename=Pagamentos.xlsx");
                        return File(report, ConstantesUtils.ExcelMimeType);
                    default:
                        Response.AddHeader("Content-Disposition", $"inline; filename=Pagamentos-ATS.pdf");
                        return File(report, ConstantesUtils.PdfMimeType);

                }
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpNotFoundResult(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult RelatorioProtocolo(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioProtocoloPagamentooFreteDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _srvPagamentoFrete.GerarRelatorioProtocolos(filtroGridModel.IdEmpresa, filtroGridModel.IdUsuario, filtroGridModel.Extensao, int.MaxValue, filtroGridModel.Page, filtroGridModel.Order, filtroGridModel.Filters);

            var mimeType = string.Empty;
            var extensaoArquivo = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case ETipoArquivo.PDF:
                    mimeType = ConstantesUtils.PdfMimeType;
                    extensaoArquivo = "pdf";
                    break;
                case ETipoArquivo.Excel:
                    mimeType = ConstantesUtils.ExcelMimeType;
                    extensaoArquivo = "xlsx";
                    break;
            }

            return File(report, mimeType, $"Relatório de protocolo.{extensaoArquivo}");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridProtocolo(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Empresa)
                    throw new Exception("Apenas usuários de perfil empresa podem acessar este recurso!");

                if (filters == null)
                    filters = new List<QueryFilters>();

                filters.Add(new QueryFilters
                {
                    Campo = "IdEmpresa",
                    CampoTipo = EFieldTipo.Number,
                    Operador = EOperador.Exact,
                    Valor = Convert.ToString(_userIdentity.IdEmpresa)
                });

                var protocolos = _protocoloApp.ConsultarGridProtocolos(take, page, order, filters);

                return ResponderSucesso(protocolos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object ConsultarGraficosSumPorEstabelecimento(DateTime? dataInicial, DateTime? dataFinal, int? uF, double? a, double? b, double? c)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Funcionalidade habilitada apenas para usuários do perfil empresa");

                var dataInicio = dataInicial?.StartOfDay() ?? DateTime.Now.StartOfDay();
                var dataFim = dataFinal?.EndOfDay() ?? DateTime.Now.EndOfDay();

                var uf = uF == 0 ? null : _estadoApp.Get(uF ?? 0)?.Sigla;

                var dadosGrafico = _pagamentoFreteApp.ConsultarGraficosSumPorEstabelecimento(dataInicio, dataFim, uf, a ?? 0, b ?? 0, c ?? 0, _userIdentity.IdEmpresa.Value);

                return ResponderSucesso(dadosGrafico);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDetalhesCurvaAbc(DateTime dataInicial, DateTime dataFinal, int idEstabelecimentoBase, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var dataGrid = _pagamentoFreteApp.ConsultarPagamentosEstabelecimentoGrid(dataInicial, dataFinal, idEstabelecimentoBase, take, page, order, filters);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioConsultaPagamentosEstabelecimentoReport(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<GerarRelatorioConsultaPagamentosEstabelecimentoReportDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _srvPagamentoFrete.GerarRelatorioConsultaPagamentosEstabelecimentoReport(filtroGridModel.DataInicial,
                filtroGridModel.DataFinal, filtroGridModel.IdEstabelecimentoBase, filtroGridModel.Order, filtroGridModel.Filters, filtroGridModel.Extensao, _userIdentity.IdEmpresa);

            var mimeType = string.Empty;
            var extensaoArquivo = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    extensaoArquivo = "pdf";
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    extensaoArquivo = "xlsx";
                    break;
            }

            return File(report, mimeType, $"Relatório de pagamento de estabelecimento.{extensaoArquivo}");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetTotaisPagamentosPorTipo(DateTime dataInicial, DateTime dataFinal, EStatusViagemEvento status, int habilitarPagamentoCartao, int? idEstabelecimentoBase, int? uF)
        {
            try
            {
                var dataInicio = dataInicial.StartOfDay();
                var dataFim = dataFinal.EndOfDay();

                var data = _pagamentoFreteApp.GetTotaisPagamentosPorTipo(dataInicio, dataFim, uF, status, habilitarPagamentoCartao, idEstabelecimentoBase, _userIdentity.IdEmpresa);

                return ResponderSucesso(data);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object ConsultarGridDetalhes(DateTime dataInicial, DateTime dataFinal, EStatusViagemEvento? status, bool habilitarPagamentoCartao, int? uF, int? idEstabelecimento, ETipoEventoViagem? tipo, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var data = (status == EStatusViagemEvento.Aberto) ? _pagamentoFreteApp.ConsultarGridDetalhesAberto(
                        dataInicial.GetDataOnly().AddHours(0).AddMinutes(0).AddSeconds(0),
                        dataFinal.GetDataOnly().AddHours(23).AddMinutes(59).AddSeconds(59),
                        habilitarPagamentoCartao, uF, idEstabelecimento, tipo, take, page, order, filters, _userIdentity.IdEmpresa) :
                    _pagamentoFreteApp.ConsultarGridDetalhesBaixado(
                        dataInicial.GetDataOnly().AddHours(0).AddMinutes(0).AddSeconds(0),
                        dataFinal.GetDataOnly().AddHours(23).AddMinutes(59).AddSeconds(59),
                        habilitarPagamentoCartao, uF, idEstabelecimento, tipo, take, page, order, filters, _userIdentity.IdEmpresa);

                return ResponderSucesso(data);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioDetalhesProvisaoAberto(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioProvisaoDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _srvPagamentoFrete.GerarRelatorioDetalhesProvisaoAberto(filtroGridModel.DataInicial, filtroGridModel.DataFinal,
                filtroGridModel.HabilitarPagamentoCartao, filtroGridModel.UF, filtroGridModel.IdEstabelecimento, filtroGridModel.Tipo, filtroGridModel.Order, filtroGridModel.Filters,
                filtroGridModel.Extensao, filtroGridModel.IdEmpresa ?? 0);

            var mimeType = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório detalhes de provisão de pagamento.{filtroGridModel.Extensao}");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioDetalhesProvisaoBaixado(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioProvisaoDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _srvPagamentoFrete.GerarRelatorioDetalhesProvisaoBaixado(filtroGridModel.DataInicial, filtroGridModel.DataFinal,
                filtroGridModel.HabilitarPagamentoCartao, filtroGridModel.UF, filtroGridModel.IdEstabelecimento, filtroGridModel.Tipo, filtroGridModel.Order, filtroGridModel.Filters,
                filtroGridModel.Extensao, filtroGridModel.IdEmpresa ?? 0);

            var mimeType = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório detalhes de provisão de pagamento.{filtroGridModel.Extensao}");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object CreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, decimal? valor)
        {
            try
            {
                var response = _pagamentoFreteApp.CreditarAbono(idViagemEventoabono, idViagemEventoSaldo, idProtocolo, _userIdentity, valor);

                return response.IsValid
                    ? ResponderSucesso("Abono creditado com sucesso!", null)
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object NaoCreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, int? idMotivo = null, string descricao = null, int? idUsuario = null)
        {
            try
            {
                if (!idUsuario.HasValue)
                    idUsuario = _userIdentity.IdUsuario;

                var response = _pagamentoFreteApp.NaoCreditarAbono(idViagemEventoabono, idViagemEventoSaldo, idProtocolo, idMotivo, descricao, idUsuario);


                return response.IsValid
                    ? ResponderSucesso("O evento foi bloqueado.", null)
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object AnalisarAbonoCartaFrete(int idViagemEventoSaldo, int idProtocolo, int? idMotivoRejeicao, string descricaoMotivo, EStatusAnaliseAbono statusAnalise)
        {
            try
            {
                var response = _pagamentoFreteApp.AnalisarAbonoCartaFrete(idViagemEventoSaldo, idProtocolo, idMotivoRejeicao, descricaoMotivo, _userIdentity, statusAnalise);

                return response.IsValid
                    ? ResponderSucesso("Abono analisado com sucesso.")
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object ConsultarMotivoAbono(int idViagemEvento)
        {
            try
            {
                var data = _pagamentoFreteApp.ConsultarMotivoAbono(idViagemEvento);

                return ResponderSucesso(data);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorCiot(string ciot)
        {
            try
            {
                return ResponderSucesso(string.Empty, _pagamentoFreteApp.GetByCiot(ciot));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarChavePagamento(string documento, string chave, string tokenPagamento)
        {
            try
            {
                var response =
                    _pagamentoFreteApp.ValidarChavePagamento(_userIdentity.IdEmpresa ?? 0, documento, chave, tokenPagamento);

                return response.IsValid
                    ? ResponderSucesso("Chave válida para pagamento!", null)
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult VerificarCartaoVinculado(string cpfCnpj)
        {
            try
            {
                cpfCnpj = cpfCnpj.OnlyNumbers2();
                return ResponderSucesso(string.Empty,
                    new { possuiCartaoVinculado = _pagamentoFreteApp.VerificarCartaoVinculado(_userIdentity.IdEmpresa ?? 0, cpfCnpj) });
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetBancosFebraban()
        {
            try
            {
                var response = _estabelecimentoBaseContaBancariaApp.GetBancosFebraban();
                return ResponderSucesso(string.Empty, response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult DeletarEventoAbono(int idViagem)
        {
            try
            {
                _pagamentoFreteApp.DeletarEventoAbono(idViagem);
                return ResponderSucesso(string.Empty);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult RelatorioFaturamento(DateTime dataInicial,DateTime datafinal,int? empresaFiltro,int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa)
                    empresaFiltro = _userIdentity.IdEmpresa;
                
                var retorno = _pagamentoFreteApp.RelatorioFaturamento(dataInicial,datafinal,empresaFiltro, take, page, order, filters);

                if (!retorno.Success)
                    return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso(retorno.Value);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao gerar relatório do faturamento no ATS");
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioFaturamento(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FaturamentoGridReportRequestModel>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.EmpresaFiltro = _userIdentity.IdEmpresa;
            
            var retorno = _pagamentoFreteApp
                .GerarRelatorioFaturamento(filtrosGridModel.DataInicial,filtrosGridModel.DataFinal,filtrosGridModel.EmpresaFiltro,
                    filtrosGridModel.Take,filtrosGridModel.Page,filtrosGridModel.Order,filtrosGridModel.Filters,filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(retorno, mimeType, $"Relatório faturamento.{filtrosGridModel.Extensao}");
        }
    }
}

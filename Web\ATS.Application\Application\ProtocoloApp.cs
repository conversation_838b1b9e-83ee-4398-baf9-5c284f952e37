﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Application.Application.Common;
using ATS.Application.Helpers;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.CrossCutting.Reports.Protocolo;
using ATS.Domain.Interface.Service;
using NLog;

namespace ATS.Application.Application
{
    public class ProtocoloApp : AppBase, IProtocoloApp
    {
        private IProtocoloService Service { get; }

        public ProtocoloApp(IProtocoloService protocoloService)
        {
            Service = protocoloService;
        }

        public object ConsultarProtocoloEvento(int idProtocolo)
        {
            return Service.ConsultarProtocoloEvento(idProtocolo);
        }

        public List<ProtocoloEvento> GetProtocolosEventos(List<int> ids)
        {
            return Service.GetProtocolosEventos(ids);
        }
        
        public object GetAnteciopacoes(int? idEmpresa, int ano, int mes)
        {
            return Service.GetAnteciopacoes(idEmpresa, ano, mes);
        }

        public object ConsultarRecebimentoProtocolos(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarRecebimentoProtocolos(take, page, order, filters);
        }

        public byte[] GerarRelatorioGridRecebimentoProtocolo(OrderFilters order, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            return Service.GerarRelatorioGridRecebimentoProtocolo(order, filters, tipoArquivo, logo);
        }

        public object ConsultarGridProtocolos(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarGridProtocolos(take, page, order, filters);
        }

        public PagamentoFreteModel ConsultarPorToken(string token, Usuario usuarioLogado, int? idProtocolo, List<int> idsEstabelecimentosUsuario = null, bool analisado = false)
        {
            var pagamento = Service.ConsultarPorToken(token, usuarioLogado, idsEstabelecimentosUsuario, idProtocolo);

            if (analisado && pagamento.Evento.IdProtocoloEvento.HasValue)
                SetAnalisado(pagamento.Evento.IdProtocoloEvento, analisado);

            return pagamento;
        }

        public object ConsultarEventosOriginal(int idProtocolo, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarEventosOriginal(idProtocolo,  take,  page, order,  filters); 
        }

        public ValidationResult ResolverProtocolo(int idProtocolo, Usuario usuarioLogado)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.ResolverProtocolo(idProtocolo, usuarioLogado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Resolver(int idProtocoloEvento)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.Resolver(idProtocoloEvento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public KeyValuePair<ValidationResult, int?> Add(Protocolo protocolo, int? idUsuario = null)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.Add(protocolo, idUsuario);

                    if (validationResult.Key.IsValid)
                        transaction.Complete();

                    return validationResult;
                }
            }
            catch (Exception e)
            {
                return new KeyValuePair<ValidationResult, int?>(new ValidationResult().Add(e.Message), null);
            }
        }

        public ValidationResult AddOcorrencia(int idProtocoloEvento, int idMotivo, string descricao, Usuario usuarioLogado)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.AddOcorrencia(idProtocoloEvento, idMotivo, descricao, usuarioLogado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Receber(List<int> ids)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var response = Service.Receber(ids);

                    if (response.IsValid)
                        transaction.Complete();

                    return response;
                }
            }
            catch (Exception e)
            {
                    return new ValidationResult().Add(e.Message);
            }
        }

        public object ConsultarDocumentoProtocolo(int idEmpresa, int? idProtocolo)
        {
            return Service.ConsultarDocumentosProtocolo(idEmpresa, idProtocolo);
        }

        public List<string> ConsultarPagamentosPorProtocolo(int idProtocolo)
        {
            return Service.ConsultarPagamentosPorProtocolo(idProtocolo);
        }

        public ValidationResult Update(Protocolo protocolo)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.Update(protocolo);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Protocolo Get(int idProtocolo)
        {
            return Service.Get(idProtocolo);
        }

        public IEnumerable<Protocolo> GetAll()
        {
            return Service.GetAll();
        }

        public List<object> ConsultarDocumentosProtocolo(int idEmpresa, int? idProtocolo)
        {
            return Service.ConsultarDocumentosProtocolo(idEmpresa, idProtocolo);
        }

        public object ConsultarAnexos(int idProtocolo)
        {
            return Service.ConsultarAnexos(idProtocolo);
        }

        public object ConsultarGrid(int? idEstabelecimento, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarGrid(idEstabelecimento, idEmpresa, take, page, order, filters);
        }

        public byte[] GerarRelatorioGrid(int? idEstabelecimento, int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            return Service.GerarRelatorioGrid(idEstabelecimento, idEmpresa, order, filters, tipoArquivo, logo);
        }

        public void Processar(int idProtocolo)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var protocolo = Service.GetComInclude(idProtocolo);
                if (protocolo == null)
                    throw new Exception($"Nenhum protocolo encontrado para o id {idProtocolo}");

                protocolo.Processado = true;

                var validationResult = Service.Update(protocolo);
                if (!validationResult.IsValid)
                    throw new Exception("Não foi possível processar o protocolo-> " + validationResult.ToFormatedMessage());

                transaction.Complete();
            }

        }

        public void AgendarPrevisaoPagamento(int idProtocolo, DateTime dataPrev)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var protocolo = Service.Get(idProtocolo);
                if (protocolo == null)
                    throw new Exception($"Nenhum protocolo encontrado para o id {idProtocolo}");

                if (protocolo.StatusProtocolo != EStatusProtocolo.Aprovado)
                    throw new Exception("Não foi possível agendar o pagamento do protocolo, pois o mesmo não está aprovado.");

                protocolo.DataPrevisaoPagamento = dataPrev;
                protocolo.StatusProtocolo = EStatusProtocolo.AguardandoPagamento;

                var validationResult = Service.Update(protocolo);
                if (!validationResult.IsValid)
                    throw new Exception("Não foi possível processar o protocolo-> " + validationResult.ToFormatedMessage());

                transaction.Complete();
            }
        }

        public object ConsultarTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase, int? idEstabelecimento, int? idAssociacao,
            DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, List<KeyValuePair<int, int>> associacoes, int take,
            int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarTriagemProtocolo(idEmpresa, idsEstabelecimentosBase, idEstabelecimento, idAssociacao, dataGeracaoInicial,
                dataGeracaoFinal, dataPagamentoInicial, dataPagamentoFinal, associacoes, take, page, order, filters);
        }

        public byte[] GerarRelatorioTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo, string logo)
        {
            return Service.GerarRelatorioTriagemProtocolo(idEmpresa, idsEstabelecimentosBase, idEstabelecimento,
                idAssociacao, dataGeracaoInicial, dataGeracaoFinal, dataPagamentoInicial, dataPagamentoFinal,
                associacoesPorEmpresa, order, filters, tipoArquivo, logo);
        }

        public List<Protocolo> ConsultarRelatorioProtocolos(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarRelatorioProtocolos(idEmpresa, take, page, order, filters);
        }

        public object ConsultarTriagemProtocoloAssociacao(int? idEmpresa, List<int> idsEstabelecimentosBase, int? idEstabelecimento, IList<int?> idAssociacao,
            DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, List<KeyValuePair<int, int>> associacoes, int take,
            int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarTriagemProtocoloAssociacao(idEmpresa, idsEstabelecimentosBase, idEstabelecimento, idAssociacao, dataGeracaoInicial,
                dataGeracaoFinal, dataPagamentoInicial, dataPagamentoFinal, associacoes, take, page, order, filters);
        }

        public List<Protocolo> GetProtocolos(List<int> idsProtocolo)
        {
            return Service.GetProtocolos(idsProtocolo);
        }

        public object ConsultarDescontoPorId(int idProtocoloEvento)
        {
            return Service.ConsultarDescontosPorId(idProtocoloEvento);
        }

        public object ConsultarTriagemAntecipacaoProtocolo(int? idEmpresa, List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order,
            List<QueryFilters> filters, Usuario usuarioLogado)
        {
            return Service.ConsultarTriagemAntecipacaoProtocolo(idEmpresa, idEstabelecimentoBase,
                dataGeracaoInicial,
                dataGeracaoFinal, dataSolcitacaoInicial, dataSolicitacaoFinal, status, take, page, order, filters, usuarioLogado);
        }

        public object ConsultarTriagemAntecipacaoOcorrenciasProtocolo(int? idProtocolo, int? idEmpresa, List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            return Service.ConsultarTriagemAntecipacaoOcorrenciaProtocolo(idProtocolo, idEmpresa, idEstabelecimentoBase,
                dataGeracaoInicial,
                dataGeracaoFinal, dataSolcitacaoInicial, dataSolicitacaoFinal, status, take, page, order, filters);
        }

        public List<RelatorioEventosVinculados> ConsultarEventosOriginalReport(int idProtocolo, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarEventosOriginalReport(idProtocolo, take, page, order, filters);
        }

        public List<RelatorioProtocoloOcorrencia> ConsultarTriagemAntecipacaoOcorrenciaProtocoloRelatorio(int? idProtocolo, int take, int page, OrderFilters order,
         List<QueryFilters> filters)
        {
            return Service.ConsultarTriagemAntecipacaoOcorrenciaProtocolo(idProtocolo, take, page, order, filters);
        }

        public void EmAnalise(int idProtocolo)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                   new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                Service.EmAnalise(idProtocolo);
                transaction.Complete();
            }
        }

        public object ConsultarPagamentos(int idProtocolo, int? take, int? page, OrderFilters order, List<QueryFilters> filters)
            => Service.ConsultarPagamentos(idProtocolo, take, page, order, filters);

        public ValidationResult Aprovar(int idProtocolo, DateTime? dataPrevisaoPagamento, int? idUsuarioLogado )
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.Aprovar(idProtocolo, dataPrevisaoPagamento, idUsuarioLogado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult ReverterRejeicao(int idProtocolo)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.ReverterRejeicao(idProtocolo);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }


        public ValidationResult Rejeitar(int idProtocolo, int idMotivo, string detalhamento, Usuario usuarioLogado)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.Rejeitar(idProtocolo, idMotivo, detalhamento, usuarioLogado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult RejeitarPagamento(int idProtocoloEvento, int? idMotivo, string detalhamento, Usuario usuarioLogado)
        {
            return Service.RejeitarPagamento(idProtocoloEvento, idMotivo, detalhamento, usuarioLogado);
        }

        public decimal CalcularTaxaAntecipacao(int idProtocolo, DateTime dataAntecipacao)
        {
            return Service.CalcularTaxaAntecipacao(idProtocolo, dataAntecipacao);
        }

        public ValidationResult EnviarSolicitacaoAntecipacao(int idProtocolo, decimal valorAntecipacao,
            DateTime dataAntecipacao)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validation =
                        Service.EnviarSolicitacaoAntecipacao(idProtocolo, valorAntecipacao, dataAntecipacao);

                    if (!validation.IsValid)
                        return validation;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult AprovarAntecipacao(int idProtocoloAntecipacao)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.AprovarAntecipacao(idProtocoloAntecipacao);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult RejeitarAntecipacao(int idProtocoloAntecipacao, int idMotivo, string detalhamento)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.RejeitarAntecipacao(idProtocoloAntecipacao, idMotivo, detalhamento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Protocolo GetComInclude(int idProtocolo)
        {
            return Service.GetComInclude(idProtocolo);
        }

        public Protocolo GetComEvento(int idProtocolo)
        {
            return Service.GetComEvento(idProtocolo);
        }

        public ValidationResult ReenviarAntecipacao(int idProtocoloAntecipacao, DateTime dataPagamentoAntecipado)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult =
                        Service.ReenviarAntecipacao(idProtocoloAntecipacao, dataPagamentoAntecipado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult RealizarPagamento(int idProtocolo)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                   new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validation = Service.RealizarPagamento(idProtocolo);
                    if (!validation.IsValid)
                        return validation;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult AlterarPesoChegada(int idProtocolo, decimal? pesoChegada, int? numeroSacas, bool hasAbono = false)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                   new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validation = Service.AlterarPesoChegada(idProtocolo, pesoChegada, numeroSacas, hasAbono);
                    if (!validation.IsValid)
                        return validation;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult RealizarDesconto(int idProtocoloEvento, decimal? valorDesconto, decimal? pesoChegada, int? idMotivoDesconto, string observacaoDesconto, string tokenAnexoDesconto)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                       new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = Service.RealizarDesconto(idProtocoloEvento, valorDesconto, pesoChegada, idMotivoDesconto, observacaoDesconto, tokenAnexoDesconto, false);
                    if (!validationResult.IsValid)
                        return new ValidationResult().Add(validationResult.ToString());

                    transaction.Complete();

                    return new ValidationResult();
                }

            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult RemoverDesconto(int idProtocoloEvento)
        {
            try
            {
                using (
                    var transaction = new TransactionScope(TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var resultado = Service.RemoverDesconto(idProtocoloEvento);

                    if (!resultado.IsValid)
                        return new ValidationResult().Add(resultado.Errors.FirstOrDefault()?.Message);

                    transaction.Complete();
                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return new ValidationResult().Add(e.Message);
            }
        }

        //public ValidationResult Liberar(int idProtocolo)
        //{
        //    try
        //    {
        //        using (var transaction = new TransactionScope(TransactionScopeOption.Required,
        //            new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
        //        {
        //            var validationResult = _service.Liberar(idProtocolo);
        //            if (!validationResult.IsValid)
        //                return validationResult;

        //            transaction.Complete();
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        return new ValidationResult().Add(e.Message);
        //    }

        //    return new ValidationResult();
        //}

        public ValidationResult VincularPagamentoProtocolo(int idProtocolo, string token, int idUsuario)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                  new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = Service.VincularPagamentoProtocolo(idProtocolo, token, idUsuario);

                if (!validationResult.IsValid)
                    return validationResult;
                transaction.Complete();

                return validationResult;
            }
        }

        public int GerarProtocoloPorViagemEvento(string token, int idEstabelecimento)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                 new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var idProtocolo = Service.GerarProtocoloPorViagemEvento(token, idEstabelecimento);
                transaction.Complete();

                return idProtocolo;
            }
        }

        public ValidationResult SetAnalisado(int? idProtocoloEvento, bool analisado)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                 new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var idProtocolo = Service.SetAnalisado(idProtocoloEvento, analisado);
                transaction.Complete();

                return idProtocolo;
            }
        }

        public void SetProtocoloAsAprovado(int idProtocolo)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                 new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                 Service.SetProtocoloAsAprovado(idProtocolo);

                transaction.Complete();
            }
        }

        public object ConsultarGridAssociacao(int? idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, int? idAssociacao = 0 )
        {
            return Service
                .ConsultarGridAssociacao(idEstabelecimento, take, page, order, filters, idEmpresa, idAssociacao);
    }

        public byte[] GerarRelatorioEtiquetas(List<int> idsProtocolos, string logo)
        {
            return Service.GerarRelatorioEtiquetas(idsProtocolos, logo);
        }

        public byte[] GerarRelatorioCapa(List<int> idsProtocolos, string logo)
        {
            return Service.GerarRelatorioCapa(idsProtocolos, logo);
        }

        public bool VerificarPagamentosSemProtocolo(int idEstabelecimentoBase)
        {
            return Service.VerificarPagamentosSemProtocolo(idEstabelecimentoBase);
        }

        public object GetDadosAnaliseAbono(int idProtocolo, int idViagemEvento)
        {
            return Service.GetDadosAnaliseAbono(idProtocolo, idViagemEvento);
        }
        public IQueryable<ProtocoloEvento> GetByIdViagemEvento(int idViagemEvento)
        {
            return Service.GetByIdViagemEvento(idViagemEvento);
        }
    }
}

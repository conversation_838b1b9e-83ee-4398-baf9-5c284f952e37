﻿using System;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Base
{
    public abstract class ReportBase : IDisposable
    {
        public  LocalReport LocalReport;

        protected ReportBase()
        {
            if (LocalReport == null)
                LocalReport = new LocalReport();
        }

        public void Dispose()
        {
            LocalReport.Dispose();
        }
    }
}

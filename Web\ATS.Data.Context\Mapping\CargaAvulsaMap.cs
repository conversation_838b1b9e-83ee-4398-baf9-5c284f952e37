﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CargaAvulsaMap  : EntityTypeConfiguration<CargaAvulsa>
    {
        public CargaAvulsaMap()
        {
            ToTable("CARGA_AVULSA");
       
            HasKey(t => t.IdCargaAvulsa);

            Property(t => t.IdCargaAvulsa)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.DataCadastro)
                .HasColumnType("datetime2");

            Property(t => t.Observacao)
                .HasColumnType("text")
                .HasMaxLength(500);
            
            Property(t => t.Arquivo)
                .HasColumnType("text")
                .HasMaxLength(1000000);
            
            HasRequired(t => t.UsuarioCadastro)
                .WithMany(t => t.<PERSON>vu<PERSON>)
                .HasForeignKey(t => t.IdUsuariocadastro);

            HasOptional(c => c.Empresa)
                .WithMany()
                .HasForeignKey(c => c.IdEmpresa);
            
            HasOptional(c => c.Filial)
                .WithMany()
                .HasForeignKey(c => c.IdFilial);
            
            HasOptional(c => c.Viagem)
                .WithMany()
                .HasForeignKey(c => new
                {
                    c.IdViagemProvisionada,
                    c.IdEmpresa
                });
            
            HasOptional(c => c.Motivo)
                .WithMany()
                .HasForeignKey(c => c.IdMotivo);
            
            Property(t => t.NroControleIntegracao)
                .HasMaxLength(100)
                .IsOptional();
            
            Property(t => t.CPFCNPJUsuario)
                .HasMaxLength(14)
                .IsOptional();
            
            Property(t => t.NomeUsuario)
                .HasMaxLength(100)
                .IsOptional();

            Property(o => o.StatusCargaAvulsa)
                .IsRequired();
                
            Property(t => t.NroControleIntegracao)
                .HasMaxLength(100)
                .IsOptional();
            
            Property(o => o.MensagemProcessamento)
                .HasMaxLength(500)
                .IsOptional();
            
            Property(t => t.MensagemAntiFraude)
                .HasMaxLength(500)
                .IsOptional();
            
            Property(o => o.CodigoPlanilhaImportada)
                .IsOptional()
                .HasMaxLength(100);
            
            HasOptional(t => t.PrestacaoContas)
                .WithMany()
                .HasForeignKey(t => t.IdPrestacaoContas);
            
            Property(o => o.MotivoRejeicaoGestor)
                .IsOptional()
                .HasMaxLength(1000);
        }
    }
}

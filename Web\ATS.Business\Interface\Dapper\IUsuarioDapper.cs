using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Dapper
{
    public interface IUsuarioDapper : IRepositoryDapper<Usuario>
    {
        ConsultaInformacoesMobileModel ConsultarInformacoesMobile(int itensPorPagina, int pagina, int? empresaId, string documento);
        ValidationResult UpdatePush(int idUsuario, string idPush, ESistemaOperacional? sistemaOperacional = null);
    }
}
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemEventoProtocoloAnexoMap : EntityTypeConfiguration<ViagemEventoProtocoloAnexo>
    {
        public ViagemEventoProtocoloAnexoMap()
        {
            ToTable("VIAGEM_EVENTO_PROTOCOLO_ANEXO");
            
            HasKey(o => o.IdViagemEventoProtocoloAnexo);

            HasRequired(o => o.ViagemEvento)
                .WithMany(o => o.ViagemEventoProtocoloAnexos)
                .HasForeignKey(o => o.IdViagemEvento);

            Property(o => o.Descricao)
                .IsRequired()
                .HasMaxLength(100);

            Property(o => o.Token)
                .IsRequired()
                .HasMaxLength(100);

            Property(o => o.TamanhoArquivo)
                .IsOptional()
                .HasMaxLength(50);
        }
    }
}
using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.ViagemV2.Request
{
    public class ViagemV2AdicionarEventosRequestModel : RequestBase
    {
        public int? ViagemId { get; set; }

        public string NumeroControle { get; set; }

        public List<ViagemV2AdicionarEventoRequestModel> Eventos { get; set; }
        
        public ValidationResult ValidarEntrada()
        {
            var validation = new ValidationResult();
            
            if ((!ViagemId.HasValue || ViagemId == 0) && string.IsNullOrEmpty(NumeroControle))
                return validation.Add("Deve ser informado o Número de controle ou Viagem Id para prosseguir.", EFaultType.Error);
            
            if (!ViagemId.HasValue || ViagemId == 0)
                return validation.Add("Viagem Id não informado.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(NumeroControle))
                if (NumeroControle.Length > 300)
                    return validation.Add("Número de controle não pode ter mais de 300 caracteres.", EFaultType.Error);

            if (Eventos == null)
                validation.Add("Nenhum evento informado.", EFaultType.Error);
            else
                foreach (var evento in Eventos)
                {
                    if (!evento.ValorPagamento.HasValue || evento.ValorPagamento == 0)
                        validation.Add($"Evento {evento.TipoEvento} não pode ter valor de pagamento zerado.", EFaultType.Error);
                    
                    if (!Enum.IsDefined(typeof(EStatusViagemEvento), evento.Status))
                        validation.Add($"Evento {evento.TipoEvento} com status inválido.", EFaultType.Error);

                    if (!evento.TipoEvento.HasValue)
                        validation.Add("Tipo de evento não informado.", EFaultType.Error);
                    else if (!Enum.IsDefined(typeof(ETipoEventoViagem), evento.TipoEvento))
                        validation.Add("Tipo de evento inválido.", EFaultType.Error);
                }
            
            return validation;
        }
    }

    public class ViagemV2AdicionarEventoRequestModel
    {
        public ViagemV2AdicionarEventoRequestModel()
        {
            ValorPagamento = 0;
            ValorTotalPagamento = 0;
            Status = EStatusViagemEvento.Aberto;
        }

        public string NumeroControle { get; set; }

        public decimal? ValorPagamento { get; set; }

        public decimal? ValorTotalPagamento { get; set; }

        public bool? HabilitarPagamentoCartao { get; set; }
        
        public EStatusViagemEvento Status { get; set; }

        public ETipoEventoViagem? TipoEvento { get; set; }
    }
}
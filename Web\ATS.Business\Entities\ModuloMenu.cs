﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ModuloMenu
    {
        public int IdModulo { get; set; }
        public int IdMenu { get; set; }

        [ForeignKey("IdModulo")]
        public virtual Modulo Modulo { get; set; }

        [ForeignKey("IdMenu")]
        public virtual Menu Menu { get; set; }
    }
}

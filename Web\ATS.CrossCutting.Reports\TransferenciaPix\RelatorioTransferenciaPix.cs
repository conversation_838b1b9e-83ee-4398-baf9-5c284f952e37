﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.TransferenciaPix
{
    public class RelatorioTransferenciaPix
    {
        public byte[] GetReport(object listaDados, string tipoArquivo, string logo)
        {
            try
            {
                var path = ReportUtils.CreateLogo(logo);
                var parametros = new Tuple<string, string, bool>[1];
                parametros[0] = new Tuple<string, string, bool>("Logo", "file:///" + path, true);

                var dataSources = new Tuple<object, string>(listaDados, "DtsTransferenciaPix");

                var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                    "ATS.CrossCutting.Reports.TransferenciaPix.RelatorioTransferenciaPix.rdlc", tipoArquivo);

                return bytes;
            }
            catch (Exception)
            {
                return new byte[0];
            }
        }
    }
}

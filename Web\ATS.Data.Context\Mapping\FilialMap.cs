using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class FilialMap : EntityTypeConfiguration<Filial>
    {
        public FilialMap()
        {
            ToTable("FILIAL");

            HasKey(t => t.IdFilial);

            Property(t => t.IdFilial)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.CNPJ)
                .IsRequired()
                .HasMaxLength(14);

            Property(t => t.RazaoSocial)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.NomeFantasia)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Sigla)
                .IsRequired()
                .HasMaxLength(10);

            Property(t => t.CEP)
                .IsRequired()
                .HasMaxLength(8);

            Property(t => t.Endereco)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Complemento)
                .HasMaxLength(100);

            Property(t => t.<PERSON>rro)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Telefone)
                .IsRequired()
                .HasMaxLength(11);

            Property(t => t.Email)
                .IsRequired()
                .HasMaxLength(100);

            HasRequired(t => t.Pais)
               .WithMany(t => t.Filiais)
               .HasForeignKey(d => d.IdPais);

            HasRequired(t => t.Estado)
               .WithMany(t => t.Filiais)
               .HasForeignKey(d => d.IdEstado);

            HasRequired(t => t.Cidade)
               .WithMany(t => t.Filiais)
               .HasForeignKey(d => d.IdCidade);

            HasRequired(t => t.Empresa)
               .WithMany(t => t.Filiais)
               .HasForeignKey(d => d.IdEmpresa);

            Property(x => x.DataHoraUltimaAtualizacao)
                .IsRequired();

            Property(x => x.Ie)
                .IsOptional();

            Property(x => x.IdSistemaExterno).HasMaxLength(15);

            #region Parâmetros de perimetro (Localização)

            Property(x => x.AlertaArea)
                .IsRequired();

            Property(x => x.Acuracia)
                .IsRequired()
                .HasPrecision(10, 0);


            #endregion

            #region Parâmetros de servidor de e-mail

            Property(x => x.EmailNome)
                .IsOptional()
                .HasMaxLength(50);

            Property(x => x.EmailEndereco)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.EmailPorta)
                .IsOptional()
                .HasPrecision(4, 0);

            Property(x => x.EmailServidor)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.EmailSsl)
                .IsRequired();

            Property(x => x.EmailUsuario)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.EmailSenha)
                .IsOptional()
                .HasMaxLength(100);
            #endregion

        }
    }
}
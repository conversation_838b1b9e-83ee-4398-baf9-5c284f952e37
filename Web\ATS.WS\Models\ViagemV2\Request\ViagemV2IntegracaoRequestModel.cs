using System;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.Viagem;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using ATS.WS.Models.ViagemV2.Integracao;
using NLog;

namespace ATS.WS.Models.ViagemV2.Request
{
    public class ViagemV2IntegracaoRequestModel : RequestBase
    {
        public int? ViagemId { get; set; }
        
        public ViagemV2DadosViagemModel DadosViagem { get; set; }

        
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public virtual ValidationResult ValidarEntrada(bool? validarDocumentosComIntegracoes, bool validarViagemId)
        {
            if (DadosViagem == null)
                return new ValidationResult().Add("Dados da viagem não informados.", EFaultType.Error);

            if (ViagemId.HasValue && validarViagemId)
                return new ValidationResult().Add("Este método permite apenas realizar integração de viagem, para isso o campo Viagem Id não deve estar preenchido. Para realizar a alteração da viagem utilize o método ViagemV2/Alterar", EFaultType.Error);

            var validacaoDadosViagem = DadosViagem.ValidarEntrada(validarDocumentosComIntegracoes ?? false, validarViagemId);

            if (!validacaoDadosViagem.IsValid)
                return validacaoDadosViagem;
            
            if (DadosViagem.CadastrosPreViagem?.ClienteOrigem != null)
            {
                var validacaoClienteOrigem = DadosViagem.CadastrosPreViagem?.ClienteOrigem.ValidarEntrada(ETipoCliente.Origem);

                if (!validacaoClienteOrigem.IsValid)
                    return validacaoClienteOrigem;
            }

            if (DadosViagem.CadastrosPreViagem?.ClienteDestino != null)
            {
                var validacaoClienteDestino = DadosViagem.CadastrosPreViagem?.ClienteDestino.ValidarEntrada(ETipoCliente.Destino);

                if (!validacaoClienteDestino.IsValid)
                    return validacaoClienteDestino;
            }
            
            if (DadosViagem.CadastrosPreViagem?.Proprietario?.Cartao?.NumeroCartao > 0 && DadosViagem.CadastrosPreViagem?.Motorista?.Cartao?.NumeroCartao > 0 
                && DadosViagem.CadastrosPreViagem.Proprietario.Cartao.NumeroCartao == DadosViagem.CadastrosPreViagem.Motorista.Cartao.NumeroCartao)
                return new ValidationResult().Add("Deve ser informado um número de cartão individual para cada, proprietário e motorista", EFaultType.Error); 

            if (DadosViagem.CadastrosPreViagem?.Proprietario != null)
            {
                var validacaoProprietario = DadosViagem.CadastrosPreViagem?.Proprietario.ValidarEntrada();

                if (!validacaoProprietario.IsValid)
                    return validacaoProprietario;
            }

            if (DadosViagem.CadastrosPreViagem?.Motorista != null)
            {
                var validacaoMotorista = DadosViagem.CadastrosPreViagem?.Motorista.ValidarEntrada();

                if (!validacaoMotorista.IsValid)
                    return validacaoMotorista;
            }

            if (DadosViagem.CadastrosPreViagem?.Veiculo != null)
            {
                var validacaoVeiculo = DadosViagem.CadastrosPreViagem?.Veiculo.ValidarEntrada();

                if (!validacaoVeiculo.IsValid)
                    return validacaoVeiculo;
            }

            if (DadosViagem?.Pedagio?.Fornecedor == FornecedorEnum.Outros)
            {
                if(DadosViagem?.Pedagio?.ValorPedagio == null)
                    return new ValidationResult().Add("Informe o valor do pedágio!", EFaultType.Error); 
                
                if(DadosViagem?.Pedagio?.Pracas == null)
                    return new ValidationResult().Add("Informe pelo menos uma praça para a compra com este fornecedor!", EFaultType.Error); 
            }

            return new ValidationResult();
        }

        public virtual ValidationResult<EValidationViagemEstabelecimento> ValidarPreencherAutorizacaoEstabelecimentos(IViagemApp viagemApp)
        {
            var validacao = new ValidationResult<EValidationViagemEstabelecimento>();
            try
            {
                if (DadosViagem.AutorizacaoEstabelecimentos == null)
                    return validacao;
                
                foreach (var autorizacaoEstabelecimento in DadosViagem.AutorizacaoEstabelecimentos)
                {
                    validacao = autorizacaoEstabelecimento.ValidarEntrada(viagemApp, CNPJEmpresa);
                    if (!validacao.IsValid)
                        return validacao;
                }
                
                var repetidos = DadosViagem.AutorizacaoEstabelecimentos.GroupBy(x => new { x.Cnpj, x.TipoEvento })
                    .Where(g => g.Count() > 1)
                    .Select(y => y.Key)
                    .ToList();

                foreach (var repetido in repetidos)
                    validacao.Add(EValidationViagemEstabelecimento.CnpjTipoEventoRepetido, EFaultType.Error,
                        new object[]{repetido.Cnpj, repetido.TipoEvento.GetDescription()});
            }
            catch (Exception e)
            {
                _logger.Error(e);
                validacao.Add(EValidationViagemEstabelecimento.ErroNaoCatalogado, EFaultType.Error, new object[]{e.GetBaseException().Message});
            }
            
            return validacao;
        }
    }
}
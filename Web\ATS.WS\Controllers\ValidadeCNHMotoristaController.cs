﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Controllers.Base;
using System;
using System.Linq;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    // ver se isso é usado ou da pra remover ????????????
    public class ValidadeCnhMotoristaController : BaseController
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly IUsuarioDocumentoApp _usuarioDocumentoApp;

        public ValidadeCnhMotoristaController(BaseControllerArgs baseArgs, IUsuarioApp usuarioApp, IUsuarioDocumentoApp usuarioDocumentoApp) : base(baseArgs)
        {
            _usuarioApp = usuarioApp;
            _usuarioDocumentoApp = usuarioDocumentoApp;
        }

        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public UsuarioDocumento GetDocumentoCnh(Motorista mot)
        {
            //nao tem nenhuma validacao ??????????????
            var usuarioMotorista = _usuarioApp.GetPorCNPJCPF(mot.CPF);
            if (usuarioMotorista != null)
            {
                var usuDocs = _usuarioDocumentoApp.GetDocumentos(usuarioMotorista.IdUsuario);
                if (usuDocs.Any())
                {
                    var docCnhMotorista = usuDocs.FirstOrDefault(x => x.TipoDocumento.Descricao == "CNH");
                    if (docCnhMotorista != null)
                        return docCnhMotorista;
                }
            }

            return null;
        }

        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public bool ValidarRegra(int idMot, UsuarioDocumento docCnhMotorista, Empresa emp)
        {
            //nao tem nenhuma validacao ??????????????
            var menosDias = emp.DiasAntesVencDoc * -1;
            if (idMot > 0 && docCnhMotorista != null)
            {
                if (!docCnhMotorista.AvisoValidade.HasValue)
                    return docCnhMotorista.Validade?.AddDays(menosDias) <= DateTime.Now;
                else
                    return docCnhMotorista.Validade?.AddDays(menosDias) <= DateTime.Now
                           && (DateTime.Now >= new DateTime(docCnhMotorista.AvisoValidade.Value.Year,
                               docCnhMotorista.AvisoValidade.Value.Month,
                               docCnhMotorista.AvisoValidade.Value.Day).AddDays(emp.FreqVencDoc));
            }

            return false;
        }
    }
}
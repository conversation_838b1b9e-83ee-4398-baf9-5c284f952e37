using System;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.Domain.Enum;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class RelatorioConciliacaoDto
    {
        public ConciliacaoItem DadosMeioHomologado { get; set; } = new ConciliacaoItem();
        public RelatorioConciliacaoAtsDto DadosAts { get; set; } = new RelatorioConciliacaoAtsDto();
        public bool ExistenteAts { get; set; }
        public string TipoEvento { get; set; }
        public string TipoTransacaoDescricao => GetTipoTransacaoDescricao();
        public string MensagemRetorno => !string.IsNullOrWhiteSpace(DadosMeioHomologado.MensagemRetorno) ? DadosMeioHomologado.MensagemRetorno : DadosAts.MotivoAts;
        public decimal ValorDecimal
        {
            get
            {
                if (!ExistenteAts || DadosMeioHomologado.TipoProcessadora.In(
                        ConciliacaoItemTipoProcessadora.DepositoEmpresa,
                        ConciliacaoItemTipoProcessadora.TransferenciaViaPixDebito,
                        ConciliacaoItemTipoProcessadora.TransferenciaViaPixCredito,
                        ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa,
                        ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio,
                        ConciliacaoItemTipoProcessadora.PagamentoDeContas,
                        ConciliacaoItemTipoProcessadora.EstornoPagamentoDeContas,
                        ConciliacaoItemTipoProcessadora.ResgateValorDebito,
                        ConciliacaoItemTipoProcessadora.ResgateValorCredito))
                    return DadosMeioHomologado.Valor ?? 0;

                return (DadosAts.IdTransacaoCartao.HasValue || DadosAts.IdViagemEvento.HasValue
                           ? DadosAts.ValorCartao
                           : DadosAts.ValorPedagio)
                       ?? 0;
            }
        }
        public DateTime? DataDt
        {
            get
            {
                if (!ExistenteAts || DadosMeioHomologado.Data.HasValue)
                    return DadosMeioHomologado.Data;

                if (DadosAts.IdTransacaoCartao.HasValue || DadosAts.IdViagemEvento.HasValue)
                    return DadosAts.DataCartao;
                else
                    return DadosAts.DataPedagio;
            }
        }
        public EStatusTransacaoConciliacao StatusAts { get; set; }
        public EStatusTransacaoConciliacao StatusMeioHomologado { get; set; }
        public EStatusTransacaoConciliacao StatusProcessadora { get; set; }
        public EStatusTransacaoConciliacao StatusGeral => GetStatusGeral();

        public RelatorioConciliacaoDto(ConciliacaoItem conciliacaoItem, EStatusTransacaoConciliacao statusProcessadora, EStatusTransacaoConciliacao statusMeioHomologado)
        {
            DadosMeioHomologado = conciliacaoItem;
            StatusMeioHomologado = statusMeioHomologado;
            StatusProcessadora = statusProcessadora;
        }

        public RelatorioConciliacaoDto(RelatorioConciliacaoAtsDto dadosAts, ConciliacaoItemTipoProcessadora tipoProcessadora, string tipoEvento, EStatusTransacaoConciliacao statusAts)
        {
            DadosAts = dadosAts;
            ExistenteAts = DadosAts != null;
            DadosMeioHomologado.TipoProcessadora = tipoProcessadora;
            TipoEvento = tipoEvento;
            StatusAts = statusAts;
            StatusProcessadora = EStatusTransacaoConciliacao.Inexistente;
            StatusMeioHomologado = EStatusTransacaoConciliacao.Inexistente;
        }

        private EStatusTransacaoConciliacao GetStatusGeral()
        {
            if (StatusAts.In(EStatusTransacaoConciliacao.Pendente, EStatusTransacaoConciliacao.Inexistente))
                return StatusAts;

            if (StatusMeioHomologado.In(EStatusTransacaoConciliacao.Pendente, EStatusTransacaoConciliacao.Inexistente))
                return StatusMeioHomologado;

            return StatusProcessadora;
        }

        private string GetTipoTransacaoDescricao()
        {
            if (DadosMeioHomologado?.TipoProcessadora == null)
                return "Inesperado";

            return DadosMeioHomologado.TipoProcessadora switch
            {
                ConciliacaoItemTipoProcessadora.CargaPortador => "Carga",
                ConciliacaoItemTipoProcessadora.EstornoPortador => "Estorno",
                ConciliacaoItemTipoProcessadora.CargaPedagioPortador => "Carga",
                ConciliacaoItemTipoProcessadora.EstornoPedagioPortador => "Estorno",
                ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio => "Resgate",
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa => "Estorno depósito",
                ConciliacaoItemTipoProcessadora.DepositoEmpresa => "Depósito",
                ConciliacaoItemTipoProcessadora.TransferenciaViaPixCredito => "Depósito",
                ConciliacaoItemTipoProcessadora.TransferenciaViaPixDebito => "Carga",
                ConciliacaoItemTipoProcessadora.OutrosDebitos => "Débito",
                ConciliacaoItemTipoProcessadora.OutrosCreditos => "Crédito",
                ConciliacaoItemTipoProcessadora.PagamentoDeContas => "Débito",
                ConciliacaoItemTipoProcessadora.EstornoPagamentoDeContas => "Crédito",
                ConciliacaoItemTipoProcessadora.ResgateValorDebito => "Débito",
                ConciliacaoItemTipoProcessadora.ResgateValorCredito => "Crédito",
                _ => DadosMeioHomologado.TipoProcessadora.ToString()
            };
        }
    }

    public class RelatorioConciliacaoAtsDto
    {
        public bool ItemConciliado { get; set; }
        public int? IdTransacaoCartao { get; set; }
        public decimal? ValorCartao { get; set; }
        public DateTime? DataCartao { get; set; }
        public DateTime? DataConfirmacaoCartao { get; set; }
        public ETipoProcessamentoCartao TipoProcessamentoCartao { get; set; }
        public EStatusPagamentoCartao? StatusPagamento { get; set; }
        public string MensagemProcessamentoWs { get; set; }
        public EOrigemTransacaoCartao OrigemTransacaoCartao { get; set; }
        public int? IdCargaAvulsa { get; set; }
        public string NomeUsuario { get; set; }
        public string DocumentoUsuario { get; set; }
        public int? IdViagem { get; set; }
        public int? IdViagemEvento { get; set; }
        public EStatusViagemEvento StatusEvento { get; set; }
        public ETipoEventoViagem? TipoEventoViagem { get; set; }
        public string NumeroCiot { get; set; }
        public string VerificadorCiot { get; set; }
        public EResultadoCompraPedagio ResultadoCompraPedagio { get; set; }
        public string MensagemCompraPedagio { get; set; }
        public decimal? ValorPedagio { get; set; }
        public DateTime? DataCompraPedagio { get; set; }
        public DateTime? DataCancelamentoPedagio { get; set; }
        public DateTime? DataConfirmacaoCompraPedagio { get; set; }
        public DateTime? DataConfirmacaoCancelamentoPedagio { get; set; }
        public DateTime? DataPedagio { get; set; }
        //Existentes tanto na viagem quanto na carga avulsa
        public string Placa { get; set; }
        public string NumeroRecibo { get; set; }
        public string Filial { get; set; }
        public string MotivoAts { get; set; }
        public ETipoCarga? TipoCarga { get; set; }
    }
}
﻿using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemDocumentoFiscalMap : EntityTypeConfiguration<ViagemDocumentoFiscal>
    {
        public ViagemDocumentoFiscalMap()
        {
            ToTable("VIAGEM_DOCUMENTO_FISCAL");

            HasKey(o => o.IdViagemDocumentoFiscal);

            HasRequired(o => o.Viagem)
                .WithMany(o => o.ViagemDocumentosFiscais)
                .HasForeignKey(o => new { o.IdViagem, o.IdEmpresa });

            Property(o => o.NumeroDocumento)
                .HasPrecision(10, 0)
                .IsOptional();

            Property(o => o.Serie)
                .HasMaxLength(4)
                .IsRequired();
            
            Property(o => o.Chave)
                .HasMaxLength(150)
                .HasColumnType("varchar")
                .IsOptional();

            Property(o => o.PesoSaida)
                .HasPrecision(10, 3)
                .IsRequired();
            
            Property(o => o.Valor)
                .HasPrecision(10, 2)
                .IsOptional();

            Property(o => o.TipoDocumento)
                .IsRequired();
            
            HasOptional(c => c.ClienteOrigem)
                .WithMany()
                .HasForeignKey(t => t.IdClienteOrigem);
            
            HasOptional(c => c.ClienteDestino)
                .WithMany()
                .HasForeignKey(t => t.IdClienteDestino);
        }
    }
}

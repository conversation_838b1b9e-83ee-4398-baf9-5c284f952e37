﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Reflection;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.CrossCutting.Reports.Cartoes.ExtratoConsolidado;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Application.Application
{
    public class ExtratoConsolidadoApp : AppBase, IExtratoConsolidadoApp
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IExtratoConsolidadoService _service;
        private readonly IEmpresaService _empresaService;
        private readonly IUsuarioService _usuarioService;
        private readonly IMotoristaService _motoristaService;
        private readonly IProprietarioService _proprietarioService;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IParametrosApp _parametrosApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;

        public ExtratoConsolidadoApp(IExtratoConsolidadoService service, 
            IUserIdentity userIdentity, 
            IEmpresaService empresaService,
            IUsuarioService usuarioService, 
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, 
            IMotoristaService motoristaService,
            IProprietarioService proprietarioService,
            IParametrosApp parametrosApp, 
            IParametrosEmpresaService parametrosEmpresa)
        {
            _service = service;
            _userIdentity = userIdentity;
            _empresaService = empresaService;
            _usuarioService = usuarioService;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _motoristaService = motoristaService;
            _proprietarioService = proprietarioService;
            _parametrosApp = parametrosApp;
            _parametrosEmpresa = parametrosEmpresa;
        }
        public ExtratoConsolidadoDTOResponse ExtratoConsolidadoGrid(ExtratoConsolidadoDTORequest request)
        {
            request.DocumentosPortadores = request.DocumentosPortadores.Select(c => c.OnlyNumbers()).ToList();
            
            if (request.ModoConsulta == EModoConsultaExtratoConsolidado.ExcluirPortadoresSelecionados)
            {
                var portadoresComCartao = GetPortadoresEmpresaComCartao();
                if (portadoresComCartao == null) throw new InvalidOperationException("Não foi possível obter a listagem de portadores para a consulta.");
                var documentos = portadoresComCartao.ListaPortadores.Select(c => c.CpfCnpj.OnlyNumbers()).ToList();
                request.DocumentosPortadores = documentos.Where(c => !request.DocumentosPortadores.Contains(c)).ToList();
            }
            
            var dados = _service.ExtratoConsolidadoGrid(request);

            return new ExtratoConsolidadoDTOResponse()
            {
                totalItems = dados?.TotalItens ?? 0,
                items = dados?.Itens
            };
        }

        public ExtratoConsolidadoPortadorGridDTOResponse ConsultarPortadorGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var listPortadoresComCartao = GetPortadoresEmpresaComCartao();

            if (listPortadoresComCartao == null) return new ExtratoConsolidadoPortadorGridDTOResponse();
                
            var query = string.IsNullOrWhiteSpace(order?.Campo)
                ? listPortadoresComCartao.ListaPortadores.OrderBy(x => x.Nome).AsQueryable()
                : listPortadoresComCartao.ListaPortadores.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}").AsQueryable();

            var filtroNome = filters?.FirstOrDefault(c => c.Campo.EqualsIgnoreCase("Nome"))?.Valor;
            var filtroDocumento = filters?.FirstOrDefault(c => c.Campo.EqualsIgnoreCase("CpfCnpj"))?.Valor;

            if (filtroNome != null) query = query.Where(c => c.Nome.ToLower().Contains(filtroNome.ToLower()));
            if (filtroDocumento != null) query = query.Where(c => c.CpfCnpj.Contains(filtroDocumento));
            
            var count = query.Count();
            
            var retorno = query
                .Skip((page - 1) * take)
                .Take(take)
                .ToList()
                .Select(c => new ExtratoConsolidadoPortadorGridDTOResponseItem()
                {
                    CpfCnpj = c.CpfCnpj.FormatarCpfCnpj(),
                    Nome = c.Nome,
                })
                .ToList();


            return new ExtratoConsolidadoPortadorGridDTOResponse()
            {
                items = retorno,
                totalItems = count
            };
        }
        
        private RelatorioPortadorCartaoResponse GetPortadoresEmpresaComCartao()
        {
            var idEmpresa = _userIdentity.IdEmpresa;
            var idUsuario = _userIdentity.IdUsuario;
            var empresa = _empresaService.Get(idEmpresa ?? 0,null);

            if (empresa == null) return null;
            
            var usuarioLogado = _usuarioService.Get(idUsuario, true);
            
            var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuarioLogado);
            
            var listaPortadores = new RelatorioPortadorCartaoRequest
            {
                ListDocumentoPortador = new List<string>()
            };
            
            var portadoresMotorista = _motoristaService
                .GetAllByIdEmpresa(empresa.IdEmpresa)
                .Where(x => x.Ativo && x.TipoContrato == ETipoContrato.Frota)
                .Select(x => x.CPF)
                .ToList();
            
            var portadoresProprietario = _proprietarioService
                .GetAllByIdEmpresa(empresa.IdEmpresa)
                .Where(x => x.Ativo && x.TipoContrato == ETipoContrato.Frota)
                .Select(x => x.CNPJCPF)
                .ToList();

            listaPortadores.ListDocumentoPortador.AddRange(portadoresMotorista);
            
            listaPortadores.ListDocumentoPortador.AddRange(portadoresProprietario);
            
            return cartoesApp.RelatorioPortadorCartaoPorListaDocumento(listaPortadores);
        }

        public byte[] GerarRelatorioExtratoConsolidadoGrid(ExtratoConsolidadoDTORequest request)
        {
            if (!_userIdentity.IdEmpresa.HasValue)
                throw new InvalidOperationException("Empresa não encontrada.");
            
            var mostrarHeaderArquivoCsv = true;
            var separadorArquivoCsv = string.Empty;

            if (request.Extensao == EExtensaoArquivoRelatorio.Csv)
            {
                mostrarHeaderArquivoCsv = _parametrosApp.GetMostrarHeaderArquivoCsv(_userIdentity.IdEmpresa.Value) == true;
                separadorArquivoCsv = _parametrosApp.GetSeparadorArquivoCsv(_userIdentity.IdEmpresa.Value);
                if (string.IsNullOrEmpty(separadorArquivoCsv)) separadorArquivoCsv = ";";
            }

            var empresa = _empresaService.Get(_userIdentity.IdEmpresa.Value, null);

            var logo = empresa?.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);

            var dateHelper = new DateTimeHelper();

            request.DataInicial = dateHelper.StartOfDay(request.DataInicial);
            request.DataFinal = dateHelper.EndOfDay(request.DataFinal);
            request.Relatorio = true;
            
            var dados = ExtratoConsolidadoGrid(request);

            if (request.Extensao == EExtensaoArquivoRelatorio.Csv)
            {
                var csvBuilder = new CsvBuilderHelper(separadorArquivoCsv, mostrarHeaderArquivoCsv);

                foreach (var item in dados.items)
                {
                    csvBuilder.AddRow();
                    csvBuilder["Portador"] = item.Portador;
                    csvBuilder["PortadorDocumento"] = item.PortadorDocumento;
                    csvBuilder["DataTransacao"] = item.DataTransacaoString;
                    csvBuilder["Valor"] = item.ValorConvertidoString;
                    csvBuilder["Tipo"] = item.DebitoCreditoDescricao;
                    csvBuilder["PlanoVendas"] = item.DescricaoPlano;
                    csvBuilder["Estabelecimento"] = item.Estabelecimento;
                    csvBuilder["NumeroCartao"] = item.NumeroCartao;
                    csvBuilder["Identificador"] = item.Identificador;
                    csvBuilder["MCC"] = item.DescricaoMCC;
                }

                return csvBuilder.ExportToBytes();
            }

            if (request.Extensao == EExtensaoArquivoRelatorio.Ofx)
            {
                if (!_parametrosEmpresa.GetUtilizaRelatoriosOfx(_userIdentity.IdEmpresa.Value)) return new byte[] { };
                
                var req = new OfxReportRequest
                {
                    DataFim = request.DataFinal,
                    DataInicio = request.DataInicial,
                    Itens = new List<OfxReportRequestItem>(),
                    ContaParametrizado = _parametrosApp.GetCodigoOfx(empresa?.IdEmpresa ?? 0)
                };

                foreach (var item in dados.items)
                {
                    var detalhes = item.DescricaoPlano;
                    if (!string.IsNullOrWhiteSpace(item.DescricaoMCC)) detalhes += $" | {item.DescricaoMCC}";

                    var ofxItem = new OfxReportRequestItem()
                    {
                        Valor = item.DebitoCredito == "D" ? -item.ValorConvertido ?? 0 : item.ValorConvertido ?? 0,
                        DataHoraTransacao = item.DataTransacao ?? DateTime.Now,
                        Detalhes = detalhes,
                        Nome = item.Portador,
                    };
                    
                    req.Itens.Add(ofxItem);
                }

                return OfxHelper.GenerateOfxResponse(req);
            }

            var relatorio = new RelatorioExtratoConsolidado().GetReport(request.Extensao.DescriptionAttr(), 
                dados.items, logo);
            return relatorio;
        }
    }
}

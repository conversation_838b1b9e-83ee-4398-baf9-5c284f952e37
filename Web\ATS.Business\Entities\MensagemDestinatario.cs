﻿using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class MensagemDestinatario
    {
        /// <summary>
        /// <PERSON><PERSON><PERSON> da tabela
        /// </summary>
        public int IdMensagemDestinatario { get; set; }

        /// <summary>
        /// Id do usuário de destino
        /// </summary>
        public int IdUsuarioDestinatario { get; set; }

        /// <summary>
        /// Id da mensagem
        /// </summary>
        public int IdMensagem { get; set; }

        public DateTime? DataHoraLido { get; set; }


        #region Referência

        public virtual Usuario UsuarioDestinatario { get; set; }
        public virtual Mensagem MensagemDestino { get; set; }
        #endregion
    }   
}

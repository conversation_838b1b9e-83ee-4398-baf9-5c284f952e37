using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class AdministradoraPlataformaService : BaseService<IAdministradoraPlataformaRepository>, IAdministradoraPlataformaService
    {
        public AdministradoraPlataformaService(IAdministradoraPlataformaRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public IQueryable<AdministradoraPlataforma> Get(int idAdministradoraPlataforma)
        {
            return Repository.Where(c => c.IdAdministradoraPlataforma == idAdministradoraPlataforma);
        }
    }
}
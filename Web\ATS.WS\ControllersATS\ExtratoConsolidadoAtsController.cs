﻿using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO;
using ATS.Domain.Grid;
using Newtonsoft.Json;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.ControllersATS
{
    public class ExtratoConsolidadoAtsController : DefaultController
    {
        private readonly IExtratoConsolidadoApp _extratoConsolidadoApp;
        private readonly IUserIdentity _userIdentity;

        public ExtratoConsolidadoAtsController(IExtratoConsolidadoApp extratoConsolidadoApp, IUserIdentity userIdentity)
        {
            _extratoConsolidadoApp = extratoConsolidadoApp;
            _userIdentity = userIdentity;
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioExtratoConsolidadoGrid(string json)
        {
            if (!_userIdentity.IdEmpresa.HasValue || _userIdentity.IdEmpresa == 0)
                throw new InvalidOperationException("Usuário sem empresa vinculada.");

            var request = JsonConvert.DeserializeObject<ExtratoConsolidadoDTORequest>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            var report = _extratoConsolidadoApp.GerarRelatorioExtratoConsolidadoGrid(request);

            return File(report, ConstantesUtils.ExcelOldMimeType, $"Relatório do extrato.{request.Extensao}");
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ExtratoConsolidadoGrid(ExtratoConsolidadoDTORequest request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue || _userIdentity.IdEmpresa == 0)
                    throw new InvalidOperationException("Usuário sem empresa vinculada.");
            
                if (request.ModoConsulta == EModoConsultaExtratoConsolidado.IncluirPortadoresSelecionados &&
                    (request.DocumentosPortadores == null || request.DocumentosPortadores.Count == 0))
                    return ResponderSucesso(null);
            
                var dados = _extratoConsolidadoApp.ExtratoConsolidadoGrid(request);

                return ResponderSucesso(dados);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPortadorGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue || _userIdentity.IdEmpresa == 0)
                    throw new InvalidOperationException("Usuário sem empresa vinculada.");
                
                return ResponderSucesso(_extratoConsolidadoApp.ConsultarPortadorGrid(take,page,order,filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using ATS.WS.Models.Webservice.Response.Estabelecimento;
using ATS.WS.Services;
using AutoMapper;
using Newtonsoft.Json;
using NLog;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using EstabelecimentoProduto = ATS.Domain.Entities.EstabelecimentoProduto;

namespace ATS.WS.ControllersATS
{
    public class EstabelecimentoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly IParametrosApp _parametrosApp;
        private readonly SrvEstabelecimento _srvEstabelecimento;
        private readonly ICredenciamentoApp _credenciamentoApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEstabelecimentoService _estabelecimentoService;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IEstabelecimentoBaseProdutoApp _estabelecimentoBaseProdutoApp;
        private readonly IUsuarioService _usuarioService;
        private readonly IEstabelecimentoContaBancariaApp _estabelecimentoContaBancariaApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public EstabelecimentoAtsController(IUserIdentity userIdentity, IEstabelecimentoBaseService estabelecimentoBaseService, IParametrosApp parametrosApp, 
            SrvEstabelecimento srvEstabelecimento, ICredenciamentoApp credenciamentoApp, IUsuarioApp usuarioApp, IEstabelecimentoService estabelecimentoService, IEstabelecimentoApp estabelecimentoApp,
            IEstabelecimentoBaseProdutoApp estabelecimentoBaseProdutoApp, IUsuarioService usuarioService, IEstabelecimentoContaBancariaApp estabelecimentoContaBancariaApp,
            IEmpresaRepository empresaRepository, CartoesAppFactoryDependencies cartoesAppFactoryDependencies)
        {
            _userIdentity = userIdentity;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _parametrosApp = parametrosApp;
            _srvEstabelecimento = srvEstabelecimento;
            _credenciamentoApp = credenciamentoApp;
            _usuarioApp = usuarioApp;
            _estabelecimentoService = estabelecimentoService;
            _estabelecimentoApp = estabelecimentoApp;
            _estabelecimentoBaseProdutoApp = estabelecimentoBaseProdutoApp;
            _usuarioService = usuarioService;
            _estabelecimentoContaBancariaApp = estabelecimentoContaBancariaApp;
            _empresaRepository = empresaRepository;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var filtrarApenasCredenciados = false;
                var filtroApenasCredenciados = filters?.FirstOrDefault(o => o.Campo == "filtrarApenasCredenciadosEmpresa");

                if (filtroApenasCredenciados != null)
                {
                    filtrarApenasCredenciados = Convert.ToBoolean(filtroApenasCredenciados.Valor);
                    filters.Remove(filtroApenasCredenciados);
                }

                var estabelecimentos = _estabelecimentoService
                    .ConsultaGrid(idEmpresa, idTipoEstabelecimento, descricao, take, page, order, filters, filtrarApenasCredenciados);

                return ResponderSucesso(estabelecimentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarEstabelecimentosRota(ConsultarEstabelecimentosRotaRequest @params)
        {
            try
            {
                return ResponderSucesso(_srvEstabelecimento.ConsultarEstabelecimentosRota(@params));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(
                        e,
                        $"Erro ao consultar estabelcimento por rota. " +
                        $"CNPJEmpresa: {@params.CNPJEmpresa} - " +
                        $"Origem: {@params.LatitudeOrigem}, {@params.LongitudeOrigem} - " +
                        $"Destino: {@params.LatitudeDestino}, {@params.LongitudeDestino}");
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarEstabelecimentosCadastroRota(ConsultarEstabelecimentosRotaRequest @params)
        {
            try
            {
                return ResponderSucesso(_srvEstabelecimento.ConsultarEstabelecimentosRota(@params, true));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(
                        e,
                        $"Erro ao consultar estabelcimento por rota. " +
                        $"CNPJEmpresa: {@params.CNPJEmpresa} - " +
                        $"Origem: {@params.LatitudeOrigem}, {@params.LongitudeOrigem} - " +
                        $"Destino: {@params.LatitudeDestino}, {@params.LongitudeDestino}");
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(EstabelecimentoCrud estabelecimentoCrud)
        {
            var validationResult = new ValidationResult();
            var estabelecimentoApp = _estabelecimentoApp;
            try
            {
                validationResult.Add(ValidarPropriedades(estabelecimentoCrud));
                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());

                var estabelecimento = Mapper.Map<EstabelecimentoCrud, Estabelecimento>(estabelecimentoCrud);
                if (estabelecimentoCrud.Produtos != null && estabelecimentoCrud.Produtos.Any())
                {
                    estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();
                    foreach (var item in estabelecimentoCrud.Produtos)
                    {
                        if (item.Descricao.Length > 100)
                            ResponderErro("Descrição dos produtos devem possuir no máximo 100 caracteres. ");

                        var produto = new EstabelecimentoProduto
                        {
                            Descricao = item.Descricao,
                            UnidadeMedida = item.UnidadeMedida,
                            Contrato = item.Contrato
                        };

                        decimal precoPromocional;
                        decimal precoUnitario;
                        if (!string.IsNullOrWhiteSpace(item.PrecoPromocional) && decimal.TryParse(item.PrecoPromocional, out precoPromocional))
                            produto.PrecoPromocional = precoPromocional;
                        if (!string.IsNullOrWhiteSpace(item.PrecoUnidade) && decimal.TryParse(item.PrecoUnidade, out precoUnitario))
                            produto.PrecoUnitario = precoUnitario;

                        if (item.IdProdutoBase > 0)
                        {
                            produto.IdEstabelecimentoBase = item.IdEstabelecimentoBase;
                            produto.IdProdutoBase = item.IdProdutoBase;
                        }
                        estabelecimento.EstabelecimentoProdutos.Add(produto);
                    }
                }

                estabelecimento.EstabelecimentoAssociacoesEstabelecimento = new List<EstabelecimentoAssociacao>();
                if (estabelecimentoCrud.Associacoes != null)
                {
                    foreach (var associacao in estabelecimentoCrud.Associacoes)
                    {
                        estabelecimento.EstabelecimentoAssociacoesEstabelecimento.Add(new EstabelecimentoAssociacao
                        {
                            IdAssociacao = associacao.IdEstabelecimento
                        });
                    }
                }

                var estabelecimentoBaseApp = new EstabelecimentoBaseApp(_estabelecimentoBaseService);
                var idEstabelecimentoBase = estabelecimentoBaseApp.GetIdByCnpj(estabelecimento.CNPJEstabelecimento);
                if (idEstabelecimentoBase > 0)
                    estabelecimento.IdEstabelecimentoBase = idEstabelecimentoBase;
                
                if(estabelecimentoCrud.RealizarCredenciamento && !estabelecimento.IdEstabelecimentoBase.HasValue)
                {
                    var estabelecimentoBase = Mapper.Map<EstabelecimentoCrud, EstabelecimentoBase>(estabelecimentoCrud);
                    estabelecimentoBase.IdEstabelecimento = 0;
                    foreach (var estabelecimentoBaseAssociacao in estabelecimentoBase.AssociacoesBaseEstabelecimento)
                        estabelecimentoBaseAssociacao.IdAssociacao = estabelecimentoApp.GetIdBase(estabelecimentoBaseAssociacao.IdAssociacao) ?? 0;
                    
                    validationResult.Add(estabelecimentoBaseApp.Add(estabelecimentoBase));
                    if(validationResult.IsValid)
                        estabelecimento.IdEstabelecimentoBase = estabelecimentoBase.IdEstabelecimento;
                }

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());

                validationResult = estabelecimentoApp.Add(estabelecimento);

                if (validationResult.IsValid)
                    AfterInsertOrUpdate(estabelecimento);

                var resultadoCadastroValidacaoChavePagamento =
                    _parametrosApp.SetEstabelecimentoValidarChavePagamento(estabelecimento.IdEstabelecimento, estabelecimentoCrud.ValidarChavePagamento ?? false);

                if (!resultadoCadastroValidacaoChavePagamento.IsValid)
                    validationResult.Add(
                        "Não foi possível gravar o parâmetro 'Validar chave de pagamento', edite o estabelecimento e tente novamente, se o problema persistir entre em contato com o suporte.");
                
                var credenciamentoApp = _credenciamentoApp;
                if (estabelecimentoCrud.RealizarCredenciamento && estabelecimento.IdEstabelecimentoBase.HasValue && !credenciamentoApp.EstabelecimentoCredenciado(estabelecimento.IdEstabelecimento, estabelecimento.IdEmpresa))
                {
                    var credenciamento = new Credenciamento(estabelecimento.IdEmpresa, estabelecimento.IdEstabelecimentoBase, estabelecimento.IdEstabelecimento,
                        EStatusCredenciamento.Enviado, EStatusDocumentacaoCredenciamento.Regular);

                    if (estabelecimentoCrud.Documentos?.Any() == true)
                    {
                        credenciamento.CredenciamentoAnexo = new List<CredenciamentoAnexo>();
                        estabelecimentoCrud.Documentos.ForEach(doc =>
                        {
                            DateTime? dataValidade = null;
                            if (doc.DataValidade.Contains("-") && doc.DataValidade.Length >= 10)
                                dataValidade = DateTime.ParseExact(doc.DataValidade.Substring(0, 10), "yyyy-MM-dd", CultureInfo.InvariantCulture);
                            else if(doc.DataValidade.Contains("/") && doc.DataValidade.Length >= 10)
                                dataValidade = DateTime.ParseExact(doc.DataValidade.Substring(0, 10), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                            
                            credenciamento.CredenciamentoAnexo.Add(new CredenciamentoAnexo
                            {
                                Descricao = doc.Descricao,
                                Token = doc.Token,
                                DataValidade = dataValidade,
                                IdDocumento = doc.IdDocumento
                            });
                        });
                    }

                    validationResult.Add(credenciamentoApp.Add(credenciamento, estabelecimentoCrud.AdministradoraPlataforma));

                    if (!_parametrosApp.GetRealizaTriagemEstabelecimentoInterno(estabelecimento.IdEmpresa) && validationResult.IsValid)
                        validationResult.Add(credenciamentoApp.AprovarCredenciamento(credenciamento.IdCredenciamento, WebConfigurationManager.AppSettings.Get("LINK_WEB_NOVO"), estabelecimentoCrud.AdministradoraPlataforma));
                }

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToFormatedMessage())
                    : ResponderSucesso("Cadastro realizado com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return ResponderErro(e.GetBaseException().Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(EstabelecimentoCrud estabelecimentoCrud)
        {
            try
            {
                if (!estabelecimentoCrud.IdEstabelecimento.HasValue)
                    return ResponderErro("Id do estabelecimento é obrigatório.");

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_estabelecimentoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, estabelecimentoCrud.IdEstabelecimento.Value))
                        return ResponderErro("Registro não encontrado.");
                }

                var validationResult = ValidarPropriedades(estabelecimentoCrud);
                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());

                var estabelecimentoApp = _estabelecimentoApp;
                var estabelecimento = estabelecimentoApp.Get(estabelecimentoCrud.IdEstabelecimento.Value);

                ReflectionHelper.CopyProperties(estabelecimentoCrud, estabelecimento);

                if (estabelecimentoCrud.HoraInicialSemValidarChave.HasValue)
                    estabelecimento.HoraInicialSemValidarChave = estabelecimentoCrud.HoraInicialSemValidarChave.Value;
                else
                    estabelecimento.HoraInicialSemValidarChave = null;

                if (estabelecimento.PontoReferencia.HasValue)
                    estabelecimento.PontoReferencia = estabelecimentoCrud.PontoReferencia;

                if (estabelecimentoCrud.HoraFinalSemValidarChave.HasValue)
                    estabelecimento.HoraFinalSemValidarChave = estabelecimentoCrud.HoraFinalSemValidarChave.Value;
                else
                    estabelecimento.HoraFinalSemValidarChave = null;

                if (estabelecimentoCrud.Produtos != null && estabelecimentoCrud.Produtos.Any())
                {
                    foreach (var item in estabelecimentoCrud.Produtos)
                    {
                        if (item.Descricao?.Length < 1 || item.Descricao?.Length > 100)
                            ResponderErro("Descrição dos produtos devem possuir entre 1 e 100 caracteres. ");

                        //Caso possua id do produto, significa que está editando um deles.
                        if (item.IdProduto > 0)
                        {
                            var produtoExistente =
                                estabelecimento.EstabelecimentoProdutos?.FirstOrDefault(
                                    x => x.IdProduto == item.IdProduto);
                            if (produtoExistente != null)
                            {
                                if (produtoExistente.Descricao != item.Descricao)
                                    produtoExistente.Descricao = item.Descricao;

                                decimal precoPromocional;
                                decimal precoUnitario;
                                if (!string.IsNullOrWhiteSpace(item.PrecoPromocional) && decimal.TryParse(item.PrecoPromocional, out precoPromocional))
                                    produtoExistente.PrecoPromocional = precoPromocional;
                                if (!string.IsNullOrWhiteSpace(item.PrecoUnidade) && decimal.TryParse(item.PrecoUnidade, out precoUnitario))
                                    produtoExistente.PrecoUnitario = precoUnitario;
                                if (produtoExistente.UnidadeMedida != item.UnidadeMedida)
                                    produtoExistente.UnidadeMedida = item.UnidadeMedida;
                                if (produtoExistente.Contrato != item.Contrato)
                                    produtoExistente.Contrato = item.Contrato;
                                if (item.IdProdutoBase > 0)
                                {
                                    produtoExistente.IdProdutoBase = item.IdProdutoBase;
                                    produtoExistente.IdEstabelecimentoBase = item.IdEstabelecimentoBase;
                                }

                            }
                        }
                        else
                        {
                            var produtoNovo = new EstabelecimentoProduto
                            {
                                Descricao = item.Descricao,
                                UnidadeMedida = item.UnidadeMedida,
                                Contrato = item.Contrato
                            };

                            decimal precoPromocional;
                            decimal precoUnitario;
                            if (!string.IsNullOrWhiteSpace(item.PrecoPromocional) && decimal.TryParse(item.PrecoPromocional, out precoPromocional))
                                produtoNovo.PrecoPromocional = precoPromocional;
                            if (!string.IsNullOrWhiteSpace(item.PrecoUnidade) && decimal.TryParse(item.PrecoUnidade, out precoUnitario))
                                produtoNovo.PrecoUnitario = precoUnitario;

                            if (item.IdProdutoBase > 0)
                            {
                                produtoNovo.IdProdutoBase = item.IdProdutoBase;
                                produtoNovo.IdEstabelecimentoBase = item.IdEstabelecimentoBase;
                            }
                            if (estabelecimento.EstabelecimentoProdutos == null)
                                estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();

                            estabelecimento.EstabelecimentoProdutos.Add(produtoNovo);
                        }
                    }
                }

                if (!estabelecimentoCrud.Associacao)
                {
                    estabelecimento.LiberaProtocolos = false;
                    estabelecimento.PermiteAlterarDocumentosPesoChegada = false;
                }

                //_estabelecimentoService.ExcluirAssociados(estabelecimento.IdEstabelecimento);

                estabelecimento.EstabelecimentoAssociacoesEstabelecimento = new List<EstabelecimentoAssociacao>();
                if (estabelecimentoCrud.Associacoes != null && !estabelecimentoCrud.Associacao)
                {
                    foreach (var associacao in estabelecimentoCrud.Associacoes)
                    {
                        estabelecimento.EstabelecimentoAssociacoesEstabelecimento.Add(new EstabelecimentoAssociacao
                        {
                            IdAssociacao = associacao.IdEstabelecimento
                        });
                    }
                }

                var produtosExclusao = estabelecimentoCrud.Produtos == null ? estabelecimento.EstabelecimentoProdutos?.ToList() : estabelecimento.EstabelecimentoProdutos
                    ?.Where(x => !estabelecimentoCrud.Produtos?.Any(y => y.IdProduto == x.IdProduto) == true).ToList();

                var estabelecimentoBaseApp = new EstabelecimentoBaseApp(_estabelecimentoBaseService);
                if (!estabelecimento.IdEstabelecimentoBase.HasValue)
                {
                    var idEstabelecimentoBase = estabelecimentoBaseApp.GetIdByCnpj(estabelecimento.CNPJEstabelecimento);
                    if (idEstabelecimentoBase > 0)
                        estabelecimento.IdEstabelecimentoBase = idEstabelecimentoBase;
                }

                if (estabelecimentoCrud.RealizarCredenciamento)
                {
                    if(!estabelecimento.IdEstabelecimentoBase.HasValue)
                    {
                        var estabelecimentoBase = Mapper.Map<EstabelecimentoCrud, EstabelecimentoBase>(estabelecimentoCrud);
                        estabelecimentoBase.IdEstabelecimento = 0;
                        foreach (var estabelecimentoBaseAssociacao in estabelecimentoBase.AssociacoesBaseEstabelecimento)
                            estabelecimentoBaseAssociacao.IdAssociacao = estabelecimentoApp.GetIdBase(estabelecimentoBaseAssociacao.IdAssociacao) ?? 0;
                        
                        validationResult.Add(estabelecimentoBaseApp.Add(estabelecimentoBase));
                        if(validationResult.IsValid)
                            estabelecimento.IdEstabelecimentoBase = estabelecimentoBase.IdEstabelecimento;
                    }

                    var credenciamentoApp = _credenciamentoApp;
                    if (estabelecimento.IdEstabelecimentoBase.HasValue && !credenciamentoApp.EstabelecimentoCredenciado(estabelecimento.IdEstabelecimento, estabelecimento.IdEmpresa))
                    {
                        var credenciamento = new Credenciamento(estabelecimento.IdEmpresa, estabelecimento.IdEstabelecimentoBase, estabelecimento.IdEstabelecimento,
                            EStatusCredenciamento.Enviado, EStatusDocumentacaoCredenciamento.Regular);
                        
                        if (estabelecimentoCrud.Documentos?.Any() == true)
                        {
                            credenciamento.CredenciamentoAnexo = new List<CredenciamentoAnexo>();
                            estabelecimentoCrud.Documentos.ForEach(doc =>
                            {
                                DateTime? dataValidade = null;
                                if (doc.DataValidade.Contains("-") && doc.DataValidade.Length >= 10)
                                    dataValidade = DateTime.ParseExact(doc.DataValidade.Substring(0, 10), "yyyy-MM-dd", CultureInfo.InvariantCulture);
                                else if(doc.DataValidade.Contains("/") && doc.DataValidade.Length >= 10)
                                    dataValidade = DateTime.ParseExact(doc.DataValidade.Substring(0, 10), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                                
                                credenciamento.CredenciamentoAnexo.Add(new CredenciamentoAnexo
                                {
                                    Descricao = doc.Descricao,
                                    Token = doc.Token,
                                    DataValidade = dataValidade,
                                    IdDocumento = doc.IdDocumento
                                });
                            });
                        }
                        
                        validationResult.Add(credenciamentoApp.Add(credenciamento, estabelecimentoCrud.AdministradoraPlataforma));

                        if (!_parametrosApp.GetRealizaTriagemEstabelecimentoInterno(estabelecimento.IdEmpresa) && validationResult.IsValid)
                            validationResult.Add(credenciamentoApp.AprovarCredenciamento(credenciamento.IdCredenciamento, WebConfigurationManager.AppSettings.Get("LINK_WEB_NOVO"), estabelecimentoCrud.AdministradoraPlataforma));
                    }

                    if (!validationResult.IsValid)
                        return ResponderErro(validationResult.ToFormatedMessage());
                }
                
                validationResult.Add(estabelecimentoApp.Update(estabelecimento, produtosExclusao));

                AfterInsertOrUpdate(estabelecimento);
                
                var resultadoCadastroValidacaoChavePagamento =
                    _parametrosApp.SetEstabelecimentoValidarChavePagamento(estabelecimento.IdEstabelecimento, estabelecimentoCrud.ValidarChavePagamento ?? false);

                if (!resultadoCadastroValidacaoChavePagamento.IsValid)
                    validationResult.Add(
                        "Não foi possível gravar o parâmetro 'Validar chave de pagamento', edite o estabelecimento e tente novamente, se o problema persistir entre em contato com o suporte.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());
                
                if (validationResult.Alerts.Any())
                    return ResponderAtencao(true, validationResult.ToString());
                
                return ResponderSucesso("Dados alterados com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idEstabelecimento)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_estabelecimentoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idEstabelecimento))
                        return ResponderErro("Registro não encontrado.");
                }

                var estabelecimento = _estabelecimentoApp.Get(idEstabelecimento);
                var estabelecimentoBase = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(estabelecimento.CNPJEstabelecimento);
                var estabelecimentoNaoCredenciado = !estabelecimento.IdEstabelecimentoBase.HasValue || !_credenciamentoApp.EstabelecimentoCredenciado(estabelecimento.IdEstabelecimento, estabelecimento.IdEmpresa);

                var idsProdutosBase = estabelecimentoBase?.EstabelecimentoBaseProdutos?
                    .Select(x => x.IdProduto)
                    .ToList();

                var produtosBase = idsProdutosBase != null ? _estabelecimentoBaseProdutoApp.GetProdutos(idsProdutosBase) : new List<EstabelecimentoBaseProduto>();

                var parametroValidarChavePagamento = _parametrosApp.GetEstabelecimentoValidarChavePagamento(idEstabelecimento);
                var validarChavePagamento = parametroValidarChavePagamento ?? estabelecimento.Empresa.ValidaChaveMHBaixaEvento;

                var retorno = new ConsultarCadastroModel
                {
                    IdEmpresa = estabelecimento.IdEmpresa,
                    RazaoSocial = estabelecimento.Empresa?.RazaoSocial,
                    IdTipoEstabelecimento = estabelecimento.IdTipoEstabelecimento,
                    CNPJEstabelecimento = estabelecimento.CNPJEstabelecimento,
                    DescricaoTipo = estabelecimento.TipoEstabelecimento.Descricao,
                    Descricao = estabelecimento.Descricao,
                    TaxaAntecipacao = estabelecimento.TaxaAntecipacao,
                    Email = estabelecimento.Email,
                    IdEstabelecimento = estabelecimento.IdEstabelecimento,
                    IdPais = estabelecimento.IdPais,
                    IdEstado = estabelecimento.IdEstado,
                    IdCidade = estabelecimento.IdCidade,
                    CEP = estabelecimento.CEP,
                    PagamentoAntecipado = estabelecimento.PagamentoAntecipado,
                    PontoReferencia = estabelecimento.PontoReferencia,
                    Latitude = estabelecimento.Latitude,
                    Longitude = estabelecimento.Longitude,
                    Logradouro = estabelecimento.Logradouro,
                    Bairro = estabelecimento.Bairro,
                    RazaoSocialEstab = estabelecimento.RazaoSocial,
                    Numero = estabelecimento.Numero,
                    Complemento = estabelecimento.Complemento,
                    Telefone = estabelecimento.Telefone,
                    ObrigaDocumentosPagamento = estabelecimento.ObrigaDocumentosPagamento,
                    Produtos = estabelecimento.EstabelecimentoProdutos?.Select(x => new Produtos
                    {
                        IdProduto = x.IdProduto,
                        IdProdutoBase = x.IdProdutoBase,
                        Descricao = x.Descricao,
                        UnidadeMedida = x.UnidadeMedida,
                        PrecoUnidade = x.PrecoUnitario.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoUnitario).Substring(3) : string.Empty,
                        PrecoPromocional = x.PrecoPromocional.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoPromocional).Substring(3) : string.Empty,
                        Contrato = x.Contrato,
                        IdEstabelecimentoBase = x.IdEstabelecimentoBase
                    })
                    .ToList(),
                    ProdutosBase = produtosBase?.Select(x => new ProdutoBase
                    {
                        IdProduto = x.IdProduto,
                        Descricao = x.Descricao,
                        UnidadeMedida = x.UnidadeMedida,
                        PrecoUnidade = x.PrecoUnitario.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoUnitario).Substring(3) : string.Empty,
                        PrecoPromocional = x.PrecoPromocional.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoPromocional).Substring(3) : string.Empty,
                    })
                    .ToList(),
                    Associados = estabelecimento.EstabelecimentoAssociacoesEstabelecimento?.Select(x => new Associacoes
                    {
                        IdEstabelecimento = x.IdAssociacao,
                        Descricao = x.Associacao.Descricao
                    }).ToList(),
                    Associacao = estabelecimento.Associacao,
                    LiberaProtocolos = estabelecimento.LiberaProtocolos,
                    PermiteAlterarDocumentosPesoChegada = estabelecimento.PermiteAlterarDocumentosPesoChegada,
                    HoraInicialSemValidarChave = estabelecimento.HoraInicialSemValidarChave,
                    HoraFinalSemValidarChave = estabelecimento.HoraFinalSemValidarChave,
                    RealizaPagamentoComCheque = estabelecimento.RealizaPagamentoComCheque,
                    IdFilialProcessoCaixaTms = estabelecimento.IdFilialProcessoCaixaTms,
                    EstabelecimentoNaoCredenciado = estabelecimentoNaoCredenciado,
                    ValidarChavePagamento = validarChavePagamento
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idEstabelecimento)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_estabelecimentoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idEstabelecimento))
                        return ResponderErro("Registro não encontrado.");
                }

                var validationResult = _estabelecimentoApp.Inativar(idEstabelecimento);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Estabelecimento inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idEstabelecimento, string cpfUsuario, int? idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_estabelecimentoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idEstabelecimento))
                        return ResponderErro("Registro não encontrado.");
                }

                var validationResult = _estabelecimentoApp.Reativar(idEstabelecimento);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Estabelecimento reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetEstabelecimentoBase(string cnpj)
        {
            try
            {
                var estabelecimento = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(cnpj);

                return ResponderSucesso(new
                {
                    estabelecimento?.Logradouro,
                    estabelecimento?.Bairro,
                    estabelecimento?.CEP,
                    estabelecimento?.Descricao,
                    estabelecimento?.Email,
                    estabelecimento?.IdCidade,
                    estabelecimento?.IdEstado,
                    estabelecimento?.IdPais,
                    estabelecimento?.IdTipoEstabelecimento,
                    estabelecimento?.Numero,
                    estabelecimento?.Latitude,
                    estabelecimento?.Longitude,
                    DescricaoTipoEstabelecimento = estabelecimento?.TipoEstabelecimento?.Descricao,
                    Produtos = estabelecimento?.EstabelecimentoBaseProdutos?.Select(x => new
                    {
                        IdProdutoBase = x.IdProduto,
                        x.IdEstabelecimentoBase,
                        x.Descricao,
                        x.UnidadeMedida,
                        PrecoUnidade = x.PrecoUnitario.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoUnitario).Substring(3) : string.Empty,
                        PrecoPromocional = x.PrecoPromocional.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoPromocional).Substring(3) : string.Empty,
                        Contrato = false

                    }),
                    ProdutosBase = estabelecimento?.EstabelecimentoBaseProdutos?.Select(x => new
                    {
                        x.IdProduto,
                        x.IdEstabelecimentoBase,
                        x.Descricao,
                        x.UnidadeMedida,
                        PrecoUnidade = x.PrecoUnitario.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoUnitario).Substring(3) : string.Empty,
                        PrecoPromocional = x.PrecoPromocional.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoPromocional).Substring(3) : string.Empty,
                        Contrato = false
                    })
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAssociacoes(int? idEmpresa, int? idEstabelecimento, bool onlyEmpresa = false)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento && !onlyEmpresa)
                {

                    idEstabelecimento = _usuarioApp.GetComEstabelecimentos(_userIdentity.IdUsuario)
                        .UsuarioEstabelecimentos?.FirstOrDefault()?.IdEstabelecimento;
                }

                if (!idEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa;

                var associacoes = _estabelecimentoService.GetAssociacoes(idEmpresa ?? 0, idEstabelecimento, _userIdentity.IdUsuario, onlyEmpresa)
                    .Select(x => new
                    {
                        x.IdEstabelecimento,
                        x.Descricao
                    });

                Estabelecimento estab = null;
                if(idEstabelecimento.HasValue)
                    estab = _estabelecimentoApp.Get(_estabelecimentoService.GetByBase(idEstabelecimento.Value) ?? 0);

                return ResponderSucesso(new
                {
                    isAssociacao = estab?.Associacao,
                    isEstabSemFiliados = !estab?.EstabelecimentoAssociacoesEstabelecimento?.Any(),
                    associacoes
                });
            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro("Não foi possível consultar os estabelecimentos não associados. ");
            }
        }


        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAssociados(int? idEstabelecimento)
        {
            try
            {
                var idEstabelecimentoUsuario = _usuarioService.GetUsuariosEstabelecimento(_userIdentity.IdUsuario)?.FirstOrDefault()?.IdEstabelecimento;
                var associacoes = _estabelecimentoService.GetAssociados(idEstabelecimento.HasValue ? idEstabelecimento.Value : idEstabelecimentoUsuario ?? 0)
                    .Select(x => new
                    {
                        x.IdEstabelecimento,
                        x.Descricao
                    }).ToList();


                return ResponderSucesso(associacoes);
            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro("Não foi possível consultar os estabelecimentos não associados. ");
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAssociacoesEmpresa(int? idEmpresa, int? idEstabelecimento)
        {
            try
            {
                var associacoes = _estabelecimentoService.GetAssociacoesEmpresa(idEmpresa ?? 0, idEstabelecimento).Select(x => new
                {
                    x.IdEstabelecimento,
                    x.Descricao
                });

                return ResponderSucesso(associacoes);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetUsuarioLogadoAssociacoes()
        {
            try
            {
                //var usuario = _usuarioApp.Get(usuarioLogado.IdUsuario)?.
                //var estabelecimentos = _estabelecimentoService.GetAssociados(usuarioLogado.IdUsuario)
                //    .Select(x => new
                //    {
                //        x.IdEstabelecimento,
                //        x.Descricao
                //    });

                return ResponderSucesso(new List<int>());
            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro("Não foi possível consultar os estabelecimentos não associados. ");
            }
        }

        private ValidationResult ValidarPropriedades(EstabelecimentoCrud estabelecimentoCrud)
        {
            var retorno = new ValidationResult();
            if (!estabelecimentoCrud.IdEmpresa.HasValue)
                retorno.Add("Empresa é obrigatória.");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.Descricao))
                retorno.Add("Descrição é obrigatória.");
            if (!estabelecimentoCrud.IdTipoEstabelecimento.HasValue)
                retorno.Add("Icone é obrigatório.");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.CEP))
                retorno.Add("CEP é obrigatório.");
            if (!estabelecimentoCrud.IdPais.HasValue)
                retorno.Add("País é obrigatório.");
            if (!estabelecimentoCrud.IdEstado.HasValue)
                retorno.Add("Estado é obrigatório.");
            if (!estabelecimentoCrud.IdCidade.HasValue)
                retorno.Add("Cidade é obrigatória.");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.Bairro))
                retorno.Add("Bairro é obrigatório. ");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.Logradouro))
                retorno.Add("Logradouro é obrigatório.");

            return retorno;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAssociacoes(int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {

                if (filters == null)
                    filters = new List<QueryFilters>();

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    filters.Add(new QueryFilters
                    {
                        Campo = "IdEmpresa",
                        CampoTipo = EFieldTipo.Number,
                        Operador = EOperador.Exact,
                        Valor = Convert.ToString(_userIdentity.IdEmpresa)
                    });

                var estabelecimentos = _estabelecimentoApp
                    .ConsultarAssociacoes(idTipoEstabelecimento, descricao, take, page, order, filters);

                return ResponderSucesso(estabelecimentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPorViagem(string tokenViagemEvento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {

                var estabelecimentos = _estabelecimentoApp.GetEstabelecimentosPorViagem(tokenViagemEvento, take, page, order, filters);
                return ResponderSucesso(estabelecimentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetDadosBancarios(int idEstabelecimento)
        {
            try
            {
                return ResponderSucesso(string.Empty,
                    _estabelecimentoContaBancariaApp.GetContasBancariasByEstabalecimento(idEstabelecimento));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        private void AfterInsertOrUpdate(Estabelecimento model)
        {
            if (!model.Ativo)
                return;
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, model.IdEmpresa, usuario.IdUsuario, false);
                var result = cartoesApp.IntegrarPessoaMicroServico(model);

                if(result.Status == IntegrarPessoaResponseStatus.Falha)
                    LogManager.GetCurrentClassLogger().Error($"Erro ao integrar estabelecimento no micro serviço de meio homologado. Erro: {result.Mensagens.FirstOrDefault()?.Message}");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao integrar estabelecimento no micro serviço de meio homologado");
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GerarRelatorioGridEstabelecimento(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            var report = _srvEstabelecimento.GerarRelatorioGridEstabelecimento(filtrosGridModel.IdEmpresa, null, string.Empty, filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de estabelecimento.{filtrosGridModel.Extensao}");
        }
    }
}

using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Domain.Models;
using ValidationResult = ATS.Domain.Validation.ValidationResult;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Usuario
    {
        /// <summary>
        /// Código do usuário
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int? IdEmpresa { get; set; }

        /// <summary>
        /// Nome do usuário
        /// </summary>
        public string Nome { get; set; }

        /// <summary>
        /// Login
        /// </summary>
        public string Login { get; set; }

        /// <summary>
        /// Senha
        /// </summary>
        public string Senha { get; set; }

        /// <summary>
        /// CPF/CNPJ do usuário
        /// </summary>
        public string CPFCNPJ { get; set; }

        /// <summary>
        /// Có<PERSON> da configuração do grupo de usuário
        /// </summary>
        public int? IdGrupoUsuario { get; set; }

        /// <summary>
        /// Template Web
        /// </summary>
        public string Template { get; set; } = "Default";

        /// <summary>
        /// Imagem do usuário
        /// </summary>
        [SkipTracking]
        public byte[] Foto { get; set; }

        /// <summary>
        /// Perfil do tipo de usuário
        /// </summary>
        public EPerfil Perfil { get; set; }

        /// <summary>
        /// Ativo
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Tipo cobrança
        /// </summary>
        public ETipoCobranca TipoCobranca{ get; set; } = ETipoCobranca.PessoaFisica;

        /// <summary>
        /// Permitir ou não responder chat
        /// </summary>
        public bool PermiteResponderChat { get; set; } = false;

        /// <summary>
        /// Código do Push (Envio de notificações para o celular)
        /// </summary>
        [SkipTracking]
        public string IdPush { get; set; }

        /// <summary>
        /// Especifica se o usuário deverá receber as notificações
        /// </summary>
        public bool ReceberNotificacao { get; set; } = false;

        /// <summary>
        /// Número de Registro Nacional de Empresas Rodoviárias de Cargas
        /// </summary>
        public string RNTRC { get; set; }

        /// <summary>
        /// Informa se o usuário é carreteiro
        /// </summary>
        public bool Carreteiro { get; set; }

        /// <summary>
        /// Data de cadastro do usuário
        /// </summary>
        public DateTime DataCadastro { get; set; }

        /// <summary>
        /// Data do último acesso via web
        /// </summary>
        [SkipTracking]
        public DateTime? DataUltimoAcessoWeb { get; set; }

        /// <summary>
        /// Data do último acesso via aplicativo
        /// </summary>
        [SkipTracking]
        public DateTime? DataUltimoAcessoAplicativo { get; set; }

        /// <summary>
        /// Data da última vez que o usuário abriu o aplicativo
        /// </summary>
        public DateTime? DataUltimaAberturaAplicativo { get; set; }

        /// <summary>
        /// Id do usuário do facebook caso exista
        /// </summary>
        public long? IdFacebook { get; set; }

        public int? IdPonto { get; set; }

        /// <summary>
        /// Defini se o usuário é do tipo Gestor
        /// </summary>
        public bool Gestor { get; set; }

        /// <summary>
        /// Campo de token do firebase para mobile
        /// </summary>
        public string TokenFirebase { get; set; }

        /// <summary>
        /// Field to save link for facebook image.
        /// </summary>
        public string FotoURL { get; set; }

        /// <summary>
        /// Define se o usuário é um vistoriador
        /// </summary>
        public bool Vistoriador { get; set; } = false;

        /// <summary>
        /// Número da CNH
        /// </summary>
        public string CNH { get; set; }

        /// <summary>
        /// Categoria da CNH
        /// </summary>
        public string CNHCategoria { get; set; }

        /// <summary>
        /// Valida da CNH do motorista
        /// </summary>
        public DateTime? ValidadeCNH { get; set; }

        /// <summary>
        /// Número do RG
        /// </summary>
        public string RG { get; set; }

        /// <summary>
        /// Orgão Expedidor do RG
        /// </summary>
        public string RGOrgaoExpedidor { get; set; }

        public string Referencia1 { get; set; }

        public string Referencia2 { get; set; }

        public ETipoCliente? TipoCliente { get; set; }

        public string CpfCnpjDesabilitado { get; set; }

        public string LoginDesabilitado { get; set; }

        public int? UsoAppEmpresa { get; set; }

        /// <summary>
        /// Informa qual o horário cadastrado para o Usuário a partir do QRA
        /// </summary>
        public int? IdHorario { get; set; }

        public DateTime? DataContatado { get; set; }

        public EStatusContatado StatusContatado { get; set; }

        public bool ReceberRelatorioOC { get; set; } = false;

        public bool? Matriz { get; set; } = false;
        public bool RecebeEmailGestao { get; set; } = false;
        
        public string NomeMae { get; set; }
        
        public string NomePai { get; set; }
        
        public DateTime? DataNascimento { get; set; }

        public bool? MostrarVideosTreinamento { get; set; }

        /// <summary>
        /// Usuário vistoriador master checklist
        /// </summary>
        public bool VistoriadorMaster { get; set; } = false;

        public bool VisualizaChecklistTodosGrupos { get; set; } = true;

        /// <summary>
        /// Indica se o usuário logado utiliza Android ou iOS (utilizado para o pushservice)
        /// </summary>
        public ESistemaOperacional? SistemaOperacional { get; set; }

        public bool VisualizaTodosChecklists { get; set; } = true;

        public int? QuantidadeErroSenha { get; set; }

        #region Referências

        /// <summary>
        /// Empresa vinculado ao usuário
        /// </summary>
        public virtual Empresa Empresa { get; set; }

        /// <summary>
        /// Grupo de usuário vinculado ao usuário
        /// </summary>
        public virtual GrupoUsuario GrupoUsuario { get; set; }
        
        #endregion

        #region Tabelas Filhas

        /// <summary>
        /// Veículos
        /// </summary>
        public virtual ICollection<Veiculo> Veiculos { get; set; }

        /// <summary>
        /// Filiais
        /// </summary>
        public virtual ICollection<UsuarioFilial> Filiais { get; set; }

        /// <summary>
        /// Viagem solicitação de abono
        /// </summary>
        public virtual ICollection<ViagemSolicitacaoAbono> ViagemSolicitacoesAbono { get; set; }

        public virtual ICollection<ViagemEvento> ViagemEventos { get; set; }

        /// <summary>
        /// Endereços
        /// </summary>
        public virtual ICollection<UsuarioEndereco> Enderecos { get; set; }

        /// <summary>
        /// Contatos
        /// </summary>
        public virtual ICollection<UsuarioContato> Contatos { get; set; }

        /// <summary>
        /// Clientes
        /// </summary>

        public virtual ICollection<UsuarioCliente> Clientes { get; set; }

        /// <summary>
        /// Horários configurados para realização dos CheckIns
        /// </summary>
        public virtual ICollection<UsuarioHorarioCheckIn> HorariosCheckIn { get; set; }

        /// <summary>
        /// Horários configurados para realização de notificação de novas cargas do usuário
        /// </summary>
        public virtual ICollection<UsuarioEstabelecimento> UsuarioEstabelecimentos { get; set; }
        public virtual ICollection<Motorista> Motoristas { get; set; }

        /// <summary>
        /// Lista de documentos atreladas ao motorista
        /// </summary>
        public virtual ICollection<UsuarioDocumento> Documentos { get; set; }

        /// <summary>
        /// Indica que o usuário é o gerenciador do estabelecimento
        /// </summary>
        public bool? UsuarioMasterEstabelecimento { get; set; }

        #endregion

        #region Navegação Inversa

        public virtual ICollection<ViagemEvento> EventosRejeitados { get; set; }

        public virtual ICollection<Protocolo> Protocolos { get; set; }

        /// <summary>
        /// Checks realizadas pelo usuário
        /// </summary>
        public virtual ICollection<ViagemCheck> ViagemChecks { get; set; }

        public virtual ICollection<ConjuntoEmpresa> ConjuntosEmpresa { get; set; }

        /// <summary>
        /// CheckIns
        /// </summary>
        public virtual ICollection<CheckIn> CheckIns { get; set; }

        /// <summary>
        /// Mensagens onde o usuário é remetente
        /// </summary>
        public virtual ICollection<Mensagem> Remetente { get; set; }

        /// <summary>
        /// AuthSession que pertencem a este usuário
        /// </summary>
        public virtual ICollection<AuthSession> AuthsSession { get; set; }

        /// <summary>
        /// Mensagens onde o usuário é destinatário
        /// </summary>
        public virtual ICollection<MensagemDestinatario> Destinatarios { get; set; }

        public virtual ICollection<MensagemGrupoDestinatario> UsuariosMembrosGrupoUsuario { get; set; }

        /// <summary>
        /// Notificações deste empresa
        /// </summary>
        public virtual ICollection<Notificacao> Notificacoes { get; set; }

        public virtual ICollection<UsuarioPreferencias> UsuarioPreferencias { get; set; }
        public virtual ICollection<LogSms> LogSms { get; set; }
        public virtual ICollection<CargaAvulsa> CargasAvulsas { get; set; }
        public virtual ICollection<ContratoCiotAgregado> ContratoCiotAgregados { get; set; }

        #region Hash Novo
        /// <summary>
        /// Código chave para transações
        /// </summary>
        public string KeyCodeTransaction { get; set; }
        #endregion

        public virtual ICollection<ViagemEvento> EventosBaixaCheque { get; set; }

        public virtual ICollection<ProtocoloEvento> UsuariosAnaliseAbono { get; set; }

        public ICollection<ViagemEvento> ViagemEventoUsuariosBaixa { get; set; }

        public virtual ICollection<ProtocoloEvento> UsuariosOcorrencia { get; set; }
        
        public virtual ICollection<CheckinResumo> CheckinResumos { get; set; }
        
        public virtual ICollection<UsuarioPermissoesConcedidasMobile> PermissoesConcedidasMobile { get; set; }
        
        public virtual ICollection<CampanhaResposta> CampanhaRespostas { get; set; }
        
        public virtual ICollection<BannerUsuario> BannersUsuario { get; set; }
        
        public virtual ICollection<LocalizacaoUsuarioPortal> LocalizacaoUsuarioPortal { get; set; }
        
        public virtual ICollection<Banner> BannersCadastrados { get; set; }
        
        public virtual ICollection<Banner> BannersAtivados { get; set; }
        
        public virtual ICollection<Banner> BannersDesativados { get; set; }
        public virtual ICollection<PlanoEmpresa> PlanosEmpresa { get; set; }
        public virtual ICollection<Plano> Planos { get; set; }
        #endregion

        #region Propriedades

        /// <summary>
        /// Retorna se o usuário esta ativo
        /// </summary>
        /// <returns></returns>
        public ValidationResult IsValid()
        {
            if (!Ativo)
                return new ValidationResult().Add("Usuário desativado.");

            return new ValidationResult();
        }

        #endregion

    
    }
}

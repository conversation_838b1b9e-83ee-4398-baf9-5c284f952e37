﻿using System;
using ATS.Domain.Entities.Base;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class WhiteListIP
    {
        public int Id { get; set; }
        public string IPV4 { get; set; }
        public DateTime DataCadastro { get; set; }
        public int? IdEmpresa { get; set; }
        public bool IsInterno { get; set; }
        public bool IsParceiro { get; set; }

        public virtual Empresa Empresa { get; set; }
    }
}
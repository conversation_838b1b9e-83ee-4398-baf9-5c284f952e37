using ATS.Data.Context.Migrations.Seeders;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Migrations;
using System.Linq;

namespace ATS.Data.Context.Migrations
{
    internal sealed class Configuration : DbMigrationsConfiguration<AtsContext>
    {
        public Configuration()
        {
            // Nunca habilite essas propriedades!
            AutomaticMigrationsEnabled = false;
            AutomaticMigrationDataLossAllowed = false;
            TargetDatabase = new DbConnectionInfo("csATS");
        }

        protected override void Seed(AtsContext context)
        {
            // new LayoutCartaoSeeder()
            //     .Execute(context);
            
            new VeiculoDigitosMercosulSeeder().Execute(context);

            //new PaisSeeder()
            //    .Execute(context);

            //new EstadoSeeder()
            //    .Execute(context);

            //new CidadeSeeder()
            //    .Execute(context);

            // new ModuloSeeder()
            //     .Execute(context);

            ////if (!context.Menu.Any())
            //    new MenuSeeder()
            //        .Execute(context);

            //new TipoCombustivelSeeder()
            //    .Execute(context);

            //new TipoCavaloSeeder()
            //    .Execute(context);

            //new TipoCarretaSeeder()
            //    .Execute(context);

            //new EspecieSeeder()
            //    .Execute(context);

            //new SolicitacaoAcessoSeeder()
            //   .Execute(context);

            //new TipoEntradaDigitalSeeder()
            //    .Execute(context);

            ////new TipoEstabelecimentoSeeder()
            ////    .Execute(context);

            //new TecnologiaRastreamentoSeeder()
            //    .Execute(context);

            //#region Grupo de Usuario

            //context.GrupoUsuario.AddOrUpdate(x => new { x.Descricao }, new GrupoUsuario { Descricao = "Administrador" });

            //#endregion

            //#region Grupo de Usuario Menu

            //var gruposUsuarioMenu = new List<GrupoUsuarioMenu>();

            //foreach (var menu in context.Menu.Where(x => x != null))
            //{
            //    gruposUsuarioMenu.Add(new GrupoUsuarioMenu { IdGrupoUsuario = GetGrupoUsuarioPorDescricao(context, "Administrador") ?? 0, IdMenu = menu.IdMenu });
            //}

            //gruposUsuarioMenu.ForEach(grupoUsuarioMenu => context.GrupoUsuarioMenu.AddOrUpdate(p => new { p.IdGrupoUsuario, p.IdMenu }, grupoUsuarioMenu));

            //#endregion

            //#region Usuário

            //if (!context.Usuario.Any())
            //    context.Usuario.AddOrUpdate(new Usuario { IdUsuario = 1, Nome = "Sistema Info", Login = "admin", Senha = "cbf2bd29dcd36fa3284fe8fd048a3440", Perfil = EPerfil.Administrador, CPFCNPJ = "85135606000197", ReceberNotificacao = false, DataCadastro = DateTime.Now });

            //#endregion

            context.SaveChanges();
        }

        private int? GetGrupoUsuarioPorDescricao(AtsContext context, string descricaoGrupo)
        {
            return context.GrupoUsuario.FirstOrDefault(x => x.Descricao == descricaoGrupo)?.IdGrupoUsuario;
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System.Linq.Dynamic;
using System.Data.Entity;

namespace ATS.Domain.Service
{
    public class ProdutoService : ServiceBase, IProdutoService
    {
        private readonly IProdutoRepository _produtoRepository;

        public ProdutoService(IProdutoRepository produtoRepository)
        {
            _produtoRepository = produtoRepository;
        }

        public ValidationResult Add(Produto produto)
        {
            try
            {
                var et = _produtoRepository.FirstOrDefault(x => x.Descricao == produto.Descricao && x.IdEmpresa == produto.IdEmpresa);
                if (et != null)
                    return new ValidationResult().Add($"O produto '{produto.Descricao}' já foi cadastrado na base de dados com o id {et.IdProduto}!");

                _produtoRepository.Add(produto);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public ValidationResult Inativar(int idProduto)
        {
            try
            {
                var produtoRepository = _produtoRepository;

                var produto = produtoRepository
                    .Get(idProduto);
                produto.Ativo = false;
                produtoRepository.Update(produto);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public List<Produto> ConsultarPorEmpresa(int emp)
        {
            return _produtoRepository.Where(x => x.Ativo && x.IdEmpresa == emp).ToList();
        }

        public ValidationResult Reativar(int idProduto)
        {
            try
            {
                var produtoRepository = _produtoRepository;

                var produto = produtoRepository
                    .Get(idProduto);
                produto.Ativo = true;
                produtoRepository.Update(produto);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public object ConsultaGrid(int? idEmpresa, int? idProduto, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var produtos = _produtoRepository
                .GetAll()
                .Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                produtos = produtos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idProduto.HasValue)
                produtos = produtos.Where(x => x.IdProduto == idProduto.Value);

            if (!string.IsNullOrWhiteSpace(descricao))
                produtos = produtos.Where(x => x.Descricao.Contains(descricao));

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                produtos = produtos.OrderBy(x => x.IdProduto);
            else
                produtos = produtos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            produtos = produtos.AplicarFiltrosDinamicos<Produto>(filters);

            return new
            {
                totalItems = produtos.Count(),
                items = produtos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdProduto,
                    x.Descricao,
                    x.Ativo,
                    Empresa = x.Empresa?.RazaoSocial
                })
            };
        }

        public Produto Get(int idProduto)
        {
            return _produtoRepository
                .Include(p => p.Empresa)
                .FirstOrDefault(o => o.IdProduto == idProduto);
        }

        public ValidationResult Update(Produto produto)
        {
            try
            {
                _produtoRepository.Update(produto);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                var mensagem = e.Message + e.InnerException?.Message ?? "";
                return new ValidationResult().Add(mensagem);
            }
        }

        public Produto GetById(int idProduto)
        {
            return _produtoRepository.FirstOrDefault(o => o.IdProduto == idProduto);
        }
    }
}

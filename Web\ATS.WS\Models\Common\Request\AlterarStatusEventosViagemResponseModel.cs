using System.Collections.Generic;

namespace ATS.WS.Models.Common.Request
{
    public class AlterarStatusEventosViagemResponseModel
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public IList<AlterarStatusEventosViagemEventoResponseModel> ViagemEventos { get; set; }
    }

    public class AlterarStatusEventosViagemEventoResponseModel
    {
        public int Linha { get; set; }
        public int IdViagemEvento { get; set; }
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }   
    }
}
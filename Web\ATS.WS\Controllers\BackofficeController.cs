using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO.Backoffice;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Webservice.Request;
using System;
using System.Web.Mvc;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class BackofficeController : BaseController
    {
        private readonly IUsuarioApp App;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;


        public BackofficeController(BaseControllerArgs baseArgs, IUsuarioApp app, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            App = app;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [AutorizarIntegracao(origempermitida: EOrigemRequisicao.ApenasInterno)]
        [Expor(EApi.Integracao)]
        public JsonResult AlterarSenhaUsuario(AlterarSenhaUsuarioRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var resultado = App.AlterarSenha(request.IdUsuario, request.NovaSenha, false);

                return Responde(new AlterarSenhaUsuarioResponse
                {
                    Sucesso = true,
                    Mensagem = string.Empty
                });
            }
            catch (Exception e)
            {
                return Responde(new AlterarSenhaUsuarioResponse
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }
    }
}
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class BloqueioCartaoTipoMap : EntityTypeConfiguration<BloqueioCartaoTipo>
    {
        public BloqueioCartaoTipoMap()
        {
            ToTable("BLOQUEIO_CARTAO_TIPO");

            HasKey(t => t.IdBloqueioCartaoTipo);
            Property(t => t.IdBloqueioCartaoTipo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            
            Property(t => t.Descricao).IsRequired();
        }
    }
}
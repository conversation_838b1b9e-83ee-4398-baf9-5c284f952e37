﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class GrupoUsuarioMenuRepository : Repository<GrupoUsuarioMenu>, IGrupoUsuarioMenuRepository
    {
        public GrupoUsuarioMenuRepository(AtsContext context) : base(context)
        {
        }
        
        public ValidationResult DeletePorIdGrupoUsuario(int idGrupoUsuario)
        {
            ICollection<GrupoUsuarioMenu> grupoUsuarioMenus =
                    Find(x => x.IdGrupoUsuario == idGrupoUsuario).ToList();

            if (grupoUsuarioMenus.Any())
            {
                foreach (var grupoUsuarioMenuBase in grupoUsuarioMenus)
                    Delete(grupoUsuarioMenuBase);
            }

            return new ValidationResult();
        }
    }
}
﻿using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class DocumentoRepository : Repository<Documento>, IDocumentoRepository
    {
        public DocumentoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null)
        {
            var query = Context.Set<PagamentoConfiguracaoProcesso>()
                .Include(p => p.Configuracao)
                .Where(p => p.Configuracao.Ativo)
                .Where(p => p.Processo == EProcessoPgtoFrete.Credenciamento);

            if (idsEmpresa?.Any() == true)
                query = query.Where(p => idsEmpresa.Contains(p.Configuracao.IdEmpresa));

            if (idsDocumentoIgnorar?.Any() == true)
                query = query.Where(p => !idsDocumentoIgnorar.Contains(p.IdDocumento));

            var documentosDeCredenciamentoIdList = query.Select(p => p.IdDocumento).Distinct();

            var result = GetAll()
                .Where(x => x.Ativo && documentosDeCredenciamentoIdList.Any(a => a == x.IdDocumento));

            return result;
        }
    }
}

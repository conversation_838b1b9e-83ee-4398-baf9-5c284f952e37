﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class EstadoRepository : Repository<Estado>, IEstadoRepository
    {
        public EstadoRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado para consultar Estado.
        /// </summary>
        /// <param name="nome">Nome de Estado</param>
        /// <returns>IQueryable de Estado</returns>
        public IQueryable<Estado> Consultar(string nome)
        {
            return (from estado in All()
                    where estado.Nome.Contains(nome)
                    orderby estado.IdEstado descending
                    select estado);
        }
    }
}
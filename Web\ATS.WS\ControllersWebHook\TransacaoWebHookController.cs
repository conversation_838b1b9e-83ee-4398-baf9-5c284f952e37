﻿using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.NotificacaoWebhook;

namespace ATS.WS.ControllersWebHook
{
    public class TransacaoWebHookController : DefaultController
    {
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;

        public TransacaoWebHookController(ITransacaoCartaoApp transacaoCartaoApp)
        {
            _transacaoCartaoApp = transacaoCartaoApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [IgnoreAuthSessionValidation]
        public JsonResult TransacaoPendenteReprocessada(TransacaoPendenteReprocessadaNotificacao request)
        {
            if (request == null)
                return ResponderErro("Transação não informada");

            if (request.ProtocoloRequisicao == 0)
                return ResponderErro("Protocolo da transação nao informado");

            var transacaoCartaoApp = _transacaoCartaoApp;
            var result = transacaoCartaoApp.AtualizarStatusTransacaoPendenteReprocessada(
                (int) request.ProtocoloRequisicao,
                request.ProtocoloProcessamento,
                request.DataTransacao,
                request.MensagemStatus);
 
            return result.IsValid ? ResponderSucesso("Transação atualizada com sucesso.") : ResponderErro(result.Errors.First().Message);
        }

        [HttpPost]
        [EnableLogRequest]
        [IgnoreAuthSessionValidation]
        public JsonResult TransacaoPendenteFalha(TransacaoPendenteFalhaNotificacao request)
        {
            try
            {
                if (request == null)
                    return ResponderErro("Transação não informada");

                if (request.ProtocoloRequisicao == 0)
                    return ResponderErro("Protocolo da transação nao informado");

                var result = _transacaoCartaoApp.AtualizarStatusTransacaoFalhaReprocessada(
                    (int) request.ProtocoloRequisicao,
                    request.ProtocoloProcessamento,
                    request.DataTransacao,
                    request.MensagemStatus);

                return result.IsValid ? ResponderSucesso("Transação atualizada com sucesso.") : ResponderErro(result.Errors.First().Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ProtocoloAntecipacaoMap : EntityTypeConfiguration<ProtocoloAntecipacao>
    {
        public ProtocoloAntecipacaoMap()
        {
            ToTable("PROTOCOLO_ANTECIPACAO");

            HasKey(t => t.IdProtocoloAntecipacao);

            Property(t => t.ValorPagamentoAntecipado)
                .HasPrecision(10, 2);

            HasRequired(t => t.Protocolo)
                .WithMany(t => t.ProtocoloAntecipacoes)
                .HasForeignKey(t => t.IdProtocolo);

            HasOptional(t => t.Motivo)
                .WithMany(t => t.ProtocoloAntecipacoes)
                .HasForeignKey(t => t.IdMotivo);

        }
    }
}
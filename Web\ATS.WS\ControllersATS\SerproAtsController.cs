﻿/*using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Web.Mvc;

namespace ATS.WS.ControllersATS
{
    public class SerproAtsController : DefaultController
    {
        private readonly ISerproApp _serproApp;

        public SerproAtsController(ISerproApp serproApp)
        {
            _serproApp = serproApp;
        }
        
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ValidarCpf(string cpf, int? validacao = null)
        {
            try
            {
                var resultado = _serproApp.ValidarPortador(cpf, validacao: validacao);
                return resultado.IsValid ? ResponderSucesso(resultado) : Responder<PERSON>rro("O CPF informado é inválido!");
            }
            catch (Exception)
            {
                return ResponderErro("Não foi possível validar o CPF!");
            }
        }
    }
}*/
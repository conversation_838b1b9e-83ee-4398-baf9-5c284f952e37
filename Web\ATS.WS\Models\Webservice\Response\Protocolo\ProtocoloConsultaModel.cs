﻿using ATS.Domain.Enum;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Response.Protocolo
{
    public class ProtocoloConsultaModel
    {
        public int IdProtocolo { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public decimal ValorProtocolo { get; set; }
        public string DataGeracao { get; set; }
        public string DataPagamento { get; set; }
        public bool Processado { get; set; }
        public int StatusProtocolo { get; set; }
        public string DescricaoStatusProtocolo { get; set; }
        public List<ProtocoloEventoConsultaModel> Eventos { get; set; }
        public List<ProtocoloAnexoConsultaModel> Anexos { get; set; }
        public List<ProtocoloAntecipacaoConsultaModel> Antecipacoes { get; set; }
    }

    public class ProtocoloEventoConsultaModel
    {
        public int IdProtocoloEvento { get; set; }
        public int IdProtocolo { get; set; }
        public int IdViagemEvento { get; set; }
        public int? IdViagem { get; set; }
        public int? IdMotivo { get; set; }
        public EStatusProtocoloEvento Status { get; set; }
        public ETipoEventoViagem? TipoEventoViagem { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public string DataHoraPagamento { get; set; }
        public string NumeroRecibo { get; set; }
        public decimal? PesoChegada { get; set; }
        public decimal? PesoDiferenca { get; set; }
        public decimal? ValorDifFreteMotorista { get; set; }
        public decimal? ValorQuebraMercadoria { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DescricaoMotivoDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdMotivoDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ObservacaoDesconto { get; set; }
    }

    public class ProtocoloAnexoConsultaModel
    {
        public int IdProtocoloAnexo { get; set; }
        public int IdProtocolo { get; set; }
        public int IdDocumento { get; set; }
        public string Token { get; set; }
    }

    public class ProtocoloAntecipacaoConsultaModel
    {
        public int IdProtocoloAntecipacao { get; set; }
        public int IdProtocolo { get; set; }
        public EStatusProtocoloAntecipacao Status { get; set; }
        public string DataSolicitacao { get; set; }
        public decimal ValorPagamentoAntecipado { get; set; }
        public int? IdMotivo { get; set; }
    }
}
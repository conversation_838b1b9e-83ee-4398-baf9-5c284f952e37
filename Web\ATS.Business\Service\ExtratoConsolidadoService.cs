﻿using System;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class ExtratoConsolidadoService : ServiceBase, IExtratoConsolidadoService
    {
        private readonly IExtrattaBizApiClient _bizApiClient;

        public ExtratoConsolidadoService(IExtrattaBizApiClient bizApiClient)
        {
            _bizApiClient = bizApiClient;
        }

        public ExtratoConsolidadoModelResponse ExtratoConsolidadoGrid(ExtratoConsolidadoDTORequest request)
        {
            var filtroPlano = request.Filters?.FirstOrDefault(c => c.Campo == "DescricaoPlano");
            var filtroDebitoCredito = request.Filters?.FirstOrDefault(c => c.Campo == "DebitoCreditoDescricao");
            var filtroConta = request.Filters?.FirstOrDefault(c => c.Campo == "Conta");
            var filtroIdentificador = request.Filters?.FirstOrDefault(c => c.Campo == "Identificador");
            var filtroEstabelecimento = request.Filters?.FirstOrDefault(c => c.Campo == "Estabelecimento");

            var req = new ExtratoConsolidadoModelRequest()
            {
                Page = request.Page,
                Take = request.Take,
                Relatorio = request.Relatorio,
                DataFim = request.DataFinal,
                DataInicio = request.DataInicial,
                OrderCampo = request.Order?.Campo,
                OrderOperador = request.Order?.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1,
                DocumentosPortadores = request.DocumentosPortadores,
                DescricaoPlano = filtroPlano?.Valor,
                DebitoCredito = filtroDebitoCredito?.Valor,
                Conta = filtroConta?.Valor?.ToIntSafe(),
                Identificador = filtroIdentificador?.Valor?.ToIntSafe(),
                Estabelecimento = filtroEstabelecimento?.Valor,
            };

            var retorno = _bizApiClient.ExtratoConsolidado(req);

            if (!retorno.Success)
                throw new Exception(retorno.Messages.FirstOrDefault());

            return retorno.Value;
        }
    }
}
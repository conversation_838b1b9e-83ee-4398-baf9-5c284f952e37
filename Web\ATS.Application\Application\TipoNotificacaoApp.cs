﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class TipoNotificacaoApp : AppBase, ITipoNotificacaoApp
    {
        private readonly ITipoNotificacaoService _tipoNotificacaoService;

        public TipoNotificacaoApp(ITipoNotificacaoService tipoNotificacaoService)
        {
            _tipoNotificacaoService = tipoNotificacaoService;
        }

        public ValidationResult Add(TipoNotificacao tipoNotificacao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoNotificacaoService.Add(tipoNotificacao);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(TipoNotificacao tipoNotificacao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoNotificacaoService.Update(tipoNotificacao);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }
        
        public ValidationResult Reativar(int idTipoNotificacao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoNotificacaoService.Reativar(idTipoNotificacao);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Inativar(int idTipoNotificacao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoNotificacaoService.Inativar(idTipoNotificacao);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public TipoNotificacao Get(int idTipoNotificacao)
        {
            return _tipoNotificacaoService.Get(idTipoNotificacao);
        }

        public IQueryable<TipoNotificacao> GetPorDataBase(DateTime? dataBase, int idEmpresa)
        {
            return _tipoNotificacaoService.GetPorDataBase(dataBase, idEmpresa);
        }

        public object ConsultaGrid(int? idEmpresa,
                                        int? idFilial,
                                        string descricao,
                                        int take,
                                        int page,
                                        OrderFilters order,
                                        List<QueryFilters> filters)
        {
            return _tipoNotificacaoService.ConsultaGrid(idEmpresa, idFilial, descricao, take, page, order, filters);
        }

        public IQueryable<TipoNotificacao> ConsultarPorEmpresaFilial(int idEmpresa, int? idFilial)
        {
            return _tipoNotificacaoService.ConsultarPorEmpresaFilial(idEmpresa, idFilial);
        }
    }
}

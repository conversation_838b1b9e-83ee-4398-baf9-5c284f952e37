﻿Write-Host "> Deploying Api"
C:\Windows\system32\inetsrv\appcmd.exe stop apppool /apppool.name:"${IIS_POOL_NAME}"
Remove-Item "${APP_PATH}\scripts" -Recurse -Force
Remove-Item "${APP_PATH}\styles" -Recurse -Force
Copy-Item -Path ".\WebNovo\dist\*" -Destination "${APP_PATH}" -Recurse -Force
C:\Windows\system32\inetsrv\appcmd.exe start apppool /apppool.name:"${IIS_POOL_NAME}"

#Write-Host "> Revivendo Api"
#Invoke-WebRequest -Uri "${API_URL}" -TimeoutSec 120

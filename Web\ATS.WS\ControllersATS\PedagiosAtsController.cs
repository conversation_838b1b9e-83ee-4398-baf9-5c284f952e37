﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common;
using ATS.WS.Models.Webservice.Request.Cartoes;
using ATS.WS.Models.Webservice.Response.Cartao;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.ControllersATS
{
    public class PedagiosAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IUsuarioApp _usuarioApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IPedagioViagemApp _pedagioViagemApp;
        private readonly ITagExtrattaApp _tagExtrattaApp;
        private readonly IViagemRepository _viagemRepository;
        private readonly IUsuarioFilialRepository _usuarioFilialRepository;
        public PedagiosAtsController(IUserIdentity userIdentity, IUsuarioApp usuarioApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IPedagioViagemApp pedagioViagemApp, ITagExtrattaApp tagExtrattaApp, IViagemRepository viagemRepository, IUsuarioFilialRepository usuarioFilialRepository)
        {
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _pedagioViagemApp = pedagioViagemApp;
            _tagExtrattaApp = tagExtrattaApp;
            _viagemRepository = viagemRepository;
            _usuarioFilialRepository = usuarioFilialRepository;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPedagioGrid(ConsultaCompraPedagioRequest request,
            bool? filtrarApenasRestituicaoSaldoResidual, int take, int page, IEnumerable<OrderFilters> order,
            List<QueryFilters> filters)
        {
            try
            {
                var usuarioLogado = _usuarioApp.Get(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuarioLogado);
                var customFilter = Mapper.Map<List<QueryFilters>, List<CustomFilter>>(filters);

                if (filtrarApenasRestituicaoSaldoResidual.HasValue && filtrarApenasRestituicaoSaldoResidual.Value)
                    customFilter.Add(new CustomFilter
                        { Field = "valorResgatado", Value = "0", Operator = CustomFilterOperator.GreaterThan });

                var filtroDocumentoFavorecido =
                    customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("DocumentoFavorecido"));
                if (filtroDocumentoFavorecido != null)
                    request.DocumentoFavorecido = filtroDocumentoFavorecido.Value.OnlyNumbers();

                var respostaConsulta = cartoesApp.ConsultarCompraPedagio(take, page, order, customFilter, request);
                if (respostaConsulta.Status == ConsultaCompraPedagioResponseStatus.Falha)
                    return ResponderErro(respostaConsulta.Mensagem);
                
                var filiaisUsuario = _usuarioFilialRepository
                    .GetFiliaisPorIdUsuario(_userIdentity.IdUsuario)
                    .Select(c => c.IdFilial)
                    .ToList();

                if (filiaisUsuario.Any())
                {
                    var idsViagem = respostaConsulta.CompraPedagioDTOList
                        .Where(c => c.ProtocoloRequisicao != null)
                        .Select(c => (int)c.ProtocoloRequisicao)
                        .ToList();

                    var idsViagemDasFiliais = _viagemRepository
                        .Where(c => c.IdFilial.HasValue && filiaisUsuario.Contains(c.IdFilial.Value) && idsViagem.Contains(c.IdViagem))
                        .Select(c => c.IdViagem)
                        .ToList();

                    respostaConsulta.CompraPedagioDTOList = respostaConsulta.CompraPedagioDTOList
                        .Where(c => c.ProtocoloRequisicao.HasValue && idsViagemDasFiliais.Contains((int)c.ProtocoloRequisicao.Value))
                        .ToList();
                }
                
                var resposta = Mapper.Map<ConsultaCompraPedagioResponse, CartaoConsultarPedagioAtsResponse>(respostaConsulta);

                var totalMoedeiro = resposta.CompraPedagioDTOList
                    .Where(x => x.Fornecedor == FornecedorEnum.Moedeiro.GetDescription()).Select(x => x.Valor).Sum()
                    .FormatMoney();
                var totalViaFacil = resposta.CompraPedagioDTOList
                    .Where(x => x.Fornecedor == FornecedorEnum.ViaFacil.GetDescription()).Select(x => x.Valor).Sum()
                    .FormatMoney();
                var totalMoveMais = resposta.CompraPedagioDTOList
                    .Where(x => x.Fornecedor == FornecedorEnum.MoveMais.GetDescription()).Select(x => x.Valor).Sum()
                    .FormatMoney();
                var totalVeloe = resposta.CompraPedagioDTOList
                    .Where(x => x.Fornecedor == FornecedorEnum.Veloe.GetDescription()).Select(x => x.Valor).Sum()
                    .FormatMoney();
                var totalExtrattaTag = resposta.CompraPedagioDTOList
                    .Where(x => x.Fornecedor == FornecedorEnum.ExtrattaTag.GetDescription()).Select(x => x.Valor).Sum()
                    .FormatMoney();

                foreach (var item in resposta.CompraPedagioDTOList)
                {
                    item.DocumentoFavorecido = item.DocumentoFavorecido?.FormatMaskCpfCnpj();
                    item.DocumentoProprietario = item.DocumentoProprietario?.FormatMaskCpfCnpj();
                }
                
                var result = new
                {
                    items = resposta.CompraPedagioDTOList,
                    totalItems = resposta.FilteredOptions.TotalRecords,
                    totalMoedeiro,
                    totalViaFacil,
                    totalMoveMais,
                    totalVeloe,
                    totalExtrattaTag
                };

                return ResponderSucesso(result);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetStatusTipo()
        {
            try
            {
                var values = Enum.GetValues(typeof(CompraStatusTipo));
                var statusList = new List<StatusCompraDTO>();

                foreach (CompraStatusTipo value in values)
                {
                    statusList.Add(new StatusCompraDTO
                    {
                        Valor = value,
                        Descricao = value.GetDescription()
                    });
                }

                return ResponderSucesso(statusList);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarEmbarcadorViaFacilSTP(int idEmpresa)
        {
            try
            {
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario,
                    true, _userIdentity.IdEmpresa);
                var result = cartoesApp.CadastrarEmbarcadorViaFacilSTP(idEmpresa);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarTransportadorRntrcViaFacil(PedagioCadastrarTransportadorViaFacilRequest request)
        {
            try
            {
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario,
                    true, _userIdentity.IdEmpresa);
                
                var result = cartoesApp.CadastrarTransportadorRntrcViaFacil(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult DeletarTransportadorRntrcViaFacil(PedagioDeletarTransportadorViaFacilRequest request)
        {
            try
            {
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario,
                    true, _userIdentity.IdEmpresa);
                
                var result = cartoesApp.DeletarTransportadorRntrcViaFacil(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTransportadorRntrcViaFacil(PedagioConsultarTransportadorViaFacilRequest request)
        {
            try
            {
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario,
                    true, _userIdentity.IdEmpresa);
                
                var result = cartoesApp.ConsultarTransportadorRntrcViaFacil(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GridTransportadorRntrcViaFacil(PedagioGridTransportadorViaFacilRequest request)
        {
            try
            {
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, _userIdentity.IdUsuario,
                    true, _userIdentity.IdEmpresa);
                
                var result = cartoesApp.GridTransportadorRntrcViaFacil(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}

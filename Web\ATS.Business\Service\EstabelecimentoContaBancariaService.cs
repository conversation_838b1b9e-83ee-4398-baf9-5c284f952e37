﻿using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class EstabelecimentoContaBancariaService : ServiceBase, IEstabelecimentoContaBancariaService
    {
        private readonly IEstabelecimentoContaBancariaRepository _repository;

        public EstabelecimentoContaBancariaService(IEstabelecimentoContaBancariaRepository repository)
        {
            _repository = repository;
        }

        public EstabelecimentoContaBancaria GetById(int id)
        {
            return _repository.FirstOrDefault(o => o.IdEstabelecimentoContaBancaria == id);
        }

        public List<object> GetContasBancariasByEstabalecimento(int idEstabelecimento)
        {
            var dadosBancarios = _repository.GetByIdEstabelecimento(idEstabelecimento);
            var listaDados = new List<object>();

            foreach (var dado in dadosBancarios)
                listaDados.Add(new {dado.NomeConta, dado.CodigoBanco, dado.NomeBanco, dado.Agencia, dado.Conta, dado.DigitoConta, TipoConta = dado.TipoConta.DescriptionAttr(), CnpjTitular = dado.CnpjTitular.ToCNPJFormato(), dado.NomeTitular });

            return listaDados;
        }

        public void DeleteListContasBancarias(List<int> idsContasBancarias)
        {
            foreach (var id in idsContasBancarias)
            {
                var conta = GetById(id);

                if (conta != null)
                    _repository.Delete(conta);
            }
        }
    }
}

﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class MotoristaApp : AppBase, IMotoristaApp
    {
        private readonly IMotoristaService _motoristaService;

        public MotoristaApp(IMotoristaService motoristaService)
        {
            _motoristaService = motoristaService;
        }

        public Motorista Get(int id)
        {
            return _motoristaService.Get(id);
        }

        public Motorista Get(string cpf)
        {
            return _motoristaService.Get(cpf);
        }

        public Motorista Get(string cpf, bool withIncludes)
        {
            return _motoristaService.GetPorCpf(cpf, withIncludes);
        }

        public byte[] GetFoto(int id)
        {
            return _motoristaService.GetFoto(id);
        }

        public ValidationResult Add(Motorista motorista, int idUsuario, bool ignorePermissaoUsuario = false)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _motoristaService.Add(motorista, idUsuario, ignorePermissaoUsuario);

                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Motorista motorista, int? idUsuario, bool ignore = false)
        {
            return _motoristaService.Update(motorista, idUsuario ?? 0, ignore);
        }

        public Motorista GetPorCpf(string cpfMotorista, bool withIncludes = true)
        {
            var idMot = _motoristaService.GetIdPorCpf(cpfMotorista);

            if (idMot.HasValue && idMot > 0)
                return _motoristaService.Get(idMot.Value);

            return null;
        }

        public ValidationResult Update(Motorista motorista, int idUsuario, bool ignore = false)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _motoristaService.Update(motorista, idUsuario, ignore);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public int? GetIdPorCpf(string cpf)
        {
            return _motoristaService.GetIdPorCpf(cpf);
        }

        public int? GetIdPorCpf(string cpf, int? idEmpresa, bool? ativo = true)
        {
            return _motoristaService.GetIdPorCpf(cpf, idEmpresa, ativo);
        }

        public Motorista GetWithChilds(int id, int? idUsuarioLogOn)
        {
            return _motoristaService.GetWithChilds(id, idUsuarioLogOn);
        }

        public Motorista GetWithChilds(string cpf, int? idUsuarioLogOn)
        {
            return _motoristaService.GetWithChilds(cpf, idUsuarioLogOn);
        }

        public ValidationResult AlterarStatus(int idMotorista)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _motoristaService.AlterarStatus(idMotorista);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public IQueryable<Motorista> QueryById(int id)
        {
            return _motoristaService.QueryById(id);
        }

        public int GetQtdMotoristasCadastradosByUser(int idUsuario)
        {
            return _motoristaService.GetQtdMotoristasCadastradosByUser(idUsuario);
        }

        public object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters,bool ativo)
        {
            return _motoristaService.ConsultarGrid(idEmpresa, take, page, order, filters,ativo);
        }

        public byte[] GerarRelatorioGridMotoristas(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters, string logo, string extensao)
        {
            return _motoristaService.GerarRelatorioGridMotoristas(idEmpresa, take, page, order, filters, logo, extensao);
        }

        public bool Any(string cpf, bool? ativo = null)
        {
            return _motoristaService.Any(cpf, ativo);
        }

        public bool Any(string cpf, int idempresa, bool? ativo = null)
        {
            return _motoristaService.Any(cpf, idempresa, ativo);
        }

        public Motorista GetFromAllTables(string cpfCnpj)
        {
            return _motoristaService.GetFromAllTables(cpfCnpj);
        }

        public IEnumerable<Motorista> ConsultarMotoristasAtualizados(int idEmpresa, DateTime dataBase)
        {
            return _motoristaService.GetMotoristasAtualizados(idEmpresa, dataBase);
        }

        public ValidationResult DesvincularMotoristaVeiculo(int idEmpresa, string cpfMotorista, string placa)
        {
            return _motoristaService.DesvincularMotoristaVeiculo(idEmpresa, cpfMotorista, placa);
        }

        public List<Veiculo> GetVeiculosMotorista(string cpf, int? idEmpresa)
        {
            return _motoristaService.GetVeiculosMotorista(cpf, idEmpresa);
        }

        public bool PertenceAEmpresa(int idempresa, int idMotorista)
        {
            return _motoristaService.QueryById(idMotorista).Any(c => c.IdEmpresa == idempresa);
        }
    }
}

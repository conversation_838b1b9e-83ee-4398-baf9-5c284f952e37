﻿using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class EstabelecimentoBaseDocumentoApp : AppBase, IEstabelecimentoBaseDocumentoApp
    {
        private readonly IEstabelecimentoBaseDocumentoService _service;
        private readonly ICredenciamentoAnexoRepository _credenciamentoAnexo;

        public EstabelecimentoBaseDocumentoApp(IEstabelecimentoBaseDocumentoService service, ICredenciamentoAnexoRepository credenciamentoAnexo)
        {
            _service = service;
            _credenciamentoAnexo = credenciamentoAnexo;
        }

        public IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null)
        {
            return _service.GetAllDocumentoCredenciamento(idsEmpresa, idsDocumentoIgnorar);
        }
        
        public object Download(string token)
        {
            return _service.Download(token);
        }

        public string Upload(string dataBase64, string fileName)
        {
            return _service.Upload(dataBase64, fileName);
        }
        
        public List<EstabelecimentoBaseDocumento> MergeDocumentosInformadosComExigidos(List<EstabelecimentoBaseDocumento> estabelecimentoDocumentos, int idEstabelecimento, List<int> idsEmpresa)
        {
            if (!estabelecimentoDocumentos.Any())
                estabelecimentoDocumentos = _credenciamentoAnexo.GetAll(idEstabelecimento)
                    .ToList().Select(x => new EstabelecimentoBaseDocumento
                    {
                        IdEstabelecimentoBase = idEstabelecimento,
                        Descricao = x.Descricao,
                        Token = x.Token,
                        DataValidade = x.DataValidade,
                        IdDocumento = x.IdDocumento
                    }).ToList();
            
            //Trás todos os documentos que forem do tipo credenciamento e estiverem ativos
            var documentosCredenciamento = _service.GetAllDocumentoCredenciamento(idsEmpresa).ToList()
                .Select(x => new EstabelecimentoBaseDocumento
            {
                IdDocumento = x.IdDocumento,
                Descricao = x.Descricao,
                PermiteEditarData = x.PossuirValidade,
                DiasValidade = x.DiasValidade
            }).ToList();

            //Merge das propriedades
            foreach (var e in documentosCredenciamento)
            foreach (var d in estabelecimentoDocumentos)
                if (e.IdDocumento == d.IdDocumento)
                {
                    e.IdEstabelecimentoBaseDocumento = d.IdEstabelecimentoBaseDocumento;
                    e.IdEstabelecimentoBase = d.IdEstabelecimentoBase;
                    e.Token = d.Token;
                    e.DataValidade = d.SetDataValidade(d.DataValidade, d.PermiteEditarData, d.DiasValidade, d.Token);
                }
            
            //Preenche data de validade na lista para novos documentos que forem exigidos e o posto ainda não preencheu
            foreach (var e in documentosCredenciamento)
                if (e.IdEstabelecimentoBaseDocumento == 0)
                    e.DataValidade = e.SetDataValidade(e.DataValidade, e.PermiteEditarData, e.DiasValidade, e.Token);

            return documentosCredenciamento;
        }
        
        public List<EstabelecimentoBaseDocumento> GetDocumentos(int idEstabelecimentoBase)
        {
            return _service.GetDocumentos(idEstabelecimentoBase);
        }
    }
}

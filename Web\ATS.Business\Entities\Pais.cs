using System;
using System.Collections.Generic;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    public class Pais
    {
        /// <summary>
        /// C�digo do pa�s
        /// </summary>
        public int IdPais { get; set; }

        /// <summary>
        /// Nome do pa�s
        /// </summary>
        public string Nome { get; set; }

        /// <summary>
        /// Sigla que representa o pa�s
        /// </summary>
        public string Sigla { get; set; }

        /// <summary>
        /// C�digo do pa�s segundo o BACEN
        /// </summary>
        public int? BACEN { get; set; }

        /// <summary>
        /// Indica se o pa�s est� ou n�o ativo
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Data da altera��o do pa�s
        /// </summary>
        public DateTime DataAlteracao { get; set; }

        #region Refer�ncias

        /// <summary>
        /// Estados vinculados
        /// </summary>
        public ICollection<Estado> Estados { get; set; } 

        public ICollection<Filial> Filiais { get; set; }

        #endregion

        #region Navega��o Inversa

        public virtual ICollection<ClienteEndereco> ClienteEnderecos { get; set; }
        public virtual ICollection<Estabelecimento> Estabelecimentos { get; set; }
        public virtual ICollection<EstabelecimentoBase> EstabelecimentosBase { get; set; }
        public virtual ICollection<Veiculo> Veiculo { get; set; }
        #endregion
    }
}
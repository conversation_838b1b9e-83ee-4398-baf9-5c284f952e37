﻿using ATS.Data.Repository.External.Extratta.Biz.Models;

namespace ATS.Domain.DTO.Pix
{
    public class CadastrarChavePixProprietarioRequest
    {
        //Valores
        public string Chave { get; set; }
        public ETipoChavePix Tipo { get; set; }
        public string CPFCNPJProprietario { get; set; }
        
        //Api de integracao
        public string CNPJAplicacao { get; set; }
        public string Token { get; set; }
        public string DocumentoUsuarioAudit { get; set; }
        public string NomeUsuarioAudit { get; set; }
        
        //Portal
        public int? IdProprietario { get; set; }
    }
}
﻿namespace ATS.CrossCutting.Reports.Pedagio.ReciboPagamento
{
    public class ReciboPagamentoDataType
    {
        public string DataImpressao { get; set; }
        public string Contratante { get; set; }
        public string Filial { get; set; }
        public string DocumentoProprietario { get; set; }
        public string Contratado { get; set; }
        public string CnpjContratante { get; set; }
        public string CnpjFilial { get; set; }
        public string Ciot { get; set; }
        public string DocumentoCliente { get; set; }
        public string IdViagem { get; set; }
        public string DataInicioViagem { get; set; }
        public string Placas { get; set; }
        public string Rntrc { get; set; }
        public string CartaoContratado { get; set; }
        public string ValorTotalParcelas { get; set; }
        public string Motorista { get; set; }
        public string DocumentoMotoristra { get; set; }
        public string CartaoMotorista { get; set; }
        public string Fornecedor { get; set; }
        public string Protocolo { get; set; }
        public string NomeCampoComprovanteCarga { get; set; }
        public string CampoComprovanteCarga { get; set; }
        public string ValorComprovanteCarga { get; set; }
        public string StatusComprovanteCarga { get; set; }
        public string MensagemProtocoladoAnttComprovanteCarga { get; set; }
        public string RazaoSocialRemetente { get; set; }
        public string CnpjCpfRemetente { get; set; }
        public string RazaoSocialDestinatario { get; set; }
        public string CnpjCpfDestinatario { get; set; }
    }
}
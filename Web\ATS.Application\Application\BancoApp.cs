﻿using System;
using ATS.Application.Interface;
using ATS.Domain.Grid;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class BancoApp : IBancoApp
    {
        private readonly IBancoService _bancoService;
        private readonly IExtrattaBizApiClient _bizApiClient;
        
        public BancoApp(IBancoService bancoService, IExtrattaBizApiClient bizApiClient)
        {
            _bancoService = bancoService;
            _bizApiClient = bizApiClient;
        }

        public object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,ConsultarBancoResponseDTO bancos)
        {
            return _bancoService.ConsultaGrid(take, page, orderFilters, filters,bancos);
        }

        public ConsultarBancosModelResponse ConsultarTodos(bool apenasComIspb = false)
        {
            var bancos = _bizApiClient.GetBancos();
            
            if (bancos?.Success != true) throw new Exception(bancos?.Messages?.FirstOrDefault() ?? "Ocorreu um erro ao consultar os bancos.");

            var listagem = bancos.Value.Bancos ?? new List<ConsultarBancosModelResponseItem>();
                
            if(apenasComIspb && listagem.Count != 0) 
                listagem = listagem.Where(c => !string.IsNullOrWhiteSpace(c.Ispb)).ToList();

            foreach (var banco in listagem)
            {
                banco.Nome = $"{banco.Cod} - {banco.Nome}";
            }
            
            return new ConsultarBancosModelResponse()
            {
                Bancos = listagem
            };
        }
    }
}
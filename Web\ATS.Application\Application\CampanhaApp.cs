﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Mail;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.Campanha;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;

namespace ATS.Application.Application
{
    public class CampanhaApp : BaseApp<ICampanhaService>, ICampanhaApp
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IEmailService _emailService;
        
        public CampanhaApp(ICampanhaService service, IUsuarioRepository usuarioRepository, IUserIdentity userIdentity, IParametrosGenericoService parametrosGenericoService, IEmailService emailService) : base(service)
        {
            _usuarioRepository = usuarioRepository;
            _userIdentity = userIdentity;
            _parametrosGenericoService = parametrosGenericoService;
            _emailService = emailService;
        }

        public CampanhaConsultaResponse ConsultarAtual()
        {
            var campanha = Service.ConsultarAtual();
            if (campanha == null) throw new Exception("Nenhuma campanha não respondida em andamento.");
            return campanha;
        }

        public CampanhaGridResponse ConsultarCampanhas()
        {
            return Service.ConsultarCampanhas();
        }

        public ValidationResult Responder(ResponderCampanhaRequest request)
        {
            var validation = new ValidationResult();
            var resp = Service.Responder(request);
            if (resp.Nota.HasValue && resp.Nota.Value > 6) return validation;
            EnviarEmailQuestionandoNotaBaixa();
            return validation;
        }
        
        private void EnviarEmailQuestionandoNotaBaixa()
        {
            var infos = _usuarioRepository
                .Where(c => c.IdUsuario == _userIdentity.IdUsuario)
                .Include(c => c.Contatos)
                .Select(c => new{c.Nome, c.Contatos})
                .FirstOrDefault();

            if (infos?.Contatos == null || !infos.Contatos.Any()) return;

            var destinatarios = infos.Contatos.Select(c => c.Email).ToList();
            
            var emailModel = new EmailModel()
            {
                Assunto = $"[EXTRATTA] Conte-nos mais sobre a escolha da sua nota!",
                Destinatarios = destinatarios,
                NomeVisualizacao = $"Pesquisa de Satisfação",
                Prioridade = MailPriority.High
            };
            
            var destinatarioCc = _parametrosGenericoService.GetParametro<string>(GLOBAL.EnderecoEmailComCopiaPesquisaSatisfacaoNotaBaixa, 0);
            if (!string.IsNullOrWhiteSpace(destinatarioCc))
            {
                var destinatariosCc = new List<string> { destinatarioCc };
                emailModel.DestinatariosCopia = destinatariosCc;
            }

            using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\pesquisa-satisfacao-nota-baixa.html"))
            {
                var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                logoEmail.ContentId = Guid.NewGuid().ToString();

                var html = ms.ReadToEnd();

                html = html.Replace("{0}", logoEmail.ContentId);
                html = html.Replace("{USUARIO}", infos.Nome);

                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                view.LinkedResources.Add(logoEmail);
                emailModel.AlternateView = view;
            }

            _emailService.EnviarEmail(emailModel);
        }


        public ValidationResult Integrar(IntegrarCampanhaRequest request)
        {
            var resp = Service.Integrar(request);
            if (!resp.IsValid) throw new Exception(resp.Errors.FirstOrDefault()?.Message);
            return resp;
        }

        public AlterarStatusCampanhaResponse AlterarStatus(AlterarStatusCampanhaRequest request)
        {
            var resp = Service.AlterarStatus(request);
            if (!resp.Sucesso) throw new Exception(resp.Mensagem);
            return resp;
        }
    }
}

﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Response.Estabelecimento
{
    public class ConsultaEstabelecimentoResponse
    {
            public int IdEstabelecimento { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string TipoEstabelecimento { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Icone { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Estabelecimento { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Pais { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public int? CodigoBACENPais { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Estado { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public int? CodigoIBGEEstado { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Cidade { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public int? CodigoIBGECidade { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Bairro { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Logradouro { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string CEP { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public int? Numero { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Complemento { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public decimal? Latitude { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public decimal? Longitude { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public List<EstabelecimentoProdutoResponse> Produtos { get; set; }
            public bool Ativo { get; set; }
            public string CNPJEstabelecimento { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Telefone { get; set; }

            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string Email { get; set; }

    }

    public class EstabelecimentoProdutoResponse
    {
        public int IdProduto { get; set; }
        public string Produto { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? PrecoUnitario { get; set; }
        public decimal? PrecoPromocional { get; set; }
    }
}
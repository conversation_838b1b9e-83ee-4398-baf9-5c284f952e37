﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.CurvaAbc
{
    public class RelatorioCurvaAbc
    {
        public byte[] GetReport(List<RelatorioCurvaAbcDataType> listaDados, string tipoArquivo, string logo, string periodo, string uf, string verde, string amarelo, string vermelho)
        {
            var parametros = new Tuple<string, string, bool>[6];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);
            parametros[1] = new Tuple<string, string, bool>("Periodo", periodo, true);
            parametros[2] = new Tuple<string, string, bool>("Uf", uf, true);
            parametros[3] = new Tuple<string, string, bool>("Verde", string.IsNullOrEmpty(verde) ? "0 %" : $"{verde} %", true);
            parametros[4] = new Tuple<string, string, bool>("Amarelo", string.IsNullOrEmpty(amarelo) ? "0 %" : $"{amarelo} %", true);
            parametros[5] = new Tuple<string, string, bool>("Vermelho", string.IsNullOrEmpty(vermelho) ? "0 %" : $"{vermelho} %", true);

            var bytes = new Base.Reports().GetReport(listaDados, parametros, true, "DtsCurvaAbc",
                "ATS.CrossCutting.Reports.CurvaAbc.RelatorioCurvaAbc.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class AtendimentoPortadorTramiteRequest
    {
        public int IdAtendimentoPortador { get; set; }
        public int? IdentificadorCartao { get; set; }
        public int? ProdutoCartao { get; set; }
        public string Operacao { get; set; }
        public ETipoTramiteAtendimentoPortador Tipo { get; set; }
        public int? IdMotivo { get; set; }
        public bool? Permanente { get; set; }

    }
}
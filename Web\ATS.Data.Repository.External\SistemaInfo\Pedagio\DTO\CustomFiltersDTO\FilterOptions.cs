﻿using System.Collections.Generic;

namespace ATS.Data.Repository.External.SistemaInfo.Pedagio.DTO.CustomFiltersDTO
{
    public class FilterOptions
    {
        public int PageSize { get; set; } = 100;
        public int PageIndex { get; set; } = 0;
        public IEnumerable<CustomFilter> CustomFilters { get; set; } = new List<CustomFilter>();
        public IEnumerable<OrderOptions> OrderBy { get; set; } = new List<OrderOptions>();

        public class CustomFilter
        {
            public string Field { get; set; }
            public CustomFilterOperator Operator { get; set; }
            public string Value { get; set; }
            public bool ServerFieldCollection { get; set; }
            public FieldType FieldType { get; set; }
        }

        public class OrderOptions
        {
            public string Field { get; set; }
            public bool Asc { get; set; } = true;
        }

        public enum CustomFilterOperator
        {
            // sw
            StartsWith = 0,

            // ew
            EndsWith = 1,

            // ct
            Contains = 2,

            //nct
            NotContains = 3,

            // eq
            Equals = 4,

            // neq
            NotEquals = 5,

            // null
            IsNull = 6,

            // notnull
            IsNotNull = 7,

            // gt
            GreaterThan = 8,

            //gte
            GreaterThanOrEqual = 10,

            // lt
            LessThan = 11,

            // lte
            LessThanOrEqual = 12
        }

        public enum FieldType
        {
            Indefinido = 0,
            Date = 1,
            String = 2,
            Number = 3,
            Intervalo = 4,
        }
    }
}

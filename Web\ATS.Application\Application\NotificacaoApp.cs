﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class NotificacaoApp : AppBase, INotificacaoApp
    {
        private readonly INotificacaoService _notificacaoService;

        public NotificacaoApp(INotificacaoService notificacaoService)
        {
            _notificacaoService = notificacaoService;
        }

        /// <summary>
        /// Adicionar a mensagem
        /// </summary>
        /// <param name="notificacao">Informações sobre a notificação</param>
        /// <returns></returns>
        //TODO:IMPLEMENTAR O PUSH SE FOR NECESSÁRIO
        public Notificacao Add(Notificacao notificacao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {

                    var notificacaoService = _notificacaoService;
                    var retorno = notificacaoService.Add(notificacao);
                    transaction.Complete();

                    return retorno;
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public IQueryable<Notificacao> ObterNotificacoesNaoLidas(int idUsuario)
        {
            return _notificacaoService.ObterNotificacoesNaoLidas(idUsuario);
        }

        public IQueryable<Notificacao> GetNotificacoesPeloUsuario(int idUsuario)
        {
            return _notificacaoService.GetNotificacoesPeloUsuario(idUsuario).AsQueryable();
        }

        public ValidationResult SetRecebidoNovo(List<int> ids)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _notificacaoService.SetRecebidoNovo(ids);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public ValidationResult SetWithLido(int id)
        {
            return _notificacaoService.SetWithLido(id);
        }

    }
}
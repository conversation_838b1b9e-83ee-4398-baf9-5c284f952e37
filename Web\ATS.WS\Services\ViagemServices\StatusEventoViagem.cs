using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Configuration;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Service;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services.ViagemServices
{
    public class StatusEventoViagem : SrvBase
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly DesbloqueioEventoViagem _desbloqueioEventoViagem;
        private readonly BloqueioEventoViagem _bloqueioEventoViagem;
        private readonly BaixaEventoViagem _baixaEventoViagem;
        private readonly CancelamentoEventoViagem _cancelamentoEventoViagem;
        private readonly IResgateCartaoAtendimentoApp _resgatarCartaoApp;
        private readonly IntegracaoMeioHomologadoViagem _integracaoMeioHomologadoViagem;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public StatusEventoViagem(IVersaoAnttLazyLoadService versaoAntt, IClienteApp clienteApp, IParametrosApp parametrosApp, IViagemApp viagemApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, 
            IProprietarioApp proprietarioApp, ICadastrosApp cadastrosApp, IEmpresaApp empresaApp, IEmpresaRepository empresaRepository, DesbloqueioEventoViagem desbloqueioEventoViagem,
            BloqueioEventoViagem bloqueioEventoViagem, BaixaEventoViagem baixaEventoViagem, CancelamentoEventoViagem cancelamentoEventoViagem, IResgateCartaoAtendimentoApp resgatarCartaoApp,
            IntegracaoMeioHomologadoViagem integracaoMeioHomologadoViagem, ICidadeRepository cidadeRepository, IMotoristaApp motoristaApp, IUsuarioApp usuarioApp,
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp)
        {
            _versaoAntt = versaoAntt;
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _empresaApp = empresaApp;
            _empresaRepository = empresaRepository;
            _desbloqueioEventoViagem = desbloqueioEventoViagem;
            _bloqueioEventoViagem = bloqueioEventoViagem;
            _baixaEventoViagem = baixaEventoViagem;
            _cancelamentoEventoViagem = cancelamentoEventoViagem;
            _resgatarCartaoApp = resgatarCartaoApp;
            _integracaoMeioHomologadoViagem = integracaoMeioHomologadoViagem;
            _cidadeRepository = cidadeRepository;
            _motoristaApp = motoristaApp;
            _usuarioApp = usuarioApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        public ViagemAlterarStatusResponse AlterarStatus(int codigoViagem, EStatusViagem statusViagem, string cnpjAplicacao, string token, string documentoUsuarioAudit, string nomeUsuarioAudit, DateTime? dtEvento = null, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado comportamentoEstornoPedagioMoedeiro = ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, DateTime? dataAtualizacao = null)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return AlterarStatusV2(codigoViagem, statusViagem, cnpjAplicacao, token, documentoUsuarioAudit, nomeUsuarioAudit, dtEvento, comportamentoEstornoPedagioMoedeiro, dataAtualizacao);
                case EVersaoAntt.Versao3:
                    return AlterarStatusV3(codigoViagem, statusViagem, cnpjAplicacao, token, documentoUsuarioAudit, nomeUsuarioAudit, dtEvento, comportamentoEstornoPedagioMoedeiro, dataAtualizacao);
                default:
                    return AlterarStatusV2(codigoViagem, statusViagem, cnpjAplicacao, token, documentoUsuarioAudit, nomeUsuarioAudit, dtEvento, comportamentoEstornoPedagioMoedeiro, dataAtualizacao);
            }
        }
        
        public AlterarStatusEventosViagemResponseModel AlterarStatusEventos(AlterarStatusEventosViagemRequestModel @params, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return AlterarStatusEventosV2(@params, isApi);
                case EVersaoAntt.Versao3:
                    return AlterarStatusEventosV3(@params);
                default:
                    return AlterarStatusEventosV2(@params, isApi);
            }
        }

        #region Status

        private ViagemAlterarStatusResponse AlterarStatusV2(int codigoViagem, EStatusViagem statusViagem,
            string cnpjAplicacao, string token, string documentoUsuarioAudit, string nomeUsuarioAudit, DateTime? dtEvento = null,
            ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado comportamentoEstornoPedagioMoedeiro =
                ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, DateTime? dataAtualizacao = null)
        {
            var tokenFromWebConfig = WebConfigurationManager.AppSettings["Token"];
            var viagem = _viagemApp.Get(codigoViagem);
            if (viagem == null)
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = $"Não foi possível identificar a viagem pelo código informado. Código: {codigoViagem}.",
                    Objeto = null
                };
            
            var usuarioDocAudit = !string.IsNullOrWhiteSpace(documentoUsuarioAudit)
                ? documentoUsuarioAudit
                : CartoesService.AuditDocIntegracao;
            
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, usuarioDocAudit, nomeUsuarioAudit);

            /*if (viagem.DataAtualizacao.HasValue && dataAtualizacao.HasValue &&
                dataAtualizacao < viagem.DataAtualizacao)
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    Objeto = null
                };*/
            
            if (statusViagem == EStatusViagem.Cancelada)
            {
                if (viagem.StatusViagem == EStatusViagem.Cancelada)
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = true,
                        Mensagem = "Viagem já estava cancelada."
                    };
                
                if (viagem.DeclaracaoCiot != null && viagem.DeclaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Agregado &&
                    viagem.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = "Não é possível cancelar a viagem com o CIOT encerrado",
                        Objeto = null
                    };
            }
            else if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = "Viagem cancelada, não é permitido realizar alterações em viagens com este status.", 
                    Objeto = null
                };
            }

            var permiteCancelar = true;
            var cancelaViagemComProtocolo = _empresaApp.All().Where(empresa => empresa.IdEmpresa == viagem.IdEmpresa)
                .Select(empresa => empresa.CancelaViagemComProtocolo).FirstOrDefault();

            if (statusViagem == EStatusViagem.Cancelada)
            {
                var diasCancelamento = _parametrosApp.GetDiasCancelamentoViagem(viagem.IdEmpresa);
                if (diasCancelamento != 0 && viagem.DataLancamento.HasValue && DateTime.Now > viagem.DataLancamento.Value.AddDays(diasCancelamento))
                {
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = $"O prazo de cancelamento da Viagem foi atingido.",
                        Objeto = null
                    };
                }
                
                if (viagem.ViagemEventos != null && !cancelaViagemComProtocolo)
                    foreach (var evento in viagem.ViagemEventos)
                        if (evento.IdProtocolo.HasValue)
                            permiteCancelar = false;

                if (viagem.IdDeclaracaoCiot.HasValue)
                {
                    var declacaroCiot = viagem.DeclaracaoCiot?.TipoDeclaracao;
                    if (declacaroCiot == ETipoDeclaracao.Agregado)
                    {
                        var quantidadeViagens = _viagemApp.QuantidadeViagensAbertasPorNumeroCiotsVinculados(viagem.IdDeclaracaoCiot.Value);
                        if (quantidadeViagens == 1)
                        {
                            return new ViagemAlterarStatusResponse
                            {
                                Sucesso = false,
                                Mensagem = "Não é possível cancelar o CIOT, pois este tem apenas uma viagem vinculada a ele. Favor vincular uma nova viagem no CIOT antes de realizar o cancelamento desta.",
                                Objeto = null
                            };
                        }
                    }
                }
            }
                
            if (!permiteCancelar)
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = "Não é possível cancelar uma viagem que possui protocolos gerados.",
                    Objeto = null
                };

            if (!string.Equals(tokenFromWebConfig, token, StringComparison.CurrentCultureIgnoreCase))
            {
                var empresasAutorizadas = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);
                if (empresasAutorizadas == null)
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = "A empresa não possui autorização para realizar esta requisição.",
                        Objeto = null
                    };

                if (!empresasAutorizadas.Any(x => x.IdEmpresa == viagem.IdEmpresa))
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = "Empresa informada não possui autorização para realizar a alteração de status desta viagem.",
                        Objeto = null
                    };
            }

            var resultMessage = string.Empty;
            try
            {
                if (statusViagem == EStatusViagem.Cancelada)
                {
                    DeclararCiotResult ciotResult = null;
                    SolicitarCompraPedagioResponseDTO compraPedagio = null;
                    ProcessoIntegracaoViagemDto mensagensProcesso =
                        _integracaoMeioHomologadoViagem
                            .CancelarViagem(viagem, cartoesApp, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, 
                                out ciotResult, out compraPedagio, documentoUsuarioAudit, nomeUsuarioAudit);
                    
                    if (!mensagensProcesso.Sucesso)
                        return new ViagemAlterarStatusResponse(false, mensagensProcesso.MensagemViagem, 
                            new ViagemAlterarStatusResponse.ObjetoDetalhe
                            {
                                CodigoFalha = mensagensProcesso.StatusResponseTipoFalha ?? EViagemAlterarStatusResponseTipoFalha.Erro
                            });
                }

                //
                viagem.StatusViagem = statusViagem;

                if (viagem.StatusViagem == EStatusViagem.Baixada || viagem.StatusViagem == EStatusViagem.Cancelada)
                    viagem.DataFinalizacao = dtEvento ?? DateTime.Now;
                
                if (viagem.StatusViagem != EStatusViagem.Cancelada && viagem.ViagemEventos != null)
                {
                    if (viagem.ViagemEventos.All(t => t.Status != EStatusViagemEvento.Aberto))
                        viagem.StatusViagem = EStatusViagem.Baixada;
                }

                var validation = _viagemApp.Update(viagem);
                if (!validation.IsValid)
                    return new ViagemAlterarStatusResponse(false, validation.ToFormatedMessage(), null);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"Não foi possível realizar a alteração de status da viagem. Erro: {e.GetBaseException().Message}");
            }

            return new ViagemAlterarStatusResponse(true, resultMessage.NullIfEmpty(), null);
        }
        
        private ViagemAlterarStatusResponse AlterarStatusV3(int codigoViagem, EStatusViagem statusViagem,
            string cnpjAplicacao, string token, string documentoUsuarioAudit, string nomeUsuarioAudit, DateTime? dtEvento = null,
            ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado comportamentoEstornoPedagioMoedeiro =
                ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, DateTime? dataAtualizacao = null)
        {
            var tokenFromWebConfig = WebConfigurationManager.AppSettings["Token"];
            var viagem = _viagemApp.Get(codigoViagem);
            if (viagem == null)
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = $"Não foi possível identificar a viagem pelo código informado. Código: {codigoViagem}.",
                    Objeto = null
                };
            
            var usuarioDocAudit = !string.IsNullOrWhiteSpace(documentoUsuarioAudit)
                ? documentoUsuarioAudit
                : CartoesService.AuditDocIntegracao;
                
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, usuarioDocAudit, nomeUsuarioAudit);

            if (viagem.DataAtualizacao.HasValue && dataAtualizacao.HasValue &&
                dataAtualizacao < viagem.DataAtualizacao)
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    Objeto = null
                };
            
            if (statusViagem == EStatusViagem.Cancelada)
            {
                if (viagem.StatusViagem == EStatusViagem.Cancelada)
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = true,
                        Mensagem = "Viagem já estava cancelada."
                    };
                
                if (viagem.DeclaracaoCiot != null && viagem.DeclaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Agregado &&
                    viagem.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = "Não é possível cancelar a viagem com o CIOT encerrado",
                        Objeto = null
                    };
            }
            else if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = "Viagem cancelada, não é permitido realizar alterações em viagens com este status.", 
                    Objeto = null
                };
            }

            var permiteCancelar = true;
            var cancelaViagemComProtocolo = _empresaApp.All().Where(empresa => empresa.IdEmpresa == viagem.IdEmpresa)
                .Select(empresa => empresa.CancelaViagemComProtocolo).FirstOrDefault();
            
            if (statusViagem == EStatusViagem.Cancelada)
            {
                var diasCancelamento = _parametrosApp.GetDiasCancelamentoViagem(viagem.IdEmpresa);
                if (diasCancelamento != 0 && viagem.DataLancamento.HasValue && DateTime.Now > viagem.DataLancamento.Value.AddDays(diasCancelamento))
                {
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = $"O prazo de cancelamento da Viagem foi atingido.",
                        Objeto = null
                    };
                }
                
                if (viagem.ViagemEventos != null && !cancelaViagemComProtocolo)
                    foreach (var evento in viagem.ViagemEventos)
                        if (evento.IdProtocolo.HasValue)
                            permiteCancelar = false;

                if (viagem.IdDeclaracaoCiot.HasValue)
                {
                    var declacaroCiot = viagem.DeclaracaoCiot?.TipoDeclaracao;
                    if (declacaroCiot == ETipoDeclaracao.Agregado)
                    {
                        var quantidadeViagens = _viagemApp.QuantidadeViagensAbertasPorNumeroCiotsVinculados(viagem.IdDeclaracaoCiot.Value);
                        if (quantidadeViagens == 1 && viagem.DeclaracaoCiot.DataDeclaracao.AddDays(5) < DateTime.Today)
                        {
                            return new ViagemAlterarStatusResponse
                            {
                                Sucesso = false,
                                Mensagem = "Não é possível cancelar o CIOT, pois este tem apenas uma viagem vinculada a ele. Favor vincular uma nova viagem no CIOT antes de realizar o cancelamento desta.",
                                Objeto = null
                            };
                        }
                    }
                }
            }
                

            if (!permiteCancelar)
                return new ViagemAlterarStatusResponse
                {
                    Sucesso = false,
                    Mensagem = "Não é possível cancelar uma viagem que possui protocolos gerados.",
                    Objeto = null
                };

            if (!string.Equals(tokenFromWebConfig, token, StringComparison.CurrentCultureIgnoreCase))
            {
                var empresasAutorizadas =
                    _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);
                if (empresasAutorizadas == null)
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = "A empresa não possui autorização para realizar esta requisição.",
                        Objeto = null
                    };

                if (!empresasAutorizadas.Any(x => x.IdEmpresa == viagem.IdEmpresa))
                    return new ViagemAlterarStatusResponse
                    {
                        Sucesso = false,
                        Mensagem = "Empresa informada não possui autorização para realizar a alteração de status desta viagem.",
                        Objeto = null
                    };
            }

            var resultMessage = string.Empty;
            try
            {
                if (statusViagem == EStatusViagem.Cancelada)
                {
                    DeclararCiotResult ciotResult = null;
                    SolicitarCompraPedagioResponseDTO compraPedagio = null;
                    ProcessoIntegracaoViagemDto mensagensProcesso =
                        _integracaoMeioHomologadoViagem
                            .CancelarViagem(viagem, cartoesApp, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, 
                                out ciotResult, out compraPedagio, documentoUsuarioAudit, nomeUsuarioAudit);
                    
                    if (!mensagensProcesso.Sucesso)
                        return new ViagemAlterarStatusResponse(false, mensagensProcesso.MensagemViagem, 
                            new ViagemAlterarStatusResponse.ObjetoDetalhe
                            {
                                CodigoFalha = mensagensProcesso.StatusResponseTipoFalha ?? EViagemAlterarStatusResponseTipoFalha.Erro
                            });
                }

                viagem.StatusViagem = statusViagem;

                if (viagem.StatusViagem == EStatusViagem.Baixada || viagem.StatusViagem == EStatusViagem.Cancelada)
                    viagem.DataFinalizacao = dtEvento ?? DateTime.Now;

                if (viagem.StatusViagem != EStatusViagem.Cancelada && viagem.ViagemEventos != null)
                {
                    if (viagem.ViagemEventos.All(t => t.Status != EStatusViagemEvento.Aberto))
                        viagem.StatusViagem = EStatusViagem.Baixada;
                }

                var validation = _viagemApp.Update(viagem);
                if (!validation.IsValid)
                    return new ViagemAlterarStatusResponse(false, validation.ToFormatedMessage(), null);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"Não foi possível realizar a alteração de status da viagem. Erro: {e.GetBaseException().Message}");
            }
            
            return new ViagemAlterarStatusResponse(true, resultMessage.NullIfEmpty(), null);
        }

        #endregion

        #region Status Evento

        private AlterarStatusEventosViagemResponseModel AlterarStatusEventosV2(AlterarStatusEventosViagemRequestModel @params, bool isApi = false)
        {
            var response = new AlterarStatusEventosViagemResponseModel
            {
                Sucesso = true,
                Mensagem = "",
                ViagemEventos = new List<AlterarStatusEventosViagemEventoResponseModel>()
            };
            
            foreach (var viagemEventoStatusModel in @params.ViagemEventos)
            {
                var i = 1;
                switch (viagemEventoStatusModel.Status)
                {
                    case EStatusViagemEvento.Aberto:
                        
                        var desbloquearEventoRequest = new DesbloquearEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseDesbloquear =
                            _desbloqueioEventoViagem.DesbloquearEvento(desbloquearEventoRequest, isApi);
                        
                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseDesbloquear.Sucesso,
                            Mensagem = responseDesbloquear.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;
                        
                    case EStatusViagemEvento.Bloqueado:
                        
                        var bloquearEventoRequestModel = new BloquearEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseBloqueio =
                            _bloqueioEventoViagem.BloquearEvento(bloquearEventoRequestModel, isApi); //BloquearEvento(bloquearEventoRequestModel);
                        
                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseBloqueio.Sucesso,
                            Mensagem = responseBloqueio.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;
                        
                    case EStatusViagemEvento.Baixado:
                        
                        var baixarEventoRequestModel = new BaixarEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseBaixar =
                            _baixaEventoViagem.BaixarEvento(baixarEventoRequestModel, isApi); //BaixarEvento(baixarEventoRequestModel);
                        
                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseBaixar.Sucesso,
                            Mensagem = responseBaixar.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;
                        
                    case EStatusViagemEvento.Cancelado:
                        
                        var cancelarEventoRequestModel = new CancelarEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseCancelar =
                            _cancelamentoEventoViagem.CancelarEvento(cancelarEventoRequestModel, isApi);
                        
                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseCancelar.Sucesso,
                            Mensagem = responseCancelar.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;
                        
                    default:
                        throw new ArgumentOutOfRangeException(nameof(viagemEventoStatusModel.Status));
                }

                i++;

                if (!response.Sucesso)
                    return response;
            }

            return response;
        }
        
        private AlterarStatusEventosViagemResponseModel AlterarStatusEventosV3(AlterarStatusEventosViagemRequestModel @params)
        {
            var response = new AlterarStatusEventosViagemResponseModel
            {
                Sucesso = true,
                Mensagem = "",
                ViagemEventos = new List<AlterarStatusEventosViagemEventoResponseModel>()
            };

            foreach (var viagemEventoStatusModel in @params.ViagemEventos)
            {
                var i = 1;
                switch (viagemEventoStatusModel.Status)
                {
                    case EStatusViagemEvento.Aberto:

                        var desbloquearEventoRequest = new DesbloquearEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseDesbloquear =
                            _desbloqueioEventoViagem.DesbloquearEvento(desbloquearEventoRequest);

                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseDesbloquear.Sucesso,
                            Mensagem = responseDesbloquear.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;

                    case EStatusViagemEvento.Bloqueado:

                        var bloquearEventoRequestModel = new BloquearEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseBloqueio =
                            _bloqueioEventoViagem.BloquearEvento(bloquearEventoRequestModel);

                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseBloqueio.Sucesso,
                            Mensagem = responseBloqueio.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;

                    case EStatusViagemEvento.Baixado:

                        var baixarEventoRequestModel = new BaixarEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseBaixar =
                            _baixaEventoViagem.BaixarEvento(baixarEventoRequestModel);

                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseBaixar.Sucesso,
                            Mensagem = responseBaixar.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;

                    case EStatusViagemEvento.Cancelado:

                        var cancelarEventoRequestModel = new CancelarEventoRequestModel
                        {
                            CNPJEmpresa = @params.CNPJEmpresa,
                            Token = @params.Token,
                            DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                            NomeUsuarioAudit = @params.NomeUsuarioAudit,
                            IdViagem = @params.IdViagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento,
                            NumeroControleEvento = viagemEventoStatusModel.NumeroControle,
                            NumeroControleViagem = @params.NumeroControle,
                            CNPJAplicacao = @params.CNPJAplicacao
                        };

                        var responseCancelar =
                            _cancelamentoEventoViagem.CancelarEvento(cancelarEventoRequestModel);

                        response.ViagemEventos.Add(new AlterarStatusEventosViagemEventoResponseModel
                        {
                            Sucesso = responseCancelar.Sucesso,
                            Mensagem = responseCancelar.Mensagem,
                            IdViagemEvento = viagemEventoStatusModel.IdViagemEvento ?? 0,
                            Linha = i
                        });

                        break;

                    default:
                        throw new ArgumentOutOfRangeException(nameof(viagemEventoStatusModel.Status));
                }

                i++;

                if (!response.Sucesso)
                    return response;
            }

            return response;
        }

        #endregion
        
        public ViagemCancelarVpoResponse CancelarVpo(int codigoViagem, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var viagem = _viagemApp.Get(codigoViagem);
            var usuarioDocAudit = !string.IsNullOrWhiteSpace(documentoUsuarioAudit) ? documentoUsuarioAudit : CartoesService.AuditDocIntegracao;
            SolicitarCompraPedagioResponseDTO compraPedagio;

            if (viagem == null)
                return new ViagemCancelarVpoResponse
                {
                    Sucesso = false,
                    Mensagem = $"Não foi possível identificar a viagem pelo código informado. Código: {codigoViagem}.",
                };

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, usuarioDocAudit, nomeUsuarioAudit);

            var mensagensProcesso = _integracaoMeioHomologadoViagem
                    .CancelarVpo(viagem, cartoesApp, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, out compraPedagio);

            if (!mensagensProcesso.Sucesso)
            {
                return new ViagemCancelarVpoResponse()
                {
                    Sucesso = false,
                    Mensagem = mensagensProcesso.MensagemViagem
                };
            }

            return new ViagemCancelarVpoResponse()
            {
                Sucesso = true,
                Mensagem = "Operação realizada com sucesso!",
            };
        }
    }
}
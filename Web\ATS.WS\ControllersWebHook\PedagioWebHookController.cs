﻿using System.Linq;
using System.Web.Mvc;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;

namespace ATS.WS.ControllersWebHook
{
    public class PedagioWebHookController : DefaultController
    {
        private readonly SrvPedagio _srvPedagio;

        public PedagioWebHookController(SrvPedagio srvPedagio)
        {
            _srvPedagio = srvPedagio;
        }

        [HttpPost]
        [EnableLogRequest]
        [IgnoreAuthSessionValidation]
        public JsonResult NotificarConfirmacaoPedagio(NotificacaoConfirmacaoPedagioModel request)
        {
            var resultado = _srvPedagio.NotificarConfirmacaoPedagio(request);

            return resultado.IsValid ? ResponderSucesso("Carga de pedágio processada com sucesso. Tipo: " + request.TipoConfirmacao) : ResponderErro(resultado.Errors.First().Message);
        }
    }
}
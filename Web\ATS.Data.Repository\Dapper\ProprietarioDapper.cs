﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using Dapper;
using NLog;

namespace ATS.Data.Repository.Dapper
{
    public class ProprietarioDapper : DapperFactory<ProprietarioCadastroServicoCartaoDto>, IProprietarioDapper
    {
        private readonly IProprietarioDomainDapper _repositoryProprietarioDomainDapper;

        public ProprietarioDapper(IProprietarioDomainDapper repositoryProprietarioDomainDapper)
        {
            _repositoryProprietarioDomainDapper = repositoryProprietarioDomainDapper;
        }

        public void AtualizaEquiparadoTac(List<int> idsProprietario, bool equiparadoTac)
        {
            var parameters = new DynamicParameters();
            parameters.Add("equiparadoTac", equiparadoTac);
            parameters.Add("idsProprietario", idsProprietario);
            
            var sql = $"UPDATE PROPRIETARIO SET equiparadotac = @equiparadoTac WHERE idproprietario IN @idsProprietario";
            using (IDbConnection dbConnection = _repositoryProprietarioDomainDapper.GetConnection())
                dbConnection.Query(sql, parameters);
        }
        
        public void AtualizaEquiparadoTac(int proprietarioId, bool equiparadoTac)
        {
            try
            {
                var query =
                    $@"UPDATE PROPRIETARIO SET habilitarcontratociotagregado = @EquiparadoTac WHERE idproprietario = @IdProprietario";

                var parameters = new {EquiparadoTac = equiparadoTac, IdProprietario = proprietarioId};

                using (var connection = new DapperContext().GetConnection)
                {
                    connection.Query<Proprietario>(query, parameters);
                }
                
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
            }
        }
        
        public List<ProprietarioCadastroServicoCartaoDto> ProprietariosSemEquiparadoTac(string cnpjcpf = null)
        {
            var sql = @"select p.idproprietario, p.idempresa, p.cnpjcpf, p.rntrc from PROPRIETARIO p with(NOLOCK)
                        where p.idproprietario in
                              (select distinct v.idproprietario from VIAGEM v with(NOLOCK)
                                left outer join VIAGEM_EVENTO ve with(NOLOCK)
                                  on v.idviagem = ve.idviagem
                              where ve.habilitarpagamentocartao = 1)
                          and p.equiparadotac is null
                          and len(cnpjcpf) = 14
                          and p.cnpjcpf = coalesce (@cnpjcpf, p.cnpjcpf)";
            
            var parameters = new DynamicParameters();
            parameters.Add("cnpjcpf", cnpjcpf);
            
            return RunSelect(sql, (object) parameters).ToList();
        }
        
        public List<ProprietarioCadastroServicoCartaoDto> ProprietariosComEquiparadoTac(List<string> cnpjcpf)
        {
            if (cnpjcpf == null)
                cnpjcpf = new List<string>();

            var sql = @"select p.idproprietario, p.idempresa, p.cnpjcpf, p.equiparadotac from PROPRIETARIO p with(NOLOCK)
                        where p.idproprietario in
                          (select distinct v.idproprietario from VIAGEM v with(NOLOCK)
                            left outer join VIAGEM_EVENTO ve with(NOLOCK)
                              on v.idviagem = ve.idviagem
                          where ve.habilitarpagamentocartao = 1)
                        and p.equiparadotac is not null";

            if (!cnpjcpf.Any())
                sql += @"
                        and len(cnpjcpf) = 14";
            else
                sql += @"
                        and p.cnpjcpf IN @cnpjcpf";
            
            var parameters = new DynamicParameters();
            parameters.Add("cnpjcpf", cnpjcpf);
            
            return RunSelect(sql, (object) parameters).ToList();
        }
    }
}

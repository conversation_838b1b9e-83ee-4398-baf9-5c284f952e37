﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ITipoNotificacaoApp
    {
        ValidationResult Add(TipoNotificacao tipoNotificacao);
        ValidationResult Update(TipoNotificacao tipoNotificacao);
        ValidationResult Reativar(int idTipoNotificacao);
        ValidationResult Inativar(int idTipoNotificacao);
        TipoNotificacao Get(int idTipoNotificacao);
        IQueryable<TipoNotificacao> GetPorDataBase(DateTime? dataBase, int idEmpresa);

        object ConsultaGrid(int? idEmpresa,
            int? idFilial,
            string descricao,
            int take,
            int page,
            OrderFilters order,
            List<QueryFilters> filters);

        IQueryable<TipoNotificacao> ConsultarPorEmpresaFilial(int idEmpresa, int? idFilial);
    }
}
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class CombustivelJSLMap : EntityTypeConfiguration<CombustivelJSL>
    {
        public CombustivelJSLMap()
        {
            ToTable("COMBUSTIVEL_JSL");

            HasKey(x => x.IdCombustivelJSL);
            
            Property(t => t.IdCombustivelJSL)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.Descricao)
                .IsRequired().HasMaxLength(100);

            Property(t => t.SincronizarATS)
                .IsRequired();
        }
    }
}
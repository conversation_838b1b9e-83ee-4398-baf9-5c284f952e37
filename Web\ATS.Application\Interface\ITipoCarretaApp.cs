﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Application.Interface
{
    public interface ITipoCarretaApp : IAppBase<TipoCarreta>
    {
        TipoCarreta Get(int id);
        List<TipoCarreta> GetTodos(int? aIdEmpresa = null);
        ValidationResult Add(TipoCarreta tipoCarreta);
        ValidationResult Update(TipoCarreta tipoCarreta);
        IQueryable<TipoCarretaGrid> Consultar(string nome, int? idEmpresa);
        ValidationResult Inativar(int idTipoCarreta);
        ValidationResult Reativar(int idTipoCarreta);
        IQueryable<TipoCarreta> GetPorCategoria(ECategoriaTipoCarreta categoria);
        IEnumerable<TipoCarreta> GetRegistrosAtualizados(DateTime dataBase, List<int> idsEmpresa);
        IQueryable<TipoCarreta> All();

        /// <summary>
        /// Retorna apenas o objeto de tipo carreta
        /// </summary>
        /// <param name="id"></param>
        /// <returns>TipoCarreta</returns>
        TipoCarreta GetTipoCarreta(int id);

        List<TipoCarreta> GetTiposCarretaPorEmpresa(int? aIdEmpresa = null);

        object ConsultarSemEmpresa();

        TipoCarreta GetPorDescricao(string nome, int idEmpresa);

        object ConsultaGrid(int? idEmpresa, int? idTipoCarreta, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters);
    }
}
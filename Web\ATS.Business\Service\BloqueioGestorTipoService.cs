using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class BloqueioGestorTipoService : ServiceBase, IBloqueioGestorTipoService
    {
        private readonly IBloqueioGestorTipoRepository _bloqueioGestorTipoRepository;

        public BloqueioGestorTipoService(IBloqueioGestorTipoRepository bloqueioGestorTipoRepository)
        {
            _bloqueioGestorTipoRepository = bloqueioGestorTipoRepository;
        }

        public IQueryable<BloqueioGestorTipo> GetAll()
        {
            return _bloqueioGestorTipoRepository.GetAll();
        }
    }
}
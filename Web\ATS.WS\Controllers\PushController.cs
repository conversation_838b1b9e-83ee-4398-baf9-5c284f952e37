﻿/*using System;
using System.Web.Http;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Webservice.Request.Push;

namespace ATS.WS.Controllers
{
    public class PushController : BaseController
    {
        private readonly IPushService _pushService;

        public PushController(BaseControllerArgs baseArgs, IPushService pushService) : base(baseArgs)
        {
            _pushService = pushService;
        }
        
        [HttpPost]
        public void Send(PushNotificationRequest @params)
        {
            try
            {
                _pushService.EnviarComResposta(@params.IdsPush, @params.Title, @params.MessageType, @params.Message,
                    new object());
            }
            catch (Exception e)
            {
                throw new Exception($"Não foi possível enviar a mensagem via Push.  {e.Message} --------------- {e.InnerException?.Message}", e);
            }
        }
    }
}*/
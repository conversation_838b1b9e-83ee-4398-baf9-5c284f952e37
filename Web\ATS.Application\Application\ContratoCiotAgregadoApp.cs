﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class ContratoCiotAgregadoApp : AppBase, IContratoCiotAgregadoApp
    {
        private readonly IContratoCiotAgregadoService _contratoCiotAgregadoService;

        public ContratoCiotAgregadoApp(IContratoCiotAgregadoService contratoCiotAgregadoService)
        {
            _contratoCiotAgregadoService = contratoCiotAgregadoService;
        }

        /// <summary>
        /// Retorna o objeto de contrato de agregado
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ContratoCiotAgregado Get(int id)
        {
            return _contratoCiotAgregadoService.Get(id);
        }

        /// <summary>
        /// Retorna o objeto de contrato de agregado contendo o registro da declaração de CIOT
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ContratoCiotAgregado GetWithDeclaracaoCiot(int idProprietario)
        {
            return _contratoCiotAgregadoService.GetWithDeclaracaoCiot(idProprietario);
        }
    }
}
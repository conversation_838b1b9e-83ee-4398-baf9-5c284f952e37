﻿using ATS.Domain.Enum;

namespace ATS.Domain.Entities.Common
{
    public class VeiculoBase
    {
        /// <summary>
        /// Placa do veículo
        /// </summary>
        public string Placa { get; set; }
        
        /// <summary>
        /// Tipo de padrão da placa (Brasil, MERCOSUL)
        /// </summary>
        public ETipoPadraoPlaca TipoPadraoPlaca { get; set; }
        
        /// <summary>
        /// Placa no padrão brasil antes da troca para o padrão MERCOSUL
        /// </summary>
        public string PlacaPadraoBrasil { get; set; }

        /// <summary>
        /// Número do chassi
        /// </summary>
        public string Chassi { get; set; }

        /// <summary>
        /// Ano de Fabricação
        /// </summary>
        public int? AnoFabricacao { get; set; }

        /// <summary>
        /// Ano do Modelo
        /// </summary>
        public int? AnoModelo { get; set; }

        /// <summary>
        /// Número do Registro Nacional de Veículos Automotores (RENAVAM)
        /// </summary>
        public string RENAVAM { get; set; }

        /// <summary>
        /// Marca
        /// </summary>
        public string Marca { get; set; }

        /// <summary>
        /// Modelo
        /// </summary>
        public string Modelo { get; set; }

        /// <summary>
        /// Informa se o veículo possui tração
        /// </summary>
        public bool ComTracao { get; set; }

        /// <summary>
        /// Tipo de rodagem
        /// </summary>
        public ETipoRodagem TipoRodagem { get; set; }

        /// <summary>
        /// Tecnologia de rastreamento utilizada pelo veículo
        /// </summary>
        public string TecnologiaRastreamento { get; set; }

        /// <summary>
        /// Tipo de veículo
        /// </summary>
        public int? IdTipoCavalo { get; set; }

        /// <summary>
        /// Tipo de carreta
        /// </summary>
        public int? IdTipoCarreta { get; set; }

        /// <summary>
        /// Ativo
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Informando o status do registro nas integrações com ERP
        /// </summary>
        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Pendente;
    }
}
﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class ViagemEventoApp : AppBase, IViagemEventoApp
    {
        private readonly IViagemEventoService _viagemEventoService;

        public ViagemEventoApp(IViagemEventoService viagemEventoService)
        {
            _viagemEventoService = viagemEventoService;
        }

        public List<ViagemEvento> GetEventosViagem(int idViagem)
        {
            return _viagemEventoService.GetEventosViagem(idViagem);
        }

        public List<DeclaracaoCiot> GetCiotViagem(int idViagem)
        {
            return _viagemEventoService.GetCiotViagem(idViagem);
        }

        public List<ViagemEvento> GetEventosViagem(List<int> idsViagemEvento)
        {
            return _viagemEventoService.GetEventosViagem(idsViagemEvento);
        }

        public ViagemEvento Get(int idViagemEvento)
        {
            return _viagemEventoService.Get(idViagemEvento);
        }
        public IQueryable<ViagemEvento> GetQueryable(int idViagemEvento)
        {
            return _viagemEventoService.GetQueryable(idViagemEvento);
        }

        public ValidationResult Update(ViagemEvento viagemEvento)
        {
            return _viagemEventoService.Update(viagemEvento);
        }

        public object ConsultaGridSemChave(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _viagemEventoService.ConsultaGrid(take, page, order, filters);
        }

        public IQueryable<ViagemEvento> GetEventoPorTokenPagSemChave(string token)
        {
            return _viagemEventoService.GetEventoPorTokenPagSemChave(token);
        }

        public IEnumerable<ViagemEvento> GetEventosViagemSemChave(int idEmpresa)
        {
            return _viagemEventoService.GetEventosViagemSemChave(idEmpresa);
        }

        public IQueryable<ViagemEvento> Find(Expression<Func<ViagemEvento, bool>> predicate, bool @readonly = false)
        {
            return _viagemEventoService.Find(predicate, @readonly);
        }
    }
}

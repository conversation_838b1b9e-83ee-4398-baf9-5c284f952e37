﻿using System;
using System.Runtime.Serialization;

namespace ATS.Data.Repository.External.SistemaInfo.Auth
{
    public class AuthAtsException : Exception
    {
        public AuthAtsException()
        {
        }

        public AuthAtsException(string message) : base(message)
        {
        }

        public AuthAtsException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected AuthAtsException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}

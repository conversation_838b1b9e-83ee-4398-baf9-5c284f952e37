﻿using System;
using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using Dapper;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using ATS.Domain.DTO;

namespace ATS.Data.Repository.Dapper
{
    public class ViagemEventoDapper : DapperFactory<ViagemEvento>, IViagemEventoDapper
    {
        public List<ViagemEvento> GetEventosAbertosParaEmpresa(int IdEmpresa)
        {
            var sql = $@"
                        SELECT
                          vg.idviagemevento, vg.Token, vg.TipoEventoViagem, v.dataemissao as DataEmissaoViagem, vg.ValorPagamento
                        FROM VIAGEM_EVENTO vg
                          JOIN VIAGEM v on (v.idviagem = vg.idviagem)
                          JOIN EMPRESA emp on (emp.idempresa = v.idempresa)
                        WHERE vg.STATUS = 0 AND vg.idempresa = {IdEmpresa}
                          and emp.diasparabloquearpagto > 0
                          and DATEDIFF(DAY, v.DataEmissao, GETDATE()) > emp.diasparabloquearpagto";

            return this.RunSelect(sql.ToString()).ToList();
        }

        public void BloquearPagamento(int idViagemEvento, string motivo)
        {
            var sql = $"Update viagem_evento set status = 1, motivobloqueio = '{motivo}' where idviagemevento = {idViagemEvento}";

            using (IDbConnection dbConnection = this.GetConnection())
                dbConnection.Execute(sql);
        }
        
        public List<int> PeriodoBloqueioEventosAberto(int IdEmpresa)
        {
            var sql = $"select PeriodoBloqueioEventosAberto from EMPRESA where idempresa = {IdEmpresa}";

            using (IDbConnection dbConnection = this.GetConnection())
            {
                var periodoBloqueioEventosAberto = dbConnection.Query<string>(sql).FirstOrDefault();
    
                return string.IsNullOrEmpty(periodoBloqueioEventosAberto) ? null : periodoBloqueioEventosAberto.Split('-').Select(Int32.Parse).ToList();
            }
        }

        public void VincularProtocoloAosEventos(List<int> viagensEventosVincularProtocolo, int idProtocolo)
        {
            string idsViagemEvento = string.Join(",", viagensEventosVincularProtocolo);
            var sql = $"UPDATE VIAGEM_EVENTO SET idprotocolo = {idProtocolo} WHERE idviagemevento IN ({idsViagemEvento})";
            using (IDbConnection dbConnection = this.GetConnection())
            {
                var retorno = dbConnection.Query(sql);
            }
        }
        
        public List<EventoCiotAgregadoDto> ConsultarEventosCiotAgregado(int idContratoCiotAgregado)
        {
            var retorno = new List<EventoCiotAgregadoDto>();
            
            var sql = @"select 
                            coalesce(valortotalpagamento, valorpagamento) as Valor,
                            idviagemevento as IdViagemEvento,
                            tipoeventoviagem as TipoEventoViagem,
                            status as Status
                        from VIAGEM_EVENTO
                        where idviagem in
                        (
                            select idviagem from VIAGEM
                            where iddeclaracaociot in 
                            (
                                select iddeclaracaociot from DECLARACAO_CIOT 
                                where idcontratociotagregado = @IdContratoCiotAgregado
                            )
                            and statusviagem != 3
                        )
                        and status != 3";
            
            var parameters = new {IdContratoCiotAgregado = idContratoCiotAgregado};
            using (IDbConnection dbConnection = this.GetConnection())
            {
                var tarifas = dbConnection.Query<EventoCiotAgregadoDto>(sql, parameters).ToList();
                retorno.AddRange(tarifas);
            }

            return retorno;
        }
    }
}

﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemRegraMap : EntityTypeConfiguration<ViagemRegra>
    {
        public ViagemRegraMap()
        {
            ToTable("VIAGEM_REGRA");

            HasKey(x => x.IdViagemRegra);

            Property(t => t.TaxaAntecipacao)
                .HasPrecision(8, 4);

            Property(t => t.ToleranciaPeso)
                .HasPrecision(8, 4);

            Property(t => t.TarifaTonelada)
                .HasPrecision(10, 2);

            Property(t => t.FreteLotacao);

            Property(t => t.UnidadeMedida)
                .HasMaxLength(5);

            HasRequired(x => x.Viagem)
                .WithMany(x => x.ViagemRegras)
                .HasForeignKey(x => new { x.IdViagem, x.IdEmpresa });
        }
    }
}

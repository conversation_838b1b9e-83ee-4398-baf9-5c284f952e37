﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Collections.Generic;
using ATS.Data.Context;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.EntityFramework
{
    public class SolicitacaoChavePixStatusRepository : Repository<SolicitacaoChavePixStatus>, ISolicitacaoChavePixStatusRepository
    {
        public SolicitacaoChavePixStatusRepository(AtsContext context) : base(context)
        {
        }
    }
}
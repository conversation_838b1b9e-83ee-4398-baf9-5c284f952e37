﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Text.RegularExpressions;
using ATS.CrossCutting.Reports.Motorista.RelatorioListaMotoristas;
using AutoMapper;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class MotoristaService : ServiceBase, IMotoristaService
    {
        private readonly IMotoristaRepository _motoristaRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IUsuarioService _usuarioService;
        private readonly IUsuarioDocumentoService _usuarioDocumentoService;
        private readonly IVeiculoRepository _veiculoRepository;


        public MotoristaService(IMotoristaRepository motoristaRepository, IUsuarioRepository usuarioRepository, IUsuarioService usuarioService, IUsuarioDocumentoService usuarioDocumentoService,
            IVeiculoRepository veiculoRepository)
        {
            _motoristaRepository = motoristaRepository;
            _usuarioRepository = usuarioRepository;
            _usuarioService = usuarioService;
            _usuarioDocumentoService = usuarioDocumentoService;
            _veiculoRepository = veiculoRepository;
        }

        public ValidationResult AlterarStatus(int idMotorista)
        {
            var motoristaRepository = _motoristaRepository;
            var motorista = motoristaRepository.Get(idMotorista);

            motorista.Ativo = !motorista.Ativo;
            motoristaRepository.Update(motorista);

            return new ValidationResult();
        }

        public Motorista Get(int id)
        {
            return _motoristaRepository.Get(id);
        }

        public Motorista GetWithChilds(int id, int? idUsuarioLogOn)
        {
            var motorista =
                _motoristaRepository
                    .Find(m => m.IdMotorista == id)
                    .Include(m => m.Empresa)
                    .Include(m => m.Pais)
                    .Include(m => m.Estado)
                    .Include(m => m.Cidade)
                    .Include(m => m.Moveis)
                    .FirstOrDefault();

            if (motorista != null && idUsuarioLogOn.HasValue && !ValidarPermissoesUsuario(id, idUsuarioLogOn, EProcesso.Get).IsValid)
                return null;

            return motorista;
        }

        public Motorista GetWithChilds(string cpf, int? idUsuarioLogOn)
        {
            var cpfOnlyNumbers = cpf.OnlyNumbers();

            var motorista =
                _motoristaRepository
                    .Find(m => m.CPF == cpfOnlyNumbers)
                    .Include(m => m.Empresa)
                    .Include(m => m.Pais)
                    .Include(m => m.Estado)
                    .Include(m => m.Cidade)
                    .Include(m => m.Moveis)
                    .FirstOrDefault();

            if (motorista != null && idUsuarioLogOn.HasValue && !ValidarPermissoesUsuario(motorista.IdMotorista, idUsuarioLogOn, EProcesso.Get).IsValid)
                return null;

            return motorista;
        }

        public ValidationResult Add(Motorista motorista, int idUsuario, bool ignorePermissaoUsuario = false)
        {
            try
            {
                motorista.Nome = new Regex("[*'\",_&#^@]").Replace(motorista.Nome, String.Empty);
                motorista.CEP = motorista.CEP.OnlyNumbers();
                motorista.Numero = motorista.Numero.OnlyNumbers() != string.Empty ? motorista.Numero.OnlyNumbers() : "0";
                var validationResult = SetVinculoMotoristaUsuario(motorista, idUsuario);

                if (!validationResult.IsValid)
                    return validationResult;

                validationResult = IsValidToCrud(motorista, idUsuario, EProcesso.Create, ignorePermissaoUsuario);

                if (!validationResult.IsValid)
                    return validationResult;

                motorista.DataHoraUltimaAtualizacao = DateTime.Now;
                _motoristaRepository.Add(motorista);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Motorista motorista, int idUsuario, bool ignorePermissaoPerfil = false)
        {
            try
            {
                motorista.Nome = new Regex("[*'\",_&#^@]").Replace(motorista.Nome, String.Empty);
                motorista.CEP = motorista.CEP.OnlyNumbers();
                motorista.Numero = motorista.Numero.OnlyNumbers() != string.Empty ? motorista.Numero.OnlyNumbers() : "0";

                var validationResult = SetVinculoMotoristaUsuario(motorista, idUsuario);

                if (!validationResult.IsValid)
                    return validationResult;

                validationResult = IsValidToCrud(motorista, idUsuario, EProcesso.Update, ignorePermissaoPerfil);
                if (!validationResult.IsValid)
                    return validationResult;

                motorista.DataHoraUltimaAtualizacao = DateTime.Now;
                _motoristaRepository.Update(motorista);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public IQueryable<Motorista> GetPorCpfQueryable(string cpf)
        {
            return _motoristaRepository.Where(c => c.CPF == cpf);
        }

        public Motorista GetPorCpf(string cpf, bool withIncludes = true)
        {
            var cpfOnlyNumbers = cpf?.OnlyNumbers();

            var query = _motoristaRepository
                .Find(m => m.CPF == cpfOnlyNumbers && m.Ativo);

            if (withIncludes)
                query = query
                    .Include(m => m.Moveis)
                    .Include(m => m.Cidade)
                    .Include(m => m.Estado)
                    .Include(m => m.Pais)
                    .Include(m => m.Empresa)
                    .Include(m => m.Empresa.Layout)
                    .Include(m => m.Empresa.Modulos)
                    .Include(m => m.Veiculos);

            return query.FirstOrDefault();
        }

        public int? GetIdPorCpf(string cpf, int? idEmpresa = null, bool? ativo = true)
        {
            var cpfOnlyNumbers = cpf?.OnlyNumbers();

            var query = _motoristaRepository
                .Where(m => m.CPF == cpfOnlyNumbers);

            if (ativo != null)
                query = query.Where(c => c.Ativo == ativo);

            if (idEmpresa.HasValue)
                query = query.Where(c => c.IdEmpresa == idEmpresa.Value);

            var retId = query.Select(c => c.IdMotorista).FirstOrDefault();
            return retId > 0 ? (int?)retId : null;
        }

        public byte[] GetFoto(int id)
        {
            return _motoristaRepository.Find(x => x.IdMotorista == id && x.Ativo)
                .Select(x => x.Foto).FirstOrDefault();
        }

        public IQueryable<Motorista> GetAllByIdEmpresa(int idEmpresa)
        {
            return _motoristaRepository.Find(x => x.IdEmpresa == idEmpresa && x.Ativo);
        }

        public IQueryable<Motorista> QueryById(int id)
        {
            return _motoristaRepository.All().Where(c => c.IdMotorista == id);
        }

        public Motorista Get(string cpf)
        {
            return _motoristaRepository.Where(c => c.CPF == cpf).FirstOrDefault();
        }

        public object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters,bool ativo)
        {
            var mots = _motoristaRepository
                .GetAll()
                .Include(x => x.Empresa);

            if (ativo)
                mots = mots.Where(x => x.Ativo == ativo);

            if (idEmpresa.HasValue)
                mots = mots.Where(o => o.IdEmpresa == idEmpresa);

            mots = string.IsNullOrWhiteSpace(order?.Campo)
                    ? mots.OrderBy(x => x.IdMotorista)
                    : mots.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            mots = mots.AplicarFiltrosDinamicos(filters);

            var count = mots.Count();

            var motsList = mots
                .Skip((page - 1) * take)
                .Take(take)
                .AsEnumerable()
                .Select(x => new
                {
                    x.IdMotorista,
                    x.Nome,
                    x.Empresa?.RazaoSocial,
                    CPF = x.CPF.ToCPFFormato(),
                    Celular = x.Celular.ToTelefoneFormato(),
                    x.Ativo
                })
                .ToList();

            return new
            {
                totalItems = count,
                items = motsList
            };
        }

        public byte[] GerarRelatorioGridMotoristas(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters, string logo, string extensao)
        {
            var listaMotoristasReport = new List<RelatorioMotoristaDataType>();
            var motoristas = GetDataToGridAndReport(idEmpresa, order, filters);

            foreach (var motorista in motoristas)
            {
                listaMotoristasReport.Add(new RelatorioMotoristaDataType
                {
                    Id = motorista.IdMotorista.ToString(),
                    Nome = motorista.Nome,
                    Celular = motorista.Celular.ToTelefoneFormato(),
                    Ativo = motorista.Ativo ? "Sim" : "Não",
                    Sexo = motorista.Sexo == "M" ? "Masculino" : "Feminino",
                    Email = motorista.Email,
                    TipoContrato = motorista.TipoContrato.DescriptionAttr(),
                    CategoriaCnh = motorista.CNHCategoria,
                    Cnh = motorista.CNH,
                    Cpf = motorista.CPF.ToCPFFormato(),
                    Rg = motorista.RG,
                    RgOrgaoExpedidor = motorista.RGOrgaoExpedidor,
                    ValidadeCnh = motorista.ValidadeCNH?.ToShortDateString(),
                    Endereco = FormatarEnderecoMotorista(motorista)
                });
            }

            return new RelatorioMotoristas().GetReport(listaMotoristasReport, extensao, logo);
        }

        public UsuarioDocumento GetDocumentoCnhPorMot(Motorista mot)
        {
            var usuMot = _usuarioRepository.GetIdPorCNPJCPF(mot.CPF.RemoveSpecialCharacters());

            if (usuMot.HasValue)
            {
                var motDocs = _usuarioDocumentoService.GetDocumentos(usuMot.Value);

                if (motDocs.Any())
                {
                    var docCnhMotorista = motDocs.FirstOrDefault(x => x.TipoDocumento.Descricao == "CNH");
                    if (docCnhMotorista != null)
                        return docCnhMotorista;
                }
            }

            return new UsuarioDocumento
            {
                Validade = null
            };
        }

        public int GetQtdMotoristasCadastradosByUser(int idUsuario)
        {
            var usuarioRepository = _usuarioRepository;

            var usuario = usuarioRepository.Get(idUsuario);

            return 0;
        }

        private IEnumerable<Motorista> GetDataToGridAndReport(int? idEmpresa, OrderFilters order, List<QueryFilters> filters)
        {
            var motoristas =
                _motoristaRepository
                    .GetAll()
                    .Include(x => x.Empresa)
                    .Include(x => x.Cidade)
                    .Include(x => x.Estado);

            if (idEmpresa.HasValue)
                motoristas = motoristas.Where(o => o.IdEmpresa == idEmpresa);

            motoristas = string.IsNullOrWhiteSpace(order?.Campo)
                    ? motoristas.OrderBy(x => x.IdMotorista)
                    : motoristas.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            motoristas = motoristas.AplicarFiltrosDinamicos<Motorista>(filters);

            return motoristas;
        }

        private static string FormatarEnderecoMotorista(Motorista motorista)
        {
            var endereco = string.Empty;

            endereco += $"{motorista.Endereco}";

            if (!string.IsNullOrEmpty(motorista.Numero))
                endereco += $", Nº {motorista.Numero}";

            if (!string.IsNullOrEmpty(motorista.Complemento))
                endereco += $", {motorista.Complemento}";

            if (!string.IsNullOrEmpty(motorista.Bairro))
                endereco += $", {motorista.Bairro}";

            if (!string.IsNullOrEmpty(motorista.Cidade?.Nome))
                endereco += $", {motorista.Cidade?.Nome}";

            if (!string.IsNullOrEmpty(motorista.Estado?.Sigla))
                endereco += $" - {motorista.Estado?.Sigla}";

            if (!string.IsNullOrEmpty(motorista.CEP))
                endereco += $", {motorista.CEP}";

            return endereco;
        }

        public bool Any(string cpf, bool? ativo = null)
        {
            var query = _motoristaRepository.Where(c => c.CPF == cpf);

            if (ativo.HasValue)
                query = query.Where(c => c.Ativo == ativo);

            return query.Any();
        }

        public bool Any(string cpf, int idempresa, bool? ativo = null)
        {
            var query = _motoristaRepository.Where(c => c.CPF == cpf && c.IdEmpresa == idempresa);

            if (ativo.HasValue)
                query = query.Where(c => c.Ativo == ativo);

            return query.Any();
        }

        private ValidationResult IsValidToCrud(Motorista motorista, int idUsuario, EProcesso processo, bool ignorePermissaoPerfil = false)
        {
            var validationResult = new ValidationResult();

            #region Motorista

            validationResult.Add(AssertionConcern.AssertArgumentIsValidCPF(motorista.CPF, @"CPF inválido"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(motorista.Nome, @"Nome deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(motorista.CNH, @"Número da CNH deve ser informado"));
            //validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(motorista.RG, @"Número do RG deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(motorista.Endereco, @"Endereço deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(motorista.Bairro, @"Bairro deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNull(motorista.IdPais, @"País deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNull(motorista.IdEstado, @"Estado deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNull(motorista.IdCidade, @"Cidade deve ser informado"));
            //validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(motorista.RGOrgaoExpedidor, @"Orgão Expedidor deve ser informado"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidCEP(motorista.CEP, @"CEP deve ser válido"));

            if (string.IsNullOrWhiteSpace(motorista.RGOrgaoExpedidor))
                motorista.RGOrgaoExpedidor = "SSP";

            if (!string.IsNullOrWhiteSpace(motorista.CNH) && motorista.CNH.Length > 14)
            {
                motorista.CNH = motorista.CNH.Substring(0, 14);
            }
            if (motorista.Celular != null)
                validationResult.Add(AssertionConcern.AssertArgumentIsValidTelefone(motorista.Celular, @"Celular deve ser válido"));

            var cpf = _motoristaRepository.Find(m => m.CPF == motorista.CPF && m.Ativo && m.IdEmpresa == motorista.IdEmpresa).Count();

            if (cpf > 0 && processo == EProcesso.Create)
                return new ValidationResult().Add($"CPF já está cadastrado para um motorista.");

            if (motorista.IdPais == 0)
                return new ValidationResult().Add($"Informe um país válido.");

            #endregion

            #region Usuário

            var usuario = _usuarioRepository.GetWithRelationships(idUsuario);

            if (usuario != null)
            {
                switch (usuario.Perfil)
                {
                    case EPerfil.Empresa:

                        // Empresa não poderá cadastrar um motorista para uma Empresa diferente da que o usuário já está vinculado.
                        if (motorista.IdEmpresa > 0 && motorista.IdEmpresa != usuario.IdEmpresa)
                            return new ValidationResult().Add($"Não é permitido ao seu perfil adicionar um motorista a outro Empresa");

                        break;
                }

                if (!validationResult.IsValid)
                    return validationResult;

                validationResult = usuario.IsValid();

                if (usuario.Perfil != EPerfil.Administrador && usuario.Perfil != EPerfil.Empresa && !ignorePermissaoPerfil)
                    validationResult.Add("Você não tem permissão para salvar esse registro.");
            }

            #endregion

            //#region Tipo de Contrato

            //if (motorista.IdEmpresa > 0 && motorista.TipoContrato == ETipoContrato.Terceiro)
            //    return new ValidationResult().Add("Tipo de Contrato Terceiro não pode estar vinculado a um Empresa");

            //#endregion

            return validationResult;
        }

        private ValidationResult SetVinculoMotoristaUsuario(Motorista motorista, int idUsuario)
        {
            var usuario = _usuarioRepository.GetPorCNPJCPF(motorista.CPF.OnlyNumbers(), true, motorista.IdEmpresa);

            if(usuario == null)
                usuario = _usuarioRepository.GetPorCNPJCPF(motorista.CPF.OnlyNumbers());

            if (usuario != null && usuario.Perfil == EPerfil.Motorista)
            {

                usuario.IdEmpresa = !motorista.Ativo ? null : motorista.IdEmpresa;
                return _usuarioService.Update(usuario, idUsuario);
            }

            return new ValidationResult();
        }

        private ValidationResult ValidarPermissoesUsuario(int? id, int? idUsuarioLogOn, EProcesso processo)
        {
            if (!idUsuarioLogOn.HasValue)
                return new ValidationResult().Add($"Usuário não localizado.");

            var perfil = _usuarioRepository.GetPerfil(idUsuarioLogOn.Value);

            if (perfil != EPerfil.Administrador && perfil != EPerfil.Empresa && perfil != EPerfil.Proprietario)
                return new ValidationResult().Add($"Perfil do usuário logado não permite manipulação destas informações.");

            switch (processo)
            {
                case EProcesso.Get:

                    if (perfil == EPerfil.Empresa)
                    {
                        var idTranspUsuario = _usuarioRepository.GetIdEmpresa(idUsuarioLogOn.GetValueOrDefault());
                        if (!idTranspUsuario.HasValue)
                            return new ValidationResult().Add($"Usuário sem vínculo com o empresa.");

                        var motorista = _motoristaRepository.Get(id.GetValueOrDefault());
                        if (motorista == null || motorista.IdEmpresa != idTranspUsuario.GetValueOrDefault())
                            return new ValidationResult().Add($"Usuário não esta vinculado ao empresa do motorista.");
                    }

                    break;
            }

            return new ValidationResult();
        }

        public Motorista GetFromAllTables(string cpfCnpj)
        {
            return _motoristaRepository.GetFromAllTables(cpfCnpj);
        }

        public List<Motorista> GetMotoristasAtualizados(int idEmpresa, DateTime dataAtualizacao)
        {
            return _motoristaRepository.GetIdsMotoristasAtualizados(dataAtualizacao).ToList();
        }

        public List<Veiculo> GetVeiculosMotorista(string cpfMotorista, int? idEmpresa)
        {
            var motoristaRepository = _motoristaRepository;
            var idMotorista = motoristaRepository.GetIdMotoristaPorCpf(cpfMotorista);
            List<Veiculo> veiculoRepository = _veiculoRepository.Where(o => o.IdMotorista == idMotorista && o.IdEmpresa == idEmpresa).ToList();

            return veiculoRepository;
        }

        public ValidationResult DesvincularMotoristaVeiculo(int idEmpresa, string cpfMotorista, string placa)
        {
            try
            {
                var motoristaRepository = _motoristaRepository;
                var veiculoRepository = _veiculoRepository;

                var motorista = motoristaRepository.Find(o => o.CPF == cpfMotorista).Include(o => o.Veiculos);

                var veiculo = motorista.FirstOrDefault()?.Veiculos.FirstOrDefault(o => o.Placa == placa);

                if (veiculo == null)
                    return new ValidationResult().Add("Nenhum veículo vinculado a este motorista nesta empresa.");

                if (veiculo.IdEmpresa != idEmpresa)
                    return
                        new ValidationResult().Add(
                            "Não é possível realizar o desvinculo, o veículo vinculado não pertence a empresa do usuário logado.");

                veiculo.IdMotorista = null;
                veiculoRepository.Update(veiculo);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}
﻿using System;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Ajax.Utilities;
using NLog;
using Sistema.Framework.Util.Extension;

namespace ATS.CrossCutting.IoC.Utils
{
    public static class StringUtil
    {
        public static string OnlyNumbers2(this string aValue)
        {
            return aValue.IsNullOrWhiteSpace() ? aValue : string.Join("", Regex.Split(aValue, @"[^\d]"));
        }

        public static string FormatCpf(this string value)
        {
            var longConverted = Convert.ToInt64(value);
            return $@"{longConverted:000\.000\.000\-00}";
        }

        public static string ToCepFormato(this string cep)
        {
            if (string.IsNullOrEmpty(cep))
                return string.Empty;

            return Convert.ToUInt64(cep).ToString(@"00000\-000");
        }

        public static string FormatarPlaca(this string placa)
        {
            placa = placa.RemoverCaracteresEspeciais();

            if (placa.IsNullOrWhiteSpace())
                return placa;

            if (placa.Length < 7)
                return placa;
            return $"{placa.Substring(0, 3)}-{placa.Substring(3, 4)}";
        }

        public static string RemoverCaracteresEspeciais(this string value)
        {
            if (string.IsNullOrWhiteSpace(value)) return value;
            return Regex.Replace(value, "[^a-zA-Z0-9_.]+", "", RegexOptions.Compiled);
        }

        public static string FormatAsTelefone(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            const string template = "({0}) {1}-{2}";

            if (value.Length == 10)
                return string.Format(template,
                                    value.Substring(0, 2),
                                    value.Substring(2, 4),
                                    value.Substring(6, 4));

            if (value.Length == 11)
                return string.Format(template,
                                    value.Substring(0, 2),
                                    value.Substring(2, 5),
                                    value.Substring(7, 4));

            return string.Empty;
        }

        public static bool IsValidCPF(string aValue)
        {
            if (aValue == null)
                return false;

            int[] lMt1 = new int[9] { 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            int[] lMt2 = new int[10] { 11, 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            string lTempCPF;
            string lDigito;
            int lSoma;
            int lResto;

            aValue = aValue.OnlyNumbers2().Trim();

            if (aValue.Length != 11)
                return false;

            lTempCPF = aValue.Substring(0, 9);
            lSoma = 0;
            for (int i = 0; i < 9; i++)
                lSoma += int.Parse(lTempCPF[i].ToString()) * lMt1[i];

            lResto = lSoma % 11;
            if (lResto < 2)
                lResto = 0;
            else
                lResto = 11 - lResto;

            lDigito = lResto.ToString();
            lTempCPF = lTempCPF + lDigito;
            lSoma = 0;

            for (int i = 0; i < 10; i++)
                lSoma += int.Parse(lTempCPF[i].ToString()) * lMt2[i];

            lResto = lSoma % 11;
            if (lResto < 2)
                lResto = 0;
            else
                lResto = 11 - lResto;

            lDigito = lDigito + lResto;
            if (!aValue.EndsWith(lDigito))
                return false;

            if (aValue[0] == aValue[1] && aValue[0] == aValue[2] && aValue[0] == aValue[3] && aValue[0] == aValue[4] && aValue[0] == aValue[5] &&
                aValue[0] == aValue[6] && aValue[0] == aValue[7] && aValue[0] == aValue[8] && aValue[0] == aValue[9] && aValue[0] == aValue[10])
                return false;

            return true;
        }

        public static bool IsValidCNPJ(string aValue)
        {
            if (aValue == null)
                return false;

            aValue = aValue.OnlyNumbers2();

            if (aValue.Length < 14)
                return false;

            int[] lDigitos, lSoma, lResultado;
            int lNrDig;
            string lFtmt;
            bool[] lCNPJOk;

            lFtmt = "6543298765432";
            lDigitos = new int[14];
            lSoma = new int[2];
            lSoma[0] = 0;
            lSoma[1] = 0;
            lResultado = new int[2];
            lResultado[0] = 0;
            lResultado[1] = 0;
            lCNPJOk = new bool[2];
            lCNPJOk[0] = false;
            lCNPJOk[1] = false;

            for (lNrDig = 0; lNrDig < 14; lNrDig++)
            {
                lDigitos[lNrDig] = int.Parse(aValue.Substring(lNrDig, 1));

                if (lNrDig <= 11)
                    lSoma[0] += (lDigitos[lNrDig] * int.Parse(lFtmt.Substring(lNrDig + 1, 1)));

                if (lNrDig <= 12)
                    lSoma[1] += (lDigitos[lNrDig] * int.Parse(lFtmt.Substring(lNrDig, 1)));
            }

            for (lNrDig = 0; lNrDig < 2; lNrDig++)
            {
                lResultado[lNrDig] = (lSoma[lNrDig] % 11);
                if ((lResultado[lNrDig] == 0) || (lResultado[lNrDig] == 1))
                    lCNPJOk[lNrDig] = (lDigitos[12 + lNrDig] == 0);
                else
                    lCNPJOk[lNrDig] = (lDigitos[12 + lNrDig] == (11 - lResultado[lNrDig]));
            }

            if (!(lCNPJOk[0] && lCNPJOk[1]))
                return false;

            return true;
        }

        public static bool ContainsInsensitive(this string source, string search)
        {
            return (new CultureInfo("pt-BR").CompareInfo).IndexOf(source, search, CompareOptions.IgnoreCase | CompareOptions.IgnoreNonSpace) >= 0;
        }

        public static string RemoverAcentosPalavra(this string text)
        {
            string comAcentos = "ÄÅÁÂÀÃäáâàãÉÊËÈéêëèÍÎÏÌíîïìÖÓÔÒÕöóôòõÜÚÛüúûùÇç";
            string semAcentos = "AAAAAAaaaaaEEEEeeeeIIIIiiiiOOOOOoooooUUUuuuuCc";

            for (int i = 0; i < comAcentos.Length; i++)
            {
                text = text.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
            }

            return text;
        }

        public static string RemoveAccents(this string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            StringBuilder sbReturn = new StringBuilder();
            var arrayText = text.Normalize(NormalizationForm.FormD).ToCharArray();
            foreach (char letter in arrayText)
            {
                if (CharUnicodeInfo.GetUnicodeCategory(letter) != UnicodeCategory.NonSpacingMark)
                    sbReturn.Append(letter);
            }
            return sbReturn.ToString();
        }

        /// <summary>
        /// Caso o texto indicado ser null ou espação em branco, retornar o valor padrão indicado pelo segundo parâmetro
        /// </summary>
        /// <param name="value"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static string DefaultIfNullOrWhiteSpace(this string value, string defaultValue)
        {
            return value.IsNullOrWhiteSpace() ? defaultValue : value;
        }

        public static string FormatTime(this string value)
        {
            if (value.Length == 1)
            {
                var newValue = value.Insert(0, "000");
                return newValue.Insert(2, ":");
            }

            if (value.Length == 2)
            {
                var newValue = value.Insert(0, "00");
                return newValue.Insert(2, ":");
            }

            if (value.Length == 3)
            {
                var newValue = value.Insert(0, "0");
                return newValue.Insert(2, ":");
            }

            return value.Insert(2, ":");

        }

        public static string ReplaceNewLinePerSpace(string value)
        {
            return value.Replace("\n", " ");
        }

        public static bool ValidateDocument(this string document)
        {
            return !string.IsNullOrEmpty(document) && IsValidDocument(document);
        }

        private static bool IsValidDocument(string document)
        {
            if (document.Length != 11 && document.Length != 14)
                return false;

            switch (document.Length)
            {
                case 11 when IsValidCpf(document):
                    return true;
                case 14 when IsValidCnpj(document):
                    return true;
                default:
                    return false;
            }
        }

        private static bool IsValidCpf(string cpf)
        {
            var multiplicator1 = new int[9] { 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            var multiplicator2 = new int[10] { 11, 10, 9, 8, 7, 6, 5, 4, 3, 2 };

            cpf = cpf.Trim().Replace(".", "").Replace("-", "");

            if (cpf.Length != 11)
                return false;

            for (var j = 0; j < 10; j++)
                if (j.ToString().PadLeft(11, char.Parse(j.ToString())) == cpf)
                    return false;

            var tempCpf = cpf.Substring(0, 9);
            var sum = 0;

            for (var i = 0; i < 9; i++)
                sum += int.Parse(tempCpf[i].ToString()) * multiplicator1[i];

            var rest = sum % 11;
            if (rest < 2)
                rest = 0;
            else
                rest = 11 - rest;

            var digit = rest.ToString();
            tempCpf = tempCpf + digit;
            sum = 0;

            for (var i = 0; i < 10; i++)
                sum += int.Parse(tempCpf[i].ToString()) * multiplicator2[i];

            rest = sum % 11;

            if (rest < 2)
                rest = 0;
            else
                rest = 11 - rest;

            digit = digit + rest;

            return cpf.EndsWith(digit);
        }

        private static bool IsValidCnpj(string cnpj)
        {
            var multiplicator1 = new int[12] { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            var multiplicator2 = new int[13] { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };

            cnpj = cnpj.Trim().Replace(".", "").Replace("-", "").Replace("/", "");
            if (cnpj.Length != 14)
                return false;

            var tempCnpj = cnpj.Substring(0, 12);
            var sum = 0;

            for (var i = 0; i < 12; i++)
                sum += int.Parse(tempCnpj[i].ToString()) * multiplicator1[i];

            var rest = (sum % 11);
            if (rest < 2)
                rest = 0;
            else
                rest = 11 - rest;

            var digit = rest.ToString();
            tempCnpj = tempCnpj + digit;
            sum = 0;

            for (var i = 0; i < 13; i++)
                sum += int.Parse(tempCnpj[i].ToString()) * multiplicator2[i];

            rest = (sum % 11);

            if (rest < 2)
                rest = 0;
            else
                rest = 11 - rest;

            digit = digit + rest;

            return cnpj.EndsWith(digit);
        }

        public static string ToDocument(this string value)
        {
            switch (value?.Length)
            {
                case 11:
                    return ToCpfFormato(value);
                case 14:
                    return ToCnpjFormato(value);
            }

            return string.Empty;
        }

        public static string ToCpfFormato(this string cnpj)
        {
            return !string.IsNullOrWhiteSpace(cnpj) ? Convert.ToUInt64(cnpj).ToString(@"000\.000\.000\-00") : "";
        }

        public static string ToCnpjFormato(this string cnpj)
        {
            return Convert.ToUInt64(cnpj).ToString(@"00\.000\.000\/0000\-00");
        }

        /// <summary>
        /// Formatar string como CPF ou CNPJ de forma segura. Valores inesperados e exceções retornam o próprio argumento de entrada
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string FormatarCpfCnpjSafe(this string value)
        {
            if (value.IsNullOrWhiteSpace())
                return string.Empty;

            try
            {
                var valueNumbers = value.OnlyNumbers();
                return valueNumbers.Length switch
                {
                    11 => string.Format(@"{0:000\.000\.000\-00}", long.Parse(valueNumbers)),
                    14 => string.Format(@"{0:00\.000\.000\/0000\-00}", long.Parse(valueNumbers)),
                    _ => value
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"Erro ao formatar CPF/CNPJ: {value}");
                return value;
            }
        }
    }
}

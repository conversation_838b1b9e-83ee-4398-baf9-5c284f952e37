﻿namespace Extratta.ImportacaoDadosConsumer.Events
{
    public class FilialIntegrarFilaEvent
    {
        public int CodigoIbgeCidade { get; set; }
        public int CodigoIbgeEstado { get; set; }
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Sigla { get; set; }
        public string Cep { get; set; }
        public string Endereco { get; set; }
        public short? Numero { get; set; }
        public string Bairro { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public string CNPJEmpresa { get; set; }
        public string Token { get; set; }
        public int CodigoBacenPais { get; set; }
    }
}
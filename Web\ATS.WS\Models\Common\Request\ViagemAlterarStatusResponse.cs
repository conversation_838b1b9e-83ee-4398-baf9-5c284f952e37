using ATS.Domain.Enum;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Models.Common.Request
{
    public class ViagemAlterarStatusResponse : Retorno<ViagemAlterarStatusResponse.ObjetoDetalhe>
    {
        public class ObjetoDetalhe
        {
            public EViagemAlterarStatusResponseTipoFalha CodigoFalha { get; set; } = EViagemAlterarStatusResponseTipoFalha.Erro;
        }

        public ViagemAlterarStatusResponse()
        {
        }

        public ViagemAlterarStatusResponse(string mensagem) : base(mensagem)
        {
        }

        public ViagemAlterarStatusResponse(bool sucesso) : base(sucesso)
        {
        }

        public ViagemAlterarStatusResponse(bool sucesso, ObjetoDetalhe retorno) : base(sucesso, retorno)
        {
        }

        public ViagemAlterarStatusResponse(bool sucesso, string mensagem, ObjetoDetalhe retorno) : base(sucesso, mensagem, retorno)
        {
        }
    }
}
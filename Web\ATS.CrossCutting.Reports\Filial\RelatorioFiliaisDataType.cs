﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.CrossCutting.Reports.Filial
{
    public class RelatorioFiliaisDataType
    {
        /// <summary>
        /// <PERSON>ó<PERSON> da Filial
        /// </summary>
        public int IdFilial { get; set; }

        /// <summary>
        /// CNPJ
        /// </summary>
        public string CNPJ { get; set; }
        
        /// <summary>
        /// Razão Social
        /// </summary>
        public string RazaoSocial { get; set; }

        /// <summary>
        /// Nome da Fantasia
        /// </summary>
        public string NomeFantasia { get; set; }

        /// <summary>
        /// Sigla da Filial
        /// </summary>
        public string Sigla { get; set; }

        /// <summary>
        /// Endereço
        /// </summary>
        public string Endereco { get; set; }

        /// <summary>
        /// Telefone 
        /// </summary>
        public string Telefone { get; set; }

        /// <summary>
        /// E-mail para contato
        /// </summary
        public string Email { get; set; }

        public string Ativo { get; set; }

    }
}

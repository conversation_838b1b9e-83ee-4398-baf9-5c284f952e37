using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioPermissaoCartaoMap : EntityTypeConfiguration<UsuarioPermissaoCartao>
    {

        public UsuarioPermissaoCartaoMap()
        {
            ToTable("USUARIO_PERMISSAO_CARTAO");

            HasKey(t => new {t.IdUsuario,t.IdBloqueioCartaoTipo});
            Property(u => u.DesbloquearCartao).IsRequired();

            HasRequired(u => u.Usuario)
                .WithMany()
                .HasForeignKey(u => u.IdUsuario);
            
            HasRequired(u => u.BloqueioCartaoTipo)
                .WithMany()
                .HasForeignKey(u => u.IdBloqueioCartaoTipo);

        }
    }
}
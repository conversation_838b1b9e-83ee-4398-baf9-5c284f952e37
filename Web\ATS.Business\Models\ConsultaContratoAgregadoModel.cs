﻿namespace ATS.Domain.Models
{
    public class ConsultaContratoAgregadoModel
    {
        /// <summary>
        /// Código do contrato de CIOT agregado
        /// </summary>
        public int IdContratoCiotAgregado { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public string NomeEmpresa { get; set; }

        /// <summary>
        /// Código do proprietario
        /// </summary>
        public string NomeProprietario { get; set; }

        /// <summary>
        /// Data de coleta da viagem
        /// </summary>
        public string DataInicio { get; set; }

        /// <summary>
        /// Data de coleta da viagem
        /// </summary>
        public string DataFinal { get; set; }

        /// <summary>
        /// Código da declaração de CIOT
        /// </summary>
        public int? IdDeclaracaoCiot { get; set; }

        /// <summary>
        /// Data de cadastro
        /// </summary>
        public string DataCadastro { get; set; }

        /// <summary>
        /// Código da situação da integração
        /// </summary>
        public int StatusContratoAgregado { get; set; }
        
        /// <summary>
        /// Resultado da declaração do CIOT
        /// </summary>
        public int ResultadoDeclaracaoCiot { get; set; }
        
        /// <summary>
        /// Descrição da situação da integração
        /// </summary>
        public string StatusContratoAgregadoDescricao { get; set; }

        /// <summary>
        /// Código da situação da integração
        /// </summary>
        public string MensagemIntegracao { get; set; }
        
        /// <summary>
        /// Código da geração do CIOT
        /// </summary>
        public string Ciot { get; set; }
        public string NumeroCiot { get; set; }
        
        /// <summary>
        /// Senha da geração do CIOT
        /// </summary>
        public string Senha { get; set; }

        /// <summary>
        /// Aviso
        /// </summary>
        public string AvisoContratoAgregadoDescricao { get; set; }
        
        /// <summary>
        /// Id da empresa
        /// </summary>
        public int EmpresaId { get; set; }
    }
}
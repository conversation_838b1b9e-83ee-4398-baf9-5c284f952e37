﻿using ATS.Application.Application;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Web.Mvc;

namespace ATS.WS.ControllersATS
{
    public class EnumAtsController : DefaultController
    {
        [HttpGet]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(string EnumName)
        {
            try
            {
                var retorno = new EnumApp().Consultar(EnumName);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Categoria;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Application.Application
{
    public class CategoriaApp : BaseApp<ICategoriaService>, ICategoriaApp
    {
        public CategoriaApp(ICategoriaService service) : base(service)
        {
        }

        public BusinessResult<CategoriaAddModelResponse> Add(CategoriaAddModel model)
        {
            return Service.Add(model);
        }

        public BusinessResult<CategoriaDisableModelResponse> Disable(CategoriaDisableModel model)
        {
            return Service.Disable(model);
        }

        public BusinessResult<CategoriaEnableModelResponse> Enable(CategoriaEnableModel model)
        {
            return Service.Enable(model);
        }

        public BusinessResult<CategoriaGetModelResponse> Get(int categoriaId)
        {
            return Service.Get(categoriaId);
        }

        public BusinessResult<IList<CategoriaGetModelResponse>> GetAll(bool filterActive)
        {
            return Service.GetAll(filterActive);
        }

        public BusinessResult<CategoriaGetGridModelResponse> GetGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.GetGrid(take, page, order, filters);
        }

        public BusinessResult<CategoriaUpdateModelResponse> Update(CategoriaUpdateModel model)
        {
            return Service.Update(model);
        }
    }
}

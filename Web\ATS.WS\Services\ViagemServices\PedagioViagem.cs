using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Service;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using Extratta.Tag.Application.Events.Events;
using MassTransit;
using NLog;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ValidationResult = ATS.Domain.Validation.ValidationResult;

namespace ATS.WS.Services.ViagemServices
{
    public class PedagioViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly IRotaModeloApp _rotaModeloApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IPublishEndpoint _publisher;
        private readonly IVeiculoRepository _veiculoRepository;

        public PedagioViagem(IVersaoAnttLazyLoadService versaoAntt, IParametrosApp parametrosApp, IViagemApp viagemApp,IRotaModeloApp rotaModeloApp,
            ICidadeApp cidadeApp, IEmpresaApp empresaApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IPublishEndpoint publisher, IVeiculoRepository veiculoRepository)
        {
            _versaoAntt = versaoAntt;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _rotaModeloApp = rotaModeloApp;
            _cidadeApp = cidadeApp;
            _empresaApp = empresaApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _publisher = publisher;
            _veiculoRepository = veiculoRepository;
        }

        public SolicitarCompraPedagioResponseDTO SolicitarCompraPedagio(ViagemIntegrarRequestModel @params, Viagem viagem, DeclararCiotResult ciotResult)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return SolicitarCompraPedagioV2(@params, viagem, ciotResult);
                case EVersaoAntt.Versao3:
                    return SolicitarCompraPedagioV3(@params, viagem, ciotResult);
                default:
                    return SolicitarCompraPedagioV2(@params, viagem, ciotResult);
            }
        }

        #region Validações compra pedágio

        public ValidationResult ValidacoesCompraPedagio(ViagemIntegrarRequestModel @params)
        {
            if (@params.Pedagio != null)
            {
                if (@params.Pedagio.Fornecedor == FornecedorEnum.Desabilitado)
                    return new ValidationResult();

                if (@params.Pedagio.ValorPedagio.HasValue && @params.Pedagio.ValorPedagio.Value > 0)
                    return new ValidationResult();

                if (@params.ValorPedagio.HasValue && @params.ValorPedagio.Value > 0)
                    return new ValidationResult();

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, @params.CNPJEmpresa, CartoesService.AuditDocIntegracao, null);

                if (@params.Pedagio.IdentificadorHistorico.HasValue)
                {
                    var request = new ConsultaHistoricoRotaRequest();
                    request.ConsultaCustoHistoricoId = @params.Pedagio.IdentificadorHistorico;
                    request.RecalcularValorParaEixos = @params.Pedagio.QtdEixos > 0 ? @params.Pedagio.QtdEixos : (int?) null;

                    var historicoRota = cartoesApp.ConsultaHistoricoRota(request);

                    if (historicoRota.Status == ConsultaHistoricoRotaResponseStatus.Sucesso)
                    {
                        if (@params.Pedagio.QtdEixos == 0 && historicoRota.QtdEixos.HasValue)
                            @params.Pedagio.QtdEixos = historicoRota.QtdEixos.Value;

                        return new ValidationResult();
                    }
                }

                if (@params.Pedagio.Localizacoes != null && @params.Pedagio.TipoVeiculo.HasValue)
                {
                    var consultaRotaRequest = new ConsultaRotaRequest
                    {
                        QtdEixos = @params.Pedagio.QtdEixos,
                        TipoVeiculo = (ConsultaRotaRequestTipoVeiculo) @params.Pedagio.TipoVeiculo,
                        ExibirDetalhes = false
                    };

                    switch (@params.Pedagio.TipoVeiculo)
                    {
                        case ETipoVeiculoPedagioEnum.Carro:
                            consultaRotaRequest.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Carro;
                            break;
                        case ETipoVeiculoPedagioEnum.Motocicleta:
                            consultaRotaRequest.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Motocicleta;
                            break;
                        case ETipoVeiculoPedagioEnum.Onibus:
                            consultaRotaRequest.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Onibus;
                            break;
                        case ETipoVeiculoPedagioEnum.Caminhao:
                            consultaRotaRequest.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Caminhao;
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }

                    var locationList = new List<LocationDTO>();

                    foreach (var localizacao in @params.Pedagio.Localizacoes)
                    {
                        var cidade = _cidadeApp.GetCidadeByIBGE(localizacao.IbgeCidade);

                        if (cidade == null && (!localizacao.Latitude.HasValue || !localizacao.Longitude.HasValue))
                            return new ValidationResult().Add($"Cidade com IBGE {localizacao.IbgeCidade} não é válida para solicitação de compra de pedágio. Informe uma cidade com IBGE válido ou a latitide / longitude.");

                        var locationDto = new LocationDTO
                        {
                            Latitude = cidade != null ? cidade.Latitude : localizacao.Latitude,
                            Longitude = cidade != null ? cidade.Longitude : localizacao.Longitude,
                            Ibge = localizacao.IbgeCidade
                        };

                        locationList.Add(locationDto);
                        consultaRotaRequest.Localizacoes = locationList;
                    }

                    var empresa = _empresaApp.Get(@params.CNPJEmpresa);

                    consultaRotaRequest.DesabilitaCacheRotas = empresa?.DesabilitaCacheRotas;

                    var response = cartoesApp.ConsultarCustoRota(consultaRotaRequest);

                    if (response.Status == ConsultaRotaResponseDtoStatus.Sucesso)
                    {
                        if (response.CustoTotal.HasValue && response.CustoTotal.Value > 0)
                        {
                            @params.Pedagio.IdentificadorHistorico = response.IdentificadorHistorico;
                            return new ValidationResult();
                        }
                    }
                    else
                    {
                        return new ValidationResult().Add(response.Mensagem);
                    }
                }

                return new ValidationResult().Add("Necessário informar um valor para o pedágio, Id ou Nome de Rota cadastrado no sistema ou histórico de pedágio ou localizações que gerem valor de pedágio.");
            }

            return new ValidationResult();
        }

        #endregion

        #region Solicitação de compra de pedágio

        private SolicitarCompraPedagioResponseDTO SolicitarCompraPedagioV2(ViagemIntegrarRequestModel @params, Viagem viagem, DeclararCiotResult ciotResult)
        {
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, @params.CNPJEmpresa, CartoesService.AuditDocIntegracao, null);
            var empresaApp = _empresaApp;
            var parametrosApp = _parametrosApp;
            decimal? valorLimite = null;
            
            int? horasExpiracaoPedagio = parametrosApp.GetHorasExpiracaoCreditoPedagio(viagem.IdEmpresa);
            var diasExpiracaoCompraPedagio = empresaApp.GetDiasExpiracaoCompraPedagio(viagem.IdEmpresa);
            var registraValePedagio = parametrosApp.GetRegistrarValePedagio(viagem.IdEmpresa);
            var lVeiculosViagem = new List<string>()
            {
                viagem.Placa
            };
            
            // Validação Carretas
            if(@params.Carretas != null && @params.Carretas.Any())
                lVeiculosViagem.AddRange(@params.Carretas);
            if(viagem.ViagemCarretas != null && viagem.ViagemCarretas.Any())
                lVeiculosViagem.AddRange(viagem.ViagemCarretas.Select(x => x.Placa).ToList());
            if(@params.CarretasV2 != null && @params.CarretasV2.Any())
                lVeiculosViagem.AddRange(@params.CarretasV2.Select(x => x.Placa).ToList());
            
            var rodagemDupla = _veiculoRepository
                .Any(x => lVeiculosViagem.Contains(x.Placa) 
                          && x.Ativo && x.IdEmpresa == viagem.IdEmpresa && x.TipoRodagem == ETipoRodagem.Duplo);

            var usarRodagemDuplaQuandoVem3OuMaisEixos = 
                @params.Pedagio?.QtdEixos >= 3 && !rodagemDupla && 
                (@params.Pedagio.Fornecedor == FornecedorEnum.MoveMais || @params.Pedagio.Fornecedor == FornecedorEnum.Veloe) && 
                _parametrosApp.GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(viagem.IdEmpresa);
            
            var empresaUtilizaValorProvisionamento = false;
            var empresaUtilizaTaxaProvisionamento = false;
            var valorTaxaUso = 0m;
            var pracasAntt = new List<string>();

            switch (@params.Pedagio?.Fornecedor)
            {
                case FornecedorEnum.ExtrattaTag:
                    valorTaxaUso = parametrosApp.GetTagExtrattaTaxaVpo(viagem.IdEmpresa);
                    empresaUtilizaValorProvisionamento = parametrosApp.GetTagExtrattaProvisionarValor(viagem.IdEmpresa);
                    empresaUtilizaTaxaProvisionamento = parametrosApp.GetTagExtrattaProvisionarTaxa(viagem.IdEmpresa);
                    break;
                case FornecedorEnum.MoveMais:
                    rodagemDupla = rodagemDupla || usarRodagemDuplaQuandoVem3OuMaisEixos;
                    valorTaxaUso = parametrosApp.GetMoveMaisExtrattaTaxaVpo(viagem.IdEmpresa);
                    empresaUtilizaValorProvisionamento = parametrosApp.GetHubMoveMaisProvisionarValor(viagem.IdEmpresa);
                    empresaUtilizaTaxaProvisionamento = parametrosApp.GetHubMoveMaisProvisionarTaxa(viagem.IdEmpresa);
                    break;
                case FornecedorEnum.ViaFacil:
                    valorTaxaUso = parametrosApp.GetViaFacilExtrattaTaxaVpo(viagem.IdEmpresa);
                    empresaUtilizaValorProvisionamento = parametrosApp.GetHubViaFacilProvisionarValor(viagem.IdEmpresa);
                    empresaUtilizaTaxaProvisionamento = parametrosApp.GetHubViaFacilProvisionarTaxa(viagem.IdEmpresa);
                    break;
                case FornecedorEnum.Veloe:
                    rodagemDupla = rodagemDupla || usarRodagemDuplaQuandoVem3OuMaisEixos;
                    valorTaxaUso = parametrosApp.GetVeloeExtrattaTaxaVpo(viagem.IdEmpresa);
                    empresaUtilizaValorProvisionamento = parametrosApp.GetHubVeloeProvisionarValor(viagem.IdEmpresa);
                    empresaUtilizaTaxaProvisionamento = parametrosApp.GetHubVeloeProvisionarTaxa(viagem.IdEmpresa);
                    break;
                case FornecedorEnum.ConectCar:
                    valorTaxaUso = parametrosApp.GetConectCarExtrattaTaxaVpo(viagem.IdEmpresa);
                    empresaUtilizaValorProvisionamento = parametrosApp.GetHubConectCarProvisionarValor(viagem.IdEmpresa);
                    empresaUtilizaTaxaProvisionamento = parametrosApp.GetHubConectCarProvisionarTaxa(viagem.IdEmpresa);
                    break;
                case FornecedorEnum.TaggyEdenred:
                    valorTaxaUso = parametrosApp.GetTaggyEdenredExtrattaTaxaVpo(viagem.IdEmpresa);
                    empresaUtilizaValorProvisionamento = parametrosApp.GetHubTaggyEdenredProvisionarValor(viagem.IdEmpresa);
                    empresaUtilizaTaxaProvisionamento = parametrosApp.GetHubTaggyEdenredProvisionarTaxa(viagem.IdEmpresa);
                    break;
                case FornecedorEnum.Outros:
                    pracasAntt = @params.Pedagio?.Pracas.Select(x => x.CodAntt).ToList();
                    break;
            }

            valorTaxaUso = Math.Round(valorTaxaUso,2);

            SolicitarCompraPedagioResponseDTO solicitarCompraPedagio;
            if (ExistSolicitacaoCompra(viagem, cartoesApp, out solicitarCompraPedagio))
                return solicitarCompraPedagio;

            string ciot = null;
            if (ciotResult?.Dados != null)
                ciot = ciotResult.Dados.Ciot + ciotResult.Dados.Verificador;

            if (empresaUtilizaValorProvisionamento || empresaUtilizaTaxaProvisionamento)
            {
                var resultSaldoEmpresa = cartoesApp.ConsultarSaldoEmpresa(@params.CNPJEmpresa);

                if (resultSaldoEmpresa.Status == ConsultarSaldoEmpresaResponseStatus.Falha)
                {
                    return new SolicitarCompraPedagioResponseDTO
                    {
                        Status = EResultadoCompraPedagio.Erro,
                        Mensagem = "Falha ao consultar saldo da empresa para emissão da compra do pedágio."
                    };
                }

                valorLimite = resultSaldoEmpresa.SaldoConta;
            }

            var empresa = _empresaApp
                .All()
                .Include(x => x.Estado)
                .Include(x => x.Cidade)
                .Where(c => c.CNPJ ==  @params.CNPJEmpresa).Select(c => new
                {
                    c.CNPJ,
                    c.PedagioTag,
                    c.RazaoSocial,
                    c.NomeFantasia,
                    c.Numero,
                    c.Endereco,
                    c.Telefone,
                    Estado = c.Estado.Sigla,
                    c.Email,
                    Cidade = c.Cidade.Nome,
                    c.CEP,
                    c.Bairro
                }).FirstOrDefault();

            var requestModel = new SolicitarCompraPedagioRequest
            {
                DocumentoFavorecido = viagem.CPFMotorista,
                Fornecedor = @params.Pedagio?.Fornecedor != null ? (SolicitarCompraPedagioRequestFornecedor?) @params.Pedagio.Fornecedor : null,
                NomeFavorecido = viagem.NomeMotorista,
                DocumentoProprietario = viagem.CPFCNPJProprietario,
                NomeProprietario = viagem.NomeProprietario,
                Placa = @params.Pedagio?.Fornecedor == FornecedorEnum.ConectCar ? viagem.Placa.ToPlacaFormato() : viagem.Placa,
                TipoVeiculo = @params.Pedagio?.TipoVeiculo != null ? (SolicitarCompraPedagioRequestTipoVeiculo?) @params.Pedagio.TipoVeiculo : null,
                QtdEixos = @params.Pedagio?.QtdEixos,
                NumeroCIOT = ciot,
                ProtocoloRequisicao = viagem.IdViagem,
                IdentificadorHistorico = @params.Pedagio?.IdentificadorHistorico,
                RegistraValePedagio = registraValePedagio,
                PedagioTag = empresa.PedagioTag,
                ValorLimiteCompra = valorLimite,
                RodagemDupla = rodagemDupla,
                TaxaCompra = valorTaxaUso,
                PracasPedagio = pracasAntt,
                EmpresaUtilizaTaxaProvisionamento = empresaUtilizaTaxaProvisionamento,
                Embarcador = new EmbarcadorEmpresa()
                {
                    Cnpj = empresa.CNPJ,
                    RazaoSocial =  empresa.RazaoSocial,
                    NomeFantasia = empresa.NomeFantasia,
                    Numero = empresa.Numero ?? 0,
                    Endereco = empresa.Endereco,
                    Telefone = empresa.Telefone,
                    Estado = empresa.Estado,
                    Email = empresa.Email,
                    Cidade = empresa.Cidade,
                    Cep = empresa.CEP,
                    Bairro = empresa.Bairro
                },
                DataIncioViagem = viagem.DataColeta,
                DataFimViagem = viagem.DataPrevisaoEntrega
            };

            var processoSaldoResidual = parametrosApp.GetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario));
            if (processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.PadraoEmpresa)
            {
                processoSaldoResidual = parametrosApp.GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(viagem.IdEmpresa);
                if (processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.PadraoEmpresa)
                    processoSaldoResidual = AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora;
            }

            if (processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora)
            {
                var possuiResgateSaldoNoDia = _viagemApp.PossuiResgateDeSaldoResidualSolicitadoNoDia(viagem.CPFMotorista, DateTime.Now);
                if (possuiResgateSaldoNoDia)
                    processoSaldoResidual = AcaoSaldoResidualNovoCreditoCartaoPedagio.Manter;
            }

            switch (processoSaldoResidual)
            {
                case AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora:
                    requestModel.ProcessoSaldoResidual = SolicitarCompraPedagioRequestProcessoSaldoResidual.EstornarSaldo;
                    break;
                case AcaoSaldoResidualNovoCreditoCartaoPedagio.Manter:
                    requestModel.ProcessoSaldoResidual = SolicitarCompraPedagioRequestProcessoSaldoResidual.Manter;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(AcaoSaldoResidualNovoCreditoCartaoPedagio));
            }

            if (@params.Pedagio?.Fornecedor == FornecedorEnum.ExtrattaTag)
                requestModel.Valor = null;
            else if (@params.Pedagio?.ValorPedagio > 0)
                requestModel.Valor = @params.Pedagio.ValorPedagio;
            else if (@params.ValorPedagio > 0)
                requestModel.Valor = @params.ValorPedagio;

            if (horasExpiracaoPedagio.HasValue && horasExpiracaoPedagio.Value > 0)
                requestModel.DataExpiracaoCreditoPendente = DateTime.Now.AddHours(horasExpiracaoPedagio.Value);

            if (diasExpiracaoCompraPedagio.HasValue)
                requestModel.DataExpiracaoCompraPedagio = DateTime.Now.AddDays((double) diasExpiracaoCompraPedagio);

            var compraPedagio = cartoesApp.SolicitarCompraPedagio(viagem.IdEmpresa, requestModel, @params.Pedagio.Localizacoes);

            if (compraPedagio.Status == EResultadoCompraPedagio.Erro && @params.Pedagio.Localizacoes == null)
            {
                if ((@params.Pedagio.NomeRota != null || @params.Pedagio.IdRotaModelo.HasValue) && !@params.Pedagio.IdentificadorHistorico.HasValue)
                {
                    if (@params.Pedagio.NomeRota != null)
                        compraPedagio.Mensagem = $"Rota {@params.Pedagio.NomeRota} não cadastrada.";
                    else
                        compraPedagio.Mensagem = $"Rota com Id: {@params.Pedagio.IdRotaModelo} não cadastrada.";
                }
            }

            if (compraPedagio.Status == EResultadoCompraPedagio.CompraConfirmada && compraPedagio.CompraCredenciaisExtratta)
            {
                if (compraPedagio.Valor.HasValue && compraPedagio.Valor.Value > 0)
                {
                    var valorTaxa = Math.Round(compraPedagio.Valor.Value * valorTaxaUso / 100,2);

                    // Criação evento na API TAG
                    _publisher.Publish(new EventoSaldoCobrancaValePedagioTagEvent
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        Perfil = EPerfil.Empresa,
                        ValorPedagio = compraPedagio.Valor.Value,
                        Placa = viagem.Placa,
                        Recibo = compraPedagio.ProtocoloValePedagio,
                        ViagemId = viagem.IdViagem,
                        Taxa = valorTaxa,
                        Fornecedor = compraPedagio.Fornecedor ?? FornecedorEnum.ExtrattaTag
                    });
                }
            }

            compraPedagio.EstornoSaldoResidualSolicitado =
                processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora;

            if (compraPedagio.Valor != null)
                viagem.ValorPedagio = compraPedagio.Valor.Value;

            viagem.ValePedagioSolicitado = requestModel.RegistraValePedagio ?? true;
            viagem.ProtocoloValePedagio = compraPedagio.ProtocoloValePedagio;
            viagem.ProtocoloEnvioValePedagio = compraPedagio.ProtocoloEnvioValePedagio;

            return compraPedagio;
        }

        private SolicitarCompraPedagioResponseDTO SolicitarCompraPedagioV3(ViagemIntegrarRequestModel @params, Viagem viagem, DeclararCiotResult ciotResult)
        {
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, @params.CNPJEmpresa, CartoesService.AuditDocIntegracao, null);
            var empresaApp = _empresaApp;
            var parametrosApp = _parametrosApp;

            int? horasExpiracaoPedagio = parametrosApp.GetHorasExpiracaoCreditoPedagio(viagem.IdEmpresa);
            var diasExpiracaoCompraPedagio = empresaApp.GetDiasExpiracaoCompraPedagio(viagem.IdEmpresa);
            var registraValePedagio = parametrosApp.GetRegistrarValePedagio(viagem.IdEmpresa);

            SolicitarCompraPedagioResponseDTO solicitarCompraPedagio;
            if (ExistSolicitacaoCompra(viagem, cartoesApp, out solicitarCompraPedagio))
                return solicitarCompraPedagio;

            string ciot = null;
            if (ciotResult?.Dados != null)
                ciot = ciotResult.Dados.Ciot + ciotResult.Dados.Verificador;

            var requestModel = new SolicitarCompraPedagioRequest
            {
                DocumentoFavorecido = viagem.CPFMotorista,
                Fornecedor = @params.Pedagio?.Fornecedor != null
                    ? (SolicitarCompraPedagioRequestFornecedor?) @params.Pedagio.Fornecedor
                    : null,
                NomeFavorecido = viagem.NomeMotorista,
                DocumentoProprietario = viagem.CPFCNPJProprietario,
                NomeProprietario = viagem.NomeProprietario,
                Placa = viagem.Placa,
                TipoVeiculo = @params.Pedagio?.TipoVeiculo != null
                    ? (SolicitarCompraPedagioRequestTipoVeiculo?) @params.Pedagio.TipoVeiculo
                    : null,
                QtdEixos = @params.Pedagio?.QtdEixos,
                NumeroCIOT = ciot,
                ProtocoloRequisicao = viagem.IdViagem,
                // EmpresaId = viagem.IdEmpresa,
                IdentificadorHistorico = @params.Pedagio?.IdentificadorHistorico,
                RegistraValePedagio = registraValePedagio
            };

            var processoSaldoResidual =
                parametrosApp.GetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(
                    StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario));
            if (processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.PadraoEmpresa)
            {
                processoSaldoResidual =
                    parametrosApp.GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(viagem.IdEmpresa);
                if (processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.PadraoEmpresa)
                    processoSaldoResidual = AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora;
            }

            if (processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora)
            {
                var possuiResgateSaldoNoDia =
                    _viagemApp.PossuiResgateDeSaldoResidualSolicitadoNoDia(viagem.CPFMotorista, DateTime.Now);
                if (possuiResgateSaldoNoDia)
                    processoSaldoResidual = AcaoSaldoResidualNovoCreditoCartaoPedagio.Manter;
            }

            switch (processoSaldoResidual)
            {
                case AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora:
                    requestModel.ProcessoSaldoResidual =
                        SolicitarCompraPedagioRequestProcessoSaldoResidual.EstornarSaldo;
                    break;
                case AcaoSaldoResidualNovoCreditoCartaoPedagio.Manter:
                    requestModel.ProcessoSaldoResidual = SolicitarCompraPedagioRequestProcessoSaldoResidual.Manter;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(AcaoSaldoResidualNovoCreditoCartaoPedagio));
            }

            if (@params.Pedagio?.ValorPedagio > 0)
            {
                requestModel.Valor = @params.Pedagio.ValorPedagio;
            }
            else if (@params.ValorPedagio > 0)
            {
                requestModel.Valor = @params.ValorPedagio;
            }

            if (horasExpiracaoPedagio.HasValue && horasExpiracaoPedagio.Value > 0)
                requestModel.DataExpiracaoCreditoPendente = DateTime.Now.AddHours(horasExpiracaoPedagio.Value);

            if (diasExpiracaoCompraPedagio.HasValue)
                requestModel.DataExpiracaoCompraPedagio = DateTime.Now.AddDays((double) diasExpiracaoCompraPedagio);

            var compraPedagio =
                cartoesApp.SolicitarCompraPedagio(viagem.IdEmpresa, requestModel, @params.Pedagio.Localizacoes);
            compraPedagio.EstornoSaldoResidualSolicitado =
                processoSaldoResidual == AcaoSaldoResidualNovoCreditoCartaoPedagio.EstornarParaAdministradora;

            if (viagem.ValorPedagio <= 0 && compraPedagio.Valor != null)
                viagem.ValorPedagio = compraPedagio.Valor.Value;

            viagem.ValePedagioSolicitado = requestModel.RegistraValePedagio ?? true;
            viagem.ProtocoloValePedagio = compraPedagio.ProtocoloValePedagio;
            viagem.ProtocoloEnvioValePedagio = compraPedagio.ProtocoloEnvioValePedagio;

            return compraPedagio;
        }

        #endregion

        #region Consultar rota do pedafio

        public CustoPedagioRotaResponseModel ConsultarCustoPedagioRota(CustoPedagioRotaRequestModel request)
        {
            var empresa = _empresaApp.Get(request.CNPJEmpresa);

            if (request.Polyline != null)
            {
                var permiteUtilizarPolyline = _parametrosApp.GetUtilizaRoteirizacaoPorPolyline(empresa?.IdEmpresa ?? 0);

                if (empresa?.IdEmpresa != null && !permiteUtilizarPolyline)
                {
                    return new CustoPedagioRotaResponseModel
                    {
                        Status = ConsultaRotaResponseDtoStatus.Falha,
                        Mensagem = "Empresa não possui permissão para roterização com polyline!"
                    };
                }
                
                var localizacaoPolyline = DecodePolylineFirstAndLast(request.Polyline);

                if (!localizacaoPolyline.Any())
                {
                    return new CustoPedagioRotaResponseModel
                    {
                        Status = ConsultaRotaResponseDtoStatus.Falha,
                        Mensagem = "Falha ao descriptografar polyline!"
                    };
                }
                
                request.Localizacoes = localizacaoPolyline;
            }
            
            if (request.Localizacoes == null)
            {
                return new CustoPedagioRotaResponseModel
                {
                    Status = ConsultaRotaResponseDtoStatus.Falha,
                    Mensagem = "Por favor, informe as localizações"
                };
            }
               
            if (request.Localizacoes.Any(x => x.IdRotaModelo.HasValue || !string.IsNullOrWhiteSpace(x.NomeRota)))
            {
                var idRotaModelo = request.Localizacoes.FirstOrDefault(x => x.IdRotaModelo.HasValue)?.IdRotaModelo;
                var nomeRota = request.Localizacoes.FirstOrDefault(x => !string.IsNullOrWhiteSpace(x.NomeRota))?.NomeRota;
                RotaModelo rotaModelo = null;

                if (idRotaModelo.HasValue && empresa?.IdEmpresa != null)
                    rotaModelo = _rotaModeloApp.GetByIdOrNomeRota(idRotaModelo,null,empresa.IdEmpresa);

                if(!string.IsNullOrWhiteSpace(nomeRota) && empresa?.IdEmpresa != null && rotaModelo == null)
                    rotaModelo = _rotaModeloApp.GetByIdOrNomeRota(null,nomeRota,empresa.IdEmpresa);

                if (empresa?.IdEmpresa == null)
                    return new CustoPedagioRotaResponseModel
                    {
                        Status = ConsultaRotaResponseDtoStatus.Falha,
                        Mensagem = "Não foi possivel realizar a consulta baseado nos dados informados (Empresa não encontrada na base)."
                    };

                if(rotaModelo == null)
                    return new CustoPedagioRotaResponseModel
                    {
                        Status = ConsultaRotaResponseDtoStatus.Falha,
                        Mensagem = "Não foi possivel realizar a consulta baseado nos dados informados (Id e/ou Nome de rota não cadastrados)."
                    };

                //Limpar para atribuir os valores pos pontos da rota a partir do banco de dados
                request.Localizacoes.Clear();

                var localizacaoOrigem = new LocalizacaoDTO
                {
                    Latitude = rotaModelo.OrigemLatitude,
                    Longitude = rotaModelo.OrigemLongitude,
                    IbgeCidade = rotaModelo.OrigemIbge.ToInt()
                };
                request.Localizacoes.Add(localizacaoOrigem);

                foreach (var ponto in rotaModelo.PontosRotaModelo)
                {
                    var localizacaoPontoRota = new LocalizacaoDTO
                    {
                        Latitude = ponto.Latitude,
                        Longitude = ponto.Longitude,
                        IbgeCidade = ponto.Ibge.ToInt()
                    };
                    request.Localizacoes.Add(localizacaoPontoRota);
                }

                var localizacaoDestino = new LocalizacaoDTO
                {
                    Latitude = rotaModelo.DestinoLatitude,
                    Longitude = rotaModelo.DestinoLongitude,
                    IbgeCidade = rotaModelo.DestinoIbge.ToInt()
                };
                request.Localizacoes.Add(localizacaoDestino);
            }

            if (request.Localizacoes.Count < 2 || request.Localizacoes.Count > 20)
            {
                return new CustoPedagioRotaResponseModel
                {
                    Status = ConsultaRotaResponseDtoStatus.Falha,
                    Mensagem = "Deve possuir no mínimo dois e no máximo vinte IBGEs informados "
                };
            }

            if (request.QtdEixos < 2)
            {
                return new CustoPedagioRotaResponseModel
                {
                    Status = ConsultaRotaResponseDtoStatus.Falha,
                    Mensagem = "Quantidade de eixos deve ser maior ou igual a 2"
                };
            }

            var cartoesService = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, CartoesService.AuditDocIntegracao, null);
           
            var consultaRotaRequest = new ConsultaRotaRequest
            {
                QtdEixos = request.QtdEixos,
                TipoVeiculo = (ConsultaRotaRequestTipoVeiculo) request.TipoVeiculo,
                TipoRodagem = (ConsultaRotaRequestTipoRodagem) request.TipoRodagem,
                ExibirDetalhes = request.ExibirDetalhes,
                TipoRota = (ConsultaRotaRequestTipoRota) request.TipoRota,
                DesabilitaCacheRotas = empresa?.DesabilitaCacheRotas,
                Billing =  request.Billing,
                Polyline = request.Polyline,
                Localizacoes = new List<LocationDTO>()
            };

            foreach (var localizacao in request.Localizacoes)
            {
                var cidade = _cidadeApp.GetCidadeByIBGE(localizacao.IbgeCidade);
                
                if (cidade == null && (!localizacao.Latitude.HasValue || !localizacao.Longitude.HasValue))
                {
                    return new CustoPedagioRotaResponseModel
                    {
                        Status = ConsultaRotaResponseDtoStatus.Falha,
                        Mensagem = $"Cidade não localizada na base, " +
                                   $"atualize o cadastro desta cidade ou faça o cadastro!"
                    };
                }
                
                consultaRotaRequest.Localizacoes.Add(new LocationDTO
                {
                    Latitude = localizacao.Latitude ?? cidade.Latitude,
                    Longitude = localizacao.Longitude ?? cidade.Longitude,
                    Ibge = localizacao.IbgeCidade
                });
            }
            
            var response = cartoesService.ConsultarCustoRota(consultaRotaRequest);

            var custoPedagioRotaResponse = new CustoPedagioRotaResponseModel()
            { 
                Status = response.Status,
                Mensagem = response.Mensagem,
                Pracas = response.Pracas,
                Localizacoes = response.Localizacoes,
                KmTotal = response.DistanciaTotalKm.ToDecimal(),
                CodPolyline = response.CodPolyline,
                TempoPrevisto = response.TempoPrevisto
            };
            
            if (response.CustoTotal != null) custoPedagioRotaResponse.CustoTotal = response.CustoTotal.Value;
            //replicar o custo total tag nos dois campos pois alguns TMSs nao desenvolveram as alteracoes pra pegar o CustoTotalTag
            //q é o que tem o valor que será usado na compra
            if (response.CustoTotalTag != null)
            {
                custoPedagioRotaResponse.CustoTotal = response.CustoTotalTag.Value;
                custoPedagioRotaResponse.CustoTotalTag = response.CustoTotalTag.Value;
            }
            
            if (response.IdentificadorHistorico != null) 
                custoPedagioRotaResponse.IdentificadorHistorico = response.IdentificadorHistorico.Value;
            
            return custoPedagioRotaResponse;
        }

        #endregion

        private bool ExistSolicitacaoCompra(Viagem viagem, CartoesApp cartoesApp, out SolicitarCompraPedagioResponseDTO solicitarCompraPedagio)
        {
            if (viagem.NumeroProtocoloPedagio.HasValue)
            {
                var consultaCompraPedagioRequest = new ConsultaCompraPedagioRequest
                    {Id = (int?) viagem.NumeroProtocoloPedagio};
                var compra = cartoesApp.ConsultarCompraPedagio(0, 0, null, null, consultaCompraPedagioRequest);

                if (compra?.CompraPedagioDTOList != null && compra.CompraPedagioDTOList.Count > 0)
                {
                    solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
                    {
                        ProtocoloProcessamento = compra.CompraPedagioDTOList[0].Id,
                        Status = viagem.ResultadoCompraPedagio,
                        Mensagem = viagem.MensagemCompraPedagio,
                        ProtocoloRequisicao = viagem.NumeroProtocoloPedagio,
                        Valor = viagem.ValorPedagio,
                        ProtocoloValePedagio = compra.CompraPedagioDTOList[0].ProtocoloValePedagio,
                        ProtocoloEnvioValePedagio = compra.CompraPedagioDTOList[0].ProtocoloEnvioValePedagio,
                        AvisoTransportador = compra.CompraPedagioDTOList[0].AvisoTransportador,
                        Fornecedor = _viagemApp.GetFornecedorViagemRota(viagem.IdViagem)
                    };
                    return true;
                }
            }

            solicitarCompraPedagio = null;
            return false;
        }
        
        private List<LocalizacaoDTO> DecodePolylineFirstAndLast(string encoded)
        {
            try
            {
                var resultlist = new List<LocalizacaoDTO>();
                if (string.IsNullOrEmpty(encoded))
                    return new List<LocalizacaoDTO>();

                int index = 0, lat = 0, lng = 0;
                LocalizacaoDTO first = null;
                LocalizacaoDTO last = null;

                while (index < encoded.Length)
                {
                    int shift = 0, result = 0, byteValue;

                    do
                    {
                        byteValue = encoded[index++] - 63;
                        result |= (byteValue & 0x1F) << shift;
                        shift += 5;
                    } while (byteValue >= 0x20);

                    int deltaLat = (result & 1) != 0 ? ~(result >> 1) : (result >> 1);
                    lat += deltaLat;

                    shift = 0;
                    result = 0;

                    do
                    {
                        byteValue = encoded[index++] - 63;
                        result |= (byteValue & 0x1F) << shift;
                        shift += 5;
                    } while (byteValue >= 0x20);

                    int deltaLng = (result & 1) != 0 ? ~(result >> 1) : (result >> 1);
                    lng += deltaLng;

                    var point = new LocalizacaoDTO
                    {
                        Latitude = Math.Round(lat / 1e5m, 8),
                        Longitude = Math.Round(lng / 1e5m, 8)
                    };

                    if (first == null)
                        first = point;

                    last = point; 
                }

                //First
                resultlist.Add(first);
                //Last
                resultlist.Add(last);
            
                return resultlist;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error($"Erro ao descriptografar polyline: {e.Message}");
                return new List<LocalizacaoDTO>();
            }
        }
    }
}
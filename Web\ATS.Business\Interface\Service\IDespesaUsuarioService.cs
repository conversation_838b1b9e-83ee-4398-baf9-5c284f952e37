﻿using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IDespesaUsuarioService : IBaseService<IDespesaUsuarioRepository>
    {
        BusinessResult<DespesaUsuarioAddModelResponse> Add(DespesaUsuarioAddModel model);
        IQueryable<DespesaUsuario> GetDespesaUsuario(int? idUsuario);
        IQueryable<DespesaUsuario> GetAll();
        IQueryable<DespesaUsuario> GetAllWithInclude();
    }
}

﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Linq.Dynamic;
using System.Transactions;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.PagamentoFrete;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using AutoMapper.QueryableExtensions;
using NLog;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Application.Application
{
    public class PagamentoFreteApp : AppBase, IPagamentoFreteApp
    {
        private readonly IPagamentoFreteService _pagamentoFreteService;
        private readonly IViagemService _viagemService;
        private readonly IProtocoloService _protocoloService;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        private readonly IViagemEventoService _viagemEventoService;
        private readonly IEmpresaRepository _empresaRepository;

        public PagamentoFreteApp(IPagamentoFreteService pagamentoFreteService, IViagemService viagemService, IProtocoloService protocoloService, CartoesServiceArgs cartoesServiceArgs,
            IViagemEventoService viagemEventoService, IEmpresaRepository empresaRepository)
        {
            _pagamentoFreteService = pagamentoFreteService;
            _viagemService = viagemService;
            _protocoloService = protocoloService;
            _cartoesServiceArgs = cartoesServiceArgs;
            _viagemEventoService = viagemEventoService;
            _empresaRepository = empresaRepository;
        }

        public PagamentoFreteModel ConsultarPorToken(string token, string cpfCnpjUsuario, string nomeUsuario, List<int> idsEstabelecimentosBaseUsuario = null, int? idEstabelecimento = null)
        {
            return _pagamentoFreteService.ConsultarPorToken(token, cpfCnpjUsuario, nomeUsuario, idsEstabelecimentosBaseUsuario, idEstabelecimento);
        }

        public CheckTokenModel CheckToken(string token)
        {
            return _pagamentoFreteService.CheckToken(token);
        }

        public ValidationResult CreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, IUserIdentity usuarioLogado, decimal? valor)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                try
                {
                    var validationResultRetorno = _pagamentoFreteService.CreditarAbono(idViagemEventoabono, idViagemEventoSaldo, idProtocolo, usuarioLogado, valor);

                    if (validationResultRetorno.IsValid)
                        transaction.Complete();

                    return validationResultRetorno;
                }
                catch (Exception e)
                {
                    return new ValidationResult().Add(e.Message);
                }
            }
        }

        public ValidationResult NaoCreditarAbono(int idViagemEventoabono, int idViagemEventoSaldo, int idProtocolo, int? idMotivo = null, string descricao = null, int? idUsuario = null)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var response = _pagamentoFreteService.NaoCreditarAbono(idViagemEventoabono, idViagemEventoSaldo, idProtocolo, idMotivo, descricao, idUsuario);

                if (response.IsValid)
                    transaction.Complete();

                return response;
            }
        }

        public ValidationResult AnalisarAbonoCartaFrete(int idViagemEventoSaldo, int idProtocolo, int? idMotivoRejeicao, string descricaoMotivo, IUserIdentity usuarioLogado, EStatusAnaliseAbono statusAnalise)
        {
            return _protocoloService.RealizarAnaliseAbono(idViagemEventoSaldo, idProtocolo, usuarioLogado.IdUsuario, true, idMotivoRejeicao, descricaoMotivo, statusAnalise);
        }

        public ValidationResult EfetuarPagamento(PagamentoFreteModel pagamentoFreteModel, Usuario usuarioLogado, bool integracaoAts, int administradoraPlataforma, List<int> idsEstabelecimentosUsuario = null)
        {
            var validationResult = new ValidationResult();
            try
            {
                validationResult.Add(_pagamentoFreteService.ValidarCredenciamento(usuarioLogado, pagamentoFreteModel.IdEstabelecimento, administradoraPlataforma));
                if (!validationResult.IsValid)
                    return validationResult;

                var viagem = _viagemService.ConsultarViagemPorToken(pagamentoFreteModel.Token);
                if (viagem == null)
                    return validationResult.Add("Viagem não encontrada!");

                #region consulta cartões
                var cartoesService = new CartoesService(_cartoesServiceArgs, viagem.IdEmpresa, viagem.Empresa.TokenMicroServices,
                    usuarioLogado?.CPFCNPJ ?? CartoesService.AuditDocIntegracao, usuarioLogado?.Nome ?? string.Empty);
                CartaoVinculadoPessoaListResponse cartaoProprietario = null;
                CartaoVinculadoPessoaListResponse cartaoMotorista = null;

                if (viagem.ViagemEventos.First(x => x.Token == pagamentoFreteModel.Token).HabilitarPagamentoCartao || pagamentoFreteModel.HabilitarPagamentoCartao)
                {
                    validationResult.Add(_pagamentoFreteService.ConsultaCartoesPagamentoEvento(cartoesService, usuarioLogado, viagem.CPFCNPJProprietario, viagem.CPFMotorista,
                        out cartaoProprietario, out cartaoMotorista));

                    if (!validationResult.IsValid)
                        return validationResult;
                }
                #endregion

                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.Serializable }))
                {
                    //Um posto conseguiu fazer duas vezes o pagamento do evento, então é editada uma propriedade no início da transaction para estourar deadlock mais fácil
                    viagem.ViagemEventos.First(x => x.Token == pagamentoFreteModel.Token).DataHoraPagamento = DateTime.Now;

                    validationResult.Add(_pagamentoFreteService.EfetuarBaixaEvento(pagamentoFreteModel, idsEstabelecimentosUsuario, integracaoAts, usuarioLogado, viagem));

                    transaction.Complete();
                }

                if (!validationResult.IsValid)
                    return validationResult;

                var viagemEvento = viagem.ViagemEventos.First(x => x.Token == pagamentoFreteModel.Token);

                if (viagemEvento.HabilitarPagamentoCartao && (viagemEvento.ValorTotalPagamento ?? viagemEvento.ValorPagamento) > 0)
                {
                    var validationCartao = _pagamentoFreteService.PagamentoCartao(cartoesService, cartaoProprietario, cartaoMotorista, viagem, viagemEvento);
                    if (!validationCartao.IsValid)
                    {
                        validationResult.Add(validationCartao);

                        viagemEvento.Status = EStatusViagemEvento.Aberto;
                        viagemEvento.DataHoraPagamento = null;
                        _viagemService.Update(viagem);
                    }
                }

            }
            catch (Exception e)
            {
                return validationResult.Add(e.GetBaseException().Message);
            }

            return validationResult;
        }

        public ValidationResult AlterarSaldo(decimal saldo, decimal pesoChegada, decimal quebraMercadoria, decimal difFreteMotorista, string token)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _pagamentoFreteService.AlterarSaldo(saldo, pesoChegada, quebraMercadoria, difFreteMotorista, token);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public byte[] GerarRecibo(string token, int? idViagemEvento, string usuario)
        {
            return _pagamentoFreteService.GerarReciboViagem(token, idViagemEvento);
        }

        public PagamentoFreteEventoModel CalcularValoresProtocolo(string token, decimal? pesoChegada, int? numeroSacas)
        {
            return _pagamentoFreteService.CalcularValoresProtocolo(token, pesoChegada, numeroSacas);
        }

        public void AtualizarPagamentoSemChave(bool liberarPagamento, string token, int? estabId, string observacao, int idUsuarioLibSemChave)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required,
             new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _pagamentoFreteService.AtualizarPagamentoSemChave(liberarPagamento, token, estabId, observacao, idUsuarioLibSemChave);
                transaction.Complete();
            }
        }

        public PagamentoFreteEventoModel CalcularValoresViagem(string token, bool habilitaPagamentoCartao, decimal? pesoChegada, int? numeroSacas, EUnidadeMedida unidadeInformada, bool? quebraAbono)
        {
            return _pagamentoFreteService.CalcularValoresViagem(token, habilitaPagamentoCartao, pesoChegada, numeroSacas, unidadeInformada, quebraAbono);
        }

        public PagamentoFreteEventoModel ConsultarEvento(string token)
        {
            return _pagamentoFreteService.ConsultarEvento(token);
        }

        public List<PagamentoFreteValorAdicionalModel> ConsultarOutrosDescontos(string token)
        {
            return _pagamentoFreteService.ConsultarOutrosDescontos(token);
        }

        public List<PagamentoFreteValorAdicionalModel> ConsultarOutrosAcrescimos(string token)
        {
            return _pagamentoFreteService.ConsultarOutrosAcrescimos(token);
        }

        public List<PagamentoFreteAnexoModel> ConsultarAnexos(string token, int? idUsuarioLogado = null)
        {
            return _pagamentoFreteService.ConsultarAnexos(token, null, idUsuarioLogado);
        }

        public void VincularAnexo(string tokenMidia, int idViagemDocumento)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _pagamentoFreteService.VincularAnexoMidiaAoViagemDocumento(tokenMidia, idViagemDocumento);

                transaction.Complete();
            }
        }

        public BusinessResult<FaturamentoGridModelResponse> RelatorioFaturamento(DateTime dataInicial, DateTime datafinal, int? empresaFiltro, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var itens = _pagamentoFreteService.RelatorioFaturamento(dataInicial, datafinal, empresaFiltro);
                
                var result = itens.AsQueryable().AplicarFiltrosDinamicos(filters);

                result = string.IsNullOrWhiteSpace(order?.Campo)
                    ? result.OrderBy(x => x.RazaoSocial).AsQueryable()
                    : result.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}").AsQueryable();

                var count = result.Count();
                
                return BusinessResult<FaturamentoGridModelResponse>.Valid(new FaturamentoGridModelResponse()
                {
                    items = result.Skip((page - 1) * take).Take(take).AsQueryable().ProjectTo<FaturamentoItemGridModelResponse>().ToList(),
                    totalItems = count
                });
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error("Falha ao consultar RelatorioFaturamento" + e.Message);
                return BusinessResult<FaturamentoGridModelResponse>.Error("Falha ao consultar Faturamento!");
            }
        }

        public byte[] GerarRelatorioFaturamento(DateTime dataInicial,DateTime datafinal,int? empresaFiltro, int take, int page, OrderFilters order, List<QueryFilters> filters,string extensao)
        {
            var itens = _pagamentoFreteService.RelatorioFaturamento(dataInicial, datafinal, empresaFiltro);
                
            var result = itens.AsQueryable().AplicarFiltrosDinamicos(filters);

            result = string.IsNullOrWhiteSpace(order?.Campo)
                ? result.OrderBy(x => x.RazaoSocial).AsQueryable()
                : result.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}").AsQueryable(); 
                
            return _pagamentoFreteService.GerarRelatorioFaturamento(result.ToList(),extensao,empresaFiltro);
        }

        public void RemoverAnexo(int idViagemDocumento)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _pagamentoFreteService.RemoverAnexo(idViagemDocumento);

                transaction.Complete();
            }
        }

        public void SolicitarAbono(string token, int? idMotivo, string detalhamento, int idUsuario)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var viagemEvento = _pagamentoFreteService.ConsultarEvento(token);
                _pagamentoFreteService.SolicitarAbono(viagemEvento.IdViagemEvento, idMotivo, detalhamento, idUsuario);

                transaction.Complete();
            }
        }

        public void AbonarViagemEvento(string tokenEvento, string tokenDocumento)
        {
            var viagemEvento = _viagemEventoService.GetByToken(tokenEvento);

            if (viagemEvento == null)
                return;

            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _pagamentoFreteService.AbonarViagemEvento(viagemEvento, tokenDocumento);

                transaction.Complete();
            }
        }


        public bool RemoverAnexo(string token)
        {
            return _pagamentoFreteService.RemoverAnexo(token);
        }

        public void CancelarAbono(string token)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _pagamentoFreteService.CancelarSolicitacao(token);

                transaction.Complete();
            }
        }

        public string EnviarSMSValidacao(string token, string cpf, string cnpj, string celular, bool isPessoaJuridica)
        {
            using (new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                /*var chave = _pagamentoFreteService.EnviarSMSValidacao(token, cpf, cnpj, celular, isPessoaJuridica);

                transaction.Complete();*/

                return ""; //chave;
            }
        }

        public byte[] GerarRelatorioGrid(DateTime dataInicial, DateTime dataFinal, string uf, double? a, double? b, double? c, string tipoArquivo, string logo, int idEmpresa)
        {
            return _pagamentoFreteService.GerarRelatorioGrid(dataInicial, dataFinal, uf, a, b, c, tipoArquivo, logo, idEmpresa);
        }

        // Consultar o total por estabelecimento e retorna o ranking para alimentar a grid do indicador.  
        public object ConsultarTotalPagamentosCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, double A, double B, double C, int page_, int take_, int idEmpresa)
        {
            return _pagamentoFreteService.ConsultarTotalPagamentosCurvaABC(dataInicio_, dataFim_, UF_, A, B, C, page_, take_, idEmpresa);
        }

        public object ConsultarGraficosSumPorEstabelecimento(DateTime dataInicio_, DateTime dataFim_, string UF_, double filtroA_, double filtroB_, double filtroC_, int idEmpresa)
        {
            return _pagamentoFreteService.ConsultarGraficosSumPorEstabelecimento(dataInicio_, dataFim_, UF_, filtroA_, filtroB_, filtroC_, idEmpresa);
        }

        public object ConsultarPagamentosEstabelecimentoGrid(DateTime dataInicio_, DateTime dataFim_, int idEstabelecimentoBase, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _pagamentoFreteService.ConsultarPagamentosEstabelecimentoGrid(dataInicio_, dataFim_, idEstabelecimentoBase, take, page, orderFilters, filters);
        }

        public byte[] ConsultarPagamentosEstabelecimentoReport(DateTime? dataInicio, DateTime? dataFim,
            int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo,
            string logo)
        {
            return _pagamentoFreteService.ConsultarPagamentosEstabelecimentoReport(dataInicio, dataFim,
                idEstabelecimentoBase, orderFilters, filters, tipoArquivo, logo);
        }

        public object GetTotaisPagamentosPorTipo(DateTime dataInicio, DateTime dataFinal, int? UF_,
            EStatusViagemEvento status_, int HabilitarPagamentoCartao, int? idEstabelecimentoBase,
            int? idEmpresa)
        {
            return _pagamentoFreteService.GetTotaisPagamentosPorTipo(dataInicio, dataFinal, UF_, status_, HabilitarPagamentoCartao, idEstabelecimentoBase, idEmpresa);
        }

        public object ConsultarGridDetalhesAberto(DateTime dataInicio_, DateTime dataFim_,
            bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa)
        {
            return _pagamentoFreteService.ConsultarGridDetalhesAberto(dataInicio_, dataFim_, HabilitarPagamentoCartao, UF, idEstabelecimentoBase, TipoEventoViagem, take, page, orderFilters, filters, idEmpresa);
        }

        public byte[] GerarRelatorioDetalhesProvisaoAberto(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa)
        {
            return _pagamentoFreteService.GerarRelatorioDetalhesProvisaoAberto(dataInicio, dataFim,
                habilitarPagamentoCartao, uf, idEstabelecimentoBase, tipoEventoViagem, orderFilters, filters,
                tipoArquivo, logo, idEmpresa);
        }

        public byte[] GerarRelatorioDetalhesProvisaoBaixado(DateTime dataInicio, DateTime dataFim,
            bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem,
            OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa)
        {
            return _pagamentoFreteService.GerarRelatorioDetalhesProvisaoBaixado(dataInicio, dataFim,
                habilitarPagamentoCartao, uf, idEstabelecimentoBase, tipoEventoViagem, orderFilters, filters,
                tipoArquivo, logo, idEmpresa);
        }

        public object ConsultarGridDetalhesBaixado(DateTime dataInicio_, DateTime dataFim_,
            bool HabilitarPagamentoCartao, int? UF, int? idEstabelecimentoBase, ETipoEventoViagem? TipoEventoViagem,
            int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, int? idEmpresa)
        {
            return _pagamentoFreteService.ConsultarGridDetalhesBaixado(dataInicio_, dataFim_, HabilitarPagamentoCartao, UF, idEstabelecimentoBase, TipoEventoViagem, take, page, orderFilters, filters, idEmpresa);
        }

        public List<ViagemEventoByCiotModel> GetByCiot(string numeroCiot)
        {
            return _pagamentoFreteService.GetByCiot(numeroCiot);
        }

        public ValidationResult ValidarChavePagamento(int empresaId, string documento, string chave, string tokenPagamento)
        {
            return _pagamentoFreteService.ValidarChavePagamento(empresaId, documento, chave, tokenPagamento);
        }

        public ValidationResult SetMotivoRejeicaoAbono(int idViagemEvento, int? idMotivo, string detalhamentoMotivo, int? idUsuario)
        {
            return _pagamentoFreteService.SetMotivoRejeicaoAbono(idViagemEvento, idMotivo, detalhamentoMotivo, idUsuario);
        }

        public object ConsultarMotivoAbono(int idViagemEvento)
        {
            return _pagamentoFreteService.ConsultarMotivoAbono(idViagemEvento);
        }

        public bool VerificarCartaoVinculado(int idEmpresa, string cpfCnpj)
        {
            var tokenAdministradora = ConfigurationManager.AppSettings["MS_TOKEN_ADMINISTRADORA"];

            var empresa = _empresaRepository.Get(idEmpresa);

            var cartoes =
                new CartoesService(_cartoesServiceArgs, empresa?.IdEmpresa, empresa?.TokenMicroServices ?? tokenAdministradora, cpfCnpj, empresa?.NomeFantasia).GetCartoesVinculados(cpfCnpj,
                    new List<int>(), false);

            if (empresa != null)
            {
                if (cartoes.Cartoes.All(x => x.Produto.Id != empresa?.IdProdutoCartaoFretePadrao))
                    return false;
            }

            return cartoes != null && cartoes.Cartoes.Any();
        }

        public ValidationResult DeletarEventoAbono(int idViagem)
        {
            return _pagamentoFreteService.DeletarEventoAbono(idViagem);
        }
    }
}
﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TipoNotificacaoMap : EntityTypeConfiguration<TipoNotificacao>
    {
        public TipoNotificacaoMap()
        {
            ToTable("TIPO_NOTIFICACAO");

            HasKey(x => x.IdTipoNotificacao);

            Property(t => t.IdTipoNotificacao)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.Descricao)
                .IsRequired();

            HasRequired(x => x.Empresa)
                .WithMany(x => x.TiposNotificacao)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Filial)
                .WithMany(x => x.TiposNotificacao)
                .HasForeignKey(x => x.IdFilial);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class MensagemGrupoUsuarioRepository : Repository<MensagemGrupoUsuario>, IMensagemGrupoUsuarioRepository
    {
        public MensagemGrupoUsuarioRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna os grupos de usuários cadastrados por esse usuário
        /// </summary>
        /// <returns></returns>
        public IQueryable<MensagemGrupoUsuario> GetGruposDoUsuario(int idUsuario)
        {
            return from p in All()
                   .Where(x => x.IdUsuario == idUsuario)
                   select p;
        }

        public MensagemGrupoUsuario AddGrupo(MensagemGrupoUsuario grupoUsuario)
        {
            return base.Add(grupoUsuario);
        }

        public void DeletarGrupo(int idGrupo)
        {
#pragma warning disable 618
            var grupo = FirstOrDefault(x => x.IdGrupoUsuario == idGrupo);
#pragma warning restore 618
            if(grupo != null)
                base.Delete(grupo);
        }

        public MensagemGrupoDestinatario AddUsuarioParaGrupo(MensagemGrupoDestinatario mensagemGrupoDestinatario)
        {
            Context.Set<MensagemGrupoDestinatario>().Add(mensagemGrupoDestinatario);

            Context.SaveChanges();

            return mensagemGrupoDestinatario;
        }

        public void RemoverUsuarioDoGrupo(MensagemGrupoDestinatario mensagemGrupoDestinatario)
        {
            Context.Set<MensagemGrupoDestinatario>().Remove(mensagemGrupoDestinatario);

            Context.SaveChanges();
        }

        /// <summary>
        /// Retorna os grupos de usuários que este usuário faz parte
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public IEnumerable<MensagemGrupoUsuario> GetGruposUsuarioPorIdUsuario(int idUsuario)
        {
            var idsGruposUsuarios =
                ((AtsContext)Context).MensagemGrupoDestinatario.Where(x => x.IdUsuarioDestinatario == idUsuario)
                    .Select(x => x.IdGrupoUsuario);

            var mensagemGrupoUsuario = new List<MensagemGrupoUsuario>();
            foreach (var idGrupos in idsGruposUsuarios)
            {
                var grupoUsuario =  from p in All()
                    .Where(x => x.IdGrupoUsuario == idGrupos)
                       select p;

                mensagemGrupoUsuario.AddRange(grupoUsuario);
            }

            return mensagemGrupoUsuario;
        }
    }
}
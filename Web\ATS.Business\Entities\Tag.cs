﻿using System;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class Tag
    {
        public int Id { get; set; }
        public long SerialNumber { get; set; }
        public int? IdEmpresa { get; set; }
        public EStatusTag Status { get; set; }
        public bool Desbloqueado { get; set; }
        public string Placa { get; set; }
        public int? ModeloIdMoveMais { get; set; }
        public string ModeloDescricaoMoveMais { get; set; }
        public string UsuarioCriacao { get; set; }
        public string UsuarioAtualizacao { get; set; }
        public DateTime DataCriacao { get; set; }
        public DateTime DataAtualizacao { get; set; }

        #region FK
        public virtual Empresa Empresa { get; set; }
        #endregion
    }
}

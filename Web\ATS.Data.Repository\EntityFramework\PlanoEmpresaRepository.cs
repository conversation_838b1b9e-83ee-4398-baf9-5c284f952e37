﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.DTO.Plano;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Data.Repository.EntityFramework
{
    public class PlanoEmpresaRepository : Repository<PlanoEmpresa>, IPlanoEmpresaRepository
    {
        public PlanoEmpresaRepository(AtsContext context) : base(context)
        {
        }
    }
}

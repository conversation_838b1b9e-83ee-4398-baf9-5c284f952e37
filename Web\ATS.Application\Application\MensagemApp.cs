﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class MensagemApp : AppBase, IMensagemApp
    {
        private readonly IMensagemService _mensagemService;

        public MensagemApp(IMensagemService mensagemService)
        {
            _mensagemService = mensagemService;
        }

        public void AtualizarMensagem(int idMensagem, int idUsuarioDestinatario, DateTime? dataHoraLeitura)
        {
            if (!dataHoraLeitura.HasValue)
                throw new Exception("Não é possível atualizar a data/hora de leitura da mensagem para um valor nulo!");

            var mensagem = _mensagemService.GetWithAllChilds(idMensagem);
            if (mensagem == null)
                throw new Exception($"Nenhuma mensagem encontrada para o id {idMensagem}!");


            var msgDestToUp = mensagem.MensagemDestinatario.FirstOrDefault(x => x.IdUsuarioDestinatario == idUsuarioDestinatario &&
                                                                                x.IdMensagem == idMensagem);
            if (msgDestToUp == null)
                throw new Exception($"Nenhuma mensagem encontrada para o IdDestinatario {idUsuarioDestinatario}!");

            msgDestToUp.DataHoraLido = dataHoraLeitura.Value;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _mensagemService.Update(mensagem);
                transaction.Complete();
            }
        }


        /// <summary>
        /// Retorna todas as mensagens cadastradas a partir da data informada por usuario e filtros
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <param name="dataInicial">Data de ínício de filtro</param>
        /// <param name="dataFinal">Data de término do filtro</param>
        /// <param name="assunto">Assunto</param>
        /// <returns></returns>
        public IQueryable<Mensagem> ConsultarRecebidos(int idUsuario, DateTime dataInicial, DateTime dataFinal, string assunto)
        {
            return _mensagemService.ConsultarRecebidos(idUsuario, dataInicial, dataFinal, assunto);
        }

        public ICollection<Mensagem> GetMensagensPeloUsuario(int idUsuarioDestinatario, DateTime? dataFiltro)
        {
            return _mensagemService.GetMensagensPeloUsuario(idUsuarioDestinatario, dataFiltro);
        }

        public ValidationResult SendBroadcast()
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                   new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var ret = _mensagemService.SendBroadcast();
                transaction.Complete();
                return ret;
            }
        }
    }
}
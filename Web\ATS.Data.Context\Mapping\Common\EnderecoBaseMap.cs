﻿using ATS.Domain.Entities.Common;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping.Common
{
    public class EnderecoBaseMap<TEntity> : EntityTypeConfiguration<TEntity> where TEntity : EnderecoBase
    {
        public EnderecoBaseMap()
        {
            Property(t => t.CEP)
                .IsRequired()
                .HasMaxLength(8);

            Property(t => t.Endereco)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Complemento)
                .HasMaxLength(100);

            Property(t => t.<PERSON>)
                .IsRequired()
                .HasMaxLength(100);
        }
    }
}
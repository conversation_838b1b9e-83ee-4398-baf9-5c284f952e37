using System;
using System.IO;
using System.Web.Mvc;
using ATS.Data.Repository.External.SistemaInfo.Infra;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;

namespace ATS.WS.Controllers
{
    public class HealthCheckController : BaseController
    {
        [IgnoreRequestLog]
        [HttpGet]
        public JsonResult LogInfo()
        {
            var data = InfraExternalRepository.GetProcessInfo();
            return Responde(data);
        }

        [HttpGet]
        public JsonResult GetDataProjeto()
        {
            var dataModificacao = string.Empty;
            try
            {
                dataModificacao = new FileInfo(this.GetType().Assembly.Location).LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            catch (Exception)
            {
                // ignored
            }

            return Responde(dataModificacao);
        }

        [HttpGet]
        public JsonResult GetTeste()
        {
            return Responde(new
            {
                Value = "Teste CI/CD 2"
            });
        }

        public HealthCheckController(BaseControllerArgs baseArgs) : base(baseArgs)
        {
        }
    }
}
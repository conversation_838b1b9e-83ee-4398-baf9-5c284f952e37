﻿using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Ciot;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class ContratoAgregadoController : BaseController
    {
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;

        public ContratoAgregadoController(BaseControllerArgs baseArgs, IParametrosApp parametrosApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IVersaoAnttLazyLoadService versaoAntt) : base(baseArgs)
        {
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _versaoAntt = versaoAntt;
        }


        /// <summary>
        /// Encerra ou cancela contratos de agregado que venceram o prazo de 30 dias 
        /// </summary>
        /// <returns></returns>
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult EncerrarContratosVencidos()
        {
            try
            {
                var encerrarCiotAgregado = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.EncerrarCancelarCiotAgregadoExpirados()
                    : _ciotV3App.EncerrarCancelarCiotAgregadoExpirados();

                return Responde(encerrarCiotAgregado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(e);
            }
        }
        
        /// <summary>
        /// Requisição para encerrar um CIOT agregado antes do prazo de encerramento automático
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Encerrar(EncerrarCiotRequest request)
        {
            try
            {
                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Json(new Retorno<object>(false, validacaoChamada.ToString(), null));
                
                var validacaoEncerrar = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.Encerrar(request.CNPJEmpresa, request.Ciot)
                    : _ciotV3App.Encerrar(request.CNPJEmpresa, request.Ciot);

                return Json(new Retorno<object>(validacaoEncerrar.IsValid, validacaoEncerrar.ToString(), null));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Json(new Retorno<object>(false, e.Message, null));
            }
        }
        
        /// <summary>
        /// Requisição para envio de emails de aviso 
        /// </summary>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public void Avisar()
        {
            try
            {
                //quem chama esse cara aqui ??????????????
                if(_versaoAntt.Value == EVersaoAntt.Versao2)
                    _ciotV2App.Avisar();
                else
                    _ciotV3App.Avisar();
            }
            catch (Exception e)
            {
                Logger.Error(e);
            }
        }
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Veiculo;
using AutoMapper;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Configuration;
using ATS.Application.Interface;
using ATS.Domain.Interface.Database;
using ATS.Domain.DTO;
using Veiculo = ATS.Domain.Entities.Veiculo;
using NLog;

namespace ATS.WS.Services
{
    public class SrvVeiculo : SrvBase, IDisposable
    {
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IFilialApp _filialApp;
        private readonly ITipoCavaloApp _tipoCavaloApp;
        private readonly ITipoCarretaApp _tipoCarretaApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IVeiculoApp _veiculoApp;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IParametrosApp _parametrosApp;

        public SrvVeiculo(IProprietarioApp proprietarioApp, IEmpresaRepository empresaRepository, IUsuarioApp usuarioApp, IEmpresaApp empresaApp, IFilialApp filialApp,
            ITipoCavaloApp tipoCavaloApp, ITipoCarretaApp tipoCarretaApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, ICidadeApp cidadeApp, IVeiculoApp veiculoApp, IMotoristaApp motoristaApp, IParametrosApp parametrosApp)
        {
            _proprietarioApp = proprietarioApp;
            _empresaRepository = empresaRepository;
            _usuarioApp = usuarioApp;
            _empresaApp = empresaApp;
            _filialApp = filialApp;
            _tipoCavaloApp = tipoCavaloApp;
            _tipoCarretaApp = tipoCarretaApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _cidadeApp = cidadeApp;
            _veiculoApp = veiculoApp;
            _motoristaApp = motoristaApp;
            _parametrosApp = parametrosApp;
        }

        public ValidationResult Cadastrar(VeiculoCreateRequest model, int idUsuarioLogado)
        {
            try
            {
                var veiculo = Mapper.Map<VeiculoCreateRequest, Veiculo>(model);
                var resultado = _veiculoApp.Add(veiculo);
                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Editar(VeiculoCreateRequest model, int idUsuarioLogado)
        {
            try
            {
                if (!model.IdVeiculo.HasValue)
                    return new ValidationResult().Add("Id do veículo não informado.");

                var veiculo = _veiculoApp.GetWithAllChilds(model.IdVeiculo.Value);

                var placaOriginal = veiculo.Placa;

                ReflectionHelper.CopyProperties(model, veiculo);

                veiculo.Placa = placaOriginal;

                return _veiculoApp.Update(veiculo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ConsultaDadosVeiculoResponseDTO ConsultaDadosVeiculo(int idVeiculo)
        {
            return _veiculoApp.ConsultaDadosVeiculo(idVeiculo);
        }

        public VeiculoEditRequest ConsultarPorId(int idVeiculo)
        {
            var veiculo = _veiculoApp.GetWithAllChilds(idVeiculo);

            if (veiculo == null)
                throw new Exception("Nenhum veículo encontrado com o Id informado");

            #region Montagem do objeto

            var veiculoEdit = new VeiculoEditRequest
            {
                IdVeiculo = veiculo.IdVeiculo,
                IdEmpresa = veiculo.IdEmpresa,
                RazaoSocialEmpresa = veiculo.Empresa?.RazaoSocial,
                IdFilial = veiculo.IdFilial,
                NomeFantasiaFilial = veiculo.Filial?.NomeFantasia,
                IdProprietario = veiculo.IdProprietario,
                RazaoSocialProprietario = veiculo.Proprietario?.RazaoSocial,
                IdMotorista = veiculo.IdMotorista,
                NomeMotorista = veiculo.Motorista?.Nome,
                IdUsuario = veiculo.IdUsuario,
                IdPais = veiculo.IdPais,
                IdEstado = veiculo.IdEstado,
                IdCidade = veiculo.IdCidade,
                NumeroFrota = veiculo.NumeroFrota,
                TipoContrato = veiculo.TipoContrato,
                QuantidadeEixos = veiculo.QuantidadeEixos,
                DataUltimaAtualizacao = veiculo.DataUltimaAtualizacao,
                Municipio = veiculo.Municipio,
                Placa = veiculo.Placa,
                AnoFabricacao = veiculo.AnoFabricacao,
                AnoModelo = veiculo.AnoModelo,
                Renavam = veiculo.RENAVAM,
                Marca = veiculo.Marca,
                Modelo = veiculo.Modelo,
                ComTracao = veiculo.ComTracao,
                TipoRodagem = veiculo.TipoRodagem,
                IdTipoCavalo = veiculo.IdTipoCavalo,
                IdTipoCarreta = veiculo.IdTipoCarreta,
                Ativo = veiculo.Ativo,
                StatusIntegracao = veiculo.StatusIntegracao,
                HabilitarContratoCiotAgregado = veiculo.HabilitarContratoCiotAgregado,
                TipoCavaloNome = veiculo.TipoCavalo?.Nome
            };

            #endregion

            return veiculoEdit;
        }

        public Retorno<VeiculoModel> Integrar(VeiculoIntegrarRequestModel @params)
        {
            try
            {
                var idEmpresaIntegracao = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                var veiculoApp = _veiculoApp;

                var veiculoDb = @params.TipoContrato == 
                                ETipoContrato.Terceiro ? veiculoApp.GetVeiculoTerceiro(@params.Placa) : veiculoApp.GetVeiculoPorPlaca(@params.Placa, idEmpresaIntegracao);

                if (veiculoDb != null)
                {
                    try
                    {
                        Alterar(@params);
                    }
                    catch (Exception e)
                    {
                        LogManager.GetCurrentClassLogger().Error(e, "Erro ao atualizar veículo " + @params.Placa);
                    }

                    var result = Mapper.Map<Veiculo, VeiculoModel>(veiculoDb);
                    result.CodigoDaOperacao = veiculoDb.IdOperacao;
                    return new Retorno<VeiculoModel>(true, result);
                }

                var veiculo = Mapper.Map<VeiculoIntegrarRequestModel, Veiculo>(@params);
                
                if (veiculo.NumeroFrota <= 0)
                {
                    veiculo.NumeroFrota = null;
                }
                

                #region Empresa

                if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa) && (veiculo.TipoContrato != ETipoContrato.Terceiro || veiculo.TipoContrato == 0))
                {
                    var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                    
                    if (idEmpresa.HasValue)
                        veiculo.IdEmpresa = idEmpresa.GetValueOrDefault();
                }

                #endregion

                #region Se Existir como MERCOSUL ou BRASIL, Atualiza

                var empresa = _empresaRepository
                    .Where(x => x.CNPJ == @params.CNPJEmpresa).FirstOrDefault();

                var placa = @params.Placa.RemoveSpecialCharacters();
                var tipoPlacaOriginal = Veiculo.GetTipoPadraoPlaca(_empresaRepository, placa, empresa?.IdEmpresa);

                var registro = _veiculoApp.PlacaExistente(placa, veiculo.IdEmpresa);

                if (registro.Existente && tipoPlacaOriginal != registro.TipoPadraoPlaca)
                {
                    var retorno = Alterar(@params);

                    if (!retorno.Sucesso)
                        throw new Exception(retorno.Mensagem);

                    veiculo = _veiculoApp.GetVeiculoPorPlaca(veiculo.Placa, veiculo.IdEmpresa);

                    var veiculoAlteracaoModel = Mapper.Map<Veiculo, VeiculoModel>(veiculo);
                    veiculoAlteracaoModel.CodigoDaOperacao = veiculo.IdOperacao;
                    return new Retorno<VeiculoModel>(true, veiculoAlteracaoModel);
                }

                #endregion

                #region Endereço do veículo

                if (@params.IBGECidade.HasValue)
                {
                    var enderecoVeiculo = ConsultarCidadeEstadoPaisByIbge(@params.IBGECidade.Value);
                    veiculo.IdCidade = enderecoVeiculo.Item1;
                    veiculo.IdEstado = enderecoVeiculo.Item2;
                    veiculo.IdPais = enderecoVeiculo.Item3;
                }

                #endregion

                #region Empresa

                if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa) && (veiculo.TipoContrato != ETipoContrato.Terceiro || veiculo.TipoContrato == 0))
                {
                    if (idEmpresaIntegracao != 0)
                    {
                        veiculo.IdEmpresa = idEmpresaIntegracao;
                    }
                }

                #endregion

                #region Proprietário

                if (!string.IsNullOrWhiteSpace(@params.CPFCNPJProprietario))
                {
                    int? idProprietario = null;

                    if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa))
                    {
                        var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                        idProprietario = _proprietarioApp.GetIdByCpfCnpjWithEmpresa(@params.CPFCNPJProprietario, idEmpresa);

                        if (idProprietario != 0)
                            veiculo.IdProprietario = idProprietario.GetValueOrDefault();
                    }

                    if (!idProprietario.HasValue)
                    {
                        idProprietario = _proprietarioApp.GetIdPorCpfCnpj(@params.CPFCNPJProprietario);

                        if (idProprietario.HasValue)
                            veiculo.IdProprietario = idProprietario.GetValueOrDefault();
                    }
                }

                #endregion

                #region Motorista

                if (!string.IsNullOrWhiteSpace(@params.CPFMotorista))
                {
                    if (veiculo.TipoContrato == ETipoContrato.Terceiro)
                    {
                        var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFMotorista);
                        
                        if (idUsuario.HasValue)
                            veiculo.IdUsuario = idUsuario.GetValueOrDefault();
                    }
                    else
                    {
                        var idMotorista = _motoristaApp.GetIdPorCpf(@params.CPFMotorista, idEmpresaIntegracao);
                        
                        if (idMotorista.HasValue)
                            veiculo.IdMotorista = idMotorista.GetValueOrDefault();
                    }
                }

                #endregion

                #region Usuário

                if (!string.IsNullOrWhiteSpace(@params.CPFCNPJUsuario))
                {
                    var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJUsuario);
                    
                    if (idUsuario.HasValue)
                        veiculo.IdUsuario = idUsuario.GetValueOrDefault();
                }

                #endregion

                if (!string.IsNullOrWhiteSpace(@params.CNPJFilial))
                {
                    var filial = _filialApp.Get(@params.CNPJFilial);
                    
                    if (filial != null && filial.IdEmpresa == veiculo.IdEmpresa)
                        veiculo.IdFilial = filial.IdFilial;
                }
                
                if (!@params.TipoRodagem.HasValue)
                {
                    var defaultDuplo = _parametrosApp.GetDefaultIntegracaoTipoRodagemDupla(empresa.IdEmpresa);
                        
                    if (defaultDuplo)
                        veiculo.TipoRodagem = ETipoRodagem.Duplo;
                    else
                        veiculo.TipoRodagem = ETipoRodagem.Simples;
                }
                else
                {
                    veiculo.TipoRodagem = @params.TipoRodagem.Value;
                }

                var validationResult = _veiculoApp.Add(veiculo);
                
                if (!validationResult.IsValid)
                    return new Retorno<VeiculoModel>(false, validationResult.ToFormatedMessage(), null);

                var veiculoModel = Mapper.Map<Veiculo, VeiculoModel>(veiculo);
                veiculoModel.CodigoDaOperacao = veiculo.IdOperacao;
                
                return new Retorno<VeiculoModel>(true, veiculoModel);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Integrar)} >> {e.Message}");
            }
        }

        public object CheckVeiculoReutilizacaoPorPlaca(string placa, int? idUsuario)
        {
            var veiculoPlaca = _veiculoApp.GetVeiculosPorPlaca(new List<string> {placa});
            
            if (veiculoPlaca != null && veiculoPlaca.Any(x => !x.IdEmpresa.HasValue && x.IdUsuario.HasValue && x.Ativo))
            {
                return new
                {
                    disponivel = false,
                    mensagem = $"O veículo {placa.ToPlacaFormato()} não está disponível! Já está sendo utilizado por outro motorista/empresa!"
                };
            }

            return new
            {
                disponivel = true
            };
        }

        public Retorno<List<string>> ConsultarNumeroPlacas(string cnpj)
        {
            var idEmpresa = _empresaApp.GetIdPorCnpj(cnpj);
            var placas = _veiculoApp.GetPlacasPorEmpresa(idEmpresa ?? 0, null);
            return new Retorno<List<string>>(true, placas);
        }

        public Retorno<List<TipoCavaloModel>> ConsultarTipoCavalo(DateTime dataBase, string cnpjEmpresa)
        {
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);
                var idsEmpresa = idEmpresa.HasValue ? new List<int> {idEmpresa.Value} : new List<int>();
                
                var retorno = _tipoCavaloApp.GetRegistrosAtualizados(dataBase, idsEmpresa)
                    .Select(x => new TipoCavaloModel
                    {
                        Ativo = x.Ativo,
                        Categoria = x.Categoria,
                        CategoriaDescricao = x.Categoria.GetDescription(),
                        IdTipoCavalo = x.IdTipoCavalo,
                        Nome = x.Nome,
                        Capacidade = x.Capacidade,
                        TipoCavaloCliente = x.TipoCavaloCliente != null && x.TipoCavaloCliente.Any()
                            ? x.TipoCavaloCliente?.Select(y => new Models.Webservice.Request.TipoCavalo.TipoCavaloClienteRequest
                            {
                                IdCliente = y.IdCliente,
                                NomeFantasia = y.Cliente?.NomeFantasia,
                                Nome = y.Nome
                            }).ToList()
                            : null
                    }).ToList();

                return new Retorno<List<TipoCavaloModel>>(true, string.Empty, retorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<TipoCavaloModel>>($"{nameof(ConsultarTipoCavalo)} >> {e.Message}");
            }
        }

        public Retorno<List<TipoCarretaModel>> ConsultarTipoCarreta(DateTime dataBase, string cnpjEmpresa)
        {
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);
                var idsEmpresa = idEmpresa.HasValue ? new List<int> {idEmpresa.Value} : new List<int>();
                
                var retorno = _tipoCarretaApp.GetRegistrosAtualizados(dataBase, idsEmpresa).Select(x => new TipoCarretaModel
                    {
                        Ativo = x.Ativo,
                        Nome = x.Nome,
                        Categoria = x.Categoria,
                        CategoriaDescricao = x.Categoria > 0 ? x.Categoria.GetDescription() : string.Empty,
                        IdTipoCarreta = x.IdTipoCarreta,
                        Capacidade = x.Capacidade,
                        Destacar = x.Destacar
                    }).OrderByDescending(x => x.Destacar)
                    .ThenBy(x => x.Nome)
                    .ToList();

                return new Retorno<List<TipoCarretaModel>>(true, string.Empty, retorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<TipoCarretaModel>>($"{nameof(ConsultarTipoCarreta)} >> {e.Message}");
            }
        }

        public Retorno<object> ConsultarVeiculoPorPlaca(string token, string cnpjAplicacao, string placa, string cnpjEmpresa)
        {
            var empresa = new int?();

            if (!string.IsNullOrEmpty(cnpjEmpresa))
                empresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);

            IQueryable<Veiculo> query = null;
            
            if (empresa.HasValue)
                query = _veiculoApp.Query(placa, empresa);

            if (query == null || !query.Any())
                query = _veiculoApp.Query(placa, somenteTerceiros: true);

            var veiculo = query
                .Select(v => new
                {
                    v.IdVeiculo,
                    v.Ativo,
                    v.QuantidadeEixos,
                    v.VeiculoConjuntos,
                    v.IdTipoCarreta,
                    v.IdTipoCavalo,
                    v.TipoCavalo,
                    v.IdEmpresa,
                    v.IdFilial,
                    v.AnoModelo,
                    v.ComTracao,
                })
                .FirstOrDefault();

            if (veiculo == null || !veiculo.Ativo)
                return new Retorno<object>(true, null);

            var numeroEixos = veiculo.QuantidadeEixos;

            if (!string.IsNullOrEmpty(cnpjAplicacao))
                foreach (var item in veiculo.VeiculoConjuntos)
                    numeroEixos += item.QuantidadeEixos;

            if (!string.IsNullOrEmpty(cnpjAplicacao))
            {
                var autenticacaoEmpresa = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);

                if (autenticacaoEmpresa == null || !autenticacaoEmpresa.Any())
                    return new Retorno<object>(true, null);

                if (!autenticacaoEmpresa.Any(x => x.IdEmpresa == veiculo.IdEmpresa) && veiculo.IdEmpresa != null)
                    return new Retorno<object>(true, new
                    {
                        veiculo.IdVeiculo,
                        NumeroEixos = numeroEixos,
                        veiculo.IdTipoCavalo,
                        veiculo.IdTipoCarreta,
                        Ano = veiculo.AnoModelo,
                        veiculo.IdFilial
                    });
            }

            var quantidadeEixoCavalo = 0;
            var quantidadeEixoCarreta = 0;

            if (veiculo.ComTracao)
                quantidadeEixoCavalo = veiculo.QuantidadeEixos;
            else
                quantidadeEixoCarreta = veiculo.QuantidadeEixos;

            return new Retorno<object>(true, new
            {
                veiculo.IdVeiculo,
                NumeroEixos = numeroEixos,
                veiculo.IdTipoCavalo,
                veiculo.IdTipoCarreta,
                Ano = veiculo.AnoModelo,
                veiculo.IdFilial,
                numeroeixoscavalo = quantidadeEixoCavalo,
                numeroeixoscarreta = quantidadeEixoCarreta
            });
        }

        public Retorno<object> ConsultarVeiculoPorNumeroFrota(string token, string cnpjAplicacao, long numeroFrota)
        {
            if (!string.IsNullOrEmpty(cnpjAplicacao))
            {
                var autenticacaoEmpresa = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);
                
                if (autenticacaoEmpresa == null || !autenticacaoEmpresa.Any())
                    return new Retorno<object>(true, null);
            }

            var listaVeiculos = _veiculoApp.GetVeiculosPorNumerosFrotas(new List<long> {numeroFrota}).ToList();

            if (listaVeiculos.Any())
            {
                var veiculo = listaVeiculos.First();

                if (veiculo.Ativo)
                {
                    var numeroEixos = veiculo.QuantidadeEixos;

                    return new Retorno<object>(true, new
                    {
                        veiculo.IdVeiculo,
                        NumeroEixos = numeroEixos,
                        veiculo.IdTipoCavalo,
                        veiculo.IdTipoCarreta,
                        Ano = veiculo.AnoModelo,
                        veiculo.IdFilial
                    });
                }
            }

            return new Retorno<object>(true, null);
        }

        public Retorno<object> Alterar(VeiculoIntegrarRequestModel @params)
        {
            try
            {
                var msgRetorno = string.Empty;
                var empresaAutorizada = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(@params.CNPJAplicacao, @params.Token);

                if (empresaAutorizada == null && !string.Equals(@params.Token, WebConfigurationManager.AppSettings["Token"], StringComparison.CurrentCultureIgnoreCase))
                    throw new Exception("Empresa não possui permissão para realizar essa operação.");

                var empresa = _empresaApp.Get(@params.CNPJEmpresa);

                if (empresa == null) 
                    throw new Exception("Empresa não encontrada.");
                
                var veiculosPorPlaca = _veiculoApp
                    .GetTodosVeiculosPorPlaca(@params.Placa.RemoveSpecialCharacters().ToUpper(), empresa.IdEmpresa)
                    .ToList();

                if (veiculosPorPlaca.Count < 1)
                    throw new Exception($"Veículo não identificado pela placa informada >> Placa: {@params.Placa}");

                var veiculoAlterar = veiculosPorPlaca.FirstOrDefault();

                if (veiculoAlterar == null) 
                    return new Retorno<object>(false, msgRetorno);

                if (!veiculoAlterar.Ativo)
                    veiculoAlterar.Ativo = true;

                if (!string.IsNullOrWhiteSpace(@params.RENAVAM))
                {
                    if (@params.RENAVAM.Length == 9)
                        @params.RENAVAM = @params.RENAVAM.PadLeft(11, '0');

                    veiculoAlterar.RENAVAM = @params.RENAVAM;
                }

                #region Setamos as propriedades

                if (!string.IsNullOrWhiteSpace(@params.Chassi))
                    veiculoAlterar.Chassi = @params.Chassi;

                if (@params.AnoFabricacao.HasValue)
                    veiculoAlterar.AnoFabricacao = @params.AnoFabricacao;

                if (@params.AnoModelo.HasValue)
                    veiculoAlterar.AnoModelo = @params.AnoModelo;

                if (!string.IsNullOrWhiteSpace(@params.Marca))
                    veiculoAlterar.Marca = @params.Marca;

                if (@params.TipoContrato > 0)
                    veiculoAlterar.TipoContrato = @params.TipoContrato;

                if (!string.IsNullOrWhiteSpace(@params.Modelo))
                    veiculoAlterar.Modelo = @params.Modelo;

                if (@params.ComTracao.HasValue)
                    veiculoAlterar.ComTracao = @params.ComTracao.Value;

                if (@params.IBGECidade.HasValue)
                {
                    var enderecoVeiculo = ConsultarCidadeEstadoPaisByIbge(@params.IBGECidade.Value);
                    veiculoAlterar.IdCidade = enderecoVeiculo.Item1;
                    veiculoAlterar.IdEstado = enderecoVeiculo.Item2;
                    veiculoAlterar.IdPais = enderecoVeiculo.Item3;
                }

                if (@params.IdTipoCavalo.HasValue)
                    veiculoAlterar.IdTipoCavalo = @params.IdTipoCavalo;

                if (@params.IdTipoCarreta.HasValue)
                    veiculoAlterar.IdTipoCarreta = @params.IdTipoCarreta;

                if (@params.IdTecnologia.HasValue)
                    veiculoAlterar.IdTecnologia = @params.IdTecnologia;

                if (@params.QuantidadeEixos.HasValue)
                    veiculoAlterar.QuantidadeEixos = @params.QuantidadeEixos.Value;

                if (@params.NumeroFrota.HasValue)
                    veiculoAlterar.NumeroFrota = @params.NumeroFrota.Value;

                if (@params.CodigoDaOperacao.HasValue)
                    veiculoAlterar.IdOperacao = @params.CodigoDaOperacao.Value;

                if (!string.IsNullOrWhiteSpace(@params.RENAVAM))
                {
                    if (@params.RENAVAM.Length == 9)
                        @params.RENAVAM = @params.RENAVAM.PadLeft(11, '0');

                    veiculoAlterar.RENAVAM = @params.RENAVAM;
                }

                if (!string.IsNullOrWhiteSpace(@params.CPFMotorista))
                {
                    if (veiculoAlterar.TipoContrato == ETipoContrato.Terceiro)
                    {
                        var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFMotorista);
                        
                        if (idUsuario.HasValue)
                            veiculoAlterar.IdUsuario = idUsuario.GetValueOrDefault();
                    }
                    else
                    {
                        var idMotorista = _motoristaApp.GetIdPorCpf(@params.CPFMotorista);
                        
                        if (idMotorista.HasValue)
                            veiculoAlterar.IdMotorista = idMotorista.GetValueOrDefault();
                    }
                }

                if (!string.IsNullOrWhiteSpace(@params.CPFCNPJUsuario))
                {
                    var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJUsuario);
                    
                    if (idUsuario.HasValue)
                        veiculoAlterar.IdUsuario = idUsuario.GetValueOrDefault();
                }

                if (!string.IsNullOrWhiteSpace(@params.CorVeiculo))
                    veiculoAlterar.CorVeiculo = @params.CorVeiculo;

                veiculoAlterar.Placa = @params.Placa.RemoveSpecialCharacters();

                if (!@params.TipoRodagem.HasValue)
                {
                    var defaultDuplo = _parametrosApp.GetDefaultIntegracaoTipoRodagemDupla(empresa.IdEmpresa);

                    veiculoAlterar.TipoRodagem = defaultDuplo ? ETipoRodagem.Duplo : ETipoRodagem.Simples;
                }
                else
                {
                    veiculoAlterar.TipoRodagem = @params.TipoRodagem.Value;
                }

                #endregion

                if (!string.IsNullOrWhiteSpace(@params.CPFCNPJProprietario))
                    veiculoAlterar.IdProprietario = _proprietarioApp.GetIdPorCpfCnpj(@params.CPFCNPJProprietario, empresa.IdEmpresa);

                var validation = _veiculoApp.Update(veiculoAlterar);
                
                if (validation.IsValid)
                {
                    return new Retorno<object>(true, null);
                }

                msgRetorno = validation.ToFormatedMessage();

                return new Retorno<object>(false, msgRetorno, msgRetorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Alterar)} >> {e.Message}");
            }
        }

        public Tuple<int, int, int> ConsultarCidadeEstadoPaisByIbge(int ibgeCidade)
        {
            var cidade = _cidadeApp.GetCidadeByIBGE(ibgeCidade);

            if (cidade != null)
                return new Tuple<int, int, int>(cidade.IdCidade, cidade.Estado.IdEstado, cidade.Estado.Pais.IdPais);

            throw new Exception("Cidade não encontrada.");
        }

        public byte[] GerarRelatorioGridVeiculos(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string extensao)
        {
            return _veiculoApp.GerarRelatorioGridVeiculos(idEmpresa, orderFilters, filters, GetLogo(idEmpresa), extensao);
        }

        public void Dispose()
        {
            //GC.Collect();
        }
    }
}
﻿using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersATS
{
    public class LayoutCartaoAtsController : DefaultController
    {
        private readonly ILayoutCartaoApp _layoutCartaoApp;

        public LayoutCartaoAtsController(ILayoutCartaoApp layoutCartaoApp)
        {
            _layoutCartaoApp = layoutCartaoApp;
        }

        [HttpGet]
        [EnableLogRequest]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetConfHistoricoTransacoes()
        {
            try
            {
                var json = ResponderSucesso(_layoutCartaoApp.GetAll().OrderBy(p => p.IdLayout)
                    .Select(x => new { x.IdLayout, x.Nome }));
                return json;
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

    }
}
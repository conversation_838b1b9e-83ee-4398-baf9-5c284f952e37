﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Domain.Interface.Dapper;

namespace ATS.Domain.Service
{
    public class NotificaoService : ServiceBase, INotificacaoService
    {
        private readonly INotificacaoRepository _notificacaoRepository;
        private readonly INotificacaoDapper _notificacaoDapper;

        public NotificaoService(INotificacaoRepository notificacaoRepository, INotificacaoDapper notificacaoDapper)
        {
            _notificacaoRepository = notificacaoRepository;
            _notificacaoDapper = notificacaoDapper;
        }

        /// <summary>
        /// Retorna a notificação pelo código
        /// </summary>
        /// <param name="id">Código da notificação</param>
        /// <returns></returns>
        public Notificacao Get(int id)
        {
            return _notificacaoRepository.Get(id);
        }

        /// <summary>
        /// Retorna lista de notificações novas
        /// </summary>
        /// <param name="idUsuario">Id do Usuario</param>
        /// <returns></returns>
        public ICollection<Notificacao> GetNotificacoesNovas(int idUsuario)
        {
            List<Notificacao> retorno = new List<Notificacao>();
            var dados = _notificacaoRepository
                .Include(x => x.usuario)
                .Where(x => !x.Recebida);

            return retorno;
        }

        public bool InserirDapper(string idusuario, string tipo, string conteudo, DateTime datahoraenvio, int idTipoNotificacao)
        {

            return _notificacaoDapper.Inserir(idusuario, tipo, conteudo, datahoraenvio, idTipoNotificacao);

        }

        /// <summary>
        /// Retorna todas as notificações para o idusuario pelo filtro de data
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <param name="dataFiltro"></param>
        /// <returns></returns>
        public ICollection<Notificacao> GetNotificacoesPeloUsuario(int idUsuario)
        {
            var dados = _notificacaoRepository
                .Include(x => x.usuario)
                /*.Include(x => x.TipoNotificacao)*/
                .Where(x => x.IdUsuario == idUsuario && x.Lida.HasValue && !x.Lida.Value );

            return dados.ToList();
        }

        /// <summary>
        /// Buscar Mensagem com todos os filhos
        /// </summary>
        /// <param name="id">Id de notificação</param>
        /// <returns>Objeto Notificacao</returns>
        public Notificacao GetWithAllChilds(int id)
        {
            return _notificacaoRepository
                .Find(x => x.IdNotificacao == id)
                .Include(x => x.usuario)
                .FirstOrDefault();
        }

        /// <summary>
        /// Adicionar a mensagem
        /// </summary>
        /// <param name="mensagem">Informações sobre a notificação</param>
        /// <returns></returns>
        public Notificacao Add(Notificacao notificacao)
        {
            try
            {
                return _notificacaoRepository.Add(notificacao);
            }
            catch (Exception e)
            {
                _logger.Error($"{e.Message} - {e.InnerException.ToString()}");
                return null;
            }
        }

        /// <summary>
        /// Retorna todas as notificações a partir da data base de filtro
        /// </summary>
        /// <param name="dataBase">Data base para filtro de novas notificações</param>
        /// <returns></returns>
        public IQueryable<Notificacao> GetPorDataEnvio(DateTime dataBase)
        {
            return _notificacaoRepository.Find(x => x.DataHoraEnvio > dataBase)
                .Include(x => x.usuario);
        }

        /// <summary>
        /// Retorna todas as notificações cadastradas a partir da data informada por usuario e filtros
        /// </summary>
        /// <param name="idUsuario">Código do remetente</param>
        /// <param name="dataInicial">Data inicial</param>
        /// <param name="dataFinal">Data final</param>
        /// <param name="tipo"> Tipo </param>
        public IQueryable<Notificacao> ConsultarRecebidos(int idUsuario, DateTime dataInicial, DateTime dataFinal, int tipo)
        {
            List<Notificacao> retorno = new List<Notificacao>();
            // Recuperamos todas as notifica que estão no intervalo de tempo do filtro
            // e onde o remetente não é o idusuario então são apenas as recebidas
            IQueryable<Notificacao> retMensagens =
                _notificacaoRepository.All()
                    .Include(m => m.usuario)
                    .Where(m => m.DataHoraEnvio >= dataInicial && m.DataHoraEnvio <= dataFinal
                            && m.IdUsuario != idUsuario);


            retMensagens = retMensagens.Where(m => m.Tipo == tipo);

            return retMensagens.AsQueryable();
        }

        public ValidationResult SetWithRecebido(List<int> ids)
        {
            try
            {

                var rep = _notificacaoRepository;
                Notificacao notificacao = new Notificacao();
                foreach (var item in ids)
                {
                    notificacao = rep.Get(item);
                    notificacao.Recebida = true;
                    rep.Update(notificacao);
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }


            return new ValidationResult();
        }

        public ValidationResult SetRecebidoNovo(List<int> ids)
        {
            try
            {

                var rep = _notificacaoRepository;
                Notificacao notificacao = new Notificacao();
                foreach (var item in ids)
                {
                    notificacao = rep.Get(item);
                    notificacao.RecebidaNovo = true;
                    rep.Update(notificacao);
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }


            return new ValidationResult();
        }

        public ValidationResult SetWithLido(int id)
        {
            try
            {
                var rep = _notificacaoRepository;
                Notificacao notificacao = new Notificacao();

                notificacao = Get(id);
                notificacao.Lida = true;
                rep.Update(notificacao);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }


            return new ValidationResult();
        }

        public IQueryable<Notificacao> ObterNotificacoesNaoLidas(int idUsuario)
        {
            var notificacoesNaoLidas = _notificacaoRepository
                .Find(x => x.IdUsuario == idUsuario)
                .Where(x => x.Lida.HasValue && !x.Lida.Value);

            return notificacoesNaoLidas;
        }

    }
}
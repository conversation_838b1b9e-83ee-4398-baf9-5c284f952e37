﻿
using System;

namespace ATS.CrossCutting.Reports.Protocolo
{
    public class ProtocoloModel
    {
        public string Logo { get; set; }
        public string TotalValorProtocolo { get; set; }
        //public string TotalValorPagamentoAntecipado { get; set; }
        public bool PerfilEmpresa { get; set; }
        public string DataGeracao { get; set; }
        public string UsuarioGeracaoProtocolo { get; set; }
    }

    public class ProtocoloListModel
    {
        public int IdProtocolo { get; set; }
        public string Estabelecimento { get; set; }
        public string Empresa { get; set; }
        public string DataGeracao { get; set; }
        public string DataPagamento { get; set; }
        public string DataAprovacao { get; set; }
        public string DataPrevisaoPagamento { get; set; }
        public string Antecipado { get; set; }
        public string ValorProtocolo { get; set; }
        //public string DataPagamentoAntecipado { get; set; }
        //public string ValorProtocoloAntecipado { get; set; }
        //public string ReceitaAntecipacao { get; set; }
        public string Status { get; set; }
        public string Pago { get; set; }
        public string NumeroEventos { get; set; }
    }
}

﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Models
{
    public class KeycloakClientModel
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        
        [<PERSON>son<PERSON>roperty("clientId")]
        public string ClientId { get; set; }
        
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("secret")]
        public string Secret { get; set; }

        [<PERSON>son<PERSON>roperty("defaultClientScopes")]
        public List<string> DefaultClientScopes { get; set; }

        [JsonProperty("optionalClientScopes")]
        public List<string> OptionalClientScopes { get; set; }


    }
}

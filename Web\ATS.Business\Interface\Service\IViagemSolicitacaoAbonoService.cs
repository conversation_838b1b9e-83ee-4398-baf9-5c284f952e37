﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Domain.Interface.Service
{
    public interface IViagemSolicitacaoAbonoService
    {
        List<ViagemSolicitacaoAbono> GetSolicitacoes(string token, string numero, DateTime? dataInicio, DateTime? dataFim, EStatusAbono? status);
        ViagemSolicitacaoAbono AlterarStatus(int IdViagemSolicitacao, EStatusAbono status);
    }
}
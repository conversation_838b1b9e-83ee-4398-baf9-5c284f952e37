﻿using ATS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Data.Entity.Migrations;
using System.Linq;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class TipoEstabelecimentoSeeder
    {
        public void Execute(AtsContext context)
        {
            var icone = context.Icone.FirstOrDefault();
            string[] listaTipos = new[] {
                "Auto elétrica",
                "Banco",
                "Borracharia",
                "Estacionamento",
                "Farmácia",
                "Hospital",
                "Hotel",
                "Loja de conveniência",
                "Mecânica",
                "Padaria",
                "Posto de gasolina",
                "Restaurante",
                "Supermercado"
            };

            if (!context.TipoEstabelecimento.Any(x => listaTipos.Contains(x.Desc<PERSON>o)))
            {
                listaTipos.ToList()
                    .ForEach(item => context.TipoEstabelecimento.AddOrUpdate(new TipoEstabelecimento()
                    {
                        Descricao = item,
                        IdIcone = icone?.IdIcone ?? null
                    }));
            }
        }
    }
}

﻿using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class Motivo
    {
        public int IdMotivo { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdFilial { get; set; }
        public string Descricao { get; set; }
        public bool Ativo { get; set; } = true;
        public DateTime DataCadastro { get; set; }

        #region Relacionamentos
        public virtual Empresa Empresa { get; set; }
        public virtual Filial Filial { get; set; }
        public virtual List<TipoMotivo> TipoMotivo { get; set; }
        #endregion

        #region Navegação inversa

        public virtual ICollection<Credenciamento> Credenciamentos { get; set; }

        public virtual ICollection<ProtocoloEvento> ProtocoloEventos { get; set; }

        public virtual ICollection<ProtocoloEvento> ProtocoloEventosOcorrencias { get; set; }
        
        public virtual ICollection<ProtocoloEvento> ProtocoloEventosDescontados { get; set; }

        public virtual ICollection<ProtocoloAntecipacao> ProtocoloAntecipacoes { get; set; }

        public virtual ICollection<ViagemSolicitacaoAbono> SolicitacoesAbono { get; set; }

        public virtual ICollection<ViagemEvento> ViagemEvento { get; set; }

        public virtual ICollection<ViagemEvento> ViagemEventosReijeitadas { get; set; }

        #endregion
    }
}

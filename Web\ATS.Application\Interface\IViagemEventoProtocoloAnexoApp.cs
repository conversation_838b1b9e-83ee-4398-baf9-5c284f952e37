using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IViagemEventoProtocoloAnexoApp : IAppBase<ViagemEventoProtocoloAnexo>
    {
        ValidationResult Add(ViagemEventoProtocoloAnexoModel viagemEventoProtocoloAnexosModel);
        ValidationResult AddListOfAnexos(List<ViagemEventoProtocoloAnexoModel> viagemEventoProtocoloAnexosModel, List<int> idsAnexosRemover);
        ValidationResult RemoverAnexos(List<int> idsAnexosRemover);
        List<ViagemEventoProtocoloAnexoModel> GetAnexosCadastrados(int idViagemEvento);
    }
}
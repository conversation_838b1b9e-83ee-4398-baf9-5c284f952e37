﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class TipoCarretaRepository : Repository<TipoCarreta>, ITipoCarretaRepository
    {
        public TipoCarretaRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado para consultar Tipo de Carreta.
        /// </summary>
        /// <param name="nome">Nome de Tipo de Carreta.</param>
        /// <returns>IQueryable de TipoCarretaGrid</returns>
        public IQueryable<TipoCarretaGrid> Consultar(string nome)
        {
            var ret = from tipoCarreta in All()
                   select new TipoCarretaGrid
                   {
                       IdTipoCarreta = tipoCarreta.IdTipoCarreta,
                       Nome = tipoCarreta.Nome,
                       CategoriaCarreta = tipoCarreta.Categoria == ECategoriaTipoCarreta.Aberta ? "Aberta" : tipoCarreta.Categoria == ECategoriaTipoCarreta.Fechada ? "Fechada" : tipoCarreta.Categoria == ECategoriaTipoCarreta.Especial ? "Especial" : "",
                       Ativo = tipoCarreta.Ativo,
                       IdEmpresa = tipoCarreta.IdEmpresa,
                       RazaoSocial = tipoCarreta.Empresa.RazaoSocial,
                       Destacar = tipoCarreta.Destacar
                   };

            if (!string.IsNullOrWhiteSpace(nome))
                ret = ret.Where(pp => pp.Nome.Contains(nome));

            return ret;
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Carreta por categoria
        /// </summary>
        /// <param name="categoria">Categoria de Tipo de Carreta</param>
        /// <returns>IQueryable de Tipo de Carreta</returns>
        public IQueryable<TipoCarreta> GetPorCategoria(ECategoriaTipoCarreta categoria)
        {
            return from tipoCarreta in All()
                   where tipoCarreta.Categoria == categoria
                   && tipoCarreta.Ativo
                   select tipoCarreta;
        }

        public object ConsultarSemEmpresa()
        {
            return from p in All()
                   where p.IdEmpresa == null
                   select new
                   {
                       p.Nome,
                       p.IdTipoCarreta
                   };
        }

        public IQueryable<TipoCarreta> GetTipoCarretaAtualizadas(DateTime dataAtualizacao, List<int> idsEmpresa)
        {
            var retorno = from tipoCarreta in All()
                          where tipoCarreta.DataHoraUltimaAtualizacao >= dataAtualizacao
                          select tipoCarreta;
            retorno = idsEmpresa != null
                ? retorno.Where(x => x.IdEmpresa == null || idsEmpresa.Contains(x.IdEmpresa.Value))
                : retorno.Where(x => x.IdEmpresa == null);

            return retorno;
        }
    }
}
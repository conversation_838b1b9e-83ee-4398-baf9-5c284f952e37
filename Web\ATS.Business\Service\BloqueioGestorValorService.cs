using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using AutoMapper.QueryableExtensions;

namespace ATS.Domain.Service
{
    public class BloqueioGestorValorService : ServiceBase, IBloqueioGestorValorService
    {
        private IBloqueioGestorValorRepository _bloqueioGestorValorRepository;
        private IBloqueioGestorTipoRepository _bloqueioGestorTipoRepository;

        public BloqueioGestorValorService(IBloqueioGestorValorRepository bloqueioGestorValorRepository, IBloqueioGestorTipoRepository bloqueioGestorTipoRepository)
        {
            _bloqueioGestorValorRepository = bloqueioGestorValorRepository;
            _bloqueioGestorTipoRepository = bloqueioGestorTipoRepository;
        }

        public IQueryable<BloqueioGestorValor> GetAll()
        {
            return _bloqueioGestorValorRepository.GetAll();
        }

        public BloqueioGestorValorDto PegarBloqueioGestorValor(int? idEmpresa, int? idFilial)
        {
            var meuBloqueioGestorValor = GetAll().Include(x => x.BloqueioGestorTipo).Include(x => x.BloqueioOrigemTipo);

            meuBloqueioGestorValor = idEmpresa.HasValue 
                ? meuBloqueioGestorValor.Where(x => x.IdEmpresa == idEmpresa) 
                : meuBloqueioGestorValor.Where(x => x.IdEmpresa == 0);
            
            meuBloqueioGestorValor = idFilial.HasValue 
                ? meuBloqueioGestorValor.Where(x => x.IdFilial == idFilial) 
                : meuBloqueioGestorValor.Where(x => x.IdFilial == null);
            
            var response = meuBloqueioGestorValor.ToList();

            var tiposBloqueio = _bloqueioGestorTipoRepository.GetAll().ToList();

            foreach (var tipoBloqueio in tiposBloqueio)
            {
                if (!response.Exists(c => c.IdEmpresa == idEmpresa && c.IdBloqueioGestorTipo == tipoBloqueio.IdBloqueioGestorTipo 
                                                                   && c.IdBloqueioOrigemTipo == EBloqueioOrigemTipo.Portal) && tipoBloqueio.HabilitarPorEmpresa && !idFilial.HasValue)
                {
                    response.Add(new BloqueioGestorValor()
                    {
                        Valor = 0,
                        IdEmpresa = idEmpresa ?? 0,
                        IdFilial = null,
                        IdBloqueioGestorTipo = tipoBloqueio.IdBloqueioGestorTipo,
                        IdBloqueioOrigemTipo = EBloqueioOrigemTipo.Portal,
                        BloqueioGestorTipo = tipoBloqueio
                    });
                } 
                
                if (!response.Exists(c => c.IdEmpresa == idEmpresa && c.IdBloqueioGestorTipo == tipoBloqueio.IdBloqueioGestorTipo && c.IdBloqueioOrigemTipo == EBloqueioOrigemTipo.API) 
                         && tipoBloqueio.HabilitarPorEmpresa && !idFilial.HasValue)
                {
                    response.Add(new BloqueioGestorValor()
                    {
                        Valor = 0,
                        IdEmpresa = idEmpresa ?? 0,
                        IdFilial = null,
                        IdBloqueioGestorTipo = tipoBloqueio.IdBloqueioGestorTipo,
                        IdBloqueioOrigemTipo = EBloqueioOrigemTipo.API,
                        BloqueioGestorTipo = tipoBloqueio
                    });
                }
                
                if (!response.Exists(c => c.IdEmpresa == idEmpresa && c.IdBloqueioGestorTipo == tipoBloqueio.IdBloqueioGestorTipo && c.IdFilial.HasValue && c.IdBloqueioOrigemTipo == EBloqueioOrigemTipo.Portal)
                    && tipoBloqueio.HabilitarPorFilial && idFilial.HasValue)
                {
                    response.Add(new BloqueioGestorValor()
                    {
                        Valor = 0,
                        IdEmpresa = idEmpresa ?? 0,
                        IdFilial = idFilial,
                        IdBloqueioGestorTipo = tipoBloqueio.IdBloqueioGestorTipo,
                        IdBloqueioOrigemTipo = EBloqueioOrigemTipo.Portal,
                        BloqueioGestorTipo = tipoBloqueio
                    });
                }  
                if (!response.Exists(c => c.IdEmpresa == idEmpresa && c.IdBloqueioGestorTipo == tipoBloqueio.IdBloqueioGestorTipo && c.IdFilial.HasValue && c.IdBloqueioOrigemTipo == EBloqueioOrigemTipo.API)
                    && tipoBloqueio.HabilitarPorFilial && idFilial.HasValue)
                {
                    response.Add(new BloqueioGestorValor()
                    {
                        Valor = 0,
                        IdEmpresa = idEmpresa ?? 0,
                        IdFilial = idFilial,
                        IdBloqueioGestorTipo = tipoBloqueio.IdBloqueioGestorTipo,
                        IdBloqueioOrigemTipo = EBloqueioOrigemTipo.API,
                        BloqueioGestorTipo = tipoBloqueio
                    });
                }
            }

            return new BloqueioGestorValorDto()
            {
                Portal = response.AsQueryable()
                    .Where(x => x.IdBloqueioOrigemTipo == EBloqueioOrigemTipo.Portal)
                    .OrderByDescending(x => x.IdBloqueioGestorTipo)
                    .ProjectTo<BloqueioGestorValorItem>().ToList(),
                Api = response.AsQueryable()
                    .Where(x => x.IdBloqueioOrigemTipo == EBloqueioOrigemTipo.API)
                    .OrderByDescending(x => x.IdBloqueioGestorTipo)
                    .ProjectTo<BloqueioGestorValorItem>().ToList(),
            };
        }

        public void IntegrarValores(BloqueioGestorValorDto valores)
        {
            var listBloqueioGestor = new List<BloqueioGestorValorItem>();
            
            if (valores == null) 
                return;
            
            
            if (valores.Portal.Any())
                listBloqueioGestor.AddRange(valores.Portal);
            
            if (valores.Api.Any())
                listBloqueioGestor.AddRange(valores.Api);

            
            foreach (var valor in listBloqueioGestor)
            {
                var obj = _bloqueioGestorValorRepository.Where(v =>
                    v.IdFilial == valor.IdFilial && v.IdEmpresa == valor.IdEmpresa &&
                    v.IdBloqueioGestorTipo == valor.IdBloqueioGestorTipo && v.IdBloqueioOrigemTipo == valor.Origem).FirstOrDefault();
                    
                if (obj != null)
                {
                    obj.Valor = valor.Valor;
                    _bloqueioGestorValorRepository.Update(obj);
                }
                else
                {
                    obj = new BloqueioGestorValor
                    {
                        IdFilial = valor.IdFilial == 0 ? null : valor.IdFilial,
                        IdEmpresa = valor.IdEmpresa ,
                        IdBloqueioGestorTipo = valor.IdBloqueioGestorTipo,
                        Valor = valor.Valor,
                        IdBloqueioOrigemTipo = valor.Origem
                    };
                        
                    _bloqueioGestorValorRepository.Add(obj);
                }
            }
        }

        public decimal? ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa,EBloqueioOrigemTipo? origem)
        {
            var repository = _bloqueioGestorValorRepository;

            var queryBase = repository.GetAll().Where(c => c.IdBloqueioGestorTipo == (int) bloqueioGestorTipo);

            var query = queryBase.Where(c => c.IdEmpresa == idEmpresa && c.IdFilial == null && c.IdBloqueioOrigemTipo == origem).Select(c => c.Valor);

            var valor = query.Any() ? query.First() : (decimal?) null;

            return valor;
        }

        public decimal? ValorLimiteConfiguradoFilial(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa, int? idFilial,EBloqueioOrigemTipo? origem)
        {
            if (!idFilial.HasValue)
                return null;
            
            var repository = _bloqueioGestorValorRepository;

            var queryBase = repository.GetAll().Where(c => c.IdBloqueioGestorTipo == (int) bloqueioGestorTipo);

            var query = queryBase.Where(c => c.IdEmpresa == idEmpresa && c.IdFilial == idFilial && c.IdBloqueioOrigemTipo == origem).Select(c => c.Valor);

            var valor = query.Any() ? query.First() : (decimal?) null;

            return valor;
        }
    }
}
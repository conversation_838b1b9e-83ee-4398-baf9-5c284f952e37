﻿using ATS.Data.Context.Mapping.Common;
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Data.Context.Mapping
{
    public class ProprietarioContatoMap : ContatoBaseMap<ProprietarioContato>
    {
        public ProprietarioContatoMap()
        {
            ToTable("PROPRIETARIO_CONTATO");

            HasKey(t => new { t.IdProprietario, IdEmpresa = t.IdEmpresa, t.IdContato });

            Property(t => t.IdProprietario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdContato)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(a => a.Proprietario)
                .WithMany(b => b.Contatos)
                .HasForeignKey(c => new { c.IdProprietario, IdEmpresa = c.IdEmpresa});
        }
    }
}
using System.Collections.Generic;

namespace ATS.Domain.DTO.PrestacaoContas
{
    public class PrestacaoContasGridResponse
    {
        public int totalItems { get; set; }
        public List<PrestacaoContasGridResponseItem> items { get; set; } = new();
    }

    public class PrestacaoContasGridResponseItem
    {
        public int Id { get; set; }
        public string Codigo { get; set; }
        public int Status { get; set; }
        public string StatusDescricao { get; set; }
        public string UsuarioNome { get; set; }
        public string UsuarioDocumento { get; set; }
        public string Valor { get; set; }
        public string Observacao { get; set; }
        public string DataSolicitacao { get; set; }
        public string DataConclusao { get; set; }
    }
}
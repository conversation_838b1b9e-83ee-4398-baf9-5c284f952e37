﻿using System;
using System.Linq;
using System.Data.Entity;
using System.Linq.Dynamic;
using System.Collections.Generic;
using ATS.Domain.Models;
using ATS.Domain.Entities;
using ATS.CrossCutting.IoC;
using ATS.Domain.Interface.Database;

namespace ATS.WS.Services
{
    public class SrvClienteProdutoEspecie : SrvBase
    {
        private readonly IClienteProdutoEspecieRepository _clienteProdutoEspecieRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IEspecieRepository _especieRepository;

        public SrvClienteProdutoEspecie(IClienteProdutoEspecieRepository clienteProdutoEspecieRepository, IProdutoRepository produtoRepository, IEspecieRepository especieRepository)
        {
            _clienteProdutoEspecieRepository = clienteProdutoEspecieRepository;
            _produtoRepository = produtoRepository;
            _especieRepository = especieRepository;
        }

        public void RemoverRegistrosPorCliente(int idCliente)
        {
            var todosRegistrosUsuario = _clienteProdutoEspecieRepository
                                                 .Find(x => x.IdCliente == idCliente)
                                                 .ToList();
            if (todosRegistrosUsuario != null && todosRegistrosUsuario.Any())
            {
                foreach (var item in todosRegistrosUsuario)
                {

                    _clienteProdutoEspecieRepository
                             .Delete(item);
        }
            }
        }

        public List<ClienteProdutoEspecieModel> Integrar(int idCliente, List<ProdutoEspecieModel> @params)
        {
            try
            {
                var listaClienteProdutoEspecie = new List<ClienteProdutoEspecieModel>();
                if (idCliente == 0)
                    throw new Exception("O id do cliente não pode ser zero.");
                if (@params == null || @params.Count == 0)
                    return listaClienteProdutoEspecie;

                RemoverRegistrosPorCliente(idCliente);

                foreach (var objeto in @params)
                {
                    var registroExiste = _clienteProdutoEspecieRepository
                                                     .Where(x => x.IdCliente == idCliente &&
                                                                 x.IdProduto == objeto.IdProduto &&
                                                                 x.IdEspecie == objeto.IdEspecie)
                                                     .Any();
                    if (registroExiste) continue;

                    var produto = _produtoRepository.Get(objeto.IdProduto);
                    if (produto == null)
                        throw new Exception("Produto inválido na lista de produto/espécie.");

                    var especie = _especieRepository.Get(objeto.IdEspecie);
                    if (especie == null)
                        throw new Exception("Espécie inválida na lista de produto/espécie.");

                    var clienteProdutoEspecie = new ClienteProdutoEspecie()
                    {
                        IdCliente = idCliente,
                        IdProduto = objeto.IdProduto,
                        IdEspecie = objeto.IdEspecie
                    };

                    _clienteProdutoEspecieRepository
                             .Add(clienteProdutoEspecie);

                    listaClienteProdutoEspecie.Add(new ClienteProdutoEspecieModel()
                    {
                        IdCliente = idCliente,
                        IdProduto = objeto.IdProduto,
                        IdEspecie = objeto.IdEspecie
                    });
                }

                return listaClienteProdutoEspecie;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public List<ClienteProdutoEspecie> GetTodosPorCliente(int idCliente) =>
            _clienteProdutoEspecieRepository.Where(x => x.IdCliente == idCliente)
                                                             .Include(x => x.Produto)
                                                             .Include(x => x.Especie)
                                                             .ToList();
    }
}
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IResgateCartaoAtendimentoApp : IBaseApp<IResgateCartaoAtendimentoService>
    {
        ValidationResult Add(ResgateCartaoAtendimento resgatarCartao);
        ValidationResult Update(ResgateCartaoAtendimento resgatarCartao);
        object ConsultarGrid(ConsultarResgateValorDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult Alterar(ResgateCartaoAtendimento resgatar, string motivoEstorno, int usuario);
        object GetResgate(int idResgate);
    }
}
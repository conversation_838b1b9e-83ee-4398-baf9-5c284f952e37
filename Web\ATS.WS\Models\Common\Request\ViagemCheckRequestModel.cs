﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;

namespace ATS.WS.Models.Common.Request
{
    public class ViagemCheckRequestModel : RequestBase
    {
        /// <summary>
        /// CPF do motorista
        /// </summary>
        public string CPFMotorista { get; set; }

        /// <summary>
        /// Código da viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código da Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Data e Hora
        /// </summary>
        public DateTime DataHora { get; set; }

        /// <summary>
        /// Status (Check) da viagem
        /// </summary>
        public EStatusCheckViagem Status { get; set; }

        /// <summary>
        /// Latitude do check-in
        /// </summary>
        public decimal Latitude { get; set; }

        /// <summary>
        /// Longitude do check-in
        /// </summary>
        public decimal Longitude { get; set; }

        /// <summary>
        /// Tipo do evento do check-in
        /// </summary>
        public ETipoEvento TipoEvento { get; set; }

    }
}
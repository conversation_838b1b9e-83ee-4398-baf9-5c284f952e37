﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.DTO.Plano;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Data.Repository.EntityFramework
{
    public class PlanoRepository : Repository<Plano>, IPlanoRepository
    {
        public PlanoRepository(AtsContext context) : base(context)
        {
        }

        public List<PlanoEmpresaDto> GetPlanosAtivos()
        {
            var planos = (from plano in All()
                          where plano.Ativo == true
                          select new PlanoEmpresaDto
                          {
                              IdPlano = plano.IdPlano,
                              Descricao = plano.Descricao,
                              Ativo = false
                          }).ToList();

            return planos;
        }

        public List<PlanoEmpresaDto> GetPlanosAtivosPorEmpresa(int empresaId)
        {
            var planos = (from plano in All()
                             .Include(p => p.PlanosEmpresa)
                         where plano.Ativo == true
                         select new PlanoEmpresaDto
                         {
                             IdPlano = plano.IdPlano,
                             Descricao = plano.Descricao,
                             Ativo = plano.PlanosEmpresa
                                 .Where(pe => pe.IdEmpresa == empresaId)
                                 .Select(pe => pe.Ativo)
                                 .FirstOrDefault()
                         }).ToList();

            return planos;
        }
    }

}

﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.CrossCutting.Reports.GrupoUsuario;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IGrupoUsuarioService : IService<GrupoUsuario>
    {
        IQueryable<GrupoUsuario> Consultar(string nome, int? idEmpresa, int? idEstabelecimentoBase);
        ValidationResult Add(GrupoUsuario entity);
        GrupoUsuario Get(int id);
        ValidationResult Update(GrupoUsuario grupoUsuario, List<GrupoUsuarioMenu> grupoUsuarioMenus);
        ValidationResult Ativar(int id, int idUsuarioLogOn);
        ValidationResult Inativar(int id, int idUsuarioLogOn);
        GrupoUsuario GetChilds(int id);

        void Inserir(int idEmpresa, string descricao, List<int> idMenusSelecionados, int? idEstabelecimentoBase);
        void Atualizar(int idGrupoUsuario, int idEmpresa, string descricao, List<int> idMenusSelecionados);
        DataModel<GrupoUsuarioModel> ConsultarGrid(int? idEmpresa, int? idEstabelecimentoBase, int take, int page, OrderFilters order, List<QueryFilters> filters);

        /// <summary>
        /// Retorna a lista de grupos de usuário do Empresa
        /// </summary>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="idEstabelecimentoBase"></param>
        /// <returns></returns>
        IQueryable<GrupoUsuario> GetPorEmpresa(int? idEmpresa, int? idEstabelecimentoBase);
        IQueryable<GrupoUsuario> GetQuery();
        byte[] GerarRelatorioGrid(int? idEmpresa, int? idEstabelecimentoBase, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo);
    }
}
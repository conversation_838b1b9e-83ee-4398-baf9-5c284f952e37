﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class UsuarioApiController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly SrvUsuario _srvUsuario;
        private readonly IUsuarioApp _usuarioApp;

        public UsuarioApiController(IUserIdentity userIdentity, SrvUsuario srvUsuario, IUsuarioApp usuarioApp)
        {
            _userIdentity = userIdentity;
            _srvUsuario = srvUsuario;
            _usuarioApp = usuarioApp;
        }
        
        /*[HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public ActionResult AlterarSenha(SenhaRequestModel @params)
        {
            try
            {
                var response = _srvUsuario.AlterarSenha(@params);
                if (!response.Sucesso)
                    return ResponderErro(response.Mensagem);

                return ResponderSucesso(response.Mensagem, response.Objeto);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        */
        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetImageJson()
        {
            try
            {
                byte[] byteArray = _usuarioApp.GetFoto(_userIdentity.IdUsuario);
                if (byteArray != null)
                {
                    string imgBase64 = Convert.ToBase64String(new FileContentResult(byteArray, "image/png").FileContents);
                    return ResponderSucesso(null, $"data:image/png;base64,{imgBase64}");
                }

                return ResponderErro("Imagem não encontrada");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetEstabelecimento(int idUsuario)
        {
            try
            {
                var retorno = _usuarioApp
                    .GetEstabelecimentos(_userIdentity.IdUsuario)?.FirstOrDefault();

                return ResponderSucesso(new
                {
                    retorno?.IdEstabelecimento,
                    Nome = retorno?.Estabelecimento.Descricao
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int idEmpresa, int? idFilial = null, bool vistoriador = false)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                return ResponderSucesso(_usuarioApp.ConsultarUsuarios(idEmpresa, vistoriador));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int Take, int Page, OrderFilters Order, List<QueryFilters> Filters)
        {
            try
            {
                var motoristas = _usuarioApp.ConsultarGrid(Take, Page, Order, Filters, _userIdentity);

                return ResponderSucesso(motoristas);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
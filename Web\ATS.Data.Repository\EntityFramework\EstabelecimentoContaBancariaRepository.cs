﻿using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class EstabelecimentoContaBancariaRepository : Repository<EstabelecimentoContaBancaria>, IEstabelecimentoContaBancariaRepository
    {
        public List<EstabelecimentoContaBancaria> GetByIdEstabelecimento(int idEstabelecimento)
        {
            return this
                .Where(o => o.IdEstabelecimento == idEstabelecimento).ToList();
        }

        public EstabelecimentoContaBancariaRepository(AtsContext context) : base(context)
        {
        }
    }
}

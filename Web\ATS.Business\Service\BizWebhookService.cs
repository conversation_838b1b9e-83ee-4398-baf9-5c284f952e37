using System;
using System.Configuration;
using System.Globalization;
using System.Linq;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.WS.Models.Webservice.Request.BizWebhook;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.Service
{
    public class BizWebhookService : ServiceBase, IBizWebhookService
    {
        private readonly IPushService _pushService;
        private readonly IParametrosRepository _parametrosRepository;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        private readonly IExtrattaBizApiClient _extrattaBizApiClient;
        private readonly IUsuarioRepository _usuarioRepository;

        public BizWebhookService(IPushService pushService, CartoesServiceArgs cartoesServiceArgs, 
            IParametrosRepository parametrosRepository, IExtrattaBizApiClient extrattaBizApiClient, IUsuarioRepository usuarioRepository)
        {
            _pushService = pushService;
            _cartoesServiceArgs = cartoesServiceArgs;
            _parametrosRepository = parametrosRepository;
            _extrattaBizApiClient = extrattaBizApiClient;
            _usuarioRepository = usuarioRepository;
        }

        public void PushCreatedAccount(BizWebhookCreatedAccountRequest request)
        {
            throw new System.NotImplementedException();
        }

        public void PushTransactionOccurred(BizWebhookTransactionOcurredRequest request)
        {
            if (request.AccountId == null) throw new ArgumentNullException(nameof(request.AccountId));
            if (request.ProductId == null) throw new ArgumentNullException(nameof(request.ProductId));
            if (request.IssuerId == null) throw new ArgumentNullException(nameof(request.IssuerId));
            if (request.BranchOfficeId == null) throw new ArgumentNullException(nameof(request.BranchOfficeId));
            if (request.AffinityGroupId == null) throw new ArgumentNullException(nameof(request.AffinityGroupId));
            if (request.EventId == null) throw new ArgumentNullException(nameof(request.EventId));

            if (request.TransactionTypeCode.HasValue && request.TransactionTypeCode.Value == 2) return;

            var infos = ConsultarInformacoesPorDadosConta(request.AccountId.Value, request.ProductId.Value,
                request.IssuerId.Value, request.BranchOfficeId.Value, request.AffinityGroupId.Value);
            if (string.IsNullOrWhiteSpace(infos.Documento) || infos.IdentificadorCartao == null)
                throw new InvalidOperationException("Documento ou identificador do cartão não encontrados.");

            var dadosTransacao = _extrattaBizApiClient
                .GetTransacao(infos.IdentificadorCartao.Value, request.ProductId.Value, request.EventId.Value);
            if(dadosTransacao?.Value == null)
                throw new InvalidOperationException("Não foi possível recuperar os dados da transação.");

            var sucesso = dadosTransacao.Value.CodigoRespostaTransacao == "00";
            var valor = (dadosTransacao.Value.ValorLocal ?? 0M).ToString("N");
            var tipoTransacao = GetTipoTransacao(dadosTransacao.Value.Mcc, dadosTransacao.Value.PlanoVendas, sucesso);
            var mensagemErroTransacao = GetStatusTransacao(dadosTransacao.Value.CodigoRespostaTransacao);
            var cartao = dadosTransacao.Value.NumeroCartao.Substring(dadosTransacao.Value.NumeroCartao.Length - 4);

            string mensagem;

            if (sucesso)
            {
                mensagem = $"{tipoTransacao} no cartão {cartao} em {dadosTransacao.Value.DataHoraTransacao:dd/MM/yyyy HH:mm}";
                if (dadosTransacao.Value.ValorLocal.HasValue && dadosTransacao.Value.ValorLocal.Value != 0)
                    mensagem += $" no valor de R$ {valor}";
                if(!string.IsNullOrWhiteSpace(dadosTransacao.Value.NomeEstabelecimento)) 
                    mensagem += $" para o estabelecimento {dadosTransacao.Value.NomeEstabelecimento}";
                mensagem += ".";
            }
            //erro do sistema
            else if (dadosTransacao.Value.CodigoRespostaTransacao == "91" || dadosTransacao.Value.CodigoRespostaTransacao == "96")
            {
                mensagem = $"{tipoTransacao} para o cartão {cartao} em {dadosTransacao.Value.DataHoraTransacao:dd/MM/yyyy HH:mm}";
                if (dadosTransacao.Value.ValorLocal.HasValue && dadosTransacao.Value.ValorLocal.Value != 0)
                    mensagem += $" no valor de R$ {valor}";
                mensagem += $". {mensagemErroTransacao}. Por favor aguarde um momento e tente novamente. " +
                            $"Caso o erro persista, entre em contato com o suporte Extratta.";
            }
            //erro por outros motivos
            else
            {
                mensagem = $"{tipoTransacao} no cartão {cartao} em {dadosTransacao.Value.DataHoraTransacao:dd/MM/yyyy HH:mm}";
                if (dadosTransacao.Value.ValorLocal.HasValue && dadosTransacao.Value.ValorLocal.Value != 0)
                    mensagem += $" no valor de R$ {valor}";
                if(!string.IsNullOrWhiteSpace(dadosTransacao.Value.NomeEstabelecimento)) 
                    mensagem += $" para o estabelecimento {dadosTransacao.Value.NomeEstabelecimento}";
                mensagem += $". {mensagemErroTransacao}.";
            }

            var result = _pushService.EnviarPorDocumento(infos.Documento, "Extratta", mensagem);
            if (!result.IsValid) throw new Exception(result.Errors.FirstOrDefault()?.Message);
        }

        private ConsultarInformacoesPorDadosContaResponse ConsultarInformacoesPorDadosConta(int conta, int produto, int emissor, int sucursal, int grupoAfinidade)
        {
            var tokenAdministradora = ConfigurationManager.AppSettings["MS_TOKEN_ADMINISTRADORA"];
            //var token = _parametrosRepository.Where(c => c.NomeTabela == nameof(ADMINISTRADORA_MH_ID)).FirstOrDefault()?.ValorString;
            //if (token == null) throw new InvalidOperationException("Token não encontrado.");
            var cartoesService = new CartoesService(_cartoesServiceArgs, null, tokenAdministradora, CartoesService.AuditDocIntegracao, string.Empty);
            var resposta = cartoesService.ConsultarInformacoesPorDadosConta(conta, produto, emissor, sucursal, grupoAfinidade);
            if (resposta.Sucesso == false) throw new InvalidOperationException(resposta.Mensagem);
            return resposta;
        }

        /// 00 - TRANSACAO AUTORIZADA,
        /// 05 - TRANSACAO NEGADA,
        /// 06 - PROBLEMAS OCORRIDOS EM TRANSACOES ELETRONICAS,
        /// 12 - TRANSACAO INVALIDA,
        /// 14 - CARTAO INVALIDO,
        /// 51 - SALDO INSUFICIENTE,
        /// 55 - CODIGO SECRETO INCORRETO,
        /// 57 - TRANSACAO NAO PERMITIDA A ESSE CLIENTE,
        /// 59 - SUSPEITA DE FRAUDE,
        /// 62 - CARTAO BLOQUEADO,
        /// 75 - EXCEDIDO NUMERO DE TENTATIVAS DE SENHAS,
        /// 85 - NOT DECLINED,
        /// 91 - INSTITUICAO TEMPORARIAMENTE FORA DE OPERACAO,
        /// 96 - MAU FUNCIONAMENTO DO SISTEMA
        private string GetStatusTransacao(string code)
        {
            return code switch
            {
                //"00" => "Transação APROVADA",
                //"05" => "Transação NEGADA",
                "06" => "Problemas durante as transações eletrônicas",
                "12" => "Transação inválida",
                "14" => "Cartão inválido para a transação",
                "51" => "Saldo insuficiente para a transação",
                "55" => "Código secreto incorreto",
                "57" => "Transação não permitida",
                "59" => "Suspeita de fraude",
                "62" => "Cartão bloqueado",
                "75" => "Número de tentativas de senha excedido",
                "91" => "Instituição temporariamente fora de operação",
                "96" => "Mau funcionamento do sistema",
                _ => null
            };
        }

        private string GetTipoTransacao(int? mcc, int? planoVendas, bool sucesso)
        {
            if (mcc >= 6010 && mcc <= 6012 && planoVendas == 51)
                return "Saque " + (sucesso ? "APROVADO" : "NEGADO");

            if (planoVendas == 400)
                return "Carga " + (sucesso ? "APROVADA" : "NEGADA");

            if (planoVendas == 405)
                return "Estorno " + (sucesso ? "EFETUADO" : "NEGADO") + " para a transportadora";

            return "Transferência " + (sucesso ? "APROVADA" : "NEGADA");
        }
    }
}
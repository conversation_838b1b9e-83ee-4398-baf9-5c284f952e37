﻿using System;
using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Plano;
using ATS.Domain.Enum;
using ATS.Domain.Models.AtendimentoPortador;
using ATS.Domain.Models.Parametro;

namespace ATS.WS.Models.Webservice.Request.Empresa
{
    public class EmpresaCreateRequest
    {
        public int? IdEmpresa { get; set; }

        public string CNPJ { get; set; }

        public string RazaoSocial { get; set; }

        public string NomeFantasia { get; set; }

        public byte[] Logo { get; set; }

        public bool Ativo { get; set; } = true;

        public DateTime? DataInicioOperacao { get; set; }

        public string TipoCliente { get; set; }

        public decimal? PrevistoMovimentacaoFinanceiraFrete { get; set; }
   
        public decimal? PrevistoMovimentacaoFinanceiraVPO { get; set; }

        public int? PrevistoQuantidadeViagens { get; set; }

        public string ComercialConta { get; set; }

        public string TMS { get; set; }

        public List<PlanoEmpresaDto> PlanosEmpresa { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        public bool ObrigarNumeroFrota { get; set; } = false;

        public int TempoPortaAberta { get; set; } = 1;

        public bool ObrigarValorTerceiro { get; set; } = false;

        public int? TempoExecucaoServico { get; set; } = 1;

        public int? TempoParada { get; set; } 

        public DateTime? UltimaExecucao { get; set; }

        public bool PossuiMonitoramento { get; set; }

        public string EnderecoWSGatilho { get; set; }

        public string UsuarioGatilho { get; set; }

        public string SenhaGatilho { get; set; }

        public bool ControleFilaOfertaCargas { get; set; }

        public bool PermiteCredenciamento { get; set; }

        public bool Atualizaestabelecimento { get; set; } = true;

        public int IntervaloConsultaFretesConcorrente { get; set; } = 7;

        public bool HabilitarControleIMEIS { get; set; }

        public bool UtilizaAplicativoPersonalizado { get; set; }
        
        public bool ObrigaRoteirizacaoPedagioViagem { get; set; }

        public string EmailsCheckList { get; set; }

        public bool ValidacaoPagFrete { get; set; } = false;

        public bool PreCadastroCarga { get; set; } = true;

        public bool ListaPreCarga { get; set; } = false;

        public int DiasAntesVencDoc { get; set; }

        public int FreqVencDoc { get; set; }

        public int? TempoLimitePermanenciaCliente { get; set; }

        public string EmailSugestoes { get; set; }

        public bool EnviaEmailTempoExcedido { get; set; } = false;

        public bool EnviaEmailCadastroOcorrencia { get; set; } = false;

        public int? TipoNotificacaoCandidatura { get; set; }

        public bool MostrarValorFreteParaFrota { get; set; } = false;

        public DateTime? DataAtualizacao { get; set; }

        public bool ObrigarCPFReceber { get; set; } = false;

        public bool AutenticarCodigoBarraNF { get; set; } = false;

        public int? LimiteKmFreteCurto { get; set; }

        public string HoraEnvEmailMotNaoEmb { get; set; }

        public int? DiasBuscaMotNaoEmb { get; set; }

        public int? HorasValidadeChaveCadastroUsuario { get; set; }

        public string IdSistemaExterno { get; set; }

        public string ModeloEmailCancelamentoOc { get; set; }

        public string ModeloEmailGeracaoOc { get; set; }

        public string ModeloEmailRelatorioOc { get; set; }

        public string ModeloEmailProtocoloRejeitado { get; set; }

        public string ModeloEmailEventoRejeitado { get; set; }

        public int? CNTRC { get; set; }

        public int NumOrdemCarregamento { get; set; } = 0;

        public int? IdOCcancGR { get; set; }

        public int? IdOCcancCTe { get; set; }

        public int? IdOCcancViagem { get; set; }

        public int? IdOCRemocaoFila { get; set; }

        public bool SomentePainelGestaoEncerraNota { get; set; } = false;

        public string CEP { get; set; }

        public string Endereco { get; set; }

        public string Complemento { get; set; }

        public int? Numero { get; set; }

        public string Bairro { get; set; }

        public int IdCidade { get; set; }

        public int IdEstado { get; set; }

        public int IdPais { get; set; }

        public string Telefone { get; set; }

        public string Email { get; set; }

        public string Celular { get; set; }

        public bool PrioridadeCooperadoCargas { get; set; } = false;

        public int? MinutosPrioridade { get; set; }

        public double RaioCooperado { get; set; }

        public TimeSpan PeriodoIniEnvCheckInCooperado { get; set; }

        public TimeSpan PeriodoFimEnvCheckInCooperado { get; set; }

        public double RaioTerceiro { get; set; }

        public TimeSpan PeriodoIniEnvCheckInTerceiro { get; set; }

        public TimeSpan PeriodoFimEnvCheckInTerceiro { get; set; }
        
        public string PeriodoBloqueioEventosAberto { get; set; }


        public bool RoteirizarCarga { get; set; } = true;

        public decimal? TemperaturaInicial { get; set; }

        public decimal? TemperaturaFinal { get; set; }

        public int? AvisoSonoroFraco { get; set; }

        public int? AvisoSonoroModerado { get; set; }

        public int? AvisoSonoroForte { get; set; }

        public TimeSpan PeriodoExpiracaoChat { get; set; }

        public bool OcultarNovoCadastros { get; set; } = false;
        
        /// <inheritdoc cref="ATS.Domain.Entities.Empresa.HabilitarAgendamentoPagamentoFrete"/>
        public bool HabilitarAgendamentoPagamentoFrete { get; set; }

        public string EmailContatoGR { get; set; }

        public bool AlertaArea { get; set; }

        public decimal Acuracia { get; set; }

        public EValidaRaio ValidaRaio { get; set; } = EValidaRaio.NaoValidar;

        public int? RaioEntrega { get; set; }

        public int? IdOcorrenciaVelocidade { get; set; }

        public int? IdMotivoVelocidade { get; set; }

        public int? QuantidadeOcorrenciaVelocidade { get; set; }

        public string EmailNome { get; set; }

        public string EmailEndereco { get; set; }

        public decimal? EmailPorta { get; set; }

        public string EmailServidor { get; set; }

        public bool EmailSsl { get; set; }
        
        public string EmailUsuario { get; set; }

        public string EmailSenha { get; set; }

        public string UrlRastreamento { get; set; }

        public string LoginRastreamento { get; set; }

        public string SenhaRastreamento { get; set; }

        public int? IdTecnologia { get; set; }

        public bool UtilizaAgendamento { get; set; }

        public bool AprovaCheckListAutomaticamente { get; set; }

        public int? HorasRespostaAgendamento { get; set; }

        public int? HorasRealizaVistoria { get; set; }

        public byte[] EmailGeracaoOc { get; set; }

        public byte[] EmailCancelamentoOc { get; set; }

        public byte[] EmailRelatorioOc { get; set; }

        public byte[] EmailProtocoloRejeitado { get; set; }

        public byte[] EmailEventoRejeitado { get; set; }

        public ContaBancariaRequest ContaBancaria { get; set; }

        public List<EmpresaCreateModuloRequest> Modulos { get; set; }

        public List<EmpresaCreateOcorrenciaRequest> EmpresaOcorrencia { get; set; }

        public List<EmpresaCreateMotivoOcorrenciaRequest> EmpresaMotivoOcorrencia { get; set; }

        public List<EmpresaCreateImeisRequest> EmpresaIMEIs { get; set; }

        public int? BateriaFraca { get; set; }

        public int? BateriaForte { get; set; }

        public int? HoraCancelaAgendamento { get; set; }

        public int? HoraAlteraAgendamento { get; set; }

        public BloqueioGestorValorDto AlcadasBloqueioGestorValor { get; set; }

        public int? TipoNotificacaoLotesInativosMesa { get; set; }

        public bool? AtualizarStatusLoteAutomaticamente { get; set; }

        public int? DistanciaEntradaRaio { get; set; }

        public int? DistanciaSaidaRaio { get; set; }

        public int? DistanciaRaioGr { get; set; }


        public int? DiasInatividadeLote { get; set; }

        public bool? ApresentaPontoReferencia { get; set; }

        public string KeyGoogle { get; set; }

        public decimal? RaioPontoReferencia { get; set; }


        public bool UtilizaCheckinRastreamento { get; set; }  = false;

        public bool? MostrarHeaderArquivoCsv { get; set; }

        public string SeparadorArquivoCsv { get; set; }
        
        public bool UtilizaNovoRotograma { get; set; }

        public bool ValidaChaveMHBaixaEvento { get; set; }
        
        public int AdministradoraPlataforma { get; set; }
        
        public bool VinculoNovoCartaoPortador { get; set; }
        
        public bool VinculoNovoCartaoBloqueadoPortador { get; set; }
        public bool DesabilitaCacheRotas { get; set; } = false;
        public bool GerarCiotViagemInternacional { get; set; }
        public bool? MantemViagemAbertaAposCancelamentoDoUltimoEvento { get; set; }
        public bool? PermiteVincularCartaoComCpfFicticio { get; set; }
        public bool PermiteEdicaoDadosAdministrativosEmpresa { get; set; }
        public decimal ValorSaldoContaFreteMinimoNotificacaoEmail { get; set; }
        public decimal ValorSaldoContaPixMinimoNotificacaoEmail { get; set; }
        public bool? UtilizaRelatoriosOfx { get; set; }
        public bool? MantemViagemAbertaAposBaixaDoUltimoEvento { get; set; }
        public bool? RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe { get; set; }
        public bool? NaoBaixarParcelasDeposito { get; set; }
        public bool? UtilizaRoteirizacaoPorPolyline { get; set; }
        
        #region Parametros Tag Extratta
        public bool? TagExtrattaUtilizaTaxaPedagio { get; set; }
        public bool? TagExtrattaPermiteUtilizarFornecedor { get; set; }
        public bool? TagExtrattaProvisionarValor { get; set; }
        public bool? TagExtrattaProvisionarTaxa { get; set; }
        public decimal? TagExtrattaTaxaVpo { get; set; }
        public decimal? TagExtrattaValorTag { get; set; }
        public decimal? TagExtrattaValorMensalidade { get; set; }
        public decimal? TagExtrattaFaixaToleranciaNotificacaoEmail { get; set; }
        public bool? TagExtrattaProvisionarTaxaPedagioWebhook { get; set; }
        public bool? TagExtrattaProvisionarValorPedagioWebhook { get; set; }
        public decimal? TagExtrattaSaldoMinimoContaFreteWebhook { get; set; }
        public bool? TagExtrattaBloquearTagUnitariaWebhook { get; set; }
        public bool? TagExtrattaBloquearTagLoteWebhook { get; set; }
        public decimal? TagExtrattaValorSubstituicao { get; set; }
        public decimal? TagExtrattaValorRecargaConta { get; set; }
        public bool? TagExtrattaEstornarValorPedagioWebhook { get; set; }
        #endregion
        
        #region Parâmetros de Meio Homologado

        /// <summary>
        /// Indica que a empresa deve ser integrada na plataforma de micro serviços, gerar o token de integração para a aplicação com nome definido no Web.Config[TITULO_SISTEMA] e armazena-lo no registro da empresa
        /// </summary>
        public bool HabilitarMeioHomologado { get; set; }

        public int? IdLayoutCartao { get; set; }
        public ETipoCarregamentoFrete? TipoCarregamentoFrete { get; set; }
        public string PercentualTransferenciaMotoristaStr { get; set; }
        public int? DiasExpiracaoSaldoPedagio { get; set; }
        public string CodigoAcessoViaFacil { get; set; }
        public string LoginAcessoViaFacil { get; set; }
        public string SenhaAcessoViaFacil { get; set; }
        public int? DiasDataExpiracaoPedagioViaFacil { get; set; }
        public bool FalhaComunicacaoPedagio { get; set; }
        public string InformacoesTransferenciaBancaria { get; set; }
        public string LoginAcessoMoveMais { get; set; }
        public string SenhaAcessoMoveMais { get; set; }
        public string LoginAcessoConectCar { get; set; }
        public string SenhaAcessoConectCar { get; set; }
        public decimal ConectCarExtrattaTaxaVpo { get; set; }
        public bool UtilizaCredenciaisExtrattaCompraConectCar { get; set; }
        public bool UtilizaCredenciaisExtrattaCompraViaFacil { get; set; }
        public bool UtilizaCredenciaisExtrattaCompraMoveMais { get; set; }
        public bool UtilizaCredenciaisExtrattaCompraTaggyEdenred { get; set; }
        public decimal? MoveMaisExtrattaTaxaVpo { get; set; }
        public decimal? ViaFacilExtrattaTaxaVpo { get; set; }
        public bool HubViaFacilProvisionarTaxa { get; set; }
        public bool HubViaFacilProvisionarValor { get; set; }
        public bool HubMoveMaisProvisionarValor { get; set; }
        public bool HubConectCarProvisionarTaxa { get; set; }
        public bool HubConectCarProvisionarValor { get; set; }
        public bool HubMoveMaisProvisionarTaxa { get; set; }
        public bool HubTaggyEdenredProvisionarValor { get; set; }
        public bool HubTaggyEdenredProvisionarTaxa { get; set; }
        public decimal? TaggyEdenredExtrattaTaxaVpo { get; set; }
        public string LoginAcessoTaggyEdenred { get; set; }
        public string SenhaAcessoTaggyEdenred { get; set; }
        public int? CodigoParceiroTaggyEdenred { get; set; }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio AcaoSaldoResidualNovoCreditoCartaoPedagio { get; set; }

        /// <summary>
        /// Código do produto padrão para operações de frete da empresa. Representa o produto da plataforma de cartões.
        /// </summary>
        public int? IdProdutoCartaoFretePadrao { get; set; }

        public bool AutorizaEstabelecimentosRedeJSL { get; set; } = true;
        public int? HorasExpiracaoCreditoPedagio { get; set; }
        public string EmailsAlertaCiotAgregado { get; set; }
        public int? DiasCancelamentoViagem { get; set; }

        public bool? ReemiteCiotPadraoAlteracaoViagem { get; set; }
        public bool PedagioTag { get; set; }
        
        public bool PermiteResgate { get; set; }

        public bool SenhaResgate { get; set; }
        public bool AcessarExtrato { get; set; }
        public bool ListarCnpjGridDespesasViagem { get; set; }
        public string Token { get; set; }
        public bool ParcelaViagemAberta { get; set; }
        public bool IntegrarComoUsuario { get; set; }
        public bool BaixarParcelaBloqueado { get; set; } = true;
        public bool BloquearNovaViagem { get; set; } 
        public bool? DefaultIntegracaoTipoRodagemDupla { get; set; }
        public bool? BloqueiaCargaAvulsaDuplicada { get; set; }
        public string CodOfx { get; set; }

        #endregion

        #region Regras de Frete

        public bool AgrupaProtocoloMesmoEvento { get; set; }
        public bool ValidaChaveBaixaEvento { get; set; }
        public string PagamentoFreteToleranciaPesoChegadaMaisStr { get; set; }
        public string PagamentoFreteToleranciaPesoChegadaMenosStr { get; set; }
        public string TempoValidadeChaveStr { get; set; }
        public string TempoValidadeChaveEstabelecimentoStr { get; set; }
        public string DiasParaBloquearPagtoStr { get; set; }
        public string PrazoParaInformarDocumentosStr { get; set; }
        public string WebHookProtocoloEndPoint { get; set; }
        public string WebHookProtocoloHeaders { get; set; }
        public string EmailCartaFrete { get; set; }
        public bool NaoValidarProtocoloRecebidoEmpresa { get; set; }
        public bool CancelaViagemComProtocolo { get; set; }
        public bool? UtilizaValidacaoPorPerfilNoPagamentoDeFrete { get; set; }

        #endregion
        
        #region Regras de Frete

        public bool? RealizaTriagemEstabelecimentoInterno { get; set; }
        
        #endregion
        
        #region parametros do portal de atendimento
        public string TokenMicroServicoCentralAtendimento { get; set; }
        public PermissoesEmpresaAtendimentoPortador PermissoesAtendimentoCartao { get; set; }
        public bool RegistrarValePedagio { get; set; } = true;

        #endregion
        
        #region parametros CNAB
        public string TipoInscricaoEmpresa { get; set; }
        public string CodigoBancoCompensacao { get; set; }
        public string NumeroInscricaoEmpresa { get; set; }
        public string CodigoConvenioBanco { get; set; }
        public string AgenciaMantenedora { get; set; }
        public string DigitoVerificaConta { get; set; }
        public string DigitoVerificaContaAgConta { get; set; }
        #endregion

        #region Veloe
        public string CnpjEmbarcadorVeloe { get; set; }
        public string TenantIdVeloe { get; set; }
        public string UserNameVeloe { get; set; }
        public string PasswordVeloe { get; set; }
        public string BasicAuthVeloe { get; set; }
        public decimal? VeloeExtrattaTaxaVpo { get; set; }
        public bool UtilizaCredenciaisExtrattaCompraVeloe { get; set; }
        public bool HubVeloeProvisionarTaxa { get; set; }
        public bool HubVeloeProvisionarValor { get; set; }

        #endregion
        
        #region ExtrattaPay
        
        public bool AprovacaoGestorCargaAvulsaIntegracao { get; set; }
        public bool AprovacaoGestorCargaAvulsaUnitario { get; set; }
        public bool AprovacaoGestorCargaAvulsaLote { get; set; }

        #endregion

        /// <summary>
        /// <inheritdoc cref="ATS.Domain.Entities.Empresa.WebHookEndpoint"/>
        /// </summary>
        public string WebHookEndpoint { get; set; }

        /// <summary>
        /// <inheritdoc cref="ATS.Domain.Entities.Empresa.WebHookHeaders"/>
        /// </summary>
        public string WebHookHeaders { get; set; }
    }

    public class EmpresaEditRequest : EmpresaCreateRequest
    {
        public string LogoBase64 { get; set; }

        public string EmailCancelamentoOcBase64 { get; set; }

        public string EmailGeracaoOcBase64 { get; set; }

        public string EmailRelatorioOcBase64 { get; set; }

        public string EmailProtocoloRejeitadoBase64 { get; set; }

        public string EmailEventoRejeitadoBase64 { get; set; }
    }

}

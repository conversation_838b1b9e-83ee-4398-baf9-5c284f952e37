﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class MensagemGrupoDestinatarioMap : EntityTypeConfiguration<MensagemGrupoDestinatario>
    {
        public MensagemGrupoDestinatarioMap()
        {

            ToTable("MENSAGEM_GRUPO_DESTINATARIO");

            HasKey(x => x.IdGrupoDestinatario);

            Property(x => x.IdGrupoDestinatario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(x => x.UsuarioMembroGrupo)
                .WithMany(x => x.UsuariosMembrosGrupoUsuario)
                .HasForeignKey(x => x.IdUsuarioDestinatario);

            HasRequired(x => x.MensagemGrupoUsuario)
                .WithMany(x => x.MensagemGrupoDestinatario)
                .HasForeignKey(x => x.IdGrupoUsuario);
        }
    }
}

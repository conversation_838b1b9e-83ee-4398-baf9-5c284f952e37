﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Grid;
using Cliente = ATS.Domain.Entities.Cliente;
using System.Text;
using ATS.Domain.Helpers;
using System.Text.RegularExpressions;
using ATS.Application.Interface;
using ATS.WS.Models;

namespace ATS.WS.Services
{
    public class SrvCliente : SrvBase
    {
        private readonly IClienteApp _clienteApp;
        private readonly SrvClienteProdutoEspecie _srvClienteProdutoEspecie;
        private readonly SrvUtils _srvUtils;
        private readonly IEmpresaApp _empresaApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IEstadoApp _estadoApp;

        public SrvCliente(IClienteApp clienteApp, SrvClienteProdutoEspecie srvClienteProdutoEspecie, SrvUtils srvUtils, IEmpresaApp empresaApp, ICidadeApp cidadeApp, IEstadoApp estadoApp)
        {
            _clienteApp = clienteApp;
            _srvClienteProdutoEspecie = srvClienteProdutoEspecie;
            _srvUtils = srvUtils;
            _empresaApp = empresaApp;
            _cidadeApp = cidadeApp;
            _estadoApp = estadoApp;
        }

        public Retorno<ClienteModel> Integrar(ClienteRequestModel @params)
        {
            try
            {                
                int? idEmpresa = null;
                if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa))
                    idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (!idEmpresa.HasValue)
                    return new Retorno<ClienteModel>(false, @"Empresa inválida ou não cadastrada na base", null);

                var cliente = GetCliente(@params, (int) idEmpresa);

                if (cliente.IdPais <= 0)
                    return new Retorno<ClienteModel>(false, $"Não foi possível identificar o país pelo código BACEN número: {@params.BACENPais}", null);
                if (cliente.IdEstado <= 0)
                    return new Retorno<ClienteModel>(false, $"Não foi possível identificar o estado pelo código IBGE número: {@params.IBGEEstado}", null);
                if (cliente.IdCidade <= 0)
                    return new Retorno<ClienteModel>(false, $"Não foi possível identificar a cidade pelo código IBGE número: {@params.IBGECidade}", null);
                if (string.IsNullOrWhiteSpace(@params.CEP))
                    return new Retorno<ClienteModel>(false, "CEP é obrigatório", null);
                cliente.IdEmpresa = idEmpresa.Value;

                /*if ((@params.Latitude == null || @params.Latitude == 0) ||
                    (@params.Longitude == null || @params.Longitude == 0))
                {
                    var resultado = SetEnderecoPrincipalDoCliente(new ClienteEnderecoModel
                    {
                        Numero = cliente.Numero,
                        Endereco = cliente.Endereco,
                        Bairro = cliente.Bairro,
                        IBGECidade = @params.ClienteEnderecos.FirstOrDefault()?.IBGECidade ?? 0,
                        IBGEEstado = @params.ClienteEnderecos.FirstOrDefault()?.IBGEEstado ?? 0,
                        BACENPais = @params.ClienteEnderecos.FirstOrDefault()?.BACENPais ?? 0,
                        CEP = cliente.CEP,
                        Latitude = 0,
                        Longitude = 0
                    });

                    cliente.Latitude = resultado.Latitude;
                    cliente.Longitude = resultado.Longitude;
                }*/

                var validationResult = (cliente.IdCliente == 0) ? _clienteApp.Add(cliente) : _clienteApp.Update(cliente);
                
                if (!validationResult.IsValid)
                    return new Retorno<ClienteModel>(false, validationResult.ToString(), null);

                var listaEnderecos = new List<ClienteEnderecoResponseModel>();

                if (cliente.ClienteEnderecos != null)
                    foreach (var endereco in cliente.ClienteEnderecos)
                    {
                        listaEnderecos.Add(new ClienteEnderecoResponseModel
                        {
                            BACENPais = endereco.Pais?.BACEN ?? 0,
                            IBGEEstado = endereco.Estado?.IBGE ?? 0,
                            IBGECidade = endereco.Cidade?.IBGE ?? 0,
                            CEP = endereco.CEP,
                            Endereco = endereco.Endereco,
                            Complemento = endereco.Complemento,
                            Numero = endereco.Numero,
                            Bairro = endereco.Bairro
                        });
                    }

                var clienteModelRetorno = Mapper.Map<Cliente, ClienteModel>(cliente);
                clienteModelRetorno.ClienteEnderecos = listaEnderecos;

                clienteModelRetorno.ListaClienteProdutoEspecie = _srvClienteProdutoEspecie.Integrar(cliente.IdCliente, @params.ListaProdutoEspecie);

                return new Retorno<ClienteModel>(true, clienteModelRetorno);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<ClienteModel>($"{nameof(Integrar)} >> {e.Message}");
            }
        }

        public void Atualizar(ClienteCadastrarAtualizarCls p)
        {
            if (!p.idCliente.HasValue)
                throw new Exception("Nennhum id cliente informado");
            
            var dbRow = _clienteApp.Get(p.idCliente.Value);
            
            if (dbRow == null)
                throw new Exception($"Nennhum cliente encontrado para o id {p.idCliente.Value}");

            var err = new StringBuilder();

            // Realiza todas validações necessárias
            if (!p.idEmpresa.HasValue)
                err.AppendLine("Empresa é obrigatória");

            if (string.IsNullOrWhiteSpace(p.razaoSocial))
                err.AppendLine("Razão social é obrigatório");

            if (string.IsNullOrWhiteSpace(p.nomeFantasia))
                err.AppendLine("Nome fantasia é obrigatório");

            if (string.IsNullOrWhiteSpace(p.cpfcnpj))
                err.AppendLine("CPF/CNPJ é obrigatório");

            if (string.IsNullOrWhiteSpace(p.cep))
                err.AppendLine("CEP é obrigatório");

            if (string.IsNullOrWhiteSpace(p.endereco))
                err.AppendLine("Endereço é obrigatório");

            if (string.IsNullOrWhiteSpace(p.enderecoBairro))
                err.AppendLine("Endereço é obrigatório");

            if (!p.enderecoIdPais.HasValue)
                err.AppendLine("País é obrigatório");

            if (!p.enderecoIdEstado.HasValue)
                err.AppendLine("Estado é obrigatório");

            if (!p.enderecoIdCidade.HasValue)
                err.AppendLine("Cidade é obrigatório");

            if (string.IsNullOrWhiteSpace(p.ie) && p.tipoPessoa == Domain.Enum.ETipoPessoa.Juridica)
                err.AppendLine("IE é obrigatório");

            if (string.IsNullOrWhiteSpace(p.rg) && p.tipoPessoa == Domain.Enum.ETipoPessoa.Fisica)
                err.AppendLine("RG é obrigatório");

            if (!p.ativarParametrosGestao)
            {
                p.obrigarCPFReceber = false;
                p.autenticarCodigoBarraNF = false;
            }

            var cliByCpfCnp = _clienteApp.GetIdPorCpfCnpj(p.cpfcnpj, p.idEmpresa.Value);
            if (cliByCpfCnp > 0 && cliByCpfCnp != p.idCliente)
                throw new Exception($"O CPF {(p.cpfcnpj.Length == 11 ? p.cpfcnpj.ToCPFFormato() : p.cpfcnpj.ToCNPJFormato())} já está sendo utilizado por outro cliente!");

            // Finaliza salvando os dados na base
            dbRow.TipoCliente = p.tipoCliente;
            dbRow.RazaoSocial = p.razaoSocial;
            dbRow.NomeFantasia = p.nomeFantasia;
            dbRow.CNPJCPF = p.cpfcnpj;
            dbRow.IE = p.ie;
            dbRow.Celular = p.celular;
            dbRow.Email = p.email;
            dbRow.RG = p.rg;
            dbRow.OrgaoExpedidorRG = p.orgaoExpedidor;

            if (p.ativarParametrosGestao)
            {
                dbRow.AutenticarCodigoBarraNF = p.autenticarCodigoBarraNF;
                dbRow.ObrigarCPFReceber = p.obrigarCPFReceber;
            }


            dbRow.CEP = p.cep;
            dbRow.Bairro = p.enderecoBairro;

            if (!string.IsNullOrWhiteSpace(p.enderecoNumero))
                dbRow.Numero = p.enderecoNumero != null &&
                    !string.IsNullOrWhiteSpace(Regex.Split(p.enderecoNumero, @"[^\d]")[0])
                    ? Convert.ToInt32(Regex.Split(p.enderecoNumero, @"[^\d]")[0]) : 0;

            dbRow.Latitude = p.latitude;
            dbRow.Longitude = p.longitude;
            dbRow.IdCidade = p.enderecoIdCidade.Value;
            dbRow.IdEstado = p.enderecoIdEstado.Value;
            dbRow.IdPais = p.enderecoIdPais.Value;
            dbRow.AtivarParametrosGestao = p.ativarParametrosGestao;
            dbRow.Endereco = p.endereco;
            dbRow.Longitude = p.longitude;
            dbRow.Complemento = p.enderecoComplemento;
            dbRow.PontoReferencia = p.PontoReferencia;
            if (p.limparLogo)
                dbRow.Logo = null;

            if (!string.IsNullOrWhiteSpace(p.imagemLogoB64))
                dbRow.Logo = Convert.FromBase64String(p.imagemLogoB64);

            var upd = _clienteApp.Update(dbRow);
            if (!upd.IsValid)
                throw new Exception(upd.ToFormatedMessage());
        }

        public Retorno<ClienteModel> ConsutarCliente(ClienteConsultaRequestModel @params)
        {
            try
            {
                var empresaParams = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (empresaParams.HasValue)
                {
                    var clienteId = _clienteApp.GetIdPorCpfCnpj(@params.CpfcnpjCliente, empresaParams.Value);
                    
                    if (clienteId != null)
                    {
                        var cliente = _clienteApp.Get(clienteId.Value);
                        if (cliente != null)
                            return new Retorno<ClienteModel>(true, Mapper.Map<Cliente, ClienteModel>(cliente));
                    }
                    else
                        return new Retorno<ClienteModel>(false, "Cliente inexistente", null);
                }

                return new Retorno<ClienteModel>(false, "Empresa inexistente", null);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<ClienteModel>($"{nameof(ConsutarCliente)} >> {e.Message}");
            }
        }

        public void Cadastrar(ClienteCadastrarAtualizarCls p)
        {
            var err = new StringBuilder();

            // Realiza todas validações necessárias
            if (!p.idEmpresa.HasValue)
                err.AppendLine("Empresa é obrigatória");

            if (string.IsNullOrWhiteSpace(p.razaoSocial))
                err.AppendLine("Razão social é obrigatório");

            if (string.IsNullOrWhiteSpace(p.nomeFantasia))
                err.AppendLine("Nome fantasia é obrigatório");

            if (string.IsNullOrWhiteSpace(p.cpfcnpj))
                err.AppendLine("CPF/CNPJ é obrigatório");

            if (string.IsNullOrWhiteSpace(p.cep))
                err.AppendLine("CEP é obrigatório");

            if (string.IsNullOrWhiteSpace(p.endereco))
                err.AppendLine("Endereço é obrigatório");

            if (string.IsNullOrWhiteSpace(p.enderecoBairro))
                err.AppendLine("Endereço é obrigatório");

            if (!p.enderecoIdPais.HasValue)
                err.AppendLine("País é obrigatório");

            if (!p.enderecoIdEstado.HasValue)
                err.AppendLine("Estado é obrigatório");

            if (!p.enderecoIdCidade.HasValue)
                err.AppendLine("Cidade é obrigatório");

            if (string.IsNullOrWhiteSpace(p.ie) && p.tipoPessoa == Domain.Enum.ETipoPessoa.Juridica)
                err.AppendLine("IE é obrigatório");

            if (string.IsNullOrWhiteSpace(p.rg) && p.tipoPessoa == Domain.Enum.ETipoPessoa.Fisica)
                err.AppendLine("RG é obrigatório");

            if (!p.ativarParametrosGestao)
            {
                p.obrigarCPFReceber = false;
                p.autenticarCodigoBarraNF = false;
            }

            var cliByCpfCnp = _clienteApp.GetIdPorCpfCnpj(p.cpfcnpj, p.idEmpresa.Value);
            if (cliByCpfCnp > 0)
                throw new Exception($"O CPF {(p.cpfcnpj.Length == 11 ? p.cpfcnpj.ToCPFFormato() : p.cpfcnpj.ToCNPJFormato())} já está sendo utilizado por outro cliente!");

            // Finaliza salvando os dados na base
            var nCli = new Cliente
            {
                IdEmpresa = p.idEmpresa.Value,
                TipoCliente = p.tipoCliente,
                TipoPessoa = p.tipoPessoa,
                Logo = p.imagemLogoB64 != null ? Convert.FromBase64String(p.imagemLogoB64) : null,
                RazaoSocial = p.razaoSocial,
                NomeFantasia = p.nomeFantasia,
                CNPJCPF = p.cpfcnpj,
                IE = p.ie,
                Celular = p.celular,
                Email = p.email,
                RG = p.rg,
                OrgaoExpedidorRG = p.orgaoExpedidor,
                Ativo = true,
                AutenticarCodigoBarraNF = p.autenticarCodigoBarraNF,
                ObrigarCPFReceber = p.obrigarCPFReceber,
                CEP = p.cep,
                Bairro = p.enderecoBairro,
                Complemento = p.enderecoComplemento
            };

            if (!string.IsNullOrWhiteSpace(p.enderecoNumero))
                nCli.Numero = p.enderecoNumero != null
                            && !string.IsNullOrWhiteSpace(Regex.Split(p.enderecoNumero, @"[^\d]")[0]) ? Convert.ToInt32(Regex.Split(p.enderecoNumero, @"[^\d]")[0]) : 0;

            nCli.Latitude = p.latitude;
            nCli.Longitude = p.longitude;
            nCli.IdCidade = p.enderecoIdCidade.Value;
            nCli.IdEstado = p.enderecoIdEstado.Value;
            nCli.IdPais = p.enderecoIdPais.Value;
            nCli.AtivarParametrosGestao = p.ativarParametrosGestao;
            nCli.Endereco = p.endereco;
            nCli.RaioLocalizacao = 0;
            nCli.PontoReferencia = p.PontoReferencia;

            var add = _clienteApp.Add(nCli);
            if (!add.IsValid)
                throw new Exception(add.ToFormatedMessage());
        }

        public byte[] GerarRelatorioGridClientes(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string extensao)
        {
            return _clienteApp.GerarRelatorioGridClientes(idEmpresa, order, filters, GetLogo(idEmpresa), extensao);
        }

        public ClienteEndereco SetEnderecoPrincipalDoCliente(ClienteEnderecoModel @params)
        {
            var cliente = new ClienteEndereco();

            @params.BACENPais = @params.BACENPais <= 0 ? 1058 : @params.BACENPais;

            var idRetPais = _srvUtils.GetIdPaisPorBACEN(@params.BACENPais);
            
            if (idRetPais == 0 || idRetPais == null)
            {
                idRetPais = _srvUtils.GetIdPaisPorBACEN(1058);
            }
            
            cliente.IdPais = idRetPais.GetValueOrDefault();
            
            var idRetEstado = _srvUtils.GetIdEstadoPorIBGE(@params.IBGEEstado);
            if (idRetEstado.HasValue && idRetEstado > 0)
                cliente.IdEstado = idRetEstado.GetValueOrDefault();

            var idRetCidade = _srvUtils.GetIdCidadePorIBGE(@params.IBGECidade);
            
            if (idRetCidade.HasValue && idRetCidade > 0)
            {
                cliente.IdCidade = idRetCidade.GetValueOrDefault();
                var cidade = _cidadeApp.Get(cliente.IdCidade);
                
                if (cliente.IdEstado == 0 && cidade.IdEstado > 0)
                {
                    cliente.IdEstado = cidade.IdEstado;
                    var estado = _estadoApp.Get(cliente.IdEstado);

                    if (cliente.IdPais == 0 && estado.IdPais > 0)
                        cliente.IdPais = estado.IdPais;
                }
            }

            cliente.Latitude = @params.Latitude;
            cliente.Longitude = @params.Longitude;

            /*if ((@params.Latitude == null || @params.Latitude == 0) ||
                (@params.Longitude == null || @params.Longitude == 0))
            {
                decimal latitude;
                decimal longitude;
                
                _locationHelper.SetCoordinatesObject(@params.Numero ?? 0, @params.Endereco, @params.Bairro, @params.IBGECidade, @params.IBGEEstado, @params.BACENPais, out latitude, out longitude);
                
                cliente.Longitude = longitude;
                cliente.Latitude = latitude;
            }*/

            cliente.Numero = @params.Numero;
            cliente.CEP = @params.CEP;
            cliente.Bairro = @params.Bairro;
            cliente.Complemento = @params.Complemento;
            cliente.Endereco = @params.Endereco;

            return cliente;
        }

        private List<ClienteEndereco> GetClienteEnderecos(IEnumerable<ClienteEnderecoModel> enderecos)
        {
            var clienteEnderecos = enderecos
                .Select(SetEnderecoPrincipalDoCliente)
                .ToList();

            return clienteEnderecos;
        }
        
        private Cliente GetCliente(ClienteRequestModel @params, int idEmpresa)
        {
            Cliente cliente;

            var idClientePorCpfCnpj = _clienteApp.GetIdPorCpfCnpj(@params.CNPJCPF, idEmpresa);

            if (!@params.IdCliente.HasValue && idClientePorCpfCnpj == null)
            {

                cliente = Mapper.Map<ClienteRequestModel, Cliente>(@params);
                cliente.TipoCliente = Domain.Enum.ETipoCliente.Origem;
            }
            else
            {
                cliente = _clienteApp.GetAllChilds(idClientePorCpfCnpj ?? 0);
                ReflectionHelper.CopyProperties(@params, cliente);
                cliente.IdCliente = idClientePorCpfCnpj ?? 0;
            }

            if (cliente == null) throw new Exception("Cliente não encontrado.");

            var clienteEnderecoModel = new ClienteEnderecoModel();

            if (@params.ClienteEnderecos == null || !@params.ClienteEnderecos.Any())
            {
                if (@params.ClienteEnderecos == null)
                    @params.ClienteEnderecos = new List<ClienteEnderecoModel>();
                
                clienteEnderecoModel.IBGECidade = @params.IBGECidade;
                clienteEnderecoModel.IBGEEstado = @params.IBGEEstado;
                clienteEnderecoModel.BACENPais = @params.BACENPais;
                clienteEnderecoModel.Bairro = @params.Bairro;
                clienteEnderecoModel.Endereco = @params.Endereco;
                clienteEnderecoModel.Complemento = @params.Complemento;
                clienteEnderecoModel.Longitude = @params.Longitude;
                clienteEnderecoModel.Latitude = @params.Latitude;
                clienteEnderecoModel.Numero = @params.Numero;
                clienteEnderecoModel.CEP = @params.CEP;
                

                @params.ClienteEnderecos.Add(clienteEnderecoModel);
            }
            else
            {
                clienteEnderecoModel = @params.ClienteEnderecos.FirstOrDefault();
            }

            var clienteEndereco = SetEnderecoPrincipalDoCliente(clienteEnderecoModel);

            cliente.IdCidade = clienteEndereco.IdCidade;
            cliente.IdEstado = clienteEndereco.IdEstado;
            cliente.Longitude = clienteEndereco.Longitude;
            cliente.Numero = clienteEndereco.Numero;
            cliente.CEP = clienteEndereco.CEP;
            cliente.Bairro = clienteEndereco.Bairro;
            cliente.IdPais = clienteEndereco.IdPais;
            cliente.Complemento = clienteEndereco.Complemento;
            cliente.Endereco = clienteEndereco.Endereco;

            if (cliente.ClienteEnderecos == null)
                cliente.ClienteEnderecos = new List<ClienteEndereco>();

            cliente.ClienteEnderecos = GetClienteEnderecos(@params.ClienteEnderecos);

            return cliente;
        }
    }
}

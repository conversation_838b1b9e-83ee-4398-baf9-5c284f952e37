﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.CidadeModels;

namespace ATS.Application.Application
{
    public class CidadeApp : AppBase, ICidadeApp
    {
        private readonly ICidadeService _cidadeService;

        public CidadeApp(ICidadeService cidadeService)
        {
            _cidadeService = cidadeService;
        }

        /// <summary>
        /// Retorna o objeto de cidade a partir do codigo do IBGE
        /// </summary>
        /// <param name="nIBGE">IBGE</param>
        /// <returns></returns>
        public Cidade GetPorIBGE(int nIBGE)
        {
            return _cidadeService.GetCidadeFromIBGE(nIBGE);
        }

        /// <summary>
        /// Método utilizado para listar as cidade
        /// </summary>
        /// <param name="nome">Nome da cidade</param>
        /// <returns>IQueryable de CidadeGrid</returns>
        public IQueryable<CidadeGrid> Consultar(string nome = null)
        {
            return _cidadeService.Consultar(nome);
        }

        /// <summary>
        /// Método utilizado para incluir Cidade.
        /// </summary>
        /// <param name="entity">Entidade de Cidade</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Cidade entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _cidadeService.Add(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para alterar Cidade.
        /// </summary>
        /// <param name="entity">Entidade de Cidade</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Cidade entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _cidadeService.Update(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para buscar Cidade.
        /// </summary>
        /// <param name="id">Id de Cidade</param>
        /// <returns>Entidade Cidade</returns>
        public Cidade Get(int id)
        {
            return _cidadeService.Get(id);
        }

        /// <summary>
        /// Retorna o objeto de Cidade a partir do código IBGE da Cidade
        /// </summary>
        /// <param name="nIBGE">Código do IBGE</param>
        /// <returns></returns>
        public Cidade GetCidadeByIBGE(int nIBGE)
        {
            return _cidadeService.GetCidadeFromIBGE(nIBGE);
        }

        public int? GetIdCidade(int nIBGE)
        {
            return _cidadeService.GetIdCidade(nIBGE);
        }

        /// <summary>
        /// Retorna a lista de cidades a partir do código do estado
        /// </summary>
        /// <param name="idEstado"></param>
        /// <returns></returns>
        public IQueryable<Cidade> GetListaCidade(int idEstado)
        {
            return _cidadeService.GetCidades(idEstado);
        }

        /// <summary>
        /// Retorna a lista de cidades atualizadas a partir da data
        /// </summary>
        /// <param name="uf">Sigla do estado</param>
        /// <param name="dataBase">Data base para filtro</param>
        /// <returns></returns>
        public IQueryable<Cidade> GetCidadesAtualizadas(string uf, DateTime dataBase)
        {
            return _cidadeService.GetCidadesAtualizadas(uf, dataBase);
        }

        /// <summary>
        /// Retorna todas as cidades ativas
        /// </summary>
        /// <returns>IQueryable de cidade</returns>
        public IQueryable<Cidade> All()
        {
            return _cidadeService.All();
        }

        /// <summary>
        /// Retorna todas as cidades parecidas com #
        /// </summary>
        /// <returns>IQueryable de cidade</returns>
        public List<Cidade> WhereNomeLike(string nome)
        {
            return _cidadeService.WhereNomeLike(nome);
        }

        public Cidade WhereNomeIs(string nome) {
            return _cidadeService.WhereNomeIs(nome);
        }

        /// <summary>
        /// Retorna a localização (Latitude e Longitude) da cidade
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <returns></returns>
        public LocalizacaoModel GetLocalizacao(int idCidade, EOrigemConsumoServicoExterno origemConsumoServicoExterno)
        {
            return _cidadeService.GetLocalizacao(idCidade, origemConsumoServicoExterno);
        }

        /// <summary>
        /// Retorna o código da cidade a partir do estado e nome
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="nome"></param>
        /// <returns></returns>
        public int? GetIdCidadePorNome(int idEstado, string nome)
        {
            return _cidadeService.GetIdCidadePorNome(idEstado, nome);
        }

        /// <summary>
        /// Inativa uma cidade na base de dados
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idCidade)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _cidadeService.Inativar(idCidade);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa uma cidade
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idCidade)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _cidadeService.Reativar(idCidade);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public List<Cidade> ConsultarPaginadas(int? codigoIBGEEstado, int? ibge, DateTime? dataBase, int? take, int? skip)
        {
            return _cidadeService.ConsultarPaginadas(codigoIBGEEstado, ibge, dataBase, take, skip);
        }

        public object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _cidadeService.ConsultarGrid(take, page, order, filters);
        }

        public Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude)
        {
            return _cidadeService.GetCidadeMaisProxima(latitude, longitude);
        }

        public List<Cidade> GetTodos()
        {
            return _cidadeService.GetTodos();
        }

        public CidadeDetalhesResponse GetDetalhes(int idcidade)
        {
            return _cidadeService.GetDetalhes(idcidade);
        }

        public int GetIdPorIBGE(int ibge)
        {
            return _cidadeService.GetIdPorIBGE(ibge);
        }

        public IQueryable<Cidade> GetQueryByIBGE(int ibge)
        {
            return _cidadeService.GetQueryByIBGE(ibge);
        }
        
        /// <summary>
        /// Retorna as informações da cidade com os objetos filhos inclusos
        /// </summary>
        /// <param name="id">Código da cidade</param>
        /// <returns></returns>
        public Cidade GetWithAllChilds(int id)
        {
            return _cidadeService.GetWithAllChilds(id);
        }
        
        public List<int> GetIbgeCidade(List<CidadeIbgeRequestModel> cidadesIbgeRequestModel,bool errorCidadeNotFound = false)
        {
            return _cidadeService.GetIbgeCidade(cidadesIbgeRequestModel,errorCidadeNotFound);
        }

        public bool ValidarIbgeCadastrado(int ibge)
        {
            return _cidadeService.ValidarIbgeCadastrado(ibge);
        }
    }
}
﻿using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using System;
using System.Web.Mvc;
using ATS.WS.Services;
using System.Collections.Generic;
using System.Linq;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    // pra que serve isso ??????????????
    public class UsuarioPreferenciasController : BaseController
    {
        private readonly SrvUsuario _srvUsuario;

        public UsuarioPreferenciasController(BaseControllerArgs baseArgs, SrvUsuario srvUsuario) : base(baseArgs)
        {
            _srvUsuario = srvUsuario;
        }

        /// <summary>
        /// Adiciona ou atualiza um/mais registros novo/existe
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult AddOrUpdate(List<UsuarioPreferenciasRequestModel> @params)
        {
            try
            {
                _srvUsuario.SaveUsuarioPreferencias(@params);
                return Responde("");
            }
            catch (Exception e)
            {
                return Responde(e.Message);
            }
        }

        [HttpGet]
        public JsonResult GetPreferenciaUsuarioCampo(int idUsuario, string campo = null)
        {
            try
            {
                return Responde(_srvUsuario
                    .GetUsuarioPreferencias(idUsuario, campo)
                    .FirstOrDefault());
            }
            catch (Exception e)
            {
                return Responde(e.Message);
            }
        }

        [HttpGet]
        public JsonResult GetPreferenciasUsuarioPrefixLike(int idUsuario, string prefix)
        {
            try
            {
                return Responde(_srvUsuario
                    .GetPreferenciasUsuarioPrefixLike(idUsuario, prefix));
            }
            catch (Exception e)
            {
                return Responde(e.Message);
            }
        }
    }
}
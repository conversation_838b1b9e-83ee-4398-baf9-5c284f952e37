﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class ProtocoloController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvProtocolo _srvProtocolo;

        public ProtocoloController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvProtocolo srvProtocolo) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvProtocolo = srvProtocolo;
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Consultar(string token, string cnpjAplicacao, string cnpjEmpresa, int? idProtocolo, bool retornarEventosPagosNoCartao = false)
        {

            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProtocolo.Consultar(cnpjAplicacao, token, cnpjEmpresa, idProtocolo, retornarEventosPagosNoCartao));

            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }

        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Processar(string token, string cnpjAplicacao, int idProtocolo) {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();
                    
                _srvProtocolo.Processar(cnpjAplicacao, token, idProtocolo);

                return Responde(new Models.Mobile.Common.Retorno<string>(true, "Protocolo processado com sucesso!"));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarPorEstabelecimento(string token, string cnpjAplicacao, string cnpjEstabelecimento, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, int? idProtocolo)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProtocolo.ConsultarPorEstabelecimento(token, cnpjAplicacao, cnpjEstabelecimento, dataPagamentoInicial, dataPagamentoFinal, dataGeracaoInicial, dataGeracaoFinal, idProtocolo));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult RealizarPagamento(int idProtocolo, string token, string cnpjAplicacao)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProtocolo.RealizarPagamento(idProtocolo, token, cnpjAplicacao));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AgendarPagamento(string token, string cnpjAplicacao, string cnpjEstabelecimento, DateTime dataPrevisaoPagamento, int idProtocolo)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProtocolo.AgendarPrevisaoPagamento(idProtocolo, cnpjEstabelecimento, cnpjAplicacao, token, dataPrevisaoPagamento));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

    }
}
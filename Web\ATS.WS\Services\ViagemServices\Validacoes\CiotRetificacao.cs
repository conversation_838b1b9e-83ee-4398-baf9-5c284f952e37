using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.WS.Services.ViagemServices.Validacoes
{
    public class CiotRetificacao
    {
        private readonly ICiotV2App _ciotV2App;
        private readonly ICadastrosApp _cadastrosApp;

        public CiotRetificacao(ICiotV2App ciotV2App, ICadastrosApp cadastrosApp)
        {
            _ciotV2App = ciotV2App;
            _cadastrosApp = cadastrosApp;
        }
        
        public ValidationResult ValidarDadosRetificacao(Viagem viagemPersistida, ViagemIntegrarRequestModel viagemRequest)
        {
            var validationResult = new ValidationResult();
            
            validationResult.Add(ValidarPlacasRepetidas(viagemRequest));
            
            if (!validationResult.IsValid)
                return validationResult;
            
            validationResult.Add(ValidarCarretas(viagemPersistida, viagemRequest));
            
                    if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(ValidarNaturezaCarga(viagemPersistida, viagemRequest));

            return validationResult;
        }
        
        private ValidationResult ValidarCarretas(Viagem viagemPersistida, ViagemIntegrarRequestModel viagemRequest)
        {
            var validationResult = new ValidationResult();
            
            if (viagemRequest.Carretas != null && viagemRequest.Carretas.Any() || viagemRequest.CarretasViagemV2 != null && viagemRequest.CarretasViagemV2.Any())
            {
                if (viagemRequest.Carretas != null && viagemRequest.Carretas.Any())
                {
                    foreach (var placa in viagemRequest.Carretas)
                    {
                        var veiculoRntrc = _ciotV2App.ObterVeiculoPorPlaca(new ViagemCarreta {Placa = placa}, viagemPersistida.IdEmpresa);
                        
                        var rntrcProprietario = veiculoRntrc?.RNTRC;
                        var documentoProprietario = veiculoRntrc?.DocumentoProprietario;

                        if (string.IsNullOrEmpty(rntrcProprietario)) 
                            rntrcProprietario = viagemRequest.RNTRC.HasValue ? viagemRequest.RNTRC.ToString() : viagemPersistida.RNTRC.ToString();

                        var request = new ConsultarFrotaTransportadorRequest
                        {
                            Placa = new ObservableCollection<string> {placa},
                            RntrcTransportador = rntrcProprietario.PadLeft(9, '0'),
                            CpfCnpjInteressado = viagemRequest.CNPJAplicacao,
                            CpfCnpjTransportador = string.IsNullOrEmpty(documentoProprietario) ? viagemPersistida.CPFCNPJProprietario : documentoProprietario
                        };
                    
                        var resultadoConsulta = _ciotV2App.ConsultarFrotaTransportador(request);

                        if (resultadoConsulta.Sucesso.HasValue && resultadoConsulta.Sucesso.Value)
                        {
                            foreach (var veiculoFrotaTransportadorResponse in resultadoConsulta.VeiculoTransportador)
                            {
                                if (!validationResult.IsValid)
                                    continue;

                                if (veiculoFrotaTransportadorResponse.SituacaoVeiculoFrotaTransportador.HasValue && !veiculoFrotaTransportadorResponse.SituacaoVeiculoFrotaTransportador.Value)
                                    validationResult.Add($"Não é possível realizar a retificação do CIOT pois a placa {veiculoFrotaTransportadorResponse.PlacaVeiculo} não pertence ao RNTRC informado");
                            }
                        }
                        else
                        {
                            if (resultadoConsulta.FalhaComunicacaoAntt.HasValue && !resultadoConsulta.FalhaComunicacaoAntt.Value)
                            {
                                validationResult.Add(resultadoConsulta.Excecao != null
                                    ? $"Erro ao consultar veículo na ANTT para retificação: {resultadoConsulta.ExceptionMessage ?? resultadoConsulta.Excecao.Mensagem}"
                                    : "Falha de comunicação com a ANTT");   
                            }
                        }
                    }
                    
                    return validationResult;
                }

                if (viagemRequest.CarretasViagemV2 != null && viagemRequest.CarretasViagemV2.Any())
                {
                    var carretasAgrupadasPorRntrc = viagemRequest.CarretasViagemV2.GroupBy(o => new { o.Rntrc,o.CPFCNPJProprietario});

                    foreach (var carretas in carretasAgrupadasPorRntrc)
                    {
                        if (!validationResult.IsValid)
                            continue;
                            
                        var listaPlacas = new ObservableCollection<string>();
                        foreach (var carreta in carretas)
                            listaPlacas.Add(carreta.Placa);
                        
                        var request = new ConsultarFrotaTransportadorRequest
                        {
                            Placa = listaPlacas,
                            RntrcTransportador = !string.IsNullOrEmpty(carretas.Key.Rntrc) ? carretas.Key.Rntrc.PadLeft(9, '0') :
                            viagemRequest.RNTRC.HasValue ? viagemRequest.RNTRC.ToString().PadLeft(9, '0') : viagemPersistida.RNTRC.ToString().PadLeft(9, '0'),
                            CpfCnpjInteressado = viagemRequest.CNPJAplicacao,
                            CpfCnpjTransportador = string.IsNullOrWhiteSpace(carretas.Key.CPFCNPJProprietario) ? viagemPersistida.CPFCNPJProprietario : carretas.Key.CPFCNPJProprietario
                        };

                        var resultadoConsulta = _ciotV2App.ConsultarFrotaTransportador(request);
                        
                        if (resultadoConsulta.Sucesso.HasValue && resultadoConsulta.Sucesso.Value)
                        {
                            foreach (var veiculoFrotaTransportadorResponse in resultadoConsulta.VeiculoTransportador)
                            {
                                if (!validationResult.IsValid)
                                    continue;

                                if (veiculoFrotaTransportadorResponse.SituacaoVeiculoFrotaTransportador.HasValue && !veiculoFrotaTransportadorResponse.SituacaoVeiculoFrotaTransportador.Value)
                                    validationResult.Add($"Não é possível realizar a retificação do CIOT pois a placa {veiculoFrotaTransportadorResponse.PlacaVeiculo} não pertence ao RNTRC informado");
                            }
                        }
                        else
                        {
                            if (resultadoConsulta.FalhaComunicacaoAntt.HasValue && !resultadoConsulta.FalhaComunicacaoAntt.Value)
                            {
                                validationResult.Add(resultadoConsulta.Excecao != null
                                    ? $"Erro ao consultar veículo na ANTT para retificação: {resultadoConsulta.ExceptionMessage ?? resultadoConsulta.Excecao.Mensagem}"
                                    : "Falha de comunicação com a ANTT");   
                            }
                        }
                    }

                    return validationResult;
                }
            }
            
            return validationResult;
        }
        
        private ValidationResult ValidarPlacasRepetidas(ViagemIntegrarRequestModel viagemRequest)
        {
            var validationResult = new ValidationResult();
            var listaPlacas = new List<string> {viagemRequest.Placa};

            
            if (viagemRequest.Carretas != null && viagemRequest.Carretas.Any() ||
                viagemRequest.CarretasViagemV2 != null && viagemRequest.CarretasViagemV2.Any())
            {
                if (viagemRequest.Carretas != null && viagemRequest.Carretas.Any()) 
                    listaPlacas.AddRange(viagemRequest.Carretas);

                if (listaPlacas.Count != listaPlacas.Distinct().Count())
                    return validationResult.Add("A operação de transporte contém mais de um veículo com a mesma placa");
            }

            if (viagemRequest.CarretasViagemV2 == null || !viagemRequest.CarretasViagemV2.Any())
                return validationResult;
            
            if (viagemRequest.CarretasViagemV2 != null && viagemRequest.CarretasViagemV2.Any()) 
                listaPlacas.AddRange(viagemRequest.CarretasViagemV2.Select(o => o.Placa));

            return listaPlacas.Count != listaPlacas.Distinct().Count()
                ? validationResult.Add("A operação de transporte contém mais de um veículo com a mesma placa")
                : validationResult;
        }
        
        private ValidationResult ValidarNaturezaCarga(Viagem viagemPersistida, ViagemIntegrarRequestModel viagemRequest)
        {
            var validationResult = new ValidationResult();

            if (!viagemRequest.NaturezaCarga.HasValue || viagemPersistida.NaturezaCarga == viagemRequest.NaturezaCarga)
                return validationResult;
            
            var naturezasCarga = _cadastrosApp.GetNaturezasCarga();
            var naturezaCargaRequest = (viagemRequest.NaturezaCarga ?? 0).ToString().PadLeft(4, '0');

            if (!naturezasCarga.Any()) 
                return validationResult;

            return naturezasCarga.All(o => o.Id != naturezaCargaRequest)
                ? validationResult.Add($"Código de natureza de carga {viagemRequest.NaturezaCarga} inválido.")
                : validationResult;
        }
    }
}
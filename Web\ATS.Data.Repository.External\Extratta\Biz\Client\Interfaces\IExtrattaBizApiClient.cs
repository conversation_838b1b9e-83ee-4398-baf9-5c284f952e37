﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.Extratta.Models;

namespace ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces
{
    public interface IExtrattaBizApiClient
    {
        IntegracaoResult<GetExtratoBizResponse> GetExtrato(int identificador, int produto, string datainicio, string dataFim, int pagina = 0, int totalItens = 15);
        IntegracaoResult<GetTransacaoBizResponse> GetTransacao(int identificador, int produto, int transacaoId);
        IntegracaoResult PutSaqueHabilitar(int identificador, int produto);
        IntegracaoResult PutSaqueDesabilitar(int identificador, int produto);
        IntegracaoResult PutCompraOnlineHabilitar(int identificador, int produto);
        IntegracaoResult PutCompraOnlineDesabilitar(int identificador, int produto);
        IntegracaoResult PutCompraInternacionalHabilitar(int identificador, int produto);
        IntegracaoResult PutCompraInternacionalDesabilitar(int identificador, int produto);
        IntegracaoResult PutCompraFisicaHabilitar(int identificador, int produto);
        IntegracaoResult PutCompraFisicaDesabilitar(int identificador, int produto);
        IntegracaoResult<ConsultarPermissaoModelResponse> GetPermissaoCartao(int identificador, int produto);
        IntegracaoResult<ConsultarBancosModelResponse> GetBancos();
        IntegracaoResult GetRelatorioDespesasViagemOfx(int identificador, int produto, DateTime datainicio, DateTime dataFim, decimal saldo, List<string> email);
        IntegracaoResult PostDadosBancariosPix(IntegrarDadosBancariosPixModelRequest request);
        IntegracaoResult<ConsultarDadosBancariosPixModelResponse> GetDadosBancariosPix(string documentoEmpresa, string documentoProprietario);
        IntegracaoResult<TransferenciaPixModelResponse> PostTransferenciaPix(string documentoOrigem, string documentoDestino, decimal valor, int transacaoPixPortalId, string mensagem);
        IntegracaoResult<GerarQrCodeEstaticoPixModelResponse> GetGerarQrCodeEstaticoPix(string documento);
        IntegracaoResult<TransferenciaPixGridTimelineResponse> GetAccountTimelinePix(string documento, int page, int take, DateTime dataInicial, DateTime dataFinal);
        IntegracaoResult<TransferenciaPixComprovanteResponse> GetComprovanteTransferenciaPix(string documento, string endToEndId, ETipoTransferenciaPix tipo);
        IntegracaoResult<TransferenciaPixComprovanteResponse> GetComprovanteTransferenciaPixPorTransacao(string documento, int idTransacaoMsCartao);
        IntegracaoResult<ContaPixResponse> GetContaPix(string documento);
        IntegracaoResult CadastrarChavePix(string documento, string chave, ETipoChavePix tipo);
        IntegracaoResult DeletarChavePix(string documento, string chave);
        IntegracaoResult<ChavesPixResponse> ConsultarChavesPix(string documento);
        IntegracaoResult VerificarDonoChave(string documentoEmpresa, string documentoTitular, ETipoChavePix tipo, string chave);
        IntegracaoResult<LimitesPixAlterarModelResponse> AlterarLimite(string documento, LimitesPixAlterarModelRequest request);
        IntegracaoResult<LimitesPixConsultarModelResponse> ConsultarLimites(string documento);
        IntegracaoResult<ExtratoConsolidadoModelResponse> ExtratoConsolidado(ExtratoConsolidadoModelRequest request);
    }
}
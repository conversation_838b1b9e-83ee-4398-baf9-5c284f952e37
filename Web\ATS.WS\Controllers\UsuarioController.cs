﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Webservice.Request;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Web.Http.Results;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Models.LocalizacaoUsuarios;
using ATS.Domain.Models;
using ATS.Domain.Models.DespesaUsuario;
using ATS.WS.Attributes;
using ATS.WS.Models.Mobile.Response;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using System.Threading.Tasks;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class UsuarioController : BaseController
    {
        private readonly SrvUsuario _srvUsuario;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly SrvDespesaUsuario _srvDespesaUsuario;
        private readonly ILocalizacaoUsuarioApp _localizacaoUsuarioApp;

        public UsuarioController(BaseControllerArgs baseArgs, SrvUsuario srvUsuario, IUsuarioApp usuarioApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmpresaApp empresaApp, SrvDespesaUsuario srvDespesaUsuario, ILocalizacaoUsuarioApp localizacaoUsuarioApp) : base(baseArgs)
        {
            _srvUsuario = srvUsuario;
            _usuarioApp = usuarioApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _empresaApp = empresaApp;
            _srvDespesaUsuario = srvDespesaUsuario;
            _localizacaoUsuarioApp = localizacaoUsuarioApp;
        }

        #region Aplicativo

        /// <summary>
        /// Realizar o login na base de dados
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AutorizarMobile]
        public string Login(LoginRequestModel @params)
        {
            try
            {
                if (!ValidarTokenLogin(@params.Token) && !ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var autenticacaoAplicacao = _autenticacaoAplicacaoApp.Get(@params.CNPJAplicacao);

                if (autenticacaoAplicacao != null && autenticacaoAplicacao.IdEmpresa != 1010)
                    return new JsonResult().Responde(_srvUsuario.Login(@params));

                return new JsonResult().Responde(_srvUsuario.LoginAppAntigo(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AutorizarMobile]
        public System.Web.Mvc.JsonResult Localizacao(LocalizacaoUsuarioAplicativoModel request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var model = new LocalizacaoUsuarioAddModel
                {
                    IdUsuario = request.IdUsuario,
                    Latitude = request.Localizacao.Latitude,
                    Longitude = request.Localizacao.Longitude
                };

                var result = _localizacaoUsuarioApp.Add(model);

                if (!result.Success)
                    return Responde(new LocalizacaoUsuarioAplicativoResponse
                    {
                        Sucesso = false,
                        Mensagem = result.ToString()
                    });

                return Responde(new LocalizacaoUsuarioAplicativoResponse
                {
                    Sucesso = true
                });
            }
            catch (Exception e)
            {
                return Responde(new LocalizacaoUsuarioAplicativoResponse
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AutorizarMobile]
        public System.Web.Mvc.JsonResult CriarDespesa(DespesaCriarAplicativoModel request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var result = _srvDespesaUsuario.Criar(request);

                if (!result.Success)
                    return Responde(new DespesaCriarAplicativoResponse
                    {
                        Sucesso = false,
                        Mensagem = result.ToString()
                    });

                return Responde(new DespesaCriarAplicativoResponse
                {
                    Sucesso = true
                });
            }
            catch (Exception e)
            {
                return Responde(new DespesaCriarAplicativoResponse
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        #endregion

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string HasProprietario(string token, string cnpjAplicacao, List<string> cpfCnpjs, bool onlyBase = false)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.HasProprietario(cpfCnpjs));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /*
        /// <summary>
        /// Realizar o login na base de dados
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        public string LoginByUser(LoginRequestModel @params)
        {
            try
            {
                if (!ValidarTokenLogin(@params.Token) && !ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                AutenticacaoAplicacao autenticacaoAplicacao = _autenticacaoAplicacaoApp.Get(@params.CNPJAplicacao);

                if (autenticacaoAplicacao != null && autenticacaoAplicacao.IdEmpresa != 1010)
                    return new JsonResult().Responde(
                        _srvUsuario.LoginUsuario(@params));
                else
                    return new JsonResult().Responde(
                        _srvUsuario.LoginAppAntigo(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        */

        /*
        /// <summary>
        /// Realizar o login na base de dados
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Autorizar]
        public string GetSolicitacaoAcesso(LoginRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var autenticacaoAplicacao = _autenticacaoAplicacaoApp.Get(@params.CNPJAplicacao);
                if (autenticacaoAplicacao != null && autenticacaoAplicacao.IdEmpresa != 1025)
                {
                    var ret = _srvUsuario.Login(@params);
                    if (!ret.Sucesso) return new JsonResult().Responde(ret);
                    return new JsonResult().Responde(ret);
                }
                else
                {
                    var ret = _srvUsuario.LoginAppAntigo(@params);
                    if (!ret.Sucesso) return new JsonResult().Responde(ret);
                    return new JsonResult().Responde(ret);
                }
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }*/

        /*
        /// <summary>
        /// Realizar o login na base de dados
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Portal)]
        public string ValidaSessaoUsuario(ValidarSessao @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();


                if (!_usuarioApp.ValidarSessaoUsuario(@params.Key))
                {
                    return new JsonResult().Responde(new
                    {
                        Sucesso = false,
                        Mensagem = "A sessão inspirou!"
                    });
                }

                return new JsonResult().Responde(new
                {
                    Sucesso = true,
                    Mensagem = ""
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        */


        /// <summary>
        /// Altera a senha do usuário
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string AlterarSenha(SenhaRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();
                return new JsonResult().Responde(
                    _srvUsuario.AlterarSenha(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Retorna o status do usuário
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string GetStatusUsuario(RequestUsuarioValidoModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.GetStatusUsuario(@params.CPFCNPJ));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Retorna a partir do CPF informado se o mesmo coincide com um usuário ou motorista.
        /// <para>Aquele que encontrar primeiro será retornado.</para>
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string HasCadastro(RequestUsuarioValidoModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.HasCadastro(@params.CPFCNPJ));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Gerenciar o usuário mobile
        /// </summary>
        /// <param name="params">Parâmetros</param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string Integrar(UsuarioIntegrarMobRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Atualizar o usuário mobile
        /// </summary>
        /// <param name="params">Parâmetros</param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public string Atualizar(UsuarioAtualizarMobRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.Atualizar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Integrar os horários de checkin
        /// </summary>
        /// <param name="params">Parâmetros</param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string IntegrarHorariosCheckIn(IntegrarHorarioCheckInRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.IntegrarHorariosCheckIn(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Integrar os horários de notificação
        /// </summary>
        /// <param name="params">Parâmetros</param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string IntegrarHorariosNotificacao(IntegrarHorariosNotificacaoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.IntegrarHorariosNotificacao(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Remove o vinculo que o usuário tem com o empresa
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string DesvincularUsuarioComEmpresa(string token, string cpfUsuario, string cnpjAplicacao)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.DesvincularUsuarioComEmpresa(cpfUsuario));
            }
            catch (Exception ex)
            {
                Logger.Error(ex);
                return new JsonResult().Mensagem(ex.Message);
            }
        }


        [HttpPost]
        [Expor(EApi.Mobile)]
        [AllowAnonymous]
        [EnableLogRequest]
        public string ValidarUsuarioCPFEmail(string token, string cnpjAplicacao, string cpf, string email)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.ValidarUsuarioCPFEmail(cpf, email));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        public string RecuperacaoSenha(string token, string cnpjAplicacao, string cpf, string email, int? administradora)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.RecuperacaoSenha(cpf, email, administradora));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /* desabilitei, rever, chama uma funcao que parece desabilitada
        [HttpPost]
        [Expor(EApi.Portal)]
        public string GetParamUsuario(string key)
        {
            try
            {
                Empresa empresa = null;
                var usuario = _usuarioApp.GetUsuarioByKey(key);

                if (usuario.IdEmpresa.HasValue)
                    empresa = _empresaApp.Get(usuario.IdEmpresa.Value, null);

                return new JsonResult().Responde(
                    new
                    {
                        usuario.TipoCliente,
                        usuario.Perfil,
                        DocUsuario = usuario.CPFCNPJ,
                        IsGestor = usuario.Gestor,
                        usuario.Foto,
                        usuario.IdUsuario,
                        Logo = empresa?.Logo != null ? Convert.ToBase64String(empresa.Logo) : string.Empty,
                        EmpresaCores = new
                        {
                            empresa?.Layout?.CorFundo,
                            empresa?.Layout?.CorGuia,
                            empresa?.Layout?.CorGuiaTexto,
                            empresa?.Layout?.CorTexto
                        }
                    });
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }*/

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string GetUsuarioPreferencias(string token, string cnpjAplicacao, int idUsuario)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.GetUsuarioPreferencias(idUsuario));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string SaveUsuarioPreferencias(string token, string cnpjAplicacao, List<UsuarioPreferenciasRequestModel> usuarioPreferencias)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.SaveUsuarioPreferencias(usuarioPreferencias));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// VincularUsuarioComEmpresa
        /// </summary>
        /// <param name="params">UsuarioMotoristaVinculoRequestModel</param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string VincularUsuarioComEmpresa(UsuarioMotoristaVinculoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvUsuario.VincularUsuarioComEmpresa(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }


        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarVistoriadores(string token, string cnpjAplicacao, string cpfUsuario)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                var usuarios = _usuarioApp.ConsultarVistoriadores(cpfUsuario);

                return new JsonResult().Responde(new Retorno<object>(true, usuarios));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        // o que que é isso ???????????????????????
        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string GerarHashCode(string token, string cnpjAplicacao, int? idUsuario, string cpf)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();
                return new JsonResult().Responde(_srvUsuario.GerarKeyCodeTransaction(idUsuario, cpf));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        // isso parece que nao é usado. ?????????????
        [System.Web.Http.HttpPost]
        [Expor(EApi.Integracao)]
        public string AtualizarDataUltimaAberturaAplicativo(string token, string cnpjAplicacao, int? usuarioId)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.AtualizarDataUltimaAberturaAplicativo(usuarioId));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        // isso tambem parece que nao e usado ?????????????
        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarInformacoesMobile(string token, string cnpjAplicacao, int itensPorPagina, int pagina, int? empresaId, string documento)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvUsuario.ConsultarInformacoesMobile(cnpjAplicacao, itensPorPagina, pagina, documento));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public string ExportarUsuariosKeycloak()
        {
            try
            {
                _srvUsuario.ExportarUsuariosKeycloak();
                return new JsonResult().Responde("Ok");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public string AtualizarUsuariosKeycloak()
        {
            try
            {
                _srvUsuario.AtualizarUsuariosKeycloak();
                return new JsonResult().Responde("Ok");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        public System.Web.Mvc.JsonResult GetRecursos(string cnpjAplicacao, string token, int idusuario)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                var retorno = _srvUsuario.GetRecursos(idusuario);

                if (!retorno.Success)
                    return Responde(new RecursosUsuarioMobileModel
                    {
                        Sucesso = false,
                        Mensagem = retorno.ToString()
                    });

                return Responde(new RecursosUsuarioMobileModel
                {
                    Sucesso = true,
                    Objeto = retorno.Value
                });
            }
            catch (Exception e)
            {
                return Responde(new RecursosUsuarioMobileModel
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public System.Web.Mvc.JsonResult ConsultarUltimoAnexoDespesa(string cnpjAplicacao, string token, int idUsuario,string hashId)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                var retorno = _srvUsuario.GetAnexoByUsuario(idUsuario, hashId);

                return Responde( new Retorno<DespesaUsuarioAnexoModelResponse>()
                {
                    Sucesso = true,
                    Objeto = retorno
                });
            }
            catch (Exception e)
            {
                return Responde(new Retorno
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }
    }
}
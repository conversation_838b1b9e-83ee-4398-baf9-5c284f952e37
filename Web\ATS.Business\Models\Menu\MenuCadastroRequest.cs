using System.Collections.Generic;

namespace ATS.Domain.Models.Menu
{
    public class MenuCadastroRequest
    {
        public MenuCadastroRequest()
        {
            ModulosId = new List<int>();
            Perfis = string.Empty;
        }

        public int IdMenu { get; set; }
        public int? IdMenuPai { get; set; }
        public string Descricao { get; set; }
        public string LinkNovo { get; set; }
        public string Funcao { get; set; }
        public string Perfis { get; set; }
        public bool Ativo { get; set; } = true;
        public bool IsMenuPai { get; set; }
        public bool IsMenuMobile { get; set; }
        public List<int> ModulosId { get; set; }
    }
}
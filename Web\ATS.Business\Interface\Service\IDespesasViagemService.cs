﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem;
using ATS.CrossCutting.Reports.DespesasViagem.RelatorioListaDespesasViagem;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Models.DespesasViagem;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.Interface.Service
{
    public interface IDespesasViagemService : IService<DespesasViagem>
    {
        byte[] GerarRelatorioGridDespesaViagem(string logo, string extensao, List<RelatorioDespesasViagemDataType> relatorio);
        byte[] GerarRelatorioExtratoGridDespesaViagem(string logo,string extensao, List<RelatorioExtratoDespesasViagemDataType> relatorio);
        byte[] GerarRelatorioExtratoV2GridDespesaViagem(string logo, string extensao, List<RelatorioExtratoDetalhadoDespesasViagemDataType> relatorio);
    }
}
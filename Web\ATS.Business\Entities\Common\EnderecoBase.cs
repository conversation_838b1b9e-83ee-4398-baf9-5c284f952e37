﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities.Common
{
    public class EnderecoBase
    {
        /// <summary>
        /// CEP
        /// </summary>
        public string CEP { get; set; }

        /// <summary>
        /// Endereço (Nome da rua, ou similar)
        /// </summary>
        public string Endereco { get; set; }

        /// <summary>
        /// Complemento
        /// </summary>
        public string Complemento { get; set; }

        /// <summary>
        /// Número do apartamento ou casa
        /// </summary>
        public int? Numero { get; set; }

        /// <summary>
        /// Nome do bairro
        /// </summary>
        public string <PERSON>rro { get; set; }

        /// <summary>
        /// Código da cidade
        /// </summary>
        public int IdCidade { get; set; }
        
        /// <summary>
        /// Código do Estado
        /// </summary>
        public int IdEstado { get; set; }

        /// <summary>
        /// Código do País
        /// </summary>
        public int IdPais { get; set; }

        #region Referências

        [ForeignKey("IdPais")]
        public virtual Pais Pais { get; set; }

        [ForeignKey("IdEstado")]
        public virtual Estado Estado { get; set; }

        [ForeignKey("IdCidade")]
        public virtual Cidade Cidade { get; set; }

        #endregion
    }
}
﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Repository.External.Extratta.Models;
using ATS.Data.Repository.External.Serpro.DTO;

namespace ATS.Application.Interface
{
    public interface ISerproApp : IAppBase
    {
        ValidationResult ValidarPortador(string cpf);
        IntegracaoResult<ValidacaoSerproResponse> ValidarPortador(ValidacaoSerproRequest request);
    }
}
﻿namespace ATS.CrossCutting.Reports.Pedagio.Recibo
{
    public class ComprovanteCompraPedagioDataType
    {
        public string CompraId { get; set; }
        public string CompraIdEstornada { get; set; }
        public string EmpresaNome { get; set; }
        public string EmpresaCnpj { get; set; }
        public string Status { get;  set; }
        public string Fornecedor { get; set; }
        public string DocumentoFavorecido { get; set; }
        public string NomeFavorecido { get; set; }
        public string Placa { get; set; }
        public string QuantidadeEixos { get; set; }
        public string Valor { get; set; }
        public string ProtocoloRequisicao { get; set; }
        public string NumeroCiot { get; set; }
        public string DataCadastro { get; set; }
        public string DataCancelamento { get; set; }
        public string DataConfirmacao { get; set; }
        public string DataExpiracaoCompraPedagio { get; set; }
        public string ConsultaCustoHistoricoId { get; set; }
        public string CartaoFrete { get; set; }
        public string ProtocoloValePedagio { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
        public string AvisoTransportador { get; set; }
        public string CnpjFornecedora { get; set; }
        public string Categoria { get; set; }
        public string CpfCnpjContratante { get; set; }
        public string NomeContratante { get; set; }
        public string NomeCampoCpfCnpj { get; set; }
    }
}
﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Aumentado_TamanhoCampo_RotaModelo : DbMigration
    {
        public override void Up()
        {
            AlterColumn("dbo.PONTOS_ROTA_MODELO", "descricao", c => c.String(nullable: false, maxLength: 500, unicode: false));
            AlterColumn("dbo.ROTA_MODELO", "origemdescricao", c => c.String(nullable: false, maxLength: 500, unicode: false));
            AlterColumn("dbo.ROTA_MODELO", "destinodescricao", c => c.String(nullable: false, maxLength: 500, unicode: false));
        }
        
        public override void Down()
        {
            AlterColumn("dbo.ROTA_MODELO", "destinodescricao", c => c.String(nullable: false, maxLength: 100, unicode: false));
            AlterColumn("dbo.ROTA_MODELO", "origemdescricao", c => c.String(nullable: false, maxLength: 100, unicode: false));
            AlterColumn("dbo.PONTOS_ROTA_MODELO", "descricao", c => c.String(nullable: false, maxLength: 100, unicode: false));
        }
    }
}

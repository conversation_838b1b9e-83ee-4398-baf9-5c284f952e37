﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Entities
{
    public class Plano
    {
        public int IdPlano { get; set; }
        public DateTime DataCriacao { get; set; }
        public DateTime DataAtualizacao { get; set; }
        public int IdUsuarioCadastro { get; set; }
        public int IdUsuarioAtualizacao { get; set; }
        public string Descricao { get; set; }
        public bool Ativo { get; set; }


        #region Relationships
        public virtual Usuario Usuario { get; set; }
        #endregion

        #region Navegação Inversa
        public virtual ICollection<PlanoEmpresa> PlanosEmpresa { get; set; }
        #endregion
    }
}

﻿using ATS.Domain.Enum;

namespace ATS.WS.Models.Webservice.Response.pagamento_frete
{
    /// <summary>
    /// Model com o minimo de dados necessários para a consulta de um pagamento de frete
    /// </summary>
    public class PagamentoFreteResumoResponse
    {
        public string DataHoraPagamento { get; set; }
        public string DataValidade { get; set; }
        public int IdEmpresa { get; set; }
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public decimal? INSS { get; set; }
        public string Instrucao { get; set; }
        public decimal? IRRPF { get; set; }
        public string NumeroRecibo { get; set; }
        public decimal? SESTSENAT { get; set; }
        public EStatusViagemEvento Status { get; set; }
        public string Token { get; set; }
        public decimal? ValorBruto { get; set; }
        public decimal? ValorPagamento { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public ETipoEventoViagem TipoEventoViagem { get; set; }
        public EOrigemIntegracao OrigemPagamento { get; set; }
    }
}
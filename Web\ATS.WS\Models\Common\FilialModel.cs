﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class FilialModel
    {
        public int IdFilial { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJEmpresa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RazaoSocialEmpresa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJ { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RazaoSocial { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string NomeFantasia { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Sigla { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CEP { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Endereco { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Complemento { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Numero { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Bairro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Telefone { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Email { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string InscricaoEstadual { get; set; } //<!-- criado dia 26-11-2018 para atribuir o valor string para a inscrição estadual da filial para imprimir na Ordem de Carregamento

        public int IdEmpresa { get; set; }
        public int IdCidade { get; set; }
        public int IdEstado { get; set; }
        public int IdPais { get; set; }
        public bool Ativo { get; set; } = true;

        public bool? MostraFilialOc { get; set; } = true; //<-- criado dia 21-11-2018 para atribuir o valor booleano para ativar a visualização da filial na Ordem de Carregamento

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? Latitude { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? Longitude { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual CidadeModel Cidade { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual List<FilialModel> PontosDeApoio { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdFilialMae { get; set; }
    }
}

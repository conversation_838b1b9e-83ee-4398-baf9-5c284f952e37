﻿using ATS.Domain.Models.Categoria;
using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class CategoriaGetAllResponseDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set { _mensagem = value?.Trim(); }
        }
        private string _mensagem { get; set; }
        public IList<CategoriaGetModelResponse> Objeto { get; set; }
    }
}

﻿using System;
using System.Linq;
using System.Net;
using System.Web.Http;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using TagExtrattaClient;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class TagExtrattaController : BaseController
    {
        private readonly ITagExtrattaApp _app;
        private readonly ICargaAvulsaApp _cargaAvulsaApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvIP _srvIP;

        public TagExtrattaController(BaseControllerArgs baseArgs, ITagExtrattaApp app, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvIP srvIP, ICargaAvulsaApp cargaAvulsaApp) : base(baseArgs)
        {
            _app = app;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvIP = srvIP;
            _cargaAvulsaApp = cargaAvulsaApp;
        }

        [System.Web.Mvc.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(true)]
        public JsonResult Vincular(VincularTagRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(@params.CNPJEmpresa) ? @params.CNPJAplicacao : @params.CNPJEmpresa, GetRealIp()))
                    return NaoAutorizado();

                var result = _app.Vincular(Mapper.Map<SalvarTagRequest>(@params));

                if (result.Success)
                    return Responde(new Retorno<string>()
                    {
                        Sucesso = true,
                        Mensagem = result.Messages.FirstOrDefault()
                    });

                return Responde(new Retorno<string>()
                {
                    Sucesso = false,
                    Mensagem = result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(true)]
        public JsonResult ConsultarModelos(string token, string cnpjAplicacao, string cnpjEmpresa,int? take,int? page)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, GetRealIp()))
                    return NaoAutorizado();

                var result = _app.GetModelosMovemais(take, page);

                if (result.Success)
                    return Responde(new Retorno<object>()
                    {
                        Sucesso = true,
                        Objeto = result.Value
                    });

                return Responde(new Retorno<string>()
                {
                    Sucesso = false,
                    Mensagem = result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(true)]
        public JsonResult CadastrarModelosMoveMais(string token, string cnpjAplicacao, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, GetRealIp()))
                    return NaoAutorizado();

                var result = _app.CadastrarModelosMoveMais();

                if (result.Success)
                    return Responde(new Retorno<object>()
                    {
                        Sucesso = true,
                        Mensagem = result.Value
                    });

                return Responde(new Retorno<string>()
                {
                    Sucesso = false,
                    Mensagem = result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno,true)]
        public JsonResult CadastrarEstoqueTags(string token, string cnpjAplicacao, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, GetRealIp()))
                    return NaoAutorizado();

                var result = _app.CadastrarEstoqueTags();

                if (result.Success)
                    return Responde(new Retorno<object>()
                    {
                        Sucesso = true,
                        Mensagem = result.Value
                    });

                return Responde(new Retorno<string>()
                {
                    Sucesso = false,
                    Mensagem = result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [System.Web.Http.Route]
        [EnableLogRequest]
        [AutorizarMoveMaisWebhook]
        public ActionResult NotificarPassagem([FromBody] NotificarPassagemRequestModel request)
        {
            try
            {
                var result = _app.NotificarPassagemPraca(Mapper.Map<PassagemPracaPedagioModelRequest>(request));

                if (!result.Success)
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
                
                return Json(new
                {
                    code = 0,
                    message = result.Value
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpStatusCodeResult(HttpStatusCode.InternalServerError, e.Message);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [System.Web.Http.Route]
        [EnableLogRequest]
        [AutorizarMoveMaisWebhook(EValidacaoToken.ApenasIP)]
        public ActionResult GenerateToken([FromBody] LoginWebhookMoveMaisRequestModel request)
        {
            try
            {
                if (!request.ValidRequest().Success)
                    return new HttpStatusCodeResult(HttpStatusCode.BadRequest);

                var result = _app.GenerateTokenWebhook(request.user,request.password);

                if (!result.Success)
                    return new HttpStatusCodeResult(HttpStatusCode.Unauthorized);
                
                Response.StatusCode = (int)HttpStatusCode.OK;
                return Json(new { Token = result.Value }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpStatusCodeResult(HttpStatusCode.InternalServerError, e.Message);
            }
        }
        
        [System.Web.Http.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult ProvisionarValor([FromBody] ProvisionarValorTagWebhookRequestModel request)
        {
            try
            {
                if (!ValidarToken(request.Token))
                    return TokenInvalido();
                
                var validRequest = request.ValidRequest();
                
                if (!validRequest.Success)
                    return Responde(new Retorno<string>()
                    {
                        Sucesso = validRequest.Success,
                        Mensagem = validRequest.Messages.FirstOrDefault(),
                    });

                var result = _cargaAvulsaApp.ProvisionarValor(Mapper.Map<ProvisionarRequest>(request));
                
                return Responde(new Retorno<string>()
                {
                    Sucesso = result.Success,
                    Mensagem = result.Success ? "OK" : result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Http.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult ProvisionarTaxa([FromBody] ProvisionarValorTagWebhookRequestModel request)
        {
            try
            {
                if (!ValidarToken(request.Token))
                    return TokenInvalido();
                
                var validRequest = request.ValidRequest();
                
                if (!validRequest.Success)
                    return Responde(new Retorno<string>()
                    {
                        Sucesso = validRequest.Success,
                        Mensagem = validRequest.Messages.FirstOrDefault(),
                    });
                
                var result = _cargaAvulsaApp.ProvisionarTaxa(Mapper.Map<ProvisionarRequest>(request));
                
                return Responde(new Retorno<string>()
                {
                    Sucesso = result.Success,
                    Mensagem = result.Success ? "OK" : result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Http.HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult EstornarPedagio([FromBody] EstornarPedagioValorTagWebhookRequestModel request)
        {
            try
            {
                if (!ValidarToken(request.Token))
                    return TokenInvalido();
                
                var validRequest = request.ValidRequest();
                
                if (!validRequest.Success)
                    return Responde(new Retorno<string>()
                    {
                        Sucesso = validRequest.Success,
                        Mensagem = validRequest.Messages.FirstOrDefault(),
                    });
                
                var result = _cargaAvulsaApp.EstornarProvisionamentoPedagio(request.EventoSaldoTagId);
                
                return Responde(new Retorno<string>()
                {
                    Sucesso = result.Success,
                    Mensagem = result.Success 
                        ? "OK" 
                        : result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Consulta a stuacao da TAG do veiculo no fornecedor especificado
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarSituacaoTagPlaca(string cnpjAplicacao, string cnpjEmpresa, string token, string documentoUsuarioAudit, string placa, Fornecedor2 fornecedor)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(documentoUsuarioAudit) || (documentoUsuarioAudit.Length != 11 && documentoUsuarioAudit.Length != 14))
                    return Mensagem("DocumentoUsuarioAudit não informado ou com formato inválido.");
                
                if (!ValidarToken(token, documentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, GetRealIp()))
                    return NaoAutorizado();

                var result = _app.ConsultarSituacaoTagPlaca(placa, fornecedor);

                return Responde(new Retorno<ConsultarSituacaoTagResponseModel>()
                {
                    Sucesso = result.Success,
                    Mensagem = result.Messages.FirstOrDefault(),
                    Objeto = result.Value
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [System.Web.Http.HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.Todos,true)]
        public JsonResult ConsultarPassagensVeiculo(string token, string cnpjAplicacao, string cnpjEmpresa,DateTime? dataInicio,DateTime? dataFim,string placa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, GetRealIp()))
                    return NaoAutorizado();

                var result = _app.ConsultarPassagensVeiculo(dataInicio,dataFim,placa);

                if (result.Success)
                    return Responde(new Retorno<object>()
                    {
                        Sucesso = true,
                        Objeto = result.Value
                    });

                return Responde(new Retorno<string>()
                {
                    Sucesso = false,
                    Mensagem = result.Messages.FirstOrDefault(),
                });
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
    }
}
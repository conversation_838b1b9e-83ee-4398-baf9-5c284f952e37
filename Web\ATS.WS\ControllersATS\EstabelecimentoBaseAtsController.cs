﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.ControllersATS.Default;
using AutoMapper;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Globalization;
using ATS.Application.Interface;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Helpers;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using Documento = ATS.Domain.Entities.Documento;

namespace ATS.WS.ControllersATS
{
    public class EstabelecimentoBaseAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IEstabelecimentoBaseDocumentoApp _estabelecimentoBaseDocumentoApp;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly ICredenciamentoApp _credenciamentoApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IUsuarioService _usuarioService;
        private readonly IDocumentoApp _documentoApp;

        public EstabelecimentoBaseAtsController(IUserIdentity userIdentity, IEstabelecimentoBaseService estabelecimentoBaseService,
            IEstabelecimentoBaseDocumentoApp estabelecimentoBaseDocumentoApp, ICredenciamentoApp credenciamentoApp, IUsuarioApp usuarioApp, IUsuarioService usuarioService, IDocumentoApp documentoApp)
        {
            _userIdentity = userIdentity;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _estabelecimentoBaseDocumentoApp = estabelecimentoBaseDocumentoApp;
            _credenciamentoApp = credenciamentoApp;
            _usuarioApp = usuarioApp;
            _usuarioService = usuarioService;
            _documentoApp = documentoApp;
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters, bool apenasProprioEstabelecimento = false)
        {
            try
            {
                if (apenasProprioEstabelecimento)
                {
                    if (filters == null)
                        filters = new List<QueryFilters>();

                    filters.Add(new QueryFilters()
                    {
                        Campo = "IdEstabelecimento",
                        CampoTipo = EFieldTipo.Number,
                        Operador = EOperador.Exact,
                        Valor = _estabelecimentoBaseService
                                .GetEstabelecimentoPorUsuario(_userIdentity.IdUsuario).ToString()
                    });
                }

                var estabelecimentos = new EstabelecimentoBaseApp(_estabelecimentoBaseService)
                    .ConsultaGrid(idTipoEstabelecimento, descricao, take, page, order, filters);

                return ResponderSucesso(estabelecimentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(EstabelecimentoCrud estabelecimentoCrud)
        {
            try
            {
                if (estabelecimentoCrud.Produtos == null)
                    estabelecimentoCrud.Produtos = new List<EstabelecimentoProdutoCrud>();
                
                if(estabelecimentoCrud.Produtos.Any(item => item.Descricao.Length > 100))
                    return ResponderErro("Descrição dos produtos devem possuir no máximo 100 caracteres.");

                var estabelecimento = Mapper.Map<EstabelecimentoCrud, EstabelecimentoBase>(estabelecimentoCrud);

                var estabelecimentoBaseApp = new EstabelecimentoBaseApp(_estabelecimentoBaseService);
                var validationResult = estabelecimentoBaseApp.Add(estabelecimento);
                
                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());
                
                var credenciamentoApp = _credenciamentoApp;
                validationResult.Add(credenciamentoApp.Add(estabelecimento, estabelecimentoCrud.IdsEmpresa, estabelecimento.IdEstabelecimento, null,
                    EStatusCredenciamento.Enviado, EStatusDocumentacaoCredenciamento.Aguardando, estabelecimentoCrud.AdministradoraPlataforma, true));

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());
                
                if (validationResult.Alerts.Any())
                    return ResponderAtencao(true, validationResult.ToString());
                
                return ResponderSucesso("Cadastro realizado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.GetBaseException().Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAssociacoes(int? idEstabelecimento)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento && !idEstabelecimento.HasValue)
                {
                    var usuarioEstabelecimento = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario);
                    idEstabelecimento = usuarioEstabelecimento.FirstOrDefault()?.IdEstabelecimento;
                }

                var associacoes = _estabelecimentoBaseService.GetAssociacoes(idEstabelecimento)
                    .Select(x => new
                    {
                        x.IdEstabelecimento,
                        x.Descricao
                    });

                return ResponderSucesso(associacoes);
            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro("Não foi possível consultar os estabelecimentos base do tipo associação. ");
            }
        }
        
        [HttpGet]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAssociacoesEmpresas(List<int> idsEmpresa)
        {
            try
            {
                var associacoes = _estabelecimentoBaseService.GetAssociacoesEmpresas(idsEmpresa);

                return ResponderSucesso(associacoes);
            }
            catch (Exception e)
            {
                Logger.Warn(e);
                return ResponderErro("Não foi possível consultar os estabelecimentos base do tipo associação. ");
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAssociacoesProtocolo(int? idEstabelecimento)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Estabelecimento && !idEstabelecimento.HasValue)
                    idEstabelecimento = _usuarioService.GetUsuariosEstabelecimento(_userIdentity.IdUsuario)?.FirstOrDefault()?.IdEstabelecimento;

                var associacoes = _estabelecimentoBaseService.GetEstabelecimentosAssociacoes(idEstabelecimento)
                    .Select(o => new {o.Associacao?.IdEstabelecimento, o.Associacao?.Descricao});

                var estabelecimentoBase = _estabelecimentoBaseService.Get(idEstabelecimento ?? 0);

                return ResponderSucesso(new
                {
                    isAssociacao = estabelecimentoBase?.Associacao,
                    isEstabSemFiliados = !estabelecimentoBase?.AssociacoesBaseEstabelecimento?.Any(),
                    associacoes
                });
            }
            catch (Exception e)
            {
                return ResponderErro($"Não foi possível consultar os estabelecimentos base do tipo associação. Erro: {e.GetBaseException().Message}");
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(EstabelecimentoCrud estabelecimentoCrud)
        {
            try
            {
                if (!estabelecimentoCrud.IdEstabelecimento.HasValue)
                    return ResponderErro("Id do estabelecimento é obrigatório.");
                
                var validacoes = ValidarPropriedades(estabelecimentoCrud);
                if (!validacoes.IsValid)
                    return ResponderErro(validacoes.ToFormatedMessage());

                var estabelecimento = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(estabelecimentoCrud.IdEstabelecimento.Value);

                //TODO: Fazer no automapper
                ReflectionHelper.CopyProperties(estabelecimentoCrud, estabelecimento);

                estabelecimento.AssociacoesBaseEstabelecimento = new List<EstabelecimentoBaseAssociacao>();
                
                if (estabelecimentoCrud.Associacoes != null && !estabelecimentoCrud.Associacao)
                {
                    foreach (var associacao in estabelecimentoCrud.Associacoes)
                    {
                        estabelecimento.AssociacoesBaseEstabelecimento.Add(new EstabelecimentoBaseAssociacao
                        {
                            IdAssociacao = associacao.IdEstabelecimento
                        });
                    }
                }

                var produtos = estabelecimentoCrud.Produtos?.Select(x =>
                {
                    var produto = new EstabelecimentoBaseProduto();
                    produto.Descricao = x.Descricao;
                    produto.IdEstabelecimentoBase = x.IdEstabelecimentoBase;
                    produto.IdProduto = x.IdProduto;
                    produto.UnidadeMedida = x.UnidadeMedida;

                    decimal precoPromocional;
                    decimal precoUnitario;
                    if (!string.IsNullOrWhiteSpace(x.PrecoPromocional) && decimal.TryParse(x.PrecoPromocional, out precoPromocional))
                        produto.PrecoPromocional = precoPromocional;
                    if (!string.IsNullOrWhiteSpace(x.PrecoUnidade) && decimal.TryParse(x.PrecoUnidade, out precoUnitario))
                        produto.PrecoUnitario = precoUnitario;

                    return produto;
                }).ToList();
                
                var documentos = estabelecimentoCrud.Documentos
                    .Select(x =>
                    {
                        var documentoAnexado = new EstabelecimentoBaseDocumento
                        {
                            IdEstabelecimentoBase = estabelecimento.IdEstabelecimento,
                            IdEstabelecimentoBaseDocumento = x.IdEstabelecimentoBaseDocumento,
                            IdDocumento = x.IdDocumento,
                            Descricao = x.Descricao,
                            PermiteEditarData = x.PermiteEditarData,
                            DataValidade = !string.IsNullOrEmpty(x.DataValidade) ? Convert.ToDateTime(x.DataValidade) : (DateTime?) null,
                            DiasValidade = x.DiasValidade,
                            Token = x.Token,
                        };

                        return documentoAnexado;
                    }).ToList();

                var contas = estabelecimentoCrud.EstabelecimentoContasBancarias?.Select(o =>
                {
                    var conta = new EstabelecimentoBaseContaBancaria
                    {
                        DigitoConta = o.DigitoConta,
                        IdEstabelecimentoBaseContaBancaria = o.IdEstabelecimentoBaseContaBancaria,
                        IdEstabelecimentoBase = o.IdEstabelecimentoBase,
                        CodigoBanco = o.CodigoBanco,
                        Conta = o.Conta,
                        NomeBanco = o.NomeBanco,
                        CnpjTitular = o.CnpjTitular,
                        Agencia = o.Agencia,
                        NomeTitular = o.NomeTitular,
                        TipoConta = o.TipoConta,
                        NomeConta = o.NomeConta
                    };

                    return conta;
                }).ToList();
                
                var validationResult = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Update(estabelecimento, produtos, contas, estabelecimentoCrud.AdministradoraPlataforma, estabelecimentoCrud.IdsExcluidos, estabelecimentoCrud.IdsContasBancariasExcluir, documentos);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());
                if (validationResult.Alerts.Any())
                    return ResponderAtencao(true, validationResult.ToString());
                
                return ResponderSucesso("Dados alterados com sucesso!");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AdicionarDocumentos(int idEstabelecimento, List<Documento> documentos, int idAdministradora)
        {
            var estabelecimento = new EstabelecimentoBaseApp(_estabelecimentoBaseService).GetWithDocumentos(idEstabelecimento);
            var novosDocumentos = new List<EstabelecimentoBaseDocumento>();
            
            documentos.ForEach(x => novosDocumentos.Add(new EstabelecimentoBaseDocumento
            {
                IdEstabelecimentoBase = estabelecimento.IdEstabelecimento,
                IdDocumento = x.IdDocumento,
                Descricao = x.Descricao,
                DiasValidade = x.DiasValidade,
            }));

            var validationResult = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Update(estabelecimento, null, null, 
                idAdministradora, null, null, novosDocumentos);

            if (!validationResult.IsValid)
                return ResponderErro(validationResult.ToFormatedMessage());
            if (validationResult.Alerts.Any())
                return ResponderAtencao(true, validationResult.ToString());
                
            return ResponderSucesso("Dados alterados com sucesso!");
        }      

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaDocumentosCredenciamento(int idEmpresa, int idEstabelecimentoBase)
        {
            try
            {
                var idsEmpresa = new List<int> {idEmpresa};
                
                var documentos = _estabelecimentoBaseDocumentoApp.GetDocumentos(idEstabelecimentoBase);
                if(documentos == null || !documentos.Any())
                    return ResponderSucesso(documentos);
                
                var idsDocumentoEmpresa = _documentoApp.GetIdsDocumentosEmpresa(idEmpresa);
                documentos = documentos.Where(x => x.IdDocumento.HasValue && idsDocumentoEmpresa.Contains(x.IdDocumento.Value)).ToList();
                
                var documentosCredenciamento = _estabelecimentoBaseDocumentoApp.MergeDocumentosInformadosComExigidos(documentos, idEstabelecimentoBase, idsEmpresa);

                var retorno = documentosCredenciamento.Select(x => new Documento
                {
                    IdDocumento = x.IdDocumento ?? 0,
                    DiasValidade = x.PermiteEditarData ? null : x.DiasValidade,
                    Descricao = x.Descricao,
                }).ToList();
                    
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }


        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idEstabelecimento)
        {
            try
            {
                var estabelecimento = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(idEstabelecimento);
                var idsEmpresa = _credenciamentoApp.GetIdsEmpresaSolicitadoCredenciamento(idEstabelecimento);
                estabelecimento.Documentos = _estabelecimentoBaseDocumentoApp.MergeDocumentosInformadosComExigidos(estabelecimento.Documentos.ToList(), idEstabelecimento, idsEmpresa);
                
                var retorno = new
                {
                    estabelecimento.IdTipoEstabelecimento,
                    DescricaoTipo = estabelecimento.TipoEstabelecimento.Descricao,
                    estabelecimento.Descricao,
                    estabelecimento.IdEstabelecimento,
                    estabelecimento.IdPais,
                    estabelecimento.IdEstado,
                    estabelecimento.IdCidade,
                    estabelecimento.CEP,
                    estabelecimento.Latitude,
                    estabelecimento.Longitude,
                    estabelecimento.Logradouro,
                    estabelecimento.Bairro,
                    estabelecimento.Numero,
                    estabelecimento.CNPJEstabelecimento,
                    estabelecimento.Email,
                    estabelecimento.EmailProtocolo,
                    estabelecimento.Complemento,
                    estabelecimento.RazaoSocial,
                    Associacoes = estabelecimento.AssociacoesBaseEstabelecimento?.Select(x => new
                    {
                        IdEstabelecimento = x.IdAssociacao,
                        x.Associacao.Descricao
                    }).ToList(),
                    estabelecimento.Associacao,
                    Produtos = estabelecimento.EstabelecimentoBaseProdutos?.Select(x => new
                    {
                        x.Descricao,
                        x.UnidadeMedida,
                        PrecoUnidade = x.PrecoUnitario.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoUnitario).Substring(3) : string.Empty,
                        PrecoPromocional = x.PrecoPromocional.HasValue ? string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", x.PrecoPromocional).Substring(3) : string.Empty,
                        x.IdProduto
                    }),
                    estabelecimentoContasBancarias = estabelecimento.EstabelecimentoBaseContasBancarias?.Select(o => new
                    {
                        o.DigitoConta,
                        o.Agencia,
                        o.CnpjTitular,
                        o.CodigoBanco,
                        o.Conta,
                        o.IdEstabelecimentoBase,
                        o.IdEstabelecimentoBaseContaBancaria,
                        o.NomeBanco,
                        o.NomeConta,
                        o.NomeTitular,
                        o.TipoConta
                    }),
                    Documentos = estabelecimento.Documentos?.Select(x => new Documento
                    {
                        IdDocumento = x.IdDocumento ?? 0,
                        DiasValidade = x.PermiteEditarData ? null : x.DiasValidade,
                        Descricao = x.Descricao,
                    }).ToList()
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idEstabelecimento)
        {
            try
            {
                var validationResult = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Inativar(idEstabelecimento);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Estabelecimento inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idEstabelecimento)
        {
            try
            {
                var validationResult = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Reativar(idEstabelecimento);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Estabelecimento reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        private ValidationResult ValidarPropriedades(EstabelecimentoCrud estabelecimentoCrud)
        {
            var retorno = new ValidationResult();
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.Descricao))
                retorno.Add("Descrição é obrigatória.");
            if (!estabelecimentoCrud.IdTipoEstabelecimento.HasValue)
                retorno.Add("Icone é obrigatório.");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.CEP))
                retorno.Add("CEP é obrigatório.");
            if (!estabelecimentoCrud.IdPais.HasValue)
                retorno.Add("País é obrigatório.");
            if (!estabelecimentoCrud.IdEstado.HasValue)
                retorno.Add("Estado é obrigatório.");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.RazaoSocial))
                retorno.Add("Razão social é obrigatória.");
            if (!estabelecimentoCrud.IdCidade.HasValue)
                retorno.Add("Cidade é obrigatória.");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.Bairro))
                retorno.Add("Bairro é obrigatório. ");
            if (string.IsNullOrWhiteSpace(estabelecimentoCrud.Logradouro))
                retorno.Add("Logradouro é obrigatório.");

            return retorno;
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetEstabelecimentoUsuarioLogado()
        {
            try
            {
                var estabelecimento = _estabelecimentoBaseService.GetEstabelecimentoPorUsuario(_userIdentity.IdUsuario);

                return ResponderSucesso(estabelecimento);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorIdDetalhes(int idEstabelecimento)
        {
            try
            {
                var estabelecimento = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(idEstabelecimento);

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    var idsDocumentoEmpresa = _documentoApp.GetIdsDocumentosEmpresa(_userIdentity.IdEmpresa.Value);
                    estabelecimento.Documentos = estabelecimento.Documentos.Where(x => x.IdDocumento.HasValue && idsDocumentoEmpresa.Contains(x.IdDocumento.Value)).ToList();
                }
                
                var retorno = new
                {
                    Tipo = estabelecimento.TipoEstabelecimento.Descricao,
                    Nome = $"{estabelecimento.Descricao} ({estabelecimento.CNPJEstabelecimento.ToCNPJFormato()})",
                    Cnpj = estabelecimento.CNPJEstabelecimento.ToCNPJFormato(),
                    estabelecimento.RazaoSocial,
                    estabelecimento.EmailProtocolo,
                    Telefone = estabelecimento.Telefone.ToTELFormato(),
                    IsAssociacao = estabelecimento.Associacao,
                    Associacoes = estabelecimento.AssociacoesBaseEstabelecimento.Select(c => new
                    {
                        c.IdAssociacao, 
                        CnpjAssociacao = c.Associacao.CNPJEstabelecimento.ToCNPJFormato(),
                        DescriacaoAssociacao = c.Associacao.Descricao 
                    }).ToList(),
                    Produtos = estabelecimento.EstabelecimentoBaseProdutos.Select(c => new
                    {
                        CodigoProduto = c.IdProduto,
                        c.Descricao,
                        c.UnidadeMedida,
                        PrecoUnitario = c.PrecoUnitario?.ToString("C", new CultureInfo("pt-BR")),
                        PrecoPromocional = c.PrecoPromocional?.ToString("C", new CultureInfo("pt-BR"))
                    }).ToList(),
                    DadosBancarios = estabelecimento.EstabelecimentoBaseContasBancarias.Select(c => new
                    {
                        Descricao = c.NomeConta,
                        c.CodigoBanco,
                        c.NomeBanco,
                        c.Agencia,
                        c.Conta,
                        Digito = c.DigitoConta,
                        Tipo = c.TipoConta,
                        CnpjTitular = c.CnpjTitular.ToCNPJFormato(),
                        NomeTitual = c.NomeTitular
                    }).ToList(),
                    Documentos = estabelecimento.Documentos.Select(c => new
                    {
                        c.IdDocumento,
                        c.Token,
                        DataValidade = c.DataValidade?.ToString("dd/MM/yyyy"),
                        c.Descricao
                    }).ToList(),
                    Endereco = $"{estabelecimento.Pais.Nome}, {estabelecimento.Estado.Nome}, {estabelecimento.Cidade.Nome}, {estabelecimento.Bairro}, {estabelecimento.Logradouro} nº {estabelecimento.Numero} - {estabelecimento.CEP}  {estabelecimento.Complemento}",
                    Localizacao = new
                    {
                        lat = estabelecimento.Latitude,
                        lng = estabelecimento.Longitude
                    },
                    estabelecimento.Email
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
    }
}

using System;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using Dapper;

namespace ATS.Data.Repository.Dapper
{
    public class CheckinResumoDapper : DapperFactory<CheckinResumo>, ICheckinResumoDapper
    {
        public CheckinResumoConsultaModel ConsultarCheckinResumosPaginado(int empresaId, int itensPorPagina, int pagina, DateTime? dataInicio, DateTime? dataFim)
        {
            var whereClause = string.Empty;

            if (dataInicio.HasValue && dataFim.HasValue)
            {
                if (dataInicio.Value.Hour == 0 && dataInicio.Value.Minute == 0)
                    dataInicio = dataInicio.Value.StartOfDay();
                
                if (dataFim.Value.Hour == 0 && dataFim.Value.Minute == 0)
                    dataFim = dataFim.Value.EndOfDay();

                whereClause = "AND CR.DATAHORA BETWEEN @DataInicio AND @DataFim";
            }
            
            var queryTotalizador = $@"SELECT count(CR.idcheckinresumo)
                                      FROM CHECKIN_RESUMO CR
                                                  LEFT JOIN MOTORISTA M ON M.IDMOTORISTA = CR.IDMOTORISTA
                                                  LEFT JOIN VEICULO V ON V.IDMOTORISTA = M.IDMOTORISTA
                                                  LEFT JOIN USUARIO U ON U.IDUSUARIO = CR.IDUSUARIO
                                                  LEFT JOIN CIDADE C ON C.IDCIDADE = CR.IDCIDADECHECKIN
                                                  LEFT JOIN ESTADO E ON E.IDESTADO = C.IDESTADO
                                      WHERE CR.IDEMPRESA = @EmpresaId
                                        {whereClause}";
            
            var query = $@"DECLARE
                            @ItensPorPagina INT = {itensPorPagina},
                            @Pagina         INT = {pagina}

                          SELECT CR.IDCHECKIN   AS CheckinId,
                                 CR.DATAHORA    AS DataHora,
                                 CR.TIPOEVENTO  AS TipoEvento,
                                 CR.LATITUDE    AS Latitude,
                                 CR.LONGITUDE   AS Longitude,
                                 CR.IDMOTORISTA AS MotoristaId,
                                 M.CPF          AS MotoristaCpf,
                                 M.NOME         AS MotoristaNome,
                                 CASE WHEN V_MOT.IDEMPRESA = CR.IDEMPRESA THEN V_MOT.PLACA ELSE V_USU.PLACA END AS Placa,
                                 U.CPFCNPJ      AS UsuarioCpfCnpj,
                                 U.NOME         AS UsuarioNome,
                                 C.IBGE         AS CidadeIbge,
                                 C.NOME         AS CidadeNome,
                                 E.SIGLA        AS Uf
                          FROM CHECKIN_RESUMO CR
                                 LEFT JOIN MOTORISTA M ON M.IDMOTORISTA = CR.IDMOTORISTA
                                 LEFT JOIN VEICULO V_MOT ON V_MOT.IDMOTORISTA = M.IDMOTORISTA
                                 LEFT JOIN USUARIO U ON U.IDUSUARIO = CR.IDUSUARIO
                                 LEFT JOIN VEICULO V_USU ON V_USU.IDUSUARIO = U.IDUSUARIO
                                 LEFT JOIN CIDADE C ON C.IDCIDADE = CR.IDCIDADECHECKIN
                                 LEFT JOIN ESTADO E ON E.IDESTADO = C.IDESTADO
                          WHERE CR.IDEMPRESA = @EmpresaId
                            {whereClause}
                          ORDER BY CR.DATAHORA
                            OFFSET (@Pagina - 1) * @ItensPorPagina ROWS
                            FETCH NEXT @ItensPorPagina ROWS ONLY";

            using (var connection = new DapperContext().GetConnection)
            {
                var parameters = new
                {
                    EmpresaId = empresaId, 
                    DataInicio = dataInicio,
                    DataFim = dataFim
                };
                
                var totalRegistros = connection.Query<int>(queryTotalizador, parameters).FirstOrDefault();
                var registros = connection.Query<CheckinResumoItensConsultaModel>(query, parameters).ToList();
                
                return new CheckinResumoConsultaModel {ResumosTotal = totalRegistros, Resumos = registros};
            }
        }
    }
}
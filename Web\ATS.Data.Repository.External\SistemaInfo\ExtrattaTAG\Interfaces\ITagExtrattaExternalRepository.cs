﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Client.Response.Base;
using TagExtrattaClient;

namespace ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Interfaces
{
    public interface ITagExtrattaExternalRepository
    {
        TagExtrattaClientResponse<RemessasGetModelResponse> GetRemessas(int? take, int? page, List<ApiQueryFilters> filters,EOperadorOrder order,string campo);        
        TagExtrattaClientResponse<TagsGetModelResponse> GetTags(int? take, int? page, List<ApiQueryFilters> filters,EOperadorOrder order,string campo, List<long> tagsSelecionadas = null, DateTime? dataInicio = null, DateTime? dataFim  = null);    
        TagExtrattaClientResponse<TagGetSerialModelResponse> GetTagSerial(long serial,int? statusTag);
        TagExtrattaClientResponse<string> ReceberRemessa(int id);
        TagExtrattaClientResponse<string> CadastrarRemessa(RemessaCadastrarModelRequest request);
        TagExtrattaClientResponse<List<TagGetModelResponse>> GetTagsLote(long min, long max,int? statusTag);
        TagExtrattaClientResponse<RemessaGetModelResponse> GetRemessa(int id);
        TagExtrattaClientResponse<VeiculoGetModelResponse> GetVeiculo(string placa);
        TagExtrattaClientResponse<ModelosVeiculoMoveMaisGetModelResponse> GetModelosMoveMais(GetModelosVeiculoMoveMaisRequest request);        
        TagExtrattaClientResponse<string> Vincular(TagVincularModelRequest request, long serial, string placa);
        TagExtrattaClientResponse<string> Bloquear(long serial);
        TagExtrattaClientResponse<string> Desbloquear(long serial);
        TagExtrattaClientResponse<string> Desvincular(long serial);
        TagExtrattaClientResponse<string> CadastrarBloqueios(BloqueioCadastrarModelRequest request);
        TagExtrattaClientResponse<BloqueioGetModelResponse> GetBloqueios(int usuarioId);
        TagExtrattaClientResponse<ConsultarGridValePedagioHubModelResponse> GridValePedagioHub(int? take, int? page, List<ApiQueryFilters> filters, EOperadorOrder order, string campo,DateTime? dataInicio,DateTime? dataFim);
        TagExtrattaClientResponse<string> CadastrarModelosMoveMais();
        TagExtrattaClientResponse<string> CadastrarEstoqueTags();
        TagExtrattaClientResponse<Guid> NotificarPassagemPraca(PassagemPracaPedagioModelRequest request);
        TagExtrattaClientResponse<PagamentosGetModelResponse> GetPagamentos(int? take, int? page, List<ApiQueryFilters> filters, EOperadorOrder order, string campo, DateTime? dataInicio, DateTime? dataFim, int? idEmpresa,FornecedorTagEnum fornecedor);
        TagExtrattaClientResponse<PagamentosItemGetModelResponse> GetPagamento(long id);
        TagExtrattaClientResponse<string> PagamentoManualEventoTag(PagamentoManualRequest request);
        TagExtrattaClientResponse<string> EstornoManualEventoTag(PagamentoManualRequest request);
        TagExtrattaClientResponse<PassagensPracaGetModelResponse> GetPassagensPraca(int? take, int? page, List<ApiQueryFilters> filters, EOperadorOrder order, string campo, DateTime? dataInicio, DateTime? dataFim,int? empresaId);
        TagExtrattaClientResponse<string> DesvincularEmpresa(long serial);
        TagExtrattaClientResponse<FaturamentoGetModelResponse> GetFaturamento(int? take, int? page, List<ApiQueryFilters> filters, EOperadorOrder order, string campo, DateTime dataInicio, DateTime dataFim,FornecedorTagEnum fornecedor);
        TagExtrattaClientResponse<FaturamentoTotalizadorGetModelResponse> GetTotalizadorFaturamento(FaturamentoTotalizadorGetRequest request);
        TagExtrattaClientResponse<FaturaGetModelResponse> GetFatura(DateTime dataInicio, DateTime dataFim, int empresaId, FornecedorTagEnum fornecedorTag);
        TagExtrattaClientResponse<PassagensPedagioCompraHubGetModelResponse> GetPassagensPedagioCompraHub(int compraId, FornecedorTagEnum fornecedor);
        TagExtrattaClientResponse<ConsultarPlacaFornecedorResponse> ConsultarPlacasFornecedor(string placa, FornecedorTagEnum fornecedor);
        TagExtrattaClientResponse<string> ContestarPassagem(ContestacaoPassagemPedagioModelRequest request);
        TagExtrattaClientResponse<ConsultarSaldoVeiculoValePedagioModelResponse> ConsultarSaldoValePedagioVeiculo(string placa);
        TagExtrattaClientResponse<PassagensPracaVeiculoGetModelResponse> GetPassagensVeiculoPraca(DateTime? dataInicio, DateTime? dataFim, string placa);
    }
}
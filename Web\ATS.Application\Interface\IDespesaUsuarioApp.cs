using System.Linq;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IDespesaUsuarioApp : IBaseApp<IDespesaUsuarioService>
    {
        BusinessResult<DespesaUsuarioAddModelResponse> Add(DespesaUsuarioAddModel model);
        IQueryable<DespesaUsuario> GetAll();
        List<string> GetListHashId(int? idUsuario);
        DespesaUsuarioAnexoModelResponse GetLastAnexo(int? idUsuario, string hashId);
    }
}

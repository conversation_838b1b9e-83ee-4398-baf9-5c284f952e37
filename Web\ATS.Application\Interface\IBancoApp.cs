﻿using ATS.Domain.Grid;
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO;
using ATS.Domain.Models;


namespace ATS.Application.Interface
{
    public interface IBancoApp 
    {
        object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,ConsultarBancoResponseDTO bancos);
        ConsultarBancosModelResponse ConsultarTodos(bool apenasComIspb = false);
    }
}
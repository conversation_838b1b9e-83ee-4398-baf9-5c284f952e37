using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.Viagem;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Request;
using ATS.WS.Services;
using ATS.WS.Services.ViagemServices;
using ATS.WS.Services.ViagemV2Services;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class ViagemV2Controller : BaseController
    {
        private readonly IViagemApp _viagemApp;
        private readonly SrvViagemV2 _srvViagemV2;
        private readonly ConsultasViagemV2 _consultasViagemV2;
        private readonly EventoViagemV2 _eventoViagemV2;
        private readonly CancelamentoViagemV2 _cancelamentoViagemV2;
        private readonly PedagioViagemV2 _pedagioViagemV2;
        private readonly PedagioViagem _pedagioViagem;
        private readonly ValoresViagemV2 _valoresViagemV2;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvIP _srvIP;
        
        public ViagemV2Controller(BaseControllerArgs baseArgs, IClienteApp clienteApp, IParametrosApp parametrosApp, IProprietarioApp proprietarioApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, ICadastrosApp cadastrosApp,
            IViagemApp viagemApp, IEmpresaApp empresaApp, IEmpresaRepository empresaRepository, SrvCartoes srvCartoes, SrvCliente srvCliente, SrvViagemV2 srvViagemV2,
            EventoViagemV2 eventoViagemV2, CancelamentoViagemV2 cancelamentoViagemV2, ConsultasViagemV2 consultasViagemV2, PedagioViagemV2 pedagioViagemV2, PedagioViagem pedagioViagem, ValoresViagemV2 valoresViagemV2, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvIP srvIP) : base(baseArgs)
        {
            _viagemApp = viagemApp;
            _srvViagemV2 = srvViagemV2;
            _eventoViagemV2 = eventoViagemV2;
            _cancelamentoViagemV2 = cancelamentoViagemV2;
            _consultasViagemV2 = consultasViagemV2;
            _pedagioViagemV2 = pedagioViagemV2;
            _pedagioViagem = pedagioViagem;
            _valoresViagemV2 = valoresViagemV2;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvIP = srvIP;
        }

        /// <summary>
        /// Médoto de integração de viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Integrar(ViagemV2IntegracaoRequestModel request)
        {
            try
            {
                var ip = this.GetRealIp();

                if (!Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit))
                    return FalhaAutenticacao();
                
                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(request.CNPJEmpresa) ? request.CNPJAplicacao : request.CNPJEmpresa, ip))
                    return NaoAutorizado();
                
                return  Responde(_srvViagemV2.Integrar(request));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método de alteração dos dados da viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Alterar(ViagemV2AlteracaoRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit) ? FalhaAutenticacao() : Responde(_srvViagemV2.Alterar(request, true));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por consultar os dados da viagem
        /// </summary>
        /// <param name="token"></param>
        /// <param name="cnpjAplicacao"></param>
        /// <param name="viagemId"></param>
        /// <returns></returns>
        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Consultar(string token, string cnpjAplicacao, int? viagemId)
        {
            try
            {
                return !Autenticar(token, cnpjAplicacao)
                    ? FalhaAutenticacao()
                    : Responde(_consultasViagemV2.Consultar(viagemId, cnpjAplicacao));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por cancelar uma viagem específica
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Cancelar(ViagemV2AlterarStatusRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_cancelamentoViagemV2.Cancelar(request));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por realizar a baixa de uma viagem específica
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Baixar(ViagemV2AlterarStatusRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_eventoViagemV2
                        .Baixar(request));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por baixar um evento específico da viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult BaixarEvento(ViagemV2BaixarEventoRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_eventoViagemV2
                        .BaixarEvento(request, true));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por cancelar um evento específico da viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult CancelarEvento(ViagemV2CancelarEventoRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_eventoViagemV2
                        .CancelarEvento(request, true));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por bloquear um evento específico da viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult BloquearEvento(ViagemV2BloquearEventoRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_eventoViagemV2
                        .BloquearEvento(request));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por desbloquear um evento específico da viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult DesbloquearEvento(ViagemV2DesbloquearEventoRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_eventoViagemV2
                        .DesbloquearEvento(request, true));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por adicionar eventos em uma viagem específica
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AdicionarEventos(ViagemV2AdicionarEventosRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_eventoViagemV2
                        .AdicionarEventos(request, true));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por realizar a solicitação de compra de pedágio
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ComprarPedagio(ViagemV2CompraPedagioRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_pedagioViagemV2.ComprarPedagio(request));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por alterar os valores da viagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AlterarValoresViagem(AlterarValoresViagemRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_valoresViagemV2
                        .AlterarValoresViagem(request, true));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarCustoPedagioRota(CustoPedagioRotaRequestModel request)
        {
            try
            {
                return !Autenticar(request.Token, request.CNPJAplicacao, request.DocumentoUsuarioAudit)
                    ? FalhaAutenticacao()
                    : Responde(_pedagioViagem.ConsultarCustoPedagioRota(request));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult RemoverCarretas(ViagemRemoverCarretasRequest request)
        {
            try
            {
                var validacaoChamada = request.ValidaRequest();
                //porque aqui nao valida o token ???????????????
                if (!validacaoChamada.IsValid)
                    return Responde(new Retorno<ViagemIntegrarResponseModel>(validacaoChamada.ToString()));

                return Responde(_srvViagemV2.RemoverCarretas(request));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AutorizarEstabelecimentos(ViagemV2AutorizarEstabelecimentosRequest request)
        {
            try
            {
                var validacaoBase = request.ValidaRequestBase(false);
                //porque aqui nao valida o token ????????????
                if (!validacaoBase.IsValid)
                    return Responde(new Retorno<ViagemV2AutorizarEstabelecimentosResponse>(validacaoBase.ToString()));

                var validacaoRequest = request.ValidarPreencherAutorizacaoEstabelecimentos(_viagemApp);
                if (!validacaoRequest.IsValid)
                    return Responde(new Retorno<ViagemV2AutorizarEstabelecimentosResponse>
                    {
                        Sucesso = false, Mensagem = "Requisição inválida: " + validacaoRequest,
                        Objeto = null, Faults = validacaoRequest.GetFaults()
                    });

                return Responde(_srvViagemV2.AutorizarEstabelecimentos(request));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPut]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult DesautorizarEstabelecimentos(ViagemV2DesautorizarEstabelecimentosRequest request)
        {
            try
            {
                var validacaoBase = request.ValidaRequestBase(false);
                //porque nao valida o token ???????????????????
                if (!validacaoBase.IsValid)
                    return Responde(new Retorno<ViagemV2DesautorizarEstabelecimentosResponse>(validacaoBase.ToString()));

                var validacaoRequest = request.ValidarPreencherAutorizacaoEstabelecimentos(_viagemApp);
                if (!validacaoRequest.IsValid)
                    return Responde(new Retorno<ViagemV2DesautorizarEstabelecimentosResponse>
                    {
                        Sucesso = false, Mensagem = "Requisição inválida: " + validacaoRequest,
                        Objeto = null, Faults = validacaoRequest.GetFaults()
                    });

                return Responde(_srvViagemV2.DesautorizarEstabelecimentos(request));
            }
            catch (Exception e)
            {
                Logger.Error(e);

                var validacao = new ValidationResult<EValidationViagemEstabelecimento>();
                validacao.Add(EValidationViagemEstabelecimento.ErroNaoCatalogado, EFaultType.Error, new object[] { e.GetBaseException().Message });

                return Responde(new Retorno<ViagemV2DesautorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                });
            }
        }
    }
}
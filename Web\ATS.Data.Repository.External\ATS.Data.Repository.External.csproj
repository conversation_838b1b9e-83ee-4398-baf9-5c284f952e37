﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8A62A98C-9D32-48EE-B100-0259B1FDF08A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ATS.Data.Repository.External</RootNamespace>
    <AssemblyName>ATS.Data.Repository.External</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <LangVersion>9</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.2\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.Bson, Version=1.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.Bson.1.0.2\lib\net45\Newtonsoft.Json.Bson.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=112.1.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.112.1.0\lib\net48\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.2\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.6.0.0\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.2\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.2\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.6.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.6\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.6\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Extratta\Abastecimento\Client\AbastecimentoApiClient.cs" />
    <Compile Include="Extratta\Abastecimento\Client\Interfaces\IAbastecimentoApiClient.cs" />
    <Compile Include="Extratta\Abastecimento\Client\Models\CancelarCreditoTicketLogRequest.cs" />
    <Compile Include="Extratta\Abastecimento\Client\Models\InserirCreditoTicketLogRequest.cs" />
    <Compile Include="Extratta\Abastecimento\Client\Models\InserirCreditoTicketLogResponse.cs" />
    <Compile Include="Extratta\Biz\Client\ExtrattaBizApiClient.cs" />
    <Compile Include="Extratta\Biz\Client\Interfaces\IExtrattaBizApiClient.cs" />
    <Compile Include="Extratta\Biz\Models\CadastrarChavePixRequest.cs" />
    <Compile Include="Extratta\Biz\Models\ChavesPixGridResponse.cs" />
    <Compile Include="Extratta\Biz\Models\ChavesPixResponse.cs" />
    <Compile Include="Extratta\Biz\Models\ExtratoConsolidadoModelRequest.cs" />
    <Compile Include="Extratta\Biz\Models\ExtratoConsolidadoModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\LimitesPixAlterarModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\ConsultarBancosModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\ConsultarDadosBancariosPixModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\ConsultarPermissaoModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\ContaPixResponse.cs" />
    <Compile Include="Extratta\Biz\Models\GerarQrCodeEstaticoPixModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\GetExtratoBizResponse.cs" />
    <Compile Include="Extratta\Biz\Models\GetTransacaoBizResponse.cs" />
    <Compile Include="Extratta\Biz\Models\IntegrarDadosBancariosPixModelRequest.cs" />
    <Compile Include="Extratta\Biz\Models\IntegrarDadosBancariosPixModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\LimitesPixAlterarModelRequest.cs" />
    <Compile Include="Extratta\Biz\Models\LimitesPixConsultarModelResponse.cs" />
    <Compile Include="Extratta\Biz\Models\RelatorioExtratoOfxBizRequest.cs" />
    <Compile Include="Extratta\Biz\Models\RelatorioExtratoOfxBizResponse.cs" />
    <Compile Include="Extratta\Biz\Models\TransferenciaPixComprovanteResponse.cs" />
    <Compile Include="Extratta\Biz\Models\TransferenciaPixGridReleasesResponse.cs" />
    <Compile Include="Extratta\Biz\Models\TransferenciaPixGridTimelineResponse.cs" />
    <Compile Include="Extratta\Biz\Models\TransferenciaPixModelRequest.cs" />
    <Compile Include="Extratta\Biz\Models\TransferenciaPixModelResponse.cs" />
    <Compile Include="Extratta\Models\IntegracaoResult.cs" />
    <Compile Include="Logs\LoggableClient.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Serpro\DTO\ValidacaoSerproRequest.cs" />
    <Compile Include="Serpro\DTO\ValidacaoSerproResponse.cs" />
    <Compile Include="Serpro\ISerproClient.cs" />
    <Compile Include="Serpro\SerproClient.cs" />
    <Compile Include="SistemaInfo\Auth\AuthAtsException.cs" />
    <Compile Include="SistemaInfo\Auth\AuthExternalRepository.cs" />
    <Compile Include="SistemaInfo\Auth\Client\AuthApi.cs" />
    <Compile Include="SistemaInfo\Auth\Client\AuthApiClient.Extensions.cs" />
    <Compile Include="SistemaInfo\Cadastro\CadastroExternalRepository.cs" />
    <Compile Include="SistemaInfo\Cadastro\Client\Cadastros.ApiClient.cs" />
    <Compile Include="SistemaInfo\Cartao\Client\Cartao.ApiClient.cs" />
    <Compile Include="SistemaInfo\Cartao\Client\Cartao.WebClient.cs" />
    <Compile Include="SistemaInfo\Cartao\Client\Cartao.WebClient.Extensions.cs" />
    <Compile Include="SistemaInfo\Cartao\DTO\ConsultarContasBancariasResponse.cs" />
    <Compile Include="SistemaInfo\Cartao\DTO\ConsultarListaBancosResponse.cs" />
    <Compile Include="SistemaInfo\Ciot\Client\Ciot.ApiClient.cs" />
    <Compile Include="SistemaInfo\Ciot\DTOs\ApiErrorResponseDto.cs" />
    <Compile Include="SistemaInfo\Ciot\DTOs\ConsultarSituacaoTransportadorInternalResponse.cs" />
    <Compile Include="SistemaInfo\ExtrattaTAG\Client\Response\Base\TagExtrattaClientResponse.cs" />
    <Compile Include="SistemaInfo\ExtrattaTAG\Interfaces\ITagExtrattaExternalRepository.cs" />
    <Compile Include="SistemaInfo\ExtrattaTAG\TagExtrattaExternalRepository.cs" />
    <Compile Include="SistemaInfo\ExtrattaTAG\Client\TagExtrattaClient.cs" />
    <Compile Include="SistemaInfo\Infra\Client\Infra.ApiClient.Extensions.cs" />
    <Compile Include="SistemaInfo\Infra\LogInfra\LogProcessInfo.cs" />
    <Compile Include="SistemaInfo\Infra\WebhookExternalRepository.cs" />
    <Compile Include="SistemaInfo\Pedagio\Client\Pedagio.ApiClient.cs" />
    <Compile Include="SistemaInfo\Pedagio\Client\Pedagio.ApiClient.Extensions.cs" />
    <Compile Include="SistemaInfo\Pedagio\DTO\CustoRotaPedagioRequest.cs" />
    <Compile Include="SistemaInfo\Pedagio\DTO\CustomFiltersDTO\FilteredOptions.cs" />
    <Compile Include="SistemaInfo\Pedagio\DTO\CustomFiltersDTO\FilterOptions.cs" />
    <Compile Include="SistemaInfo\Pedagio\DTO\GetStatusPedagioResponse.cs" />
    <Compile Include="SistemaInfo\Pedagio\PedagioAtsException.cs" />
    <Compile Include="SistemaInfo\Pedagio\PedagioExternalRepository.cs" />
    <Compile Include="SistemaInfo\RequestLogUtils.cs" />
    <Compile Include="SistemaInfo\SistemaInfoMicroServiceBaseClient.cs" />
    <Compile Include="SistemaInfo\Cartao\CartaoAtsException.cs" />
    <Compile Include="SistemaInfo\Cartao\CartaoExternalRepository.cs" />
    <Compile Include="SistemaInfo\Cartao\DTO\AtsPortadorRequest.cs" />
    <Compile Include="SistemaInfo\Ciot\CiotExternalRepository.cs" />
    <Compile Include="SistemaInfo\Infra\InfraAtsException.cs" />
    <Compile Include="SistemaInfo\Infra\Client\Infra.ApiClient.cs" />
    <Compile Include="SistemaInfo\Infra\InfraExternalRepository.cs" />
    <Compile Include="Utils\RestClient.cs" />
    <Compile Include="Utils\RequestUtils.cs" />
    <Compile Include="SistemaInfo\SistemaInfoConsts.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <Compile Include="SistemaInfo\Cartao\Client\Cartao.ApiClient.Extensions.cs" />
    <Compile Include="SistemaInfo\Ciot\Client\Ciot.ApiClient.Extensions.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sistema.Framework\Sistema.Framework.Util\Sistema.Framework.Util.csproj">
      <Project>{2A5DA508-D09F-4DC8-B0D6-E21A6E1A7AE6}</Project>
      <Name>Sistema.Framework.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
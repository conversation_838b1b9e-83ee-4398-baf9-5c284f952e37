﻿using ATS.Domain.Enum;
using System;

namespace ATS.WS.Models.Mobile
{
    public class CheckInMobileModel
    {
        public int IdMotorista   { get; set; }
        public DateTime DataHora { get; set; }
        public decimal? Latitude  { get; set; }
        public decimal? Longitude { get; set; }
        public string CP<PERSON><PERSON>rista { get; set; }
        public string Nome<PERSON><PERSON>rista { get; set; }
        public string Foto { get; set; }
        public string Endereco { get; set; }

        public string Placa { get; set; }
        public decimal? Temperatura { get; set; }
        public decimal? Temperatura2 { get; set; }
        public decimal? Temperatura3 { get; set; }
        public decimal? Velocidade { get; set; }
        public bool? PortaAberta { get; set; }
        public bool VeiculoLigado { get; set; }
        public decimal? PercentualBateria { get; set; }
        public decimal? Autonomia { get; set; }
        public int? IdOperacao { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using System.Data.Entity.Migrations;
using System;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class TipoDocumentoSeeder
    {
        public void Execute(AtsContext context)
        {
            context.TipoDocumento.AddOrUpdate(new[]
            {
                new TipoDocumento { Descricao = "CNH" }
            });
        }
    }
}
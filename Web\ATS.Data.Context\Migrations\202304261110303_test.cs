﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class test : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.PONTOS_ROTA_MODELO", "latitude", c => c.Decimal(precision: 18, scale: 7));
            AddColumn("dbo.PONTOS_ROTA_MODELO", "longitude", c => c.Decimal(precision: 18, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "origemlatitude", c => c.Decimal(precision: 18, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "origemlongitude", c => c.Decimal(precision: 18, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "destinolatitude", c => c.Decimal(precision: 18, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "destinolongitude", c => c.Decimal(precision: 18, scale: 7));
        }
        
        public override void Down()
        {
            DropColumn("dbo.ROTA_MODELO", "destinolongitude");
            DropColumn("dbo.ROTA_MODELO", "destinolatitude");
            DropColumn("dbo.ROTA_MODELO", "origemlongitude");
            DropColumn("dbo.ROTA_MODELO", "origemlatitude");
            DropColumn("dbo.PONTOS_ROTA_MODELO", "longitude");
            DropColumn("dbo.PONTOS_ROTA_MODELO", "latitude");
        }
    }
}

﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using ATS.WS.Models.Common;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Configuration;
using ATS.Application.Interface;

namespace ATS.WS.Services
{
    public class SrvCredenciamento : SrvBase
    {
        private readonly ICredenciamentoApp _credenciamentoApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly INotificacaoApp _notificacaoApp;

        public SrvCredenciamento(ICredenciamentoApp credenciamentoApp, IUsuarioApp usuarioApp, INotificacaoApp notificacaoApp)
        {
            _credenciamentoApp = credenciamentoApp;
            _usuarioApp = usuarioApp;
            _notificacaoApp = notificacaoApp;
        }


        /// <summary>
        ///
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public Retorno<ValidationResult> Add(CredenciamentoModel @params)
        {
            try
            {

                if (@params.IdEstabelecimento == 0)
                    throw new Exception("Informe uma estabelecimento para continuar!");

                if (@params.IdEmpresa == 0)
                    throw new Exception("Informe uma empresa para continuar!");

                Credenciamento credenciamento = new Credenciamento()
                {
                    IdEmpresa = @params.IdEmpresa,
                    IdEstabelecimentoBase = @params.IdEstabelecimento,
                    Status = EStatusCredenciamento.Enviado,
                    StatusDocumentacao = EStatusDocumentacaoCredenciamento.Aguardando,
                    DataSolicitacao = DateTime.Now,
                    DataAtualizacao = DateTime.Now
                };

                var ret = _credenciamentoApp.Add(credenciamento, @params.AdministradoraPlataforma);

                return new Retorno<ValidationResult>(true, ret);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<ValidationResult>($"{nameof(Add)} >> {e.Message}");
            }
        }
        
        public Retorno<ValidationResult> AprovarDocumentacao(int idCredenciamento, int administradoraPlataforma)
        {
            var ret = _credenciamentoApp.AtualizarStatusDocumentacao(idCredenciamento, EStatusDocumentacaoCredenciamento.Regular, administradoraPlataforma);

            return new Retorno<ValidationResult>(ret, null);
        }

        public Retorno<ValidationResult> ReprovarDocumentacao(int idCredenciamento, string motivo, int administradoraPlataforma)
        {
            var ret_ = _credenciamentoApp.AtualizarStatusDocumentacao(idCredenciamento, EStatusDocumentacaoCredenciamento.Irregular, administradoraPlataforma, motivo);

            return new Retorno<ValidationResult>(ret_, null);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public Retorno<ValidationResult> Update(CredenciamentoModel @params)
        {
            try
            {

                if (@params.IdEstabelecimento == 0)
                    throw new Exception("Informe um estabelecimento para continuar!");

                if (@params.IdEmpresa == 0)
                    throw new Exception("Informe uma empresa para continuar!");

                if (!@params.IdCredenciamento.HasValue)
                    throw new Exception("Informe um credenciamento para atualizar");


                var credenciamento = _credenciamentoApp.Get(@params.IdCredenciamento.Value);

                if (credenciamento == null)
                    throw new Exception("Não foi possível identificar o credenciamento pelo código informado. ");

                credenciamento.IdEmpresa = @params.IdEmpresa;
                credenciamento.IdEstabelecimentoBase = @params.IdEstabelecimento;
                
                    credenciamento.Status = EStatusCredenciamento.Enviado;
                credenciamento.StatusDocumentacao = EStatusDocumentacaoCredenciamento.Aguardando;
                    credenciamento.DataSolicitacao = DateTime.Now;
                credenciamento.DataAtualizacao = DateTime.Now;
                
                var ret = _credenciamentoApp.Update(credenciamento, @params.AdministradoraPlataforma);

                return new Retorno<ValidationResult>(true, ret);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<ValidationResult>($"{nameof(Add)} >> {e.Message}");
            }
        }

        public Credenciamento GetCredenciamentoPorProtocolo(int idProtocolo)
        {
            return _credenciamentoApp.GetCredenciamentoPorProtocolo(idProtocolo);
        }

        public byte[] GerarRelatorioGridCredenciamento(int? idEmpresa,
                                        int Take,
                                        int Page,
                                        OrderFilters Order,
                                        List<QueryFilters> Filters, string extensao)
        {
            return _credenciamentoApp.GerarRelatorioGridCredenciamento(idEmpresa, Take, Page, Order, Filters, extensao, GetLogo(idEmpresa));
        }

        //TODO this task must be rewriten in a async way. 
        public void VerificaCredenciamentos(int idUsuario)
        {

            var estabelecimentos_ = _usuarioApp.GetAllChilds(idUsuario);

            if (estabelecimentos_ == null)
                return;

            var idsEstabelecimentos_ = estabelecimentos_.UsuarioEstabelecimentos.Select(x => x.IdEstabelecimento).ToList();

            var credenciamentosIrregulares_ = _credenciamentoApp.GetAllCredenciamentosIrregulares(idsEstabelecimentos_);

            var notificacaoApp_ = _notificacaoApp;
            
            if (credenciamentosIrregulares_)
            {
                var tipoNotificacao = WebConfigurationManager.AppSettings["TipoNotificacaoDocumentacao"];

                if (string.IsNullOrEmpty(tipoNotificacao))
                    return;

                notificacaoApp_.Add(new Notificacao()
                {
                    Conteudo = "Existe credenciamentos com documentação irregular, verifique  os credenciamentos!",
                    IdTipoNotificacao = Convert.ToInt32(tipoNotificacao),  
                    Tipo = 1,
                    Recebida = false, 
                    RecebidaNovo = false, 
                    IdUsuario = idUsuario, 
                    DataHoraEnvio = DateTime.Now
                });
            }
        }
    }
}
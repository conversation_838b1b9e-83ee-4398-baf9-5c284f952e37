﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.Models.Menu;

namespace ATS.Application.Interface
{
    public interface IMenuApp : IAppBase<Menu>
    {
        Menu Get(int id);
        ValidationResult Add(Menu entity);
        ValidationResult Update(Menu entity);
        IQueryable<Menu> All();
        IQueryable<MenuGrid> Consultar(string descricao);
        IQueryable<Menu> GetPorIdGrupoUsuario(int idGrupoUsuario);
        MenusUsuarioDto GetMenusPermitidos(int idUsuario);
        IQueryable<GrupoUsuarioMenuGrid> GetArvoreMenuPorIdGrupoUsuario(int idGrupoUsuario);
        ValidationResult Inativar(int idMenu);
        ValidationResult Reativar(int idMenu);
        Menu GetPorIdentificadorPermissao(int identificadorPermissao);
        IQueryable<GrupoUsuarioMenuGrid> GetMenusPorModulo(int idModulo, int idGrupoUsuario, int idEmpresa, int perfil);
        
        List<GrupoUsuarioMenuGrid> GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idModulo, int idEmpresa, bool verificarAutorizacaoEmpresa);

        object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ValidationResult AlterarStatus(int menuId);
        List<MenusPaiCadastroMenu> GetMenusPai();
        ValidationResult Cadastrar(MenuCadastroRequest request);
        object ObterParaEditar(int id);
    }
}
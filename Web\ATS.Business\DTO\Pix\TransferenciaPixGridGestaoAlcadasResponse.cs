﻿using System;
using System.Collections.Generic;
using System.Globalization;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;

namespace ATS.Domain.DTO.Pix
{
    public class TransferenciaPixGridGestaoAlcadasResponse
    {
        public int totalItems { get; set; }
        public List<TransferenciaPixGridGestaoAlcadasResponseItem> items { get; set; }
    }

    public class TransferenciaPixGridGestaoAlcadasResponseItem
    {
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public int IdEmpresa { get; set; }
        public string NomeEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string CnpjEmpresaFormatado => CnpjEmpresa.FormatarCpfCnpjSafe();
        public string DocumentoProprietario { get; set; }
        public string DocumentoProprietarioFormatado => DocumentoProprietario.FormatarCpfCnpjSafe();
        public string NomeProprietario { get; set; }
        public ETipoEventoViagem TipoEventoEnum { get; set; }
        public string TipoEvento => TipoEventoEnum.GetDescription();
        public string ValorFormatado => ValorDecimal.ToString("C", new CultureInfo("pt-BR"));
        public decimal ValorDecimal { get; set; }
        public string DataCadastroFormatado => DataCadastro.ToString("dd/MM/yyyy HH:mm");
        public DateTime DataCadastro { get; set; }
        public string Ciot { get; set; }
        public string DocumentoCliente { get; set; }
    }
}
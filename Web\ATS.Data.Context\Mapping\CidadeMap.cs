using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CidadeMap : EntityTypeConfiguration<Cidade>
    {
        public CidadeMap()
        {
            ToTable("CIDADE");

            HasKey(t => t.IdCidade);

            Property(t => t.IdCidade)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.IBGE)
                .IsOptional();

            Property(t => t.Latitude)
                .IsOptional();

            Property(t => t.Longitude)
                .IsOptional();

            Property(t => t.DataAlteracao)
                .IsRequired()
                .HasColumnType("datetime2");

            HasRequired(a => a.Estado)
                .WithMany(b => b.Cidades)
                .HasForeignKey(c => c.IdEstado);
        }
    }
}
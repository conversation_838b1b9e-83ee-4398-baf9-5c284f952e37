using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Entities;
using ATS.Domain.Models.Categoria;
using AutoMapper;

namespace ATS.Application.AutoMapper
{
    public class DomainToModelApplicationMappingProfile : Profile
    {
        protected override void Configure()
        {
            VeiculoMap();
        }

        private void VeiculoMap()
        {
            CreateMap<Categoria, CategoriaGetModelResponse>()
                .ForMember(d => d.IdCategoria, opts => opts.MapFrom(s => s.IdCategoria))
                .ForMember(d => d.Descricao, opts => opts.MapFrom(s => s.Descricao))
                .ForMember(d => d.Ativo, opts => opts.MapFrom(s => s.Ativo));

            CreateMap<Veiculo, VeiculoDTO>()
                .ForMember(d => d.NomeFantasiaEmpresa, opts => opts.MapFrom(s => s.Empresa.NomeFantasia))
                .ForMember(d => d.NomeFantasiaFilial, opts => opts.MapFrom(s => s.Filial.NomeFantasia))
                .ForMember(d => d.RazaoSocialFilial, opts => opts.MapFrom(s => s.Filial.RazaoSocial))
                .ForMember(d => d.EmpresaAvisoSonoroFraco, opts => opts.MapFrom(s => s.Empresa.AvisoSonoroFraco))
                .ForMember(d => d.EmpresaAvisoSonoroModerado, opts => opts.MapFrom(s => s.Empresa.AvisoSonoroModerado))
                .ForMember(d => d.EmpresaAvisoSonoroForte, opts => opts.MapFrom(s => s.Empresa.AvisoSonoroForte));
        }
        
    }
}
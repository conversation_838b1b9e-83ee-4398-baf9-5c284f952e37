using System;
using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class CartaoVinculadoPessoaListResponseDto
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public List<CartaoVinculadoPessoaResponseDto> Objeto { get; set; }
    }
    
    public class CartaoVinculadoPessoaResponseDto 
    {
        public int? Identificador { get; set; }
        public DateTime? DataVinculo { get; set; }
        public ProdutoResponseDto Produto { get; set; }
        public int? CartaoMestreId { get; set; }
        public string Status { get; set; } = StatusCartao.Vinculado.ToString();
    }

    public class ProdutoResponseDto
    {
        public int Id { get; set; }
        public string Nome { get; set; }
        public bool IsMultiplasContas { get; set; }
    }
}
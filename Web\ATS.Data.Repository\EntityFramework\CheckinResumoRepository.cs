using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Validation;


namespace ATS.Data.Repository.EntityFramework
{
    public class CheckinResumoRepository : Repository<CheckinResumo>, ICheckinResumoRepository
    {
        private readonly ICheckinResumoDapper _checkinResumoDapper;
        public CheckinResumoRepository(AtsContext context, ICheckinResumoDapper checkinResumoDapper) : base(context)
        {
            _checkinResumoDapper = checkinResumoDapper;
        }

        public ValidationResult Salvar(CheckinResumo checkinResumo)
        {
            try
            {
                Add(checkinResumo);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Editar(CheckinResumo checkinResumo)
        {
            try
            {
                Update(checkinResumo);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public CheckinResumo ConsultarResumoExistente(int? idUsuario, int? idEmpresa)
        {
            return Find(o => o.IdUsuario == idUsuario && o.IdEmpresa == idEmpresa).FirstOrDefault();
        }

        public CheckinResumoConsultaModel ConsultarCheckinResumosPaginado(int empresaId, int itensPorPagina, int pagina, DateTime? dataInicio, DateTime? dataFim)
        {
                var resumos = _checkinResumoDapper
                    .ConsultarCheckinResumosPaginado(empresaId, itensPorPagina, pagina, dataInicio, dataFim);

                return resumos;
        }
    }
}
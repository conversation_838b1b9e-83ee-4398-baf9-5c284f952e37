using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class BloqueioGestorValorMap : EntityTypeConfiguration<BloqueioGestorValor>
    {
        public BloqueioGestorValorMap()
        {
            ToTable("BLOQUEIO_GESTOR_VALOR");

            HasKey(t => t.IdBloqueioGestorValor);

            Property(t => t.IdBloqueioGestorValor)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdBloqueioGestorTipo)
                .HasIndex("BLOQUEIO_GESTOR_VALOR_UK", true, 0);
            Property(t => t.IdEmpresa)
                .HasIndex("BLOQUEIO_GESTOR_VALOR_UK", true, 1);
            Property(t => t.IdFilial)
                .HasIndex("BLOQUEIO_GESTOR_VALOR_UK", true, 2);
            Property(t => t.IdBloqueioOrigemTipo)
                .HasIndex("BLOQUEIO_GESTOR_VALOR_UK", true, 3);
            
            Property(t => t.IdFilial).IsOptional();
            Property(t => t.Valor).IsRequired();

            HasRequired(u => u.BloqueioOrigemTipo)
                .WithMany()
                .HasForeignKey(u => u.IdBloqueioOrigemTipo);
            
            HasRequired(u => u.Empresa)
                .WithMany()
                .HasForeignKey(u => u.IdEmpresa);

            HasOptional(u => u.Filial)
                .WithMany()
                .HasForeignKey(u => u.IdFilial);

            HasRequired(u => u.BloqueioGestorTipo)
                .WithMany()
                .HasForeignKey(u => u.IdBloqueioGestorTipo);

        }
    }
}
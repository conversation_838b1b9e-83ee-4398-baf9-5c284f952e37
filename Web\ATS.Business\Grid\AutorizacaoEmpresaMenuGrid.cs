﻿using System.Collections.Generic;
using ATS.Domain.Entities;

namespace ATS.Domain.Grid
{
    public class AutorizacaoEmpresaMenuGrid
    {
        public int? IdEmpresa { get; set; }
        public int IdMenu { get; set; }
        public string Menu { get; set; }
        public int? IdModulo { get; set; }
        public string <PERSON><PERSON><PERSON> { get; set; }
        public bool Ativo { get; set; }
        public bool HasPermissao { get; set; }
        public int IdTreeViewModulo { get; set; }
        public int IdTreeViewMenu { get; set; }
    }
}

﻿using System;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class BannerUsuarioRepository : Repository<BannerUsuario>, IBannerUsuarioRepository
    {
        public BannerUsuarioRepository(AtsContext context) : base(context)
        {
        }

        public bool NaoMostrarNovamente(int bannerId, int idUsuario)
        {
            var visualizacao = Where(c => c.IdBanner == bannerId && c.IdUsuario == idUsuario).FirstOrDefault();
            if (visualizacao == null) return false;
            return visualizacao.NaoMostrarNovamente;
        }
    }
}
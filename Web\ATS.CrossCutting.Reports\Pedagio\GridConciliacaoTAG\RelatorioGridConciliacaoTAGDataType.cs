﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Pedagio.GridConciliacaoTAG
{
    public class RelatorioGridConciliacaoTagDataType
    {
        public List<RelatorioGridConciliacaoTagItemDataType> items { get; set; }
    }

    public class RelatorioGridConciliacaoTagItemDataType
    { 
        public int? Id { get; set; }
        public string RodagemDupla { get; set; }
        public int? Eixos { get; set; }
        public string RazaoSocial { get; set; }
        public string Ciot { get; set; }
        public string Cnpj { get; set; }
        public string Proprietario { get; set; }
        public string Fornecedor { get; set; }
        public string Valor { get; set; }
        public string DataEmissao { get; set; }
        public string Placa { get; set; }
        public string Mensagem { get; set; }
        public string DistanciaTotal { get; set; }
        public string ViagemId { get; set; }
    }
}
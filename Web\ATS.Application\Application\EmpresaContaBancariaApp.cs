﻿using System;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class EmpresaContaBancariaApp : AppBase, IEmpresaContaBancariaApp
    {
        public ValidationResult Add(EmpresaContaBancaria empresa)
        {
            throw new NotImplementedException();
        }

        public ValidationResult Update(EmpresaContaBancaria empresa)
        {
            throw new NotImplementedException();
        }
    }
}
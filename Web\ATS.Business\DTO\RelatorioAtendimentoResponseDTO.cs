using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class RelatorioAtendimentoResponseDTO
    {
        public int totalItems { get; set; }
        public IList<ConsultaAtendimentoExternalItensResponseDTO> items { get; set; }
    }

    public class ConsultaAtendimentoExternalItensResponseDTO
    {
        public string IdAtendimentoPortador { get; set; }
        public string Cnpjcpf { get; set; }
        public string Observacao { get; set; }
        public string DataInicio { get; set; }
        public string DataFinal { get; set; }
        public string IdUsuario { get; set; }
        public string UsuarioNome { get; set; }
        public string Status { get; set; }
        public string Protocolo { get; set; }
        public string IdMotivoFinalizacaoAtendimento { get; set; }
        public string DescricaoMotivoAtendimento { get; set; }
        public string TotalData { get; set; }
    }

    public class ConsultaAtendimentoInternalResponseDTO
    {
        public string IdAtendimentoPortador { get; set; }
        public string Cnpjcpf { get; set; }
        public string Observacao { get; set; }
        public string DataInicio { get; set; }
        public string DataFinal { get; set; }
        public string IdUsuario { get; set; }
        public string UsuarioNome { get; set; }
        public EStatusAtendimentoPortador Status { get; set; }
        public string Protocolo { get; set; }
        public string IdMotivoFinalizacaoAtendimento { get; set; }
        public string DescricaoMotivoAtendimento { get; set; }
        public string TotalData { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Viagem.ConsultaViagem
{
    public class RelatorioConsultaViagemDataType
    {
        // ReSharper disable once InconsistentNaming
        public IList<RelatorioConsultaViagemItemDataType> items { get; set; }

        // ReSharper disable once InconsistentNaming
        public int totalItems { get; set; }
    }

    public enum ESituacaoRelatorioCiot
    {
        NaoSolicitado = 0,
        Erro = 1,
        Sucesso = 2
    }

    public class RelatorioConsultaViagemItemDataType
    {
        public DateTime DataIntegracao { get; set; }
        public DateTime? DataEmissao { get; set; }
        public string NumeroDocumentoCliente { get; set; }
        public string StatusViagem { get; set; }
        public string ValorAdiantamento { get; set; }
        public string ValorSaldo { get; set; }
        public string ValorTarifas { get; set; }
        public string ValorPedagio { get; set; }
        public ESituacaoRelatorioCiot CIOT { get; set; }
        public string InformacaoCiot { get; set; }
        public string CnpjEmpresa { get; set; }
        public string NomeEmpresa { get; set; }
        public string CnpjFilial { get; set; }
        public string NomeFilial { get; set; }
        public string CnpjCpfClienteOrigem { get; set; }
        public string NomeClienteOrigem { get; set; }
        public string CnpjCpfClienteDestino { get; set; }
        public string NomeClienteDestino { get; set; }
        public string Placa { get; set; }
        public string CnpjCpfProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string RNTRCProprietario { get; set; }
        public string CpfMotorista { get; set; }
        public string StatusVpo { get; set; }
        public string ValorAbastecimento { get; set; }
        public string ValorEstadia { get; set; }
        public string NomeMotorista { get; set; }
        public int IdViagem { get; set; }
        public string Inss { get; set; }
        public string Irrf { get; set; }
        public string Sestsenat { get; set; }
        public string CnpjFornecedorPedagio { get; set; }
        public string NomeFornecedor { get; set; }
        public string NumeroProtocolo { get; set; }
    }
}
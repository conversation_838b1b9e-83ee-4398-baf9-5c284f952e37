using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Application
{
    public class PedagioViagemApp : IPedagioViagemApp
    {
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly ICidadeApp _cidadeApp;

        public PedagioViagemApp(CartoesAppFactoryDependencies cartoesAppFactoryDependencies, ICidadeApp cidadeApp)
        {
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _cidadeApp = cidadeApp;
        }

        public List<ViagemRotaPonto> ObterPontosRota(int empresaId, Guid identificadorHistorico, int quantidadeEixos, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, empresaId, documentoUsuarioAudit ?? CartoesService.AuditDocIntegracao, nomeUsuarioAudit);
            var request = new ConsultaHistoricoRotaRequest
            {
                ConsultaCustoHistoricoId = identificadorHistorico,
                RecalcularValorParaEixos = quantidadeEixos > 0 ? quantidadeEixos : (int?) null,
                IncluirFragmentos = true
            };

            var historicoRota = cartoesApp.ConsultaHistoricoRota(request);

            if (historicoRota?.Fragmentos?.Any() != true)
                return null;
            
            // Como o fragmento representa a rota de um ponto ao outro, pegamos todas as localizações de origem e na última posição pegamos 
            // a localização de destino representando assim a ida do último ponto até a localização de destino
            var listaPontos = new List<ViagemRotaPonto>();
            var indexForeach = 1;
            foreach (var fragmento in historicoRota.Fragmentos)
            {
                int? cidadeOrigemId = null;
                if (fragmento.CidadeOrigemIbge.HasValue)
                {
                    var cidadeId = _cidadeApp.All().Where(o => o.IBGE == fragmento.CidadeOrigemIbge)
                    .Select(o => o.IdCidade).FirstOrDefault();

                    if (cidadeId > 0)
                        cidadeOrigemId = cidadeId;
                }

                listaPontos.Add(new ViagemRotaPonto
                {
                    Latitude = fragmento.LatitudeOrigem,
                    Longitude = fragmento.LongitudeOrigem,
                    IdCidade = cidadeOrigemId,
                    Sequencia = indexForeach
                });

                if (indexForeach == historicoRota.Fragmentos.Count)
                {
                    int? cidadeDestinoId = null;
                    if (fragmento.CidadeDestinoIbge.HasValue)
                    {
                        var cidadeId =  _cidadeApp.All().Where(o => o.IBGE == fragmento.CidadeDestinoIbge)
                            .Select(o => o.IdCidade).FirstOrDefault();

                        if (cidadeId > 0)
                            cidadeDestinoId = cidadeId;
                    }

                    listaPontos.Add(new ViagemRotaPonto
                    {
                        Latitude = fragmento.LatitudeDestino,
                        Longitude = fragmento.LongitudeDestino,
                        IdCidade = cidadeDestinoId,
                        Sequencia = indexForeach
                    });
                }

                indexForeach++;
            }

            return listaPontos;
        }
    }
}
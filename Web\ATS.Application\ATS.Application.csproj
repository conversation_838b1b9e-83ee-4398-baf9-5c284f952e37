﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9938E854-E507-49C3-8ABC-A3E698D34E55}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ATS.Application</RootNamespace>
    <AssemblyName>ATS.Application</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkProfile />
    <LangVersion>9</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=4.2.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.4.2.1\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle, Version=1.8.5.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.NetFramework.1.8.5.2\lib\net20\BouncyCastle.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.2\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.Memory, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Memory.9.0.0\lib\net462\Microsoft.Bcl.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.TimeProvider.8.0.1\lib\net462\Microsoft.Bcl.TimeProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=6.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.8.6.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.8.6.0\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.8.6.0\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.8.6.0\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson">
      <HintPath>..\packages\MongoDB.Bson.3.2.0\lib\net472\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=2.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.2.7.0\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.8.6.0\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.2\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Dynamic">
      <HintPath>..\packages\System.Linq.Dynamic.1.0.7\lib\net40\System.Linq.Dynamic.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.2\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.2\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Application\AdministradoraPlataformaApp.cs" />
    <Compile Include="Application\AtendimentoPortadorApp.cs" />
    <Compile Include="Application\AutenticacaoAplicacaoApp.cs" />
    <Compile Include="Application\AutorizacaoEmpresaApp.cs" />
    <Compile Include="Application\BancoApp.cs" />
    <Compile Include="Application\BannerApp.cs" />
    <Compile Include="Application\BloqueioCartaoTipoApp.cs" />
    <Compile Include="Application\BizWebhookApp.cs" />
    <Compile Include="Application\BloqueioFinanceiroTipoApp.cs" />
    <Compile Include="Application\BloqueioGestorTipoApp.cs" />
    <Compile Include="Application\BloqueioGestorValorApp.cs" />
    <Compile Include="Application\CadastrosApp.cs" />
    <Compile Include="Application\CampanhaApp.cs" />
    <Compile Include="Application\CargaAvulsaApp.cs" />
    <Compile Include="Application\AuthSessionApp.cs" />
    <Compile Include="Application\CargaAvulsaLoteApp.cs" />
    <Compile Include="Application\CategoriaApp.cs" />
    <Compile Include="Application\CheckinResumoApp.cs" />
    <Compile Include="Application\CiotV2App.cs" />
    <Compile Include="Application\ClienteProdutoEspecieApp.cs" />
    <Compile Include="Application\CiotV3App.cs" />
    <Compile Include="Application\CombustivelJSLApp.cs" />
    <Compile Include="Application\Common\BaseApp.cs" />
    <Compile Include="Application\CteApp.cs" />
    <Compile Include="Application\ContratoCiotAgregadoApp.cs" />
    <Compile Include="Application\DeclaracaoCiotApp.cs" />
    <Compile Include="Application\DespesaUsuarioApp.cs" />
    <Compile Include="Application\DespesasViagemApp.cs" />
    <Compile Include="Application\EmpresaContaBancariaApp.cs" />
    <Compile Include="Application\EstabelecimentoBaseContaBancariaApp.cs" />
    <Compile Include="Application\EstabelecimentoBaseDocumentoApp.cs" />
    <Compile Include="Application\EstabelecimentoContaBancariaApp.cs" />
    <Compile Include="Application\ExtratoConsolidadoApp.cs" />
    <Compile Include="Application\ImportacaoDadosApp.cs" />
    <Compile Include="Application\LimiteTransacaoPortadorApp.cs" />
    <Compile Include="Application\LocalizacaoUsuarioApp.cs" />
    <Compile Include="Application\PedagioApp.cs" />
    <Compile Include="Application\PedagioRotaApp.cs" />
    <Compile Include="Application\PedagioViagemApp.cs" />
    <Compile Include="Application\PermissaoCartaoApp.cs" />
    <Compile Include="Application\PlanoApp.cs" />
    <Compile Include="Application\PrestacaoContasApp.cs" />
    <Compile Include="Application\RecursoMobileApp.cs" />
    <Compile Include="Application\ResgateCartaoAtendimentoApp.cs" />
    <Compile Include="Application\RotaModeloApp.cs" />
    <Compile Include="Application\RotasCacheApp.cs" />
    <Compile Include="Application\SerproApp.cs" />
    <Compile Include="Application\TagExtrattaApp.cs" />
    <Compile Include="Application\UsuarioPermissaoCartaoApp.cs" />
    <Compile Include="Application\UsuarioPermissaoFinanceiroApp.cs" />
    <Compile Include="Application\UsuarioPermissoesConcedidasMobileApp.cs" />
    <Compile Include="Application\UsuarioPermissaoGestorApp.cs" />
    <Compile Include="Application\ParametrosApp.cs" />
    <Compile Include="Application\CartoesApp.cs" />
    <Compile Include="Application\CheckApp.cs" />
    <Compile Include="Application\CheckInApp.cs" />
    <Compile Include="Application\ConjuntoApp.cs" />
    <Compile Include="Application\LayoutCartaoApp.cs" />
    <Compile Include="Application\MotivoApp.cs" />
    <Compile Include="Application\PINApp.cs" />
    <Compile Include="Application\ProdutoApp.cs" />
    <Compile Include="Application\TransacaoCartaoApp.cs" />
    <Compile Include="Application\LayoutApp.cs" />
    <Compile Include="Application\UsuarioDocumentoApp.cs" />
    <Compile Include="Application\TipoDocumentoApp.cs" />
    <Compile Include="Application\ValePedagioApp.cs" />
    <Compile Include="Application\ViagemDocumentoFiscalApp.cs" />
    <Compile Include="Application\ViagemEventoProtocoloAnexoApp.cs" />
    <Compile Include="Application\ViagemSolicitacaoAbonoApp.cs" />
    <Compile Include="Application\EnumApp.cs" />
    <Compile Include="Application\ProtocoloApp.cs" />
    <Compile Include="Application\DataMediaServerApp.cs" />
    <Compile Include="Application\Common\AppBase.cs" />
    <Compile Include="Application\EmailApp.cs" />
    <Compile Include="Application\EstabelecimentoBaseApp.cs" />
    <Compile Include="Application\EstabelecimentoBaseProdutoApp.cs" />
    <Compile Include="Application\GrupoUsuarioMenuApp.cs" />
    <Compile Include="Application\EspecieApp.cs" />
    <Compile Include="Application\IconeApp.cs" />
    <Compile Include="Application\DocumentoApp.cs" />
    <Compile Include="Application\CredenciamentoApp.cs" />
    <Compile Include="Application\PagamentoConfiguracaoApp.cs" />
    <Compile Include="Application\PagamentoConfiguracaoProcessoApp.cs" />
    <Compile Include="Application\PagamentoFreteApp.cs" />
    <Compile Include="Application\RotaApp.cs" />
    <Compile Include="Application\NotificacaoApp.cs" />
    <Compile Include="Application\TipoCarretaApp.cs" />
    <Compile Include="Application\TipoCavaloApp.cs" />
    <Compile Include="Application\EstabelecimentoApp.cs" />
    <Compile Include="Application\TipoEstabelecimentoApp.cs" />
    <Compile Include="Application\TipoNotificacaoApp.cs" />
    <Compile Include="Application\ViagemEventoApp.cs" />
    <Compile Include="Application\ViagemEstabelecimentoApp.cs" />
    <Compile Include="Application\WebhookApp.cs" />
    <Compile Include="AutoMapper\DomainToModelApplicationMappingProfile.cs" />
    <Compile Include="Helpers\ValidationResultHelper.cs" />
    <Compile Include="Interface\Common\IBaseApp.cs" />
    <Compile Include="Interface\IAdministradoraPlataformaApp.cs" />
    <Compile Include="Interface\IAtendimentoPortadorApp.cs" />
    <Compile Include="Interface\IAutenticacaoAplicacaoApp.cs" />
    <Compile Include="Interface\IAutorizacaoEmpresaApp.cs" />
    <Compile Include="Interface\IBancoApp.cs" />
    <Compile Include="Interface\IBannerApp.cs" />
    <Compile Include="Interface\IBloqueioCartaoTipoApp.cs" />
    <Compile Include="Interface\IBizWebhookApp.cs" />
    <Compile Include="Interface\IBloqueioFinanceiroTipoApp.cs" />
    <Compile Include="Interface\IBloqueioGestorTipoApp.cs" />
    <Compile Include="Interface\IBloqueioGestorValorApp.cs" />
    <Compile Include="Interface\ICadastrosApp.cs" />
    <Compile Include="Application\ProprietarioApp.cs" />
    <Compile Include="Interface\Common\IAppBase.cs" />
    <Compile Include="Interface\ICampanhaApp.cs" />
    <Compile Include="Interface\ICargaAvulsaApp.cs" />
    <Compile Include="Interface\ICargaAvulsaLoteApp.cs" />
    <Compile Include="Interface\IAuthSessionApp.cs" />
    <Compile Include="Interface\ICartoesApp.cs" />
    <Compile Include="Interface\ICategoriaApp.cs" />
    <Compile Include="Interface\ICheckApp.cs" />
    <Compile Include="Interface\ICheckInApp.cs" />
    <Compile Include="Interface\ICidadeApp.cs" />
    <Compile Include="Interface\ICiotV2App.cs" />
    <Compile Include="Interface\ICiotV3App.cs" />
    <Compile Include="Interface\IClienteProdutoEspecieApp.cs" />
    <Compile Include="Interface\ICombustivelJSLApp.cs" />
    <Compile Include="Interface\IConjuntoApp.cs" />
    <Compile Include="Interface\IConsumoServicoExternoApp.cs" />
    <Compile Include="Interface\ICredenciamentoApp.cs" />
    <Compile Include="Interface\ICteApp.cs" />
    <Compile Include="Interface\IContratoCiotAgregadoApp.cs" />
    <Compile Include="Interface\IDeclaracaoCiotApp.cs" />
    <Compile Include="Interface\IDespesasViagemApp.cs" />
    <Compile Include="Interface\IDespesaUsuarioApp.cs" />
    <Compile Include="Interface\IEstabelecimentoApp.cs" />
    <Compile Include="Interface\IEstabelecimentoBaseApp.cs" />
    <Compile Include="Interface\IEstabelecimentoBaseContaBancariaApp.cs" />
    <Compile Include="Interface\IEstabelecimentoBaseDocumentoApp.cs" />
    <Compile Include="Interface\IEstabelecimentoBaseProdutoApp.cs" />
    <Compile Include="Interface\IEstabelecimentoContaBancariaApp.cs" />
    <Compile Include="Interface\ICheckinResumoApp.cs" />
    <Compile Include="Interface\IExtratoConsolidadoApp.cs" />
    <Compile Include="Interface\IIconeApp.cs" />
    <Compile Include="Interface\IImportacaoDadosApp.cs" />
    <Compile Include="Interface\ILimiteTransacaoPortadorApp.cs" />
    <Compile Include="Interface\ILocalizacaoUsuarioApp.cs" />
    <Compile Include="Interface\IPagamentoFreteApp.cs" />
    <Compile Include="Interface\IPedagioRotaApp.cs" />
    <Compile Include="Interface\IPedagioViagemApp.cs" />
    <Compile Include="Interface\IPermissaoCartaoApp.cs" />
    <Compile Include="Interface\IPinApp.cs" />
    <Compile Include="Interface\IPlanoApp.cs" />
    <Compile Include="Interface\IPrestacaoContasApp.cs" />
    <Compile Include="Interface\IRecursoMobileApp.cs" />
    <Compile Include="Interface\IResgateCartaoAtendimentoApp.cs" />
    <Compile Include="Interface\IRotaApp.cs" />
    <Compile Include="Interface\IRotaModeloApp.cs" />
    <Compile Include="Interface\IRotasCacheApp.cs" />
    <Compile Include="Interface\ITagExtrattaApp.cs" />
    <Compile Include="Interface\ISerproApp.cs" />
    <Compile Include="Interface\ITipoEstabelecimentoApp.cs" />
    <Compile Include="Interface\IUsuarioPermissaoCartaoApp.cs" />
    <Compile Include="Interface\IUsuarioPermissaoFinanceiroApp.cs" />
    <Compile Include="Interface\IUsuarioPermissoesConcedidasMobileApp.cs" />
    <Compile Include="Interface\IEmpresaContaBancariaApp.cs" />
    <Compile Include="Interface\ILayoutApp.cs" />
    <Compile Include="Interface\ILayoutCartaoApp.cs" />
    <Compile Include="Interface\IMotivoApp.cs" />
    <Compile Include="Interface\IUsuarioPermissaoGestorApp.cs" />
    <Compile Include="Interface\IValePedagioApp.cs" />
    <Compile Include="Interface\IViagemEstabelecimentoApp.cs" />
    <Compile Include="Interface\IViagemEventoApp.cs" />
    <Compile Include="Interface\IViagemPendenteGestorApp.cs" />
    <Compile Include="Interface\IParametrosApp.cs" />
    <Compile Include="Interface\IProdutoDadosCargaApp.cs" />
    <Compile Include="Interface\ITipoCavaloClienteApp.cs" />
    <Compile Include="Interface\ITransacaoCartaoApp.cs" />
    <Compile Include="Interface\IUsuarioDocumentoApp.cs" />
    <Compile Include="Interface\ITipoDocumentoApp.cs" />
    <Compile Include="Interface\IClienteApp.cs" />
    <Compile Include="Interface\IEspecieApp.cs" />
    <Compile Include="Interface\IModuloMenuApp.cs" />
    <Compile Include="Interface\IEmailApp.cs" />
    <Compile Include="Interface\IDocumentoApp.cs" />
    <Compile Include="Interface\IPagamentoConfiguracaoApp.cs" />
    <Compile Include="Interface\IPagamentoConfiguracaoProcessoApp.cs" />
    <Compile Include="Interface\IProdutoApp.cs" />
    <Compile Include="Interface\INotificacaoApp.cs" />
    <Compile Include="Interface\IMensagemApp.cs" />
    <Compile Include="Interface\IMensagemGrupoUsuarioApp.cs" />
    <Compile Include="Interface\IEmpresaApp.cs" />
    <Compile Include="Interface\IEstadoApp.cs" />
    <Compile Include="Interface\IFilialApp.cs" />
    <Compile Include="Interface\IGrupoUsuarioApp.cs" />
    <Compile Include="Interface\IGrupoUsuarioMenuApp.cs" />
    <Compile Include="Interface\IMenuApp.cs" />
    <Compile Include="Interface\IModuloApp.cs" />
    <Compile Include="Interface\IEmpresaModuloApp.cs" />
    <Compile Include="Interface\IMotoristaApp.cs" />
    <Compile Include="Interface\IMotoristaMovelApp.cs" />
    <Compile Include="Interface\IPaisApp.cs" />
    <Compile Include="Interface\IProprietarioApp.cs" />
    <Compile Include="Interface\ITipoCarretaApp.cs" />
    <Compile Include="Interface\ITipoCavaloApp.cs" />
    <Compile Include="Interface\ITipoCombustivelApp.cs" />
    <Compile Include="Interface\IUsuarioApp.cs" />
    <Compile Include="Interface\IUsuarioFilialApp.cs" />
    <Compile Include="Interface\IVeiculoCombustivelApp.cs" />
    <Compile Include="Interface\IVeiculoConjuntoApp.cs" />
    <Compile Include="Interface\IVeiculoApp.cs" />
    <Compile Include="Interface\IVeiculosHistoricoEmpresaApp.cs" />
    <Compile Include="Interface\IViagemApp.cs" />
    <Compile Include="Application\CidadeApp.cs" />
    <Compile Include="Application\ClienteApp.cs" />
    <Compile Include="Application\MensagemApp.cs" />
    <Compile Include="Application\EstadoApp.cs" />
    <Compile Include="Application\FilialApp.cs" />
    <Compile Include="Application\EmpresaApp.cs" />
    <Compile Include="Application\GrupoUsuarioApp.cs" />
    <Compile Include="Application\MenuApp.cs" />
    <Compile Include="Application\ModuloApp.cs" />
    <Compile Include="Application\EmpresaModuloApp.cs" />
    <Compile Include="Application\MotoristaApp.cs" />
    <Compile Include="Application\PaisApp.cs" />
    <Compile Include="Application\UsuarioFilialApp.cs" />
    <Compile Include="Application\VeiculoApp.cs" />
    <Compile Include="Interface\IViagemDocumentoFiscalApp.cs" />
    <Compile Include="Interface\IViagemEventoProtocoloAnexoApp.cs" />
    <Compile Include="Interface\IViagemSolicitacaoAbonoApp.cs" />
    <Compile Include="Interface\IViagemVirtualApp.cs" />
    <Compile Include="Interface\IWebhookApp.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Application\TipoCombustivelApp.cs" />
    <Compile Include="Application\UsuarioApp.cs" />
    <Compile Include="Application\ViagemApp.cs" />
    <Compile Include="WS\MobilePush.cs" />
    <Compile Include="WS\PushResponse.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Business\ATS.Domain.csproj">
      <Project>{2810acec-3610-4aad-8e20-d01e14466d87}</Project>
      <Name>ATS.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.CrossCutting.Reports\ATS.CrossCutting.Reports.csproj">
      <Project>{3f484548-f9a3-4e22-a74d-8dd198c7abab}</Project>
      <Name>ATS.CrossCutting.Reports</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Data.Repository.External\ATS.Data.Repository.External.csproj">
      <Project>{8a62a98c-9d32-48ee-b100-0259b1fdf08a}</Project>
      <Name>ATS.Data.Repository.External</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Data.Repository\ATS.Data.Repository.csproj">
      <Project>{07EC0440-CCCD-4ED5-8564-8F6EEBBC12C6}</Project>
      <Name>ATS.Data.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.DataMedia.Context\ATS.MongoDB.Context.csproj">
      <Project>{6E8229CB-290B-48E6-A229-26C2E1A8F422}</Project>
      <Name>ATS.MongoDB.Context</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sistema.Framework\Sistema.Framework.Util\Sistema.Framework.Util.csproj">
      <Project>{2a5da508-d09f-4dc8-b0d6-e21a6e1a7ae6}</Project>
      <Name>Sistema.Framework.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Container\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
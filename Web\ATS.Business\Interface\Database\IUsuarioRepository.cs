﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using System.Linq;
using System;
using System.Collections.Generic;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Models.Usuario;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Database
{
    public interface IUsuarioRepository : IRepository<Usuario>
    {
        Usuario GetAllChilds(int id);
        IQueryable<Usuario> Consultar(string nome, int? idEmpresa);
        bool ExisteUsuarioAtivoComCpf(string cpfCnpjDesabilitado);
        Usuario GetUsuarioBase(int id);

        IEnumerable<ConsultaVistoriadores> ConsultarVistoriadores(string cpfUsuario);
        void AtualizarDataUltimoAcesso(int idUsuario, ETipoAcessoSistema tipoAcesso);
        UsuarioMicroServicoInstanciaAppDto UsuarioMicroServicoInstanciaApp(int idUsuario);
        UsuarioFotoDto GetFotoUsuario(string cpfcnpj);

        ValidationResult AtualizarDataUltimaAberturaAplicativo(int usuarioId);
        ConsultaInformacoesMobileModel ConsultarInformacoesMobile(int itensPorPagina, int pagina, int? empresaId, string documento);
        int? GetGrupoUsuario(int idUsuario);
        
        /// <summary>
        /// Retorn os dados completos do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        Usuario GetWithRelationships(int id);

        EPerfil GetPerfil(int idUsuario);
        IQueryable<Usuario> GetPorGrupoUsuario(int idGrupoUsuario);
        int? GetIdPorCNPJCPF(string nCNPJCPF, int? idEmpresa = null);
        Usuario GetPorCNPJCPF(string nCNPJCPF, bool nWithIncludes = true, int? idEmpresa = null);
        int? GetIdEmpresa(int id);
        int? GetIdEmpresa(string cpf);
        string GetCNPJCPF(int idUsuario);
        int? GetGrupoUsuarioInativo(int idUsuario);
    }
}
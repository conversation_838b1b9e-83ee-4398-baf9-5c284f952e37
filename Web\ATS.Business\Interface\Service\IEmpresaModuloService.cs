﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IEmpresaModuloService
    {
        /// <summary>
        /// Método utilizado por listar ModuloEmpresa
        /// </summary>
        /// <param name="idEmpresa">id de Empresa</param>
        /// <returns>IQueryable de ModuloEmpresa</returns>
        IQueryable<EmpresaModulo> ListarModuloPorIdEmpresa(int? idEmpresa);

        /// <summary>
        /// Validar os modulos do empresa
        /// </summary>
        /// <param name="empresa"></param>
        /// <returns></returns>
        IEnumerable<ValidationError> IsValid(Empresa empresa);
    }
}
﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.Models.Webservice.Response.Proprietario;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class ProprietarioController : BaseApiController<IProprietarioApp>
    {
        private readonly IProprietarioApp _proprietarioApp;
        private readonly SrvProprietario _srvProprietario;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public ProprietarioController(BaseControllerArgs baseArgs, IProprietarioApp app, IProprietarioApp proprietarioApp, SrvProprietario srvProprietario, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs, app)
        {
            _proprietarioApp = proprietarioApp;
            _srvProprietario = srvProprietario;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        /// <summary>
        /// Integrar o proprietario
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Integrar(ProprietarioIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvProprietario.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Integrar o proprietario
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Alterar(ProprietarioIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvProprietario.Editar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Consultar(string token, string cnpjAplicacao, string cnpjcpfProprietario)
        {
            try
            {
                if(!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProprietario.Consultar(token, cnpjAplicacao, cnpjcpfProprietario));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarPorRntrc(string token, string cnpjAplicacao, string rntrc)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProprietario.ConsultarTransportadorPorRntrc(token, cnpjAplicacao, rntrc));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Consultar dados para identificar se a próxima integração de viagem do sistema para o proprietário irá gerar de saque/transferência da ANTT.
        /// Caso o proprietário necessitar declarar CIOT, será retornado o valor da tarifa.
        /// Utilizado este método caso o cliente necessitar contabilizar este valor para algum processo prévio a integração da viagem, como calculo de impostos.
        /// </summary>
        /// <param name="token"></param>
        /// <param name="cnpjAplicacao"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <param name="cnpjCpfProprietario"></param>
        /// <param name="rntrc"></param>
        /// <param name="placa"></param>
        /// <returns></returns>
        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarTarifaAnttProximaViagem(string token, string cnpjAplicacao, string cnpjEmpresa, string cnpjCpfProprietario, string rntrc, string placa)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(
                    _srvProprietario.ConsultarTarifaAnttProximaViagem(
                        token, cnpjAplicacao, cnpjEmpresa, cnpjCpfProprietario, rntrc, placa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Parametros relacionado ao cartão do proprietário
        /// </summary>
        /// <param name="token"></param>
        /// <param name="cnpjAplicacao"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <param name="cnpjCpfProprietario"></param>
        /// <returns></returns>
        [HttpGet]
        [EnableLogRequest]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public JsonResult ConsultarParametrosCartaoProprietario(string token, string cnpjAplicacao, string cnpjEmpresa, string cnpjCpfProprietario)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(
                    _srvProprietario
                        .ConsultarParametrosCartaoProprietario(token, cnpjAplicacao, cnpjEmpresa, cnpjCpfProprietario));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarParametrosCartao(string token, string cnpjAplicacao, string cnpjEmpresa, string cnpjCpfProprietario)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(
                    _srvProprietario
                        .ConsultarParametrosCartaoProprietario(token, cnpjAplicacao, cnpjEmpresa, cnpjCpfProprietario));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Atualizar parâmetros relacionado ao cartão do proprietário
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [AutorizarMobile]
        [EnableLogRequest]
        [Expor(EApi.Mobile)]
        public JsonResult AtualizarParametrosCartaoProprietario(AtualizarParametroCartaoProprietarioRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvProprietario
                    .AtualizarParametrosCartaoProprietario(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AtualizarParametrosCartao(AtualizarParametroCartaoProprietarioRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvProprietario
                    .AtualizarParametrosCartaoProprietario(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AtualizarBaseEquiparadoTac(string cnpjAplicacao, string token, string cnpjcpf = null)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProprietario.AtualizarBaseEquiparadoTac(cnpjcpf));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult AtualizarCadastroServicoCartao(string cnpjAplicacao, string token, List<string> cnpjcpf)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_srvProprietario
                    .AtualizarCadastroServicoCartao(cnpjcpf));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarSituacaoAntt(ConsultarSituacaoAnttRequest request)
        {
            try
            {
                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ConsultaSituacaoTransportadorResponse(false, validacaoChamada.ToString()));

                var resposta = _proprietarioApp.ConsultarSituacaoAntt(request.CpfCnpj, request.CNPJAplicacao);
                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarSituacaoVeiculosAntt(ConsultarSituacaoVeiculosAnttRequest request)
        {
            try
            {
                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ConsultarSituacaoVeiculosAnttResponse(false, validacaoChamada.ToString()));

                var CNPJEmpresaAplicacao = string.IsNullOrWhiteSpace(request.CNPJEmpresa) ? request.CNPJAplicacao : request.CNPJEmpresa;

                var retorno =
                    _srvProprietario.ConsultarSituacaoVeiculosAntt(
                        CNPJEmpresaAplicacao, request.CpfCnpjProprietario, request.Placas);

                return Responde(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult PercentuaisTransferencias(PercentualProprietarioRequestDTO request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var resposta = _srvProprietario.SetProprietarioPercentual(request);
                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult PercentuaisTransferenciasMotoristas(PercentualProprietarioMotoristaRequestDTO request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var resposta = _srvProprietario.SetProprietarioMotoristaPercentual(request);
                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult IntegrarProprietarioFila(ProprietarioIntegrarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return TokenInvalido();

                return Responde(_srvProprietario.Integrar(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
    }
}
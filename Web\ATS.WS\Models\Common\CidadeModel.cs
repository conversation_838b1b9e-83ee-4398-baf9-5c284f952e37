﻿using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class CidadeModel
    {
        public int IdCidade      { get; set; }
        public int IdPais        { get; set; }
        public int IdEstado      { get; set; }
        public string Nome       { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IBGE         { get; set; }
        public decimal Latitude  { get; set; }
        public decimal Longitude { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual EstadoModel Estado { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual PaisModel Pais { get; set; }
    }
}
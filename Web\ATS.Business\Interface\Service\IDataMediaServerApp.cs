﻿using ATS.MongoDB.Context.Entities;
using MongoDB.Bson;

namespace ATS.Domain.Interface.Service
{
    public interface IDataMediaServerApp
    {
        ObjectId Add(int type, string base64Data, string fileName, string mimeType = "");
        Media GetMedia(string _id);
        void DeleteByToken(string token);
        object VisualizarMedia(string token);
        string GetIconeTipoEstabelecimentoCacheable(string mediaId);
    }
}
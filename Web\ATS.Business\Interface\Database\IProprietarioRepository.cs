﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Linq;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Enum;

namespace ATS.Domain.Interface.Database
{
    public interface IProprietarioRepository : IRepository<Proprietario>
    {
        IQueryable<Proprietario> Consultar(string razaoSocial, int idEmpresa, bool? onlyAtivo);
        Proprietario GetProprietario(int idEmpresa, string cpfCNPJEmpresa);
        ProprietarioAnttDto GetDadosProprietarioAntt(string cnpjCpf, string cnpjEmpresa);
        ProprietarioAnttDto GetDadosProprietarioAntt(int idProprietario);
        bool Any(string cnpjCpf, int idEmpresa);
        ETipoContrato GetTipoContrato(string cpfCnpj, int idEmpresa);
        Proprietario GetPorCpfCnpj(string cpfCnpj, int? idEmpresa = null);
        Proprietario GetPorCpfCnpj(string cpf, List<string> cnpjs);
        int? GetIdPorCpfCnpj(string cpfCnpj, int? idEmpresa = null);
        Proprietario GetById(int idProprietario);
        IQueryable<Proprietario> AllAtivos();
        bool PermiteTransferenciaSemCartaFrete(string cnpjCpf);
        IQueryable<Proprietario> GetQuery(int idProprietario);
    }
}
﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Linq;

namespace ATS.Domain.Service
{
    public class AutenticacaoAplicacaoService : ServiceBase, IAutenticacaoAplicacaoService
    {
        private readonly IAutenticacaoAplicacaoRepository _autenticacaoAplicacaoRepository;

        public AutenticacaoAplicacaoService(IAutenticacaoAplicacaoRepository autenticacaoAplicacaoRepository)
        {
            _autenticacaoAplicacaoRepository = autenticacaoAplicacaoRepository;
        }

        public bool AcessoConcedido(string cnpjAplicacao, string token)
        {
            return _autenticacaoAplicacaoRepository.AcessoConcedido(cnpjAplicacao, token);
        }

        public IQueryable<AutenticacaoAplicacao> GetAutenticacaoAplicacaoPorCNPJAplicacao(string cnpjAplicacao, string token)
        {
            return _autenticacaoAplicacaoRepository.GetAutenticacaoAplicacaoPorCNPJAplicacao(cnpjAplicacao, token);
        }

        public AutenticacaoAplicacao Get(string cnpjAplicacao)
        {
            return _autenticacaoAplicacaoRepository.Get(cnpjAplicacao);
        }

        public IQueryable<AutenticacaoAplicacao> GetPorIdEmpresa(int idEmpresa)
        {
            return _autenticacaoAplicacaoRepository
                .Find(x => x.IdEmpresa == idEmpresa);
        }
    }
}

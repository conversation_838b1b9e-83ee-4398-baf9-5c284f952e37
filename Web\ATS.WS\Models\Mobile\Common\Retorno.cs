﻿using ATS.Domain.Enum;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.WS.Models.Mobile.Common
{
    public class Retorno
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public List<Fault> Faults { get; set; }

        public Retorno()
        {
        }

        public Retorno(string mensagem)
        {
            Mensagem = mensagem;
        }

        public Retorno(bool sucesso)
        {
            Sucesso = sucesso;
        }

        public Retorno(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public Retorno(ValidationResult validationResult)
        {
            Sucesso = validationResult.IsValid;
            Mensagem = validationResult.ToString();

            if (!validationResult.IsValid)
                Faults = validationResult.GetFaults();
        }

        public Retorno(ValidationResult validationResult, string mensagem)
        {
            Sucesso = validationResult.IsValid;
            Mensagem = mensagem;

            if (!validationResult.IsValid)
                Faults = validationResult.GetFaults();
        }
    }

    public class Retorno<T>
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public T Objeto { get; set; }
        public List<Fault> Faults { get; set; }

        public Retorno()
        {
        }

        public Retorno(string mensagem)
        {
            Mensagem = mensagem;
        }

        public Retorno(bool sucesso)
        {
            Sucesso = sucesso;
        }

        public Retorno(bool sucesso, T retorno)
        {
            Sucesso = sucesso;
            Objeto  = retorno;
        }

        public Retorno(bool sucesso, string mensagem, T retorno)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
            Objeto = retorno;
        }

        public Retorno(ValidationResult validationResult, T retorno)
        {
            Sucesso = validationResult.IsValid ;
            Mensagem = "";
            Objeto = retorno;

            if (!validationResult.IsValid)
                Faults = validationResult.GetFaults();
        }

        public Retorno(ValidationResult validationResult, T retorno, string mensagem)
        {
            Sucesso = validationResult.IsValid ;
            Mensagem = mensagem;
            Objeto = retorno;

            if (!validationResult.IsValid)
                Faults = validationResult.GetFaults();
        }
    }

    public class Retorno<T, TValidationType>
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public T Objeto { get; set; }
        public List<Fault> Faults { get; set; }

        public Retorno()
        {
        }

        public Retorno(string mensagem)
        {
            Mensagem = mensagem;
        }

        public Retorno(bool sucesso)
        {
            Sucesso = sucesso;
        }

        public Retorno(bool sucesso, T retorno)
        {
            Sucesso = sucesso;
            Objeto  = retorno;
        }

        public Retorno(bool sucesso, string mensagem, T retorno)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
            Objeto = retorno;
        }

        public Retorno(ValidationResult validationResult, T retorno)
        {
            Sucesso = validationResult.IsValid ;
            Mensagem = "";
            Objeto = retorno;

            if (!validationResult.IsValid)
                Faults = validationResult.GetFaults();
        }
    }
}
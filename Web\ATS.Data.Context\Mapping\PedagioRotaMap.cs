using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class PedagioRotaMap: EntityTypeConfiguration<PedagioRota>
    {
        public PedagioRotaMap()
        {
            ToTable("PEDAGIO_ROTA");
            HasKey(t => t.IdPedagioRota);

            Property(t => t.Descricao).HasMaxLength(300).IsRequired();
            Ignore(t => t.UsuarioCadastro);
            Ignore(t => t.Usuario<PERSON>tuali<PERSON>);

            HasMany(c => c.Pont<PERSON>)
                .WithRequired(c => c.PedagioRota)
                .HasForeignKey(c => c.IdPedagioRota);

            HasRequired(c => c.Empresa)
                .WithMany()
                .HasForeignKey(c => c.IdEmpresa);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Client.Response.Base;
using ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Interfaces;
using NLog;
using TagExtrattaClient;


namespace ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG
{
    public class TagExtrattaExternalRepository : ITagExtrattaExternalRepository,IDisposable
    {
        private HttpClient _httpClient;
        private readonly IUserIdentity _identity;
        private TagExtrattaClient.Client _client;
        public TagExtrattaExternalRepository(IUserIdentity identity)
        {
            _identity = identity;
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(Convert.ToInt32(WebConfigurationManager.AppSettings["TimeoutTagExtratta"] ?? "50"));
            _client = new TagExtrattaClient.Client(WebConfigurationManager.AppSettings["UrlApiExtrattaTag"],_httpClient);
        }

        public TagExtrattaClientResponse<RemessasGetModelResponse> GetRemessas(int? take, int? page, List<ApiQueryFilters> filters,EOperadorOrder order,string campo)
        {
            try
            {
                var req = new GetRemessasRequest
                {
                    Take = take,
                    Page = page,
                    Filter = filters,
                    Order = new ApiOrderFilters
                    {
                        Campo = campo,
                        Operador = order
                    }
                };
                
                var result = _client
                    .List2Async(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,req)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<RemessasGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                            $"{msgError} " +
                            $"/ {e.StatusCode} " +
                            $"/ {e.Response}");
                
                return TagExtrattaClientResponse<RemessasGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<TagsGetModelResponse> GetTags(int? take, int? page, List<ApiQueryFilters> filters,EOperadorOrder order,string campo, List<long> tagsSelecionadas = null, DateTime? dataInicio = null, DateTime? dataFim  = null)
        {
            try
            {
                var req = new GetTagsRequest()
                {
                    Take = take,
                    Page = page,
                    Filter = filters,
                    Order = new ApiOrderFilters
                    {
                        Campo = campo,
                        Operador = order
                    },
                    TagsParaNaoIncluir = tagsSelecionadas,
                    DataInicio = dataInicio,
                    DataFim = dataFim
                };
                
                var result = _client
                    .List3Async(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil, req)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<TagsGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<TagsGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<TagGetSerialModelResponse> GetTagSerial(long serial,int? statusTag)
        {
            try
            {
                var result = _client
                    .SerialAsync(serial,statusTag, _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<TagGetSerialModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<TagGetSerialModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> ReceberRemessa(int id)
        {
            try
            {
                 _client
                    .ReceberAsync(id,_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> CadastrarRemessa(RemessaCadastrarModelRequest request)
        {
            try
            {
                 _client
                     .RemessaPOSTAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid();
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }

        public TagExtrattaClientResponse<List<TagGetModelResponse>> GetTagsLote(long min,long max,int? statusTag)
        {
            try
            {
                var result = _client
                    .LoteAsync(min,max,statusTag.GetHashCode(),_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult()
                    .ToList();
                
                return TagExtrattaClientResponse<List<TagGetModelResponse>>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<List<TagGetModelResponse>>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<RemessaGetModelResponse> GetRemessa(int id)
        {
            try
            {
                var result = _client
                    .RemessaGETAsync(id,_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<RemessaGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<RemessaGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<VeiculoGetModelResponse> GetVeiculo(string placa)
        {
            try
            {
                var result = _client
                    .VeiculoAsync(placa,_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<VeiculoGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<VeiculoGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<ModelosVeiculoMoveMaisGetModelResponse> GetModelosMoveMais(GetModelosVeiculoMoveMaisRequest request)
        {
            try
            {
                var result = _client
                    .ModelosAsync( _identity.IdUsuario, _identity.IdEmpresa,_identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<ModelosVeiculoMoveMaisGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<ModelosVeiculoMoveMaisGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }

        public TagExtrattaClientResponse<string> Vincular(TagVincularModelRequest request,long serial,string placa)
        {
            try
            {
                _client
                    .VincularAsync(serial, placa, _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil, request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> Bloquear(long serial)
        {
            try
            {
                _client
                    .BloquearAsync(serial, _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> Desbloquear(long serial)
        {
            try
            {
                _client
                    .DesbloquearAsync(serial, _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> Desvincular(long serial)
        {
            try
            {
                _client
                    .DesvincularAsync(serial, _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> CadastrarBloqueios(BloqueioCadastrarModelRequest request)
        {
            try
            {
                _client
                    .BloqueioPOSTAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<BloqueioGetModelResponse> GetBloqueios(int usuarioId)
        {
            try
            {
                var result = _client
                    .BloqueioGETAsync(usuarioId,_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<BloqueioGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<BloqueioGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<ConsultarGridValePedagioHubModelResponse> GridValePedagioHub(int? take, int? page, List<ApiQueryFilters> filters,EOperadorOrder order,string campo,DateTime? dataInicio,DateTime? dataFim)
        {
            try
            {
                var req = new GridValePedagioHubRequest()
                {
                    Take = take,
                    Page = page,
                    Filter = filters,
                    Order = new ApiOrderFilters
                    {
                        Campo = campo,
                        Operador = order,
                    },
                    DataInicio = dataInicio,
                    DataFim = dataFim
                };
                
                var result = _client
                    .GridValePedagioHubAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil, req)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<ConsultarGridValePedagioHubModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<ConsultarGridValePedagioHubModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }

        public TagExtrattaClientResponse<string> CadastrarModelosMoveMais()
        {
            try
            {
                _client
                    .Modelos2Async( _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<string> CadastrarEstoqueTags()
        {
            try
            {
                _client
                    .TagPOSTAsync( _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<Guid> NotificarPassagemPraca(PassagemPracaPedagioModelRequest request)
        {
            try
            {
                var result = _client
                    .PassagemAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<Guid>.Valid(result);
            }
            catch (ApiException<ICollection<Guid>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<Guid>.Error(e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<PagamentosGetModelResponse> GetPagamentos(int? take, int? page, List<ApiQueryFilters> filters,EOperadorOrder order,string campo,DateTime? dataInicio,DateTime? dataFim, int? idEmpresa,FornecedorTagEnum fornecedor)
        {
            try
            {
                var req = new PagamentosGetRequest()
                {
                    Take = take,
                    Page = page,
                    Filter = filters,
                    Order = new ApiOrderFilters
                    {
                        Campo = campo,
                        Operador = order,
                    },
                    DataInicio = dataInicio,
                    DataFim = dataFim,
                    IdEmpresa = idEmpresa,
                    FornecedorTag = fornecedor
                };

                var result = _client
                    .PagamentosPOSTAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil, req)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<PagamentosGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<PagamentosGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }
        }

        public TagExtrattaClientResponse<PagamentosItemGetModelResponse> GetPagamento(long id)
        {
            try
            {
                var result = _client
                    .PagamentosGETAsync(id,_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<PagamentosItemGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<PagamentosItemGetModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }
        }

        public TagExtrattaClientResponse<string> PagamentoManualEventoTag(PagamentoManualRequest request)
        {
            try
            {
                _client
                    .PagamentoManualAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }
        }

        public TagExtrattaClientResponse<string> EstornoManualEventoTag(PagamentoManualRequest request)
        {
            try
            {
                _client
                    .EstornoManualAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }
        }

        public TagExtrattaClientResponse<PassagensPracaGetModelResponse> GetPassagensPraca(int? take, int? page,
            List<ApiQueryFilters> filters,EOperadorOrder order,string campo,DateTime? dataInicio,DateTime? dataFim,int? empresaId)
        {
            try
            {
                var req = new GetPassagensPracaRequest()
                {
                    Take = take,
                    Page = page,
                    Filter = filters,
                    Order = new ApiOrderFilters
                    {
                        Campo = campo,
                        Operador = order,
                    },
                    DataInicio = dataInicio,
                    DataFim = dataFim,
                    EmpresaId = empresaId
                };

                var result = _client
                    .ListAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,req)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<PassagensPracaGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<Guid>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<PassagensPracaGetModelResponse>.Error(e.Message,e.StatusCode);
            }
        }

        public void Dispose()
        {
            _httpClient.Dispose();
        }
        
        public TagExtrattaClientResponse<string> DesvincularEmpresa(long serial)
        {
            try
            {
                _client
                    .DesvincularEmpresaAsync(serial, _identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();
                
                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();
                
                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");
                
                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault() ?? e.Message,e.StatusCode);
            }  
        }
        
        public TagExtrattaClientResponse<FaturamentoGetModelResponse> GetFaturamento(int? take, int? page,
            List<ApiQueryFilters> filters,EOperadorOrder order,string campo,DateTime dataInicio,DateTime dataFim,FornecedorTagEnum fornecedor)
        {
            try
            {
                var req = new FaturamentoGetRequest()
                {
                    Take = take,
                    Page = page,
                    Filter = filters,
                    Order = new ApiOrderFilters
                    {
                        Campo = campo,
                        Operador = order,
                    },
                    DataInicio = dataInicio,
                    DataFim = dataFim,
                    FornecedorTag = fornecedor
                };

                var result = _client
                    .FaturamentoAsync(_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil,req)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<FaturamentoGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<Guid>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<FaturamentoGetModelResponse>.Error(e.Message,e.StatusCode);
            }
        }

        public TagExtrattaClientResponse<FaturamentoTotalizadorGetModelResponse> GetTotalizadorFaturamento(FaturamentoTotalizadorGetRequest request)
        {
            try
            {
                var result = _client
                    .TotalizadorAsync(_identity.IdUsuario, _identity.IdEmpresa, _identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<FaturamentoTotalizadorGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<Guid>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<FaturamentoTotalizadorGetModelResponse>.Error(e.Message, e.StatusCode);
            }
        }
        
        public TagExtrattaClientResponse<FaturaGetModelResponse> GetFatura(DateTime dataInicio,DateTime dataFim,int empresaId, FornecedorTagEnum fornecedorTag)
        {
            try
            {
                var result = _client
                    .FaturaAsync(dataInicio,dataFim,empresaId,fornecedorTag,_identity.IdUsuario, _identity.IdEmpresa, _identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<FaturaGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<Guid>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<FaturaGetModelResponse>.Error(e.Message, e.StatusCode);
            }
        }

        public TagExtrattaClientResponse<ConsultarPlacaFornecedorResponse> ConsultarPlacasFornecedor(string placa, FornecedorTagEnum fornecedor)
        {
            try
            {
                var result =_client.PlacafornecedorAsync(placa, fornecedor, _identity.IdUsuario, _identity.IdEmpresa, _identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<ConsultarPlacaFornecedorResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e, $"ExtrattaTAG: " +
                    $"{msgError} " +
                    $"/ {e.StatusCode} " +
                    $"/ {e.Response}" +
                    $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<ConsultarPlacaFornecedorResponse>.Error(e.Result.FirstOrDefault() ?? e.Message, e.StatusCode);
            }
        }
        
        public TagExtrattaClientResponse<PassagensPedagioCompraHubGetModelResponse> GetPassagensPedagioCompraHub(int compraId, FornecedorTagEnum fornecedor)
        {
            try
            {
                var result = _client
                    .PassagensValePedagioHubAsync(compraId,fornecedor,_identity.IdUsuario, _identity.IdEmpresa, _identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<PassagensPedagioCompraHubGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<Guid>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                      $"{msgError} " +
                      $"/ {e.StatusCode} " +
                      $"/ {e.Response}" +
                      $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<PassagensPedagioCompraHubGetModelResponse>.Error(e.Message, e.StatusCode);
            }
        }
        
        public TagExtrattaClientResponse<string> ContestarPassagem(ContestacaoPassagemPedagioModelRequest request)
        {
            try
            {
                _client
                    .ContestarPassagemAsync(_identity.IdUsuario, _identity.IdEmpresa, _identity.Perfil,request)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<string>.Valid("OK");
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e,$"ExtrattaTAG: " +
                      $"{msgError} " +
                      $"/ {e.StatusCode} " +
                      $"/ {e.Response}" +
                      $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<string>.Error(e.Result.FirstOrDefault(), e.StatusCode);
            }
        }
        
        public TagExtrattaClientResponse<ConsultarSaldoVeiculoValePedagioModelResponse> ConsultarSaldoValePedagioVeiculo(string placa)
        {
            try
            {
                var result =_client.ValePedagioAsync(placa, _identity.IdUsuario, _identity.IdEmpresa, _identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<ConsultarSaldoVeiculoValePedagioModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                var msgError = e.Message + e.Result.FirstOrDefault();

                LogManager.GetCurrentClassLogger().Error(
                    e, $"ExtrattaTAG: " +
                       $"{msgError} " +
                       $"/ {e.StatusCode} " +
                       $"/ {e.Response}" +
                       $"{nameof(TagExtrattaExternalRepository)}");

                return TagExtrattaClientResponse<ConsultarSaldoVeiculoValePedagioModelResponse>.Error(e.Result.FirstOrDefault() ?? e.Message, e.StatusCode);
            }
        }
        
        public TagExtrattaClientResponse<PassagensPracaVeiculoGetModelResponse> GetPassagensVeiculoPraca(DateTime? dataInicio,DateTime? dataFim,string placa)
        {
            try
            {
                var result = _client
                    .PassagensVeiculoAsync(dataInicio,dataFim,placa,_identity.IdUsuario,_identity.IdEmpresa,_identity.Perfil)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                return TagExtrattaClientResponse<PassagensPracaVeiculoGetModelResponse>.Valid(result);
            }
            catch (ApiException<ICollection<string>> e)
            {
                return TagExtrattaClientResponse<PassagensPracaVeiculoGetModelResponse>.Error(e.Result.FirstOrDefault(),e.StatusCode);
            }
        }
    }
}
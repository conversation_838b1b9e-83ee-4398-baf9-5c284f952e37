﻿using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Models.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Usuario;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Web.Mvc;
using ATS.Domain.Models.AtendimentoPortador;
using ATS.Domain.Validation;
using ATS.Domain.Interface.Service;
using System.Threading.Tasks;
using NLog;

namespace ATS.WS.ControllersATS
{
    public class UsuarioAtsController : DefaultController
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly IUsuarioFilialApp _usuarioFilialApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IGrupoUsuarioApp _grupoUsuarioApp;
        private readonly IClienteApp _clienteApp;
        private readonly IUsuarioDocumentoApp _usuarioDocumentoApp;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosApp _parametrosApp;
        private readonly SrvUsuario _srvUsuario;
        private readonly IGrupoUsuarioMenuService _grupoUsuarioMenuService;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IBloqueioFinanceiroTipoApp _bloqueioFinanceiroTipo;

        public UsuarioAtsController(IUsuarioApp usuarioApp, IUserIdentity userIdentity,
            IUsuarioFilialApp usuarioFilialApp, IEmpresaApp empresaApp,
            IGrupoUsuarioApp grupoUsuarioApp, IClienteApp clienteApp, IUsuarioDocumentoApp usuarioDocumentoApp,
            IParametrosApp parametrosApp, SrvUsuario srvUsuario, IGrupoUsuarioMenuService grupoUsuarioMenuService, 
            IParametrosUsuarioService parametrosUsuarioService, IBloqueioFinanceiroTipoApp bloqueioFinanceiroTipo)
        {
            _usuarioApp = usuarioApp;
            _userIdentity = userIdentity;
            _usuarioFilialApp = usuarioFilialApp;
            _empresaApp = empresaApp;
            _grupoUsuarioApp = grupoUsuarioApp;
            _clienteApp = clienteApp;
            _usuarioDocumentoApp = usuarioDocumentoApp;
            _parametrosApp = parametrosApp;
            _srvUsuario = srvUsuario;
            _grupoUsuarioMenuService = grupoUsuarioMenuService;
            _parametrosUsuarioService = parametrosUsuarioService;
            _bloqueioFinanceiroTipo = bloqueioFinanceiroTipo;
        }

        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetByPerfil(EPerfil perfil, int? idFilial, int? idEmpresa)
        {
            try
            {
                var resultado = _usuarioApp.GetByPerfil(perfil, idFilial, idEmpresa).OrderBy(o => o.Nome);
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoUsuarioJuridicoEmpresa(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                var resultado = _empresaApp.GetPermissaoUsuarioJuridicoEmpresa(idEmpresa);
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(UsuarioAtsConsultaGridFilters @params, bool listarTerceiros = false, bool usuariosAtivos = true)
        {
            try
            {
                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && !_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                    return ResponderErro("Permissão negada!");

                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    @params.idEmpresa = _userIdentity.IdEmpresa ?? 0;

                var usuarios = _usuarioApp.ConsultaGrid(@params.idEmpresa,
                    @params.nome,
                    @params.Take,
                    @params.Page,
                    @params.Order,
                    @params.Filters,
                    (EPerfil) _userIdentity.Perfil,
                    _userIdentity.IdUsuario,
                    listarTerceiros,
                    usuariosAtivos);
                return ResponderSucesso(usuarios);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        [EnableLogAudit]
        public JsonResult Inativar(int idUsuario)
        {
            try
            {
                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                        return ResponderErro("Permissão negada!");

                    if (_userIdentity.IdEmpresa.HasValue)
                    {
                        if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                            throw new Exception("Registro não encontrado.");
                    }
                }

                var validationResult = _usuarioApp.Inativar(idUsuario, _userIdentity.IdUsuario);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Usuário inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetFilial(int idUsuario)
        {
            try
            {
                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && !_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                    return ResponderErro("Permissão negada!");

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                        throw new Exception("Registro não encontrado.");
                }

                return ResponderSucesso(_usuarioFilialApp.GetFilialCompletaPorIdUsuario(idUsuario));
            }
            catch (Exception)
            {
                return ResponderErro("Ops, aconteceu um problema interno no servidor.");
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        [EnableLogAudit]
        public JsonResult Reativar(int idUsuario)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                        return ResponderErro("Registro não encontrado.");
                }

                var user = _usuarioApp.Get(idUsuario);

                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                        return ResponderErro("Permissão negada!");
                    if (user.IdEmpresa != _userIdentity.IdEmpresa)
                        return ResponderErro("Empresa do usuário a reativar difere da empresa do usuário logado!");
                }

                if (user.Perfil == EPerfil.Empresa)
                {
                    var userGroup = _usuarioApp.UserGroupIsActive(idUsuario);
                    if (!userGroup)
                    {
                        return ResponderErro("Não foi possível reativar este usuário, favor entrar em contato com a Extratta");
                    }
                }
                
                var validationResult = _usuarioApp.Reativar(idUsuario, _userIdentity.IdUsuario);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Usuário reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetEmailsUsuarioByListaId(int[] listaIds)
        {
            try
            {
                if (listaIds == null)
                    return ResponderSucesso(new List<GridContatosModel>());

                if (listaIds.Where(id => _userIdentity.IdEmpresa.HasValue).Any(id => !_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, id)))
                {
                    return ResponderErro("Registro não encontrado.");
                }

                return ResponderSucesso(_usuarioApp.GetEmailsUsuarioByListaId(listaIds));
            }
            catch (Exception)
            {
                return ResponderErro("Ops, aconteceu um problema interno no servidor.");
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idUsuario)
        {
            object retorno;
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                        return ResponderErro("Registro não encontrado.");
                }

                if (!_usuarioApp.Get(idUsuario).Ativo)
                    throw new Exception($"Não é possível editar um registro desativado.");

                Usuario usuario = _usuarioApp.GetAllChilds(idUsuario);

                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                        return ResponderErro("Permissão negada!");
                    if (usuario.IdEmpresa != _userIdentity.IdEmpresa)
                        return ResponderErro("Empresa do usuário a consultar difere da empresa do usuário logado!");
                }

                var primeiroContato = usuario.Contatos?.FirstOrDefault();
                var primeiroEndereco = usuario.Enderecos?.FirstOrDefault();
                var primeiroVeiculo = usuario.Veiculos?.FirstOrDefault(x => x.Ativo);
                var permissaoAlterarAlcada = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(idUsuario);
                var permissaoAprovarReprovarAdiantamentoApp = _parametrosApp.GetPermiteAprovarSolicitacaoAdiantamentoApp(idUsuario);
                var permissaoEfetuarCargaAdiantamentoApp = _parametrosApp.GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(idUsuario);
                var permissaoSolicitarAdiantamentoApp = _parametrosApp.GetPermiteSolicitarAdiantamentoApp(idUsuario);
                var permissoesAtendimentoCartao = usuario.Perfil == EPerfil.Empresa && usuario.IdEmpresa.HasValue
                    ? _parametrosApp.GetPermissoesUsuarioAtendimentoCartao(usuario.IdUsuario, usuario.IdEmpresa.Value)
                    : null;

                retorno = new
                {
                    usuario.IdUsuario,
                    usuario.Nome,
                    TipoPessoa = usuario.TipoCobranca == ETipoCobranca.PessoaJuridica ? ETipoPessoa.Juridica : ETipoPessoa.Fisica,
                    usuario.CPFCNPJ,
                    usuario.Perfil,
                    usuario.Carreteiro,
                    usuario.IdHorario,
                    usuario.IdGrupoUsuario,
                    usuario.IdPonto,
                    usuario.Login,
                    usuario.TipoCliente,
                    usuario.ReceberNotificacao,
                    usuario.PermiteResponderChat,
                    usuario.Gestor,
                    usuario.Vistoriador,
                    usuario.Referencia1,
                    usuario.Referencia2,
                    usuario.ReceberRelatorioOC,
                    usuario.VistoriadorMaster,
                    usuario.Matriz,
                    usuario.RecebeEmailGestao,
                    usuario.VisualizaTodosChecklists,
                    usuario.VisualizaChecklistTodosGrupos,
                    CNH = new
                    {
                        Numero = usuario.CNH,
                        Categoria = usuario.CNHCategoria
                    },
                    Empresa = new
                    {
                        usuario.Empresa?.IdEmpresa,
                        usuario.Empresa?.RazaoSocial,
                        usuario.Empresa?.UtilizaAgendamento
                    },
                    Veiculo = new
                    {
                        primeiroVeiculo?.IdTipoCarreta,
                        primeiroVeiculo?.IdTipoCavalo,
                        primeiroVeiculo?.IdTecnologia,
                        primeiroVeiculo?.Placa,
                        primeiroVeiculo?.NumeroFrota,
                        primeiroVeiculo?.RENAVAM,
                        primeiroVeiculo?.Chassi,
                        primeiroVeiculo?.AnoFabricacao,
                        primeiroVeiculo?.AnoModelo,
                        primeiroVeiculo?.Marca,
                        primeiroVeiculo?.Modelo,
                        primeiroVeiculo?.ComTracao,
                        primeiroVeiculo?.TipoRodagem,
                        primeiroVeiculo?.QuantidadeEixos
                    },
                    Contato = new
                    {
                        primeiroContato?.Telefone,
                        primeiroContato?.Celular,
                        primeiroContato?.Email
                    },
                    Endereco = new
                    {
                        primeiroEndereco?.CEP,
                        primeiroEndereco?.Endereco,
                        primeiroEndereco?.Numero,
                        primeiroEndereco?.Complemento,
                        primeiroEndereco?.Bairro,
                        primeiroEndereco?.IdPais,
                        primeiroEndereco?.IdEstado,
                        primeiroEndereco?.IdCidade
                    },
                    PermiteAlterarLimiteEmpresa = permissaoAlterarAlcada.PermiteEmpresa,
                    PermiteAlterarLimiteFilial = permissaoAlterarAlcada.PermiteFilial,
                    PermiteAcessoExtratoDetalhado = _srvUsuario.GetPermitirAcessoExtratoDetalhadoUsuario(usuario.IdUsuario),
                    AplicativoPermiteRealizarTransferenciaBancaria = _srvUsuario.GetAplicativoPermiteRealizarTransferenciaBancaria(usuario.IdUsuario),
                    AplicativoPermiteRealizarTransferenciaCartoes = _srvUsuario.GetAplicativoPermiteRealizarTransferenciaCartoes(usuario.IdUsuario),
                    Permissoes = _srvUsuario.GetTiposGestorBloqueio(usuario.IdUsuario, usuario.Perfil).ToArray(),
                    PermissaoFinanceiro = _srvUsuario.GetPermissaoFinanceiro(usuario.IdUsuario, usuario.Perfil).ToArray(),
                    PermissaoTag = _srvUsuario.GetBloqueioTag(usuario.IdUsuario).BloqueiosTag.ToList(),
                    PermissaoCartao = _srvUsuario.GetPermissaoCartao(usuario.IdUsuario, usuario.Perfil).ToArray(),
                    PermissoesAtendimentoCartao = permissoesAtendimentoCartao,
                    LimitesPortador = _srvUsuario.GetLimitesPortador(usuario.CPFCNPJ),
                    PermiteAprovarSolicitacaoAdiantamentoApp = permissaoAprovarReprovarAdiantamentoApp,
                    PermiteSolicitarAdiantamentoApp = permissaoSolicitarAdiantamentoApp,
                    PermiteEfetuarCargaSolicitacaoAdiantamentoApp = permissaoEfetuarCargaAdiantamentoApp,
                    LimiteDiarioPagamentoPix = _parametrosUsuarioService.GetLimiteDiarioPagamentoPixUsuario(usuario.IdUsuario),
                    LimiteUnitarioPagamentoPix = _parametrosUsuarioService.GetLimiteUnitarioPagamentoPixUsuario(usuario.IdUsuario),
                    PermiteRealizarPagamentoPix = _parametrosUsuarioService.GetPermiteRealizarPagamentoPix(usuario.IdUsuario),
                    PermiteSolicitarAlteracoesLimitePix = _parametrosUsuarioService.GetPermiteSolicitarAlteracoesLimitePix(usuario.IdUsuario),
                    PermitirEdicaoDadosAdministrativosPix = _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosPix(usuario.IdUsuario),
                    PermitirCadastroChavePix = _parametrosUsuarioService.GetPermitirCadastroChavePix(usuario.IdUsuario),
                    GestorAlcadasPix = _parametrosUsuarioService.GetGestorAlcadasPix(usuario.IdUsuario),
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetGruposPorEmpresa(int? idEmpresa)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    idEmpresa = _userIdentity.IdEmpresa;
                }

                int? idEstabelecimentoBase = null;

                if (_userIdentity.Perfil == (int) EPerfil.Estabelecimento)
                    idEstabelecimentoBase = _usuarioApp.GetIdEstabelecimentoBase(_userIdentity.IdUsuario);

                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                        return ResponderErro("Permissão negada!");
                    if (idEmpresa == null || _userIdentity.IdEmpresa != idEmpresa)
                        return ResponderErro("Empresa difere da empresa do usuário logado!");
                }

                IEnumerable<object> grupoUsuarios =
                    _grupoUsuarioApp.GetPorEmpresa(idEmpresa.GetValueOrDefault(), idEstabelecimentoBase)
                        .OrderBy(x => x.Descricao)
                        .Select(u => new {u.IdGrupoUsuario, u.Descricao}).ToList();

                return ResponderSucesso(grupoUsuarios);
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetImageJson(int idUsuario)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                        return ResponderErro("Registro não encontrado.");
                }

                byte[] byteArray = _usuarioApp.GetFoto(idUsuario);
                if (byteArray == null)
                    throw new Exception($"Nenhuma imagem encontrada para o usuário de ID {idUsuario}!");

                return ResponderSucesso($"data:image/png;base64,{Convert.ToBase64String(new FileContentResult(byteArray, "image/png").FileContents)}");
            }
            catch (Exception err)
            {
                return ResponderErro(err);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [Validate2FA]
        [EnableLogRequest]
        //[IgnoreAuthSessionValidation]
        public JsonResult CadastrarParaNovoEstabelecimento(CadastrarAtualizarCls @params)
        {
            ValidationResult validation = new ValidationResult();

            if (_userIdentity.IdEmpresa.HasValue)
            {
                @params.idUsuario = _userIdentity.IdUsuario;
                @params.idEmpresa = _userIdentity.IdEmpresa;
            }

            try
            {
                //isso nao esta bem claro ??????????
                validation = _srvUsuario.InserirParaNovoEstabelecimento(@params);
            }
            catch (Exception e)
            {
                validation.Add(e.GetBaseException().Message);
            }
            
            return validation.IsValid
                ? ResponderSucesso("Usuário inserido com sucesso!") 
                : ResponderErro(validation.ToString());
        }
        
        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        public JsonResult CadastrarAtualizar(CadastrarAtualizarCls @params)
        {
            try
            {
                ValidationResult validation;

                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    @params.idEmpresa = _userIdentity.IdEmpresa;

                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                {
                    if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                        return ResponderErro("Permissão negada!");
                    if (@params.idEmpresa != _userIdentity.IdEmpresa)
                        return ResponderErro("Empresa do usuário a cadastrar difere da empresa do usuário logado!");
                }

                validation = @params.idUsuario > 0 ? _srvUsuario.AtualizarExistente(@params, _userIdentity) : _srvUsuario.InserirNovo(@params, _userIdentity);

                return validation.IsValid ? ResponderSucesso(@params.idUsuario > 0 ? "Usuário atualizado com sucesso!" : "Usuário inserido com sucesso!") : ResponderErro(validation.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult ResetarSenha(int idUsuario)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                        return ResponderErro("Registro não encontrado.");
                }

                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil == (int) EPerfil.Administrador)
                    return _usuarioApp.ResetarSenha(idUsuario, _userIdentity.IdUsuario) ? ResponderSucesso("Email de reset da senha enviado!") : ResponderErro("Reset de senha falhou!");

                if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                    return ResponderErro("Permissão negada!");

                // seria melhor ter um retorno de sucesso aqui. ???????????????
                return _usuarioApp.ResetarSenha(idUsuario, _userIdentity.IdUsuario) ? ResponderSucesso("Email de reset da senha enviado!") : ResponderErro("Reset de senha falhou!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult Resetar2FA(int idUsuario)
        {
            try
            {
                //se for diferente de aministrador: tem que ter o menu liberado
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (!_grupoUsuarioMenuService.HasMenuLiberado(_userIdentity.IdUsuario, EMenu.Usuarios))
                        return ResponderErro("Permissão negada!");

                    if (_userIdentity.IdEmpresa.HasValue)
                    {
                        if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                            return ResponderErro("Registro não encontrado.");
                    }
                }
                if (_usuarioApp.Resetar2FA(idUsuario))
                    return ResponderSucesso("Autenticação 2FA resetada com sucesso!");
                return ResponderErro("Reset de 2FA falhou!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridClientes(int? idEmpresa, int? idUsuario, ETipoCliente? tipoClienteSelecionado, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            if (!idUsuario.HasValue)
                idUsuario = _userIdentity.IdUsuario;

            if (_userIdentity.IdEmpresa.HasValue)
            {
                if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario ?? 0))
                    return ResponderErro("Registro não encontrado.");
            }

            Usuario usuarioClientesByUsuario = _usuarioApp.GetClientesByUsuario(idUsuario);
            IQueryable<Cliente> usuarioClientesByEmpresa;

            try
            {
                if (tipoClienteSelecionado == ETipoCliente.Origem)
                    usuarioClientesByEmpresa = _clienteApp.GetAll()
                        .Where(x => x.TipoCliente == ETipoCliente.Origem && x.Ativo);
                else
                    usuarioClientesByEmpresa = _clienteApp.All()
                        .Where(x => x.Ativo);

                if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                    usuarioClientesByEmpresa = usuarioClientesByEmpresa.OrderBy(x => x.IdCliente);
                else
                    usuarioClientesByEmpresa = usuarioClientesByEmpresa.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");


                usuarioClientesByEmpresa = usuarioClientesByEmpresa.AplicarFiltrosDinamicos(filters);

                return ResponderSucesso(new
                {
                    totalItems = usuarioClientesByEmpresa.Count(),
                    items = usuarioClientesByEmpresa.Skip((page - 1) * take).Take(take)
                        .ToList().Select(x => new
                        {
                            x.IdCliente,
                            IdUsuario = idUsuario,
                            x.NomeFantasia,
                            CNPJCPF = x.CNPJCPF.Length == 11 ? x.CNPJCPF.ToCPFFormato() : x.CNPJCPF.ToCNPJFormato(),
                            IsChecked = usuarioClientesByUsuario?.Clientes.Any(z => z.IdCliente == x.IdCliente)
                        })
                });
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridDocumentos(int? idEmpresa, int? idUsuario, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            IQueryable<UsuarioDocumento> docsUsuario;

            try
            {
                if (!idUsuario.HasValue)
                    idUsuario = _userIdentity.IdUsuario;

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario ?? 0))
                        return ResponderErro("Registro não encontrado.");
                }

                docsUsuario = _usuarioDocumentoApp.GetDocumentos(idUsuario.Value);


                if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                    docsUsuario = docsUsuario.OrderBy(x => x.IdUsuarioDocumento);
                else
                    docsUsuario = docsUsuario.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");


                // Aplica para todos os campos de string
                docsUsuario = docsUsuario.AplicarFiltrosDinamicos(filters);
                
                return ResponderSucesso(new
                {
                    totalItems = docsUsuario.Count(),
                    items = docsUsuario.Skip((page - 1) * take).Take(take)
                        .ToList().Select(x => new
                        {
                            x.IdUsuarioDocumento,
                            Validade = x.Validade?.ToString("G"),
                            x.AvisoValidade,
                            x.IdTipoDocumento,
                            x.TipoDocumento?.Descricao
                        })
                });
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridPermissaoFilial(int? idEmpresa, int? idUsuario, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            IQueryable<UsuarioFilial> filiaisUsu;

            try
            {
                if (!idUsuario.HasValue)
                    idUsuario = _userIdentity.IdUsuario;

                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario ?? 0))
                        return ResponderErro("Registro não encontrado.");
                }

                filiaisUsu = _usuarioFilialApp.GetFiliaisPorIdUsuario(idUsuario.Value);

                if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                    filiaisUsu = filiaisUsu.OrderBy(x => x.IdUsuario);
                else
                    filiaisUsu = filiaisUsu.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

                // Aplica para todos os campos de string
                filiaisUsu = filiaisUsu.AplicarFiltrosDinamicos(filters);

                return ResponderSucesso(new
                {
                    totalItems = filiaisUsu.Count(),
                    items = filiaisUsu.Skip((page - 1) * take).Take(take)
                        .ToList().Select(x => new
                        {
                            x.IdUsuario,
                            x.IdFilial,
                            x.Filial.NomeFantasia
                        })
                });
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridUsuariosLiderados(int? idUsuarioEdicao, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idUsuarioEdicao = _userIdentity.IdUsuario;

                if (idUsuarioEdicao == null)
                    throw new InvalidOperationException("Informe um usuário para a consulta.");
                
                var retorno = _usuarioApp.ConsultarGridUsuariosLiderados(idUsuarioEdicao.Value, take, page, order, filters);

                return ResponderSucesso(retorno);
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridUsuariosDisponiveisParaSeremLiderados(int? idUsuarioEdicao, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idUsuarioEdicao = _userIdentity.IdUsuario;

                if (idUsuarioEdicao == null)
                    throw new InvalidOperationException("Informe um usuário para a consulta.");
                
                var retorno = _usuarioApp.ConsultarGridUsuariosDisponiveisParaSeremLiderados(idUsuarioEdicao.Value, _userIdentity.IdEmpresa, take, page, order, filters);

                return ResponderSucesso(retorno);
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult IncluirUsuarioLiderado(int idUsuario, int idUsuarioParaIncluir)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    if (idUsuario != _userIdentity.IdUsuario)
                        throw new InvalidOperationException("Usuário sem permissão.");
                
                _usuarioApp.IncluirUsuarioLiderado(idUsuario, _userIdentity.IdEmpresa, idUsuarioParaIncluir, _userIdentity.IdUsuario);

                return ResponderSucesso("Usuário incluído.");
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult RemoverUsuarioLiderado(int idUsuario, int idUsuarioParaRemover)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idUsuario = _userIdentity.IdUsuario;
                
                var retorno = _usuarioApp.RemoverUsuarioLiderado(idUsuario, idUsuarioParaRemover);

                return ResponderSucesso("Usuário removido.");
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult PegarTiposGestorBloqueio()
        {
            return ResponderSucesso(
                _srvUsuario.PegarTiposGestorBloqueio().ToArray());
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridUsuarios(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltroGridUsuarioModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa ?? 0;

            var report = _srvUsuario.GerarRelatorioGridUsuarios(filtrosGridModel.IdEmpresa,
                filtrosGridModel.PerfilUsuarioLogado, filtrosGridModel.IdUsuarioLogado, filtrosGridModel.ListarTerceiros,
                filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de usuários.{filtrosGridModel.Extensao}");
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetTiposGestorBloqueio(int? idUsuario)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario ?? 0))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = _srvUsuario.GetTiposGestorBloqueio(idUsuario ?? 0).ToArray();

                return ResponderSucesso(string.Empty, retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoFinanceiro(int? idUsuario)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario ?? 0))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = _srvUsuario.GetPermissaoFinanceiro(idUsuario ?? 0).ToArray();

                return ResponderSucesso(string.Empty, retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridPorEmpresa(UsuarioAtsConsultaGridFilters @params, int? idFilial, int? idOperacao, bool marcarTodosUsuario, int apertouUsuario, List<int> grupoUsuarios)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    @params.idEmpresa = _userIdentity.IdEmpresa;

                if (@params.idEmpresa != null)
                {
                    var usuarios = _usuarioApp.ConsultaGridPorEmpresa(@params.idEmpresa, idFilial, idOperacao, @params.Take, @params.Page, @params.Order, @params.Filters, marcarTodosUsuario,
                        apertouUsuario, grupoUsuarios);
                    return ResponderSucesso(usuarios);
                }
            
                return ResponderSucesso(null);
                
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorCnpj(string cpfCnpj)
        {
            object retorno;
            int? idempresa = null;
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idempresa = _userIdentity.IdEmpresa;

                var usuario = _usuarioApp.GetPorCNPJCPF(cpfCnpj, idEmpresa: idempresa);

                if (usuario != null)
                {
                    var primeiroContato = usuario.Contatos?.FirstOrDefault();
                    var primeiroEndereco = usuario.Enderecos?.FirstOrDefault();
                    var primeiroVeiculo = usuario.Veiculos?.FirstOrDefault(x => x.Ativo);
                    var permissaoAlterarAlcada = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(usuario.IdUsuario);

                    retorno = new
                    {
                        usuario.IdUsuario,
                        usuario.Nome,
                        usuario.CPFCNPJ,
                        usuario.Perfil,
                        usuario.Carreteiro,
                        usuario.IdHorario,
                        usuario.IdGrupoUsuario,
                        usuario.IdPonto,
                        usuario.Login,
                        usuario.TipoCliente,
                        usuario.ReceberNotificacao,
                        usuario.PermiteResponderChat,
                        usuario.Gestor,
                        usuario.Vistoriador,
                        usuario.Referencia1,
                        usuario.Referencia2,
                        usuario.ReceberRelatorioOC,
                        usuario.VistoriadorMaster,
                        usuario.Matriz,
                        usuario.RecebeEmailGestao,
                        usuario.VisualizaTodosChecklists,
                        CNH = new
                        {
                            Numero = usuario.CNH,
                            Categoria = usuario.CNHCategoria
                        },
                        Empresa = new
                        {
                            usuario.Empresa?.IdEmpresa, usuario.Empresa?.RazaoSocial
                        },
                        Veiculo = new
                        {
                            primeiroVeiculo?.IdTipoCarreta,
                            primeiroVeiculo?.IdTipoCavalo,
                            primeiroVeiculo?.IdTecnologia,
                            primeiroVeiculo?.Placa,
                            primeiroVeiculo?.NumeroFrota,
                            primeiroVeiculo?.RENAVAM,
                            primeiroVeiculo?.Chassi,
                            primeiroVeiculo?.AnoFabricacao,
                            primeiroVeiculo?.AnoModelo,
                            primeiroVeiculo?.Marca,
                            primeiroVeiculo?.Modelo,
                            primeiroVeiculo?.ComTracao,
                            primeiroVeiculo?.TipoRodagem,
                            primeiroVeiculo?.QuantidadeEixos
                        },
                        Contato = new
                        {
                            primeiroContato?.Telefone,
                            primeiroContato?.Celular,
                            primeiroContato?.Email
                        },
                        Endereco = new
                        {
                            primeiroEndereco?.CEP,
                            primeiroEndereco?.Endereco,
                            primeiroEndereco?.Numero,
                            primeiroEndereco?.Complemento,
                            primeiroEndereco?.Bairro,
                            primeiroEndereco?.IdPais,
                            primeiroEndereco?.IdEstado,
                            primeiroEndereco?.IdCidade
                        },
                        PermiteAlterarLimiteEmpresa = permissaoAlterarAlcada.PermiteEmpresa,
                        R = permissaoAlterarAlcada.PermiteFilial,
                        Permissoes = _srvUsuario.GetTiposGestorBloqueio(usuario.IdUsuario, usuario.Perfil).ToArray()
                    };

                    return ResponderSucesso(retorno);
                }
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }

            return ResponderErro("Usuário não encontrado");
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult EditarEmpresaUsuario(int idUsuario, int idEmpresa)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_usuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idUsuario))
                        return ResponderErro("Registro não encontrado.");

                    idEmpresa = _userIdentity.IdEmpresa.Value;
                }

                if (idUsuario != 0)
                {
                    var usuario = _usuarioApp.Get(idUsuario);
                    // Regra vinda diretamente da Gerência. Não foi definida por análise ou por desenvolvimento:
                    // Quando vier uma integração de proprietário / motorista / usuário e já estiver 
                    // cadastrado para outra empresa, troca o idempresa para a que está integrando agora.
                    usuario.IdEmpresa = idEmpresa;
                    var cadastrar = _usuarioApp.UpdateUsuarioEmpresaVinculada(usuario, _userIdentity.IdUsuario);

                    if (cadastrar)
                        return ResponderSucesso("Dados do usuário atualizados com sucesso.");
                    else
                        return ResponderErro("Dados do usuário não atualizados");
                }

                return ResponderErro("Dados do usuário não atualizados");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaPermissoesPlataformaAtendimento(int? idEmpresa)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    idEmpresa = _userIdentity.IdEmpresa.Value;
                }

                if (!_userIdentity.IdEmpresa.HasValue && !idEmpresa.HasValue)
                    return Responder(true, string.Empty, new PermissoesUsuarioAtendimentoPortador());

                return Responder(true, string.Empty,
                    _parametrosApp.GetNovoUsuarioPermissoesAtendimentoCartao((_userIdentity.IdEmpresa ?? idEmpresa).Value));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult OcultarListagemTerceiros()
        {
            try
            {
                return _userIdentity.IdEmpresa.HasValue
                    ? Responder(true, string.Empty, _parametrosApp.GetOcultarListagemTerceiros(_userIdentity.IdEmpresa.Value))
                    : Responder(true, string.Empty, false);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult CadastraSomentePerfilEmpresa()
        {
            try
            {
                return _userIdentity.IdEmpresa.HasValue
                    ? Responder(true, string.Empty, _parametrosApp.CadastraSomentePerfilEmpresa(_userIdentity.IdEmpresa.Value))
                    : Responder(true, string.Empty, false);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar]
        public JsonResult AutenticarMenu(string link)
        {
            try
            {
                return ResponderSucesso("Url permitida"); /*_usuarioApp.AutenticarMenu(link, _userIdentity.IdUsuario)
                    ? ResponderSucesso("Url permitida")
                    : ResponderErro("Url não permitida");*/
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar]
        public JsonResult GetMenusAcessoUsuario()
        {
            try
            {
                return ResponderSucesso(_usuarioApp.GetMenusAcessoUsuario(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoEf2A()
        {
            try
            {
                return ResponderSucesso(_srvUsuario.GetItemPermissaoFinanceiro(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoGerenciamentoCartao()
        {
            try
            {
                return ResponderSucesso(_srvUsuario.GetPermissaoCartao(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoGerenciamentoPix()
        {
            try
            {
                return ResponderSucesso(_userIdentity.Perfil == (int) EPerfil.Administrador && _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosPix(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro("Não foi possível consultar o parâmetro.");
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetIpUsuario()
        {
            try
            {
                return ResponderSucesso(_userIdentity.IpUsuario);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro("Erro ao obter o IP do usuário: " + e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoCadastroChavePix()
        {
            try
            {
                return ResponderSucesso(_parametrosUsuarioService.GetPermitirCadastroChavePix(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro("Não foi possível consultar o parâmetro.");
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPermissaoAprovacaoCargaAvulsa()
        {
            try
            {
                return ResponderSucesso(_parametrosUsuarioService.GetPermitirCadastroChavePix(_userIdentity.IdUsuario));
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro("Não foi possível consultar o parâmetro.");
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface IPINService
    {
        PIN Get(int id);
        List<PIN> Get(string celular, bool IsValid = false);
        ValidationResult Add(PIN entity);
        ValidationResult Update(PIN entity);
        ValidationResult ValidatePIN(string PIN, string celular);
        ValidationResult SendPIN(string celular, string codigo);
        ValidationResult SetEnviadoOperadora(string IdSMS, bool status, ESMSSendStatus statusCode, DateTime? DataEnvio);
        ValidationResult SetEnviado(string IdSMS, bool status, ESMSDeliveredStatus statusCode, DateTime? DataEnvio);
    }
}
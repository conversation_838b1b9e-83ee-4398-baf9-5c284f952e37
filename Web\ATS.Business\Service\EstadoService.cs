﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;

namespace ATS.Domain.Service
{
    public class EstadoService : ServiceBase, IEstadoService
    {
        private readonly IEstadoRepository _estadoRepository;

        public EstadoService(IEstadoRepository estadoRepository)
        {
            _estadoRepository = estadoRepository;
        }

        /// <summary>
        /// Validar o objeto para os processos de CRUD
        /// </summary>
        /// <param name="estado">Entidade</param>
        /// <param name="processo">Processo</param>
        /// <returns></returns>
        private ValidationResult IsValid(Estado estado, EProcesso processo)
        {
            ValidationResult validationResult = new ValidationResult();

            // Validar se a sigla possui somente caracteres em caixa alta e que sejam letras
            validationResult.Add(AssertionConcern.AssertArgumentMatches("^[A-Z]+$", estado.Sigla, "Sigla deve possuir somente letras."));

            switch (processo)
            {
                case EProcesso.Create:
                    if (_estadoRepository.Any(e => e.IdPais == estado.IdPais && e.Sigla == estado.Sigla))
                        validationResult.Add($"Sigla já cadastrada para outro Estado.");
                    break;

                case EProcesso.Update:
                    if (_estadoRepository.Any(e => e.IdPais == estado.IdPais && e.Sigla == estado.Sigla && e.IdEstado != estado.IdEstado))
                        validationResult.Add($"Sigla já cadastrada para outro Estado.");
                    break;
            }

            return validationResult;
        }

        /// <summary>
        /// Formatar os valores de acordo com as regras de negócio
        /// </summary>
        /// <param name="estado"></param>
        private void FormatValues(Estado estado)
        {
            estado.Sigla = estado.Sigla?.ToUpper();
        }

        /// <summary>
        /// Método utilizado para buscar Estado.
        /// </summary>
        /// <param name="id">Id de Estado</param>
        /// <returns>Entidade Estado</returns>
        public Estado Get(int id)
        {
            return _estadoRepository.Get(id);
        }
        
        public List<Estado> GetTodos()
        {
            return _estadoRepository.GetAll().ToList();
        }

        public Estado GetEstadoBySigla(string sigla)
        {
            return _estadoRepository.Find(x => x.Sigla == sigla).FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para alterar Estado.
        /// </summary>
        /// <param name="entity">Entidade de Estado</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Estado entity)
        {
            try
            {
                FormatValues(entity);

                ValidationResult validationResult = IsValid(entity, EProcesso.Update);
                if (!validationResult.IsValid)
                    return validationResult;

                _estadoRepository.Update(entity);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para incluir Estado.
        /// </summary>
        /// <param name="entity">Entidade de Estado</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Estado entity)
        {
            try
            {
                FormatValues(entity);

                ValidationResult validationResult = IsValid(entity, EProcesso.Create);
                if (!validationResult.IsValid)
                    return validationResult;

                _estadoRepository.Add(entity);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para incluir Estado.
        /// </summary>
        /// <param name="nIBGE">Código IBGE</param>
        /// <returns>Id de estado </returns>
        public Estado GetPorIBGE(int nIBGE)
        {
            return _estadoRepository.FirstOrDefault(x => x.IBGE == nIBGE);
        }

        public int GetIdPorIBGE(int nIBGE)
        {
            return _estadoRepository.Find(x => x.IBGE == nIBGE)
                .Select(x => x.IdEstado)
                .FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para consultar Estado.
        /// </summary>
        /// <param name="nome">Nome de Estado</param>
        /// <returns>IQueryable de Estado</returns>
        public IQueryable<Estado> Consultar(string nome)
        {
            if (string.IsNullOrWhiteSpace(nome))
                nome = string.Empty;

            return _estadoRepository.Consultar(nome);
        }

        /// <summary>
        /// Retorna todos os estados ativos
        /// </summary>
        /// <returns>IQueryable de Estado</returns>
        public IQueryable<Estado> All()
        {
            return _estadoRepository.All().Where(x => x.Ativo);
        }

        /// <summary>
        /// Retorna o Estado via pesquisa por sigla da UF
        /// </summary>
        /// <param name="uf"></param>
        /// <returns></returns>
        public Estado GetPorSigla(string uf)
        {
            return _estadoRepository
                .Find(x => x.Sigla == uf.ToUpper() && x.Ativo)?.FirstOrDefault();
        }

        public int? GetIdEstado(string uf)
        {
            return _estadoRepository
                .Find(x => x.Sigla == uf.ToUpper() && x.Ativo)?.FirstOrDefault()?.IdEstado;
        }

        /// <summary>
        /// Retorna o código do estado a partir do país e sigla
        /// </summary>
        /// <param name="idPais"></param>
        /// <param name="sigla"></param>
        /// <returns></returns>
        public int? GetIdEstadoPorSigla(int idPais, string sigla)
        {
            return _estadoRepository
                    .Find(x => x.Sigla == sigla.ToUpper() && x.IdPais == idPais && x.Ativo)?
                    .Select(x => x.IdEstado)?.FirstOrDefault();
        }

        /// <summary>
        /// Inativa um estado
        /// </summary>
        /// <param name="idEstado">ID do estado a ser inativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idEstado)
        {
            try
            {
                IEstadoRepository repository = _estadoRepository;

                Estado estado = repository.Get(idEstado);
                if (!estado.Ativo)
                    return new ValidationResult().Add($"Estado já desativado na base de dados.");

                estado.Ativo = false;

                repository.Update(estado);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa um estado
        /// </summary>
        /// <param name="idEstado">ID do estado a ser reativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idEstado)
        {
            try
            {
                IEstadoRepository repository = _estadoRepository;

                Estado estado = repository.Get(idEstado);
                if (estado.Ativo)
                    return new ValidationResult().Add($"Estado já ativo na base de dados.");

                estado.Ativo = true;

                repository.Update(estado);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public int GetIdEstadoPorIbge(int codigoIbge)
        {
            return _estadoRepository
                .Find(x => x.IBGE == codigoIbge && x.Ativo)?.FirstOrDefault()?.IdEstado ?? 0;
        }

        /// <summary>
        /// Retorna a lista de estados atualizados a partir da data
        /// </summary>
        /// <param name="dataBase">Data base para filtro</param>
        /// <returns></returns>
        public IQueryable<Estado> GetEstadosAtualizados(DateTime dataBase, ERegiaoBrasil? Regiao)
        {
            var query = _estadoRepository.Include(x => x.Pais)
                .Where(x => x.DataAlteracao > dataBase);

            if (Regiao.HasValue)
                query = query.Where(x => x.Regiao == Regiao);

            return query;
        }

        public IQueryable<Estado> ConsultarPorIdPais(int idPais)
        {
            return _estadoRepository.Find(x => x.IdPais == idPais && x.Ativo);
        }

        public object ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var estados = _estadoRepository.All()
                .Include(x => x.Pais);

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                estados = estados.OrderBy(x => x.IdEstado);
            else
                estados = estados.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            estados = estados.AplicarFiltrosDinamicos<Estado>(filters);

            return new
            {
                totalItems = estados.Count(),
                items = estados.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdEstado,
                    x.Ativo,
                    x.Nome,
                    x.IBGE,
                    NomePais = x.Pais?.Nome
                })
            };
        }

        public bool ValidarIbgeCadastrado(int ibge)
        {
            var ibgeCadastrado = _estadoRepository.Where(o => o.IBGE == ibge).FirstOrDefault();
            return ibgeCadastrado != null;
        }
    }
}
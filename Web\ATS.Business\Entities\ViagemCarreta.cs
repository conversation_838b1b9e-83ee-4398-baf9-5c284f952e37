﻿using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ViagemCarreta
    {
        public int IdViagemCarreta { get; set; }
        public int? IdEmpresa { get; set; }
        public int IdViagem { get; set; }
        public string Placa { get; set; }
        public string Rntrc { get; set; }

        #region Relacionamentos

        public virtual Viagem Viagem { get; set; }

        #endregion
    }
}

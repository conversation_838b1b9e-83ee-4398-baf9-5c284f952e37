﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioClienteService : IService<UsuarioCliente>
    {
        ValidationResult Delete(UsuarioCliente usuarioCliente);
        IQueryable<UsuarioCliente> GetUsuarioClientePorIdUsuario(int idUsuario);
    }
}

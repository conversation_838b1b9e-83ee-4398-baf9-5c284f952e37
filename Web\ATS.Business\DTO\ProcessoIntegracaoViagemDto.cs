﻿using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class ProcessoIntegracaoViagemDto
    {
        public bool Sucesso { get; set; }
        public string MensagemViagem
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(_mensagemViagem))
                    return _mensagemViagem;
                
                if (!string.IsNullOrWhiteSpace(MensagemCiot))
                    _mensagemViagem += MensagemCiot;
                else if (!string.IsNullOrWhiteSpace(MensagemPedagio))
                    _mensagemViagem += MensagemPedagio;
                else if (!string.IsNullOrWhiteSpace(MensagemCartao))
                    _mensagemViagem += MensagemCartao;
                else if (!string.IsNullOrWhiteSpace(MensagemPix))
                    _mensagemViagem += MensagemPix;
                
                return _mensagemViagem;
            }
            set
            {
                _mensagemViagem = value;
            }
        }
        private string _mensagemViagem { get; set; }
        public string MensagemCiot { get; set; }
        public string MensagemPedagio { get; set; }
        public string MensagemCartao { get; set; }
        public string MensagemPix { get; set; }
        public EViagemAlterarStatusResponseTipoFalha? StatusResponseTipoFalha { get; set; }  
    }
}
﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ADD_COLUMN_ISPEDAGIO_AVUSLO : DbMigration
    {
        public override void Up()
        {
            DropIndex("dbo.VIAGEM", new[] { "idclienteorigem" });
            DropIndex("dbo.VIAGEM", new[] { "idclientedestino" });
            AddColumn("dbo.VIAGEM", "ispedagioavulso", c => c<PERSON>(nullable: false));
            AlterColumn("dbo.VIAGEM", "idclienteorigem", c => c.Int());
            AlterColumn("dbo.VIAGEM", "idclientedestino", c => c.Int());
            CreateIndex("dbo.VIAGEM", "idclienteorigem");
            CreateIndex("dbo.VIAGEM", "idclientedestino");
        }
        
        public override void Down()
        {
            DropIndex("dbo.VIAGEM", new[] { "idclientedestino" });
            DropIndex("dbo.VIAGEM", new[] { "idclienteorigem" });
            AlterColumn("dbo.VIAGEM", "idclientedestino", c => c.Int(nullable: false));
            AlterColumn("dbo.VIAGEM", "idclienteorigem", c => c.Int(nullable: false));
            DropColumn("dbo.VIAGEM", "ispedagioavulso");
            CreateIndex("dbo.VIAGEM", "idclientedestino");
            CreateIndex("dbo.VIAGEM", "idclienteorigem");
        }
    }
}

﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoProdutoMap : EntityTypeConfiguration<EstabelecimentoProduto>
    {
        public EstabelecimentoProdutoMap()
        {
            ToTable("ESTABELECIMENTO_PRODUTO");

            HasKey(x => new { x.IdEstabelecimento, x.IdProduto });

            Property(x => x.IdEstabelecimento)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(x => x.IdProduto)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.UnidadeMedida)
            .HasMaxLength(10);

            HasRequired(x => x.Estabelecimento)
                .WithMany(x => x.EstabelecimentoProdutos)
                .HasForeignKey(x => x.IdEstabelecimento);

            HasOptional(x => x.ProdutoBase)
                .WithMany(x => x.EstabelecimentoProduto)
                .HasForeignKey(x => new { x.IdEstabelecimentoBase, x.IdProdutoBase });
        }
    }
}

﻿using System;

namespace ATS.Domain.Extensions
{
    public static class DateTimeExtensions
    {
        public static DateTime ToLocalDate(this DateTime dataHoraSatelite)
        {
            var horarioVerao = new DateTime(dataHoraSatelite.Year, dataHoraSatelite.Month, dataHoraSatelite.Day)
                .IsDaylightSavingTime();
            
            return dataHoraSatelite.AddHours(horarioVerao ? -2 : -3);
        }
        
        public static bool IsHorarioComercial(this DateTime dataHora)
        {
            if (dataHora.DayOfWeek >= DayOfWeek.Monday && dataHora.DayOfWeek <= DayOfWeek.Friday)
            {
                if (dataHora.Hour >= 8 && dataHora.Hour < 18)
                {
                    return true;
                }
            }

            return false;
        }
    }
}

using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ResgateCartaoAtendimentoMap : EntityTypeConfiguration<ResgateCartaoAtendimento>
    {
        public ResgateCartaoAtendimentoMap()
        {
            ToTable("RESGATE_CARTAO_ATENDIMENTO");
            
            HasKey(t => t.IdResgate);

            Property(t => t.IdResgate)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            
            Property(t => t.Motivo)
                .HasColumnType("text")
                .HasMaxLength(200);

            Property(t => t.Produto)
                .IsRequired();

            Property(t => t.Valor)
                .HasPrecision(10, 2)
                .IsRequired();
            
            Property(t => t.CpfCnpjPortador)
                .IsRequired()
                .IsUnicode(false)
                .HasColumnType("varchar")
                .HasMaxLength(14);
            
            Property(t => t.CnpjEmpresa)
                .IsRequired()
                .IsUnicode(false)
                .HasColumnType("varchar")
                .HasMaxLength(14);
            
            Property(t => t.NumeroCartao)
                .HasColumnType("int");
            
            Property(t => t.Produto)
                .HasColumnType("int");
            
            Property(t => t.Motivo)
                .HasColumnType("varchar")
                .HasMaxLength(250);
            
            Property(t => t.IdUsuarioCadastro)
                .HasColumnType("int");
            
            Property(t => t.DataHoraCadastro)
                .IsRequired()
                .HasColumnType("datetime");
            
            Property(t => t.StatusResgate)
                .IsRequired()
                .HasColumnType("int");
            
            Property(t => t.IdUsuarioEstorno)
                .HasColumnType("int")
                .IsOptional();
            
            Property(t => t.DataHoraEstorno)
                .IsOptional()
                .HasColumnType("datetime");
            
            Property(t => t.MotivoEstorno)
                .HasColumnType("varchar")
                .HasMaxLength(250);
        }
        
    }
}
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.ViagemV2.Request
{
    public class ViagemV2AlterarStatusRequestModel : RequestBase
    {
        public ViagemV2AlterarStatusRequestModel()
        {
            ComportamentoPedagioConfirmado =
                ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal;
        }

        public int? ViagemId { get; set; }

        public ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado ComportamentoPedagioConfirmado { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (!ViagemId.HasValue || ViagemId == 0)
                return new ValidationResult().Add("Viagem Id não informado.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
﻿using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class BloqueioCartaoTipoApp : AppBase, IBloqueioCartaoTipoApp
    {
        private readonly IBloqueioCartaoTipoService _bloqueioCartaoTipoService;

        public BloqueioCartaoTipoApp(IBloqueioCartaoTipoService bloqueioCartaoTipoService)
        {
            _bloqueioCartaoTipoService = bloqueioCartaoTipoService;
        }

        public IQueryable<BloqueioCartaoTipo> GetAll()
        {
            return _bloqueioCartaoTipoService.GetAll();
        }
    }
}
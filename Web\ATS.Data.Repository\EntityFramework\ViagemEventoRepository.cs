﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models.ViagemModels;
using MongoDB.Driver.Core.Operations;

namespace ATS.Data.Repository.EntityFramework
{
    public class ViagemEventoRepository : Repository<ViagemEvento>, IViagemEventoRepository
    {
        public ViagemEventoRepository(AtsContext context) : base(context)
        {
        }

        public bool AnyParcelaNaoBaixada(List<int> idsViagemEvento)
        {
            if (!idsViagemEvento.Any()) return false;
            return Any(c => idsViagemEvento.Contains(c.IdViagemEvento) && c.Status != EStatusViagemEvento.Baixado);
        }

        public IEnumerable<ViagemEventoIdentifierModel> GetIdsParaProcessarAgendamentoDePagamento(DateTime dataAgendamentoInicio, DateTime dataAgendamentoFim)
        {
            return All(true)
                .Where(x => x.Status == EStatusViagemEvento.Agendado
                            && x.Viagem.StatusViagem != EStatusViagem.Cancelada
                            && x.DataAgendamentoPagamento >= dataAgendamentoInicio
                            && x.DataAgendamentoPagamento <= dataAgendamentoFim
                            && x.HabilitarPagamentoCartao)
                .OrderByDescending(x => x.IdViagemEvento) // Ordernar descrescente para o processamento iniciar do mais recente ao mais antigo. Caso o registro antigo estiver travado por algum erro, ele é o ultimo a ser processado.
                .Select(x => new ViagemEventoIdentifierModel
                {
                    IdViagem = x.IdViagem,
                    IdViagemEvento = x.IdViagemEvento,
                    IdEmpresa = x.IdEmpresa,
                    CnpjEmpresa = x.Viagem.Empresa.CNPJ,
                    DataLancamento = x.Viagem.DataLancamento
                })
                .ToArray();
        }

        public IQueryable<ViagemEvento> GetEventosQuery(int idViagem, bool @readonly = false)
        {
            return Find(v => v.IdViagem == idViagem, @readonly);
        }

        public IEnumerable<ViagemEvento> GetViagensEventos(int viagemid)
        {
            return Where(c => c.IdViagem == viagemid);
        }
    }
}
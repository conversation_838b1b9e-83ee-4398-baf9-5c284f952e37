﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IUsuarioDocumentoRepository : IRepository<UsuarioDocumento>
    {
        IQueryable<UsuarioDocumento> GetDocumentos(int idMotorista);
        UsuarioDocumento GetDocCNH(int idUsuario);
        UsuarioDocumento GetDocCNH(int idUsuario, int idTipoDoc);
        List<Tuple<int, DateTime?>> GetDocumentoCNHPorIdUsuIdTipoDoc(List<int> usuariosMotoristas, int tpDoc);
    }
}
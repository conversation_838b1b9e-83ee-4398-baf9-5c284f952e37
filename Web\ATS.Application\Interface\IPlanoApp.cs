﻿using ATS.Application.Interface.Common;
using ATS.Domain.DTO.Plano;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Application.Interface
{
    public interface IPlanoApp : IAppBase<Plano>
    {
        List<PlanoEmpresaDto> GetPlanosAtivos();
        List<PlanoEmpresaDto> GetPlanosEmpresa(int idEmpresa);
        ValidationResult AddPlanoEmpresa(PlanoEmpresa planoEmpresa);
        ValidationResult UpdatePlanoEmpresa(PlanoEmpresa planoEmpresa);
        public PlanoEmpresa GetPlanoEmpresa(int idPlano, int idEmpresa);
    }
}

﻿using System.Data.Entity;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class VeiculoCombustivelRepository : Repository<VeiculoTipoCombustivel>, IVeiculoCombustivelRepository
    {
        public VeiculoCombustivelRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<VeiculoTipoCombustivel> GetByIdVeiculoWithAllChilds(int? idVeiculo)
        {
            /*
            return
                from veiculoCombustivel in All()
                .Where(x => x.TipoCombustivel.Ativo)
                .Include(x => x.TipoCombustivel)
                .Include(x => x.Veiculo)
                where veiculoCombustivel.IdVeiculo == idVeiculo
                select veiculoCombustivel;*/

            return null;
        }
    }
}

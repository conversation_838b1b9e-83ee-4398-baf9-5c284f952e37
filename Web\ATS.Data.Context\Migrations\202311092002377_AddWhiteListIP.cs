﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddWhiteListIP : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.WHITE_LIST_IP",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        ipv4 = c.String(nullable: false, maxLength: 20, unicode: false),
                        datacadastro = c.DateTime(nullable: false),
                        idempresa = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.WHITE_LIST_IP", "idempresa", "dbo.EMPRESA");
            DropIndex("dbo.WHITE_LIST_IP", "IX_IdEmpresa");
            DropTable("dbo.WHITE_LIST_IP");
        }
    }
}

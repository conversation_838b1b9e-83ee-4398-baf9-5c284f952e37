﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TransacaoCartaoMap : EntityTypeConfiguration<TransacaoCartao>
    {
        public TransacaoCartaoMap()
        {
            ToTable("TRANSACAO_CARTAO");

            HasKey(x => x.IdTransacaoCartao);

            Property(x => x.IdTransacaoCartao)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.IdViagemEvento).IsOptional();

            Property(x => x.IdCargaAvulsa).IsOptional();
            
            Property(x => x.IdResgate).IsOptional();

            Property(x => x.StatusPagamento).IsRequired();

            Property(x => x.MensagemProcessamentoWs).HasMaxLength(300);

            Property(x => x.NumeroProtocoloWs);

            Property(x => x.CnpjCpfOrigem).HasMaxLength(14);

            Property(x => x.CnpjCpfDestino).HasMaxLength(14);

            Property(x => x.OrigemTransacaoCartao).IsRequired();
        }
    }
}

# Dependency Injection

## TD;DR

- Para iniciar um novo ciclo de vida

```csharp
using (var scope = IoC.Container.BeginLifetimeScope())
{
    var cidadeApp = scope.Resolve<ICidadeApp>();
    cidadeApp.ValidarIbgeCadastrado(IbgeCidade)
}
```

- Todo construtor deve ser extremamente leve e nunca fazer acessos externos (Isso em qualquer linguagem/framework). Somente atribua referências e realize pequenas expressões resolvidas na RAM do processo sem lockin externo do SO.
- Se precisar realmente inicializar algo no construtor por comodidade, utilizr o patter de lazy load. Note que não executado o código, apenas criado a expressão que será executada ao demandas o valor.

```csharp
public class ExemploService
{
    private readonly Lazy<EVersaoAntt> _versaoAnttLazy;

    public VersaoAnttLazyLoadService(IParametrosService parametrosService)
    {
        _parametrosService = parametrosService;
        _versaoAnttLazy = new Lazy<EVersaoAntt>(() =>
        {
            var versaoAntt = _parametrosService.GetVersaoAntt();
            return versaoAntt.HasValue ? (EVersaoAntt)versaoAntt : EVersaoAntt.Versao2;
        });
    }
}
}
```

## Normalização Dependency Injection

O projeto utiliza o AutoFac como framework de injeção de dependência, porém quando o legado o implantou, não adequou o código para respeitar os padrões de injeção de dependência.

Em março/2023 foi realizado um grande refactor para normalizar com o padrão de mercado.

### Como era

O código legado estava fortemente acoplado a um contexto de requisição HTTP, utilizando uma feature do AutoFac para gerenciar o scope do ciclo de vida dos componentes pelo HttpContext.

Isso impedia processos em backgrouds na aplicação (Jobs, Worker, Thread isoladas, etc), além do próprio design de classe ser um anti-pattern não incentivado pelo mercado.

Código no formato antigo (Não utilizar mais!):

```csharp
public class MinhaController
{
    public void MeuMetodo()
    {
        var cidadeService = new CidadeService(); // Criava a dependência em qualquer lugar e de forma repetida
        var dado = cidadeService.GetXXXXX();
        // ..
    }
}

public class CidadeService()
{
    private readonly ICidadeRepository _cidadeRepository;

    public CidadeService()
    {
        _cidadeRepository = new IoC().Resolve<ICidadeRepository>(); // Em outros casos criava no construtor
    }
}
```

### Novo modelo

Respeitando o padrão de injeção de dependências via construtor.


```csharp
public class MinhaController
{
    private readonly ICidadeService _cidadeService;

    public MinhaController(ICidadeService cidadeService) // Sempre deve ser injetada a referência via construtor
    {
        _cidadeService = cidadeService; // Armazenar numa variável readonly
    }

    public void MeuMetodo()
    {
        var dado = cidadeService.GetXXXXX(); // Onde precisa, usa-se a mesma instância da variável
        // ..
    }
}

public class CidadeService() // Nada diferente da classe acima
{
    private readonly ICidadeRepository _cidadeRepository;

    public CidadeService(ICidadeRepository cidadeRepository)
    {
        _cidadeRepository = cidadeRepository;
    }
}
```

Nem todos os problemas de design de classes foram resolvidos durante a normalização, existem dependências circulares muito forte nas grandes classes do sistema (Exemplo: EstabelecimentoBaseService usa CredenciamentoService e vice-versa).

Em alguns pontos você pode encontrar o modelo abaixo com o paliativo para resolver dependências circulares. **Isso não é um exemplo a se seguido!** Busque quebrar as classes menos com suas devidas responsabilidades afim de evitar dependência circular.

```csharp
public class CredenciamentoService
{
    //
    private readonly ILifetimeScope _scope;
    private IEstabelecimentoService _estabelecimentoService => _scope.Resolve<IEstabelecimentoService>(); // <-- Paliativo para resolver dependências circulares

    public CredenciamentoService(ILifetimeScope scope, .....)
    {
        _scope = scope;
    }
}
```

### Jobs

Agora é possível implementar processos em background como o [ProcessarPagamentoAgendadoJob](../Web/ATS.WS/Jobs/ProcessarPagamentoAgendadoJob.cs);

### Ciclo de vida em outras threads

Agora é possível criar ciclo de vida independente da thread principal, como na classe [MotoristaIntegrarRequestModel](../Web/ATS.WS/Models/Common/Request/MotoristaIntegrarRequestModel.cs#LN53).

```csharp
using (var scope = IoC.Container.BeginLifetimeScope())
{
    var cidadeApp = scope.Resolve<ICidadeApp>();
    cidadeApp.ValidarIbgeCadastrado(IbgeCidade)
}
```
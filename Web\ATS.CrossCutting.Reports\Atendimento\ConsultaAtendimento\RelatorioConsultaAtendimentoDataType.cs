using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Atendimento.ConsultaAtendimento
{
    public class RelatorioConsultaAtendimentoDataType
    {
        public IList<RelatorioConsultaAtendimentoItemDataType> items { get; set; }

        // ReSharper disable once InconsistentNaming
        public int totalItems { get; set; }
    }

    public class RelatorioConsultaAtendimentoItemDataType
    {
        public string IdAtendimentoPortador { get; set; }
        public string Cnpjcpf { get; set; }
        public string Observacao { get; set; }
        public string DataInicio { get; set; }
        public string DataFinal { get; set; }
        public string IdUsuario { get; set; }
        public string UsuarioNome { get; set; }
        public string Status { get; set; }
        public string Protocolo { get; set; }
        public string IdMotivoFinalizacaoAtendimento { get; set; }
        public string DescricaoMotivoAtendimento { get; set; }
        public string TotalData { get; set; }
    }
}
using System;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Request;
using ATS.WS.Services.ViagemServices;

namespace ATS.WS.Services.ViagemV2Services
{
    public class CancelamentoViagemV2
    {
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IViagemApp _viagemApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly StatusEventoViagem _statusEventoViagem;

        public CancelamentoViagemV2(IClienteApp clienteApp, IParametrosApp parametrosApp, IProprietarioApp proprietarioApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IViagemApp viagemApp, IVersaoAnttLazyLoadService versaoAntt, ICadastrosApp cadastrosApp, IEmpresaApp empresaApp, IEmpresaRepository empresaRepository, StatusEventoViagem statusEventoViagem)
        {
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _viagemApp = viagemApp;
            _versaoAntt = versaoAntt;
            _cadastrosApp = cadastrosApp;
            _empresaApp = empresaApp;
            _empresaRepository = empresaRepository;
            _statusEventoViagem = statusEventoViagem;
        }

        public Retorno<object> Cancelar(ViagemV2AlterarStatusRequestModel request)
        {
            var validacaoEntrada = request.ValidarEntrada();
            
            if (!validacaoEntrada.IsValid)
                return new Retorno<object>(validacaoEntrada, null, "Falha ao cancelar viagem.");

            var resultadoCancelamento =
                _statusEventoViagem.AlterarStatus(request.ViagemId ?? 0, EStatusViagem.Cancelada,
                    request.CNPJAplicacao, request.Token, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit,DateTime.Now, request.ComportamentoPedagioConfirmado);
            
            return !resultadoCancelamento.Sucesso
                ? new Retorno<object>(new ValidationResult().Add(resultadoCancelamento.Mensagem, EFaultType.Error), resultadoCancelamento, "Falha ao cancelar viagem.")
                : new Retorno<object>(true, "Cancelamento realizado com sucesso.", null);
        }
    }
}
﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddCamposEmpresa : DbMigration
    {
        public override void Up()
        {
          
            AddColumn("dbo.EMPRESA", "tipoinscricaoempresa", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.EMPRESA", "codigobancocompensacao", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.EMPRESA", "numeroinscricaoempresa", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.EMPRESA", "codigoconveniobanco", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.EMPRESA", "agenciamantenedora", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.EMPRESA", "digitoverificaconta", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.EMPRESA", "digitoverificacontaagconta", c => c.String(maxLength: 100, unicode: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.EMPRESA", "digitoverificacontaagconta");
            DropColumn("dbo.EMPRESA", "digitoverificaconta");
            DropColumn("dbo.EMPRESA", "agenciamantenedora");
            DropColumn("dbo.EMPRESA", "codigoconveniobanco");
            DropColumn("dbo.EMPRESA", "numeroinscricaoempresa");
            DropColumn("dbo.EMPRESA", "codigobancocompensacao");
            DropColumn("dbo.EMPRESA", "tipoinscricaoempresa");
        }
    }
}

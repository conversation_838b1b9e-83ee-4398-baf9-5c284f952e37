﻿using System;
using ATS.Domain.Enum;
using System.ComponentModel.DataAnnotations;
using ATS.Data.Repository.External.Extratta.Biz.Models;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class SolicitacaoChavePixEvento
    {
        public int IdSolicitacaoChavePixEvento { get; set; }
        public int IdSolicitacaoChavePix { get; set; }
        public ESolicitacaoChavePixStatus IdSolicitacaoChavePixStatus { get; set; }
        public DateTime DataCadastro { get; set; }
        public int? IdUsuarioCadastro { get; set; }
        public string DocumentoUsuarioAudit { get; set; }
        
        
        
        public virtual SolicitacaoChavePixStatus SolicitacaoChavePixStatus { get; set; }
        public virtual SolicitacaoChavePix SolicitacaoChavePix { get; set; }
        public virtual Usuario UsuarioCadastro { get; set; }
    }

}
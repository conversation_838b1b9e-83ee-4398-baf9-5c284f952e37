﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using ATS.CrossCutting.IoC;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.Helpers;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Sistema.Framework.Util.Extension;
using System.Data.Entity.Migrations;

namespace ATS.Data.Repository.EntityFramework
{
    public class EmpresaRepository : Repository<Empresa>, IEmpresaRepository
    {
        public EmpresaRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Retorna todas as empresas
        /// </summary>
        /// <param name="where">Clausula para where</param>
        /// <param name="readonly">Especifica se é somente leitura ou não</param>
        /// <returns></returns>
        public IQueryable<Empresa> All(Expression<Func<Empresa, bool>> @where, bool @readonly = false)
        {
            return Find(@where, @readonly);
        }

        /// <summary>
        /// Retorna o registro com todos os elementos filhos
        /// </summary>
        /// <param name="id">Código do empresa</param>
        /// <returns></returns>
        public Empresa GetWithAllChilds(int id)
        {
            return (from empresa in All()
                    .Include(x => x.Modulos)
                    .Include(x => x.Pais)
                    .Include(x => x.Estado)
                    .Include(x => x.Cidade)
                    .Include(x => x.Layout)
                where empresa.IdEmpresa == id
                select empresa).FirstOrDefault();
        }

        /// <summary>
        /// Retorna todas as empresas que possuem monitormamento.
        /// </summary>
        /// <returns></returns>
        public IQueryable<Empresa> GetEmpresasComMonitoramento()
        {
            return (from empresa in All()
                where (empresa.PossuiMonitoramento && empresa.Ativo)
                select empresa);
        }

        public Empresa GetAsNoTracking(int id)
        {
            return (from empresa in All()
                        .AsNoTracking()
                        .Include(x => x.Layout)
                        .Include(x => x.Modulos)
                    where empresa.IdEmpresa == id
                    select empresa)
                .FirstOrDefault();
        }

        /// <summary>
        /// Retorna o objeto de Empresa
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
#pragma warning disable 672
        public override Empresa Get(int id)
#pragma warning restore 672
        {
            return (from empresa in All()
                        .Include(x => x.Layout)
                        .Include(x => x.Modulos)
                    where empresa.IdEmpresa == id
                    select empresa)
                .FirstOrDefault();
        }

        public void UpdateNumOrdemCarregamento(int idempresa, int numordemcarregamento)
        {
            ((AtsContext)Context).Database.ExecuteSqlCommand(
                String.Format("UPDATE EMPRESA SET NUMORDEMCARREGAMENTO = {0} WHERE IDEMPRESA = {1}", numordemcarregamento, idempresa));
        }

        public int? GetLayoutPadraoCartao(int id)
        {
            return (from empresa in All()
                        .AsNoTracking()
                        .Include(x => x.IdLayoutCartao)
                    where empresa.IdEmpresa == id
                    select empresa.IdLayoutCartao)
                .FirstOrDefault();
        }

        public EmpresaIndicadores GetEmpresaIndicadores(int idEmpresa)
        {
            return Context.Set<EmpresaIndicadores>().Where(e => e.IdEmpresa == idEmpresa)
                .FirstOrDefault();
        }

        public void AddEmpresaIndicadores(EmpresaIndicadores empresaIndicadores)
        {
            Context.Set<EmpresaIndicadores>().Add(empresaIndicadores);
            Context.SaveChanges();
        }

        public void UpdateEmpresaIndicadores(EmpresaIndicadores empresaIndicadores)
        {
            Context.Set<EmpresaIndicadores>().AddOrUpdate(empresaIndicadores);
            Context.SaveChanges();
        }

        public string GetTokenMicroServices(int id)
        {
            return Where(e => e.IdEmpresa == id && e.TokenMicroServices != null && e.Ativo)
                .Select(e => e.TokenMicroServices)
                .FirstOrDefault();
        }

        public string GetTokenMicroServices(string cnpj)
        {
            return Where(e => e.CNPJ == cnpj && e.TokenMicroServices != null && e.Ativo)
                .Select(e => e.TokenMicroServices)
                .FirstOrDefault();
        }

        public int? GetIdProdutoCartaoFretePadrao(int idEmpresa)
        {
            return Where(e => e.IdEmpresa == idEmpresa)
                .Select(e => e.IdProdutoCartaoFretePadrao)
                .FirstOrDefault();
        }

        public TarifasMeioHomologadoModel GetTarifasMeioHomologado(string cnpj)
        {
            var result = AsNoTracking()
                .Where(e => e.CNPJ == cnpj)
                .Select(e => new TarifasMeioHomologadoModel
                {
                    QuantidadeTotal = e.QuantidadeTotalTarifasMeioHomologado ?? 0,
                    ValorTotal = e.ValorTotalTarifasMeioHomologado ?? 0
                })
                .FirstOrDefault();

            if (result == null)
                throw new Exception("Empresa não localizada com CNPJ: " + cnpj);

            return result;
        }

        public TimeSpan? GetTempoExpiracaoPedagio(int idEmpresa)
        {
            var result = AsNoTracking().Where(c => c.IdEmpresa == idEmpresa).Select(c => c.TempoExpiracaoCreditoPedagio)
                .FirstOrDefault();

            return result;
        }

        public List<ConsultaTodasEmpresasDto> ConsultarTodas(bool? ativo = null)
        {
            var empresas = All();

            if (ativo.HasValue)
                empresas = empresas.Where(x => x.Ativo == ativo.Value);

            return empresas.ProjectTo<ConsultaTodasEmpresasDto>().ToList();
        }

        public bool AnyRntrc(int idEmpresa)
        {
            return Where(x => x.IdEmpresa == idEmpresa).Any(x => x.CNTRC > 0);
        }

        public bool HabilitarAgendamentoPagamentoFrete(int idEmpresa) => Where(x => x.IdEmpresa == idEmpresa)
                .Select(x => x.HabilitarAgendamentoPagamentoFrete)
                .First();
        
        public int? GetIdPorCnpj(string cnpj)
        {
            var cnpjOnlyNumbers = cnpj?.OnlyNumbers();
            var retId = this
                .Where(p => p.CNPJ == cnpjOnlyNumbers && p.Ativo).Select(p => p.IdEmpresa).FirstOrDefault();

            return retId > 0 ? (int?)retId : null;
        }
        
        public string GetCnpj(int id)
        {
            return this
                .Find(t => t.IdEmpresa == id).Select(t => t.CNPJ).FirstOrDefault().OnlyNumbers();
        }
        
        public IQueryable<Empresa> GetQuery(int idEmpresa)
        {
            return Where(c => c.IdEmpresa == idEmpresa);
        }
        
        public IQueryable<Empresa> Query(int id)
        {
            return Where(e => e.IdEmpresa == id);
        }
        
        public int? GetLayoutCartaoPadrao(int id)
        {
            return GetLayoutPadraoCartao(id);
        }
        
        public List<Empresa> GetTodas()
        {
            return All().ToList();
        }
        
        public int ObterMinutosValidadeChavePagamento(int empresaId)
        {
            var minutosValidade = Where(o => o.IdEmpresa == empresaId).Select(o => o.MinutosValidadeHash).FirstOrDefault();

            if (minutosValidade.HasValue)
                return minutosValidade.Value;
            
            throw new Exception($"Minutos de validade da chave não configurado para a empresa {empresaId}");
        }
        
        public bool EmpresaValidaPagamentoFrete(int id)
        {
            var retorno = Where(c => c.IdEmpresa == id)
                .Select(c => c.ValidacaoPagFrete).FirstOrDefault();

            return retorno;
        }
        
        public bool GetPermissaoUsuarioJuridicoEmpresa(int idEmpresa)
        {
            var result = Find(x => x.IdEmpresa == idEmpresa)
                .Select(x => x.PermiteUsuarioJuridico).FirstOrDefault();

            return result;
        }
        
        public bool GetPermissaoUsuarioJuridicoCnpjEmpresa(string cnpj)
        {
            var result = Find(x => x.CNPJ == cnpj)
                .Select(x => x.PermiteUsuarioJuridico).FirstOrDefault();

            return result;
        }

        public Empresa Get(string cnpj)
        {
            return Find(x => x.CNPJ == cnpj && x.Ativo).FirstOrDefault();
        }
        
        public Empresa GetWithIncludeAutenticacao(string cnpj)
        {
            return Find(x => x.CNPJ == cnpj && x.Ativo).Include(x => x.AutenticacaoAplicacao).FirstOrDefault();
        }
        
        public Empresa GetWithIncludeAutenticacao(int? id)
        {
            return Find(x => x.IdEmpresa == id && x.Ativo).Include(x => x.AutenticacaoAplicacao).FirstOrDefault();
        }

        public (string Endpoint, string Headers, string BaixarEventoViagemEndpoint) GetWebhookParameters(int idEmpresa)
        {
            var data = All(true)
                .Where(x => x.IdEmpresa == idEmpresa)
                .Select(x => new { x.WebHookEndpoint, x.WebHookHeaders })
                .First();
            var endpoint = data.WebHookEndpoint?.Trim();
            return (
                Endpoint: endpoint,
                Headers: data.WebHookHeaders?.Trim(),
                BaixarEventoViagemEndpoint: string.IsNullOrWhiteSpace(endpoint) ? null : endpoint.SetEndChars("/") + "baixar-evento-viagem");
        }
    }

    /*public class EmpresaImeisRepository : Repository<EmpresaIMEI>, IEmpresaImeisRepository
    {
        /// <summary>
        /// Retorna o objeto de Empresa
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public override EmpresaIMEI Get(int id)
        {
            return (from empImei in All()
                    where empImei.IdEmpresaIMEI == id
                    select empImei)
                  .FirstOrDefault();
        }

    }*/
}
﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ILimiteTransacaoPortadorApp : IBaseApp<ILimiteTransacaoPortadorService>
    {
        BusinessResult LimitarValor(string documento, ETipoLimiteTransacaoPortador tipo, decimal valor);
        decimal GetLimite(string documento, ETipoLimiteTransacaoPortador tipo);
        IList<PortadorLimitesValor> GetLimites(string documento);
        void LimitarValoresPadrão(ETipoPessoa tipoPessoa, string cpfcnpj);
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Triggers;
using ATS.Domain.Service;
using ATS.Domain.Trigger.Base;

namespace ATS.Domain.Trigger
{
    public class EstabelecimentoBaseTrigger : Trigger<EstabelecimentoBase>, IEstabelecimentoBaseTrigger
    {
        public EstabelecimentoBaseTrigger()
        {
            //this.RegisterAfterTrigger(EOperationTrigger.Insert, (d, y) => _estabelecimentoBaseService.AfterUpdate(d, y), "EnviarEstabelecimentoWebHook");
            //this.RegisterAfterTrigger(EOperationTrigger.Update, (d, y) => _estabelecimentoBaseService.AfterUpdate(d, y), "EnviarEstabelecimentoWebHook");
        }
    }
}

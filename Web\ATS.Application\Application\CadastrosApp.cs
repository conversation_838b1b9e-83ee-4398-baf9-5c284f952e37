using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;
using SistemaInfo.MicroServices.Rest.Cadastros.ApiClient;

namespace ATS.Application.Application
{
    public class CadastrosApp : AppBase, ICadastrosApp
    {
        private readonly ICadastrosService _service;

        public CadastrosApp(ICadastrosService service)
        {
            _service = service;
        }
        
        public ConsultarBancoResponseDTO Bancos()
        {
            return _service.Bancos();
        }

        public List<ConsultarNaturezaCargaResponse> GetNaturezasCarga()
        {
            return _service.GetNaturezasCarga();
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioEstabelecimentoMap : EntityTypeConfiguration<UsuarioEstabelecimento>
    {
        public UsuarioEstabelecimentoMap()
        {
            ToTable("USUARIO_ESTABELECIMENTO");

            HasKey(x => new {x.IdUsuario, x.IdEstabelecimento});

            HasRequired(x => x.Usuario)
                .WithMany(x => x.UsuarioEstabelecimentos)
                .HasForeignKey(x => x.IdUsuario);

            HasRequired(x => x.Estabelecimento)
                .WithMany(x => x.UsuarioEstabelecimentos)
                .HasForeignKey(x => x.IdEstabelecimento);
        }
    }
}

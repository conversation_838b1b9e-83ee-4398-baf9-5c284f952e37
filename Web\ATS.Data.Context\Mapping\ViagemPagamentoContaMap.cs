using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemPagamentoContaMap : EntityTypeConfiguration<ViagemPagamentoConta>
    {
        public ViagemPagamentoContaMap()
        {
            ToTable("VIAGEM_PAGAMENTO_CONTA");

            HasKey(o => new {o.IdViagem, o.IdEmpresa});

            Property(o => o.IdViagem)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(o => o.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(o => o.CpfCnpjConta)
                .HasMaxLength(14)
                .IsOptional();

            Property(o => o.CodigoBacenBanco)
                .HasMaxLength(5)
                .IsOptional();

            Property(o => o.Agencia)
                .HasMaxLength(6)
                .IsOptional();

            Property(o => o.Conta)
                .HasMaxLength(20)
                .IsOptional();
        }
    }
}
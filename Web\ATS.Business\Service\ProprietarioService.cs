﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web.Configuration;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.Reports.Proprietario.RelatorioListaProprietarios;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using ATS.Domain.Models.Proprietarios;
using Microsoft.Ajax.Utilities;
using NLog;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class ProprietarioService : BaseService<IProprietarioRepository>, IProprietarioService
    {
        private readonly IProprietarioDapper _dapper;
        private readonly ICiotV2Service _ciotV2Service;
        private readonly ICiotV3Service _ciotV3Service;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        
        public new IProprietarioRepository Repository => base.Repository;

        public ProprietarioService(IProprietarioRepository repository, IUserIdentity sessionUser, IProprietarioDapper dapper, ICiotV2Service ciotV2Service, ICiotV3Service ciotV3Service, 
            IEmpresaRepository empresaRepository, IVersaoAnttLazyLoadService versaoAntt, IUsuarioRepository usuarioRepository, CartoesServiceArgs cartoesServiceArgs) : base(repository, sessionUser)
        {
            _dapper = dapper;
            _ciotV2Service = ciotV2Service;
            _ciotV3Service = ciotV3Service;
            _empresaRepository = empresaRepository;
            _versaoAntt = versaoAntt;
            _usuarioRepository = usuarioRepository;
            _cartoesServiceArgs = cartoesServiceArgs;
        }
        
        /// <summary>
        /// Retorna as mensagens de validação se o registro for válido para os processos de inserção e atualização
        /// </summary>
        /// <param name="prop">Entidade de proprietário</param>
        /// <param name="processo">Processo que estará sendo realizado</param>
        /// <returns></returns>
        private ValidationResult IsValidCrud(Proprietario prop, EProcesso processo)
        {
            var validationResult = new ValidationResult();
            var empresa = _empresaRepository.Get(prop.IdEmpresa);

            if (prop.Enderecos == null || !prop.Enderecos.Any())
                return new ValidationResult().Add(new ValidationError($"É obrigatório informar o Endereço."));

            if (prop.Contatos == null || !prop.Contatos.Any())
                return new ValidationResult().Add(new ValidationError($"É obrigatório informar o Contato."));

            validationResult.Add(prop.CNPJCPF.Length > 13
                ? AssertionConcern.AssertArgumentIsValidCNPJ(prop.CNPJCPF, "CNPJ inválido.")
                : AssertionConcern.AssertArgumentIsValidCPF (prop.CNPJCPF, "CPF inválido."));

            validationResult.Add(AssertionConcern.AssertArgumentIsValidCEP(prop.Enderecos.First()?.CEP, @"CEP deve ser válido"));

            foreach (var telefone in prop.Contatos)
            {
                if (telefone.Telefone.IsNullOrWhiteSpace())
                {
                    telefone.Telefone = empresa.Telefone.IsNullOrWhiteSpace() ? "99999999" : empresa.Telefone;
                } 
                if (telefone.Celular.IsNullOrWhiteSpace())
                {
                    telefone.Celular = empresa.Celular.IsNullOrWhiteSpace() ? "999999999" : empresa.Celular;
                }
                if (AssertionConcern.AssertArgumentIsValidTelefone(telefone.Telefone, @"Telefone deve ser válido") != null)
                {
                    telefone.Telefone = empresa.Telefone.IsNullOrWhiteSpace() ? "99999999" : empresa.Telefone;
                }
                if (AssertionConcern.AssertArgumentIsValidTelefone(telefone.Celular, @"Celular deve ser válido") != null)
                {
                    telefone.Celular = empresa.Celular.IsNullOrWhiteSpace() ? "999999999" : empresa.Celular;
                }
            }
            
            if (!string.IsNullOrEmpty(prop.Contatos.First().Email))
                validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(prop.Contatos.First().Email, @"E-mail deve ser válido"));

            if (string.IsNullOrWhiteSpace(prop.RNTRC))
                validationResult.Add($"RNTRC é obrigatório para o {WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]}.");

            if (prop.Enderecos.First()!= null )
                if (prop.Enderecos.First().IdPais == 0)
                {
                    return new ValidationResult().Add(new ValidationError($"Informe um país válido"));
                }

            if (processo == EProcesso.Create)
            {
                if (Any(prop.CNPJCPF, prop.IdEmpresa))
                    validationResult.Add($"CPF/CNPJ informado já cadastrado para outro {WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]}.");
            }
            else if (processo == EProcesso.Update)
            {
                if (Repository
                    .Any(p => p.CNPJCPF == prop.CNPJCPF && p.IdEmpresa == prop.IdEmpresa && p.IdProprietario != prop.IdProprietario))
                    validationResult.Add($"CPF/CNPJ informado já cadastrado para outro {WebConfigurationManager.AppSettings["TIPO_PROPRIETARIO_SINGULAR"]}.");
            }

            if (processo == EProcesso.Update && prop.CNPJCPF != GetCpfCnpj(prop.IdProprietario))
                validationResult.Add("CPF/CNPJ não pode ser alterado");

            if (prop.TipoCarregamentoFrete.Equals(ETipoCarregamentoFrete.CarregarProprietarioTransferirMotorista) && prop.PercentualTransferenciaMotorista == 0)
                validationResult.Add("O percentual de transferência é obrigatório");

            if (prop.TipoCarregamentoFrete.Equals(ETipoCarregamentoFrete.CarregarProprietarioTransferirMotorista) && prop.PercentualTransferenciaMotorista < 0)
                validationResult.Add("O percentual de transferência deve ser maior que 0");

            if (prop.TipoCarregamentoFrete.Equals(ETipoCarregamentoFrete.CarregarProprietarioTransferirMotorista) && prop.PercentualTransferenciaMotorista > 100)
                validationResult.Add("O percentual de transferência deve ser no máximo 100");

            return validationResult;
        }

        public bool Any(string cnpjCpf, int idEmpresa)
        {
            return Repository.Any(cnpjCpf, idEmpresa);
        }

        /// <summary>
        /// Formatar os valores dos campos de acordo com as regras de negócio
        /// </summary>
        /// <param name="proprietario"></param>
        private static void ApplyRules(Proprietario proprietario)
        {
            proprietario.CNPJCPF = proprietario.CNPJCPF.OnlyNumbers();
            proprietario.RNTRC = proprietario.RNTRC.PadLeft(9, '0');

            #region Contatos

            foreach (var contato in proprietario.Contatos)
            {
                // Certifica-se de setar o valor da chave composta.
                contato.IdEmpresa = proprietario.IdEmpresa;

                contato.Telefone = contato.Telefone?.OnlyNumbers();
                contato.Celular  = contato.Celular?.OnlyNumbers();
            }

            #endregion

            #region Endereços

            foreach (var endereco in proprietario.Enderecos)
            {
                // Certifica-se de setar o valor da chave composta.
                endereco.IdEmpresa = proprietario.IdEmpresa;
                endereco.CEP = endereco.CEP?.OnlyNumbers();
            }

            #endregion
        }

        /// <summary>
        /// Retorna o objeto de proprietário contendo os registros filhos
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Proprietario Get(int id)
        {
            return Repository.Get(id);
        }

        /// <summary>
        /// Buscar Proprietário
        /// </summary>
        /// <param name="id">Código de Proprietário</param>
        /// <returns>Objeto Proprietario</returns>
        public Proprietario GetAllChilds(int id)
        {
            return Repository
                .Find(x => x.IdProprietario == id)
                .Include(x => x.Enderecos)
                .Include(x => x.Contatos)
                .Include(x => x.Empresa)
                .Include(x => x.Veiculos)
                .FirstOrDefault();
        }

        public IQueryable<Proprietario> Find(Expression<Func<Proprietario, bool>> predicate, bool @readonly = false)
        {
            return Repository.Find(predicate, @readonly);
        }

        /// <summary>
        /// Adicionar o proprietario a base de dados
        /// </summary>
        /// <param name="proprietario">Dados do Proprietário</param>
        /// <returns>Objeto de validação ValidationResult</returns>
        public ValidationResult Add(Proprietario proprietario)
        {
            try
            {
                ApplyRules(proprietario);

                var validationResult = IsValidCrud(proprietario, EProcesso.Create);
                if (!validationResult.IsValid)
                    return validationResult;
                
                var usuario = _usuarioRepository.GetPorCNPJCPF(proprietario.CNPJCPF?.OnlyNumbers());
                
                if (usuario != null)
                {
                    if (usuario.Perfil == EPerfil.Motorista)
                    {
                        usuario.Perfil = EPerfil.Proprietario;
                        usuario.IdEmpresa = proprietario.IdEmpresa;
                    }
                    else if(usuario.Perfil == EPerfil.Proprietario)
                    {
                        usuario.IdEmpresa = proprietario.IdEmpresa;
                        _usuarioRepository.Update(usuario);
                    }
                    else if(usuario.Perfil != EPerfil.Proprietario)
                    {
                        usuario.IdEmpresa = proprietario.IdEmpresa;
                        _usuarioRepository.Update(usuario);
                    }
                }

                if (!proprietario.PercentualTransferenciaMotorista.HasValue)
                    proprietario.PercentualTransferenciaMotorista = 0;

                if (proprietario.EquiparadoTac == null)
                {
                    if(proprietario.CNPJCPF?.Length == 11)
                        proprietario.EquiparadoTac = true;
                    else
                    {
                        var consulta = EquiparadoTac(proprietario.CNPJCPF, proprietario.RNTRC.PadLeft(9, '0'), null, proprietario.IdEmpresa);
                        
                        if (!consulta.FalhaComunicacaoAntt)
                            proprietario.EquiparadoTac = consulta.Retorno != ERetornoConsultaTAC.NaoEquiparado;
                    }
                }

                Repository.Add(proprietario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro de Proprietário.
        /// </summary>
        /// <param name="proprietario">Dados de Proprietário</param>
        /// <returns>Objeto de validação ValidationResult</returns>
        public ValidationResult Update(Proprietario proprietario)
        {
            try
            {
                ApplyRules(proprietario);

                var validationResult = IsValidCrud(proprietario, EProcesso.Update);
                if (!validationResult.IsValid)
                    return validationResult;
                
                if (proprietario.EquiparadoTac == null)
                {
                    if(proprietario.CNPJCPF?.Length == 11)
                        proprietario.EquiparadoTac = true;
                    else
                    {
                        var consulta = EquiparadoTac(proprietario.CNPJCPF, proprietario.RNTRC.PadLeft(9, '0'), null, proprietario.IdEmpresa);
                        
                        if (!consulta.FalhaComunicacaoAntt)
                            proprietario.EquiparadoTac = consulta.Retorno != ERetornoConsultaTAC.NaoEquiparado;
                    }
                }

                Repository.Update(proprietario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public int? GetIdByCpfCnpjWithEmpresa(string cpfCnpj, int? idEmpresa)
        {
            var cpfCnpjOnlyNumbers = cpfCnpj?.OnlyNumbers();
            var query = Repository
                .Where(p => p.CNPJCPF == cpfCnpjOnlyNumbers);

            query = query.Where(p => p.IdEmpresa == idEmpresa);

            int? retId = query.Select(p => p.IdProprietario).FirstOrDefault();

            return retId;            
        }
        
        public Proprietario GetByCpfCnpj(string cpfCnpj, int? idEmpresa)
        {
            var cpfCnpjOnlyNumbers = cpfCnpj?.OnlyNumbers();
            
            var query = Repository.Where(p => p.CNPJCPF == cpfCnpjOnlyNumbers);

            if(idEmpresa.HasValue)
                query = query.Where(p => p.IdEmpresa == idEmpresa);

            return query.FirstOrDefault();
        }

        /// <summary>
        /// Retorna os proprietários a partir dos dados do filtro
        /// </summary>
        /// <param name="razaoSocial">Razão Social do Proprietário</param>
        /// <param name="idEmpresa">Id da Empresa</param>
        /// <param name="idUsuarioLogOn">Código do usuário logado</param>
        /// <param name="onlyAtivo">Indica se deve buscar somente os ativos</param>
        /// <returns>IQueryable de ProprietarioGrid</returns>
        public IQueryable<Proprietario> Consultar(string razaoSocial, int idEmpresa, int? idUsuarioLogOn, bool? onlyAtivo)
        {
            if (string.IsNullOrWhiteSpace(razaoSocial))
                razaoSocial = string.Empty;

            var retConsulta =
                Repository.Consultar(razaoSocial, idEmpresa, onlyAtivo);

            if (retConsulta.Any() && idUsuarioLogOn.HasValue)
            {
                var perfil = _usuarioRepository.GetPerfil(idUsuarioLogOn.GetValueOrDefault());
                
                switch (perfil)
                {
                    case EPerfil.Empresa:
                        var idTranspUsuario = _usuarioRepository.GetIdEmpresa(idUsuarioLogOn.GetValueOrDefault());
                        if (idTranspUsuario.HasValue)
                            retConsulta = retConsulta.Where(r => r.IdEmpresa == idTranspUsuario);
                        break;

                    case EPerfil.Proprietario:
                        // Sendo proprietário irá exibir apenas os que ele estiver vinculado, efetuando a busca por CPF/CNPJ.
                        var cpfcnpj = _usuarioRepository.GetCNPJCPF(idUsuarioLogOn.GetValueOrDefault());
                        if (!string.IsNullOrWhiteSpace(cpfcnpj))
                            retConsulta = retConsulta.Where(p => p.CNPJCPF == cpfcnpj);
                        break;
                }
            }

            return retConsulta;
        }

        /// <summary>
        /// Retorna o ID do proprietário, informando o CPF/CNPJ do proprietário
        /// </summary>
        /// <param name="idEmpresa">ID do empresa</param>
        /// <param name="cpfCnpj">CPF/CNPJ do Empresa</param>
        /// <returns></returns>
        public int? GetIdProprietario(int idEmpresa, string cpfCnpj)
        {
            return Repository
                .GetProprietario(idEmpresa, cpfCnpj)?.IdProprietario;
        }

        public IQueryable<Proprietario> GetAllByIdEmpresa(int idEmpresa)
        {
            return Repository.GetAll().Where(x => x.IdEmpresa == idEmpresa);
        }

        /// <summary>
        /// Retorna o CNPJ/CPF do proprietário
        /// </summary>
        /// <param name="idProprietario"></param>
        /// <returns></returns>
        public string GetCpfCnpj(int idProprietario)
        {
            return Repository
                .Find(p => p.IdProprietario == idProprietario)
                .Select(p => p.CNPJCPF)
                .FirstOrDefault()?.OnlyNumbers();
        }

        public decimal GetPorcentagemTransferenciaMotorista(int idProprietario, int idEmpresa)
        {
            var parametro = Repository
                .Find(x => x.IdProprietario == idProprietario && x.IdEmpresa == idEmpresa)
                .Select(x => new
                {
                    x.PercentualTransferenciaMotorista,
                    x.TipoCarregamentoFrete,
                    PercentualTransferenciaMotoristaEmpresa = x.Empresa.PercentualTransferenciaMotorista
                }).FirstOrDefault();

            if (parametro == null)
                return 0;

            switch (parametro.TipoCarregamentoFrete)
            {
                case ETipoCarregamentoFrete.PadraoDoEmbarcador:
                    return parametro.PercentualTransferenciaMotoristaEmpresa;
                case ETipoCarregamentoFrete.CarregarApenasProprietario:
                    return 0;
                case ETipoCarregamentoFrete.CarregarProprietarioTransferirMotorista:
                    return parametro.PercentualTransferenciaMotorista ?? new decimal();
                default:
                    throw new ArgumentOutOfRangeException(
                        $"Não foi possível identificar o percentual de transferência ao motorista do proprietário {idProprietario} empresa {idEmpresa}");
            }
        }

        /// <summary>
        /// Ativar ou inativar
        /// </summary>
        /// <returns></returns>
        public ValidationResult AlterarStatus(int idProprietario)
        {
            try
            {
                var repository = Repository;
                var proprietario = repository.FirstOrDefault(o => o.IdProprietario == idProprietario);

                if (proprietario == null)
                    return new ValidationResult().Add("Proprietario não encontrado.");

                proprietario.Ativo = !proprietario.Ativo;
                repository.Update(proprietario);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        /// <summary>
        /// Retorna os dados para a grid do Web Novo
        /// </summary>
        /// <returns></returns>
        public object ConsultarGridProprietarios(int? idEmpresa, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            var proprietarios = GetDataToGridAndReport(idEmpresa, orderFilters, filters);

            var count = proprietarios.Count();

            var proprietariosLista = proprietarios
                .Skip((page - 1) * take)
                .Take(take)
                .AsEnumerable()
                .Select(o => new
                {
                    o.IdProprietario,
                    RazaoSocialEmpresa = o.Empresa?.RazaoSocial,
                    CNPJCPF = o.CNPJCPF.ToCpfOrCnpj(),
                    o.RazaoSocial,
                    o.NomeFantasia,
                    o.RNTRC,
                    TipoContratoDescricao = o.TipoContrato.GetDescription(),
                    o.TipoContrato,
                    EquiparadoTac = o.EquiparadoTac != null && (bool) o.EquiparadoTac ? "Sim" : "Não",
                    Localidade = o.Enderecos?.FirstOrDefault()?.Cidade?.Nome,
                    o.Ativo
                })
                .ToList();

            return new
            {
                totalItems = count,
                items = proprietariosLista
            };
        }

        /// <summary>
        /// Geração do relatório a partir da grid
        /// </summary>
        /// <returns></returns>
        public byte[] GerarRelatorioGridProprietarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao)
        {
            var listaProprietarios = new List<RelatorioProprietarioDataType>();
            var proprietarios = GetDataToGridAndReport(idEmpresa, orderFilters, filters);

            foreach (var proprietario in proprietarios)
            {
                listaProprietarios.Add(new RelatorioProprietarioDataType
                {
                    Ativo = proprietario.Ativo ? "Sim" : "Não",
                    Empresa = proprietario.Empresa?.RazaoSocial,
                    RazaoSocial = proprietario.RazaoSocial,
                    IdProprietario = proprietario.IdProprietario.ToString(),
                    Endereco = FormatarEnderecoProprietario(proprietario),
                    TipoContrato = proprietario.TipoContrato.DescriptionAttr(),
                    CpfCnpj = proprietario.CNPJCPF.ToCpfOrCnpj(),
                    RgOrgaoExpedidor = proprietario.RGOrgaoExpedidor,
                    Rg = proprietario.RG,
                    Rntrc = proprietario.RNTRC,
                    NomeFantasia = proprietario.NomeFantasia,
                    Ie = proprietario.IE,
                    StatusIntegracao = proprietario.StatusIntegracao.DescriptionAttr(),
                    TacAgregado = proprietario.EquiparadoTac != null && (bool)proprietario.EquiparadoTac ? "Sim" : "Não"
                });
            }

            return new RelatorioProprietarios().GetReport(listaProprietarios, extensao, logo);
        }

        public ProprietarioConsultaDetalheViagemResponse ConsultaDetalheParaViagem(ProprietarioConsultaDetalheViagemRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.CpfCnpj))
            {
                return new ProprietarioConsultaDetalheViagemResponse
                {
                    Existe = false
                };                
            }
        
            var query = Repository.Where(c => c.IdEmpresa == SessionUser.IdEmpresa);

            if (!string.IsNullOrWhiteSpace(request.CpfCnpj))
            {
                var cpfcnpj = request.CpfCnpj.OnlyNumbers();
                query = query.Where(c => c.CNPJCPF == cpfcnpj);
            }
            
            if (!query.Any())
            {
                return new ProprietarioConsultaDetalheViagemResponse
                {
                    Existe = false
                };
            }

            return query.Select(c => new ProprietarioConsultaDetalheViagemResponse
            {
                Existe = true,
                Nome = c.NomeFantasia,
                CpfCnpj = c.CNPJCPF,
                RNTRC = c.RNTRC,
                IdProprietario = c.IdProprietario
            }).FirstOrDefault();
        }

        public ValidationResult AtualizarRntrc(ProprietarioAtualizarRNTRCRequest request)
        {
            try
            {
                var proprietario = Repository.FirstOrDefault(c => c.IdProprietario == request.IdProprietario);
            
                if (proprietario == null)
                    return new ValidationResult().Add("Nenhum proprietário encontrado com o Id informado.");

                proprietario.RNTRC = request.RNTRC;

                Repository.Update(proprietario, false);
                Repository.SaveChanges();
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.GetBaseException().Message);
            }
        }

        public AtualizaBaseEquiparadoTacResponse AtualizarBaseEquiparadoTac(string cnpjcpf = null)
        {
            var proprietariosAtualizados = new AtualizaBaseEquiparadoTacResponse();
            
            try
            {
                var proprietarios = _dapper.ProprietariosSemEquiparadoTac(cnpjcpf);
                
                if (proprietarios.Count == 0)
                {
                    proprietariosAtualizados.Mensagem = "Nenhum proprietário sem EquiparadoTac foi encontrado na base de dados";
                    return proprietariosAtualizados;
                }
                
                //Populado apenas para aparecer no log
                proprietariosAtualizados.Objeto.AddRange(proprietarios);

                foreach (var proprietario in proprietarios)
                {
                    var consulta = EquiparadoTac(proprietario.CnpjCpf, proprietario.Rntrc.PadLeft(9, '0'), null, proprietario.IdEmpresa);

                    if (consulta.FalhaComunicacaoAntt)
                        continue;

                    proprietario.EquiparadoTac = consulta.Retorno != ERetornoConsultaTAC.NaoEquiparado;
                }
                
                proprietarios = proprietarios.Where(x => x.EquiparadoTac != null).ToList();

                if (proprietarios.Count == 0)
                {
                    proprietariosAtualizados.Mensagem = "Nenhum proprietário encontrado na base de dados preencheu o campo EquiparadoTac nas consultas da ANTT";
                    return proprietariosAtualizados;
                }

                var pessoasRequest = IntegrarPessoasCartao(proprietarios.Select(x => x.IdProprietario).ToList());
                
                CartoesService cartaoService = null;
                var idEmpresaService = 0;
                foreach (var proprietario in proprietarios)
                {
                    if (cartaoService == null || idEmpresaService != proprietario.IdEmpresa)
                    {
                        var tokenEmpresa = _empresaRepository.GetTokenMicroServices(proprietario.IdEmpresa);
                        cartaoService = new CartoesService(_cartoesServiceArgs, proprietario.IdEmpresa, tokenEmpresa, "00000000000", null);
                        idEmpresaService = proprietario.IdEmpresa;
                    }

                    var pessoaRequest = pessoasRequest.First(x => x.IdProprietario == proprietario.IdProprietario);
                    pessoaRequest.Info.TacEquiparado = proprietario.EquiparadoTac;
                    
                    var retorno = cartaoService.IntegrarPessoaMicroServico(pessoaRequest);

                    proprietario.PessoaIntegrada = retorno.Status == IntegrarPessoaResponseStatus.Sucesso;
                }
                
                proprietarios = proprietarios.Where(x => x.PessoaIntegrada).ToList();
                
                if (proprietarios.Count == 0)
                {
                    proprietariosAtualizados.Mensagem = "Nenhuma pessoa foi integrada no serviço de cartão";
                    return proprietariosAtualizados;
                }
                
                _dapper.AtualizaEquiparadoTac(proprietarios.Where(x => x.EquiparadoTac == true).Select(x => x.IdProprietario).ToList(), true);
                _dapper.AtualizaEquiparadoTac(proprietarios.Where(x => x.EquiparadoTac == false).Select(x => x.IdProprietario).ToList(), false);
                
            }
            catch (Exception e)
            {
                proprietariosAtualizados.Sucesso = false;
                proprietariosAtualizados.Mensagem = $"Erro ao atualizar registros - {e.Message}{(e.InnerException != null ? $" - {e.InnerException.Message}" : string.Empty)}";
            }
            
            return proprietariosAtualizados;
        }
        
        public AtualizaBaseEquiparadoTacResponse AtualizarCadastroServicoCartao(List<string> cnpjcpf)
        {
            var proprietariosAtualizados = new AtualizaBaseEquiparadoTacResponse();
            
            try
            {
                var proprietarios = _dapper.ProprietariosComEquiparadoTac(cnpjcpf);
                
                if (proprietarios.Count == 0)
                {
                    proprietariosAtualizados.Mensagem = "Nenhum proprietário com EquiparadoTac foi encontrado na base de dados";
                    return proprietariosAtualizados;
                }
                
                //Populado apenas para aparecer no log
                proprietariosAtualizados.Objeto.AddRange(proprietarios);

                var pessoasRequest = IntegrarPessoasCartao(proprietarios.Select(x => x.IdProprietario).ToList());
                
                CartoesService cartaoService = null;
                var idEmpresaService = 0;
                
                foreach (var proprietario in proprietarios)
                {
                    if (cartaoService == null || idEmpresaService != proprietario.IdEmpresa)
                    {
                        var tokenEmpresa = _empresaRepository.GetTokenMicroServices(proprietario.IdEmpresa);
                        cartaoService = new CartoesService(_cartoesServiceArgs, proprietario.IdEmpresa, tokenEmpresa, "00000000000", null);
                        idEmpresaService = proprietario.IdEmpresa;
                    }
                    
                    var retorno = cartaoService.IntegrarPessoaMicroServico(pessoasRequest.First(x => x.IdProprietario == proprietario.IdProprietario));

                    proprietario.PessoaIntegrada = retorno.Status == IntegrarPessoaResponseStatus.Sucesso;
                }
                
                if (!proprietarios.Any(x => x.PessoaIntegrada))
                    proprietariosAtualizados.Mensagem = "Nenhuma pessoa foi integrada no serviço de cartão";
            }
            catch (Exception e)
            {
                proprietariosAtualizados.Sucesso = false;
                proprietariosAtualizados.Mensagem = $"Erro ao integrar pessoas no serviço de cartão - {e.Message}{(e.InnerException != null ? $" - {e.InnerException.Message}" : string.Empty)}";
            }
            
            return proprietariosAtualizados;
        }
        
        private List<IntegrarPessoaRequestDTO> IntegrarPessoasCartao(ICollection<int> idsProprietario)
        {
            return Repository.AllAtivos().Where(x => idsProprietario.Contains(x.IdProprietario))
                .Select(x => new IntegrarPessoaRequestDTO
                {
                    IdProprietario = x.IdProprietario,
                    Nome = x.RazaoSocial,
                    NomeFantasia = x.NomeFantasia,
                    CpfCnpj = x.CNPJCPF,
                    Endereco = new IntegrarPessoaEnderecoRequestDTO
                    {
                        Cidade = x.Enderecos.FirstOrDefault().Cidade.IBGE,
                        Cep = x.Enderecos.FirstOrDefault().CEP,
                        Bairro = x.Enderecos.FirstOrDefault().Bairro,
                        Logradouro = x.Enderecos.FirstOrDefault().Endereco,
                        Numero = x.Enderecos.FirstOrDefault().Numero,
                        Complemento = x.Enderecos.FirstOrDefault().Complemento
                    },
                    Info = new IntegrarPessoaInfoRequestDTO
                    {
                        Sexo = SistemaInfoConsts.Masculino,
                        Rg = x.RG,
                        DataNascimento = x.DataNascimento,
                        NomePai = x.NomePai,
                        NomeMae = x.NomeMae,
                        Celular = x.Contatos.FirstOrDefault().Celular,
                        Telefone = x.Contatos.FirstOrDefault().Telefone,
                        Email = x.Contatos.FirstOrDefault().Email,
                        TacEquiparado = x.EquiparadoTac
                    },
                    Flags = new IntegrarPessoaTipoFlagsRequestDTO()
                    {
                        PontoCargaMoedeiro = false,
                        PontoDistribuicaoCartao = false
                    }
                }).ToList();
        }

        /// <summary>
        /// Busca os dados tanto para a grid quando para o relatório
        /// </summary>
        /// <returns></returns>
        private IQueryable<Proprietario> GetDataToGridAndReport(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var proprietarios =
                Repository
                    .GetAll()
                    .Include(o => o.Empresa)
                    .Include(o => o.Enderecos)
                    .Include(o => o.Enderecos.Select(c => c.Cidade));

            if (idEmpresa.HasValue)
                proprietarios = proprietarios.Where(o => o.IdEmpresa == idEmpresa);

            var filtroLocalidade = filters?.FirstOrDefault(o => o.Campo == "Localidade");

            if (filtroLocalidade != null)
            {
                proprietarios =
                    proprietarios.Where(o => o.Enderecos.FirstOrDefault().Cidade.Nome.Contains(filtroLocalidade.Valor));

                filters.Remove(filtroLocalidade);
            }
            
            var filtroTacAgregado = filters?.FirstOrDefault(o => o.Campo == "EquiparadoTac");

            if (filtroTacAgregado != null)
            {
                proprietarios = proprietarios.Where(o => o.EquiparadoTac == (filtroTacAgregado.Valor == "1"));
            }

            proprietarios = proprietarios.AplicarFiltrosDinamicos(filters);

            if (orderFilters?.Campo == "Localidade")
            {
                switch (orderFilters.Operador)
                {
                    case EOperadorOrder.Ascending:
                        proprietarios = proprietarios.OrderBy(o => o.Enderecos.FirstOrDefault().Cidade.Nome);
                        break;
                    case EOperadorOrder.Descending:
                        proprietarios = proprietarios.OrderByDescending(o => o.Enderecos.FirstOrDefault().Cidade.Nome);
                        break;
                }
            }
            else
            {
                proprietarios = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                    ? proprietarios.OrderByDescending(o => o.IdProprietario)
                    : proprietarios.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");
            }

            return proprietarios;
        }

        /// <summary>
        /// Formata o endereço do proprietario concatenando as propriedades
        /// </summary>
        /// <param name="proprietario"></param>
        /// <returns></returns>
        private static string FormatarEnderecoProprietario(Proprietario proprietario)
        {
            var enderecoProprietario = proprietario.Enderecos.FirstOrDefault();

            if (enderecoProprietario == null)
                return string.Empty;

            var endereco = string.Empty;

            endereco += $"{enderecoProprietario.Endereco}";

            if (enderecoProprietario.Numero.HasValue)
                endereco += $", Nº {enderecoProprietario.Numero}";

            if (!string.IsNullOrEmpty(enderecoProprietario.Complemento))
                endereco += $", {enderecoProprietario.Complemento}";

            if (!string.IsNullOrEmpty(enderecoProprietario.Bairro))
                endereco += $", {enderecoProprietario.Bairro}";

            if (!string.IsNullOrEmpty(enderecoProprietario.Cidade?.Nome))
                endereco += $", {enderecoProprietario.Cidade?.Nome}";

            if (!string.IsNullOrEmpty(enderecoProprietario.Estado?.Sigla))
                endereco += $" - {enderecoProprietario.Estado?.Sigla}";

            if (!string.IsNullOrEmpty(enderecoProprietario.CEP))
                endereco += $", CEP {enderecoProprietario.CEP.ToCEPFormato()}";

            return endereco;
        }
        
        public ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario, string cnpjEmpresa, int? idEmpresa = null)
        {
            try
            {
                if(!string.IsNullOrEmpty(cnpjEmpresa))
                    switch (_versaoAntt.Value)
                    {
                        case EVersaoAntt.Versao2:
                            _ciotV2Service.GerarToken(cnpjEmpresa);
                            break;
                        case EVersaoAntt.Versao3:
                            _ciotV3Service.GerarToken(cnpjEmpresa);
                            break;
                        default:
                            _ciotV2Service.GerarToken(cnpjEmpresa);
                            break;
                    }
                else if(idEmpresa.HasValue)
                    switch (_versaoAntt.Value)
                    {
                        case EVersaoAntt.Versao2:
                            _ciotV2Service.GerarToken(idEmpresa.Value);
                            break;
                        case EVersaoAntt.Versao3:
                            _ciotV3Service.GerarToken(idEmpresa.Value);
                            break;
                        default:
                            _ciotV2Service.GerarToken(idEmpresa.Value);
                            break;
                    }

                var retornoTac = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2Service.EquiparadoTac(cpfCnpjProprietario, rntrcProprietario)
                    : _ciotV3Service.EquiparadoTac(cpfCnpjProprietario, rntrcProprietario);

                if (retornoTac.Retorno == ERetornoConsultaTAC.Erro && retornoTac.FalhaComunicacaoAntt)
                {    
                    var proprietario = Repository.GetPorCpfCnpj(cpfCnpjProprietario);
                    
                    if (proprietario?.EquiparadoTac != null)
                    {
                        return new ConsultarSituacaoTransportadorInternalResponse
                        {
                            Retorno = proprietario.EquiparadoTac.Value ? ERetornoConsultaTAC.Equiparado : ERetornoConsultaTAC.NaoEquiparado,
                            Mensagem = string.Empty
                        };
                    }

                    // Caso o proprietário não tenha nunca sido utilizado e a ANTT esteja em contigência, retorna como Equiparado
                    return new ConsultarSituacaoTransportadorInternalResponse
                    {
                        Retorno = ERetornoConsultaTAC.Equiparado,
                        Mensagem = ""
                    };
                }

                return retornoTac;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"Erro ao consultar situação ANTT do proprietário CPF/CNPJ {cpfCnpjProprietario} - RNTRC {rntrcProprietario}");

                var proprietario = Repository.GetPorCpfCnpj(cpfCnpjProprietario);

                if (proprietario?.EquiparadoTac != null)
                {
                    return new ConsultarSituacaoTransportadorInternalResponse
                    {
                        Retorno = proprietario.EquiparadoTac.Value ? ERetornoConsultaTAC.Equiparado : ERetornoConsultaTAC.NaoEquiparado,
                        Mensagem = string.Empty
                    };
                }

                return new ConsultarSituacaoTransportadorInternalResponse
                {
                    Retorno = ERetornoConsultaTAC.Erro,
                    Mensagem = $"Erro ao consultar a situação do contratado {cpfCnpjProprietario.FormatarCpfCnpj()} na ANTT."
                };
            }
        }

        public ConsultaSituacaoTransportadorResponse ConsultarSituacaoAntt(string cpfCnpj, string cnpjEmpresa)
        {
            var empresaId = _empresaRepository.GetIdPorCnpj(cnpjEmpresa);
            var proprietario = Repository.Where(o => o.CNPJCPF == cpfCnpj && o.IdEmpresa == empresaId).FirstOrDefault();

            if (proprietario == null)
                return new ConsultaSituacaoTransportadorResponse(false, $"Proprietário de documento {cpfCnpj.ToCpfOrCnpj()} não encontrado na base de dados.");

            var retorno =  _ciotV3Service.ConsultarSituacaoProprietarioAntt(cpfCnpj, proprietario.RNTRC, cnpjEmpresa);

            if (retorno.Sucesso.HasValue && !retorno.Sucesso.Value)
            {
                return new ConsultaSituacaoTransportadorResponse
                {
                    Sucesso = false,
                    Mensagem = retorno.ExceptionMessage
                };
            }

            if (retorno.Sucesso.HasValue && retorno.Sucesso.Value)
                return new ConsultaSituacaoTransportadorResponse
                {
                    Sucesso = true,
                    Mensagem = string.Empty,
                    Objeto = new ConsultaSituacaoTransportadorObjetoResponse
                    {
                        IdProprietario = proprietario.IdProprietario,
                        EquiparadoTac = retorno.EquiparadoTAC ?? proprietario.EquiparadoTac ?? false,
                        RntrcAtivo = retorno.RntrcAtivo ?? true,
                        TipoTransportador = retorno.TipoTransportador,
                        DataValidadeRntrc = retorno.DataValidadeRNTRC.HasValue  ? retorno.DataValidadeRNTRC.Value.ToString("dd/MM/yyyy") : string.Empty
                    }
                };

            return null;
        }

        public ProprietarioAnttDto GetDadosProprietarioAntt(string cnpjCpf, string cnpjEmpresa)
        {
            return Repository.GetDadosProprietarioAntt(cnpjCpf, cnpjEmpresa);
        }

        public void AtualizaEquiparadoTac(int proprietarioId, bool equiparadoTac)
        {
            _dapper.AtualizaEquiparadoTac(proprietarioId, equiparadoTac);
        }
    }
}

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.DataMediaServer;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class ViagemEventoProtocoloAnexoService : ServiceBase, IViagemEventoProtocoloAnexoService
    {
        private readonly IViagemEventoProtocoloAnexoRepository _repository;
        private readonly IDataMediaServerRepository _mongoRepository;

        public ViagemEventoProtocoloAnexoService(IViagemEventoProtocoloAnexoRepository repository, IDataMediaServerRepository mongoRepository)
        {
            _repository = repository;
            _mongoRepository = mongoRepository;
        }

        public ValidationResult Add(ViagemEventoProtocoloAnexoModel viagemEventoProtocoloAnexo)
        {
            try
            {
                if (viagemEventoProtocoloAnexo.IdViagemEventoProtocoloAnexoModel == 0)
                {
                    viagemEventoProtocoloAnexo.Validate();
                            
                    var file = new FileInfo(viagemEventoProtocoloAnexo.NomeArquivo);
                    var mimeType = MimeTypeHelper.GetMimeType(file.Extension);
                    var resultadoCadastroMongo = _mongoRepository.Add(0, viagemEventoProtocoloAnexo.Base64, viagemEventoProtocoloAnexo.NomeArquivo, mimeType);
    
                    _repository.Add(new ViagemEventoProtocoloAnexo
                    {
                        IdViagemEvento = viagemEventoProtocoloAnexo.IdViagemEvento, Descricao = viagemEventoProtocoloAnexo.Detalhes,
                        Token = resultadoCadastroMongo.ToString(),
                        TamanhoArquivo = viagemEventoProtocoloAnexo.Tamanho
                    });
                }
                
                return new ValidationResult();
    
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult AddListOfAnexos(List<ViagemEventoProtocoloAnexoModel> viagemEventoProtocoloAnexosModel, List<int> idsAnexosRemover)
        {
            try
            {
                if (idsAnexosRemover != null && idsAnexosRemover.Any())
                {
                    var anexos = _repository.Where(o => idsAnexosRemover.Contains(o.IdViagemEventoProtocoloAnexo));

                    foreach (var anexo in anexos)
                    {
                        _mongoRepository.DeleteByToken(anexo.Token);
                        _repository.Delete(anexo);
                    }
                }
                
                if (viagemEventoProtocoloAnexosModel != null)
                    foreach (var item in viagemEventoProtocoloAnexosModel)
                    {
                        if (item.IdViagemEventoProtocoloAnexoModel == 0)
                        {
                            item.Validate();
                            
                            var file = new FileInfo(item.NomeArquivo);
                            var mimeType = MimeTypeHelper.GetMimeType(file.Extension);
                            var resultadoCadastroMongo = _mongoRepository.Add(0, item.Base64, item.NomeArquivo, mimeType);
    
                            _repository.Add(new ViagemEventoProtocoloAnexo
                            {
                                IdViagemEvento = item.IdViagemEvento, Descricao = item.Detalhes,
                                Token = resultadoCadastroMongo.ToString(),
                                TamanhoArquivo = item.Tamanho
                            });
                        }
                    }
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult RemoverAnexos(List<int> idsAnexosRemover)
        {
            try
            {
                if (idsAnexosRemover != null && idsAnexosRemover.Any())
                {
                    var anexos = _repository.Where(o => idsAnexosRemover.Contains(o.IdViagemEventoProtocoloAnexo));

                    foreach (var anexo in anexos)
                    {
                        _mongoRepository.DeleteByToken(anexo.Token);
                        _repository.Delete(anexo);
                    }
                }
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
        
        public List<ViagemEventoProtocoloAnexoModel> GetAnexosCadastrados(int idViagemEvento)
        {
            var listaAnexos = new List<ViagemEventoProtocoloAnexoModel>();
            var anexos = _repository.Where(o => o.IdViagemEvento == idViagemEvento);
            
            foreach (var anexo in anexos)
            {
                var media = _mongoRepository.GetMedia(anexo.Token);
                
                listaAnexos.Add(new ViagemEventoProtocoloAnexoModel
                {
                    IdViagemEventoProtocoloAnexoModel = anexo.IdViagemEventoProtocoloAnexo,
                    IdViagemEvento = anexo.IdViagemEvento,
                    NomeArquivo = media?.FileName,
                    Base64 = media?.Data,
                    Detalhes = anexo.Descricao,
                    Tamanho = anexo.TamanhoArquivo,
                    Token = anexo.Token
                });
            }

            return listaAnexos;
        }
    }
}
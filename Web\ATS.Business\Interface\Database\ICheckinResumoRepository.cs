using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Database
{
    public interface ICheckinResumoRepository : IRepository<CheckinResumo>
    {
        ValidationResult Salvar(CheckinResumo checkinResumo);

        ValidationResult Editar(CheckinResumo checkinResumo);

        CheckinResumo ConsultarResumoExistente(int? idUsuario, int? idEmpresa);

        CheckinResumoConsultaModel ConsultarCheckinResumosPaginado(int empresaId, int itensPorPagina, int pagina, DateTime? dataInicio, DateTime? dataFim);
    }
}
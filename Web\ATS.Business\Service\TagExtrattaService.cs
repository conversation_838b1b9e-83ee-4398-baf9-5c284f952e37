﻿using System;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Pedagio.FaturaTAG;
using ATS.CrossCutting.Reports.Pedagio.GridConciliacaoTAG;
using ATS.CrossCutting.Reports.Pedagio.GridFaturamentoTAG;
using ATS.CrossCutting.Reports.Pedagio.GridPagamentosTAG;
using ATS.CrossCutting.Reports.Pedagio.GridPassagemWebhookTAG;
using ATS.CrossCutting.Reports.Pedagio.PracasPedagioMoveMais;
using ATS.CrossCutting.Reports.TagExtratta.ConsultaSituacaoTags;
using ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using NLog;
using TagExtrattaClient;
using EOperadorOrder = ATS.Domain.Enum.EOperadorOrder;
using EStatusTag = ATS.Domain.Enum.EStatusTag;
using FornecedorEnum = ATS.Domain.Enum.FornecedorEnum;
using RelatorioGridPagamentosTagItemDataType = ATS.CrossCutting.Reports.Pedagio.GridPagamentosTAG.RelatorioGridPagamentosTagItemDataType;

namespace ATS.Domain.Service
{
    public class TagExtrattaService : ServiceBase, ITagExtrattaService
    {
        private readonly RelatorioConsultaSituacaoTags _relatorioConsultaSituacaoTags;
        private readonly ITagExtrattaExternalRepository _externalRepository;
        private readonly IEmpresaRepository _empresaRepository;

        public TagExtrattaService(ITagExtrattaExternalRepository externalRepository,IEmpresaRepository empresaRepository)
        {
            _externalRepository = externalRepository;
            _empresaRepository = empresaRepository;
            _relatorioConsultaSituacaoTags = new RelatorioConsultaSituacaoTags();
        }

        public byte[] GerarRelatorioConsultaSituacaoTags(string logo, string extensao,
            List<RelatorioConsultaSituacaoTagsDataType> relatorio)
        {
            return _relatorioConsultaSituacaoTags.GetReport(relatorio, extensao, logo);
        }

        public BusinessResult<RemessasGetModelResponse> ConsultaRemessas(int? take, int? page,
            List<QueryFilters> filters, OrderFilters order)
        {
            try
            {
                var filterApi = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters);
                var operador = order.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1;

                var result = _externalRepository
                    .GetRemessas(take, page, filterApi, operador, order.Campo);

                if (result.Success)
                    return BusinessResult<RemessasGetModelResponse>.Valid(result.Result);

                return BusinessResult<RemessasGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<RemessasGetModelResponse>.Error("Falha ao consultar remessas.");
            }
        }

        public BusinessResult<RemessaGetModelResponse> GetRemessa(int id)
        {
            try
            {
                var result = _externalRepository
                    .GetRemessa(id);

                if (result.Success)
                    return BusinessResult<RemessaGetModelResponse>.Valid(result.Result);

                return BusinessResult<RemessaGetModelResponse>.Error(result.StatusCode == 404
                    ? "Nenhuma remessa encontrada."
                    : result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<RemessaGetModelResponse>.Error("Falha ao consultar remessa.");
            }
        }

        public BusinessResult<List<TagGetModelResponse>> GetLote(long min, long max, EStatusTag? statusTag)
        {
            try
            {
                var result = _externalRepository.GetTagsLote(min, max, statusTag.GetHashCode());

                if (result.Success)
                    return BusinessResult<List<TagGetModelResponse>>.Valid(result.Result);

                return BusinessResult<List<TagGetModelResponse>>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<List<TagGetModelResponse>>.Error("Falha ao consultar lote de tags.");
            }
        }

        public BusinessResult EnviarRemessa(RemessaCadastrarModelRequest request)
        {
            try
            {
                var result = _externalRepository.CadastrarRemessa(request);

                if (result.Success)
                    return BusinessResult.Valid();

                return BusinessResult.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult.Error("Falha ao enviar remessa.");
            }
        }

        public BusinessResult ReceberRemessa(int idRemessa)
        {
            try
            {
                var result = _externalRepository.ReceberRemessa(idRemessa);

                if (result.Success)
                    return BusinessResult.Valid();

                return BusinessResult.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult.Error("Falha ao enviar remessa.");
            }
        }

        public BusinessResult<TagGetSerialModelResponse> GetTagSerial(long serial, EStatusTag? statusTag)
        {
            try
            {
                var result = _externalRepository.GetTagSerial(serial, statusTag.GetHashCode());

                if (result.Success)
                    return BusinessResult<TagGetSerialModelResponse>.Valid(result.Result);

                return BusinessResult<TagGetSerialModelResponse>.Error(result.StatusCode == 404
                    ? "Nenhuma TAG encontrada."
                    : result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<TagGetSerialModelResponse>.Error("Falha ao consultar serial da TAG.");
            }
        }

        public BusinessResult<TagsGetModelResponse> GetTags(int? take, int? page, List<QueryFilters> filters,
            OrderFilters order, List<long> tagsSelecionadas = null, DateTime? dataInicio = null,
            DateTime? dataFim = null)
        {
            try
            {
                var filterApi = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters);
                var operador = order.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1;

                var result = _externalRepository.GetTags(take, page, filterApi, operador, order.Campo, tagsSelecionadas,
                    dataInicio, dataFim);

                if (result.Success)
                    return BusinessResult<TagsGetModelResponse>.Valid(result.Result);

                return BusinessResult<TagsGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<TagsGetModelResponse>.Error("Falha ao consultar TAGs.");
            }
        }

        public BusinessResult<VeiculoGetModelResponse> GetVeiculo(string placa)
        {
            try
            {
                var result = _externalRepository.GetVeiculo(placa);

                if (result.Success)
                    return BusinessResult<VeiculoGetModelResponse>.Valid(result.Result);

                return BusinessResult<VeiculoGetModelResponse>.Error(result.StatusCode == 404
                    ? "Nenhum veículo encontrado."
                    : result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<VeiculoGetModelResponse>.Error("Falha ao consultar veículo.");
            }
        }

        public BusinessResult<ModelosVeiculoMoveMaisGetModelResponse> GetModelosMoveMais(
            GetModelosVeiculoMoveMaisRequest request)
        {
            try
            {
                var result = _externalRepository.GetModelosMoveMais(request);

                if (result.Success)
                    return BusinessResult<ModelosVeiculoMoveMaisGetModelResponse>.Valid(result.Result);

                return BusinessResult<ModelosVeiculoMoveMaisGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<ModelosVeiculoMoveMaisGetModelResponse>.Error(
                    "Falha ao consultar modelos da move mais.");
            }
        }

        public BusinessResult<string> Vincular(TagVincularModelRequest request, long serial, string placa)
        {
            try
            {
                var result = _externalRepository.Vincular(request, serial, placa);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao vincular TAG ao veículo.");
            }
        }

        public BusinessResult<string> Bloquear(long serial)
        {
            try
            {
                var result = _externalRepository.Bloquear(serial);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao bloquear TAG do veículo.");
            }
        }

        public BusinessResult<string> Desbloquear(long serial)
        {
            try
            {
                var result = _externalRepository.Desbloquear(serial);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao desbloquear TAG do veículo.");
            }
        }

        public BusinessResult<string> Desvincular(long serial)
        {
            try
            {
                var result = _externalRepository.Desvincular(serial);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao desvincular TAG do veículo.");
            }
        }

        public BusinessResult<string> CadastrarBloqueios(BloqueioCadastrarModelRequest request)
        {
            try
            {
                var result = _externalRepository.CadastrarBloqueios(request);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao cadastrar bloqueio usuário TAG.");
            }
        }

        public BusinessResult<BloqueioGetModelResponse> GetBloqueios(int usuarioId)
        {
            try
            {
                var result = _externalRepository.GetBloqueios(usuarioId);

                if (result.Success)
                    return BusinessResult<BloqueioGetModelResponse>.Valid(result.Result);

                return BusinessResult<BloqueioGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<BloqueioGetModelResponse>.Error("Falha ao consultar bloqueios TAG do usuário.");
            }
        }

        public BusinessResult<ConsultarGridValePedagioHubModelResponse> GridValePedagioHub(int? take, int? page,
            List<QueryFilters> filters, OrderFilters order, DateTime? dataInicio, DateTime? dataFim)
        {
            try
            {
                var filterApi = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters);

                var operador = order.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1;

                var result = _externalRepository
                    .GridValePedagioHub(take, page, filterApi, operador, order.Campo, dataInicio, dataFim);

                if (result.Success)
                    return BusinessResult<ConsultarGridValePedagioHubModelResponse>.Valid(result.Result);

                return BusinessResult<ConsultarGridValePedagioHubModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<ConsultarGridValePedagioHubModelResponse>.Error("Falha ao consultar vale pedágio da Hub.");
            }
        }

        public byte[] GerarRelatorioPracasPedagioMoveMais(PracasPedagioResponseDTO data, string extensao,
            int? idEmpresa)
        {
            try
            {
                var empresa = _empresaRepository
                    .Select(x => new { x.IdEmpresa, x.Logo })
                    .FirstOrDefault(x => x.IdEmpresa == idEmpresa);

                var logo = empresa?.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);

                return new RelatorioPracasPedagioMoveMais().GetReport(extensao,
                    Mapper.Map<RelatorioPracasPedagioMoveMaisDataType>(data), logo);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public byte[] GerarRelatorioGridValePedagioHub(List<GridValePedagioHubItemResponse> items, string extensao,
            int? idEmpresa)
        {
            try
            {
                var empresa = _empresaRepository
                    .Select(x => new { x.IdEmpresa, x.Logo })
                    .FirstOrDefault(x => x.IdEmpresa == idEmpresa);

                var logo = empresa?.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);
                var data = new RelatorioGridConciliacaoTagDataType
                {
                    items = items.AsQueryable().ProjectTo<RelatorioGridConciliacaoTagItemDataType>().ToList()
                };

                return new RelatorioGridConciliacaoTag().GetReport(extensao, data, logo);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<string> CadastrarModelosMoveMais()
        {
            try
            {
                var result = _externalRepository.CadastrarModelosMoveMais();

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Messages);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao cadastrar modelos de veículo da Move Mais");
            }
        }

        public BusinessResult<string> CadastrarEstoqueTags()
        {
            try
            {
                var result = _externalRepository.CadastrarEstoqueTags();

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao cadastrar estoque de TAGs da Move Mais");
            }
        }

        public BusinessResult<Guid> NotificarPassagemPraca(PassagemPracaPedagioModelRequest request)
        {
            try
            {
                var result = _externalRepository.NotificarPassagemPraca(request);

                if (result.Success)
                    return BusinessResult<Guid>.Valid(result.Result);

                return BusinessResult<Guid>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<Guid>.Error("Falha ao notificar webhook passagem praça pedágio!");
            }
        }

        public BusinessResult<PagamentosGetModelResponse> GetPagamentos(int? take, int? page,
            List<QueryFilters> filters, OrderFilters order, DateTime? dataInicio, DateTime? dataFim, int? idEmpresa,FornecedorEnum fornecedor)
        {
            try
            {
                var filterApi = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters);

                var operador = order.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1;

                var fornecedorTag = (FornecedorTagEnum) fornecedor;

                var result = _externalRepository.GetPagamentos(take, page, filterApi, operador, order.Campo, dataInicio,
                    dataFim, idEmpresa,fornecedorTag);

                if (result.Success)
                    return BusinessResult<PagamentosGetModelResponse>.Valid(result.Result);

                return BusinessResult<PagamentosGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<PagamentosGetModelResponse>.Error(
                    "Falha ao realizar a consulta");
            }
        }

        public byte[] GerarRelatorioGridPagamentos(List<PagamentosItemTagResponse> items, string extensao)
        {
            try
            {
                var data = new RelatorioGridPagamentosTagDataType
                {
                    items = items.AsQueryable().ProjectTo<RelatorioGridPagamentosTagItemDataType>().ToList()
                };

                return new RelatorioGridPagamentosTag().GetReport(extensao, data);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<PagamentosItemGetModelResponse> GetPagamento(long id)
        {
            try
            {
                var result = _externalRepository.GetPagamento(id);

                if (result.Success)
                    return BusinessResult<PagamentosItemGetModelResponse>.Valid(result.Result);

                return BusinessResult<PagamentosItemGetModelResponse>.Error(result.StatusCode == 404
                    ? "Nenhum pagamento encontrado."
                    : result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<PagamentosItemGetModelResponse>.Error("Falha ao consultar pagamento.");
            }
        }

        public BusinessResult<string> PagamentoManualEventoTag(PagamentoManualRequest request)
        {
            try
            {
                var result = _externalRepository.PagamentoManualEventoTag(request);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao realizar pagamento manual.");
            }
        }

        public BusinessResult<string> EstornoManualEventoTag(PagamentoManualRequest request)
        {
            try
            {
                var result = _externalRepository.EstornoManualEventoTag(request);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao realizar estorno manual.");
            }
        }

        public BusinessResult<PassagensPracaGetModelResponse> GetPassagensPraca(int? take, int? page,
            List<QueryFilters> filters, OrderFilters order, DateTime? dataInicio, DateTime? dataFim,int? empresaId)
        {
            try
            {
                var filterApi = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters);

                var operador = order.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1;

                var result = _externalRepository.GetPassagensPraca(take,
                    page, filterApi, operador, order.Campo, dataInicio, dataFim,empresaId);

                if (result.Success)
                    return BusinessResult<PassagensPracaGetModelResponse>.Valid(result.Result);

                return BusinessResult<PassagensPracaGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<PassagensPracaGetModelResponse>.Error("Registros não encontrados.");
            }
        }

        public byte[] GerarRelatorioGridPassagemWebhook(List<GridPassagemWebhookItemResponse> items, string extensao)
        {
            try
            {
                var data = new GridPassagemWebhookTagDataType
                {
                    items = items.AsQueryable().ProjectTo<RelatorioGridPassagemWebhookTagItemDataType>().ToList()
                };

                return new RelatorioGridPassagemWebhookTag().GetReport(extensao, data);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }

        public BusinessResult<string> DesvincularEmpresa(long serial)
        {
            try
            {
                var result = _externalRepository.DesvincularEmpresa(serial);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error("Falha ao desvincular TAG da empresa.");
            }
        }

        public BusinessResult<FaturamentoGetModelResponse> ConsultaFaturamento(int? take, int? page,
            List<QueryFilters> filters, OrderFilters order, DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor)
        {
            try
            {
                var filterApi = Mapper.Map<List<QueryFilters>, List<ApiQueryFilters>>(filters);
                var operador = order.Operador == EOperadorOrder.Ascending
                    ? TagExtrattaClient.EOperadorOrder._0
                    : TagExtrattaClient.EOperadorOrder._1;
                
                var fornecedorTag = (FornecedorTagEnum) fornecedor;

                var result = _externalRepository
                    .GetFaturamento(take, page, filterApi, operador, order.Campo, dataInicio, dataFim,fornecedorTag);

                if (result.Success)
                    return BusinessResult<FaturamentoGetModelResponse>.Valid(result.Result);

                return BusinessResult<FaturamentoGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<FaturamentoGetModelResponse>.Error("Falha ao consultar faturamento.");
            }
        }

        public BusinessResult<FaturamentoTotalizadorGetModelResponse> ConsultaTotalizadorFaturamento(
            FaturamentoTotalizadorGetRequest request)
        {
            try
            {
                var result = _externalRepository
                    .GetTotalizadorFaturamento(request);

                if (result.Success)
                    return BusinessResult<FaturamentoTotalizadorGetModelResponse>.Valid(result.Result);

                return BusinessResult<FaturamentoTotalizadorGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<FaturamentoTotalizadorGetModelResponse>.Error("Falha ao realizar a consulta");
            }
        }

        public byte[] GerarRelatorioGridFaturamento(List<FaturamentoTagItemResponse> items,
            FaturamentoTotalizadorResponse total, string extensao)
        {
            try
            {
                var data = new RelatorioGridFaturamentoTagDataType
                {
                    items = items.AsQueryable().ProjectTo<RelatorioGridFaturamentoTagItemDataType>().ToList()
                };

                var totalizadorData = Mapper.Map<TotalizadorFaturamentoTagDataType>(total);

                return new RelatorioGridFaturamentoTag().GetReport(extensao, data, totalizadorData);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }


        public BusinessResult<FaturaGetModelResponse> ConsultaFatura(DateTime dataInicio, DateTime dataFim,
            int empresaId, FornecedorTagEnum fornecedorTag)
        {
            try
            {
                var result = _externalRepository.GetFatura(dataInicio, dataFim, empresaId, fornecedorTag);

                if (result.Success)
                    return BusinessResult<FaturaGetModelResponse>.Valid(result.Result);

                return BusinessResult<FaturaGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<FaturaGetModelResponse>.Error("Falha ao consultar dados da fatura");
            }
        }

        public byte[] GerarRelatorioFatura(FaturaTagGetResponse request, FornecedorTagEnum fornecedorTag)
        {
            try
            {
                var data = Mapper.Map<RelatorioFaturaTagDataType>(request);

                return new RelatorioFaturaTag().GetReport(data, fornecedorTag);
            }
            catch (Exception)
            {
                return new byte[] { };
            }
        }
        
        public BusinessResult<PassagensPedagioCompraHubGetModelResponse> GetPassagensPedagioCompraHub(int compraId, FornecedorTagEnum fornecedor)
        {
            try
            {
                var result = _externalRepository.GetPassagensPedagioCompraHub(compraId, fornecedor);

                if (result.Success)
                    return BusinessResult<PassagensPedagioCompraHubGetModelResponse>.Valid(result.Result);

                return BusinessResult<PassagensPedagioCompraHubGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<PassagensPedagioCompraHubGetModelResponse>.Error("Falha ao consultar dados das passagens deste VPO");
            }
        }

        public BusinessResult<PlacaFornecedorResponse> ConsultarPlacasFornecedor(string placa, FornecedorEnum fornecedor)
        {
            try
            {
                var fornecedorTag = Mapper.Map<FornecedorTagEnum>(fornecedor);

                var result = _externalRepository.ConsultarPlacasFornecedor(placa, fornecedorTag);

                if (result.Success)
                {
                    var statusPlaca = Mapper.Map<StatusPlaca>(result.Result.Status);
                    var resultPlacaFornecedor = new PlacaFornecedorResponse { Status = statusPlaca, Mensagem = result.Result.Mensagem };

                    return BusinessResult<PlacaFornecedorResponse>.Valid(resultPlacaFornecedor);
                }
                    

                return BusinessResult<PlacaFornecedorResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<PlacaFornecedorResponse>.Error("Falha ao consultar viabilidade da placa");
            }
        }
        
        public BusinessResult<string> ContestarPassagem(ContestacaoPassagemPedagioModelRequest request)
        {
            try
            {
                var result = _externalRepository.ContestarPassagem(request);

                if (result.Success)
                    return BusinessResult<string>.Valid(result.Result);

                return BusinessResult<string>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<string>.Error($"Falha ao contestar passagem {e}");
            }
        }

        public BusinessResult<SaldoValePedagioVeiculoTagResponse> ConsultarSaldoValePedagioVeiculo(string placa)
        {
            try
            {
                var result = _externalRepository.ConsultarSaldoValePedagioVeiculo(placa);

                if (result.Success)
                    return BusinessResult<SaldoValePedagioVeiculoTagResponse>.Valid(new SaldoValePedagioVeiculoTagResponse
                    {
                        Saldo = result.Result.Saldo.FormatMoney()
                    });
                
                return BusinessResult<SaldoValePedagioVeiculoTagResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<SaldoValePedagioVeiculoTagResponse>.Error("Falha ao consultar saldo vale pedágio do veículo");
            }
        }
        
        public BusinessResult<PassagensPracaVeiculoGetModelResponse> GetPassagensVeiculoPraca(DateTime? dataInicio, DateTime? dataFim,string placa)
        {
            try
            {
                var result = _externalRepository.GetPassagensVeiculoPraca(dataInicio, dataFim,placa);

                if (result.Success)
                    return BusinessResult<PassagensPracaVeiculoGetModelResponse>.Valid(result.Result);

                return BusinessResult<PassagensPracaVeiculoGetModelResponse>.Error(result.Messages);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e,$"{nameof(TagExtrattaService)} / {e.Message}");
                return BusinessResult<PassagensPracaVeiculoGetModelResponse>.Error("Registros não encontrados.");
            }
        }
    }
}
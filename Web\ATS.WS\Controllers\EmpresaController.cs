﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request.Base;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Models.Mobile.Common;
using ATS.Domain.Service;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class EmpresaController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEmpresaService _empresaService;

        public EmpresaController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IEmpresaApp empresaApp, IEmpresaService empresaService) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _empresaApp = empresaApp;
            _empresaService = empresaService;
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string GetLogoPorCnpj(RequestBase request, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_empresaApp.GetLogoPorCnpj(cnpjEmpresa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string GetParametrosPorCnpj(RequestBase request, string cnpjEmpresa)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(new Retorno<object>(true, _empresaApp.GetParametrosPorCnpj(cnpjEmpresa)));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public void CriarAutenticacaoEmpresaPorCnpj(string cnpj)
        {
            var empresa = _empresaApp.Get(cnpj);

            if (empresa != null)
                _empresaService.CreateAuthenticationByCompany(empresa);
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string GetTokenAdministradora(string token)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(null, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(new Retorno<object>(true, _empresaApp.GetTokenAdministradora()));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
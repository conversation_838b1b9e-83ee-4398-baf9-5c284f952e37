using ATS.Domain.Entities;
using ATS.Domain.Enum;
using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class ViagemSolicitacaoAbono
    {

        /// <summary>
        /// Solicitação de abono
        /// </summary>
        public int IdViagemSolicitacao { get; set; }

        /// <summary>
        /// Motivo solicitação...
        /// </summary>
        public int? IdMotivo { get; set; }

        /// <summary>
        /// Detalhamento
        /// </summary>
        public string Detalhamento { get; set; }

        /// <summary>
        /// Status
        /// </summary>
        public EStatusAbono Status { get; set; }

        public int IdViagemEvento { get; set; }

        public int? IdUsuario { get; set; }

        public string NomeUsuario { get; set; }
        
        public string DescricaoMotivo { get; set; }

        public DateTime DataSolicitacao { get; set; }

        public DateTime?  DataAtualizacao { get; set; }

        #region Referências

        public virtual Motivo Motivo { get; set; }

        public virtual ViagemEvento ViagemEvento { get; set; }

        public virtual Usuario Usuario { get; set; }


        #endregion

    }
}
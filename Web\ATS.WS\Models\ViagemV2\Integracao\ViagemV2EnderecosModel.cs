using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2EnderecosModel
    {
        /// <summary>
        /// Endereço de coleta da mercadoria
        /// </summary>
        public string EnderecoColeta { get; set; }

        /// <summary>
        /// Endereço de entrega da mercadoria
        /// </summary>
        public string EnderecoEntrega { get; set; }

        public string CepOrigem { get; set; }

        public string CepDestino { get; set; }
        
        public ValidationResult ValidarEntrada()
        {
            if (!string.IsNullOrEmpty(EnderecoColeta))
                if (EnderecoColeta.Length > 250)
                    return new ValidationResult().Add("Endereço de coleta não pode conter mais que 250 caracteres.", EFaultType.Error);

            if (!string.IsNullOrEmpty(EnderecoEntrega))
                if (EnderecoEntrega.Length > 250)
                    return new ValidationResult().Add("Endereço de entrega não pode conter mais que 250 caracteres.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(CepOrigem))
                if (CepOrigem.Length > 8)
                    return new ValidationResult().Add("CEP de origem não pode conter maiis que 8 caracteres.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(CepDestino))
                if (CepDestino.Length > 8)
                    return new ValidationResult().Add("CEP de destino não pode conter maiis que 8 caracteres.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
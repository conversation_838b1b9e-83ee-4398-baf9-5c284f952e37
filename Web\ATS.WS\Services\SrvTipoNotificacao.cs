﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using AutoMapper;
using System;
using ATS.Domain.Interface.Service;

namespace ATS.WS.Services
{
    public class SrvTipoNotificacao : SrvBase
    {
        private readonly ITipoNotificacaoApp _tipoNotificacaoApp;

        public SrvTipoNotificacao(ITipoNotificacaoApp tipoNotificacaoApp)
        {
            _tipoNotificacaoApp = tipoNotificacaoApp;
        }

        public ValidationResult Cadastrar(TipoNotificacaoRequestModel @params, EPerfil perfilUsuario, int? idEmpresa)
        {

            try
            {
                var validationResult = new ValidationResult();

                if (perfilUsuario != EPerfil.Administrador)
                {
                    if (idEmpresa != null)
                        @params.IdEmpresa = (int)idEmpresa;
                    else
                        validationResult.Add("Usuário não possui vinculo com empresa.");
                }

                if (@params.IdEmpresa <= 0)
                    validationResult.Add("Empresa não informada.");

                if (@params.Descricao == null || @params.Descricao.Length <= 0)
                    validationResult.Add("Resposta não informada.");

                var tipoNotificacao = Mapper.Map<TipoNotificacaoRequestModel, TipoNotificacao>(@params);

                if (!validationResult.IsValid)
                    return validationResult;

                validationResult = _tipoNotificacaoApp.Add(tipoNotificacao);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Editar(TipoNotificacaoRequestModel @params, EPerfil perfilUsuario, int? idEmpresa)
        {
            try
            {
                var validationResult = new ValidationResult();

                if (perfilUsuario != EPerfil.Administrador)
                {
                    if (idEmpresa != null)
                        @params.IdEmpresa = (int)idEmpresa;
                    else
                        validationResult.Add("Usuário não possui vinculo com empresa.");
                }

                if (@params.IdEmpresa <= 0)
                    validationResult.Add("Empresa não informada.");

                if (@params.Descricao == null || @params.Descricao.Length <= 0)
                    validationResult.Add("Descrição não informada.");

                var tipoNot = Mapper.Map<TipoNotificacaoRequestModel, TipoNotificacao>(@params);

                if (!validationResult.IsValid)
                    return validationResult;

                validationResult = _tipoNotificacaoApp.Update(tipoNot);

                return new ValidationResult();

            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}
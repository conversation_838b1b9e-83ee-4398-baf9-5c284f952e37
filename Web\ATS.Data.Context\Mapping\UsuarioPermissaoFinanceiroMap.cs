using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioPermissaoFinanceiroMap : EntityTypeConfiguration<UsuarioPermissaoFinanceiro>
    {

        public UsuarioPermissaoFinanceiroMap()
        {
            ToTable("USUARIO_PERMISSAO_FINANCEIRO");

            HasKey(t => new {t.IdUsuario,t.IdBloqueioGestorTipo});

            Property(u => u.DesbloquearFinanceiro).IsRequired();
            
            HasRequired(u => u.Usuario)
                .WithMany()
                .HasForeign<PERSON>ey(u => u.IdUsuario);
            
            HasRequired(u => u.BloqueioFinanceiroTipo)
                .WithMany()
                .HasForeign<PERSON>ey(u => u.IdBloqueioGestorTipo);

        }
    }
}
using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class AdministradoraPlataformaApp : BaseApp<IAdministradoraPlataformaService>, IAdministradoraPlataformaApp
    {
        public AdministradoraPlataformaApp(IAdministradoraPlataformaService service) : base(service)
        {
        }

        public IQueryable<AdministradoraPlataforma> Get(int idAdministradoraPlataforma)
        {
            return Service.Get(idAdministradoraPlataforma);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Helpers;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Grid.Base;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Models.Proprietarios;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.ControllersATS
{
    public class ProprietarioAtsController : BaseAtsController<IProprietarioApp>
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly SrvProprietario _srvProprietario;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ISerproApp _serproApp;
        private readonly IExtrattaBizApiClient _bizApiClient;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IProprietarioService _proprietarioService;

        public ProprietarioAtsController(IProprietarioApp app, 
            IUserIdentity userIdentity,
            IParametrosApp parametrosApp,
            SrvProprietario srvProprietario, 
            IProprietarioApp proprietarioApp,
            IParametrosEmpresaService parametrosEmpresa,
            ISerproApp serproApp, 
            IExtrattaBizApiClient bizApiClient, 
            IParametrosUsuarioService parametrosUsuarioService, IProprietarioService proprietarioService) : base(app, userIdentity)
        {
            _parametrosApp = parametrosApp;
            _srvProprietario = srvProprietario;
            _proprietarioApp = proprietarioApp;
            _parametrosEmpresa = parametrosEmpresa;
            _serproApp = serproApp;
            _bizApiClient = bizApiClient;
            _parametrosUsuarioService = parametrosUsuarioService;
            _proprietarioService = proprietarioService;
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(ProprietarioCreateModel model)
        {
            try
            {
                /*if (SessionUser.IdEmpresa.HasValue)
                {
                    if (model.CNPJCPF.OnlyNumbers().Length == 11 &&
                        _parametrosEmpresa.GetPermiteCadastrarProprietarioComCpfFicticio(SessionUser.IdEmpresa.Value) == false)
                    {
                        var validacaoSerpro = _serproApp.ValidarPortador(model.CNPJCPF);
                        if (!validacaoSerpro.IsValid)
                            throw new Exception(validacaoSerpro.Errors.FirstOrDefault()?.Message);
                    }
                }*/
                
                var resultado = _srvProprietario.Cadastrar(model, (EPerfil) SessionUser.Perfil, SessionUser.IdEmpresa);
                SetParametrosTransferenciaCartao(model.ParametrosTransferencia, model.CNPJCPF);

                var parametrosApp = _parametrosApp;
                var validationResult = parametrosApp.SetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(model.AcaoSaldoResidualNovoCreditoCartaoPedagio, model.CNPJCPF);
                if (!validationResult.IsValid)
                    ResponderErro(validationResult.ToFormatedMessage());

                return resultado.IsValid
                    ? ResponderSucesso(string.Empty)
                    : ResponderErro(resultado.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (SessionUser.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = SessionUser.IdEmpresa;

                var retorno = App.ConsultarGridProprietarios(idEmpresa, take, page, order, filters);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorCnpjCpf(string cnpjCpf, int idEmpresa)
        {
            try
            {
                if (SessionUser.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = SessionUser.IdEmpresa ?? 0;

                var retorno = App.ConsultarPorCnpjCpf(cnpjCpf, idEmpresa);
                return retorno == null ? ResponderErro("Proprietário não cadastrado para esta empresa!") : ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDadosProprietario(int idProprietario)
        {
            try
            {
                if (SessionUser.IdEmpresa.HasValue)
                {
                    if (!_proprietarioApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, idProprietario))
                       return ResponderErro("Registro não encontrado.");
                }

                var retorno = App.ConsultarPorId(idProprietario);
                return retorno == null ? ResponderErro("Proprietário não cadastrado para esta empresa!") : ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int id)
        {
            try
            {
                if (SessionUser.IdEmpresa.HasValue)
                {
                    if (!_proprietarioApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, id))
                       return ResponderErro("Registro não encontrado.");
                }

                var proprietario = App.GetAllChilds(id);
                var parametroApp = _parametrosApp;
                var listaEnderecos = new List<ProprietarioEnderecoCreateModel>();
                var listaContatos = new List<ProprietarioContatoCreateModel>();

                var endereco = new ProprietarioEnderecoCreateModel
                {
                    IdEmpresa = proprietario.Enderecos.FirstOrDefault()?.IdEmpresa ?? 0,
                    IdProprietario = proprietario.Enderecos.FirstOrDefault()?.IdProprietario ?? 0,
                    Endereco = proprietario.Enderecos.FirstOrDefault()?.Endereco,
                    Complemento = proprietario.Enderecos.FirstOrDefault()?.Complemento,
                    Numero = proprietario.Enderecos.FirstOrDefault()?.Numero,
                    Bairro = proprietario.Enderecos.FirstOrDefault()?.Bairro,
                    CEP = proprietario.Enderecos.FirstOrDefault()?.CEP,
                    IdEstado = proprietario.Enderecos.FirstOrDefault()?.IdEstado ?? 0,
                    IdCidade = proprietario.Enderecos.FirstOrDefault()?.IdCidade ?? 0,
                    IdPais = proprietario.Enderecos.FirstOrDefault()?.IdPais ?? 0,
                    IdEndereco = proprietario.Enderecos.FirstOrDefault()?.IdEndereco ?? 0
                };

                var contato = new ProprietarioContatoCreateModel
                {
                    IdEmpresa = proprietario.Contatos.FirstOrDefault()?.IdEmpresa ?? 0,
                    IdProprietario = proprietario.Contatos.FirstOrDefault()?.IdProprietario ?? 0,
                    Email = proprietario.Contatos.FirstOrDefault()?.Email,
                    Celular = proprietario.Contatos.FirstOrDefault()?.Celular,
                    Telefone = proprietario.Contatos.FirstOrDefault()?.Telefone,
                    IdContato = proprietario.Contatos.FirstOrDefault()?.IdContato ?? 0
                };

                listaEnderecos.Add(endereco);
                listaContatos.Add(contato);

                var proprietarioModel = new ProprietarioCreateModel
                {
                    Ativo = proprietario.Ativo,
                    CNPJCPF = proprietario.CNPJCPF,
                    IE = proprietario.IE,
                    IdEmpresa = proprietario.IdEmpresa,
                    IdProprietario = proprietario.IdProprietario,
                    NomeFantasia = proprietario.NomeFantasia,
                    RG = proprietario.RG,
                    RGOrgaoExpedidor = proprietario.RGOrgaoExpedidor,
                    RNTRC = proprietario.RNTRC,
                    RazaoSocial = proprietario.RazaoSocial,
                    StatusIntegracao = proprietario.StatusIntegracao,
                    TipoContrato = proprietario.TipoContrato,
                    RazaoSocialEmpresa = proprietario.Empresa?.RazaoSocial,
                    Enderecos = listaEnderecos,
                    Contatos = listaContatos,
                    DataNascimento = proprietario.DataNascimento,
                    Inss = proprietario.Inss,
                    Referencia1 = proprietario.Referencia1,
                    Referencia2 = proprietario.Referencia2,
                    TipoCarregamentoFrete = proprietario.TipoCarregamentoFrete,
                    PercentualTransferenciaMotorista = proprietario.PercentualTransferenciaMotorista,
                    DataMascimentoStr = proprietario.DataNascimento?.ToString("MM/dd/yyyy"),
                    ParametrosTransferencia = parametroApp.GetPercentualTransferenciaCartaoProprietario(proprietario.CNPJCPF).PercentuaisTransferenciaMotorista.Select(p => new ProprietarioParametrosTransferencia
                    {
                        Chave = p.Chave,
                        Texto = p.Texto,
                        Valor = p.Valor
                    }).ToList(),
                    AcaoSaldoResidualNovoCreditoCartaoPedagio = _parametrosApp.GetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(proprietario.CNPJCPF),
                    HabilitarContratoCiotAgregado = proprietario.HabilitarContratoCiotAgregado,
                    NomePai = proprietario.NomePai,
                    NomeMae = proprietario.NomeMae,
                    TransferirEntreCartoes = proprietario.TransferirEntreCartoes,
                    PermiteReceberPagamentoPix = _parametrosApp.GetProprietarioPermiteReceberPagamentoPix(proprietario.CNPJCPF, proprietario.IdEmpresa)
                };

                if (SessionUser.Perfil == (int) EPerfil.Administrador && proprietarioModel.PermiteReceberPagamentoPix == true && _parametrosUsuarioService.GetPermitirEdicaoDadosAdministrativosPix(SessionUser.IdUsuario))
                {
                    var dadosBancariosPix = _bizApiClient
                        .GetDadosBancariosPix(proprietario.Empresa?.CNPJ, proprietario.CNPJCPF);

                    if (dadosBancariosPix?.Success == true)
                    {
                        proprietarioModel.DadosBancariosPix = new DadosBancariosPixModel
                        {
                            ChavePix = dadosBancariosPix.Value.ChavePix,
                            Agencia = dadosBancariosPix.Value.Agencia,
                            Conta = dadosBancariosPix.Value.Conta,
                            CodBanco = dadosBancariosPix.Value.BancoCod,
                            IspbBanco = dadosBancariosPix.Value.BancoIspb,
                            NomeBanco = dadosBancariosPix.Value.BancoNome,
                            TipoChavePix = dadosBancariosPix.Value.TipoChavePix,
                        };
                    }
                }

                return Responder(true, string.Empty, proprietarioModel);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int idProprietario)
        {
            try
            {
                if (SessionUser.IdEmpresa.HasValue)
                {
                    if (!_proprietarioApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, idProprietario))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = App.AlterarStatus(idProprietario);

                return !retorno.IsValid
                    ? ResponderErro(retorno.Errors.FirstOrDefault()?.Message)
                    : ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDadosPix(int idProprietario)
        {
            try
            {
                if (SessionUser.IdEmpresa.HasValue)
                {
                    if (!_proprietarioApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, idProprietario))
                        return ResponderErro("Registro não encontrado.");
                }
                
                if (!_parametrosUsuarioService.GetPermiteRealizarPagamentoPix(SessionUser.IdUsuario))
                    return ResponderErro("Usuário sem permissão para a consulta.");

                var proprietarioInfo = _proprietarioService
                    .Find(c => c.IdProprietario == idProprietario)
                    .Select(c => new
                    {
                        DocumentoProprietario = c.CNPJCPF,
                        DocumentoEmpresa = c.Empresa.CNPJ,
                        IdEmpresa = c.IdEmpresa
                    })
                    .FirstOrDefault();
                
                if(string.IsNullOrWhiteSpace(proprietarioInfo?.DocumentoProprietario))
                    return ResponderErro("Registro não encontrado.");
                
                if (!_parametrosApp.GetProprietarioPermiteReceberPagamentoPix(proprietarioInfo.DocumentoProprietario, proprietarioInfo.IdEmpresa))
                    return ResponderErro("Proprietário inválido para consulta.");
                
                var retorno = _bizApiClient.GetDadosBancariosPix(proprietarioInfo.DocumentoEmpresa, proprietarioInfo.DocumentoProprietario);

                if(!retorno.Success)
                    return ResponderErro("Não foi possível consultar os dados bancários do proprietário. " + retorno.Messages.FirstOrDefault());
                
                return ResponderSucesso("Dados consultados com sucesso.", retorno.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridProprietarios(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (SessionUser.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = SessionUser.IdEmpresa ?? 0;

            var report = _srvProprietario.GerarRelatorioGridProprietarios(filtrosGridModel.IdEmpresa,
                filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de proprietários.{filtrosGridModel.Extensao}");
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaDetalheParaViagem(ProprietarioConsultaDetalheViagemRequest request)
        {
            try
            {
                var resposta = App.ConsultaDetalheParaViagem(request);
                return ResponderSucesso(resposta);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AtualizarRntrc(ProprietarioAtualizarRNTRCRequest request)
        {
            try
            {
                if (SessionUser.IdEmpresa.HasValue)
                {
                    if (!_proprietarioApp.PertenceAEmpresa(SessionUser.IdEmpresa.Value, request.IdProprietario))
                       return ResponderErro("Registro não encontrado.");
                }

                var resposta = App.AtualizarRntrc(request);
                return resposta.IsValid ? ResponderErro(resposta.Errors.FirstOrDefault()?.Message) : ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        private JsonResult SetParametrosTransferenciaCartao(List<ProprietarioParametrosTransferencia> request, string documentoProprietario)
        {
            if (!request.Any())
                return Responder(true, string.Empty, null);

            var parametroApp = _parametrosApp;

            var parametro = new PercentualTransferenciaFreteViagemParametro();

            parametro.Abastecimento = request.Where(c => c.Chave == nameof(parametro.Abastecimento)).Select(c => c.Valor).FirstOrDefault();
            parametro.Abono = request.Where(c => c.Chave == nameof(parametro.Abono)).Select(c => c.Valor).FirstOrDefault();
            parametro.Adiantamento = request.Where(c => c.Chave == nameof(parametro.Adiantamento)).Select(c => c.Valor).FirstOrDefault();
            parametro.Estadia = request.Where(c => c.Chave == nameof(parametro.Estadia)).Select(c => c.Valor).FirstOrDefault();
            parametro.Saldo = request.Where(c => c.Chave == nameof(parametro.Saldo)).Select(c => c.Valor).FirstOrDefault();
            parametro.TarifaAntt = request.Where(c => c.Chave == nameof(parametro.TarifaAntt)).Select(c => c.Valor).FirstOrDefault();
            parametro.RPA = request.Where(c => c.Chave == nameof(parametro.RPA)).Select(c => c.Valor).FirstOrDefault();

            var proprietarioParametrosTran = parametroApp.SetPercentualTransferenciaFreteProprietario(parametro, documentoProprietario);

            return proprietarioParametrosTran.Errors.Any() ? Responder(false, proprietarioParametrosTran.Errors.First().Message, proprietarioParametrosTran) : ResponderSucesso(proprietarioParametrosTran);
        }
    }
}
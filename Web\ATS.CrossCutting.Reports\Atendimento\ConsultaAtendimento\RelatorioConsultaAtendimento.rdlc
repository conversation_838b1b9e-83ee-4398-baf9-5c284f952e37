<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsViagemConsultaViagem1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>878ddd40-14e1-4280-8d00-70aeb3ba9814</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSetAtendimento">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsViagemConsultaViagem1</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Cnpjcpf">
          <DataField>Cnpjcpf</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataFinal">
          <DataField>DataFinal</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataInicio">
          <DataField>DataInicio</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DescricaoMotivoAtendimento">
          <DataField>DescricaoMotivoAtendimento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IdAtendimentoPortador">
          <DataField>IdAtendimentoPortador</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="IdMotivoFinalizacaoAtendimento">
          <DataField>IdMotivoFinalizacaoAtendimento</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="IdUsuario">
          <DataField>IdUsuario</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="Observacao">
          <DataField>Observacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Protocolo">
          <DataField>Protocolo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Status">
          <DataField>Status</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TotalData">
          <DataField>TotalData</DataField>
          <rd:TypeName>System.Double</rd:TypeName>
        </Field>
        <Field Name="UsuarioNome">
          <DataField>UsuarioNome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Atendimento.ConsultaAtendimento</rd:DataSetName>
        <rd:TableName>RelatorioConsultaAtendimentoDataType</rd:TableName>
        <rd:ObjectDataSourceSelectMethod>items</rd:ObjectDataSourceSelectMethod>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Atendimento.ConsultaAtendimento.RelatorioConsultaAtendimentoDataType, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.5cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.96049cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.94384cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.97559cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.68466cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.34642cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.3405cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.5cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.22138cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.35184cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.56012cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>4.87815cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.52063cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Atendimento</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox12">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Cnpj/CPF</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox12</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Data Inicial</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox13</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Data Final</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox15</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Id Motivo Atendimento</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Motivo Atendimento</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox14</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Id Usuário</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox17</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Usuário</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Protocolo</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox18</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox5">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Status</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox5</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Total Data</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox19</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Observação</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox24</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BackgroundColor>Gainsboro</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.56854cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="IdAtendimentoPortador">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!IdAtendimentoPortador.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>IdAtendimentoPortador</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Cnpjcpf">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Cnpjcpf.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Cnpjcpf</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DataInicio">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DataInicio.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DataInicio</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DataFinal">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DataFinal.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DataFinal</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="IdMotivoFinalizacaoAtendimento">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!IdMotivoFinalizacaoAtendimento.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>IdMotivoFinalizacaoAtendimento</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DescricaoMotivoAtendimento">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DescricaoMotivoAtendimento.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DescricaoMotivoAtendimento</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="IdUsuario">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!IdUsuario.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>IdUsuario</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="UsuarioNome">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!UsuarioNome.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>UsuarioNome</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Protocolo">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Protocolo.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Protocolo</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Status">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Status.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Status</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalData">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalData.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TotalData</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Observacao">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Observacao.Value</Value>
                                  <Style>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Observacao</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <KeepTogether>true</KeepTogether>
            <DataSetName>DataSetAtendimento</DataSetName>
            <Height>1.08917cm</Height>
            <Width>36.26299cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>0.78706in</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>14.33599in</Width>
      <Page>
        <PageHeader>
          <Height>2.64583cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox7">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Consulta de Atendimento</Value>
                      <Style>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox7</rd:DefaultName>
              <Left>9.72144cm</Left>
              <Height>0.84708cm</Height>
              <Width>11.03006cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Data: " &amp; Now()</Value>
                      <Style>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox3</rd:DefaultName>
              <Top>0.32646cm</Top>
              <Left>25.98856cm</Left>
              <Height>0.52062cm</Height>
              <Width>4.48438cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BottomBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
          </Style>
        </PageHeader>
        <PageHeight>21cm</PageHeight>
        <PageWidth>37.5cm</PageWidth>
        <InteractiveHeight>21cm</InteractiveHeight>
        <InteractiveWidth>37.5cm</InteractiveWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0.5cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="Logo">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>1</NumberOfColumns>
      <NumberOfRows>17</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Logo</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="Ats1">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAI4AAACNCAYAAABhaUKmAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MDA2QUQ3OTk0OEY3MTFFNjlCMTFGMTU0N0MyNjczMTMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MDA2QUQ3OUE0OEY3MTFFNjlCMTFGMTU0N0MyNjczMTMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDowMDZBRDc5NzQ4RjcxMUU2OUIxMUYxNTQ3QzI2
NzMxMyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDowMDZBRDc5ODQ4RjcxMUU2OUIxMUYxNTQ3QzI2NzMxMyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PguNyGwAAEDOSURBVHja7L0JnFxXdSd87lvq1V7V+65dsmzJlvEu7zYQbAg2EMJkIEB+w/CRwAfxEDKZEMLAJJOZfB8EAmQhMAnDMAECExZvsc1im3i3bHm3drV676ru2pe33Tvn3Pequqq7uqWWWnJL9NXvqrtrefXq3f8753/Wy4QQsDbWxnKHRv89f+3uUzuKy8HUAmBFo6BZJnDGABCQKlNgMpuFSdeGrV3dkESMvlwsQChoQJ8RhGPj0zA8NQUOF8AcBxT8aQSCwIQCistA5SowJkCh03TweEIFXWGg4E/DrQADPKAIg4avN/FxFc8jBAaekIO/M3AUlT3m5tTtEA1eIOKxInO6+5SZDktVe2fV9nWqDgNMhV6hsi7QWBJPOMZUFsKPCzINf1OZwEO7+BoT/y4LTcnhcxlFgZRQlXGmilGmiGFQlJQAMcsYm1Y4L+FzpqMqPGJaQi9XodCeAIZvktckFMKfHGzbAiMcA7dcATUeB+448vzB0KFSKkMwFATh0k0toKrh99V0vB4CAoEAqPiYZVkQxOuYr1ZAw9c5pRIe24BsOg2zs3loD+J7okFQh9YBjE2AieuQwC8TsjkIdmrLvfXJJz3gnCOD+ZO+UyfODhVgRycEtodA3SjA3YZ43jyl9HQxApnCvVfjf0y+E3+quLhy0lHwQVxshkdTAvTTm4hc73kVX0+voYnoFoKPMM4PgePux9+P4IMv46sO4JzFmaHbCyc/pyTOWT5oGXWc5+HcjPfolbjUV+Kib8J7tO9aJalZ+GCRcSmZaO1qQJFTJYCAlAgecJgEj0K/Eyh0AocPHJ2m
KsFT+50FJNhQ2Cjr8DPX4UfdBJwkIa9AuTTmhqwDKHh/gQ88g596EOeoFIkekNaA8xqAJYBzCCfp2es0YNcpiroNNZtUZbiScv1yuD74F4LGAwg0goYmSh9Gjys1geWPxaifaJjyP1LLvrRC8CmahqDDy6pqId7VtUUwtiVcKt/qWpbDq5W93LYexBN7DD/7CXzzDE7rbJREZxtwCCxxnFcgK7pdMONKoSi7iAXpHBcNEVAn+7SYdf3FmhWaaMZIS4WnNP7Bmn71seJJrvoxhTc5cghUgwx5jOqiUFHwEoeRh4XDGvDEZdyxLxNmtayY1efAdn6BYLsT3/0czipOew04KzvCOAdwvhXv4NtxdS601UibKrz1VYQvAgSXKy4aUDEnR5h8XKmBABYDD1sAIgXqWJwDSxMKYe7YtfeT6JOnxeekF75Z0VGrGkZY5bHdwrZ3a9Hqe92q+RSqyh+jJLobX5X1QbQGnFMYEZzr8cq/T1GU23FhzmNySWiROLCF8uTU6XUr3cQaISga0bhQUtVeK7wzbT6U/zdJIiLjCCJmBPqUKL9NmNatomq+4FrV7yKI/hFflcJprgFneSNERBev9LuRPPwWXuI+xpSmdWQL7vnFtU6NjShsMaDMk0KLAaPpJScCWGXxp+ogEh6IgkEdQXQJc0KXhIzgvxfV8t+gsPohqr4xnwetAec4HGYjzrcJpnwErZWhOVSIxRdinmOitqhiKTQ1ch22HGE0n/MshBCDEztmE4h8IDEk1lo8tlVEQn/By+X3ucXyX+Px78OnxlaTJbaKgMP6cBF+FcHxITRzLp2vhpjPUmrgYUsuq/c6dhwYMDbvnYzVgScxRR/lm+h1fsM8EAsflqx+IORWONGy80QbW0CIlgEkAlAA1Lh+MQuF/84olX8iHOfv8Jmf+n6hNeBI4ivgKkULfBitol/j5DUWS4gBsbwFEK3oS42/BjRQg0hWgwHkGiqoYR2UCFpAySiosaD08tLj
5MPhRHK5A65tATcrgKY1ahEbBQVaUQ4aQ5YFqo6GE0gbD01ydQ48dUtvGSfvk2q8LhBK6G9wq9XXO1Xz7/F4X8Pj7H2t+c9rBhx/7ZD4wnvxNv0IA7WXzV/t2ivF/MVX5mSP8G9sMr/FItjCF6hBA5RoCEERAb0zDnpPGwQGu8AY6ga9twO0thjoyRgCJgJqNIwgCoFiIKA0DcjTLAFi2QgaU7r33WIR3EIezJk0VCfGwRybACszA265JMMI5NdRgwi8QAAUnHWfkdRKywMQ2YosFGJaIPABBOj1CKIvM8a+hw9P/tIAx79kQcH5NaZlf8IVyi1KHTBKsypp4CxzJu9Ccszqz8wBSw0HIRCPgZGIQXT7BghvHITg5n4Inb8BjP4uUBMR0PA5xdCX/R2MBWvLwclmwcZZPnoEyocPQXX4GFQnx8HJlYC5aAGGwhJISkCXjkJoAe6FN02DlMLPIO82DwS22kJ8ydC1a7nj/AW+YI/viT53gUN3mqEo3QFVeXc6W/i9dKE4SNdFURqkiphz7i70pzZ47lpIJQJLqLsDQn3d0HXDZRC/eDuENyFg1veipImdPumJC6q3t8sZ3rQJ4ObXI2ByUBkZgfL+fZB58kmwUtNgTk0gcAypAqU/R1HmbhLV/116sj3O1OTh9j3V9FlGOIJgdd9VLRV2qYr6/4Hj/LPv/zkHgYMIQYGydSAW+8NCpfL+A1NppWJaoDXdfQstpybJ0mBh1SSMgrwi2N0N7ds2QMdVr4OON1wO8Qu2SmnymnKARAJiNHfuhM5b3wwVlEKzjz4KhRdeAKuYB8e1vIg5x+vicE9qUpaAjJ95BFu6ICiMQSEM8vloyLfwOSHjKsjPovHzRMD8ipvNXSgY+yJe4+FzCjicC4YM4fpQNPZZ7tg3jA/PQK5YxuujLGJ5zI8LLCTLxEfimzdA9zWXQPd1l0PPzVeBEjRWpVOKJEx0x045rXQa8gdfhSqzoDQzhVypBNwiwm0B
VE0Q1SpAxQRewp/IlXipDAwnSSDFdzSQumMouZiB0sswQtDRcQdXlW2KbX0GwfPUOQEcJoQR1rRfCyfjf8yM4PaRA5Mwlc4Jcn8wxlo665ai1MGudujatQOG3voG6L76Uohv3wRn0wh0dkJn57UoaFyoWAXI5rNg8QpKYxcEciFu2z6AcJarEjhuNg9iJgtuela46Qy4pQqDSgFYPgcqkXcEphYMvRktuT68rJ/By3TP6eY9pw84qJpc7kQTsdgHe3q7/zDZ0901feioGB2dErbr4k2jLW1qNzn1BAKmE4auuQKG3vJ66Hv9tWB0JOFsHgpTIWIkIdQRg2IxA8XCLFQrOfyqjlRHLB4FaE+CRmqKvEa2g9NivILSCQHkTEwAH0uDm5oFyOSYQCmlufC6gKJ+JaxqfwKu/S18Y+UsA460ORMoSu/o2rjxP0YH+8O5kVE4enhY5EtlJonhknbXnPMviBdvYPcVsOG2W2D9LTeBThf0HBrkMIzHOyEabYdScRbyuWmoVnOovlBgCNNzNCoe7yFAqfj9kegzfdtGlFCu4JkcuBNTgo9NMWd4AoxYcKhtMv3n9uR4ktvu36icF88O4FBmgRBtejj8idCGDR/XN24MWqUCjB04KCZSM0yoCtMVdlwfj4Z6vO+yS+C8X78N1r3pJgj3dsO5PBQkwTEEUDCEEqiQhkKeAFSGujp3ydHoemqsZnWhOaomE6D1dIK48HwhcgWUQDNgHBlts145+J/NqVQQrbW/FJznVzdwvJhLmxaL/EF4w8Y7wuvXG+Sqmz14GI4dGYEqfnFdD7R2sUsTHKUMZ5DYsB7Of+dtsO1db4ckEuBfpqHrBrS1D0AoFIdsZhJKpVnpoa6HRGpAomvtIi9CDiRKxMBVpkQjoCTjAjavE/pVl0SMyek/tB7fo5mHhz+Hry+sTuCQuc15UotG/mNw69Y7ggMDBolWa3QExg4eErP5Ernh2VLhGzKtN918E1z8gffBuhuv
PflYzzkwSPJ0B0JQKEQhm50E0yy1jsfXrhFHYl0sebcfXkclEgLjdReEwHR+337qOQeJ1Bfw7cXVBRwCjRBh1L+/G1439DGjv98gnSyKRcgNj8D4RErYnCvaItyGvnu0pw92ve89cNF7fwMiPd2wNkD6bxLJPggYUcjMjkgSrbAlQl4+iARaZhQeQYsWtMHekLZt8ydgerYCpvVl5rrm8gJ+pwE4zP8GKGkCajzyoeCWLXcEOzvDZGsDWgHks5g8NsKzRIiVxaPVva/bBVf9vx+BrW9+k+cUWxtNI4TSR+/ZAoYxjuR5ChznOJZ2LXxhWqDEQkLbtimuv3Lk99XRsYzVlvgHNxDkEoEKOzXgkEo5GSljeykETAsGfz24bet/Mnp6k4K78o5g1Srkx8bF5HQaUNowTdNbkuCtb74Vrv74x6Dnwp1rCFlqobQAtHesw2urQS47jj/tpYPtCB6BAGOosrSNg0Lvbu+2KuZ/djvaJ7ii3kOZAWjrn7Tw8YBzEnc5mYkOU4HHYtfpA/3/xVi3rlsh0JDbHJHsZDOQGpsQ2VIVv4O6MKEuYMDl73kXXP6xj0K0v28NGSci4dGS6uwaRMkThExmFMxq6fjGiu0wpadTsL5ucIrmkBo0/pSVKhOCwbMtg6qnW1VRlSEKnK2Bnt4/Da7fsEnGUBzUqyRZzCqUJyYglUojN3MVTWv+GCMahUs/8ttwxQd+C4xEYg0RyxxktpMET6eHoUIe5CWkDkfzXYnHQFvXD3B4FPRA4HUuUz7rOPbvIK7GeEP24fKBI/hyxQ2dU5vR0/UpY93gdSqKPOE6nuTCb8HzeTEzPilmC2VFmtgNrr1wWxvsvuOjcPH7fxN0KoddGyfHe8IJ6OreCKnpI1CtLGFpo8nOFJVpQ33CCQcYWV96LPZWXsgfZFx8MhoKVkkCiGWKHg84wciJ4gWE1J22xuKxDxhbtvyGFol4GXAAXlQXGX0llYL0TFapWI4XyPQHgeaa
T/wHuBitJzUQWFv9UzXZg7E6eJaSPJR8pvSjpdqeEHw0zVSU+ErI+PesYj0XNYxvMlUTXJwMcPQTXEQCDSIWVO0mddPmj6tt7QHyZpLAIskiP7pcgsL0NMzkcl7wwDcRAwgwkjS73vvuNdCsNHi6NsJ06giYrSQPXX/LIscgU7o7BB+elHEvNRCK4Vp+EtfnVSHEE8sFjlInUScyyVOpqBtYX/8nAh2dfQzVE6fgG1Uvki8HQWRlMpCZmWWlqu0lJElcGmhu/46UNJphrK32aXAWdnauR/UVa5LwcyKHy5QMdbAXOFpTlMahUp6PEdyWrlbvMF23SyWAUbcMqvk63qxJHFU7PkeWwHC5IeLR39L7em4iTMgUAF+HyRQs2wIzk5VtNkwEVI0Ub3v3r8MlH/x3oIfDa6t8mkYYOY/C1sPkxAGQPr4mniO9+uQMBDtoCCTMTIlFZYC1xNjtMcYeKZjm3wa7uxwNtcGJ5ETLlU0fO3Z8LYX8JTzQf22ov+/DqmHo3LLnPoB5KomXSlBMzUC+WKnn8m255Y1w/R0fAyMWW1vd0zzIw1wxAxAyiFJY4Li8iSQryQSDRFTwQtrXHgqa9kao4jofqzr2Y8FQcA+T/XtOEDjlXPa4/gA1GOqLxOO/qyeSXdQESHKdOqoUWTri5AuQzeSgSCYgisyeXRciGf44xPvW/DRnYpAE2bLlfJmakc+PeqpHPoF3NtIIVE2APAf4WMpzDlKTJrzhHaZsxUX+bVzn30NA5U8YOG1DW47js3E0oYm3Bbt73yQ5jeVHa2t6ihzPqLaq6QzkC0Ww8YNj3T2w+3c/Cj07d6yt6Bkcuq5Dsq0HKA0nm0nV14mAgpYUqD2dzPUTwyj1lEBC+VG8VPwN/P1eEQz9kJ9A2xUJHD0UWdr7qLAtWn/nh5kRCrhVy/P7sDkSxuhzKhUoZXOQK5Rl06Fd7/4N2PzG1/9SR7hfq6HK4OgQVMomWGbe695BGoIIcWc7CNQG
3LH9Lh8ef9WCwWh1NvORULLtCQTS2PGkjgSOaS5eFMgIl7Ho29V4cgeh1HMW0kH5nLnHXXAKCJx8Ccp4Qpuuvx4uef9vgmqsmd2vGd8JhCHRNgDTU1WZjuppDhdU5JpKGHlMxfGTLVmNikA1l7tOy+dvYfH4N8VxevUodYup1eQCOIOtSlvsw0haGIk76bdxuP/T+52sK6tYhAKS40BnN1z63vdBfGhgbfVe4xGPt0FbezsaNnSbK5LnsCiCJhZBAeTI6tRGzUIdM8pjYx9zHGeAoObgY61mHTjUOarVVLgb1KOh92vR6CBQDqzT2q5nSIYdBE7JqsLmX3kDbHrDjWurtgoGBUUTiV4IG2EUArjUFBYKI0GOhb21nAccWe/uODv5dOptCZcHkqhvWs3jOgAVhtwmFn8/6UgpbVyvhKNxEnC4aYNZrkJoaAgufNevgRYKrq3aqiHLEYjEumQckdaP4dqwaFjGFsHhCzmopilONvthhfNOFXmRSm105806x3FcpxVcUdpE368FQ121SsNWDEgoQhaTUVVm31veBAOXvm5ttVaX3IForBtipTKUCpMU8ASIhGU/M1JVrIW/zq1Wt5Qr5XcnL73sS2iyW62IsrK4mIMhNRJ9u0RkndvMm6SqbPxZMSHQ1wdb3nKL7Lq5NlableWZ6IFAVEoaSY4JQLy15YTgYeV06t2gB7rI1zN/1oHDHNY8XWZoAeMdaKJtpLwb4Swy8TmO5rlTLEPn7suh96IL11ZplY5wKCbDEnLRg0HZq5myNVtKE10HM5fblT925BYeCukCLbHGuYTEYUkWjL2FImZe9JsvnDJ25YJdKAFLJKDnxutPLgV1bZwppgzReCfoqgGCauw1z43icVxoBhB1R0VmXd5/4DZ8PAqkRWhta7PGceYRJBWJ1BVqKHSxFxHn0LKqm4Kp5EVG4PTc8nroWOM2q35Q9DxYDUMpoFGs3Cvwq0kdNtdqRvp1DAOsbPaK3DPP7mSa
9gg0eJNj27b5OceO2wgIQw3qt4OqxYTNPY9jS2aEH102oYRES732yrXqhLOEKCeiXVBOdkCZ+oc4XlOoOnD813itVjRwZ2Z7hFm9TdjwFL6mehxyzIaYHrpSFrOQtOFi4SSQUnZfuQzhC86DzksvXluTs2QYgTCEkp1eGEIGAGrr6q+18KkIkmg1EmLl4SPXIyVZUOjmSZx8vY2uykPh3YoR3ilbyy/CuuVnmjY4mgHtSIq1Fc3oE+CiBCzks1DIZcC2LJkPq+oBiMcSkIjFUYwGTyoGxvE7WVbVq8RY8Ria1ywhEDA8R9qJvINKjGw0LvA7ViolsKi/IKXh+hJApSYDeOermi63Gwrg9ybJTlHwWq7Tcr8HZYBTvrIeRMvKLHqpMeQ1bmrw630Xap1rp9M7Ihs3Xh4c6BvDD3ObgNNw1JCqq9d5FQu8dRI78zruOaUKhNYNwuAN167oxc/MpODVl56BY4cOQCE7690Zsr+bAuFoDM4Pt8H6626E9oF1y7poBJqxY4fhxWceR/CYclFWctDx6Wx2XnIVrN+8XTYRWOq1ucwMTIwehamxUbxJZqFUysuYoSNvFH+rAVWR1QyqRqAxIChbwBlgBCPQ39cH7d2D0NPXv+xzjSa7IKAEwJIOXM8JKLz83wVsVggWcfL5a3lP190I2mbgBDRRU24b8YjXu7hIqrN4L2ZpxqEpnrzyEtCS8RW7+OnpSXj0p/fA9PRYPQBXbyOJd0WlkIfHXBv2P3g33HzzW6G7f+jEFxYtwOmJUQme0zXolDsQDAPrN0vJ02pUykV45cU9cPCVFyA/O9vcrHtOoMs/XVxUmc2HgCqXvHxiAqSmqTBy4EW8ebbAbe/4N8v364TDEN+1C1L/cp9XKkyFeSD8Xs3+GTC/47vCoDIyekM1ne5nQsiL1/2W2z2OU+UBmsxiwfOEHtzCXC/AudgkT7GWTEICSfFKDRLZzz7+MExPjS9ZJGaUylBMp+HxXzwg24As
Q0aDdpqdkxIDirpotUGpkINHH7wX9jz6EOQzs837BSxDstm42C7eCPH4yd+0yd27QUMJThUQ0ldXkz6ubxC5DYYRg52gaFuZQBkv2AJybHBVvcxF3sJsP47hNBysPl3Jb4KD/RDZtnJt1CZGjiJoxo5/FX31NDU+AuPHjpw1pJS4y9OP/hyO7H9lxTYtORWeRt1RA909QPlVsmybe1K5KRZJoSaUQk6+qLNS8TKmKhpr9OMEKE0CRBsPiKtkM2h3EX5Du8c5QoYVklfsWtFmjdOTY2C3yAsiYkj7cjq2s+CiHTn4CmzadgGc6MYJkjvQF6/7LcQCQNJjnLdOgJO8pbE/TeN7a+kGi3CbowdfhUP7XlpahdAmaUxpkkTC627m++hEg/m8zEbb878LcqbYRRdB5chRVFdektdcr+CGXCvalBTXRe/puSbU2dGGj03XgTODhnpS1buFrm9hLodFa4rpYduLdYQv2LZioDGrVUhPTXgWxbxx0aW7aedC2LvnMUkcG0dqclxaXXrg+AAm66R/3UbkSQWPHOMiNV54WXGKF49I6+T4MXn3NV1oyqHuXQfJ9g5/Gyre9F7u5/eu27QN5rdzcVANHzv0yqJWKhHfto5u6EXOFo0lPc5BlADPgQLQjo0WrGNJq8usVqBcLsgcqC6UGKcyIudtA4ZkW/KcgHfOFLSub3Iisz9dtK50qM5mt1mm3U7A6a8B508mD7IP9mzefokW7KngCSuL7SBH/hvbBaOnG0IbhlYMOMVCFvK52ZaieNuOi+Wp7EMyWbCaX2NWypBCTtQ/tPGExHp3z4CcS41jh/dDanpiAXCIH52382LYcv5Fy/5+lXIKye1RXHw0OrRmaWYEw3DJ7hvggosuOw0uguN4kocGQUeuao6NeWCtJXbWdkeRBZhM8jYzk+kPt7Vtc6vVfVDrgb85EFafKs2eF9cDGm/QbwumvJgMAmiG651tK/YFZtJT8m6aP9o6OuXdGwxHIJFs
X3BhSUJNjK5sT2jeItWgxhAXU2HHG6WCizcH3tWquwDMG7aeDzt2XX7GQUND7+yQ4JHaT66v8PbPqk+P11LjbidXDMUvumi7nogrdXL86Z4LgrfH+rekUKQqPsNuOanEAkV+cOPQioYYUuOjCJyFnVV7+oZku1a620lFzP9MUjVkhZ2Krj8jxBivXbWsI3/hC9RnV/drVzpE15OAI8u3ZdzKc/rWBUdt3VEt0r4YmYcf3mJnckad40xzN96hGdtMyviCxXuFycSfMJKq8zevqLUxm55ecDeT1dfe3i0vLt3tnV19knjOVyGlfBaKOGOJtlULHAV1PyMV5bJ5lFGgGiu9pucW2rxJGiBU9UBt/70cHdbkW5J6SQ2AOZneZk6nqSSmLG/hYDHVWWRso1KPXUDriejTYjEI9q9cjz4iuOQUmy+oNSRksQYpQ+SxlVOtVMxLx+FqHsgAcE3IO9v8LV3HgaOHXoXsbPo1O7dAbw8aOxFwbe5lWfB58Unw41jSIcw2t99wTWddVa3PjrYL0LpkjbGf2ddqcjTFA93tK7rBxtjoMSSOxQU6vr29E+LJtvrjFG6IJVrwHLTyZtMTqxo4kWgC4omuljxmBon4L35yF0yOHTt+b7/TMDRUQXpHh6eqaI0pOY/PX3sPRLxYHuh9221zwBmNDvapru1HvheZFBhUGRj9PaDGVqa7Oam+HN5tZG7OV47xtg6g/S3mrI8QdHT1LLj4tq1CIXcEj+WsWuCEI3HoX7eVKoxaPj+F5v/P7v0/8NKzj0M+e2Z3TtRwLQM9Xd7OPBI83GtS0DS9iDmKfDb+/Tu768CZCbevl2SHL6GqaMdjRUfgdPuxjRUww/N5KKMp3ujMop8EDgJJY8NJ4jdtnd0LCDKFXIuFFFQr6VUtdbactwM6Oxf3u5SLBXjqkZ/Bg/f9EPa/+jxel/wZOS/aAdDo8q9rPdywMJWGAERt+gqPPz1UB45quwNeKkYtHtUiVVRKHLRw
OtpX7KRnpyZRWmTn+vNSGbqqgG050I4XeX56QrK9y0staLRMdCTXKR3GR7KrGjjxZDvsuuI6VFux43jQR+Hhf/kBPPmTu+HYgVfR2jz9e9eTa4UJxdsG1PWdm7zRJPd/IlVRQ8HBOnDwru31ALNIfrE/6ZVadOU24chmZ8Cyq3X6TqorHAoCR2TrqKbmq6Ukqq9EW3tzLg4STtuqIkE+DKt9rN98Hlxx7RuQ7yyx842fqHBw9BA89NA98NSjP5NxOdc9fTtHS+pBoY4GSVMXIrWAt/TnEIEW/XVzHE+2S+5ovdS5kSmO5hrtZbkijja8EJnMlJ+4NOcQs1HPbti4WTr9FpiO5AgMxWCK2na4ookrzc6k8OI6vvm+esfm7RdKvvbCM48jIR6WqRMt4zv4uFkswivPPQ1jw4dg03kXwZbtO+XNs+LAoYZXMkbpyFzkui3OWNM+7bQznyLEHDlGDpMEmOMyi3EcnZotx1amq1apmJNxofkOqUrVhHW9gxAJtwZoW2evt7d30xuR52SKkJlJw9kwBjdsgWtufrNM+oq1HX/frUI2A3ufeAj+9af3yJCIEHxFz4cIsooSXpY7+aqqRk/qKRZ1IwnaGoET9+x3vtCOr00qCaatkFeovJcWuVIsL7jZiONEKD10kdyZzsGhJmurDsRSDlLpMThbBnGey66+Ga57/Vtg63m7pDRdrEV+7RJNjx2BRx+8B/a/tHeBI/SUJE4wSA0HPD+O26yeyDHLG7DBXZ6YU1VchGQAk/G51hctTp/auC+9SdmJj5nUlIzyzrPPIZZsg1hn16Kxm2RHF6qxMIKu0NSbl75cNpWqW2Vnw6Dz7B/cBB2dfTB8aD3se+EZmEJyvCDlo/YdhWeJkvVFeTIUAF6R85AbxWoyJgUKn9udkM39Lnc/l6VYPNwocQLEpuUujiQFkT0vnH522wqY4uQxzSMxnh9m4HhinYluKXEWG0HkB630PB1rFsFYrZTgbBvEeQgE
1//K7XDxldfLgG59E68Wo1opw3NPPSJTYVfGJFe9nYY5b/LbNWqbOcoiAnPAoQ4qtScX4zgSecop7ThSt6aQ27R0szOynDrlhhdLjc7u/pYApkT3PPKBs3Uk2jvgst03wtU33gqbt10oif5iAdwc3nikslrlMC0bOLUENcdf/3pZFMyZ5cLP0RKSPdckzuK+m8a5WJH6soFDC9yiYaWu6pDo6gJNX9oyakNVRq+dP2zLhEx6Gs72MbB+E+y+8Ra44uo3QE9PD2hUddICQBSmmFmhOB2rNdISXnl33YPseCkXUGtr42uJGnDcekCraTaIHHqJ63XgOmXH38w02PMcW1TPEI5EId7R4W3UvsQgqRRp4U+iL5yaHj/pvJnVNIjH7bjkCthy4eUgVH1BxqJnmealej7V4aVR+OvtegLDi1n5QBENgsP1Yjt+YyUwJTmu+XIWm5YjU0dPZRTyORg9NixLPOaT2Fg8iRfs+A7GSDQO0UR7S55D6upMeFvP1Nh2/i4II+9LtugT7aIZRJkFpwwcx+8kW1/rOkj8HltNoQerDhyUTuW6YFliUkY8tTU5JTM8k4YsFdrNS7YmC6mzqxeME8gfprhVor2zpfVEFaDz/UNn86DvunXbedJzvOD7UiXoCnAcKneiuVgqDTQEO/HuLNeBg/ot1+zHaT15HvFVPrU90Lldlh02bMtuIsVUXZFsp1TREysn7u7pg1a77pEKnElNwrk04tEI5IutJYvCTj0T0y1VcJrz4pSiOZWU16PlubofR3CWlXF1VyxZk+0gcJzcyZu7lOcxkx4D167KpCbGRN3DRfyG8m2UE0xJpbRSCnhSId8Cszw9JX8q50AHDVqO7OxMS9cOSSAjeOoOWV5EgYDgqVvWnjTxlsYHprdWFOqBzBzHcUUKak0ol5gcOY5TOHngkP8hPT0jSz7qoKndVQiaaOzEKxO9xK6F7nq6W6YnR6BSKZ8T0ubg/lfh4KvPQyunDpUFRWOn
vsOgi8ARluu7Yhp9OFDXNnXL2nHTc55jwSeBNxRkLUaiCDgzJ58nUshmIZ/NNzfxqfkw2tqlBFnOIKlDJmnTOZK1kS9ALpuBSCS6agBAVRwjR/fLuigqiaFF11HVUqcP2ZFCpS4UmvwGlAlIFhOl1e57aS8UC7nWbglU7cupn19s2JkCagHbU00Noo41OiEVL/eJO3yiAThi1BNRfMl4iajaYI+dPPGkbD+zXIL5ZVskcinX5kT5TW30Dq6H555+pKW1MUv1VgNDqwY4lCJKJcBUfEimtaqoUpVSzhFNlSnSe0tGArk9CGCVagn4IumkBLShjdtWROJYkzNyu28pZWpNB+hM/N+ltuIeGUeCPFoHDgLtmAqeaKp1SmkJHgRO6di4ZODKMtvtS1MZLSrTrMA8LSWT0DvQomq5SdcSo72jS+p4Wowm4ODFnhg5DDsvuXL1SBzLlBUNspR5BdwFVIS4bceuFbGoyqPjIEwfoLwmJububsmtFM+zrIBzbM4cB5gUfp3yYv9q5b+FkRGwc4VlnyD5G4i0tvI9U2wqLB16ywtnkMjv7u1v6QhMU9yqfHI853SESEmqeukgp+5970PQXH7NGyAYOvXcKBvVenFkXNaPs5oPx20MP4m6bwcloRBcmZ4zxxmbdZkYp9YDLjJkF1pMxhFwaJJP5JDnLB845OHMLZKITWZ4wFj+jsAk8tt7BloaguQEHBk+uPw7ENXE/Gw75ntXTyWVgUh7zTF5ssCkqtbzd10G19x8K7R39awIoN0MmvnTOa+ygfN6YnpTmMn34+AFGOMum2ngOCwlGD9EElDW/rBWHMdzSfNMCaqHxyFy/oZlm+KuYzdvkAbe5lyD67fIblMncxd3961HVfcESn+z6V6mmAvxhOUOXfqGWHMzCvznUrHiKeyG09bVDZu374SpsRHpPT/RzA+67vFEB6qm9ZLT9CGvM4yV2/LAHJ4AkSl7GRS1rD/5k/vQ8N0mlFXhAmJEzFlV
LrMK+NR+YNp19Cal5kBoWniPONnFAhReOgQdb7l6WSfYjRbQdW98q7QuaumdBCIV76KevsHjxqcWFdv9g3DTm9/pkU5V9RdcSEB2dPcu+3hdfQNw061vRy5WrfuBqCehbhjQ0z940gsUi7fBVdf/iozel0pFsBDUZDlZeN7Ef0iaceHV5hNZJnCQCqcKVQrFUOLXSgKmNkq4luRimdslU8xT2KLeBpnZfD8TvDTnAERqhMg6xPzgpseP5i+kh0CrXIXi/mGgPTmVwIkndVGG29CGrS2lxqkMaqpIEqtV9Phkjk28Yf3mbS2roE/lXOm9oXBUztq2ThIsMsPOVxMNr601jiQQna7ENOKslYMjvvPPf4zNww0D31ySKgwxAuacqmIqNbPYh+81ER7GnAHegjDi3Vd86TBUR6chvGlg2Rfv9HlYV/LY7LRu7Cd78Uiy/Np6ts3xFJRfPuo5/2rnUktQr60/JbHTPw5VV6iv1iSIt5krL9OrXlVVY0Jj+gbhpwku5Dj+B05OQ/nAsWUDZ7UPyuch7zZJASLelG1Iv9NjdEEjkZh8nIrnKLhIz9eaKJF0IMcduRTo9eSToliaK73kipQgZI4TQQ5FotIFQa9XGKvXj5HPplgqSFBRCEYe07KghBYp+X0M5IF099Nn1nKB6fNOVs2XD4yANTbtpV7WpYOAOUcb82JUeHzNNicUEPtqiJLAqTIZcJzhwjnQydo32FDbZqh1d0MrU4Ds4y9A55t2n1PAIWuKGhiUS3nkR32yxUo2nYKqVYUwuQxQzWRn0jL7znPiKbLunTpqUaoHpcOSKkpPjePjZSTDO2SaR8AISzdIbnpCpt/mUtPQt3Gz5DuyQrWjS3puZybGoMptCTJKgaVGC7N4PlW7iiCNyM+UDRoUDXRU0eV8FtqRx2nayQGn+NTL4GR9C5msJr/KDpQ5fw4JIAtPPuaW9kedQt0s9jZzZdJayGqgPWKD+0bebJ80KD7/M8oVSP3i
GdiQK4KWiJ4zwKHcXzJzI/GYbKsivytYsqoiieSUigep0yk5K0lqKHhBqQUdkVxyOuZmZ6SUIW9uLp+REsbjMa58X7StHWLJDpg8ekiSYs7nzHsCp4nkuG9gvfx7CkFE2YwcLZqBdZukVCGQEbinJsdQIsUgoGktMwRO6CbJl6Dw8LPgFqseh2G+32Ze61yyqjQEcxlCj5hqQAY4N9f9ON4/W+fsGQccTifLfZos6TITzRMfLx8dh/zTL51TEod5u6Z4mxz7DjvbsaT5TCAgKUBprQQwVZJWxVNl8rmytMQIDPRYT8+A5/DE45BVRmqHuplLFRhPSFXHGrz0ZPHqVGkAtc/l0n1BKo/eW0WglUsllGhhMNAoKWZnIdHWefLW1N4DUDk8OZdvJWpB7nnJ6lQ1gmh1XH0PB8Xlflxc/h9iIZpcUfX9iOuXPF8xX9SLTMcvIbEq3PNTONeGl+zmb6uEP0lFUPfyBHXPCEZlyIA2Qq2LbFxsMvu7egekyiEJQOAhlRVESUXgCRgBj5P4DkTbNqWaA+Z1Va3xCwKYjF+hJNNR/Qhffcr3mBVZxCitM7T8wrHogiaVyxkz9zwC1ZFpT8rUsoQbdtas/Q2ygaX6UkXoB/NumOOcU1WjUCtkY8ciEHkozKIX8iX3LCdUWjDy6IvQfXQMQhvOJZJcy7f2LB/HtWVFBjkAyadCu81NjY9KqUNpHaJuhbFacMbvhqtI38vw4f1g9pjSD0R8hzgRqTZqqlBCIkyBX6rZD1BRHGJpYmxYglFD0kuBX+Jc4yNHwURpRzyLjks8yTgJT3vduEFCnH1or9xL1YuB1ywoHzDKnEeHcxUSPP/zCC8ea9xeU2u2mIRVFea/hiD6QVE3yxe/wJlDYzD2L4/Alt9+1zkDG+IuBApF8Qqm29p7oIpEt2ZCU0NL6rBFFpEER6KtbtW04UKTCiOLSZJn/H3bBRejigqilAhLaUJWVU//OplCkkC+QwScylPobyr7
yecyMquPnH+Sc3V2oxWX9xyC8bg8BxkRP4W+h7P3/AItqlGoRcDrrQn5XAs34adUCM6qQV59RGW8KUdVAqed1RO/SXA94QrnebwYlx+fYBUh9dPHYN2/eRME2hLnBHB0Su1o0ACRSAKtqXiTr6jGLeb7jmihvZjS3GPJjjkeQoV2jZWm5BQlzuJJKCbfSMS78dhklof8OvraY2TFnbTlWCzB7ANPgDNbgOY0BQ8xTPheQL/Pscqd52d52xPQ0JKiTo7njSkkwI9Jgnyc6eIHze55FaYfehzO5VGTNov9fWqOyuMf+1Q+bwEp/vljkHtqn1y/5mKoef/7lQ2MC0p4WlCs5tdVqY3TZA77EYqvNPcp8uKTQ2V8GiZ+9FPZ6XttrHpHFWR+9AsoHpvxVtB3+PF6qKnO0rz1FXwabbsfu0hocdb/1YGT06uNk+f16nNlzX5alrAcZ5KCnHrkeRi598G1hVnlI3//IzD68+e8/cp810rNZ8cbJA+fA9GTiLbn/RoZgIYmShI4DuXaNExTcQuv6um787rFFdFsnM+XOpSrUxqZgmM/eEAGPtfGKrUVbQem/89PoTg8BX7vCW8NKargU48akFzPL8ETkL9LBV5QZMbE3KyT45eM2Xlal7zMyl2psP1htaScH6oq0pFFB56fwCZ8Tp356dMwiuBZ965bAc6SNiO/TGPqxw/DyAPP4NLwWkZxPSFdlirVPcX+LjWMv2gIfk+IFexWq6l4IQelaWo0QZkUqvL9TI8GxRgdzBGyprgBnY2zmErBke/cDeWJ1NoqrbJhTs7AxLfuBnN8UgLGrWkQP72jtoYuCL/blxCoab5rA5u2ESLWvFkHTpsWWTATWrAKpv0NLRgas9bFoZxQhUsfw8X8dgTeRFhOP/YsHPmnu/3NQtbGqlBRnMP49x+AmUeerffxE7ViuwYAyZ/M8+vgX8MVbn9rTATNERGG+XPOqlJaTWkCjoNlfy0Ui4PY2KVUOgPCUfyc
1Bb/rEIBDn/nTph5ft/aiq0WQvziQRj71l1gZ/PN5ndT7LFB+tD/wvkbBnxKAYpMLZxzqipktJxaUKva1fJ31Ao/FI4nQBvqUOyeiHACeFhCqWi2sCiCM/PCAdj/P74HTqm8tmqvtfVdqcLRr/8zZJ991Zckoh4lqGkNaVGxudgBahV8sfV92hcPJ7SadeAoXFlkasBc5YiZLf+lzpkbibaB0d/FxEBcuCGNy+oHSkJv8OvQ6Rz78QNwlFTWOdCn5mxWUZPfvw8mvn//nF/GV0Wuz1PB5zseKZZ+HccV5hdRKBxbslSqZlUF+RL1OaiBrHL57mqm9J5Ef/eVCooZu0NljmYwJ5UTbrGKRN31vZuesVbJZOCFr/xPSF60HTovvXBtFV+DUXh+Pxz6y/8F5myusVUxNGQ219OsZDYAQkEVpccYd+61GTtuEySlhs5FJx3UdocrqcxXVNupUOVkUA9BMBGHYF8H0zpigvZzFFL6cJ+ZC5jdfwRe+OI3oJqaXVvFMzysmRzs//w/wMyLhz1zmzUQX9kfoObq839KCaSWgJe+goRjbDEV1aiqvGT144QLUHS5qLnusWezPwx0dv1bxQghOhloIQUCnTqzjCBYuSI45YrkPsJviXH0R/dBYn0v7Pyjj0IgFFxb0TMBGuQ1B774TZj84QPSq+/6yWK1siFWSwpmc1xHIbcet7+Hr7lPgQo/kZJBCRzFVo4jlhjyHTZbnS58hamha0KdyXUqBMEVyLERQGpcB10LgBUogFWqgGNXpfgj6fPyN74H1a1DcOV73rlo0+u1sUJk2HHguX+6E6a+/n0iK/UQguKrJVYzwmvpxbSuOE1hHlKF8Tf4e47VldoJAMcoHD+TjNRPMVd5SoX8lyOJ6J8oWiCoGggcSo0UFlpgYQgoOjh6RXakqJpVtOQtKORycORzX4ehnj4YuuWGtdU9jWPmJ49B6s//Aao5ry0Ka8ytqVNWD0E1UBkQqMzw9Jd0EXyG
s1o6IJwYcLy9po8/FNqjo1D53+Z4Zne4v/sdKGbkx9A+nq7wphphENB0MKoWVCtFqFoVKB4dgcf/9AsomSLQf/Vlayt8Gkb60Wfg+c9+GcrDY81LL8HD6mqpsVKTQFQS5R+jNfztPMs4eZmQdmLhIimxqLz3hKakQ+5EfjTzBT5bOKLh21VFg4AehEAwJBOMSGVpAcPrgB5JQEe4DeJ6BFn+AXji05+D1J4X1lZ5hcfsMy/Bc5/+Isw894pnnDQ69mBhscFcMYLYP+2mvmgKK6WCKgWAKgXB0rMucSLhEyumpwaPqk5NgOBRZzb350Yo+Hk1FIzIUpxAgOJb4EjpQ7v1UkYbEmiB0ocZELVNqOw5BM9+8i/g4v/2e9B9yc61FV+Bkdn7Muz9o8/D9OPP+kWYot7jSDQW2NVUlN/DWFVYASXNn+FzT4i6D+7Eh5c6mjyxxGdROw+00d2K9b+tqexFRm/nhxVD92oraHcZKhkRJgJIoVCZ3CuTzl9lSKcVA/gTCJ5PfR52/dkd0HvxrrWVPxVO88yLsPeTn4Opf93jm91el425tHlP7Xj507INm9yQVw/ryEfh69yF78aUsGxFwZbZfIUR6b1r4KaTcE1Saq6+Ltie+Gqwp/0W8NvGU2t3TiUkVRN4xQLXsuTfsrE2nqlmARTtElSu6Yfdn/kDGLhsjfOczEihhHnmj1D1P7kXoGZk+zv/MH/v+Hp2C/WFNB2hqIyFu2LQti56pzme/20nZ40v2PvrBMYts8/WOnIt/59XViyOOZnyp5xM5XkhVKmqFPLvqLQ1YlBm98ufKIkoCVxTAlRkDQk1AtVH98EDn/w0HP7Zz9dQsMxx+N6fw57f/zOYfvJZP8jD6xkK9dVkHnd1TBts0xSBpAGdFw1C32WDzyTXJ/4YKce4t1kMX/asqyqktidx+l63BbT994jZ7Kd05v416+gYrLfEQJIsQoqkXFyqLRuBhpIIf+pcgy6ehH3P7IP7P/XH
sPujH4ML3/n2FdsL61wdJLn3f/uHsOf//yoItJ6479ljC+iEJ/Vdy4FA1BAdm4egY+cQCyaMYVEsflKUis+hBAKOk6knl3TnAYedfLkF+XGMSuGe6Fj6syUl8F9ZMtGtcNdTWyh5uMGkBiVHIWWaub4h2Ib/Opw8HDt0FH72mf8CueFhuPQDvwWRrq41hLQY1dQMvPLVb8HLX/tHcChFggm5cy/Ucr/9jTpo43naqEUPBUTb5h7WdfEWaN++nvFqabI6Nf1p13bup6YF4Y2dEOhxTjpZc15B3slQHfnJLisWvumOT8a5YXw6GDYS3s4jZG1pEtW0WTunlhwm/lQcVFsq9JqdMOPmoZjLwBNf/ivIHDwMl/7OB2Hw0kvWkNLIZ5AEv/SVv4fhH93vAaPef1j40oXL5DkixkbYEImhLta5fT0CZh0EuzsYVMqz+SPT/9XKF76N64GsAoGzoeOU9h5bmRiA9FIzy6maX+WzmaCuJj+pREIR8MEDVEyv491BU/PMdI4XoE1JQE+lDYp8ChxUY/vuvBtShw/Bpe97L1zwjtvBiMd/qQFDOU0H//lelDLfgszzr0CtK77sLOxwCSLuOrJJQSgZg+S6LtGxbQjaNvZDsKuNKboBpel0wZ2c+O+iVPofaF3Ztdud26eWpbmywSMGJbdqfslKZ1VDZf9JiQXDwvb2KwfqHI5SRyDKFQQOdWlnigt90AOpch5yoiQ7Rcy+9Ao8/Gd/DuN798LFv/ketLp+OaVPeu8LcOB/fgcO3vVzMGdmpKvWRVPaRbJLgGF4PQNBA+JdHZBc3wudmwdFfKgb9FiEkSqiRAkzPVMsjYz+N1at/DXaTpWV7MO7osDxu4AVETxfMFMzrqa0/6Eai0YV1+M8ZFFRMT3Xmdy+SGg2xNUkDDpdUDQrstRGxStEsZYXvv1PMPnMc7Dj194G299+G95N634pAFMan4QjP74PSfAPIPX8i7JNrOPzFrqxAqEAhJNtEO/v
gjYETGKwB8JdSeQ0QSaQLVMHMHJ7OPlCvjw+/qd2qfTXAVUtLbZB7OqQOLWhkKum8pfi2LipDvV+UrTF2yV4KNdU8p2A5D1oXKH0UaFf9EFqJgdT1ox0VtX6xqT37YNHv/AlGH70MdjxznfA1je98ZxVX+TvOnzX/bD/uz+EkYceB5MIsPBatYWiYQRLDGJd7ZAc7IJ4byeEOtrwWoTxOmqyhYqDFpLwVZk9O5OuTE79iWuaX8NHKqej9+JpynOgMD4rQS7/VwG3kLOUDX/sJjrWqaqXHCZNQLS4yPckVBvCWhI2OP2QmymC6dpyD8rasCsVOPrgwzD1wktw9F8egC23/Spsev1NaGZGzgnAcATMyE8egud/cCekHnwUSpMpJLhB6FzfDZH2BEQ6aTvtNoh0JMBIREELhWVzJ1mZQIl2tuWl6OINSD+dXP5IeWz8syhpvqOFgma9v9/ZAZyaBxOqgXzmG04qMmUqxmeVmPE6CKj+/tYEnoDkPtTJtAf6YcDOwZHMqHQyqfMaIlZQz7985z0w/ORT8OoP74Tzb/9VWHft1RDq7Dg7JUy5DJOPPgET9/4Epp57AaqFPAxs7IPQJdshhAAx8MYgoJATVdHxRpJ3mRdsdizbU/01TzFJHccBO53e4xZKnxaWfZ/0F5/GwsjTnFlFpIc5qHjvdPK5ScXWPqu3t91KqaayEwJJJvL1ONRJMwob3fWQrRZgtpoFaNFJk3wX5ekUHLr7Pph68hnovXAnrL/5Rtj0xhshvnH9WQEYe2YWck8/C/mXX4bc/gNo3ZiwbsdGlCQGqKGQbPfG9FrDe5DXSUoW4i7zOQqpdZwOSmVrOnWXKOQ/w3RjjweY01tNe2ZS8qhXrhBPsdns77iW83G3t/1DWhSvlKx9R9WF5jrlKyfUbthsrhfl4QqrulaTyppDj5fBVp5Kw9GpB2Hi8T2w/1vfhYErLoWhW98IPZfsAmOV9epx0awuHTgMhT17oXz4MNi0
JykCIoQqKRqIAhiavAaCDAfZ7cytf9fFr6m3V7idz1es9Mzf8mLhi/jnMaacmfLrM5fLSSLVcYeZZX/GyuaeF479B1osupWhGJaNDcizjHp6cGgD5Cp5cWDyCHOpsc+i4tZjglahCKmXXoXZlw/B0fsehlhPNwzceC1ol+2Ajedtg9jQQMvN7U/nIGechVylOjoBpX0Hofjci+CgKnKKaNyQqianKH5vlzbcsMktUWtohM9px+mFoyjyeWorY81m9tuFwn9nrvsDfCZ7Jmv2z2wSMH0xRckgSr5pjc28wpPV/xDoaXungsKH5DLtXKImI2zrlu2iWC2K8UyKiQUt6RnAgq3SQPbRK4yOQWlkAjIvHYRgMgEj3Z3QvmM7JLdshMT5WyC6YUBKo0B7clnbCSxJbk2LCCnOomw2XTlyDKpjk1AZHpVgEVbV6+IpAaN6m2rUOjSKWsM9NncviKV8HUz6Ux1qtJ2a+V41X/h8MKDvYYriiDMsRV+b7HGF2cLmj4pK+Yg5W31EjSZ/V4vHNih4YYkYR7o72Pnbdoji80+LfKXI1OPsZzD/otlIPNVKFWbGpiD7wj5vK8MIkk0EU6SnAyLrBiGKkiiIZm0ALRc9jtZKJAxqJARq0EAyqtXTE6Qrn/woaP245aq3Y26hJBtL2+mM7DJPxfwEHkFpJI4p/SgyOdxASUeJb6Rya10luWgGSL0mtyEvuKHdW11a4zFk86rZ2cOBzOyXSpb9XYfD5JlSTasDOLU7CGBCOM7fOsX8k6i6PhRIxN6nhsPg4HPt6wbZ+VZZ7H1xr7AcW4IHWsqa1jLIEn52P1obpM7sfAnMibTch0LT94JmGLK1rOrviynN3AiRU0N2FvUi/4q3kalDCS0Oms4muKYlpRtwL7GbEtXkwgYU6aOisIq0gshalJu140/FL7ht2lhFLAH9xhXSvPLqHAF15hu6Wfma5tp78D0mY6/dXhCvfb0KYyjLxaOiau2v2On7IBj4
f4JtiRuQOcLA1i2salbES6++zF3XVdRWZNmvDJoPpEUUi3wtpRw4tmxER2U/YJO/mrwD1CdGMAkYuUem8PgEhUqYjLEpcpIfivlxN3L9k1Sp92Gste9QYUFH+gW6SMAiKkpuxC7vLTLbranUQ06p9FUu+P1oMMzUyxdew7GaCp3S3OX/ZOcyjymm9WatLf5hPR67YPOOC5jpVNn+AwdRa7hM83fKFQ27TdTWaTGoMGjdJXNuzTxA1XJ1GSy93gseE43qp+EJIZr/Zg1qSTRbivWDSswo0jHo5nIvm+n0X/FS+V4lFDwme7C12EH5lx04ns9HiCOoCr7u5or382r5nXo88qEtW7eur5oWO3xsBBhtRH8SIlo0gGi576urQdEKRKLh6K3LS5re3+ox8sdonpNPptrm8kedUu7vhGl+HxznGF4Tc7V1OVudpZWMmXixDqCV9YVKdubbAVV/77ahvndZZvX8yekZVRxnkVtLoGaVJhqqAZY6ljIfPAuXvsV7RXML4VaAY3Ur07MoTdOxy6V9bsX8Lv7+v9SwPikEr67WtniruyYX+Q9evKNciM9FqqV/uDwaeMvj5fDt6ULpEmQlPXDchRSLSIBauiVrUB+ny6kz7ywkWDyFiAYBcMeasrOVvbMT4z8KBI27Y+2dKcF5ZbX3UTxbirkraN2MBgD+PqIp/zgt4FK8pd+GANiNttPFrnSpiYVb5jTIGLEEz2ll4zQtGxMtiG4ricKauY5oBgtJQuqxyKuW7XJnL8rGx4Swf2RZ5lNoaps8oFtwlvTdPKu6AOA6UAdMymJ7GOfjKIl6Ipxf1elUrx4ORG8ICbETFxlNHNUnNGL5koTNrbnwfSrUZEpKiZrjjs0BRMxLFq+rIF8NkQVGviDXdLiu6S+Ojxx5OB6PP6IHtMeVSGgSD2DJ/ImzrFPr2do+gmBBm3gP4+UexfkjJAMbIlxsxmW8VOHmNQzUzbhqA7i2QcFqtc7LpcSLyKmlzDji
Kw51CuGma/Jx/PsA0/ljCKinjUT0IHfdo4JKPoQ4qztsngt9R2rtvqlj5X5cuftB2O24cu0C9PMYF+crLt8EKtuKN/ZmEOrgXFNfMadKxFK+uGYQzVndQhYzeV1W+ahwnMNC4fsVXT0CDryCAHtVuCyjxtUZmNsZSpwD1xzOpYY1jUbVlD9p19q7ZDcPgAgCpBsXu9113C4GynqUSLTheS8qik4BPIkYSIBQwggFAwRXfY1FURAbZ4m5IscYy+JHpBEzk4orRlWVDbuGluIcZlGdTQtNlIRQTAbL6BlyFo7/K8AA7aNnd5sDf3kAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>5473ada7-8ef1-4bf6-8ddf-5c6933901cda</rd:ReportID>
</Report>
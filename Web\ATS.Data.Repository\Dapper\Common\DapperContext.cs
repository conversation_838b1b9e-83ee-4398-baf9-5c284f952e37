﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace ATS.Data.Repository.Dapper.Common
{
    public class DapperContext : IDisposable
    {
        public IDbConnection GetConnection
        {
            get
            {
                string strConn = ConfigurationManager.ConnectionStrings["csATS"].ConnectionString;
                return new SqlConnection(strConn);
            }
        }

        public void Dispose()
        {            
        }
    }
}
﻿using ATS.Domain.Enum;
using Newtonsoft.Json;
using System.Collections.Generic;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Models.Common
{
    // Requisições da equipe SistemaMob:
    // Dados de tabelas filhas devem ser evitados de serem carregados e acoplados no objeto principal
    // As informações relacionadas ao Empresa foram adicionadas devido a necessidade explicita.
    public class UsuarioModel
    {
        public int IdUsuario { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdEmpresa { get; set; }

        public string Nome { get; set; }
        public string Login { get; set; }
        public string Senha { get; set; }
        public string CPFCNPJ { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<ProprietarioGestorFrota> ProprietariosGestorFrota { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Foto { get; set; }

        public EPerfil Perfil { get; set; }
        public bool Ativo { get; set; } = true;
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Terceiro;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string IdPush { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RNTRC { get; set; }

        public bool Carreteiro { get; set; }
        public bool ReceberNotificacao { get; set; }

        public bool Vistoriador { get; set; }
        public bool VistoriadorMaster { get; set; }
        public int? IdHorario { get; set; }
        public AutenticacaoAplicacaoModel Autenticacao { get; set; }
        public Indicadores Indicadores { get; set; }
        public bool PrimeiroAcesso { get; set; } // Created to allow passoword redefinition.
        public bool PrimeiroLogin { get; set; } = false; // Created to and an app notification.
        public bool PreCadastro { get; set; } = false;

        public string CNH { get; set; }
        public string RG { get; set; }


        /// <summary>
        /// Indica se a versão do aplicativo do usuário ainda está sendo permitido usar.
        /// </summary>
        public bool PermiteVersaoAplicativo = true;

        /// <summary>
        /// Código chave para transações
        /// </summary>
        public string KeyCodeTransaction { get; set; }

        #region Veículo

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Placa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Marca { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Chassi { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RENAVAM { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Modelo { get; set; }

        public bool ComTracao { get; set; }
        public int? IdTipoCavalo { get; set; }
        public int? IdTipoCarreta { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? AnoModelo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? AnoFabricacao { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ETipoRodagem TipoRodagem { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string TecRastreamento { get; set; }

        #endregion

        #region Endereço

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CEP { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Numero { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Endereco { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Bairro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IBGECidade { get; set; }

        #endregion

        #region Filiais

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdFilial { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string NomeFilial { get; set; }

        #endregion

        #region Contato

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Telefone { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Celular { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Email { get; set; }

        #endregion

        #region Horários

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<int> HorariosCheckIn { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<int> HorariosNotificacao { get; set; }

        #endregion

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual GrupoUsuarioModel GrupoUsuario { get; set; }

        public virtual EmpresaModel Empresa { get; set; }


        public List<object> FiliaisVinculadas { get; set; }

        public List<MotoristaVeiculoModel> Veiculos { get; set; }

        public List<ConjuntoRequestModel> Conjuntos { get; set; }

        public ETipoPessoa? TipoCobranca { get; set; } = null;

        public ProprietarioModel Proprietario { get; set; } = null;

        public virtual List<MensagemModel> Mensagens { get; set; } = new List<MensagemModel>();

        #region Sub-Classes

        /// <summary>
        /// Para o perfil gestor de frota, são retornoados todos os proprietário que são representados pelo usuário mobile neste dto.
        /// Na SOTRAN é possível que o usúário do aplicativo represente mais de um CNPJ.
        /// </summary>
        public class ProprietarioGestorFrota
        {
            public string CpfCnpj { get; set; }
            public string Nome { get; set; }
            public List<MotoristaGestorFrota> Motoristas { get; set; }
        }

        public class MotoristaGestorFrota
        {
            public string CpfCnpjProprietario { get; set; }
            public string CpfMotorista { get; set; }
            public string NomeMotorista { get; set; }
        }

        #endregion
    }

    public class UsuarioModelSemFoto
    {
        public int IdUsuario { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdEmpresa { get; set; }

        public string Nome { get; set; }
        public string Login { get; set; }
        public string Senha { get; set; }
        public string CPFCNPJ { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<string> CPFCNPJs { get; set; }

        public EPerfil Perfil { get; set; }
        public bool Ativo { get; set; } = true;
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Terceiro;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string IdPush { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RNTRC { get; set; }

        public bool Carreteiro { get; set; } = false;
        public bool ReceberNotificacao { get; set; } = false;

        public bool Vistoriador { get; set; } = false;
        public int? IdHorario { get; set; }
        public AutenticacaoAplicacaoModel Autenticacao { get; set; }
        public Indicadores Indicadores { get; set; }
        public bool PrimeiroAcesso { get; set; } = false; // Created to allow passoword redefinition.
        public bool PrimeiroLogin { get; set; } = false; // Created to and an app notification.
        public bool PreCadastro { get; set; } = false;

        public string CNH { get; set; }
        public string RG { get; set; }


        /// <summary>
        /// Indica se a versão do aplicativo do usuário ainda está sendo permitido usar.
        /// </summary>
        public bool PermiteVersaoAplicativo = true;

        #region Veículo

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Placa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Marca { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Chassi { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RENAVAM { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Modelo { get; set; }

        public bool ComTracao { get; set; }
        public int? IdTipoCavalo { get; set; }
        public int? IdTipoCarreta { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? AnoModelo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? AnoFabricacao { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ETipoRodagem TipoRodagem { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string TecRastreamento { get; set; }

        #endregion

        #region Endereço

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CEP { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Numero { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Endereco { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Bairro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IBGECidade { get; set; }

        #endregion

        #region Filiais

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdFilial { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string NomeFilial { get; set; }

        #endregion

        #region Contato

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Telefone { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Celular { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Email { get; set; }

        #endregion

        #region Horários

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<int> HorariosCheckIn { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public List<int> HorariosNotificacao { get; set; }

        #endregion

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual GrupoUsuarioModel GrupoUsuario { get; set; }

        public virtual EmpresaModel Empresa { get; set; }


        public List<object> FiliaisVinculadas { get; set; }

        public List<MotoristaVeiculoModel> Veiculos { get; set; }

        public List<ConjuntoRequestModel> Conjuntos { get; set; }

        public ETipoPessoa? TipoCobranca { get; set; } = null;

        public ProprietarioModel Proprietario { get; set; } = null;

        public virtual List<MensagemModel> Mensagens { get; set; } = new List<MensagemModel>();
    }
}
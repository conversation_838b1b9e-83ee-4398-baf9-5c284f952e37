﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.GrupoUsuario;
using ATS.WS.Models.Webservice.Request.Usuario;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Models;

namespace ATS.WS.ControllersATS
{
    public class GrupoUsuarioAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IGrupoUsuarioApp _grupoUsuarioApp;
        private readonly SrvGrupoUsuario _srvGrupoUsuario;
        private readonly IParametrosApp _parametrosApp;

        public GrupoUsuarioAtsController(IUserIdentity userIdentity, IUsuarioApp usuarioApp, IGrupoUsuarioApp grupoUsuarioApp, SrvGrupoUsuario srvGrupoUsuario, IParametrosApp parametrosApp)
        {
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _grupoUsuarioApp = grupoUsuarioApp;
            _srvGrupoUsuario = srvGrupoUsuario;
            _parametrosApp = parametrosApp;
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorEmpresa(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                var gruposUsuario = _grupoUsuarioApp.GetPorEmpresa(idEmpresa, null);

                foreach (var grupoUsuario in gruposUsuario)
                {
                    grupoUsuario.Empresa = null;
                    grupoUsuario.Usuarios = null;
                    grupoUsuario.Menus = null;
                    grupoUsuario.NotificacoesPush = null;
                }

                return ResponderSucesso(gruposUsuario);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                var gruposUsuario = _grupoUsuarioApp.GetPorEmpresa(idEmpresa, null)
                    .Select(grupoUsuario => new
                    {
                        grupoUsuario.IdGrupoUsuario,
                        grupoUsuario.Descricao
                    })
                    .ToList();

                return ResponderSucesso(gruposUsuario);
            }
            catch (Exception e)
            {

                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarAtualizar(CadastrarAtualizarGrupoUsuarioRequest @params)
        {
            try
            {
                if(!_parametrosApp.GetPermitirEdicaoDadosAdministrativosGrupoUsuario(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão.");
                
                if (!@params.IdGrupoUsuario.HasValue)
                    _grupoUsuarioApp.Inserir(@params.IdEmpresa, @params.Descricao, @params.IdMenusSelecionados);
                else
                    _grupoUsuarioApp.Atualizar(@params.IdGrupoUsuario.Value, @params.IdEmpresa, @params.Descricao, @params.IdMenusSelecionados);

                return ResponderSucesso(@params.IdGrupoUsuario.HasValue ? "Grupo de usuário atualizado com sucesso!" : "Grupo de usuário inserido com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(UsuarioAtsConsultaGridFilters @params)
        {
            try
            {
                int? idEstabelecimentoBase = null;
            
                int? idEmpresa = null;
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                if (_userIdentity.Perfil == (int) EPerfil.Estabelecimento)
                    idEstabelecimentoBase = _usuarioApp.GetIdEstabelecimentoBase(_userIdentity.IdUsuario);
                
                var grupos = _usuarioApp.ConsultaGrid(idEmpresa, idEstabelecimentoBase, @params.Take, @params.Page, @params.Order, @params.Filters);
                return ResponderSucesso(grupos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idGrupoUsuario)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_grupoUsuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idGrupoUsuario))
                        return ResponderErro("Registro não encontrado.");
                }

                var grpUsu = _grupoUsuarioApp.GetChilds(idGrupoUsuario);
                if (grpUsu == null)
                    throw new Exception($"Nenhum grupo de usuário encontrado para o id {idGrupoUsuario}!");

                return ResponderSucesso(new
                {
                    grpUsu.Descricao,
                    grpUsu.IdEmpresa,
                    grpUsu.Empresa.NomeFantasia,
                    grpUsu.IdGrupoUsuario
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int id)
        {
            try
            {
                if(!_parametrosApp.GetPermitirEdicaoDadosAdministrativosGrupoUsuario(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão.");
                
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_grupoUsuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, id))
                        return ResponderErro("Registro não encontrado.");
                }

                var validationResult = _grupoUsuarioApp.Inativar(id, _userIdentity.IdUsuario);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Grupo de usuário inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int id)
        {
            try
            {
                if(!_parametrosApp.GetPermitirEdicaoDadosAdministrativosGrupoUsuario(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário sem permissão.");
                
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_grupoUsuarioApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, id))
                        return ResponderErro("Registro não encontrado.");
                }

                var validationResult = _grupoUsuarioApp.Ativar(id, _userIdentity.IdUsuario);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Grupo de usuário reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public ActionResult GerarRelatorioGrid(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioGrupoUsuarioModel>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var idEstabelecimentoBase = _usuarioApp.GetIdEstabelecimentoBase(filtrosGridModel.IdUsuario ?? 0);

            var report = _srvGrupoUsuario.GerarRelatorioGrid(filtrosGridModel.IdEmpresa, idEstabelecimentoBase, null, string.Empty,
                filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters,
                filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de grupos de usuário.{filtrosGridModel.Extensao}");
        }
    }
}
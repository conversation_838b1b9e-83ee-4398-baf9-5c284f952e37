﻿using ATS.Domain.Grid;
using ATS.Domain.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Domain.Interface.Service;

namespace ATS.WS.Services
{
    public class SrvGrupoUsuario : SrvBase
    {
        private readonly IGrupoUsuarioService _grupoUsuarioService;

        public SrvGrupoUsuario(IGrupoUsuarioService grupoUsuarioService)
        {
            _grupoUsuarioService = grupoUsuarioService;
        }

        public byte[] GerarRelatorioGrid(int? idEmpresa,
                                        int? idEstabelecimentoBase,
                                        int? idFilial,
                                        string descricao,
                                        int Take,
                                        int Page,
                                        OrderFilters Order,
                                        List<QueryFilters> Filters, string extensao)
        {
            return _grupoUsuarioService.GerarRelatorioGrid(idEmpresa, idEstabelecimentoBase, idFilial, descricao, Take, Page, Order, Filters, extensao, Get<PERSON>ogo(idEmpresa));
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class RotaMap : EntityTypeConfiguration<Rota>
    {
        public RotaMap()
        {
            ToTable("ROTA");

            HasKey(x => x.IdRota);
            
            Property(t => t.From).HasMaxLength(200);
            Property(t => t.To).HasMaxLength(200);

            HasRequired(x => x.CidadeOrigem)
                .WithMany(x => x.RotaOrigem)
                .HasForeignKey(x => x.IdCidadeOrigem);

            HasRequired(x => x.CidadeDestino)
                .WithMany(x => x.RotaDestino)
                .HasForeignKey(x => x.IdCidadeDestino);

            Property(x => x.FromLatitude)
                .HasPrecision(18, 8);
            Property(x => x.FromLongitude)
                .HasPrecision(18, 8);

            Property(x => x.ToLatitude)
               .HasPrecision(18, 8);
            Property(x => x.ToLongitude)
                .HasPrecision(18, 8);

            HasRequired(t => t.Empresa)
             .WithMany(t => t.Rotas)
             .HasForeignKey(d => d.IdEmpresa);
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsPedagioReciboPagamento">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>44e454a4-20c5-4938-955e-daa6b833e1f8</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPedagioReciboPagamento1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b29476b5-8311-47ad-8b2a-119991edb1e7</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="ReciboPagamentoDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPedagioReciboPagamento</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataImpressao">
          <DataField>DataImpressao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Contratante">
          <DataField>Contratante</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Filial">
          <DataField>Filial</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DocumentoProprietario">
          <DataField>DocumentoProprietario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Contratado">
          <DataField>Contratado</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CnpjContratante">
          <DataField>CnpjContratante</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CnpjFilial">
          <DataField>CnpjFilial</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Ciot">
          <DataField>Ciot</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DocumentoCliente">
          <DataField>DocumentoCliente</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IdViagem">
          <DataField>IdViagem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataInicioViagem">
          <DataField>DataInicioViagem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placas">
          <DataField>Placas</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Rntrc">
          <DataField>Rntrc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CartaoContratado">
          <DataField>CartaoContratado</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Motorista">
          <DataField>Motorista</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DocumentoMotoristra">
          <DataField>DocumentoMotoristra</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CartaoMotorista">
          <DataField>CartaoMotorista</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="RazaoSocialRemetente">
          <DataField>RazaoSocialRemetente</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CnpjCpfRemetente">
          <DataField>CnpjCpfRemetente</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="RazaoSocialDestinatario">
          <DataField>RazaoSocialDestinatario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CnpjCpfDestinatario">
          <DataField>CnpjCpfDestinatario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Fornecedor">
          <DataField>Fornecedor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Protocolo">
          <DataField>Protocolo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NomeCampoComprovanteCarga">
          <DataField>NomeCampoComprovanteCarga</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CampoComprovanteCarga">
          <DataField>CampoComprovanteCarga</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValorComprovanteCarga">
          <DataField>ValorComprovanteCarga</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusComprovanteCarga">
          <DataField>StatusComprovanteCarga</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MensagemProtocoladoAnttComprovanteCarga">
          <DataField>MensagemProtocoladoAnttComprovanteCarga</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.ReciboPagamento</rd:DataSetName>
        <rd:TableName>ReciboPagamentoDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.Recibo.ReciboPagamentoDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="ParcelasDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPedagioReciboPagamento1</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="IdEvento">
          <DataField>IdEvento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TipoEvento">
          <DataField>TipoEvento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataPagamento">
          <DataField>DataPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Instrucoes">
          <DataField>Instrucoes</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusEvento">
          <DataField>StatusEvento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PagamentoCartao">
          <DataField>PagamentoCartao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.Recibo</rd:DataSetName>
        <rd:TableName>ComprovanteCompraPedagioHistoricoPracasDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.Recibo.ComprovanteCompraPedagioHistoricoPracasDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="Textbox1">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Contratante:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>2.7553cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox2">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Contratante.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>2.7553cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>9.80971cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox19">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>RECIBO DE PAGAMENTO</Value>
                    <Style>
                      <FontSize>22pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox19</rd:DefaultName>
            <Top>0.66104cm</Top>
            <Left>8.99017cm</Left>
            <Height>1.31673cm</Height>
            <Width>12.15744cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox20">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Data Impressão:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox20</rd:DefaultName>
            <Top>0.97854cm</Top>
            <Left>22.4253cm</Left>
            <Height>0.63675cm</Height>
            <Width>3.26336cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox21">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!DataImpressao.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox21</rd:DefaultName>
            <Top>0.97854cm</Top>
            <Left>25.68866cm</Left>
            <Height>0.63675cm</Height>
            <Width>3.33309cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CNPJ:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>2.7553cm</Top>
            <Left>12.74cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox5">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Id Viagem:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>2.7553cm</Top>
            <Left>20.75573cm</Left>
            <Height>0.6cm</Height>
            <Width>4.44786cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!IdViagem.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>2.7553cm</Top>
            <Left>25.23887cm</Left>
            <Height>0.6cm</Height>
            <Width>3.78288cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox3">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Filial:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>3.49641cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Documento:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.23752cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox9">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Filial.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>3.49641cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>9.80971cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!DocumentoCliente.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.23752cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>9.80971cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox11">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Contratado:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox12">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Contratado.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>9.8097cm</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox13">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CNPJ:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>3.49641cm</Top>
            <Left>12.74cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>14</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox14">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CIOT:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.23752cm</Top>
            <Left>12.74cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>15</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox15">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Documento:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>12.74cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>16</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CnpjContratante.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>2.7553cm</Top>
            <Left>15.32634cm</Left>
            <Height>0.6cm</Height>
            <Width>5.39412cm</Width>
            <ZIndex>17</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox16">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CnpjFilial.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>3.49641cm</Top>
            <Left>15.32634cm</Left>
            <Height>0.6cm</Height>
            <Width>5.39412cm</Width>
            <ZIndex>18</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox17">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Ciot.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.23752cm</Top>
            <Left>15.32634cm</Left>
            <Height>0.6cm</Height>
            <Width>5.39412cm</Width>
            <ZIndex>19</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox18">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!DocumentoProprietario.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>15.32634cm</Left>
            <Height>0.6cm</Height>
            <Width>3.91627cm</Width>
            <ZIndex>20</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox22">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Data Início Viagem:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>3.49641cm</Top>
            <Left>20.75574cm</Left>
            <Height>0.6cm</Height>
            <Width>4.44785cm</Width>
            <ZIndex>21</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox23">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Placas:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.23752cm</Top>
            <Left>20.75574cm</Left>
            <Height>0.6cm</Height>
            <Width>4.44785cm</Width>
            <ZIndex>22</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox24">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>RNTRC:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>19.27789cm</Left>
            <Height>0.6cm</Height>
            <Width>1.76007cm</Width>
            <ZIndex>23</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox25">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Rntrc.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>21.07323cm</Left>
            <Height>0.6cm</Height>
            <Width>2.36631cm</Width>
            <ZIndex>24</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox26">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Cartão:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>23.43954cm</Left>
            <Height>0.6cm</Height>
            <Width>1.76405cm</Width>
            <ZIndex>25</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox28">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Placas.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.23752cm</Top>
            <Left>25.23887cm</Left>
            <Height>0.6cm</Height>
            <Width>3.78288cm</Width>
            <ZIndex>26</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox29">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CartaoContratado.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>4.97863cm</Top>
            <Left>25.23887cm</Left>
            <Height>0.6cm</Height>
            <Width>3.78288cm</Width>
            <ZIndex>27</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox27">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!DataInicioViagem.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox21</rd:DefaultName>
            <Top>3.49641cm</Top>
            <Left>25.23887cm</Left>
            <Height>0.6cm</Height>
            <Width>3.78288cm</Width>
            <ZIndex>28</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="Tablix3">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.25719cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.09438cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.51772cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>12.13339cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.18177cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>3.0134cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.78862cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox64">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Detalhado de Parcelas</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox64</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox66">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox66</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox76">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox76</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox73">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox73</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox70">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox70</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox36">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox36</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox65">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Id Evento</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox58</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox58">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Tipo Evento</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox58</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox60">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Data Pagamento</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox58</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox62">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Instruções</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox58</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox74">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Status</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox74</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox71">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Valor</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox71</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox37">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Cartão</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox37</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>Cornsilk</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="IdEvento">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!IdEvento.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>IdEvento</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox61">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TipoEvento.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox59</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox67">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DataPagamento.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox59</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox63">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Instrucoes.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox59</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox68">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!StatusEvento.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox59</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox69">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Valor.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox59</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PagamentoCartao">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PagamentoCartao.Value</Value>
                                  <Style>
                                    <FontSize>11pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PagamentoCartao</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Detalhes" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>ParcelasDts</DataSetName>
            <Top>7.9437cm</Top>
            <Left>0.03528cm</Left>
            <Height>1.8cm</Height>
            <Width>28.98647cm</Width>
            <ZIndex>29</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox86">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox86</rd:DefaultName>
            <Top>9.7437cm</Top>
            <Left>0.03528cm</Left>
            <Height>0.6cm</Height>
            <Width>21.00268cm</Width>
            <ZIndex>30</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>LightGrey</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox87">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>TOTAL:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox86</rd:DefaultName>
            <Top>9.7437cm</Top>
            <Left>21.03796cm</Left>
            <Height>0.6cm</Height>
            <Width>3.21705cm</Width>
            <ZIndex>31</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>LightGrey</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox94">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!ValorTotalParcelas.Value</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox94</rd:DefaultName>
            <Top>9.7437cm</Top>
            <Left>24.25501cm</Left>
            <Height>0.6cm</Height>
            <Width>2.97812cm</Width>
            <ZIndex>32</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>LightGrey</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Image Name="Image2">
            <Source>Embedded</Source>
            <Value>logoextrattalaranjapreto</Value>
            <Sizing>FitProportional</Sizing>
            <Height>2.57891cm</Height>
            <Width>6.15414cm</Width>
            <ZIndex>33</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Image>
          <Textbox Name="Textbox30">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CartaoMotorista.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>5.71974cm</Top>
            <Left>25.23887cm</Left>
            <Height>0.6cm</Height>
            <Width>3.78288cm</Width>
            <ZIndex>34</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox31">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Cartão:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>5.71974cm</Top>
            <Left>20.75574cm</Left>
            <Height>0.6cm</Height>
            <Width>4.44785cm</Width>
            <ZIndex>35</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox32">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!DocumentoMotoristra.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>5.71974cm</Top>
            <Left>15.32634cm</Left>
            <Height>0.6cm</Height>
            <Width>5.39412cm</Width>
            <ZIndex>36</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox33">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Documento:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>5.71974cm</Top>
            <Left>12.74cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>37</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox34">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Motorista.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>5.71974cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>9.80971cm</Width>
            <ZIndex>38</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox35">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Motorista:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>5.71974cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>39</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox88">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value />
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox86</rd:DefaultName>
            <Top>9.1437cm</Top>
            <Left>27.23313cm</Left>
            <Height>0.6cm</Height>
            <Width>1.78862cm</Width>
            <ZIndex>40</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>LightGrey</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Image Name="Image7">
            <Source>Embedded</Source>
            <Value>Campoassinaturas</Value>
            <Sizing>FitProportional</Sizing>
            <Top>10.4148cm</Top>
            <Left>0.30868cm</Left>
            <Height>2.59483cm</Height>
            <Width>28.71307cm</Width>
            <ZIndex>41</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Image>
          <Textbox Name="Textbox38">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Remetente:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>6.42557cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>42</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox39">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!RazaoSocialRemetente.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>6.42557cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>20.69733cm</Width>
            <ZIndex>43</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox40">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Destinatário:</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Right</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>7.1314cm</Top>
            <Left>0.30868cm</Left>
            <Height>0.6cm</Height>
            <Width>2.55106cm</Width>
            <ZIndex>44</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox41">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!RazaoSocialDestinatario.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>7.1314cm</Top>
            <Left>2.89502cm</Left>
            <Height>0.6cm</Height>
            <Width>20.69734cm</Width>
            <ZIndex>45</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox42">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CnpjCpfRemetente.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>6.42557cm</Top>
            <Left>23.62763cm</Left>
            <Height>0.6cm</Height>
            <Width>5.39412cm</Width>
            <ZIndex>46</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox43">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CnpjCpfDestinatario.Value, "ReciboPagamentoDts")</Value>
                    <Style>
                      <FontSize>11pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox2</rd:DefaultName>
            <Top>7.1314cm</Top>
            <Left>23.62763cm</Left>
            <Height>0.6cm</Height>
            <Width>5.39412cm</Width>
            <ZIndex>47</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>13.00963cm</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>29.32705cm</Width>
      <Page>
        <PageHeight>21cm</PageHeight>
        <PageWidth>29.7cm</PageWidth>
        <LeftMargin>0.35cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="ValorTotalParcelas">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>ValorTotalParcelas</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="logoextrattalaranjapreto">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAYGBgYHBgcICAcKCwoLCg8ODAwODxYQERAREBYiFRkVFRkVIh4kHhweJB42KiYmKjY+NDI0PkxERExfWl98fKcBBgYGBgcGBwgIBwoLCgsKDw4MDA4PFhAREBEQFiIVGRUVGRUiHiQeHB4kHjYqJiYqNj40MjQ+TERETF9aX3x8p//CABEIAGIA0gMBIgACEQEDEQH/xAAxAAEAAgMBAAAAAAAAAAAAAAAABAUCAwYBAQEAAwEBAAAAAAAAAAAAAAAAAgMEBQH/2gAMAwEAAhADEAAAAuqAAPB7ExrlNats4h6AAAAAAAAY5RY+7cuZ359XQ6NtBbTjs0XWPZKzOhzg9AAAAAAAAAU8HoqHFuQJsOFthf6JGzn4VkHyyF/op9RexYeRlYx6outcqsLCLTTjdhNpy3y3QCXYcX2Z6ABT2HL06LLZDiZdPYewpu7n8pKt6+Xkqh6LMrY1puMKS4zNFX0lSVm+5yPeevfDCl6CEVHYV9gAPPYsVbBnbMHQ11/V1WimLfUs+KaNeUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAC/9oADAMBAAIAAwAAACEAASsAAAAAAAAAAL+osIAAAAAAAAAC/IsgQijiCQwAABgOqjQxBSCxSgAfIUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//EAAL/2gAMAwEAAgADAAAAEPPPI3/PPPPPPPPAzpilfPPPPPPPPKKd2QCBKLJOHNPKFIKnNMJDKANFPPd9JlPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP/8QAIxEAAgEEAgICAwAAAAAAAAAAAgMEAAUSEzAyAREQQiJQUv/aAAgBAgEBPwD5O4RALHy4KAxMch4nEQLMgDIqC+ltAWJwGrhM0xcg7H0q3wmTJn5dex0IiI4jx3yF5WzcHUqjPZIHwo+w1AijGV6+3JdZ
K9en7FUFjBkZlrxGksFqxMeN7NaTP+aFc+VLyMGeiZVyt2uMBJDr3qxulAZpatmP6f8A/8QAJxEAAgEDAwQABwAAAAAAAAAAAgMEAAESBSIyBhETMBAUMUJQYrL/2gAIAQMBAT8A+IafNMchjsohISxL1IFZuADPESv9af0vggmKfmVaVAKVLsN+Ac61WaEKLt5cAorkRd7+vp7UvKj5dp7g/miWiNm0Qwy3nWpTSlyTL7LcPZoUEms81+AVJhg5Jhmzd+1OUaXGo+Q+tIeRoB35UEqBDh4gxfYbVpesXKQwZB7C4Vry4rredTF5fh//xAA2EAACAQMCAwQHBwUBAAAAAAABAgMABBEFEhMhMRAiQVEgMkBCUmGRFBUjU2JxgSQzUGChwf/aAAgBAQABPwL/AAL3cSNtOa+3RfOo5BIuR7IWA6muLH8YoOp6HsuJeFHnx8OyNC7BRSIEUAeyXUPFjx4+FYqCThuD9aDArmrm44snyHSgatItq7j1Ps17BhuIPHr2PcSLblBQNWUPFfJ9UdjyJGpZjgCjrNrn3vpUNxFNHvQ5q31CCeTYud3zq4v4Ldgr5z5CtRvuHEFTIdhmtNvkwI3LF2ap9RtoG2sTnyFW+pwTyBFVs0+r2qMV7xq2v7e4O1T3vI1ql7wl4aMQ/wD5Wm3ybUiYsZGNapfsDwonII9atPvIpFSEbiwXnmptUtoXKHcSKg1K2nbaCQfn6LoHUg1JGY3KmsVw24mwePSoIRDGFHZrUp4iReGMmgNI4O0t3sda0QnjyDw21fI1pfCVPHmK062a4mNzN58q1tU/CbHeNWFjAsUMu3v4zWxbnVGU9C9R2ltCd6IF5dac6MGbPM5qPYL+Pgk7d4xWtqgeNgO8etWVjAiQybe/t61qsaC8AXq3X+ags7eDvIuDip20niuX5setT8D7Qv2cnHKh09G8nh4wQrk1mP8AL/7UEsCzqCnPwPbqoxfd71cChDopGeL/ANq2gs7d
GmiPIjrU7zX8rsg7qDlWjT7o2hPu9K1z+7EP01bXMDxKEcZVOYrShvvnb9zWruy2nL3mxVlDp7Q7pn73lmrJUbUhs9UEkVrR/qIx+ira4glQCNwcKOVXzAanlugK1d3SNZTPE+fD61p8Vi6sZ35+VbYW1BFh9TeMejcS8KMtT5Zix6mg/wCHurmTmrOfixc/WHXsuLSG4XDj9jX3JB+Y9PZRtbrBubaKt7WK3j2J/NRadBDNxULZ8qu7OG5AD+HQ1badFb8TDMdwxVrYxWxYoTzqaGOVCjjIr7lt/jerawt7dtyg58zV1Zw3QG/qPEVaWMdruKknNXWnwXLbjkN5ioLKGGFovWDdc0dFts8nYVBpttAwcZLDxPo3UnFf5DpTLS2Ja1z73UUFqBjE4b60pBGfZbnfswo61wJfgNRWrs43Ly7Li1O/ci9a4EvwGrTiAFWX/Zf/xAApEAEAAgIBAwMDBAMAAAAAAAABABEhMVEQQWEgcZFAgbFQocHRYPDx/9oACAEBAAE/If0FhYTdE8PxmedfSF2AT/uTUj0FO5geZaquVndsmoIPpLEaZ+yYKJSRTdaEAXwl3LNHtdFj7+E9Vy/TcuX68TFOP3ITQMu+B6K0ndfPiEIvtLC1BnMPKY3yRJXBeFfEepMui6uC6PjGhh93+5jJ47DUJJQuTGJWJhpo4lrZwqZl8abDSjW+cudyxhsjEfzJ3hDNRpoh1j0Gr9JBYSdiDTySiU6Zjlaq+6dq7by9FicPumGuHz7uCHv+DAxoXivuTJsZeT/UHT2S+CIPnr7s2JbZ4JeyFMu0woaylu5/aoMwyFt4J2i5bd2BXgHzYdz2qrAsDNLcypsyi+zctS+PTiMDd1V9oc6KbDssur65EtvuhuCbDwuMQz1t4Ixm+jgP5l5c5eyJ4V3943IF7hU8GnzMVGND9mVVZtqlVK5/FJG4R+TEUFzuJyj72JghTI7RSKDhNYh+ZAV6Rc32OWXgtLWEBcS3
tzKa3Z/t0PZZo2T/AFCU2uJy1GwNOU7blZPLbGYDCKwbmSZfXhi9KabZYxYs3UcYluSuy5WJNOyMphpXxBiiK5JWdinJC43CJ1Ca9CgZnwl0LZY0ZXDB7DkgoafpdtLg+3Trig5b7wAqbuG1c9ODwA0/S10rpUqV/hX/xAAoEAEAAgICAAUEAgMAAAAAAAABABEhMUFRIEBhcYEQkbHBYNGh4fD/2gAIAQEAAT8Q8Vnm187IBBYA2xTJXlLclytSz+nLnH9Ny5fM+VQtuxavLCabeXo5YF1UjygfuzRa9hSJp6lueVDkhxLuGK7j5Y0++36OnRYHfAeVBUVWRVU12+YZezhJc7RKoWRaLwuIAFQheWqgiPq7GZPqBSdJ3HucUyq2QN+GSDS4wtnaLorftFTM5pJuxb2RGUiwl7GFwkHi84ImZe4kSx86KRcdoNFrGhsXFEzj4vnXCF5rJBcbej2GKZWFJBHY/qEUnWL7jbMZmtbjuHIqlRhiyX/kwNBX/wBxgwJu3X9aO7F9LyyjS2sQSa0masZZ4ZXY5XcCgip64vzM43YbEr6Fdsw/W3HgJWzTruIjQJ4+ruWZvtARl2SLDvBc9fVv38J9qQ0LRT/bcpudGp8Pn6VMr3ueECa+u5lJFlY7sx/r/pqtMeLZ+Wzb9PlRlbqkEU2QkS/xtEcbN9xZTPycJgFoPr4Yxg5kK57FYNVkmXAlyqAk6Lvtwf8Ar3TB5DV2TY5fDTI1qQmCozdzhL+5lil2x6lRxAP4+X0uNbtUi22r2jERpYg5UtZIxcrk37MZ2lqYxYysuhMsA0BUBdsQmi7xC/7PCPZ6ymuIsogd65gOiGFnXqgrPqHB0qXt4qsIBlS9tlcT22Oxzs0r4B4FCtRUB2B32zZiAQr+7T7xFSImESX81oQV8QsTyrZelpshphcEv6CAA1VEK0Z54QDHJCtv5KL/2Q==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="Campoassinaturas">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAyAAAABGCAYAAAA0NfCyAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAErGlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSfvu78nIGlkPSdXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQnPz4KPHg6eG1wbWV0YSB4bWxuczp4PSdhZG9iZTpuczptZXRhLyc+CjxyZGY6UkRGIHhtbG5zOnJkZj0naHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyc+CgogPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9JycKICB4bWxuczpBdHRyaWI9J2h0dHA6Ly9ucy5hdHRyaWJ1dGlvbi5jb20vYWRzLzEuMC8nPgogIDxBdHRyaWI6QWRzPgogICA8cmRmOlNlcT4KICAgIDxyZGY6bGkgcmRmOnBhcnNlVHlwZT0nUmVzb3VyY2UnPgogICAgIDxBdHRyaWI6Q3JlYXRlZD4yMDIzLTAyLTE3PC9BdHRyaWI6Q3JlYXRlZD4KICAgICA8QXR0cmliOkV4dElkPmY0OWNlN2QxLTM5ZjktNDM5ZS1iNmFlLTRiZWI0ZTZmZjYzMTwvQXR0cmliOkV4dElkPgogICAgIDxBdHRyaWI6RmJJZD41MjUyNjU5MTQxNzk1ODA8L0F0dHJpYjpGYklkPgogICAgIDxBdHRyaWI6VG91Y2hUeXBlPjI8L0F0dHJpYjpUb3VjaFR5cGU+CiAgICA8L3JkZjpsaT4KICAgPC9yZGY6U2VxPgogIDwvQXR0cmliOkFkcz4KIDwvcmRmOkRlc2NyaXB0aW9uPgoKIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PScnCiAgeG1sbnM6ZGM9J2h0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvJz4KICA8
ZGM6dGl0bGU+CiAgIDxyZGY6QWx0PgogICAgPHJkZjpsaSB4bWw6bGFuZz0neC1kZWZhdWx0Jz5fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fX19fIChBc3NpbmF0dXJhIGRvIEZhdm9yZWNpZG8pIC0gMTwvcmRmOmxpPgogICA8L3JkZjpBbHQ+CiAgPC9kYzp0aXRsZT4KIDwvcmRmOkRlc2NyaXB0aW9uPgoKIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PScnCiAgeG1sbnM6cGRmPSdodHRwOi8vbnMuYWRvYmUuY29tL3BkZi8xLjMvJz4KICA8cGRmOkF1dGhvcj5HdWlsaGVybWUgU2FudGFuYTwvcGRmOkF1dGhvcj4KIDwvcmRmOkRlc2NyaXB0aW9uPgoKIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PScnCiAgeG1sbnM6eG1wPSdodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvJz4KICA8eG1wOkNyZWF0b3JUb29sPkNhbnZhPC94bXA6Q3JlYXRvclRvb2w+CiA8L3JkZjpEZXNjcmlwdGlvbj4KPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KPD94cGFja2V0IGVuZD0ncic/PrpPJGwAACAASURBVHic7Z15eFRFtsB/vSSddAIhCZElkISEJYDsBMIyBsP2ZDE6CjoCAqPgIKMiIuCgPn066CCouKMg4oID6qCCuw+V0RFHQYRBdpTFIGHCmpCll/P+4NXldqcTUAGVOb/vy9e5dWs5dapu9zm1XYeICIqi/Oowj67D4UBEQq4N5l54+Kkq3+FwEAwGrf+dTif2rxRTfiAQAMDlcllhP0QeE/9U5HWmscseDAYtHTkcjp/UPvZ0gUAAEcHpdIbkf7oIbw+Hw4HL5Tptfe1EMgSDQQBLhlPdR+z1CgaDVjva+/vJ5G9/ZoLB
4GnRWyS9OJ3OX8WzoijKfw4OdUAU5ddNdUaFMQx/qJH0U8o+0XVN8v7Qsn5KXqeDcOPSGL/2e+GEOyQ/pdzqrk8XkcqFM+N81CTD6ewjpr2qu/4xeZyO9vqlPyuKoig/7JtTUZRfDGbWw+FwUFJSQnFxMYcOHaK0tBSfz4fL5cLpdFojrfaZEvuMSaT/w8Mi3TMOzrvvvsugQYN47bXXcDgc+P3+ENmOHj3K1KlTmTVrFkAVOSLlHalsk2769OncdtttVFZWhszA1FS3E5V1MvW1j9XUlNbpdOJ2u0PqaQy/m2++mdzcXH73u99RWlqK0+m09PhjZDJtu3jxYv7whz+wa9euk9KJvR4nqk+kOKbcoqIirrvuOp555pkQZ+tk2uOHlh8e38iwb98+rrrqKqZOnYqIUF5ezpQpU6rtbydTfnX93el0smrVKkaNGsXHH39std/J9j/jfGzbto0xY8bw+uuv19heP0RPNenF7/efUA92PSuKopx2RFGUXyXBYFACgYCIiFx55ZUCSFRUlADSpEkTmTx5spSWlobE9fv9Vnq/32+lr+46GAxGvOfz+aSiokJERP785z8LIJMmTQqJ6/P5RERk8+bNAkjt2rWlvLzcksfkHQwGQ+QKL9se9/DhwwIIIIWFhSIiEggEquTh9/tDZLbfr67c8DLt16bO4TJFkn3v3r3y4osvysGDBy35RESuueYaAeSGG26QmJgY6dy5sxw5ciQkTqQ2OpFMIiLnn3++ALJkyRIREamsrAzJJzytXafh5Zhre3uHy2XKXb58uQDSvn37kHwj1SNSXWoqz952kfqLud64caMAkpiYKCIi27Zts/qb6aNG3kj1ranf2+tu5L3rrrsEkJtuusnK28gU3t/C61hZWSkiIgsWLBBALr744pAyT6b9q6tHdXpJTk62njufz3dCPSuKopwJ3D+H06Moyqnl6NGjAOTm5pKamsqyZcuYMWMGFRUVPPjgg/j9fqKiogCs
kVyXywUcH/00136/P+R+IBAIued2u3G73fh8PgBiY2MBSExMBKCiogKv12vNvjRr1owVK1aQkJCAx+OxRlrta9LD87fLZh+5rVWrFp9//jl+v58GDRpYMw6Alcbn81l1BaisrCQ6OhqXyxWy/MVerj1NuD7MPbfbXWVGw5QbCATw+XzExMTw9NNPc8stt7B161YSEhJwOp0cOnSIdu3a8cUXX9CpUyduvPFGlixZwnfffUeLFi2svIxsZkS8ujayywQwd+5ctm7dSn5+PiISUhfT9pH2Gphru/4BK1/7HgJzHQwGrfu/+c1veO+990hPT7fqYGbbItXD9A+Px2O1h70dzGyCvXz7EjV7fzHtmpSUhMvlom7dulRWVpKZmclHH31EnTp1iI6ODskvvH/Z+7bP5wu5F94mlZWVuFwurrvuOjp27EjXrl1DdGXS2fubITxs6NChnHPOOZx77rlWGTXpzejhRM9JJL0kJSVZ+RpZa9KzoijKmUCXYCnKWYAxOK+//npefPFFXnnlFQDeeecdKioqiIqKYseOHYwYMYIGDRrQqFEjJkyYwJEjR6x9IuvWrWPIkCE0adKEzMxMRowYwf79+3G5XKxYsYLevXtTt25dsrOzefjhh6sYrfv27WPs2LGkpaUxcOBANm/ejNPppLCwkPvuu4+HHnoIn88XYmQ5HA7+/e9/M27cOLKyskhKSqJNmzY89thjVt3sRnNZWRn3338/f/nLXygpKSEYDNK3b1/uuusu5s6dS3p6Oi1btuS2225j8+bNDBw4kIyMDHr27Mnf//53a8nMqFGjGDFiBMuWLaN169Y0a9aMadOmWWU5nU7eeOMN8vLyaNy4MZ06deKpp56yjMR169aRk5PDO++8w8KFC0lOTmb+/Pm8/fbb/PnPfwagoKCArl27smnTJhISEjh8+DBjx47F6/UycOBAvF6v5XysX7+erl27snDhQm6//XYaNGjAueeey8yZMy1dOZ1OXn/9
dXr16kVaWhotWrTgzjvvBGDhwoVMnz6dVatW4XA42Lp1K8OHDycjI4OUlBRycnL461//WmW5j9HrU089RU5ODikpKWRkZHDVVVexd+9ea6P1gQMHuOGGG2jRogUZGRnk5+ezYcMGvv32W+655x7mzZsHQFFREd26deOxxx5j1qxZpKam0rp1a2bPns3atWvp0aMHmZmZ9O/fn/Xr11uG99/+9jd69uxJvXr1SE1NZciQIWzdujVkCaHdOL733ntp1aoV2dnZPPfcc8TExFBRUYHb7aaoqMjqb2aZ3sMPP0zr1q1JTk6mZcuW3HrrrdYyxb///e9ccMEFpKen07RpU8aNG2fptFu3bmzatIlJkyaRnp7Ov/71Lz799FOmTZvG0qVLAXj22Wfp3r077733HhdddBHp6emcd955rFy5knnz5ll9cvLkyVRUVACwcuVK7r77bhYtWgTAunXr6NKlS7Xt73Q6T/icVKeXyspKy5F85ZVXTqhnRVGUM8Jpn2NRFOW0YF+Cdfnllwsgzz//vIiIzJs3TwDp0aOHiIiUlpZKq1atBJAxY8bIZZddJoBceumlIiKyY8cOSUlJsdK0a9dOWrVqJSIi69atE0Cio6NlypQp0qVLFwHk/vvvFxGRu+++WwDxeDySk5Mjbdq0EUA6d+4sIiLbt28XQFJSUkKWYBnZi4uLpUWLFtKvXz+ZOHGi1K1bN2Q5kX3pU0lJibXM7MCBA1JeXi6xsbHWEpzevXtbS7Q8Ho+0a9dO2rZtK4BkZmZaeWVnZwsgSUlJ0qdPH0lMTBRAZsyYISIib7zxhpXPwIEDJTk5WQC59957RUTk/fffF0BSU1OteE888YRMmzZNAHE6nZKbmyv9+/eXLVu2iIhI586dJScnRyZPnixNmjQJ0eF7771n5dOoUSPp06ePdf3666+LiMjSpUutsAEDBki9evVkxIgRIiJW/FdffVVERL7++mtp2LChXHLJJTJ+/HhLZ//85z9F
5PjSHbOk57rrrpP09HSZNGmS5OXlCSBDhw61+prJv0WLFtK7d2+pVauWbNmyRT777LOQtt66daslY/369a2lYYDExMRIly5dpFmzZgLI+eefb+V/7733Sr169WTChAkyePBgAaRr164hfd0sozJ92+12S69evay2yczMFBGRb7/91mpbEZG3335bAElPT5dp06ZJq1atpG/fviIismrVKnE4HAJInz59JCsrS/Ly8kRE5I477rDSGfl37NghDzzwgAAyceJEERG57bbbBBCXyyUdO3a0njO32y116tSR3r17S3R0tADy+OOPi4jI/PnzQ54/058itf+yZctERKSoqCjic/LSSy+JiMiTTz4ZUS9Nmza1lmKejJ4VRVHOBOqAKMqvFLsRP3ToUMvY6NChgzgcDvF4PJbx8sILLwggl19+uZW+a9euAkhJSYllVI0ePdq6b4yW8ePHCyAPPfSQiBxzVpxOp7Ru3VpERO677z4BZNCgQSJybJ9GZmamAPLll19KUVGRJZtZk28MHft6dBNmHJqpU6eKyLH9DHYHJCUlRWJiYuTgwYNSXl4u9evXD3FYJk6cGOJ87du3TxISEsTpdMqOHTtERCQnJ0cAmTdvnoiILFmyJMSQHjRokADy9NNPi8hxJyw5OVlERD755BPLcJ07d66UlZVZBvK5554rgHz77bdV6mj+NwaoaY8PP/zQ2rdg0o0bN04AufPOO0VEpH///pajIyJSXl5u6fPiiy8WQN56662IfcXsP3n00Uctndp1bv9//fr14nA4JDU1VUREPv74YwGkVatWUlJSYrWx/Z5xJrZv3245hKtXrxaR487xkCFDRERkzZo1Asg555xj5Wdn3759kpycLG63W4qKiiy9Gfn69u0boocPPvggxAEx/TMjI0NERJ555hkBJDc3V7755puQsq699loB5NZbbxWRY86u6fczZswQQBo0aCAff/yxJeusWbMEkGnTpomIyD333COA9OrV
S0RECgsLpXbt2gLI4sWLRUQsx/SPf/yjiBx/Hq+88koRqbn977rrrhCZq3tOevXqFVEvTZs2tRz/k9GzoijKmUCXYCnKWURlZSVbt25FRHjkkUcYOHAgAJs3bwZg2bJlxMTE4PV6+eyzzwDYvn07O3bsAI6t6Tf5mL0dW7ZsAeBPf/oTHo+H1q1bEwwG2blzJ3B8XXmnTp0AqFWrFh07dgRg165dVfYTGMyyoi1btnDZZZeRkpJCXFwcM2bMALDe5RC+LCQQCFh5ORwOKioqiI6OttbkZ2ZmAtCjRw8A4uPjSU5OJhgMWnlWVlYC0LlzZwA6duyIy+WisLCQQ4cO8c033wBYebZo0YJmzZpRXFzMzp07rb0F3bp146qrriImJsbaF2PKMPtynE4n33//PWPHjiU1NRWv18sNN9wQsY5t2rQhLS0tpB5mqdvWrVtD2sjsxYHjy9RMfitXrmTgwIHUqVOHWrVq8cILLwDHl8uZNObUsunTp9O8eXNiY2Pp3r17yHKnbdu2AdCtWzfi4uLw+Xx4vd6Qcu3tUVZWRqNGjWjZsiUAGRkZIXI3bNjQWhpk0j/22GO0adMGr9dLVlYWxcXFREdHW/IaWSsrK61+l5eXZ7VRrVq1rD1JdnkCgQAXXngh3bt3Z+XKlTRp0oT+/fuzbt064HjfNrIZvZp2Axg1ahQ9evQI2VcTXmc43t8SEhKIj4/H5XKRm5sbogPTPuFy1tT+Js3XX38d8Tkx8uzateuEejkZPZsleva8FUVRTjXqgCjKWYAxlmbPns3KlSsBeOSRRzh8+DBw3KjKz8/nueee4/777+eZZ55h4cKFNG/e3DJAjGEeFRVVZePqmDFjWLBgATNnzuS5557jmWeeAY4bUWZ9O0BpaamVNpIRYwxKgLFjx7J48WIuvvhivvrqKyZMmBBSpx+qA2Ow2Y3ycBlM2cY4M45DdHQ0UVFRxMTEhNy318/oA45vwA93rgA8Ho/1/5QpU3jq
qafIzc3ls88+Y/r06SFymM/o6GhLfrtRaC/XyGSOWbZjnJXLLruMN998k2uvvZY1a9ZwxRVXhNy362n+/PlMmzaN6Oholi5dymuvvWZtrLfrtby8HMDa0G6XLRx7PSIZ3eYvPj6e9957j/Hjx1NUVMRzzz3Hhx9+yDnnnENlZWXIgQHm076R2oRF2r/gdDqprKwkMTGRTz75hOeee45OnTrx7rvvMnjwYCoqKqq0s9vtJjo6ukpd7O1RHUbWcCcvkg6qI1L7m3xP9JxUp5dAIIDH4+Hdd989oZ5Nn9KXFiqKcrpRB0RRzgKMQXj06FFatWrFhAkT+Oqrr6wN0V26dAFgz549FBQU8Ic//IGRI0fSoUMHPB4PzZs3B+Cvf/0rxcXFHDx40Noga0Z2fT4fl19+Oddccw2DBg2iffv2wHGD55VXXmHPnj188cUXfPjhhwBkZ2dbpwfZDXfAekfIpk2bcDgc3HTTTTRt2pS1a9cCVIlvsJ/+ZK7tDpPRhfl0OBxWmvA48+fPt+pt5PV6vdbMx/PPPw/Aq6++ys6dO8nKyqJhw4aWwRruQNhPdPr222+pqKjg8OHD1kj7uHHjaNOmDV9//TVw3Li1yxUuozFiW7VqZcksImzZsoU333wzJG50dDSlpaXs3LmT2rVrc+utt5KVlcVXX30VUo49302bNgHHTmbq06cPu3fvJhAIEBcXF1Lu22+/zerVq/H7/bz11lvs2bPHcsDsjo05nSm8HnZnwu7EbNy4EYBBgwZxySWXUFpaSlFREXFxcSEOTjAYJCoqyppZMbM68+bNo7S01JqVMU6K0+kkNjaWzZs38/XXXzN8+HCWL19O48aN2bFjByUlJbRr1w6A5557jvLycgoLC60DHEwfC2/j8FPXzLW9fm63O6RPhqcJd+5ran8z81Ldc2KoTi+mjU5Gz3v37mXPnj0RN/4riqKcStQBUZSzgAMHDgDHR6n/+7//m8aNGzNjxgwWLVpEv379
uOqqq/j888/xeDykpaXhcDgYOXIkAMOHD6dt27Z8+OGH1K1bl6SkJIYNG0Z5eTnXXXcdnTt35pFHHsHj8dC4cWMSExOZPXs2APv37wegpKSEhg0bkpOTQ1lZGSNHjqRJkyaUlJQQCATYu3dvFYPS6/XSu3dvRISOHTuSmJjI9u3bgWOnasHxI2jN/3v37uXo0aNWWFFREaWlpdYshJl9MbM/Jo3f77dGlc3n888/j8Ph4NZbbwXgxhtvBI7NWDRt2pSZM2ficDgYOnQoAPfddx9wfHmVqTscc8TcbjfdunUDoHfv3sTExLB+/XoGDx4MwMCBA0lKSmLFihXAMYcQjs88FRcXW/mVlJQAcOTIEUs2j8fD7NmzcTqdNG/e3JLHpDty5AhxcXF0796dw4cP06BBA5KSkqy8Dh48aOVvDF2zZOeOO+6gbt26/OlPf6J27drs3LmTsrIyOnbsyIgRIyguLqZTp05ERUUxYMAAdu7cabWBKT8QCOD3+ykqKrLumXYw7RIMBiktLeXAgQMEAgFr+dPTTz9N3bp1GTp0KA0bNuTQoUMRX0J47bXXAnDPPffgcDisFw7u3r07RAaj23fffZfWrVvTsGFDsrOz2bVrF/n5+SQnJzNq1CgaNGjAokWLiI2NJTU1lYkTJ0bUvyk/vH9Fird3717Ky8urLMc7dOgQAGVlZcDx57am9jdxqntOioqKAKzTu8L1UlhYCFCjnl0uF0eOHKFZs2YMHjxYHQ9FUU47rjvuuOOOn1sIRVF+HOZYWONUDBgwgHr16hETE0Nubi7x8fGcc845tGvXjgsvvJC2bduSnJxMvXr16NevH5MnTyY1NZXY2FiGDBlCSkoKjRo1Iicnh2nTpnHuuefi8Xi44oorSEtLIzk5mbS0NC699FLGjh1LYmIi0dHR1K9fnzlz5pCdnU1iYiKjR4/mzjvvtGYdYmJiuOCCC8jNzQ1ZNuRwOMjPz8fr9ZKSksLQoUOZM2cO8fHx
5OXl0axZM6uOxmCOj4/nvPPOIy8vD7fbjdfrta6jo6Nxu93ExsZSUFBAVlYWDoeD+Ph4unTpYjkFjz/+OEVFRbzyyiukpaXRsmVLZs2aRe/evfH7/SQmJvK73/2OlJQUUlNT6dOnDw8//DD5+fkAVhkDBgygffv2IaPkPXv2xOv1kp6ezqBBgxg4cCB9+vShVq1a1K1bl/79+zNv3jwyMjLo2rUrbdu2tUbr7flFRUXh9Xq56KKLyMjIID09nYsuuog6deqQlZVFXl4eN910E6mpqXg8HjIzM+nXrx9169alX79+REVFUb9+fcaOHcuDDz6I2+2mT58+NG7cGDg+wt6iRQtatWpFXFwc7du356mnnqJ///5kZmbSq1cvnE4nF154IU2aNKFevXq0adOGq6++msGDB1t9z8yIOZ3OkLZ2uVxER0dTp04dBg8eTKNGjQDwer3069ePrl270qhRI7p160Z0dLR1xPPll19O3bp16dOnjzVLZI6KzczMtNr6vPPO48knn6RLly60a9eOnj17WjINGDCA3NxcGjZsSGJiIgkJCWRlZTFq1Cj+8pe/4PF4SEpK4tJLL6VOnTpkZGTQrVs3pk6dSmZmJi6XC6/XS0FBAU2aNLF0ZvpcQUEBmZmZREVFERcXFxKvVq1a9OjRg169eln7g7xeLxdeeCHNmze3+s/gwYNp2bIlLper2vYvKCggIyOD888/n7i4uCrPSX5+Ps2aNbP6RLhe2rdvT9euXUlNTY2o58TERAoKClizZg2PPfYYw4YNo3///tb7gHQZlqIopwOH6FCHovyqCV+rbWYM7Ia+WVIRyZiIFN+O/eV94dhf5Hayae3y1pR3+P2TWZMenl+k/AOBALm5uXzxxRds3Lgx5EWAJn51ckXSo/kKNWvuf+jelUhpItUDIu+LCY/7Q9vkRHo92TpFyudk2uNE+Zt8zWdN8SM9C9XVraZ+H67D6vqFeSHgD6lfdXn/kDTh1NQ/airH
zn333cfkyZPZsGED2dnZJyxTURTlp6BvQleUXyl2w9fv91tvqDajloFAwHrrudkMbpYe2Q05s+7cbFi1vxHZ5CciVTa3ulwua7OyWX5k34xtNrTayzUzIiZ/Y9TZ15y7XK6Qt7Hb6xmeF4S+vdu8aNAYTya93+9HRIiOjsbhcFRZFmT2qZj4Ri67PswLG039jYz2N6SH18f+1vBAIGDVwcQzctvzM3oLr4fJI/zt4EZfpizzhnF7W5j8IunUrkMT15yGFKn97e1rdBupj52oPcLjhMtkyrf3F6M7e3yjS/n/N8Db84+KiqrSjqaP2dsyvJ3t/Tq8X9jD3W73SdXPtK+JE95/Tqb9T+Y5qU4vNenZHL5gTgvLzs626qob0RVFOV3oDIii/AcQbsQb7Nc/5J49LPwz0v2a5Pox6X4M9hH0m2++mc2bN/PII4+Qnp4eYtSHyxHpuqZR9UjpzL0fW8cf2kYnq9Oa9B+erqb2/7HY01dXj/D4J6r3iepXU/oT5fdzcaLn5GT7gMnLfm3Pv6b/FUVRTiXqgCiK8h9DTUa4ovynEb580Mx8nK5BAEVRFIM6IIqi/EdgHy22G15qbCmKoijKmUX3gCiK8h+HOhuKoiiK8vOhMyCKoiiKoiiKopwx9EWEiqIoiqIoiqKcMdQBURRFURRFURTljKEOiKIoiqIoiqIoZwx1QBRFURRFURRFOWOoA6IoiqIoiqIoyhlDHRBFURRFURRFUc4Y6oAoiqIoiqIoinLGUAdEURRFURRFUZQzhjogiqIoiqIoiqKcMdQBURRFURRFURTljKEOiKIoiqIoiqIoZwx1QBRFURRFURRFOWOoA6IoiqIoiqIoyhlDHRBFURRFURRFUc4Y6oAoiqIoiqIoinLGUAdEURRFURRFUZQzhjogiqIoiqIoiqKcMdQBURRFURRFURTljKEOiKIoiqIoJ0RErM9I/yuKopws6oAoiqIoilIj4U5GMBhERHA4HKelHOPY
2K8VRTl7UAdEURRFUZQT4nA4CAaDOBwOXC4XDofjtDghhmAweFryVRTl50cdEEVRFEVRqsU4GcFgEKfTyf79+7n99tspKSmxnJBTXc6ZcnIURfl5UAdEURRFUZSIGOPf7mSMHj2aVatWER8ff1qcD6fTSUVFBRMmTGDPnj1WuKIoZw/un1sARVEURVF+edidC7/fT1RUFNOmTWPz5s1s2LDBiuN0/rSxTLuTYxyQUaNGsXv3bho0aACgMyCKcpahDoiiKIqiKCEY58PhcODz+YiKiuKVV17h0UcfZd26dQAEAgFcLtdPWiIVycmZNWsWn376KVu3bgWwnBJFUc4eHKJHSyiKoiiKYsM4FX6/H7fbzcaNG+ncuTNLliyhb9++llNid1R+TBkmrcnv7bffZsiQIXz++edkZ2db5es+EEU5u1AHRFEURVEUC2PsmxkOn89Hs2bNuO6667jppptOifNhL8c4Gbt27aJdu3bMnz+fgoKCU1aOoii/PNQBURRFURQFCN2PYfZ35OXlUb9+fRYtWoTf78flclnxf6rzYV9e1bJlS4YMGcL//M//qPOhKGc56oAoiqIoihJxSdTEiRN5//33Wbt2LXB8P8ZP3fcR7uQUFBQgIrz++uvWMbx6BK+inL3oJnTlF4/9R9G8fVdRlF8fDofjJxuvyunF7nwsXLiQBQsWsHHjRuD0bTq//fbb2bhxI19//XWIHNpPFOXsRR0Q5ReP+QE6Fcc9Kory86JGZSi/tAGWQCBAdHQ0q1ev5ve//z3vvPMOKSkpVFRU4Ha7CQQCp6Qcv9+Px+Ph1VdfZfbs2axduxaXy0VlZSUul+uUlfNz4HQ61YFSlBOgS7CUXzz2H+gNGzbw3XffhawNNvcidWUN13AN//nDzah6y5YtSU1NVcMsAr8knRw5coS2bdty++23M3r06NNWzqZNm8jLy+Pll1+mZ8+ep62cn4NfUnsqyi8RnQFRfhUEg0FcLhd//vOfeeGF
F0LuORwOvF4vpaWlGq7hGv4LDQd49NFHufbaa62lN0roAMuaNWsoLi4O2eQNx0bUA4FAFYP2VIeLCHFxcXzyySd06NCBNm3asHz58iozzz+lXDOTHQgEGDNmDOeddx61atXi/fffx+12n9H6no7wYDBIu3btSE5OVidEUWpAZ0CUXzw6A6LhGv7rDtcZkOqxf7/l5eWxYsWKkPsul4uEhAT2799/xsKdTieJiYkEAgEOHjx4Wso198yyq5+zvqe6Xq+99hoXXnihdbywoihVUQdE+dWgRoui/PrR5ziUX9IMiAk3e1GioqJOS/4igsvlwul0UlFR8bPX91SG6wyIopwc6oAov3h+aZs0fwnYj6n8tWM/ivM/hRPVORgMApx1OnE49BSs6lCdnF1oeypKzagDoiiniHBHyexbsY8qGsfhx56SYi/DXP/UfKr7/0wQLvfJ1MNe5/CTchwOh3VMqLn+pVBdW9mvI92DqvUI14HT6bQM+0jxlV8uOsBy9qGnYCnKiVEHRFFOEebHxv5mXxMeabQ73PC0U124ubdnzx4SExOJiYmJ6EDYr0+0Tj+SMxMeJ9L1D40bqQxTl/3795OVlRVSn5PJJxLV1TmSrIwiYAAAEvdJREFUfCb+Dwn7IfWu7rqiooL9+/fToEGDKnkHg0E2bdpEbGwsGRkZVeSx5xm+9ENnF5TTTXUDACbcDLrU5AzX9F2jfVdR/jM4u+b3FeVnwvxwmtHo1atXM27cOL777jtrxuP9999n2LBh3HLLLZSXl1s/uOaH3MyQ+P3+iOGVlZU4HA6++OILGjZsyPXXXw+Az+cjEAhYf8Fg0DrRxhi0fr/fyscYDaYMv99PWVmZlRaw4htM3iZdpHJMHBGx7tmN93DDxe/3A3Dbbbdx7rnnsmnTJgArrfk05Zq0ZhZp586d3HjjjVx33XWMHz+e3//+9zzyyCOWLEaP4XUOr1t19+31q6ne4eGmXqbufr/ful9RUQHAlClT
aNiwIR9//LEVx6Q7fPgwHTp04Morr6wio70Mh8PBqlWrGDduHHv37sXpdFq6UZTThd1RcLvd1l9UVBRut9v6zgmfyTN/9jCTX6TBkPA0OlaqKGcXejyDopwizMZKgKFDh1K7dm1SU1Otkenp06fzwQcfANCrVy/69+9PIBCwfsAN9g2ZkcLT09OZPHkyffr0ASA6OrpaeczMS3WzLw6Hg/z8fPLz87n99tut++aIVCO7kSM8P/Npr7ud8FF5u+FisBv/cHwZVXg+Rl7jPHz33Xc8+OCDANSpU4eDBw9y8OBB/vjHP1qn60SSxchs8rTHCwaDVU6tMU5MdfUGqqSxOziR2rCgoIDo6GiaNWsGEHIkbZ06daioqMDn81l5RdItQNOmTXn22WfZt28fL7/8cojBpo6Icjowz7DP5+Oee+5h7969AMTHx9OlSxcKCgpwu93WM2Mca7uD7HK5rP/Lysosx8WkMwM5pjz7rIr2a0U5O1AHRFFOAQ6HwzpyccmSJWzbto2PPvoIOPbDu337dj744ANGjhzJ0qVLWbhwIf3797cM6vnz57N06VIAunbtytixY0lOTmbu3LksW7YMgC5dujB+/HjgmFFrjq586aWX2LRpEwUFBTzwwAMcPXqUoUOH8tvf/haAb775hnnz5rFmzRrcbjeDBg3iqquuAmDOnDl89NFH7N+/n8LCQkaPHk1GRga33XYbAwcOpKCggIqKCu68806SkpKYNGkSa9as4fnnn2fKlCksWLCArVu38sQTT7By5UqeffZZtm/fTlJSEqNHj6Zv376WoWE3Hp566imWLVtG7969qxjzPp+PuXPn8sEHH5CQkMCVV15JXl5elVHVmJgYAMaMGcOTTz5JZWWllde6deuYP38+GzduJC4ujiFDhjB06FA+/fRTnnrqKUaOHEleXh4Aixcv5n//93+ZOnUqTZo04dVXX+Xll1/G7/czcOBARowYgcPhYPXq1bz44otMnjyZBQsW
sG3bNh555BFcLhdPP/00r732GjExMfz+97+nf//+Vn1fffVV/va3v1FWVkbHjh2ZMmWKZVSVl5cDUFhYyMyZM/nuu+8YMmQIderUCXEgt2zZwuOPP84333xDmzZtGD9+PPXq1SMhIYF7772X66+/nk2bNtGiRYsqSwAV5XTg9/uZOXMmR44coXbt2hw+fBiAzp078/rrr9OgQYMqDr3pl5WVlURHR7N8+XJGjhzJt99+azklkQZM4Mfvd1MU5ReKKIrykwkGg+Lz+URE5MorrxSv1yvl5eUSCARERGTWrFkCyIoVK+SSSy6RmJgYOXDggIiIPPTQQwJIjx49pHfv3uL1emX//v3y5JNPhoTHxcVJUVGRfPHFFwLI0KFDRURk9OjRAkj9+vWladOmAgggy5cvFxGRN998U2JiYmTw4MHStm1bAeThhx+WsrIyadmypQBSr149ycjIkLfeektWrVolgIwZM0ZERA4dOiSANGrUSEREnn32WQGkdevWAkjz5s1FROSOO+6QuLg4ueyyyyQ5OVkA+eSTT0RExO/3W/p5+OGHBZCYmBjJysqS2NhYAeTzzz8XEZHLLrtMAGnSpIl4vV4B5OWXX7by8fv9IiKWnFdffbXVBkbfc+fOlZiYGLn00kulSZMmAsiyZcvk22+/FUDy8/NFRKSiokJSUlIEkMrKSnn88ccFkMTERGnUqJEAMmHCBBERmT9/fki9W7RoISIiN998swDyX//1X9KpUycBZPHixSHtXrt2bUlPT7d0eM011wgg77//voiIdO3aVQBp3LixNGjQQADp3r27iIhs2bJF6tSpI4C0atVKAGnWrJkUFRWJiMiGDRsEkFmzZomISGVlpQSDwR/fmRWlBkzfKi8vl9TUVMnIyJDS0lKprKyUO++8UwC56KKLREQkEAjIokWLZPjw4dKvXz+59tpr5euvvxYRkR07dkiXLl0EkGHDhsmdd94pIiLffPON/OlPf5KBAwdKQUGB
zJ071ypT+7WinD2oA6IopwD7D2O7du0s49GEd+rUSZKTk0VE5OWXXxZAFi1aJCIif/zjHwWQ2267TXw+n2Vg33DDDSHhJq/PP/9cABk7dqyIiEyaNMmKJ3LcoZk+fbolU2lpqfj9fvn0008tY1lEZPny5QLIlClTrLgff/yxADJx4kQRETl8+LDUqVNH2rVrJyIif/vb3wSQ9PR0Wb16tZSVlVlpjxw5IiLHnYx7771XRI4Z+nb9ALJhwwYREcnLyxNANm7caBnTF1xwgYgcM0YAadu2rZXe6Oef//ynABIbGyvp6eni8XjknnvuseKVlJRIMBiUV155RQD5wx/+ICIiAwYMEEAOHTpk6ePWW28VEZGkpCRJTEyU4uJiERHJyckRQA4cOCBLly616r1q1SoJBAJSWFgogOTk5Ijf75c9e/ZIVFSU5eCkpKRIrVq1pLCwUERE9u/fLyIiN910kwCyatUqWbt2bUibrFmzRgDJzc0Nad8FCxaIyHGn5r777rPaNjY2VkaOHGnpRw015XRhd0Dq1asnaWlp1uCCiFiDGrt37xYRkb59+0paWppceuml1kCJz+eTBx98UACJioqSjIwMGThwoIiIvPvuu1UGTB544AERkZDvQUVRft3oPL2inEKCwSDFxcUkJycDx5ZmrV27llWrVhEbG8ukSZN46aWXAHj55ZcBGD9+PK1ateKuu+4iLi6OmTNnAjBu3LiQ8BkzZgDHlyqZvRNmKVZ+fj4AaWlpANba6wULFtC+fXsSEhIYMmQIUPX9EiZueF3s98M3V1999dV06NABj8dDMBjk9ttvJysri4SEBKZPn14lD4CysjJ27NhB8+bNyczMBLCWQnk8HrZt2wZAjx49AGjUqBEdOnRgw4YNHDlyJKT+Ro7U1FT69+/PBRdcQFZWFgAPPPAA2dnZJCQkWJv1jSzDhw8H4M033+Stt94CYNiwYRw4cID9+/eTk5NDUlJSiBxbtmzB4/FY9e7Y
saO1tA5g/fr1JCQk0Lx5c3w+H+Xl5ezatYt9+/bRtWtXGjRogN/vp3bt2iGyuFwudu/eDUDPnj0BaNWqFampqZa+jU66du0KQPfu3QHYsGEDcGz/SEpKCsXFxSF6UZQzgfz/IQzmuWzevDkAe/bsAeDdd9/lX//6Fy+++CLDhw/n+++/57PPPuOGG26gT58++Hw+Vq9ezbJlywgGg/Tt25fi4mKWLFnCnDlzAHj//feBs++9OIryn4zuAVGUU4jT6SQmJsZa2w+wZMkS4NjpRg8++CBRUVE4nU7ee+89CgsLyc7OZv369SxdupTJkyczdepUsrOzKSgoqBKek5NDamoqEPndEHDcMfF4PIgIo0aNol69emzevJlAIEBaWpoV1xjCZj8FHHdGTD4+n4+SkpIqG63NxmiHw8Fbb73FXXfdxYABA3jjjTdYtGgRl19+eRWDwel0Eh8fz8GDB617Bw8etMpLTEwEYN++fcCxvS7FxcXEx8dXW36/fv149NFHrfA1a9YwceJE2rdvz5YtW/jyyy/p3r27FX/AgAHUrl2bZ555hm3bttGpUyeys7M5cOAAbreboqIiK69///vfANSqVYsDBw6ElAuQkJAAHHMO5syZQ3l5OdHR0Xi9XjweD06n08ojXH7TZl6vF8ByINxuNyUlJVb7mjLMGvtDhw4Bxzb9wrE2LC0ttdpQdI28cobx+/3WYRj79+8HoG7duhw6dIhrrrmGN998k5iYGOtgBTNoYr6HzPel0+lk4cKF3HHHHRQWFlrfB+a7Qvu1opw96HCCopwijDGflZXFxo0bgWM/sAsXLgSOGcZHjx6lrKyMKVOmcPDgQVasWMFrr73GvffeS5s2baxRcJfLxZIlS6qEHz161CrPOAimXPNjbv80x74mJiZSWFjI3XffDRz/IY+LiwNg+fLlzJ07ly+//NJ698RLL73ECy+8wOjRo0PqGe7oAJSUlADHTnD66quvePjhh0NkM0fjejwe
evbsSVFREZMmTeKJJ55g3rx5wDEDu3v37jRs2JDZs2fzxBNPcOONN7Jz505++9vfEhsbW+VoX7tOysrKQj4TExPZtGkT9913X4j8ZmP7O++8w9atW60N+YmJiVx66aWsWbOGadOmMXv2bJ5//nkyMzPJzs6mtLQ0pN4+n49zzz2XDh068MEHH7BixQoqKip44403WL9+Peeccw65ubmsWbOGSZMm8cwzz1i6NHUwG9OdTidz5sxh3rx5jBs3jkOHDlmOziWXXALAjTfeyOLFi5k6dSoAgwYNAuDAgQMUFxdbM0rhs06KcrowJ1fFxcXhcDhYuXIlf//732natCkZGRnMmDGDRYsWcfPNN1NUVMQVV1wBHD/1zTzDxskuKipi2LBhHD58mM2bN/OPf/wjJJ6iKGcP6oAoyinCGKb5+fns2rWL77//nvXr17N582Z++9vf0qRJE+uH96KLLgKOLS3YtGkTt9xyC82aNePpp5/miiuuYNCgQaxfv55bbrmF5s2b8/TTTzNs2DAGDRpkGdzGeTAj6CZv8+lwOIiJieH6669n48aN5OTk4PP5rON74diJNRdffDGffvopY8aM4R//+Afp6encdNNNfP/99wwfPhyn02md2GXPPzY21qr3gAED6NOnDwsXLqRbt26cf/75NGzYMEQ/Jv3dd99NmzZtmD17No899hhXXnklXq/XMjKWLFlC69atGTduHA8++CAXX3yxtfzMPgJqDHQzExAVFYWIkJubyxVXXMEHH3xAp06dyMjIoE2bNiFGzIgRI/B4PKSlpVkGPsBDDz3E4MGDmT59OhMmTKBNmzYsXrwYOD4Ka+ptDP3FixfTvXt3rr76anJzcy3dATzxxBN06NCBWbNmMXr0aD7//HPg+IyT3++nVq1aPP744wQCAa6++mpKSkro0aOHpedBgwYxc+ZMvvzySy677DK2bNnC7Nmz6du3LwArV660+p3RkY4UK6cb+f9T2goLC5k2bRpj
xoyhW7duANYyUjNrGBcXxzvvvMOiRYuA4zMg5lm6//77Wbx4sTUbGj5goijK2Ye+CV1RThHmCMm9e/dSv359rr/+embPns2///1vateuTXR0dMiMQElJCcFgkISEBIqLiykqKiIpKYl69epZeUYK9/v9lJaWEh0dTWxsLOXl5VRUVBAXF4fb7cbn81lLcoyhu3PnThwOB40bNyYQCFBWVmYZ7gDbt2/H6/VSr149y3gtLCzE7/eTlpZGMBikpKSE2rVrW/nHxsZa+z/Muz62bdtGYmIiycnJHD161FqSZpYFmbg+n4+dO3eSkZGBy+Xi0KFDxMfHWy9tNDJHR0dTv379EP2avAKBACUlJZYewveGmDrVr1+fyspK/H6/5eg4HA6OHDmC0+kkLi4u5MhbgO+//56KigrS09OB4y8otNfbLpPR15EjR2jUqBFxcXEEAgHLSdq5cyfBYJC0tDScTmeVNoNjy73KysqsNjp69Ci1atWy2qikpISioiLOOecc4uPjrbJ/85vfsHv3br755htLVrseFOVUYp6fsrIycnJy2LRpE36/n/j4ePLy8pgyZQq/+c1vAFi7di0XXnghO3bsoEmTJvzud79jzpw5LF68mPz8fJYtW2bNeGRlZbF582amTp1qzVqOHj2aXbt24fF4WLZsmfZtRTmLUAdEUU4BdqPY5XLx6KOP8uijj/LRRx+RkpISEudEP6L2l+6Fh0PoRszw9f6Rru1l2Q3m6uQIN8bD00QqL/zdE3bj217vSHFrShte7/C8ItU7vF41yRKeNpKOw+WJlCa8vcL7Q3VpT0bfJsyej8/nIyoqis8++4whQ4awYMECzj//fKs83QeinC7sz1BJSQkiQlRUFC6Xy9oHYpZKOp1Ojh49yu7du0lLSyMmJobDhw/j9XqtlwuaJYQNGza0ZnOrGzBRB0RRzh7UAVGUU4T9UTIjhE6n09oMbjd8wx+78LQmTk3hP/Ta5GHPz5Rtv3+iNJHy
P5k01eVhxx43kkwnyjM8/5rqHKnc6tJWV+9I5dllPpF+f0gbmU+7TDX1MUU5XVTXx4LBoPWSTXNd08CE3fE2YVD9gIn2a0U5e/g/rmENn8sizQMAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>bbea77d8-8b66-418e-a67d-c5baf0b8e7b9</rd:ReportID>
</Report>
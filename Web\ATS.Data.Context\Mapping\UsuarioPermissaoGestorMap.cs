using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioPermissaoGestorMap : EntityTypeConfiguration<UsuarioPermissaoGestor>
    {

        public UsuarioPermissaoGestorMap()
        {
            ToTable("USUARIO_PERMISSAO_GESTOR");

            HasKey(t => new {t.IdUsuario,t.IdBloqueioGestorTipo});

            Property(u => u.DesbloquearEmpresa).IsRequired();

            Property(u => u.DesbloquearFilial).IsRequired();
            
            HasRequired(u => u.Usuario)
                .WithMany()
                .HasForeignKey(u => u.IdUsuario);
            
            HasRequired(u => u.BloqueioGestorTipo)
                .WithMany()
                .HasF<PERSON>ign<PERSON>ey(u => u.IdBloqueioGestorTipo);

        }
    }
}
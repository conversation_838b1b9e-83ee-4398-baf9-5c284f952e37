﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.EntityFramework
{
    public class RotaModeloRepository : Repository<RotaModelo>, IRotaModeloRepository
    {
        public RotaModeloRepository(AtsContext context) : base(context)
        {
        }
    }
}
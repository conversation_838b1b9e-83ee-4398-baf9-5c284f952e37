﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ConjuntoEmpresaMap : EntityTypeConfiguration<ConjuntoEmpresa>
    {
        public ConjuntoEmpresaMap()
        {
            ToTable("CONJUNTO_EMPRESA");

            HasKey(t => new {t.IdConjuntoEmpresa});
            
            Property(t => t.IdConjuntoEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.IdConjunto)
                .IsRequired();

            HasRequired(x => x.Empresa)
                .WithMany(x => x.ConjuntosEmpresa)
                .HasForeignKey(x => x.IdEmpresa);

            HasRequired(x => x.Veiculo)
               .WithMany(x => x.ConjuntosEmpresa)
               .HasForeignKey(x => x.IdVeiculo);

            HasOptional(x => x.Motorista)
               .WithMany(x => x.ConjuntosEmpresa)
               .HasForeignKey(x => x.IdMotorista);

            HasRequired(x => x.Conjunto)
                .WithMany(x => x.ConjuntosEmpresa)
                .HasForeignKey(x => x.IdConjunto);

            HasOptional(x => x.Usuario)
               .WithMany(x => x.ConjuntosEmpresa)
               .HasForeignKey(x => x.IdUsuario);

        }
    }
}
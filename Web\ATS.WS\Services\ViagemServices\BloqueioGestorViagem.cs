using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Net.Mail;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using MassTransit;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ValidationResult = ATS.Domain.Validation.ValidationResult;

namespace ATS.WS.Services.ViagemServices
{
    public class BloqueioGestorViagem
    {
        private readonly IViagemApp _viagemApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IEmailService _emailService;
        private readonly IUsuarioRepository _usuarioRepository;
        
        public BloqueioGestorViagem(IViagemApp viagemApp,CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IEmailService emailService, IUsuarioRepository usuarioRepository)
        {
            _viagemApp = viagemApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _emailService = emailService;
            _usuarioRepository = usuarioRepository;
        }

        public Retorno<BloqueioGestorValorResponseDTO> ValidarBloqueioGestor(ViagemIntegrarRequestModel @params, Viagem viagem,EBloqueioOrigemTipo origem)
        {
            decimal valorTotalFrete = 0;
            
            foreach (var item in @params.ViagemEventos)
            {
                if (!item.HabilitarPagamentoCartao)
                    continue;
                
                if (item.Status != EStatusViagemEvento.Baixado)
                    continue;
                
                if (item.IdViagemEvento.HasValue)
                {
                    if (viagem.ViagemEventos.Any(x => x.IdViagemEvento == item.IdViagemEvento && x.Status == item.Status))
                        continue;
                }

                valorTotalFrete += item.ValorPagamento ?? 0;
            }
            
            var listValidacao = new List<EBloqueioGestorTipo>();
            var msgErroProcesso = string.Empty;
            decimal? valorPedagio = null;
            
            if (valorTotalFrete > 0)
            {
                listValidacao.Add(EBloqueioGestorTipo.ValorLimiteFreteDiario);
                listValidacao.Add(EBloqueioGestorTipo.ValorMaximoFreteUnitario);
            }

            if (@params.Pedagio != null && @params.Pedagio.Fornecedor != FornecedorEnum.Desabilitado)
            {
                listValidacao.Add(EBloqueioGestorTipo.ValorLimitePedagioDiario);
                listValidacao.Add(EBloqueioGestorTipo.ValorMaximoPedagioUnitario);
                
                if (!@params.Pedagio.ValorPedagio.HasValue && !@params.ValorPedagio.HasValue)
                {
                    var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, @params.CNPJEmpresa, CartoesService.AuditDocIntegracao, null);

                    var requestPedagio = new ConsultaHistoricoRotaRequest
                    {
                        ConsultaCustoHistoricoId = @params.Pedagio.IdentificadorHistorico,
                        RecalcularValorParaEixos = @params.Pedagio.QtdEixos
                    };

                    var responsePedagio = cartoesApp.ConsultaHistoricoRota(requestPedagio);

                    valorPedagio = responsePedagio.CustoTotal ?? 0;
                }
                else
                {
                    valorPedagio = @params.Pedagio.ValorPedagio ?? @params.ValorPedagio ?? 0;
                }
            }

            var validacaoFiltroGestor = new ValidationResult();
            
            if (listValidacao.Any())
            {
                if (!valorPedagio.HasValue)
                {
                    listValidacao.Remove(EBloqueioGestorTipo.ValorLimitePedagioDiario);
                    listValidacao.Remove(EBloqueioGestorTipo.ValorMaximoPedagioUnitario);
                }
                
                validacaoFiltroGestor = _viagemApp.BloqueioProcessoViagem(listValidacao, viagem, valorPedagio ?? 0,origem,valorTotalFrete);

                if (!validacaoFiltroGestor.IsValid)
                    foreach (var str in validacaoFiltroGestor.Errors)
                        msgErroProcesso += str.Message + Environment.NewLine;
            }
          
            
            var response = new Retorno<BloqueioGestorValorResponseDTO>
            {
                Sucesso = string.IsNullOrWhiteSpace(msgErroProcesso),
                Mensagem = msgErroProcesso.NullIfEmpty(),
                Objeto = new BloqueioGestorValorResponseDTO()
                {
                    Status = string.IsNullOrWhiteSpace(msgErroProcesso) ? EBloqueioGestorStatus.Liberado : EBloqueioGestorStatus.Pendente,
                    MensagemPedagio = string.Join(" ", validacaoFiltroGestor.GetFaults().Where(x => x.Code == "Pedagio").Select(x => x.Message)),
                    MensagemFrete = string.Join(" ", validacaoFiltroGestor.GetFaults().Where(x => x.Code == "Frete").Select(x => x.Message))
                }
            };

            if (!string.IsNullOrWhiteSpace(msgErroProcesso) && origem == EBloqueioOrigemTipo.API)
                EnviarEmailNotificacaoGestores(viagem.IdEmpresa, msgErroProcesso,viagem,valorTotalFrete, @params.Pedagio?.ValorPedagio ?? @params.ValorPedagio ?? 0);

            return response;
        }

        public void EnviarEmailNotificacaoGestores(int idEmpresa,string mensagem,Viagem viagem,decimal? valorTotalFrete,decimal? valorPedagio)
        {
            var lDestinatarios = _usuarioRepository
                .GetAll().Include(x => x.Contatos)
                .Where(x => x.IdEmpresa == viagem.IdEmpresa && x.RecebeEmailGestao && x.Contatos.Any())
                .Select(x => x.Contatos.FirstOrDefault())
                .Select(x => x.Email)
                .ToList();
            
            var emailModel = new EmailModel
            {
                Assunto = "Pagamento Pendente de Aprovação do Gestor",
                Prioridade = MailPriority.High,
                Destinatarios = lDestinatarios
            };
            
            using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\comunicado-pagamento-frete-pendente.html"))
            {
                var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                logoEmail.ContentId = Guid.NewGuid().ToString();

                var html = ms.ReadToEnd();
                html = html.Replace("{0}", logoEmail.ContentId);
                html = html.Replace("{Header}", mensagem);
                html = html.Replace("{IdViagem}", viagem.IdViagem.ToString());
                html = html.Replace("{Documento}", viagem.DocumentoCliente);
                html = html.Replace("{Ciot}", viagem.DeclaracaoCiot?.Ciot ?? "Não habilitado");
                
                if(mensagem.Contains("pedágio"))
                    html = html.Replace("{Valor}", $"R$ {valorPedagio}");
                else
                    html = html.Replace("{Valor}", $"R$ {valorTotalFrete}");

                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                view.LinkedResources.Add(logoEmail);
                emailModel.AlternateView = view;
            }
            
            _emailService.EnviarEmail(emailModel);
        }
    }
}
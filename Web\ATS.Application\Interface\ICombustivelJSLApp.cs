using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ICombustivelJSLApp: IAppBase<CombustivelJSL>
    {
        IQueryable<CombustivelJSL> GetQuery(int idcombustivel);
        bool SincronizaATS(int idcombustivel);
        bool CombustivelExistenteEstabelecimento(int idCombustivelJSL, int idestabelecimento);
        ValidationResult Integrar(int idcombustivel, int idproduto, int idestabelecimento);
        IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByCombustivel(int idcombustivel);
        IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByEstabelecimento(int idestabelecimento);
    }
}
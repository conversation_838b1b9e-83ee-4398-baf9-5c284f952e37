﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.TagExtratta.ConsultaSituacaoTags
{
    public class RelatorioConsultaSituacaoTags
    {
        public byte[] GetReport(List<RelatorioConsultaSituacaoTagsDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var dataSources = new Tuple<object, string>(listaDados, "DtsConsultaSituacaoTags");

            var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                "ATS.CrossCutting.Reports.TagExtratta.ConsultaSituacaoTags.RelatorioConsultaSituacaoTags.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Data.Repository.External.SistemaInfo.Cartao.DTO
{
    public class ConsultarContasBancariasResponse
    {
        public ConsultarContasBancariasResponseStatus Status { get; set; }
        public string Mensagem { get; set; }
        
        public List<PessoaContaBancariaResponse> ContasBancarias;
    }

    public enum ConsultarContasBancariasResponseStatus
    {
        Falha = 0,
        Sucesso = 1
    }
}
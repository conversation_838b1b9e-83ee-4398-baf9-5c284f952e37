﻿using ATS.Domain.Enum;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ViagemRegra
    {
        /// <summary>
        /// Codigo da regra da viagem
        /// </summary>
        public int IdViagemRegra { get; set; }

        /// <summary>
        /// Código da viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Valor da taxa de antecipação relacionada a regra da viagem
        /// </summary>
        public decimal? TaxaAntecipacao { get; set; }

        /// <summary>
        /// Valor da tolerância de peso relacionada a regra da viagem
        /// </summary>
        public decimal? ToleranciaPeso { get; set; }

        /// <summary>
        /// Valor da tarifa por tonelada relacionada a regra da viagem
        /// </summary>
        public decimal? TarifaTonelada { get; set; }

        /// <summary>
        /// Valor total do frete relacionada a regra da viagem
        /// </summary>
        public decimal? TotalFrete { get; set; }

        /// <summary>
        /// Tipo de quebra de mercadoria relacionada a regra da viagem
        /// </summary>
        public ETipoQuebraMercadoria TipoQuebraMercadoria { get; set; } = 0;

        /// <summary>
        /// Informa se o frete é lotação
        /// </summary>
        public bool FreteLotacao { get; set; } = false;

        /// <summary>
        /// Relacionamento com a viagem
        /// </summary>
        public virtual Viagem Viagem { get; set; }

        /// <summary>
        /// Unidade de medida (padrão KG)
        /// </summary>
        public string UnidadeMedida { get; set; } = "KG";
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;
using System.ComponentModel.DataAnnotations.Schema;


namespace ATS.Data.Context.Mapping
{
    public class CredenciamentoMotivoMap : EntityTypeConfiguration<CredenciamentoMotivo>
    {
        public CredenciamentoMotivoMap()
        {
            ToTable("CREDENCIAMENTO_MOTIVO");

            HasKey(x => new { x.IdCredenciamento, x.IdCredenciamentoMotivo });

            Property(x => x.IdCredenciamentoMotivo)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.DataRejeicao)
                .HasColumnType("datetime2");

            HasRequired(x => x.Credenciamento)
                .WithMany(x => x.CredenciamentoMotivos)
                .HasForeignKey(x => x.IdCredenciamento);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Pedagio;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.Service
{
    public class RotasCacheService : IRotasCacheService
    {
        private readonly PedagioExternalRepository _pedagioRepository;

        public RotasCacheService(string documentoUsuarioAudit,string nomeUsuarioAudit)
        {
            _pedagioRepository = new PedagioExternalRepository(SistemaInfoConsts.TokenAdministradora,documentoUsuarioAudit,nomeUsuarioAudit);
        }

        public DataModel<object> ConsultarGrid(DateTime dataInicio, DateTime dataFim, int take, int page, OrderFiltersPedagio order, List<QueryFiltersPedagio> filters)
        {
            var request = new GridCustoCacheRequest()
            {
                DataInicio = dataInicio,
                DataFim = dataFim,
                Take = take,
                Page = page,
                Order = order,
                Filters = filters
            };
            
            var response = _pedagioRepository.ConsultarGridCache(request);
            
            return new DataModel<object>
            {
                items = response.Items,
                totalItems = response.TotalItems ?? 0
            };
        }

        public void DeletarRotaCache(Guid guidRota)
        {
            _pedagioRepository.DeletarRotaCache(guidRota);
        }
    }
}

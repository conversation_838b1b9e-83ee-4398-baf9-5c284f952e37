using System;
using System.Web.UI.WebControls;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using TrackerEnabledDbContext.Common.Extensions;

namespace ATS.Domain.Service
{
    public class CheckinResumoService : ServiceBase, ICheckinResumoService
    {
        public void AplicarRegrasConsultaResumoPaginado(int? empresaId, int? itensPorPagina, int? pagina, DateTime? dataInicio, DateTime? dataFim)
        {
            if (!empresaId.HasValue)
                throw new Exception("O código da empresa não foi informado.");
            
            if (empresaId.Value <= 0)
                throw new Exception("O código da empresa não pode ser menor ou igual a 0.");
            
            if (!itensPorPagina.HasValue)
                throw new Exception("O número de itens por página não foi informado.");
            
            if (itensPorPagina.Value <= 0)
                throw new Exception("O número de itens por página não pode ser menor ou igual a 0.");
            
            if (!pagina.HasValue)
                throw new Exception("O número da página não foi informado.");
            
            if (pagina.Value <= 0)
                throw new Exception("O número da página não pode ser menor ou igual a 0.");
            
            if (dataInicio.HasValue && !dataFim.HasValue)
                throw new Exception("Ao informar a data de inicio, a data fim deve obrigatóriamente ser informada.");
            
            if (!dataInicio.HasValue && dataFim.HasValue)
                throw new Exception("Ao informar a data de fim, a data inicio deve obrigatóriamente ser informada.");

            if (dataInicio.HasValue && dataFim.HasValue)
            {
                if (dataInicio.Value.Equals(DateTime.MinValue))
                    throw new Exception("A data de inicio deve ser uma data válida.");
            
                if (dataFim.Equals(DateTime.MinValue))
                    throw new Exception("A data de fim deve ser uma data válida.");
                
                if (dataInicio > dataFim)
                    throw new Exception("A data de inicio não pode ser maior que a data de fim.");
            }
        }
    }
}
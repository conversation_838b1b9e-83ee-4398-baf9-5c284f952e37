using System.Collections.Generic;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IResgateCartaoAtendimentoService : IBaseService<IResgateCartaoAtendimentoRepository>
    {
        ValidationResult Add(ResgateCartaoAtendimento resgatarCartao);
        ValidationResult Update(ResgateCartaoAtendimento resgatarCartao);
        object ConsultarGrid(ConsultarResgateValorDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult Alterar(ResgateCartaoAtendimento resgatar, string motivoEstorno, int usuario);
        ResgateCartaoAtendimento GetResgate(int idResgate);
        
    }
}
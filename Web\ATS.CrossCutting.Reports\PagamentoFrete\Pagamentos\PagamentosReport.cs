﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos
{
    public class PagamentosReport
    {
        public byte[] Relatorio(List<PagamentosModel> pagamentosModel, PagamentosHeaderModel pagamentosHeaderModel, int tipoArquivo, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var pathLogo = ReportUtils.CreateLogo(logo);

                // A classe ReportParameter espera por 3 argumentos: o nome do parâmetro (string), o valor do parâmetro (string), e se esse parâmetro vai ser visível (true), por isso passamos um Tupple com esses três tipos, para se tornar genérico a passagem dos parâmetros
                var reportParametros = new Tuple<string, string, bool>[1];
                reportParametros[0] = new Tuple<string, string, bool>("Logo", "file:///" + pathLogo, true);

                var parametros = new ReportParameter[reportParametros.Length];

                if (parametros.Length > 0)
                    for (var i = 0; i < reportParametros.Length; i++)
                    {
                        parametros[i] = new ReportParameter(reportParametros[i].Item1, reportParametros[i].Item2,
                            reportParametros[i].Item3);
                    }
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = pagamentosModel,
                    Name = "pagamentosModel"
                });
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = new List<PagamentosHeaderModel> { pagamentosHeaderModel },
                    Name = "pagamentosHeaderModel"
                });

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos.Pagamentos.rdlc";

                if (parametros.Length > 0)
                    localReport.SetParameters(parametros);

                localReport.Refresh();
                return localReport.Render("PDF");

            }
            catch (Exception)
            {
                return new byte[0];
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }

        /// <summary>
        /// Retorna um relatorio renderizado
        /// </summary>
        /// <param name="pagamentosModel">Dados para o relatório</param>
        /// <param name="pagamentosExcel"></param>
        /// <param name="pagamentosHeaderModel"></param>
        /// <param name="tipoArquivo">Tipo de renderização:</param>
        /// <param name="pagamentosHeaderModelExcel"></param>
        /// <param name="headerParametro"></param>
        /// <para>1 = PDF, 2 = Excel</para>
        /// <returns></returns>
        public byte[] RelatorioPagamentosExcel(List<PagamentosExcel> pagamentosExcel, List<Tuple<string, string>> headerParametro)
        {
            var localReport = new LocalReport();

            try
            {
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = pagamentosExcel,
                    Name = "pagamentosModelExcel"
                });

                var parametros = new ReportParameter[headerParametro.Count];

                if (parametros.Length > 0)
                {
                    for (var i = 0; i < headerParametro.Count; i++)
                    {
                        parametros[i] = new ReportParameter(headerParametro[i].Item1, headerParametro[i].Item2);
                    }
                }

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos.PagamentosDetalhado.rdlc";

                if (parametros.Length > 0)
                    localReport.SetParameters(parametros);

                localReport.Refresh();

                return localReport.Render(ConstantesUtils.FormatoExcelOpenXml);

            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }

        /// <summary>
        /// Retorna um relatorio renderizado
        /// </summary>
        /// <param name="pagamentosModel">Dados para o relatório</param>
        /// <param name="pagamentosHeaderModel"></param>
        /// <param name="tipoArquivo">Tipo de renderização:</param>
        /// <para>1 = PDF, 2 = Excel</para>
        /// <returns></returns>
        public byte[] RelatorioPagamentoPdf(List<PagamentosModel> pagamentosModel, PagamentosHeaderModel pagamentosHeaderModel)
        {
            var localReport = new LocalReport();

            try
            {
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = pagamentosModel,
                    Name = "pagamentosModel"
                });
                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = new List<PagamentosHeaderModel> { pagamentosHeaderModel },
                    Name = "pagamentosHeaderModel"
                });

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos.Pagamentos.rdlc";
                localReport.Refresh();

                return localReport.Render("Pdf");

            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}
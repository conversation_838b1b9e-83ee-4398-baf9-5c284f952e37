﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using System.Linq.Expressions;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Models.Proprietarios;

namespace ATS.Domain.Interface.Service
{
    public interface IProprietarioService : IBaseService<IProprietarioRepository>
    {
        IProprietarioRepository Repository { get; }
        
        Proprietario Get(int id);
        Proprietario GetAllChilds(int id);
        ValidationResult Add(Proprietario proprietario);
        ValidationResult Update(Proprietario proprietario);
        bool Any(string cnpjCpf, int idEmpresa);
        IQueryable<Proprietario> Consultar(string razaoSocial, int idEmpresa, int? idUsuarioLogOn, bool? onlyAtivo);
        int? GetIdProprietario(int idEmpresa, string cpfCnpj);
        IQueryable<Proprietario> GetAllByIdEmpresa(int idEmpresa);
        string GetCpfCnpj(int idProprietario);
        //ValidationResult HasPermissaoVisualizar(int id, int idUsuarioLogOn);
        //ValidationResult HasPermissaoEditar(int id, int idUsuarioLogOn);
        decimal GetPorcentagemTransferenciaMotorista(int idProprietario, int idEmpresa);
        ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario, string cnpjEmpresa, int? idEmpresa = null);
        int? GetIdByCpfCnpjWithEmpresa(string cpfCnpj, int? idEmpresa);
        ValidationResult AlterarStatus(int idProprietario);
        object ConsultarGridProprietarios(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        byte[] GerarRelatorioGridProprietarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao);
        ProprietarioConsultaDetalheViagemResponse ConsultaDetalheParaViagem(ProprietarioConsultaDetalheViagemRequest request);
        ValidationResult AtualizarRntrc(ProprietarioAtualizarRNTRCRequest request);
        AtualizaBaseEquiparadoTacResponse AtualizarBaseEquiparadoTac(string cnpjcpf = null);
        AtualizaBaseEquiparadoTacResponse AtualizarCadastroServicoCartao(List<string> cnpjcpf);
        ConsultaSituacaoTransportadorResponse ConsultarSituacaoAntt(string cpfCnpj, string cnpjEmpresa);
        ProprietarioAnttDto GetDadosProprietarioAntt(string cnpjCpf, string cnpjEmpresa);
        void AtualizaEquiparadoTac(int proprietarioId, bool equiparadoTac);
        IQueryable<Proprietario> Find(Expression<Func<Proprietario, bool>> predicate, bool @readonly = false);
        Proprietario GetByCpfCnpj(string cpfCnpj, int? idEmpresa);
    }
}
﻿namespace ATS.CrossCutting.Reports.PagamentoFrete.Recibo
{
    public class PagamentoFreteReciboModel
    {
        public string EstabelecimentoNome { get; set; }
        public string EstabelecimentoCnpj { get; set; }
        public string EmpresaLogo { get; set; }
        public string ViagemToken { get; set; }
        public string ViagemEventoTipo { get; set; }
        public string ViagemEventoNumeroRecibo { get; set; }
        public string ViagemEventoValorTotalPagamento { get; set; }
        public string ViagemEventoValorTotalPagamentoComPedagio { get; set; }
        public string ViagemEventoDataHoraPagamento { get; set; }        
        public string NomeProprietario { get; set; }
        public string ProprietarioCpfCnpj { get; set; }
        public string ProprietarioRntrc { get; set; }
        public string ViagemPesoSaida { get; set; }
        public string ViagemPesoChegada { get; set; }
        public string ViagemDifFreteMotorista { get; set; }
        public string ViagemQuebraMercadoria { get; set; }
        public string MotoristaNome { get; set; }
        public byte[] Barcode { get; set; }
        public string Instrucoes { get; set; }
        public string IRRF { get; set; }
        public string INSS { get; set; }
        public string SESTSENAT { get; set; }
        public string ISSQN { get; set; }
        public string Outros { get; set; }
        public string Usuario { get; set; }
        public string ValorPedagio { get; set; }
        public string NumeroCte { get; set; }
        public string Ciot { get; set; }
        
        /// <summary>
        /// Código da filial no sistema emissor do frete (TMS de nosso cliente)
        /// </summary>
        public string NumeroFilialExterno { get; set; }
        
        /// <summary>
        /// Número do documento do cliente para impressão no comprovante
        /// </summary>
        public string DocumentoCliente { get; set; }

        public string DataDescarga { get; set; }

        /// <summary>
        /// Valor do evento de adiantamento contempla o pagamento do pedágio embutido
        /// </summary>
        public bool PedagioInclusoNoAdiantamento { get; set; }
    }

    public class PagamentoFreteReciboAcrescimosDescontosModel
    {
        public string Descricao { get; set; }

        public string Valor { get; set; }
    }
}
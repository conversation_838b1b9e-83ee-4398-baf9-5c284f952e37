﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

namespace SistemaInfo.Cartoes.Repository.External.SistemaInfo.Cartap.Web.Client
{
    #pragma warning disable // Disable all warnings

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class AtualizarClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public AtualizarClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
    		_settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
    	}
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<string> GetScriptAsync(string x_web_auth_token)
        {
            return GetScriptAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public string GetScript(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await GetScriptAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<string> GetScriptAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/GetScript");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(string); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<string>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(string);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ExecutarAsync(string x_web_auth_token)
        {
            return ExecutarAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void Executar(string x_web_auth_token)
        {
            System.Threading.Tasks.Task.Run(async () => await ExecutarAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ExecutarAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/Executar");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Content = new System.Net.Http.StringContent(string.Empty);
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ExecutarSeedAsync(string x_web_auth_token)
        {
            return ExecutarSeedAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void ExecutarSeed(string x_web_auth_token)
        {
            System.Threading.Tasks.Task.Run(async () => await ExecutarSeedAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ExecutarSeedAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/ExecutarSeed");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Content = new System.Net.Http.StringContent(string.Empty);
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<bool> PossuiAtualizacaoPendenteAsync(string x_web_auth_token)
        {
            return PossuiAtualizacaoPendenteAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public bool PossuiAtualizacaoPendente(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await PossuiAtualizacaoPendenteAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<bool> PossuiAtualizacaoPendenteAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Atualizar/PossuiAtualizacaoPendente");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Content = new System.Net.Http.StringContent(string.Empty);
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(bool); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<bool>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(bool);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
    	private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
    	{
    		if (value is System.Enum)
    		{
    			string name = System.Enum.GetName(value.GetType(), value);
    			if (name != null)
    			{
    				var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
    				if (field != null)
    				{
    					var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
    						as System.Runtime.Serialization.EnumMemberAttribute;
    					if (attribute != null)
    					{
    						return attribute.Value;
    					}
    				}
    			}
    		}
    
    		return System.Convert.ToString(value, cultureInfo);
    	}
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class Client 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public Client(string baseUrl)
        {
            BaseUrl = baseUrl; 
    		_settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
    	}
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Busca cartão através do identificador e produto.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultaCartaoResponse> CartoesGetAsync(int identificador, int produto, string x_web_auth_token)
        {
            return CartoesGetAsync(identificador, produto, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca cartão através do identificador e produto.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultaCartaoResponse CartoesGet(int identificador, int produto, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CartoesGetAsync(identificador, produto, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca cartão através do identificador e produto.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li><li>Cartão Processadora: Grupo de valores de chave e valor que referenciam dados específicos de cada cartão"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultaCartaoResponse> CartoesGetAsync(int identificador, int produto, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (identificador == null)
                throw new System.ArgumentNullException("identificador");
    
            if (produto == null)
                throw new System.ArgumentNullException("produto");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/{identificador}/{produto}");
            urlBuilder_.Replace("{identificador}", System.Uri.EscapeDataString(ConvertToString(identificador, System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Replace("{produto}", System.Uri.EscapeDataString(ConvertToString(produto, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultaCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ == "204") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("N\u00e3o existe cart\u00e3o com os dados indicados", status_, responseData_, headers_, null);
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultaCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Buscar todos os cartões da administradora</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<CartaoResponse>> CartoesGetAsync(System.DateTime? data, string x_web_auth_token)
        {
            return CartoesGetAsync(data, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar todos os cartões da administradora</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<CartaoResponse> CartoesGet(System.DateTime? data, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CartoesGetAsync(data, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar todos os cartões da administradora</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<CartaoResponse>> CartoesGetAsync(System.DateTime? data, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes?");
            if (data != null) urlBuilder_.Append("data=").Append(System.Uri.EscapeDataString(data.Value.ToString("s", System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<CartaoResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<CartaoResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<CartaoResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca todas as pessoas cadastradas (Somente para testes)</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<ConsultarPessoaResponse>> PessoasAsync(string x_web_auth_token)
        {
            return PessoasAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todas as pessoas cadastradas (Somente para testes)</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<ConsultarPessoaResponse> Pessoas(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await PessoasAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todas as pessoas cadastradas (Somente para testes)</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<ConsultarPessoaResponse>> PessoasAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<ConsultarPessoaResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<ConsultarPessoaResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<ConsultarPessoaResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca todas as processadoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<ProcessadoraDTOResponse>> ProcessadoraGetAsync(string x_web_auth_token)
        {
            return ProcessadoraGetAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todas as processadoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<ProcessadoraDTOResponse> ProcessadoraGet(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProcessadoraGetAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todas as processadoras</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<ProcessadoraDTOResponse>> ProcessadoraGetAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<ProcessadoraDTOResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<ProcessadoraDTOResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<ProcessadoraDTOResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Exclui uma Processadora</summary>
        /// <param name="id">Id da processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebActionResponse> ProcessadoraDeleteAsync(int id, string x_web_auth_token)
        {
            return ProcessadoraDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Exclui uma Processadora</summary>
        /// <param name="id">Id da processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebActionResponse ProcessadoraDelete(int id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProcessadoraDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Exclui uma Processadora</summary>
        /// <param name="id">Id da processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebActionResponse> ProcessadoraDeleteAsync(int id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (id == null)
                throw new System.ArgumentNullException("id");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora?");
            urlBuilder_.Append("id=").Append(System.Uri.EscapeDataString(ConvertToString(id, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("DELETE");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebActionResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebActionResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(WebActionResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca uma processadora por Id</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ProcessadoraDTOResponse> ProcessadoraGetAsync(int id, string x_web_auth_token)
        {
            return ProcessadoraGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca uma processadora por Id</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ProcessadoraDTOResponse ProcessadoraGet(int id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProcessadoraGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca uma processadora por Id</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ProcessadoraDTOResponse> ProcessadoraGetAsync(int id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (id == null)
                throw new System.ArgumentNullException("id");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora/{id}");
            urlBuilder_.Replace("{id}", System.Uri.EscapeDataString(ConvertToString(id, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ProcessadoraDTOResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ProcessadoraDTOResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ProcessadoraDTOResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca todos os Tipos de Transação cadastrados</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<TipoTransacaoDTOResponse>> TipoTransacaoGetAsync(string x_web_auth_token)
        {
            return TipoTransacaoGetAsync(x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todos os Tipos de Transação cadastrados</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<TipoTransacaoDTOResponse> TipoTransacaoGet(string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await TipoTransacaoGetAsync(x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todos os Tipos de Transação cadastrados</summary>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<TipoTransacaoDTOResponse>> TipoTransacaoGetAsync(string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<TipoTransacaoDTOResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<TipoTransacaoDTOResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<TipoTransacaoDTOResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Remove um Tipo de Transacao</summary>
        /// <param name="id">Id da transacao a ser removida</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebActionResponse> TipoTransacaoDeleteAsync(int? id, string x_web_auth_token)
        {
            return TipoTransacaoDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Remove um Tipo de Transacao</summary>
        /// <param name="id">Id da transacao a ser removida</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebActionResponse TipoTransacaoDelete(int? id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await TipoTransacaoDeleteAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Remove um Tipo de Transacao</summary>
        /// <param name="id">Id da transacao a ser removida</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebActionResponse> TipoTransacaoDeleteAsync(int? id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(id, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("DELETE");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebActionResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebActionResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(WebActionResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Busca um Tipo de Transacao com base no Id</summary>
        /// <param name="id">Id do tipo de transacao</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<TipoTransacaoDTOResponse> TipoTransacaoGetAsync(int id, string x_web_auth_token)
        {
            return TipoTransacaoGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca um Tipo de Transacao com base no Id</summary>
        /// <param name="id">Id do tipo de transacao</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public TipoTransacaoDTOResponse TipoTransacaoGet(int id, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await TipoTransacaoGetAsync(id, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca um Tipo de Transacao com base no Id</summary>
        /// <param name="id">Id do tipo de transacao</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<TipoTransacaoDTOResponse> TipoTransacaoGetAsync(int id, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (id == null)
                throw new System.ArgumentNullException("id");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao/{id}");
            urlBuilder_.Replace("{id}", System.Uri.EscapeDataString(ConvertToString(id, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(TipoTransacaoDTOResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoDTOResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(TipoTransacaoDTOResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
    	private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
    	{
    		if (value is System.Enum)
    		{
    			string name = System.Enum.GetName(value.GetType(), value);
    			if (name != null)
    			{
    				var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
    				if (field != null)
    				{
    					var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
    						as System.Runtime.Serialization.EnumMemberAttribute;
    					if (attribute != null)
    					{
    						return attribute.Value;
    					}
    				}
    			}
    		}
    
    		return System.Convert.ToString(value, cultureInfo);
    	}
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class CartoesClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public CartoesClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
    		_settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
    	}
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Cadastra um novo cartão</summary>
        /// <param name="cartaoCadastrarRequest">DTO de integração contendo dados de pessoa e cartão</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CartaoCadastrarResponse> CadastrarAsync(CartaoCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return CadastrarAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cadastra um novo cartão</summary>
        /// <param name="cartaoCadastrarRequest">DTO de integração contendo dados de pessoa e cartão</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CartaoCadastrarResponse Cadastrar(CartaoCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CadastrarAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Cadastra um novo cartão</summary>
        /// <param name="cartaoCadastrarRequest">DTO de integração contendo dados de pessoa e cartão</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CartaoCadastrarResponse> CadastrarAsync(CartaoCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/Cadastrar");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(cartaoCadastrarRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CartaoCadastrarResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoCadastrarResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CartaoCadastrarResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.</summary>
        /// <param name="cartaoCadastrarRequest">cartao a ser cadastrado</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CartaoVirtualCadastrarResponse> CadastrarCartaoVirtualAsync(CartaoVirtualCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return CadastrarCartaoVirtualAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.</summary>
        /// <param name="cartaoCadastrarRequest">cartao a ser cadastrado</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CartaoVirtualCadastrarResponse CadastrarCartaoVirtual(CartaoVirtualCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await CadastrarCartaoVirtualAsync(cartaoCadastrarRequest, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Cadastrar um cartao virtual. Disponível apenas para o serviço da Biz utilizar.</summary>
        /// <param name="cartaoCadastrarRequest">cartao a ser cadastrado</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CartaoVirtualCadastrarResponse> CadastrarCartaoVirtualAsync(CartaoVirtualCadastrarRequest cartaoCadastrarRequest, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/CadastrarCartaoVirtual");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(cartaoCadastrarRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CartaoVirtualCadastrarResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVirtualCadastrarResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CartaoVirtualCadastrarResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Método responsável por importar cartoes a  partir de um arquivo</summary>
        /// <param name="file">Upload File</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task ImportarCartoesAsync(FileParameter file, string x_web_auth_token)
        {
            return ImportarCartoesAsync(file, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Método responsável por importar cartoes a  partir de um arquivo</summary>
        /// <param name="file">Upload File</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void ImportarCartoes(FileParameter file, string x_web_auth_token)
        {
            System.Threading.Tasks.Task.Run(async () => await ImportarCartoesAsync(file, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Método responsável por importar cartoes a  partir de um arquivo</summary>
        /// <param name="file">Upload File</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task ImportarCartoesAsync(FileParameter file, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Cartoes/ImportarCartoes");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    var boundary_ = System.Guid.NewGuid().ToString();
                    var content_ = new System.Net.Http.MultipartFormDataContent(boundary_);
                    content_.Headers.Remove("Content-Type");
                    content_.Headers.TryAddWithoutValidation("Content-Type", "multipart/form-data; boundary=" + boundary_);
                    if (file == null)
                        throw new System.ArgumentNullException("file");
                    else
                        content_.Add(new System.Net.Http.StreamContent(file.Data), "file", file.FileName ?? "file");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
    	private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
    	{
    		if (value is System.Enum)
    		{
    			string name = System.Enum.GetName(value.GetType(), value);
    			if (name != null)
    			{
    				var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
    				if (field != null)
    				{
    					var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
    						as System.Runtime.Serialization.EnumMemberAttribute;
    					if (attribute != null)
    					{
    						return attribute.Value;
    					}
    				}
    			}
    		}
    
    		return System.Convert.ToString(value, cultureInfo);
    	}
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class DashboardClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public DashboardClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
    		_settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
    	}
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<IndicadorTransacaoSemanaResponse> IndicadoresTransacoesSemanaAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return IndicadoresTransacoesSemanaAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public IndicadorTransacaoSemanaResponse IndicadoresTransacoesSemana(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IndicadoresTransacoesSemanaAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<IndicadorTransacaoSemanaResponse> IndicadoresTransacoesSemanaAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (offsetWeeks == null)
                throw new System.ArgumentNullException("offsetWeeks");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Dashboard/IndicadoresTransacoesSemana?");
            urlBuilder_.Append("offsetWeeks=").Append(System.Uri.EscapeDataString(ConvertToString(offsetWeeks, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (administradoraId != null) urlBuilder_.Append("administradoraId=").Append(System.Uri.EscapeDataString(ConvertToString(administradoraId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (empresaId != null) urlBuilder_.Append("empresaId=").Append(System.Uri.EscapeDataString(ConvertToString(empresaId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (produtoId != null) urlBuilder_.Append("produtoId=").Append(System.Uri.EscapeDataString(ConvertToString(produtoId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(IndicadorTransacaoSemanaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(IndicadorTransacaoSemanaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<IndicadorTransacaoSemanaGraficoResponse> IndicadoresTransacoesSemanaGraficoAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return IndicadoresTransacoesSemanaGraficoAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public IndicadorTransacaoSemanaGraficoResponse IndicadoresTransacoesSemanaGrafico(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IndicadoresTransacoesSemanaGraficoAsync(offsetWeeks, administradoraId, empresaId, produtoId, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<IndicadorTransacaoSemanaGraficoResponse> IndicadoresTransacoesSemanaGraficoAsync(int offsetWeeks, int? administradoraId, int? empresaId, int? produtoId, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            if (offsetWeeks == null)
                throw new System.ArgumentNullException("offsetWeeks");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Dashboard/IndicadoresTransacoesSemanaGrafico?");
            urlBuilder_.Append("offsetWeeks=").Append(System.Uri.EscapeDataString(ConvertToString(offsetWeeks, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (administradoraId != null) urlBuilder_.Append("administradoraId=").Append(System.Uri.EscapeDataString(ConvertToString(administradoraId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (empresaId != null) urlBuilder_.Append("empresaId=").Append(System.Uri.EscapeDataString(ConvertToString(empresaId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (produtoId != null) urlBuilder_.Append("produtoId=").Append(System.Uri.EscapeDataString(ConvertToString(produtoId, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(IndicadorTransacaoSemanaGraficoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaGraficoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(IndicadorTransacaoSemanaGraficoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
    	private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
    	{
    		if (value is System.Enum)
    		{
    			string name = System.Enum.GetName(value.GetType(), value);
    			if (name != null)
    			{
    				var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
    				if (field != null)
    				{
    					var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
    						as System.Runtime.Serialization.EnumMemberAttribute;
    					if (attribute != null)
    					{
    						return attribute.Value;
    					}
    				}
    			}
    		}
    
    		return System.Convert.ToString(value, cultureInfo);
    	}
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class ProcessadoraClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public ProcessadoraClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
    		_settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
    	}
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Integra a Processadora</summary>
        /// <param name="processadora">Objeto da Processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(ProcessadoraDTORequest processadora, string x_web_auth_token)
        {
            return IntegrarAsync(processadora, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Integra a Processadora</summary>
        /// <param name="processadora">Objeto da Processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebIntegrationResponse Integrar(ProcessadoraDTORequest processadora, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IntegrarAsync(processadora, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Integra a Processadora</summary>
        /// <param name="processadora">Objeto da Processadora</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(ProcessadoraDTORequest processadora, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Processadora/Integrar");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(processadora, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebIntegrationResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebIntegrationResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(WebIntegrationResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
    	private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
    	{
    		if (value is System.Enum)
    		{
    			string name = System.Enum.GetName(value.GetType(), value);
    			if (name != null)
    			{
    				var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
    				if (field != null)
    				{
    					var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
    						as System.Runtime.Serialization.EnumMemberAttribute;
    					if (attribute != null)
    					{
    						return attribute.Value;
    					}
    				}
    			}
    		}
    
    		return System.Convert.ToString(value, cultureInfo);
    	}
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class TipoTransacaoClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public TipoTransacaoClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
    		_settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
    	}
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Integra um Tipo de Transação</summary>
        /// <param name="tipoTransacao">Objeto do tipo de transação</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(TipoTransacaoDTORequest tipoTransacao, string x_web_auth_token)
        {
            return IntegrarAsync(tipoTransacao, x_web_auth_token, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Integra um Tipo de Transação</summary>
        /// <param name="tipoTransacao">Objeto do tipo de transação</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public WebIntegrationResponse Integrar(TipoTransacaoDTORequest tipoTransacao, string x_web_auth_token)
        {
            return System.Threading.Tasks.Task.Run(async () => await IntegrarAsync(tipoTransacao, x_web_auth_token, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Integra um Tipo de Transação</summary>
        /// <param name="tipoTransacao">Objeto do tipo de transação</param>
        /// <param name="x_web_auth_token">Token de identificação para front end</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<WebIntegrationResponse> IntegrarAsync(TipoTransacaoDTORequest tipoTransacao, string x_web_auth_token, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/TipoTransacao/Integrar");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    request_.Headers.TryAddWithoutValidation("x-web-auth-token", x_web_auth_token != null ? ConvertToString(x_web_auth_token, System.Globalization.CultureInfo.InvariantCulture) : null);
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(tipoTransacao, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(WebIntegrationResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<WebIntegrationResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(WebIntegrationResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
    	private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
    	{
    		if (value is System.Enum)
    		{
    			string name = System.Enum.GetName(value.GetType(), value);
    			if (name != null)
    			{
    				var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
    				if (field != null)
    				{
    					var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
    						as System.Runtime.Serialization.EnumMemberAttribute;
    					if (attribute != null)
    					{
    						return attribute.Value;
    					}
    				}
    			}
    		}
    
    		return System.Convert.ToString(value, cultureInfo);
    	}
    }
    
    

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private System.DateTime? _dataDesvinculo;
        private CartaoStatus _status;
        private bool? _contaVirtual;
        private string _motivoDesvinculo;
        private Pessoa _pessoa;
        private ProdutoResponse _produto;
        private System.Collections.Generic.List<CartaoProcessadoraResponse> _cartaoProcessadora;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesvinculo
        {
            get { return _dataDesvinculo; }
            set 
            {
                if (_dataDesvinculo != value)
                {
                    _dataDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CartaoStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contaVirtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ContaVirtual
        {
            get { return _contaVirtual; }
            set 
            {
                if (_contaVirtual != value)
                {
                    _contaVirtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motivoDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotivoDesvinculo
        {
            get { return _motivoDesvinculo; }
            set 
            {
                if (_motivoDesvinculo != value)
                {
                    _motivoDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoProcessadora", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<CartaoProcessadoraResponse> CartaoProcessadora
        {
            get { return _cartaoProcessadora; }
            set 
            {
                if (_cartaoProcessadora != value)
                {
                    _cartaoProcessadora = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoStatus : System.ComponentModel.INotifyPropertyChanged
    {
        private string _abreviado;
        private string _descricao;
        private CartaoStatusId _id;
    
        [Newtonsoft.Json.JsonProperty("abreviado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Abreviado
        {
            get { return _abreviado; }
            set 
            {
                if (_abreviado != value)
                {
                    _abreviado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CartaoStatusId Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoStatus FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoStatus>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Pessoa : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private string _tipoPessoa;
        private string _sexo;
        private int? _cidadeId;
        private bool? _ativo;
        private System.DateTime? _dataAtivacao;
        private System.DateTime? _dataDesativacao;
        private Cidade _cidade;
        private System.Collections.Generic.List<PessoaContaBancaria> _contasBancarias;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoPessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TipoPessoa
        {
            get { return _tipoPessoa; }
            set 
            {
                if (_tipoPessoa != value)
                {
                    _tipoPessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sexo
        {
            get { return _sexo; }
            set 
            {
                if (_sexo != value)
                {
                    _sexo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidadeId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CidadeId
        {
            get { return _cidadeId; }
            set 
            {
                if (_cidadeId != value)
                {
                    _cidadeId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ativo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Ativo
        {
            get { return _ativo; }
            set 
            {
                if (_ativo != value)
                {
                    _ativo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAtivacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAtivacao
        {
            get { return _dataAtivacao; }
            set 
            {
                if (_dataAtivacao != value)
                {
                    _dataAtivacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesativacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesativacao
        {
            get { return _dataDesativacao; }
            set 
            {
                if (_dataDesativacao != value)
                {
                    _dataDesativacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Cidade Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contasBancarias", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<PessoaContaBancaria> ContasBancarias
        {
            get { return _contasBancarias; }
            set 
            {
                if (_contasBancarias != value)
                {
                    _contasBancarias = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Pessoa FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Pessoa>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProdutoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int _id;
        private string _nome;
        private bool _isMultiplasContas;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("isMultiplasContas", Required = Newtonsoft.Json.Required.Always)]
        public bool IsMultiplasContas
        {
            get { return _isMultiplasContas; }
            set 
            {
                if (_isMultiplasContas != value)
                {
                    _isMultiplasContas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProdutoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProdutoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoProcessadoraResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _cartaoId;
        private string _key;
        private int? _valor;
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("key", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Key
        {
            get { return _key; }
            set 
            {
                if (_key != value)
                {
                    _key = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoProcessadoraResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoProcessadoraResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Cidade : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private int? _ibge;
        private int? _estadoId;
        private Estado _estado;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibge", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Ibge
        {
            get { return _ibge; }
            set 
            {
                if (_ibge != value)
                {
                    _ibge = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estadoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? EstadoId
        {
            get { return _estadoId; }
            set 
            {
                if (_estadoId != value)
                {
                    _estadoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Estado Estado
        {
            get { return _estado; }
            set 
            {
                if (_estado != value)
                {
                    _estado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Cidade FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Cidade>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaContaBancaria : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _pessoaId;
        private string _nomeConta;
        private string _codigoBacenBanco;
        private string _agencia;
        private string _conta;
        private int? _digitoConta;
        private Pessoa _pessoa;
        private Banco _banco;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("pessoaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PessoaId
        {
            get { return _pessoaId; }
            set 
            {
                if (_pessoaId != value)
                {
                    _pessoaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeConta
        {
            get { return _nomeConta; }
            set 
            {
                if (_nomeConta != value)
                {
                    _nomeConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoBacenBanco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoBacenBanco
        {
            get { return _codigoBacenBanco; }
            set 
            {
                if (_codigoBacenBanco != value)
                {
                    _codigoBacenBanco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Agencia
        {
            get { return _agencia; }
            set 
            {
                if (_agencia != value)
                {
                    _agencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("conta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Conta
        {
            get { return _conta; }
            set 
            {
                if (_conta != value)
                {
                    _conta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("digitoConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? DigitoConta
        {
            get { return _digitoConta; }
            set 
            {
                if (_digitoConta != value)
                {
                    _digitoConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pessoa Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("banco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Banco Banco
        {
            get { return _banco; }
            set 
            {
                if (_banco != value)
                {
                    _banco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaContaBancaria FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaContaBancaria>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Estado : System.ComponentModel.INotifyPropertyChanged
    {
        private string _sigla;
        private string _nome;
        private int? _ibge;
        private int? _paisId;
        private Pais _pais;
        private System.Collections.Generic.List<Cidade> _cidades;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("sigla", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sigla
        {
            get { return _sigla; }
            set 
            {
                if (_sigla != value)
                {
                    _sigla = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibge", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Ibge
        {
            get { return _ibge; }
            set 
            {
                if (_ibge != value)
                {
                    _ibge = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("paisId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PaisId
        {
            get { return _paisId; }
            set 
            {
                if (_paisId != value)
                {
                    _paisId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Pais Pais
        {
            get { return _pais; }
            set 
            {
                if (_pais != value)
                {
                    _pais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidades", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Cidade> Cidades
        {
            get { return _cidades; }
            set 
            {
                if (_cidades != value)
                {
                    _cidades = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Estado FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Estado>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Banco : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _url;
        private string _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url
        {
            get { return _url; }
            set 
            {
                if (_url != value)
                {
                    _url = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public string Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Banco FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Banco>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class Pais : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _sigla;
        private int? _bacen;
        private string _prefixoTelefone;
        private System.Collections.Generic.List<Estado> _estados;
        private int _id;
        private int? _usuarioCadastroId;
        private System.DateTime? _dataCadastro;
        private int? _usuarioAlteracaoId;
        private System.DateTime? _dataAlteracao;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sigla", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sigla
        {
            get { return _sigla; }
            set 
            {
                if (_sigla != value)
                {
                    _sigla = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bacen", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Bacen
        {
            get { return _bacen; }
            set 
            {
                if (_bacen != value)
                {
                    _bacen = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("prefixoTelefone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PrefixoTelefone
        {
            get { return _prefixoTelefone; }
            set 
            {
                if (_prefixoTelefone != value)
                {
                    _prefixoTelefone = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estados", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Estado> Estados
        {
            get { return _estados; }
            set 
            {
                if (_estados != value)
                {
                    _estados = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioCadastroId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioCadastroId
        {
            get { return _usuarioCadastroId; }
            set 
            {
                if (_usuarioCadastroId != value)
                {
                    _usuarioCadastroId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("usuarioAlteracaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? UsuarioAlteracaoId
        {
            get { return _usuarioAlteracaoId; }
            set 
            {
                if (_usuarioAlteracaoId != value)
                {
                    _usuarioAlteracaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataAlteracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataAlteracao
        {
            get { return _dataAlteracao; }
            set 
            {
                if (_dataAlteracao != value)
                {
                    _dataAlteracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Pais FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Pais>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private System.DateTime? _dataDesvinculo;
        private string _portador;
        private ProdutoResponse _produto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesvinculo
        {
            get { return _dataDesvinculo; }
            set 
            {
                if (_dataDesvinculo != value)
                {
                    _dataDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("portador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Portador
        {
            get { return _portador; }
            set 
            {
                if (_portador != value)
                {
                    _portador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoCadastrarRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _produtoId;
        private System.Collections.Generic.Dictionary<string, string> _parametros;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produtoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProdutoId
        {
            get { return _produtoId; }
            set 
            {
                if (_produtoId != value)
                {
                    _produtoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parametros", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> Parametros
        {
            get { return _parametros; }
            set 
            {
                if (_parametros != value)
                {
                    _parametros = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoCadastrarRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoCadastrarRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoCadastrarResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private string _mensagem;
        private int? _cartaoId;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoCadastrarResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoCadastrarResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoVirtualCadastrarRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _produtoId;
        private System.Collections.Generic.Dictionary<string, string> _parametros;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produtoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProdutoId
        {
            get { return _produtoId; }
            set 
            {
                if (_produtoId != value)
                {
                    _produtoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("parametros", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> Parametros
        {
            get { return _parametros; }
            set 
            {
                if (_parametros != value)
                {
                    _parametros = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoVirtualCadastrarRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVirtualCadastrarRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoVirtualCadastrarResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private bool? _sucesso;
        private string _mensagem;
        private int? _cartaoId;
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoVirtualCadastrarResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVirtualCadastrarResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IFormFile : System.ComponentModel.INotifyPropertyChanged
    {
        private string _contentType;
        private string _contentDisposition;
        private System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<string>> _headers;
        private long? _length;
        private string _name;
        private string _fileName;
    
        [Newtonsoft.Json.JsonProperty("contentType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContentType
        {
            get { return _contentType; }
            set 
            {
                if (_contentType != value)
                {
                    _contentType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contentDisposition", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContentDisposition
        {
            get { return _contentDisposition; }
            set 
            {
                if (_contentDisposition != value)
                {
                    _contentDisposition = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("headers", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<string>> Headers
        {
            get { return _headers; }
            set 
            {
                if (_headers != value)
                {
                    _headers = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("length", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? Length
        {
            get { return _length; }
            set 
            {
                if (_length != value)
                {
                    _length = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name
        {
            get { return _name; }
            set 
            {
                if (_name != value)
                {
                    _name = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("fileName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string FileName
        {
            get { return _fileName; }
            set 
            {
                if (_fileName != value)
                {
                    _fileName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IFormFile FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IFormFile>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IndicadorTransacaoSemanaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool? _sucesso;
        private string _mensagem;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
        private System.Collections.Generic.List<IndicadorItem> _indicadores;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set 
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set 
            {
                if (_dataFim != value)
                {
                    _dataFim = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("indicadores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<IndicadorItem> Indicadores
        {
            get { return _indicadores; }
            set 
            {
                if (_indicadores != value)
                {
                    _indicadores = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IndicadorTransacaoSemanaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebProcessingStateOnServer : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServerState? _state;
        private string _errorMessage;
    
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public WebProcessingStateOnServerState? State
        {
            get { return _state; }
            set 
            {
                if (_state != value)
                {
                    _state = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errorMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set 
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebProcessingStateOnServer FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebProcessingStateOnServer>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IndicadorItem : System.ComponentModel.INotifyPropertyChanged
    {
        private string _indicador;
        private IndicadorItemTipo? _tipo;
        private double? _segunda;
        private double? _terca;
        private double? _quarta;
        private double? _quinta;
        private double? _sexta;
        private double? _sabado;
        private double? _domingo;
        private double? _totalSemana;
        private double? _totalMes;
        private string _segundaFormatted;
        private string _tercaFormatted;
        private string _quartaFormatted;
        private string _quintaFormatted;
        private string _sextaFormatted;
        private string _sabadoFormatted;
        private string _domingoFormatted;
        private string _totalSemanaFormatted;
        private string _totalMesFormatted;
    
        [Newtonsoft.Json.JsonProperty("indicador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Indicador
        {
            get { return _indicador; }
            set 
            {
                if (_indicador != value)
                {
                    _indicador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public IndicadorItemTipo? Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("segunda", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Segunda
        {
            get { return _segunda; }
            set 
            {
                if (_segunda != value)
                {
                    _segunda = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("terca", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Terca
        {
            get { return _terca; }
            set 
            {
                if (_terca != value)
                {
                    _terca = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quarta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Quarta
        {
            get { return _quarta; }
            set 
            {
                if (_quarta != value)
                {
                    _quarta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quinta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Quinta
        {
            get { return _quinta; }
            set 
            {
                if (_quinta != value)
                {
                    _quinta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Sexta
        {
            get { return _sexta; }
            set 
            {
                if (_sexta != value)
                {
                    _sexta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sabado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Sabado
        {
            get { return _sabado; }
            set 
            {
                if (_sabado != value)
                {
                    _sabado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("domingo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? Domingo
        {
            get { return _domingo; }
            set 
            {
                if (_domingo != value)
                {
                    _domingo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalSemana", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? TotalSemana
        {
            get { return _totalSemana; }
            set 
            {
                if (_totalSemana != value)
                {
                    _totalSemana = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalMes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? TotalMes
        {
            get { return _totalMes; }
            set 
            {
                if (_totalMes != value)
                {
                    _totalMes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("segundaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SegundaFormatted
        {
            get { return _segundaFormatted; }
            set 
            {
                if (_segundaFormatted != value)
                {
                    _segundaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tercaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TercaFormatted
        {
            get { return _tercaFormatted; }
            set 
            {
                if (_tercaFormatted != value)
                {
                    _tercaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quartaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string QuartaFormatted
        {
            get { return _quartaFormatted; }
            set 
            {
                if (_quartaFormatted != value)
                {
                    _quartaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quintaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string QuintaFormatted
        {
            get { return _quintaFormatted; }
            set 
            {
                if (_quintaFormatted != value)
                {
                    _quintaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sextaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SextaFormatted
        {
            get { return _sextaFormatted; }
            set 
            {
                if (_sextaFormatted != value)
                {
                    _sextaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sabadoFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SabadoFormatted
        {
            get { return _sabadoFormatted; }
            set 
            {
                if (_sabadoFormatted != value)
                {
                    _sabadoFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("domingoFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DomingoFormatted
        {
            get { return _domingoFormatted; }
            set 
            {
                if (_domingoFormatted != value)
                {
                    _domingoFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalSemanaFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TotalSemanaFormatted
        {
            get { return _totalSemanaFormatted; }
            set 
            {
                if (_totalSemanaFormatted != value)
                {
                    _totalSemanaFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("totalMesFormatted", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string TotalMesFormatted
        {
            get { return _totalMesFormatted; }
            set 
            {
                if (_totalMesFormatted != value)
                {
                    _totalMesFormatted = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IndicadorItem FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorItem>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IndicadorTransacaoSemanaGraficoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool? _sucesso;
        private string _mensagem;
        private System.DateTime? _dataInicioSemanaAtual;
        private System.DateTime? _dataFimSemanaAtual;
        private System.DateTime? _dataInicioSemanaAnterior;
        private System.DateTime? _dataFimSemanaAnterior;
        private System.Collections.Generic.List<TransacaoDiaGraficoitem> _indicadores;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioSemanaAtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioSemanaAtual
        {
            get { return _dataInicioSemanaAtual; }
            set 
            {
                if (_dataInicioSemanaAtual != value)
                {
                    _dataInicioSemanaAtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimSemanaAtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimSemanaAtual
        {
            get { return _dataFimSemanaAtual; }
            set 
            {
                if (_dataFimSemanaAtual != value)
                {
                    _dataFimSemanaAtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicioSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicioSemanaAnterior
        {
            get { return _dataInicioSemanaAnterior; }
            set 
            {
                if (_dataInicioSemanaAnterior != value)
                {
                    _dataInicioSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFimSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFimSemanaAnterior
        {
            get { return _dataFimSemanaAnterior; }
            set 
            {
                if (_dataFimSemanaAnterior != value)
                {
                    _dataFimSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("indicadores", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TransacaoDiaGraficoitem> Indicadores
        {
            get { return _indicadores; }
            set 
            {
                if (_indicadores != value)
                {
                    _indicadores = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IndicadorTransacaoSemanaGraficoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IndicadorTransacaoSemanaGraficoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransacaoDiaGraficoitem : System.ComponentModel.INotifyPropertyChanged
    {
        private string _dia;
        private double? _valorSemanaAtual;
        private double? _valorSemanaAnterior;
        private double? _valorMesmaHoraSemanaAnterior;
    
        [Newtonsoft.Json.JsonProperty("dia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Dia
        {
            get { return _dia; }
            set 
            {
                if (_dia != value)
                {
                    _dia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSemanaAtual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? ValorSemanaAtual
        {
            get { return _valorSemanaAtual; }
            set 
            {
                if (_valorSemanaAtual != value)
                {
                    _valorSemanaAtual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? ValorSemanaAnterior
        {
            get { return _valorSemanaAnterior; }
            set 
            {
                if (_valorSemanaAnterior != value)
                {
                    _valorSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorMesmaHoraSemanaAnterior", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public double? ValorMesmaHoraSemanaAnterior
        {
            get { return _valorMesmaHoraSemanaAnterior; }
            set 
            {
                if (_valorMesmaHoraSemanaAnterior != value)
                {
                    _valorMesmaHoraSemanaAnterior = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransacaoDiaGraficoitem FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransacaoDiaGraficoitem>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarPessoaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private string _nome;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private string _sexo;
        private bool? _ativo;
        private int? _cidade;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sexo
        {
            get { return _sexo; }
            set 
            {
                if (_sexo != value)
                {
                    _sexo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ativo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Ativo
        {
            get { return _ativo; }
            set 
            {
                if (_ativo != value)
                {
                    _ativo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarPessoaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarPessoaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ApiProcessingStateOnServer : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServerState? _state;
        private string _errorMessage;
    
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ApiProcessingStateOnServerState? State
        {
            get { return _state; }
            set 
            {
                if (_state != value)
                {
                    _state = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errorMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set 
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ApiProcessingStateOnServer FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ApiProcessingStateOnServer>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProcessadoraDTORequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _nome;
        private string _url;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url
        {
            get { return _url; }
            set 
            {
                if (_url != value)
                {
                    _url = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProcessadoraDTORequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProcessadoraDTORequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebIntegrationResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private WebIntegrationResponseStatus? _status;
        private System.Collections.Generic.List<WebResponseValidation> _mensagens;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public WebIntegrationResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<WebResponseValidation> Mensagens
        {
            get { return _mensagens; }
            set 
            {
                if (_mensagens != value)
                {
                    _mensagens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebIntegrationResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebIntegrationResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebResponseValidation : System.ComponentModel.INotifyPropertyChanged
    {
        private WebResponseValidationType? _type;
        private string _message;
        private string _field;
    
        [Newtonsoft.Json.JsonProperty("type", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public WebResponseValidationType? Type
        {
            get { return _type; }
            set 
            {
                if (_type != value)
                {
                    _type = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message
        {
            get { return _message; }
            set 
            {
                if (_message != value)
                {
                    _message = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("field", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Field
        {
            get { return _field; }
            set 
            {
                if (_field != value)
                {
                    _field = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebResponseValidation FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebResponseValidation>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class WebActionResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private WebProcessingStateOnServer _processingStateOnServer;
        private bool? _sucesso;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public WebProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sucesso", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Sucesso
        {
            get { return _sucesso; }
            set 
            {
                if (_sucesso != value)
                {
                    _sucesso = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static WebActionResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<WebActionResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProcessadoraDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _nome;
        private string _url;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("url", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Url
        {
            get { return _url; }
            set 
            {
                if (_url != value)
                {
                    _url = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProcessadoraDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProcessadoraDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoDTORequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _descricao;
        private int? _transacaoContrariaId;
        private TipoTransacaoDTORequestTipoTransacaoApiSuportada? _tipoTransacaoApiSuportada;
        private TipoTransacaoDTORequestPoliticaFalha? _politicaFalha;
        private System.Collections.Generic.List<TipoTransacaoItemDTORequest> _itens;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("transacaoContrariaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TransacaoContrariaId
        {
            get { return _transacaoContrariaId; }
            set 
            {
                if (_transacaoContrariaId != value)
                {
                    _transacaoContrariaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoTransacaoApiSuportada", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TipoTransacaoDTORequestTipoTransacaoApiSuportada? TipoTransacaoApiSuportada
        {
            get { return _tipoTransacaoApiSuportada; }
            set 
            {
                if (_tipoTransacaoApiSuportada != value)
                {
                    _tipoTransacaoApiSuportada = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TipoTransacaoDTORequestPoliticaFalha? PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("itens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TipoTransacaoItemDTORequest> Itens
        {
            get { return _itens; }
            set 
            {
                if (_itens != value)
                {
                    _itens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoDTORequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoDTORequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoItemDTORequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _tipoTransacaoItem;
    
        [Newtonsoft.Json.JsonProperty("tipoTransacaoItem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoTransacaoItem
        {
            get { return _tipoTransacaoItem; }
            set 
            {
                if (_tipoTransacaoItem != value)
                {
                    _tipoTransacaoItem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoItemDTORequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoItemDTORequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _descricao;
        private int? _transacaoContrariaId;
        private string _apiSuportada;
        private string _politicaFalha;
        private System.Collections.Generic.List<TipoTransacaoItemDTOResponse> _itens;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Descricao
        {
            get { return _descricao; }
            set 
            {
                if (_descricao != value)
                {
                    _descricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("transacaoContrariaId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TransacaoContrariaId
        {
            get { return _transacaoContrariaId; }
            set 
            {
                if (_transacaoContrariaId != value)
                {
                    _transacaoContrariaId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("apiSuportada", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ApiSuportada
        {
            get { return _apiSuportada; }
            set 
            {
                if (_apiSuportada != value)
                {
                    _apiSuportada = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("itens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<TipoTransacaoItemDTOResponse> Itens
        {
            get { return _itens; }
            set 
            {
                if (_itens != value)
                {
                    _itens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TipoTransacaoItemDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _lineId;
        private int? _tipoTransacaoItem;
    
        [Newtonsoft.Json.JsonProperty("lineId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? LineId
        {
            get { return _lineId; }
            set 
            {
                if (_lineId != value)
                {
                    _lineId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoTransacaoItem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TipoTransacaoItem
        {
            get { return _tipoTransacaoItem; }
            set 
            {
                if (_tipoTransacaoItem != value)
                {
                    _tipoTransacaoItem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TipoTransacaoItemDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TipoTransacaoItemDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum CartaoStatusId
    {
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoRemessaParaCliente")]
        AguardandoRemessaParaCliente = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoRemessaParaPontoDistribuicao")]
        AguardandoRemessaParaPontoDistribuicao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "EmTransitoParaPontoDistribuicao")]
        EmTransitoParaPontoDistribuicao = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "EmTransitoRemessaRejeitada")]
        EmTransitoRemessaRejeitada = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoVinculacao")]
        AguardandoVinculacao = 4,
    
        [System.Runtime.Serialization.EnumMember(Value = "Vinculado")]
        Vinculado = 5,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoExtravio")]
        CanceladoExtravio = 6,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoTrocaCartao")]
        CanceladoTrocaCartao = 7,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoManual")]
        CanceladoManual = 8,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum WebProcessingStateOnServerState
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ok")]
        Ok = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum IndicadorItemTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Informacao")]
        Informacao = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "TotalizadorCargaCredito")]
        TotalizadorCargaCredito = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "TotalizadorEstornoCargaCredito")]
        TotalizadorEstornoCargaCredito = 2,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ApiProcessingStateOnServerState
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ok")]
        Ok = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum WebIntegrationResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum WebResponseValidationType
    {
        [System.Runtime.Serialization.EnumMember(Value = "Information")]
        Information = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Warning")]
        Warning = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Validation")]
        Validation = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TipoTransacaoDTORequestTipoTransacaoApiSuportada
    {
        [System.Runtime.Serialization.EnumMember(Value = "Indefinida")]
        Indefinida = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "CargaCartao")]
        CargaCartao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "OnUs")]
        OnUs = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Agrupador")]
        Agrupador = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TipoTransacaoDTORequestPoliticaFalha
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoAplicado")]
        NaoAplicado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Estornar")]
        Estornar = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Reenviar")]
        Reenviar = 2,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class FileParameter
    {
        public FileParameter(System.IO.Stream data) 
            : this (data, null)
        {
        }

        public FileParameter(System.IO.Stream data, string fileName)
        {
            Data = data;
            FileName = fileName;
        }

        public System.IO.Stream Data { get; private set; }

        public string FileName { get; private set; }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public string StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, string statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message, innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********** (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, string statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}
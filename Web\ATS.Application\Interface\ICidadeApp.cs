﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Enum;
using ATS.Domain.Models.CidadeModels;

namespace ATS.Application.Interface
{
    public interface ICidadeApp : IAppBase<Cidade>
    {
        Cidade GetPorIBGE(int nIBGE);
        //IQueryable<Cidade> Consultar(string nome);
        IQueryable<CidadeGrid> Consultar(string nome);
        ValidationResult Add(Cidade entity);
        ValidationResult Update(Cidade entity);
        Cidade Get(int id);
        Cidade GetCidadeByIBGE(int nIBGE);
        IQueryable<Cidade> GetListaCidade(int idEstado);
        IQueryable<Cidade> GetCidadesAtualizadas(string uf, DateTime dataBase);
        IQueryable<Cidade> All();
        List<Cidade> WhereNomeLike(string nome);
        LocalizacaoModel GetLocalizacao(int idCidade, EOrigemConsumoServicoExterno origemConsumoServicoExterno);
        int? GetIdCidadePorNome(int idEstado, string nome);
        ValidationResult Inativar(int idCidade);
        ValidationResult Reativar(int idCidade);
        object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        List<Cidade> GetTodos();
        CidadeDetalhesResponse GetDetalhes(int idcidade);

        int? GetIdCidade(int nIBGE);

        Cidade WhereNomeIs(string nome);


        List<Cidade> ConsultarPaginadas(int? codigoIBGEEstado, int? ibge, DateTime? dataBase, int? take, int? skip);
        Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude);
        int GetIdPorIBGE(int ibge);
        IQueryable<Cidade> GetQueryByIBGE(int ibge);

        /// <summary>
        /// Retorna as informações da cidade com os objetos filhos inclusos
        /// </summary>
        /// <param name="id">Código da cidade</param>
        /// <returns></returns>
        Cidade GetWithAllChilds(int id);

        List<int> GetIbgeCidade(List<CidadeIbgeRequestModel> cidadesIbgeRequestModel,bool errorCidadeNotFound = false);
        bool ValidarIbgeCadastrado(int ibge);
    }
}
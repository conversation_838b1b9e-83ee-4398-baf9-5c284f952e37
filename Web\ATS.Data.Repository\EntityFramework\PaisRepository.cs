﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class PaisRepository : Repository<Pais>, IPaisRepository
    {
        public PaisRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado para buscar Pais por código do IBGE.
        /// </summary>
        /// <param name="codigoBACEN">Código do IBGE</param>
        /// <returns>Entidade Pais</returns>
        public Pais GetPorCodigoBACEN(int codigoBACEN)
        {
            return Find(x => x.BACEN == codigoBACEN).FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para consultar País.
        /// </summary>
        /// <param name="nome">Nome de <PERSON></param>
        /// <returns>IQueryable de Pais</returns>
        public IQueryable<Pais> Consultar(string nome)
        {
            return (from pais in All()
                    where pais.Nome.Contains(nome)
                    orderby pais.IdPais descending
                    select pais);
        }
    }
}
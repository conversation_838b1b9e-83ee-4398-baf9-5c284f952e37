﻿using System;
using System.Transactions;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using NLog;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.Services
{
    public class SrvPedagio : SrvBase
    {
        private readonly IViagemApp _viagemApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IWebhookService _webhookService;
        private readonly WebhookActionDependencies _webhookActionDependencies;
        private readonly IEmpresaService _empresaService;

        public SrvPedagio(IViagemApp viagemApp, IEmpresaApp empresaApp, IWebhookService webhookService, WebhookActionDependencies webhookActionDependencies, IEmpresaService empresaService)
        {
            _viagemApp = viagemApp;
            _empresaApp = empresaApp;
            _webhookService = webhookService;
            _webhookActionDependencies = webhookActionDependencies;
            _empresaService = empresaService;
        }
        
        public ValidationResult NotificarConfirmacaoPedagio(NotificacaoConfirmacaoPedagioModel request)
        {
            var validationResult = new ValidationResult();
            
            if (request.TipoConfirmacao == TipoConfirmacaoPedagio.Estorno && request.ProtocoloRequisicaoPedagio < 0)
                request.ProtocoloRequisicaoPedagio = Math.Abs(request.ProtocoloRequisicaoPedagio);

            var viagem = _viagemApp.Get(request.ProtocoloRequisicaoPedagio, false);
            if (viagem == null)
                return validationResult.Add($"Viagem com ID {Math.Abs(request.ProtocoloRequisicaoPedagio)} não localizada");

            try 
            {
                TransactionScope transaction = null;
                using (transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions {IsolationLevel = IsolationLevel.Serializable}))
                {
                    switch (request.TipoConfirmacao)
                    {
                        case TipoConfirmacaoPedagio.Compra:
                            viagem.ResultadoCompraPedagio = EResultadoCompraPedagio.CompraConfirmada;
                            viagem.MensagemCompraPedagio = "Compra de pedágio confirmada com sucesso";
                            viagem.DataConfirmacaoPedagio = DateTime.Now;

                            viagem.DataConfirmacaoCreditoPedagio = request.DataConfirmacao;
                            if (request.DataConfirmacao.Year <= 2000) // Pra garantir
                                viagem.DataConfirmacaoCreditoPedagio = DateTime.Now;
                            
                            validationResult.Add(_viagemApp.Update(viagem));
                                transaction.Complete();
                                
                            NotificarConfirmacaoPedagioExterno(viagem, request);
                            return validationResult;                                       
                        
                        case TipoConfirmacaoPedagio.Estorno:
                            viagem.ResultadoCompraPedagio = EResultadoCompraPedagio.CancelamentoConfirmado;
                            viagem.MensagemCompraPedagio = "Cancelamento de pedágio confirmado com sucesso";
                            viagem.DataCancelamentoPedagio = DateTime.Now;

                            viagem.DataConfirmacaoEstornoPedagio = request.DataConfirmacao;
                            if (request.DataConfirmacao.Year <= 2000) // Pra garantir
                                viagem.DataConfirmacaoEstornoPedagio = DateTime.Now;
                            
                            validationResult.Add(_viagemApp.Update(viagem));
                            transaction.Complete();
                            
                            NotificarConfirmacaoPedagioExterno(viagem, request);
                            return validationResult;
                        
                        case TipoConfirmacaoPedagio.ResgateSaldoResidual:                    
                            transaction.Complete();
                            NotificarConfirmacaoPedagioExternoResgateSaldo(viagem, request);
                            
                            return new ValidationResult();
                            
                        default:
                            transaction.Complete();
                            var result = new ValidationResult();
                            result.Add("Tipo de notificação de pedágio inesperada: " + request.TipoConfirmacao);
                            return result;
                    }
                }
            }
            catch (Exception ex)
            {
                var e = ex.GetBaseException();
                if (e.ToString().Contains("deadlock"))
                    validationResult.Add("A viagem está em atualização em outro processo e não é permitido alterações simultâneas");
                else
                    validationResult.Add($"{e.Message} {(e.InnerException != null ? e.InnerException.Message : string.Empty)}".Trim());
            }

            return validationResult;
        }

        private void NotificarConfirmacaoPedagioExterno(Viagem viagem, NotificacaoConfirmacaoPedagioModel model)
        {                        
            try
            {
                var token = _empresaApp.GetTokenMicroServices(viagem.IdEmpresa);
                if (string.IsNullOrWhiteSpace(token))
                    token = SistemaInfoConsts.TokenAdministradora;

                var webhook = _webhookService.GetByIdRegistro(viagem.IdEmpresa, WebhookTipoEvento.CargaDescargaCartaoPedagio);

                if (webhook != null && !string.IsNullOrWhiteSpace(webhook.Endpoint))
                {
                    var infoAdicional = ("Confirmação de pedágio portador: {0}." +
                                         " Viagem: {1}." +
                                         " Valor Crédito: {2}")
                        .FormatEx(model.DocumentoPortador, viagem?.IdViagem, model.ValorConfirmado.FormatMoney());
                                        
                    // Requisição
                    var requisicao = new NotificacaoConfirmacaoPedagioExternoModel
                    {
                        IdViagem = viagem.IdViagem,
                        DataConfirmacao = model.DataConfirmacao,
                        CnpjEstabelecimentoConfirmacao = model.CnpjEstabelecimento,
                        Valor = viagem.ValorPedagio,
                        Tipo = model.TipoConfirmacao,
                        DocumentoPortador = model.DocumentoPortador
                    };

                    webhook.EnviarNotificacao(_webhookActionDependencies, infoAdicional, requisicao, token, viagem.IdEmpresa);
                }

                CartoesApp.EnviarPushCompraPedagioMoedeiro(_empresaService, viagem.IdEmpresa, viagem.CPFMotorista, viagem.ValorPedagio,
                    SolicitarCompraPedagioRequestFornecedor.Moedeiro, true,
                    model.TipoConfirmacao == TipoConfirmacaoPedagio.Estorno);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao notificar web hook de confirmacao de pedagio: " + viagem.IdViagem);
            }
        }
        
        private void NotificarConfirmacaoPedagioExternoResgateSaldo(Viagem viagem, NotificacaoConfirmacaoPedagioModel model)
        {
            try
            {
                var token = _empresaApp.GetTokenMicroServices(viagem.IdEmpresa);
                if (string.IsNullOrWhiteSpace(token))
                    token = SistemaInfoConsts.TokenAdministradora;
                
                var webhook = _webhookService.GetByIdRegistro(viagem.IdEmpresa, WebhookTipoEvento.ResgateSaldoResidualCartaoPedagio);

                if (webhook != null && !string.IsNullOrWhiteSpace(webhook.Endpoint))
                {
                    var infoAdicional = ("Resgate de saldo residual portador: {0}." +
                                         " Viagem sendo creditada: {1}." +
                                         " Valor resgatado: {2}")
                        .FormatEx(model.DocumentoPortador, viagem?.IdViagem, model.ValorConfirmado.FormatMoney());
                    
                    // Requisição
                    var requisicao = new NotificacaoConfirmacaoPedagioExternoModel
                    {
                        IdViagem = viagem.IdViagem,
                        DataConfirmacao = model.DataConfirmacao,
                        CnpjEstabelecimentoConfirmacao = model.CnpjEstabelecimento,
                        Valor = viagem.ValorPedagio,
                        Tipo = model.TipoConfirmacao,
                        DocumentoPortador = model.DocumentoPortador
                    };

                    webhook.EnviarNotificacao(_webhookActionDependencies, infoAdicional, requisicao, token, viagem.IdEmpresa);
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao notificar web hook de resgate de saldo residual de pedágio de pedagio: " + viagem.IdViagem);
            }
        }
    }
}
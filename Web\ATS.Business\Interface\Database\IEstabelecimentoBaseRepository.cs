﻿using System.Collections.Generic;
using ATS.Domain.DTO.Estabelecimento;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IEstabelecimentoBaseRepository : IRepository<EstabelecimentoBase>
    {
        List<AssociacoesEmpresaDto> GetAssociacoesEmpresas(List<int> idsEmpresa);
        int GetIdByCnpj(string cnpj);
    }
}

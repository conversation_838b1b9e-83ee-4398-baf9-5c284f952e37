using System;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    public class ReciboCargaAvulsaModel
    {
        public int IdCargaAvulsa { get; set; }
        public string NomeMotorista { get; set; }
        public string CpfMotorista { get; set; }
        public string PlacaCavalo { get; set; }
        public string PlacaCarreta1 { get; set; }
        public string PlacaCarreta2 { get; set; }
        public string PlacaCarreta3 { get; set; }
        public string NumeroControleIntegracao { get; set; }
        public decimal Valor { get; set; }
        public DateTime DataCadastro { get; set; }
        public EStatusCargaAvulsa StatusCargaAvulsa { get; set; }
        public string Observacao { get; set; }
        public DateTime? DataConfirmacaoMeioHomologado { get; set; }
        public int? IdTransacaoCartao { get; set; }
        public int EmpresaId { get; set; }
        public byte[] LogoEmpresa { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string EnderecoEmpresa { get; set; }
        public string NumeroEmpresa { get; set; }
        public string BairroEmpresa { get; set; }
        public string CepEmpresa { get; set; }
        public string TelefoneEmpresa { get; set; }
        public string NomeCidadeEmpresa { get; set; }
        public string SiglaEstadoEmpresa { get; set; }
        public string CelularMotorista { get; set; }
    }
}
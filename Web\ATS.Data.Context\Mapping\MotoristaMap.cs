using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class MotoristaMap : EntityTypeConfiguration<Motorista>
    {
        public MotoristaMap()
        {
            ToTable("MOTORISTA");

            HasKey(t => t.IdMotorista);
            
            Property(t => t.IdMotorista)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            
            Property(t => t.CPF)
                .HasIndex("IX_MOTORISTA_CPF_ATIVO_IDEMPRESA", true, 0)
                .IsRequired()
                .HasMaxLength(11);

            Property(t => t.Ativo)
                .HasIndex("IX_MOTORISTA_CPF_ATIVO_IDEMPRESA", true, 1);
            
            Property(t => t.IdEmpresa)
                .HasIndex("IX_MOTORISTA_CPF_ATIVO_IDEMPRESA", true, 2);

            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(150);

            Property(t => t.Sexo)
                .IsRequired()
                .IsFixedLength()
                .HasMaxLength(1);

            Property(t => t.CNH)
                .IsRequired()
                .HasMaxLength(14);

            Property(t => t.Endereco)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Complemento)
                .HasMaxLength(100)
                .IsOptional();

            Property(t => t.Bairro)
                .HasMaxLength(50);

            Property(t => t.Celular)
                .HasMaxLength(11);

            Property(t => t.Email)
                .HasMaxLength(100);

            Property(t => t.RGOrgaoExpedidor)
                .HasMaxLength(10)
                .IsRequired();

            Property(t => t.CNHCategoria)
                .HasMaxLength(2)
                .IsRequired();

            Property(t => t.Numero)
                .HasMaxLength(5)
                .IsOptional();

            Property(x => x.DataHoraUltimaAtualizacao)
                .IsRequired();
            
            Property(t => t.NomeMae)
                .IsOptional()
                .HasMaxLength(100);
            
            Property(t => t.NomePai)
                .IsOptional()
                .HasMaxLength(100);
        }
    }
}
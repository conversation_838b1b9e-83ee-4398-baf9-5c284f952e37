﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services;
using System;
using System.Web.Http;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class DataMediaServerController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvDataMediaServer _srvDataMediaServer;

        public DataMediaServerController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvDataMediaServer srvDataMediaServer) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvDataMediaServer = srvDataMediaServer;
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(string token, string cnpjAplicacao, string fileToken)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvDataMediaServer.GetMedia(fileToken));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Integrar(string token, string cnpjAplicacao, int dataType, string base64Data, string fileName, string mimeType)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvDataMediaServer.Add(dataType, base64Data, fileName, mimeType));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
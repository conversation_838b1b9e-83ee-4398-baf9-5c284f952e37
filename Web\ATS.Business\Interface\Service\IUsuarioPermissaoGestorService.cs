using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioPermissaoGestorService
    {
        ValidationResult Integrar(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo, bool empresa, bool filial);
        UsuarioPermissaoGestor GetParametroPermissaoGestor(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo);
    }
}
﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class ClienteProdutoEspecieService : ServiceBase, IClienteProdutoEspecieService
    {
        private readonly IClienteProdutoEspecieRepository _clienteProdutoEspecieRepository;
        private readonly IProdutoRepository _produtoRepository;
        private readonly IEspecieRepository _especieRepository;

        public ClienteProdutoEspecieService(IClienteProdutoEspecieRepository clienteProdutoEspecieRepository, IProdutoRepository produtoRepository, IEspecieRepository especieRepository)
        {
            _clienteProdutoEspecieRepository = clienteProdutoEspecieRepository;
            _produtoRepository = produtoRepository;
            _especieRepository = especieRepository;
        }

        public object GetTodosProdutosPorClienteForUiGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            filters = filters.Where(f => f.Campo != "Ativo").ToList();
            var idCliente = Convert.ToInt32(filters.Where(f => f.Campo == "IdCliente")?.FirstOrDefault()?.Valor ?? "0");
            var idEspecie = Convert.ToInt32(filters.Where(f => f.Campo == "IdEspecie")?.FirstOrDefault()?.Valor ?? "0");

            var produtos = new List<ClienteProdutoEspecie>().AsQueryable();
            if (idEspecie == 0)
            {
                produtos = _clienteProdutoEspecieRepository
                                    .Where(x => x.IdCliente == idCliente)
                                    .GroupBy(p => p.IdProduto)
                                    .Select(g => g.FirstOrDefault())
                                    .Include(x => x.Produto)
                                    .Include(x => x.Produto.Empresa);
            }
            else
            {
                produtos = _clienteProdutoEspecieRepository
                                   .Where(x => x.IdCliente == idCliente && x.IdEspecie == idEspecie)
                                   .Include(x => x.Produto)
                                   .Include(x => x.Produto.Empresa);
            }

            if (produtos == null || produtos.Count() == 0)
            {
                var todosProdutos = _produtoRepository.GetAll().Include(x => x.Empresa);
                var listaProdutos = new List<ClienteProdutoEspecie>();
                foreach (var produto in todosProdutos)
                {
                    listaProdutos.Add(new ClienteProdutoEspecie()
                    {
                        IdCliente = idCliente,
                        IdProduto = produto.IdProduto,
                        Produto = new Produto()
                        {
                            IdProduto = produto.IdProduto,
                            Descricao = produto.Descricao,
                            Ativo = produto.Ativo,
                            Empresa = produto.Empresa
                        }
                    });
                }

                produtos = listaProdutos.AsQueryable();
            }

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                produtos = produtos.OrderBy(x => x.IdProduto);
            else
                produtos = produtos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            produtos = produtos.AplicarFiltrosDinamicos<ClienteProdutoEspecie>(filters);

            return new
            {
                totalItems = produtos.Count(),
                items = produtos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdProduto,
                    x.Produto.Descricao,
                    x.Produto.Ativo,
                    Empresa = x.Produto.Empresa?.RazaoSocial
                })
            };
        }

        public object GetTodasEspeciesPorClienteForUiGrid(int Take, int Page, OrderFilters Order, List<QueryFilters> Filters)
        {
            Filters = Filters.Where(f => f.Campo != "Ativo").ToList();
            var idCliente = Convert.ToInt32(Filters.Where(f => f.Campo == "IdCliente")?.FirstOrDefault()?.Valor ?? "0");
            var idProduto = Convert.ToInt32(Filters.Where(f => f.Campo == "IdProduto")?.FirstOrDefault()?.Valor ?? "0");
            var especies = _clienteProdutoEspecieRepository.Where(x => x.IdCliente == idCliente && x.IdProduto == idProduto)
                                                                            .Include(x => x.Especie);

            if (especies == null || especies.Count() == 0)
            {
                var todasEspecies = _especieRepository.GetAll();
                var listaEspecies = new List<ClienteProdutoEspecie>();
                foreach (var especie in todasEspecies)
                {
                    listaEspecies.Add(new ClienteProdutoEspecie()
                    {
                        IdCliente = idCliente,
                        IdProduto = idProduto,
                        IdEspecie = especie.IdEspecie,
                        Especie = new Especie()
                        {
                            IdEspecie = especie.IdEspecie,
                            Descricao = especie.Descricao
                        }
                    });
                }

                especies = listaEspecies.AsQueryable();
            }

            especies = string.IsNullOrWhiteSpace(Order?.Campo)
                   ? especies.OrderBy(x => x.IdEspecie)
                   : especies.OrderBy($"{Order.Campo} {Order.Operador.DescriptionAttr()}");

            especies = especies.AplicarFiltrosDinamicos<ClienteProdutoEspecie>(Filters);

            return new
            {
                totalItems = especies.Count(),
                items = especies.Skip((Page - 1) * Take).Take(Take)
                .ToList().Select(x => new
                {
                    x.IdEspecie,
                    x.Especie.Descricao
                })
            };
        }

        public Especie getEspecieByName(string especieName)
        {

            return _especieRepository.Where(m => m.Descricao == especieName).FirstOrDefault();
        }
    }

}

﻿namespace ATS.WS.Models.Common.Request
{
    public class NotificarPassagemRequestModel
    {
        public string Associate { get; set; }
        public long? BiId { get; set; }
        public string CategoryAxesName { get; set; }
        public string ClassifiedClass { get; set; }
        public int? ContractId { get; set; }
        public string Description { get; set; }
        public string Historic { get; set; }
        public string IdHistoric { get; set; }
        public string InsertDate { get; set; }
        public int? MoveMaisId { get; set; }
        public long? ObuId { get; set; }
        public string OccurrenceDate { get; set; }
        public string Plate { get; set; }
        public long? TagSerialNumber { get; set; }
        public long? TransactionId { get; set; }
        public long? TransferId { get; set; }
        public string MotivoEixoSuspenso { get; set; }
        public string CidadePraca { get; set; }
        public string EstadoPraca { get; set; }
        public decimal? LatitudePraca { get; set; }
        public decimal? LongitudePraca { get; set; }
        public long? IdAnttPraca { get; set; }
        public string IdMoveMaisPraca { get; set; }
        public string Type { get; set; }
        public int? Value { get; set; }
        public int? VehicleId { get; set; }
        public string CnpjEmissorVp { get; set; }
        public string NomeEmissorVp { get; set; }
        public string ProcotocoloVp { get; set; }
    }
}
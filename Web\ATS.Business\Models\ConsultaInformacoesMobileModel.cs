using System;
using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class ConsultaInformacoesMobileModel
    {
        public int TotalRegistros { get; set; }

        public List<ConsultaInformacoesMobileItemsModel> Registros { get; set; }
    }

    public class ConsultaInformacoesMobileItemsModel
    {
        public int UsuarioId { get; set; }

        public string UsuarioNome { get; set; }
        
        public DateTime? DataUltimoLogin { get; set; }

        public DateTime? DataUltimoAcesso { get; set; }

        public DateTime? DataUltimoCheckin { get; set; }
        
        // Calendário
        public bool PermiteLerCalendario { get; set; }

        public bool PermiteEscreverCalendario { get; set; }

        // Câmera
        public bool PermiteUsarCamera { get; set; }
        
        // Perfil
        public bool PermiteLerPerfil { get; set; }

        // Contatos
        public bool PermiteLerContatos { get; set; }
        
        public bool PermiteEscreverContatos { get; set; }
        
        public bool PermiteBuscarContatos { get; set; }

        // Localização
        public bool PermiteAcessoLocalizacao { get; set; }

        // Microfone
        public bool PermiteGravarAudio { get; set; }

        // Telefone
        public bool PermiteLerEstadoTelefone { get; set; }
        
        public bool PermiteRealizarChamada { get; set; }
        
        public bool PermiteLerLogChamada { get; set; }
        
        public bool PermiteEscreverLogChamada { get; set; }
        
        public bool PermiteAdicionarCorreioVoz { get; set; }
        
        public bool PermiteUsarSip { get; set; }
        
        public bool PermiteProcessarChamadaSaida { get; set; }

        // Sensores
        public bool PermiteUsarSensoresComporais { get; set; }

        // SMS
        public bool PermiteEnviarSms { get; set; }
        
        public bool PermiteReceberSms { get; set; }
        
        public bool PermiteLerSms { get; set; }
        
        public bool PermiteReceberWapPush { get; set; }
        
        public bool PermiteReceberMms { get; set; }

        // Storage
        public bool PermiteLerArmazenamentoExterno { get; set; }
        
        public bool PermiteEscreverArmazenamentoExterno { get; set; }
        
        // Sistema
        public bool PermiteReceberConfirmacaoInicio { get; set; }
        public bool PermiteIgnorarOtimizacoesBateria { get; set; }
        public bool PermiteImpedirBloqueio { get; set; }
        public bool PermiteJanelasOverlay { get; set; }
        public bool PermiteForeground { get; set; }
        public bool PermiteInternet { get; set; }
        public bool PermiteAcessarInformacoesRede { get; set; }
    }
}
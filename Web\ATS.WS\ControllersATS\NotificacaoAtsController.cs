﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class NotificacaoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly INotificacaoApp _notificacaoApp;

        public NotificacaoAtsController(IUserIdentity userIdentity, INotificacaoApp notificacaoApp)
        {
            _userIdentity = userIdentity;
            _notificacaoApp = notificacaoApp;
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ObterTotalMensagensNovasPorUsuario(int idUsuario)
        {
            var totalNotificacoesNaoLidas = _notificacaoApp.ObterNotificacoesNaoLidas(idUsuario).ToList().Count();

            return ResponderSucesso(totalNotificacoesNaoLidas);
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ObterMensagensNovasPorUsuario(int idUsuario, bool todas = false)
        {
            var notificacoesNaoLidasRet = new List<object>();
            var notificacoesNaoLidas = _notificacaoApp
                                            .ObterNotificacoesNaoLidas(idUsuario);

            foreach (var notfi in notificacoesNaoLidas.OrderByDescending(x => x.IdNotificacao))
                notificacoesNaoLidasRet.Add(new
                {
                    notfi.IdNotificacao,
                    notfi.Conteudo,
                    notfi.DataHoraEnvio,
                    notfi.Link,
                    TempoMinutosCorridoNotificacao = (DateTime.Now - notfi.DataHoraEnvio).TotalMinutes
                });

            return ResponderSucesso(new
            {
                Notificacoes = notificacoesNaoLidasRet,
                TotalMensagens = notificacoesNaoLidas.Count()
            });
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult MarcarNotificacaoLida(int idNotificacao)
        {
            try
            {
                var retorno = _notificacaoApp.SetWithLido(idNotificacao);
                return !retorno.IsValid ? ResponderErro(retorno.ToFormatedMessage())
                    : ResponderSucesso("Notificações atualizadas com sucesso! ");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult MarcarTodasNotificacoesLida(List<int> idsNotificacao)
        {
            try
            {
                if (idsNotificacao == null) return ResponderErro("Não foi possível identificar os parâmetros. ");

                foreach (var idNotificacao in idsNotificacao)
                    _notificacaoApp.SetWithLido(idNotificacao);

                return ResponderSucesso("Noficações atualizadas com sucesso! ");

            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [AcceptVerbs("GET", "POST")]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetNotificacoes(int? IdUsuarioLogado)
        {
            try
            {
                int idUsuario = IdUsuarioLogado == null ? _userIdentity.IdUsuario : IdUsuarioLogado.Value;

                if (_userIdentity.IdUsuario < 1)
                    return ResponderSucesso(new object());

                var queryNotificacao = _notificacaoApp.GetNotificacoesPeloUsuario((int)idUsuario);
                var count = queryNotificacao.Where(x => x.Lida == false).Count();

                queryNotificacao = queryNotificacao
                    .OrderByDescending(x => x.IdNotificacao)
                    .Take(20).Where(x => !x.RecebidaNovo);

                var dataResult = queryNotificacao
                    .Select(m => new
                    {
                        m.IdNotificacao,
                        m.Tipo,
                        m.DataHoraEnvio,
                        m.Conteudo,
                        Recebida = m.RecebidaNovo,
                        m.Lida,
                        m.Link
                    }).ToList();

                if (dataResult.Count > 0)
                    _notificacaoApp.SetRecebidoNovo(dataResult.Select(x => x.IdNotificacao).ToList());

                var retorno = new
                {
                    count,
                    notificacoes = dataResult
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }

        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetAll(int? take, int? skip, int? IdUsuarioLogado = null)
        {
            try
            {
                int idUsuario = IdUsuarioLogado == null ? _userIdentity.IdUsuario : IdUsuarioLogado.Value;

                var queryableNotificacao = _notificacaoApp.GetNotificacoesPeloUsuario(idUsuario);
                int count = queryableNotificacao.Where(x => x.Lida == false).Count();
                var dataResult = queryableNotificacao
                .OrderByDescending(x => x.IdNotificacao)
                    .Select(m => new
                    {
                        m.IdNotificacao,
                        m.Tipo,
                        m.DataHoraEnvio,
                        m.Conteudo,
                        Recebida = m.RecebidaNovo,
                        m.Lida,
                        m.Link
                    })
                    .ToList();
                if (skip.HasValue)
                    dataResult = dataResult.Skip(skip.Value).ToList();
                if (take.HasValue)
                    dataResult = dataResult.Take(take.Value).ToList();

                _notificacaoApp.SetRecebidoNovo(dataResult.Where(x => !x.Recebida).Select(x => x.IdNotificacao).ToList());

                var retorno = new
                {
                    count,
                    notificacoes = dataResult
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }

        }
    }
}
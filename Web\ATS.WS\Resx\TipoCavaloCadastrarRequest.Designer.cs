﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ATS.WS.Resx {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "*******")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class TipoCavaloCadastrarRequest {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TipoCavaloCadastrarRequest() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ATS.WS.Resx.TipoCavaloCadastrarRequest", typeof(TipoCavaloCadastrarRequest).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo Capacidade caso preenchido deve ser maior que 1.
        /// </summary>
        internal static string Capacidade {
            get {
                return ResourceManager.GetString("Capacidade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo Categoria está inválido.
        /// </summary>
        internal static string CategoriaInvalida {
            get {
                return ResourceManager.GetString("CategoriaInvalida", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo Nome deve ter entre 1 e 150 caracteres.
        /// </summary>
        internal static string NomeTamanhoMax {
            get {
                return ResourceManager.GetString("NomeTamanhoMax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo Nome não pode estar vazio.
        /// </summary>
        internal static string NomeVazio {
            get {
                return ResourceManager.GetString("NomeVazio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo Número de eixos caso preenchido deve ser maior que 1.
        /// </summary>
        internal static string NumeroEixos {
            get {
                return ResourceManager.GetString("NumeroEixos", resourceCulture);
            }
        }
    }
}

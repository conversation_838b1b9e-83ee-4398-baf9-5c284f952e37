﻿namespace ATS.Domain.Entities
{
    /// <summary>
    /// Grupos de usuários para envio de mensagens 
    /// Apenas para vincular a mensagem ao grupo que possui o usuário definido
    /// </summary>
    public class GruposUsuarios 
    {
        /// <summary>
        /// Chave primaria da tabela
        /// </summary>
        public int IdGrupoUsuario { get; set; }

        /// <summary>
        /// Id da mensagem
        /// </summary>
        public int IdMensagem { get; set; }

        /// <summary>
        /// Id do grupo de usuário que representa esta mensagem
        /// </summary>
        public int IdMensagemGrupoUsuario { get; set; }

        #region Referências
        
        public virtual Mensagem MensagemVinculada { get; set; }

        public virtual MensagemGrupoUsuario MensagemGruposUsuarios { get; set; }

        #endregion
    }
}

﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CredenciamentoMap : EntityTypeConfiguration<Credenciamento>
    {
        public CredenciamentoMap()
        {
            ToTable("CREDENCIAMENTO");

            Has<PERSON>ey(x => x.IdCredenciamento);

            HasRequired(x => x.Empresa)
                .WithMany(x => x.Credenciamentos)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Estabelecimento)
                .WithMany(x => x.Credenciamentos)
                .HasForeignKey(x => x.IdEstabelecimento);

            HasOptional(x => x.EstabelecimentoBase)
                .WithMany(x => x.Credenciamentos)
                .HasForeignKey(x => x.IdEstabelecimentoBase);

            HasOptional(x => x.Motivo)
                .WithMany(x => x.Credenciamentos)
                .HasForeignKey(x => x.IdMotivo);
        }
    }
}

﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class MigrationDesvinculoCiot : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.DECLARACAO_CIOT", "idviagemciotdesvinculado", c => c.Int());
            AddColumn("dbo.DECLARACAO_CIOT", "idusuariodesvinculo", c => c.Int());
            AddColumn("dbo.DECLARACAO_CIOT", "datadesvinculo", c => c.DateTime());
            CreateIndex("dbo.DECLARACAO_CIOT", "idusuariodesvinculo", name: "IX_IdUsuarioDesvinculo");
            AddForeignKey("dbo.DECLARACAO_CIOT", "idusuariodesvinculo", "dbo.USUARIO", "idusuario");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.DECLARACAO_CIOT", "idusuariodesvinculo", "dbo.USUARIO");
            DropIndex("dbo.DECLARACAO_CIOT", "IX_IdUsuarioDesvinculo");
            DropColumn("dbo.DECLARACAO_CIOT", "datadesvinculo");
            DropColumn("dbo.DECLARACAO_CIOT", "idusuariodesvinculo");
            DropColumn("dbo.DECLARACAO_CIOT", "idviagemciotdesvinculado");
        }
    }
}

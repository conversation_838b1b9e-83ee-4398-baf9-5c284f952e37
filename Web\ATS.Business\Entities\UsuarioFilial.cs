﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class UsuarioFilial
    {
        public int IdUsuario { get; set; }
        public int IdFilial  { get; set; }

        [ForeignKey("IdUsuario")]
        public virtual Usuario Usuario { get; set; }

        [ForeignKey("IdFilial")]
        public virtual Filial  Filial  { get; set; }
    }
}
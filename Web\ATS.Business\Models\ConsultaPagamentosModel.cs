﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.Models
{
    public class ConsultaPagamentosModel
    {
        public IEnumerable<ConsultaPagamentosItemModel> items { get; set; }
        public PagamentosPorViagemEventoModel totais { get; set; }
        public int totalItems { get; set; }
    }

    public class ConsultaPagamentosItemModel
    {
        public string CnpjEmpresa { get; set; }
        public string NomeEmpresa { get; set; }
        public int? IdViagem { get; set; }
        public int? IdViagemEvento { get; set; }
        public int? IdCargaAvulsa { get; set; }
        public DateTime DataLancamento { get; set; }
        public string DataLancamentoFormatted => DataLancamento.ToString("G");
        public DateTime? DataHoraPagamento { get; set; }
        public string DataHoraPagamentoFormatted => DataHoraPagamento?.ToString("G");
        public decimal ValorPagamento { get; set; }
        public string ValorPagamentoFormatted => ValorPagamento.FormatMoney();
        public bool PagamentoCartao { get; set; }
        public string PagamentoCartaoFormatted => PagamentoCartao ? "Sim" : "Não";
        public string Placa { get; set; } = "Não informado.";
        public EConsultaPagamentosModelTipo Tipo { get; set; }
        public string TipoFormatted => Tipo.GetDescription();
        public EStatusViagemEvento Status { get; set; }
        public string StatusFormatted => Status.GetDescription();
        public FornecedorEnumPagamento? Fornecedor { get; set; }
        public string FornecedorFormatted => Fornecedor.GetDescription();
        public string DocumentoCliente { get; set; }
        public string CIOT { get; set; }
        public string CpfMotorista { get; set; }
        public string NomeMotorista { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public int? IdFilial { get; set; }
        public string CnpjFilial { get; set; }
        public string Filial { get; set; }
    }
}
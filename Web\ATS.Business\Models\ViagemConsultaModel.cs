﻿using System;
using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Service;
using Newtonsoft.Json;

namespace ATS.Domain.Models
{
    public class ViagemConsultaCarretaModel
    {
        public string Placa { get; set; }
    }
    
    public class ViagemConsultaModel
    {
        public int IdViagem { get; set; }

        public int IdEmpresa { get; set; }

        public string RazaoSocialEmpresa { get; set; }

        public string PaisClienteOrigem { get; set; }

        public string UFClienteOrigem { get; set; }

        public string CidadeClienteOrigem { get; set; }

        public string PaisClienteDestino { get; set; }

        public string UFClienteDestino { get; set; }

        public string CidadeClienteDestino { get; set; }

        public string PaisClienteTomador { get; set; }
        public string UFClienteTomador { get; set; }
        public string CidadeClienteTomador { get; set; }

        public string NomeProprietario { get; set; }
        public string DocumentoProprietario { get; set; }
        public string RNTRCProprietario { get; set; }
        public CartaoIdentificacaoDto CartaoProprietario { get; set; }
        public string Placa { get; set; }
        public IList<ViagemConsultaCarretaModel> Carretas { get; set; }

        public int QuantidadeCargas { get; set; }

        public string NumeroDocumento { get; set; }

        public string DataEmissao { get; set; }

        public string Produto { get; set; }

        public EUnidadeMedida Unidade { get; set; }

        public decimal Quantidade { get; set; }
        
        public string CPFCNPJClienteTomador { get; set; }
        
        public bool PedagioBaixado { get; set; } = false;
        
        public decimal ValorPedagio { get; set; } = 0;

        // Impostos
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal IRRPF { get; set; } = 0;
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal INSS { get; set; } = 0;
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal SESTSENAT { get; set; } = 0;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EStatusCheckViagem? StatusUltimoCheckViagem { get; set; } = null;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EStatusViagem StatusViagem { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string TokenViagem { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? PesoChegada { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? PesoSaida { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? PesoDiferenca { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorMercadoria { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorDifFreteMotorista { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorQuebraMercadoria { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DataLancamento { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? NaturezaCarga { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DataPrevisaoEntrega { get; set; }
        
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]        
        public string NumeroControle { get; set; }

        public List<ViagemRegrasConsultaModel> ViagemRegras { get; set; }
        public List<ViagemEstabelecimentoConsultaModel> ViagemEstabelecimentos { get; set; }
        public List<ViagemEventoConsultaModel> ViagemEventos { get; set; }
        public List<ViagemConsultaDocumentoFiscalModel> ViagemDocumentosFiscais { get; set; }

        // ReSharper disable once InconsistentNaming
        public DeclararCiotResult CIOT { get; set; }

        public SolicitarCompraPedagioResponseDTO Pedagio { get; set; }

        public string CepOrigem { get; set; }

        public string CepDestino { get; set; }

        public int? CodigoTipoCarga { get; set; }

        public int? DistanciaViagem { get; set; }

        public ViagemDadosPagamentoConsultaModel DadosPagamento { get; set; }

        public string CnpjFilial { get; set; }
    }

    public class ViagemRegrasConsultaModel
    {
        public decimal? TaxaAntecipacao { get; set; }
        public decimal? ToleranciaPeso { get; set; }
        public decimal? TarifaTonelada { get; set; }
        public ETipoQuebraMercadoria TipoQuebraMercadoria { get; set; }
    }

    public class ViagemEstabelecimentoConsultaModel
    {
        public int IdViagemEstabelecimento { get; set; }
        public int IdEstabelecimento { get; set; }
        public int IdViagem { get; set; }
        public ETipoEventoViagem TipoEventoViagem { get; set; }
    }

    public class ViagemConsultaDocumentoFiscalModel
    {
        public int? IdViagemDocumentoFiscal { get; set; }

        public decimal NumeroDocumento { get; set; }

        public string Serie { get; set; }

        public decimal PesoSaida { get; set; }

        public decimal? Valor { get; set; }
        
        public int? IdClienteOrigem { get; set; }
        
        public int? IdClienteDestino { get; set; }

        public ETipoDocumento TipoDocumento { get; set; }
        public string Chave { get; set; }
    }

    public class ViagemEventoConsultaModel
    {
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public int? IdProtocolo { get; set; }
        public int? IdEstabelecimentoBase { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public decimal ValorPagamento { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public decimal ValorBruto { get; set; }
        
        /// <summary>
        /// Propriedade com nome ambiguo, esta propriedade indica que o posto solicitou o abono.
        /// Para CF: O posto assume o risco e já paga o abono. Caso haver problemas a triagem irá descontar da fatura do posto;
        /// Para MH: O posto solicita o pagamento do abono, mas não paga o valor da quebra neste momento. A triagem irá finalizar o processo na analise do protocolo e decidir se confirma ou recusa a solicitação.
        /// </summary>
        public decimal? ValorQuebraAbonada { get; set; }
        public bool HabilitarPagamentoCartao { get; set; }
        public string DataPagamento { get; set; }
        public string HoraPagamento { get; set; }
        public string DataValidade { get; set; }
        public string NumeroRecibo { get; set; }
        public string NumeroControle { get; set; }
        public string Instrucao { get; set; }
        public string Token { get; set; }
        public EStatusViagemEvento Status { get; set; }
        public decimal? PesoChegada { get; set; }
        public decimal? PesoDiferenca { get; set; }
        public decimal? ValorDifFreteMotorista { get; set; }
        public decimal? ValorQuebraMercadoria { get; set; }

        public List<ViagemDocumentoConsultaModel> ViagemDocumentos { get; set; }
        public List<ViagemValorAdicionalConsultaModel> ViagemOutrosDescontos { get; set; }
        public List<ViagemValorAdicionalConsultaModel> ViagemOutrosAcrescimos { get; set; }
        public decimal SESTSENAT { get; set; }
        public decimal IRRPF { get; set; }
        public decimal INSS { get; set; }
        public EOrigemIntegracao OrigemPagamento { get; set; }                
        public string DataDescarga { get; set; }
    }

    public class ViagemDocumentoConsultaModel
    {
        public int IdDocumento { get; set; }
        public int IdEvento { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public string Descricao { get; set; }
        public ETipoDocumento TipoDocumento { get; set; }
        public int NumeroDocumento { get; set; }
        public bool ObrigaAnexo { get; set; }
        public bool ObrigaDocOriginal { get; set; }
    }

    public class ViagemValorAdicionalConsultaModel
    {
        public int IdViagemOutrosDescontos { get; set; }
        public int NumeroDocumento { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
    }

    public class StatusOperacaoCartaoModel
    {
        public StatusOperacaoCartaoModel()
        {
        }

        public StatusOperacaoCartaoModel(ERetornoOperacaoCartao status, string mensagem)
        {
            Status = status;
            Mensagem = mensagem;
        }

        public ERetornoOperacaoCartao Status { get; set; }
        public string Mensagem { get; set; }
    }


    public class ViagemMotoristasModel
    {
        public string Cpf { get; set; }
        public string Nome { get; set; }
        public DateTime? UltimaDataLancamento { get; set; }
    }

    public class ViagemDadosPagamentoConsultaModel
    {
        public EViagemFormaPagamento FormaPagamento { get; set; }

        public string CodigoBacen { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }
    }
}

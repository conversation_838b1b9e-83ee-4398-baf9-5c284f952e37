﻿using ATS.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class AutorizacaoEmpresaRepository : Repository<AutorizacaoEmpresa>, IAutorizacaoEmpresaRepository
    {
        public AutorizacaoEmpresaRepository(AtsContext context) : base(context)
        {
        }
    }
}

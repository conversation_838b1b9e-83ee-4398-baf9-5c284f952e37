﻿using System;
using System.Collections.Generic;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioGridExtratoDetalhadoDTO : FiltrosGridBaseModel
    {
        public int IdUsuarioLogado { get; set; }
        public int Produto { get; set; }
        public int Identificador { get; set; }
        public DateTime DataFim { get; set; }
        public DateTime DataInicio { get; set; }
        public int Dias { get; set; } = 30;
        public new int Take { get; set; }
        public new int Page { get; set; }
    }
}
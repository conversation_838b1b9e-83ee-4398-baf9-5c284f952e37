﻿using ATS.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class UsuarioEndereco : EnderecoBase
    {
        /// <summary>
        /// Código do usuário
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Código do endereço
        /// </summary>
        public int IdEndereco { get; set; }

        #region Referências

        /// <summary>
        /// Usuário
        /// </summary>
        public virtual Usuario Usuario { get; set; }

        #endregion
    }
}
﻿using ATS.Domain.Enum;
using System;

namespace ATS.WS.Models.Webservice.Request.TipoCarreta
{
    public class CadastroTipoCarretaRequestModel
    {
        public int IdTipoCarreta { get; set; }
        public string Nome { get; set; }
        public ECategoriaTipoCarreta Categoria { get; set; }
        public int? QtdeEixos { get; set; }
        public int? MetrosCubicos { get; set; }
        public bool? EixosEspacados { get; set; }
        public bool Ativo { get; set; } = true;
        public int? IdEmpresa { get; set; }
        public DateTime? DataHoraUltimaAtualizacao { get; set; }
    }
}
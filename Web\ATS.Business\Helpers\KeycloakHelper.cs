﻿using System;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http;
using System.Net;
using System.Threading.Tasks;
using System.Linq;
using System.Web.Configuration;
using System.Security.Authentication;
using ATS.Domain.Models;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using Org.BouncyCastle.Asn1.X9;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Security;
using System.IO;
using Org.BouncyCastle.OpenSsl;
using System.Net.Http.Headers;
using System.Collections.Specialized;
using NLog;

namespace ATS.Domain.Helpers
{
    public class KeycloakHelper
    {
        public static Logger _logger = LogManager.GetCurrentClassLogger();


        private KeycloakOpenIdTokenModel userAccessToken = null;
        private readonly KeycloakSettings settings;
        private KeycloakOpenIdTokenModel clientAccessToken = null;

        public KeycloakHelper()
        {
            settings = new KeycloakSettings();
            settings.OpenIdConnectUrl = WebConfigurationManager.AppSettings["KC_OPENID_CONNECT_URL"];
            settings.AuthorizationUrl = WebConfigurationManager.AppSettings["KC_AUTHORIZATION_URL"];
            settings.TokenUrl = WebConfigurationManager.AppSettings["KC_TOKEN_URL"];
            settings.LogoutUrl = WebConfigurationManager.AppSettings["KC_LOGOUT_URL"];
            settings.ScopesUrl = WebConfigurationManager.AppSettings["KC_SCOPES_URL"];
            settings.UsersUrl = WebConfigurationManager.AppSettings["KC_USERS_URL"];
            settings.ClientsUrl = WebConfigurationManager.AppSettings["KC_CLIENTS_URL"];
            settings.Manage2faUrl = WebConfigurationManager.AppSettings["KC_MANAGE2FA_URL"];
            settings.ClientId = WebConfigurationManager.AppSettings["KC_CLIENT_ID"];
            settings.ClientSecret = WebConfigurationManager.AppSettings["KC_CLIENT_SECRET"];
            settings.PublicKey = WebConfigurationManager.AppSettings["KC_PUBLICK_KEY"];
            settings.DefaultResourceId = WebConfigurationManager.AppSettings["KC_DEFAULT_RESOURCE_ID"];
            settings.User = WebConfigurationManager.AppSettings["KC_USER"];
            settings.Password = WebConfigurationManager.AppSettings["KC_PASSWORD"];
            settings.RedirectUri = WebConfigurationManager.AppSettings["KC_REDIRECT_URI"];
        }

        private static RSAParameters ConvertPemToRSAParameters(string pemString)
        {
            using (var stringReader = new StringReader(pemString))
            {
                var pemReader = new PemReader(stringReader);
                RsaKeyParameters rsaKeyParameters = (RsaKeyParameters)pemReader.ReadObject();
                return DotNetUtilities.ToRSAParameters(rsaKeyParameters);
            }
        }

        public bool ValidateToken(string jwtToken, out SecurityToken validatedToken, out ClaimsPrincipal tokenClaims)
        {
            validatedToken = null;
            tokenClaims = null;

            var tokenHandler = new JwtSecurityTokenHandler();

            RSA rsa = RSA.Create();
            rsa.ImportParameters(ConvertPemToRSAParameters(
                "-----BEGIN PUBLIC KEY-----" + (char)13 +
                settings.PublicKey + (char)13 +
                "-----END PUBLIC KEY-----" + (char)13
                ));

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new RsaSecurityKey(rsa),
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            //vai levantar exceção no caso de token inválido ou expirado
            tokenClaims = tokenHandler.ValidateToken(jwtToken, validationParameters, out validatedToken);
            return true;
        }

        public async Task<KeycloakClientModel> GetClientAsync(string clientId)
        {
            clientId = clientId.Trim().ToLower();
            var token = await GetClientAccessTokenAsync("");
            var httpClient = new HttpClient(new HttpClientHandler() { SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls });
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);
            KeycloakClientModel client = null;
            var res = await httpClient.GetAsync(settings.ClientsUrl + "?clientId=" + clientId);
            if (res.StatusCode == HttpStatusCode.OK)
            {
                string content = await res.Content.ReadAsStringAsync();
                var list = JsonConvert.DeserializeObject<List<KeycloakClientModel>>(content);
                if (list.Count > 0)
                {
                    foreach (var item in list)
                    {
                        if (item.ClientId.Equals(clientId))
                        {
                            client = item;
                            break;
                        }
                    }
                }
            }
            else
            {
                if (res.StatusCode != HttpStatusCode.NotFound)
                {
                    _logger.Error("KEYCLOAK - ERRO NO METODO GetClientAsync: {res}", res);
                    res.EnsureSuccessStatusCode();
                }
            }
            return client;
        }

        public KeycloakClientModel GetClient(string clientId)
        {
            clientId = clientId.Trim().ToLower();
            var token = GetClientAccessToken("");
            var apiUrl = $"{settings.ClientsUrl}?clientId={WebUtility.UrlEncode(clientId)}";
            ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
            using (WebClient client = new WebClient())
            {
                client.Headers[HttpRequestHeader.Authorization] = $"Bearer {token.AccessToken}";
                try
                {
                    string content = client.DownloadString(apiUrl);
                    var list = JsonConvert.DeserializeObject<List<KeycloakClientModel>>(content);
                    if (list != null && list.Count > 0)
                    {
                        foreach (var item in list)
                        {
                            if (item.ClientId.Equals(clientId))
                            {
                                return item;
                            }
                        }
                    }
                    return null;
                }
                catch (Exception ex)
                {
                    var msg = ex.ToString();
                    _logger.Error("Keycloak erro no GetClient {clientId}: {msg}", clientId, msg);
                }
            }
            return null;
        }

        public async Task<HttpStatusCode> CreateClientAsync(string clientId, string name)
        {
            clientId = clientId.Trim().ToLower();
            var token = await GetClientAccessTokenAsync("");
            var httpClient = new HttpClient(new HttpClientHandler() { SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls });
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);
            string content = @"
                {
                    ""protocol"": ""openid-connect"",
                    ""clientId"": """ + clientId + @""",
                    ""name"": """ + name + @""",
                    ""description"": """",
                    ""publicClient"": false,
                    ""authorizationServicesEnabled"": false,
                    ""serviceAccountsEnabled"": true,
                    ""implicitFlowEnabled"": false,
                    ""directAccessGrantsEnabled"": false,
                    ""standardFlowEnabled"": false,
                    ""frontchannelLogout"": false,
                    ""alwaysDisplayInConsole"": false,
                    ""attributes"": {
                        ""oauth2.device.authorization.grant.enabled"": false,
                        ""oidc.ciba.grant.enabled"": false
                    }
                }";
            var req = new StringContent(content, Encoding.UTF8, "application/json");
            var res = await httpClient.PostAsync(settings.ClientsUrl, req);
            return res.StatusCode;
        }

        public HttpStatusCode CreateClient(string clientId, string name)
        {
            clientId = clientId.Trim().ToLower();
            var token = GetClientAccessToken("");
            using (WebClient client = new WebClient())
            {
                client.Headers[HttpRequestHeader.Authorization] = $"Bearer {token.AccessToken}";
                client.Headers[HttpRequestHeader.ContentType] = "application/json";
                string content = @"
                {
                    ""protocol"": ""openid-connect"",
                    ""clientId"": """ + clientId + @""",
                    ""name"": """ + name + @""",
                    ""description"": """",
                    ""publicClient"": false,
                    ""authorizationServicesEnabled"": false,
                    ""serviceAccountsEnabled"": true,
                    ""implicitFlowEnabled"": false,
                    ""directAccessGrantsEnabled"": false,
                    ""standardFlowEnabled"": false,
                    ""frontchannelLogout"": false,
                    ""alwaysDisplayInConsole"": false,
                    ""attributes"": {
                        ""oauth2.device.authorization.grant.enabled"": false,
                        ""oidc.ciba.grant.enabled"": false
                    }
                }";
                byte[] requestData = Encoding.UTF8.GetBytes(content);
                byte[] responseData = client.UploadData(settings.ClientsUrl, "POST", requestData);
                string responseBody = Encoding.UTF8.GetString(responseData);
                return HttpStatusCode.OK;
            }
        }

        public bool TokenHasRole(string accessToken, string role)
        {
            var accessTokenPayload = accessToken.Split('.')[1];
            while (accessTokenPayload.Length % 4 != 0) { accessTokenPayload += '='; }
            byte[] data = Convert.FromBase64String(accessTokenPayload);
            string decodedString = System.Text.Encoding.UTF8.GetString(data);
            var token = JsonConvert.DeserializeObject<KeycloakAccessTokenModel>(decodedString);
            return token.realm_access.roles.Contains(role);
        }

        public List<KeycloakScopeModel> GetClientScopes()
        {
            var token = GetClientAccessToken("");
            ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

            using var client = new WebClient
            {
                Headers = {[HttpRequestHeader.Authorization] = $"Bearer {token.AccessToken}"}
            };

            try
            {
                var content = client.DownloadString(settings.ScopesUrl);
                var result = JsonConvert.DeserializeObject<List<KeycloakScopeModel>>(content);
                return result;
            }
            catch (Exception ex)
            {
                var msg = ex.ToString();
                _logger.Error("KeycloakHelper erro: {msg}", msg);
            }

            return null;
        }

        private KeycloakScopeModel GetClientScopeByName(string scopeName)
        {
            List<KeycloakScopeModel> list = GetClientScopes();
            if (list != null && list.Count > 0)
            {
                foreach (var item in list)
                {
                    if (item.Name.Equals(scopeName))
                    {
                        return item;
                    }
                }
            }
            return null;
        }

        private void UpdateScopeType(string scopeName, string scopeType)
        {
            using (var client = new WebClient())
            {
                try
                {
                    KeycloakScopeModel scope = GetClientScopeByName(scopeName);
                    if (scope == null) return;

                    var token = GetClientAccessToken("");
                    client.Headers.Add(HttpRequestHeader.Authorization, $"Bearer {token.AccessToken}");
                    client.Headers[HttpRequestHeader.ContentType] = "application/json";
                    client.Headers[HttpRequestHeader.Accept] = "application/json";

                    string apiUrl = "";
                    if (scopeType.Equals("default")) {
                        apiUrl = settings.DefaultClientScopesUrl + "/" + scope.Id;
                    } else
                    {
                        apiUrl = settings.OptionalClientScopesUrl + "/" + scope.Id;
                    }

                    //deleta
                    byte[] responseBytes = client.UploadData(apiUrl, "DELETE", new byte[0]);
                    string responseString = System.Text.Encoding.UTF8.GetString(responseBytes);

                    //cria
                    responseBytes = client.UploadData(apiUrl, "PUT", new byte[0]);
                    responseString = System.Text.Encoding.UTF8.GetString(responseBytes);
                }
                catch (Exception ex)
                {
                    var msg = ex.ToString();
                    _logger.Error("KeycloakHelper erro: {msg}", msg);
                    throw;
                }
            }
        }

        public void AddClientScope(string scopeName, string scopeDescription, string scopeType)
        {
            using (var client = new WebClient())
            {
                try
                {
                    var token = GetClientAccessToken("");
                    client.Headers.Add(HttpRequestHeader.Authorization, $"Bearer {token.AccessToken}");
                    string content = @"
                        {
                            ""name"": """ + scopeName + @""",
                            ""description"": """ + scopeDescription + @""",
                            ""protocol"": ""openid-connect""
                        }";
                    client.Headers[HttpRequestHeader.ContentType] = "application/json";
                    byte[] requestData = Encoding.UTF8.GetBytes(content);
                    byte[] responseData = client.UploadData(settings.ScopesUrl, "POST", requestData);
                    string responseBody = Encoding.UTF8.GetString(responseData);

                    if (!string.IsNullOrEmpty(scopeType)) UpdateScopeType(scopeName, scopeType);
                    
                    Console.WriteLine("Keycloak scope registered: " + scopeName);
                }
                catch (Exception ex)
                {
                    var msg = ex.ToString();
                    _logger.Error("KeycloakHelper erro: {msg}", msg);
                    throw;
                }
            }
        }

        public async Task AddClientScopeAsync(string scopeName, string scopeDescription, string scopeType)
        {
            var httpClient = new HttpClient();
            var token = await GetClientAccessTokenAsync("");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);
            var scope = new KeycloakScopeModel
            {
                Name = scopeName,
                Description = scopeDescription,
                Protocol = "openid-connect",
                Type = scopeType
            };
            var dados = JsonConvert.SerializeObject(scope, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            var req = new StringContent(dados, Encoding.UTF8, "application/json");
            var res = await httpClient.PostAsync(settings.ScopesUrl, req);
            if (res.StatusCode == HttpStatusCode.Created)
            {
                UpdateScopeType(scopeName, scopeType);
                Console.WriteLine("Keycloak scope registered: " + scopeName);
            }
            else if ((res.StatusCode != HttpStatusCode.OK) && (res.StatusCode != HttpStatusCode.Created) &&
                     (res.StatusCode != HttpStatusCode.Conflict))
            {
                _logger.Error("KEYCLOAK - ERRO NO METODO AddClientScopeAsync: {res}", res);
                res.EnsureSuccessStatusCode();
            }
        }

        public void AddScopeToClient(string clientId, string scopeName, string scopeType)
        {
            using (var webClient = new WebClient())
            {
                try
                {
                    KeycloakClientModel client = GetClient(clientId);
                    if (client == null) return;

                    if (client.DefaultClientScopes.Any(x => x.Equals(scopeName)) || client.OptionalClientScopes.Any(x => x.Equals(scopeName))) return;
                    
                    KeycloakScopeModel scope = GetClientScopeByName(scopeName);
                    if (scope == null) return;

                    var token = GetClientAccessToken("");
                    webClient.Headers.Add(HttpRequestHeader.Authorization, $"Bearer {token.AccessToken}");
                    webClient.Headers[HttpRequestHeader.ContentType] = "application/json";
                    webClient.Headers[HttpRequestHeader.Accept] = "application/json";

                    string apiUrl = "";
                    if (scopeType.Equals("default"))
                    {
                        apiUrl = settings.ClientsUrl + "/" + client.Id + "/default-client-scopes/" + scope.Id;
                    }
                    else
                    {
                        apiUrl = settings.ClientsUrl + "/" + client.Id + "/optional-client-scopes/" + scope.Id;
                    }

                    byte[] requestData = Encoding.UTF8.GetBytes("{}");
                    byte[] responseBytes = webClient.UploadData(apiUrl, "PUT", requestData);
                    string responseString = System.Text.Encoding.UTF8.GetString(responseBytes);
                }
                catch (Exception e)
                {
                    var msg = e.ToString();
                    _logger.Error("Keycloak erro vincular scopo {scopeName} ao cliente {clientId}: {msg}", scopeName, clientId, msg);
                    throw;
                }
            }
        }



        public void CreateOrUpdateUser(string userName, string name, string email, string password, bool enabled, Dictionary<string, object> attributes, bool tempPass = true)
        {
            Task.Run(async () => await CreateOrUpdateUserAsync(userName, name, email, password, enabled, attributes, tempPass).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public List<KeycloakUserModel> GetAllUsers()
        {
            return Task.Run(async () => await GetAllUsersAsync().ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public void RequireUserActions(string userName, List<string> actions)
        {
            Task.Run(async () => await RequireUserActionsAsync(userName, actions).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public KeycloakOpenIdTokenModel GetUserAccessTokenByCode(string code, string scopes)
        {
            return Task.Run(async () => await GetUserAccessTokenByCodeAsync(code, scopes).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public KeycloakOpenIdTokenModel GetClientAccessToken(string scopes, string clientId = null, string clientSecret = null, bool remember = true)
        {
            return Task.Run(async () => await GetClientAccessTokenAsync(scopes, clientId, clientSecret, remember).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public KeycloakOpenIdTokenModel GetUserAccessToken(string userName, string password, string scopes)
        {
            return Task.Run(async () => await GetUserAccessTokenAsync(userName, password, scopes).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public KeycloakOpenIdTokenModel RefreshUserAccessToken(string refreshToken)
        {
            return Task.Run(async () => await RefreshUserAccessTokenAsync(refreshToken).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public bool LogoutUserSession(string accessToken, string refreshToken)
        {
            return Task.Run(async () => await LogoutUserSessionAsync(accessToken, refreshToken).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        public String Validate2fa(string accessToken, string code)
        {
            return Task.Run(async () => await Validate2faAsync(accessToken, code).ConfigureAwait(false)).GetAwaiter().GetResult();
        }

        private async Task<List<KeycloakUserModel>> GetAllUsersAsync()
        {
            var token = await GetClientAccessTokenAsync("").ConfigureAwait(false);

            using var httpClient = new HttpClient(new HttpClientHandler() { SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls });

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);

            var res = await httpClient.GetAsync(settings.UsersUrl + "?username=&max=999999");

            if (res.StatusCode == HttpStatusCode.OK)
            {
                var content = await res.Content.ReadAsStringAsync().ConfigureAwait(false);
                var list = JsonConvert.DeserializeObject<List<KeycloakUserModel>>(content);
                return list;
            }

            _logger.Error("KEYCLOAK - ERRO NO METODO GetAllUsersAsync: {res}", res);
            res.EnsureSuccessStatusCode();

            return null;
        }

        private async Task CreateOrUpdateUserAsync(string userName, string name, string email, string password, bool enabled, Dictionary<string, object> attributes, bool tempPass = true)
        {
            userName = userName.Trim().ToLower();
            var content = "";
            var firstName = "";
            var lastName = "";
            var parts = name.Split(' ');
            if (parts.Length > 0)
            {
                if (parts.Length > 1)
                {
                    var list = new List<string>();
                    lastName = parts[parts.Length - 1];
                    for (var i = 0; i < (parts.Length - 1); i++)
                    {
                        list.Add(parts[i]);
                    }
                    firstName = string.Join(" ", list.ToArray());
                }
                else
                {
                    firstName = parts[0];
                }
            }

            var token = await GetClientAccessTokenAsync("").ConfigureAwait(false);

            using var httpClient = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);

            var user = new KeycloakUserModel
            {
                username = userName,
                email = email,
                enabled = enabled,
                emailVerified = true,
                firstName = firstName,
                lastName = lastName
            };
            if (password != "")
            {
                user.credentials = new List<KeycloakUserCredential>()
                    {
                        new()
                        {
                            type = "password",
                            value = password,
                            temporary = tempPass
                        }
                    };
            }
            if (attributes != null)
            {
                user.attributes = new Dictionary<string, object>();
                foreach (var attribute in attributes)
                {
                    user.attributes.Add(attribute.Key, attribute.Value);
                }
            }

            content = JsonConvert.SerializeObject(user, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

            var req = new StringContent(content, Encoding.UTF8, "application/json");

            var existUser = await GetUserAsync(userName).ConfigureAwait(false);
            if (existUser != null)
            {
                _logger.Info("Send - CreateOrUpdateUserAsync (PUT): {content}", content);

                //atualizar o usuario
                var res = await httpClient.PutAsync(settings.UsersUrl + "/" + existUser.id, req).ConfigureAwait(false);
                var body = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

                _logger.Info("Response - CreateOrUpdateUserAsync (PUT): {res}, {body}", res, body);

                res.EnsureSuccessStatusCode();
            }
            else
            {
                _logger.Info("Send - CreateOrUpdateUserAsync (POST): {content}", content);

                //cadastrar um usuario novo
                var res = await httpClient.PostAsync(settings.UsersUrl, req).ConfigureAwait(false);
                var body = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

                _logger.Info("Response - CreateOrUpdateUserAsync (POST): {res}, {body}", res, body);

                res.EnsureSuccessStatusCode();
            }
        }

        private async Task RequireUserActionsAsync(string userName, List<string> actions)
        {
            var content = "";

            var existUser = await GetUserAsync(userName).ConfigureAwait(false);

            var token = await GetClientAccessTokenAsync("").ConfigureAwait(false);

            using var httpClient = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);

            var r1 = await httpClient.GetAsync(settings.UsersUrl + "/" + existUser.id + "/credentials").ConfigureAwait(false);

            if (r1.StatusCode == HttpStatusCode.OK)
            {
                content = await r1.Content.ReadAsStringAsync();
                var list = JsonConvert.DeserializeObject<List<KeycloakUserCredentialInfo>>(content);
                if (list.Count > 0)
                {
                    foreach (var item in list.Where(item => item.type.Equals("otp")))
                    {
                        var r2 = await httpClient.DeleteAsync(settings.UsersUrl + "/" + existUser.id + "/credentials/" + item.id).ConfigureAwait(false);

                        if (r2.StatusCode != HttpStatusCode.OK)
                        {
                            _logger.Error("KEYCLOAK - ERRO NO METODO RequireUserActionsAsync r2: {r2}", r2);
                        }
                        r2.EnsureSuccessStatusCode();
                    }
                }
            }
            else
            {
                _logger.Error("KEYCLOAK - ERRO NO METODO RequireUserActionsAsync r1: {r1}", r1);
                r1.EnsureSuccessStatusCode();
            }

            var user = new KeycloakUserModel
            {
                username = userName,
                enabled = true,
                emailVerified = true,
                requiredActions = actions
            };

            content = JsonConvert.SerializeObject(user, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });

            var req = new StringContent(content, Encoding.UTF8, "application/json");

            //atualizar o usuario
            var res = await httpClient.PutAsync(settings.UsersUrl + "/" + existUser.id, req).ConfigureAwait(false);

            if (!res.IsSuccessStatusCode)
            {
                _logger.Error("KEYCLOAK - ERRO NO METODO RequireUserActionsAsync: {res}", res);
            }

            res.EnsureSuccessStatusCode();
        }

        private async Task<KeycloakUserModel> GetUserAsync(string userName)
        {
            userName = userName.Trim().ToLower();

            var token = await GetClientAccessTokenAsync("").ConfigureAwait(false);

            using var httpClient = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);

            KeycloakUserModel user = null;

            var res = await httpClient.GetAsync(settings.UsersUrl + "?username=" + userName).ConfigureAwait(false);

            if (res.StatusCode == HttpStatusCode.OK)
            {
                var content = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

                var list = JsonConvert.DeserializeObject<List<KeycloakUserModel>>(content);

                if (list.Count > 0)
                {
                    foreach (var item in list.Where(item => item.username.Equals(userName)))
                    {
                        user = item;
                        break;
                    }
                }
            }
            else
            {
                _logger.Error("KEYCLOAK - ERRO NO METODO GetUserAsync: {res}", res);
                res.EnsureSuccessStatusCode();
            }

            return user;
        }

        private async Task<KeycloakOpenIdTokenModel> GetClientAccessTokenAsync(string scopes, string clientId = null, string clientSecret = null, bool remember = true)
        {
            KeycloakOpenIdTokenModel result = null;

            if (remember) result = clientAccessToken;

            //ver se mudou a lista de scopos
            var scopeHasChanged = false;

            if (!string.IsNullOrEmpty(scopes) && result != null &&
                !string.IsNullOrEmpty(result?.Scope))
            {
                if (!result.Scope.Contains(scopes))
                {
                    var oldScopes = result.Scope.Split(' ');
                    var newScopes = scopes.Split(' ');
                    var existScopes = oldScopes.Select(x => newScopes.Contains(x));
                    scopeHasChanged = (existScopes.Count() != newScopes.Count());
                }
            }

            //falta checar se mudou a lista de escopos
            if (result == null || result?.ExpiresAt < DateTime.Now.AddSeconds(10) ||
                scopeHasChanged)
            {
                var fields = new List<KeyValuePair<string, string>>
                {
                    new("grant_type", "client_credentials"),
                    new("client_id", clientId ?? settings.ClientId),
                    new("client_secret", clientSecret ?? settings.ClientSecret)
                };

                if (scopes != "")
                    fields.Add(new KeyValuePair<string, string>("scope", scopes));

                using var client = new HttpClient(new HttpClientHandler
                {
                    SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
                });

                var req = new HttpRequestMessage(HttpMethod.Post, settings.TokenUrl)
                {
                    Content = new FormUrlEncodedContent(fields)
                };

                var res = await client.SendAsync(req).ConfigureAwait(false);

                var responseBody = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

                result = JsonConvert.DeserializeObject<KeycloakOpenIdTokenModel>(responseBody);
                result.StatusCode = res.StatusCode;
            }

            if (remember)
                clientAccessToken = result;

            return result;
        }

        private async Task<KeycloakOpenIdTokenModel> GetUserAccessTokenByCodeAsync(string code, string scopes)
        {
            var fields = new List<KeyValuePair<string, string>>
            {
                new ("grant_type", "authorization_code"),
                new ("client_id", settings.ClientId!),
                new ("client_secret", settings.ClientSecret!),
                new ("code", code),
                new ("redirect_uri", settings.RedirectUri!)
            };

            if (scopes != "")
                fields.Add(new KeyValuePair<string, string>("scope", scopes));

            using var client = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            var req = new HttpRequestMessage(HttpMethod.Post, settings.TokenUrl)
            { Content = new FormUrlEncodedContent(fields) };

            var res = await client.SendAsync(req).ConfigureAwait(false);

            var responseBody = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

            if (res.StatusCode == HttpStatusCode.OK)
            {
                userAccessToken = JsonConvert.DeserializeObject<KeycloakOpenIdTokenModel>(responseBody);
                userAccessToken.StatusCode = res.StatusCode;
            }
            else
            {
                _logger.Error("KeycloakHelper: {responseBody}", responseBody);
                userAccessToken = new KeycloakOpenIdTokenModel { StatusCode = res.StatusCode };
            }

            return userAccessToken;
        }

        private async Task<KeycloakOpenIdTokenModel> GetUserAccessTokenAsync(string userName, string password, string scopes)
        {
            //ver se mudou a lista de scopos
            var scopeHasChanged = false;

            if (!string.IsNullOrEmpty(scopes) && userAccessToken != null &&
                !string.IsNullOrEmpty(userAccessToken?.Scope))
            {
                if (!userAccessToken.Scope.Contains(scopes))
                {
                    var oldScopes = userAccessToken.Scope.Split(" "[0]);
                    var newScopes = scopes.Split(" "[0]);
                    var existScopes = oldScopes.Select(x => newScopes.Contains(x));
                    scopeHasChanged = (existScopes.Count() != newScopes.Count());
                }
            }

            //falta checar se mudou a lista de escopos
            if ((userAccessToken != null) && (!(userAccessToken?.ExpiresAt < DateTime.Now.AddSeconds(10))) && !scopeHasChanged)
                return userAccessToken;

            var fields = new List<KeyValuePair<string, string>>
            {
                new ("grant_type", "password"),
                new ("client_id", settings.ClientId),
                new ("client_secret", settings.ClientSecret),
                new ("username", userName),
                new ("password", password)
            };
            if (scopes != "")
                fields.Add(new KeyValuePair<string, string>("scope", scopes));

            using var client = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            var req = new HttpRequestMessage(HttpMethod.Post, settings.TokenUrl)
            {
                Content = new FormUrlEncodedContent(fields)
            };

            var content = string.Join(", ", fields.Select(kvp => $"{kvp.Key}={kvp.Value}"));

            _logger.Info("Send - GetUserAccessTokenByCodeAsync (POST): {content}", content);

            var res = await client.SendAsync(req).ConfigureAwait(false);
            var body = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

            _logger.Info("Response - GetUserAccessTokenByCodeAsync (POST): {res}, {body}", res, body);

            if (res.StatusCode == HttpStatusCode.OK)
            {
                userAccessToken = JsonConvert.DeserializeObject<KeycloakOpenIdTokenModel>(body) ?? new KeycloakOpenIdTokenModel();
                userAccessToken.StatusCode = res.StatusCode;
            }
            else
            {
                userAccessToken = new KeycloakOpenIdTokenModel
                {
                    StatusCode = res.StatusCode
                };
            }

            return userAccessToken;
        }

        private async Task<KeycloakOpenIdTokenModel> RefreshUserAccessTokenAsync(string refreshToken)
        {
            var fields = new List<KeyValuePair<string, string>>
            {
                new("client_id", settings.ClientId),
                new ("client_secret", settings.ClientSecret),
                new ("grant_type", "refresh_token"),
                new ("refresh_token", refreshToken)
            };

            using var client = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            // client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var req = new HttpRequestMessage(HttpMethod.Post, settings.TokenUrl)
            {
                Content = new FormUrlEncodedContent(fields)
            };

            var res = await client.SendAsync(req).ConfigureAwait(false);

            if (res.StatusCode == HttpStatusCode.OK)
            {
                var responseBody = await res.Content.ReadAsStringAsync().ConfigureAwait(false);

                userAccessToken = JsonConvert.DeserializeObject<KeycloakOpenIdTokenModel>(responseBody);
                userAccessToken.StatusCode = res.StatusCode;
            }
            else
            {
                userAccessToken = new KeycloakOpenIdTokenModel
                {
                    StatusCode = res.StatusCode
                };
            }

            return userAccessToken;
        }

        private async Task<bool> LogoutUserSessionAsync(string accessToken, string refreshToken)
        {
            var fields = new List<KeyValuePair<string, string>>
            {
                new ("client_id", settings.ClientId),
                new ("client_secret", settings.ClientSecret),
                new ("refresh_token", refreshToken)
            };

            using var client = new HttpClient(new HttpClientHandler
            {
                SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls
            });

            //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            var req = new HttpRequestMessage(HttpMethod.Post, settings.LogoutUrl)
            {
                Content = new FormUrlEncodedContent(fields)
            };

            var res = await client.SendAsync(req).ConfigureAwait(false);

            return res.StatusCode is HttpStatusCode.OK or HttpStatusCode.NoContent;
        }

        public async Task<String> Validate2faAsync(string accessToken, string code)
        {
            //extrair o username do token
            var accessTokenPayload = accessToken.Split('.')[1];
            while (accessTokenPayload.Length % 4 != 0) { accessTokenPayload += '='; }
            byte[] data = Convert.FromBase64String(accessTokenPayload);
            string decodedString = System.Text.Encoding.UTF8.GetString(data);
            var userToken = JsonConvert.DeserializeObject<KeycloakAccessTokenModel>(decodedString);
            var userName = userToken.preferred_username;
            //carregar os dados do usuario do keycloak
            var user = await GetUserAsync(userName);
            if (user != null)
            {
                //pegar o token do client portal-extratta
                var clientToken = await GetClientAccessTokenAsync("");
                var httpClient1 = new HttpClient(new HttpClientHandler() { SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls });
                httpClient1.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", clientToken.AccessToken);
                //descobrir o deviceName vinculado ao 2fa do usuario
                var req1 = await httpClient1.GetAsync(settings.UsersUrl + "/" + user.id + "/credentials").ConfigureAwait(false);
                if (req1.StatusCode == HttpStatusCode.OK)
                {
                    string content1 = await req1.Content.ReadAsStringAsync();
                    var list = JsonConvert.DeserializeObject<List<KeycloakUserCredentialInfo>>(content1);
                    string deviceName = "";
                    if (list.Count > 0)
                    {
                        foreach (var item in list.Where(item => item.type.Equals("otp")))
                        {
                            deviceName = item.userLabel;
                            break;
                        }
                    }
                    if (deviceName != "")
                    {
                        //validar se o code é valido para este usuario e deviceName
                        var httpClient2 = new HttpClient(new HttpClientHandler() { SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls11 | SslProtocols.Tls });
                        httpClient2.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", clientToken.AccessToken);
                        var url2 = settings.Manage2faUrl + "/" + user.id.ToString() + "/validate-2fa-code";
                        string content2 = 
                            "{\"deviceName\":\"" + deviceName + 
                            "\",\"totpCode\":\"" + code + 
                            "\"}";
                        var req = new StringContent(content2, Encoding.UTF8, "application/json");
                        var res = await httpClient2.PostAsync(url2, req).ConfigureAwait(false);
                        if (res.IsSuccessStatusCode)
                        {
                            return "OK";
                        }
                        else
                        {
                            string body = await res.Content.ReadAsStringAsync();
                            if (body.Contains("invalid totp code"))
                            {
                                return "Código inválido";
                            } else
                            {
                                return "Falha na verificação 2fa: " + res.ReasonPhrase + " | " + body;
                            }
                        }
                    } else
                    {
                        return "Falha na verificação 2fa: usuário não tem credencial 2fa";
                    }
                } else
                {
                    return "Falha na verificação 2fa: acesso a credenciais";
                }
            } else
            {
                return "Falha na verificação 2fa: usuário inválido";
            }            
        }

    }
}
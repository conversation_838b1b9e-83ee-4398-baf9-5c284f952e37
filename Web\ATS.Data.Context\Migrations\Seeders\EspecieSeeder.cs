﻿using System;
using ATS.Domain.Entities;
using System.Data.Entity.Migrations;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class EspecieSeeder
    {
        public void Execute(AtsContext context)
        {
            int idEspecie = 1;

            context.Especie.AddOrUpdate(new[]
            {
                new Especie { IdEspecie = idEspecie++, Descricao = "Fardo", DataHoraUltimaAtualizacao= DateTime.Now },
                new Especie { IdEspecie = idEspecie++, Descricao = "Caixas", DataHoraUltimaAtualizacao= DateTime.Now }
            });
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Transactions;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class ProdutoApp : AppBase, IProdutoApp
    {
        private readonly IProdutoService _produtoService;

        public ProdutoApp(IProdutoService produtoService)
        {
            _produtoService = produtoService;
        }

        public ValidationResult Add(Produto produto)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _produtoService.Add(produto);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Produto produto)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _produtoService.Update(produto);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Inativar(int idProduto)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _produtoService.Inativar(idProduto);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idproduto)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _produtoService.Reativar(idproduto);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object ConsultaGrid(int? idEmpresa, int? idProduto, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _produtoService.ConsultaGrid(idEmpresa, idProduto, descricao, take, page, orderFilters, filters);
        }

        public List<Produto> ConsultarPorEmpresa(int emp)
        {
            return _produtoService.ConsultarPorEmpresa(emp);
        }

        public Produto GetPorId(int value)
        {
            return _produtoService.Get(value);
        }
    }
}

﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Models.Webservice.Request.Credenciamento;
using AutoMapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class MotivoCredenciamentoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IFilialApp _filialApp;
        private readonly IMotivoCredenciamentoService _motivoCredenciamentoService;

        public MotivoCredenciamentoAtsController(IUserIdentity userIdentity, IMotivoCredenciamentoService motivoCredenciamentoService, IFilialApp filialApp)
        {
            _userIdentity = userIdentity;
            _motivoCredenciamentoService = motivoCredenciamentoService;
            _filialApp = filialApp;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idFilial, string descricao, int take, int page, 
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var consultarGrid = _motivoCredenciamentoService.ConsultarGrid(idEmpresa, idFilial, descricao, take, page, order, filters);

                return ResponderSucesso(consultarGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridTodos(int? idEmpresa, int? idFilial, string descricao, int take, int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!idEmpresa.HasValue && _userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var consultarGrid = _motivoCredenciamentoService.ConsultarGridTodos(idEmpresa, idFilial, descricao, take, page, order, filters);

                return ResponderSucesso(consultarGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idMotivo)
        {
            try
            {
                var motivo = _motivoCredenciamentoService.Get(idMotivo);

                if (motivo == null)
                    throw new Exception("Não foi possível encontrar o registro desejado");

                if (_userIdentity.Perfil != (int)EPerfil.Administrador &&
                    motivo.IdEmpresa != _userIdentity.IdEmpresa)
                    throw new InvalidOperationException("Usuário não autenticado.");
                
                var motivoRequest = new MotivoRequestModel();

                motivoRequest = Mapper.Map(motivo, motivoRequest);
                return ResponderSucesso(motivoRequest);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idMotivo)
        {
            try
            {
                var validationResult = _motivoCredenciamentoService.Inativar(idMotivo);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Motivo inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idMotivo)
        {
            try
            {
                var validationResult = _motivoCredenciamentoService.Reativar(idMotivo);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Motivo reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarAtualizar(MotivoRequestModel @params)
        {
            var validationResult = new ValidationResult();
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");
                
                if (@params.IdFilial.HasValue && _userIdentity.IdEmpresa.HasValue &&
                    !_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, @params.IdFilial.Value))
                    return ResponderErro("Usuário não autenticado.");

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                {
                    if (_userIdentity.IdEmpresa.HasValue)
                        @params.IdEmpresa = (int)_userIdentity.IdEmpresa;
                    else
                        validationResult.Add("Usuário não possui vinculo com empresa.");
                }

                if (@params.IdEmpresa <= 0)
                    validationResult.Add("Empresa não informada.");

                if (@params.Descricao == null || @params.Descricao.Length <= 0)
                    validationResult.Add("Descrição não informada.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToFormatedMessage());

                if (@params.IdMotivo > 0)
                {
                    var motivo = _motivoCredenciamentoService.Get(@params.IdMotivo.Value);

                    motivo.IdFilial = @params.IdFilial;
                    motivo.Descricao = @params.Descricao;
                    List<ETipoMotivo> tipos = @params.TipoMotivo.Select(x => x.Tipo).ToList();

                    validationResult.Add(_motivoCredenciamentoService.Update(motivo, tipos));
                }
                else
                {
                    var motivo = new Motivo();
                    motivo = Mapper.Map(@params, motivo);
                    validationResult.Add(_motivoCredenciamentoService.Add(motivo));
                }

                return validationResult.IsValid
                    ? ResponderSucesso("Dados " + (!@params.IdMotivo.HasValue ? "incluídos" : "atualizados") +
                                       " com sucesso.")
                    : ResponderErro(validationResult.ToFormatedMessage());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridMotivo(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            
            if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;
            
            var report = _motivoCredenciamentoService.GerarRelatorioGridMotivo(filtrosGridModel.IdEmpresa, null,
                string.Empty, filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order,
                filtrosGridModel.Filters, filtrosGridModel.Extensao, GetLogo(filtrosGridModel.IdEmpresa));

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de motivos.{filtrosGridModel.Extensao}");
        }
    }
}
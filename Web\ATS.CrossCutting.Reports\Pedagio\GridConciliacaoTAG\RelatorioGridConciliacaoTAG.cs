﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Pedagio.GridConciliacaoTAG
{
    public class RelatorioGridConciliacaoTag
    {
        public byte[] GetReport(string tipo, RelatorioGridConciliacaoTagDataType dadosRelatorio, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DtoConsultaGridConciliacaoTAG"
                });

                var path = ReportUtils.CreateLogo(logo);

                var parametros = new List<ReportParameter>();
                parametros.Add(new ReportParameter("Logo", "file:///" + path));

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Pedagio.GridConciliacaoTAG.RelatorioGridConciliacaoTAG.rdlc";

                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}

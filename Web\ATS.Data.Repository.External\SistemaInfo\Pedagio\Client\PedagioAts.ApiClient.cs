﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v********* (NJsonSchema v******** (Newtonsoft.Json v9.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

using System.Web;
using ATS.Data.Repository.External.SistemaInfo;

namespace SistemaInfo.MicroServices.Rest.Cartao.PedagiosClient
{
    #pragma warning disable // Disable all warnings

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v9.0.0.0))")]
    public partial class Client : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Pedagios/Api";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public Client(HttpContext configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Busca todas as compras conforme os parâmetros.</summary>
        /// <param name="consultaCompraPedagioRequest">Objeto de filtros para consulta de compras.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultaCompraPedagioResponse> ConsultarComprasAsync(int pageSize, int pageIndex, System.Collections.Generic.IEnumerable<object> customFilters, System.Collections.Generic.IEnumerable<object> orderBy, ConsultaCompraPedagioRequest consultaCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarComprasAsync(pageSize, pageIndex, customFilters, orderBy, consultaCompraPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca todas as compras conforme os parâmetros.</summary>
        /// <param name="consultaCompraPedagioRequest">Objeto de filtros para consulta de compras.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultaCompraPedagioResponse ConsultarCompras(int pageSize, int pageIndex, System.Collections.Generic.IEnumerable<object> customFilters, System.Collections.Generic.IEnumerable<object> orderBy, ConsultaCompraPedagioRequest consultaCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarComprasAsync(pageSize, pageIndex, customFilters, orderBy, consultaCompraPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca todas as compras conforme os parâmetros.</summary>
        /// <param name="consultaCompraPedagioRequest">Objeto de filtros para consulta de compras.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultaCompraPedagioResponse> ConsultarComprasAsync(int pageSize, int pageIndex, System.Collections.Generic.IEnumerable<object> customFilters, System.Collections.Generic.IEnumerable<object> orderBy, ConsultaCompraPedagioRequest consultaCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (pageSize == null)
                throw new System.ArgumentNullException("pageSize");
    
            if (pageIndex == null)
                throw new System.ArgumentNullException("pageIndex");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/ConsultarCompras?");
            urlBuilder_.Append("PageSize=").Append(System.Uri.EscapeDataString(ConvertToString(pageSize, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Append("PageIndex=").Append(System.Uri.EscapeDataString(ConvertToString(pageIndex, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            if (customFilters != null) 
            {
                foreach (var item_ in customFilters) { urlBuilder_.Append("CustomFilters=").Append(System.Uri.EscapeDataString(ConvertToString(item_, System.Globalization.CultureInfo.InvariantCulture))).Append("&"); }
            }
            if (orderBy != null) 
            {
                foreach (var item_ in orderBy) { urlBuilder_.Append("OrderBy=").Append(System.Uri.EscapeDataString(ConvertToString(item_, System.Globalization.CultureInfo.InvariantCulture))).Append("&"); }
            }
            urlBuilder_.Length--;
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(consultaCompraPedagioRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultaCompraPedagioResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCompraPedagioResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultaCompraPedagioResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Realiza uma solicitação de compra de pedágio.</summary>
        /// <param name="solicitarCompraPedagioRequest">Objeto de filtros para solicitação de compras</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<SolicitarCompraPedagioResponse> SolicitarCompraAsync(SolicitarCompraPedagioRequest solicitarCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return SolicitarCompraAsync(solicitarCompraPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Realiza uma solicitação de compra de pedágio.</summary>
        /// <param name="solicitarCompraPedagioRequest">Objeto de filtros para solicitação de compras</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public SolicitarCompraPedagioResponse SolicitarCompra(SolicitarCompraPedagioRequest solicitarCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await SolicitarCompraAsync(solicitarCompraPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Realiza uma solicitação de compra de pedágio.</summary>
        /// <param name="solicitarCompraPedagioRequest">Objeto de filtros para solicitação de compras</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<SolicitarCompraPedagioResponse> SolicitarCompraAsync(SolicitarCompraPedagioRequest solicitarCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/SolicitarCompra");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(solicitarCompraPedagioRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(SolicitarCompraPedagioResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<SolicitarCompraPedagioResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(SolicitarCompraPedagioResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Cancela uma solicitação de pedágio.</summary>
        /// <param name="cancelarCompraPedagioRequest">Objeto de filtros para solicitação de compras</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CancelarCompraPedagioResponse> CancelarCompraAsync(CancelarCompraPedagioRequest cancelarCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CancelarCompraAsync(cancelarCompraPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Cancela uma solicitação de pedágio.</summary>
        /// <param name="cancelarCompraPedagioRequest">Objeto de filtros para solicitação de compras</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CancelarCompraPedagioResponse CancelarCompra(CancelarCompraPedagioRequest cancelarCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CancelarCompraAsync(cancelarCompraPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Cancela uma solicitação de pedágio.</summary>
        /// <param name="cancelarCompraPedagioRequest">Objeto de filtros para solicitação de compras</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CancelarCompraPedagioResponse> CancelarCompraAsync(CancelarCompraPedagioRequest cancelarCompraPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/CancelarCompra");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(cancelarCompraPedagioRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CancelarCompraPedagioResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarCompraPedagioResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(CancelarCompraPedagioResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Calcula o custo de pedágios de uma rota solicitada.</summary>
        /// <param name="consultaPedagioRequest">Objeto de filtros para consulta de custo de pedágio.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultaRotaResponse> CalcularRotaAsync(ConsultaRotaRequest consultaPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CalcularRotaAsync(consultaPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Calcula o custo de pedágios de uma rota solicitada.</summary>
        /// <param name="consultaPedagioRequest">Objeto de filtros para consulta de custo de pedágio.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultaRotaResponse CalcularRota(ConsultaRotaRequest consultaPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CalcularRotaAsync(consultaPedagioRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Calcula o custo de pedágios de uma rota solicitada.</summary>
        /// <param name="consultaPedagioRequest">Objeto de filtros para consulta de custo de pedágio.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultaRotaResponse> CalcularRotaAsync(ConsultaRotaRequest consultaPedagioRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/CalcularRota");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(consultaPedagioRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultaRotaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaRotaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultaRotaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consulta o historico de determinada consulta de rotas.</summary>
        /// <param name="consultaHistoricoRotaRequest">Objeto de filtros para consulta de historico de pedágio.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultaHistoricoRotaResponse> ConsultarHistoricoRoterizacaoAsync(ConsultaHistoricoRotaRequest consultaHistoricoRotaRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarHistoricoRoterizacaoAsync(consultaHistoricoRotaRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consulta o historico de determinada consulta de rotas.</summary>
        /// <param name="consultaHistoricoRotaRequest">Objeto de filtros para consulta de historico de pedágio.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultaHistoricoRotaResponse ConsultarHistoricoRoterizacao(ConsultaHistoricoRotaRequest consultaHistoricoRotaRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarHistoricoRoterizacaoAsync(consultaHistoricoRotaRequest, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consulta o historico de determinada consulta de rotas.</summary>
        /// <param name="consultaHistoricoRotaRequest">Objeto de filtros para consulta de historico de pedágio.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultaHistoricoRotaResponse> ConsultarHistoricoRoterizacaoAsync(ConsultaHistoricoRotaRequest consultaHistoricoRotaRequest, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/ConsultarHistoricoRoterizacao");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(consultaHistoricoRotaRequest, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultaHistoricoRotaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaHistoricoRotaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", (int)response_.StatusCode, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
            
                        return default(ConsultaHistoricoRotaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class CustomFilter : System.ComponentModel.INotifyPropertyChanged
    {
        private string _field;
        private CustomFilterOperator? _operator;
        private string _value;
        private bool? _serverFieldCollection;
        private CustomFilterFieldType? _fieldType;
    
        [Newtonsoft.Json.JsonProperty("field", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Field
        {
            get { return _field; }
            set 
            {
                if (_field != value)
                {
                    _field = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("operator", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CustomFilterOperator? Operator
        {
            get { return _operator; }
            set 
            {
                if (_operator != value)
                {
                    _operator = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("value", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Value
        {
            get { return _value; }
            set 
            {
                if (_value != value)
                {
                    _value = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("serverFieldCollection", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ServerFieldCollection
        {
            get { return _serverFieldCollection; }
            set 
            {
                if (_serverFieldCollection != value)
                {
                    _serverFieldCollection = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("fieldType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CustomFilterFieldType? FieldType
        {
            get { return _fieldType; }
            set 
            {
                if (_fieldType != value)
                {
                    _fieldType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CustomFilter FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CustomFilter>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class OrderOptions : System.ComponentModel.INotifyPropertyChanged
    {
        private string _field;
        private bool? _asc;
    
        [Newtonsoft.Json.JsonProperty("field", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Field
        {
            get { return _field; }
            set 
            {
                if (_field != value)
                {
                    _field = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("asc", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Asc
        {
            get { return _asc; }
            set 
            {
                if (_asc != value)
                {
                    _asc = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static OrderOptions FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<OrderOptions>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaCompraPedagioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private System.Collections.Generic.List<Anonymous> _status;
        private ConsultaCompraPedagioRequestFornecedor? _fornecedor;
        private string _documentoFavorecido;
        private int? _idCidadeOrigem;
        private int? _idCidadeDestino;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore, ItemConverterType = typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public System.Collections.Generic.List<Anonymous> Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("fornecedor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaCompraPedagioRequestFornecedor? Fornecedor
        {
            get { return _fornecedor; }
            set 
            {
                if (_fornecedor != value)
                {
                    _fornecedor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoFavorecido", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoFavorecido
        {
            get { return _documentoFavorecido; }
            set 
            {
                if (_documentoFavorecido != value)
                {
                    _documentoFavorecido = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("idCidadeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IdCidadeOrigem
        {
            get { return _idCidadeOrigem; }
            set 
            {
                if (_idCidadeOrigem != value)
                {
                    _idCidadeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("idCidadeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IdCidadeDestino
        {
            get { return _idCidadeDestino; }
            set 
            {
                if (_idCidadeDestino != value)
                {
                    _idCidadeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set 
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set 
            {
                if (_dataFim != value)
                {
                    _dataFim = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaCompraPedagioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCompraPedagioRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaCompraPedagioResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultaCompraPedagioResponseStatus _status;
        private string _mensagem;
        private FilteredOptions _filteredOptions;
        private System.Collections.Generic.List<CompraPedagioDTOResponse> _compraPedagioDTOList;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaCompraPedagioResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("filteredOptions", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public FilteredOptions FilteredOptions
        {
            get { return _filteredOptions; }
            set 
            {
                if (_filteredOptions != value)
                {
                    _filteredOptions = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("compraPedagioDTOList", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<CompraPedagioDTOResponse> CompraPedagioDTOList
        {
            get { return _compraPedagioDTOList; }
            set 
            {
                if (_compraPedagioDTOList != value)
                {
                    _compraPedagioDTOList = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaCompraPedagioResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaCompraPedagioResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ApiProcessingStateOnServer : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServerState _state;
        private string _errorMessage;
    
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ApiProcessingStateOnServerState State
        {
            get { return _state; }
            set 
            {
                if (_state != value)
                {
                    _state = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errorMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set 
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ApiProcessingStateOnServer FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ApiProcessingStateOnServer>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class FilteredOptions : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _totalRecords;
        private int? _pageCount;
    
        [Newtonsoft.Json.JsonProperty("totalRecords", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? TotalRecords
        {
            get { return _totalRecords; }
            set 
            {
                if (_totalRecords != value)
                {
                    _totalRecords = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pageCount", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? PageCount
        {
            get { return _pageCount; }
            set 
            {
                if (_pageCount != value)
                {
                    _pageCount = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static FilteredOptions FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<FilteredOptions>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class CompraPedagioDTOResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private string _cnpjEmpresa;
        private string _nomeEmpresa;
        private CompraPedagioDTOResponseStatus? _status;
        private CompraPedagioDTOResponseFornecedor? _fornecedor;
        private CompraPedagioDTOResponseProcessoSaldoResidual? _processoSaldoResidual;
        private string _documentoFavorecido;
        private string _nomeFavorecido;
        private string _placa;
        private int? _qtdEixos;
        private decimal? _valor;
        private long? _protocoloRequisicao;
        private string _numeroCIOT;
        private System.DateTime? _dataCadastro;
        private System.DateTime? _dataConfirmacao;
        private System.DateTime? _dataCancelamento;
        private System.Guid? _consultaCustoHistoricoId;
        private int? _ibgeOrigem;
        private string _cidadeOrigem;
        private string _estadoOrigem;
        private int? _ibgeDestino;
        private string _cidadeDestino;
        private string _estadoDestino;
        private int? _cartaoFrete;
        private int? _produtoFrete;
        private int? _cartaoPedagio;
        private int? _produtoPedagio;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cnpjEmpresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CnpjEmpresa
        {
            get { return _cnpjEmpresa; }
            set 
            {
                if (_cnpjEmpresa != value)
                {
                    _cnpjEmpresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeEmpresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeEmpresa
        {
            get { return _nomeEmpresa; }
            set 
            {
                if (_nomeEmpresa != value)
                {
                    _nomeEmpresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CompraPedagioDTOResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("fornecedor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CompraPedagioDTOResponseFornecedor? Fornecedor
        {
            get { return _fornecedor; }
            set 
            {
                if (_fornecedor != value)
                {
                    _fornecedor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("processoSaldoResidual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CompraPedagioDTOResponseProcessoSaldoResidual? ProcessoSaldoResidual
        {
            get { return _processoSaldoResidual; }
            set 
            {
                if (_processoSaldoResidual != value)
                {
                    _processoSaldoResidual = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoFavorecido", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoFavorecido
        {
            get { return _documentoFavorecido; }
            set 
            {
                if (_documentoFavorecido != value)
                {
                    _documentoFavorecido = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFavorecido", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFavorecido
        {
            get { return _nomeFavorecido; }
            set 
            {
                if (_nomeFavorecido != value)
                {
                    _nomeFavorecido = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Placa
        {
            get { return _placa; }
            set 
            {
                if (_placa != value)
                {
                    _placa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("qtdEixos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QtdEixos
        {
            get { return _qtdEixos; }
            set 
            {
                if (_qtdEixos != value)
                {
                    _qtdEixos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroCIOT", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NumeroCIOT
        {
            get { return _numeroCIOT; }
            set 
            {
                if (_numeroCIOT != value)
                {
                    _numeroCIOT = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCadastro", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCadastro
        {
            get { return _dataCadastro; }
            set 
            {
                if (_dataCadastro != value)
                {
                    _dataCadastro = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataConfirmacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataConfirmacao
        {
            get { return _dataConfirmacao; }
            set 
            {
                if (_dataConfirmacao != value)
                {
                    _dataConfirmacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataCancelamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataCancelamento
        {
            get { return _dataCancelamento; }
            set 
            {
                if (_dataCancelamento != value)
                {
                    _dataCancelamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("consultaCustoHistoricoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? ConsultaCustoHistoricoId
        {
            get { return _consultaCustoHistoricoId; }
            set 
            {
                if (_consultaCustoHistoricoId != value)
                {
                    _consultaCustoHistoricoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibgeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IbgeOrigem
        {
            get { return _ibgeOrigem; }
            set 
            {
                if (_ibgeOrigem != value)
                {
                    _ibgeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidadeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CidadeOrigem
        {
            get { return _cidadeOrigem; }
            set 
            {
                if (_cidadeOrigem != value)
                {
                    _cidadeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estadoOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string EstadoOrigem
        {
            get { return _estadoOrigem; }
            set 
            {
                if (_estadoOrigem != value)
                {
                    _estadoOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibgeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IbgeDestino
        {
            get { return _ibgeDestino; }
            set 
            {
                if (_ibgeDestino != value)
                {
                    _ibgeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidadeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CidadeDestino
        {
            get { return _cidadeDestino; }
            set 
            {
                if (_cidadeDestino != value)
                {
                    _cidadeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estadoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string EstadoDestino
        {
            get { return _estadoDestino; }
            set 
            {
                if (_estadoDestino != value)
                {
                    _estadoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoFrete
        {
            get { return _cartaoFrete; }
            set 
            {
                if (_cartaoFrete != value)
                {
                    _cartaoFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produtoFrete", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProdutoFrete
        {
            get { return _produtoFrete; }
            set 
            {
                if (_produtoFrete != value)
                {
                    _produtoFrete = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoPedagio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoPedagio
        {
            get { return _cartaoPedagio; }
            set 
            {
                if (_cartaoPedagio != value)
                {
                    _cartaoPedagio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produtoPedagio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProdutoPedagio
        {
            get { return _produtoPedagio; }
            set 
            {
                if (_produtoPedagio != value)
                {
                    _produtoPedagio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CompraPedagioDTOResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CompraPedagioDTOResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class SolicitarCompraPedagioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _documentoFavorecido;
        private string _nomeFavorecido;
        private string _placa;
        private int? _qtdEixos;
        private SolicitarCompraPedagioRequestFornecedor? _fornecedor;
        private decimal? _valor;
        private int? _protocoloRequisicao;
        private System.Collections.Generic.List<LocationDTO> _localizacoes;
        private SolicitarCompraPedagioRequestTipoVeiculo? _tipoVeiculo;
        private System.Guid? _identificadorHistorico;
        private string _numeroCIOT;
        private System.DateTime? _dataExpiracaoCreditoPendente;
        private System.DateTime? _dataExpiracaoCompraPedagio;
        private SolicitarCompraPedagioRequestProcessoSaldoResidual? _processoSaldoResidual;
    
        [Newtonsoft.Json.JsonProperty("documentoFavorecido", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoFavorecido
        {
            get { return _documentoFavorecido; }
            set 
            {
                if (_documentoFavorecido != value)
                {
                    _documentoFavorecido = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFavorecido", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFavorecido
        {
            get { return _nomeFavorecido; }
            set 
            {
                if (_nomeFavorecido != value)
                {
                    _nomeFavorecido = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("placa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Placa
        {
            get { return _placa; }
            set 
            {
                if (_placa != value)
                {
                    _placa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("qtdEixos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QtdEixos
        {
            get { return _qtdEixos; }
            set 
            {
                if (_qtdEixos != value)
                {
                    _qtdEixos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("fornecedor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public SolicitarCompraPedagioRequestFornecedor? Fornecedor
        {
            get { return _fornecedor; }
            set 
            {
                if (_fornecedor != value)
                {
                    _fornecedor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("localizacoes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<LocationDTO> Localizacoes
        {
            get { return _localizacoes; }
            set 
            {
                if (_localizacoes != value)
                {
                    _localizacoes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoVeiculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public SolicitarCompraPedagioRequestTipoVeiculo? TipoVeiculo
        {
            get { return _tipoVeiculo; }
            set 
            {
                if (_tipoVeiculo != value)
                {
                    _tipoVeiculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("identificadorHistorico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? IdentificadorHistorico
        {
            get { return _identificadorHistorico; }
            set 
            {
                if (_identificadorHistorico != value)
                {
                    _identificadorHistorico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroCIOT", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NumeroCIOT
        {
            get { return _numeroCIOT; }
            set 
            {
                if (_numeroCIOT != value)
                {
                    _numeroCIOT = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataExpiracaoCreditoPendente", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataExpiracaoCreditoPendente
        {
            get { return _dataExpiracaoCreditoPendente; }
            set 
            {
                if (_dataExpiracaoCreditoPendente != value)
                {
                    _dataExpiracaoCreditoPendente = value; 
                    RaisePropertyChanged();
                }
            }
        }

        [Newtonsoft.Json.JsonProperty("dataExpiracaoCompraPedagio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataExpiracaoCompraPedagio
        {
            get { return _dataExpiracaoCompraPedagio; }
            set
            {
                if (_dataExpiracaoCompraPedagio != value)
                {
                    _dataExpiracaoCompraPedagio = value;
                    RaisePropertyChanged();
                }
            }
        }

	[Newtonsoft.Json.JsonProperty("processoSaldoResidual", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public SolicitarCompraPedagioRequestProcessoSaldoResidual? ProcessoSaldoResidual
        {
            get { return _processoSaldoResidual; }
            set 
            {
                if (_processoSaldoResidual != value)
                {
                    _processoSaldoResidual = value; 
                    RaisePropertyChanged();
                }
            }
        }

        public string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static SolicitarCompraPedagioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<SolicitarCompraPedagioRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class LocationDTO : System.ComponentModel.INotifyPropertyChanged
    {
        private decimal? _latitude;
        private decimal? _longitude;
        private int? _ibge;
        private int? _raioBuscarEstradasProximas;
    
        [Newtonsoft.Json.JsonProperty("latitude", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Latitude
        {
            get { return _latitude; }
            set 
            {
                if (_latitude != value)
                {
                    _latitude = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("longitude", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Longitude
        {
            get { return _longitude; }
            set 
            {
                if (_longitude != value)
                {
                    _longitude = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibge", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Ibge
        {
            get { return _ibge; }
            set 
            {
                if (_ibge != value)
                {
                    _ibge = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("raioBuscarEstradasProximas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? RaioBuscarEstradasProximas
        {
            get { return _raioBuscarEstradasProximas; }
            set 
            {
                if (_raioBuscarEstradasProximas != value)
                {
                    _raioBuscarEstradasProximas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static LocationDTO FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<LocationDTO>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class SolicitarCompraPedagioResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private SolicitarCompraPedagioResponseStatus _status;
        private string _mensagem;
        private CompraPedagioDTOResponse _compraPedagio;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public SolicitarCompraPedagioResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("compraPedagio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public CompraPedagioDTOResponse CompraPedagio
        {
            get { return _compraPedagio; }
            set 
            {
                if (_compraPedagio != value)
                {
                    _compraPedagio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static SolicitarCompraPedagioResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<SolicitarCompraPedagioResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class CancelarCompraPedagioRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CancelarCompraPedagioRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarCompraPedagioRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class CancelarCompraPedagioResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private CancelarCompraPedagioResponseStatus _status;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CancelarCompraPedagioResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CancelarCompraPedagioResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CancelarCompraPedagioResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaRotaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Collections.Generic.List<LocationDTO> _localizacoes;
        private ConsultaRotaRequestTipoVeiculo _tipoVeiculo;
        private int? _qtdEixos;
        private bool? _exibirDetalhes;
    
        [Newtonsoft.Json.JsonProperty("localizacoes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<LocationDTO> Localizacoes
        {
            get { return _localizacoes; }
            set 
            {
                if (_localizacoes != value)
                {
                    _localizacoes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoVeiculo", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaRotaRequestTipoVeiculo TipoVeiculo
        {
            get { return _tipoVeiculo; }
            set 
            {
                if (_tipoVeiculo != value)
                {
                    _tipoVeiculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("qtdEixos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QtdEixos
        {
            get { return _qtdEixos; }
            set 
            {
                if (_qtdEixos != value)
                {
                    _qtdEixos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("exibirDetalhes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? ExibirDetalhes
        {
            get { return _exibirDetalhes; }
            set 
            {
                if (_exibirDetalhes != value)
                {
                    _exibirDetalhes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaRotaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaRotaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaRotaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultaRotaResponseStatus _status;
        private string _mensagem;
        private System.Collections.Generic.List<Praca> _pracas;
        private decimal? _custoTotal;
        private System.Guid? _identificadorHistorico;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaRotaResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pracas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Praca> Pracas
        {
            get { return _pracas; }
            set 
            {
                if (_pracas != value)
                {
                    _pracas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("custoTotal", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? CustoTotal
        {
            get { return _custoTotal; }
            set 
            {
                if (_custoTotal != value)
                {
                    _custoTotal = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("identificadorHistorico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? IdentificadorHistorico
        {
            get { return _identificadorHistorico; }
            set 
            {
                if (_identificadorHistorico != value)
                {
                    _identificadorHistorico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaRotaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaRotaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class Praca : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private LocationDTO _localizacao;
        private Endereco _endereco;
        private string _telefone;
        private System.Collections.Generic.List<Preco> _precos;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("localizacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public LocationDTO Localizacao
        {
            get { return _localizacao; }
            set 
            {
                if (_localizacao != value)
                {
                    _localizacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("endereco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public Endereco Endereco
        {
            get { return _endereco; }
            set 
            {
                if (_endereco != value)
                {
                    _endereco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("telefone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Telefone
        {
            get { return _telefone; }
            set 
            {
                if (_telefone != value)
                {
                    _telefone = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("precos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<Preco> Precos
        {
            get { return _precos; }
            set 
            {
                if (_precos != value)
                {
                    _precos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Praca FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Praca>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class Endereco : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeRua;
        private string _nomeCidade;
        private string _nomeEstado;
        private string _nomePais;
    
        [Newtonsoft.Json.JsonProperty("nomeRua", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRua
        {
            get { return _nomeRua; }
            set 
            {
                if (_nomeRua != value)
                {
                    _nomeRua = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeCidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeCidade
        {
            get { return _nomeCidade; }
            set 
            {
                if (_nomeCidade != value)
                {
                    _nomeCidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeEstado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeEstado
        {
            get { return _nomeEstado; }
            set 
            {
                if (_nomeEstado != value)
                {
                    _nomeEstado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomePais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomePais
        {
            get { return _nomePais; }
            set 
            {
                if (_nomePais != value)
                {
                    _nomePais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Endereco FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Endereco>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class Preco : System.ComponentModel.INotifyPropertyChanged
    {
        private decimal? _precoEixoAdicional;
        private decimal? _valor;
    
        [Newtonsoft.Json.JsonProperty("precoEixoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PrecoEixoAdicional
        {
            get { return _precoEixoAdicional; }
            set 
            {
                if (_precoEixoAdicional != value)
                {
                    _precoEixoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static Preco FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<Preco>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaHistoricoRotaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Guid? _consultaCustoHistoricoId;
        private int? _recalcularValorParaEixos;
    
        [Newtonsoft.Json.JsonProperty("consultaCustoHistoricoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? ConsultaCustoHistoricoId
        {
            get { return _consultaCustoHistoricoId; }
            set 
            {
                if (_consultaCustoHistoricoId != value)
                {
                    _consultaCustoHistoricoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("recalcularValorParaEixos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? RecalcularValorParaEixos
        {
            get { return _recalcularValorParaEixos; }
            set 
            {
                if (_recalcularValorParaEixos != value)
                {
                    _recalcularValorParaEixos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaHistoricoRotaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaHistoricoRotaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaHistoricoRotaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultaHistoricoRotaResponseStatus _status;
        private string _mensagem;
        private int? _qtdEixos;
        private ConsultaHistoricoRotaResponseTipoVeiculo? _tipoVeiculo;
        private decimal? _custoTotal;
        private int? _ibgeOrigem;
        private string _nomeCidadeOrigem;
        private string _estadoOrigem;
        private int? _ibgeDestino;
        private string _nomeCidadeDestino;
        private string _estadoDestino;
        private decimal? _latitudeOrigem;
        private decimal? _longitudeOrigem;
        private decimal? _latitudeDestino;
        private decimal? _longitudeDestino;
        private System.Collections.Generic.List<ConsultaHistoricoRotaPracaResponse> _pracas;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaHistoricoRotaResponseStatus Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("qtdEixos", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QtdEixos
        {
            get { return _qtdEixos; }
            set 
            {
                if (_qtdEixos != value)
                {
                    _qtdEixos = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipoVeiculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultaHistoricoRotaResponseTipoVeiculo? TipoVeiculo
        {
            get { return _tipoVeiculo; }
            set 
            {
                if (_tipoVeiculo != value)
                {
                    _tipoVeiculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("custoTotal", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? CustoTotal
        {
            get { return _custoTotal; }
            set 
            {
                if (_custoTotal != value)
                {
                    _custoTotal = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibgeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IbgeOrigem
        {
            get { return _ibgeOrigem; }
            set 
            {
                if (_ibgeOrigem != value)
                {
                    _ibgeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeCidadeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeCidadeOrigem
        {
            get { return _nomeCidadeOrigem; }
            set 
            {
                if (_nomeCidadeOrigem != value)
                {
                    _nomeCidadeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estadoOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string EstadoOrigem
        {
            get { return _estadoOrigem; }
            set 
            {
                if (_estadoOrigem != value)
                {
                    _estadoOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ibgeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? IbgeDestino
        {
            get { return _ibgeDestino; }
            set 
            {
                if (_ibgeDestino != value)
                {
                    _ibgeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeCidadeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeCidadeDestino
        {
            get { return _nomeCidadeDestino; }
            set 
            {
                if (_nomeCidadeDestino != value)
                {
                    _nomeCidadeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("estadoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string EstadoDestino
        {
            get { return _estadoDestino; }
            set 
            {
                if (_estadoDestino != value)
                {
                    _estadoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("latitudeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? LatitudeOrigem
        {
            get { return _latitudeOrigem; }
            set 
            {
                if (_latitudeOrigem != value)
                {
                    _latitudeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("longitudeOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? LongitudeOrigem
        {
            get { return _longitudeOrigem; }
            set 
            {
                if (_longitudeOrigem != value)
                {
                    _longitudeOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("latitudeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? LatitudeDestino
        {
            get { return _latitudeDestino; }
            set 
            {
                if (_latitudeDestino != value)
                {
                    _latitudeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("longitudeDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? LongitudeDestino
        {
            get { return _longitudeDestino; }
            set 
            {
                if (_longitudeDestino != value)
                {
                    _longitudeDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pracas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<ConsultaHistoricoRotaPracaResponse> Pracas
        {
            get { return _pracas; }
            set 
            {
                if (_pracas != value)
                {
                    _pracas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaHistoricoRotaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaHistoricoRotaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultaHistoricoRotaPracaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private decimal? _latitude;
        private decimal? _longitude;
        private string _nomeRua;
        private string _nomeCidade;
        private string _nomeEstado;
        private string _nomePais;
        private string _telefone;
        private decimal? _valor;
        private decimal? _precoEixoAdicional;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("latitude", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Latitude
        {
            get { return _latitude; }
            set 
            {
                if (_latitude != value)
                {
                    _latitude = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("longitude", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Longitude
        {
            get { return _longitude; }
            set 
            {
                if (_longitude != value)
                {
                    _longitude = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeRua", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeRua
        {
            get { return _nomeRua; }
            set 
            {
                if (_nomeRua != value)
                {
                    _nomeRua = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeCidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeCidade
        {
            get { return _nomeCidade; }
            set 
            {
                if (_nomeCidade != value)
                {
                    _nomeCidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeEstado", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeEstado
        {
            get { return _nomeEstado; }
            set 
            {
                if (_nomeEstado != value)
                {
                    _nomeEstado = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomePais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomePais
        {
            get { return _nomePais; }
            set 
            {
                if (_nomePais != value)
                {
                    _nomePais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("telefone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Telefone
        {
            get { return _telefone; }
            set 
            {
                if (_telefone != value)
                {
                    _telefone = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("precoEixoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? PrecoEixoAdicional
        {
            get { return _precoEixoAdicional; }
            set 
            {
                if (_precoEixoAdicional != value)
                {
                    _precoEixoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultaHistoricoRotaPracaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultaHistoricoRotaPracaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum CustomFilterOperator
    {
        [System.Runtime.Serialization.EnumMember(Value = "StartsWith")]
        StartsWith = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "EndsWith")]
        EndsWith = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Contains")]
        Contains = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "NotContains")]
        NotContains = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Equals")]
        Equals = 4,
    
        [System.Runtime.Serialization.EnumMember(Value = "NotEquals")]
        NotEquals = 5,
    
        [System.Runtime.Serialization.EnumMember(Value = "IsNull")]
        IsNull = 6,
    
        [System.Runtime.Serialization.EnumMember(Value = "IsNotNull")]
        IsNotNull = 7,
    
        [System.Runtime.Serialization.EnumMember(Value = "GreaterThan")]
        GreaterThan = 8,
    
        [System.Runtime.Serialization.EnumMember(Value = "GreaterThanOrEqual")]
        GreaterThanOrEqual = 9,
    
        [System.Runtime.Serialization.EnumMember(Value = "LessThan")]
        LessThan = 10,
    
        [System.Runtime.Serialization.EnumMember(Value = "LessThanOrEqual")]
        LessThanOrEqual = 11,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum CustomFilterFieldType
    {
        [System.Runtime.Serialization.EnumMember(Value = "Indefinido")]
        Indefinido = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Date")]
        Date = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "String")]
        String = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Number")]
        Number = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Intervalo")]
        Intervalo = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum Anonymous
    {
        [System.Runtime.Serialization.EnumMember(Value = "Pendente")]
        Pendente = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Confirmado")]
        Confirmado = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Cancelado")]
        Cancelado = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "PendenteCancelamento")]
        PendenteCancelamento = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Bloqueado")]
        Bloqueado = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultaCompraPedagioRequestFornecedor
    {
        [System.Runtime.Serialization.EnumMember(Value = "Desabilitado")]
        Desabilitado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Moedeiro")]
        Moedeiro = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "ViaFacil")]
        ViaFacil = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Conectcar")]
        Conectcar = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultaCompraPedagioResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ApiProcessingStateOnServerState
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ok")]
        Ok = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum CompraPedagioDTOResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Pendente")]
        Pendente = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Confirmado")]
        Confirmado = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Cancelado")]
        Cancelado = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "PendenteCancelamento")]
        PendenteCancelamento = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "Bloqueado")]
        Bloqueado = 4,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum CompraPedagioDTOResponseFornecedor
    {
        [System.Runtime.Serialization.EnumMember(Value = "Desabilitado")]
        Desabilitado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Moedeiro")]
        Moedeiro = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "ViaFacil")]
        ViaFacil = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Conectcar")]
        Conectcar = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum CompraPedagioDTOResponseProcessoSaldoResidual
    {
        [System.Runtime.Serialization.EnumMember(Value = "EstornarSaldo")]
        EstornarSaldo = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Manter")]
        Manter = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum SolicitarCompraPedagioRequestFornecedor
    {
        [System.Runtime.Serialization.EnumMember(Value = "Desabilitado")]
        Desabilitado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Moedeiro")]
        Moedeiro = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "ViaFacil")]
        ViaFacil = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Conectcar")]
        Conectcar = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum SolicitarCompraPedagioRequestTipoVeiculo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Carro")]
        Carro = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Motocicleta")]
        Motocicleta = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Onibus")]
        Onibus = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Caminhao")]
        Caminhao = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum SolicitarCompraPedagioRequestProcessoSaldoResidual
    {
        [System.Runtime.Serialization.EnumMember(Value = "EstornarSaldo")]
        EstornarSaldo = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Manter")]
        Manter = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum SolicitarCompraPedagioResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum CancelarCompraPedagioResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultaRotaRequestTipoVeiculo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Carro")]
        Carro = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Motocicleta")]
        Motocicleta = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Onibus")]
        Onibus = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Caminhao")]
        Caminhao = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultaRotaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultaHistoricoRotaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "******** (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultaHistoricoRotaResponseTipoVeiculo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Carro")]
        Carro = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Motocicleta")]
        Motocicleta = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Onibus")]
        Onibus = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Caminhao")]
        Caminhao = 3,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message + "\n\nStatus: " + statusCode + "\nResponse: \n" + response.Substring(0, response.Length >= 512 ? 512 : response.Length), innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v******** (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}

﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Service
{
    public interface IPagamentoConfiguracaoProcessoService : IService<PagamentoConfiguracaoProcesso>
    {
        ValidationResult Add(PagamentoConfiguracaoProcesso pagamentoConfiguracao);
        ValidationResult Update(PagamentoConfiguracaoProcesso pagamentoConfiguracao);
        IEnumerable<PagamentoConfiguracaoProcesso> GetItemsByProcess(int idConfiguracao, EProcessoPgtoFrete processo);
        List<PagamentoConfiguracaoProcesso> GetbyPagtoDocto(int idPagtoDocumento);
        void Remove(PagamentoConfiguracaoProcesso processo);
    }
}

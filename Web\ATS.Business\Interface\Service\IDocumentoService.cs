﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.Reports.Documento;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IDocumentoService : IService<Documento>
    {
        ValidationResult Add(Documento documento);
        ValidationResult Update(Documento documento);
        ValidationResult Inativar(int idDocumento);
        ValidationResult Reativar(int idDocumento);
        IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null);
        List<int> GetIdsDocumentosEmpresa(int idEmpresa);
        int GetIdEmpresa(int idDocumento);
        IEnumerable<Documento> GetPorEmpresa(int? idEmpresa, int? idFilial);
        Documento Get(int idDocumento);
        IQueryable<Documento> GetAll(int idEmpresa, int? idFilial);
        DataModel<DocumentoModel> ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        byte[] GerarRelatorioGridDocumento(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo);
    }
}

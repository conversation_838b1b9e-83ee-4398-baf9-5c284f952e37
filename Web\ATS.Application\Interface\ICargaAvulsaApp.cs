﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.CargaAvulsa;

namespace ATS.Application.Interface
{
    public interface ICargaAvulsaApp : IBaseApp<ICargaAvulsaService>
    {
        CargaAvulsaAddResponseModel Add(CargaAvulsa cargaAvulsa, string cnpjEmpresa = null, string ipv4 = null, EBloqueioOrigemTipo? origem = null, bool? ignorarValidacaoDuplicada = null);
        decimal GetTransacoesDiaria(int? idEmpresa,int? idFilial);
        object ConsultaGrid(int? idEmpresa, int idUsuario, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor);
        object ConsultaGridPlanilha(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor);
        IQueryable<CargaAvulsa> Find(Expression<Func<CargaAvulsa, bool>> predicate);
        EstornarCargaAvulsaResponseModel EstornarCargaAvulsa(int? idCargaAvulsa, string numeroControle, string cnpjEmpresa);
        byte[] GerarRelatorioGridCargaAvulsa(OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo, int? idEmpresa,bool apenasLiberacaoGestor);
        ValidationResult ReprocessarCargaAvulsa(int cargaAvulsaId);
        ValidationResult AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa status);
        byte[] GerarReciboCargaAvulsa(int cargaAvulsaId, int? empresaid, string usuarioNome, string usuarioDocumento);
        ValidationResult Aprovar(int cargaAvulsaId, string cnpjEmpresa);
        BusinessResult GestorAprovar(int cargaAvulsaId);
        BusinessResult GestorReprovar(int cargaAvulsaId,string motivoRejeicao,bool validarPermissao,bool origemPortal = false);
        BusinessResult GestorReprovarPlanilha(string codImportacao);
        BusinessResult GestorAprovarPlanilha(string codImportacao);
        ValidationResult Processar(int cargaAvulsaId, string cnpjEmpresa);
        bool PertenceAEmpresa(int empresaId, int cargaAvulsaId);
        BusinessResult ProvisionarValor(ProvisionarRequest request);
        BusinessResult ProvisionarTaxa(ProvisionarRequest request);
        BusinessResult EstornarProvisionamentoValePedagio(int? idViagem, int? idEmpresa);
        BusinessResult EstornarProvisionamentoPedagio(long? eventoSaldoTagId);
    }
}

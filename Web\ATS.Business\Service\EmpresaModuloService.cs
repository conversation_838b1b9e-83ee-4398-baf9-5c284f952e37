﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class EmpresaModuloService : ServiceBase, IService<EmpresaModulo>, IEmpresaModuloService
    {
        private readonly IEmpresaModuloRepository _empresaModuloRepository;

        public EmpresaModuloService(IEmpresaModuloRepository empresaModuloRepository)
        {
            _empresaModuloRepository = empresaModuloRepository;
        }

        /// <summary>
        /// Método utilizado por listar ModuloEmpresa
        /// </summary>
        /// <param name="idEmpresa">id de Empresa</param>
        /// <returns>IQueryable de ModuloEmpresa</returns>
        public IQueryable<EmpresaModulo> ListarModuloPorIdEmpresa(int? idEmpresa)
        {
            return _empresaModuloRepository.ListarModuloPorIdEmpresa(idEmpresa);
        }

        /// <summary>
        /// Validar os modulos do empresa
        /// </summary>
        /// <param name="empresa"></param>
        /// <returns></returns>
        public IEnumerable<ValidationError> IsValid(Empresa empresa)
        {
            List<ValidationError> validationErrors = new List<ValidationError>();

            if (empresa.Modulos != null && empresa.Modulos.Count > 0)
            {
                IEnumerable<EmpresaModulo> modulos = empresa.Modulos.Where(x => x.IdModulo == 0);
                if (modulos.Any())
                    validationErrors.Add(new ValidationError(@"Módulo(s) inválido(s)."));
            }

            return validationErrors;
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Models.Menu;

namespace ATS.Domain.Interface.Service
{
    public interface IMenuService : IService<Menu>
    {
        Menu Get(int id);
        ValidationResult Add(Menu menu);
        ValidationResult Update(Menu menu);
        IQueryable<Menu> All();
        IQueryable<MenuGrid> Consultar(string descricao);
        IQueryable<Menu> GetPorIdGrupoUsuario(int idGrupoUsuario);
        List<ModuloEstruturaModel> GetMenusPermitidos(int idUsuario);
        IQueryable<GrupoUsuarioMenuGrid> GetArvoreMenuPorIdGrupoUsuario(int idGrupoUsuario);
        IEnumerable<EPerfil> GetPerfis(int idMenu);
        ValidationResult Inativar(int idMenu);
        ValidationResult Reativar(int idMenu);
        Menu GetPorIdentificadorPermissao(int identificadorPermissao);
        IQueryable<GrupoUsuarioMenuGrid> GetMenusPorModulo(int idModulo, int idGrupoUsuario, int idEmpresa, int perfil);

        List<GrupoUsuarioMenuGrid> GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idModulo, int idEmpresa, bool verificarAutorizacaoEmpresa);

        /// <summary>
        /// Retorna os itens do menu organizados na hierarquia por empresa
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        IQueryable<AutorizacaoEmpresaMenuGrid> GetArvoreMenuPorIdEmpresa(int idEmpresa);

        object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ValidationResult AlterarStatus(int menuId);
        ValidationResult Cadastrar(MenuCadastroRequest request);
        ValidationResult Editar(MenuCadastroRequest request);
        object ObterParaEditar(int id);
    }
}
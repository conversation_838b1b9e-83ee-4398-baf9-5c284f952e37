﻿using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO.Pix;
using ATS.Domain.Interface.Service;
using Newtonsoft.Json;

namespace ATS.WS.ControllersATS
{
    public class PixAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ITransacaoPixService _pixService;
        private readonly IUsuarioPermissaoFinanceiroService _usuarioPermissaoFinanceiroService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly ISolicitacaoChavePixService _solicitacaoChavePixService;

        public PixAtsController(
            IUserIdentity userIdentity, 
            ITransacaoPixService pixService, 
            IUsuarioPermissaoFinanceiroService usuarioPermissaoFinanceiroService,
            IParametrosEmpresaService parametrosEmpresaService,
            ISolicitacaoChavePixService solicitacaoChavePixService)
        {
            _userIdentity = userIdentity;
            _pixService = pixService;
            _usuarioPermissaoFinanceiroService = usuarioPermissaoFinanceiroService;
            _parametrosEmpresaService = parametrosEmpresaService;
            _solicitacaoChavePixService = solicitacaoChavePixService;
        }
        
        /// <summary>
        /// Retorna se um proprietário está parametrizado para receber pagamentos Pix
        /// </summary>
        /// <param name="idProprietario"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult VerificarProprietario(int? idProprietario = null)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || idProprietario == null)
                    return ResponderErro("Não foi possível validar a permissão.");

                var valido = _pixService.VerificarProprietario(_userIdentity.IdEmpresa.Value, idProprietario.Value);

                return valido ? ResponderSucesso(string.Empty) : ResponderErro(string.Empty);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro("Erro ao validar a permissão.");
            }
        }

        /// <summary>
        /// Retorna o saldo da conta Pix da empresa
        /// </summary>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Saldo()
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar o saldo.");

                var permissao = _usuarioPermissaoFinanceiroService.GetParametroPermissaoFinanceiro(_userIdentity.IdUsuario, 
                    EBloqueioFinanceiroTipo.permiteVisualizarSaldoPixTopbar);
                
                if(permissao == null || !permissao.DesbloquearFinanceiro)
                    return ResponderErro("Sem permissão para consultar o saldo.");

                var conta = _pixService.ConsultarContaPix(_userIdentity.IdEmpresa.Value);

                return ResponderSucesso("", (conta?.Value?.Balance ?? 0).ToString("C2"));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro("Erro ao validar a permissão.");
            }
        }

        /// <summary>
        /// Gera um QR Code para recebimento na conta Pix da empresa
        /// </summary>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult GerarQrCode()
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível gerar o QR Code. Dados inválidos.");
            
                var retorno = _pixService.GerarQrCode(_userIdentity.IdEmpresa.Value);

                if (!retorno.Success) return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso("QR Code gerado com sucesso.", retorno.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro("Erro ao gerar QR Code.");
            }
        }
        
        /// <summary>
        /// Listagem das transferencias Pix baseadas no retorno da Timeline da conta Pix pela Biz
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid([FromBody] TransferenciaPixGridAppRequest request)
        {
            try
            {
                if (request.Take < 15) request.Take = 15;
                if (request.Take > 50) request.Take = 50;

                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar as transferências. Dados inválidos.");

                var retorno = _pixService.ConsultarGrid(_userIdentity.IdEmpresa.Value, 
                    request.Page, request.Take, request.DataInicial, request.DataFinal, request.Filters);

                if (!retorno.Success) return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso(retorno.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Relatorio da listagem de transferencias
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public ActionResult GerarRelatorioGrid(string json)
        {
            if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                return ResponderErro("Não foi possível obter o relatório. Dados inválidos.");
            
            var request = JsonConvert.DeserializeObject<TransferenciaPixGridRelatorioRequest>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _pixService.GerarRelatorioGrid(_userIdentity.IdEmpresa.Value, request.Dados, request.Extensao);

            var mimeType = string.Empty;

            switch (request.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de transferências Pix.{request.Extensao}");
        }
        
        /// <summary>
        /// Retorna os detalhes de uma transferencia Pix pelo endToEndId para comprovante
        /// </summary>
        /// <param name="endToEndId"></param>
        /// <param name="tipo"></param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        [System.Web.Mvc.HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTransferencia(string endToEndId, ETipoTransferenciaPix tipo)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar as transferências. Dados inválidos.");

                if (tipo == ETipoTransferenciaPix.Indefinido)
                    throw new InvalidOperationException("Tipo de transferência inválido para consulta.");
                
                var retorno = _pixService.ConsultarTransferencia(_userIdentity.IdEmpresa.Value, endToEndId, tipo);
                return retorno.Success
                    ? ResponderSucesso(retorno.Value)
                    : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Retorna os detalhes de uma transferencia Pix pelo idViagemEvento para comprovante
        /// </summary>
        /// <param name="idViagemEvento"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTransferenciaPorEvento(int idViagemEvento)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar as transferências. Dados inválidos.");

                var retorno = _pixService.ConsultarTransferenciaPorEvento(_userIdentity.IdEmpresa.Value, idViagemEvento);
                return retorno.Success
                    ? ResponderSucesso(retorno.Value)
                    : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Cadastra uma chave Pix para a empresa no DICT passando pela api da Biz
        /// </summary>
        /// <param name="chave"></param>
        /// <param name="tipo"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [EnableLogAudit]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarChave([FromBody] string chave, [FromBody] ETipoChavePix tipo)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível cadastrar a chave Pix. Dados inválidos.");

                var retorno = _pixService.CadastrarChave(_userIdentity.IdEmpresa.Value, chave, tipo);
                return retorno.Success
                    ? ResponderSucesso("Chave Pix cadastrada com sucesso.")
                    : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Deleta uma chave da empresa no DICT pela api da Biz
        /// </summary>
        /// <param name="chave"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [EnableLogAudit]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult DeletarChave(string chave)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível deletar a chave Pix. Dados inválidos.");

                var retorno = _pixService.DeletarChave(_userIdentity.IdEmpresa.Value, chave);
                return retorno.Success
                    ? ResponderSucesso("Chave Pix deletada com sucesso.")
                    : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Listagem das chaves Pix da empresa
        /// </summary>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarChavesPixGrid()
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar as chaves Pix. Dados inválidos.");

                var retorno = _pixService.ConsultarChaves(_userIdentity.IdEmpresa.Value);
                return retorno.Success
                    ? ResponderSucesso(retorno.Value)
                    : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Consulta os limites configurados na Biz da conta da empresa 
        /// </summary>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarLimites()
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar as transferências. Dados inválidos.");

                var retorno = _pixService.ConsultarLimites(_userIdentity.IdEmpresa.Value);

                return retorno.Success ? ResponderSucesso(retorno.Value) : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Solicita uma alteracao do limite da conta Pix da empresa na Biz
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [EnableLogAudit]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult AlterarLimites([FromBody] AlterarLimitesPixRequest request)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível solicitar a alteração de limites. Dados inválidos.");

                if (request.Valor <= 0)
                    return ResponderErro("Valor inválido.");
                
                var retorno = _pixService.AlterarLimite(_userIdentity.IdEmpresa.Value, request);
                return retorno.Success ? ResponderSucesso(retorno.Value.Mensagem) : ResponderErro(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Lista as solicitações de cadastros de chaves pix para os proprietários da empresa,
        /// necessario parametro de gestao de cadastros de chave pix no usuario
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridSolicitacaoChavePixProprietario([FromBody] SolicitacaoChavePixGridAppRequest request)
        {
            try
            {
                if (request.Take < 15) request.Take = 15;
                if (request.Take > 50) request.Take = 50;

                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível consultar as transferências. Dados inválidos.");

                var retorno = _solicitacaoChavePixService.ConsultarGridSolicitacaoChavePixProprietario(
                    request.Page, request.Take, request.Filters, request.Order);

                if (!retorno.Success) return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso(retorno.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
     
        /// <summary>
        /// Aprova ou reprova uma solicitacao de cadastro de chave pix,
        /// necessario parametro de gestao de cadastros de chave pix no usuario
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [Validate2FA]
        public JsonResult AlterarStatusSolicitacaoChavePixProprietario([FromBody] SolicitacaoChavePixAlterarStatusAppRequest request)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível alterar o status da solicitação. Dados inválidos.");

                var retorno = _solicitacaoChavePixService.AlterarStatusSolicitacaoChavePixProprietario(request);

                if (!retorno.Success) return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridGestaoAlcadas([FromBody] TransferenciaPixGridRequest request)
        {
            try
            {
                if (request.Take < 10) request.Take = 10;
                if (request.Take > 500) request.Take = 500;

                if (_userIdentity.IdUsuario == 0 || !_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Não foi possível consultar as transferências. Dados inválidos.");

                var retorno = _pixService.ConsultarGridGestaoAlcadas(request.Page, 
                    request.Take, request.DataInicial, request.DataFinal, request.Order, request.Filters);

                if (!retorno.Success) return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso(retorno.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatusParcelaGestaoAlcadas([FromBody] AlterarStatusParcelaPixGestaoAlcadasRequest request)
        {
            try
            {
                if (_userIdentity.IdUsuario == 0 || !_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro($"Não foi possível {(request.Aprovar ? "aprovar" : "cancelar")} a parcela. Dados inválidos.");

                var validarSenha = _parametrosEmpresaService.GetSolicitarSenhaTransacionalPix(_userIdentity.IdEmpresa.Value);
                
                if (string.IsNullOrWhiteSpace(request.Senha) && request.Aprovar && validarSenha)
                    return ResponderErro($"Senha transacional não informada.");
                
                if (!string.IsNullOrWhiteSpace(request.Senha) && request.Aprovar && validarSenha)
                {
                    var retornoValidarSenha = _pixService.ValidarSenhaCartaoPixGestaoAlcadas(request.IdViagemEvento, request.Senha);
                    if (!retornoValidarSenha.Success) return ResponderErro($"Não foi possível validar a senha transacional.");
                }

                var retorno = request.Aprovar 
                    ? _pixService.EfetuarTransferenciaPixGestaoAlcadas(request.IdViagemEvento) 
                    : _pixService.CancelarTransferenciaPixGestaoAlcadas(request.IdViagemEvento);

                if (!retorno.Success) return ResponderErro(retorno.Messages.FirstOrDefault());

                return ResponderSucesso(retorno.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }

        [System.Web.Mvc.HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult SolicitarSenhaTransacional()
        {
            try
            {
                if (_userIdentity.IdUsuario == 0 || !_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro($"Não foi possível consultar o parâmetro.");

                var retorno = _parametrosEmpresaService.GetSolicitarSenhaTransacionalPix(_userIdentity.IdEmpresa.Value);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Cria uma solicitacao de cadastro de chave pix para um proprietario
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [EnableLogRequest]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [Validate2FA]
        public JsonResult CadastrarChaveProprietario([FromBody] CadastrarChavePixProprietarioRequest @params)
        {
            try
            {
                var retorno = _solicitacaoChavePixService.CadastrarChaveProprietario(@params);

                return Responder(retorno.Success, retorno.Value?.Mensagem ?? retorno.Messages.FirstOrDefault(), null);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Download do layout da planilha para solicitacoes em massa de chaves pix
        /// </summary>
        /// <returns></returns>
        [System.Web.Mvc.HttpGet]
        [Expor(EApi.Portal)]
        public FileResult DownloadLayoutPlanilhaSolicitacaoChavePix()
        {
            var dirInfo = new DirectoryInfo("~/Files/Layout_Solicitacao_Chave.xlsx");
            return File(dirInfo.ToString(), ConstantesUtils.ExcelMimeType, string.Concat("Layout Planilha Solicitacao Chave Pix", ConstantesUtils.ExcelXmlExtention));
        }
        
        /// <summary>
        /// Valida a planilha de solicitacoes em massa de chaves pix
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarPlanilhaSolicitacaoChavePixProprietario(HttpPostedFileBase file)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível validar a planilha. Dados inválidos.");

                Logger.Info($"Solicitacoes Chave Pix: Iniciando processo de importação em {DateTime.Now}");
                
                var response = _solicitacaoChavePixService.ValidarPlanilha(file);

                if (!response.Success) return ResponderErro(response.Messages.FirstOrDefault());
                
                return ResponderSucesso(response.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
        
        /// <summary>
        /// Efetua o cadastro das linhas validadas da planilha de solicitacoes em massa de chaves pix
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarPlanilhaChavePixProprietario([FromBody] CadastrarPlanilhaChavePixProprietarioRequest request)
        {
            try
            {
                if (_userIdentity.IdEmpresa == null || _userIdentity.IdEmpresa == 0 || _userIdentity.IdUsuario == 0)
                    return ResponderErro("Não foi possível cadastrar a planilha. Dados inválidos.");

                var response = _solicitacaoChavePixService.CadastrarPlanilhaChavePixProprietario(request);

                if (!response.Success) return ResponderErro(response.Messages.FirstOrDefault());
                
                return ResponderSucesso(response.Value.Mensagem);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }
    }
}
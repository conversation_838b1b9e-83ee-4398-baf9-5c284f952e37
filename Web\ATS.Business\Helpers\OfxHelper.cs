using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

namespace ATS.Domain.Helpers
{
    public static class OfxHelper
    {
        public static byte[] GenerateOfxResponse(OfxReportRequest request)
        {
            if (request.Itens == null)
                throw new InvalidOperationException("Nenhum item informado.");

            var accountType = "CHECKING"; // Options: CHECKING, SAVINGS, etc.
            var currency = "BRL";
            var lang = "POR";
            var dateServerSaldo = request.DataSaldo.ToString("yyyyMMddhhmmss") + "[-3:BRT]";
            var dateStart = request.DataInicio.ToString("yyyyMMddhhmmss") + "[-3:BRT]";
            var dateEnd = request.DataFim.ToString("yyyyMMddhhmmss") + "[-3:BRT]";
            string bankId = "613"; // COD BANCO BIZ OMNI BANCO SA
            string accountId = request.Conta;
            string accountIdParametro = request.ContaParametrizado;

            // Build OFX file
            var sb = new StringBuilder();

            sb.AppendLine("OFXHEADER:100");
            sb.AppendLine("DATA:OFXSGML");
            sb.AppendLine("VERSION:102");
            sb.AppendLine("SECURITY:NONE");
            sb.AppendLine("ENCODING:UTF-8");
            sb.AppendLine("CHARSET:1252");
            sb.AppendLine("COMPRESSION:NONE");
            sb.AppendLine("OLDFILEUID:NONE");
            sb.AppendLine("NEWFILEUID:NONE");
            sb.AppendLine();
            sb.AppendLine("<OFX>");
            sb.AppendLine("<SIGNONMSGSRSV1>");
            sb.AppendLine("<SONRS>");
            sb.AppendLine("<STATUS>");
            sb.AppendLine("<CODE>0</CODE>");
            sb.AppendLine("<SEVERITY>INFO</SEVERITY>");
            sb.AppendLine("</STATUS>");
            sb.AppendLine($"<DTSERVER>{dateServerSaldo}</DTSERVER>");
            sb.AppendLine($"<LANGUAGE>{lang}</LANGUAGE>");
            sb.AppendLine("</SONRS>");
            sb.AppendLine("</SIGNONMSGSRSV1>");
            sb.AppendLine("<BANKMSGSRSV1>");
            sb.AppendLine("<STMTTRNRS>");
            sb.AppendLine("<STATUS>");
            sb.AppendLine("<CODE>0</CODE>");
            sb.AppendLine("<SEVERITY>INFO</SEVERITY>");
            sb.AppendLine("</STATUS>");
            sb.AppendLine("<STMTRS>");
            
            if (!string.IsNullOrWhiteSpace(accountId))
            {
                sb.AppendLine($"<CURDEF>{currency}</CURDEF>");
                sb.AppendLine("<BANKACCTFROM>");
                sb.AppendLine($"<BANKID>{bankId}</BANKID>");
                sb.AppendLine($"<ACCTID>{accountId}</ACCTID>");
                sb.AppendLine($"<ACCTTYPE>{accountType}</ACCTTYPE>");
                sb.AppendLine("</BANKACCTFROM>");
            }
            else if (!string.IsNullOrWhiteSpace(accountIdParametro))
            {
                sb.AppendLine($"<ACCTID>{accountIdParametro}</ACCTID>");
                sb.AppendLine($"<CURDEF>{currency}</CURDEF>");
            }
            else
                sb.AppendLine($"<CURDEF>{currency}</CURDEF>");
            
            sb.AppendLine("<BANKTRANLIST>");
            sb.AppendLine($"<DTSTART>{dateStart}</DTSTART>");
            sb.AppendLine($"<DTEND>{dateEnd}</DTEND>");

            foreach (var transaction in request.Itens)
            {
                sb.AppendLine("<STMTTRN>");
                sb.AppendLine($"<TRNTYPE>{(transaction.Valor > 0 ? "CREDIT" : "DEBIT")}</TRNTYPE>");
                sb.AppendLine($"<DTPOSTED>{transaction.DataHoraTransacao.ToString("yyyyMMddhhmmss") + "[-3:BRT]"}</DTPOSTED>");
                sb.AppendLine($"<TRNAMT>{transaction.Valor.ToString(CultureInfo.InvariantCulture)}</TRNAMT>");
                if(!string.IsNullOrWhiteSpace(transaction.Nome)) sb.AppendLine($"<NAME>{transaction.Nome}</NAME>");
                sb.AppendLine($"<MEMO>{transaction.Detalhes}</MEMO>");
                sb.AppendLine("</STMTTRN>");
            }

            sb.AppendLine("</BANKTRANLIST>");
            sb.AppendLine("<LEDGERBAL>");
            sb.AppendLine($"<BALAMT>{(request.Saldo ?? 0).ToString(CultureInfo.InvariantCulture)}</BALAMT>");
            sb.AppendLine($"<DTASOF>{dateServerSaldo}</DTASOF>");
            sb.AppendLine("</LEDGERBAL>");
            sb.AppendLine("</STMTRS>");
            sb.AppendLine("</STMTTRNRS>");
            sb.AppendLine("</BANKMSGSRSV1>");
            sb.AppendLine("</OFX>");

            return Encoding.UTF8.GetBytes(sb.ToString());
        }
    }

    public class OfxReportRequest
    {
        public DateTime DataFim { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataSaldo { get; set; } = DateTime.Now;
        public string Conta { get; set; }
        public string ContaParametrizado { get; set; }
        public decimal? Saldo { get; set; }
        public List<OfxReportRequestItem> Itens { get; set; }
    }

    public class OfxReportRequestItem
    {
        public decimal Valor { get; set; }
        public DateTime DataHoraTransacao { get; set; }
        public string Detalhes { get; set; }
        public string Nome { get; set; }
    }
}
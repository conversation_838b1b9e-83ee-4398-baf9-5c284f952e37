using System;
using System.Globalization;

namespace ATS.CrossCutting.IoC.Utils
{
    /// <summary>
    /// Métodos de formatações de valores
    /// </summary>
    public static class FormatUtils
    {
        #region Constantes

        /// <summary>
        /// Formato de data pt-Br para exibição em geral ao usuário (dd/MM/yyyy)
        /// </summary>
        public const string DatePtBr = "dd/MM/yyyy";

        /// <summary>
        /// Formato de data e hora pt-Br para exibição para o usuário (dd/MM/yyyy HH:mm:ss)
        /// </summary>
        public const string DateTimePtBr = "dd/MM/yyyy HH:mm:ss";
        
        public static readonly CultureInfo PtBr = CultureInfo.CreateSpecificCulture("pt-BR");


        #endregion
        
        #region DateTime

        /// <summary>
        /// Retornar o nome do dia da semana (Domingo, segunda, terça)
        /// </summary>
        /// <param name="value"></param>
        /// <param name="firstUpperCase">Primeira letra do resultado deve ser maiuscular</param>
        /// <returns></returns>
        public static string DiaSemana(this DateTime value, bool firstUpperCase = true)
        {
            var result = value.ToString("dddd", FormatUtils.PtBr);
            if (firstUpperCase && result.Length > 0)
                return char.ToUpper(result[0]) + result.Substring(1);

            return result;
        }

        /// <summary>
        /// Formatar data para padrão brasileiro dd/MM/yyyy
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public static string FormatDateBr(this DateTime aValue)
        {
            return aValue.ToString(DatePtBr);
        }

        /// <summary>
        /// Formatar data para padrão brasileiro dd/MM/yyyy ou retornar null
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public static string FormatDateBr(this DateTime? aValue)
        {
            return aValue?.FormatDateBr();
        }

        /// <summary>
        /// Formatar data para padrão brasileiro dd/MM/yyyy HH:mm:ss
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public static string FormatDateTimeBr(this DateTime aValue)
        {
            return aValue.ToString(DateTimePtBr);
        }

        /// <summary>
        /// Formatar data para padrão brasileiro dd/MM/yyyy HH:mm:ss ou retornar null
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public static string FormatDateTimeBr(this DateTime? aValue)
        {
            return aValue?.FormatDateTimeBr();
        }

        public static string FormatToLog(this DateTime value) => value.ToString("dd/MM/yyyy HH:mm:ss.fffff");

        #endregion
        
        #region Texto

        public static string CpfCnpj(string aValue)
        {
            if (string.IsNullOrWhiteSpace(aValue))
                return aValue;

            var lValue = aValue.OnlyNumbers2();
            if (!string.IsNullOrWhiteSpace(lValue))
            {
                try
                {
                    if (aValue.Length == 11)
                        return Convert.ToInt64(aValue).ToString(@"000\.000\.000\-00");

                    if (aValue.Length == 14)
                        return Convert.ToInt64(aValue).ToString(@"00\.000\.000\/0000\-00");
                }
                catch
                {
                    // Abafa
                }
            }

            return aValue;
        }

        #endregion
        
        #region Boolean

        public static string ToStringBr(this bool aValue)
        {
            return aValue ? "Sim" : "Não";
        }

        #endregion
        
        #region Valores

        // Conflito com outras partes do ATS
        /*/// <summary>
        /// Formatar dinheiro R$ 0.000,00
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public static string FormatMoney(this decimal aValue)
        {
            return aValue.ToString("C2");
        }*/

        #endregion
    }
}
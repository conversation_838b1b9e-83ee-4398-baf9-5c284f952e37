﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsoTipoEstabelecimentoRepository : Repository<UsoTipoEstabelecimento>, IUsoTipoEstabelecimentoRepository
    {
        public UsoTipoEstabelecimentoRepository(AtsContext context) : base(context)
        {
        }
        
        public void Delete(EUsoTipoEstabelecimento uso, int idTipoEstabelecimento)
        {
            try
            {
                var query = Context.Set<UsoTipoEstabelecimento>()
                    .Where(x => x.Uso == uso && x.IdTipoEstabelecimento == idTipoEstabelecimento);

                if (!query.Any()) return;

                Context.Set<UsoTipoEstabelecimento>().Remove(query.First());
                Context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{e.Message} {e.InnerException}");
                throw;
            }
        }
    }
}
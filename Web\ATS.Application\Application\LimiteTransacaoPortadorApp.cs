﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class LimiteTransacaoPortadorApp : BaseApp<ILimiteTransacaoPortadorService>, ILimiteTransacaoPortadorApp
    {
        private readonly IParametrosApp _parametrosApp;

        public LimiteTransacaoPortadorApp(ILimiteTransacaoPortadorService service, IParametrosApp parametrosApp) : base(service)
        {
            _parametrosApp = parametrosApp;
        }

        public BusinessResult LimitarValor(string documento, ETipoLimiteTransacaoPortador tipo, decimal valor)
        {
            return Service.LimitarValor(documento, tipo, valor);
        }

        public decimal GetLimite(string documento, ETipoLimiteTransacaoPortador tipo)
        {
            return Service.GetLimite(documento, tipo);
        }

        public IList<PortadorLimitesValor> GetLimites(string documento)
        {
            return Service.GetLimites(documento);
        }

        public void LimitarValoresPadrão(ETipoPessoa tipoPessoa, string cpfcnpj)
        {
            var limitePadraoTransferenciaCartaoCNPJ = _parametrosApp.GetLimitePadraoTransferenciaCartaoCNPJ();
            var limitePadraoTransferenciaCartaoCPF = _parametrosApp.GetLimitePadraoTransferenciaCartaoCPF();
            var limitePadraoTransferenciaTEDCNPJ = _parametrosApp.GetLimitePadraoTransferenciaTEDCNPJ();
            var limitePadraoTransferenciaTEDCPF = _parametrosApp.GetLimitePadraoTransferenciaTEDCPF();

            if (tipoPessoa == ETipoPessoa.Fisica)
            {
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.UnitarioTransferenciaCartoes, limitePadraoTransferenciaCartaoCPF);
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.DiarioTransferenciaCartoes, limitePadraoTransferenciaCartaoCPF);
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.UnitarioTransferenciaTED, limitePadraoTransferenciaTEDCPF);
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.DiarioTransferenciaTED, limitePadraoTransferenciaTEDCPF);
            }
            else
            {
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.UnitarioTransferenciaCartoes, limitePadraoTransferenciaCartaoCNPJ);
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.DiarioTransferenciaCartoes, limitePadraoTransferenciaCartaoCNPJ);
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.UnitarioTransferenciaTED, limitePadraoTransferenciaTEDCNPJ);
                LimitarValor(cpfcnpj, ETipoLimiteTransacaoPortador.DiarioTransferenciaTED, limitePadraoTransferenciaTEDCNPJ);
            }
        }
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Interface.Service
{
    public interface IDeclaracaoCiotService : IService<DeclaracaoCiot>
    {
//        DeclaracaoCiot ConsultarCiot(int viagemid);
        List<ItensGraficoPorEmpresa> ConsultaNumeroGraficoDeclaracaoCiot(int? idEmpresa, DateTime dataInicial, DateTime dataFinal, DateTime? dataSelecinada);
    }
}
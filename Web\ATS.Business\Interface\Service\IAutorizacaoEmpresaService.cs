﻿using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Service
{
    public interface IAutorizacaoEmpresaService : IService<AutorizacaoEmpresa>
    {
        AutorizacaoEmpresa Get(int idMenu, int idEmpresa);
        List<AutorizacaoEmpresa> Get(int idEmpresa);
        ValidationResult Delete(AutorizacaoEmpresa autorizacaoEmpresa);
        ValidationResult Add(AutorizacaoEmpresa autorizacaoEmpresa);
        object ConsultarGridAutorizacoesEmpresa(int take, int page, OrderFilters order, List<QueryFilters> filters);
    }
}

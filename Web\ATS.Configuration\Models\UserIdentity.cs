﻿using ATS.CrossCutting.IoC.Interfaces;

namespace ATS.CrossCutting.IoC.Models
{
    public class UserIdentity : IUserIdentity
    {
        public int IdUsuario { get; set; }
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public int Perfil { get; set; }
        public int? IdEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string IpUsuario { get; set; }
        public EUserOrigin? Origem { get; set; }
        
        public decimal Latitude { get; set; }
        
        public decimal Longitude { get; set; }
        
        public string Cidade { get; set; }
        
        public string Provedor { get; set; }
        
        public string Estado { get; set; }
    }
}

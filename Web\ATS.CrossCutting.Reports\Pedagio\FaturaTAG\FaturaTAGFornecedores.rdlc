<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsFaturaTagPedagio">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>44e454a4-20c5-4938-955e-daa6b833e1f8</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsFaturaTagValePedagio">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b29476b5-8311-47ad-8b2a-119991edb1e7</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsFaturaTagMensalidade">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b29476b5-8311-47ad-8b2a-119991edb1e7</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPedagioFaturaTAG">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>6cef8e0b-1062-4182-a45b-b6deadd0fb4f</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPedagioFaturaTAG1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>5a810fd5-806d-4ebd-8b80-39d621adfc85</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPedagioFaturaTAG2">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>37eadb4a-4a45-414d-b1b9-3b36cacb5f4d</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPedagioFaturaTAG3">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>dc443292-cdf6-4f1b-b9ee-cef32edb1f53</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="FaturaTagValePedagioMoveMaisDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPedagioFaturaTAG</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataCriacao">
          <DataField>DataCriacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="StatusTaxa">
          <DataField>StatusTaxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusValor">
          <DataField>StatusValor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Taxa">
          <DataField>Taxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Tipo">
          <DataField>Tipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemId">
          <DataField>ViagemId</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>PassagensValePedagioTagDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.PassagensValePedagioTagDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="FaturaTagValePedagioViaFacilDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPedagioFaturaTAG1</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataCriacao">
          <DataField>DataCriacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="StatusTaxa">
          <DataField>StatusTaxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusValor">
          <DataField>StatusValor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Taxa">
          <DataField>Taxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Tipo">
          <DataField>Tipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemId">
          <DataField>ViagemId</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>PassagensValePedagioTagDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.PassagensValePedagioTagDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="FaturaTagValePedagioVeloeDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPedagioFaturaTAG2</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataCriacao">
          <DataField>DataCriacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="StatusTaxa">
          <DataField>StatusTaxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusValor">
          <DataField>StatusValor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Taxa">
          <DataField>Taxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Tipo">
          <DataField>Tipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemId">
          <DataField>ViagemId</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>PassagensValePedagioTagDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.PassagensValePedagioTagDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="FaturaTagValePedagioConectCarDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPedagioFaturaTAG3</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataCriacao">
          <DataField>DataCriacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
        <Field Name="StatusTaxa">
          <DataField>StatusTaxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusValor">
          <DataField>StatusValor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Taxa">
          <DataField>Taxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Tipo">
          <DataField>Tipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemId">
          <DataField>ViagemId</DataField>
          <rd:TypeName>System.Nullable`1[[System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]]</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>PassagensValePedagioTagDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.PassagensValePedagioTagDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="Rectangle1">
            <ReportItems>
              <Image Name="Image2">
                <Source>Embedded</Source>
                <Value>logoextrattalaranjapreto</Value>
                <Sizing>FitProportional</Sizing>
                <Top>0.29986cm</Top>
                <Left>8.26993cm</Left>
                <Height>2.10266cm</Height>
                <Width>3.99458cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
              <Rectangle Name="Rectangle2">
                <KeepTogether>true</KeepTogether>
                <Top>2.57891cm</Top>
                <Left>0.02496cm</Left>
                <Height>0.46271cm</Height>
                <Width>20.93431cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <BackgroundColor>#ff7a26</BackgroundColor>
                </Style>
              </Rectangle>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Left>0.54434cm</Left>
            <Height>3.04162cm</Height>
            <Width>20.95928cm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <BottomBorder>
                <Style>None</Style>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle11">
            <ReportItems>
              <Textbox Name="Textbox2">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Emissão:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>16.45842cm</Left>
                <Height>0.52063cm</Height>
                <Width>1.66215cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox3">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Emissao.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox3</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>18.12058cm</Left>
                <Height>0.52062cm</Height>
                <Width>2.76458cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox1">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Vencimento:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>11.55544cm</Left>
                <Height>0.52062cm</Height>
                <Width>2.10312cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox4">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Vencimento.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox3</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>13.65857cm</Left>
                <Height>0.52063cm</Height>
                <Width>2.79986cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>3.04162cm</Top>
            <Left>0.54434cm</Left>
            <Height>0.69702cm</Height>
            <Width>20.95928cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Style>None</Style>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle12">
            <ReportItems>
              <Textbox Name="Textbox5">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>CNPJ:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.73834cm</Top>
                <Left>0.31243cm</Left>
                <Height>0.52062cm</Height>
                <Width>1.22118cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox6">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Razão Social:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>0.31243cm</Left>
                <Height>0.52062cm</Height>
                <Width>2.35006cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox7">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Endereço:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>2.20264cm</Top>
                <Left>0.31243cm</Left>
                <Height>0.6cm</Height>
                <Width>1.84736cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox10">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Endereco.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox10</rd:DefaultName>
                <Top>2.20264cm</Top>
                <Left>2.15979cm</Left>
                <Height>0.6cm</Height>
                <Width>12.0544cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox11">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Cnpj.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox10</rd:DefaultName>
                <Top>0.73834cm</Top>
                <Left>1.53361cm</Left>
                <Height>0.52062cm</Height>
                <Width>19.04528cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox8">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Email:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>8.26993cm</Left>
                <Height>0.52062cm</Height>
                <Width>1.21235cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox9">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Telefone:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>14.39058cm</Left>
                <Height>0.52062cm</Height>
                <Width>1.72388cm</Width>
                <ZIndex>6</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox14">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Telefone.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox14</rd:DefaultName>
                <Top>1.4968cm</Top>
                <Left>16.11446cm</Left>
                <Height>0.52944cm</Height>
                <Width>4.46442cm</Width>
                <ZIndex>7</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox15">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Email.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox14</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>9.48229cm</Left>
                <Height>0.52944cm</Height>
                <Width>4.73191cm</Width>
                <ZIndex>8</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox16">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!RazaoSocial.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox14</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>2.6625cm</Left>
                <Height>0.52944cm</Height>
                <Width>5.30506cm</Width>
                <ZIndex>9</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox12">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Nº Fatura:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>2.20263cm</Top>
                <Left>14.39058cm</Left>
                <Height>0.6cm</Height>
                <Width>1.84736cm</Width>
                <ZIndex>10</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox13">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!NumeroFatura.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox10</rd:DefaultName>
                <Top>2.20263cm</Top>
                <Left>16.24005cm</Left>
                <Height>0.6cm</Height>
                <Width>4.33883cm</Width>
                <ZIndex>11</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>3.73863cm</Top>
            <Left>0.54434cm</Left>
            <Height>3.12382cm</Height>
            <Width>20.95928cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle21">
            <ReportItems>
              <Textbox Name="Textbox41">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>www.extratta.com.br</Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                          <Color>#fb7f33</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox41</rd:DefaultName>
                <Top>0.7351cm</Top>
                <Left>4.99975cm</Left>
                <Height>0.6cm</Height>
                <Width>8.46694cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox42">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>0800 600 0096</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox42</rd:DefaultName>
                <Top>0.4176cm</Top>
                <Left>16.52506cm</Left>
                <Height>0.6cm</Height>
                <Width>4.36009cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox44">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value><EMAIL></Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox42</rd:DefaultName>
                <Top>1.12343cm</Top>
                <Left>16.52506cm</Left>
                <Height>0.6cm</Height>
                <Width>4.3601cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Image Name="Image1">
                <Source>Embedded</Source>
                <Value>email</Value>
                <Sizing>FitProportional</Sizing>
                <Top>1.14149cm</Top>
                <Left>15.62011cm</Left>
                <Height>0.58194cm</Height>
                <Width>0.72856cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
              <Image Name="Image5">
                <Source>Embedded</Source>
                <Value>telefone</Value>
                <Sizing>FitProportional</Sizing>
                <Top>0.4176cm</Top>
                <Left>15.62011cm</Left>
                <Height>0.5475cm</Height>
                <Width>0.72856cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
              <Image Name="Image3">
                <Source>Embedded</Source>
                <Value>logoextrattalaranjapreto</Value>
                <Sizing>FitProportional</Sizing>
                <Top>0.31176cm</Top>
                <Left>0.05258cm</Left>
                <Height>1.58231cm</Height>
                <Width>3.33606cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>27.44499cm</Top>
            <Left>0.54699cm</Left>
            <Height>1.89408cm</Height>
            <Width>20.97045cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox45">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Vale Pedágio Sem Parar</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>7.4834cm</Top>
            <Left>0.54731cm</Left>
            <Height>1.98713cm</Height>
            <Width>20.95929cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </TopBorder>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle27">
            <ReportItems>
              <Tablix Name="Tablix4">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>3.16893cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>3.09795cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.96107cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.29883cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.62185cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66146cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66215cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.47593cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox129">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Data da Emissão</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox158">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Código Viagem</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox157</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox78">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox130">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Tipo</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox56">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox59">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox131">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox60">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox57</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DataCriacao2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DataCriacao.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DataCriacao</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ViagemId2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ViagemId.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ViagemId</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Tipo2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Tipo.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Tipo1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Taxa2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Taxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Taxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusValor2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusValor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusValor1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusTaxa2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusTaxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusTaxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes4" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagValePedagioViaFacilDts</DataSetName>
                <Left>0.02199cm</Left>
                <Height>1.2cm</Height>
                <Width>20.94817cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Rectangle Name="Rectangle26">
                <ReportItems>
                  <Textbox Name="Textbox171">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Nenhuma emissão de vale pedágio encontrada no período</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox169</rd:DefaultName>
                    <Top>0.68242cm</Top>
                    <Left>5.87065cm</Left>
                    <Height>0.6cm</Height>
                    <Width>8.69267cm</Width>
                    <Visibility>
                      <Hidden>=CountRows("FaturaTagValePedagioViaFacilDts") &gt; 0</Hidden>
                    </Visibility>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.37639cm</Top>
                <Height>1.69744cm</Height>
                <Width>20.9645cm</Width>
                <ZIndex>1</ZIndex>
                <Visibility>
                  <Hidden>=CountRows("FaturaTagValePedagioViaFacilDts") &gt; 0</Hidden>
                </Visibility>
                <Style>
                  <Border>
                    <Style>Solid</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                  </TopBorder>
                  <BottomBorder>
                    <Color>#d9d9d9</Color>
                  </BottomBorder>
                </Style>
              </Rectangle>
            </ReportItems>
            <Top>9.50581cm</Top>
            <Left>0.54731cm</Left>
            <Height>3.07383cm</Height>
            <Width>20.97016cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox46">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Vale Pedágio Move Mais</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>12.57963cm</Top>
            <Left>0.54731cm</Left>
            <Height>1.98713cm</Height>
            <Width>20.9645cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </TopBorder>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle28">
            <ReportItems>
              <Tablix Name="Tablix5">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>3.16893cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>3.09795cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.96107cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.29883cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.62185cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66146cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66215cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.47593cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox132">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Data da Emissão</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox159">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Código Viagem</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox157</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox79">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox133">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Tipo</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox81">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox82">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox134">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox83">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox57</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DataCriacao3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DataCriacao.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DataCriacao</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ViagemId3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ViagemId.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ViagemId</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Tipo3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Tipo.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Tipo1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Taxa3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Taxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Taxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusValor3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusValor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusValor1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusTaxa3">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusTaxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusTaxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes5" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagValePedagioMoveMaisDts</DataSetName>
                <Left>0.02199cm</Left>
                <Height>1.2cm</Height>
                <Width>20.94817cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Rectangle Name="Rectangle29">
                <ReportItems>
                  <Textbox Name="Textbox172">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Nenhuma emissão de vale pedágio encontrada no período</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox169</rd:DefaultName>
                    <Top>0.61186cm</Top>
                    <Left>5.80422cm</Left>
                    <Height>0.6cm</Height>
                    <Width>8.69267cm</Width>
                    <Visibility>
                      <Hidden>=CountRows("FaturaTagValePedagioMoveMaisDts") &gt; 0</Hidden>
                    </Visibility>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.37639cm</Top>
                <Height>1.62688cm</Height>
                <Width>20.9645cm</Width>
                <ZIndex>1</ZIndex>
                <Visibility>
                  <Hidden>=CountRows("FaturaTagValePedagioMoveMaisDts") &gt; 0</Hidden>
                </Visibility>
                <Style>
                  <Border>
                    <Style>Solid</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                  </TopBorder>
                  <BottomBorder>
                    <Color>#d9d9d9</Color>
                  </BottomBorder>
                </Style>
              </Rectangle>
            </ReportItems>
            <Top>14.56676cm</Top>
            <Left>0.54731cm</Left>
            <Height>3.00327cm</Height>
            <Width>20.97016cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox47">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Vale Pedágio Veloe</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>17.57003cm</Top>
            <Left>0.54731cm</Left>
            <Height>1.98713cm</Height>
            <Width>20.9645cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </TopBorder>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle30">
            <ReportItems>
              <Tablix Name="Tablix6">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>3.16893cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>3.09795cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.96107cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.29883cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.62185cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66146cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66215cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.47593cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox135">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Data da Emissão</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox160">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Código Viagem</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox157</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox84">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox136">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Tipo</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox86">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox87">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox137">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox88">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox57</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DataCriacao4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DataCriacao.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DataCriacao</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ViagemId4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ViagemId.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ViagemId</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Tipo4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Tipo.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Tipo1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Taxa4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Taxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Taxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusValor4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusValor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusValor1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusTaxa4">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusTaxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusTaxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes6" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagValePedagioVeloeDts</DataSetName>
                <Left>0.02762cm</Left>
                <Height>1.2cm</Height>
                <Width>20.94817cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Rectangle Name="Rectangle31">
                <ReportItems>
                  <Textbox Name="Textbox173">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Nenhuma emissão de vale pedágio encontrada no período</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox169</rd:DefaultName>
                    <Top>0.61186cm</Top>
                    <Left>5.7852cm</Left>
                    <Height>0.6cm</Height>
                    <Width>8.69267cm</Width>
                    <Visibility>
                      <Hidden>=CountRows("FaturaTagValePedagioVeloeDts") &gt; 0</Hidden>
                    </Visibility>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.30583cm</Top>
                <Left>0.00563cm</Left>
                <Height>1.62688cm</Height>
                <Width>20.97013cm</Width>
                <ZIndex>1</ZIndex>
                <Visibility>
                  <Hidden>=CountRows("FaturaTagValePedagioVeloeDts") &gt; 0</Hidden>
                </Visibility>
                <Style>
                  <Border>
                    <Style>Solid</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                  </TopBorder>
                  <BottomBorder>
                    <Color>#d9d9d9</Color>
                  </BottomBorder>
                </Style>
              </Rectangle>
            </ReportItems>
            <Top>19.55716cm</Top>
            <Left>0.54168cm</Left>
            <Height>2.93271cm</Height>
            <Width>20.97579cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox48">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Vale Pedágio ConectCar</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>22.48987cm</Top>
            <Left>0.53647cm</Left>
            <Height>1.98713cm</Height>
            <Width>20.98097cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </TopBorder>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle32">
            <ReportItems>
              <Tablix Name="Tablix7">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>3.16822cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>3.09724cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.96036cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.29812cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.62114cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66075cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.66144cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.47522cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox138">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Data da Emissão</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox161">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Código Viagem</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox157</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox89">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox139">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Tipo</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox91">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox92">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox140">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox93">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox57</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DataCriacao5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DataCriacao.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DataCriacao</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ViagemId5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ViagemId.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ViagemId</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa6">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Tipo5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Tipo.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Tipo1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor6">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Taxa5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Taxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Taxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusValor5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusValor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusValor1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusTaxa5">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusTaxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusTaxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes7" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagValePedagioConectCarDts</DataSetName>
                <Left>0.02762cm</Left>
                <Height>1.2cm</Height>
                <Width>20.94249cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
              <Rectangle Name="Rectangle33">
                <ReportItems>
                  <Textbox Name="Textbox174">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Nenhuma emissão de vale pedágio encontrada no período</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox169</rd:DefaultName>
                    <Top>0.61186cm</Top>
                    <Left>5.7852cm</Left>
                    <Height>0.6cm</Height>
                    <Width>8.69267cm</Width>
                    <Visibility>
                      <Hidden>=CountRows("FaturaTagValePedagioConectCarDts") &gt; 0</Hidden>
                    </Visibility>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.30583cm</Top>
                <Left>0.00563cm</Left>
                <Height>1.62688cm</Height>
                <Width>20.9645cm</Width>
                <ZIndex>1</ZIndex>
                <Visibility>
                  <Hidden>=CountRows("FaturaTagValePedagioConectCarDts") &gt; 0</Hidden>
                </Visibility>
                <Style>
                  <Border>
                    <Style>Solid</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                  </TopBorder>
                  <BottomBorder>
                    <Color>#d9d9d9</Color>
                  </BottomBorder>
                </Style>
              </Rectangle>
            </ReportItems>
            <Top>24.477cm</Top>
            <Left>0.54731cm</Left>
            <Height>2.93271cm</Height>
            <Width>20.97013cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox66">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="Vigência: " + Parameters!Vigencia.Value</Value>
                    <Style>
                      <FontSize>8pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox62</rd:DefaultName>
            <Top>6.86245cm</Top>
            <Left>0.54168cm</Left>
            <Height>0.62146cm</Height>
            <Width>20.96459cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>35.46933cm</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>21.83469cm</Width>
      <Page>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>22.5cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="Cnpj">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Cnpj</Prompt>
    </ReportParameter>
    <ReportParameter Name="RazaoSocial">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>RazaoSocial</Prompt>
    </ReportParameter>
    <ReportParameter Name="Email">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Email</Prompt>
    </ReportParameter>
    <ReportParameter Name="Telefone">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Telefone</Prompt>
    </ReportParameter>
    <ReportParameter Name="Endereco">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Endereco</Prompt>
    </ReportParameter>
    <ReportParameter Name="Vigencia">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Vigencia</Prompt>
    </ReportParameter>
    <ReportParameter Name="Emissao">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Emissao</Prompt>
    </ReportParameter>
    <ReportParameter Name="Vencimento">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Vencimento</Prompt>
    </ReportParameter>
    <ReportParameter Name="NumeroFatura">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>6</NumberOfColumns>
      <NumberOfRows>5</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Cnpj</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>RazaoSocial</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Email</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Telefone</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Endereco</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Vigencia</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>Emissao</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>Vencimento</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>NumeroFatura</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="logoextrattalaranjapreto">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAYGBgYHBgcICAcKCwoLCg8ODAwODxYQERAREBYiFRkVFRkVIh4kHhweJB42KiYmKjY+NDI0PkxERExfWl98fKcBBgYGBgcGBwgIBwoLCgsKDw4MDA4PFhAREBEQFiIVGRUVGRUiHiQeHB4kHjYqJiYqNj40MjQ+TERETF9aX3x8p//CABEIAGIA0gMBIgACEQEDEQH/xAAxAAEAAgMBAAAAAAAAAAAAAAAABAUCAwYBAQEAAwEBAAAAAAAAAAAAAAAAAgMEBQH/2gAMAwEAAhADEAAAAuqAAPB7ExrlNats4h6AAAAAAAAY5RY+7cuZ359XQ6NtBbTjs0XWPZKzOhzg9AAAAAAAAAU8HoqHFuQJsOFthf6JGzn4VkHyyF/op9RexYeRlYx6outcqsLCLTTjdhNpy3y3QCXYcX2Z6ABT2HL06LLZDiZdPYewpu7n8pKt6+Xkqh6LMrY1puMKS4zNFX0lSVm+5yPeevfDCl6CEVHYV9gAPPYsVbBnbMHQ11/V1WimLfUs+KaNeUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAC/9oADAMBAAIAAwAAACEAASsAAAAAAAAAAL+osIAAAAAAAAAC/IsgQijiCQwAABgOqjQxBSCxSgAfIUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//EAAL/2gAMAwEAAgADAAAAEPPPI3/PPPPPPPPAzpilfPPPPPPPPKKd2QCBKLJOHNPKFIKnNMJDKANFPPd9JlPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP/8QAIxEAAgEEAgICAwAAAAAAAAAAAgMEAAUSEzAyAREQQiJQUv/aAAgBAgEBPwD5O4RALHy4KAxMch4nEQLMgDIqC+ltAWJwGrhM0xcg7H0q3wmTJn5dex0IiI4jx3yF5WzcHUqjPZIHwo+w1AijGV6+3JdZ
K9en7FUFjBkZlrxGksFqxMeN7NaTP+aFc+VLyMGeiZVyt2uMBJDr3qxulAZpatmP6f8A/8QAJxEAAgEDAwQABwAAAAAAAAAAAgMEAAESBSIyBhETMBAUMUJQYrL/2gAIAQMBAT8A+IafNMchjsohISxL1IFZuADPESv9af0vggmKfmVaVAKVLsN+Ac61WaEKLt5cAorkRd7+vp7UvKj5dp7g/miWiNm0Qwy3nWpTSlyTL7LcPZoUEms81+AVJhg5Jhmzd+1OUaXGo+Q+tIeRoB35UEqBDh4gxfYbVpesXKQwZB7C4Vry4rredTF5fh//xAA2EAACAQMCAwQHBwUBAAAAAAABAgMABBEFEhMhMRAiQVEgMkBCUmGRFBUjU2JxgSQzUGChwf/aAAgBAQABPwL/AAL3cSNtOa+3RfOo5BIuR7IWA6muLH8YoOp6HsuJeFHnx8OyNC7BRSIEUAeyXUPFjx4+FYqCThuD9aDArmrm44snyHSgatItq7j1Ps17BhuIPHr2PcSLblBQNWUPFfJ9UdjyJGpZjgCjrNrn3vpUNxFNHvQ5q31CCeTYud3zq4v4Ldgr5z5CtRvuHEFTIdhmtNvkwI3LF2ap9RtoG2sTnyFW+pwTyBFVs0+r2qMV7xq2v7e4O1T3vI1ql7wl4aMQ/wD5Wm3ybUiYsZGNapfsDwonII9atPvIpFSEbiwXnmptUtoXKHcSKg1K2nbaCQfn6LoHUg1JGY3KmsVw24mwePSoIRDGFHZrUp4iReGMmgNI4O0t3sda0QnjyDw21fI1pfCVPHmK062a4mNzN58q1tU/CbHeNWFjAsUMu3v4zWxbnVGU9C9R2ltCd6IF5dac6MGbPM5qPYL+Pgk7d4xWtqgeNgO8etWVjAiQybe/t61qsaC8AXq3X+ags7eDvIuDip20niuX5setT8D7Qv2cnHKh09G8nh4wQrk1mP8AL/7UEsCzqCnPwPbqoxfd71cChDopGeL/ANq2gs7d
GmiPIjrU7zX8rsg7qDlWjT7o2hPu9K1z+7EP01bXMDxKEcZVOYrShvvnb9zWruy2nL3mxVlDp7Q7pn73lmrJUbUhs9UEkVrR/qIx+ira4glQCNwcKOVXzAanlugK1d3SNZTPE+fD61p8Vi6sZ35+VbYW1BFh9TeMejcS8KMtT5Zix6mg/wCHurmTmrOfixc/WHXsuLSG4XDj9jX3JB+Y9PZRtbrBubaKt7WK3j2J/NRadBDNxULZ8qu7OG5AD+HQ1badFb8TDMdwxVrYxWxYoTzqaGOVCjjIr7lt/jerawt7dtyg58zV1Zw3QG/qPEVaWMdruKknNXWnwXLbjkN5ioLKGGFovWDdc0dFts8nYVBpttAwcZLDxPo3UnFf5DpTLS2Ja1z73UUFqBjE4b60pBGfZbnfswo61wJfgNRWrs43Ly7Li1O/ci9a4EvwGrTiAFWX/Zf/xAApEAEAAgIBAwMDBAMAAAAAAAABABEhMVEQQWEgcZFAgbFQocHRYPDx/9oACAEBAAE/If0FhYTdE8PxmedfSF2AT/uTUj0FO5geZaquVndsmoIPpLEaZ+yYKJSRTdaEAXwl3LNHtdFj7+E9Vy/TcuX68TFOP3ITQMu+B6K0ndfPiEIvtLC1BnMPKY3yRJXBeFfEepMui6uC6PjGhh93+5jJ47DUJJQuTGJWJhpo4lrZwqZl8abDSjW+cudyxhsjEfzJ3hDNRpoh1j0Gr9JBYSdiDTySiU6Zjlaq+6dq7by9FicPumGuHz7uCHv+DAxoXivuTJsZeT/UHT2S+CIPnr7s2JbZ4JeyFMu0woaylu5/aoMwyFt4J2i5bd2BXgHzYdz2qrAsDNLcypsyi+zctS+PTiMDd1V9oc6KbDssur65EtvuhuCbDwuMQz1t4Ixm+jgP5l5c5eyJ4V3943IF7hU8GnzMVGND9mVVZtqlVK5/FJG4R+TEUFzuJyj72JghTI7RSKDhNYh+ZAV6Rc32OWXgtLWEBcS3
tzKa3Z/t0PZZo2T/AFCU2uJy1GwNOU7blZPLbGYDCKwbmSZfXhi9KabZYxYs3UcYluSuy5WJNOyMphpXxBiiK5JWdinJC43CJ1Ca9CgZnwl0LZY0ZXDB7DkgoafpdtLg+3Trig5b7wAqbuG1c9ODwA0/S10rpUqV/hX/xAAoEAEAAgICAAUEAgMAAAAAAAABABEhMUFRIEBhcYEQkbHBYNGh4fD/2gAIAQEAAT8Q8Vnm187IBBYA2xTJXlLclytSz+nLnH9Ny5fM+VQtuxavLCabeXo5YF1UjygfuzRa9hSJp6lueVDkhxLuGK7j5Y0++36OnRYHfAeVBUVWRVU12+YZezhJc7RKoWRaLwuIAFQheWqgiPq7GZPqBSdJ3HucUyq2QN+GSDS4wtnaLorftFTM5pJuxb2RGUiwl7GFwkHi84ImZe4kSx86KRcdoNFrGhsXFEzj4vnXCF5rJBcbej2GKZWFJBHY/qEUnWL7jbMZmtbjuHIqlRhiyX/kwNBX/wBxgwJu3X9aO7F9LyyjS2sQSa0masZZ4ZXY5XcCgip64vzM43YbEr6Fdsw/W3HgJWzTruIjQJ4+ruWZvtARl2SLDvBc9fVv38J9qQ0LRT/bcpudGp8Pn6VMr3ueECa+u5lJFlY7sx/r/pqtMeLZ+Wzb9PlRlbqkEU2QkS/xtEcbN9xZTPycJgFoPr4Yxg5kK57FYNVkmXAlyqAk6Lvtwf8Ar3TB5DV2TY5fDTI1qQmCozdzhL+5lil2x6lRxAP4+X0uNbtUi22r2jERpYg5UtZIxcrk37MZ2lqYxYysuhMsA0BUBdsQmi7xC/7PCPZ6ymuIsogd65gOiGFnXqgrPqHB0qXt4qsIBlS9tlcT22Oxzs0r4B4FCtRUB2B32zZiAQr+7T7xFSImESX81oQV8QsTyrZelpshphcEv6CAA1VEK0Z54QDHJCtv5KL/2Q==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="email">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAADAklEQVR4nO2YXYhNURTHf2MYjJpiyOcUMuGFlM80SIrSJB5EISTNgyij5nXypORh8IAopXyVKFLyYKIJU/OEIaSUfH/M+LyMma09rcMyM+fcc8697t1uZ9W/mc5Za+3/2neftf97Q2KJJZZYYnkyUyCgYApJOUAiU6RsIfOANw6QiYv3wELvO5kEPHSAlImIJ8BUr4hp8nck0OwAORMSzcL5dw2fgOXyYBBwxgGSJg0uAKXCeQnQ5nWtn0CNvCgG9jtA1vjgCNBfuG4Cfsjzv5wagH7itAPodIC4EXQB9cKtSP7X73sFnJUlZm0l8MWR9rpGOJUAJ/rw6TOwCRgugXOAV3ks4h1QJVyGAtd8/HwTPAIqJcFE4EEeingMTBYOE4DWAN/ARG+B+ZJoGHA9h0XcBEbI2LOAl2n80yb8BqyWhAOBkzko4hwwWMZcEfI7DZXYtuftAR0jm2hQnXMr0BEyLvYgWyIMEnaytmUwWZEHPK921aXAxywU8RmoVuridIwcsQa+rXTOdOBZBkW8AGZKrnLgRsw8sQm0iJyxNj5Na/RDq8R60qglAz6xgu6qPaZIbVaNEXI0SozOUQncyVUhVnmWqWVlZ3VtGvkQJINWyWZr9wqk7R7/l4XYDrVLzd564Ku8s+KyVs3ubh/B2SnvvBy1ys/m2sAf2xmxK4Zyeg0slgEGAAd8/A6q72YBcEmO0RYXlWYqFl+/HCXityiCzgt1EquQxKNDdJWrwFj8bZz4BOVoAsYo/1uZFnJUrWWruZ6HnJ0PwF5gtqz5UlHR+4D2CG25SkmjQ3EKScnBCiUVvkdYr9lCB1CneKwL0F29HtjNba4E2l/jWB4KMD1wChginGbI7UlgIVamj5KACsduVe6rqx+rAK74FXJYOpK1ZXIyM46hXY7fXuerV+27+7yx
Ue0BdaJEjaPoAvYoFV7tXQd5F3Rlsmub/wSXlcSZ4nUDey6+5wA5ExFPlbTpPkq2OUAqLuynsRlZc6YAQL4JJIXQ4xdJLLHEEkuMXNsvflcw/bT5iToAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="telefone">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAEIAAABCCAYAAADjVADoAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEUElEQVR4nO2ba4hWRRjHf67miuJ6DRNEW9Q2KtEUhD64mpdIEETUwkUwJUvFD+Y9SXC9VIiy+kGJrdASYT8oah9ys9SgMlG6WISK4mXzgkppaaaWuzLwuLweZs5lznk9c17fPzzsh53znJnfzpl55plnoaiiinJIXYDJwKIUbC4wHChJG8Js4AbQlLL9DFSkBeENBwDk2nmgm81AustgFgKvAu0iPNsW+NOBwXttXRQAzwCbgTseJ2pg40P6eNmBQevsTJjOvwDsAu76OFK/GxnC1+sODFpn//t1ugLYHcFZg0x9P73pwKBNptVU4LaFs+pCAjETaLR0dhPoWQggBljOhFyrswShtrFZQKW0O2Vo9y+wEhgBTAS+8vGp1rZxwEvAelkLQoE4mABZNZtejAjikmYvLwNOanyrQeWqBbBN47PW8MkHghic4DQ7BpRGALE05C5zwNCuv8anKUg6GwRibcLf3LIIIKoMnR7habfV0K7M0+46Zu0LAvF1wiBuaeJ4E4jVhk6v8LT7TT4Fr4ZrfPbRtHsMuBAE4veEQTRJHBIGxD/A8562g2UX8rad52nXGTiiaVcPtMlppwCu8elrsxryAEJZeQgQ92fQx3JE1oXyubZHjtNrfP7Cyo4Dq4B3gO8D+tmsX/MEYkxIEGlbszbl6QXDsgaiKg/O//acPzIBojVwLmHnb3sWtikODNgUrT6gGQk6r9XkBJMM2pK0w14QJcB3MZ3+B7zmdZzj/xcHBu616brOPhvz4LUEfw2UtaPJEdvpl81eHsNxX8Kl/eoDToP5tisSX7Ty62gbOTjZvKAf4dVZ1g1T/K+LVIcAg2LaU0DLsJ0capmgqSG6ghZp1Y9307yQqbEAoRK5YyO+xy++UCfJCaSsUuBHCxjXJUcQF8QJ4DkcUYXl9VyDXAaF
0TTN858DnXBM0ywXzkMhUvxKTwJ/5awHq1y4oDWpzhLGbgnfg9QbmCO7gtPqKN+sDYztQft11lQBXLWEscXl6W6j0TEiwg2GnGNmNd8ShLJPC+0z+TAGjDrJKBeESoFvYsD4zJNlzrQ6Sh2SLYx9EYKm0TKTVKS7Xz7PKNU6eVc3SZvbwjgqMYRJaqf5yPDsWanCcUY9Y96LXPEJpKpDPL9JZqczMcalGDBuSSifu71OktRfmOdVWcErrmzPA4A/YsBokoumT4BvLfMhB10J0/sBF2PCSCoX+XTaMPoApx2AcVtKoVJVD9kR0obRGLL0Ma963DLDlbSpeCV1dQC+cO1KLy21kqq2tEDccWVbva/pCZQv2tgPOKhKiSQfJoi3cFTlwE8PCYLKqLXH8WP8+wFV/0mYqtLNhEYFFITF3S2eIEPqLpVySYPYSAZVAiww1Fba2LWszQbdOeXLBECo+9SC0ETgsiUEFbwVlLpKbqIxQgS52LUoMkmpRMteHyDqwmmH1Gc9EuolFXvvAR/IzykB/z5VVFFFEUv3AE3Xgc5ltdLfAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>bbea77d8-8b66-418e-a67d-c5baf0b8e7b9</rd:ReportID>
</Report>
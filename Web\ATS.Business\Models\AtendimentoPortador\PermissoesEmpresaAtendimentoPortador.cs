namespace ATS.Domain.Models.AtendimentoPortador
{
    public class PermissoesEmpresaAtendimentoPortador
    {
        public bool BloquearCartao { get; set; }
        public bool DesbloquearCartao { get; set; }
        public bool AlterarSenhaCartao { get; set; }
        public bool TransferenciaBancaria { get; set; }
        public bool TransferenciaCartoes { get; set; }
        public bool Resgate { get; set; }
        public bool EstornoResgate { get; set; }

        public bool AlgumaPermissao()
        {
            if (BloquearCartao)
                return true;
            if (DesbloquearCartao)
                return true;
            if (AlterarSenhaCartao)
                return true;
            if (TransferenciaBancaria)
                return true;
            if (TransferenciaCartoes)
                return true;
            if (<PERSON>s<PERSON>)
                return true;
            if (EstornoResgate)
                return true;
            
            return false;
        }
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Interface.Service;
using ATS.MongoDB.Context.Entities;
using MongoDB.Bson;

namespace ATS.WS.Services
{
    public class SrvDataMediaServer : SrvBase
    {
        private readonly IDataMediaServerApp _dataMediaServerApp;

        public SrvDataMediaServer(IDataMediaServerApp dataMediaServerApp)
        {
            _dataMediaServerApp = dataMediaServerApp;
        }

        public ObjectId Add(int type, string base64Data, string fileName, string mimeType)
        {
            return _dataMediaServerApp.Add(type, base64Data, fileName, mimeType);
        }

        public Media GetMedia(string _id)
        {
            return _dataMediaServerApp.GetMedia(_id);
        }

        public void DeleteByToken(string token)
        {
            _dataMediaServerApp.DeleteByToken(token);
        }
    }
}
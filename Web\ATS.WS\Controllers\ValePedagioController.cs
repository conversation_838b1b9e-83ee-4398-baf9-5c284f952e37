﻿using System;
using System.Text.RegularExpressions;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;

namespace ATS.WS.Controllers
{
    public class ValePedagioController : BaseController
    {
        private readonly IValePedagioApp _app;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public ValePedagioController(BaseControllerArgs baseArgs, IValePedagioApp app, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _app = app;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpGet]
        [EnableLogRequest]
        public JsonResult ConsultaExtratoSemParar(DateTime dataInicio, DateTime dataFim, string token, string cnpjAplicacao)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                return Responde(_app.GetExtratoSemParar(dataInicio, dataFim, cnpjAplicacao));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpGet]
        [EnableLogRequest]
        public JsonResult ConsultaVeiculoSemParar(string placa, string token, string cnpjAplicacao)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                if (placa == null || placa.Length < 7 || placa.Length > 8)
                    throw new InvalidOperationException("Placa em tamanho inválido");

                placa = Regex.Replace(placa, "[^a-zA-Z0-9]", "");

                return Responde(_app.GetStatusVeiculoSemParar(placa, cnpjAplicacao));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
    }
}
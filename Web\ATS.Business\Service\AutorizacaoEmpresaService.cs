﻿using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;

namespace ATS.Domain.Service
{
    public class AutorizacaoEmpresaService : ServiceBase, IAutorizacaoEmpresaService
    {
        private readonly IAutorizacaoEmpresaRepository _autorizacaoEmpresaRepository;
        private readonly IEmpresaRepository _empresaRepository;

        public AutorizacaoEmpresaService(IAutorizacaoEmpresaRepository autorizacaoEmpresaRepository, IEmpresaRepository empresaRepository)
        {
            _autorizacaoEmpresaRepository = autorizacaoEmpresaRepository;
            _empresaRepository = empresaRepository;
        }

        public ValidationResult Delete(AutorizacaoEmpresa autorizacaoEmpresa)
        {
            try
            {
                _autorizacaoEmpresaRepository.Delete(autorizacaoEmpresa);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(AutorizacaoEmpresa autorizacaoEmpresa)
        {
            try
            {
                _autorizacaoEmpresaRepository.Update(autorizacaoEmpresa);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Add(AutorizacaoEmpresa autorizacaoEmpresa)
        {
            try
            {
                _autorizacaoEmpresaRepository.Add(autorizacaoEmpresa);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public object ConsultarGridAutorizacoesEmpresa(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var empresas = _empresaRepository.All(x => x.Ativo);

            if (string.IsNullOrWhiteSpace(order?.Campo))
                empresas = empresas.OrderBy(x => x.IdEmpresa);
            else
                empresas = empresas.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            empresas = empresas.AplicarFiltrosDinamicos<Empresa>(filters);

            return new
            {
                totalItems = empresas.Count(),
                items = empresas.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdEmpresa,
                    x.RazaoSocial
                })
            };
        }

        public AutorizacaoEmpresa Get(int idMenu, int idEmpresa)
        {
            return _autorizacaoEmpresaRepository
                .Find(x => x.IdMenu == idMenu && x.IdEmpresa == idEmpresa)
                .FirstOrDefault();
        }

        public List<AutorizacaoEmpresa> Get(int idEmpresa)
        {
            return _autorizacaoEmpresaRepository
                .Find(x => x.IdEmpresa == idEmpresa).ToList();
        }
    }
}

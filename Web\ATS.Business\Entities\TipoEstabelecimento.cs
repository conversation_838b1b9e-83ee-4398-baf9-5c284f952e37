﻿using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class TipoEstabelecimento
    {
        public int IdTipoEstabelecimento { get; set; }
        public string Descricao { get; set; }
        public int? IdIcone { get; set; }
        public DateTime? DataUltimaAtualizacao { get; set; }
        public bool Ativo { get; set; } = true;
        public int? IdEmpresa { get; set; }

        #region Relacionamentos

        public virtual Empresa Empresa { get; set; }
        public virtual Icone Icone { get; set; }
        public virtual List<UsoTipoEstabelecimento> UsoTipoEstabelecimento { get; set; }

        #endregion

        #region Navegação inversa
        public virtual ICollection<Estabelecimento> Estabelecimentos { get; set; }
        public virtual ICollection<EstabelecimentoBase> EstabelecimentosBase { get; set; }
        #endregion

    }
}

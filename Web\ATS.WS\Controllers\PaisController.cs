﻿using System;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Controllers
{
    public class PaisController : BaseController
    {
        private readonly IPaisApp _paisApp;

        // GET: Pais
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar()
        {
            try
            {
                var paises = _paisApp
                   .Consultar(string.Empty)
                   .OrderBy(x => x.Nome)
                   .Select(x => new { x.IdPais, Descricao = x.Nome });

                return new JsonResult().Responde(paises);
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        public PaisController(BaseControllerArgs baseArgs, IPaisApp paisApp) : base(baseArgs)
        {
            _paisApp = paisApp;
        }
    }
}
﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class RotaTrajetoMap : EntityTypeConfiguration<RotaTrajeto>
    {
        public RotaTrajetoMap()
        {
            ToTable("ROTA_TRAJETO");

            HasKey(x => new { x.IdTrajeto, x.IdRota });

            Property(x => x.IdTrajeto)
                    .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .HasMaxLength(1500);

            Property(x => x.Latitude)
               .HasPrecision(18, 8);

            Property(x => x.Longitude)
                .HasPrecision(18, 8);

            Property(t => t.DuracaoViagem).HasMaxLength(200);
            Property(t => t.GoogleIcon).HasMaxLength(200);
            Property(t => t.DistanciaViagem).HasMaxLength(200);
            
            
            HasRequired(x => x.Rota)
                .WithMany(x => x.RotaTrajeto)
                .HasForeignKey(x => x.IdRota);
        }
    }
}

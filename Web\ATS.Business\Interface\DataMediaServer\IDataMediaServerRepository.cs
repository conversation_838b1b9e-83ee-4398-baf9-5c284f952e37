﻿using System.Collections.Generic;
using ATS.MongoDB.Context.Entities;
using MongoDB.Bson;

namespace ATS.Domain.Interface.DataMediaServer
{
    public interface IDataMediaServerRepository
    {
        ObjectId Add(int type, string base64Data, string fileName, string mimeType = null);
        Media GetMedia(string _id);
        void DeleteByToken(string token);
        List<Media> GetListMedia(List<string> listId);
    }
}

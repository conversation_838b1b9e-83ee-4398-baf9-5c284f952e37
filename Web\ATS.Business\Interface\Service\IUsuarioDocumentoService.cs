﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioDocumentoService : IService<UsuarioDocumento>
    {
        IQueryable<UsuarioDocumento> GetDocumentos(int idMotorista);
        List<Tuple<int, DateTime?>> GetDocumentoCNHPorIdUsuIdTipoDoc(List<int> usuariosMotoristas, int tpDoc);

        UsuarioDocumento GetDocCNH(int idUsuario);
        UsuarioDocumento GetDocCNH(int idUsuario, int idTipoDoc);
        void RemoverItem(UsuarioDocumento item);
        void AtualizarAvisoValidadeCNH(Motorista motorista);
        void UpdateusuarioDocumento(UsuarioDocumento doc);
    }
}
﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ProtocoloEvento
    {
        /// <summary>
        /// Código de evento do protocolo
        /// </summary>
        public int IdProtocoloEvento { get; set; }

        /// <summary>
        /// Código do protocolo
        /// </summary>
        public int IdProtocolo { get; set; }

        /// <summary>
        /// Código do evento da viagem
        /// </summary>
        public int IdViagemEvento { get; set; }

        /// <summary>
        /// Código do motivo
        /// </summary>
        public int? IdMotivo { get; set; }

        /// <summary>
        /// Detalhamento do motivo
        /// </summary>
        public string Detalhamento { get; set; }


        public bool OcorrenciaPendente { get; set; }

        public DateTime? DataOcorrencia { get; set; }
        

        public bool EventoAnalisado { get; set; }

        /// <summary>
        /// Status de evento do protocolo
        /// </summary>
        public EStatusProtocoloEvento Status { get; set; }

        public decimal? ValorTotalPagamento { get; set; }
        public decimal? ValorPagamento { get; set; }
        public decimal? IRRPF { get; set; }
        public decimal? INSS { get; set; }
        public decimal? SESTSENAT { get; set; }
        public decimal? ValorBruto { get; set; }
        public string NumeroRecibo { get; set; }
        public decimal? PesoChegada { get; set; }
        public decimal? ValorDifFreteMotorista { get; set; }
        public decimal? ValorQuebraMercadoria { get; set; }

        public decimal? ValorDesconto { get; set; }
        public int? IdMotivoDesconto { get; set; }
        public int? IdMotivoOcorrencia { get; set; }
        public string DescricaoMotivoDesconto { get; set; }
        public string DetalhamentoOcorrencia { get; set; }
        public string ObservacaoDesconto { get; set; }
        public int? IdProtocoloEvento_Vinculado { get; set; }
        public int? IdProtocoloOrigem { get; set; }
        public string TokenAnexoDesconto { get; set; }
        public EStatusAnaliseAbono? AnaliseAbono { get; set; }
        public DateTime? DataHoraAnaliseAbono { get; set; }
        public int? IdUsuarioAnaliseAbono { get; set; }
        public bool EventoReincidente { get; set; }
        public int? IdUsuarioCadOcorrencia { get; set; }
        public int? IdUsuarioBaixaOcorrencia { get; set; }
        public DateTime? DataBaixaOcorrencia { get; set; }

        #region Virtual Fields
        public virtual Protocolo Protocolo { get; set; }
        public virtual ViagemEvento ViagemEvento { get; set; }
        public virtual Motivo Motivo { get; set; }
        public virtual Motivo MotivoDesconto { get; set; }
        public virtual Motivo MotivoOcorrencia { get; set; }
        public virtual ProtocoloEvento ProtocoloEvento_Vinculado { get; set; }
        public virtual Usuario UsuarioAnaliseAbono { get; set; }        
        public virtual Usuario UsuarioOcorrencia { get; set; }
        #endregion

        #region Reverse navigation 
        public virtual List<ProtocoloEvento> ProtocolosEventos_Vinculados { get; set; }
        #endregion
    }
}
﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

using System.Web;
using ATS.Data.Repository.External.SistemaInfo;

namespace SistemaInfo.MicroServices.Rest.Infra.ApiClient
{
    #pragma warning disable // Disable all warnings

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class Client : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Infra/Api";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public Client(SistemaInfoMicroServiceClientParams configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Atualizar log com os dados de resposta</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task LogIntegracoesPutAsync(LogIntegracaoUpdateApiRequest log, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return LogIntegracoesPutAsync(log, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Atualizar log com os dados de resposta</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void LogIntegracoesPut(LogIntegracaoUpdateApiRequest log, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            System.Threading.Tasks.Task.Run(async () => await LogIntegracoesPutAsync(log, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Atualizar log com os dados de resposta</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task LogIntegracoesPutAsync(LogIntegracaoUpdateApiRequest log, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/LogIntegracoes");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(log, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("PUT");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Inserir log de integrações</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task LogIntegracoesPostAsync(LogIntegracaoInsertApiRequest log, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return LogIntegracoesPostAsync(log, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Inserir log de integrações</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void LogIntegracoesPost(LogIntegracaoInsertApiRequest log, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            System.Threading.Tasks.Task.Run(async () => await LogIntegracoesPostAsync(log, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Inserir log de integrações</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task LogIntegracoesPostAsync(LogIntegracaoInsertApiRequest log, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/LogIntegracoes");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(log, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class NotificacaoClient : SistemaInfoMicroServiceBaseClient
    {
        private string _baseUrl = "/Infra/Api";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public NotificacaoClient(SistemaInfoMicroServiceClientParams configuration) : base(configuration)
        {
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Inserir Notificacao de WebHook</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task WebhookAsync(NotificacaoWebhookApiRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return WebhookAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Inserir Notificacao de WebHook</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void Webhook(NotificacaoWebhookApiRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            System.Threading.Tasks.Task.Run(async () => await WebhookAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Inserir Notificacao de WebHook</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task WebhookAsync(NotificacaoWebhookApiRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Notificacao/Webhook");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(notificacao, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Inserir Notificacao de Email</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task EmailAsync(NotificacaoEmailApiRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EmailAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Inserir Notificacao de Email</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void Email(NotificacaoEmailApiRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            System.Threading.Tasks.Task.Run(async () => await EmailAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Inserir Notificacao de Email</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task EmailAsync(NotificacaoEmailApiRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Notificacao/Email");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(notificacao, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Inserir Notificacao de Sms</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task SmsAsync(NotificacaoSmsBusRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return SmsAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Inserir Notificacao de Sms</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void Sms(NotificacaoSmsBusRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            System.Threading.Tasks.Task.Run(async () => await SmsAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Inserir Notificacao de Sms</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task SmsAsync(NotificacaoSmsBusRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Notificacao/Sms");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(notificacao, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Inserir Notificacao de Push</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task PushAsync(NotificacaoPushBusRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return PushAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Inserir Notificacao de Push</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public void Push(NotificacaoPushBusRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            System.Threading.Tasks.Task.Run(async () => await PushAsync(notificacao, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Inserir Notificacao de Push</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task PushAsync(NotificacaoPushBusRequest notificacao, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Notificacao/Push");
    
            var client_ = await CreateHttpClientAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                using (var request_ = await CreateHttpRequestMessageAsync(cancellationToken).ConfigureAwait(false))
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(notificacao, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        if (response_.Content != null && response_.Content.Headers != null)
                        {
                            foreach (var item_ in response_.Content.Headers)
                                headers_[item_.Key] = item_.Value;
                        }
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            return;
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = response_.Content == null ? null : await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", (int)response_.StatusCode, responseData_, headers_, null);
                        }
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value != null && value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    

    /// <summary>Dados para persistência do log com conteúdo e informações da requisição efetuada</summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class LogIntegracaoInsertApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Guid? _id;
        private System.Guid? _parentId;
        private System.Guid? _rootId;
        private int? _nivel;
        private string _microServico;
        private string _aplicacao;
        private string _basePath;
        private string _metodo;
        private string _queryString;
        private string _verbo;
        private string _contentyType;
        private LogIntegracaoInsertApiRequestDirecao? _direcao;
        private System.DateTime? _dataRequisicao;
        private string _requisicao;
        private string _headerRequisicao;
        private string _ipRequisicao;
        private string _ipServidor;
        private string _hostNameServidor;
        private string _infoAdicional;
        private string _handlerClassName;
        private string _handlerMethodName;
    
        /// <summary>Identificador único da operação. Pré-inicializado com o valor Guid.NewGuid()</summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Em execuções em cadeia de microserviços, neste campo será armazenado o Id do log que originou o log atual</summary>
        [Newtonsoft.Json.JsonProperty("parentId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? ParentId
        {
            get { return _parentId; }
            set 
            {
                if (_parentId != value)
                {
                    _parentId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Em execuções em cadeia de microserviços, neste campo será armazenado o Id do log raiz que originou a execução em cadeia até inserir o log atual</summary>
        [Newtonsoft.Json.JsonProperty("rootId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? RootId
        {
            get { return _rootId; }
            set 
            {
                if (_rootId != value)
                {
                    _rootId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Nível do micros erviço executando o processo, indica por quantas aplicações diferentes a requisição já passou.
        /// Exemplo:
        /// 0 = Camanda web processando
        /// 1 = Camada de serviços da plataforma processando
        /// 2 = Microserviço específico da processadora de cartões processando</summary>
        [Newtonsoft.Json.JsonProperty("nivel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Nivel
        {
            get { return _nivel; }
            set 
            {
                if (_nivel != value)
                {
                    _nivel = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificador do recurso sendo processado (Exemplo: Cartao, Ciot, ATS).
        /// Esta informação é o micro serviço que está esta recebendo/enviando o comando de integração.</summary>
        [Newtonsoft.Json.JsonProperty("microServico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MicroServico
        {
            get { return _microServico; }
            set 
            {
                if (_microServico != value)
                {
                    _microServico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Nome da aplicação que capturou a requisição, ou que está enviando o comando de saída (Exemplo: SistemaInfo.Ciot.Api.exe, SistemaInfo.Ciot.Service.exe, etc).
        /// Esta informação é o nome do exe do micro serviço que está esta recebendo/enviando o comando de integração.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>URL base para fornecer recurso web (Exemplo: /Cartoes/Api, /Ciot/Api)</summary>
        [Newtonsoft.Json.JsonProperty("basePath", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BasePath
        {
            get { return _basePath; }
            set 
            {
                if (_basePath != value)
                {
                    _basePath = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Rota da aplicação executada para executar ação, ou nome da fila do RabbitMQ (Exemplo: /Operacoes/ConsultarExtrato, /Operacoes/VincularPortador, Cartao.AtivarVirtual.Request, Biz.ConsultaExtrato.Request)</summary>
        [Newtonsoft.Json.JsonProperty("metodo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Metodo
        {
            get { return _metodo; }
            set 
            {
                if (_metodo != value)
                {
                    _metodo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Argumentos na query string da requisição</summary>
        [Newtonsoft.Json.JsonProperty("queryString", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string QueryString
        {
            get { return _queryString; }
            set 
            {
                if (_queryString != value)
                {
                    _queryString = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>get, post, put, etc</summary>
        [Newtonsoft.Json.JsonProperty("verbo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Verbo
        {
            get { return _verbo; }
            set 
            {
                if (_verbo != value)
                {
                    _verbo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Formato de dados da integração: application/json, application/xml</summary>
        [Newtonsoft.Json.JsonProperty("contentyType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContentyType
        {
            get { return _contentyType; }
            set 
            {
                if (_contentyType != value)
                {
                    _contentyType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Input / Output</summary>
        [Newtonsoft.Json.JsonProperty("direcao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public LogIntegracaoInsertApiRequestDirecao? Direcao
        {
            get { return _direcao; }
            set 
            {
                if (_direcao != value)
                {
                    _direcao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Data e hora da requisição recepcionada pelo serviço específico</summary>
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Cópia do comando de requisição</summary>
        [Newtonsoft.Json.JsonProperty("requisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Requisicao
        {
            get { return _requisicao; }
            set 
            {
                if (_requisicao != value)
                {
                    _requisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Headers da requisição</summary>
        [Newtonsoft.Json.JsonProperty("headerRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HeaderRequisicao
        {
            get { return _headerRequisicao; }
            set 
            {
                if (_headerRequisicao != value)
                {
                    _headerRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Para Input: IP do client que está comunicando com o server
        /// Para Output: IP ou Link destino da comunicação</summary>
        [Newtonsoft.Json.JsonProperty("ipRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string IpRequisicao
        {
            get { return _ipRequisicao; }
            set 
            {
                if (_ipRequisicao != value)
                {
                    _ipRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>IP do servidor que recepcionou a integração</summary>
        [Newtonsoft.Json.JsonProperty("ipServidor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string IpServidor
        {
            get { return _ipServidor; }
            set 
            {
                if (_ipServidor != value)
                {
                    _ipServidor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>HostName do servidor que recepcionou a integração</summary>
        [Newtonsoft.Json.JsonProperty("hostNameServidor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HostNameServidor
        {
            get { return _hostNameServidor; }
            set 
            {
                if (_hostNameServidor != value)
                {
                    _hostNameServidor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Informação adicional para vincular no log</summary>
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Nome da classe que processou a operação</summary>
        [Newtonsoft.Json.JsonProperty("handlerClassName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HandlerClassName
        {
            get { return _handlerClassName; }
            set 
            {
                if (_handlerClassName != value)
                {
                    _handlerClassName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Nome do método que processou a operação</summary>
        [Newtonsoft.Json.JsonProperty("handlerMethodName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HandlerMethodName
        {
            get { return _handlerMethodName; }
            set 
            {
                if (_handlerMethodName != value)
                {
                    _handlerMethodName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static LogIntegracaoInsertApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<LogIntegracaoInsertApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    /// <summary>Dados para persistência do log com conteúdo e informações da resposta processada</summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class LogIntegracaoUpdateApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Guid? _id;
        private System.DateTime? _dataResposta;
        private string _resposta;
        private string _headerResposta;
        private int? _statusCodeResposta;
        private string _handlerClassName;
        private string _handlerMethodName;
    
        /// <summary>Íd do log gerado para armazenar a requisição</summary>
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Data e hora da resposta</summary>
        [Newtonsoft.Json.JsonProperty("dataResposta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataResposta
        {
            get { return _dataResposta; }
            set 
            {
                if (_dataResposta != value)
                {
                    _dataResposta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Cópia do comando de resposta</summary>
        [Newtonsoft.Json.JsonProperty("resposta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Resposta
        {
            get { return _resposta; }
            set 
            {
                if (_resposta != value)
                {
                    _resposta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Header da resposta</summary>
        [Newtonsoft.Json.JsonProperty("headerResposta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HeaderResposta
        {
            get { return _headerResposta; }
            set 
            {
                if (_headerResposta != value)
                {
                    _headerResposta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Código da resposta (Http 200, 404, 500, entre outros)</summary>
        [Newtonsoft.Json.JsonProperty("statusCodeResposta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? StatusCodeResposta
        {
            get { return _statusCodeResposta; }
            set 
            {
                if (_statusCodeResposta != value)
                {
                    _statusCodeResposta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Nome da classe que processou a operação</summary>
        [Newtonsoft.Json.JsonProperty("handlerClassName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HandlerClassName
        {
            get { return _handlerClassName; }
            set 
            {
                if (_handlerClassName != value)
                {
                    _handlerClassName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Nome do método que processou a operação</summary>
        [Newtonsoft.Json.JsonProperty("handlerMethodName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string HandlerMethodName
        {
            get { return _handlerMethodName; }
            set 
            {
                if (_handlerMethodName != value)
                {
                    _handlerMethodName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static LogIntegracaoUpdateApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<LogIntegracaoUpdateApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    /// <summary>Configuração da notificação de webhook</summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoWebhookApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _endpoint;
        private string _verbo;
        private string _requisicao;
        private System.Collections.Generic.Dictionary<string, string> _headers;
        private System.DateTime? _dataRequisicao;
        private string _infoAdicional;
        private string _aplicacao;
        private string _aplicacaoDestino;
        private int? _tempo;
        private NotificacaoPoliticaFalhaApiRequest _politicaFalha;
    
        /// <summary>Link destino para executar a integração</summary>
        [Newtonsoft.Json.JsonProperty("endpoint", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Endpoint
        {
            get { return _endpoint; }
            set 
            {
                if (_endpoint != value)
                {
                    _endpoint = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Verbo HTTP para executar a integração (POST, GET, PUT, outros)</summary>
        [Newtonsoft.Json.JsonProperty("verbo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Verbo
        {
            get { return _verbo; }
            set 
            {
                if (_verbo != value)
                {
                    _verbo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Conteúdo para transmitir no body da notificação</summary>
        [Newtonsoft.Json.JsonProperty("requisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Requisicao
        {
            get { return _requisicao; }
            set 
            {
                if (_requisicao != value)
                {
                    _requisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Conteúdo para transmitir no header da notificação</summary>
        [Newtonsoft.Json.JsonProperty("headers", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> Headers
        {
            get { return _headers; }
            set 
            {
                if (_headers != value)
                {
                    _headers = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Data e hora que foi solicitado o envio da notificação. Deve ser indicado a hora do servidor do client.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Informações adicionais para listar no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificação da aplicação que solicitou o envio da notificação.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificação da aplicação destino da notificação.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AplicacaoDestino
        {
            get { return _aplicacaoDestino; }
            set 
            {
                if (_aplicacaoDestino != value)
                {
                    _aplicacaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Tempo de vida em horas para reenvio da notificação em caso de falhas.
        /// Após este periodo o serviço na tentará mais reenviar automaticamente.</summary>
        [Newtonsoft.Json.JsonProperty("tempo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Tempo
        {
            get { return _tempo; }
            set 
            {
                if (_tempo != value)
                {
                    _tempo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Configurações de como se comportar durante as falhas de notificações</summary>
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoPoliticaFalhaApiRequest PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoWebhookApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoWebhookApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    /// <summary>Configuração em cenários de falhas no envio da notificação</summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoPoliticaFalhaApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _minutosParaNovaTentativa;
        private int? _minutosParaMonitorar;
        private int? _quantidadeMaximaTentativasReenvio;
        private System.Collections.ObjectModel.ObservableCollection<NotificacaoPoliticaFalhaAlertasApiRequest> _alertas;
    
        /// <summary>Minutos para aguardar entre novas tentativas de reenvio da notificação.
        /// Padrão: 1 minuto.</summary>
        [Newtonsoft.Json.JsonProperty("minutosParaNovaTentativa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? MinutosParaNovaTentativa
        {
            get { return _minutosParaNovaTentativa; }
            set 
            {
                if (_minutosParaNovaTentativa != value)
                {
                    _minutosParaNovaTentativa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Tempo de monitoramento das falhas pelo serviço de notificação.
        /// O serviço tentará reenviar uma notificação com falha durante X minutos, após este tempo sem sucesso, a mesma terá seu status alterado para "Expirada".
        /// Padrão: 4 horas (240 minutos).</summary>
        [Newtonsoft.Json.JsonProperty("minutosParaMonitorar", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? MinutosParaMonitorar
        {
            get { return _minutosParaMonitorar; }
            set 
            {
                if (_minutosParaMonitorar != value)
                {
                    _minutosParaMonitorar = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Quantidade máxima de tentativas de reenvio da notificação com falha.
        /// Se o serviço falhas X vezes na tentativa de notificação, a mesma terá seu status alterado para "Expirada".
        /// Padrão: null - Recurso desabilitado.</summary>
        [Newtonsoft.Json.JsonProperty("quantidadeMaximaTentativasReenvio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeMaximaTentativasReenvio
        {
            get { return _quantidadeMaximaTentativasReenvio; }
            set 
            {
                if (_quantidadeMaximaTentativasReenvio != value)
                {
                    _quantidadeMaximaTentativasReenvio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Configurações de envio de alertas a responsáveis em caso de falhas</summary>
        [Newtonsoft.Json.JsonProperty("alertas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<NotificacaoPoliticaFalhaAlertasApiRequest> Alertas
        {
            get { return _alertas; }
            set 
            {
                if (_alertas != value)
                {
                    _alertas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoPoliticaFalhaApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoPoliticaFalhaApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    /// <summary>Configurações de envio de alertas a responsáveis em caso de falhas</summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoPoliticaFalhaAlertasApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _falhasParaEnviarAlerta;
        private object _configuracao;
    
        /// <summary>Enviar alerta ao atingir esta quantidade de tentativas.
        /// Ao ocorrer a falha de número X, envia o alerta.
        /// Caso a mensagem expirar antes de chegar a este número de falhas, o alerta é executado também.</summary>
        [Newtonsoft.Json.JsonProperty("falhasParaEnviarAlerta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? FalhasParaEnviarAlerta
        {
            get { return _falhasParaEnviarAlerta; }
            set 
            {
                if (_falhasParaEnviarAlerta != value)
                {
                    _falhasParaEnviarAlerta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Configuração do alerta a enviar</summary>
        [Newtonsoft.Json.JsonProperty("configuracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public object Configuracao
        {
            get { return _configuracao; }
            set 
            {
                if (_configuracao != value)
                {
                    _configuracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoPoliticaFalhaAlertasApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoPoliticaFalhaAlertasApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoBaseApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private System.DateTime? _dataRequisicao;
        private string _infoAdicional;
        private string _aplicacao;
        private string _aplicacaoDestino;
        private int? _tempo;
        private NotificacaoPoliticaFalhaApiRequest _politicaFalha;
    
        /// <summary>Data e hora que foi solicitado o envio da notificação. Deve ser indicado a hora do servidor do client.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Informações adicionais para listar no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificação da aplicação que solicitou o envio da notificação.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificação da aplicação destino da notificação.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AplicacaoDestino
        {
            get { return _aplicacaoDestino; }
            set 
            {
                if (_aplicacaoDestino != value)
                {
                    _aplicacaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Tempo de vida em horas para reenvio da notificação em caso de falhas.
        /// Após este periodo o serviço na tentará mais reenviar automaticamente.</summary>
        [Newtonsoft.Json.JsonProperty("tempo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Tempo
        {
            get { return _tempo; }
            set 
            {
                if (_tempo != value)
                {
                    _tempo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Configurações de como se comportar durante as falhas de notificações</summary>
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoPoliticaFalhaApiRequest PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoBaseApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoBaseApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    /// <summary>Configuração da notificação de e-mail</summary>
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoEmailApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private NotificacaoEmailSmtpApiRequest _smtp;
        private NotificacaoEmailMessageApiRequest _message;
        private System.DateTime? _dataRequisicao;
        private string _infoAdicional;
        private string _aplicacao;
        private string _aplicacaoDestino;
        private int? _tempo;
        private NotificacaoPoliticaFalhaApiRequest _politicaFalha;
    
        [Newtonsoft.Json.JsonProperty("smtp", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoEmailSmtpApiRequest Smtp
        {
            get { return _smtp; }
            set 
            {
                if (_smtp != value)
                {
                    _smtp = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoEmailMessageApiRequest Message
        {
            get { return _message; }
            set 
            {
                if (_message != value)
                {
                    _message = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Data e hora que foi solicitado o envio da notificação. Deve ser indicado a hora do servidor do client.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Informações adicionais para listar no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificação da aplicação que solicitou o envio da notificação.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Identificação da aplicação destino da notificação.
        /// Utilizado para apresentação no painel de gerenciamento de notificações.
        /// Utilizado apenas para carater informativo para facilitar auditorias.</summary>
        [Newtonsoft.Json.JsonProperty("aplicacaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AplicacaoDestino
        {
            get { return _aplicacaoDestino; }
            set 
            {
                if (_aplicacaoDestino != value)
                {
                    _aplicacaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Tempo de vida em horas para reenvio da notificação em caso de falhas.
        /// Após este periodo o serviço na tentará mais reenviar automaticamente.</summary>
        [Newtonsoft.Json.JsonProperty("tempo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Tempo
        {
            get { return _tempo; }
            set 
            {
                if (_tempo != value)
                {
                    _tempo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        /// <summary>Configurações de como se comportar durante as falhas de notificações</summary>
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoPoliticaFalhaApiRequest PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoEmailApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoEmailApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoEmailSmtpApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _host;
        private int? _port;
        private string _userName;
        private string _password;
        private bool _enableSsl;
        private bool _useDefaultCredentials;
    
        [Newtonsoft.Json.JsonProperty("host", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Host
        {
            get { return _host; }
            set 
            {
                if (_host != value)
                {
                    _host = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("port", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Port
        {
            get { return _port; }
            set 
            {
                if (_port != value)
                {
                    _port = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("userName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string UserName
        {
            get { return _userName; }
            set 
            {
                if (_userName != value)
                {
                    _userName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("password", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Password
        {
            get { return _password; }
            set 
            {
                if (_password != value)
                {
                    _password = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("enableSsl", Required = Newtonsoft.Json.Required.Always)]
        public bool EnableSsl
        {
            get { return _enableSsl; }
            set 
            {
                if (_enableSsl != value)
                {
                    _enableSsl = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("useDefaultCredentials", Required = Newtonsoft.Json.Required.Always)]
        public bool UseDefaultCredentials
        {
            get { return _useDefaultCredentials; }
            set 
            {
                if (_useDefaultCredentials != value)
                {
                    _useDefaultCredentials = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoEmailSmtpApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoEmailSmtpApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoEmailMessageApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private NotificacaoEmailAddressApiRequest _from;
        private System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAddressApiRequest> _to;
        private System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAddressApiRequest> _copy;
        private System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAddressApiRequest> _hiddenCopy;
        private System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAttachmentApiRequest> _attachments;
        private string _subject;
        private string _subjectEncoding;
        private string _body;
        private string _bodyEncoding;
        private bool _isBodyHtml;
        private NotificacaoEmailMessageApiRequestPriority _priority;
    
        [Newtonsoft.Json.JsonProperty("from", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoEmailAddressApiRequest From
        {
            get { return _from; }
            set 
            {
                if (_from != value)
                {
                    _from = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("to", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAddressApiRequest> To
        {
            get { return _to; }
            set 
            {
                if (_to != value)
                {
                    _to = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("copy", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAddressApiRequest> Copy
        {
            get { return _copy; }
            set 
            {
                if (_copy != value)
                {
                    _copy = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("hiddenCopy", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAddressApiRequest> HiddenCopy
        {
            get { return _hiddenCopy; }
            set 
            {
                if (_hiddenCopy != value)
                {
                    _hiddenCopy = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("attachments", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<NotificacaoEmailAttachmentApiRequest> Attachments
        {
            get { return _attachments; }
            set 
            {
                if (_attachments != value)
                {
                    _attachments = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("subject", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Subject
        {
            get { return _subject; }
            set 
            {
                if (_subject != value)
                {
                    _subject = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("subjectEncoding", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string SubjectEncoding
        {
            get { return _subjectEncoding; }
            set 
            {
                if (_subjectEncoding != value)
                {
                    _subjectEncoding = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("body", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Body
        {
            get { return _body; }
            set 
            {
                if (_body != value)
                {
                    _body = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("bodyEncoding", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string BodyEncoding
        {
            get { return _bodyEncoding; }
            set 
            {
                if (_bodyEncoding != value)
                {
                    _bodyEncoding = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("isBodyHtml", Required = Newtonsoft.Json.Required.Always)]
        public bool IsBodyHtml
        {
            get { return _isBodyHtml; }
            set 
            {
                if (_isBodyHtml != value)
                {
                    _isBodyHtml = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("priority", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public NotificacaoEmailMessageApiRequestPriority Priority
        {
            get { return _priority; }
            set 
            {
                if (_priority != value)
                {
                    _priority = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoEmailMessageApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoEmailMessageApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoEmailAddressApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _address;
        private string _displayName;
    
        [Newtonsoft.Json.JsonProperty("address", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Address
        {
            get { return _address; }
            set 
            {
                if (_address != value)
                {
                    _address = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("displayName", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DisplayName
        {
            get { return _displayName; }
            set 
            {
                if (_displayName != value)
                {
                    _displayName = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoEmailAddressApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoEmailAddressApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoEmailAttachmentApiRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _contentBase64;
        private string _name;
        private string _mediaType;
    
        [Newtonsoft.Json.JsonProperty("contentBase64", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ContentBase64
        {
            get { return _contentBase64; }
            set 
            {
                if (_contentBase64 != value)
                {
                    _contentBase64 = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Name
        {
            get { return _name; }
            set 
            {
                if (_name != value)
                {
                    _name = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mediaType", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MediaType
        {
            get { return _mediaType; }
            set 
            {
                if (_mediaType != value)
                {
                    _mediaType = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoEmailAttachmentApiRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoEmailAttachmentApiRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoSmsBusRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private NotificacaoSmsBusRequestTipo? _tipo;
        private System.Guid? _id;
        private System.DateTime? _dataRequisicao;
        private string _infoAdicional;
        private string _aplicacao;
        private string _aplicacaoDestino;
        private NotificacaoPoliticaFalhaBusRequest _politicaFalha;
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public NotificacaoSmsBusRequestTipo? Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aplicacaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AplicacaoDestino
        {
            get { return _aplicacaoDestino; }
            set 
            {
                if (_aplicacaoDestino != value)
                {
                    _aplicacaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoPoliticaFalhaBusRequest PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoSmsBusRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoSmsBusRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoPoliticaFalhaBusRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _minutosParaNovaTentativa;
        private int? _minutosParaMonitorar;
        private int? _quantidadeMaximaTentativasReenvio;
        private System.Collections.ObjectModel.ObservableCollection<NotificacaoPoliticaFalhaAlertaBusRequest> _alertas;
    
        [Newtonsoft.Json.JsonProperty("minutosParaNovaTentativa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? MinutosParaNovaTentativa
        {
            get { return _minutosParaNovaTentativa; }
            set 
            {
                if (_minutosParaNovaTentativa != value)
                {
                    _minutosParaNovaTentativa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("minutosParaMonitorar", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? MinutosParaMonitorar
        {
            get { return _minutosParaMonitorar; }
            set 
            {
                if (_minutosParaMonitorar != value)
                {
                    _minutosParaMonitorar = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("quantidadeMaximaTentativasReenvio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? QuantidadeMaximaTentativasReenvio
        {
            get { return _quantidadeMaximaTentativasReenvio; }
            set 
            {
                if (_quantidadeMaximaTentativasReenvio != value)
                {
                    _quantidadeMaximaTentativasReenvio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("alertas", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.ObjectModel.ObservableCollection<NotificacaoPoliticaFalhaAlertaBusRequest> Alertas
        {
            get { return _alertas; }
            set 
            {
                if (_alertas != value)
                {
                    _alertas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoPoliticaFalhaBusRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoPoliticaFalhaBusRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoPoliticaFalhaAlertaBusRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _falhasParaEnviarAlerta;
        private INotificacaoBaseBusRequest _configuracao;
    
        [Newtonsoft.Json.JsonProperty("falhasParaEnviarAlerta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? FalhasParaEnviarAlerta
        {
            get { return _falhasParaEnviarAlerta; }
            set 
            {
                if (_falhasParaEnviarAlerta != value)
                {
                    _falhasParaEnviarAlerta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("configuracao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public INotificacaoBaseBusRequest Configuracao
        {
            get { return _configuracao; }
            set 
            {
                if (_configuracao != value)
                {
                    _configuracao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoPoliticaFalhaAlertaBusRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoPoliticaFalhaAlertaBusRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class INotificacaoBaseBusRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private System.Guid? _id;
        private INotificacaoBaseBusRequestTipo? _tipo;
        private System.DateTime? _dataRequisicao;
        private string _infoAdicional;
        private string _aplicacao;
        private string _aplicacaoDestino;
        private NotificacaoPoliticaFalhaBusRequest _politicaFalha;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public INotificacaoBaseBusRequestTipo? Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aplicacaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AplicacaoDestino
        {
            get { return _aplicacaoDestino; }
            set 
            {
                if (_aplicacaoDestino != value)
                {
                    _aplicacaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoPoliticaFalhaBusRequest PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static INotificacaoBaseBusRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<INotificacaoBaseBusRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class NotificacaoPushBusRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private NotificacaoPushBusRequestTipo? _tipo;
        private System.Guid? _id;
        private System.DateTime? _dataRequisicao;
        private string _infoAdicional;
        private string _aplicacao;
        private string _aplicacaoDestino;
        private NotificacaoPoliticaFalhaBusRequest _politicaFalha;
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public NotificacaoPushBusRequestTipo? Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Guid? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataRequisicao
        {
            get { return _dataRequisicao; }
            set 
            {
                if (_dataRequisicao != value)
                {
                    _dataRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("infoAdicional", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string InfoAdicional
        {
            get { return _infoAdicional; }
            set 
            {
                if (_infoAdicional != value)
                {
                    _infoAdicional = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aplicacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Aplicacao
        {
            get { return _aplicacao; }
            set 
            {
                if (_aplicacao != value)
                {
                    _aplicacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("aplicacaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string AplicacaoDestino
        {
            get { return _aplicacaoDestino; }
            set 
            {
                if (_aplicacaoDestino != value)
                {
                    _aplicacaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("politicaFalha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public NotificacaoPoliticaFalhaBusRequest PoliticaFalha
        {
            get { return _politicaFalha; }
            set 
            {
                if (_politicaFalha != value)
                {
                    _politicaFalha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static NotificacaoPushBusRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<NotificacaoPushBusRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum LogIntegracaoInsertApiRequestDirecao
    {
        [System.Runtime.Serialization.EnumMember(Value = "Input")]
        Input = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Output")]
        Output = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum NotificacaoEmailMessageApiRequestPriority
    {
        [System.Runtime.Serialization.EnumMember(Value = "Normal")]
        Normal = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Low")]
        Low = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "High")]
        High = 2,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum NotificacaoSmsBusRequestTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Webhook")]
        Webhook = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Email")]
        Email = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sms")]
        Sms = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Push")]
        Push = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum INotificacaoBaseBusRequestTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Webhook")]
        Webhook = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Email")]
        Email = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sms")]
        Sms = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Push")]
        Push = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum NotificacaoPushBusRequestTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Webhook")]
        Webhook = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Email")]
        Email = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sms")]
        Sms = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Push")]
        Push = 3,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public int StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message, innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "********* (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, int statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}
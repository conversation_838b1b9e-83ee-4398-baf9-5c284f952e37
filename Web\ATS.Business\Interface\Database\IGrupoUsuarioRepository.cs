﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IGrupoUsuarioRepository : IRepository<GrupoUsuario>
    {
        IQueryable<GrupoUsuario> Consultar(string nome, int? idEmpresa, int? idEstabelecimentoBase);
        IQueryable<GrupoUsuario> GetPorEmpresa(int? idEmpresa, int? idEstabelecimentoBase);
        GrupoUsuario GetChilds(int id);
    }
}
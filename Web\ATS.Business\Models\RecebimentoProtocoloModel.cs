﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class RecebimentoProtocoloModel
    {
        public int IdProtocolo { get; set; }

        public string Associacao { get; set; }

        public string NomeFantasia { get; set; }

        public int? IdEmpresa { get; set; }

        public string CNPJEstabelecimento { get; set; }

        public string Descricao { get; set; }

        public int IdEstabelecimentoBase { get; set; }

        public string CNPJ { get; set; }

        public string DataGeracao { get; set; }

        public string DataPagamento { get; set; }

        public double Taxa { get; set; }

        public decimal ValorProtocolo { get; set; }

        public string Status { get; set; }

        public EStatusProtocolo StatusProtocolo { get; set; }

        public bool Antecipado { get; set; }

        public string AntecipadoStr { get; set; }

        public string Pago { get; set; }

        public decimal? TaxaPagamentoAntecipado { get; set; }

        public decimal? ValorPagamentoAntecipado { get; set; }

        public string DataPagamentoAntecipado { get; set; }

        public bool PermiteAntecipacao { get; set; }

        public int? NumeroEventos { get; set; }

        public string DataPrevisaoPagamento { get; set; }

        public string DataAprovacao { get; set; }

        public bool RequerLiberacao { get; set; }

        public ETipoDestinatario TipoDestinatario { get; set; }

        public int? IdEstabelecimentoDestinatario { get; set; }

        public string EstabPagamentoAntecipado { get; set; }

        public string TipoEvento { get; set; }

        public string TipoPagamento { get; set; }

        public string PossuiEventoReincidente { get; set; }
    }
}

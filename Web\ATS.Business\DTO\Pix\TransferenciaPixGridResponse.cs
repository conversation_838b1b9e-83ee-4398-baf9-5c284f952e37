﻿// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class TransferenciaPixGridResponse
    {
        public int totalItems { get; set; }
        public string totalPagamento { get; set; }
        public string totalRecebimento { get; set; }
        public string totalMovimentado { get; set; }
        public List<TransferenciaPixGridResponseItem> items { get; set; }
    }

    public class TransferenciaPixGridResponseItem
    {
        public string Codigo { get; set; }
        public ETipoTransferenciaPix TipoEnum { get; set; }
        public string Tipo { get; set; }
        public string DocumentoOrigem { get; set; }
        public string DocumentoDestino { get; set; }
        public string NomeOrigem { get; set; }
        public string NomeDestino { get; set; }
        public string DataTransferencia { get; set; }
        public DateTime DataTransferenciaDateTime { get; set; }
        public string Valor { get; set; }
        public int? IdTransacaoPixPortal { get; set; }
        public string Ciot { get; set; }
        public string DocumentoCliente { get; set; }
        public string MensagemRetorno { get; set; }
        public int? IdViagem { get; set; }
        public int? IdViagemEvento { get; set; }
        public ETipoEventoViagem? TipoEvento { get; set; }
        public string TipoEventoDescricao { get; set; }
        public ETransacaoPixStatus Status { get; set; }
        public string StatusDescricao { get; set; }
    }
}
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ParametrosMap : EntityTypeConfiguration<Parametros>
    {
        public ParametrosMap()
        {
            ToTable("PARAMETROS");
            HasKey(t => t.IdParametro);
            
//            HasKey(t => new {t.<PERSON>, t.IdEmpresa, t.IdRegistro, t.Chave});

            Property(t => t.NomeTabela).IsRequired();
            Property(t => t.IdRegistro).IsOptional();
            Property(t => t.IdRegistroStr).IsOptional();
            Property(t => t.Chave).IsRequired();
            Property(t => t.IdEmpresa).IsOptional();
            Property(t => t.ValorString).HasMaxLength(1000).IsOptional();
            Property(t => t.ValorDecimal).IsOptional();
            Property(t => t.ValorDateTime).IsOptional();
            Property(t => t.ValorBoolean).IsOptional();
        }
    }
}
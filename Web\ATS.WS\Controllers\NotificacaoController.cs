﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class NotificacaoController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvNotificacao _srvNotificacao;

        public NotificacaoController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvNotificacao srvNotificacao) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvNotificacao = srvNotificacao;
        }

        ///// <summary>
        ///// Consultar as notificacoes
        ///// </summary>
        ///// <param name="params"></param>
        ///// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(int idUsuario)
        {
            try
            {
                //nao valida token aqui ???????????????
                return new JsonResult().Responde(_srvNotificacao.Consultar(idUsuario));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        ///// <summary>
        ///// Consultar as notificacoes
        ///// </summary>
        ///// <param name="params"></param>
        ///// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarMobile(string cnpjAplicacao, string token, int idUsuario, int? idTipoNotificacao, DateTime dataBase)
        {
            try
            {
                //nao valida token aqui ???????????????
                if (!string.IsNullOrWhiteSpace(cnpjAplicacao) && !string.IsNullOrWhiteSpace(token))
                    if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                        return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvNotificacao.ConsultarPorUsuario(idUsuario, idTipoNotificacao, dataBase));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Inserir(NotificacaoRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                _srvNotificacao.AdicionarNotificacao(@params);

                return new JsonResult().Responde(@params);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarTipoNotificacao(string cnpjAplicacao, string token, string cnpjEmpresa, DateTime? dataBase)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvNotificacao.ConsultarTipoNotificacao(dataBase, cnpjEmpresa));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
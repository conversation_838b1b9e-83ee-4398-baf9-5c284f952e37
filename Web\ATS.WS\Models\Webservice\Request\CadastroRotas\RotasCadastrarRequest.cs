﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Webservice.Request.CadastroRotas
{
    public class RotasModeloIntegrarRequest
    {
        public int? IdRotaModelo { get; set; }
        public int? IdEmpresa { get; set; }
        public DateTime DataCadastro { get; set; }
        public List<decimal> CodigosIbge { get; set; }
        public List<Destino> Destinos { get; set; }
        public float DistanciaTotal { get; set; }
        public decimal CustoRota { get; set; }
        public decimal CustoRotaTag { get; set; }
        public string NomeRota { get; set; }
        public int QtdeEixos { get; set; }
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public ETipoRodagem TipoRodagem { get; set; } = ETipoRodagem.Duplo; 
        public bool IdaEVolta { get; set; }
        public int? CodPolyline { get; set; }

        #region Tabelas Filhas

        public List<PracasRotaModeloRequest> PracasRotaModelo { get; set;}
        public List<PontosRotaModeloRequest> PontosRotaModelo { get; set;}
        
        #endregion
    }

    public class Destino
    {
        public string Local { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
    }

    public class PontosRotaModeloRequest
    {
        public int IdRotaModelo { get; set; }
        public int IdPonto { get; set; }
        public string Descricao { get; set; }
        public decimal Ibge { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
    }
    
    public class PracasRotaModeloRequest
    {
        public int IdPraca { get; set; }
        public int IdRotaModelo { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
        public decimal ValorTag { get; set; }
    }
    
}

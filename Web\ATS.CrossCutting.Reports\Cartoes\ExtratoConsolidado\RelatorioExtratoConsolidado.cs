﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;

namespace ATS.CrossCutting.Reports.Cartoes.ExtratoConsolidado
{
    public class RelatorioExtratoConsolidado
    {
        public byte[] GetReport(string tipo, List<ExtratoConsolidadoModelResponseItem> dadosRelatorio, string logo)
        {
            
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var dataSources = new Tuple<object, string>(dadosRelatorio, "DtsExtratoConsolidado");

            var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                "ATS.CrossCutting.Reports.Cartoes.ExtratoConsolidado.RelatorioExtratoConsolidado.rdlc", tipo);

            return bytes;
        }
    }
}

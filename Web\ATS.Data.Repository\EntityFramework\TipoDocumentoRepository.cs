﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class TipoDocumentoRepository : Repository<TipoDocumento>, ITipoDocumentoRepository
    {
        public TipoDocumentoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<TipoDocumento> Consultar(string descricao)
        {
            return (from tpDoc in All()
                    where tpDoc.Descricao.Contains(descricao)
                    orderby tpDoc.IdTipoDocumento descending
                    select tpDoc);
        }
    }
}
﻿using System;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.WS.ControllersATS
{
    public class ImportacaoDadosAtsController : DefaultController
    {
        private readonly IImportacaoDadosApp _app;
        public ImportacaoDadosAtsController(IImportacaoDadosApp app)
        {
            _app = app;
        }

        [HttpGet]
        [Expor(EApi.Portal)]
        public FileResult GetLayoutModeloPadrao()
        {
            var dirInfo = new DirectoryInfo("~/Files/Modelo_Importacao_Dados.xlsx");
            return File(dirInfo.ToString(), ConstantesUtils.ExcelXmlExtention, $"Modelo_Importacao_Dados.{ConstantesUtils.ExcelXmlExtention}");
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarPlanilha(HttpPostedFileBase file)
        {
            try
            {
                var result = _app.ValidarPlanilha(file);
                
                if(!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ProcessarPlanilha(HttpPostedFileBase file,int idEmpresa)
        {
            try
            {
                var result = _app.Processar(file,idEmpresa);
                
                if(!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("Operação realizada com sucesso");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
    }
}
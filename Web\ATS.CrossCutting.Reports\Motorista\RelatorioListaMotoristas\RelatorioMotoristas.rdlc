<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.4418cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>9.365cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.4125cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.4125cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.76459cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.36194cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.44167cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.23542cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.05021cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.47355cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.25646cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.89688cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>7.05083cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>16.7875cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.6cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Id">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Id.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Id</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Nome">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Nome.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Nome</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Cpf">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Cpf.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Cpf</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Rg">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Rg.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Rg</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="RgOrgaoExpedidor">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!RgOrgaoExpedidor.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>RgOrgaoExpedidor</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Cnh">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Cnh.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Cnh</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CategoriaCnh">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CategoriaCnh.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>CategoriaCnh</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ValidadeCnh">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ValidadeCnh.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ValidadeCnh</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Sexo">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Sexo.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Sexo</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Celular">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Celular.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Celular</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Ativo">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Ativo.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Ativo</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TipoContrato">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!TipoContrato.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>TipoContrato</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Email">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Email.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Email</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Endereco">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Endereco.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Endereco</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details1" />
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DtsMotoristas</DataSetName>
        <Left>0.3cm</Left>
        <Height>0.6cm</Height>
        <Width>60.95084cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.07209cm</Height>
    <Style>
      <Border>
        <Style>None</Style>
      </Border>
    </Style>
  </Body>
  <Width>61.55666cm</Width>
  <Page>
    <PageHeader>
      <Height>2.05542cm</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Textbox Name="Textbox1">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>RELATÓRIO DE MOTORISTAS</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>14pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox1</rd:DefaultName>
          <Top>0.25542cm</Top>
          <Left>2.7418cm</Left>
          <Height>1.2cm</Height>
          <Width>55.08828cm</Width>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox2">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Página:  </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>=Globals!PageNumber</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value> de </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>=Globals!TotalPages</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox2</rd:DefaultName>
          <Top>0.25542cm</Top>
          <Left>57.83008cm</Left>
          <Height>0.6cm</Height>
          <Width>3.42076cm</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <TopBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </TopBorder>
            <BottomBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <RightBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </RightBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox3">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>="Emissão: " &amp; Now()</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox2</rd:DefaultName>
          <Top>0.85542cm</Top>
          <Left>57.83008cm</Left>
          <Height>0.6cm</Height>
          <Width>3.42076cm</Width>
          <ZIndex>2</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BottomBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <RightBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </RightBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Image Name="Logo">
          <Source>External</Source>
          <Value>=Parameters!Logo.Value</Value>
          <MIMEType>image/png</MIMEType>
          <Sizing>FitProportional</Sizing>
          <Top>0.25542cm</Top>
          <Left>0.3cm</Left>
          <Height>1.2cm</Height>
          <Width>2.4418cm</Width>
          <ZIndex>3</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <TopBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </LeftBorder>
          </Style>
        </Image>
        <Textbox Name="Textbox26">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Código</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox24</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>0.3cm</Left>
          <Height>0.6cm</Height>
          <Width>2.4418cm</Width>
          <ZIndex>4</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox27">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Nome</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox25</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>2.7418cm</Left>
          <Height>0.6cm</Height>
          <Width>9.365cm</Width>
          <ZIndex>5</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox31">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>CPF</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox30</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>12.1068cm</Left>
          <Height>0.6cm</Height>
          <Width>3.4125cm</Width>
          <ZIndex>6</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox33">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>RG</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox32</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>15.5193cm</Left>
          <Height>0.6cm</Height>
          <Width>3.4125cm</Width>
          <ZIndex>7</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox35">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Órgão expedidor</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox34</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>18.9318cm</Left>
          <Height>0.6cm</Height>
          <Width>2.76459cm</Width>
          <ZIndex>8</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox37">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>CNH</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox36</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>21.69639cm</Left>
          <Height>0.6cm</Height>
          <Width>3.36194cm</Width>
          <ZIndex>9</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox41">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Categoria</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox38</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>25.05833cm</Left>
          <Height>0.6cm</Height>
          <Width>1.44167cm</Width>
          <ZIndex>10</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox43">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Validade</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox39</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>26.5cm</Left>
          <Height>0.6cm</Height>
          <Width>2.23542cm</Width>
          <ZIndex>11</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox45">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Sexo</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox40</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>28.73542cm</Left>
          <Height>0.6cm</Height>
          <Width>2.05021cm</Width>
          <ZIndex>12</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox46">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Celular</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox42</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>30.78562cm</Left>
          <Height>0.6cm</Height>
          <Width>2.47355cm</Width>
          <ZIndex>13</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox48">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Ativo</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox44</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>33.25917cm</Left>
          <Height>0.6cm</Height>
          <Width>1.25646cm</Width>
          <ZIndex>14</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox49">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Tipo de contrato</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox47</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>34.51563cm</Left>
          <Height>0.6cm</Height>
          <Width>2.89688cm</Width>
          <ZIndex>15</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox51">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>E-mail</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox50</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>37.41251cm</Left>
          <Height>0.6cm</Height>
          <Width>7.05083cm</Width>
          <ZIndex>16</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox56">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Endereço</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox55</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>44.46334cm</Left>
          <Height>0.6cm</Height>
          <Width>16.7875cm</Width>
          <ZIndex>17</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <PageHeight>21cm</PageHeight>
    <PageWidth>62cm</PageWidth>
    <LeftMargin>0.2cm</LeftMargin>
    <RightMargin>0.2cm</RightMargin>
    <TopMargin>0cm</TopMargin>
    <BottomMargin>0cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsMotoristaRelatorioListaMotoristas">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>3d91617f-6b3a-48ff-a475-37eef8000910</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DtsMotoristas">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsMotoristaRelatorioListaMotoristas</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Ativo">
          <DataField>Ativo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CategoriaCnh">
          <DataField>CategoriaCnh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Celular">
          <DataField>Celular</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Cnh">
          <DataField>Cnh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Cpf">
          <DataField>Cpf</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Email">
          <DataField>Email</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Endereco">
          <DataField>Endereco</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Id">
          <DataField>Id</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Nome">
          <DataField>Nome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Rg">
          <DataField>Rg</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="RgOrgaoExpedidor">
          <DataField>RgOrgaoExpedidor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Sexo">
          <DataField>Sexo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TipoContrato">
          <DataField>TipoContrato</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValidadeCnh">
          <DataField>ValidadeCnh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Motorista.RelatorioListaMotoristas</rd:DataSetName>
        <rd:TableName>RelatorioMotoristaDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Motorista.RelatorioListaMotoristas.RelatorioMotoristaDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="Logo">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>5fa1e652-f57f-4b9e-8199-3340f752e200</rd:ReportID>
</Report>
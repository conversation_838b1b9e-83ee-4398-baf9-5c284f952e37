using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.Viagem;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using ATS.WS.Models.ViagemV2.Integracao;
using NLog;

namespace ATS.WS.Models.Common.Request
{
    public class ViagemV2AutorizarEstabelecimentosRequest : RequestBase
    {
        public int ViagemId { get; set; }
        public List<ViagemV2AutorizacaoEstabelecimentoModel> AutorizacaoEstabelecimentos { get; set; } = new List<ViagemV2AutorizacaoEstabelecimentoModel>();

        private static Logger _logger = LogManager.GetCurrentClassLogger();
        public ValidationResult ValidaRequest()
        {
            return ValidaRequestBase(false);
        }
        
        public virtual ValidationResult<EValidationViagemEstabelecimento> ValidarPreencherAutorizacaoEstabelecimentos(IViagemApp viagemApp)
        {
            var validacao = new ValidationResult<EValidationViagemEstabelecimento>();
            try
            {
                if(ViagemId == 0)
                    validacao.Add(EValidationViagemEstabelecimento.ViagemIdObrigatorio, EFaultType.Error);
                
                if (!AutorizacaoEstabelecimentos.Any())
                    validacao.Add(EValidationViagemEstabelecimento.AutorizacaoEstabelecimentosObrigatorio, EFaultType.Error);
                
                if (!validacao.IsValid)
                    return validacao;
                
                foreach (var autorizacaoEstabelecimento in AutorizacaoEstabelecimentos)
                {
                    validacao = autorizacaoEstabelecimento.ValidarEntrada(viagemApp, CNPJEmpresa);
                    if (!validacao.IsValid)
                        return validacao;
                }

                var repetidos = AutorizacaoEstabelecimentos.GroupBy(x => new { x.Cnpj, x.TipoEvento })
                    .Where(g => g.Count() > 1)
                    .Select(y => y.Key)
                    .ToList();

                foreach (var repetido in repetidos)
                    validacao.Add(EValidationViagemEstabelecimento.CnpjTipoEventoRepetido, EFaultType.Error,
                        new object[]{repetido.Cnpj, repetido.TipoEvento.GetDescription()});
            }
            catch (Exception e)
            {
                _logger.Error(e);
                validacao.Add(EValidationViagemEstabelecimento.ErroNaoCatalogado, EFaultType.Error, new object[]{e.GetBaseException().Message});
            }
            
            return validacao;
        }
    }
}
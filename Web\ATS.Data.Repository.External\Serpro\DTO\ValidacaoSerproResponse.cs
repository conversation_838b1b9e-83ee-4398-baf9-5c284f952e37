using System.Collections.Generic;
using System.Net;

namespace ATS.Data.Repository.External.Serpro.DTO
{
    public class ValidacaoSerproResponse
    {
        /*/// <summary>
        /// Disponível apenas em ambiente de desenvolvimento, informa a causa especifica do erro da API
        /// </summary>
        public string Cause { get; set; }
        public HttpStatusCode? StatusCode { get; set; }
        public bool? Success { get; set; }
        public string Code { get; set; }
        /// <summary>
        /// Caso haja alguma inconsistência nos campos (que não seja formatação de data/algum erro na API da Serpro)
        /// </summary>
        public List<ValidacaoSerproErro> Errors { get; set; }*/
        public string Message { get; set; }
        public ValidacaoSerproItem Cpf { get; set; }
        public ValidacaoSerproItem BirthDate { get; set; }
        public ValidacaoSerproItem MotherName { get; set; }
        public ValidacaoSerproItem DriverLicenseRegisterNumber { get; set; }
        public ValidacaoSerproItem FirstDriverLicenseDate { get; set; }
        public ValidacaoSerproItem PhotoBase64 { get; set; }
    }

    public class ValidacaoSerproItem
    {
        public bool? Valid { get; set; }
        public double? Similarity { get; set; }
    }

    public class ValidacaoSerproErro
    {
        public string Field { get; set; }
        public ValidacaoSerproErroDetails Details { get; set; }
    }

    public class ValidacaoSerproErroDetails
    {
        public string Tag { get; set; }
        public string Expected { get; set; }
    }
}
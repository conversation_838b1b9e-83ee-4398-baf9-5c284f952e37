{"runtime": "Net70", "defaultVariables": "", "documentGenerator": {"fromDocument": {"json": "{\r\n  \"swagger\": \"2.0\",\r\n  \"info\": {\r\n    \"version\": \"v1\",\r\n    \"title\": \"/Cartoes/Api\",\r\n    \"description\": \"API's para manipulação de cartões.\\r\\n\\r\\nTodos os métodos seguem o mesmo padrão de autenticação, sendo necessário indicar no header da requisição os seguintes valores:\\r\\n<li>x-auth-token: Token de identificação da empresa requisitando a operação</li>\\r\\n<li>x-audit-user-doc: Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação</li>\\r\\n<li>x-audit-user-name: Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</li>\\r\\n\\r\\nOs retornos de métodos respeitam o padrão do protocolo REST/HTTP, fazendo uso do \\\"Http Code\\\" adequado a situação:\\r\\n<li>http code 200: Existente na operação do servidor, com retorno de dados</li>\\r\\n<li>http code 204: A requisição foi processada com sucesso no servidor, porém não há informação para retornar. Geralmente utilizado em consultas de registros. Na especificação dos métodos abaixo estará descrito se este código é possível de retorno pela API</li>\\r\\n<li>http code 4XX: Erro de requisição por conteúdo mal formatado pelo consumidor. Geralmente JSON em formato inválidio enviado pelo cliente. O servidor não realizou nenhuma operação</li>\\r\\n<li>http code 5XX: Erro interno no servidor de aplicação, neste caso o conteúdo da requisição está OK, e algo não planejado ocorreu na API</li>\\r\\n\\r\\nPelas definições abaixo é possiviel verificar os métodos que retornam a informação \\\"processingStateOnServer\\\". Esta subpropriedade é apenas informação técnica, não representando sucesso da execução de regras de negócios. Ela indica que o servidor executou a procedimento sem problemas técnicos, através do indicador \\\"State\\\" com os valores \\\"Ok\\\" ou \\\"Error\\\", sendo adicionado mensagens em caso de erros na propriedade \\\"ErrorMessage\\\".\"\r\n  },\r\n  \"basePath\": \"/Cartoes/Api\",\r\n  \"paths\": {\r\n    \"/Administradoras\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Administradora\"\r\n        ],\r\n        \"summary\": \"Busca as administradoras da plataforma\",\r\n        \"operationId\": \"AdministradorasGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/AdministradorasResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Buscar/Todos/{data}/{produto}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Buscar\"\r\n        ],\r\n        \"summary\": \"Buscar todos os cartões vinculados, aguardando vinculação e desativados para empresa/administradora autenticada.\",\r\n        \"operationId\": \"BuscarTodosByDataByProdutoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"data\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Obter apenas registros inseridos na plataforma após esta data e hora. Null para obter todos os registros.\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"produto\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Filtar apenas cartões do produto indicado. Null para obter todos disponíveis para empresa. Maiores detalhes na documentação da api “/Produtos”.\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Lista com todos os cartões da empresa autenticada.\\r\\n<li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li>\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/CartaoResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Buscar/{identificador}/{produto}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Buscar\"\r\n        ],\r\n        \"summary\": \"Busca cartão através do identificador e produto.\\r\\nEsta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.\\r\\nOu seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.\",\r\n        \"operationId\": \"BuscarByIdentificadorByProdutoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"identificador\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Número identificador do cartão\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"produto\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Id do tipo de produto. Maiores detalhes na documentação da api \\\"/Produtos\\\".\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Dados do cartão com o identificador e produto indicado.\\r\\n<li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoResponse\"\r\n            }\r\n          },\r\n          \"204\": {\r\n            \"description\": \"Não existe cartão com os dados indicados\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Buscar/CartoesDisponiveisParaRemessa\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Buscar\"\r\n        ],\r\n        \"summary\": \"Método responsável por trazer os cartões disponíveis pela Administradora para envio de remessa\",\r\n        \"operationId\": \"BuscarCartoesDisponiveisParaRemessaGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/CartaoResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Buscar/MotivosBloqueio\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Buscar\"\r\n        ],\r\n        \"summary\": \"Método responsável por trazer todos os motivos de bloqueio de cartão\",\r\n        \"operationId\": \"BuscarMotivosBloqueioGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PageSize\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"PageIndex\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"CustomFilters\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"OrderBy\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/FilteredResult[MotivoBloqueioModel]\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Buscar/AdministradoraCartao/{identificador}/{idproduto}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Buscar\"\r\n        ],\r\n        \"summary\": \"Método responsável por trazer os cartões disponíveis pela Administradora para envio de remessa\",\r\n        \"operationId\": \"BuscarAdministradoraCartaoByIdentificadorByIdprodutoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"identificador\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"idproduto\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultaCartaoAdministradoraResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Buscar/DocumentosDestinoTransferencia/{documentoOdigem}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Buscar\"\r\n        ],\r\n        \"summary\": \"Através do Id da pessoa (localizado pelo documento de origem), busca na tabela Cartao.Cartao pelos\\r\\ncartões que estão vinculados e que já foram vinculados na pessoa (PessoaId), cuja data de disvinculo\\r\\nseja nula (para cartões vinculados) ou menor que 90 dias retroativos.\",\r\n        \"operationId\": \"BuscarDocumentosDestinoTransferenciaByDocumentoOdigemGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documentoOdigem\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DocumentosDestinoTransferenciaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/ConsultarSaldoEmpresa/{Cnpj}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Consultar saldo existente na conta de adiantamneto da empresa na processadora de cartões\",\r\n        \"operationId\": \"EmpresasConsultarSaldoEmpresaByCnpjGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"Cnpj\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/ConsultarSaldoEmpresas\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"operationId\": \"EmpresasConsultarSaldoEmpresasGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"Cnpjs\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoEmpresasResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/Integrar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Integrar ou atualizar registro de Empresa\",\r\n        \"operationId\": \"EmpresasIntegrarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Dados cadastrais da empresa.\\r\\n            <li>nome: Razão social para empresa</li><li>Cnpj: Documento CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação da Empresa</li><li>cidade: Código IBGE da cidade</li>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarEmpresaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Resultado da integração.\\r\\n            <li>status: Enum Falha/Existente</li><li>mensagens: Lista com mensagens de erros encontradas na integração</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/GetOrGenerateCartaoAppToken\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"Função de uso administrativo para obter ou criar um novo token de autenticação na aplicação de cartões\",\r\n        \"operationId\": \"EmpresasGetOrGenerateCartaoAppTokenPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cnpjEmpresa\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"appName\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"grupoContabililzacaoTransacao\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Se informado um valor, o grupo de contabilização deste token é modificado\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/GetOrGenerateCartaoAppTokenApiResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Empresas/Consultar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Empresa\"\r\n        ],\r\n        \"summary\": \"\",\r\n        \"operationId\": \"EmpresasConsultarPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"Cnpj\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/VincularPortador\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Vincular cartão ao portador. Caso haver cartão do mesmo produto já vinculado ao portador, e este não suportar multiplas contas em simutâneo, o cartão anterior será desativado e o saldo residual será transferido automaticamente ao novo cartão.\",\r\n        \"operationId\": \"OperacoesVincularPortadorPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Informações cadastrais para realizar vinculo. Enviar dados atualizados do portador e o cartão sendo entregue ao mesmo.\\r\\n            <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul>r\\r\\n            <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></ul>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/VincularRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/VincularResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/DesvincularPortador\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.\",\r\n        \"operationId\": \"OperacoesDesvincularPortadorPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"<para>Dados do cartão e motivo do desvinculo.</para>\\r\\n<para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API \\\"/Operacoes/VincularPortador\\\".</para>\\r\\n<li>cartao: Grupo com dados de identificação do cartão</li>\\r\\n<ul>\\r\\n  <li>identificador: Número único de identificação do cartão</li>\\r\\n  <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li>\\r\\n</ul>\\r\\n<li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DesvincularRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DesvincularResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/Bloquear\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.\",\r\n        \"operationId\": \"OperacoesBloquearPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"<para>Dados do cartão e motivo do desvinculo.</para>\\r\\n<para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API \\\"/Operacoes/VincularPortador\\\".</para>\\r\\n<li>cartao: Grupo com dados de identificação do cartão</li>\\r\\n<ul>\\r\\n  <li>identificador: Número único de identificação do cartão</li>\\r\\n  <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li>\\r\\n</ul>\\r\\n<li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BloquearCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BloquearCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/BloquearCartaoParametrizacaoEmpresa\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.\",\r\n        \"operationId\": \"OperacoesBloquearCartaoParametrizacaoEmpresaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"<para>Dados do cartão e motivo do desvinculo.</para>\\r\\n<para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API \\\"/Operacoes/VincularPortador\\\".</para>\\r\\n<li>cartao: Grupo com dados de identificação do cartão</li>\\r\\n<ul>\\r\\n  <li>identificador: Número único de identificação do cartão</li>\\r\\n  <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li>\\r\\n</ul>\\r\\n<li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BloquearCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BloquearCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/Desbloquear\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.\",\r\n        \"operationId\": \"OperacoesDesbloquearPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"<para>Dados do cartão e motivo do desvinculo.</para>\\r\\n<para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API \\\"/Operacoes/VincularPortador\\\".</para>\\r\\n<li>cartao: Grupo com dados de identificação do cartão</li>\\r\\n<ul>\\r\\n  <li>identificador: Número único de identificação do cartão</li>\\r\\n  <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li>\\r\\n</ul>\\r\\n<li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DesbloquearCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/DesbloquearCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/VincularCartaoVirtual\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Vincular uma conta-cartão virtual em um portador\",\r\n        \"operationId\": \"OperacoesVincularCartaoVirtualPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de requisicao\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/VincularCartaoVirtualRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/VincularCartaoVirtualResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConsultarExtrato\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Consulta um extrato do cartão\",\r\n        \"operationId\": \"OperacoesConsultarExtratoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de consulta de extrato\\r\\n            <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>dataInicio: Data e hora inicial do filtro</li><li>dataFim: Data e hora final do tipo</li><li>Tipo: C = Crédito, D = Débito, Vazio = Todos</li></ul>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarExtratoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarExtratoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConsultarSaldoCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Consultar o saldo do cartão\",\r\n        \"operationId\": \"OperacoesConsultarSaldoCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Objeto de consulta de saldo\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarSaldoCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConsultarProtocolo\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Consultar situação de processamento pelo número de protocolo único da requisição do cliente\",\r\n        \"operationId\": \"OperacoesConsultarProtocoloPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarProtocoloRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarProtocoloResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/Carregar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Realizar a carga de um valor em um determinado cartão\",\r\n        \"operationId\": \"OperacoesCarregarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Informações necessárias para a carga de cartão\\r\\n            <li>empresa: CNPJ da empresa que está realizando a carga</li><li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>historico: código do histórico da operação.</li><li>valor: valor da transação.</li><li>protocoloRequisicao: protocolo da requisição. Gerado pelo consumidor do método, deve ser único.</li><li>informacoesAdicionais: (Opcional) Informações adicionais que podem ser enviadas.</li><ul><li>additionalPropN: Chave única para identificação da informação adicional.</li><li>string: Valor da informação adicional.</li></ul>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CarregarValorRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CarregarValorResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/EstornarCarga\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Estornar carga de crédito no cartão pré-pago. Será estornado o valor da operação vinculada ao protocolo que originou o crédito.\\r\\nProcedimento sujeito a existência de saldo no cartão pré-pago.\",\r\n        \"operationId\": \"OperacoesEstornarCargaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"<para># Empresa: CNPJ para movimentar saldo.<br /></para>\\r\\n<para># ProtocoloRequisicaoParaEstorno: Protocolo que o consumidor gerou para efetivar a carga de crédito.<br /></para>\\r\\n<para># ProtocoloRequisicao: Número único gerado pelo cliente consumidor do serviço, utilizado para impedir requisições duplicadas.</para>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarCargaCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarCargaCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ResgatarValor\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Resgata um determinado valor do cartão pré-pago.\",\r\n        \"operationId\": \"OperacoesResgatarValorPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ResgatarValorRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ResgatarValorResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/EstornarResgateValor\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Estorna um Resgate de determinado valor do cartão pré-pago.\",\r\n        \"operationId\": \"OperacoesEstornarResgateValorPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarResgateValorRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarResgateValorReponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/CarregarPedagio\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Carregar pedágio\",\r\n        \"operationId\": \"OperacoesCarregarPedagioPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CarregarPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CarregarPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/EstornarCargaPedagio\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Estornar carga de pedágio\",\r\n        \"operationId\": \"OperacoesEstornarCargaPedagioPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarCargaPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarCargaPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConfirmarCargaPedagio\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Confirmação da carga de pedágio\",\r\n        \"operationId\": \"OperacoesConfirmarCargaPedagioPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConfirmarCargaPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConfirmarCargaPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConfirmarEstornoPedagio\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Confirmação o estorno da carga de pedágio\",\r\n        \"operationId\": \"OperacoesConfirmarEstornoPedagioPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConfirmarEstornoPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConfirmarEstornoPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/EstornarSaldoResidualPedagio\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Estornar o saldo residual do pedágio\",\r\n        \"operationId\": \"OperacoesEstornarSaldoResidualPedagioPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarSaldoResidualPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarSaldoResidualPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConfirmarEstornoSaldoResidualPedagio\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Confirmar o estorno do saldo residual do pedágio\",\r\n        \"operationId\": \"OperacoesConfirmarEstornoSaldoResidualPedagioPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConfirmarEstornoSaldoResidualPedagioRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConfirmarEstornoSaldoResidualPedagioResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/TransferirParaCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Realizar a transferência de um valor em um determinado cartão para um outro cartão da mesma operadora.\",\r\n        \"operationId\": \"OperacoesTransferirParaCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Informações necessárias para a carga de cartão\\r\\n            <li>empresa: CNPJ da empresa que está realizando a carga</li><li>cartaoOrigem: Grupo com dados de identificação do cartão de origem da transferência</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>cartaoDestino: Grupo com dados de identificação do cartão de destino da transferência</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>historico: código do histórico da operação.</li><li>valor: valor da transação.</li><li>protocoloRequisicao: protocolo da requisição. Gerado pelo consumidor do método, deve ser único.</li><li>informacoesAdicionais: (Opcional) Informações adicionais que podem ser enviadas.</li><ul><li>additionalPropN: Chave única para identificação da informação adicional.</li><li>string: Valor da informação adicional.</li></ul>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/TransferirValorCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/TransferirValorCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/EstornarTransferenciaCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Estornar transferência de crédito realizada entre cartões da mesma processadora.\\r\\nSerá estornado o valor da operação vinculada ao protocolo que originou a transferência.\\r\\nProcedimento sujeito a existência de saldo no cartão origem da transferência\",\r\n        \"operationId\": \"OperacoesEstornarTransferenciaCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarTransferenciaCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarTransferenciaCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/TransferirParaContaBancaria\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Realizar a transferência de um valor em um determinado cartão para uma conta bancária.\\r\\nNão é possível solicitar estorno da transferência pelo usuário, somente será estornado o valor e retornado ao cartão caso os dados destino do dinheiro forem inválidos durante o processamento da TED pelo banco.\",\r\n        \"operationId\": \"OperacoesTransferirParaContaBancariaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Informações necessárias para a carga de cartão\\r\\n            <li>cartaoOrigem: Grupo com dados de identificação do cartão de origem da transferência</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>contaBancaria: Informações sobre a conta bancária</li><ul><li>nomeConta: Nome para identificação da conta bancária (Texto livre)</li><li>codigoBacenBanco: Código do banco de acordo com os códigos BACENs. Para mais informações consulte: https://www.codigobanco.com/</li><li>agencia: Número da agência</li><li>conta: Número da conta bancária</li><li>digitoConta: Digito da conta bancária</li><li>tipoConta: Tipo da conta bancária\\r\\n            <ul><li>1 - Corrente</li><li>2 - Poupança</li></ul></li></ul><li>documentoFavorecido: CPF ou CNPJ do favorecido da transferência.</li><li>historico: código do histórico da operação.</li><li>valor: valor da transação.</li><li>protocoloRequisicao: protocolo da requisição. Gerado pelo consumidor do método, deve ser único.</li><li>informacoesAdicionais: (Opcional) Informações adicionais que podem ser enviadas.</li><ul><li>additionalPropN: Chave única para identificação da informação adicional.</li><li>string: Valor da informação adicional.</li></ul>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/TransferirValorContaBancariaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/TransferirValorContaBancariaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/CompraRedeCredenciada\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Efetuar compra em rede credenciada, debitando o cartão origem em tempo real, e gerando agenda de pagamento ao estabelecimento posteriormente em D+N\",\r\n        \"operationId\": \"OperacoesCompraRedeCredenciadaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Informações cadastrais para realizar transação.\\r\\n            <li>CnpjEstabelecimento: Cnpj do estabelecimento da compra (Necessário estar cadastrado como Pessoa. Utilizar a API \\\"Pessoa/Integrar\\\")</li><li>Cartao: Grupo com dados de identificação do cartão</li><ul><li>Identificador: Número único de identificação do cartão</li><li>Produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api \\\"/Produtos\\\"</li></ul><li>historico: código do histórico da operação.</li><li>valor: valor da transação.</li><li>protocoloRequisicao: protocolo da requisição. Gerado pelo consumidor do método, deve ser único.</li><li>informacoesAdicionais: (Opcional) Informações adicionais que podem ser enviadas.</li><ul><li>additionalPropN: Chave única para identificação da informação adicional.</li><li>string: Valor da informação adicional.</li></ul>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CompraRedeCredenciadaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CompraRedeCredenciadaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/EstornarCompraRedeCredenciada\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Estornar compra realizada em rede credenciada, retornando o dinheiro em tempo real ao cartão origem da transação, e gerando agenda de estorno da operação com o estabelecimento.\",\r\n        \"operationId\": \"OperacoesEstornarCompraRedeCredenciadaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarCompraRedeCredenciadaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EstornarCompraRedeCredenciadaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ConsultarCompraRedeCredenciada\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Consultas as compras efetuadas em rede credenciada pelo CNPJ do estabelecimento.\",\r\n        \"operationId\": \"OperacoesConsultarCompraRedeCredenciadaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarCompraRedeCredenciadaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarCompraRedeCredenciadaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/ValidarSenhaCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Validacao de senha do cartão\",\r\n        \"operationId\": \"OperacoesValidarSenhaCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ValidarSenhaCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ValidarSenhaCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/AlterarSenhaCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Alterar a senha do cartão\",\r\n        \"operationId\": \"OperacoesAlterarSenhaCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/AlterarSenhaCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/AlterarSenhaCartaoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/CartoesVinculadosListaComSaldo\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Busca os cartões vinculados em cada uma das pessoas informadas. Para buscar os cartões vinculados, buscar na tabela Cartao.Pessoa\\r\\npela pessoa cujo campo cpfcnpj seja igual ao documento e selecionar o campo PessoaId. Após isto, buscar na tabela Cartao.Cartao pelo cartao\\r\\nvinculado na pessoa através do campo pessoaId. Trazer os cartoes que tem o status 6 -Vinculado\",\r\n        \"operationId\": \"OperacoesCartoesVinculadosListaComSaldoPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"CartoesVinculadosLista\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/PessoasCartoesVinculadosResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Operacoes/CancelarCartaoRemessa\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Operacao\"\r\n        ],\r\n        \"summary\": \"Cancela cartão da remessa ou a própria remessa caso só tenha um cartão\",\r\n        \"operationId\": \"OperacoesCancelarCartaoRemessaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cancelarRemessaRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CancelarCartaoRemessaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CancelarCartaoRemessaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/{documento}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Buscar pessoa pelo documento\",\r\n        \"operationId\": \"PessoasByDocumentoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Documento CPF ou CNPJ\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Dados cadastrais da pessoa.\\r\\n            <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>ativo: Indicador booleano (true/false)</li><li>cidade: Código IBGE da cidade</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarPessoaResponse\"\r\n            }\r\n          },\r\n          \"204\": {\r\n            \"description\": \"Indica que não existe pessoa com o documento solicitado\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/GetDetalhesByDocumento/{documento}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Buscar pessoa com informações detalhadas pelo documento\",\r\n        \"operationId\": \"PessoasGetDetalhesByDocumentoByDocumentoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Documento CPF ou CNPJ\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Dados cadastrais da pessoa.\\r\\n            <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>ativo: Indicador booleano (true/false)</li><li>cidade: Código IBGE da cidade</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarPessoaDetalhadaResponse\"\r\n            }\r\n          },\r\n          \"204\": {\r\n            \"description\": \"Indica que não existe pessoa com o documento solicitado\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/ContasBancarias/{documento}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Buscar as contas bancárias vinculadas a uma pessoa\",\r\n        \"operationId\": \"PessoasContasBancariasByDocumentoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/PessoaContaBancariaResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/ContasBancarias/{documento}/{codigoBanco}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Buscar as contas bancárias vinculadas a uma pessoa de apenas um banco\",\r\n        \"operationId\": \"PessoasContasBancariasByDocumentoByCodigoBancoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"codigoBanco\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/PessoaContaBancariaResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/InativarContaBancaria\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Inativa uma conta bancária vinculada a uma pessoa\",\r\n        \"operationId\": \"PessoasInativarContaBancariaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/InativarContaBancariaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/InativarContaBancariaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/Integrar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Integrar ou atualizar registro pessoa ou estabelecimento.\\r\\nPara integrações com finalidade de vincular cartão a portador, execute a API \\\"/Operacoes/VincularPortador\\\" para maior facilidade.\",\r\n        \"operationId\": \"PessoasIntegrarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"Dados cadastrais da pessoa.\\r\\n            <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li>\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarPessoaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Resultado da integração.\\r\\n            <li>status: Enum Falha/Existente</li><li>mensagens: Lista com mensagens de erros encontradas na integração</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/IntegrarPessoaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/Bloquear\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Função administrativa não sendo liberada a todos os consumidores.\\r\\nBloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.\\r\\nNo caso de um portador, não será mais possível vincular novos cartões, não siginifica que o cartão atual será desativado.\",\r\n        \"operationId\": \"PessoasBloquearPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Documento CPF/CNPJ da pessoa a ser desativada\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Existente no bloqueio da pessoa\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ApiActionResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/Desbloquear\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Função administrativa não sendo liberada a todos os consumidores.\\r\\nDesbloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.\\r\\nNo caso de um portador, este poderá voltar a possuir novos cartões de vinculo, não significa que os cartões bloqueados serão reativados.\",\r\n        \"operationId\": \"PessoasDesbloquearPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Documento CPF/CNPJ da pessoa a ser ativada\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Existente no desbloqueio da pessoa\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ApiActionResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/CartoesVinculados/{documento}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Buscar cartões ativos do portador.\\r\\nEsta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.\\r\\nOu seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.\",\r\n        \"operationId\": \"PessoasCartoesVinculadosByDocumentoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Documento CPF/CNPJ da pessoa\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"idProdutos\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Produtos para listas os cartões. Vazio para listar todos\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoVinculadoPessoaListResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/CartoesBloqueado/{documento}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Buscar cartões ativos do portador.\\r\\nEsta rotina não está limitada ao estoque de cartões direcionado a empresa autenticada no token, os cartões vinculados em qualquer local, são compartilhados com todas empresas autorizadas da plataforma.\\r\\nOu seja, o portador possui apenas um único cartão e pode receber créditos (e demais integrações) de N empresas autorizadas a carregar neste tipo de cartão.\",\r\n        \"operationId\": \"PessoasCartoesBloqueadoByDocumentoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Documento CPF/CNPJ da pessoa\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"idProdutos\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Produtos para listas os cartões. Vazio para listar todos\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoBloqueadoPessoaListResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/HistoricoCartoes/{documento}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Listar histórico dos cartões já vinculados ao portador, exibindo os cartões ativos e os desativados.\",\r\n        \"operationId\": \"PessoasHistoricoCartoesByDocumentoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Documento CPF/CNPJ da pessoa\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"idProdutos\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Produtos para listas os cartões. Vazio para listar todos\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Lista com todos os cartões já vinculados ao portador de acordo com a requisição\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/HistoricoCartaoPessoaListResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/PontosDistribuicao\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Listar os  pontos de distribuicao disponiveis para a administradora/empresa enviada através do Token\",\r\n        \"operationId\": \"PessoasPontosDistribuicaoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cnpjList\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Lista de CNPJ de pontos distribuição visivel para o usuário do contexto\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/ConsultarPontoDistribuicaoResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      },\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Listar os  pontos de distribuicao disponiveis para a administradora/empresa enviada através do Token\",\r\n        \"operationId\": \"PessoasPontosDistribuicaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/PontosDistribuicaoGetRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/ConsultarPontoDistribuicaoResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/CartoesVinculadosPaginados\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Método responsável por listar uma consulta paginada de cartões por pessoa, administradoras e produtos\",\r\n        \"operationId\": \"PessoasCartoesVinculadosPaginadosGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"documento\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Documento do portador (CNPJ /CPF)\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"administradoras\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Lista de administradoras as quaisse está realizando a consulta\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"produtos\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Lista de produtos para filtro na consulta\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"integer\",\r\n              \"format\": \"int32\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"pageSize\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Numero de itens para consulta paginada\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"pageIndex\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Numero da pagina na consulta paginada\",\r\n            \"required\": false,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/CartaoVinculadoPaginadoPessoaListResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Pessoas/CartoesVinculadosLista\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Pessoa\"\r\n        ],\r\n        \"summary\": \"Busca os cartões vinculados em cada uma das pessoas informadas. Para buscar os cartões vinculados, buscar na tabela Cartao.Pessoa\\r\\npela pessoa cujo campo cpfcnpj seja igual ao documento e selecionar o campo PessoaId. Após isto, buscar na tabela Cartao.Cartao pelo cartao\\r\\nvinculado na pessoa através do campo pessoaId. Trazer os cartoes que tem o status 6 -Vinculado\",\r\n        \"operationId\": \"PessoasCartoesVinculadosListaPost\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"Documentos\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/PessoasCartoesVinculadosResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Produtos\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Produto\"\r\n        ],\r\n        \"summary\": \"Listar produtos habilitados ao cliente autenticado na requisição.\",\r\n        \"operationId\": \"ProdutosGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Lista com produtos disponíveis ao cliente autenticado na requisição.\\r\\n<li>id: Identificando único do produto para plataforma de cartões</li><li>nome: Nome comercial do cartão</li><li>isMultiplasContas: Significa que o produto permite mais de um cartão ativo em simultâneo para o mesmo CPF. Quando vinculado um cartão, onde este indicador é \\\"false\\\", o cartão antigo é automaticamente bloqueado e o saldo existente é movido ao novo cartão</li>\",\r\n            \"schema\": {\r\n              \"type\": \"array\",\r\n              \"items\": {\r\n                \"$ref\": \"#/definitions/ProdutoResponse\"\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Produtos/{id}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Produto\"\r\n        ],\r\n        \"summary\": \"Consultar produto pelo codigo autenticado na requisição.\",\r\n        \"operationId\": \"ProdutosByIdGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"id\",\r\n            \"in\": \"path\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Produto.\\r\\n<li>id: Identificando único do produto para plataforma de cartões</li><li>nome: Nome comercial do cartão</li><li>isMultiplasContas: Significa que o produto permite mais de um cartão ativo em simultâneo para o mesmo CPF. Quando vinculado um cartão, onde este indicador é \\\"false\\\", o cartão antigo é automaticamente bloqueado e o saldo existente é movido ao novo cartão</li>\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ProdutoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Produtos/ArteProduto\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Produto\"\r\n        ],\r\n        \"summary\": \"Retornar a arte do produto\",\r\n        \"operationId\": \"ProdutosArteProdutoGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"produtoId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Id do produto no MS Cartão\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ProdutoArteResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Produtos/CorBase\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Produto\"\r\n        ],\r\n        \"summary\": \"Retornar a cor base do cartão\",\r\n        \"operationId\": \"ProdutosCorBaseGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"produtoId\",\r\n            \"in\": \"query\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ProdutoCorBaseResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Relatorios/Cartoes/Situacao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Relatorio\"\r\n        ],\r\n        \"description\": \"\",\r\n        \"operationId\": \"RelatoriosCartoesSituacaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioCartaoApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioCartaoApiResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Relatorios/Transacoes/ConciliacaoAnalitico\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Relatorio\"\r\n        ],\r\n        \"description\": \"\",\r\n        \"operationId\": \"RelatoriosTransacoesConciliacaoAnaliticoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioConciliacaoAnaliticoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioConciliacaoAnaliticoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Relatorios/Transacoes/Faturamento\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Relatorio\"\r\n        ],\r\n        \"description\": \"\",\r\n        \"operationId\": \"RelatoriosTransacoesFaturamentoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioFaturamentoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioFaturamentoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Relatorios/Transferencias/ContaBancaria\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Relatorio\"\r\n        ],\r\n        \"description\": \"\",\r\n        \"operationId\": \"RelatoriosTransferenciasContaBancariaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioTransferenciasContaBancariaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioTransferenciasContaBancariaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Relatorios/Transferencias/ContasBancariasPorDocumento\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Relatorio\"\r\n        ],\r\n        \"description\": \"\",\r\n        \"operationId\": \"RelatoriosTransferenciasContasBancariasPorDocumentoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PageSize\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"PageIndex\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"CustomFilters\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"OrderBy\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioTransferenciasContasBancariasRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioTransferenciasContasBancariasResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Relatorios/RelatorioPortadorPorProduto\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Relatorio\"\r\n        ],\r\n        \"summary\": \"Consulta de portadores por produto\",\r\n        \"operationId\": \"RelatoriosRelatorioPortadorPorProdutoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"PageSize\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"PageIndex\",\r\n            \"in\": \"query\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"CustomFilters\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"OrderBy\",\r\n            \"in\": \"query\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {},\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"relatorioPortadorProdutoRequest\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioPortadorProdutoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/RelatorioPortadorProdutoResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/Enviar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Envio de remessas de cartão\",\r\n        \"operationId\": \"RemessaEnviarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EnvioRemessaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EnvioRemessaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/ValidarCartao\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Valida cartão a ser enviado para remessa\",\r\n        \"operationId\": \"RemessaValidarCartaoPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ValidarCartaoRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EnvioRemessaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/ValidarLoteCartoes\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Valida uma faixa de cartoes para ser enviado para remessa\",\r\n        \"operationId\": \"RemessaValidarLoteCartoesPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ValidarCartoesLoteRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/EnvioRemessaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/Baixar\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Baixa de remessas de cartão\",\r\n        \"operationId\": \"RemessaBaixarPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BaixaRemessaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/BaixaRemessaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/Consultar/{lote}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Consultar remessa a partir do numero do lote\",\r\n        \"operationId\": \"RemessaConsultarByLoteGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"lote\",\r\n            \"in\": \"path\",\r\n            \"description\": \"\",\r\n            \"required\": true,\r\n            \"type\": \"integer\",\r\n            \"format\": \"int32\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarRemessaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/ConsultarRemessasEmpresa/{filtrarEmpresaOrigem}\": {\r\n      \"get\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Consultar remessas enviadas ou recebidas com base na lista de CNPJ informados.\",\r\n        \"operationId\": \"RemessaConsultarRemessasEmpresaByFiltrarEmpresaOrigemGet\",\r\n        \"consumes\": [],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"cnpjList\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Lista de CNPJ para filtrar a remessa\",\r\n            \"required\": false,\r\n            \"type\": \"array\",\r\n            \"items\": {\r\n              \"type\": \"string\"\r\n            },\r\n            \"collectionFormat\": \"multi\"\r\n          },\r\n          {\r\n            \"name\": \"filtrarEmpresaOrigem\",\r\n            \"in\": \"path\",\r\n            \"description\": \"Ao informar <code>empresaOrigem = true</code> a lista de CNPJ informada será filtrada\\r\\n             pela empresaOrigem, caso contrário, será filtrada pela <code>empresaDestino</code>\",\r\n            \"required\": true,\r\n            \"type\": \"boolean\"\r\n          },\r\n          {\r\n            \"name\": \"dataInicio\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Data de início para filtrar a remessa\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"dataFim\",\r\n            \"in\": \"query\",\r\n            \"description\": \"Data fim para filtrar a remessa\",\r\n            \"required\": true,\r\n            \"type\": \"string\",\r\n            \"format\": \"date-time\"\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarRemessaEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Remessa/ConsultarRemessasEmpresa\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"Remessa\"\r\n        ],\r\n        \"summary\": \"Consultar remessas enviadas ou recebidas com base na lista de CNPJ informados.\",\r\n        \"operationId\": \"RemessaConsultarRemessasEmpresaPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [\r\n          \"application/json\"\r\n        ],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"request\",\r\n            \"in\": \"body\",\r\n            \"description\": \"\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarRemessasEmpresaRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\",\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/ConsultarRemessaEmpresaResponse\"\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  \"definitions\": {\r\n    \"AdministradorasResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"administradoras\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/AdministradorasObjResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ApiProcessingStateOnServer\": {\r\n      \"required\": [\r\n        \"state\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"state\": {\r\n          \"enum\": [\r\n            \"Ok\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"errorMessage\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"AdministradorasObjResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesvinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"portador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        }\r\n      }\r\n    },\r\n    \"ProdutoResponse\": {\r\n      \"required\": [\r\n        \"id\",\r\n        \"nome\",\r\n        \"isMultiplasContas\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"isMultiplasContas\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"produtoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"taxas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ProdutoTaxa\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ProdutoTaxa\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"enum\": [\r\n            \"TransferenciaContaBancaria\",\r\n            \"TransferenciaEntreContas\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"CustomFilter\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"operator\": {\r\n          \"enum\": [\r\n            \"StartsWith\",\r\n            \"EndsWith\",\r\n            \"Contains\",\r\n            \"NotContains\",\r\n            \"Equals\",\r\n            \"NotEquals\",\r\n            \"IsNull\",\r\n            \"IsNotNull\",\r\n            \"GreaterThan\",\r\n            \"GreaterThanOrEqual\",\r\n            \"LessThan\",\r\n            \"LessThanOrEqual\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"value\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"serverFieldCollection\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"fieldType\": {\r\n          \"enum\": [\r\n            \"Auto\",\r\n            \"Date\",\r\n            \"String\",\r\n            \"Number\",\r\n            \"Boolean\",\r\n            \"Intervalo\",\r\n            \"Unknown\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"OrderOptions\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"asc\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"FilteredResult[MotivoBloqueioModel]\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"data\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/MotivoBloqueioModel\"\r\n          }\r\n        },\r\n        \"filteredOptions\": {\r\n          \"$ref\": \"#/definitions/FilteredOptions\"\r\n        }\r\n      }\r\n    },\r\n    \"MotivoBloqueioModel\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"permanente\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"FilteredOptions\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"totalRecords\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"pageCount\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultaCartaoAdministradoraResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"administradoraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"empresaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DocumentosDestinoTransferenciaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pessoas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/PessoasDestinoParaTransferencia\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoasDestinoParaTransferencia\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoVinculado\": {\r\n          \"$ref\": \"#/definitions/CartaoPessoasDestinoParaTransferencia\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoPessoasDestinoParaTransferencia\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"saldoConta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoEmpresasResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"empresas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultarSaldoEmpresasResponseItem\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoEmpresasResponseItem\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpj\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"saldoConta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarEmpresaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dadosBancarios\": {\r\n          \"$ref\": \"#/definitions/ContaBancariaRequest\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaEnderecoRequest\"\r\n        },\r\n        \"info\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaInfoRequest\"\r\n        },\r\n        \"flags\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaTipoFlagsRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"ContaBancariaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoBacenBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"digitoConta\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoConta\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Corrente\",\r\n            \"Poupanca\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarPessoaEnderecoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"logradouro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numero\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"bairro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cep\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"complemento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cidade\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarPessoaInfoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sexo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rg\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeMae\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePai\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataNascimento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"telefone\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"celular\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"email\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tacEquiparado\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarPessoaTipoFlagsRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"pontoDistribuicaoCartao\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"pontoCargaMoedeiro\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ApiResponseValidation\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ApiResponseValidation\": {\r\n      \"required\": [\r\n        \"type\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"type\": {\r\n          \"enum\": [\r\n            \"Information\",\r\n            \"Warning\",\r\n            \"Validation\",\r\n            \"Error\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"message\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"field\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"GetOrGenerateCartaoAppTokenApiResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"token\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"grupoContabilizacaoTransacao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"razaoSocial\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"VincularRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"IdentificadorCartao\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarPessoaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"endereco\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaEnderecoRequest\"\r\n        },\r\n        \"info\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaInfoRequest\"\r\n        },\r\n        \"flags\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaTipoFlagsRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"VincularResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DesvincularRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"motivoDesvinculo\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DesvincularResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"BloquearCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"motivo\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"observacao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"BloquearCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DesbloquearCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"observacao\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"DesbloquearCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"VincularCartaoVirtualRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"pessoa\": {\r\n          \"$ref\": \"#/definitions/IntegrarPessoaRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"VincularCartaoVirtualResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarExtratoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"exibirMetadados\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"somenteTransferencia\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarExtratoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"saldoInicialPeriodo\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"saldoFinalPeriodo\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"detalhes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultarExtratoDetalheResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarExtratoDetalheResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"data\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"descricaoProcessadora\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"tipoTransacaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoTransacaoDescricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"saldoFinal\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"historicoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"historico\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"nomeDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"instituicao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dv\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarSaldoCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valorLimiteCredito\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorSaldoDisponivel\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarProtocoloRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarProtocoloResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataConfirmacaoTransacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoOrigem\": {\r\n          \"$ref\": \"#/definitions/ConsultarProtocoloCartaoResponse\"\r\n        },\r\n        \"cartaoDestino\": {\r\n          \"$ref\": \"#/definitions/ConsultarProtocoloCartaoResponse\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarProtocoloCartaoResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"CarregarValorRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"numeroCIOT\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CarregarValorResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarCargaCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicaoParaEstorno\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"EstornarCargaCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numeroTransacao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ResgatarValorRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ResgatarValorResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarResgateValorRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicaoParaEstorno\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarResgateValorReponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CarregarPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estabelecimento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"numeroCIOT\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CarregarPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"retornoProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarCargaPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estabelecimento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicaoParaEstorno\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"informacoesProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"EstornarCargaPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"retornoProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConfirmarCargaPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"informacoesProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ConfirmarCargaPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"retornoProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConfirmarEstornoPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estabelecimento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicaoParaEstorno\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConfirmarEstornoPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"retornoProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarSaldoResidualPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estabelecimento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"numeroCIOT\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"EstornarSaldoResidualPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"retornoProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConfirmarEstornoSaldoResidualPedagioRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"ConfirmarEstornoSaldoResidualPedagioResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"retornoProcessadora\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"TransferirValorCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoOrigem\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"cartaoDestino\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"TransferirValorCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarTransferenciaCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicaoParaEstorno\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"permitirTransacaoPendente\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"EstornarTransferenciaCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numeroTransacao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"TransferirValorContaBancariaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartaoOrigem\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"contaBancaria\": {\r\n          \"$ref\": \"#/definitions/IdentificadorContaBancaria\"\r\n        },\r\n        \"documentoFavorecido\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"IdentificadorContaBancaria\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoBacenBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"digitoConta\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoConta\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Corrente\",\r\n            \"Poupanca\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"TransferirValorContaBancariaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CompraRedeCredenciadaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpjEstabelecimento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CompraRedeCredenciadaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloProcessamento\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EstornarCompraRedeCredenciadaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"protocoloRequisicaoParaEstorno\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoesAdicionais\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"EstornarCompraRedeCredenciadaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numeroTransacao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarCompraRedeCredenciadaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpjEstabelecimento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarCompraRedeCredenciadaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"compras\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CompraRedeCredenciadaDetalhes\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CompraRedeCredenciadaDetalhes\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"data\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"documentoPortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipo\": {\r\n          \"enum\": [\r\n            \"Compra\",\r\n            \"Estorno\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"historicoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"historico\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ValidarSenhaCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"senha\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ValidarSenhaCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"AlterarSenhaCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"senha\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"AlterarSenhaCartaoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoasCartoesVinculadosResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pessoas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/PessoasCartoesVinculados\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoasCartoesVinculados\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CartoesVinculados\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CartoesVinculados\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutosCartoesVinculados\"\r\n        },\r\n        \"cartaoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"status\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"motivoBloqueio\": {\r\n          \"$ref\": \"#/definitions/MotivoBloqueioProdutosCartoesVinculados\"\r\n        },\r\n        \"dataBloqueio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"saldo\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ProdutosCartoesVinculados\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"idMultiplasContas\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"produtoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"taxas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/TaxasProdutosCartoesVinculados\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"MotivoBloqueioProdutosCartoesVinculados\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"permanente\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"TaxasProdutosCartoesVinculados\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"descricao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"CancelarCartaoRemessaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"statusCartaoFrete\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CancelarCartaoRemessaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarPessoaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sexo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ativo\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"cidade\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarPessoaDetalhadaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"sexo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"ativo\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"cidade\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"bairro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cep\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"complemento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"logradouro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"estado\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"numero\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"celular\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"email\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"telefone\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"rg\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeMae\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePai\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataNascimento\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PessoaContaBancariaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"nomeConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoBacenBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"digitoConta\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoConta\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Corrente\",\r\n            \"Poupanca\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"InativarContaBancariaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documento\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"codigoBacenBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoContaBancaria\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Corrente\",\r\n            \"Poupanca\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"InativarContaBancariaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"IntegrarPessoaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ApiResponseValidation\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ApiActionResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoVinculadoPessoaListResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CartaoVinculadoPessoaResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoVinculadoPessoaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        },\r\n        \"cartaoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"motivoBloqueio\": {\r\n          \"$ref\": \"#/definitions/MotivoBloqueioModel\"\r\n        },\r\n        \"dataBloqueio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoBloqueadoPessoaListResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CartaoBloqueadoPessoaResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoBloqueadoPessoaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        },\r\n        \"cartaoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"motivoBloqueio\": {\r\n          \"$ref\": \"#/definitions/MotivoBloqueioModel\"\r\n        },\r\n        \"dataBloqueio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"HistoricoCartaoPessoaListResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/HistoricoCartaoPessoaResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"HistoricoCartaoPessoaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesvinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        },\r\n        \"motivoDesvinculo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartaoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"motivoBloqueio\": {\r\n          \"$ref\": \"#/definitions/MotivoBloqueioModel\"\r\n        },\r\n        \"dataBloqueio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"AguardandoRemessaParaCliente\",\r\n            \"AguardandoRemessaParaPontoDistribuicao\",\r\n            \"EmTransitoParaPontoDistribuicao\",\r\n            \"EmTransitoRemessaRejeitada\",\r\n            \"AguardandoVinculacao\",\r\n            \"Vinculado\",\r\n            \"CanceladoTrocaCartao\",\r\n            \"Cancelado\",\r\n            \"RejeitadoPontoDistribuicao\",\r\n            \"Bloqueado\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusDescricao\": {\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarPontoDistribuicaoResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeFantasia\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"PontosDistribuicaoGetRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpjs\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CartaoVinculadoPaginadoPessoaListResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CartaoVinculadoPaginadoPessoaResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartaoVinculadoPaginadoPessoaResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"contas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/CartoesVinculadosPaginadoContasResponse\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"CartoesVinculadosPaginadoContasResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"administradoraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"$ref\": \"#/definitions/CartoesVinculadosPaginadoProdutoResponse\"\r\n        },\r\n        \"cartaoMestre\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"cartaoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"motivoBloqueio\": {\r\n          \"$ref\": \"#/definitions/MotivoBloqueioModel\"\r\n        },\r\n        \"dataBloqueio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"CartoesVinculadosPaginadoProdutoResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"isMultiplasContas\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"produtoMestre\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"produtoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"corBase\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"taxas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ProdutoTaxa\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"ProdutoArteResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"produtoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"arte\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataAtualizacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ProdutoCorBaseResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"produtoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"corBase\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataAtualizacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioCartaoApiRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoPeriodo\": {\r\n          \"enum\": [\r\n            \"Todos\",\r\n            \"Vinculo\",\r\n            \"Desvinculo\",\r\n            \"RecepcaoPontoDistribuicao\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"produtos\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"format\": \"int32\",\r\n            \"type\": \"integer\"\r\n          }\r\n        },\r\n        \"statusCartao\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"enum\": [\r\n              \"AguardandoRemessaParaCliente\",\r\n              \"AguardandoRemessaParaPontoDistribuicao\",\r\n              \"EmTransitoParaPontoDistribuicao\",\r\n              \"EmTransitoRemessaRejeitada\",\r\n              \"AguardandoVinculacao\",\r\n              \"Vinculado\",\r\n              \"CanceladoTrocaCartao\",\r\n              \"Cancelado\",\r\n              \"RejeitadoPontoDistribuicao\",\r\n              \"Bloqueado\"\r\n            ],\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"pontoDistribuicao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"portador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"remessa\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioCartaoApiResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dados\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/Item\"\r\n          }\r\n        },\r\n        \"sucesso\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"Item\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cartaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cartaoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produtoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"produtoMestreId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"portadorId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"documentoPortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"portador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"AguardandoRemessaParaCliente\",\r\n            \"AguardandoRemessaParaPontoDistribuicao\",\r\n            \"EmTransitoParaPontoDistribuicao\",\r\n            \"EmTransitoRemessaRejeitada\",\r\n            \"AguardandoVinculacao\",\r\n            \"Vinculado\",\r\n            \"CanceladoTrocaCartao\",\r\n            \"Cancelado\",\r\n            \"RejeitadoPontoDistribuicao\",\r\n            \"Bloqueado\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusText\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusPedagio\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesvinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"motivoDesvinculo\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"administradoraId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"administradora\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"empresaId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cnpjEmpresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"empresa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pontoDistribuicaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"documentoPontoDistribuicao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"pontoDistribuicao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataRecepcaoPontoDistribuicao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioConciliacaoAnaliticoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"processarCartaoId\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"exibirMetadados\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"novaVersao\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioConciliacaoAnaliticoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"saldoInicial\": {\r\n          \"$ref\": \"#/definitions/SaldoEmpresaProcessadoraResponse\"\r\n        },\r\n        \"saldoFinal\": {\r\n          \"$ref\": \"#/definitions/SaldoEmpresaProcessadoraResponse\"\r\n        },\r\n        \"itens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConciliacaoItem\"\r\n          }\r\n        },\r\n        \"empresaCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"SaldoEmpresaProcessadoraResponse\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"saldoConta\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"ConciliacaoItem\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"statusProcessadora\": {\r\n          \"enum\": [\r\n            \"Existente\",\r\n            \"Pendente\",\r\n            \"Erro\",\r\n            \"Inexistente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusMeioHomologado\": {\r\n          \"enum\": [\r\n            \"Existente\",\r\n            \"Pendente\",\r\n            \"Erro\",\r\n            \"Inexistente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoProcessadora\": {\r\n          \"enum\": [\r\n            \"CargaPortador\",\r\n            \"EstornoPortador\",\r\n            \"CargaPedagioPortador\",\r\n            \"EstornoPedagioPortador\",\r\n            \"EstornoDepositoEmpresa\",\r\n            \"DepositoEmpresa\",\r\n            \"OutrosDebitos\",\r\n            \"OutrosCreditos\",\r\n            \"Inesperado\",\r\n            \"ResgateSaldoResidualPedagio\",\r\n            \"CargaPedagioManual\",\r\n            \"EstornoPedagioManual\",\r\n            \"TransferenciaViaPixDebito\",\r\n            \"TransferenciaViaPixCredito\",\r\n            \"PagamentoDeContas\",\r\n            \"EstornoPagamentoDeContas\",\r\n            \"ResgateValorDebito\",\r\n            \"ResgateValorCredito\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        },\r\n        \"data\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"documentoPortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"protocoloResposta\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"mensagemRetorno\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoTransacao\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"historico\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"informacoes\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"metadados\": {\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioFaturamentoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"listaCnpjEmpresa\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"processarCartaoId\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"exibirMetadados\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioFaturamentoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"lRelatorioConciliacao\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/RelatorioConciliacaoAnaliticoResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContaBancariaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Todos\",\r\n            \"NaoExportados\",\r\n            \"Exportados\",\r\n            \"Sucesso\",\r\n            \"Erro\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"tipoTransacao\": {\r\n          \"enum\": [\r\n            \"Todos\",\r\n            \"Transferencias\",\r\n            \"Estornos\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContaBancariaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"itens\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ItemTransacao\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ItemTransacao\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"transacaoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"identificadorCartao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjPortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cpfCnpjConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeDonoConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoTransacao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"valor\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"status\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusExportacao\": {\r\n          \"enum\": [\r\n            \"NaoExportado\",\r\n            \"Exportado\",\r\n            \"Sucesso\",\r\n            \"Erro\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagemExportacao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeContaBancaria\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataExportacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataAlteracaoExportacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContasBancariasRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoPortador\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContasBancariasResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"filteredOptions\": {\r\n          \"$ref\": \"#/definitions/FilteredOptions\"\r\n        },\r\n        \"transacoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/RelatorioTransferenciasContasBancarias\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContasBancarias\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cpfCnpjPortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomePortador\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/RelatorioTransferenciasContasBancariasCartao\"\r\n        },\r\n        \"contasBancaria\": {\r\n          \"$ref\": \"#/definitions/RelatorioTransferenciasContasBancariasConta\"\r\n        },\r\n        \"valores\": {\r\n          \"$ref\": \"#/definitions/RelatorioTransferenciasContasBancariasValores\"\r\n        },\r\n        \"protocoloRequisicao\": {\r\n          \"format\": \"int64\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"AguardandoEnvioRemessa\",\r\n            \"AguardandoRetornoBanco\",\r\n            \"FinalizadoSucesso\",\r\n            \"FinalizadoFalha\",\r\n            \"FalhaAoEstornar\",\r\n            \"EstornadoParaConta\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataTransacao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataEnvioAoBanco\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataRetornoBanco\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"motivo\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContasBancariasCartao\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContasBancariasConta\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"codigoBacen\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeBanco\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"agencia\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"conta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"digitoConta\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"tipoConta\": {\r\n          \"enum\": [\r\n            \"Indefinido\",\r\n            \"Corrente\",\r\n            \"Poupanca\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioTransferenciasContasBancariasValores\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"valorTransferencia\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorTaxa\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        },\r\n        \"valorTotal\": {\r\n          \"format\": \"double\",\r\n          \"type\": \"number\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioPortadorProdutoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"produtoId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"dataInicial\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFinal\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioPortadorProdutoResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"Falha\",\r\n            \"Sucesso\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"filteredOptions\": {\r\n          \"$ref\": \"#/definitions/FilteredOptions\"\r\n        },\r\n        \"relatorioPortadorProduto\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/RelatorioPortadorProduto\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"RelatorioPortadorProduto\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cpfCnpj\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nome\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"status\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataVinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataDesvinculo\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"EnvioRemessaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documentoOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"EnvioRemessaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"lote\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ValidarCartaoRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documentoOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartao\": {\r\n          \"$ref\": \"#/definitions/IdentificadorCartao\"\r\n        }\r\n      }\r\n    },\r\n    \"ValidarCartoesLoteRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"documentoOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"identificadorInicial\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"identificadorFinal\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"BaixaRemessaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"lote\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/IdentificadorCartaoRemessa\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"IdentificadorCartaoRemessa\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"AguardandoBaixa\",\r\n            \"Aceito\",\r\n            \"Rejeitado\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"motivoRemessa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"produtoCartao\": {\r\n          \"$ref\": \"#/definitions/ProdutoResponse\"\r\n        },\r\n        \"identificador\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"produto\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        }\r\n      }\r\n    },\r\n    \"BaixaRemessaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"remessaRejeitados\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarRemessaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"lote\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"documentoOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeOrigem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"documentoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataCadastro\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataAlteracao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataBaixa\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"remessaOriginalId\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"nomeUsuarioCadastro\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"nomeUsuarioBaixa\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"cartoes\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/IdentificadorCartaoRemessa\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarRemessaEmpresaResponse\": {\r\n      \"required\": [\r\n        \"status\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"processingStateOnServer\": {\r\n          \"$ref\": \"#/definitions/ApiProcessingStateOnServer\"\r\n        },\r\n        \"status\": {\r\n          \"enum\": [\r\n            \"NaoProcessado\",\r\n            \"Sucesso\",\r\n            \"Erro\",\r\n            \"ProtocoloExistente\",\r\n            \"Pendente\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"mensagem\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"consultarRemessaResponseList\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/ConsultarRemessaResponse\"\r\n          }\r\n        },\r\n        \"stackTrace\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"ConsultarRemessasEmpresaRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"cnpjList\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"filtrarEmpresaOrigem\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"dataInicio\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataFim\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "url": "http://localhost:5400/Cartoes/Api/swagger/v1/swagger.json", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToCSharpClient": {"clientBaseClass": "SistemaInfoMicroServiceBaseClient", "configurationClass": "HttpContext", "generateClientClasses": true, "generateClientInterfaces": false, "clientBaseInterface": null, "injectHttpClient": false, "disposeHttpClient": true, "protectedMethods": [], "generateExceptionClasses": true, "exceptionClass": "SwaggerException", "wrapDtoExceptions": true, "useHttpClientCreationMethod": true, "httpClientType": "System.Net.Http.HttpClient", "useHttpRequestMessageCreationMethod": true, "useBaseUrl": true, "generateBaseUrlProperty": true, "generateSyncMethods": true, "generatePrepareRequestAndProcessResponseAsAsyncMethods": false, "exposeJsonSerializerSettings": false, "clientClassAccessModifier": "public", "typeAccessModifier": "public", "generateContractsOutput": false, "contractsNamespace": null, "contractsOutputFilePath": null, "parameterDateTimeFormat": "s", "parameterDateFormat": "yyyy-MM-dd", "generateUpdateJsonSerializerSettingsMethod": true, "useRequestAndResponseSerializationSettings": false, "serializeTypeInformation": false, "queryNullValue": "", "className": "{controller}Client", "operationGenerationMode": "MultipleClientsFromPathSegments", "additionalNamespaceUsages": ["System.Web", "ATS.Data.Repository.External.SistemaInfo"], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": true, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.Generic.List", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "SistemaInfo.MicroServices.Rest.Cartao.ApiClient", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.Generic.List", "arrayInstanceType": null, "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": null, "arrayBaseType": "System.Collections.Generic.List", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Inpc", "jsonLibrary": "NewtonsoftJson", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": false, "generateNullableReferenceTypes": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": "../../Web/ATS.Data.Repository.External/SistemaInfo/Cartao/Client/Cartao.ApiClient.cs", "newLineBehavior": "Auto"}}}
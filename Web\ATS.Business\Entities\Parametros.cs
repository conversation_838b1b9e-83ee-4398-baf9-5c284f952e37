using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Parametros
    {
        public int IdParametro { get; set; }

        [Index("IX_ParametrosUniqueKey", 1, IsUnique = true)]
        public string NomeTabela { get; set; }

        [Index("IX_ParametrosUniqueKey", 2, IsUnique = true)]
        public int? IdEmpresa { get; set; }

        [Index("IX_ParametrosUniqueKey", 3, IsUnique = true)]
        public int? IdRegistro { get; set; }

        [Index("IX_ParametrosUniqueKey", 4, IsUnique = true)]
        public string IdRegistroStr { get; set; }

        [Index("IX_ParametrosUniqueKey", 5, IsUnique = true)]
        public string Chave { get; set; }

        [SkipTracking]
        public string ValorString { get; set; }
        public decimal? ValorDecimal { get; set; }
        public DateTime? ValorDateTime { get; set; }
        public bool? ValorBoolean { get; set; }
    }

    public enum EMPRESA_ID
    {
        [EnumMember, Description("Administradora da plataforma para qual a empresa está integrando.")]
        IdAdministradoraPlataforma,

        [EnumMember, Description("Ação com o saldo residual em uma nova carga de pedágio moedeiro.")]
        AcaoSaldoResidualNovoCreditoCartaoPedagio,

        [EnumMember, Description("Autoriza automaticamente os estabelecimentos da rede JSL.")]
        AutorizaEstabelecimentosRedeJSL,

        [EnumMember,  Description("Valor limite que cada cheque pode conter")]
        ValorLimitePagamentoCheque,

        [EnumMember, Description("E-mail de contato para falhas de integrações entre sistemas:")]
        EmailSustentacaoIntegracaoSistemas,

        [EnumMember, Description("Série padrão do cheque")]
        SeriePadraoCheque,

        [EnumMember, Description("Quantidade máxima de impressões que pode ser realizada por cheque")]
        QuantidadeLimiteImpressoesCheque,

        [EnumMember, Description("Margem do topo para impressão de cheque")]
        MarginTopImpressaoCheque,

        [EnumMember, Description("Margem da esquerda para impressão de cheque")]
        MarginLeftImpressaoCheque,

        [EnumMember, Description("Endpoint de consumo para cheques")]
        EndpointIntegracaoCheque,

        [EnumMember, Description("Headers para consumo da API de cheques")]
        HeadersIntegracaoCheque,

        [EnumMember, Description("Banco padrão do cheque")]
        BancoPadraoCheque,

        [EnumMember, Description("Obriga informar o armazém na ordem de carregamento")]
        ObrigatoriedadeArmazemOC,

        [EnumMember, Description("Obriga ordem de compra na ordem de carregeamento")]
        OrdemCompraOC,

        [EnumMember, Description("Obriga fórmula na ordem de carregeamento")]
        ObrigatoriedadeFormulaOC,

        [EnumMember, Description("Obriga pedido na ordem de carregeamento")]
        ObrigatoriedadePedidoOC,

        [EnumMember, Description("Obriga protocolo na ordem de carregeamento")]
        ObrigatoriedadeProtocoloOC,

        [EnumMember, Description("Obriga quantidade (kg) na ordem de carregeamento")]
        ObrigatoriedadeQuantidadeOC,

        [EnumMember, Description("Horas limite para carga do crédito de pedágio da viagem, campo DataExpiracaoCreditoPendente")]
        HorasExpiracaoCreditoPedagio,

 		[EnumMember, Description("Roteiriza obrigatoriamente as viagens criadas")]
        ObrigaRoteirizacaoPedagioViagem,

        [EnumMember, Description("Separador de arquivos CSV")]
        SeparadorArquivoCsv,

        [EnumMember, Description("Mostrar o cabeçalho no arquivo CSV")]
        MostrarHeaderArquivoCsv,

        [EnumMember, Description("Informações referente ao envio de Transferência Bancaria")]
        InformacoesTransferenciaBancaria,

        [EnumMember, Description("Parâmetro para seguir com o cancelamento da viagem mesmo que o proprietário não tenha saldo suficiente para realizar o extorno. Nesse caso será verificado se há saldo no motorista para repassar ao proprietário e assim realizar o extorno para cancelar a viagem.")]
        CancelarViagemComSaldoMenorQueValorDoExtorno,

        [EnumMember, Description("Número de registros de carga avulsa que serão processados pelo serviço")]
        NumeroDeRegistrosDeCargaAvulsaParaProcessar,

        [EnumMember, Description("Parâmetro utilizado para realizar ou não a validação se os documentos informados na viagem, são os mesmos informados nas integrações que são feitas junto com a viagem")]
        ValidarDocumentosViagemComDocumentosDasIntegracoes,

        [EnumMember, Description("Reemitir CIOT padrão para alterações na viagem")]
        ReemiteCiotPadraoAlteracaoViagem,

        [EnumMember, Description("Permitir consultar todas as viagens da empresa sem nenhum filtro")]
        PermiteConsultarTodasViagensEmpresa,

        [EnumMember, Description("Token utilizado no caso de a empresa utilizar token administradora ou super token")]
        TokenMicroServicoCentralAtendimento,

        [EnumMember, Description("Permissão para bloquear cartão na plataforma de atendimento")]
        AtendimentoPermiteBloquearCartao,

        [EnumMember, Description("Permissão para desbloquear cartão na plataforma de atendimento")]
        AtendimentoPermiteDesbloquearCartao,

        [EnumMember, Description("Permissão para alterar senha de cartão na plataforma de atendimento")]
        AtendimentoPermiteAlterarSenhaCartao,

        [EnumMember, Description("Permissão para realizar transferência bancária na plataforma de atendimento")]
        AtendimentoPermiteRealizarTransferenciaBancaria,

        [EnumMember, Description("Permissão para realizar transferência entre cartões na plataforma de atendimento")]
        AtendimentoPermiteRealizarTransferenciaCartoes,

        [EnumMember, Description("Permissão para realizar resgate de frete na plataforma de atendimento")]
        AtendimentoPermiteRealizarResgate,

        [EnumMember, Description("Permissão para estornar resgate de frete na plataforma de atendimento")]
        AtendimentoPermiteRealizarEstornoResgate,

        [EnumMember, Description("Ocultar botão de pesquisa de terceiros na tela de usuário")]
        OcultarListagemTerceiros,

        [EnumMember, Description("Somente disponibiliza o perfil empresa ao cadastrar novos usuários")]
        CadastraSomentePerfilEmpresa,

        [EnumMember, Description("Define se na compra de pedágio fará o registro do vale pedágio na ANTT")]
        RegistrarValePedagio,

        [EnumMember, Description("Define se realiza triagem de documentos de credenciamentos feitos internamente ou se aprova a documentação automaticamente")]
        RealizaTriagemEstabelecimentoInterno,

        [EnumMember, Description("E-mails para os quais serão enviados avisos sobre o contrato agregado")]
        EmailAvisoCiotAgregado,

        [EnumMember, Description("Dias para o cancelamento da viagem, caso seja 0 ou não seja preenchido em tela o valor preenchido devera ser 0")]
        DiasCancelamentoViagem,

        [EnumMember, Description("Motivo padrão para bloqueio, parametro na empresa vincular novo cartão bloqueado")]
        MotivoPadraoBloqueioCartaoParametroEmpresa,

        [EnumMember, Description("Valor minimo alerta email conta frete")]
        ValorMinimoAlertaEmailContaFrete,
        
        [EnumMember, Description("Valor minimo alerta email conta Pix")]
        ValorMinimoAlertaEmailContaPix,

        [EnumMember, Description("Exibe portadores com cnpj na grid despesas de viagem")]
        GridDespesasViagemListarCnpj,

        [EnumMember, Description("Mantém viagem aberta após cancelamento do último evento")]
        MantemViagemAbertaAposCancelamentoDoUltimoEvento,
        
        [EnumMember, Description("Permite cadastrar Motorista com CPF fictício")]
        PermiteCadastrarMotoristaComCpfFicticio,

        [EnumMember, Description("Permite cadastrar Proprietário com CPF fictício")]
        PermiteCadastrarProprietarioComCpfFicticio,

        [EnumMember, Description("Permite vincular cartão para CPF fictício")]
        PermiteVincularCartaoComCpfFicticio,
        
        [EnumMember, Description("Permite utlização do fornecedor Tag Extratta")]
        TagExtrattaPermiteUtilizarFornecedor,   
        
        [EnumMember, Description("Taxa de VPO ao utilizar fornecedor Tag Extratta")]
        TagExtrattaTaxaVpo,

        [EnumMember, Description("Valo da Tag Extratta")]
        TagExtrattaValorTag,

        [EnumMember, Description("Valor da mensalidade da Tag Extratta")]
        TagExtrattaValorMensalidade,

        [EnumMember, Description("Tag Extratta provisionar valor")]
        TagExtrattaProvisionarValor,
        
        [EnumMember, Description("Tag Extratta provisionar taxa")]
        TagExtrattaProvisionarTaxa,
        
        [EnumMember, Description("Hub Move Mais provisionar valor")]
        HubMoveMaisProvisionarValor,
        
        [EnumMember, Description("Hub Move Mais provisionar taxa")]
        HubMoveMaisProvisionarTaxa,
        
        [EnumMember, Description("Hub ConectCar provisionar valor")]
        HubConectCarProvisionarValor,
        
        [EnumMember, Description("Hub ConectCar provisionar taxa")]
        HubConectCarProvisionarTaxa,
        
        [EnumMember, Description("Hub Via Facil provisionar valor")]
        HubViaFacilProvisionarValor,
        
        [EnumMember, Description("Hub Via Facil provisionar taxa")]
        HubViaFacilProvisionarTaxa,
        
        [EnumMember, Description("Hub Taggy Edenred provisionar valor")]
        HubTaggyEdenredProvisionarValor,
        
        [EnumMember, Description("Hub Taggy Edenred provisionar taxa")]
        HubTaggyEdenredProvisionarTaxa,
        
        [EnumMember, Description("% da taxa aplicada quando quando fornecedor Taggy Edenred utilizar credenciais extratta")]
        TaggyEdenredExtrattaTaxaVpo,
        
        [EnumMember, Description("Hub Veloe provisionar valor")]
        HubVeloeProvisionarValor,
        
        [EnumMember, Description("Hub Veloe provisionar taxa")]
        HubVeloeProvisionarTaxa,
        
        [EnumMember, Description("Tag Extratta provisionar taxa na passagem do webhook")]
        TagExtrattaProvisionarTaxaPedagioWebhook,
        
        [EnumMember, Description("Tag Extratta provisionar valor na passagem do webhook")]
        TagExtrattaProvisionarValorPedagioWebhook,
        
        [EnumMember, Description("Saldo minímo cartao frete para bloqueio da TAG")]
        TagExtrattaSaldoMinimoContaFreteWebhook,

        [EnumMember, Description("Tag Extratta faixa de tolerância em relação ao saldo mínimo cartao frete para notificação via email ao cliente")]
        TagExtrattaFaixaToleranciaNotificacaoEmail,
        
        [EnumMember, Description("Bloquear apenas a TAG da passagem do webhook")]
        TagExtrattaBloquearTagUnitaria,

        [EnumMember, Description("Bloquear todas as TAGs da empresa na passagem do webhook")]
        TagExtrattaBloquearTagLote,
        
        [EnumMember, Description("Valor substituição da TAG")]
        TagExtrattaValorSubstituicao,
        
        [EnumMember, Description("Valor recarga da conta")]
        TagExtrattaValorRecargaConta,   
        
        [EnumMember, Description("Tag Extratta estornar pedágio na passagem do webhook")]
        TagExtrattaEstornarPedagio,
        
        [EnumMember, Description("% da taxa aplicada quando quando fornecedor Move mais utilizar credenciais extratta")]
        MoveMaisExtrattaTaxaVpo,
        
        [EnumMember, Description("Cobrar taxa utilizando pedágio Tag Extratta")]
        TagExtrattaUtilizaTaxaPedagio  ,
        
        [EnumMember, Description("% da taxa aplicada quando quando fornecedor Via Facil utilizar credenciais extratta")]
        ViaFacilExtrattaTaxaVpo,
        
        [EnumMember, Description("% da taxa aplicada quando fornecedor Veloe utilizar credenciais extratta")]
        VeloeExtrattaTaxaVpo,
        
        [EnumMember, Description("Permite a empresa utilizar a opcao de efetuar o pagamento de um evento por Pix")]
        PermiteRealizarPagamentoPix,
        
        [EnumMember, Description("Limite do valor total diário para pagamentos efetuados via Pix por Integracao")]
        LimiteDiarioPagamentoPixIntegracao,
        
        [EnumMember, Description("Limite do valor de um unico pagamento via Pix por Integracao")]
        LimiteUnitarioPagamentoPixIntegracao,
        
        [EnumMember, Description("Limite do valor total diário para pagamentos efetuados via Pix pelo Portal")]
        LimiteDiarioPagamentoPixPortal,
        
        [EnumMember, Description("Limite do valor de um unico pagamento via Pix pelo Portal")]
        LimiteUnitarioPagamentoPixPortal,
        
        [EnumMember, Description("Carga avulsa gerada na integração será salva com status pendente de aprovação")]
        SolicitaAprovacaoGestorCargaAvulsaIntegracao,
        
        [EnumMember, Description("Carga avulsa gerada unitário será salva com status pendente de aprovação")]
        SolicitaAprovacaoGestorCargaAvulsaUnitario,
        
        [EnumMember, Description("Carga avulsa gerada em lote será salva com status pendente de aprovação")]
        SolicitaAprovacaoGestorCargaAvulsaLote,
        
        [EnumMember, Description("\"% da taxa aplicada quando quando fornecedor ConectCar utilizar credenciais extratta")]
        ConectCarExtrattaTaxaVpo,
        
        [EnumMember, Description("Permite usuários da empresa gerarem relatórios na extensão Ofx")]
        UtilizaRelatoriosOfx,

        [EnumMember, Description("Mantém viagem aberta após baixa do último evento")]
        MantemViagemAbertaAposBaixaDoUltimoEvento,
        
        [EnumMember, Description("Dafault do tipo de rodagem na Integração")]
        DefaultIntegracaoTipoRodagemDupla,
        
        [EnumMember, Description("Solicitar o PIN (Senha do Cartão) para baixar parcelas Pix, usado atualmente na gestão de alçadas caso a Biz queira auditar o processo")]
        SolicitarSenhaTransacionalPix,

        [EnumMember, Description("Usuário marcado como gestor nível 1 para solicitações de chaves Pix de proprietários")]
        IdUsuarioGestorChavePixNivel1,

        [EnumMember, Description("Usuário marcado como gestor nível 2 para solicitações de chaves Pix de proprietários")]
        IdUsuarioGestorChavePixNivel2,

        [EnumMember, Description("Todas as solicitacoes de chaves Pix para proprietarios dessa empresa serao aprovadas automaticamente")]
        AprovarSolicitacoesChavePixAutomaticamente,
        
        [EnumMember, Description("Valida Cargas Avulsas duplicadas para mesmo valor, documento e portador")]
        BloqueiaCargaAvulsaDuplicada,
        
        [EnumMember, Description("Periodo em horas para validacao das cargas avulsas duplicadas")]
        HorasBloqueioCargaAvulsaDuplicada,

        [EnumMember, Description("Usa tipo de rodagem Dupla quando tem 3 ou mais eixos na compra de pedágio para MoveMais e Veloe")]
        RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe,

        [EnumMember, Description("Usuário marcado como gestor nível 2 para solicitações de chaves Pix de proprietários")]
        NaoBaixarParcelasDeposito,
        
        [EnumMember, Description("Utiliza Roteirização a partir de polyline")]
        UtilizaRoteirizacaoPorPolyline,
        
        [EnumMember, Description("Código OFX")]
        CodOfx,

    }

    // Não renomear o Enum
    public enum GLOBAL
    {
        [EnumMember, Description("% Transferência de Adiantamento")]
        PercTransferenciaAdiantamento,

        [EnumMember, Description("% Transferência de Saldo")]
        PercTransferenciaSaldo,

        [EnumMember, Description("% Transferência de Estadia")]
        PercTransferenciaEstadia,

        [EnumMember, Description("% Transferência de RPA")]
        PercTransferenciaRPA,

        [EnumMember, Description("% Transferência de Tarifa ANTT")]
        PercTransferenciaTarifaANTT,

        [EnumMember, Description("% Transferência de Abastecimento")]
        PercTransferenciaAbastecimento,

        [EnumMember, Description("% Transferência de Abono")]
        PercTransferenciaAbono,

        [EnumMember, Description("Id do tipo de estabelecimento padrão da JSL")]
        IdTipoEstabelecimentoPadraoJSL,

		[EnumMember, Description("SuperUserToken de utilização da plataforma de atendimento para acessar os microserviços")]
        SuperUserToken,

        [EnumMember, Description("Id da empresa padrão para usuários de estabelecimento JSL")]
        IdEmpresaPadraoUsuarioEstabelecimentoJSL,

        [EnumMember, Description("E-mail de contato para falhas de integrações entre sistemas (interno Sistema Info):")]
        EmailSustentacaoIntegracaoSistemas,

        [EnumMember, Description("Id usuário genérico para chamadas web service que precisam de usuário cadastrado no ATS:")]
        IdUsuarioGenericoWS,

        [EnumMember, Description("Parâmetro manual criado para adicionar validação do documento do proprietário na integração da viagem")]
        ValidaCnpjCpfProprietarioNaViagem,

        [EnumMember, Description("Utilizado por portadores que não tenham vínculo com empresa, como motoristas terceiros")]
        IdLayoutCartao,

        [EnumMember, Description("Utilizado por portadores que não tenham vínculo com empresa, como motoristas terceiros")]
        IdProdutoCartaoFrete,

        [EnumMember, Description("Criado novo metodo para o conciliação de transações devido a má performance do método atual")]
        MetodoRefatoradoRelatorioConciliacaoAnalitico,

        [EnumMember, Description("Tipo de carga padrão na ANTT")]
        TipoCargaAnttDefault,

        [EnumMember, Description("Parâmetro criado para a virada de chave da ANTT, utilizado para verificar se usará a nova versão ou a antiga")]
        VersaoAntt,

        [EnumMember, Description("Parâmetro criado para validar apenas quantidade de caracteres da CNH")]
        ValidarApenasQuantidadeDeCaracteresCnh,

        [EnumMember, Description("Grupo de contabilização de transações do micro serviço de cartão")]
        GrupoContabilizacaoCentralAtendimento,

        [EnumMember, Description("Desativa o parâmetro de Antifraude")]
        DesativaAntiFraude,

        [EnumMember, Description("Ativa a verificação de IP em determinadas integrações")]
        AtivarVerificacaoIP,
        
        [EnumMember, Description("CNPJ da extratta que irá receber as tarifas do VPO Extratta")]
        TagExtrattaCnpjProvisionamento,
        
        [EnumMember, Description("Usuário para autenticação do webhook de passagem na praça de pedágio TAG Extratta")]
        UsuarioWebhookMoveMaisTagExtratta,
        
        [EnumMember, Description("Senha para autenticação do webhook de passagem na praça de pedágio TAG Extratta")]
        SenhaWebhookMoveMaisTagExtratta,
        
        [EnumMember, Description("Endereco de email para enviar CC quando um usuário der nota baixa na pesquisa de satisfação")]
        EnderecoEmailComCopiaPesquisaSatisfacaoNotaBaixa,

        [EnumMember, Description("Início do período noturno para transações, em horas")]
        PeriodoNoturnoInicio,

        [EnumMember, Description("Fim do período noturno para transações, em horas")]
        PeriodoNoturnoFim,

        [EnumMember, Description("Valor máximo de uma carga avulsa no período noturno")]
        CargaValorMaximoUnitarioNoturno,

        [EnumMember, Description("Valor máximo de uma carga avulsa no período diurno")]
        CargaValorMaximoUnitarioDiurno,

        [EnumMember, Description("Valor máximo da primeira carga avulsa de um cartão")]
        CargaValorMaximoPrimeiraTransacao,

        [EnumMember, Description("Intervalo de horas desde o vínculo do cartão para habilitar cargas nele")]
        CargaIntervaloHorasVinculoCartao,

        [EnumMember, Description("Intervalo de horas entre cargas para permitir uma outra")]
        CargaIntervaloHorasOutrasCargas,

        [EnumMember, Description("Habilita o envio do e-mail de aviso quando uma nova localização de login de um usuário é detectada")]
        HabilitarEnvioEmailLoginNovaLocalizacao,

        [EnumMember, Description("Habilita o envio de notificações push sobre as transações retornadas pela biz")]
        HabilitarPushWebhookTransacao,

        [EnumMember, Description("Habilita consulta de veículo no Sem Parar durante integrações de viagem e pedágio avulso")]
        HabilitarConsultaStatusVeiculoSemPararIntegracoes,
        
        [EnumMember, Description("Chave RSA privada, usada para descriptografia de dados")]
        ChaveRsaPrivada,
        
        [EnumMember, Description("Chave RSA publica, usada para criptografia de dados")]
        ChaveRsaPublica,
        
        [EnumMember, Description("Controla se ocorrerá o bloqueio do usuario caso erre a senha multiplas vezes no login do mobile")]
        LoginMobileBloquearUsuarioErroSenha,
        
        [EnumMember, Description("Numero de erros de senha necessarios para bloquear um usuario no login do mobile")]
        LoginMobileQuantidadeErroSenhaBloqueioUsuario,
        
        [EnumMember, Description("Limite global do valor total diário para pagamentos efetuados via Pix")]
        LimiteDiarioPagamentoPix,
        
        [EnumMember, Description("Limite global do valor de um unico pagamento via Pix")]
        LimiteUnitarioPagamentoPix,

        [EnumMember, Description("Limite global de valor de transferencia cartao para CNPJ")]
        LimitePadraoTransferenciaCartaoCNPJ,

        [EnumMember, Description("Limite global de valor de transferencia cartao para CPF")]
        LimitePadraoTransferenciaCartaoCPF,

        [EnumMember, Description("Limite global de valor de transferencia TED para CNPJ")]
        LimitePadraoTransferenciaTEDCNPJ,

        [EnumMember, Description("Limite global de valor de transferencia TED para CPF")]
        LimitePadraoTransferenciaTEDCPF,

    }

    // Não renomear o Enum
    public enum USUARIO_ID
    {
        [EnumMember, Description("Permite alterar o limite de alçada na empresa")]
        PermiteAlterarLimitesAlcadasEmpresa,

        [EnumMember, Description("Permite alterar o limite de alçada na filial")]
        PermiteAlterarLimitesAlcadasFilial,

        [EnumMember, Description("Permissão para bloquear cartão na plataforma de atendimento")]
        AtendimentoPermiteBloquearCartao,

        [EnumMember, Description("Permissão para desbloquear cartão na plataforma de atendimento")]
        AtendimentoPermiteDesbloquearCartao,

        [EnumMember, Description("Permissão para alterar senha de cartão na plataforma de atendimento")]
        AtendimentoPermiteAlterarSenhaCartao,

        [EnumMember, Description("Permissão para realizar transferência bancária na plataforma de atendimento")]
        AtendimentoPermiteRealizarTransferenciaBancaria,

        [EnumMember, Description("Permissão para realizar transferência entre cartões na plataforma de atendimento")]
        AtendimentoPermiteRealizarTransferenciaCartoes,

        [EnumMember, Description("Permissão para realizar resgate de frete na plataforma de atendimento")]
        AtendimentoPermiteRealizarResgate,

        [EnumMember, Description("Permissão para estornar resgate de frete na plataforma de atendimento")]
        AtendimentoPermiteRealizarEstornoResgate,

        [EnumMember, Description("Administradora dos aplicativos para o usuário")]
        IdProjetoFireBase,

        [EnumMember, Description("Permissão para realizar transferência bancária na plataforma de atendimento")]
        AplicativoPermiteRealizarTransferenciaBancaria,

        [EnumMember, Description("Permissão para realizar transferência entre cartões na plataforma de atendimento")]
        AplicativoPermiteRealizarTransferenciaCartoes,

        [EnumMember, Description("Permite acesso geral aos métodos do atendimento")]
        PermiteAcessoAtendimento,

        [EnumMember, Description("Permite edição de dados administrativos das Empresas")]
        PermitirEdicaoDadosAdministrativosEmpresa,

        [EnumMember, Description("Permite edição de dados administrativos das Filiais")]
        PermitirEdicaoDadosAdministrativosFilial,

        [EnumMember, Description("Permite edição de dados administrativos dos Usuários")]
        PermitirEdicaoDadosAdministrativosUsuario,

        [EnumMember, Description("Permite edição de dados administrativos dos Grupos de Usuários")]
        PermitirEdicaoDadosAdministrativosGrupoUsuario,

        [EnumMember, Description("Permite acesso ao extrato detalhado")]
        PermitirAcessoExtratoDetalhado,

        [EnumMember, Description("Permite aprovar solicitações de adiantamentos feitas por Proprietários e Motoristas no APP")]
        PermiteAprovarSolicitacaoAdiantamentoApp,

        [EnumMember, Description("Permite solicitar adiantamento via aplicativo")]
        PermiteSolicitarAdiantamentoApp,

        [EnumMember, Description("Permite efetuar as cargas dos adiantamentos solicitados pelos Proprietários e Motoristas no APP")]
        PermiteEfetuarCargaSolicitacaoAdiantamentoApp,
        
        [EnumMember, Description("Limite do valor total diário para pagamentos efetuados via Pix, esse limite é prioritário sobre o limite da empresa e pode ser maior")]
        LimiteDiarioPagamentoPix,
        
        [EnumMember, Description("Limite do valor de um unico pagamento via Pix, esse limite é prioritário sobre o limite da empresa e pode ser maior")]
        LimiteUnitarioPagamentoPix,
        
        [EnumMember, Description("Permite edição de dados de dados bancarios Pix")]
        PermitirEdicaoDadosAdministrativosPix,
        
        [EnumMember, Description("Permite o usuario utilizar a opcao de efetuar o pagamento de parcelas por Pix")]
        PermiteRealizarPagamentoPix,
        
        [EnumMember, Description("Permite o usuario cadastrar e deletar as chaves Pix da empresa")]
        PermitirCadastroChavePix,
        
        [EnumMember, Description("Permite solicitar alterações dos limites Pix da empresa no Banco Central")]
        PermiteSolicitarAlteracoesLimitePix,
        
        [EnumMember, Description("Configura o usuário como um gestor de alçadas para as transferências Pix")]
        GestorAlcadasPix,
    }

    // Não renomear o Enum
    public enum PROPRIETARIO_DOCUMENTO
    {
        [EnumMember, Description("% Transferência de Adiantamento")]
        PercTransferenciaAdiantamento,

        [EnumMember, Description("% Transferência de Saldo")]
        PercTransferenciaSaldo,

        [EnumMember, Description("% Transferência de Estadia")]
        PercTransferenciaEstadia,

        [EnumMember, Description("% Transferência de RPA")]
        PercTransferenciaRPA,

        [EnumMember, Description("% Transferência de Tarifa ANTT")]
        PercTransferenciaTarifaANTT,

        [EnumMember, Description("% Transferência de Abastecimento")]
        PercTransferenciaAbastecimento,

        [EnumMember, Description("% Transferência de Abono")]
        PercTransferenciaAbono,

        [EnumMember, Description("Ação com o saldo residual em uma nova carga de pedágio moedeiro.")]
        AcaoSaldoResidualNovoCreditoCartaoPedagio,

        [EnumMember, Description("Permite que este proprietário receba pagamentos Pix de empresas")]
        PermiteReceberPagamentoPix,
    }

    public enum PROPRIETARIO_MOTORISTA_DOCUMENTO
    {
        [EnumMember, Description("% Transferência de Adiantamento")]
        PercTransferenciaAdiantamento,

        [EnumMember, Description("% Transferência de Saldo")]
        PercTransferenciaSaldo,

        [EnumMember, Description("% Transferência de Estadia")]
        PercTransferenciaEstadia,

        [EnumMember, Description("% Transferência de RPA")]
        PercTransferenciaRPA,

        [EnumMember, Description("% Transferência de Tarifa ANTT")]
        PercTransferenciaTarifaANTT,

        [EnumMember, Description("% Transferência de Abastecimento")]
        PercTransferenciaAbastecimento,

        [EnumMember, Description("% Transferência de Abono")]
        PercTransferenciaAbono
    }

    public enum FILIAL_PARAMS
    {
        [EnumMember, Description("Obriga informar o armazém na ordem de carregamento")]
        ObrigatoriedadeArmazemOC,

        [EnumMember, Description("Obriga ordem de compra na ordem de carregeamento")]
        OrdemCompraOC,

        [EnumMember, Description("Obriga fórmula na ordem de carregeamento")]
        ObrigatoriedadeFormulaOC,

        [EnumMember, Description("Obriga pedido na ordem de carregeamento")]
        ObrigatoriedadePedidoOC,

        [EnumMember, Description("Obriga protocolo na ordem de carregeamento")]
        ObrigatoriedadeProtocoloOC,

        [EnumMember, Description("Obriga quantidade (kg) na ordem de carregeamento")]
        ObrigatoriedadeQuantidadeOC
    }

    public enum ADMINISTRADORA_MH_ID
    {
        [EnumMember, Description("IdAdministradora utilizada na Transferencia Cartoes/Bancos")]
        TokenAdministradora,

        [EnumMember, Description("ProdutoId padrão conforme administradora Transferencia Cartoes/Bancos")]
        ProdutoIdPadrao
    }

    public enum ADMINISTRADORA_PLATAFORMA_ID
    {
        [EnumMember, Description("Caminho do e-mail utilizado para recuperação de senhas do APP")]
        CaminhoEmailRecuperacaoSenhaApp,

        [EnumMember, Description("Caminho do logo da administradora")]
        LogoAdministradora,

        [EnumMember, Description("Endereço de e-mail para envio dos e-mails da plataforma")]
        EmailEndereco,

        [EnumMember, Description("Senha de e-mail para envio dos e-mails da plataforma")]
        EmailSenha,

        [EnumMember, Description("Porta de e-mail para envio dos e-mails da plataforma")]
        EmailPorta,

        [EnumMember, Description("SMTP de e-mail para envio dos e-mails da plataforma")]
        EmailSMTP,

        [EnumMember, Description("SSL de e-mail para envio dos e-mails da plataforma")]
        EmailSSL
    }

    public enum PROJETO_FIREBASE
    {
        [EnumMember, Description("Key para envio de push do aplicativo da plataforma")]
        KeyEnvioPush
    }

    public enum ESTABELECIMENTO_ID
    {
        [EnumMember, Description("Define se o estabelecimento irá validar a chave de pagamento na baixa de evento")]
        ValidarChavePagamento
    }

    public enum RECURSO_MOBILE
    {
        [EnumMember, Description("Define se o mobile valida a senha que é igual ao CPF")]
        ValidarSenhaIgualCPF,

        [EnumMember, Description("Define telefone para o atendimento")]
        TelefoneAtendimento,

        [EnumMember, Description("Aplicativo utiliza keycloack para autenticação")]
        UtilizaKeycloack
    }
}

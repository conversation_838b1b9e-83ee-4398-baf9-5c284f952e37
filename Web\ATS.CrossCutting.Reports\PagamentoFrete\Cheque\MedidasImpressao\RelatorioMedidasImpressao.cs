﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.Base;

namespace ATS.CrossCutting.Reports.PagamentoFrete.Cheque.MedidasImpressao
{
    public class RelatorioMedidasImpressao
    {
        public byte[] GetReport(Margins margins)
        {
            var lista = new List<RelatorioMedidasImpressaoDataType>
            {
                new RelatorioMedidasImpressaoDataType {MarginLeft = 0, MarginTop = 0}
            };

            var bytes = new Base.Reports().GetReport(lista, new Tuple<string, string, bool>[0], true,
                "DtsMedidasImpressao",
                "ATS.CrossCutting.Reports.PagamentoFrete.Cheque.MedidasImpressao.RelatorioMedidasImpressao.rdlc", "pdf",
                new Margins {Top = margins.Top, Botton = margins.Botton, Right = margins.Right, Left = margins.Left});

            return bytes;
        }
    }
}

using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Models.ViagemModels
{
   public class ReciboPefDadosResponse
   {
      public DateTime DataImpressao { get; set; }
      public string Contratante { get; set; }
      public string Filial { get; set; }
      public string DocumentoProprietario { get; set; }
      public string Contratado { get; set; }
      public string CnpjContratante { get; set; }
      public string CnpjFilial { get; set; }
      public string Ciot { get; set; }
      public string DocumentoCliente { get; set; }
      public int IdViagem { get; set; }
      public DateTime? DataInicioViagem { get; set; }
      public string PlacaCavalo { get; set; }
      public IEnumerable<PlacaCarreta> PlacasCarreta { get; set; }
      public string Placas { get; set; }
      public int? Rntrc { get; set; }
      public string CartaoContratado { get; set; }
      public decimal ValorTotalParcelas { get; set; }
      public string Motorista { get; set; }
      public string DocumentoMotoristra { get; set; }
      public string CartaoMotorista { get; set; }
      public string Fornecedor { get; set; }
      public string Protocolo { get; set; }
      public string NomeCampoComprovanteCarga { get; set; }
      public string CampoComprovanteCarga { get; set; }
      public decimal ValorComprovanteCarga { get; set; }
      public string StatusComprovanteCarga { get; set; }
      public string MensagemProtocoladoAnttComprovanteCarga { get; set; }
      public bool ViagemPossuiPedagio { get; set; }
      public List<Parcelas> Parcelas { get; set; }
      public string RazaoSocialRemetente { get; set; }
      public string CnpjCpfRemetente { get; set; }
      public string RazaoSocialDestinatario { get; set; }
      public string CnpjCpfDestinatario { get; set; }
    }

   public class Parcelas
   {
      public int IdEvento { get; set; }
      public ETipoEventoViagem TipoEvento { get; set; }
      public DateTime? DataPagamento { get; set; }
      public string Instrucoes { get; set; }
      public EStatusViagemEvento StatusEvento { get; set; }
      public decimal Valor { get; set; }
      public bool PagamentoCartao { get; set; }
   }

   public class PlacaCarreta
   {
      public string Placa { get; set; }
   }
}


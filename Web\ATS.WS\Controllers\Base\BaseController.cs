﻿using ATS.Application.Application;
using NLog;
using System;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Application.Interface.Common;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Models;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Controllers.Base
{

    public abstract class BaseApiController<TEntityApp> : BaseController where TEntityApp : IBaseApp
    {
        public readonly TEntityApp App;

        protected BaseApiController(BaseControllerArgs baseArgs, TEntityApp app) : base(baseArgs)
        {
            App = app;
        }
    }

    public class BaseControllerArgs
    {
        public BaseControllerArgs(IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IUserIdentity userIdentity)
        {
            AutenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            UserIdentity = userIdentity;
        }

        public IAutenticacaoAplicacaoApp AutenticacaoAplicacaoApp { get; } 
        public IUserIdentity UserIdentity { get; } 
    }

    public class BaseController : Controller
    {

        #region Propriedades

        public static Logger Logger = LogManager.GetCurrentClassLogger();
        protected BaseControllerArgs BaseArgs { get; }

        #endregion

        public BaseController(BaseControllerArgs baseArgs)
        {
            BaseArgs = baseArgs;
        }

        #region Validações de Token

        public bool Autenticar(string token, string cnpjAplicacao, string documentoUsuarioAudit = null)
        {
            return ValidarToken(token, documentoUsuarioAudit) || BaseArgs.AutenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token);
        }

        public bool ValidarToken(string token, string documentoUsuarioAudit = null)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            BaseArgs.UserIdentity.Origem = EUserOrigin.AtsWs;
            
            if (!string.IsNullOrWhiteSpace(documentoUsuarioAudit) && 
                ((documentoUsuarioAudit.Length == 11 && documentoUsuarioAudit.OnlyNumbers().Length == 11) ||
                 (documentoUsuarioAudit.Length == 14 && documentoUsuarioAudit.OnlyNumbers().Length == 14)))
                BaseArgs.UserIdentity.CpfCnpj = documentoUsuarioAudit;
            else 
                BaseArgs.UserIdentity.CpfCnpj = token.ToLower();

            string tokenFromWebConfig = WebConfigurationManager.AppSettings["Token"];
            if (token.ToLower() == tokenFromWebConfig.ToLower())
                return true;

            return false;
        }

        public bool ValidarTokenLogin(string token)
        {
            var userIdentity = BaseArgs.UserIdentity;
            userIdentity.CpfCnpj = token.ToLower();
            userIdentity.Origem = EUserOrigin.AtsWs;

            if (string.IsNullOrWhiteSpace(token))
                return false;

            string tokenLogin = WebConfigurationManager.AppSettings["TokenLogin"];

            if (token.ToLower() == tokenLogin.ToLower())
                return true;

            return false;
        }

        public ActionResult Index()
        {
            return Content(@"<html lang='pt-BR'>
                           <head>
                           <link rel='icon' type='image/ico' href='favicon.ico'>
                           <style type='text/css'>
                              #sistemaInfo { 
                                border-style: none; 
                                min-height: 70px; margin: 110px auto; width: 400px; text-align: center 
                              }
                              #frase { 
                                font-family: sans-serif;
                                font-size: 32px;
                                color: white;
                                font-stretch: condensed;
                              }
                           </style> 
                           </head>
                           <body style='background-color: rgb(63, 58, 54);'>
                              <div id='sistemaInfo'>
                                 <img alt='' src='../sistema.png' title='Sistema Info' />
                                 <p id='frase'>Extratta (API)</p>
                              </div>
                           </body>
                           </html>");
        }

        #endregion


        public JsonResult Responde<T>(T resposta)
        {
            return Json(resposta, JsonRequestBehavior.AllowGet);
        }

        public virtual JsonResult BigJson(object data)
        {
            return new JsonResult
            {
                Data = data,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                MaxJsonLength = 2147483644
            };
        }

        public JsonResult Mensagem(string mensagem)
        {
            return Json(new Models.Mobile.Common.Retorno<object>(mensagem), JsonRequestBehavior.AllowGet);
        }
        
        public JsonResult NaoAutorizado()
        {
            return Json(new Models.Mobile.Common.Retorno<object>("Integração não autorizada, entre em contato com o suporte."), JsonRequestBehavior.AllowGet);
        }

        public JsonResult TokenInvalido()
        {
            return Json(new Models.Mobile.Common.Retorno<object>("Token inválido"), JsonRequestBehavior.AllowGet);
        }

        public JsonResult FalhaAutenticacao()
        {
            return Json(new Models.Mobile.Common.Retorno<object>("Falha na autenticação, verifique seus dados"), JsonRequestBehavior.AllowGet);
        }

        public string GetRealIp()
        {
            var ip = this.HttpContext.Request.Headers["x-forwarded-for"] ?? this.HttpContext.Request.Headers["x-real-ip"] ?? this.HttpContext.Request.Headers["x_forwarded_for"];

            if (string.IsNullOrEmpty(ip))
                ip = this.HttpContext.Request.UserHostAddress;
            
            ip = ip.Split(',')[0] ;

            return ip;
        }
    }
}
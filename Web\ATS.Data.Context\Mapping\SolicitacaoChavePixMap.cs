﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class SolicitacaoChavePixMap : EntityTypeConfiguration<SolicitacaoChavePix>
    {
        public SolicitacaoChavePixMap()
        {
            ToTable("SOLICITACAO_CHAVE_PIX");

            HasKey(x => x.IdSolicitacaoChavePix);
            Property(x => x.IdSolicitacaoChavePix)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.DocumentoUsuarioAudit).IsOptional().HasMaxLength(200);
            Property(x => x.DataCadastro).HasColumnType("datetime2");
            Property(x => x.Chave).IsRequired().HasMaxLength(200);
            Property(x => x.TipoChave).IsRequired();
            Property(x => x.DocumentoTitular).IsRequired().HasMaxLength(20);
 
            HasOptional(x => x.UsuarioCadastro)
                .WithMany()
                .HasForeignKey(x => x.IdUsuarioCadastro);
 
            HasRequired(x => x.Empresa)
                .WithMany()
                .HasForeignKey(x => x.IdEmpresa);
 
            HasRequired(x => x.SolicitacaoChavePixStatus)
                .WithMany()
                .HasForeignKey(x => x.IdSolicitacaoChavePixStatus);
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsCartoesReciboTransferencia">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>44e454a4-20c5-4938-955e-daa6b833e1f8</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsCartoesReciboTransferencia1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b29476b5-8311-47ad-8b2a-119991edb1e7</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="ReciboTransferenciaDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsCartoesReciboTransferencia</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="NomeOrigem">
          <DataField>NomeOrigem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CpfCnpjOrigem">
          <DataField>CpfCnpjOrigem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CartaoOrigem">
          <DataField>CartaoOrigem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NomeDestino">
          <DataField>NomeDestino</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CpfCnpjDestino">
          <DataField>CpfCnpjDestino</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CartaoDestino">
          <DataField>CartaoDestino</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataTransacao">
          <DataField>DataTransacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Descricao">
          <DataField>Descricao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Informacoes">
          <DataField>Informacoes</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValorFormatado">
          <DataField>ValorFormatado</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataAtual">
          <DataField>DataAtual</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia</rd:DataSetName>
        <rd:TableName>ReciboTransferenciaDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia.ReciboTransferenciaDataType, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Image Name="Image2">
            <Source>Embedded</Source>
            <Value>logoextrattalaranjapreto</Value>
            <Sizing>FitProportional</Sizing>
            <Top>0.10936cm</Top>
            <Left>0.00001cm</Left>
            <Height>2.29669cm</Height>
            <Width>7.9004cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Image>
          <Textbox Name="Textbox6">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>RECIBO DE TRANSFERÊNCIA ENTRE CARTÕES</Value>
                    <Style>
                      <FontSize>12pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox6</rd:DefaultName>
            <Top>0.89288cm</Top>
            <Left>1.05834cm</Left>
            <Height>0.6cm</Height>
            <Width>19.93977cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox7">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>INFORMAÇÕES DE ORIGEM</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox6</rd:DefaultName>
            <Top>2.64594cm</Top>
            <Left>0.00002cm</Left>
            <Height>0.6cm</Height>
            <Width>20.99809cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>#a6a6a6</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox8">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>NOME:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>3.9165cm</Top>
            <Height>0.6cm</Height>
            <Width>1.38875cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox9">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CARTÃO:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>4.52003cm</Top>
            <Height>0.6cm</Height>
            <Width>1.82973cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox10">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!NomeOrigem.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>3.9165cm</Top>
            <Left>1.39228cm</Left>
            <Height>0.6cm</Height>
            <Width>14.00938cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox11">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CPF/CNPJ:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>3.9165cm</Top>
            <Left>15.40518cm</Left>
            <Height>0.6cm</Height>
            <Width>2.05021cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox12">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CpfCnpjOrigem.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>3.9165cm</Top>
            <Left>17.45892cm</Left>
            <Height>0.6cm</Height>
            <Width>3.53918cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox13">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CartaoOrigem.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>4.52003cm</Top>
            <Left>1.83326cm</Left>
            <Height>0.6cm</Height>
            <Width>19.16485cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox14">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>INFORMAÇÕES DA TRANSAÇÃO</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox6</rd:DefaultName>
            <Top>8.96344cm</Top>
            <Left>0.00002cm</Left>
            <Height>0.6cm</Height>
            <Width>20.99809cm</Width>
            <ZIndex>9</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>#a6a6a6</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox15">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!ValorFormatado.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>10.19872cm</Top>
            <Left>1.61807cm</Left>
            <Height>0.6cm</Height>
            <Width>19.38004cm</Width>
            <ZIndex>10</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox16">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>VALOR:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>10.19872cm</Top>
            <Left>0.00002cm</Left>
            <Height>0.6cm</Height>
            <Width>1.61805cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox17">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>DATA E HORA:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>10.80225cm</Top>
            <Left>0.00002cm</Left>
            <Height>0.6cm</Height>
            <Width>2.73812cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox18">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!DataTransacao.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>10.80225cm</Top>
            <Left>2.73814cm</Left>
            <Height>0.6cm</Height>
            <Width>18.25997cm</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox19">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>INFORMAÇÕES:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>11.40578cm</Top>
            <Left>0.00001cm</Left>
            <Height>0.6cm</Height>
            <Width>2.9586cm</Width>
            <ZIndex>14</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox20">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!Informacoes.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>11.40578cm</Top>
            <Left>2.95861cm</Left>
            <Height>0.6cm</Height>
            <Width>18.0395cm</Width>
            <ZIndex>15</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox21">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>INFORMAÇÕES DE DESTINO</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox6</rd:DefaultName>
            <Top>5.82233cm</Top>
            <Height>0.6cm</Height>
            <Width>20.9981cm</Width>
            <ZIndex>16</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <BackgroundColor>#a6a6a6</BackgroundColor>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox22">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>NOME:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>7.09289cm</Top>
            <Height>0.6cm</Height>
            <Width>1.38875cm</Width>
            <ZIndex>17</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox23">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CARTÃO:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>7.69642cm</Top>
            <Height>0.6cm</Height>
            <Width>1.82973cm</Width>
            <ZIndex>18</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox24">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!NomeDestino.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>7.09289cm</Top>
            <Left>1.39228cm</Left>
            <Height>0.6cm</Height>
            <Width>14.00938cm</Width>
            <ZIndex>19</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox25">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>CPF/CNPJ:</Value>
                    <Style>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox8</rd:DefaultName>
            <Top>7.09289cm</Top>
            <Left>15.40518cm</Left>
            <Height>0.6cm</Height>
            <Width>2.05021cm</Width>
            <ZIndex>20</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox26">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CartaoDestino.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Left</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>7.69641cm</Top>
            <Left>1.82973cm</Left>
            <Height>0.6cm</Height>
            <Width>19.16837cm</Width>
            <ZIndex>21</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox27">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CpfCnpjDestino.Value, "ReciboTransferenciaDts")</Value>
                    <Style />
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox10</rd:DefaultName>
            <Top>7.09289cm</Top>
            <Left>17.45892cm</Left>
            <Height>0.6cm</Height>
            <Width>3.53919cm</Width>
            <ZIndex>22</ZIndex>
            <Style>
              <Border>
                <Color>Silver</Color>
                <Style>Solid</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>12.00578cm</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>20.99811cm</Width>
      <Page>
        <PageFooter>
          <Height>2.54cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Image Name="Image3">
              <Source>Embedded</Source>
              <Value>WhatsAppImage20230427at091729</Value>
              <Sizing>FitProportional</Sizing>
              <Top>1.06646cm</Top>
              <Left>0.00001cm</Left>
              <Height>1.44708cm</Height>
              <Width>20.99809cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
            <Textbox Name="Textbox28">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>DATA DE IMPRESSÃO: </Value>
                      <Style />
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Top>1.91354cm</Top>
              <Left>14.65206cm</Left>
              <Height>0.6cm</Height>
              <Width>4.12043cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox29">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!DataAtual.Value, "ReciboTransferenciaDts")</Value>
                      <Style />
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox10</rd:DefaultName>
              <Top>1.91354cm</Top>
              <Left>18.80776cm</Left>
              <Height>0.6cm</Height>
              <Width>2.19034cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="logoextrattalaranjapreto">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAYGBgYHBgcICAcKCwoLCg8ODAwODxYQERAREBYiFRkVFRkVIh4kHhweJB42KiYmKjY+NDI0PkxERExfWl98fKcBBgYGBgcGBwgIBwoLCgsKDw4MDA4PFhAREBEQFiIVGRUVGRUiHiQeHB4kHjYqJiYqNj40MjQ+TERETF9aX3x8p//CABEIAGIA0gMBIgACEQEDEQH/xAAxAAEAAgMBAAAAAAAAAAAAAAAABAUCAwYBAQEAAwEBAAAAAAAAAAAAAAAAAgMEBQH/2gAMAwEAAhADEAAAAuqAAPB7ExrlNats4h6AAAAAAAAY5RY+7cuZ359XQ6NtBbTjs0XWPZKzOhzg9AAAAAAAAAU8HoqHFuQJsOFthf6JGzn4VkHyyF/op9RexYeRlYx6outcqsLCLTTjdhNpy3y3QCXYcX2Z6ABT2HL06LLZDiZdPYewpu7n8pKt6+Xkqh6LMrY1puMKS4zNFX0lSVm+5yPeevfDCl6CEVHYV9gAPPYsVbBnbMHQ11/V1WimLfUs+KaNeUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAC/9oADAMBAAIAAwAAACEAASsAAAAAAAAAAL+osIAAAAAAAAAC/IsgQijiCQwAABgOqjQxBSCxSgAfIUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//EAAL/2gAMAwEAAgADAAAAEPPPI3/PPPPPPPPAzpilfPPPPPPPPKKd2QCBKLJOHNPKFIKnNMJDKANFPPd9JlPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP/8QAIxEAAgEEAgICAwAAAAAAAAAAAgMEAAUSEzAyAREQQiJQUv/aAAgBAgEBPwD5O4RALHy4KAxMch4nEQLMgDIqC+ltAWJwGrhM0xcg7H0q3wmTJn5dex0IiI4jx3yF5WzcHUqjPZIHwo+w1AijGV6+3JdZ
K9en7FUFjBkZlrxGksFqxMeN7NaTP+aFc+VLyMGeiZVyt2uMBJDr3qxulAZpatmP6f8A/8QAJxEAAgEDAwQABwAAAAAAAAAAAgMEAAESBSIyBhETMBAUMUJQYrL/2gAIAQMBAT8A+IafNMchjsohISxL1IFZuADPESv9af0vggmKfmVaVAKVLsN+Ac61WaEKLt5cAorkRd7+vp7UvKj5dp7g/miWiNm0Qwy3nWpTSlyTL7LcPZoUEms81+AVJhg5Jhmzd+1OUaXGo+Q+tIeRoB35UEqBDh4gxfYbVpesXKQwZB7C4Vry4rredTF5fh//xAA2EAACAQMCAwQHBwUBAAAAAAABAgMABBEFEhMhMRAiQVEgMkBCUmGRFBUjU2JxgSQzUGChwf/aAAgBAQABPwL/AAL3cSNtOa+3RfOo5BIuR7IWA6muLH8YoOp6HsuJeFHnx8OyNC7BRSIEUAeyXUPFjx4+FYqCThuD9aDArmrm44snyHSgatItq7j1Ps17BhuIPHr2PcSLblBQNWUPFfJ9UdjyJGpZjgCjrNrn3vpUNxFNHvQ5q31CCeTYud3zq4v4Ldgr5z5CtRvuHEFTIdhmtNvkwI3LF2ap9RtoG2sTnyFW+pwTyBFVs0+r2qMV7xq2v7e4O1T3vI1ql7wl4aMQ/wD5Wm3ybUiYsZGNapfsDwonII9atPvIpFSEbiwXnmptUtoXKHcSKg1K2nbaCQfn6LoHUg1JGY3KmsVw24mwePSoIRDGFHZrUp4iReGMmgNI4O0t3sda0QnjyDw21fI1pfCVPHmK062a4mNzN58q1tU/CbHeNWFjAsUMu3v4zWxbnVGU9C9R2ltCd6IF5dac6MGbPM5qPYL+Pgk7d4xWtqgeNgO8etWVjAiQybe/t61qsaC8AXq3X+ags7eDvIuDip20niuX5setT8D7Qv2cnHKh09G8nh4wQrk1mP8AL/7UEsCzqCnPwPbqoxfd71cChDopGeL/ANq2gs7d
GmiPIjrU7zX8rsg7qDlWjT7o2hPu9K1z+7EP01bXMDxKEcZVOYrShvvnb9zWruy2nL3mxVlDp7Q7pn73lmrJUbUhs9UEkVrR/qIx+ira4glQCNwcKOVXzAanlugK1d3SNZTPE+fD61p8Vi6sZ35+VbYW1BFh9TeMejcS8KMtT5Zix6mg/wCHurmTmrOfixc/WHXsuLSG4XDj9jX3JB+Y9PZRtbrBubaKt7WK3j2J/NRadBDNxULZ8qu7OG5AD+HQ1badFb8TDMdwxVrYxWxYoTzqaGOVCjjIr7lt/jerawt7dtyg58zV1Zw3QG/qPEVaWMdruKknNXWnwXLbjkN5ioLKGGFovWDdc0dFts8nYVBpttAwcZLDxPo3UnFf5DpTLS2Ja1z73UUFqBjE4b60pBGfZbnfswo61wJfgNRWrs43Ly7Li1O/ci9a4EvwGrTiAFWX/Zf/xAApEAEAAgIBAwMDBAMAAAAAAAABABEhMVEQQWEgcZFAgbFQocHRYPDx/9oACAEBAAE/If0FhYTdE8PxmedfSF2AT/uTUj0FO5geZaquVndsmoIPpLEaZ+yYKJSRTdaEAXwl3LNHtdFj7+E9Vy/TcuX68TFOP3ITQMu+B6K0ndfPiEIvtLC1BnMPKY3yRJXBeFfEepMui6uC6PjGhh93+5jJ47DUJJQuTGJWJhpo4lrZwqZl8abDSjW+cudyxhsjEfzJ3hDNRpoh1j0Gr9JBYSdiDTySiU6Zjlaq+6dq7by9FicPumGuHz7uCHv+DAxoXivuTJsZeT/UHT2S+CIPnr7s2JbZ4JeyFMu0woaylu5/aoMwyFt4J2i5bd2BXgHzYdz2qrAsDNLcypsyi+zctS+PTiMDd1V9oc6KbDssur65EtvuhuCbDwuMQz1t4Ixm+jgP5l5c5eyJ4V3943IF7hU8GnzMVGND9mVVZtqlVK5/FJG4R+TEUFzuJyj72JghTI7RSKDhNYh+ZAV6Rc32OWXgtLWEBcS3
tzKa3Z/t0PZZo2T/AFCU2uJy1GwNOU7blZPLbGYDCKwbmSZfXhi9KabZYxYs3UcYluSuy5WJNOyMphpXxBiiK5JWdinJC43CJ1Ca9CgZnwl0LZY0ZXDB7DkgoafpdtLg+3Trig5b7wAqbuG1c9ODwA0/S10rpUqV/hX/xAAoEAEAAgICAAUEAgMAAAAAAAABABEhMUFRIEBhcYEQkbHBYNGh4fD/2gAIAQEAAT8Q8Vnm187IBBYA2xTJXlLclytSz+nLnH9Ny5fM+VQtuxavLCabeXo5YF1UjygfuzRa9hSJp6lueVDkhxLuGK7j5Y0++36OnRYHfAeVBUVWRVU12+YZezhJc7RKoWRaLwuIAFQheWqgiPq7GZPqBSdJ3HucUyq2QN+GSDS4wtnaLorftFTM5pJuxb2RGUiwl7GFwkHi84ImZe4kSx86KRcdoNFrGhsXFEzj4vnXCF5rJBcbej2GKZWFJBHY/qEUnWL7jbMZmtbjuHIqlRhiyX/kwNBX/wBxgwJu3X9aO7F9LyyjS2sQSa0masZZ4ZXY5XcCgip64vzM43YbEr6Fdsw/W3HgJWzTruIjQJ4+ruWZvtARl2SLDvBc9fVv38J9qQ0LRT/bcpudGp8Pn6VMr3ueECa+u5lJFlY7sx/r/pqtMeLZ+Wzb9PlRlbqkEU2QkS/xtEcbN9xZTPycJgFoPr4Yxg5kK57FYNVkmXAlyqAk6Lvtwf8Ar3TB5DV2TY5fDTI1qQmCozdzhL+5lil2x6lRxAP4+X0uNbtUi22r2jERpYg5UtZIxcrk37MZ2lqYxYysuhMsA0BUBdsQmi7xC/7PCPZ6ymuIsogd65gOiGFnXqgrPqHB0qXt4qsIBlS9tlcT22Oxzs0r4B4FCtRUB2B32zZiAQr+7T7xFSImESX81oQV8QsTyrZelpshphcEv6CAA1VEK0Z54QDHJCtv5KL/2Q==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="WhatsAppImage20230427at091729">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCAAOAfsDASIAAhEBAxEB/8QAGQABAQEAAwAAAAAAAAAAAAAABAABAgMJ/8QAIRAAAQMFAQADAQAAAAAAAAAAAAIDkQUTFVNUAQQREiH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAA
AAAAAAAAAP/aAAwDAQACEQMRAD8A9Sfan8BKlJV8tvz3z3698+yylP625O+2j9ff4TBttvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcllKf1tyItt60wVtvWmAD5Sn9bcmZWndjcibbetMHCyn3+/lMAf/9k=</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>bbea77d8-8b66-418e-a67d-c5baf0b8e7b9</rd:ReportID>
</Report>
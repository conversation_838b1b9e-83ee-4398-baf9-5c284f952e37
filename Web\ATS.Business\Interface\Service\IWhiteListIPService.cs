using ATS.Domain.Enum;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Interface.Service
{
    public interface IWhiteListIPService : IBaseService<IWhiteListIPRepository>
    {
        bool IPLiberado(string ip, EOrigemRequisicao origem = EOrigemRequisicao.Todos);
        bool IPLiberado(string ip, int idempresa, EOrigemRequisicao origem = EOrigemRequisicao.Todos);
    }
}
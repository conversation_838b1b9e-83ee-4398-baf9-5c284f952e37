﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IMotoristaApp : IAppBase<Motorista>
    {
        bool Any(string cpf, int idempresa, bool? ativo = null);
        Motorista Get(int id);
        Motorista Get(string cpf);
        byte[] GetFoto(int id);
        ValidationResult Add(Motorista motorista, int idUsuario, bool ignorePermissaoUsuario = false);
        ValidationResult Update(Motorista motorista, int? idUsuario, bool ignore = false);
        int? GetIdPorCpf(string cpf);
        Motorista GetWithChilds(string cpf, int? idUsuarioLogOn);
        IQueryable<Motorista> QueryById(int id);
        Motorista Get(string cpf, bool withIncludes);
        Motorista GetPorCpf(string cpfMotorista, bool withIncludes = true);
        ValidationResult Update(Motorista motorista, int idUsuario, bool ignore = false);
        int? GetIdPorCpf(string cpf, int? idEmpresa, bool? ativo = true);
        Motorista GetWithChilds(int id, int? idUsuarioLogOn);
        ValidationResult AlterarStatus(int idMotorista);
        int GetQtdMotoristasCadastradosByUser(int idUsuario);
        object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters,bool ativo);
        byte[] GerarRelatorioGridMotoristas(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters, string logo, string extensao);
        bool Any(string cpf, bool? ativo = null);
        Motorista GetFromAllTables(string cpfCnpj);
        IEnumerable<Motorista> ConsultarMotoristasAtualizados(int idEmpresa, DateTime dataBase);
        ValidationResult DesvincularMotoristaVeiculo(int idEmpresa, string cpfMotorista, string placa);
        List<Veiculo> GetVeiculosMotorista(string cpf, int? idEmpresa);
        bool PertenceAEmpresa(int idempresa, int idMotorista);

    }
}
﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Data.Repository.EntityFramework;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;

namespace ATS.Application.Application
{
    public class CheckInApp : AppBase, ICheckInApp
    {
        private readonly ICheckInRepository _checkinRepository;
        private readonly ICheckInService _checkInService;

        public CheckInApp(ICheckInRepository checkinRepository, ICheckInService checkInService)
        {
            _checkinRepository = checkinRepository;
            _checkInService = checkInService;
        }
        
        /// <summary>
        /// Adicionar o CheckIn
        /// </summary>
        /// <param name="checkIn">Dados do CheckIn</param>
        /// <returns>Retorna o código</returns>
        public ValidationResult Add(CheckIn checkIn)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _checkInService.Add(checkIn);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna os check-ins realizados em determinado período e placa
        /// </summary>
        /// <param name="dataInicial"></param>
        /// <param name="dataFinal"></param>
        /// /// <param name="placa"></param>
        /// <returns></returns>
        public List<CheckinConsultaModel> GetByPlaca(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa)
        {
            return _checkinRepository.GetByPlaca(IdEmpresa, dataInicial, dataFinal, placa);
        }
        
        public List<CheckinConsultaModel> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, int IdMotoristaVeiculo)
        {
            return _checkinRepository.GetByPlacaMotorista(IdEmpresa, dataInicial, dataFinal, IdMotoristaVeiculo);
        }

        public IQueryable<CheckIn> GetByEmpresa(int IdEmpresa)
        {
            var checkins = _checkInService.GetByEmpresa(IdEmpresa);

            return checkins;
        }

        /*public List<GestaoEntregaCheckin> GetCheckinsGestaoEntrega(List<string> cpfMotoristas, DateTime dataInicial, DateTime dataFinal)
        {
            return _checkInService.GetCheckinsGestaoEntrega(cpfMotoristas, dataInicial, dataFinal);
        }*/


        public List<CheckinConsultaModel> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf)
        {
            return _checkinRepository.GetByCpf(IdEmpresa, dataInicial, dataFinal, cpf);
        }


        /// <summary>
        /// Retorna check-ins para um período de data
        /// </summary>
        /// <param name="dataInicial"></param>
        /// <param name="dataFinal"></param>
        /// <returns></returns>
        public IQueryable<CheckIn> GetCheckInsPeriodo(DateTime dataInicial, DateTime? dataFinal = null)
        {
            return _checkInService.GetChekinsPeriodo(dataInicial, dataFinal);
        }

        /// <summary>
        /// Retorna os CheckIn realizados hoje
        /// </summary>
        /// <returns></returns>
        public IQueryable<CheckIn> GetCheckInsHoje(int? idTipoCavalo, int? idTipoCarreta)
        {
            return _checkInService.GetCheckInsHoje(idTipoCavalo, idTipoCarreta);
        }

        /// <summary>
        /// Retorna os dados do último CheckIn realizado pelo usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public CheckIn GetLast(int idUsuario)
        {
            return _checkInService.GetLast(idUsuario);
        }

        /// <summary>
        /// Retorna os dados do último CheckIn realizado pelo usuário buscando pelo IdMotorista
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public CheckIn GetLastByIdMotorista(int idMotorista)
        {
            return _checkInService.GetLastByIdMotorista(idMotorista);
        }

        public CheckIn GetLastByCpf(DateTime aDataInicial, DateTime aDataFinal, string cpf)
        {
            return _checkInService.GetLastByCpf(aDataInicial, aDataFinal, cpf);
        }

        public List<CheckIn> GetChekinsPeriodoTelao(DateTime dataInicial, DateTime dataFinal)
        {
            return _checkInService.GetChekinsPeriodoTelao(dataInicial, dataFinal);
        }
    }
}
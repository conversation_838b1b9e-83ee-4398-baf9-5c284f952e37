﻿using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Webservice.Request.Email;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;

namespace ATS.WS.Controllers
{
    public class EmailController : BaseController
    {
        private readonly ILayoutApp _layoutApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvEmail _srvEmail;

        public EmailController(BaseControllerArgs baseArgs, ILayoutApp layoutApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvEmail srvEmail) : base(baseArgs)
        {
            _layoutApp = layoutApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvEmail = srvEmail;
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string EnviarEmail(EnviarEmailRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvEmail.EnviarEmail(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
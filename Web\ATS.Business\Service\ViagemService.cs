﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity;
using System.Data.Entity.SqlServer;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Threading;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.Common.Request;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using ClosedXML.Excel;
using Microsoft.Ajax.Utilities;
using Sistema.Framework.Util.Enumerate;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using DateTimeHelper = ATS.Domain.Helpers.DateTimeHelper;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class ViagemService : BaseService<IViagemRepository>, IViagemService
    {

		private static readonly object LockObj = new object();
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IViagemRotaRepository _viagemRotaRepository;
        private readonly IFornecedorCnpjPedagioRepository _fornecedorCnpjPedagioRepository;
        private readonly IEmpresaService _empresaService;
        private readonly IVeiculoRepository _veiculoRepository;
        private readonly IClienteService _clienteService;
        private readonly IMotoristaRepository _motoristaRepository;
        private readonly ICiotV2Service _ciotV2Service;
        private readonly ICiotV3Service _ciotV3Service;
        private readonly ICadastrosService _cadastrosService;
        private readonly IParametrosService _parametrosService;
        private readonly ICargaAvulsaService _cargaAvulsaService;
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly IFilialRepository _filialRepository;
        private readonly IContratoCiotAgregadoRepository _ciotAgregadoRepository;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IMotoristaService _motoristaService;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IUsuarioService _usuarioService;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IPagamentoFreteService _pagamentoFreteService;
        private readonly IEstabelecimentoService _estabelecimentoService;
        private readonly IVeiculoService _veiculoService;
        private readonly IViagemDocumentoRepository _viagemDocumentoRepository;
        private readonly IProtocoloEventoRepository _protocoloEventoRepository;
        private readonly IProtocoloAntecipacaoRepository _protocoloAntecipacaoRepository;
        private readonly IViagemCarretaRepository _viagemCarretaRepository;
        private readonly IViagemEstabelecimentoRepository _viagemEstabelecimentoRepository;
        private readonly IBloqueioGestorValorService _bloqueioGestorValorService;
        private readonly IViagemPendenteGestorService _viagemPendenteGestorService;
        private readonly IViagemDapper _viagemDapper;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        private readonly IViagemRepository _viagemRepository;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IRotaModeloService _rotaModeloService;

        public ViagemService(ICargaAvulsaService cargaAvulsaService,IViagemRepository repository, IUserIdentity sessionUser, IViagemEventoRepository viagemEventoRepository,
            IVeiculoRepository veiculoRepository, IClienteService clienteService, IMotoristaRepository motoristaRepository,
            IViagemRotaRepository viagemRotaRepository, ICadastrosService cadastrosService, ICiotV2Service ciotV2Service, ICiotV3Service ciotV3Service, IParametrosService parametrosService,
            IFornecedorCnpjPedagioRepository fornecedorCnpjPedagioRepository,IDeclaracaoCiotRepository declaracaoCiotRepository, IFilialRepository filialRepository,
            IContratoCiotAgregadoRepository ciotAgregadoRepository, IVersaoAnttLazyLoadService versaoAntt, IMotoristaService motoristaService, IProprietarioRepository proprietarioRepository,
            IEmpresaRepository empresaRepository, IUsuarioService usuarioService, IPagamentoFreteService pagamentoFreteService, IEstabelecimentoService estabelecimentoService,
            IVeiculoService veiculoService, IViagemDocumentoRepository viagemDocumentoRepository, IProtocoloEventoRepository protocoloEventoRepository,
            IProtocoloAntecipacaoRepository protocoloAntecipacaoRepository, IViagemCarretaRepository viagemCarretaRepository,
            IViagemEstabelecimentoRepository viagemEstabelecimentoRepository, IBloqueioGestorValorService bloqueioGestorValorService, IViagemPendenteGestorService viagemPendenteGestorService,
            IViagemDapper viagemDapper, CartoesServiceArgs cartoesServiceArgs, IViagemRepository viagemRepository, IUsuarioRepository usuarioRepository, IParametrosEmpresaService parametrosEmpresaService, IEmpresaService empresaService, IRotaModeloService rotaModeloService) : base(repository, sessionUser)
        {
            _viagemEventoRepository = viagemEventoRepository;
            _veiculoRepository = veiculoRepository;
            _clienteService = clienteService;
            _motoristaRepository = motoristaRepository;
            _viagemRotaRepository = viagemRotaRepository;
            _cadastrosService = cadastrosService;
            _ciotV2Service = ciotV2Service;
            _ciotV3Service = ciotV3Service;
            _parametrosService = parametrosService;
            _fornecedorCnpjPedagioRepository = fornecedorCnpjPedagioRepository;
            _cargaAvulsaService = cargaAvulsaService;
            _declaracaoCiotRepository = declaracaoCiotRepository;
            _filialRepository = filialRepository;
            _ciotAgregadoRepository = ciotAgregadoRepository;
            _versaoAntt = versaoAntt;
            _motoristaService = motoristaService;
            _proprietarioRepository = proprietarioRepository;
            _empresaRepository = empresaRepository;
            _usuarioService = usuarioService;
            _pagamentoFreteService = pagamentoFreteService;
            _estabelecimentoService = estabelecimentoService;
            _veiculoService = veiculoService;
            _viagemDocumentoRepository = viagemDocumentoRepository;
            _protocoloEventoRepository = protocoloEventoRepository;
            _protocoloAntecipacaoRepository = protocoloAntecipacaoRepository;
            _viagemCarretaRepository = viagemCarretaRepository;
            _viagemEstabelecimentoRepository = viagemEstabelecimentoRepository;
            _bloqueioGestorValorService = bloqueioGestorValorService;
            _viagemPendenteGestorService = viagemPendenteGestorService;
            _viagemDapper = viagemDapper;
            _cartoesServiceArgs = cartoesServiceArgs;
            _viagemRepository = viagemRepository;
            _usuarioRepository = usuarioRepository;
            _parametrosEmpresaService = parametrosEmpresaService;
            _empresaService = empresaService;
            _rotaModeloService = rotaModeloService;
        }

        /// <summary>
        /// Validar as informações para os determinados processos
        /// </summary>
        /// <param name="viagem">Entidade de usuário</param>
        /// <param name="processo">Processo que esta sendo validado</param>
        /// <returns></returns>
        public ValidationResult IsValidToCrudViagem(Viagem viagem, EProcesso processo)
        {
            ValidationResult validationResult = new ValidationResult();
            var clienteService = _clienteService;

            validationResult.Add(AssertionConcern.AssertArgumentIsValidCPF(viagem.CPFMotorista, "CPF do motorista informado é inválido."));

            validationResult.Add(AssertionConcern.AssertArgumentIsValidPlaca(viagem.Placa, "Placa deve possuir um valor válido"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(viagem.Placa, "Placa deve possuir um valor válido"));

            // Validar o código do cliente de origem
            var clienteValido = clienteService.ClienteValidoIntegracao(viagem.IdClienteOrigem, viagem.IdEmpresa);
            if (!clienteValido)
                validationResult.Add($"Cliente de origem não consta na base de Clientes do Empresa.");

            // Validar o código do cliente de destino
            clienteValido = clienteService.ClienteValidoIntegracao(viagem.IdClienteDestino, viagem.IdEmpresa);
            if (!clienteValido)
                validationResult.Add($"Cliente de origem não consta na base de Clientes da Empresa.");

                //Valida documentos complementares
            if (viagem.IdViagemComplementada != null && string.IsNullOrEmpty(viagem.NumeroDocumentoComplementado))
                validationResult.Add($"Número do documento complementado não foi informado.");

            // Data de previsão e coleta
            if (viagem.DataColeta.HasValue && viagem.DataColeta.Value > viagem.DataPrevisaoEntrega)
                validationResult.Add($"Data de coleta para a viagem não pode ser superior a data de previsão de entrega");

            if (viagem.Produto?.Length > 100)
            {
                //Todo: validar

                viagem.Produto = viagem.Produto.Substring(0, 100);
            }

            if (viagem.ViagemPagamentoConta != null && !string.IsNullOrWhiteSpace(viagem.ViagemPagamentoConta.CodigoBacenBanco))
            {
                if(!_cadastrosService.Bancos().Objeto.Any(x => x.Id == viagem.ViagemPagamentoConta.CodigoBacenBanco))
                    validationResult.Add($"Código Bacen do banco é inválido");
            }

            // Irá verificar se o motorista possui o CPF cadastrado a algum Empresa.
            // Caso tenha, verifica se a empresa é diferente da que esta realizando a viagem.
            // Sendo, irá bloquear, pois o motorista dentro do sistema faz parte de outro Empresa
            var motorista = _motoristaService.GetPorCpfQueryable(viagem.CPFMotorista).Select(c => new
            {
                c.IdEmpresa,
                c.Ativo,
                c.CPF,
                c.CNH,
                c.Nome
            }).FirstOrDefault();

            // Retirado pois em uma viagem com frete terceirizado a validacao nao pode ocorrer, e em nenhuma outra situacao ela ocorrerá
            //if (motorista?.IdEmpresa != null && motorista.IdEmpresa.Value != viagem.IdEmpresa && motorista.Ativo)
            //    validationResult.Add($"Motorista indicado para a viagem consta como ativo para outra empresa.");

            // Bloqueando veículo não cadastrado no ATS para integrações do meio homologado
            if (viagem.HabilitarDeclaracaoCiot || (viagem.ViagemEventos != null && viagem.ViagemEventos.Any(ve => ve.HabilitarPagamentoCartao)))
            {
                if (viagem.MensagemDeclaracaoCiot?.Length > 500)
                    viagem.MensagemDeclaracaoCiot = viagem.MensagemDeclaracaoCiot.Substring(0, 500);

                var veiculoRepository = _veiculoRepository;
                var proprietarioRepository = _proprietarioRepository;

                var veiculo = veiculoRepository
                    .Include(v => v.Proprietario).Select(c => new
                    {
                        c.Placa,
                        c.IdEmpresa,
                        c.Proprietario,
                        c.Proprietario.CNPJCPF,
                        c.Proprietario.NomeFantasia
                    })
                    .FirstOrDefault(v => v.Placa == viagem.Placa && (v.IdEmpresa == null || v.IdEmpresa == viagem.IdEmpresa));
                if (veiculo == null)
                    validationResult.Add($"Placa {viagem.Placa} não integrada para empresa.");
                else
                {
                    if (viagem.CPFCNPJProprietario.IsNullOrWhiteSpace() &&
                        (veiculo.Proprietario == null || veiculo.Proprietario.CNPJCPF.IsNullOrWhiteSpace()))
                        validationResult.Add("CPF/CNPJ do proprietário não informado ou não definido no veículo");

                    if (viagem.NomeProprietario.IsNullOrWhiteSpace())
                    {
                        var proprietarioNome = proprietarioRepository.Where(c => c.CNPJCPF == viagem.CPFCNPJProprietario).Select(c => c.RazaoSocial).FirstOrDefault();

                        if (string.IsNullOrWhiteSpace(proprietarioNome))
                            validationResult.Add("Nome do proprietário não informado ou não definido no veículo");
                        else
                        {
                            viagem.NomeProprietario = proprietarioNome;
                        }
                    }

                    /*if (!viagem.IdProprietario.HasValue)
                    {
                        validationResult.Add("O proprietário da viagem não está cadastrado para esta empresa.");
                    }*/

                    // RNTRC só deve ser validado em caso em que houver CIOT na viagem
                    if (viagem.HabilitarDeclaracaoCiot)
                    {
                        if (viagem.RNTRC.GetValueOrDefault(0) == 0 &&
                            (veiculo.Proprietario == null || veiculo.Proprietario.RNTRC.IsNullOrWhiteSpace()))
                            validationResult.Add("RNTRC do proprietário não informado ou não definido no veículo");
                    }
                }

            }

            if (processo == EProcesso.Create)
            {
                // No processo de integrar a viagem, o status não deve ser informado.
                if (viagem.ViagemChecks != null && viagem.ViagemChecks.Any())
                    validationResult.Add($"Status não deve ser informado no processo de integração da viagem.");

                var empresaValidaPagamentoFrete = _empresaRepository.EmpresaValidaPagamentoFrete(viagem.IdEmpresa);
                if (empresaValidaPagamentoFrete)
                {
                    //Valida motorista existente
                    var usuario = _usuarioService.GetPorCnpjcpfQueryable(viagem.CPFMotorista).Select(c => new
                    {
                        c.CPFCNPJ,
                        c.CNH,
                        c.Nome
                    }).FirstOrDefault();

                    if (motorista != null)
                    {
                        viagem.CPFMotorista = motorista.CPF;
                        viagem.CNHMotorista = motorista.CNH;
                        viagem.NomeMotorista = motorista.Nome;
                    }
                    else
                    {
                        if (usuario != null)
                        {
                            viagem.CPFMotorista = usuario.CPFCNPJ;
                            viagem.CNHMotorista = usuario.CNH;
                            viagem.NomeMotorista = usuario.Nome;
                        }
                        else
                            validationResult.Add("Motorista informado não está cadastrado.");
                    }

                    if (!viagem.PesoSaida.HasValue)
                        validationResult.Add("Peso de saída não informado.");
                    if (!viagem.ValorMercadoria.HasValue)
                        validationResult.Add("Valor da mercadoria não informado.");

                    if (viagem.ViagemRegras?.Any() == true)
                    {
                        foreach (var regra in viagem.ViagemRegras)
                        {
                            if (!regra.TaxaAntecipacao.HasValue)
                                validationResult.Add("Taxa de antecipação não informada.");
                            if (!regra.ToleranciaPeso.HasValue)
                                validationResult.Add("Tolerância de peso não informada.");
                            if (!regra.TarifaTonelada.HasValue)
                                validationResult.Add("Tarifa por tonelada não informada.");
                        }
                    }
                    else if (viagem.ViagemEventos != null && viagem.ViagemEventos.Any() &&
                             (viagem.ViagemRegras == null || !viagem.ViagemRegras.Any()))
                    {
                        // Viagem com informações de pagamento, obriga a ter regras, pois o pagamento do saldo no posto depende destes parâmetros
                        validationResult.Add("Regra da viagem não informada.");
                    }

                    if (viagem.ViagemEstabelecimentos?.Any() == true)
                    {
                        foreach (var estabelecimento in viagem.ViagemEstabelecimentos)
                        {
                            if (estabelecimento.IdEstabelecimento < 1)
                                validationResult.Add("Estabelecimento não informado.");
                        }
                    }

                    if (viagem.ViagemEventos?.Any() == true)
                    {
                        foreach (var eventos in viagem.ViagemEventos)
                        {
                            if (eventos.ValorPagamento <= 0)
                            {
                                var pagamentoService = _pagamentoFreteService;
                                var deveIncluirPedagioJuntoComPagamentoDoEvento = pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(eventos, viagem);

                                if (eventos.TipoEventoViagem != ETipoEventoViagem.Adiantamento || !deveIncluirPedagioJuntoComPagamentoDoEvento)
                                    validationResult.Add("Valor do pagamento não informado ou negativo.");
                            }

                            if (eventos.ViagemDocumentos?.Any() == true)
                            {
                                foreach (var documento in eventos.ViagemDocumentos)
                                {
                                    if (string.IsNullOrWhiteSpace(documento.Descricao))
                                        validationResult.Add("Descrição do documento não informada.");
                                }
                            }

                            if (eventos.ViagemValoresAdicionais?.Any() == true)
                            {
                                foreach (var valoresAdicionais in eventos.ViagemValoresAdicionais)
                                {
                                    if (valoresAdicionais.NumeroDocumento < 1)
                                        validationResult.Add("Número do documento não informado.");
                                    if (string.IsNullOrWhiteSpace(valoresAdicionais.Descricao))
                                        validationResult.Add("Descrição do valor não informado.");
                                    if (valoresAdicionais.Valor <= 0)
                                        validationResult.Add("Valor adicional não informado.");
                                }
                            }
                        }
                    }
                }

            }
            else if (processo == EProcesso.Update)
            {
                if (viagem.ViagemChecks?.Any() == true)
                    validationResult.AddRange(new ViagemCheckService().IsValid(viagem));

                if (viagem.ViagemEstabelecimentos?.Any() == true)
                {
                    foreach (var estabelecimento in viagem.ViagemEstabelecimentos)
                    {
                        if (estabelecimento.IdEstabelecimento < 1)
                            validationResult.Add("Estabelecimento não informado.");

                        var estabelecimentoEntity = _estabelecimentoService.Get(estabelecimento.IdEstabelecimento);
                        if (estabelecimentoEntity == null)
                            validationResult.Add("Estabelecimento não encontrado.");
                    }
                }
            }

            return validationResult;
        }

        public ValidationResult IsValidToCrudViagemPedagioAvulso(Viagem viagem)
        {
            var validationResult = new ValidationResult();

            validationResult.Add(AssertionConcern.AssertArgumentIsValidPlaca(viagem.Placa, "Placa deve possuir um valor válido"));
            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(viagem.Placa, "Placa deve possuir um valor válido"));

            // Data de previsão e coleta
            if (viagem.DataColeta.HasValue && viagem.DataColeta.Value > viagem.DataPrevisaoEntrega)
                validationResult.Add($"Data de coleta para a viagem não pode ser superior a data de previsão de entrega");

            return validationResult;
        }

        public object GetEventosViagem(string cpfCnpjProprietario, string cpfMotorista, List<EStatusViagemEvento> statusEvento = null, List<ETipoEventoViagem> tipoEvento = null, DateTime? dataInicio = null, DateTime? dataFim = null  )
        {

            if (dataInicio == null)
                dataInicio = new DateTimeHelper().StartOfDay(DateTime.Now.AddDays(-7));

            if (dataFim == null)
                dataFim = new DateTimeHelper().EndOfDay(DateTime.Now);

            var query = _viagemEventoRepository
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.CIOTs)
                .Where(x => x.Viagem.DataEmissao >= dataInicio && x.Viagem.DataEmissao <= dataFim);


            if (!string.IsNullOrEmpty(cpfCnpjProprietario))
                query = query.Where(x => x.Viagem.CPFCNPJProprietario == cpfCnpjProprietario);


            if (!string.IsNullOrEmpty(cpfMotorista))
                query = query.Where(x => x.Viagem.CPFMotorista == cpfMotorista);

            if (statusEvento != null)
                query = query.Where(x => statusEvento.Contains(x.Status));

            if (tipoEvento != null)
                query = query.Where(x => tipoEvento.Contains(x.TipoEventoViagem));

            return query.ToList().Select(x => new
            {
                DataEmissao = x.Viagem.DataEmissao != null ? x.Viagem.DataEmissao.Value.ToString("G") : "",
                IdEvento = x.IdViagemEvento,
                TipoEvento = x.TipoEventoViagem,
                StatusEvento = x.Status,
                ValorPagamento = x.ValorPagamento,
                ValorTotalPagamento = x.ValorTotalPagamento,
                DataPagamento = x.DataHoraPagamento != null ? x.DataHoraPagamento.Value.ToString("G") : "",
                HoraPagamento = x.DataHoraPagamento != null ? x.DataHoraPagamento.Value.ToShortTimeString() : "",
                NumeroRecibo = x.NumeroRecibo,
                CIOT = x.Viagem.CIOTs?.FirstOrDefault() != null? $"{x.Viagem.CIOTs?.FirstOrDefault()?.Ciot}/{x.Viagem.CIOTs?.FirstOrDefault()?.Verificador}" : "",
                DataRejeicaoAbono = x.DataHoraRejeicaoAbono != null ? x.DataHoraRejeicaoAbono.Value.ToString("G") : "",
                DetalhamentoAbono = x.DescricaoRejeicaoAbono
            }).ToList();

        }

        public object GetHistoricoViagem(string placa, int idEmpresa, int skip)
        {
            var viagens = Repository
                .Find(x => (x.Placa == placa || x.ViagemCarretas.Any(y => y.Placa == placa)) && x.IdEmpresa == idEmpresa)
                .Include(x => x.ViagemCarretas)
                .OrderByDescending(x => x.IdViagem)
                .Skip(skip);

            var retorno = new List<object>();
            foreach (var viagem in viagens)
            {
                var placas = new List<string> { viagem.Placa };
                placas.AddRange(viagem.ViagemCarretas.Select(x => x.Placa));
                retorno.Add(new
                {
                    viagem.IdViagem,
                    DataViagem = viagem.DataFinalizacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    StatusViagem = EnumHelpers.GetDescription(viagem.StatusViagem),
                    Placas = placas
                });

            }

            return retorno;
        }

        /// <summary>
        /// Formatar valores de campos e afins
        /// </summary>
        /// <param name="viagem"></param>
        private void FormatValues(Viagem viagem)
        {
            viagem.Placa = viagem.Placa.RemoveSpecialCharacters();

            if (!string.IsNullOrWhiteSpace(viagem.CPFMotorista))
                viagem.CPFMotorista = StringExtension.OnlyNumbers(viagem.CPFMotorista);
        }

        public Viagem GetComViagemEstabelecimentos(int id)
        {
            var query = Repository
                .Find(x => x.IdViagem == id)
                .Include(x => x.ViagemEstabelecimentos);

            return query.FirstOrDefault();
        }

        /// <summary>
        /// Retorna todas as viagens a partir do periodo de busca
        /// </summary>
        /// <param name="tipoBusca"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idFilial"></param>
        /// <param name="idTipoCavalo"></param>
        /// <param name="idTipoCarreta"></param>
        /// <returns></returns>
        public IEnumerable<Viagem> GetViagens(ETipoBuscaViagens tipoBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta)
        {
            DateTime dataInicio;
            DateTime dataFinal;


            if (tipoBusca == ETipoBuscaViagens.MesAtual)
            {
                dataInicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                dataFinal = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 23, 59, 59).AddMonths(1).AddDays(-1);
            }
            else
            {
                dataInicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).AddMonths(-1);
                dataFinal = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 23, 59, 59).AddDays(-1);
            }

            List<Viagem> viagens = Repository
                    .Find(p => p.DataPrevisaoEntrega >= dataInicio &&
                               p.DataPrevisaoEntrega <= dataFinal &&
                               p.IdEmpresa == idEmpresa)
                    .Include(x => x.ViagemCargas).ToList();

            List<Viagem> viagensRetorno = new List<Viagem>();
            viagensRetorno.AddRange(viagens);
            foreach (var viagem in viagens)
            {
                //Verifica se existe o veículo da viagem na tabela de veículos
                Veiculo veiculo = _veiculoService.GetVeiculoPorPlacaPorEmpresa(viagem.Placa, viagem.IdEmpresa);

                if (veiculo == null) continue;

                if ((idTipoCavalo.HasValue && veiculo.IdTipoCavalo != idTipoCavalo) ||
                    (idTipoCarreta.HasValue && veiculo.IdTipoCarreta != idTipoCarreta) ||
                    idFilial.HasValue && veiculo.IdFilial != idFilial)
                {
                    viagensRetorno.Remove(viagem);
                }
            }

            return viagensRetorno;
        }

        /// <summary>
        /// Retorna todas as viagens que ainda estão 'Em Viagem'
        /// </summary>
        /// <returns></returns>
        public IEnumerable<Viagem> GetViagensEmViagem(int idEmpresa)
        {
            List<Viagem> retViagens = new List<Viagem>();

            IQueryable<Viagem> viagens = Repository
                .Find(p => p.StatusViagem == EStatusViagem.Aberto || p.StatusViagem == EStatusViagem.Programada
                && p.IdEmpresa == idEmpresa
                )
                .Include(x => x.ViagemChecks);

            foreach (Viagem viagem in viagens)
            {
                ViagemCheck ultimoViagemCheck = viagem.ViagemChecks.Where(p => p.Status == EStatusCheckViagem.InicioViagem)
                    .OrderByDescending(p => p.DataHora).FirstOrDefault();

                if (ultimoViagemCheck != null)
                    retViagens.Add(viagem);
            }

            return retViagens;
        }

        /// <summary>
        /// Retorna o registro de viagem com todos os objetos encadeados
        /// </summary>
        /// <param name="id">ID do registro</param>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <returns></returns>
        public Viagem GetChilds(int id, int idEmpresa)
        {
            return Repository.GetChilds(id, idEmpresa);
        }

        /// <summary>
        /// Integrar os dados de uma check (situação) da viagem
        /// </summary>
        /// <param name="idViagem">Código da Viagem</param>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="check">Dados da check</param>
        /// <returns></returns>
        public ValidationResult IntegrarCheck(int idViagem, int idEmpresa, ViagemCheck check)
        {
            try
            {
                Viagem viagem = Repository.Get(idViagem, idEmpresa);
                if (viagem == null)
                    return new ValidationResult().Add($"Viagem não localizada.");

                // Inserir o registro da situação
                viagem.ViagemChecks.Add(new ViagemCheck
                {
                    DataHora = check.DataHora,
                    IdUsuario = check.IdUsuario,
                    Status = check.Status,
                    IdEmpresa = check.IdEmpresa
                });

                // Sendo o final da descarga, irá 'fechar' a viagem.
                if (check.Status == EStatusCheckViagem.FimDescarga)
                    viagem.StatusViagem = EStatusViagem.Baixada;

                ValidationResult validationResult = IsValidToCrudViagem(viagem, EProcesso.Update);
                if (!validationResult.IsValid)
                    return validationResult;

                viagem.DataAtualizacao = DateTime.Now;
                Repository.Update(viagem);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public string GerarTokenViagemEvento()
        {
            string token;

            lock (LockObj)
            {
                token = new DateTimeHelper().ConvertToUnix(DateTime.UtcNow).ToString(CultureInfo.InvariantCulture).Replace(",", "").Replace(".", "").PadLeft(16, '0');
                Thread.Sleep(1);
            }

            return token;
        }

        /// <summary>
        /// Adicionar o registro de viagem
        /// </summary>
        /// <param name="viagem"></param>
        /// <param name="isPedagioAvulso"></param>
        /// <returns></returns>
        public ValidationResult Add(Viagem viagem,bool isPedagioAvulso = false)
        {
            try
            {
                FormatValues(viagem);

                if (viagem.ViagemDocumentosFiscais != null && viagem.ViagemDocumentosFiscais.Any())
                    foreach (var item in viagem.ViagemDocumentosFiscais)
                    {
                        if (viagem.IdViagem == 0)
                            item.IdViagemDocumentoFiscal = 0;

                        item.IdEmpresa = viagem.IdEmpresa;
                        item.IdViagem = viagem.IdViagem;
                    }

                if (viagem.ViagemEstabelecimentos != null && viagem.ViagemEstabelecimentos.Any())
                    foreach (var viagemViagemEstabelecimento in viagem.ViagemEstabelecimentos)
                    {
                        viagemViagemEstabelecimento.IdEmpresa = viagem.IdEmpresa;
                    }

                if (viagem.ViagemPagamentoConta != null)
                    viagem.ViagemPagamentoConta.IdEmpresa = viagem.IdEmpresa;

                ValidationResult validationResult;

                if (isPedagioAvulso || viagem.IsPedagioAvulso)
                    validationResult = IsValidToCrudViagemPedagioAvulso(viagem);
                else
                    validationResult = IsValidToCrudViagem(viagem, EProcesso.Create);

                if (!validationResult.IsValid)
                    return validationResult;

                viagem.DataAtualizacao = DateTime.Now;
                Repository.Add(viagem);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public void AjustarProprietario(Viagem viagem)
        {
            ProprietarioViagemDto proprietario;

            //Prioridade deve ser sempre o proprietário informado na viagem, caso não seja informado pode buscar do veículo da viagem
            if (!string.IsNullOrWhiteSpace(viagem.CPFCNPJProprietario))
            {
                var proprietarioQuery = _proprietarioRepository.AllAtivos()
                    .Where(p => p.CNPJCPF == viagem.CPFCNPJProprietario && p.IdEmpresa == viagem.IdEmpresa);

                if (viagem.RNTRC > 0)
                {
                    var rntrcViagem = string.Concat('%', viagem.RNTRC.Value.ToString(), '%');
                    proprietarioQuery = proprietarioQuery.Where(p => SqlFunctions.PatIndex(rntrcViagem, p.RNTRC) > 0);
                }

                proprietario = proprietarioQuery.ProjectTo<ProprietarioViagemDto>().FirstOrDefault();
            }
            else
            {
                var proprietarioVeiculoQuery = _veiculoRepository
                    .Find(veiculo => veiculo.Ativo &&
                                     veiculo.Placa == viagem.Placa &&
                                     (veiculo.IdEmpresa == viagem.IdEmpresa || veiculo.IdEmpresa == null) &&
                                     veiculo.IdProprietario != null &&
                                     veiculo.Proprietario.IdEmpresa == viagem.IdEmpresa
                    );

                if (viagem.RNTRC > 0)
                {
                    var rntrcViagem = string.Concat('%', viagem.RNTRC.Value.ToString(), '%');
                    proprietarioVeiculoQuery = proprietarioVeiculoQuery.Where(veiculo => SqlFunctions.PatIndex(rntrcViagem, veiculo.Proprietario.RNTRC) > 0);
                }

                proprietario = proprietarioVeiculoQuery.OrderByDescending(veiculo => veiculo.IdEmpresa)
                    .ProjectTo<ProprietarioViagemDto>().FirstOrDefault();
            }

            if (proprietario == null)
                return;

            viagem.IdProprietario = proprietario.IdProprietario;

            if (string.IsNullOrWhiteSpace(viagem.CPFCNPJProprietario))
                viagem.CPFCNPJProprietario = proprietario.CNPJCPF;

            if (string.IsNullOrWhiteSpace(viagem.NomeProprietario))
                viagem.NomeProprietario = proprietario.NomeFantasia;

            if (!viagem.RNTRC.HasValue)
                viagem.RNTRC = proprietario.RNTRC.ToIntNullable();
        }

        public ValidationResult Update(Viagem viagem)
        {
            ValidationResult validationResult;

            if (viagem.IsPedagioAvulso)
                validationResult = IsValidToCrudViagemPedagioAvulso(viagem);
            else
                validationResult = IsValidToCrudViagem(viagem, EProcesso.Update);

            if (!validationResult.IsValid)
                return validationResult;

            var eventos = viagem.ViagemEventos;
            List<ViagemEvento> viagemEventoAdd = new List<ViagemEvento>();

            if (eventos != null)
            {
                foreach (var evento in eventos)
                {
                    if (string.IsNullOrWhiteSpace(evento.Token))
                        evento.Token = GerarTokenViagemEvento();

                    viagemEventoAdd.Add(evento);
                }

                viagem.ViagemEventos = viagemEventoAdd;

                var eventosStatusBaixarViagem = new List<EStatusViagemEvento>
                {
                    EStatusViagemEvento.Aberto,
                    EStatusViagemEvento.Bloqueado,
                    EStatusViagemEvento.Agendado
                };

                if (viagem.ViagemEventos.Any() && viagem.StatusViagem != EStatusViagem.Cancelada && viagem.StatusViagem != EStatusViagem.Baixada)
                {
                    var manterViagemAbertaAposCancelamento = _parametrosEmpresaService.GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(viagem.IdEmpresa) ?? false;
                    var manterViagemAbertaAposBaixa = _parametrosEmpresaService.GetMantemViagemAbertaAposBaixaDoUltimoEvento(viagem.IdEmpresa);
                    if (viagem.ViagemEventos.All(o => o.Status == EStatusViagemEvento.Cancelado) && !manterViagemAbertaAposCancelamento)
                        viagem.StatusViagem = EStatusViagem.Cancelada;

                    else if (viagem.ViagemEventos.All(t => !eventosStatusBaixarViagem.Contains(t.Status)) && !manterViagemAbertaAposBaixa)
                        viagem.StatusViagem = EStatusViagem.Baixada;
                }
            }

            viagem.DataAtualizacao = DateTime.Now;

            //Verficação se essa viagem foi cancelada após um desvinculo de CIOT
            var desvinculo = _declaracaoCiotRepository.GetByCiotDesvinculo(viagem.IdViagem);

            if (desvinculo != null && viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                //Revinculação
                viagem.DeclaracaoCiot = desvinculo;
                desvinculo.ViagemOrigem = viagem;

                _declaracaoCiotRepository.Update(desvinculo);
            }

            Repository.Update(viagem);

            return validationResult;
        }

        /// <summary>
        /// Pega a ultima viagemCheck de uma viagem
        /// </summary>
        /// <param name="idViagem"></param>
        /// <returns>Entidade ViagemCheck</returns>
        public ViagemCheck GetUltimoViagemCheck(int idViagem)
        {
            Viagem viagem = Repository
                .Find(x => x.IdViagem == idViagem)
                .Include(x => x.ViagemChecks).FirstOrDefault();

            if (viagem != null &&
                viagem.ViagemChecks != null &&
                viagem.ViagemChecks.Count > 0)
                return viagem.ViagemChecks.OrderByDescending(x => x.DataHora).FirstOrDefault();
            return null;
        }

        /// <summary>
        /// Retorna a ultima viagem aberta de determinada placa
        /// </summary>
        /// <param name="placa"></param>
        /// <returns>Viagem</returns>
        public Viagem GetViagemAbertaPlaca(string placa)
        {
            Viagem viagem = Repository
                .Find(x => x.Placa == placa && x.StatusViagem == EStatusViagem.Aberto)
                .Include(x => x.ClienteDestino)
                .Include(x => x.ClienteDestino.Estado)
                .OrderByDescending(x => x.IdViagem).FirstOrDefault();
            return viagem;
        }

        public List<Viagem> GetViagemAbertaPlacas(List<string> placas)
        {
            var viagem = Repository
                .Find(x => placas.Contains(x.Placa) && x.StatusViagem == EStatusViagem.Aberto)
                .Include(x => x.ClienteDestino)
                .Include(x => x.ClienteDestino.Estado)
                .OrderByDescending(x => x.IdViagem).ToList();
            return viagem;
        }

        public List<Viagem> GetViagensAbertasPorPlaca(List<string> placas)
        {
            return Repository
                .Find(x => placas.Contains(x.Placa) && x.StatusViagem == EStatusViagem.Aberto)
                .Include(x => x.ClienteDestino)
                .Include(x => x.ClienteDestino.Estado)
                .ToList();
        }

        /// <summary>
        /// Retorna a ultima viagem fechada de determinada placa
        /// </summary>
        /// <param name="placa"></param>
        /// <returns></returns>
        public Viagem GetUltimaViagemFechadaPlaca(string placa)
        {
            Viagem viagem = Repository
                .Find(x => x.Placa == placa && x.StatusViagem == EStatusViagem.Baixada)
                .Include(x => x.ClienteDestino)
                .Include(x => x.ClienteDestino.Estado)
                .OrderByDescending(x => x.IdViagem).FirstOrDefault();
            return viagem;
        }

        public List<Viagem> GetUltimaViagemFechadaPlacas(List<string> placas)
        {
            var viagem = Repository
                .Find(x => placas.Contains(x.Placa) && x.StatusViagem == EStatusViagem.Baixada)
                .Include(x => x.ClienteDestino)
                .Include(x => x.ClienteDestino.Estado)
                .OrderByDescending(x => x.IdViagem).ToList();
            return viagem;
        }

        /// <summary>
        /// Retorna viagens finalizadas dentro de um periodo de busca
        /// </summary>
        /// <param name="tipoBusca">Tipo de busca para indicar o período</param>
        /// <param name="idEmpresa">ID do empresa</param>
        /// <param name="idFilial">Filtro ID da filial</param>
        /// <param name="idTipoCavalo">Filtro ID do tipo veículo</param>
        /// <param name="idTipoCarreta">Filtro ID tipo carreta</param>
        /// <returns></returns>
        public IEnumerable<Viagem> GetViagensFinalizadasEntre(ETipoBuscaViagens tipoBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta)
        {
            try
            {
                var dataInicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var dataFinal = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);

                #region Define período de busca

                switch (tipoBusca)
                {
                    case ETipoBuscaViagens.FimViagemHoje:
                        {
                            dataInicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);
                            dataFinal = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
                        }
                        break;
                    case ETipoBuscaViagens.FimViagemDoisASeteDias:
                        {
                            dataInicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day).AddDays(-6);
                            dataFinal =
                                new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59).AddDays(-1);
                        }
                        break;
                    case ETipoBuscaViagens.FimViagemSeteDiasMais:
                        {
                            dataInicio =
                                new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59).AddDays(-7);
                        }
                        break;
                }

                #endregion

                List<Viagem> viagens = new List<Viagem>();

                if (tipoBusca == ETipoBuscaViagens.FimViagemSeteDiasMais)
                    viagens = Repository
                        .Find(p => p.DataPrevisaoEntrega <= dataInicio &&
                                   (p.IdEmpresa > 0 && p.IdEmpresa == idEmpresa)
                                   && p.StatusViagem == EStatusViagem.Baixada)
                        .Include(x => x.ViagemCargas)
                        .Include(x => x.ViagemChecks)
                        .ToList();
                else
                    viagens = Repository
                        .Find(p => p.DataPrevisaoEntrega >= dataInicio && p.DataPrevisaoEntrega <= dataFinal &&
                                   (p.IdEmpresa > 0 && p.IdEmpresa == idEmpresa) &&
                                   p.StatusViagem == EStatusViagem.Baixada)
                        .Include(x => x.ViagemCargas)
                        .Include(x => x.ViagemChecks)
                        .ToList();

//                 Filtro desabilitado por algum motivo
//                 #region Filtro de tipo veículo e tipo carreta
//
//                 foreach (var viagem in viagens)
//                 {
//                     Veiculo veiculo = null; // _veiculoService.Get(viagem.Placa, viagem.IdEmpresa); <--- Deve passar IdVeiculo
//
//                     if (veiculo != null)
//                     {
//                         if ((idTipoCavalo.HasValue && veiculo.IdTipoCavalo != idTipoCavalo) ||
//                             (idTipoCarreta.HasValue && veiculo.IdTipoCarreta != idTipoCarreta) ||
//                             (idFilial.HasValue && veiculo.IdFilial != idFilial))
//                             viagens.Remove(viagem);
//                     }
//                     else
//                     {
//                         /*UsuarioVeiculo veiculoUsuario =
//                             _usuarioVeiculoRepository.GetPorPlaca(viagem.Placa);
//                         if (veiculoUsuario != null)
//                         {
//                             if ((idTipoCavalo.HasValue && veiculoUsuario.IdTipoCavalo != idTipoCavalo) ||
//                                 (idTipoCarreta.HasValue && veiculoUsuario.IdTipoCarreta != idTipoCarreta))
//
//                                 viagens.Remove(viagem);
//                         }*/
//                     }
//                 }
//
//                 #endregion

                return viagens;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        /// <summary>
        /// Retorna as viagens segundo os parâmetros especificados
        /// </summary>
        public IQueryable<Viagem> Consultar(string cpfMotorista, string tokenViagem, DateTime? dataLancamentoInicial, DateTime? dataLancamentoFinal, List<EStatusCheckViagem> statusCheckViagem, List<EStatusViagem> statusViagem, string token, string cnpjAplicacao, List<int> idsViagem,List<string> numerosControle)
        {
            var idEmpresa = _empresaRepository.GetIdPorCnpj(cnpjAplicacao);
            var permiteConsultarSemFiltro = _parametrosService.GetPermiteConsultarTodasViagensEmpresa(idEmpresa??0);

            return Repository.Consultar(cpfMotorista, tokenViagem, dataLancamentoInicial, dataLancamentoFinal, statusCheckViagem, statusViagem, token, cnpjAplicacao, idsViagem, permiteConsultarSemFiltro,numerosControle);
        }

        /// <summary>
        /// Retorna todas viagens em viagem de um determinado CPF
        /// </summary>
        public IEnumerable<Viagem> GetViagensEmViagemPorCPF(string cpfMotorista)
        {
            return Repository
                .Find(p => p.CPFMotorista == cpfMotorista &&
                            p.StatusViagem == EStatusViagem.Aberto)
                .Include(p => p.ViagemChecks);
        }

        /// <summary>
        /// Retorna a ultima viagem realizada pela placa
        /// </summary>
        /// <param name="placa"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public Viagem GetUltimaViagemPorPlaca(string placa, int idEmpresa)
        {
            return Repository
                .Find(x => x.IdEmpresa == idEmpresa && (x.Placa == placa || x.ViagemCarretas.Any(y => y.Placa == placa)))
                .Include(x => x.ViagemCarretas)
                .OrderByDescending(x => x.IdViagem)
                .FirstOrDefault();
        }

        /// <summary>
        /// Retorna a ultima viagem realizada pela placa
        /// </summary>
        /// <param name="placa"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public List<FrotaUtilizadaModel> GetUltimaViagemFrota(List<string> placa, int idEmpresa)
        {
            return Repository
                .Find(x => x.IdEmpresa == idEmpresa && (placa.Contains(x.Placa) || x.ViagemCarretas.Any(y => placa.Contains(y.Placa))))
                .Include(x => x.ViagemCarretas)
                .OrderByDescending(x => x.IdViagem)
                .Select(x => new FrotaUtilizadaModel
                {
                    IdViagem = x.IdViagem,
                    ViagemCarretas = x.ViagemCarretas.ToList(),
                    Placa = x.Placa
                })
                .ToList();
        }

        public List<string> GetPlacas(int idEmpresa)
        {
            var retorno = Repository
                .Find(x => x.IdEmpresa == idEmpresa)
                .OrderByDescending(x => x.IdViagem)
                .GroupBy(x => x.Placa)
                .Select(x => x.FirstOrDefault())
                .Select(x => x.Placa)
                .ToList();

            return retorno;
        }

        public List<PlacasViagemModel> GetPlacasComCarretas(int idEmpresa)
        {
            var retorno = Repository
                .Find(x => x.IdEmpresa == idEmpresa)
                .Include(x => x.ViagemCarretas)
                .ToList()
                .GroupBy(x => x.Placa)
                .Select(x => x.LastOrDefault())
                .Select(x => new PlacasViagemModel
                {
                    Placa = x.Placa,
                    TotalCarretas = x.ViagemCarretas.Count
                }).ToList();

            return retorno;
        }

        public List<Viagem> GetViagensPorPeriodo(int idEmpresa, DateTime dataInicial, DateTime dataFinal)
        {
            return Repository
                .Find(x => x.IdEmpresa == idEmpresa && x.DataColeta > dataInicial && x.DataColeta < dataFinal)
                .Include(x => x.ViagemCarretas)
                .ToList();
        }

        public object ConsultarDocumentos(int idViagemEvento)
        {
            return _viagemDocumentoRepository
                .Find(x => x.IdViagemEvento == idViagemEvento).Select(x => new
                {
                    Token = x.TokenAnexo,
                    Documento = x.Descricao,
                    IdViagemDocumento = x.IdViagemDocumento,
                    x.ObrigaAnexo,
                    x.ObrigaDocOriginal
                });
        }

        public bool HasAssociacao(int? idViagemEvento)
        {

            if (!idViagemEvento.HasValue)
                return false;

            return _protocoloEventoRepository
              .Where(x => x.IdViagemEvento == idViagemEvento && x.Protocolo.IdEstabelecimentoDestinatario != null)
              .Include(x => x.Protocolo)
              .OrderByDescending(x => x.IdProtocoloEvento)
              .Any();

        }

        public object GetCartasPagas(int? idEmpresa, int ano, int mes)
        {
            var date = DateTime.Parse(ano.ToString() + "-" + mes.ToString() + "-1 00:00:00");
            var dateBegin = new DateTime(date.Year, date.Month, 1);
            var dateEnd = dateBegin.AddMonths(1).AddDays(-1).AddHours(23).AddMinutes(59).AddSeconds(59);

            var cartas = _viagemEventoRepository.Find(x => x.Viagem.DataLancamento >= dateBegin
                && x.Viagem.DataLancamento <= dateEnd
                && x.Status == EStatusViagemEvento.Baixado);

            if (idEmpresa.HasValue)
            {
                cartas = cartas.Where(x => x.IdEmpresa == idEmpresa);
            }

            return new
            {
                reais = cartas.Sum(x => x.ValorTotalPagamento) ?? 0
            };
        }

        public object GetCartasEmAberto(int? idEmpresa, int ano, int mes)
        {
            var date = DateTime.Parse(ano.ToString() + "-" + mes.ToString() + "-1 00:00:00");
            var dateBegin = new DateTime(date.Year, date.Month, 1);
            var dateEnd = dateBegin.AddMonths(1).AddDays(-1).AddHours(23).AddMinutes(59).AddSeconds(59);

            var cartas = _viagemEventoRepository.Find(x => x.Viagem.DataLancamento >= dateBegin
               && x.Viagem.DataLancamento <= dateEnd
               && (x.Status == EStatusViagemEvento.Aberto || x.Status == EStatusViagemEvento.Bloqueado))
               .Include(x => x.Viagem);


            if (idEmpresa.HasValue)
            {
                cartas = cartas.Where(x => x.IdEmpresa == idEmpresa);
            }

            return new
            {
                reais = cartas.Any() ? cartas.Sum(x => x.ValorPagamento) : 0,
            };
        }

        public object GetTotalDeCartas(int? idEmpresa, int ano, int mes)
        {
            var date = DateTime.Parse(ano.ToString() + "-" + mes.ToString() + "-1 00:00:00");
            var dateBegin = new DateTime(date.Year, date.Month, 1);
            var dateEnd = dateBegin.AddMonths(1).AddDays(-1);

            var cartas = _viagemEventoRepository.Find(x => x.Viagem.DataLancamento >= dateBegin
                && x.Status != EStatusViagemEvento.Cancelado
                && x.Viagem.DataLancamento <= dateEnd);

            if (idEmpresa.HasValue)
            {
                cartas = cartas.Where(x => x.IdEmpresa == idEmpresa);
            }

            return new
            {
                reais = cartas.Any() ? cartas.Sum(x => x.ValorTotalPagamento) : 0,
                porcento = 0
            };
        }

        public string GetMonthDescription(int month)
        {
            switch (month)
            {
                case 1:
                    return "Jan";
                case 2:
                    return "Fev";
                case 3:
                    return "Mar";
                case 4:
                    return "Abr";
                case 5:
                    return "Mai";
                case 6:
                    return "Jun";
                case 7:
                    return "Jul";
                case 8:
                    return "Ago";
                case 9:
                    return "Set";
                case 10:
                    return "Out";
                case 11:
                    return "Nov";
                case 12:
                    return "Dez";
            }

            return "";
        }

        public object GetTotalCartasMes(int? idEmpresa, int ano)
        {
            var labels = new List<Object>();
            var data = new List<Object>();

            for (var i = 1; i <= 12; i++)
            {

                var date = DateTime.Parse(ano.ToString() + "-" + i.ToString() + "-1 00:00:00");
                var dateBegin = new DateTime(date.Year, date.Month, 1);
                var dateEnd = dateBegin.AddMonths(1).AddDays(-1).AddHours(23).AddMinutes(59).AddSeconds(59);

                var cartas = _viagemEventoRepository.Find(x => x.Viagem.DataLancamento >= dateBegin
                    && x.Viagem.DataLancamento <= dateEnd
                    && x.Status != EStatusViagemEvento.Cancelado)
                    .Include(x => x.Viagem);


                if (idEmpresa.HasValue)
                {
                    cartas = cartas.Where(x => x.IdEmpresa == idEmpresa);
                }

                labels.Add(GetMonthDescription(i));
                data.Add(cartas.Count());
            }

            return new
            {
                labels,
                data
            };
        }

        public object GetTotalGanhosPorMes(int? idEmpresa, int ano)
        {
            var antecipacaoRepository = _protocoloAntecipacaoRepository;
            var labels = new List<Object>();
            var data = new List<Object>();

            for (var i = 1; i <= 12; i++)
            {

                var date = DateTime.Parse(ano + "-" + i + "-1 00:00:00");
                var dateBegin = new DateTime(date.Year, date.Month, 1);
                var dateEnd = dateBegin.AddMonths(1).AddDays(-1);

                var antecipacoes = antecipacaoRepository.Find(x => x.Protocolo.DataGeracao >= dateBegin
                    && x.Protocolo.DataGeracao <= dateEnd
                    && x.Status == EStatusProtocoloAntecipacao.Aprovada)
                    .Include(x => x.Protocolo);

                labels.Add(GetMonthDescription(i));

                if (idEmpresa.HasValue)
                {
                    antecipacoes = antecipacoes.Where(x => x.Protocolo.IdEmpresa == idEmpresa);
                }

                var dados = antecipacoes.ToList();
                Decimal total = 0;

                foreach (var item in dados)
                {
                    total += (item.Protocolo.ValorProtocolo - item.ValorPagamentoAntecipado);
                }

                data.Add(total);
            }

            return new
            {
                labels,
                data
            };
        }

        public object GetTotalPagamentos(int? idEmpresa, int ano, int mes)
        {
            var date = DateTime.Parse(ano + "-" + mes + "-1 00:00:00");
            var dateBegin = new DateTime(date.Year, date.Month, 1);
            var dateEnd = dateBegin.AddMonths(1).AddDays(-1).AddHours(23).AddMinutes(59).AddSeconds(59);

            var cartas = _viagemEventoRepository.Find(x => x.Viagem.DataLancamento >= dateBegin
                && x.Viagem.DataLancamento <= dateEnd
                && x.Status != EStatusViagemEvento.Cancelado);

            if (idEmpresa.HasValue)
            {
                cartas = cartas.Where(x => x.IdEmpresa == idEmpresa);
            }

            decimal reais = 0;
            foreach (var item in cartas)
            {
                if (item.ValorTotalPagamento.HasValue)
                    reais += item.ValorTotalPagamento.Value;
                else
                    reais += item.ValorPagamento;
            }

            return new
            {
                reais,
            };
        }

        public ValidationResult RemoverPlacasCarretas(int idViagem, List<int> idViagemCarretaRemovidaList = null)
        {
            try
            {
                var viagemCarretaRepo = _viagemCarretaRepository;
                var viagemCarretas = viagemCarretaRepo
                    .Find(x => x.IdViagem == idViagem);
                foreach (var carreta in viagemCarretas)
                {
                    if (idViagemCarretaRemovidaList != null && !idViagemCarretaRemovidaList.Contains(carreta.IdViagemCarreta))
                        continue;

                    viagemCarretaRepo.Delete(carreta);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return e.InnerException != null
                    ? new ValidationResult().Add(e.Message + " / " + e.InnerException.Message)
                    : new ValidationResult().Add(e.Message);
            }
        }

        public void RemoverViagemEstabelecimentos(int idViagem, List<int> idViagemEstabelecimentoRemovidoList = null)
        {
            var viagemEstabelecimentoRepository = _viagemEstabelecimentoRepository;
            var viagemEstabelecimentos = viagemEstabelecimentoRepository.Find(x => x.IdViagem == idViagem);
            foreach (var estabelecimento in viagemEstabelecimentos)
            {
                if (idViagemEstabelecimentoRemovidoList != null && !idViagemEstabelecimentoRemovidoList.Contains(estabelecimento.IdViagemEstabelecimento))
                    continue;

                viagemEstabelecimentoRepository.Delete(estabelecimento);
            }
        }

        public bool ViagemCadastrada(int idEmpresa, string numeroControle)
        {
            return Repository.FirstOrDefault(o => o.NumeroControle == numeroControle && o.IdEmpresa == idEmpresa) != null;
        }

        public ValidationResult BloqueioProcessoViagem(List<EBloqueioGestorTipo> idsTipoBloqueio, Viagem viagem, decimal valorPedagioViagem, EBloqueioOrigemTipo origem, decimal valorTotalFrete)
        {
            var datainicial = DateTime.Now.StartOfDay();
            var dataFinal = DateTime.Now.EndOfDay();

            var validation = new ValidationResult();

            var queryBase = Repository.Where(c => c.DataLancamento > datainicial &&
                                                  c.DataLancamento < dataFinal &&
                                                  c.IdEmpresa == viagem.IdEmpresa &&
                                                  c.IdViagem != viagem.IdViagem);

            foreach (var eBloqueioGestorTipo in idsTipoBloqueio)
            {
                var validationPerTipo = new ValidationResult();
                var valorConfiguradoFilial = _bloqueioGestorValorService.ValorLimiteConfiguradoFilial(eBloqueioGestorTipo, viagem.IdEmpresa, viagem.IdFilial, origem) ?? 0;
                var valorConfiguradoEmpresa = _bloqueioGestorValorService.ValorLimiteConfiguradoEmpresa(eBloqueioGestorTipo, viagem.IdEmpresa, origem) ?? 0;

                var myPendence = _viagemPendenteGestorService
                    .GetQuery()
                    .FirstOrDefault(c => c.IdViagem == viagem.IdViagem && c.IdBloqueioGestorTipo == (int)eBloqueioGestorTipo);

                if (myPendence?.Status == (int)EBloqueioGestorStatus.Liberado)
                    continue;

                switch (eBloqueioGestorTipo)
                {
                    case EBloqueioGestorTipo.ValorLimiteFreteDiario:

                        var queryValorDiarioFrete = queryBase.Include(c => c.ViagemEventos);
        

                        if (viagem.IdFilial != null)
                            queryValorDiarioFrete.Where(x => x.IdFilial == viagem.IdFilial);

                        var valorLimiteDiarioFrete = queryValorDiarioFrete
                            .Select(c => new
                            {
                                ValorViagemEventos = c.ViagemEventos.Where(a => a.Status == EStatusViagemEvento.Baixado && a.HabilitarPagamentoCartao)
                                        .Select(a => a.ValorTotalPagamento ?? a.ValorPagamento)
                                        .DefaultIfEmpty(0)
                                        .Sum()
                            })
                            .Select(c => c.ValorViagemEventos)
                            .DefaultIfEmpty(0)
                            .Sum();

                        if (valorConfiguradoEmpresa > 0 && valorLimiteDiarioFrete + valorTotalFrete > valorConfiguradoEmpresa)
                        {
                            var mensagem = $"Pagamento do valor de frete ({Math.Round(valorTotalFrete, 2):C}) excede o limite diário da empresa. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Frete");
                        }

                        if (valorConfiguradoFilial > 0 && valorLimiteDiarioFrete + valorTotalFrete > valorConfiguradoFilial)
                        {
                            var mensagem = $"Pagamento do valor de frete ({Math.Round(valorTotalFrete, 2):C}) excede o limite diário da filial. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Frete");
                        }


                        if (validationPerTipo.IsValid)
                        {
                            if (myPendence != null)
                            {
                                myPendence.Status = (int)EBloqueioGestorStatus.Liberado;
                                myPendence.Motivo = $"Liberação automática por integração com valor dentro do limite ({Math.Round(valorTotalFrete, 2):C}).";
                            }
                        }

                        break;

                    case EBloqueioGestorTipo.ValorLimitePedagioDiario:

                        var statusExcluidos = new List<EResultadoCompraPedagio>
                        {
                            EResultadoCompraPedagio.Erro,
                            EResultadoCompraPedagio.NaoRealizado
                        };

                        var queryValorLimitePedagio = queryBase.Where(c => !statusExcluidos.Contains(c.ResultadoCompraPedagio));

                        if (viagem.IdFilial != null)
                            queryValorLimitePedagio.Where(x => x.IdFilial == viagem.IdFilial);

                        var valorLimiteDiarioPedagio = queryValorLimitePedagio
                                         .Select(c => c.ValorPedagio)
                                         .DefaultIfEmpty(0)
                                         .Sum();

                        if (valorConfiguradoEmpresa > 0 && valorLimiteDiarioPedagio + valorPedagioViagem > valorConfiguradoEmpresa)
                        {
                            var mensagem = $"Pagamento do valor de pedágio ({Math.Round(valorPedagioViagem, 2):C}) excede o limite diário da empresa. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Pedagio");
                        }

                        if (valorConfiguradoFilial > 0 && valorLimiteDiarioPedagio + valorPedagioViagem > valorConfiguradoFilial && viagem.IdFilial != null)
                        {
                            var mensagem = $"Pagamento do valor de pedágio ({Math.Round(valorPedagioViagem, 2):C}) excede o limite diário da filial. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Pedagio");
                        }

                        if (validationPerTipo.IsValid)
                        {
                            if (myPendence != null)
                            {
                                myPendence.Status = (int)EBloqueioGestorStatus.Liberado;
                                myPendence.Motivo = $"Liberação automática por integração com valor dentro do limite ({Math.Round(valorPedagioViagem, 2):C}).";
                            }
                        }

                        break;

                    case EBloqueioGestorTipo.ValorMaximoFreteUnitario:

                        if (valorConfiguradoEmpresa > 0 && valorTotalFrete > valorConfiguradoEmpresa)
                        {
                            var mensagem = $"Pagamento do valor de frete ({Math.Round(valorTotalFrete, 2):C}) excede o limite unitário da empresa. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Frete");
                        }

                        if (valorConfiguradoFilial > 0 && valorTotalFrete > valorConfiguradoFilial)
                        {
                            var mensagem = $"Pagamento do valor de frete ({Math.Round(valorTotalFrete, 2):C}) excede o limite unitário da filial. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Frete");
                        }

                        if (validationPerTipo.IsValid)
                        {
                            if (myPendence != null)
                            {
                                myPendence.Status = (int)EBloqueioGestorStatus.Liberado;
                                myPendence.Motivo = $"Liberação automática por integração com valor dentro do limite ({Math.Round(valorTotalFrete, 2):C}).";
                            }
                        }

                        break;

                    case EBloqueioGestorTipo.ValorMaximoPedagioUnitario:

                        if (valorConfiguradoEmpresa > 0 && valorPedagioViagem > valorConfiguradoEmpresa)
                        {
                            var mensagem = $"Pagamento do valor de pedágio ({Math.Round(valorPedagioViagem, 2):C}) excede o limite unitário da empresa. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Pedagio");
                        }

                        if (valorConfiguradoFilial > 0 && valorPedagioViagem > valorConfiguradoFilial)
                        {
                            var mensagem = $"Pagamento do valor de pedágio ({Math.Round(valorPedagioViagem, 2):C}) excede o limite unitário da filial. Necessária aprovação do gestor.";

                            validationPerTipo.Add(mensagem);
                            validation.Add(mensagem, EFaultType.Error, "Pedagio");
                        }

                        if (validationPerTipo.IsValid)
                        {
                            if (myPendence != null)
                            {
                                myPendence.Status = (int)EBloqueioGestorStatus.Liberado;
                                myPendence.Motivo = $"Liberação automática por integração com valor dentro do limite ({Math.Round(valorPedagioViagem, 2):C}).";
                            }
                        }

                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(EBloqueioGestorTipo));
                }

                if (!validationPerTipo.IsValid)
                {
                    var ocorrencia = string.Empty;

                    foreach (var mensagen in validationPerTipo.Errors.Select(c => c.Message))
                    {
                        ocorrencia += $"{mensagen} ";
                    }

                    if (myPendence == null)
                    {
                        myPendence = new ViagemPendenteGestor
                        {
                            DataCadastro = DateTime.Now
                        };
                    }

                    myPendence.IdViagem = viagem.IdViagem;
                    myPendence.IdFilial = viagem.IdFilial;
                    myPendence.IdEmpresa = viagem.IdEmpresa;
                    myPendence.IdBloqueioGestorTipo = (int)eBloqueioGestorTipo;
                    myPendence.Status = (int)EBloqueioGestorStatus.Pendente;
                    myPendence.Ocorrencia = ocorrencia;

                    _viagemPendenteGestorService.Save(myPendence);
                }
                else
                {
                    if (myPendence != null)
                        _viagemPendenteGestorService.Save(myPendence);
                }
            }

            return validation;
        }

        public ValidationResult AlterarViagemPendente(int idViagem, EBloqueioGestorTipo idBloqueioGestorTipo, int status, int idUsuario, string motivo)
        {
            var usuarioService = _usuarioService;
            var viagemPendenteGestorService = _viagemPendenteGestorService;

            var pendencia = viagemPendenteGestorService.GetQuery()
                                    .FirstOrDefault(v => v.IdViagem == idViagem && v.IdBloqueioGestorTipo == (int) idBloqueioGestorTipo);

            if (motivo?.Length > 100)
                return new ValidationResult().Add("O motivo informado deve ter no máximo 100 caracteres.");

            if (pendencia == null)
                return new ValidationResult().Add("Não foi encontrada nenhum registro de pendência para a viagem informada.");

            if (pendencia.Status != (int) EBloqueioGestorStatus.Pendente)
                return new ValidationResult().Add("Status de pendência da viagem não permite esta operação.");

            var permissao = usuarioService.GetConfiguracaoPermissaoGestor(idUsuario, idBloqueioGestorTipo);

            if (permissao == null)
                return new ValidationResult().Add("Usuário não configurado para desbloqueio de viagem.");

            if (pendencia.IdFilial.HasValue)
            {
                if (!permissao.DesbloquearFilial)
                    return new ValidationResult().Add("Usuário não possui permissão para desbloqueio de viagem bloqueada por uma filial.");
            }

            if (!permissao.DesbloquearEmpresa)
                return new ValidationResult().Add("Usuário não possui permissão para desbloqueio de viagem bloqueada por uma empresa.");

            pendencia.IdUsuarioDesbloqueio = idUsuario;
            pendencia.DataStatus = DateTime.Now;
            pendencia.Status = status;
            pendencia.Motivo = motivo;
            viagemPendenteGestorService.Update(pendencia);

            return new ValidationResult();
        }

        public List<Viagem> GetByCte(string numero)
        {
            var viagens = Repository
                .Find(o => o.NumeroDocumento == numero)
                .Include(o => o.ViagemEventos)
                .ToList();

            foreach (var item in viagens)
            {
                if (item.ViagemEventos != null && item.ViagemEventos.Any())
                {
                    item.ViagemEventos = item.ViagemEventos.Where(x => x.Status != EStatusViagemEvento.Baixado).ToList();
                }
            }

            return viagens;
        }

        public bool ExisteCartaFreteParaCombinacao(string cpfCnpjProprietario, string cpfMotorista, int idEmpresa, int mesesAnteriores)
        {
            if(!string.IsNullOrWhiteSpace(cpfCnpjProprietario))
            {
                var valido = _proprietarioRepository.PermiteTransferenciaSemCartaFrete(cpfCnpjProprietario);

                if (valido)
                    return true;
            }

            var dataLanc = DateTime.Today.AddMonths(mesesAnteriores * -1);
            var query = Repository
                .All(true)
                .Where(v => v.IdEmpresa == idEmpresa
                            && v.DataLancamento >= dataLanc
                            && (v.CPFCNPJProprietario == cpfCnpjProprietario || v.Proprietario.CNPJCPF == cpfCnpjProprietario)
                            && v.CPFMotorista == cpfMotorista
                            && v.StatusViagem != EStatusViagem.Cancelada);

            return query.Any();
        }

        public ViagemCalcularValorPedagioResponse CalcularValorPedagio(ViagemCalcularValorPedagioRequest request)
        {
            var placas = new List<string>();
            var listLocalizacao = new List<LocationDTO>();

            foreach (var requestPlaca in request.Placas)
            {
                placas.Add(requestPlaca.RemoveSpecialCharacters());
            }

            var quantidadeEixos = _veiculoRepository.All().Where(c => placas.Contains(c.Placa) && c.IdEmpresa == SessionUser.IdEmpresa && c.Ativo).Sum(c => c.QuantidadeEixos);

            var cartoesService = CartoesService.CreateByEmpresa(_cartoesServiceArgs, SessionUser.IdEmpresa ?? 0, SessionUser.CpfCnpj, SessionUser.Nome);

            var empresa = _empresaRepository.Get(SessionUser.IdEmpresa ?? 0);

            var pedagioRotaModelo = _rotaModeloService.GetByIdOrNomeRota(request.CodModeloRota,null, empresa?.IdEmpresa);

            if (pedagioRotaModelo != null)
            {
                var localizacaoRotaModelo = _rotaModeloService.SetarRetornoDestino(pedagioRotaModelo);

                foreach (var item in localizacaoRotaModelo)
                {
                    listLocalizacao.Add(new LocationDTO()
                    {
                        Ibge = item.Ibge.ToInt(),
                        Latitude = item.Latitude,
                        Longitude = item.Longitude,
                    });
                }
            }
            else
            {
                foreach (var item in request.Pontos)
                {
                    listLocalizacao.Add(new LocationDTO()
                    {
                        Latitude = item.Latitude,
                        Longitude = item.Longitude,
                    });
                }
            }
            
            var consultaRotaRequest = new ConsultaRotaRequest
            {
                QtdEixos = quantidadeEixos,
                TipoVeiculo = (ConsultaRotaRequestTipoVeiculo) request.TipoVeiculo,
                ExibirDetalhes = request.ExibirDetalhes,
                DesabilitaCacheRotas = empresa?.DesabilitaCacheRotas,
                Localizacoes = listLocalizacao
            };

            if (empresa != null)
            {
                var permiteUtilizarPolyline = _parametrosEmpresaService.GetUtilizaRoteirizacaoPorPolyline(empresa.IdEmpresa);

                if (permiteUtilizarPolyline)
                    consultaRotaRequest.CodPolyline = pedagioRotaModelo?.CodPolyline;
            }
            else
                consultaRotaRequest.CodPolyline = pedagioRotaModelo?.CodPolyline;

            var response = cartoesService.ConsultarCustoRota(consultaRotaRequest);

            var retorno = new ViagemCalcularValorPedagioResponse()
            {
                Sucesso = response.Status == ConsultaRotaResponseDtoStatus.Sucesso,
                CustoTotal = response.CustoTotal ?? 0,
                CustoTotalTag = response.CustoTotalTag ?? 0,
                IdentificadorHistorico = response.IdentificadorHistorico ?? Guid.Empty,
                Mensagem = response.Mensagem,
                DistanciaTotalKm = response.DistanciaTotalKm
            };

            if (request.ExibirDetalhes)
            {
                retorno.Pracas = retorno.Pracas.Select(c => new ViagemCalcularValorPedagioPracaResponse
                {
                    Nome = c.Nome,
                    Endereço = c.Endereço,
                    Valor = c.Valor
                }).ToList();
            }

            return retorno;
        }

        public ViagemEvento ConsultarViagemEvento(string token)
        {
            return _viagemEventoRepository.Where(c => c.Token == token).FirstOrDefault();
        }

        public ViagemConsultarResponse ConsultarViagemV2(int idViagem, int? idEmpresa, bool filtrarVeiculoTerceiro = false)
        {
            var query = Repository.Where(c => c.IdViagem == idViagem);

            if (!query.Any())
                throw new Exception("Nenhuma viagem encontrada com o Id informado.");

            if (idEmpresa != null && query.FirstOrDefault()?.IdEmpresa != idEmpresa)
                throw new Exception("O usuário logado não tem acesso á essa essa viagem.");

            var retorno = query.Select(c => new ViagemConsultarResponse
            {
                IdViagem = c.IdViagem,
                DataLancamento = c.DataLancamento ?? DateTime.Now,
                DataEmissao = c.DataEmissao,
                IdFilial = c.IdFilial,
                NomeFilial = c.Filial.NomeFantasia,
                NomeMotorista = c.NomeMotorista,
                CpfMotorista = c.CPFMotorista,
                IdProprietario = c.IdProprietario,
                NomeProprietario = c.NomeProprietario,
                CPFCNPJProprietario = c.CPFCNPJProprietario,
                RNTRC = c.RNTRC.ToString(),
                DocumentoCliente = c.DocumentoCliente,
                DataInicioFrete = c.DataColeta,
                DataFimFrete = c.DataPrevisaoEntrega,
                NaturezaCarga = c.NaturezaCarga.ToString(),
                HabilitarDeclaracaoCiot = c.HabilitarDeclaracaoCiot,
                INSS = c.INSS,
                IRRPF = c.IRRPF,
                SESTSENAT = c.SESTSENAT,
                StatusViagem = c.StatusViagem,
                Veiculo = new ViagemConsultarVeiculoResponse
                {
                    Placa = c.Placa
                },
                DadosBancario = new ViagemIntegrarDadosBancarioResponse()
                {
                    Agencia = c.Agencia,
                    ContaCorrente = c.ContaCorrente,
                    FormaPagamento = c.FormaPagamentoSemCartao,
                    TipoConta = c.TipoConta,
                    IdBanco = c.IdBanco,
                    DescricaoBanco = c.DescricaoBanco
                },
                DataAtualizacao = c.DataAtualizacao
            }).FirstOrDefault();

            if (retorno == null)
                throw new Exception("Nenhuma viagem encontrada com o Id informado.");

            var ciot = query.Where(c => c.DeclaracaoCiot != null)
                .Select(c => new
            {
                CiotSucesso = c.IdDeclaracaoCiot.HasValue,
                c.DeclaracaoCiot.Ciot,
                c.DeclaracaoCiot.Verificador,
                c.DeclaracaoCiot.TipoDeclaracao,
                c.DeclaracaoCiot.Status
            }).FirstOrDefault();

            if (ciot != null && query.Select(c => c.HabilitarDeclaracaoCiot).FirstOrDefault())
            {
                retorno.CiotSucesso = ciot.CiotSucesso;

                if (ciot.CiotSucesso)
                {
                    retorno.NumeroCIOT = $"{ciot.Ciot}/{ciot.Verificador}";
                    retorno.CiotAgregado = ciot.TipoDeclaracao == ETipoDeclaracao.Agregado;
                    retorno.CiotEncerrado = ciot.Status == EStatusDeclaracaoCiot.Encerrado;
                    retorno.DeclaracaoCiot = ciot.Status;
                }
                else
                {
                    retorno.NumeroCIOT = query.Select(c => c.MensagemDeclaracaoCiot).FirstOrDefault()?.Replace("Erro ao declarar CIOT: ", "");
                }
            }
            else
            {
                retorno.NumeroCIOT = query.FirstOrDefault()?.MensagemDeclaracaoCiot;
            }

            var motorista = _motoristaRepository.Where(c => c.CPF == retorno.CpfMotorista && c.IdEmpresa == SessionUser.IdEmpresa);

            if (motorista.Any())
            {
                retorno.IdMotorista = motorista.Select(c => c.IdMotorista).FirstOrDefault();
            }

            var veiculoQuery = _veiculoRepository.Where(c => c.Placa == retorno.Veiculo.Placa);

            if (!filtrarVeiculoTerceiro)
                veiculoQuery = veiculoQuery.Where(o => o.IdEmpresa == SessionUser.IdEmpresa);

            if (veiculoQuery.Any())
            {
                retorno.Veiculo = veiculoQuery.Select(c => new ViagemConsultarVeiculoResponse
                {
                    Placa = c.Placa,
                    Tipo = c.TipoCavalo.Nome,
                    IdVeiculo = c.IdVeiculo,
                    Modelo = c.Modelo,
                    RENAVAM = c.RENAVAM,
                    Eixos = c.QuantidadeEixos
                }).FirstOrDefault();
            }

            var carretas = query.Select(c => c.ViagemCarretas.Select(a => new { a.IdViagemCarreta, a.Placa}).ToList()).FirstOrDefault();

            if (carretas != null)
            {
                retorno.Carretas = new List<ViagemConsultarVeiculoResponse>();

                foreach (var carreta in carretas)
                {
                    var carretaQuery = _veiculoRepository.Where(c => c.Placa == carreta.Placa);

                    if (!filtrarVeiculoTerceiro)
                        carretaQuery = carretaQuery.Where(c => c.IdEmpresa == SessionUser.IdEmpresa);

                    retorno.Carretas.Add(carretaQuery.Select(c => new ViagemConsultarVeiculoResponse
                    {
                        Placa = c.Placa,
                        Tipo = c.TipoCarreta.Nome,
                        Modelo = c.Modelo,
                        IdVeiculo = c.IdVeiculo,
                        RENAVAM = c.RENAVAM,
                        Eixos = c.QuantidadeEixos
                    }).FirstOrDefault());
                }
            }

            var viagemRotaQuery = _viagemRotaRepository.GetByViagem(idViagem);
            var fornecedorQuery = _fornecedorCnpjPedagioRepository;

            if (viagemRotaQuery.Any())
            {
                retorno.Rota = new ViagemConsultarRotaResponse();
                retorno.Rota.Pontos = viagemRotaQuery.Select(c => c.Pontos.OrderBy(x => x.Sequencia).Select(a => new ViagemConsultarRotaPontoResponse
                {
                    Latitude = a.Latitude ?? 0,
                    Longitude = a.Longitude ?? 0,
                    Cidade = a.Cidade.Nome,
                    Estado = a.Cidade.Estado.Sigla,
                    Pais = a.Cidade.Estado.Pais.Nome,
                    Sequencia = a.Sequencia
                }).ToList()).FirstOrDefault();

                retorno.Pedagio = viagemRotaQuery.Select(c => new ViagemConsultarPedagioResponse
                {
                    TipoVeiculo = c.TipoVeiculo,
                    Fornecedor = c.FornecedorPedagio,
                    CNPJFornecedor = string.Empty
                }).FirstOrDefault();

                if (retorno.Pedagio != null)
                {
                    var cnpjFornecedor = fornecedorQuery.GetCnpjFornecedor(retorno.Pedagio.Fornecedor.GetHashCode());
                    retorno.Pedagio.CNPJFornecedor = cnpjFornecedor.IsNullOrWhiteSpace() ? string.Empty : cnpjFornecedor;
                }

                if (retorno.Pedagio != null)
                {
                    retorno.Pedagio.Valor = query.Select(c => c.ValorPedagio).FirstOrDefault();
                    retorno.Pedagio.StatusPedagio = query.Select(c => c.ResultadoCompraPedagio).FirstOrDefault();
                    retorno.Pedagio.ProtocoloEnvioValePedagio = query.Select(c => c.ProtocoloEnvioValePedagio).FirstOrDefault();
                    retorno.Pedagio.ProtocoloValePedagio = query.Select(c => c.ProtocoloValePedagio).FirstOrDefault();
                    retorno.Pedagio.MensagemStatus = query.Select(c => c.MensagemCompraPedagio).FirstOrDefault();
                    retorno.Pedagio.ProtocoloEnvioValePedagio = retorno.Pedagio.Fornecedor.RegistraValePedagioCertificadoExtratta() 
                        ? retorno.Pedagio.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(retorno.Pedagio.Fornecedor)
                        : retorno.Pedagio.ProtocoloValePedagio.ToProtocoloAnttFormat(retorno.Pedagio.Fornecedor);
                }
            }

            if (retorno.Pedagio == null)
            {
                retorno.Pedagio = new ViagemConsultarPedagioResponse();
                retorno.Pedagio.Fornecedor = FornecedorEnum.Desabilitado;
                retorno.Pedagio.TipoVeiculo = ETipoVeiculoPedagioEnum.Caminhao;
                retorno.Pedagio.StatusPedagio = EResultadoCompraPedagio.NaoRealizado;
            }

            retorno.Documentos = query.Select(c => c.ViagemDocumentosFiscais.Select(a => new ViagemConsultarDocumentoResponse
            {
                Id = a.IdViagemDocumentoFiscal,
                Valor = a.Valor ?? 0,
                PesoSaida = a.PesoSaida,
                TipoDocumento = a.TipoDocumento,
                NumeroDocumento = a.NumeroDocumento.ToString(),
                Serie = a.Serie,
                Chave = a.Chave,
                IdClienteOrigem = a.IdClienteOrigem ?? 0,
                IdClienteDestino = a.IdClienteDestino ?? 0,
                ClienteOrigem = a.ClienteOrigem.NomeFantasia,
                ClienteDestino = a.ClienteDestino.NomeFantasia,
                CnpjClienteOrigem = a.ClienteOrigem.CNPJCPF,
                CnpjClienteDestino = a.ClienteDestino.CNPJCPF
            }).ToList()).FirstOrDefault();

            retorno.Parcelas = query.Select(c => c.ViagemEventos.Select(a => new ViagemConsultarParcelaResponse
            {
                IdViagemEvento = a.IdViagemEvento,
                Token = a.Token,
                Status = a.Status,
                Valor = a.ValorPagamento,
                TipoEvento = a.TipoEventoViagem,
                FormaPagamento = a.HabilitarPagamentoPix == true ? EViagemEventoFormaPagamento.Pix : a.HabilitarPagamentoCartao ? EViagemEventoFormaPagamento.Cartao : EViagemEventoFormaPagamento.SemCartao,
                Instrucao = a.Instrucao,
                DataPagamento = a.DataHoraPagamento,
                DataAgendamentoPagamento = a.DataAgendamentoPagamento,
                MensagemTransacao = a.TransacaoCartao.OrderByDescending(x => x.DataCriacao).FirstOrDefault().MensagemProcessamentoWs,
                Documentos = a.ViagemDocumentos.Select(b => new ViagemConsultarParcelaDocumentoResponse
                {
                    Id = b.IdViagemDocumento,
                    Numero = b.NumeroDocumento.ToString(),
                    Descricao = b.Descricao,
                    TipoDocumento = b.TipoDocumento,
                    AnexoObrigatorio = b.ObrigaAnexo
                }).ToList(),
                AcrescimosDescontos = a.ViagemValoresAdicionais.Select(d => new ViagemConsultarParcelaAcrescimoDescontoResponse
                {
                    Id = d.IdViagemValorAdicional,
                    Tipo = d.Tipo,
                    Valor = d.Valor,
                    Descricao = d.Descricao,
                    NumeroDocumento = d.NumeroDocumento.ToString()
                }).ToList()
            }).ToList()).FirstOrDefault();

            if (retorno.Parcelas?.All(c => c.FormaPagamento == EViagemEventoFormaPagamento.Cartao) ?? false)
            {
                retorno.DadosPagamento = new ViagemIntegrarDadosPagamentoResponse
                {
                    FormaPagamento = EViagemFormaPagamento.Cartao
                };
            }
            else
            {
                retorno.DadosPagamento = new ViagemIntegrarDadosPagamentoResponse
                {
                    FormaPagamento = EViagemFormaPagamento.Outros
                };
            }

            return retorno;
        }

        public ViagemConsultarResponse ConsultarViagemV3(int idViagem, bool filtrarVeiculoTerceiro = false)
        {
            var query = Repository.Where(c => c.IdViagem == idViagem);

            if (!query.Any())
                throw new Exception("Nenhuma viagem encontrada com o Id informado.");

            var retorno = query.Select(c => new ViagemConsultarResponse
            {
                IdViagem = c.IdViagem,
                IdEmpresa = c.IdEmpresa,
                DataLancamento = c.DataLancamento ?? DateTime.Now,
                DataEmissao = c.DataEmissao,
                IdFilial = c.IdFilial,
                NomeFilial = c.Filial.NomeFantasia,
                NomeMotorista = c.NomeMotorista,
                CpfMotorista = c.CPFMotorista,
                IdProprietario = c.IdProprietario,
                NomeProprietario = c.NomeProprietario,
                RNTRC = c.RNTRC.ToString(),
                DocumentoCliente = c.DocumentoCliente,
                DataInicioFrete = c.DataColeta,
                DataFimFrete = c.DataPrevisaoEntrega,
                NaturezaCarga = c.NaturezaCarga.ToString(),
                HabilitarDeclaracaoCiot = c.HabilitarDeclaracaoCiot,
                INSS = c.INSS,
                IRRPF = c.IRRPF,
                SESTSENAT = c.SESTSENAT,
                StatusViagem = c.StatusViagem,
                CepOrigem = c.CepOrigem,
                CepDestino = c.CepDestino,
                CodigoTipoCarga = c.CodigoTipoCarga,
                DistanciaViagem = c.DistanciaViagem,
                Veiculo = new ViagemConsultarVeiculoResponse
                {
                    Placa = c.Placa
                },
                DadosPagamento = new ViagemIntegrarDadosPagamentoResponse()
                {
                    FormaPagamento = c.FormaPagamento,
                    CodigoBacen = c.ViagemPagamentoConta.CodigoBacenBanco,
                    Agencia = c.ViagemPagamentoConta.Agencia,
                    Conta = c.ViagemPagamentoConta.Conta
                },
                DadosAntt = new ViagemIntegrarDadosAnttResponse()
                {
                    AltoDesempenho = c.AltoDesempenho,
                    DestinacaoComercial = c.DestinacaoComercial,
                    FreteRetorno = c.FreteRetorno,
                    CepRetorno = c.CepRetorno,
                    DistanciaRetorno = c.DistanciaRetorno
                },
                DataAtualizacao = c.DataAtualizacao
            }).FirstOrDefault();

            if (retorno == null)
                throw new Exception("Nenhuma viagem encontrada com o Id informado.");

            if (_versaoAntt.Value == EVersaoAntt.Versao3)
                retorno.DescricaoTipoCarga = DescricaoTipoCarga(retorno.CodigoTipoCarga, retorno.IdEmpresa);

            var ciot = query.Where(c => c.DeclaracaoCiot != null)
                .Select(c => new
            {
                CiotSucesso = c.IdDeclaracaoCiot.HasValue,
                c.DeclaracaoCiot.Ciot,
                c.DeclaracaoCiot.Verificador,
                c.DeclaracaoCiot.TipoDeclaracao,
                c.DeclaracaoCiot.Status
            }).FirstOrDefault();

            if (ciot != null && query.Select(c => c.HabilitarDeclaracaoCiot).FirstOrDefault())
            {
                retorno.CiotSucesso = ciot.CiotSucesso;

                if (ciot.CiotSucesso)
                {
                    retorno.NumeroCIOT = $"{ciot.Ciot}/{ciot.Verificador}";
                    retorno.CiotAgregado = ciot.TipoDeclaracao == ETipoDeclaracao.Agregado;
                    retorno.CiotEncerrado = ciot.Status == EStatusDeclaracaoCiot.Encerrado;
                }

                else
                {
                    retorno.NumeroCIOT = query.Select(c => c.MensagemDeclaracaoCiot).FirstOrDefault();
                }
            }

            var motorista = _motoristaRepository.Where(c => c.CPF == retorno.CpfMotorista && c.IdEmpresa == SessionUser.IdEmpresa);

            if (motorista.Any())
            {
                retorno.IdMotorista = motorista.Select(c => c.IdMotorista).FirstOrDefault();
            }

            var veiculoQuery = _veiculoRepository.Where(c => c.Placa == retorno.Veiculo.Placa);

            if (!filtrarVeiculoTerceiro)
                veiculoQuery = veiculoQuery.Where(o => o.IdEmpresa == SessionUser.IdEmpresa);

            if (veiculoQuery.Any())
            {
                retorno.Veiculo = veiculoQuery.Select(c => new ViagemConsultarVeiculoResponse
                {
                    Placa = c.Placa,
                    Tipo = c.TipoCavalo.Nome,
                    IdVeiculo = c.IdVeiculo,
                    Modelo = c.Modelo,
                    RENAVAM = c.RENAVAM
                }).FirstOrDefault();
            }

            var carretas = query.Select(c => c.ViagemCarretas.Select(a => new { a.IdViagemCarreta, a.Placa}).ToList()).FirstOrDefault();

            if (carretas != null)
            {
                retorno.Carretas = new List<ViagemConsultarVeiculoResponse>();

                foreach (var carreta in carretas)
                {
                    var carretaQuery = _veiculoRepository.Where(c => c.Placa == carreta.Placa && c.IdEmpresa == SessionUser.IdEmpresa);

                    retorno .Carretas.Add(carretaQuery.Select(c => new ViagemConsultarVeiculoResponse
                    {
                        Placa = c.Placa,
                        Tipo = c.TipoCarreta.Nome,
                        Modelo = c.Modelo,
                        IdVeiculo = c.IdVeiculo,
                        RENAVAM = c.RENAVAM
                    }).FirstOrDefault());
                }
            }

            var viagemRotaQuery = _viagemRotaRepository.GetByViagem(idViagem);

            if (viagemRotaQuery.Any())
            {
                retorno.Rota = new ViagemConsultarRotaResponse();
                retorno.Rota.Pontos = viagemRotaQuery.Select(c => c.Pontos.OrderBy(x => x.Sequencia).Select(a => new ViagemConsultarRotaPontoResponse
                {
                    Latitude = a.Latitude ?? 0,
                    Longitude = a.Longitude ?? 0,
                    Cidade = a.Cidade.Nome,
                    Estado = a.Cidade.Estado.Sigla,
                    Pais = a.Cidade.Estado.Pais.Nome,
                    Sequencia = a.Sequencia
                }).ToList()).FirstOrDefault();

                retorno.Pedagio = viagemRotaQuery.Select(c => new ViagemConsultarPedagioResponse
                {
                    TipoVeiculo = c.TipoVeiculo,
                    Fornecedor = c.FornecedorPedagio,
                }).FirstOrDefault();

                if (retorno.Pedagio != null)
                {
                    retorno.Pedagio.Valor = query.Select(c => c.ValorPedagio).FirstOrDefault();
                    retorno.Pedagio.StatusPedagio = query.Select(c => c.ResultadoCompraPedagio).FirstOrDefault();
                    retorno.Pedagio.ProtocoloEnvioValePedagio = query.Select(c => c.ProtocoloEnvioValePedagio).FirstOrDefault();
                    retorno.Pedagio.ProtocoloValePedagio = query.Select(c => c.ProtocoloValePedagio).FirstOrDefault();
                    retorno.Pedagio.MensagemStatus = query.Select(c => c.MensagemCompraPedagio).FirstOrDefault();
                    retorno.Pedagio.ProtocoloEnvioValePedagio = retorno.Pedagio.Fornecedor.RegistraValePedagioCertificadoExtratta() 
                        ? retorno.Pedagio.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(retorno.Pedagio.Fornecedor)
                        : retorno.Pedagio.ProtocoloValePedagio.ToProtocoloAnttFormat(retorno.Pedagio.Fornecedor);
                }
            }

            if (retorno.Pedagio == null)
            {
                retorno.Pedagio = new ViagemConsultarPedagioResponse();
                retorno.Pedagio.Fornecedor = FornecedorEnum.Desabilitado;
                retorno.Pedagio.TipoVeiculo = ETipoVeiculoPedagioEnum.Caminhao;
                retorno.Pedagio.StatusPedagio = EResultadoCompraPedagio.NaoRealizado;
            }

            retorno.Documentos = query.Select(c => c.ViagemDocumentosFiscais.Select(a => new ViagemConsultarDocumentoResponse
            {
                Id = a.IdViagemDocumentoFiscal,
                Valor = a.Valor ?? 0,
                PesoSaida = a.PesoSaida,
                TipoDocumento = a.TipoDocumento,
                NumeroDocumento = a.NumeroDocumento.ToString(),
                Serie = a.Serie,
                Chave = a.Chave,
                IdClienteOrigem = a.IdClienteOrigem ?? 0,
                IdClienteDestino = a.IdClienteDestino ?? 0,
                ClienteOrigem = a.ClienteOrigem.NomeFantasia,
                ClienteDestino = a.ClienteDestino.NomeFantasia,
                CnpjClienteOrigem = a.ClienteOrigem.CNPJCPF,
                CnpjClienteDestino = a.ClienteDestino.CNPJCPF
            }).ToList()).FirstOrDefault();

            retorno.Parcelas = query.Select(c => c.ViagemEventos.Select(a => new ViagemConsultarParcelaResponse
            {
                IdViagemEvento = a.IdViagemEvento,
                Token = a.Token,
                Status = a.Status,
                Valor = a.ValorPagamento,
                TipoEvento = a.TipoEventoViagem,
                Instrucao = a.Instrucao,
                DataPagamento = a.DataHoraPagamento,
                Documentos = a.ViagemDocumentos.Select(b => new ViagemConsultarParcelaDocumentoResponse
                {
                    Id = b.IdViagemDocumento,
                    Numero = b.NumeroDocumento.ToString(),
                    Descricao = b.Descricao,
                    TipoDocumento = b.TipoDocumento,
                    AnexoObrigatorio = b.ObrigaAnexo
                }).ToList(),
                AcrescimosDescontos = a.ViagemValoresAdicionais.Select(d => new ViagemConsultarParcelaAcrescimoDescontoResponse
                {
                    Id = d.IdViagemValorAdicional,
                    Tipo = d.Tipo,
                    Valor = d.Valor,
                    Descricao = d.Descricao,
                    NumeroDocumento = d.NumeroDocumento.ToString()
                }).ToList()
            }).ToList()).FirstOrDefault();

            retorno.AlgumaParcelaPaga = retorno.Parcelas != null && retorno.Parcelas.Any(x => x.Status == EStatusViagemEvento.Baixado);
            return retorno;
        }

        private string DescricaoTipoCarga(int? codigoTipoCarga, int idEmpresa)
        {
            if (!codigoTipoCarga.HasValue)
                return "Não selecionado";

            var filtroCodigoTipoCarga = new List<QueryFilters>
            {
                new QueryFilters
                {
                    Campo = "Codigo",
                    Operador = EOperador.Exact,
                    Valor = codigoTipoCarga.ToString(),
                    CampoTipo = EFieldTipo.Number
                }
            };

            var consultaTipoCarga = ConsultarTiposCarga(idEmpresa, 1, 1, null, filtroCodigoTipoCarga);
            if (consultaTipoCarga.Sucesso != true || consultaTipoCarga.Tipos == null || !consultaTipoCarga.Tipos.Any())
                return "Serviço de CIOT indisponível.";

            var tipoCarga = consultaTipoCarga.Tipos.First();
            return !string.IsNullOrEmpty(tipoCarga.Descricao) ? tipoCarga.Descricao : "Serviço de CIOT indisponível";

        }

        public IQueryable<ViagemEvento> GetEvento(int idviagem, int idviagemEvento)
        {
            return _viagemEventoRepository.Where(c => c.IdViagem == idviagem && c.IdViagemEvento == idviagemEvento && c.IdEmpresa == SessionUser.IdEmpresa);
        }

        public IQueryable<Viagem> GetViagem(int idviagem)
        {
            return _viagemRepository.GetQuery(idviagem);
        }

        public ValidationResult AddViagemRota(ViagemRota viagemRota)
        {
            var validationResult = new ValidationResult();

            try
            {
                _viagemRotaRepository.Add(viagemRota);
            }
            catch (Exception e)
            {
                validationResult.Add("Erro ao adicionar rota da viagem: " + e.GetBaseException().Message);
            }

            return validationResult;
        }

        public ValidationResult UpdateViagemRota(ViagemRota viagemRota)
        {
            var validationResult = new ValidationResult();

            try
            {
                _viagemRotaRepository.Update(viagemRota);
            }
            catch (Exception e)
            {
                validationResult.Add("Erro ao atualizar rota da viagem: " + e.GetBaseException().Message);
            }

            return validationResult;
        }

        public ViagemRota GetViagemRota(int idViagem)
        {
            return _viagemRotaRepository.GetByViagem(idViagem).FirstOrDefault();
        }

        public Guid? GetHistoricoViagemRota(int idViagem)
        {
            return _viagemRotaRepository.GetByViagem(idViagem).Select(x => x.IdentificadorHistorico).FirstOrDefault();
        }

        public ValidationResult DeleteViagemRota(ViagemRota viagemRota)
        {
            var validationResult = new ValidationResult();

            try
            {
                _viagemRotaRepository.DeleteNew(viagemRota.IdViagemRota);
            }
            catch (Exception e)
            {
                validationResult.Add("Erro ao remover rota da viagem: " + e.GetBaseException().Message);
            }

            return validationResult;
        }

        public ViagemDadosCiotResponse GetDadosCiot(int idviagem)
        {
            var retorno = Repository.Where(c => c.IdViagem == idviagem).Select(c => new ViagemDadosCiotResponse
            {
                CiotStatus = c.DeclaracaoCiot != null ? EResultadoDeclaracaoCiot.Sucesso : c.ResultadoDeclaracaoCiot ?? EResultadoDeclaracaoCiot.NaoHabilitado,
                CiotMensagem = c.MensagemDeclaracaoCiot,
                Numero = c.DeclaracaoCiot.Ciot,
                Verificador = c.DeclaracaoCiot.Verificador,
                Senha = c.DeclaracaoCiot.Senha
            }).FirstOrDefault();

            return retorno;
        }

        public ViagemDadosValePedagio GetDadosValePedagio(int idviagem)
        {
            return _viagemDapper.GetDadosValePedagio(idviagem);
        }

        public ConsultarEventoResponseModel ConsultarDetalheEvento(ConsultarEventoRequestModel request)
        {
            var dados = _viagemEventoRepository.Where(c => c.IdViagem == request.IdViagem && c.NumeroControle == request.NumeroControle)
                .Select(x => new
                {
                    x.Viagem.DataEmissao,
                    IdEvento = x.IdViagemEvento,
                    TipoEvento = x.TipoEventoViagem,
                    StatusEvento = x.Status,
                    x.ValorPagamento,
                    x.ValorTotalPagamento,
                    DataPagamento = x.DataHoraPagamento,
                    HoraPagamento = x.DataHoraPagamento,
                    x.NumeroRecibo,
                    DataRejeicaoAbono = x.DataHoraRejeicaoAbono,
                    DetalhamentoAbono = x.DescricaoRejeicaoAbono
                }).FirstOrDefault();

            if (dados == null)
                return new ConsultarEventoResponseModel();

            var ciot = Repository.Where(c => c.IdViagem == request.IdViagem).Select(c => new
            {
                c.DeclaracaoCiot.Ciot,
                c.DeclaracaoCiot.Verificador
            }).FirstOrDefault();

            return new ConsultarEventoResponseModel
            {
                DataEmissao = dados.DataEmissao != null ? dados.DataEmissao.Value.ToString("G") : "",
                IdEvento = dados.IdEvento,
                TipoEvento = dados.TipoEvento,
                StatusEvento = dados.StatusEvento,
                ValorPagamento = dados.ValorPagamento,
                ValorTotalPagamento = dados.ValorTotalPagamento,
                DataPagamento = dados.DataPagamento != null ? dados.DataPagamento.Value.ToString("G") : "",
                HoraPagamento = dados.DataPagamento != null ? dados.DataPagamento.Value.ToShortTimeString() : "",
                NumeroRecibo = dados.NumeroRecibo,
                CIOT = $"{ciot?.Ciot}/{ciot?.Verificador}",
                DataRejeicaoAbono = dados.DataRejeicaoAbono != null ? dados.DataRejeicaoAbono.Value.ToString("G") : "",
                DetalhamentoAbono = dados.DetalhamentoAbono
            };
        }

        public IEnumerable<ViagemMotoristasModel> GetMotoristasComViagensDoProprietario(string cnpjProprietario,
            DateTime? dataCadastroBase = null)
        {
            return Repository.GetMotoristasComViagensDoProprietario(cnpjProprietario, dataCadastroBase);
        }

        public SolicitarCompraPedagioResponseDTO GetStatusPedagio(Viagem viagem)
        {
            if (viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.NaoRealizado)
                return new SolicitarCompraPedagioResponseDTO
                {
                    Status = EResultadoCompraPedagio.NaoRealizado,
                    Valor = viagem.ValorPedagio,
                    Mensagem = "Recurso de compra de pedágio desabilitada durante a integração da viagem"
                };

            var fornecedor = GetFornecedorViagemRota(viagem.IdViagem);
            var cnpjFornecedor = fornecedor.HasValue ? _fornecedorCnpjPedagioRepository.GetCnpjFornecedor(fornecedor.Value) : null;

            return new SolicitarCompraPedagioResponseDTO
            {
                ProtocoloProcessamento = viagem.NumeroProtocoloPedagio.ToIntNullable(),
                Mensagem = viagem.MensagemCompraPedagio,
                ProtocoloRequisicao = viagem.IdViagem,
                Status = viagem.ResultadoCompraPedagio,
                Valor = viagem.ValorPedagio,
                Fornecedor = fornecedor,
                CnpjFornecedor = cnpjFornecedor,
                ProtocoloValePedagio = viagem.ProtocoloValePedagio,
                ProtocoloEnvioValePedagio = viagem.ProtocoloEnvioValePedagio
            };
        }

        public Viagem ConsultarViagemPorToken(string token)
        {
            var viagem = Repository.Include(t => t.Empresa)
                .Include(t => t.ViagemEventos.Select(s => s.ViagemDocumentos))
                .Include(t => t.ViagemEstabelecimentos).FirstOrDefault(t => t.ViagemEventos.Any(x => x.Token == token));

            return viagem;
        }

        public bool PossuiResgateDeSaldoResidualSolicitadoNoDia(string documento, DateTime dia)
        {
            var dataInicio = dia.Date;
            var dataFim = dia.EndOfDay();
            var query = Repository
                .AsNoTracking()
                .Where(c => c.CPFMotorista == documento &&
                            c.DataLancamento >= dataInicio &&
                            c.DataLancamento <= dataFim &&
                            c.EstornoSaldoResidualPedagioSolicitado == true &&
                            c.StatusViagem != EStatusViagem.Cancelada);
            return query.Any();
    }

        public IList<ConsultaViagemInternalResponseDTO> RelatorioConsultaViagens(ConsultaViagemRequestDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters)
        {
            var viagemRepository = _viagemDapper;

            var resultado = viagemRepository.GetViagens(request);

            resultado = resultado.AsQueryable().AplicarOrderByDinamicos(Order).AplicarFiltrosDinamicos(filters).ToList();

            return resultado;
        }

        public ConsultaViagemExternalResponseDTO ConsultarViagens(ConsultaViagemRequestDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var viagemRepository = _viagemDapper;

            var resultado = viagemRepository.GetViagens(request);

            var lista = resultado.AsQueryable().AplicarOrderByDinamicos(order).AplicarFiltrosDinamicos(filters);

            var dados = lista.ToList().Select(registro => new ConsultaViagemExternalItensResponseDTO
            {
                IdViagem = registro.IdViagem,
                DataAtualizacao = registro.DataAtualizacao.ToString("dd/MM/yyyy HH:mm:ss"),
                DataEmissao = registro.DataEmissao?.ToString("dd/MM/yyyy HH:mm:ss"),
                DataIntegracao = registro.DataIntegracao.ToString("dd/MM/yyyy HH:mm:ss"),
                DataFimFrete = registro.DataPrevisaoEntrega.ToString("dd/MM/yyyy HH:mm:ss"),
                DataInicioFrete = registro.DataColeta.ToString("dd/MM/yyyy HH:mm:ss"),
                EmpresaCnpj = registro.EmpresaCnpj.FormatarCpfCnpj(ignorarErro: true),
                EmpresaNome = registro.EmpresaNome,
                FilialCnpj = registro.FilialCnpj.FormatarCpfCnpj(ignorarErro:true),
                FilialNome = registro.FilialNome,
                ClienteOrigemCnpjCpf = registro.ClienteOrigemCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                ClienteOrigemNome = registro.ClienteOrigemNome,
                ClienteDestinoCnpjCpf = registro.ClienteDestinoCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                ClienteDestinoNome = registro.ClienteDestinoNome,
                Placa = registro.Placa.ToPlacaFormato(),
                ProprietarioCnpjCpf = registro.ProprietarioCnpjCpf.FormatarCpfCnpj(ignorarErro: true),
                ProprietarioNome = registro.ProprietarioNome,
                RazaoSocial = registro.RazaoSocial,
                ProprietarioRntrc = registro.ProprietarioRntrc.ToString(),
                MotoristaCpf = registro.MotoristaCpf.FormatarCpfCnpj(ignorarErro: true),
                MotoristaNome = registro.MotoristaNome,
                DocumentoCliente = registro.DocumentoCliente,
                ValePedagioConfirmado = registro.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada && registro.FornecedorPedagio.ConfirmacaoEmUmaEtapa(),
                CiotHabilitado = registro.CiotHabilitadoBoolean,
                CiotGerado = registro.HasCIOTBoolean,
                CIOT = registro.CiotHabilitadoBoolean && registro.HasCIOTBoolean ? $"{registro.CIOT}/{registro.Verificador}" : registro.CIOT,
                ValorSaldoStr = registro.ValorSaldo.ToString("C", new CultureInfo("pt-BR")),
                ValorAdiantamentoStr = registro.ValorAdiantamento.ToString("C", new CultureInfo("pt-BR")),
                ValorTarifaStr = registro.ValorTarifa.ToString("C", new CultureInfo("pt-BR")),
                ValorPedagioStr = registro.ValorPedagio.ToString("C", new CultureInfo("pt-BR")),
                ValorSaldo = registro.ValorSaldo,
                ValorAdiantamento = registro.ValorAdiantamento,
                ValorAbasteciemnto = registro.ValorAbastecimento,
                ValorAbastecimentoStr = registro.ValorAbastecimento.ToString("C", new CultureInfo("pt-BR")),
                ValorEstadia = registro.ValorEstadia,
                ValorEstadiaStr = registro.ValorEstadia.ToString("C", new CultureInfo("pt-BR")),
                ValorTarifa = registro.ValorTarifa,
                ValorPedagio = registro.ValorPedagio,
                StatusViagem = registro.StatusViagem.GetDescription(),
                InssStr = registro.Inss.ToString("C", new CultureInfo("pt-BR")),
                IrrfStr = registro.Irrf.ToString("C", new CultureInfo("pt-BR")),
                SestsenatStr = registro.Sestsenat.ToString("C", new CultureInfo("pt-BR")),
                Inss = registro.Inss,
                Irrf = registro.Irrf,
                Sestsenat = registro.Sestsenat,
                FornecedorPedagio = registro.FornecedorPedagio,
                FornecedorPedagioStr = registro.FornecedorPedagio.GetDescription(),
                FornecedorCnpj = registro.FornecedorCnpj.FormatarCpfCnpj(ignorarErro: true),
                ProtocoloEnvioValePedagio = registro.FornecedorPedagio.RegistraValePedagioCertificadoExtratta() 
                    ? registro.ProtocoloEnvioValePedagio.ToProtocoloAnttFormat(registro.FornecedorPedagio)
                    : registro.ProtocoloValePedagio.ToProtocoloAnttFormat(registro.FornecedorPedagio),
                ResultadoCompraPedagio = registro.ResultadoCompraPedagio,
                ResultadoCompraPedagioStr = registro.ResultadoCompraPedagio != EResultadoCompraPedagio.Erro
                    ? registro.ResultadoCompraPedagio.GetDescription()
                    : registro.ResultadoCompraPedagio.GetDescription() + ": " + registro.MensagemCompraPedagio
            }).AsQueryable();

            var itens = new List<ConsultaViagemExternalItensResponseDTO>();

            if (take > 0)
                itens = dados.Skip((page - 1) * take).Take(take).ToList();

            var retornoTotal = new ConsultaViagemExternalResponseDTO
            {
                totalItems = dados.Count(),
                items = itens
            };

            return retornoTotal;
        }

        public ConsultaViagemExternalCiotResponseDTO ConsultarDetalhesCiotViagem(int idViagem)
        {
            var viagemRepository = Repository;
            var declaracaoCiotRepository = _declaracaoCiotRepository;

            var declaracaoCiot = viagemRepository.Where(c => c.IdViagem == idViagem && c.IdEmpresa == SessionUser.IdEmpresa).Select(c => c.IdDeclaracaoCiot).First();

            var ciots = declaracaoCiotRepository.Where(c => c.IdDeclaracaoCiot == declaracaoCiot).First();

            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    _ciotV2Service.GerarToken(ciots.IdEmpresa);
                    break;
                case EVersaoAntt.Versao3:
                    _ciotV3Service.GerarToken(ciots.IdEmpresa);
                    break;
                default:
                    _ciotV2Service.GerarToken(ciots.IdEmpresa);
                    break;
            }

            var dadosCiotRequest = new ConsultarSituacaoCiotRequest();
            dadosCiotRequest.Ciot = ciots.Ciot;
            dadosCiotRequest.SenhaAlteracao = ciots.Senha;

            var dados = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2Service.ConsultarSituacaoCiot(dadosCiotRequest)
                : _ciotV3Service.ConsultarSituacaoCiot(dadosCiotRequest);

            if (dados.Sucesso.HasValue && !dados.Sucesso.Value)
                return new ConsultaViagemExternalCiotResponseDTO();

            return new ConsultaViagemExternalCiotResponseDTO
            {
                Ciot = dados.Ciot?.Trim(),
                Verificador = dados.CodigoVerificador?.Trim(),
                StatusCiot = dados.SituacaoDeclaracao?.Trim(),
                DataInicioFrete = dados.DataInicioFrete?.ToString("dd/MM/yyyy HH:mm:ss"),
                DataFimFrete = dados.DataTerminoFrete?.ToString("dd/MM/yyyy HH:mm:ss"),
                AvisoAoTransportador = dados.AvisoTransportador?.Trim(),
                TipoCiot = (dados.TipoViagem.HasValue ? (ConsultaViagemExternalCiotResponseDTO.ETipoViagemConsultaCiot) dados.TipoViagem.Value : 0).GetDescription()
            };
        }

        public Viagem ObterViagemPorEmpresa(int viagemId, int empresaId)
        {
            var viagem = _viagemRepository
                .Include(o => o.ViagemEventos)
                .Include(o => o.ClienteOrigem)
                .Include(o => o.ClienteDestino)
                .Include(o => o.Filial)
                .Include(o => o.ViagemEstabelecimentos)
                .Include(o => o.ViagemEstabelecimentos.Select(o2 => o2.Estabelecimento))
                .FirstOrDefault(o => o.IdViagem == viagemId && o.IdEmpresa == empresaId);

            return viagem;
        }

        public ConsultaTiposCargasResponse ConsultarTiposCarga(string cnpjEmpresa)
        {
            var resposta = _ciotV3Service.ConsultarTiposCarga(cnpjEmpresa);

            if (resposta.Sucesso.HasValue && resposta.Sucesso.Value)
                return new ConsultaTiposCargasResponse
                {
                    Sucesso = true,
                    Mensagem = null,
                    Objeto = resposta.Tipos.Select(o => new ConsultaTiposCargasObjetoResponse
                    {
                        Codigo = o.Codigo.HasValue ? o.Codigo.ToString() : string.Empty,
                        Descricao = o.Descricao
                    })
                };

            return new ConsultaTiposCargasResponse
            {
                Sucesso = false,
                Mensagem = resposta.ExceptionMessage ??
                           resposta.FalhaComunicacaoAntt.ToString(),
                Objeto = null
            };
        }

        public ConsultarTiposCargaResponse ConsultarTiposCarga(int idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var resposta = _ciotV3Service.ConsultarTiposCarga(idEmpresa);

            if (resposta.Sucesso == true)
            {
                var itensQuery = resposta.Tipos.AsQueryable();
                filters.Remove(filters.FirstOrDefault(x => x.Campo == "Ativo"));
                itensQuery = itensQuery.Where(x => x.Codigo.HasValue);

                if(filters.Any())
                    itensQuery = itensQuery.AplicarFiltrosDinamicos(filters);
                if (order != null && !string.IsNullOrWhiteSpace(order.Campo))
                    itensQuery = itensQuery.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

                resposta.Tipos = new ObservableCollection<DadosTipoCargaResponse>(itensQuery.ToList());
            }

            return resposta;
        }

        public int QuantidadeViagensAbertasPorNumeroCiotsVinculados(int idDclaracaociot)
        {
            return Repository.Where(x => x.IdDeclaracaoCiot == idDclaracaociot && x.StatusViagem != EStatusViagem.Cancelada).Count();
        }

        public FornecedorEnum? GetFornecedorViagemRota(int idViagem)
        {
            return _viagemRotaRepository.GetByViagem(idViagem).Select(x => x.FornecedorPedagio).FirstOrDefault();
        }

        public ConsultaPagamentosModel GetRelatorioPagamentosPorViagem(int idEmpresa, DateTime dataInicio, DateTime dataFim)
        {
            var pagamentoQuery = GetPagamentoPorPerfil(idEmpresa, dataInicio, dataFim);

            var items = pagamentoQuery.ToList();

            var response = new ConsultaPagamentosModel
            {
                totalItems = pagamentoQuery.Count(),
                items = items
            };

            var relatorioPagamento = new PagamentosPorViagemEventoModel()
            {
                AbastecimentoAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AbastecimentoBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AbastecimentoCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AbastecimentoBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                CargaAvulsaBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.CargaAvulsa && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                CargaAvulsaCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.CargaAvulsa && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney()
            };

            response.totais = relatorioPagamento;

            return response;
        }

        public ConsultaPagamentosModel GetPagamentosPorViagem(int idEmpresa, DateTime dataInicial, DateTime dataFinal, int take, int page,
            OrderFilters order = null, List<QueryFilters> filters = null)
        {
            var pagamentoQuery = GetPagamentoPorPerfil(idEmpresa, dataInicial, dataFinal);

            var total = pagamentoQuery.Count();

            if (filters != null)
                pagamentoQuery = pagamentoQuery.AplicarFiltrosDinamicos(filters);

            if (order != null && !string.IsNullOrWhiteSpace(order.Campo))
                pagamentoQuery = pagamentoQuery.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");
            else
            {
                pagamentoQuery = pagamentoQuery.OrderByDescending(c => c.DataHoraPagamento).ThenByDescending(c => c.DataLancamento);
            }

            var items = pagamentoQuery.Skip((page - 1) * take).Take(take).ToList();

            var response = new ConsultaPagamentosModel
            {
                totalItems = total,
                items = items
            };

            var relatorioPagamento = new PagamentosPorViagemEventoModel()
            {
                AbastecimentoAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AbastecimentoBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AbastecimentoCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AbastecimentoBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Abastecimento && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                AdiantamentoBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Adiantamento && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                TarifaAnttBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.TarifaAntt && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                SaldoBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Saldo && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                RpaBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.RPA && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                EstadiaCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Estadia && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioAberto = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Aberto)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioBloqueado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Bloqueado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                PedagioCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.Pedagio && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                CargaAvulsaBaixado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.CargaAvulsa && x.Status == EStatusViagemEvento.Baixado)
                    .Sum(x => x.ValorPagamento).FormatMoney(),
                CargaAvulsaCancelado = response.items.Where(x => x.Tipo == EConsultaPagamentosModelTipo.CargaAvulsa && x.Status == EStatusViagemEvento.Cancelado)
                    .Sum(x => x.ValorPagamento).FormatMoney()
            };

            response.totais = relatorioPagamento;

            return response;
        }

        public byte[] GerarRelatorioPagamentos(int idEmpresa, DateTime dataInicial, DateTime dataFinal, ETipoArquivoPagamento? tipoRelatorio)
        {
            var pagamentoQuery = GetPagamentoPorPerfil(idEmpresa, dataInicial, dataFinal);
            byte[] report;

            switch (tipoRelatorio)
            {
                case ETipoArquivoPagamento.PDF:
                {
                    var pagamentosListModel = new List<PagamentosModel>();
                    var pagamentosHeaderModel = new PagamentosHeaderModel
                    {
                        DataGeracao = DateTime.Now.ToString("dd/MM/yyyy"),
                        Usuario = string.Empty
                    };

                    foreach (var item in pagamentoQuery)
                    {
                        var model = new PagamentosModel
                        {
                            DataLancamento = item.DataLancamentoFormatted,
                            CteSerie = item.DocumentoCliente,
                            DataPagamento = item.DataHoraPagamentoFormatted,
                            DataLiberacao = null,
                            Evento = EnumHelper.GetDescriptionToString(item.Tipo),
                            Valor = item.ValorPagamento.FormatMoney(),
                            Status = EnumHelper.GetDescriptionToString(item.Status),
                            DataAgendamentoPagamento = item.DataHoraPagamentoFormatted,
                            Placa = item.Placa,
                            CpfMotorista = item.CpfMotorista.FormatarCpfCnpjSafe(),
                            Motorista = item.NomeMotorista,
                            CIOT = item.CIOT,
                            DocProprietario = item.CpfCnpjProprietario.FormatarCpfCnpjSafe(),
                            Proprietario = item.NomeProprietario,
                            PagoCartao = item.PagamentoCartaoFormatted,
                            IdViagemEvento = item.IdViagemEvento
                        };

                        pagamentosListModel.Add(model);
                    }

                    report = new PagamentosReport().RelatorioPagamentoPdf(pagamentosListModel, pagamentosHeaderModel);
                    break;
                }
                case ETipoArquivoPagamento.Excel:
                {
                    using var package = new XLWorkbook();
                    var worksheet = package.Worksheets.Add("Pagamentos");

                    worksheet.Cell("A1").Value = "Contratante";
                    worksheet.Cell("B1").Value = "CNPJ";
                    worksheet.Cell("C1").Value = "Filial";
                    worksheet.Cell("D1").Value = "Sigla Filial";
                    worksheet.Cell("E1").Value = "Documento Cliente";
                    worksheet.Cell("F1").Value = "ID Viagem";
                    worksheet.Cell("G1").Value = "Numero Cartão";
                    worksheet.Cell("H1").Value = "Data Lançamento";
                    worksheet.Cell("I1").Value = "Data Pagamento";
                    worksheet.Cell("J1").Value = "Tipo Evento";
                    worksheet.Cell("K1").Value = "Valor";
                    worksheet.Cell("L1").Value = "Situação";
                    worksheet.Cell("M1").Value = "Agendado Para";
                    worksheet.Cell("N1").Value = "Usuário efetivação";
                    worksheet.Cell("O1").Value = "Pago Cartão";
                    worksheet.Cell("P1").Value = "CPF Motorista";
                    worksheet.Cell("Q1").Value = "Nome Motorista";
                    worksheet.Cell("R1").Value = "Doc. Proprietário";
                    worksheet.Cell("S1").Value = "Nome Proprietário";
                    worksheet.Cell("T1").Value = "Placa";
                    worksheet.Cell("U1").Value = "IdParcela";

                    var line = 2;
                    foreach (var item in pagamentoQuery)
                    {
                        worksheet.Cell($"A{line}").Value = item.NomeEmpresa;
                        worksheet.Cell($"B{line}").Value = item.CnpjEmpresa.FormatarCpfCnpjSafe();
                        worksheet.Cell($"C{line}").Value = item.Filial;
                        worksheet.Cell($"D{line}").Value = item.Filial;
                        worksheet.Cell($"E{line}").Value = item.DocumentoCliente;
                        worksheet.Cell($"F{line}").Value = item.IdViagem.ToStringSafe();
                        worksheet.Cell($"G{line}").Value = string.Empty;
                        worksheet.Cell($"H{line}").Value = item.DataLancamentoFormatted;
                        worksheet.Cell($"I{line}").Value = item.DataHoraPagamentoFormatted;
                        worksheet.Cell($"J{line}").Value = item.TipoFormatted;
                        worksheet.Cell($"K{line}").Value = item.ValorPagamentoFormatted;
                        worksheet.Cell($"L{line}").Value = item.StatusFormatted;
                        worksheet.Cell($"M{line}").Value = string.Empty;
                        worksheet.Cell($"N{line}").Value = string.Empty;
                        worksheet.Cell($"O{line}").Value = item.PagamentoCartaoFormatted;
                        worksheet.Cell($"P{line}").Value = item.CpfMotorista.FormatarCpfCnpjSafe();
                        worksheet.Cell($"Q{line}").Value = item.NomeMotorista;
                        worksheet.Cell($"R{line}").Value = item.CpfCnpjProprietario.FormatarCpfCnpjSafe();
                        worksheet.Cell($"S{line}").Value = item.NomeProprietario;
                        worksheet.Cell($"T{line}").Value = item.Placa.FormatarPlaca();
                        worksheet.Cell($"U{line}").Value = item.IdViagemEvento;

                        line++;
                    }

                    worksheet.Column("A").AdjustToContents();
                    worksheet.Column("B").AdjustToContents();
                    worksheet.Column("C").AdjustToContents();
                    worksheet.Column("D").AdjustToContents();
                    worksheet.Column("E").AdjustToContents();
                    worksheet.Column("F").AdjustToContents();
                    worksheet.Column("G").AdjustToContents();
                    worksheet.Column("H").AdjustToContents();
                    worksheet.Column("I").AdjustToContents();
                    worksheet.Column("J").AdjustToContents();
                    worksheet.Column("K").AdjustToContents();
                    worksheet.Column("L").AdjustToContents();
                    worksheet.Column("M").AdjustToContents();
                    worksheet.Column("N").AdjustToContents();
                    worksheet.Column("O").AdjustToContents();
                    worksheet.Column("P").AdjustToContents();
                    worksheet.Column("R").AdjustToContents();
                    worksheet.Column("S").AdjustToContents();
                    worksheet.Column("T").AdjustToContents();
                    worksheet.Column("U").AdjustToContents();

                    using (var stream = new MemoryStream())
                    {
                        package.SaveAs(stream);
                        stream.Position = 0;
                        return stream.ToArray();
                    }
                }
                default:
                {
                    var pagamentosListModelExcel = new List<PagamentosExcel>();
                    foreach (var item in pagamentoQuery)
                    {
                        var modelExcel = new PagamentosExcel()
                        {
                            Cnpj = item.CnpjEmpresa.ToCnpjFormato(),
                            Documento = item.DocumentoCliente,
                            IdViagem = item.IdViagem.ToStringSafe(),
                            Lancamento = item.DataLancamentoFormatted,
                            Cartao = string.Empty,
                            Pagamento = item.DataHoraPagamentoFormatted,
                            Tipo = item.TipoFormatted,
                            Valor = item.ValorPagamento,
                            Status = item.Status.GetDescription(),
                            Login = string.Empty,
                            NomeFilial = item.Filial
                        };

                        pagamentosListModelExcel.Add(modelExcel);
                    }

                    var empresa = _empresaService.All().Where(c => c.IdEmpresa == idEmpresa)
                        .Select(c => new
                        {
                            c.Logo, c.NomeFantasia, c.CNPJ
                        }).FirstOrDefault();
                    var logo = empresa?.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);
                    var listaDataSources = new List<Tuple<string, string>>
                    {
                        new("NomeEmpresa", empresa != null ? empresa.NomeFantasia : string.Empty),
                        new("ValorTotal", new decimal(0).FormatMoney()),
                        new("Logo", logo),
                        new("CnpjEmpresa", empresa != null ? empresa.CNPJ.FormatarCpfCnpj(): string.Empty),
                        new("DataGeracao", DateTime.Now.FormatDateTimeBr()),
                        new("ValorFilial", pagamentosListModelExcel.Where(x => !string.IsNullOrWhiteSpace(x.NomeFilial))
                            .Sum(x => x.Valor).FormatMoney())
                    };

                    report = new PagamentosReport().RelatorioPagamentosExcel(pagamentosListModelExcel, listaDataSources);
                    break;
                }
            }

            return report;
        }

        public IQueryable<ConsultaPagamentosItemModel> GetPagamentoPorPerfil(int idEmpresa, DateTime dataInicio, DateTime dataFim)
        {
            var eventos = _viagemDapper.GetPagamentos(idEmpresa, dataInicio, dataFim);

             return eventos;
         }

        public DadosFilialEPagamento GetCnpjFilialEDadosPagamento(string ciot)
        {
            var declaracao = _declaracaoCiotRepository.FirstOrDefault(dc => dc.Ciot == ciot);

            if (declaracao == null) return null;

            var retornoViagem = Repository.FirstOrDefault(v => v.IdDeclaracaoCiot == declaracao.IdDeclaracaoCiot);

            if (retornoViagem == null) return null;

            #region Consultando nome da filial caso existir

            var nomeFilial = string.Empty;

            if (retornoViagem.IdFilial != null)
            {
                nomeFilial = _filialRepository
                    .Where(f => f.IdFilial == retornoViagem.IdFilial)
                    .Select(f => f.NomeFantasia)
                    .FirstOrDefault() ?? string.Empty;
            }
            else if (retornoViagem.CNPJFilial != null)
            {
                var cnpjFilial = retornoViagem.CNPJFilial.OnlyNumbers();
                nomeFilial = _filialRepository
                    .Where(f => f.CNPJ == cnpjFilial)
                    .Select(f => f.NomeFantasia)
                    .FirstOrDefault() ?? string.Empty;
            }


            #endregion

            var lMapperDadosFilial = Mapper.Map<DadosFilialEPagamento>(retornoViagem);

            #region Validando tipo de conta

            lMapperDadosFilial.TipoConta = lMapperDadosFilial.TipoConta switch
            {
                "ContaCorrente" => "Conta corrente",
                "ContaPoupanca" => "Conta poupança",
                _ => string.Empty
            };

            #endregion

            lMapperDadosFilial.TipoDeclaracao = declaracao.TipoDeclaracao == ETipoDeclaracao.Agregado ?
                "Agregado" : "Padrão";

            lMapperDadosFilial.NomeFilial = nomeFilial;

            lMapperDadosFilial.CnpjFilial = retornoViagem.CNPJFilial.FormatarCpfCnpj();

            lMapperDadosFilial.MensagemFormaPagamento = retornoViagem.FormaPagamento == EViagemFormaPagamento.Cartao ?
                "Cartão Extratta Meios de Pagamentos" : "Depósito em conta";

            return lMapperDadosFilial;
        }

        public object ConsultarUltimaViagemEmpresa(int empresaId, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var ultimaViagemEmpresa = Repository.GetAll()
                .Include(x => x.ClienteOrigem)
                .Include(x => x.ClienteDestino)
                .Where(x => x.Empresa.IdEmpresa == empresaId)
                .Where(x => x.StatusViagem == EStatusViagem.Aberto || x.StatusViagem == EStatusViagem.Baixada)
                .OrderByDescending(x => x.IdViagem)
                .Take(1);

            return new {
            totalItems = ultimaViagemEmpresa.Count(),
            items =
                ultimaViagemEmpresa.Skip((page - 1) * take)
                    .Take(take)
                    .ToList()
                    .Select(v =>
                            new
                     {
                         v.IdViagem,
                         DataEmissao = v.DataEmissao.ParaFormatoBrasileiroStr(),
                         DataLancamento = v.DataLancamento.ParaFormatoBrasileiroStr(),
                         StatusViagem = v.StatusViagem.ToString(),
                         NomeClienteOrigem = v.ClienteOrigem?.NomeFantasia,
                         NomeClienteDestino = v.ClienteDestino?.NomeFantasia
                     })
             };
        }

        public bool VincularViagemAoContrato(int idContratoCiotAgregado, int idUltimaViagem)
        {
            var ciotAgregado = _ciotAgregadoRepository
                .FirstOrDefault(x => x.IdContratoCiotAgregado == idContratoCiotAgregado);

            if (ciotAgregado == null)
                throw new InvalidOperationException("Nenhum CIOT encontrado com o código informado");

            if (!ciotAgregado.IdDeclaracaoCiot.HasValue)
                throw new InvalidOperationException($"Não existe CIOT para o contrato agregado {ciotAgregado.IdContratoCiotAgregado}");
            
            var declaracaoCiot = _declaracaoCiotRepository
                .FirstOrDefault(x => x.IdDeclaracaoCiot == ciotAgregado.IdDeclaracaoCiot);

            if (declaracaoCiot == null)
                throw new InvalidOperationException("Nenhum CIOT encontrado para essa viagem");

            var ultimaViagem = Repository.FirstOrDefault(x => x.IdViagem == idUltimaViagem);
            
            if (ultimaViagem == null)
                throw new InvalidOperationException("Viagem não encontrada na base de dados");

            if (ultimaViagem.IdDeclaracaoCiot.HasValue)
                throw new InvalidOperationException($"Já existe um CIOT para a viagem {ultimaViagem.IdViagem}");

            ultimaViagem.DeclaracaoCiot = declaracaoCiot;
            declaracaoCiot.ViagemOrigem = ultimaViagem;
            ciotAgregado.Status = EStatusContratoAgregado.Vigente;
            ciotAgregado.ResultadoDeclaracaoCiot = EResultadoDeclaracaoCiot.Sucesso;
            ciotAgregado.MensagemDeclaracaoCiot = null;

            try
            {
                _declaracaoCiotRepository.Update(declaracaoCiot);
                Repository.Update(ultimaViagem);
                _ciotAgregadoRepository.Update(ciotAgregado);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public ReciboPefDadosResponse GetDadosReciboPef(int idviagem)
        {
            return Repository.Where(v => v.IdViagem == idviagem)
                .Include(v => v.Filial)
                .Include(v => v.Proprietario)
                .Include(v => v.DeclaracaoCiot)
                .Include(v => v.ViagemEventos)
                .Include(v => v.ViagemCarretas)
                .Include(v => v.Empresa)
                .Include(v => v.ClienteDestino)
                .Include(v => v.ClienteOrigem)
                .ProjectTo<ReciboPefDadosResponse>().FirstOrDefault();
        }

        public ComprovanteCargaResponse GetDadosComprovanteCarga(int idviagem)
        {
            return _viagemDapper.GetDadosComprovanteCarga(idviagem);
        }
    }
}
﻿using System;
using ATS.Domain.Enum;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class TipoCarreta
    {
        public int IdTipoCarreta { get; set; }
        public string Nome { get; set; }
        public ECategoriaTipoCarreta Categoria { get; set; }
        public int? QtdeEixos { get; set; }
        public int? MetrosCubicos { get; set; }
        public int? Capacidade { get; set; }
        public bool? EixosEspacados { get; set; }
        public bool Ativo { get; set; } = true;
        public int? IdEmpresa { get; set; }
        public bool Destacar { get; set; } = false;
        [SkipTracking]
        public DateTime? DataHoraUltimaAtualizacao { get; set; }


        public virtual ICollection<Veiculo> Veiculos { get; set; }
        public virtual ICollection<ConjuntoCarreta> ConjuntoCarretas { get; set; }
        public virtual Empresa Empresa { get; set; }

    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IEmailService
    {
        ValidationResult TestarEmail(string emailNome, string emailEndereco, int porta, bool usaSSL, string servidor, string logon, string senha);
        EmailConfiguration GetEmailConfiguration(int? idFilial, int idEmpresa);
        ValidationResult EnviarEmail(EmailModel emailModel, bool copiaOculta = false, int? administradora = null, ConfiguracaoEnvioEmail config = null);
        void EnviarEmailComAnexo(EmailModel model);
        void EnviarEmailAsync(EmailModel emailModel, ConfiguracaoEnvioEmail config = null);
    }
}
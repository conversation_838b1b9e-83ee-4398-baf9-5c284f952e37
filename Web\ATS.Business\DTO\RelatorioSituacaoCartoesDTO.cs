﻿using System.Collections.Generic;
using ATS.Domain.Grid;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class RelatorioSituacaoCartoesDTO : RelatorioCartaoApiRequest
    {
        public int? IdEmpresa { get; set; }
        public int Take { get; set; }
        public int Page { get; set; }
        public OrderFilters Order { get; set; }
        public List<QueryFilters> Filters { get; set; }
        public string Extensao { get; set; }
        public int IdUsuario { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Models
{
    public class KeycloakSettings
    {
        public string OpenIdConnectUrl { get; set; }
        public string AuthorizationUrl { get; set; }
        public string TokenUrl { get; set; }
        public string LogoutUrl { get; set; }
        public string ScopesUrl { get; set; }
        public string UsersUrl { get; set; }
        public string ClientsUrl { get; set; }
        public string Manage2faUrl { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string PublicKey { get; set; }
        public string DefaultResourceId { get; set; }
        public string User { get; set; }
        public string Password { get; set; }
        public string RedirectUri { get; set; }
        public string DefaultClientScopesUrl
        {
            get
            {
                return ScopesUrl.Substring(0, ScopesUrl.LastIndexOf("/")) + "/default-default-client-scopes";
            }
        }
        public string OptionalClientScopesUrl
        {
            get
            {
                return ScopesUrl.Substring(0, ScopesUrl.LastIndexOf("/")) + "/default-optional-client-scopes";
            }
        }
    }
}
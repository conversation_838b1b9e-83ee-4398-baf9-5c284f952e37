<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsFaturaTagPedagio">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>44e454a4-20c5-4938-955e-daa6b833e1f8</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsFaturaTagValePedagio">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b29476b5-8311-47ad-8b2a-119991edb1e7</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsFaturaTagMensalidade">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b29476b5-8311-47ad-8b2a-119991edb1e7</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="FaturaTagPedagioDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsFaturaTagPedagio</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="DataPassagem">
          <DataField>DataPassagem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DescricaoPassagem">
          <DataField>DescricaoPassagem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Taxa">
          <DataField>Taxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusTaxa">
          <DataField>StatusTaxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusValor">
          <DataField>StatusValor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Tipo">
          <DataField>Tipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>PassagensPedagioTagDataTypeResponse</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.PassagensPedagioTagDataTypeResponse, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="FaturaTagValePedagioDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsFaturaTagValePedagio</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="ViagemId">
          <DataField>ViagemId</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DataCriacao">
          <DataField>DataCriacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Tipo">
          <DataField>Tipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Taxa">
          <DataField>Taxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusTaxa">
          <DataField>StatusTaxa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StatusValor">
          <DataField>StatusValor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>PassagensValePedagioTagDataTypeResponse</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.PassagensValePedagioTagDataTypeResponse, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="FaturaTagMensalidadeDts">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsFaturaTagMensalidade</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Serial">
          <DataField>Serial</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="MesReferente">
          <DataField>MesReferente</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Pedagio.FaturaTAG</rd:DataSetName>
        <rd:TableName>MensalidadeTagDataTypeResponse</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Pedagio.FaturaTAG.MensalidadeTagDataTypeResponse, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Rectangle Name="Rectangle1">
            <ReportItems>
              <Image Name="Image2">
                <Source>Embedded</Source>
                <Value>logoextrattalaranjapreto</Value>
                <Sizing>FitProportional</Sizing>
                <Top>0.29986cm</Top>
                <Left>8.26993cm</Left>
                <Height>2.10266cm</Height>
                <Width>3.99458cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
              <Rectangle Name="Rectangle2">
                <KeepTogether>true</KeepTogether>
                <Top>2.57891cm</Top>
                <Left>0.02496cm</Left>
                <Height>0.46271cm</Height>
                <Width>20.93431cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <BackgroundColor>#ff7a26</BackgroundColor>
                </Style>
              </Rectangle>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Left>0.54434cm</Left>
            <Height>3.04162cm</Height>
            <Width>20.95928cm</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <BottomBorder>
                <Style>None</Style>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle11">
            <ReportItems>
              <Textbox Name="Textbox2">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Emissão:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>16.45842cm</Left>
                <Height>0.52063cm</Height>
                <Width>1.66215cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox3">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Emissao.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox3</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>18.12058cm</Left>
                <Height>0.52062cm</Height>
                <Width>2.76458cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox1">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Vencimento:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>11.55544cm</Left>
                <Height>0.52062cm</Height>
                <Width>2.10312cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox4">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Vencimento.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox3</rd:DefaultName>
                <Top>0.17639cm</Top>
                <Left>13.65857cm</Left>
                <Height>0.52063cm</Height>
                <Width>2.79986cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>3.04162cm</Top>
            <Left>0.54434cm</Left>
            <Height>0.69702cm</Height>
            <Width>20.95928cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Style>None</Style>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle12">
            <ReportItems>
              <Textbox Name="Textbox5">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>CNPJ:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.73834cm</Top>
                <Left>0.31243cm</Left>
                <Height>0.52062cm</Height>
                <Width>1.22118cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox6">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Razão Social:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>0.31243cm</Left>
                <Height>0.52062cm</Height>
                <Width>2.35006cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox7">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Endereço:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>2.20264cm</Top>
                <Left>0.31243cm</Left>
                <Height>0.6cm</Height>
                <Width>1.84736cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox10">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Endereco.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox10</rd:DefaultName>
                <Top>2.20264cm</Top>
                <Left>2.15979cm</Left>
                <Height>0.6cm</Height>
                <Width>12.01157cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox11">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Cnpj.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox10</rd:DefaultName>
                <Top>0.73834cm</Top>
                <Left>1.53361cm</Left>
                <Height>0.52062cm</Height>
                <Width>19.04528cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox8">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Email:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>8.26993cm</Left>
                <Height>0.52062cm</Height>
                <Width>1.21235cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox9">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Telefone:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>14.39058cm</Left>
                <Height>0.52062cm</Height>
                <Width>1.72388cm</Width>
                <ZIndex>6</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox14">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Telefone.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox14</rd:DefaultName>
                <Top>1.4968cm</Top>
                <Left>16.11446cm</Left>
                <Height>0.52944cm</Height>
                <Width>4.46442cm</Width>
                <ZIndex>7</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox15">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!Email.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox14</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>9.48229cm</Left>
                <Height>0.52944cm</Height>
                <Width>4.73191cm</Width>
                <ZIndex>8</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox16">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!RazaoSocial.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox14</rd:DefaultName>
                <Top>1.50563cm</Top>
                <Left>2.6625cm</Left>
                <Height>0.52944cm</Height>
                <Width>5.30506cm</Width>
                <ZIndex>9</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox45">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Nº Fatura:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>2.20263cm</Top>
                <Left>14.39058cm</Left>
                <Height>0.6cm</Height>
                <Width>1.84736cm</Width>
                <ZIndex>10</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox46">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!NumeroFatura.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox10</rd:DefaultName>
                <Top>2.20263cm</Top>
                <Left>16.24005cm</Left>
                <Height>0.6cm</Height>
                <Width>4.33884cm</Width>
                <ZIndex>11</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>3.73863cm</Top>
            <Left>0.54434cm</Left>
            <Height>3.12382cm</Height>
            <Width>20.95928cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle14">
            <ReportItems>
              <Textbox Name="Textbox12">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>VALORES TRIBUTÁVEIS</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.47966cm</Top>
                <Left>3.02882cm</Left>
                <Height>0.75875cm</Height>
                <Width>4.11854cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Rectangle Name="Rectangle16">
                <ReportItems>
                  <Textbox Name="Textbox20">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Valor</Value>
                            <Style>
                              <FontSize>9pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Top>0.02284cm</Top>
                    <Left>0.02544cm</Left>
                    <Height>0.52062cm</Height>
                    <Width>2.5229cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox39">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!MensalidadeValorTotal.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>0.66712cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>2.54834cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.77173cm</Top>
                <Left>7.72536cm</Left>
                <Height>4.92582cm</Height>
                <Width>2.68945cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </TopBorder>
                  <LeftBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </LeftBorder>
                </Style>
              </Rectangle>
              <Rectangle Name="Rectangle18">
                <ReportItems>
                  <Textbox Name="Textbox19">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Quantidade</Value>
                            <Style>
                              <FontSize>9pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Top>0.04067cm</Top>
                    <Height>0.50279cm</Height>
                    <Width>3.5977cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox38">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!MensalidadeQtd.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>0.66712cm</Top>
                    <Height>0.512cm</Height>
                    <Width>3.55612cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.77173cm</Top>
                <Left>4.12766cm</Left>
                <Height>4.92583cm</Height>
                <Width>3.5977cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </TopBorder>
                  <LeftBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </LeftBorder>
                  <RightBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </RightBorder>
                </Style>
              </Rectangle>
              <Rectangle Name="Rectangle25">
                <ReportItems>
                  <Textbox Name="Textbox18">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Descrição</Value>
                            <Style>
                              <FontSize>9pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Top>0.02284cm</Top>
                    <Left>0.07071cm</Left>
                    <Height>0.52062cm</Height>
                    <Width>4.05743cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox29">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Mensalidade</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>0.66712cm</Top>
                    <Left>0.07071cm</Left>
                    <Height>0.49417cm</Height>
                    <Width>4.05743cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.77173cm</Top>
                <Left>0.02496cm</Left>
                <Height>4.92583cm</Height>
                <Width>4.12814cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                  </TopBorder>
                  <RightBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                  </RightBorder>
                </Style>
              </Rectangle>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>6.86245cm</Top>
            <Left>0.54434cm</Left>
            <Height>6.72041cm</Height>
            <Width>10.44242cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <RightBorder>
                <Color>#d9d9d9</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle15">
            <ReportItems>
              <Textbox Name="Textbox13">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>VALORES NÂO TRIBUTÁVEIS</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox1</rd:DefaultName>
                <Top>0.47969cm</Top>
                <Left>3.24376cm</Left>
                <Height>0.75875cm</Height>
                <Width>5.17804cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Rectangle Name="Rectangle8">
                <ReportItems>
                  <Textbox Name="Textbox21">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Descrição</Value>
                            <Style>
                              <FontSize>9pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Height>0.52062cm</Height>
                    <Width>4.05743cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox25">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Passagens Pedágio</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>0.64427cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>4.05743cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox26">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Estorno de Pedágio</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>1.13843cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>4.05743cm</Width>
                    <ZIndex>2</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox27">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Emissão de Vale Pedágio</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>1.6326cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>4.05743cm</Width>
                    <ZIndex>3</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox28">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Estorno de Vale Pedágio</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>2.12676cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>4.05743cm</Width>
                    <ZIndex>4</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.79458cm</Top>
                <Height>4.92583cm</Height>
                <Width>4.05743cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                  </TopBorder>
                  <RightBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                  </RightBorder>
                </Style>
              </Rectangle>
              <Rectangle Name="Rectangle9">
                <ReportItems>
                  <Textbox Name="Textbox22">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Quantidade</Value>
                            <Style>
                              <FontSize>9pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Top>0.00001cm</Top>
                    <Left>0.02646cm</Left>
                    <Height>0.52062cm</Height>
                    <Width>3.30667cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox30">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!PassagensPedagioQtd.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>0.64428cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>3.33313cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox31">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!EstornoPedagioQtd.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>1.13845cm</Top>
                    <Left>0.02646cm</Left>
                    <Height>0.49417cm</Height>
                    <Width>3.30667cm</Width>
                    <ZIndex>2</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox32">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!PassagensValePedagioQtd.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>1.6326cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>3.33313cm</Width>
                    <ZIndex>3</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox33">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!EstornoValePedagioQtd.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>2.12677cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>3.33313cm</Width>
                    <ZIndex>4</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.79458cm</Top>
                <Left>4.03097cm</Left>
                <Height>4.92583cm</Height>
                <Width>3.35959cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </TopBorder>
                  <LeftBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </LeftBorder>
                  <RightBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </RightBorder>
                </Style>
              </Rectangle>
              <Rectangle Name="Rectangle10">
                <ReportItems>
                  <Textbox Name="Textbox23">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>Valor</Value>
                            <Style>
                              <FontSize>9pt</FontSize>
                              <FontWeight>Bold</FontWeight>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox1</rd:DefaultName>
                    <Top>0.00001cm</Top>
                    <Left>0.03528cm</Left>
                    <Height>0.52062cm</Height>
                    <Width>3.12629cm</Width>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox34">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!PassagensPedagioValorTotal.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>0.64429cm</Top>
                    <Left>0.03528cm</Left>
                    <Height>0.49417cm</Height>
                    <Width>3.12629cm</Width>
                    <ZIndex>1</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox35">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!EstornoPedagioTotal.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>1.13846cm</Top>
                    <Height>0.49417cm</Height>
                    <Width>3.16157cm</Width>
                    <ZIndex>2</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox36">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!PassagensValePedagioValorTotal.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>1.63261cm</Top>
                    <Left>0.03528cm</Left>
                    <Height>0.49417cm</Height>
                    <Width>3.12629cm</Width>
                    <ZIndex>3</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <Textbox Name="Textbox37">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Parameters!EstornoValePedagioTotal.Value</Value>
                            <Style>
                              <FontSize>8pt</FontSize>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Top>2.12678cm</Top>
                    <Left>0.03528cm</Left>
                    <Height>0.49417cm</Height>
                    <Width>3.12628cm</Width>
                    <ZIndex>4</ZIndex>
                    <Style>
                      <Border>
                        <Style>None</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </ReportItems>
                <KeepTogether>true</KeepTogether>
                <Top>1.79459cm</Top>
                <Left>7.35528cm</Left>
                <Height>4.92582cm</Height>
                <Width>3.19185cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <TopBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </TopBorder>
                  <LeftBorder>
                    <Color>#d9d9d9</Color>
                    <Style>Solid</Style>
                    <Width>1pt</Width>
                  </LeftBorder>
                </Style>
              </Rectangle>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>6.86245cm</Top>
            <Left>10.95915cm</Left>
            <Height>6.72044cm</Height>
            <Width>10.54713cm</Width>
            <ZIndex>4</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <LeftBorder>
                <Color>#d9d9d9</Color>
              </LeftBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle13">
            <KeepTogether>true</KeepTogether>
            <Top>15.4696cm</Top>
            <Left>0.54169cm</Left>
            <Height>0.66146cm</Height>
            <Width>20.96459cm</Width>
            <ZIndex>5</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox17">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Mensalidade TAG</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>16.13106cm</Top>
            <Left>0.54434cm</Left>
            <Height>2.05768cm</Height>
            <Width>20.96194cm</Width>
            <ZIndex>6</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox24">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Passagens Pedágio</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>19.54749cm</Top>
            <Left>0.54169cm</Left>
            <Height>2.41873cm</Height>
            <Width>20.96459cm</Width>
            <ZIndex>7</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
                <Width>1pt</Width>
              </TopBorder>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox40">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Vale Pedágio</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>25.04004cm</Top>
            <Left>0.54731cm</Left>
            <Height>1.98713cm</Height>
            <Width>20.95929cm</Width>
            <ZIndex>8</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </TopBorder>
              <LeftBorder>
                <Style>Solid</Style>
              </LeftBorder>
              <RightBorder>
                <Style>Solid</Style>
              </RightBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle19">
            <ReportItems>
              <Textbox Name="Textbox170">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Nenhuma emissão de vale pedágio encontrada no período</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox169</rd:DefaultName>
                <Top>0.61186cm</Top>
                <Left>5.80719cm</Left>
                <Height>0.6cm</Height>
                <Width>8.69267cm</Width>
                <Visibility>
                  <Hidden>=CountRows("FaturaTagValePedagioDts") &gt; 0</Hidden>
                </Visibility>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>28.40355cm</Top>
            <Left>0.54168cm</Left>
            <Height>1.62688cm</Height>
            <Width>20.95929cm</Width>
            <ZIndex>9</ZIndex>
            <Visibility>
              <Hidden>=CountRows("FaturaTagValePedagioDts") &gt; 0</Hidden>
            </Visibility>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle20">
            <ReportItems>
              <Textbox Name="Textbox169">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Nenhum registro de passagem encontrada no período</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox169</rd:DefaultName>
                <Top>0.63311cm</Top>
                <Left>6.03324cm</Left>
                <Height>0.6cm</Height>
                <Width>8.18128cm</Width>
                <Visibility>
                  <Hidden>=CountRows("FaturaTagPedagioDts") &gt; 0</Hidden>
                </Visibility>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>23.48372cm</Top>
            <Left>0.54434cm</Left>
            <Height>1.55632cm</Height>
            <Width>20.96226cm</Width>
            <ZIndex>10</ZIndex>
            <Visibility>
              <Hidden>=CountRows("FaturaTagPedagioDts") &gt; 0</Hidden>
            </Visibility>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
                <Width>1pt</Width>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle21">
            <ReportItems>
              <Textbox Name="Textbox41">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>www.extratta.com.br</Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                          <Color>#fb7f33</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox41</rd:DefaultName>
                <Top>0.7351cm</Top>
                <Left>4.99975cm</Left>
                <Height>0.6cm</Height>
                <Width>8.46694cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox42">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>0800 600 0096</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox42</rd:DefaultName>
                <Top>0.4176cm</Top>
                <Left>16.52506cm</Left>
                <Height>0.6cm</Height>
                <Width>4.36009cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox44">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value><EMAIL></Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style />
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox42</rd:DefaultName>
                <Top>1.12343cm</Top>
                <Left>16.52506cm</Left>
                <Height>0.6cm</Height>
                <Width>4.3601cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Image Name="Image1">
                <Source>Embedded</Source>
                <Value>email</Value>
                <Sizing>FitProportional</Sizing>
                <Top>1.14149cm</Top>
                <Left>15.62011cm</Left>
                <Height>0.58194cm</Height>
                <Width>0.72856cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
              <Image Name="Image5">
                <Source>Embedded</Source>
                <Value>telefone</Value>
                <Sizing>FitProportional</Sizing>
                <Top>0.4176cm</Top>
                <Left>15.62011cm</Left>
                <Height>0.5475cm</Height>
                <Width>0.72856cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
              <Image Name="Image3">
                <Source>Embedded</Source>
                <Value>logoextrattalaranjapreto</Value>
                <Sizing>FitProportional</Sizing>
                <Top>0.31176cm</Top>
                <Left>0.05258cm</Left>
                <Height>1.58231cm</Height>
                <Width>3.33606cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Image>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>30.03043cm</Top>
            <Left>0.54699cm</Left>
            <Height>1.89408cm</Height>
            <Width>20.95929cm</Width>
            <ZIndex>11</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle22">
            <ReportItems>
              <Tablix Name="Tablix2">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>2.59605cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>3.50275cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.88855cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.44902cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.01827cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.67988cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.50193cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.39508cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.77362cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox120">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Data Passagem</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox43">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Praça</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox43</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox61">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox122">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Tipo</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox49">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>TAG Serial</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox49</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox53">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox51">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox124">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox57">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox57</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DataPassagem">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DataPassagem.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DataPassagem</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DescricaoPassagem">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DescricaoPassagem.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DescricaoPassagem</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Tipo">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Tipo.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Tipo</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Serial1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Serial.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Serial1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Taxa">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Taxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Taxa</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusValor">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusValor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusValor</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusTaxa">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusTaxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusTaxa</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes2" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagPedagioDts</DataSetName>
                <Top>0.07938cm</Top>
                <Left>0.08264cm</Left>
                <Height>1.2cm</Height>
                <Width>20.80515cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
            </ReportItems>
            <Top>21.96622cm</Top>
            <Left>0.54168cm</Left>
            <Height>1.5175cm</Height>
            <Width>20.96194cm</Width>
            <ZIndex>12</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
                <Width>1pt</Width>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
                <Width>1pt</Width>
              </BottomBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle23">
            <ReportItems>
              <Tablix Name="Tablix1">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>4.92797cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>4.91323cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>5.66873cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>5.32263cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox117">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox117</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox119">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>TAG Serial</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox121">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Período</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox123">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Serial">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Serial.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Serial</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="MesReferente">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!MesReferente.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>MesReferente</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagMensalidadeDts</DataSetName>
                <Top>0.08819cm</Top>
                <Left>0.05523cm</Left>
                <Height>1.2cm</Height>
                <Width>20.83256cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
            </ReportItems>
            <Top>18.18874cm</Top>
            <Left>0.54169cm</Left>
            <Height>1.35875cm</Height>
            <Width>20.96459cm</Width>
            <ZIndex>13</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle24">
            <ReportItems>
              <Tablix Name="Tablix3">
                <TablixBody>
                  <TablixColumns>
                    <TablixColumn>
                      <Width>2.80703cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.73605cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.59917cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>1.93693cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.80727cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.25995cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.29956cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.30025cm</Width>
                    </TablixColumn>
                    <TablixColumn>
                      <Width>2.11403cm</Width>
                    </TablixColumn>
                  </TablixColumns>
                  <TablixRows>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox126">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Data da Emissão</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox119</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox157">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Código Viagem</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox157</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox77">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Placa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox61</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox127">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Tipo</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox121</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox50">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>TAG Serial</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox49</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox54">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox53</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox52">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox51</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox128">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Valor</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox123</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Textbox58">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>Status Taxa</Value>
                                      <Style>
                                        <FontSize>9pt</FontSize>
                                        <FontWeight>Bold</FontWeight>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Textbox57</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <LeftBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </LeftBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                    <TablixRow>
                      <Height>0.6cm</Height>
                      <TablixCells>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="DataCriacao">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!DataCriacao.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>DataCriacao</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="ViagemId">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!ViagemId.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>ViagemId</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Placa1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Placa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Placa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Tipo1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Tipo.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Tipo1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Serial2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Serial.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Serial2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Valor2">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Valor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Valor2</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="Taxa1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!Taxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>Taxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusValor1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusValor.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusValor1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <RightBorder>
                                  <Color>#d9d9d9</Color>
                                  <Style>Solid</Style>
                                  <Width>1pt</Width>
                                </RightBorder>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                        <TablixCell>
                          <CellContents>
                            <Textbox Name="StatusTaxa1">
                              <CanGrow>true</CanGrow>
                              <KeepTogether>true</KeepTogether>
                              <Paragraphs>
                                <Paragraph>
                                  <TextRuns>
                                    <TextRun>
                                      <Value>=Fields!StatusTaxa.Value</Value>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                    </TextRun>
                                  </TextRuns>
                                  <Style>
                                    <TextAlign>Center</TextAlign>
                                  </Style>
                                </Paragraph>
                              </Paragraphs>
                              <rd:DefaultName>StatusTaxa1</rd:DefaultName>
                              <Style>
                                <Border>
                                  <Style>None</Style>
                                </Border>
                                <VerticalAlign>Middle</VerticalAlign>
                                <PaddingLeft>2pt</PaddingLeft>
                                <PaddingRight>2pt</PaddingRight>
                                <PaddingTop>2pt</PaddingTop>
                                <PaddingBottom>2pt</PaddingBottom>
                              </Style>
                            </Textbox>
                          </CellContents>
                        </TablixCell>
                      </TablixCells>
                    </TablixRow>
                  </TablixRows>
                </TablixBody>
                <TablixColumnHierarchy>
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixColumnHierarchy>
                <TablixRowHierarchy>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Detalhes3" />
                    </TablixMember>
                  </TablixMembers>
                </TablixRowHierarchy>
                <DataSetName>FaturaTagValePedagioDts</DataSetName>
                <Left>0.02199cm</Left>
                <Height>1.2cm</Height>
                <Width>20.86024cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                </Style>
              </Tablix>
            </ReportItems>
            <Top>27.02716cm</Top>
            <Left>0.54731cm</Left>
            <Height>1.37639cm</Height>
            <Width>20.95929cm</Width>
            <ZIndex>14</ZIndex>
            <Style>
              <Border>
                <Color>#d9d9d9</Color>
                <Style>Solid</Style>
              </Border>
              <LeftBorder>
                <Color>Black</Color>
              </LeftBorder>
              <RightBorder>
                <Color>Black</Color>
              </RightBorder>
            </Style>
          </Rectangle>
          <Textbox Name="Textbox63">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=Parameters!ValorTotalTributaveis.Value</Value>
                    <Style>
                      <FontSize>8pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox62</rd:DefaultName>
            <Top>14.20432cm</Top>
            <Left>8.49209cm</Left>
            <Height>0.56854cm</Height>
            <Width>2.54834cm</Width>
            <ZIndex>15</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox62">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Total:</Value>
                    <Style>
                      <FontSize>9pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox62</rd:DefaultName>
            <Top>14.20432cm</Top>
            <Left>4.96141cm</Left>
            <Height>0.56854cm</Height>
            <Width>3.53068cm</Width>
            <ZIndex>16</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="Textbox66">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="Vigência: " + Parameters!Vigencia.Value</Value>
                    <Style>
                      <FontSize>8pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox62</rd:DefaultName>
            <Top>13.58286cm</Top>
            <Left>0.54169cm</Left>
            <Height>0.62146cm</Height>
            <Width>20.96459cm</Width>
            <ZIndex>17</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <VerticalAlign>Middle</VerticalAlign>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Rectangle Name="Rectangle3">
            <ReportItems>
              <Textbox Name="Textbox65">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Total:</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.02646cm</Top>
                <Left>4.1017cm</Left>
                <Height>0.54208cm</Height>
                <Width>3.27139cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox64">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!ValorTotalNaoTributaveis.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.02646cm</Top>
                <Left>7.37309cm</Left>
                <Height>0.54208cm</Height>
                <Width>3.08745cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>14.20432cm</Top>
            <Left>11.04043cm</Left>
            <Height>0.56854cm</Height>
            <Width>10.46617cm</Width>
            <ZIndex>18</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <LeftBorder>
                <Color>#d9d9d9</Color>
              </LeftBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle5">
            <KeepTogether>true</KeepTogether>
            <Top>14.20432cm</Top>
            <Left>0.54434cm</Left>
            <Height>0.56854cm</Height>
            <Width>10.47806cm</Width>
            <ZIndex>19</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
              <LeftBorder>
                <Color>Black</Color>
                <Width>1pt</Width>
              </LeftBorder>
              <RightBorder>
                <Color>#d9d9d9</Color>
                <Width>1pt</Width>
              </RightBorder>
            </Style>
          </Rectangle>
          <Rectangle Name="Rectangle17">
            <ReportItems>
              <Textbox Name="Textbox75">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Valor Estornado:</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>0.08264cm</Left>
                <Height>0.66146cm</Height>
                <Width>3.27957cm</Width>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox76">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!EstornoPago.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>3.36221cm</Left>
                <Height>0.66146cm</Height>
                <Width>1.94092cm</Width>
                <ZIndex>1</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox73">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Valor Não Estornado:</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>5.30313cm</Left>
                <Height>0.66146cm</Height>
                <Width>3.27957cm</Width>
                <ZIndex>2</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox74">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!EstornoNaoPago.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>8.5827cm</Left>
                <Height>0.66146cm</Height>
                <Width>1.94092cm</Width>
                <ZIndex>3</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox71">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Valor Pago:</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>10.52362cm</Left>
                <Height>0.66146cm</Height>
                <Width>3.27957cm</Width>
                <ZIndex>4</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox72">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!TotalPago.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>13.80319cm</Left>
                <Height>0.66146cm</Height>
                <Width>1.94092cm</Width>
                <ZIndex>5</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox69">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Valor Não Pago:</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>15.74411cm</Left>
                <Height>0.66146cm</Height>
                <Width>3.27957cm</Width>
                <ZIndex>6</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox70">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!TotalNaoPago.Value</Value>
                        <Style>
                          <FontSize>8pt</FontSize>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.03528cm</Top>
                <Left>19.02368cm</Left>
                <Height>0.66146cm</Height>
                <Width>1.94092cm</Width>
                <ZIndex>7</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox67">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=Parameters!ValorFatura.Value</Value>
                        <Style>
                          <FontSize>9pt</FontSize>
                          <Color>=IIf(Parameters!ValorFatura.Value &gt; 0, "Green", "Red")</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.69674cm</Top>
                <Left>17.87715cm</Left>
                <Height>0.66146cm</Height>
                <Width>3.07394cm</Width>
                <ZIndex>8</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
              <Textbox Name="Textbox68">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>Total a Pagar:</Value>
                        <Style>
                          <FontWeight>Bold</FontWeight>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>Textbox62</rd:DefaultName>
                <Top>0.69674cm</Top>
                <Left>14.57048cm</Left>
                <Height>0.66146cm</Height>
                <Width>3.30667cm</Width>
                <ZIndex>9</ZIndex>
                <Style>
                  <Border>
                    <Style>None</Style>
                  </Border>
                  <VerticalAlign>Middle</VerticalAlign>
                  <PaddingLeft>2pt</PaddingLeft>
                  <PaddingRight>2pt</PaddingRight>
                  <PaddingTop>2pt</PaddingTop>
                  <PaddingBottom>2pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <KeepTogether>true</KeepTogether>
            <Top>14.77286cm</Top>
            <Left>0.54168cm</Left>
            <Height>1.3582cm</Height>
            <Width>20.9646cm</Width>
            <ZIndex>20</ZIndex>
            <Style>
              <Border>
                <Style>Solid</Style>
              </Border>
              <TopBorder>
                <Color>#d9d9d9</Color>
              </TopBorder>
              <BottomBorder>
                <Color>#d9d9d9</Color>
              </BottomBorder>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>32.29433cm</Height>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Body>
      <Width>21.98285cm</Width>
      <Page>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>22.5cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="Cnpj">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Cnpj</Prompt>
    </ReportParameter>
    <ReportParameter Name="RazaoSocial">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>RazaoSocial</Prompt>
    </ReportParameter>
    <ReportParameter Name="Email">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Email</Prompt>
    </ReportParameter>
    <ReportParameter Name="Telefone">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Telefone</Prompt>
    </ReportParameter>
    <ReportParameter Name="Endereco">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Endereco</Prompt>
    </ReportParameter>
    <ReportParameter Name="Vigencia">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Vigencia</Prompt>
    </ReportParameter>
    <ReportParameter Name="Emissao">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Emissao</Prompt>
    </ReportParameter>
    <ReportParameter Name="Vencimento">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>Vencimento</Prompt>
    </ReportParameter>
    <ReportParameter Name="MensalidadeQtd">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>MensalidadeQtd</Prompt>
    </ReportParameter>
    <ReportParameter Name="MensalidadeValorTotal">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>MensalidadeValorTotal</Prompt>
    </ReportParameter>
    <ReportParameter Name="ValorTotalNaoTributaveis">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ValorTotalNaoTributaveis</Prompt>
    </ReportParameter>
    <ReportParameter Name="PassagensValePedagioQtd">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>PassagensValePedagioQtd</Prompt>
    </ReportParameter>
    <ReportParameter Name="PassagensValePedagioValorTotal">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>PassagensValePedagioValorTotal</Prompt>
    </ReportParameter>
    <ReportParameter Name="PassagensPedagioQtd">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>PassagensPedagioQtd</Prompt>
    </ReportParameter>
    <ReportParameter Name="PassagensPedagioValorTotal">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>PassagensPedagioValorTotal</Prompt>
    </ReportParameter>
    <ReportParameter Name="EstornoValePedagioQtd">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>EstornoValePedagioQtd</Prompt>
    </ReportParameter>
    <ReportParameter Name="EstornoValePedagioTotal">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>EstornoValePedagioTotal</Prompt>
    </ReportParameter>
    <ReportParameter Name="EstornoPedagioQtd">
      <DataType>Integer</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>EstornoPedagioQtd</Prompt>
    </ReportParameter>
    <ReportParameter Name="EstornoPedagioTotal">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>EstornoPedagioTotal</Prompt>
    </ReportParameter>
    <ReportParameter Name="ValorTotalTributaveis">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ValorTotalTributaveis</Prompt>
    </ReportParameter>
    <ReportParameter Name="EstornoPago">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>EstornoPago</Prompt>
    </ReportParameter>
    <ReportParameter Name="EstornoNaoPago">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>EstornoNaoPago</Prompt>
    </ReportParameter>
    <ReportParameter Name="TotalPago">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>TotalPago</Prompt>
    </ReportParameter>
    <ReportParameter Name="TotalNaoPago">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>TotalNaoPago</Prompt>
    </ReportParameter>
    <ReportParameter Name="ValorFatura">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ValorFatura</Prompt>
    </ReportParameter>
    <ReportParameter Name="NumeroFatura">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>6</NumberOfColumns>
      <NumberOfRows>6</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Cnpj</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>RazaoSocial</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Email</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Telefone</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Endereco</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Vigencia</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>Emissao</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>Vencimento</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>MensalidadeQtd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>MensalidadeValorTotal</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>ValorTotalTributaveis</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>PassagensValePedagioQtd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>PassagensValePedagioValorTotal</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>PassagensPedagioQtd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>PassagensPedagioValorTotal</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>EstornoValePedagioQtd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>EstornoValePedagioTotal</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>2</RowIndex>
          <ParameterName>EstornoPedagioQtd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>EstornoPedagioTotal</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>ValorTotalNaoTributaveis</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>EstornoPago</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>EstornoNaoPago</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>TotalPago</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>3</RowIndex>
          <ParameterName>TotalNaoPago</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>5</ColumnIndex>
          <RowIndex>4</RowIndex>
          <ParameterName>ValorFatura</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>5</RowIndex>
          <ParameterName>NumeroFatura</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="logoextrattalaranjapreto">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAYGBgYHBgcICAcKCwoLCg8ODAwODxYQERAREBYiFRkVFRkVIh4kHhweJB42KiYmKjY+NDI0PkxERExfWl98fKcBBgYGBgcGBwgIBwoLCgsKDw4MDA4PFhAREBEQFiIVGRUVGRUiHiQeHB4kHjYqJiYqNj40MjQ+TERETF9aX3x8p//CABEIAGIA0gMBIgACEQEDEQH/xAAxAAEAAgMBAAAAAAAAAAAAAAAABAUCAwYBAQEAAwEBAAAAAAAAAAAAAAAAAgMEBQH/2gAMAwEAAhADEAAAAuqAAPB7ExrlNats4h6AAAAAAAAY5RY+7cuZ359XQ6NtBbTjs0XWPZKzOhzg9AAAAAAAAAU8HoqHFuQJsOFthf6JGzn4VkHyyF/op9RexYeRlYx6outcqsLCLTTjdhNpy3y3QCXYcX2Z6ABT2HL06LLZDiZdPYewpu7n8pKt6+Xkqh6LMrY1puMKS4zNFX0lSVm+5yPeevfDCl6CEVHYV9gAPPYsVbBnbMHQ11/V1WimLfUs+KaNeUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAC/9oADAMBAAIAAwAAACEAASsAAAAAAAAAAL+osIAAAAAAAAAC/IsgQijiCQwAABgOqjQxBSCxSgAfIUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//EAAL/2gAMAwEAAgADAAAAEPPPI3/PPPPPPPPAzpilfPPPPPPPPKKd2QCBKLJOHNPKFIKnNMJDKANFPPd9JlPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP/8QAIxEAAgEEAgICAwAAAAAAAAAAAgMEAAUSEzAyAREQQiJQUv/aAAgBAgEBPwD5O4RALHy4KAxMch4nEQLMgDIqC+ltAWJwGrhM0xcg7H0q3wmTJn5dex0IiI4jx3yF5WzcHUqjPZIHwo+w1AijGV6+3JdZ
K9en7FUFjBkZlrxGksFqxMeN7NaTP+aFc+VLyMGeiZVyt2uMBJDr3qxulAZpatmP6f8A/8QAJxEAAgEDAwQABwAAAAAAAAAAAgMEAAESBSIyBhETMBAUMUJQYrL/2gAIAQMBAT8A+IafNMchjsohISxL1IFZuADPESv9af0vggmKfmVaVAKVLsN+Ac61WaEKLt5cAorkRd7+vp7UvKj5dp7g/miWiNm0Qwy3nWpTSlyTL7LcPZoUEms81+AVJhg5Jhmzd+1OUaXGo+Q+tIeRoB35UEqBDh4gxfYbVpesXKQwZB7C4Vry4rredTF5fh//xAA2EAACAQMCAwQHBwUBAAAAAAABAgMABBEFEhMhMRAiQVEgMkBCUmGRFBUjU2JxgSQzUGChwf/aAAgBAQABPwL/AAL3cSNtOa+3RfOo5BIuR7IWA6muLH8YoOp6HsuJeFHnx8OyNC7BRSIEUAeyXUPFjx4+FYqCThuD9aDArmrm44snyHSgatItq7j1Ps17BhuIPHr2PcSLblBQNWUPFfJ9UdjyJGpZjgCjrNrn3vpUNxFNHvQ5q31CCeTYud3zq4v4Ldgr5z5CtRvuHEFTIdhmtNvkwI3LF2ap9RtoG2sTnyFW+pwTyBFVs0+r2qMV7xq2v7e4O1T3vI1ql7wl4aMQ/wD5Wm3ybUiYsZGNapfsDwonII9atPvIpFSEbiwXnmptUtoXKHcSKg1K2nbaCQfn6LoHUg1JGY3KmsVw24mwePSoIRDGFHZrUp4iReGMmgNI4O0t3sda0QnjyDw21fI1pfCVPHmK062a4mNzN58q1tU/CbHeNWFjAsUMu3v4zWxbnVGU9C9R2ltCd6IF5dac6MGbPM5qPYL+Pgk7d4xWtqgeNgO8etWVjAiQybe/t61qsaC8AXq3X+ags7eDvIuDip20niuX5setT8D7Qv2cnHKh09G8nh4wQrk1mP8AL/7UEsCzqCnPwPbqoxfd71cChDopGeL/ANq2gs7d
GmiPIjrU7zX8rsg7qDlWjT7o2hPu9K1z+7EP01bXMDxKEcZVOYrShvvnb9zWruy2nL3mxVlDp7Q7pn73lmrJUbUhs9UEkVrR/qIx+ira4glQCNwcKOVXzAanlugK1d3SNZTPE+fD61p8Vi6sZ35+VbYW1BFh9TeMejcS8KMtT5Zix6mg/wCHurmTmrOfixc/WHXsuLSG4XDj9jX3JB+Y9PZRtbrBubaKt7WK3j2J/NRadBDNxULZ8qu7OG5AD+HQ1badFb8TDMdwxVrYxWxYoTzqaGOVCjjIr7lt/jerawt7dtyg58zV1Zw3QG/qPEVaWMdruKknNXWnwXLbjkN5ioLKGGFovWDdc0dFts8nYVBpttAwcZLDxPo3UnFf5DpTLS2Ja1z73UUFqBjE4b60pBGfZbnfswo61wJfgNRWrs43Ly7Li1O/ci9a4EvwGrTiAFWX/Zf/xAApEAEAAgIBAwMDBAMAAAAAAAABABEhMVEQQWEgcZFAgbFQocHRYPDx/9oACAEBAAE/If0FhYTdE8PxmedfSF2AT/uTUj0FO5geZaquVndsmoIPpLEaZ+yYKJSRTdaEAXwl3LNHtdFj7+E9Vy/TcuX68TFOP3ITQMu+B6K0ndfPiEIvtLC1BnMPKY3yRJXBeFfEepMui6uC6PjGhh93+5jJ47DUJJQuTGJWJhpo4lrZwqZl8abDSjW+cudyxhsjEfzJ3hDNRpoh1j0Gr9JBYSdiDTySiU6Zjlaq+6dq7by9FicPumGuHz7uCHv+DAxoXivuTJsZeT/UHT2S+CIPnr7s2JbZ4JeyFMu0woaylu5/aoMwyFt4J2i5bd2BXgHzYdz2qrAsDNLcypsyi+zctS+PTiMDd1V9oc6KbDssur65EtvuhuCbDwuMQz1t4Ixm+jgP5l5c5eyJ4V3943IF7hU8GnzMVGND9mVVZtqlVK5/FJG4R+TEUFzuJyj72JghTI7RSKDhNYh+ZAV6Rc32OWXgtLWEBcS3
tzKa3Z/t0PZZo2T/AFCU2uJy1GwNOU7blZPLbGYDCKwbmSZfXhi9KabZYxYs3UcYluSuy5WJNOyMphpXxBiiK5JWdinJC43CJ1Ca9CgZnwl0LZY0ZXDB7DkgoafpdtLg+3Trig5b7wAqbuG1c9ODwA0/S10rpUqV/hX/xAAoEAEAAgICAAUEAgMAAAAAAAABABEhMUFRIEBhcYEQkbHBYNGh4fD/2gAIAQEAAT8Q8Vnm187IBBYA2xTJXlLclytSz+nLnH9Ny5fM+VQtuxavLCabeXo5YF1UjygfuzRa9hSJp6lueVDkhxLuGK7j5Y0++36OnRYHfAeVBUVWRVU12+YZezhJc7RKoWRaLwuIAFQheWqgiPq7GZPqBSdJ3HucUyq2QN+GSDS4wtnaLorftFTM5pJuxb2RGUiwl7GFwkHi84ImZe4kSx86KRcdoNFrGhsXFEzj4vnXCF5rJBcbej2GKZWFJBHY/qEUnWL7jbMZmtbjuHIqlRhiyX/kwNBX/wBxgwJu3X9aO7F9LyyjS2sQSa0masZZ4ZXY5XcCgip64vzM43YbEr6Fdsw/W3HgJWzTruIjQJ4+ruWZvtARl2SLDvBc9fVv38J9qQ0LRT/bcpudGp8Pn6VMr3ueECa+u5lJFlY7sx/r/pqtMeLZ+Wzb9PlRlbqkEU2QkS/xtEcbN9xZTPycJgFoPr4Yxg5kK57FYNVkmXAlyqAk6Lvtwf8Ar3TB5DV2TY5fDTI1qQmCozdzhL+5lil2x6lRxAP4+X0uNbtUi22r2jERpYg5UtZIxcrk37MZ2lqYxYysuhMsA0BUBdsQmi7xC/7PCPZ6ymuIsogd65gOiGFnXqgrPqHB0qXt4qsIBlS9tlcT22Oxzs0r4B4FCtRUB2B32zZiAQr+7T7xFSImESX81oQV8QsTyrZelpshphcEv6CAA1VEK0Z54QDHJCtv5KL/2Q==</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="email">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAADAklEQVR4nO2YXYhNURTHf2MYjJpiyOcUMuGFlM80SIrSJB5EISTNgyij5nXypORh8IAopXyVKFLyYKIJU/OEIaSUfH/M+LyMma09rcMyM+fcc8697t1uZ9W/mc5Za+3/2neftf97Q2KJJZZYYnkyUyCgYApJOUAiU6RsIfOANw6QiYv3wELvO5kEPHSAlImIJ8BUr4hp8nck0OwAORMSzcL5dw2fgOXyYBBwxgGSJg0uAKXCeQnQ5nWtn0CNvCgG9jtA1vjgCNBfuG4Cfsjzv5wagH7itAPodIC4EXQB9cKtSP7X73sFnJUlZm0l8MWR9rpGOJUAJ/rw6TOwCRgugXOAV3ks4h1QJVyGAtd8/HwTPAIqJcFE4EEeingMTBYOE4DWAN/ARG+B+ZJoGHA9h0XcBEbI2LOAl2n80yb8BqyWhAOBkzko4hwwWMZcEfI7DZXYtuftAR0jm2hQnXMr0BEyLvYgWyIMEnaytmUwWZEHPK921aXAxywU8RmoVuridIwcsQa+rXTOdOBZBkW8AGZKrnLgRsw8sQm0iJyxNj5Na/RDq8R60qglAz6xgu6qPaZIbVaNEXI0SozOUQncyVUhVnmWqWVlZ3VtGvkQJINWyWZr9wqk7R7/l4XYDrVLzd564Ku8s+KyVs3ubh/B2SnvvBy1ys/m2sAf2xmxK4Zyeg0slgEGAAd8/A6q72YBcEmO0RYXlWYqFl+/HCXityiCzgt1EquQxKNDdJWrwFj8bZz4BOVoAsYo/1uZFnJUrWWruZ6HnJ0PwF5gtqz5UlHR+4D2CG25SkmjQ3EKScnBCiUVvkdYr9lCB1CneKwL0F29HtjNba4E2l/jWB4KMD1wChginGbI7UlgIVamj5KACsduVe6rqx+rAK74FXJYOpK1ZXIyM46hXY7fXuerV+27+7yx
Ue0BdaJEjaPoAvYoFV7tXQd5F3Rlsmub/wSXlcSZ4nUDey6+5wA5ExFPlbTpPkq2OUAqLuynsRlZc6YAQL4JJIXQ4xdJLLHEEkuMXNsvflcw/bT5iToAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
    <EmbeddedImage Name="telefone">
      <MIMEType>image/jpeg</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAEIAAABCCAYAAADjVADoAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEUElEQVR4nO2ba4hWRRjHf67miuJ6DRNEW9Q2KtEUhD64mpdIEETUwkUwJUvFD+Y9SXC9VIiy+kGJrdASYT8oah9ys9SgMlG6WISK4mXzgkppaaaWuzLwuLweZs5lznk9c17fPzzsh53znJnfzpl55plnoaiiinJIXYDJwKIUbC4wHChJG8Js4AbQlLL9DFSkBeENBwDk2nmgm81AustgFgKvAu0iPNsW+NOBwXttXRQAzwCbgTseJ2pg40P6eNmBQevsTJjOvwDsAu76OFK/GxnC1+sODFpn//t1ugLYHcFZg0x9P73pwKBNptVU4LaFs+pCAjETaLR0dhPoWQggBljOhFyrswShtrFZQKW0O2Vo9y+wEhgBTAS+8vGp1rZxwEvAelkLQoE4mABZNZtejAjikmYvLwNOanyrQeWqBbBN47PW8MkHghic4DQ7BpRGALE05C5zwNCuv8anKUg6GwRibcLf3LIIIKoMnR7habfV0K7M0+46Zu0LAvF1wiBuaeJ4E4jVhk6v8LT7TT4Fr4ZrfPbRtHsMuBAE4veEQTRJHBIGxD/A8562g2UX8rad52nXGTiiaVcPtMlppwCu8elrsxryAEJZeQgQ92fQx3JE1oXyubZHjtNrfP7Cyo4Dq4B3gO8D+tmsX/MEYkxIEGlbszbl6QXDsgaiKg/O//acPzIBojVwLmHnb3sWtikODNgUrT6gGQk6r9XkBJMM2pK0w14QJcB3MZ3+B7zmdZzj/xcHBu616brOPhvz4LUEfw2UtaPJEdvpl81eHsNxX8Kl/eoDToP5tisSX7Ty62gbOTjZvKAf4dVZ1g1T/K+LVIcAg2LaU0DLsJ0capmgqSG6ghZp1Y9307yQqbEAoRK5YyO+xy++UCfJCaSsUuBHCxjXJUcQF8QJ4DkcUYXl9VyDXAaF
0TTN858DnXBM0ywXzkMhUvxKTwJ/5awHq1y4oDWpzhLGbgnfg9QbmCO7gtPqKN+sDYztQft11lQBXLWEscXl6W6j0TEiwg2GnGNmNd8ShLJPC+0z+TAGjDrJKBeESoFvYsD4zJNlzrQ6Sh2SLYx9EYKm0TKTVKS7Xz7PKNU6eVc3SZvbwjgqMYRJaqf5yPDsWanCcUY9Y96LXPEJpKpDPL9JZqczMcalGDBuSSifu71OktRfmOdVWcErrmzPA4A/YsBokoumT4BvLfMhB10J0/sBF2PCSCoX+XTaMPoApx2AcVtKoVJVD9kR0obRGLL0Ma963DLDlbSpeCV1dQC+cO1KLy21kqq2tEDccWVbva/pCZQv2tgPOKhKiSQfJoi3cFTlwE8PCYLKqLXH8WP8+wFV/0mYqtLNhEYFFITF3S2eIEPqLpVySYPYSAZVAiww1Fba2LWszQbdOeXLBECo+9SC0ETgsiUEFbwVlLpKbqIxQgS52LUoMkmpRMteHyDqwmmH1Gc9EuolFXvvAR/IzykB/z5VVFFFEUv3AE3Xgc5ltdLfAAAAAElFTkSuQmCC</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>bbea77d8-8b66-418e-a67d-c5baf0b8e7b9</rd:ReportID>
</Report>
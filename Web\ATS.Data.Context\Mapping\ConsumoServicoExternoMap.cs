using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ConsumoServicoExternoMap: EntityTypeConfiguration<ConsumoServicoExterno>
    {
        public ConsumoServicoExternoMap()
        {
            ToTable("CONSUMO_SERVICO_EXTERNO");

            HasKey(t => t.IdConsumoServicoExterno);
            
            Property(t => t.Descricao).HasMaxLength(500);
            Property(t => t.Uri).HasMaxLength(500);
            
        }
    }
}
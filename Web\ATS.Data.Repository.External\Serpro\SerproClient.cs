using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI.WebControls;
using ATS.Data.Repository.External.Extratta.Models;
using ATS.Data.Repository.External.Logs;
using ATS.Data.Repository.External.Serpro.DTO;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Infra;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using NLog;
using SistemaInfo.MicroServices.Rest.Infra.ApiClient;

namespace ATS.Data.Repository.External.Serpro
{
    public class SerproClient : LoggableClient, ISerproClient
    {
        private string BaseUrl { get; }
        private readonly HttpClient Client;
        private readonly JsonSerializerSettings SerializerOptions;
        private readonly JsonSerializerSettings DeserializerOptions;

        public SerproClient(HttpContext httpContext) : base(httpContext)
        {
            Client = new()
            {
                DefaultRequestHeaders =
                {
                    Authorization = new AuthenticationHeaderValue(ConfigurationManager.AppSettings["SERPRO_API_KEY"])
                },
                Timeout = TimeSpan.FromSeconds(15)
            };

            SerializerOptions = new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() };
            DeserializerOptions = new JsonSerializerSettings { Error = (_, ev) => ev.ErrorContext.Handled = true };

            BaseUrl = ConfigurationManager.AppSettings["SERPRO_URL"];
        }

        public IntegracaoResult<ValidacaoSerproResponse> ValidarPortador(ValidacaoSerproRequest request)
        {
            return Task.Run(async () => await ValidarPortadorAsync(request, CancellationToken.None)).ConfigureAwait(false).GetAwaiter().GetResult();
        }

        #region Métodos Async

        private async Task<IntegracaoResult<ValidacaoSerproResponse>> ValidarPortadorAsync(ValidacaoSerproRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var url = BaseUrl + "api/services/validation/serpro";

                var body = JsonConvert.SerializeObject(request, SerializerOptions);

                var result = await PostAsync(url, string.Empty, string.Empty, body, cancellationToken);

                if (!result.Success)
                {
                    try
                    {
                        var responseError = JsonConvert.DeserializeObject<ValidacaoSerproResponse>(result.Value, DeserializerOptions);
                        return IntegracaoResult<ValidacaoSerproResponse>.Valid(responseError);
                    }
                    catch (Exception)
                    {
                        //
                    }
                    return IntegracaoResult<ValidacaoSerproResponse>.Error("Erro ao obter o resultado da validação.");
                }

                if (string.IsNullOrWhiteSpace(result.Value))
                    return IntegracaoResult<ValidacaoSerproResponse>.Error("O objeto retornou vazio!");

                var response = JsonConvert.DeserializeObject<ValidacaoSerproResponse>(result.Value, DeserializerOptions);

                //response.StatusCode = httpResponse.StatusCode;
                //response.Success = httpResponse.IsSuccessStatusCode;
                return IntegracaoResult<ValidacaoSerproResponse>.Valid(response);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return IntegracaoResult<ValidacaoSerproResponse>.Error(e.Message);
            }
        }

        #endregion

        #region Auxiliar

        private async Task<IntegracaoResult<string>> PostAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content
            };

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
            {
                request.Headers.Authorization = new AuthenticationHeaderValue(tokenType, token);
            }

            LogPublishRequest(Client, request, url, BaseUrl);

            var httpResponse = await Client.SendAsync(request, cancellationToken);

            LogPublishResponse(httpResponse);

            var result = await httpResponse.Content.ReadAsStringAsync();

            return IntegracaoResult<string>.Valid(result);
        }

        #endregion
    }
}
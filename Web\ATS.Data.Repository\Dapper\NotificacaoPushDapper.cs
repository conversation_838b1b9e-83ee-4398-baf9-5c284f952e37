﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using System.Data;
using Dapper;
using ATS.CrossCutting.IoC;
using System.Linq;
using System.Collections;

namespace ATS.Data.Repository.Dapper
{
    public class NotificacaoPushDapper : DapperFactory<NotificacaoPushModel>, INotificacaoPushDapper
    {
        public NotificacaoPushModel ExecutarRegra(string query)
        {
            try
            {
                using (IDbConnection dbConnection = this.GetConnection())
                {
                    NotificacaoPushModel notificacaoPushModel = new NotificacaoPushModel();
                    //Valida DDL comandos e comandos de atualização de dados
                    if (query.ToLower().Contains("delete") 
                        || query.ToLower().Contains("update") 
                        || query.ToLower().Contains("drop")
                        || query.ToLower().Contains("alter")
                        || query.ToLower().Contains("create"))
                    {
                        notificacaoPushModel.Sucesso = false;
                        return notificacaoPushModel;
                    }
                    
                    var retCosulta = (IDictionary<string, object>)dbConnection.Query(query).First();

                    List<object> parmetros = new List<object>();

                    var first = retCosulta.Values.First();

                    var valuesNotFirst = retCosulta.Values.Where(x => x != first);

                    parmetros = valuesNotFirst.ToList();

                    //primeiro retorno deve ser sempre booleano. 
                    notificacaoPushModel.Sucesso = Convert.ToBoolean(first);
                    notificacaoPushModel.Retornos = parmetros;
                    
                    return notificacaoPushModel;
                }
            } catch (Exception ex) {
                throw ex;
            }
        }
    }

}
﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Dapper;

namespace ATS.Domain.Interface.Database.Common
{
    public interface IRepositoryDapper<T> : IDisposable
    {
        IDbConnection GetConnection();
        IEnumerable<T> RunSelect(string select);
        IEnumerable<T> RunSelect(string select, object param);
        IEnumerable<UserDTO> RunSelect<UserDTO>(string select);
        IEnumerable<T> RunSelect(string select, DynamicParameters parameters);
        IEnumerable<UserDTO> RunSelect<UserDTO>(string select, DynamicParameters parameters);
        IQueryable<UserDTO> RunQuery<UserDTO>(string @select, DynamicParameters @params);
        List<T> RunSelectToList(string select, object param = null);
    }
}

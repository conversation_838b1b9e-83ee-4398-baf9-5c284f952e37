﻿using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class BloqueioFinanceiroTipoApp : AppBase, IBloqueioFinanceiroTipoApp
    {
        private readonly IBloqueioFinanceiroTipoService _bloqueioFinanceiroTipoService;

        public BloqueioFinanceiroTipoApp(IBloqueioFinanceiroTipoService bloqueioFinanceiroTipoService)
        {
            _bloqueioFinanceiroTipoService = bloqueioFinanceiroTipoService;
        }

        public IQueryable<BloqueioFinanceiroTipo> GetAll()
        {
            return _bloqueioFinanceiroTipoService.GetAll();
        }
    }
}
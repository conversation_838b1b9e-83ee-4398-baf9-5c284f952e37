﻿using Newtonsoft.Json;

namespace ATS.Domain.Models
{
    public class RequestBaseDomainModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Token { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJAplicacao { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJEmpresa { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DocumentoUsuarioAudit { get; set; } = "00000000000";
    }
}
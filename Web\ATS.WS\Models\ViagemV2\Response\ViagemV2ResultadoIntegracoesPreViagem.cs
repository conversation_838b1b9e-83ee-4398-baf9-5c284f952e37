namespace ATS.WS.Models.ViagemV2.Response
{
    public class ViagemV2ResultadoIntegracoesPreViagem
    {
        public bool Sucesso { get; set; }

        public string Mensagem { get; set; }

        public ViagemV2ResultadoIntegracoesPreViagem RetornarSucesso()
        {
            Sucesso = true;
            Mensagem = "Integração realizada com sucesso.";

            return this;
        }

        public ViagemV2ResultadoIntegracoesPreViagem RetornarFalha(string mensagem)
        {
            Sucesso = false;
            Mensagem = mensagem;

            return this;
        }
        
        public ViagemV2ResultadoIntegracoesPreViagem RetornarSucesso(string msg)
        {
            Sucesso = true;
            Mensagem = msg;

            return this;
        }
    }
}
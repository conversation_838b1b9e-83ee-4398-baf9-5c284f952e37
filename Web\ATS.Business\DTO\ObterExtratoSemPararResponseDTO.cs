﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.DTO
{
    public class ObterExtratoSemPararResponseDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get => _mensagem;
            set => _mensagem = value?.Trim();
        }
        private string _mensagem { get; set; }
        public ObterExtratoSemPararObjetoResponseDTO Objeto { get; set; }
    }

    public class ObterExtratoSemPararObjetoResponseDTO
    {
        public List<ObterExtratoSemPararItemResponseDTO> Itens { get; set; }

        public decimal SaldoInicial
        {
            get
            {
                return Itens.OrderBy(c => c.DataOperacao).Select(c => c.Saldo<PERSON>ia).FirstOrDefault() ?? 0;
            }
        }

        public decimal SaldoFinal
        {
            get
            {
                return Itens.OrderBy(c => c.DataOperacao).Select(c => c.Saldo<PERSON>ia).LastOrDefault() ?? 0;
            }
        }

        public int TotalItens => Itens.Count;
    }

    public class ObterExtratoSemPararItemResponseDTO
    {
        public long? Numero { get; set; }
        public DateTime? DataOperacao { get; set; }
        public DateTime? DataCompra { get; set; }
        public string Acao { get; set; }
        public string Descricao { get; set; }
        public decimal? ValorOperacao { get; set; }
        public long? NumeroViagem { get; set; }
        public DateTime? DataInicioVigencia { get; set; }
        public DateTime? DataFimVigencia { get; set; }
        public DateTime? DataPassagem { get; set; }
        public string CnpjCpfTransportador { get; set; }
        public string NomeTransportador { get; set; }
        public string Tag { get; set; }
        public string Placa { get; set; }
        public string NomeRota { get; set; }
        public decimal? SaldoDia { get; set; }
        public string NomePraca { get; set; }
        public string NomeRodovia { get; set; }
        public string NomeConcessionaria { get; set; }
        public string Fatura { get; set; }
        public DateTime? DataFatura { get; set; }
        public int? TipoVp { get; set; }
        public int? Status { get; set; }
    }
}
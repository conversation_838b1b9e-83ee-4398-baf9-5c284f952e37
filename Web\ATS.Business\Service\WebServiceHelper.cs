﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net;
using System.Web.Configuration;
using System.Web.Script.Serialization;
using System.Xml;
using System.Linq;
using System.Device.Location;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using NLog;

namespace ATS.Domain.Service
{
    internal class WebServiceHelper
    {
        public static Logger Logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// Realizar a chamada ao webservice
        /// </summary>
        /// <typeparam name="T">Objeto de retorno</typeparam>
        /// <param name="url">URL</param>
        /// <param name="object">Conteúdo serializado em JSON</param>
        /// <returns></returns>
        private T InvocarWebService<T>(string url, object @object) where T : class
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.ContentType = "application/json; charset=utf-8";
            request.Method = "POST";

            using (Stream requestStream = request.GetRequestStream())
            {
                using (StreamWriter streamWriter = new StreamWriter(requestStream))
                    streamWriter.Write(new JavaScriptSerializer().Serialize(@object));
            }

          
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {
                if (response.StatusCode != HttpStatusCode.OK)
                    throw new Exception($"Server error (HTTP {response.StatusCode}: {response.StatusDescription}).");

                using (Stream responseStream = request.GetResponse().GetResponseStream())
                {
                    using (StreamReader streamReader = new StreamReader(responseStream))
                    {
                        string jsonResponse = streamReader.ReadToEnd();
                        if (!string.IsNullOrWhiteSpace(jsonResponse))
                            return (T)new JavaScriptSerializer().Deserialize(jsonResponse, typeof(T));
                    }
                }
            }

            return null;
        }

        public string GetAdressFromCoordinate(string aLatitude, string aLongitude)
        {
            if (string.IsNullOrWhiteSpace(aLatitude) || string.IsNullOrWhiteSpace(aLongitude) || aLatitude == "0" || aLongitude == "0")
            {
                return "";
            }

            aLongitude = aLongitude.Replace(',', '.');
            aLatitude = aLatitude.Replace(',', '.');
            
            var latLng = (aLatitude + "," + aLongitude).Trim();
            var lApiUrl = string.Format("https://maps.googleapis.com/maps/api/geocode/xml?latlng=" + latLng + "&sensor=true&key=" + GetKeyGoogle());

            AtualizaConsumoServicoExterno(new ConsumoServicoExterno()
            {
                Uri = lApiUrl,
                Descricao = "Atualiza posicionamento motorista",
                OrigemConsumoServicoExterno = EOrigemConsumoServicoExterno.PosicionamentoMotorista,
                DataConsumo = DateTime.Now
            });
            
            
            XmlDocument doc = new XmlDocument();
            try
            {
                doc.Load(lApiUrl);
                XmlNode element = doc.SelectSingleNode("//GeocodeResponse/status");
                if (element != null && element.InnerText == "ZERO_RESULTS")
                {
                    return ("");
                }
                var enderecoXml = doc.SelectSingleNode("//GeocodeResponse/result/formatted_address");
                if (enderecoXml != null)
                {
                    var endereco = enderecoXml.InnerText;
                    return endereco;
                }
            }
            catch
            {
                return "";
            }

            return "";
        }

        private void AtualizaConsumoServicoExterno(ConsumoServicoExterno consumoServicoExterno)
        {
            try
            {
                /*var atualizaConsumoServicoExterno = WebConfigurationManager.AppSettings.Get("ATUALIZA_CONSUMO_SERVICO_EXTERNO");
                if(atualizaConsumoServicoExterno == "true")
                    new ConsumoServicoExternoService().Add(consumoServicoExterno);*/
            }
            catch (Exception)
            {
                // ignored
            }
        }

        public GoogleMatrixRespose GetDistanciaPorLatitudeLongitude(decimal? latitudeOrigem, decimal? longitudeOrigem, decimal? latitudeDestino, decimal? longitudeDestino, string googlekey)
        {

            var path = WebConfigurationManager.AppSettings.Get("URL_API_GOOGLE");
            var key = WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");
            if (!string.IsNullOrEmpty(googlekey))
                key = googlekey;

            var pathUriCompleta = $"{path}json?" +
                $"origins={latitudeOrigem.ToString().Replace(",",".")},{longitudeOrigem.ToString().Replace(",", ".")}" +
                $"&destinations={latitudeDestino.ToString().Replace(",", ".")},{longitudeDestino.ToString().Replace(",", ".")}" +
                $"&mode=driving" +
                $"&language=pr-BR&" +
                $"key={key}";

            var lClient = new WebClient();
            lClient.Encoding = System.Text.Encoding.UTF8;
            string lGeoCodeInfo;
            try
            {
                var lUri = new Uri(pathUriCompleta);
                lGeoCodeInfo = lClient.DownloadString(lUri);
            }
            catch
            {
                var uri = new Uri(pathUriCompleta);
                lGeoCodeInfo = lClient.DownloadString(uri);
            }

            if (!string.IsNullOrEmpty(lGeoCodeInfo))
            {
                var json = new JavaScriptSerializer().Deserialize<GoogleMatrixResponse>(lGeoCodeInfo);

                if (json.Status.ToUpper() == "OK" && json.rows != null)
                {
                    var queryElements = new List<Element>();
                    foreach (var row in json.rows)
                    {
                        foreach (var element in row.elements)
                        {
                            if(element.distance != null)
                                if (element.distance.value > 0)
                                    queryElements.Add(element);
                        }
                    }
                    
                    var lower = queryElements.Min(o => o.duration.value);

                    var lowerPath = queryElements.FirstOrDefault(x => x.status == "OK" && x.duration.value == lower);
                    if(lowerPath != null)
                        return new GoogleMatrixRespose()
                        {
                            Distancia = lowerPath.distance.value,
                            TempoDuracao = lowerPath.duration.value
                        };
                }
            }

            return null;
        }

        public Results GetPontoReferenciaLatitudeLongitude(decimal? latitude, decimal? longitude, decimal raio, string keyGoogle, bool limpaLocalizacao)
        {
            var retorno = new Results();
            var path = WebConfigurationManager.AppSettings.Get("URL_API_PONTO_REFERENCIA_GOOGLE");
            var key = WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");

            if (!string.IsNullOrEmpty(keyGoogle))
                key = keyGoogle;

            var uri = new Uri($"{path}json?location={latitude.ToString().Replace(",", ".")},{longitude.ToString().Replace(",",".")}&radius={raio*1000}&key={key}");
            var lClient = new WebClient();
            var lGeoCodeInfo = "";

            try
            {
                lClient.Encoding = System.Text.Encoding.UTF8;
                lGeoCodeInfo = lClient.DownloadString(uri);

                if (!string.IsNullOrEmpty(lGeoCodeInfo))
                {
                    var json = new JavaScriptSerializer().Deserialize<GooglePlaceResponse>(lGeoCodeInfo);
                    if (json.Status.ToUpper() == "OK" && json.Results != null)
                    {
                        var nearest = (from h in json.Results
                                       let geo = new GeoCoordinate { Latitude = Double.Parse(h.Geometry.Location.Lat.Replace(".", ",")), Longitude = Double.Parse(h.Geometry.Location.Lng.Replace(".", ",")) }
                                       orderby geo.GetDistanceTo(new GeoCoordinate(Double.Parse(latitude.ToString().Replace(".", ",")), Double.Parse(longitude.ToString().Replace(".", ","))))
                                       select h).Where(s => !s.Types.Contains("route") && !s.Types.Contains("locality") && !s.Types.Contains("political")).Take(10);

                        if (nearest != null)
                            return nearest.FirstOrDefault();
                    }
                }
            }
            catch(Exception)
            {
                // ignored
            }

            return null;
        }

        private string GetKeyGoogle()
        {
            try
            {
                return WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");
            }
            catch (Exception)
            {
                return "";
            }
        }
        public Coordinate GetCoordinateFromAdress(string aAdress, EOrigemConsumoServicoExterno origemConsumoServicoExterno)
            {
            try
            {
                
                var lApiUrl = @"https://maps.googleapis.com/maps/api/geocode/json?key=" + GetKeyGoogle() + 
                              "&sensor=false&address=" + aAdress + "&sensor=false";

                AtualizaConsumoServicoExterno(new ConsumoServicoExterno()
                {
                    Uri = lApiUrl,
                    Descricao = origemConsumoServicoExterno.GetDescription(),
                    OrigemConsumoServicoExterno = origemConsumoServicoExterno,
                    DataConsumo = DateTime.Now
                });
                
                var lClient = new WebClient();
                string lGeoCodeInfo;
                try
                {
                    var lUri = new Uri(lApiUrl);
                    lGeoCodeInfo = lClient.DownloadString(lUri);
                }
                catch
                {
                    var uri = new Uri(lApiUrl);
                    lGeoCodeInfo = lClient.DownloadString(uri);
                }

                if (!string.IsNullOrEmpty(lGeoCodeInfo))
                {
                    var json = new JavaScriptSerializer().Deserialize<GoogleGeoCodeResponse>(lGeoCodeInfo);
                    if (json.Status == "OK")
                    {
                        string lLatitude = json.Results[0].Geometry.Location.Lat;
                        string lLongitude = json.Results[0].Geometry.Location.Lng;

                        return new Coordinate()
                        {
                            Status = true,
                            Latitude = decimal.Parse(lLatitude, CultureInfo.InvariantCulture),
                            Longitude = decimal.Parse(lLongitude, CultureInfo.InvariantCulture)
                        };
                    }
                    else
                    {
                        Logger.Error($"Erro ao consultar coordenadas para o endereço {aAdress}, resposta: {lGeoCodeInfo}");
                    }
                }
                return new Coordinate()
                {
                    Status = false,
                    Latitude = 0,
                    Longitude = 0
                };
            }
            catch (Exception e)
            {
                Logger.Error($"Exceção ao consultar coordenadas para o endereço {aAdress}, resposta: {e.GetBaseException().Message}");
                return new Coordinate()
                {
                    Status = false,
                    Latitude = 0,
                    Longitude = 0
                };
            }
        }
        
         public Coordinate GetCoordinateFromAdress(string aAdress, EOrigemConsumoServicoExterno origemConsumoServicoExterno, string key)
            {
            try
            {
                var lApiUrl = @"https://maps.googleapis.com/maps/api/geocode/json?key="+ key +"&sensor=false&address=" + aAdress + "&sensor=false";

                AtualizaConsumoServicoExterno(new ConsumoServicoExterno()
                {
                    Uri = lApiUrl,
                    Descricao = origemConsumoServicoExterno.GetDescription(),
                    OrigemConsumoServicoExterno = origemConsumoServicoExterno,
                    DataConsumo = DateTime.Now
                });
                
                var lClient = new WebClient();
                string lGeoCodeInfo;
                try
                {
                    var lUri = new Uri(lApiUrl);
                    lGeoCodeInfo = lClient.DownloadString(lUri);
                }
                catch
                {
                    var uri = new Uri(lApiUrl);
                    lGeoCodeInfo = lClient.DownloadString(uri);
                }

                if (!string.IsNullOrEmpty(lGeoCodeInfo))
                {
                    var json = new JavaScriptSerializer().Deserialize<GoogleGeoCodeResponse>(lGeoCodeInfo);
                    if (json.Status == "OK")
                    {
                        string lLatitude = json.Results[0].Geometry.Location.Lat;
                        string lLongitude = json.Results[0].Geometry.Location.Lng;

                        return new Coordinate()
                        {
                            Status = true,
                            Latitude = decimal.Parse(lLatitude, CultureInfo.InvariantCulture),
                            Longitude = decimal.Parse(lLongitude, CultureInfo.InvariantCulture)
                        };
                    }
                }
                return new Coordinate()
                {
                    Status = false,
                    Latitude = 0,
                    Longitude = 0
                };
            }
            catch (Exception)
            {
                return new Coordinate()
                {
                    Status = false,
                    Latitude = 0,
                    Longitude = 0
                };
            }
        }
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Enum;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IEstadoApp : IAppBase<Estado>
    {
        Estado Get(int id);
        ValidationResult Add(Estado entity);
        ValidationResult Update(Estado entity);
        IQueryable<Estado> Consultar(string nome = null);
        Estado GetPorIBGE(int nIBGE);
        Estado GetPorSigla(string uf);
        ValidationResult Inativar(int idEstado);
        ValidationResult Reativar(int idEstado);
        IQueryable<Estado> All();
        int? GetIdEstadoPorSigla(int idPais, string sigla);
        int GetIdEstadoPorIbge(int codigoIbge);
        List<Estado> GetTodos();

        /// <summary>
        /// Obtém estado pela sigla
        /// </summary>
        /// <param name="sigla"></param>
        /// <returns></returns>
        Estado GetEstadoBySigla(string sigla);

        IQueryable<Estado> ConsultarPorIdPais(int idPais);

        int? GetIdEstado(string uf);

        /// <summary>
        /// Retorna a lista de estados atualizados a partir da data
        /// </summary>
        /// <param name="dataBase">Data base para filtro</param>
        /// <returns></returns>
        IQueryable<Estado> GetEstadosAtualizados(DateTime dataBase, ERegiaoBrasil? Regiao);

        object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        bool ValidarIbgeCadastrado(int ibge);
    }
}
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;

namespace ATS.Domain.DTO
{
    public class PixExtratoGridResponse
    {
        // ReSharper disable once InconsistentNaming
        public int totalItems { get; set; }
        // ReSharper disable once InconsistentNaming
        public List<PixExtratoGridResponseItem> items { get; set; }
    }

    public class PixExtratoGridResponseItem
    {
        public string Valor { get; set; }
        public string Tipo { get; set; }
        public string DocumentoOrigem { get; set; }
        public string DocumentoDestino { get; set; }
        public string NomeOrigem { get; set; }
        public string NomeDestino { get; set; }
        public string DataTransferencia { get; set; }
        public string Codigo { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Menu;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;

namespace ATS.Domain.Service
{
    public class MenuService : ServiceBase, IMenuService
    {
        private readonly IMenuRepository _repository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IEmpresaModuloService _empresaModuloService;
        private readonly IAutorizacaoEmpresaRepository _autorizacaoEmpresaRepository;
        private readonly IModuloMenuRepository _moduloMenuRepository;
        private readonly IModuloMenuService _moduloMenuService;

        public MenuService(IMenuRepository repository, IEmpresaModuloService empresaModuloService, IAutorizacaoEmpresaRepository autorizacaoEmpresaRepository,
            IModuloMenuRepository moduloMenuRepository, IModuloMenuService moduloMenuService, IUsuarioRepository usuarioRepository)
        {
            _repository = repository;
            _empresaModuloService = empresaModuloService;
            _autorizacaoEmpresaRepository = autorizacaoEmpresaRepository;
            _moduloMenuRepository = moduloMenuRepository;
            _moduloMenuService = moduloMenuService;
            _usuarioRepository = usuarioRepository;
        }
        
        /// <summary>
        /// Método utilizado para buscar Menu.
        /// </summary>
        /// <param name="id">Id de Menu</param>
        /// <returns>Entidade Menu</returns>
        public Menu Get(int id)
        {
            return
                _repository.Find(x => x.IdMenu == id).Include(x => x.ModuloMenus).FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para incluir Menu.
        /// </summary>
        /// <param name="menu">Entidade de Menu</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Menu menu)
        {
            try
            {
                _repository.Add(menu);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para alterar Menu.
        /// </summary>
        /// <param name="menu">Entidade de Menu</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Menu menu)
        {
            try
            {
                _repository.Update(menu);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        public List<GrupoUsuarioMenuGrid> GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idModulo, int idEmpresa, bool verificarAutorizacaoEmpresa)
        {
            return _repository.GetMenusDisponiveisPorEmpresaModuloAutorizacao(idModulo, idEmpresa, verificarAutorizacaoEmpresa);
        }

        /// <summary>
        /// Método utilizado para listar menus ativos
        /// </summary>
        /// <returns>IQueryable de Menu</returns>
        public IQueryable<Menu> All()
        {
            return _repository.All().Where(x => x.Ativo);
        }

        /// <summary>
        /// Método utilizado para consultar Menu.
        /// </summary>
        /// <param name="descricao">Descrição de Menu</param>
        /// <returns>IQueryable de MenuGrid</returns>
        public IQueryable<MenuGrid> Consultar(string descricao)
        {
            if (descricao == null)
                descricao = string.Empty;

            return _repository.Consultar(descricao);
        }

        /// <summary>
        /// Método utilizado para listar Menu por Grupo de Usuario.
        /// </summary>
        /// <param name="idGrupoUsuario">Id de Grupo de Usuario</param>
        /// <returns>IQueryable de Menu</returns>
        public IQueryable<Menu> GetPorIdGrupoUsuario(int idGrupoUsuario)
        {
            return _repository.GetPorIdGrupoUsuario(idGrupoUsuario);
        }

        /// <summary>
        /// Retorna a lista de menus permitidos de acordo com a especificação do do grupo de usuário e perfil do usuário
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public List<ModuloEstruturaModel> GetMenusPermitidos(int idUsuario)
        {
            #region Realizar a consulta

            var empresaModulos = new List<EmpresaModulo>();
            var usuario = _usuarioRepository.Get(idUsuario);
            
            if (usuario.IdEmpresa.HasValue)
                empresaModulos = _empresaModuloService.ListarModuloPorIdEmpresa(usuario.IdEmpresa).ToList();

            List<ModuloEstruturaModel> listModuloEstruturaModel = new List<ModuloEstruturaModel>();

            var menus = _repository.GetMenusPermissao(_usuarioRepository.GetGrupoUsuario(idUsuario)).ToList();

            var tempMenu = new List<Menu>();
            if ((usuario.Perfil != EPerfil.Administrador) && (usuario.Perfil != EPerfil.Motorista) && (usuario.Perfil != EPerfil.Proprietario) && usuario.Perfil != EPerfil.Estabelecimento)
            {
                var autorizacoes = _autorizacaoEmpresaRepository.Find(x => x.IdEmpresa == usuario.IdEmpresa && x.HasPermissao);

                foreach (var menu in menus) 
                {
                    if (menu.ModuloMenus?.Count(x =>
                        empresaModulos.Count(y => y.IdModulo == x.IdModulo) > 0) > 0
                        && autorizacoes.Any(x => x.IdMenu == menu.IdMenu) || menu.IsMenuPai)
                        tempMenu.Add(menu);
                }
                
                menus = tempMenu;
            }

            string perfil = usuario.Perfil.GetHashCode().ToString();

            menus = menus.Where(m => (m.Perfis.Split(',')
                                        .Select(i => i).Contains(perfil)
                                        || string.IsNullOrWhiteSpace(m.Perfis) || m.Perfis == perfil
                                     ) && !m.IsMenuMobile).OrderBy(x => x.Sequencia).ToList();
            
            foreach (Menu menu in menus)
            {
                foreach (var menuLista in menu.MenuLista.ToList())
                {
                    if (!menuLista.Perfis.Contains(perfil))
                        menu.MenuLista.Remove(menuLista);
                }
                
                var moduloMenu = _moduloMenuService.GetPorIdMenu(menu.IdMenu, empresaModulos?.Select(x => x.IdModulo).ToList());

                foreach (var item in moduloMenu)
                {
                    if (!item.Modulo.Ativo) continue;
                    var moduloMenuModel = new ModuloEstruturaModel
                    {
                        NomeModulo = item.Modulo.Descricao,
                        IdModulo = item.IdModulo,
                        Icone = item.Modulo.ClassIcon ?? "",
                        IdMenu = item.IdMenu,
                        Sequencia = item.Modulo.Sequencia,
                        MenuEstruturaModel = new List<MenuEstruturaModel>()
                    };
                    if (listModuloEstruturaModel.Any(x => x.NomeModulo == item.Modulo.Descricao))
                        continue;

                    var menusPais =
                        menus.Where(
                            m => m.IdMenuPai == null
                            || m.ModuloMenus.Any(mm => mm.IdModulo == item.IdModulo)).Select(s => new
                            {
                                IdMenuPai = s.IdMenu,
                                DescricaoMenuPai = s.Descricao,
                                LinkMenuPai = s.Link
                            });

                    foreach (var menupai in menusPais)
                    {
                        var lMenu = menus.Where(s => s.IdMenuPai == menupai.IdMenuPai
                                                     && ((s.ModuloMenus != null &&
                                                          s.ModuloMenus.Any(mm => mm.IdModulo == item.IdModulo)) || s.ModuloMenus == null)).OrderBy(x => x.Sequencia)
                                                     .Select(s => new MenuEstruturaModel()
                                                     {
                                                         IdMenu = s.IdMenu,
                                                         IdMenuPai = menupai.IdMenuPai,
                                                         Link = s.Link,
                                                         LinkNovo = s.LinkNovo,
                                                         Funcao = s.Funcao,
                                                         IsNovoATS = !string.IsNullOrEmpty(s.LinkNovo),
                                                         Menu = s.Descricao
                                                     }).ToList();

                        if (!lMenu.Any()) 
                            continue;
                        
                        var lMenuPaiViewModel = new MenuEstruturaModel()
                        {
                            Menu = menupai.DescricaoMenuPai,
                            Link = menupai.LinkMenuPai,
                            Menus = lMenu,
                            IdMenu = menupai.IdMenuPai
                        };

                        moduloMenuModel.MenuEstruturaModel.Add(lMenuPaiViewModel);
                    }
                    if (moduloMenuModel.MenuEstruturaModel.Count > 0)
                        listModuloEstruturaModel.Add(moduloMenuModel);
                }
            }

            #endregion

            listModuloEstruturaModel = listModuloEstruturaModel.OrderBy(x => x.Sequencia).ToList();

            return listModuloEstruturaModel;
        }

        /// <summary>
        /// Retorna os itens do menu organizados na hierarquia 
        /// </summary>
        /// <param name="idGrupoUsuario"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuarioMenuGrid> GetArvoreMenuPorIdGrupoUsuario(int idGrupoUsuario)
        {
            return _repository.GetArvoreMenuPorIdGrupoUsuario(idGrupoUsuario);
        }

        /// <summary>
        /// Verifica se o usuário possui permissão para acessar o determinado menu
        /// </summary>
        /// <param name="idMenu"></param>
        /// <param name="idUsuario"></param>
        /// <param name="perfil"></param>
        /// <returns></returns>
        public IEnumerable<EPerfil> GetPerfis(int idMenu)
        {
            string[] perfis = Get(idMenu)?.Perfis?.Split(',');

            return perfis?.Where(i => !string.IsNullOrWhiteSpace(i))
                .Select(i => ((EPerfil)Convert.ToInt32(i)));
        }

        /// <summary>
        /// Inativa um menu
        /// </summary>
        /// <param name="idMenu">ID do Menu</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idMenu)
        {
            try
            {
                Menu menu = _repository.Get(idMenu);
                if (!menu.Ativo)
                    return new ValidationResult().Add($"Menu já inativo na base de dados.");

                menu.Ativo = false;

                _repository.Update(menu);
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa um menu
        /// </summary>
        /// <param name="idMenu">ID do menu</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idMenu)
        {
            try
            {
                Menu menu = _repository.Get(idMenu);
                if (menu.Ativo)
                    return new ValidationResult().Add($"Menu já ativo na base de dados.");

                menu.Ativo = true;

                _repository.Update(menu);
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Busca o menu por IdentificadorPermissao
        /// </summary>
        /// <param name="identificadorPermissao">Identificador de Permissao do menu</param>
        /// <returns>Objeto Menu</returns>
        public Menu GetPorIdentificadorPermissao(int identificadorPermissao)
        {
            return _repository.Find(x => x.IdentificadorPermissao == identificadorPermissao).FirstOrDefault();
        }

        /// <summary>
        /// Retorna os itens do menu organizados na hierarquia por empresa
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public IQueryable<AutorizacaoEmpresaMenuGrid> GetArvoreMenuPorIdEmpresa(int idEmpresa)
        {
            return _repository.GetArvoreMenuPorIdEmpresa(idEmpresa);
        }

        /// <summary>
        /// Retorna os menus pelo módulo
        /// </summary>
        /// <param name="idModulo"></param>
        /// <param name="idGrupoUsuario"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="perfil"></param>
        /// <returns></returns>
        public IQueryable<GrupoUsuarioMenuGrid> GetMenusPorModulo(int idModulo, int idGrupoUsuario, int idEmpresa, int perfil)
        {
            return _repository.GetMenusPorModulo(idModulo, idGrupoUsuario, idEmpresa, perfil);
        }
        
        public object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var menus = _repository.GetAll();

            menus = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? menus.OrderByDescending(x => x.IdMenu)
                : menus.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            menus = menus.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = menus.Count(),
                items = menus.Skip((page - 1) * take).Take(take)
                    .ToList().Select(o => new
                    {
                        o.IdMenu,
                        o.Descricao,
                        o.Ativo
                    })
            };
        }
        
        public ValidationResult AlterarStatus(int menuId)
        {
            try
            {
                var menu = _repository.Get(menuId);
                
                if (menu == null)
                    return new ValidationResult().Add($"Menu {menuId} não encontrado na base de dados.");
            
                menu.Ativo = !menu.Ativo;
                _repository.Update(menu);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Cadastrar(MenuCadastroRequest request)
        {
            try
            {
                if (request.IdMenu > 0)
                    return Editar(request);
                        
                var moduloMenuRepository = _moduloMenuRepository;    
                var menu = Mapper.Map<Menu>(request);
                
                _repository.Add(menu);

                if (request.ModulosId != null)
                    foreach (var moduloId in request.ModulosId)
                        moduloMenuRepository.Add(new ModuloMenu {IdMenu = menu.IdMenu, IdModulo = moduloId});

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Editar(MenuCadastroRequest request)
        {
            var moduloMenuRepository = _moduloMenuRepository;   
            
            try
            {
                var menu = Mapper.Map<Menu>(request);
                _repository.Update(menu);

                var modulosPersistidos = moduloMenuRepository.Where(o => o.IdMenu == menu.IdMenu).Select(o => o.IdModulo).ToList();
                
                var modulosMenuParaRemover = modulosPersistidos.Except(request.ModulosId).ToList();
                var modulosMenuParaAdicionar = request.ModulosId.Except(modulosPersistidos).ToList();

                foreach (var moduloMenuParaRemover in modulosMenuParaRemover)
                {
                    var moduloMenu = moduloMenuRepository.FirstOrDefault(o => o.IdModulo == moduloMenuParaRemover && o.IdMenu == menu.IdMenu);
                    moduloMenuRepository.Delete(moduloMenu);
                }

                foreach (var moduloParaAdicionar in modulosMenuParaAdicionar)
                    moduloMenuRepository.Add(new ModuloMenu {IdMenu = menu.IdMenu, IdModulo = moduloParaAdicionar});
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object ObterParaEditar(int id)
        {
            var menu = _repository
                .Include(o => o.ModuloMenus)
                .FirstOrDefault(o => o.IdMenu == id);
            
            if (menu == null)
                throw new Exception($"Menu {id} não encontrado na base de dados.");

            var listaPerfis = new List<int>();
            
            if (!string.IsNullOrEmpty(menu.Perfis))
            {
                var perfis = menu.Perfis.Split(',');
                listaPerfis = perfis.Select(perfil => Convert.ToInt32(perfil)).ToList();    
            }

            return new
            {
                menu.Ativo, 
                menu.Descricao, 
                menu.IdMenu, 
                menu.IdMenuPai, 
                menu.IsMenuMobile,
                menu.IsMenuPai, 
                menu.LinkNovo, 
                menu.Funcao, 
                ListaPerfis = listaPerfis,
                Modulos = menu.ModuloMenus.Select(o => o.IdModulo)
            };
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoBaseProdutoMap : EntityTypeConfiguration<EstabelecimentoBaseProduto>
    {
        public EstabelecimentoBaseProdutoMap()
        {
            ToTable("ESTABELECIMENTO_BASE_PRODUTO");

            HasKey(x => new { x.IdEstabelecimentoBase, x.IdProduto });

            Property(x => x.IdEstabelecimentoBase)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(x => x.IdProduto)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.UnidadeMedida)
            .HasMaxLength(10);

            HasRequired(x => x.EstabelecimentoBase)
                .WithMany(x => x.EstabelecimentoBaseProdutos)
                .HasForeignKey(x => x.IdEstabelecimentoBase);
        }
    }
}

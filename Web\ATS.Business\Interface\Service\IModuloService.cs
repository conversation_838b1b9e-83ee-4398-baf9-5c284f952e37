﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IModuloService : IService<Modulo>
    {
        Modulo Get(int id);
        ValidationResult Add(Modulo modulo);
        ValidationResult Update(Modulo modulo);
        IQueryable<Modulo> All();
        IQueryable<Modulo> Consultar(string descricao);
        ValidationResult Inativar(int id);
        ValidationResult Reativar(int id);

        /// <summary>
        /// Retorna os módulos do transportador
        /// </summary>
        /// <param name="idTransportador"></param>
        /// <returns></returns>
        List<Modulo> GetModulosPorEmpresa(int idEmpresa);

        object GetModulosPorUsuario(int idUsuario);
        object ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
    }
}
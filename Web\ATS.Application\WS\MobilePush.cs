﻿using ATS.Domain.Entities;
using ATS.Domain.Models;
using NLog;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Web.Configuration;
using ATS.Application.Application;
using ATS.CrossCutting.IoC;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using Sistema.Framework.Util.Extension;
using Newtonsoft.Json;

namespace ATS.Application.WS
{
    public class MobilePush
    {
        private readonly IPushService _pushService;
        
        public MobilePush(IPushService pushService, INotificacaoPushService notificacaoPushService)
        {
            _pushService = pushService;
            _notificacaoPushService = notificacaoPushService;
        }
        
        #region Propriedades

        public static Logger Logger = LogManager.GetCurrentClassLogger();

        #endregion

        #region Private

        /// <summary>
        /// Pegar o token para o envio do push para aplicação mobile
        /// </summary>
        /// <returns>Token para envio do push</returns>
        public static string GetToken()
        {
            const string tokenPush = "x807f9uEkgXH5BVU5LMr9YBtFzlv055l";
            try
            {
                string tokenPushParam = System.Configuration.ConfigurationManager.AppSettings["TOKEN_PUSH"];

                return !string.IsNullOrEmpty(tokenPushParam) ? tokenPushParam : tokenPush;
            }
            catch
            {
                return tokenPush;
            }
        }

        /// <summary>
        /// Função para pegar o link do servidor que faz o envio dos push para o Google
        /// </summary>
        /// <returns>Link do servidor WebService para envio do push</returns>
        private string SEND_MOB_NOTIFY()
        {
            const string lnkDef = "http://localhost:8080/pushnotification/ws/gcm/sendnotification";

            try
            {
                string filtro = System.Configuration.ConfigurationManager.AppSettings["SEND_MOB_NOTIFY"];
                return (!string.IsNullOrEmpty(filtro) ? filtro : lnkDef);
            }
            catch
            {
                return lnkDef;
            }
        }

        private string SEND_MOB_NOTIFY_Excelsior()
        {
            const string lnkDef = "http://sw8-202:9463/push/ws/fcm/sendnotification";

            try
            {
                string filtro = System.Configuration.ConfigurationManager.AppSettings["SEND_MOB_NOTIFY_FCM"];
                return (!string.IsNullOrEmpty(filtro) ? filtro : lnkDef);
            }
            catch
            {
                return lnkDef;
            }
        }

        /// <summary>
        /// Thread para executar o envio do push. Assim a aplicação não trava esperando o push ser enviado.
        /// </summary>
        private static Thread ThrEnviarPush;

        private readonly INotificacaoPushService _notificacaoPushService;

        #endregion

        /// <summary>
        /// Envia push a partir de uma requisição
        /// </summary>
        /// <param name="pushRequest">Requisição a ser enviada</param>
        public void EnviarMensagemPushMobile(PushRequest pushRequest)
        {
            if (pushRequest == null)
                return;

            var mobilePushNew = new RequestCore
            {
                Requisicao = pushRequest,
                Token = GetToken()
            };

            var aJson = JsonConvert.SerializeObject(pushRequest);

            //Cria thread para envio do push, assim o servidor não trava esperando o envio.
            ThrEnviarPush = new Thread(() =>
            {
                EnviarMensagemNotificacao(aJson);
            }
                )
            {
                Name = "ThreadEnviarPush",
                IsBackground = true
            };
            ThrEnviarPush.Start();
        }

        /// <summary>
        /// Envia push a partir de uma requisição - (Exclusivo Excelsior)
        /// </summary>
        /// <param name="pushRequestExcelsior"></param>
        public void EnviarMensagemPushMobileExcelsior(PushRequestExcelsior pushRequestExcelsior)
        {
            if (pushRequestExcelsior == null)
                return;

            var mobilePushNew = new RequestCoreExcelsior
            {
                Requisicao = pushRequestExcelsior,
                Token = GetToken()
            };

            var aJson = JsonConvert.SerializeObject(pushRequestExcelsior);

            //Cria thread para envio do push, assim o servidor não trava esperando o envio.
            ThrEnviarPush = new Thread(() =>
            {
                EnviarMensagemNotificacaoExcelsior(aJson);
            }
                )
            {
                Name = "ThreadEnviarPush",
                IsBackground = true
            };
            ThrEnviarPush.Start();
        }

        /// <summary>
        /// Cria a thread para envio do push afim de não travar o servidor
        /// Faz o envio do JSON do push para o WebService
        /// </summary>
        /// <param name="jsonRequest">JSON do push a ser enviado</param>
        /// <returns>PushResponse com status e mensagem de sucesso ou erro</returns>
        private PushResponse EnviarMensagemNotificacao(string jsonRequest)
        {
            PushResponse jsResponse;

            try
            {
                var wsReq = (HttpWebRequest)WebRequest.Create(SEND_MOB_NOTIFY());
                wsReq.ContentType = "application/json";
                wsReq.Method = "POST";

                using (var stWr = new StreamWriter(wsReq.GetRequestStream(), Encoding.UTF8))
                {
                    stWr.Write(jsonRequest);
                    stWr.Flush();
                    stWr.Close();

                    var httpResp = (HttpWebResponse)wsReq.GetResponse();

                    using (var sRd = new StreamReader(httpResp.GetResponseStream()))
                    {
                        try
                        {
                            jsResponse = JsonConvert.DeserializeObject<PushResponse>(sRd.ReadToEnd());
                            Console.WriteLine(jsResponse.Mensagem);
                        }
                        catch (Exception ex2)
                        {
                            jsResponse = new PushResponse
                            {
                                Id = 0,
                                Status = "999",
                                Mensagem = ex2.Message
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                jsResponse = new PushResponse
                {
                    Id = 0,
                    Status = "999",
                    Mensagem = ex.Message
                };
            }

            return jsResponse;
        }

        /// <summary>
        /// Cria a thread para envio do push afim de não travar o servidor (Uso exclusivo para Excelsior)
        /// </summary>
        /// <param name="jsonRequest"></param>
        /// <returns></returns>
        private PushResponse EnviarMensagemNotificacaoExcelsior(string jsonRequest)
        {
            PushResponse jsResponse;

            try
            {
                var wsReq = (HttpWebRequest)WebRequest.Create(SEND_MOB_NOTIFY_Excelsior());
                wsReq.ContentType = "application/json";
                wsReq.Method = "POST";

                using (var stWr = new StreamWriter(wsReq.GetRequestStream(), Encoding.UTF8))
                {
                    stWr.Write(jsonRequest);
                    stWr.Flush();
                    stWr.Close();

                    var httpResp = (HttpWebResponse)wsReq.GetResponse();

                    using (var sRd = new StreamReader(httpResp.GetResponseStream()))
                    {
                        try
                        {
                            jsResponse = JsonConvert.DeserializeObject<PushResponse>(sRd.ReadToEnd());
                            Console.WriteLine(jsResponse.Mensagem);
                        }
                        catch (Exception ex2)
                        {
                            jsResponse = new PushResponse
                            {
                                Id = 0,
                                Status = "999",
                                Mensagem = ex2.Message
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                jsResponse = new PushResponse
                {
                    Id = 0,
                    Status = "999",
                    Mensagem = ex.Message
                };
            }

            return jsResponse;
        }

        /// <summary>
        /// Envia mensagem via Push do App MensagemApp e MensagemGrupoUsuarioApp
        /// </summary>
        /// <param name="mensagem">Objeto de Mensagem</param>
        /// <param name="listaIdPush">Lista dos ids de pushs a serem enviados</param>
        public void EnviarPush(Mensagem mensagem, List<string> listaIdPush, ETipoMensagemPush tipoMensagem)
        {
            int idPush = 1;
            try
            {
                //Alimenta o objeto PushChat
                PushChat pushChat = new PushChat
                {
                    Token = MobilePush.GetToken(),
                    CpfRemetente = mensagem.IdUsuarioRemetente.ToString(),
                    CpfDestinatario = null, //null porque não é possível mandar mais de 1 destinatário.
                    Assunto = mensagem.Assunto,
                    Conteudo = mensagem.Conteudo,
                    DataHoraEnvio = DateTime.Now
                };

                //Serializa o objeto PushChat
                var jsonPushChat = JsonConvert.SerializeObject(pushChat);

                //Envia o Push
                MobilePush push = this;
                push.EnviarMensagemPushMobile(
                    new PushRequest
                    {
                        TempoExpiracao = 0,
                        Extras = null,
                        Id = idPush,
                        ListaIdPush = listaIdPush,
                        Mensagem = new PushMensagem
                        {
                            MensagemTipo = ((int)tipoMensagem).ToString(),
                            TituloNotificacao = "Nova mensagem de Oferta de Cargas",
                            ObjetoJSON = jsonPushChat
                        }
                    }
                    );
            }
            catch (Exception ex)
            {
                throw new Exception("Não foi possível enviar a mensagem via Push.", ex);
            }
        }

        /// <summary>
        /// Enviar push a partir de um objeto PushCarga
        /// </summary>
        /// <param name="pushCarga">Objeto Push Carga</param>
        /// <param name="listaIdPush">Lista de IdPush dos usuários a receber o push</param>
        /// <param name="tituloNotificacao">Título da notificação push</param>
        public void EnviarPush(PushCarga pushCarga, List<string> listaIdPush, string tituloNotificacao)
        {
            MobilePush push = this;

            try
            {
                string objectJson = JsonConvert.SerializeObject(pushCarga);

                if (listaIdPush.Any())
                    _notificacaoPushService.EnviarRequest(listaIdPush, tituloNotificacao, ETipoMensagemPush.SelectedToFreight, objectJson);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Método genérico para envio de push
        /// </summary>
        /// <param name="mensagem"></param>
        /// <param name="titulo"></param>
        /// <param name="tipoMensagem"></param>
        /// <param name="cpfMotorista"></param>
        public void EnviarMensagem(string mensagem, string titulo, ETipoMensagemPush tipoMensagem, string cpfMotorista)
        {
            try
            {
                var result = _pushService.EnviarPorDocumento(cpfMotorista, titulo, mensagem, tipoMensagem:tipoMensagem);
                if (!result.IsValid) throw new Exception(result.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception ex)
            {
                throw new Exception("Não foi possível enviar a mensagem via Push.", ex);
            }
        }

        public void EnviarPushNovo(List<string> idsPush, string title, ETipoMensagemPush messageType, string message)
        {
            try
            {
                _pushService.Enviar(idsPush, title, message, tipoMensagem:messageType);
            }
            catch (Exception e)
            {
                throw new Exception("Não foi possível enviar a mensagem via Push.", e);
            }
        }
    }
}
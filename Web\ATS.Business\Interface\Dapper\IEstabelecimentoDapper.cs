using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Dapper
{
    public interface IEstabelecimentoDapper : IRepositoryDapper<ConsultaEstabelecimentoModelResponse>
    {
        IEnumerable<ConsultaEstabelecimentoModelResponse> ConsultarEstabelecimentos(int idEmpresa, string cnpjEstabelecimento);
    }
}
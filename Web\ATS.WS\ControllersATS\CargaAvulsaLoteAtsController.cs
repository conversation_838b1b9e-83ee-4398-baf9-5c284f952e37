using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using NLog;

namespace ATS.WS.ControllersATS
{
    public class CargaAvulsaLoteAtsController : BaseAtsController<ICargaAvulsaLoteApp>
    {
        public CargaAvulsaLoteAtsController(ICargaAvulsaLoteApp app, IUserIdentity userIdentity) : base(app, userIdentity)
        {
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarPlanilha(HttpPostedFileBase file, int idEmpresa)
        {
            try
            {
                if (SessionUser.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = SessionUser.IdEmpresa ?? 0;

                Logger.Info($"CargaAvulsa: Iniciando processo de importação em {DateTime.Now}");
                var response = App.ValidarCargaAvulsaPlanilha(file,idEmpresa);
                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Expor(EApi.Portal)]
        public FileResult DownloadLayoutPlanilha()
        {
            var dirInfo = new DirectoryInfo("~/Files/Layout Carga Avulsa.xlsx");
            return File(dirInfo.ToString(), ConstantesUtils.ExcelMimeType, string.Concat("Layout Planilha Carga Avulsa", ConstantesUtils.ExcelXmlExtention));
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarCargaAvulsaPlanilha(CargaAvulsaValidacaoPlanilha cargaAvulsa)
        {
            try
            {
                cargaAvulsa.UsuarioCadastroId = SessionUser.IdUsuario;
                cargaAvulsa.UsuarioCadastroCpf = SessionUser.CpfCnpj;

                if (!cargaAvulsa.IdEmpresa.HasValue)
                    cargaAvulsa.IdEmpresa = SessionUser.IdEmpresa;
                
                var response = App.CadastrarCargaAvulsaPlanilha(cargaAvulsa);
                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro($"Ocorreu um erro inesperado ao gravar o registro: {e.Message}");
            }
        }

        [HttpGet]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult RealizarCargasAvulsaPendentes()
        {
            LogManager.GetCurrentClassLogger().Info($"Iniciando a execução das cargas avulsas pendentes: {DateTime.Now}");
            App.RealizarCargasAvulsaPendentes();
            LogManager.GetCurrentClassLogger().Info($"Finalizando a execução das cargas avulsas pendentes: {DateTime.Now}");
            return ResponderSucesso("Operação realizada com sucesso");
        }
        
        [HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarAlcadasLimites(CargaAvulsaValidacaoAlcadasLimitesPlanilha request)
        {
            try
            {
                var response = App.ValidarAlcadasLimites(request);

                if (!response.IsValid)
                    return ResponderErro(response.Errors.FirstOrDefault()?.Message);
                
                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro($"Ocorreu um erro inesperado ao validar os limites: {e.Message}");
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using System.Data.Entity.Migrations;
using System;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class TipoCavaloSeeder
    {
        public void Execute(AtsContext context)
        {
            int idTipoCavalo = 1;

            context.TipoCavalo.AddOrUpdate(new[]
            {
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Truck", Categoria = ECategoriaTipoCavalo.Medios, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Toco", Categoria = ECategoriaTipoCavalo.Leves, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Toco ¾", Categoria = ECategoriaTipoCavalo.Leves, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Bi-Truck", Categoria = ECategoriaTipoCavalo.Medios, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Só Cavalo", Categoria = ECategoriaTipoCavalo.Leves, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Só Cavalo Trucado", Categoria = ECategoriaTipoCavalo.Leves, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Carreta", Categoria = ECategoriaTipoCavalo.Pesados, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Carreta Ls", Categoria = ECategoriaTipoCavalo.Pesados, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Bitrem", Categoria = ECategoriaTipoCavalo.Pesados, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Rodotrem", Categoria = ECategoriaTipoCavalo.Pesados, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Bitrenzão", Categoria = ECategoriaTipoCavalo.Pesados, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Vuc/Hr", Categoria = ECategoriaTipoCavalo.Medios, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Van", Categoria = ECategoriaTipoCavalo.Medios, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Utilitário", Categoria = ECategoriaTipoCavalo.Medios, DataHoraUltimaAtualizacao = DateTime.Now},
                new TipoCavalo { IdTipoCavalo = idTipoCavalo++, Nome = "Outros", Categoria = ECategoriaTipoCavalo.Medios, DataHoraUltimaAtualizacao = DateTime.Now}
            });
        }
    }
}
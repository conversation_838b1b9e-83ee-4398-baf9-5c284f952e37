using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioProtocoloPagamentooFreteDTO : FiltrosGridBaseModel
    {
        public int IdUsuario { get; set; } 
        public new ETipoArquivo? Extensao { get; set; }
    }

    public class GerarRelatorioConsultaPagamentosEstabelecimentoReportDTO : FiltrosGridBaseModel
    {
        public DateTime? DataInicial { get; set; } 
        public DateTime? DataFinal { get; set; } 
        public int IdEstabelecimentoBase { get; set; } 
    }

    public class RelatorioEventosVinculadosDTO : FiltrosGridBaseModel
    {
        public int IdProtocolo { get; set; }
    }
}
using System;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioCurvaAbcDTO : FiltrosGridBaseModel
    {
        public DateTime? DataInicial { get; set; }
        public DateTime? DataFinal { get; set; } 
        public int? UF { get; set; }
        public double? A { get; set; }
        public double? B { get; set; } 
        public double? C { get; set; } 
    }
}
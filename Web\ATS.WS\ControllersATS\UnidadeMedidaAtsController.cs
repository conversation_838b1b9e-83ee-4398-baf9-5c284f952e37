﻿using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Linq;
using System.Web.Configuration;
using System.Web.Mvc;

namespace ATS.WS.ControllersATS
{
    public class UnidadeMedidaAtsController : DefaultController
    {
        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult Consultar()
        {
            try
            {
                var unidadesMedida = WebConfigurationManager.AppSettings["UnidadeMedida"];
                var retorno = unidadesMedida.Split(';').ToList().Select(x => new { Unidade = x });

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
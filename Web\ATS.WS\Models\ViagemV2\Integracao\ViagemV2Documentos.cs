using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2Documentos
    {
        /// <summary>
        /// CPF/CNPJ do cliente de origem
        /// </summary>
        public string ClienteOrigemDocumento { get; set; }

        /// <summary>
        /// CPF/CNPJ do cliente de destino
        /// </summary>
        public string ClienteDestinoDocumento { get; set; }

        /// <summary>
        /// CPF/CNPJ do cliente tomador
        /// </summary>
        public string ClienteTomadorDocumento { get; set; }

        /// <summary>
        /// CNPJ da filial
        /// </summary>
        public string FilialDocumento { get; set; }

        /// <summary>
        /// CPF do motorista
        /// </summary>
        public string MotoristaDocumento { get; set; }

        /// <summary>
        /// CPF/CNPJ do proprietário
        /// </summary>
        public string ProprietarioDocumento { get; set; }

        /// <summary>
        /// Número do documento na nota (numeração para o cliente)
        /// </summary>
        public string NumeroDocumentoNotaCliente { get; set; }

        /// <summary>
        /// Número do documento fiscal
        /// </summary>
        public string NumeroDocumentoFiscal { get; set; }

        /// <summary>
        /// Nome do proprietário
        /// </summary>
        public string ProprietarioNome { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (string.IsNullOrEmpty(ClienteOrigemDocumento))
                return new ValidationResult().Add("Documento do cliente de origem não informado.", EFaultType.Error);

            if (!ClienteOrigemDocumento.ValidateDocument())
                return new ValidationResult().Add("Documento do cliente de origem inválido.", EFaultType.Error);

            if (string.IsNullOrEmpty(ClienteDestinoDocumento))
                return new ValidationResult().Add("Documento do cliente de destino não foi informado.", EFaultType.Error);

            if (!ClienteDestinoDocumento.ValidateDocument())
                return new ValidationResult().Add("Documento do cliente de destino inválido.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(ClienteTomadorDocumento))
                if (!ClienteTomadorDocumento.ValidateDocument())
                    return new ValidationResult().Add("Documento do cliente tomador inválido", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(FilialDocumento))
                if (!FilialDocumento.ValidateDocument())
                    return new ValidationResult().Add("Documento da filial inválido.", EFaultType.Error);
            
            if (string.IsNullOrEmpty(MotoristaDocumento))
                return new ValidationResult().Add("Documento do motorista não informado.", EFaultType.Error);
            
            if (!MotoristaDocumento.ValidateDocument())
                return new ValidationResult().Add("Documento do motorista inválido.", EFaultType.Error);
            
            if (string.IsNullOrEmpty(ProprietarioDocumento))
                return new ValidationResult().Add("Documento do proprietário não informado.", EFaultType.Error);
            
            if (!ProprietarioDocumento.ValidateDocument())
                return new ValidationResult().Add("Documento do proprietário inválido.", EFaultType.Error);

            return new ValidationResult();
        }
    }
}
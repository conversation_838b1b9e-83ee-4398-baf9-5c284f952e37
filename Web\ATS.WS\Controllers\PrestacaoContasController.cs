﻿using ATS.WS.Controllers.Base;
using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO.PrestacaoContas;
using ATS.WS.Attributes;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.Domain.Enum;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Controllers
{
    public class PrestacaoContasController : BaseController
    {
        private readonly IPrestacaoContasApp _prestacaoContasApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        
        public PrestacaoContasController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IPrestacaoContasApp prestacaoContasApp) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _prestacaoContasApp = prestacaoContasApp;
        }

        [HttpPost]
        [EnableLogRequest]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string Novo(PrestacaoContasNovoRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(new Retorno<object>(true, _prestacaoContasApp.Novo(@params)));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        
        [HttpGet]
        [EnableLogRequest]
        [AutorizarMobile]
        [Expor(EApi.Integracao)]
        public string HasAberta()
        {
            try
            {
                var result = _prestacaoContasApp.HasAberta();

                return new JsonResult().Responde(!result.Success 
                    ? new Retorno<object>(false,result.Messages.FirstOrDefault()) 
                    : new Retorno<object>(true, new
                    {
                        Aberto = result.Value
                    }));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [EnableLogRequest]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public string SolicitarEncerramento()
        {
            try
            {
                var result = _prestacaoContasApp.SolicitarEncerramento();

                return new JsonResult().Responde(!result.Success 
                    ? new Retorno<object>(false,result.Messages.FirstOrDefault()) 
                    : new Retorno<object>(true, result.Value));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
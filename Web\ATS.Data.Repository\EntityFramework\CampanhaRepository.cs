﻿using System;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class CampanhaRepository : Repository<Campanha>, ICampanhaRepository
    {
        public CampanhaRepository(AtsContext context) : base(context)
        {
        }

        public IQueryable<Campanha> GetAtual()
        {
            return Where(c => c.DataInicio <= DateTime.Now && (!c.DataFim.HasValue || c.DataFim >= DateTime.Now) && c.Ativa);
        }

        public IQueryable<Campanha> GetById(int id)
        {
            return Where(c => c.Id == id);
        }
    }
}
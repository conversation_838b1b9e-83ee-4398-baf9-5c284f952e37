﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace ATS.Data.Repository.EntityFramework
{
    public class FilialRepository : Repository<Filial>, IFilialRepository
    {
        public FilialRepository(AtsContext context) : base(context)
        {
        }
        
        /*/// <summary>
        /// Retorna o registro com todos os elementos filhos
        /// </summary>
        /// <param name="idFilial">Id de Filial</param>
        /// <returns></returns>
        public Filial GetWithAllChilds(int idFilial)
        {
            return (from filial in All()
                         .Include(x => x.Pais)
                         .Include(x => x.Estado)
                         .Include(x => x.Cidade)
                         .Include(x => x.Empresa)
                         .Include(x => x.Veiculos)
                         .Include(x => x.FilialContatos)
                    where filial.IdFilial == idFilial
                    select filial).FirstOrDefault();
        }*/

        /// <summary>
        /// Retorna a Filial contendo os dados de Pais, Estado e Cidade
        /// </summary>
        /// <param name="id">ID da Filial</param>
        /// <returns></returns>
        public Filial GetWithLocalizacao(int id)
        {
            return (from filial in All().Include(x => x.Pais).Include(x => x.Estado).Include(x => x.Cidade)
                    where filial.IdFilial == id && filial.Ativo
                    select filial)?.FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para consultar Filial
        /// </summary>
        /// <param name="razaoSocial">Razão Social da filial</param>
        /// <param name="idEmpresa">Id de Empresa</param>
        /// <returns>IQueryable de FilialGrid</returns>
        public IQueryable<Filial> Consultar(string razaoSocial, int idEmpresa)
        {
            return from filial in All()
                   .Include(x => x.Empresa)
                   where filial.RazaoSocial.Contains(razaoSocial) && (idEmpresa == 0 || filial.IdEmpresa == idEmpresa)
                   orderby filial.IdFilial descending
                   select filial;
        }

        /*public IQueryable<Filial> ConsultarPorNomeFantasia(string nomeFantasia, int idEmpresa)
        {
            return from filial in All()
                   .Include(x => x.Empresa)
                   where filial.NomeFantasia.Contains(nomeFantasia) && (idEmpresa == 0 || filial.IdEmpresa == idEmpresa)
                   orderby filial.IdFilial descending
                   select filial;
        }*/

        /// <summary>
        /// Retorna a lista de filiais habilitadas vinculadas a um determinado empresa
        /// </summary>
        /// <param name="idEmpresa">Código do empresa</param>
        /// <returns>IQueryable de Filial</returns>
        public IQueryable<Filial> GetFiliaisPorEmpresa(int idEmpresa)
        {
            if(idEmpresa == 0)
                return (from filial in All()
                        where filial.Ativo
                        select filial);

            return (from filial in All()
                    where filial.IdEmpresa == idEmpresa && filial.Ativo
                    select filial);
        }

        public Filial GetFilialPorEmpresa(int idEmpresa, int idFilial)
        {
            return (from filial in All()
                    where filial.IdEmpresa == idEmpresa &&  filial.IdFilial == idFilial
                    select filial)
                    .FirstOrDefault();
        }

        public string GetCnpjPorId(int idFilial)
        {
            return All().Where(c => c.IdFilial == idFilial).Select(c => c.CNPJ).FirstOrDefault();
        }

        public IQueryable<Filial> GetIdsFiliaisAtualizadas(DateTime dataHora, int? idEmpresa)
        {
            var ret = from filial in All()
                         .Include(x => x.Pais)
                         .Include(x => x.Estado)
                         .Include(x => x.Cidade)
                         .Include(x => x.Empresa)
                   where filial.DataHoraUltimaAtualizacao >= dataHora 
                   select filial;

            if (idEmpresa != null)
            {
                ret = ret.Where(x => x.IdEmpresa == idEmpresa); 
            }

            return ret;
        }

        public int GetFilialIdFromFunc(string filialCliente)
        {

            var context = (AtsContext)Context;
            if (context.Database.Connection.State == ConnectionState.Closed)
                context.Database.Connection.Open();

            var retornoFinal = context.Database.SqlQuery<int>("select dbo.fRetornaId_Filial(@Filial) filial", new System.Data.SqlClient.SqlParameter("@Filial", filialCliente)).Single();


            return retornoFinal;
    }
    }
}
using System;

namespace ATS.Domain.Models
{
    public class ViagemEventoProtocoloAnexoModel
    {
        public int IdViagemEventoProtocoloAnexoModel { get; set; }

        public int IdViagemEvento { get; set; }
        
        public string NomeArquivo { get; set; }

        public string <PERSON><PERSON><PERSON> { get; set; }

        public string Detalhes { get; set; }
        
        public string Base64 { get; set; }

        public string Token { get; set; }

        public void Validate()
        {
            if (string.IsNullOrEmpty(NomeArquivo))
                throw new Exception("Nome do arquivo não informado.");
            
            if (string.IsNullOrEmpty(Detalhes))
                throw new Exception("Detalhes do arquivo não informado.");
            
            if (string.IsNullOrEmpty(Base64))
                throw new Exception("Base64 do arquivo não informado.");
        }
    }
}
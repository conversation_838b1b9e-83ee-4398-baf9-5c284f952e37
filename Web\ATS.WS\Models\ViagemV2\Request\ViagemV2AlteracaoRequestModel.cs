using System.Linq;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Request
{
    public class ViagemV2AlteracaoRequestModel : ViagemV2IntegracaoRequestModel
    {
        public new int? ViagemId { get; set; }

        public override ValidationResult ValidarEntrada(bool? validarDocumentosComIntegracoes, bool validarViagemId)
        {
            if (!ViagemId.HasValue)
                return new ValidationResult().Add("Viagem Id não informado.", EFaultType.Error);
                
            var resultadoValidacoesBase = base.ValidarEntrada(validarDocumentosComIntegracoes, validarViagemId);

            return !resultadoValidacoesBase.IsValid ? resultadoValidacoesBase : new ValidationResult();
        }

        public ValidationResult ValidarEntradaComViagemPersistida(Domain.Entities.Viagem viagemPersistida)
        {
            var validation = new ValidationResult();
            
            if (viagemPersistida == null)
                return validation.Add($"Viagem com Id {ViagemId} não localizado na base de dados.", EFaultType.Error);

            return DadosViagem.DadosIniciais.NumeroControle != viagemPersistida.NumeroControle
                ? validation.Add("Número de controle da viagem não pode ser alterado.", EFaultType.Error)
                : new ValidationResult();
        }
    }
}
﻿using ATS.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ProprietarioContato : ContatoBase
    {
        /// <summary>
        /// Código do proprietário
        /// </summary>
        public int IdProprietario { get; set; }

        /// <summary>
        /// Código do empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código do contato
        /// </summary>
        public int IdContato { get; set; }

        #region Navegação inversa

        /// <summary>
        /// Proprietário
        /// </summary>
        public virtual Proprietario Proprietario { get; set; }

        #endregion
    }
}
﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ILimiteTransacaoPortadorService : IBaseService<ILimiteTransacaoPortadorRepository>
    {
        BusinessResult LimitarValor(string documento, ETipoLimiteTransacaoPortador tipo, decimal valor);
        decimal GetLimite(string documento, ETipoLimiteTransacaoPortador tipo);
        IList<PortadorLimitesValor> GetLimites(string documento);
    }
}
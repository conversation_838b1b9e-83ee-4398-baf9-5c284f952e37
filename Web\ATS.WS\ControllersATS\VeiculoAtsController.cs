﻿using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.WS.Attributes;
using ATS.WS.Models.Webservice.Request.Veiculo;
using ATS.WS.Services;
using Newtonsoft.Json;

namespace ATS.WS.ControllersATS
{
    public class VeiculoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly SrvVeiculo _srvVeiculo;
        private readonly IVeiculoApp _veiculoApp;
        private readonly ITipoCavaloApp _tipoCavaloApp;
        private readonly ITipoCarretaApp _tipoCarretaApp;
        private readonly ITipoCombustivelApp _tipoCombustivelApp;
        private readonly IFilialApp _filialApp;

        public VeiculoAtsController(IUserIdentity userIdentity, SrvVeiculo srvVeiculo, IVeiculoApp veiculoApp, ITipoCavaloApp tipoCavaloApp, ITipoCarretaApp tipoCarretaApp, ITipoCombustivelApp tipoCombustivelApp, IFilialApp filialApp)
        {
            _userIdentity = userIdentity;
            _srvVeiculo = srvVeiculo;
            _veiculoApp = veiculoApp;
            _tipoCavaloApp = tipoCavaloApp;
            _tipoCarretaApp = tipoCarretaApp;
            _tipoCombustivelApp = tipoCombustivelApp;
            _filialApp = filialApp;
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(VeiculoCreateRequest model)
        
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && model.IdEmpresa != _userIdentity.IdEmpresa)
                    return ResponderErro("Usuário não autenticado.");
                
                if (model.IdFilial.HasValue && _userIdentity.IdEmpresa.HasValue &&
                    !_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, model.IdFilial.Value))
                    return ResponderErro("Usuário não autenticado.");

                if (model.IdVeiculo.HasValue)
                {
                    var resultadoEdit = _srvVeiculo.Editar(model, _userIdentity.IdUsuario);

                    return resultadoEdit.IsValid
                        ? ResponderSucesso("Veículo atualizado com sucesso!")
                        : ResponderErro(resultadoEdit.Errors.FirstOrDefault()?.Message);
                }

                var resultado = _srvVeiculo.Cadastrar(model, _userIdentity.IdUsuario);

                return resultado.IsValid
                    ? ResponderSucesso("Veículo cadastrado com sucesso!")
                    : ResponderErro(resultado.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idVeiculo)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_veiculoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idVeiculo))
                        return ResponderErro("Registro não encontrado.");
                }

                return ResponderSucesso(_srvVeiculo.ConsultarPorId(idVeiculo));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaDadosVeiculo(int idVeiculo)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_veiculoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idVeiculo))
                        return ResponderErro("Registro não encontrado.");
                }

                return ResponderSucesso(_srvVeiculo.ConsultaDadosVeiculo(idVeiculo));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult CheckVeiculoReutilizacaoPorPlaca(string placa, int? idUsuario)
        {
            try
            {
                var retorno = _srvVeiculo.CheckVeiculoReutilizacaoPorPlaca(placa, idUsuario);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int idVeiculo)
        {
            try
            {
                if (_userIdentity.IdEmpresa.HasValue)
                {
                    if (!_veiculoApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, idVeiculo))
                        return ResponderErro("Registro não encontrado.");
                }

                var retorno = _veiculoApp.AlterarStatus(idVeiculo);

                return !retorno.IsValid
                    ? ResponderErro(retorno.Errors.FirstOrDefault()?.Message)
                    : ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var retorno = _veiculoApp.ConsultarGrid(idEmpresa, take, page, order, filters);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridVeiculosComTracao(int? idEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var retorno = _veiculoApp.ConsultarGrid(idEmpresa, true, take, page, order, filters);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridVeiculosSemTracao(int? idEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var retorno = _veiculoApp.ConsultarGrid(idEmpresa, false, take, page, order, filters);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAnosFabricacao()
        {
            try
            {
                var listaAnos = new List<VeiculoAnoFabricacaoModeloRequestModel>();

                for (var i = DateTime.Now.Year; i > 1900; i--)
                    listaAnos.Add(new VeiculoAnoFabricacaoModeloRequestModel {Codigo = i, Descricao = i.ToString()});

                return ResponderSucesso(string.Empty, listaAnos);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAnosModelo()
        {
            try
            {
                var listaAnos = new List<VeiculoAnoFabricacaoModeloRequestModel>();

                for (var i = DateTime.Now.Year + 1; i > 1900; i--)
                    listaAnos.Add(new VeiculoAnoFabricacaoModeloRequestModel {Codigo = i, Descricao = i.ToString()});

                return ResponderSucesso(string.Empty, listaAnos);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetTiposCavalo(int? idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var tiposCavalo = _tipoCavaloApp.All()
                    .Where(o => !idEmpresa.HasValue || o.IdEmpresa == idEmpresa || o.IdEmpresa == null)
                    .OrderBy(o => o.Nome).Select(o => new {Codigo = o.IdTipoCavalo, Descricao = o.Nome});

                return ResponderSucesso(string.Empty, tiposCavalo);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetTiposCarreta(int? idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                var tiposCarreta = _tipoCarretaApp.All()
                    .Where(o => !idEmpresa.HasValue || o.IdEmpresa == idEmpresa || o.IdEmpresa == null)
                    .OrderByDescending(x => x.Destacar).ThenBy(x => x.Nome)
                    .Select(o => new {Codigo = o.IdTipoCarreta, Descricao = o.Nome});

                return ResponderSucesso(string.Empty, tiposCarreta);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetTiposCombustivel()
        {
            try
            {
                var combustiveis = _tipoCombustivelApp.All()
                    .Select(o => new {Codigo = o.IdTipoCombustivel, o.Descricao});

                return ResponderSucesso(string.Empty, combustiveis);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridVeiculos(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _srvVeiculo.GerarRelatorioGridVeiculos(filtrosGridModel.IdEmpresa, filtrosGridModel.Order,
                filtrosGridModel.Filters, filtrosGridModel.Extensao);
            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de veículo.{filtrosGridModel.Extensao}");
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridVeiculo(int? idEmpresa, int? idFilial, int take, int page, OrderFilters order,
            List<QueryFilters> filters, bool marcarTodosVeiculo, int apertouVeiculo)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;

                if (idEmpresa != null)
                {
                    var retorno = _veiculoApp.ConsultarGridVeiculoEmpresa(idEmpresa, idFilial, take, page, order,
                        filters, marcarTodosVeiculo, apertouVeiculo);
                    return ResponderSucesso(retorno);
                }

                return ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
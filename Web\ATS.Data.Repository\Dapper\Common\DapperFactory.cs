﻿using ATS.Domain.Interface.Dapper.Common;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace ATS.Data.Repository.Dapper.Common
{
    public class DapperFactory<T> : IQueryDapper<T>
    {
        /// <summary>
        /// Retorna a conexão do Dapper para ser executado manualmente ou de forma personalizada.
        /// </summary>
        /// <returns>Instância de conexão</returns>
        public IDbConnection GetConnection()
        {
            return new DapperContext().GetConnection;
        }

        /// <summary>
        /// Executa o comando select enviado via parâmetro, retornando o objeto do tipo de classe
        /// </summary>
        /// <param name="select">String contendo o SQL a ser executado</param>
        /// <returns></returns>
        public IEnumerable<T> RunSelect(string @select)
        {
            return RunSelect(@select, (object) null);
        }
        
        /// <summary>
        /// Executa o comando select enviado via parâmetro, retornando o objeto do tipo de classe
        /// </summary>
        /// <param name="select">String contendo o SQL a ser executado</param>
        /// <returns></returns>
        public IEnumerable<T> RunSelect(string @select, object param)
        {
            using (var context = new DapperContext())
            using (var connection = context.GetConnection)
            {
                return connection.Query<T>(@select, param).AsQueryable();
            }
        }

        public IEnumerable<UserDTO> RunSelect<UserDTO>(string @select)
        {
            using (IDbConnection connection = new DapperContext().GetConnection)
            {
                return connection.Query<UserDTO>(@select).AsQueryable();
            }
        }
        
        /// <summary>
        /// Executa o comando select enviado via parâmetro, retornando o objeto do tipo de classe
        /// </summary>
        /// <param name="select">String contendo o SQL a ser executado</param>
        /// <returns></returns>
        public IEnumerable<T> RunSelect(string @select, DynamicParameters @params)
        {
            if (!@select.Trim().ToUpper().StartsWith("SELECT"))
                throw new Exception("É possivel utilizar somente SELECT com este metodo.");

            using (IDbConnection connection = new DapperContext().GetConnection)
            {
                return connection.Query<T>(@select, @params).AsQueryable();
            }
        }

        /// <summary>
        /// Executa o comando select enviado via parâmetro, retornando o objeto do tipo de classe
        /// </summary>
        /// <param name="select">String contendo o SQL a ser executado</param>
        /// <param name="params"></param>
        /// <returns></returns>
        public IEnumerable<UserDTO> RunSelect<UserDTO>(string @select, DynamicParameters @params)
        {
            using var connection = new DapperContext().GetConnection;
            return connection.Query<UserDTO>(@select, @params).AsQueryable();
        }

        /// <summary>
        /// Executa o comando select enviado via parâmetro, retornando o objeto do tipo de classe
        /// </summary>
        /// <param name="select">String contendo o SQL a ser executado</param>
        /// <param name="params"></param>
        /// <returns></returns>
        public IQueryable<UserDTO> RunQuery<UserDTO>(string @select, DynamicParameters @params)
        {
            using var connection = new DapperContext().GetConnection;
            return connection.Query<UserDTO>(@select, @params).AsQueryable();
        }
        public List<T> RunSelectToList(string @select, object param = null)
        {
            if (!@select.Trim().ToUpper().StartsWith("SELECT"))
                throw new Exception("É possivel utilizar somente SELECT com este metodo.");

            using (IDbConnection connection = new DapperContext().GetConnection)
            {
                return connection.Query<T>(@select, param).ToList();
            }
        }

        /*public IEnumerable<UserDTO> RunSelect<UserDTO>(string @select)
        {
            if (!@select.Trim().ToUpper().StartsWith("SELECT"))
                throw new Exception("É possivel utilizar somente SELECT com este metodo.");
            
            using (IDbConnection connection = new DapperContext().GetConnection)
            {
                return connection.Query<UserDTO>(@select).AsQueryable();
            }
        }*/

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }
    }
}

﻿using ATS.CrossCutting.IoC.Utils;
using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.CargaAvulsa.ReciboCargaAvulsa
{
    public class RelatorioReciboCargaAvulsa
    {
        private const string DataSet = "DtsReciboCargaAvulsa";
        private const string DataSetRodape = "DtsReciboCargaAvulsaRodape";
        private const string ReportPath = "ATS.CrossCutting.Reports.CargaAvulsa.ReciboCargaAvulsa.RelatorioReciboCargaAvulsa.rdlc";

        public List<RelatorioReciboCargaAvulsaDto> DadosRelatorio { get; }
        public string TipoArquivo { get; }
        public string Logo { get; }
        public Tuple<string, string, bool>[] Parametros { get; }
        public List<Tuple<object, string>> DataSources { get; }
        public string Usuario { get; }

        public RelatorioReciboCargaAvulsa(List<RelatorioReciboCargaAvulsaDto> dadosRelatorio, List<RelatorioReciboCargaAvulsaRodapeDto> dadosRodapeRelatorio, string usuario, string logo)
        {
            DadosRelatorio = dadosRelatorio;
            Usuario = usuario;
            Logo = logo;
            TipoArquivo = ConstantesUtils.FormatoPdfMinusculo;
            Parametros = new Tuple<string, string, bool>[3];
            DataSources = new List<Tuple<object, string>> { new Tuple<object, string>(DadosRelatorio, DataSet), new Tuple<object, string>(dadosRodapeRelatorio, DataSetRodape) };
        }

        public byte[] GetReport()
        {
            Parametros[0] = new Tuple<string, string, bool>(ConstantesUtils.ParametroLogoRelatorio, Logo, true);
            Parametros[1] = new Tuple<string, string, bool>("Usuario", Usuario, true);
            Parametros[2] = new Tuple<string, string, bool>("DataHora", DateTime.Now.ToString("dd/MM/yyyy HH:mm"), true);
            var bytes = new Base.Reports().GetReport(DataSources, Parametros, true, ReportPath, TipoArquivo);

            return bytes;
        }
    }
}

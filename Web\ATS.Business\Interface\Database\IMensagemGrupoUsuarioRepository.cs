﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IMensagemGrupoUsuarioRepository : IRepository<MensagemGrupoUsuario>
    {
        IQueryable<MensagemGrupoUsuario> GetGruposDoUsuario(int idUsuario);
        void DeletarGrupo(int idGrupo);
        MensagemGrupoUsuario AddGrupo(MensagemGrupoUsuario grupoUsuarios);
        MensagemGrupoDestinatario AddUsuarioParaGrupo(MensagemGrupoDestinatario mensagemGrupoDestinatario);
        void RemoverUsuarioDoGrupo(MensagemGrupoDestinatario mensagemGrupoDestinatario);
        IEnumerable<MensagemGrupoUsuario> GetGruposUsuarioPorIdUsuario(int idUsuario);
    }
}

﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Collections.Generic;
using ATS.Data.Context;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.EntityFramework
{
    public class TransacaoCartaoRepository : Repository<TransacaoCartao>, ITransacaoCartaoRepository
    {
        public TransacaoCartaoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<TransacaoCartao> All(Expression<Func<TransacaoCartao, bool>> @where, bool @readonly = false)
        {
            return Find(@where, @readonly);
        }

        public List<TransacaoCartao> GetByIdEvento(int idevento)
        {
            return All().Where(c => c.IdViagemEvento == idevento).ToList();
        }

        public List<TransacaoCartao> GetByIdCarga(int idcarga)
        {
            return All().Where(c => c.IdCargaAvulsa == idcarga).ToList();
        }

        public TransacaoCartao GetByIdEventoAndTipoProcessamento(int idevento, ETipoProcessamentoCartao tipoProcessamentoCartao)
        {
            return All().Where(c => c.IdViagemEvento == idevento).FirstOrDefault(c => c.TipoProcessamentoCartao == tipoProcessamentoCartao);
        }

        public TransacaoCartao GetById(int idtransacaocartao)
        {
            return All().FirstOrDefault(c => c.IdTransacaoCartao == idtransacaocartao);
        }
        
        public TransacaoCartao GetByResgate(int idresgate)
        {
            return All().FirstOrDefault(c => c.IdResgate == idresgate);
        }

        public TransacaoCartao GetByIdIncludeViagem(int idtransacaoCartao)
        {
            return All().Where(c => c.IdTransacaoCartao == idtransacaoCartao).Include(c => c.ViagemEvento).FirstOrDefault();
        }

        public int GetCountByIdEvento(int idevento)
        {
            return (from transacaoCartao in All()
                    where transacaoCartao.IdViagemEvento == idevento
                    select transacaoCartao.IdTransacaoCartao)
                    .ToList().Count;
        }
    }
}
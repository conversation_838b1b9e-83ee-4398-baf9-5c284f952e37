﻿using ATS.Domain.Interface.Database;
using FluentValidation;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Mobile.Request.Validators
{
    public class DespesaCriarAplicativoModelValidator : AbstractValidator<DespesaCriarAplicativoModel>, IDespesaCriarAplicativoModelValidator
    {
        public DespesaCriarAplicativoModelValidator(ICategoriaRepository categoriaRepository, IUsuarioRepository usuarioRepository)
        {
            RuleFor(o => o.IdUsuario)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .Must(c => usuarioRepository.Any(a => a.IdUsuario == c))
                .WithMessage("Usuário informada é inválido!");

            RuleFor(o => o.Despesa)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotEmpty().NotNull().WithMessage("Despesa deve ser informada!")
                .DependentRules(k =>
                {
                    RuleFor(o => o.Despesa.IdCategoria)
                        .Cascade(CascadeMode.StopOnFirstFailure)
                        .Must(c => categoriaRepository.TodosAtivos().Contains(c))
                        .WithMessage("Categoria informada é inválida!");

                    RuleFor(o => o.Despesa.Descricao)
                        .Cascade(CascadeMode.StopOnFirstFailure)
                        .NotNull().NotEmpty().WithMessage($"O campo Descricao deve ser informado!")
                        .Length(1, 500).WithMessage("O campo Descricao deve ter no máximo 500 caracteres!");

                    When(o => !string.IsNullOrWhiteSpace(o.Despesa.LinkNota), () =>
                    {
                        RuleFor(o => o.Despesa.LinkNota)
                            .Cascade(CascadeMode.StopOnFirstFailure)
                            .Length(1, 1000).WithMessage("O campo Descricao deve ter no máximo 1000 caracteres!");
                    });

                    When(o => !string.IsNullOrWhiteSpace(o.Despesa.FotoNotaBase64), () =>
                    {
                        RuleFor(o => o.Despesa.FotoNotaBase64)
                            .Cascade(CascadeMode.StopOnFirstFailure)
                            .Must(c => c.IsBase64()).WithMessage("O campo FotoNotaBase64 deve ser um Base 64 válido!");
                    });
                });

            RuleFor(o => o.Localizacao)
                .Cascade(CascadeMode.StopOnFirstFailure)
                .NotNull().WithMessage("É obrigatório o envio da localização!")
                .DependentRules(c =>
                {
                    RuleFor(o => o.Localizacao.Latitude)
                        .Must(a => a.IsValidLatitude()).WithMessage("A Latitude enviada deve ser válida!");

                    RuleFor(o => o.Localizacao.Longitude)
                        .Must(a => a.IsValidLongitude()).WithMessage("A Longitude enviada deve ser válida!");
                });
        }
    }

    public interface IDespesaCriarAplicativoModelValidator : IValidator<DespesaCriarAplicativoModel>
    {

    }
}
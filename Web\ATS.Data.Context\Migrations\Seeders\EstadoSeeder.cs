﻿using ATS.Domain.Entities;
using System;
using System.Data.Entity.Migrations;
using System.Linq;
using ATS.Domain.Enum;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class EstadoSeeder
    {
        public void Execute(AtsContext context)
        {
            if (context.Estado.Any())
                return;

            int idEstado = 1;

            context.Estado.AddOrUpdate(new[]
            {
                new Estado { IdEstado = idEstado++, IBGE = 11, Nome = "Rondônia", IdPais = 30, Sigla = "RO", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte},
                new Estado { IdEstado = idEstado++, IBGE = 12, Nome = "Acre", IdPais = 30, Sigla = "AC", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte },
                new Estado { IdEstado = idEstado++, IBGE = 13, Nome = "Amazonas", IdPais = 30, Sigla = "AM", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte },
                new Estado { IdEstado = idEstado++, IBGE = 14, Nome = "Roraima", IdPais = 30, Sigla = "RR", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte },
                new Estado { IdEstado = idEstado++, IBGE = 15, Nome = "Pará", IdPais = 30, Sigla = "PA", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte },
                new Estado { IdEstado = idEstado++, IBGE = 16, Nome = "Amapá", IdPais = 30, Sigla = "AP", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte },
                new Estado { IdEstado = idEstado++, IBGE = 17, Nome = "Tocantins", IdPais = 30, Sigla = "TO", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Norte },
                new Estado { IdEstado = idEstado++, IBGE = 21, Nome = "Maranhão", IdPais = 30, Sigla = "MA", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 22, Nome = "Piauí", IdPais = 30, Sigla = "PI", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 23, Nome = "Ceará", IdPais = 30, Sigla = "CE", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 24, Nome = "Rio Grande do Norte", IdPais = 30, Sigla = "RN", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 25, Nome = "Paraíba", IdPais = 30, Sigla = "PB", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 26, Nome = "Pernambuco", IdPais = 30, Sigla = "PE", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 27, Nome = "Alagoas", IdPais = 30, Sigla = "AL", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 28, Nome = "Sergipe", IdPais = 30, Sigla = "SE", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 29, Nome = "Bahia", IdPais = 30, Sigla = "BA", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Nordeste },
                new Estado { IdEstado = idEstado++, IBGE = 31, Nome = "Minas Gerais", IdPais = 30, Sigla = "MG", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sudeste },
                new Estado { IdEstado = idEstado++, IBGE = 32, Nome = "Espírito Santo", IdPais = 30, Sigla = "ES", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sudeste },
                new Estado { IdEstado = idEstado++, IBGE = 33, Nome = "Rio de Janeiro", IdPais = 30, Sigla = "RJ", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sudeste },
                new Estado { IdEstado = idEstado++, IBGE = 35, Nome = "São Paulo", IdPais = 30, Sigla = "SP", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sudeste },
                new Estado { IdEstado = idEstado++, IBGE = 41, Nome = "Paraná", IdPais = 30, Sigla = "PR", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sul },
                new Estado { IdEstado = idEstado++, IBGE = 42, Nome = "Santa Catarina", IdPais = 30, Sigla = "SC", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sul },
                new Estado { IdEstado = idEstado++, IBGE = 43, Nome = "Rio Grande do Sul", IdPais = 30, Sigla = "RS", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.Sul },
                new Estado { IdEstado = idEstado++, IBGE = 50, Nome = "Mato Grosso do Sul", IdPais = 30, Sigla = "MS", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.CentroOeste },
                new Estado { IdEstado = idEstado++, IBGE = 51, Nome = "Mato Grosso", IdPais = 30, Sigla = "MT", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.CentroOeste },
                new Estado { IdEstado = idEstado++, IBGE = 52, Nome = "Goiás", IdPais = 30, Sigla = "GO", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.CentroOeste },
                new Estado { IdEstado = idEstado++, IBGE = 53, Nome = "Distrito Federal", IdPais = 30, Sigla = "DF", DataAlteracao = DateTime.Now.Date, Regiao = ERegiaoBrasil.CentroOeste }
            });

            context.SaveChanges();
        }
    }
}
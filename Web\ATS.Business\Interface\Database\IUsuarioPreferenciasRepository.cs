﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IUsuarioPreferenciasRepository : IRepository<UsuarioPreferencias>
    {
        IQueryable<UsuarioPreferencias> GetPreferenciasByIdUsuario(int idUsuario, string campo = null);
        void SaveUsuarioPreferencias(List<UsuarioPreferencias> usuarioPreferencias);
        IQueryable<UsuarioPreferencias> GetPreferenciasByIdUsuarioPrefixLike(int idUsuario, string prefix);
    }
}

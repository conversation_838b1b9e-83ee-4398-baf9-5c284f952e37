﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Application.Application;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;

namespace ATS.WS.Services
{
    public class SrvTipoParada : SrvBase
    {

        public Retorno<TipoParadaResponseDTO> Integrar(TipoParadaIntegracaoMobileModel @params)
        {
            TipoParada tipoParadaRequest = Mapper.Map<TipoParadaIntegracaoMobileModel, TipoParada>(@params);
            tipoParadaRequest.IdEmpresa = new EmpresaApp().GetIdPorCNPJ(@params.CNPJEmpresa) ?? 0;
            

            if (@params.IdTipoParada.HasValue)
            {
                new TipoParadaApp().Editar(tipoParadaRequest);
            }
            else if (!@params.IdTipoParada.HasValue)
            {
                new TipoParadaApp().Cadastrar(tipoParadaRequest);
            }
                
            return new Retorno<TipoParadaResponseDTO>(true,
                    Mapper.Map<TipoParada, TipoParadaResponseDTO>(tipoParadaRequest));
        }

        public ValidationResult Cadastrar(TipoParadaIntegrarRequestModel requestModel)
        {
            return new TipoParadaApp().Cadastrar(Mapper.Map<TipoParadaIntegrarRequestModel, TipoParada>(requestModel));
        }

        

        public ValidationResult Editar(TipoParadaIntegrarRequestModel requestModel)
        {
            return new TipoParadaApp().Editar(Mapper.Map<TipoParadaIntegrarRequestModel, TipoParada>(requestModel));
        }


    }
}
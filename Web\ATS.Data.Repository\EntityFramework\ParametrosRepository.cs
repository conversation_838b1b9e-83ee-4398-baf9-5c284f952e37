using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Validation;

namespace ATS.Data.Repository.EntityFramework
{
    public class ParametrosRepository: Repository<Parametros>, IParametrosRepository
    {
        public ParametrosRepository(AtsContext context) : base(context)
        {
        }
        
        public class ParametrosResponse
        {
            public string Parametro { get; set; }
            public object Valor { get; set; }
        }
        
        public ValidationResult SetValue<T>(T valor, string nomeTabela, string chave, int idRegistro, int? idEmpresa)
        {
            try
            {
                var existe = false;
                var parametro = new Parametros();

                if (AnyParametro(nomeTabela, chave, idRegistro, idEmpresa))
                {
                    existe = true;
                    parametro = GetParametro(nomeTabela, chave, idRegistro, idEmpresa);
                }
                
                parametro.Chave = chave;
                parametro.IdRegistro = idRegistro;
                parametro.IdRegistroStr = null;
                parametro.IdEmpresa = idEmpresa;
                parametro.NomeTabela = nomeTabela;
            
                if (typeof(T) == typeof(string))
                    parametro.ValorString = Convert.ToString(valor);
            
                if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                    parametro.ValorDecimal = Convert.ToDecimal(valor);
            
                if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                    parametro.ValorDateTime = Convert.ToDateTime(valor);
                
                if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                    parametro.ValorBoolean = Convert.ToBoolean(valor);

                if (!existe)
                    Add(parametro);
                else
                    Update(parametro);
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetValue<T>(T valor, string nomeTabela, string chave, string idRegistro, int? idEmpresa)
        {
            try
            {
                var existe = false;
                var parametro = new Parametros();

                if (AnyParametro(nomeTabela, chave, idRegistro, idEmpresa))
                {
                    existe = true;
                    parametro = GetParametro(nomeTabela, chave, idRegistro, idEmpresa);
                }
                
                parametro.Chave = chave;
                parametro.IdRegistroStr = idRegistro;
                parametro.IdRegistro = null;
                parametro.IdEmpresa = idEmpresa;
                parametro.NomeTabela = nomeTabela;
            
                if (typeof(T) == typeof(string))
                    parametro.ValorString = Convert.ToString(valor);
            
                if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                    parametro.ValorDecimal = Convert.ToDecimal(valor);
            
                if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                    parametro.ValorDateTime = Convert.ToDateTime(valor);
                
                if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                    parametro.ValorBoolean = Convert.ToBoolean(valor);

                if (!existe)
                    Add(parametro);
                else
                    Update(parametro);
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public Dictionary<string, object> GetValues<T>(string nomeTabela, IList<string> Chaves, int idRegistro, int? idEmpresa)
        {
            var registrosQuery = All().Where(c => c.IdEmpresa == idEmpresa && c.NomeTabela == nomeTabela &&
                                                             c.IdRegistro == idRegistro &&
                                                             Chaves.Contains(c.Chave)).ToList();

            var resposta = new Dictionary<string, object>();
            
            foreach (var parametros in registrosQuery)
            {
                var response = new ParametrosResponse
                {
                    Parametro = parametros.Chave,
                };

                if (typeof(T) == typeof(string))
                    response.Valor = parametros.ValorString;
            
                else if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                    response.Valor = parametros.ValorDecimal;
            
                else if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                    response.Valor = parametros.ValorDateTime;

                else if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                    response.Valor = parametros.ValorBoolean;

                else response.Valor = null;
                
                resposta.Add(response.Parametro, response.Valor);
            }

            return resposta;
        }
        
        public Dictionary<string, object> GetValues<T>(string nomeTabela, IList<string> Chaves, string idRegistro, int? idEmpresa)
        {
            var registrosQuery = All().Where(c => c.IdEmpresa == idEmpresa && c.NomeTabela == nomeTabela &&
                                                             c.IdRegistroStr == idRegistro &&
                                                             Chaves.Contains(c.Chave)).ToList();

            var resposta = new Dictionary<string, object>();
            
            foreach (var parametros in registrosQuery)
            {
                var response = new ParametrosResponse
                {
                    Parametro = parametros.Chave
                };

                if (typeof(T) == typeof(string))
                    response.Valor = parametros.ValorString;
            
                else if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                    response.Valor = parametros.ValorDecimal;
            
                else if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                    response.Valor = parametros.ValorDateTime;

                else if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                    response.Valor = parametros.ValorBoolean;
                
                else response.Valor = null;
                
                resposta.Add(response.Parametro, response.Valor);
            }

            return resposta;
        }
        
        public object GetValue<T>(string nomeTabela, string chave, int idRegistro, int? idEmpresa)
        {
            var registroQuery = All().FirstOrDefault(c => c.IdEmpresa == idEmpresa && c.IdRegistro == idRegistro && c.NomeTabela == nomeTabela && c.Chave == chave);

            if (registroQuery == null)
                return null;
                
            if (typeof(T) == typeof(string))
                return registroQuery.ValorString;

            if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                return registroQuery.ValorDecimal;

            if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                return registroQuery.ValorDateTime;
            
            if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                return registroQuery.ValorBoolean;
            
            return null;
        }
        
        public object GetValue<T>(string nomeTabela, string chave, string idRegistro, int? idEmpresa)
        {
            var registroQuery = All().FirstOrDefault(c => c.IdEmpresa == idEmpresa && c.IdRegistroStr == idRegistro && c.NomeTabela == nomeTabela && c.Chave == chave);

            if (registroQuery == null)
                return null;
                
            if (typeof(T) == typeof(string))
                return registroQuery.ValorString;

            if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                return registroQuery.ValorDecimal;

            if (typeof(T) == typeof(DateTime) || typeof(T) == typeof(DateTime?))
                return registroQuery.ValorDateTime;
            
            if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                return registroQuery.ValorBoolean;
            
            return null;
        }

        public bool AnyParametro(string nomeTabela, string chave, int idRegistro, int? idEmpresa)
        {
            var registroQuery = All().Any(c => c.IdEmpresa == idEmpresa && c.IdRegistro == idRegistro && c.NomeTabela == nomeTabela && c.Chave == chave);

            return registroQuery;
        }

        public bool AnyParametro(string nomeTabela, string chave, string idRegistro, int? idEmpresa)
        {
            var registroQuery = All().Any(c => c.IdEmpresa == idEmpresa && c.IdRegistroStr == idRegistro && c.NomeTabela == nomeTabela && c.Chave == chave);

            return registroQuery;
        }

        public Parametros GetParametro(string nomeTabela, string chave, int idRegistro, int? idEmpresa)
        {
            var registroQuery = All().FirstOrDefault(c => c.IdEmpresa == idEmpresa && c.IdRegistro == idRegistro && c.NomeTabela == nomeTabela && c.Chave == chave);

            return registroQuery;
        }

        public Parametros GetParametro(string nomeTabela, string chave, string idRegistro, int? idEmpresa)
        {
            var registroQuery = All().FirstOrDefault(c => c.IdEmpresa == idEmpresa && c.IdRegistroStr == idRegistro && c.NomeTabela == nomeTabela && c.Chave == chave);

            return registroQuery;
        }
    }
}
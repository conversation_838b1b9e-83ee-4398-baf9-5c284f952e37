﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web.Http;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.ControllersATS
{
    public class DespesasViagemAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IDespesasViagemApp _despesasViagemApp;

        public DespesasViagemAtsController(IUserIdentity userIdentity, IDespesasViagemApp despesasViagemApp)
        {
            _userIdentity = userIdentity;
            _despesasViagemApp = despesasViagemApp;
        }

        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue || _userIdentity.IdEmpresa == 0)
                    return ResponderErro("Funcionalidade disponível apenas para usuários de perfil empresa!");
                
                return ResponderSucesso(_despesasViagemApp.ConsultarPortadorGrid(take,page,order,filters,null,null));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridDespesaViagem(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<DespesasViagemGridReportRequestModel>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _despesasViagemApp
                .GerarRelatorioGridDespesaViagem(filtrosGridModel.Take,filtrosGridModel.Page,filtrosGridModel.Order,filtrosGridModel.Filters,filtrosGridModel.Extensao,filtrosGridModel.IdEmpresa,filtrosGridModel.IdUsuario);
        
            return File(report, ConstantesUtils.ExcelMimeType, $"Relatorio Desapesas de viagem.{filtrosGridModel.Extensao}");
        }
        
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult ResgatarValor(ResgatarValorDespesasViagemRequest request)
        {
            try
            {
                var result = _despesasViagemApp.ResgatarValor(request);
                
                if(!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("Resgate realizado com sucesso!");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        [System.Web.Mvc.HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarImagensExtrato(string hashID)
        {
            try
            {
                var listResultImagemExtrato = _despesasViagemApp.ConsultarImagensVinculadasExtrato(hashID);
                
                if(!listResultImagemExtrato.ListImagensBase64.Any())
                    return ResponderErro("Nenhuma imagem encontrada!");

                return ResponderSucesso(listResultImagemExtrato);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [System.Web.Mvc.HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarResgatePortador(string cpfCnpjPortador)
        {
            try
            {
                var result = _despesasViagemApp.ConsultarResgatePortador(cpfCnpjPortador.OnlyNumbers());
                
                if(!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioExtratoGrid(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioGridExtratoDTO>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            filtrosGridModel.IdUsuarioLogado = _userIdentity.IdUsuario;

            var report = _despesasViagemApp.ConsultarRelatorioExtratoGrid(filtrosGridModel);
                
            var mimeType = ConstantesUtils.PdfMimeType;
            
            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
                case "csv":
                    mimeType = ConstantesUtils.CsvMimeType;
                    break;
                case "ofx":
                    mimeType = ConstantesUtils.OfxMimeType;
                    break;
            }


            return File(report, mimeType, $"Relatório do extrato.{filtrosGridModel.Extensao}");
        }


        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioExtratoGridV2(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioGridExtratoDetalhadoDTO>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            filtrosGridModel.IdUsuarioLogado = _userIdentity.IdUsuario;

            var report = _despesasViagemApp.ConsultarRelatorioExtratoV2Grid(filtrosGridModel);

            var mimeType = ConstantesUtils.PdfMimeType;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }


            return File(report, mimeType, $"Relatório do extrato.{filtrosGridModel.Extensao}");
        }
        
        [System.Web.Mvc.HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GerarRelatorioExtratoDetalhadoOfx([FromBody] RelatorioGridExtratoDetalhadoDTO request)
        {
            request.IdUsuarioLogado = _userIdentity.IdUsuario;
            var result = _despesasViagemApp.GerarRelatorioExtratoDetalhadoOfx(request);
            return Responder(result.Success, result.Messages.FirstOrDefault(), null);
        }
    }
}
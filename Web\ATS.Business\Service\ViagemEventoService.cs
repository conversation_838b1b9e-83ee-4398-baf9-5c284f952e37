﻿using ATS.Data.Repository.External.SistemaInfo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using NLog;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using Autofac;
using Microsoft.Ajax.Utilities;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;

namespace ATS.Domain.Service
{
    public class ViagemEventoService : ServiceBase, IViagemEventoService
    {
        private readonly ILifetimeScope _scope;
        private readonly IViagemRepository _viagemRepository;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IDeclaracaoCiotRepository _declaracaoCiotRepository;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IWebhookService _webhookService;
        private readonly WebhookActionDependencies _webhookActionDependencies;
        private readonly IProtocoloEventoRepository _protocoloEventoRepository;

        private IPagamentoFreteService _pagamentoFreteService => _scope.Resolve<IPagamentoFreteService>();

        public ViagemEventoService(ILifetimeScope scope, IViagemRepository viagemRepository, IViagemEventoRepository viagemEventoRepository, IDeclaracaoCiotRepository declaracaoCiotRepository,
            IEmpresaRepository empresaRepository, IWebhookService webhookService, WebhookActionDependencies webhookActionDependencies, IProtocoloEventoRepository protocoloEventoRepository)
        {
            _scope = scope;
            _viagemRepository = viagemRepository;
            _viagemEventoRepository = viagemEventoRepository;
            _declaracaoCiotRepository = declaracaoCiotRepository;
            _empresaRepository = empresaRepository;
            _webhookService = webhookService;
            _webhookActionDependencies = webhookActionDependencies;
            _protocoloEventoRepository = protocoloEventoRepository;
        }
        
        public ViagemEvento CreateAbonoEvent(decimal valor, ViagemEvento viagemEVentoSaldo)
        {
            var viagemEvento = new ViagemEvento();

            viagemEvento.TipoEventoViagem = ETipoEventoViagem.Abono;
            viagemEvento.ValorPagamento = valor;
            viagemEvento.ValorTotalPagamento = null;
            viagemEvento.IdViagem = viagemEVentoSaldo.IdViagem;
            viagemEvento.IdViagemEventoOrigem = viagemEVentoSaldo.IdViagemEvento;
            viagemEvento.Status = EStatusViagemEvento.Aberto;
            viagemEvento.OrigemPagamento = EOrigemIntegracao.ATS;
            viagemEvento.HabilitarPagamentoCartao = true;
            viagemEvento.IdEmpresa = viagemEVentoSaldo.IdEmpresa;
            viagemEvento.NumeroRecibo = viagemEVentoSaldo.NumeroRecibo;

            _viagemEventoRepository.Add(viagemEvento);

            return viagemEvento;
        }
        
        public List<ViagemEvento> GetEventosViagem(int idViagem)
        {
            return _viagemEventoRepository
                .Find(x => x.IdViagem == idViagem)
                .Include(x => x.ViagemDocumentos)
                .Include(x => x.ViagemValoresAdicionais)
                .ToList();
        }

        public List<DeclaracaoCiot> GetCiotViagem(int idViagem)
        {
            return _declaracaoCiotRepository
                .Find(x => x.IdViagem == idViagem)
                .ToList();
        }

        public IQueryable<ViagemEvento> GetEventosViagemQuery(List<int> idsViagemEvento)
        {
            return _viagemEventoRepository
                .Find(x => idsViagemEvento.Contains(x.IdViagemEvento))
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.Empresa)
                .Include(x => x.Viagem.Filial)
                .Include(x => x.EstabelecimentoBase);
        }

        public List<ViagemEvento> GetEventosViagem(List<int> idsViagemEvento)
        {
            return GetEventosViagemQuery(idsViagemEvento).ToList();
        }

        public object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var eventos = _viagemEventoRepository
                .GetAll().Where(x => x.DataLibSemChave.HasValue)
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.Empresa);

            if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                eventos = eventos.OrderBy(x => x.IdViagemEvento);
            else
                eventos = eventos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            eventos = eventos.AplicarFiltrosDinamicos<ViagemEvento>(filters);

            return new
            {
                totalItems = eventos.Count(),
                items = eventos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdViagemEvento,
                    x.Token,
                    NomeFantasiaEmpresa = x.Viagem.Empresa.NomeFantasia,
                    DataLibSemChave = x.DataLibSemChave.Value.ToString("dd/MM/yyyy HH:mm:ss"),
                    Status = x.Status.DescriptionAttr(),
                    ValorPagamento = x.ValorPagamento.ToString("G"),
                    LiberarPagtoSemChave = x.LiberarPagtoSemChave ? "Sim" : "Não",
                    x.Viagem?.NomeMotorista,
                    Placa = x.Viagem?.Placa?.ToPlacaFormato(),
                    UsuLibSemChave = x.UsuarioLibSemChave?.Nome
                })
            };
        }

        public IEnumerable<ViagemEvento> GetEventosViagemSemChave(int idEmpresa)
        {
            return _viagemEventoRepository
                .Find(x => x.IdEmpresa == idEmpresa && x.DataLibSemChave.HasValue)
                .Include(x => x.Viagem)
                .Include(x => x.EstabelecimentoBase)
                .ToList();
        }

        public IQueryable<ViagemEvento> GetEventoPorTokenPagSemChave(string token)
        {
            return _viagemEventoRepository
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemCarretas)
                .Include(x => x.Viagem.ViagemEstabelecimentos)
                .Include(x => x.Viagem.ViagemEstabelecimentos.Select(p => p.Estabelecimento))
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.UsuarioLibSemChave)
                .Where(x => x.Token == token);
        }

        public ViagemEvento Get(int idViagemEvento)
        {
            return _viagemEventoRepository
                .Include(x => x.Viagem)
                .FirstOrDefault(x => x.IdViagemEvento == idViagemEvento);
        }

        public IQueryable<ViagemEvento> GetQueryable(int idViagemEvento)
        {
            return _viagemEventoRepository
                .Where(x => x.IdViagemEvento == idViagemEvento);
        }

        public IQueryable<ViagemEvento> GetQuery()
        {
            return _viagemEventoRepository.All();
        }
        
        public ViagemEvento GetByToken(string token)
        {
            return _viagemEventoRepository
                .Find(x => x.Token == token)
                .Include(x => x.ViagemDocumentos)
                .FirstOrDefault();
        }

        public ValidationResult Update(ViagemEvento viagemEvento)
        {
            _viagemEventoRepository.Update(viagemEvento);
            return new ValidationResult();
        }

        public ViagemEvento AfterUpdate(ViagemEvento viagemEvento, ViagemEvento oldEvento)
        {
            if (viagemEvento.Status == EStatusViagemEvento.Baixado && 
                oldEvento.Status == EStatusViagemEvento.Baixado)
                return viagemEvento;

            if (viagemEvento.Status == EStatusViagemEvento.Cancelado &&
                oldEvento.Status == EStatusViagemEvento.Cancelado)
                return viagemEvento;

            if (viagemEvento.Status == EStatusViagemEvento.Agendado &&
                oldEvento.Status == EStatusViagemEvento.Agendado)
                return viagemEvento;

            // Só entra na regra de notificação se for um Agendamento sendo alterado para Baixado.
            // Coloquei dessa forma porque ninguem utilizava webhook em produção e o primeiro cliente precisava apenas para este cenário.
            // Se você for liberar para todo tipo de atualização de evento, remova esse IF, preserve os acima,
            // crie um parâmetros na empresa para configurar os tipos de eventos necessários e inicialize-o de forma a manter os clientes de produção recebendo apenas
            // as notificações de baixa de pagamento agendado e não impacta-los.
            if (viagemEvento.Status != EStatusViagemEvento.Baixado ||
                oldEvento.Status != EStatusViagemEvento.Agendado)
                return viagemEvento;


            try
            {
                // 1 - Notificar se estiver alterando o status de qualquer tipo de evento para baixado ou cancelado
                // 2 - Notificar se estiver bloqueando um abono
                
                var token = string.IsNullOrEmpty(viagemEvento.Viagem?.Empresa?.TokenMicroServices)
                    ? _empresaRepository.GetTokenMicroServices(viagemEvento.IdEmpresa)
                    : viagemEvento.Viagem.Empresa.TokenMicroServices;
                if (token.IsNullOrWhiteSpace())
                    token = SistemaInfoConsts.TokenAdministradora;
                
                var notificar = viagemEvento.Status == EStatusViagemEvento.Baixado ||
                                viagemEvento.Status == EStatusViagemEvento.Cancelado;

                if (!notificar)
                    notificar = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Abono
                                && viagemEvento.Status == EStatusViagemEvento.Bloqueado;
                
                if (notificar)
                {
                    var tipoEventoWebhook = viagemEvento.Status.In(EStatusViagemEvento.Cancelado, EStatusViagemEvento.Bloqueado)
                        ? WebhookTipoEvento.CancelarEventoViagem
                        : WebhookTipoEvento.BaixarEventoViagem;

                    //var webhook = _webhookService.GetByIdRegistro(viagemEvento.IdViagem, tipoEventoWebhook);
                    //var webhook = _webhookService.GetByIdRegistro(viagemEvento.Viagem.IdEmpresa, tipoEventoWebhook);
                    var webhookParameters = _empresaRepository.GetWebhookParameters(viagemEvento.Viagem.IdEmpresa);

                    if (!string.IsNullOrWhiteSpace(webhookParameters.BaixarEventoViagemEndpoint))
                    {
                        if (viagemEvento.Viagem == null)
                            viagemEvento.Viagem = _viagemRepository.Get(viagemEvento.IdViagem, viagemEvento.IdEmpresa);

                        string infoAdicional = string.Empty;
                        
                        // Baixa de {tipoEvento} R$ {valor} - ({IdViagemEvento})
                        // If numeroRecibo.HasValues +++ .Recibo: {numeroRecibo}
                        // Se tiver CIOT +++++ . CIOT: {numeroCiot}
                        switch (viagemEvento.Status)
                        {                            
                              case EStatusViagemEvento.Baixado:
                                  infoAdicional += "Baixa";
                                  break;
                            case EStatusViagemEvento.Cancelado:
                                infoAdicional += "Cancelamento";
                                break;
                            case EStatusViagemEvento.Bloqueado:
                                infoAdicional += "Bloqueio";
                                break;
                        }

                        var pagamentoService = _pagamentoFreteService;
                        var somarPedagio = pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagemEvento.Viagem);

                        infoAdicional += " de {0} R$ {1}".FormatEx(
                            viagemEvento.TipoEventoViagem,
                            (viagemEvento.ValorTotalPagamento ?? viagemEvento.ValorPagamento) + (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0));

                        if (!string.IsNullOrWhiteSpace(viagemEvento.Viagem.DocumentoCliente))
                            infoAdicional += ". Documento: " + viagemEvento.Viagem.DocumentoCliente;
                        else if (!string.IsNullOrWhiteSpace(viagemEvento.NumeroRecibo))
                            infoAdicional += ". Recibo: " + viagemEvento.NumeroRecibo;

                        var ciotRep = _declaracaoCiotRepository;
                        var ciot = ciotRep.ConsultarCiot(viagemEvento.IdViagem);
                        if (ciot != null)
                            infoAdicional += $". CIOT: {ciot.Ciot}/{ciot.Verificador}";

                        infoAdicional += ". IdViagemEvento: " + viagemEvento.IdViagemEvento;

                        // ValorQuebraMercadoria
                        var valorQuebraMercadoria = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo
                            ? (viagemEvento.QuebraMercadoriaAbonada ?? false)
                                ? viagemEvento.Viagem.ValorQuebraMercadoriaCalculado
                                : viagemEvento.Viagem.ValorQuebraMercadoriaCalculado - viagemEvento.Viagem.ValorQuebraMercadoria
                            : null;

                        if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.Abono)
                        {
                            valorQuebraMercadoria = viagemEvento.Viagem.ValorQuebraMercadoriaCalculado;
                            /*valorQuebraMercadoria = viagemEvento.ValorPagamento -
                                                    viagemEvento.ValorTotalPagamento.GetValueOrDefault();

                            if (valorQuebraMercadoria.GetValueOrDefault() <= 0)
                                valorQuebraMercadoria = null;*/
                        }

                        // ValorQuebraAbonada
                        var valorQuebraAbonada = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo &&
                                                 viagemEvento.QuebraMercadoriaAbonada.GetValueOrDefault(false)
                            ? viagemEvento.Viagem.ValorQuebraMercadoria.GetValueOrDefault(0)
                            : (decimal?) null;
                        if (viagemEvento.TipoEventoViagem == ETipoEventoViagem.Abono)
                        {
                            // viagemEvento.ValorPagamento = Valor máximo que pode ser abonado
                            var saldoRestanteAbono = viagemEvento.ValorPagamento -
                                                     viagemEvento.ValorTotalPagamento.GetValueOrDefault();

                            valorQuebraAbonada =
                                viagemEvento.Viagem.ValorQuebraMercadoriaCalculado.GetValueOrDefault() -
                                saldoRestanteAbono;
                        }
                        
                        // Requisição
                        var requisicao = new ViagemEventoConsultaModel
                        {
                            IdViagem = viagemEvento.IdViagem,
                            IdViagemEvento = viagemEvento.IdViagemEvento,
                            IdEstabelecimentoBase = viagemEvento.IdEstabelecimentoBase,
                            Token = viagemEvento.Token,
                            Status = viagemEvento.Status,
                            TipoEvento = viagemEvento.TipoEventoViagem,
                            DataPagamento = viagemEvento.DataHoraPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                            HoraPagamento = viagemEvento.DataHoraPagamento?.TimeOfDay.ToString(@"hh\:mm\:ss"),
                            IdProtocolo = viagemEvento.IdProtocolo,
                            NumeroRecibo = viagemEvento.NumeroRecibo,
                            ValorPagamento = viagemEvento.ValorPagamento,
                            ValorBruto = viagemEvento.ValorBruto,
                            ValorTotalPagamento = (viagemEvento.ValorTotalPagamento ?? 0) + (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                            ValorQuebraAbonada = valorQuebraAbonada,
                            DataValidade = viagemEvento.DataValidade?.ToString("yyyy-MM-dd HH:mm:ss"),
                            INSS = viagemEvento.INSS,
                            IRRPF = viagemEvento.IRRPF,
                            SESTSENAT = viagemEvento.SESTSENAT,
                            Instrucao = viagemEvento.Instrucao,
                            HabilitarPagamentoCartao = viagemEvento.HabilitarPagamentoCartao,
                            PesoChegada = viagemEvento.Viagem.PesoChegada,
                            PesoDiferenca = viagemEvento.Viagem.PesoDiferenca,
                            ValorDifFreteMotorista = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo
                                ? viagemEvento.Viagem.DifFreteMotorista
                                : null,
                            ValorQuebraMercadoria = valorQuebraMercadoria,
                            DataDescarga = viagemEvento.Viagem.DataDescarga?.ToString("yyyy-MM-dd")
                        };

                        var webhook = new Webhook
                        {
                            Endpoint = webhookParameters.BaixarEventoViagemEndpoint,
                            Headers = webhookParameters.Headers ?? string.Empty,
                            IdRegistro = viagemEvento.IdViagem,
                            Tipo = tipoEventoWebhook,
                            Verbo = "POST",
                            Aplicacao = "Extratta Frete"
                        };
                        _webhookService.Add(webhook);
                        webhook.EnviarNotificacao(_webhookActionDependencies, infoAdicional, requisicao, token, viagemEvento.IdEmpresa);
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao notificar web hook de baixa de evento: " + viagemEvento.IdViagemEvento);
            }

            return viagemEvento;
        }

        public bool GetTipoPagamento(int idViagemEvento)
        {
            var habilitaPagamentoCartao = _viagemEventoRepository
                .Find(o => o.IdViagemEvento == idViagemEvento).Select(o => o.HabilitarPagamentoCartao).FirstOrDefault();

            return habilitaPagamentoCartao;
        }

        public bool IsReicidente(int idViagemEvento)
        {
            var isReicidente = _protocoloEventoRepository.Where(o =>
                o.IdViagemEvento == idViagemEvento && o.Status == EStatusProtocoloEvento.Rejeitado).OrderByDescending(o => o.DataHoraAnaliseAbono).FirstOrDefault();

            return isReicidente != null;
        }
        
        public IQueryable<ViagemEvento> Find(Expression<Func<ViagemEvento, bool>> predicate, bool @readonly = false)
        {
            return _viagemEventoRepository.Find(predicate, @readonly);
        }
        
        public ViagemEvento GetByTokenAndEmpresa(int empresaId, string token)
        {
            return _viagemEventoRepository
                .Find(x => x.Token == token && x.IdEmpresa == empresaId)
                .Include(x => x.ViagemDocumentos)
                .FirstOrDefault();
        }
    }
}

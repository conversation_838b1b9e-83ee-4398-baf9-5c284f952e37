using System;
using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class HistoricoCartaoPessoaListResponseDto
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public List<HistoricoCartaoPessoaResponseDto> Objeto { get; set; }
    }

    public class HistoricoCartaoPessoaResponseDto
    {
        public int? Identificador;
        public DateTime? DataVinculo;
        public DateTime? DataDesvinculo;
        public ProdutoResponseDto Produto;
        public string MotivoDesvinculo;
        public int? CartaoMestreId;
        public MotivoBloqueioCartaoDto MotivoBloqueio;
        public DateTime? DataBloqueio;
        public HistoricoCartaoPessoaResponseStatus? Status;
        public string StatusDescricao;
    }

    public class MotivoBloqueioCartaoDto
    {
        public int? Id;
        public string Descricao;
        public bool? Permanente;
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ATS.Domain.DTO.Estabelecimento;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IEstabelecimentoBaseService : IService<EstabelecimentoBase>
    {
        ValidationResult Add(EstabelecimentoBase estabelecimento);
        ValidationResult Update(EstabelecimentoBase estabelecimento, List<EstabelecimentoBaseProduto> produtos, List<EstabelecimentoBaseContaBancaria> contaBancarias, int administradoraPlataforma, List<int> idsProdutosBaseExcluir = null, List<int> idsContasBancariasExcluir = null, List<EstabelecimentoBaseDocumento> documentos = null);
        ValidationResult Inativar(int idEstabelecimento);
        ValidationResult Reativar(int idEstabelecimento);
        IQueryable<EstabelecimentoBase> GetQueryByCnpj(string cnpj);
        IQueryable<EstabelecimentoBase> GetQueryById(int id);
        IQueryable<EstabelecimentoBase> GetEstabelecimentosJSL();
        ValidationResult IntegrarEstabelecimentoJSL(EstabelecimentoBase estabelecimentoBase);
        Task<ValidationResult> AutorizarEstabelecimentoJslEmpresas(EstabelecimentoBase estabelecimentoBase);
        Task<ValidationResult> CredenciaEstabelecimentosJsl(EstabelecimentoBase estabelecimentoBase, int administradoraPlataforma);
        ValidationResult AutorizarEstabelecimentoJSLParaEmpresa(int idEstabelecimento, int idempresa, int administradoraPlataforma);
        ValidationResult RemoverCombustiveisJSL(IList<int> idprodutobase);
        ValidationResult EmailSolicitacaoRecebida(EstabelecimentoBase estabelecimento, int? idEmpresa, int administradoraPlataforma);
        object ConsultaGrid(int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        EstabelecimentoBase Get(int idEstabelecimento);
        EstabelecimentoBase Get(string cnpj);
        int GetIdByCnpj(string cnpj);
        EstabelecimentoBase GetWithDocumentos(int idEstabelecimento);
        List<EstabelecimentoBase> GetAssociacoes(int? ignoreId);
        List<AssociacoesEmpresaDto> GetAssociacoesEmpresas(List<int> idsEmpresa);
        List<EstabelecimentoBaseAssociacao> GetEstabelecimentosAssociacoes(int? idEstabelecimentoBase);
        bool UsuarioTemEstabelecimento(int idUsuario);
        int GetEstabelecimentoPorUsuario(int idUsuario);
        void EnviarAtualizacaoEstabelecimentoWebHook(EstabelecimentoBase newEstabelecimentoBase);
    }
}

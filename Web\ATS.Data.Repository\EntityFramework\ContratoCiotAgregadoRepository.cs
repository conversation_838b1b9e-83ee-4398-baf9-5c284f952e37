﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class ContratoCiotAgregadoRepository : Repository<ContratoCiotAgregado>, IContratoCiotAgregadoRepository
    {
        public ContratoCiotAgregadoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<ContratoCiotAgregado> ConsultarContratos(DateTime dataInicio, DateTime dataFinal, int? idEmpresa)
        {
            IQueryable<ContratoCiotAgregado> resultado = from contratoCiotAgregado in All()
                .Include(x => x.DeclaracaoCiot)
                .Include(x => x.DeclaracaoCiot.ViagensVinculadas)
                .Include(x => x.ContratoCiotAgregadoVeiculos)
                .Include(x => x.Proprietario)
                .Include(x => x.Empresa)
                .Where(x => x.DataInicio >= dataInicio && x.DataInicio <= dataFinal)
                orderby contratoCiotAgregado.IdContratoCiotAgregado descending
                select contratoCiotAgregado;
            if (idEmpresa.HasValue)
                resultado = resultado.Where(x => x.IdEmpresa == idEmpresa);

            return resultado;
        }

        public bool AnyContratoAberto(int idProprietario, int idEmpresa)
        {
            var hoje = DateTime.Now;
            return Any(x => x.Status == EStatusContratoAgregado.Vigente 
                            && x.IdDeclaracaoCiot.HasValue
                            && x.IdProprietario == idProprietario 
                            && x.IdEmpresa == idEmpresa
                            && hoje <= x.DataFinal.Value);
        }

        public List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa)
        {
            var hoje = DateTime.Now;
            return Where(x => x.Status == EStatusContratoAgregado.Vigente 
                                   && x.IdDeclaracaoCiot.HasValue
                                   && x.IdProprietario == idProprietario 
                                   && x.IdEmpresa == idEmpresa
                                   && hoje <= x.DataFinal.Value)
                .Select(x => x.ContratoCiotAgregadoVeiculos.Select(v => v.Veiculo.Placa).ToList())
                .FirstOrDefault() ?? new List<string>();
        }
    }
}
﻿using System.Collections.Generic;
using System.Linq.Dynamic;
using System.Linq;
using System;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using System.Text.RegularExpressions;
using ATS.Domain.Extensions;

namespace ATS.Domain.Helpers
{
    public static class QueryableExtensions
    {
        public static string ToDynamicStringWhere(this List<QueryFilters> src, IQueryable source, bool isAddress)
        {
            var str = string.Empty;
            src.ForEach(x =>
            {
                if (x.CampoTipo == EFieldTipo.String && !isAddress)
                    x.Valor = Regex.Replace(x.Valor, "[^a-zA-Zà-ú-À-Ú0-9_.\\s]+", "", RegexOptions.Compiled);

                if (str != "") str += " and ";

                var operador = "";

                if (x.Operador == EOperador.GreaterThanOrEqual) operador = " >= ";
                if (x.Operador == EOperador.GreaterThan) operador = " > ";
                if (x.Operador == EOperador.LessThanOrEqual) operador = " <= ";
                if (x.Operador == EOperador.LessThan) operador = " < ";
                if (x.Operador == EOperador.Exact) operador = " == ";


                if (x.CampoTipo == EFieldTipo.Date)
                {
                    if (x.Operador == EOperador.Contains)
                        operador = " == ";

                    DateTime valorConvertido;
                    if (DateTime.TryParse(x.Valor, out valorConvertido))
                    {
                        if (FieldIsNullable(source, x.Campo))
                            str += $"  ({x.Campo}.Value.Year  {operador} {valorConvertido.Year}  " +
                                   $"&& {x.Campo}.Value.Month {operador} {valorConvertido.Month} " +
                                   $"&& {x.Campo}.Value.Day   {operador} {valorConvertido.Day})  ";
                        else
                            str += $"  ({x.Campo}.Year        {operador} {valorConvertido.Year}  " +
                                   $"&& {x.Campo}.Month       {operador} {valorConvertido.Month} " +
                                   $"&& {x.Campo}.Day         {operador} {valorConvertido.Day})  ";
                    }
                }
                else if (x.CampoTipo == EFieldTipo.String)
                {
                    // Fazer contains e um campo numérico
                    if (FieldIsNumber(source, x.Campo) && x.Operador == EOperador.Contains)
                        
                        if (x.Campo == "Perfil")
                        {
                            var pf = "";
                            if (x.Valor == "1")
                            {
                                pf = EPerfil.Administrador.ToString();
                            }else if (x.Valor == "2")
                            {
                                pf = EPerfil.Empresa.ToString();
                            }else if (x.Valor == "3")
                            {
                                pf = EPerfil.Motorista.ToString();
                            }
                            else if (x.Valor == "5")
                            {
                                pf = EPerfil.Proprietario.ToString();
                            }

                            str += $" ({x.Campo} == \"{pf}\")  ";
                        }
                        else
                        {
                            str += $" ({x.Campo} != null && {x.Campo}.ToString().ToLower().Contains(\"{x.Valor.ToLower()}\")) "; 
                        }
                       
                    else
                    {
                        if (x.Operador == EOperador.Exact)
                            str += $" ({x.Campo} != null && {x.Campo}.ToString() == \"{x.Valor}\") ";
                        else
                        {
                            str += $" ({x.Campo} != null && {x.Campo}.ToLower().Contains(\"{x.Valor.ToLower()}\")) ";
                            //str += $" ({x.Campo} != null && {x.Campo}.ToLower().Contains(\"{x.Valor.ToLower()}\")) ";
                        }
                    }
                }
                else if (x.CampoTipo == EFieldTipo.Number)
                {
                    if (x.ServerFieldCollection)
                    {
                        var strSplit = x.Campo.Split('.');
                        var tableCollection = string.Empty;
                        var fieldCollection = string.Empty;

                        try
                        {
                            tableCollection = strSplit[0];
                            fieldCollection = strSplit[1];
                        }
                        catch 
                        { 
                            //ignore
                        }

                        if (!string.IsNullOrEmpty(tableCollection) && !string.IsNullOrEmpty(fieldCollection))
                            str += $" ({tableCollection}.Any(Int32({fieldCollection}) == {x.Valor})) ";
                    }
                    else
                    {
                        var isBool = DtSourceFieldIsBoolean(source, x.Campo);
                        if (!isBool)
                        {
                            try
                            {
                                str += $" ({x.Campo} != null && (Int32({x.Campo}) == Int32({x.Valor}))) ";
                            }
                            catch (Exception)
                            {
                                // ignored
                            }

                        }
                        else
                            str += $" ({x.Campo} != null && ({x.Campo}.ToString() == \"{Convert.ToBoolean(Convert.ToInt32(x.Valor))}\")) ";
                    }
                }
                else if (x.CampoTipo == EFieldTipo.Decimal)
                {
                    x.Valor = x.Valor.Replace(".", string.Empty).Replace(",", ".");

                    if (x.Valor.EndsWith("."))
                        x.Valor = x.Valor.Replace(".", string.Empty);
                    
                    if(x.Valor.ToString().Contains("."))
                        str += $"({x.Campo} != null && (decimal({x.Campo}) == {x.Valor}))";
                    else
                        str += $" ({x.Campo} != null && (Int32({x.Campo}) == {x.Valor})) ";
                }
                else if (x.CampoTipo == EFieldTipo.Intervalo)
                {
                    if (x.Operador == EOperador.LessThanOrEqual)
                    {
                        if (FieldIsNullable(source, x.Campo))
                            str += $" (DateTime.Now - {x.Campo}).Value.Days  <= {x.Valor} ";
                        else
                            str += $" (DateTime.Now - {x.Campo}).Days  <= {x.Valor} ";
                    }
                }
            });

            return str == string.Empty ? null : str;
        }

        public static bool FieldIsNullable(IQueryable source, string fieldName)
        {
            if (source.ElementType.FullName == null)
                return false;
            
            var memberInfo = Type.GetType(source.ElementType.FullName);
            if (memberInfo?.FullName != null)
            {
                var entidadeSource = System.Reflection.Assembly.GetExecutingAssembly().CreateInstance(memberInfo.FullName);
                if (entidadeSource != null)
                {
                    var campoFiltroEntidade = entidadeSource.GetType().GetProperty(fieldName);
                    if (campoFiltroEntidade != null)
                        return campoFiltroEntidade.PropertyType.Name == "Nullable`1";

                    if (fieldName.Contains('.'))
                    {
                        Type currentType = entidadeSource.GetType();

                        foreach (string propertyName in fieldName.Split('.'))
                        {
                            var property = currentType?.GetProperty(propertyName);
                            if (property?.PropertyType.Name == "Nullable`1")
                                return true;

                            currentType = property?.PropertyType;
                        }
                    }
                }
            }

            return false;
        }

        public static bool FieldIsNumber(IQueryable source, string field)
        {
            try
            {
                if (source.ElementType.FullName == null)
                    return false;
                
                var memberInfo = Type.GetType(source.ElementType.FullName);
                if (memberInfo?.FullName != null)
                {
                    var entidadeSource = System.Reflection.Assembly.GetExecutingAssembly().CreateInstance(memberInfo.FullName);
                    if (entidadeSource != null)
                        return IsNumericType(entidadeSource.GetType().GetProperty(field)?.PropertyType);
                }
            }
            catch
            {
                // ignore
            }

            return false;
        }

        public static bool IsNumericType(Type type)
        {
            if (type == null)
                return false;

            switch (Type.GetTypeCode(type))
            {
                case TypeCode.Byte:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.SByte:
                case TypeCode.Single:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                    return true;
                case TypeCode.Object:
                    if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
                    {
                        return IsNumericType(Nullable.GetUnderlyingType(type));
                    }
                    return false;
            }
            return false;
        }

        public static bool DtSourceFieldIsBoolean(IQueryable source, string field)
        {
            if (source.ElementType.FullName == null)
                return false;
            
            var memberInfo = Type.GetType(source.ElementType.FullName);
            if (memberInfo?.FullName != null)
            {
                var entidadeSource = System.Reflection.Assembly.GetExecutingAssembly().CreateInstance(memberInfo.FullName);
                if (entidadeSource != null)
                    return entidadeSource.GetType().GetProperty(field)?.PropertyType == typeof(bool);
            }

            return false;
        }

        public static IQueryable<T> AplicarFiltrosDinamicos<T>(this IQueryable<T> source, List<QueryFilters> src = null, bool isAddress = false)
        {
            if (src != null && src.Any())
            {
                try
                {
                    var filtroStr = src.ToDynamicStringWhere(source, isAddress);
                    if (filtroStr != null)
                        return source.Where(filtroStr);
                }
                catch (Exception e)
                {
                    Console.Write(e);
                    // ignored
                }
            }

            return source;
        }

        public static IQueryable<T> AplicarOrderByDinamicos<T>(this IQueryable<T> source, OrderFilters filters = null)
        {
            if (filters != null)
            {
                try
                {
                      source = source.OrderBy($"{filters.Campo} {filters.Operador.DescriptionAttr()}");
                }
                catch (Exception e)
                {
                    Console.Write(e);
                    // ignored
                }
            }

            return source;
        }
    }
}
using System;
using System.Collections.Generic;
using System.Transactions;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;


namespace ATS.Application.Application
{
    public class ViagemEventoProtocoloAnexoApp: AppBase, IViagemEventoProtocoloAnexoApp
    {
        private readonly IViagemEventoProtocoloAnexoService _service;

        public ViagemEventoProtocoloAnexoApp(IViagemEventoProtocoloAnexoService service)
        {
            _service = service;
        }

        public ValidationResult Add(ViagemEventoProtocoloAnexoModel viagemEventoProtocoloAnexosModel)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.Serializable }))
                {
                    var response = _service.Add(viagemEventoProtocoloAnexosModel);
                    
                    if (response.IsValid)
                        transaction.Complete();
                    else 
                        transaction.Dispose();

                    return response;
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult AddListOfAnexos(List<ViagemEventoProtocoloAnexoModel> viagemEventoProtocoloAnexosModel, List<int> idsAnexosRemover)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.Serializable }))
                {
                    var response = _service.AddListOfAnexos(viagemEventoProtocoloAnexosModel, idsAnexosRemover);
                    
                    if (response.IsValid)
                        transaction.Complete();

                    return response;
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
        
        public ValidationResult RemoverAnexos(List<int> idsAnexosRemover)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.Serializable }))
                {
                    var response = _service.RemoverAnexos(idsAnexosRemover);
                    
                    if (response.IsValid)
                        transaction.Complete();
                    else 
                        transaction.Dispose();

                    return response;
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public List<ViagemEventoProtocoloAnexoModel> GetAnexosCadastrados(int idViagemEvento)
        {
            return _service.GetAnexosCadastrados(idViagemEvento);
        }
    }
}
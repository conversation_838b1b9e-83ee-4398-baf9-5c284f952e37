﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ConjuntoCarretaEmpresaMap : EntityTypeConfiguration<ConjuntoCarretaEmpresa>
    {
        public ConjuntoCarretaEmpresaMap()
        {
            ToTable("CONJUNTO_CARRETA_EMPRESA");

            HasKey(t => new {t.IdConjuntoCarretaEmpresa});
            
            Property(t => t.IdConjuntoCarretaEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(x => x.ConjuntoEmpresa)
               .WithMany(x => x.Conjuntos<PERSON>arrata)
               .HasForeignKey(x => x.IdConjuntoEmpresa);

        }
    }
}
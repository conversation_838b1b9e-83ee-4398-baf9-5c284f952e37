﻿using ATS.Domain.Entities.Common;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping.Common
{
    public class ContatoBaseMap<TEntity> : EntityTypeConfiguration<TEntity> where TEntity : ContatoBase
    {
        public ContatoBaseMap()
        {
            Property(t => t.Telefone)
                .IsOptional()
                .HasMaxLength(20);

            Property(t => t.Celular)
                .IsOptional()
                .HasMaxLength(20);

            Property(t => t.Email)
                .IsOptional()
                .HasMaxLength(200);
        }
    }
}
﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC.Interfaces;

namespace ATS.Domain.Service
{
    public class PagamentoConfiguracaoService : ServiceBase, IPagamentoConfiguracaoService
    {

        public readonly IPagamentoConfiguracaoRepository _repository; 
        private readonly IUserIdentity _userIdentity; 

        public PagamentoConfiguracaoService(IPagamentoConfiguracaoRepository repository, IUserIdentity userIdentity)
        {
            _repository = repository;
            _userIdentity = userIdentity;
        }

        public ValidationResult Add(PagamentoConfiguracao pagamentoConfiguracao)
        {
            try
            {
                pagamentoConfiguracao.DataCadastro = DateTime.Now;
                pagamentoConfiguracao.DataUltimaAtualizaca = DateTime.Now;
                pagamentoConfiguracao.Ativo = true;

                _repository.Add(pagamentoConfiguracao);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
            finally
            {
                if (pagamentoConfiguracao!= null)
                    InativaTodas(pagamentoConfiguracao.IdEmpresa, pagamentoConfiguracao.IdFilial, pagamentoConfiguracao.IdPagamentoConfiguracao);
            }
        }

        public IEnumerable<PagamentoConfiguracao> GetPorEmpresa(int idEmpresa, int?  idFilial)
        {
            return _repository
                .Find(x => x.IdEmpresa == idEmpresa && x.IdFilial == idFilial && x.Ativo)
                .Include(x => x.PagamentoConfiguracoesProcesso)
                .Include(x => x.PagamentoConfiguracoesProcesso.Select(o => o.Documento))
                .Include(x => x.PagamentoConfiguracoesProcesso.Select(o => o.Documento).Select(p => p.ProtocoloAnexos));
        }

     
        public ValidationResult Update(PagamentoConfiguracao pagamentoConfiguracao)
        {
            try
            {
                _repository.Update(pagamentoConfiguracao);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");

            } finally
            {
                if (pagamentoConfiguracao != null)
                    InativaTodas(pagamentoConfiguracao.IdEmpresa, pagamentoConfiguracao.IdFilial, pagamentoConfiguracao.IdPagamentoConfiguracao);
            }
        }



        public object ConsultarGrid(int? idEmpresa, int? idFilial, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var config = _repository
                .GetAll()
                .Include(x => x.Empresa)
                .Include(c => c.Filial);

            if (idEmpresa.HasValue)
                config = config.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idFilial.HasValue)
                config = config.Where(x => x.IdFilial == idFilial.Value);


            config = string.IsNullOrWhiteSpace(order?.Campo)
                    ? config.OrderBy(x => x.IdPagamentoConfiguracao)
                    : config.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");
            
            config = config.AplicarFiltrosDinamicos<PagamentoConfiguracao>(filters);

            return new
            {
                totalItems = config.Count(),
                items = config.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    IdPagamentoConfiguracao = x.IdPagamentoConfiguracao,
                    Empresa=x.Empresa.RazaoSocial,
                    Filial=x.Filial?.RazaoSocial,
                    IdEmpresa = x.IdEmpresa, 
                    IdFilial = x.IdFilial,
                    Ativo=x.Ativo
                })
            };
        }
        
        public ValidationResult Inativar(int idPagamentoConfiguracao)
        {
            try
            {
                var pagamentoConfiguracao = _repository.Get(idPagamentoConfiguracao);
                
                if (pagamentoConfiguracao == null)
                    return new ValidationResult().Add("Configuração não encontrada para inativação. ");
                
                if(_userIdentity.Perfil != (int)EPerfil.Administrador && pagamentoConfiguracao.IdEmpresa != _userIdentity.IdEmpresa)
                    return new ValidationResult().Add("Usuário não autenticado.");

                InativaTodas(pagamentoConfiguracao.IdEmpresa, pagamentoConfiguracao.IdFilial);

                pagamentoConfiguracao.Ativo = false;

                _repository.Update(pagamentoConfiguracao);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public void InativaTodas(int IdEmpresa, int? idFilial, int? idException=new Nullable<int>())
        {
            var pagamentoConfiguracaoRepository = _repository;
            var todos = pagamentoConfiguracaoRepository.Find( x => x.IdEmpresa == IdEmpresa && x.IdFilial == idFilial && x.Ativo == true);

            if (idException.HasValue)
                todos = todos.Where(x => x.IdPagamentoConfiguracao != idException);
            
            foreach( var item in todos)
            {
                item.Ativo = false;
                pagamentoConfiguracaoRepository.Update(item);
            }
        }


        public ValidationResult Reativar(int idPagamentoConfiguracao)
        {
            try
            {
                var pagamentoConfiguracao = _repository.Get(idPagamentoConfiguracao);
                
                if (pagamentoConfiguracao == null)
                    return new ValidationResult().Add("Configuração não encontrada para reativação. ");

                if(_userIdentity.Perfil != (int)EPerfil.Administrador && pagamentoConfiguracao.IdEmpresa != _userIdentity.IdEmpresa)
                    return new ValidationResult().Add("Usuário não autenticado.");
                
                InativaTodas(pagamentoConfiguracao.IdEmpresa, pagamentoConfiguracao.IdFilial);

                pagamentoConfiguracao.Ativo = true;

                _repository.Update(pagamentoConfiguracao);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }


        public PagamentoConfiguracao ConsultarPorId(int idPagamentoConfiguracao)
        {
            var q = _repository
                .Where(x => x.Ativo && x.IdPagamentoConfiguracao == idPagamentoConfiguracao)
                .Include(x => x.Empresa)
                .Include(x => x.Filial)
                .Include(x => x.PagamentoConfiguracoesProcesso);

            if (_userIdentity.Perfil != (int)EPerfil.Administrador && _userIdentity.IdEmpresa.HasValue)
                q = q.Where(c => c.IdEmpresa == _userIdentity.IdEmpresa.Value);

            return q.FirstOrDefault();
        }
    }
}

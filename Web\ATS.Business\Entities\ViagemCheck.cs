﻿using ATS.Domain.Enum;
using System;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    /// <summary>
    /// Realiza o vinculo das situações relacionadas a determinada carga.
    /// </summary>
    [TrackChanges]
    public class ViagemCheck
    {
        /// <summary>
        /// Código da Carga
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código da Situação (Sequencial)
        /// </summary>
        public int IdCheck { get; set; }

        /// <summary>
        /// Código do Usuário que esta realizando a operação
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Data e Hora do registro
        /// </summary>
        public DateTime DataHora { get; set; }

        /// <summary>
        /// Status
        /// </summary>
        public EStatusCheckViagem Status { get; set; } = EStatusCheckViagem.ApresentacaoCarregamento;

        // Referência
        public virtual Usuario Usuario   { get; set; }
        public virtual Viagem Viagem     { get; set; }
    }
}
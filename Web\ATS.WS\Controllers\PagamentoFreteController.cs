﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class PagamentoFreteController : BaseController
    {
        private readonly SrvPagamentoFrete _srvPagamentoFrete;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public PagamentoFreteController(BaseControllerArgs baseArgs, SrvPagamentoFrete srvPagamentoFrete, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _srvPagamentoFrete = srvPagamentoFrete;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        /// <summary>
        /// Realiza a consulta de um pagamento de frete por seu token
        /// </summary>
        /// <param name="token">Token de autorização de consulta, concedido a empresa</param>
        /// <param name="cnpjAplicacao">CNPJ da aplicação em que está realizando a request</param>
        /// <param name="tokenPagamento">Token do registro de pagamento de frete</param>
        /// <returns></returns>
        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Consultar(string token, string cnpjAplicacao, string tokenPagamento)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return TokenInvalido();

                if (string.IsNullOrWhiteSpace(tokenPagamento))
                    return Mensagem("Token do pagamento não informado. ");

                return Responde(_srvPagamentoFrete.Consultar(token, cnpjAplicacao, tokenPagamento));
            }
            catch (Exception e)
            {
                return Mensagem(e.Message);
            }
        }

    }
}
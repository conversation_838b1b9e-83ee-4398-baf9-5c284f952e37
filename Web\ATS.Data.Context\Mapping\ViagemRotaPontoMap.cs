using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ViagemRotaPontoMap: EntityTypeConfiguration<ViagemRotaPonto>
    {
        public ViagemRotaPontoMap()
        {
            ToTable("VIAGEM_ROTA_PONTO");

            HasKey(c => c.IdViagemRotaPonto);

            Property(c => c.IdViagemRota).IsRequired();
            Property(c => c.Sequencia).IsRequired();
            Property(c => c.Latitude).IsOptional();
            Property(c => c.Longitude).IsOptional();

            HasOptional(c => c.Cidade)
                .WithMany()
                .HasForeign<PERSON>ey(c => c.IdCidade);

            HasRequired(c => c.ViagemRota)
                .WithMany(c => c.<PERSON>)
                .HasForeignKey(c => c.IdViagemRota);
        }
    }
}
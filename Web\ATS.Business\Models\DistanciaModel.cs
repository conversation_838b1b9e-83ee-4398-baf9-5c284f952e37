﻿namespace ATS.Domain.Models
{
    public class DistanciaModel
    {
        /// <summary>
        /// Coordenadas de origem do cálculo da distância
        /// </summary>
        public LocalizacaoModel Origem { get; set; }

        /// <summary>
        /// Coordenadas de destino do cálculo da distância
        /// </summary>
        public LocalizacaoModel Destino { get; set; }

        /// <summary>
        /// Distância entre as coordenadas de origem e destino da carga
        /// </summary>
        public decimal? Distancia { get; set; }

        public DistanciaModel()
        {
            Origem  = new LocalizacaoModel();
            Destino = new LocalizacaoModel();
        }
    }
}
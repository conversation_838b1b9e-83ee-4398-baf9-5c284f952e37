﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class PagamentoConfiguracaoProcessoService : ServiceBase, IPagamentoConfiguracaoProcessoService
    {

        public readonly IPagamentoConfiguracaoProcessoRepository _repository;

        public PagamentoConfiguracaoProcessoService(IPagamentoConfiguracaoProcessoRepository repository)
        {
            _repository = repository;
        }
        
        public ValidationResult Add(PagamentoConfiguracaoProcesso pagamentoConfiguracao)
        {
            try
            {
                _repository.Add(pagamentoConfiguracao);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }


        public ValidationResult Update(PagamentoConfiguracaoProcesso pagamentoConfiguracaoProcesso)
        {
            try
            {
                _repository.Update(pagamentoConfiguracaoProcesso);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public IEnumerable<PagamentoConfiguracaoProcesso> GetItemsByProcess(int idConfiguracao, EProcessoPgtoFrete processo)
        {
            return _repository.Find( x => x.IdConfiguracao == idConfiguracao && x.Processo == processo)
                .Include( x => x.Documento);
        }
        
        public List<PagamentoConfiguracaoProcesso> GetbyPagtoDocto(int idPagtoDocumento)
        {
            return _repository.Find(x => x.IdConfiguracao == idPagtoDocumento).ToList();
        }

        public void Remove(PagamentoConfiguracaoProcesso processo)
        {
            _repository.Delete(processo);
        }

  
    }
}

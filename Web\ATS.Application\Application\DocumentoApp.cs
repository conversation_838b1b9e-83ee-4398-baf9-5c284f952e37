﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class DocumentoApp : AppBase, IDocumentoApp
    {
        private readonly IDocumentoService _documentoService;

        public DocumentoApp(IDocumentoService documentoService)
        {
            _documentoService = documentoService;
        }

        public ValidationResult Add(Documento pagamentoDocumento)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _documentoService.Add(pagamentoDocumento);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public IEnumerable<Documento> GetPorEmpresa(int idEmpresa, int? idFilial)
        {
            return _documentoService.GetPorEmpresa(idEmpresa, idFilial);
        }
        
        public List<int> GetIdsDocumentosEmpresa(int idEmpresa)
        {
            return _documentoService.GetIdsDocumentosEmpresa(idEmpresa);
        }

        public ValidationResult Inativar(int idPagamentoDocumento)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _documentoService.Inativar(idPagamentoDocumento);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public ValidationResult Reativar(int idPagamentoDocumento)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _documentoService.Reativar(idPagamentoDocumento);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public ValidationResult Update(Documento pagamentoDocumento)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _documentoService.Update(pagamentoDocumento);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public Documento Get(int idDocumento)
        {
            return _documentoService.Get(idDocumento);
        }
    }
}

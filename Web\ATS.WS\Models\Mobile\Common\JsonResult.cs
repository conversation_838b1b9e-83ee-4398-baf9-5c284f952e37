﻿using ATS.WS.Configuration;
using Newtonsoft.Json;

namespace ATS.WS.Models.Mobile.Common
{
    public class JsonResult
    {
        public string Responde<T>(T resposta)
        {
            return JsonConvert.SerializeObject(resposta, new JsonDateTimeConverter());
        }

        public string Mensagem(string mensagem)
        {
            return JsonConvert.SerializeObject(new Retorno<object>(mensagem), new JsonDateTimeConverter());
        }

        public string TokenInvalido()
        {
            return JsonConvert.SerializeObject(new Retorno<object>("Token inválido"));
        }
    }
}
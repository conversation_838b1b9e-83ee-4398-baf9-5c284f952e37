﻿using ATS.CrossCutting.IoC;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class VeiculoRepository : Repository<Veiculo>, IVeiculoRepository
    {
        private readonly IVeiculoDigitosMercosulRepository _veiculoDigitosMercosulRepository;
        public VeiculoRepository(AtsContext context, IVeiculoDigitosMercosulRepository veiculoDigitosMercosulRepository) : base(context)
        {
            _veiculoDigitosMercosulRepository = veiculoDigitosMercosulRepository;
        }
        
        public Veiculo GetWithAllChilds(int idVeiculo)
        {
            return (from veiculo in All()
                         .Include(x => x.TipoCombustiveis.Select(a => a.TipoCombustivel))
                         .Include(x => x.VeiculoConjuntos)
                         .Include(x => x.Empresa)
                         .Include(x => x.Filial)
                         .Include(x => x.TipoCavalo)
                         .Include(x => x.TipoCarreta)
                         .Include(x => x.Motorista)
                         .Include(x => x.Proprietario)
                         .Include(x => x.Veiculos)
                    where veiculo.IdVeiculo == idVeiculo
                    select veiculo).FirstOrDefault();
        }

        public IQueryable<Veiculo> GetVeiculosPorNumerosFrotas(List<long> numeroFrota)
        {
            return from veiculo in All()
                   where numeroFrota.Contains((long)veiculo.NumeroFrota)
                   select veiculo;
        }
        
        public IQueryable<Veiculo> GetVeiculosPorPlaca(List<string> placas)
        {
            return from veiculo in All()
                where placas.Contains(veiculo.Placa)
                select veiculo;
        }

        public string ConvertToPadraoMercosul(string placa)
        {
            if (!Veiculo.RegexBrasil.IsMatch(placa))
                throw new Exception($"Placa {placa} inválida!");

            var digito = Convert.ToInt32(placa.Substring(4, 1));

            var veiculoMercosulRepository = _veiculoDigitosMercosulRepository;

            var digitoMercosul = veiculoMercosulRepository.GetDigitoMercosul(digito);
            
            var aStringBuilder = new StringBuilder(placa);
            aStringBuilder.Remove(4, 1);
            aStringBuilder.Insert(4, digitoMercosul);
            placa = aStringBuilder.ToString();

            return placa;
        }

        public string ConvertToPadraoBrasil(string placa)
        {
            if (!Veiculo.RegexMercosul.IsMatch(placa))
                throw new Exception($"Placa {placa} inválida!");

            var digito = placa.Substring(4, 1);

            var veiculoMercosulRepository = _veiculoDigitosMercosulRepository;

            var digitoMercosul = veiculoMercosulRepository.GetNumeroByDigitoMercosul(digito);
            
            var aStringBuilder = new StringBuilder(placa);
            aStringBuilder.Remove(4, 1);
            aStringBuilder.Insert(4, digitoMercosul);
            placa = aStringBuilder.ToString();

            return placa;
        }
        
        public string GetRntrcProprietarioVeiculo(string placa, int idEmpresa)
        {
            var rntrc = Where(x => x.Placa == placa && (x.IdEmpresa == idEmpresa || x.IdEmpresa == null) && x.IdProprietario.HasValue)
                .OrderByDescending(x => x.IdEmpresa)
                .Select(x => x.Proprietario.RNTRC)
                .FirstOrDefault();

            return rntrc?.PadLeft(9, '0');
        }
        
        public string GetRntrcProprietarioVeiculo(int idVeiculo)
        {
            var rntrc = Where(x => x.IdVeiculo == idVeiculo && x.IdProprietario.HasValue)
                .Select(x => x.Proprietario.RNTRC)
                .FirstOrDefault();

            return rntrc?.PadLeft(9, '0');
        }
        
        public Veiculo GetVeiculoPorPlacaWithoutIncludes(string placa, int idEmpresa)
        {
            return Find(v => v.Placa == placa.Trim() && v.IdEmpresa == idEmpresa && v.Ativo).FirstOrDefault();
        }
    }
}
﻿//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

namespace SistemaInfo.Cartoes.Repository.External.SistemaInfo.Cartap.Api.Client
{
    #pragma warning disable // Disable all warnings

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class BuscarClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public BuscarClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Buscar todos os cartões vinculados, aguardando vinculação e desativados para o cliente autenticado na requisição.</summary>
        /// <param name="data">Obter apenas registros inseridos na plataforma após esta data e hora. Null para obter todos os registros.</param>
        /// <param name="produto">Filtar apenas cartões do produto indicado. Null para obter todos. Maiores detalhes na documentação da api “/Produtos”.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com todos os cartões da empresa autenticada.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<CartaoResponse>> TodosAsync(System.DateTime data, int produto, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return TodosAsync(data, produto, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar todos os cartões vinculados, aguardando vinculação e desativados para o cliente autenticado na requisição.</summary>
        /// <param name="data">Obter apenas registros inseridos na plataforma após esta data e hora. Null para obter todos os registros.</param>
        /// <param name="produto">Filtar apenas cartões do produto indicado. Null para obter todos. Maiores detalhes na documentação da api “/Produtos”.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com todos os cartões da empresa autenticada.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<CartaoResponse> Todos(System.DateTime data, int produto, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await TodosAsync(data, produto, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar todos os cartões vinculados, aguardando vinculação e desativados para o cliente autenticado na requisição.</summary>
        /// <param name="data">Obter apenas registros inseridos na plataforma após esta data e hora. Null para obter todos os registros.</param>
        /// <param name="produto">Filtar apenas cartões do produto indicado. Null para obter todos. Maiores detalhes na documentação da api “/Produtos”.</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com todos os cartões da empresa autenticada.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<CartaoResponse>> TodosAsync(System.DateTime data, int produto, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (data == null)
                throw new System.ArgumentNullException("data");
    
            if (produto == null)
                throw new System.ArgumentNullException("produto");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Buscar/Todos/{data}/{produto}");
            urlBuilder_.Replace("{data}", System.Uri.EscapeDataString(data.ToString("s", System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Replace("{produto}", System.Uri.EscapeDataString(ConvertToString(produto, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<CartaoResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<CartaoResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<CartaoResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class Client 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public Client(string baseUrl)
        {
            BaseUrl = baseUrl; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Busca cartão através do identificador e produto.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CartaoResponse> BuscarAsync(int identificador, int produto, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return BuscarAsync(identificador, produto, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Busca cartão através do identificador e produto.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CartaoResponse Buscar(int identificador, int produto, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await BuscarAsync(identificador, produto, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Busca cartão através do identificador e produto.</summary>
        /// <param name="identificador">Número identificador do cartão</param>
        /// <param name="produto">Id do tipo de produto. Maiores detalhes na documentação da api "/Produtos".</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Dados do cartão com o identificador e produto indicado.
        /// <li>identificador: Número único por produto que identifica o cartão</li><li>dataVinculo: Data que o cartão foi vinculado ao portado</li><li>dataDesvinculo: Data que o cartão foi desativado, seja manualmente ou por troca de cartão</li><li>portador: CPF/CNPJ do portador vinculado ao cartão</li><li>produto: Grupo de valores que identificam o produto que o cartão pertence. Maiores detalhes na documentação da api "/Produtos"</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CartaoResponse> BuscarAsync(int identificador, int produto, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (identificador == null)
                throw new System.ArgumentNullException("identificador");
    
            if (produto == null)
                throw new System.ArgumentNullException("produto");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Buscar/{identificador}/{produto}");
            urlBuilder_.Replace("{identificador}", System.Uri.EscapeDataString(ConvertToString(identificador, System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Replace("{produto}", System.Uri.EscapeDataString(ConvertToString(produto, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ == "204") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("N\u00e3o existe cart\u00e3o com os dados indicados", status_, responseData_, headers_, null);
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Buscar pessoa pelo documento</summary>
        /// <param name="documento">Documento CPF ou CNPJ</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Dados cadastrais da pessoa.
        ///             <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>ativo: Indicador booleano (true/false)</li><li>cidade: Código IBGE da cidade</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarPessoaResponse> PessoasAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return PessoasAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar pessoa pelo documento</summary>
        /// <param name="documento">Documento CPF ou CNPJ</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Dados cadastrais da pessoa.
        ///             <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>ativo: Indicador booleano (true/false)</li><li>cidade: Código IBGE da cidade</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarPessoaResponse Pessoas(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await PessoasAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar pessoa pelo documento</summary>
        /// <param name="documento">Documento CPF ou CNPJ</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Dados cadastrais da pessoa.
        ///             <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>ativo: Indicador booleano (true/false)</li><li>cidade: Código IBGE da cidade</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarPessoaResponse> PessoasAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (documento == null)
                throw new System.ArgumentNullException("documento");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/{documento}");
            urlBuilder_.Replace("{documento}", System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarPessoaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarPessoaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ == "204") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("Indica que n\u00e3o existe pessoa com o documento solicitado", status_, responseData_, headers_, null);
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarPessoaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Listar produtos habilitados ao cliente autenticado na requisição.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com produtos disponíveis ao cliente autenticado na requisição.
        /// <li>id: Identificando único do produto para plataforma de cartões</li><li>nome: Nome comercial do cartão</li><li>isMultiplasContas: Significa que o produto permite mais de um cartão ativo em simultâneo para o mesmo CPF. Quando vinculado um cartão, onde este indicador é "false", o cartão antigo é automaticamente bloqueado e o saldo existente é movido ao novo cartão</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<ProdutoResponse>> ProdutosAsync(string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ProdutosAsync(x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Listar produtos habilitados ao cliente autenticado na requisição.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com produtos disponíveis ao cliente autenticado na requisição.
        /// <li>id: Identificando único do produto para plataforma de cartões</li><li>nome: Nome comercial do cartão</li><li>isMultiplasContas: Significa que o produto permite mais de um cartão ativo em simultâneo para o mesmo CPF. Quando vinculado um cartão, onde este indicador é "false", o cartão antigo é automaticamente bloqueado e o saldo existente é movido ao novo cartão</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<ProdutoResponse> Produtos(string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ProdutosAsync(x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Listar produtos habilitados ao cliente autenticado na requisição.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com produtos disponíveis ao cliente autenticado na requisição.
        /// <li>id: Identificando único do produto para plataforma de cartões</li><li>nome: Nome comercial do cartão</li><li>isMultiplasContas: Significa que o produto permite mais de um cartão ativo em simultâneo para o mesmo CPF. Quando vinculado um cartão, onde este indicador é "false", o cartão antigo é automaticamente bloqueado e o saldo existente é movido ao novo cartão</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<ProdutoResponse>> ProdutosAsync(string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Produtos");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<ProdutoResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<ProdutoResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<ProdutoResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class OperacoesClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public OperacoesClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Vincular cartão ao portador. Caso haver cartão do mesmo produto já vinculado ao portador, e este não suportar multiplas contas em simutâneo, o cartão anterior será desativado e o saldo residual será transferido automaticamente ao novo cartão.</summary>
        /// <param name="request">Informações cadastrais para realizar vinculo. Enviar dados atualizados do portador e o cartão sendo entregue ao mesmo.
        ///             <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<VincularResponse> VincularPortadorAsync(VincularRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return VincularPortadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Vincular cartão ao portador. Caso haver cartão do mesmo produto já vinculado ao portador, e este não suportar multiplas contas em simutâneo, o cartão anterior será desativado e o saldo residual será transferido automaticamente ao novo cartão.</summary>
        /// <param name="request">Informações cadastrais para realizar vinculo. Enviar dados atualizados do portador e o cartão sendo entregue ao mesmo.
        ///             <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public VincularResponse VincularPortador(VincularRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await VincularPortadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Vincular cartão ao portador. Caso haver cartão do mesmo produto já vinculado ao portador, e este não suportar multiplas contas em simutâneo, o cartão anterior será desativado e o saldo residual será transferido automaticamente ao novo cartão.</summary>
        /// <param name="request">Informações cadastrais para realizar vinculo. Enviar dados atualizados do portador e o cartão sendo entregue ao mesmo.
        ///             <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<VincularResponse> VincularPortadorAsync(VincularRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/VincularPortador");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(VincularResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<VincularResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(VincularResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.</summary>
        /// <param name="request"><para>Dados do cartão e motivo do desvinculo.</para>
        /// <para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API "/Operacoes/VincularPortador".</para>
        /// <li>cartao: Grupo com dados de identificação do cartão</li>
        /// <ul>
        ///   <li>identificador: Número único de identificação do cartão</li>
        ///   <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li>
        /// </ul>
        /// <li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<DesvincularResponse> DesvincularPortadorAsync(DesvincularRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return DesvincularPortadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.</summary>
        /// <param name="request"><para>Dados do cartão e motivo do desvinculo.</para>
        /// <para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API "/Operacoes/VincularPortador".</para>
        /// <li>cartao: Grupo com dados de identificação do cartão</li>
        /// <ul>
        ///   <li>identificador: Número único de identificação do cartão</li>
        ///   <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li>
        /// </ul>
        /// <li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public DesvincularResponse DesvincularPortador(DesvincularRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await DesvincularPortadorAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Desvincular cartão do portador. O saldo residual continuará no cartão bloqueado e não poderá ser utilizado pelo portador, porém poderá ser transferido a um novo cartão que futuramente possa ser entregue ao portador.</summary>
        /// <param name="request"><para>Dados do cartão e motivo do desvinculo.</para>
        /// <para>Para troca de cartões que não possuem multiplas contas em simultâneo ativas, utilize apenas a API "/Operacoes/VincularPortador".</para>
        /// <li>cartao: Grupo com dados de identificação do cartão</li>
        /// <ul>
        ///   <li>identificador: Número único de identificação do cartão</li>
        ///   <li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li>
        /// </ul>
        /// <li>motivoDesvinculo: Descritivo sobre o motivo do desvinculo do cartão [char(200)]</li></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<DesvincularResponse> DesvincularPortadorAsync(DesvincularRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/DesvincularPortador");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(DesvincularResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<DesvincularResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(DesvincularResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Vincular uma conta-cartão virtual em um portador</summary>
        /// <param name="request">Objeto de requisicao</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<VincularCartaoVirtualResponse> VincularCartaoVirtualAsync(VincularCartaoVirtualRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return VincularCartaoVirtualAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Vincular uma conta-cartão virtual em um portador</summary>
        /// <param name="request">Objeto de requisicao</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public VincularCartaoVirtualResponse VincularCartaoVirtual(VincularCartaoVirtualRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await VincularCartaoVirtualAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Vincular uma conta-cartão virtual em um portador</summary>
        /// <param name="request">Objeto de requisicao</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<VincularCartaoVirtualResponse> VincularCartaoVirtualAsync(VincularCartaoVirtualRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/VincularCartaoVirtual");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(VincularCartaoVirtualResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<VincularCartaoVirtualResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(VincularCartaoVirtualResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consulta um extrato do cartão</summary>
        /// <param name="request">Objeto de consulta de extrato
        ///             <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>dataInicio: Data e hora inicial do filtro</li><li>dataFim: Data e hora final do tipo</li><li>Tipo: C = Crédito, D = Débito, Vazio = Todos</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarExtratoResponse> ConsultarExtratoAsync(ConsultarExtratoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarExtratoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consulta um extrato do cartão</summary>
        /// <param name="request">Objeto de consulta de extrato
        ///             <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>dataInicio: Data e hora inicial do filtro</li><li>dataFim: Data e hora final do tipo</li><li>Tipo: C = Crédito, D = Débito, Vazio = Todos</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarExtratoResponse ConsultarExtrato(ConsultarExtratoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarExtratoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consulta um extrato do cartão</summary>
        /// <param name="request">Objeto de consulta de extrato
        ///             <li>cartao: Grupo com dados de identificação do cartão</li><ul><li>identificador: Número único de identificação do cartão</li><li>produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul><li>pessoa: Grupo com dados cadastrais do portador</li><ul><li>dataInicio: Data e hora inicial do filtro</li><li>dataFim: Data e hora final do tipo</li><li>Tipo: C = Crédito, D = Débito, Vazio = Todos</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarExtratoResponse> ConsultarExtratoAsync(ConsultarExtratoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/ConsultarExtrato");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarExtratoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarExtratoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarExtratoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar o saldo do cartão</summary>
        /// <param name="request">Objeto de consulta de saldo</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarSaldoCartaoResponse> ConsultarSaldoCartaoAsync(ConsultarSaldoCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarSaldoCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar o saldo do cartão</summary>
        /// <param name="request">Objeto de consulta de saldo</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarSaldoCartaoResponse ConsultarSaldoCartao(ConsultarSaldoCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarSaldoCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar o saldo do cartão</summary>
        /// <param name="request">Objeto de consulta de saldo</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarSaldoCartaoResponse> ConsultarSaldoCartaoAsync(ConsultarSaldoCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/ConsultarSaldoCartao");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarSaldoCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarSaldoCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultar situação de processamento pelo número de protocolo único da requisição do cliente</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarProtocoloResponse> ConsultarProtocoloAsync(ConsultarProtocoloRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarProtocoloAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultar situação de processamento pelo número de protocolo único da requisição do cliente</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarProtocoloResponse ConsultarProtocolo(ConsultarProtocoloRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarProtocoloAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultar situação de processamento pelo número de protocolo único da requisição do cliente</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarProtocoloResponse> ConsultarProtocoloAsync(ConsultarProtocoloRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/ConsultarProtocolo");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarProtocoloResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarProtocoloResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarProtocoloResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Carga de valor no cartão pré-valor</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CarregarValorResponse> CarregarAsync(CarregarValorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CarregarAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Carga de valor no cartão pré-valor</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CarregarValorResponse Carregar(CarregarValorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CarregarAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Carga de valor no cartão pré-valor</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CarregarValorResponse> CarregarAsync(CarregarValorRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/Carregar");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CarregarValorResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CarregarValorResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CarregarValorResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Estornar carga de crédito no cartão pré-pago. Será estornado o valor da operação vinculada ao protocolo que originou o crédito.
        /// Procedimento sujeito a existência de saldo no cartão pré-pago.</summary>
        /// <param name="request"><para># Empresa: CNPJ para movimentar saldo.<br /></para>
        /// <para># NumeroTransacaoParaEstorno: Protocolo que o consumidor gerou para efetivar a carga de crédito.<br /></para>
        /// <para># ProtocoloRequisicao: Número único gerado pelo cliente consumidor do serviço, utilizado para impedir requisições duplicadas.</para></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<EstornarCargaCartaoResponse> EstornarCargaAsync(EstornarCargaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EstornarCargaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Estornar carga de crédito no cartão pré-pago. Será estornado o valor da operação vinculada ao protocolo que originou o crédito.
        /// Procedimento sujeito a existência de saldo no cartão pré-pago.</summary>
        /// <param name="request"><para># Empresa: CNPJ para movimentar saldo.<br /></para>
        /// <para># NumeroTransacaoParaEstorno: Protocolo que o consumidor gerou para efetivar a carga de crédito.<br /></para>
        /// <para># ProtocoloRequisicao: Número único gerado pelo cliente consumidor do serviço, utilizado para impedir requisições duplicadas.</para></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public EstornarCargaCartaoResponse EstornarCarga(EstornarCargaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EstornarCargaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Estornar carga de crédito no cartão pré-pago. Será estornado o valor da operação vinculada ao protocolo que originou o crédito.
        /// Procedimento sujeito a existência de saldo no cartão pré-pago.</summary>
        /// <param name="request"><para># Empresa: CNPJ para movimentar saldo.<br /></para>
        /// <para># NumeroTransacaoParaEstorno: Protocolo que o consumidor gerou para efetivar a carga de crédito.<br /></para>
        /// <para># ProtocoloRequisicao: Número único gerado pelo cliente consumidor do serviço, utilizado para impedir requisições duplicadas.</para></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<EstornarCargaCartaoResponse> EstornarCargaAsync(EstornarCargaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/EstornarCarga");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(EstornarCargaCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarCargaCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(EstornarCargaCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Transferência de crédito para cartão da mesma processadora</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<TransferirValorCartaoResponse> TransferirParaCartaoAsync(TransferirValorCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return TransferirParaCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Transferência de crédito para cartão da mesma processadora</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public TransferirValorCartaoResponse TransferirParaCartao(TransferirValorCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await TransferirParaCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Transferência de crédito para cartão da mesma processadora</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<TransferirValorCartaoResponse> TransferirParaCartaoAsync(TransferirValorCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/TransferirParaCartao");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(TransferirValorCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<TransferirValorCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(TransferirValorCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Estornar transferência de crédito realizada entre cartões da mesma processadora.
        /// Será estornado o valor da operação vinculada ao protocolo que originou a transferência.
        /// Procedimento sujeito a existência de saldo no cartão origem da transferência</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<EstornarTransferenciaCartaoResponse> EstornarTransferenciaCartaoAsync(EstornarTransferenciaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EstornarTransferenciaCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Estornar transferência de crédito realizada entre cartões da mesma processadora.
        /// Será estornado o valor da operação vinculada ao protocolo que originou a transferência.
        /// Procedimento sujeito a existência de saldo no cartão origem da transferência</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public EstornarTransferenciaCartaoResponse EstornarTransferenciaCartao(EstornarTransferenciaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EstornarTransferenciaCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Estornar transferência de crédito realizada entre cartões da mesma processadora.
        /// Será estornado o valor da operação vinculada ao protocolo que originou a transferência.
        /// Procedimento sujeito a existência de saldo no cartão origem da transferência</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<EstornarTransferenciaCartaoResponse> EstornarTransferenciaCartaoAsync(EstornarTransferenciaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/EstornarTransferenciaCartao");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(EstornarTransferenciaCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarTransferenciaCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(EstornarTransferenciaCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Transferencia de um cartao para uma conta bancaria</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<TransferirValorContaBancariaResponse> TransferirParaContaBancariaAsync(TransferirValorContaBancariaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return TransferirParaContaBancariaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Transferencia de um cartao para uma conta bancaria</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public TransferirValorContaBancariaResponse TransferirParaContaBancaria(TransferirValorContaBancariaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await TransferirParaContaBancariaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Transferencia de um cartao para uma conta bancaria</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<TransferirValorContaBancariaResponse> TransferirParaContaBancariaAsync(TransferirValorContaBancariaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/TransferirParaContaBancaria");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(TransferirValorContaBancariaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<TransferirValorContaBancariaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(TransferirValorContaBancariaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Estornar Transfefrencia de uma conta bancaria</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<EstornarTransferenciaContaBancariaResponse> EstornarTransferenciaContaBancariaAsync(EstornarTransferenciaContaBancariaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EstornarTransferenciaContaBancariaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Estornar Transfefrencia de uma conta bancaria</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public EstornarTransferenciaContaBancariaResponse EstornarTransferenciaContaBancaria(EstornarTransferenciaContaBancariaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EstornarTransferenciaContaBancariaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Estornar Transfefrencia de uma conta bancaria</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<EstornarTransferenciaContaBancariaResponse> EstornarTransferenciaContaBancariaAsync(EstornarTransferenciaContaBancariaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/EstornarTransferenciaContaBancaria");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(EstornarTransferenciaContaBancariaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarTransferenciaContaBancariaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(EstornarTransferenciaContaBancariaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Efetuar compra em rede credenciada, debitando o cartão origem em tempo real, e gerando agenda de pagamento ao estabelecimento posteriormente em D+N</summary>
        /// <param name="request">Informações cadastrais para realizar transação.
        ///             <li>CnpjEstabelecimento: Cnpj do estabelecimento da compra (Necessário estar cadastrado como Pessoa. Utilizar a API "Pessoa/Integrar")</li><li>Cartao: Grupo com dados de identificação do cartão</li><ul><li>Identificador: Número único de identificação do cartão</li><li>Produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CompraRedeCredenciadaResponse> CompraRedeCredenciadaAsync(CompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CompraRedeCredenciadaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Efetuar compra em rede credenciada, debitando o cartão origem em tempo real, e gerando agenda de pagamento ao estabelecimento posteriormente em D+N</summary>
        /// <param name="request">Informações cadastrais para realizar transação.
        ///             <li>CnpjEstabelecimento: Cnpj do estabelecimento da compra (Necessário estar cadastrado como Pessoa. Utilizar a API "Pessoa/Integrar")</li><li>Cartao: Grupo com dados de identificação do cartão</li><ul><li>Identificador: Número único de identificação do cartão</li><li>Produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CompraRedeCredenciadaResponse CompraRedeCredenciada(CompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CompraRedeCredenciadaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Efetuar compra em rede credenciada, debitando o cartão origem em tempo real, e gerando agenda de pagamento ao estabelecimento posteriormente em D+N</summary>
        /// <param name="request">Informações cadastrais para realizar transação.
        ///             <li>CnpjEstabelecimento: Cnpj do estabelecimento da compra (Necessário estar cadastrado como Pessoa. Utilizar a API "Pessoa/Integrar")</li><li>Cartao: Grupo com dados de identificação do cartão</li><ul><li>Identificador: Número único de identificação do cartão</li><li>Produto: Código do tipo de produto referente ao cartão. Maiores detalhes na documentação da api "/Produtos"</li></ul></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CompraRedeCredenciadaResponse> CompraRedeCredenciadaAsync(CompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/CompraRedeCredenciada");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CompraRedeCredenciadaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CompraRedeCredenciadaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CompraRedeCredenciadaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Em construção.
        /// Estornar compra realizada em rede credenciada, retornando o dinheiro em tempo real ao cartão origem da transação, e gerando agenda de estorno da operação com o estabelecimento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<EstornarCompraRedeCredenciadaResponse> EstornarCompraRedeCredenciadaAsync(EstornarCompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return EstornarCompraRedeCredenciadaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Em construção.
        /// Estornar compra realizada em rede credenciada, retornando o dinheiro em tempo real ao cartão origem da transação, e gerando agenda de estorno da operação com o estabelecimento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public EstornarCompraRedeCredenciadaResponse EstornarCompraRedeCredenciada(EstornarCompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await EstornarCompraRedeCredenciadaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Em construção.
        /// Estornar compra realizada em rede credenciada, retornando o dinheiro em tempo real ao cartão origem da transação, e gerando agenda de estorno da operação com o estabelecimento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<EstornarCompraRedeCredenciadaResponse> EstornarCompraRedeCredenciadaAsync(EstornarCompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/EstornarCompraRedeCredenciada");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(EstornarCompraRedeCredenciadaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarCompraRedeCredenciadaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(EstornarCompraRedeCredenciadaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Consultas as compras efetuadas em rede credenciada pelo CNPJ do estabelecimento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ConsultarCompraRedeCredenciadaResponse> ConsultarCompraRedeCredenciadaAsync(ConsultarCompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ConsultarCompraRedeCredenciadaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Consultas as compras efetuadas em rede credenciada pelo CNPJ do estabelecimento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ConsultarCompraRedeCredenciadaResponse ConsultarCompraRedeCredenciada(ConsultarCompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ConsultarCompraRedeCredenciadaAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Consultas as compras efetuadas em rede credenciada pelo CNPJ do estabelecimento.</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ConsultarCompraRedeCredenciadaResponse> ConsultarCompraRedeCredenciadaAsync(ConsultarCompraRedeCredenciadaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/ConsultarCompraRedeCredenciada");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ConsultarCompraRedeCredenciadaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarCompraRedeCredenciadaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ConsultarCompraRedeCredenciadaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Validacao de senha do cartão</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ValidarSenhaCartaoResponse> ValidarSenhaCartaoAsync(ValidarSenhaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ValidarSenhaCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Validacao de senha do cartão</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ValidarSenhaCartaoResponse ValidarSenhaCartao(ValidarSenhaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ValidarSenhaCartaoAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Validacao de senha do cartão</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ValidarSenhaCartaoResponse> ValidarSenhaCartaoAsync(ValidarSenhaCartaoRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Operacoes/ValidarSenhaCartao");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ValidarSenhaCartaoResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ValidarSenhaCartaoResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ValidarSenhaCartaoResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NSwag", "11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class PessoasClient 
    {
        private string _baseUrl = "";
        private System.Lazy<Newtonsoft.Json.JsonSerializerSettings> _settings;
    
        public PessoasClient(string baseUrl)
        {
            BaseUrl = baseUrl; 
            _settings = new System.Lazy<Newtonsoft.Json.JsonSerializerSettings>(() => 
            {
                var settings = new Newtonsoft.Json.JsonSerializerSettings();
                UpdateJsonSerializerSettings(settings);
                return settings;
            });
        }
    
        public string BaseUrl 
        {
            get { return _baseUrl; }
            set { _baseUrl = value; }
        }
    
        protected Newtonsoft.Json.JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }
    
        partial void UpdateJsonSerializerSettings(Newtonsoft.Json.JsonSerializerSettings settings);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, string url);
        partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder);
        partial void ProcessResponse(System.Net.Http.HttpClient client, System.Net.Http.HttpResponseMessage response);
    
        /// <summary>Buscar as contas bancárias vinculadas a uma pessoa</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<PessoaContaBancariaResponse>> ContasBancariasGetAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ContasBancariasGetAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar as contas bancárias vinculadas a uma pessoa</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<PessoaContaBancariaResponse> ContasBancariasGet(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ContasBancariasGetAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar as contas bancárias vinculadas a uma pessoa</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<PessoaContaBancariaResponse>> ContasBancariasGetAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (documento == null)
                throw new System.ArgumentNullException("documento");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/ContasBancarias/{documento}");
            urlBuilder_.Replace("{documento}", System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<PessoaContaBancariaResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<PessoaContaBancariaResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<PessoaContaBancariaResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Buscar as contas bancárias vinculadas a uma pessoa de apenas um banco</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<System.Collections.Generic.List<PessoaContaBancariaResponse>> ContasBancariasGetAsync(string documento, string codigoBanco, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return ContasBancariasGetAsync(documento, codigoBanco, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar as contas bancárias vinculadas a uma pessoa de apenas um banco</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Collections.Generic.List<PessoaContaBancariaResponse> ContasBancariasGet(string documento, string codigoBanco, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await ContasBancariasGetAsync(documento, codigoBanco, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar as contas bancárias vinculadas a uma pessoa de apenas um banco</summary>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<System.Collections.Generic.List<PessoaContaBancariaResponse>> ContasBancariasGetAsync(string documento, string codigoBanco, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (documento == null)
                throw new System.ArgumentNullException("documento");
    
            if (codigoBanco == null)
                throw new System.ArgumentNullException("codigoBanco");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/ContasBancarias/{documento}/{codigoBanco}");
            urlBuilder_.Replace("{documento}", System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture)));
            urlBuilder_.Replace("{codigoBanco}", System.Uri.EscapeDataString(ConvertToString(codigoBanco, System.Globalization.CultureInfo.InvariantCulture)));
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(System.Collections.Generic.List<PessoaContaBancariaResponse>); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.List<PessoaContaBancariaResponse>>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(System.Collections.Generic.List<PessoaContaBancariaResponse>);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Integrar ou atualizar registro pessoa ou estabelecimento.
        /// Para integrações com finalidade de vincular cartão a portador, execute a API "/Operacoes/VincularPortador" para maior facilidade.</summary>
        /// <param name="request">Dados cadastrais da pessoa.
        ///             <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Resultado da integração.
        ///             <li>status: Enum Falha/Sucesso</li><li>mensagens: Lista com mensagens de erros encontradas na integração</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<IntegrarPessoaResponse> IntegrarAsync(IntegrarPessoaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return IntegrarAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Integrar ou atualizar registro pessoa ou estabelecimento.
        /// Para integrações com finalidade de vincular cartão a portador, execute a API "/Operacoes/VincularPortador" para maior facilidade.</summary>
        /// <param name="request">Dados cadastrais da pessoa.
        ///             <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Resultado da integração.
        ///             <li>status: Enum Falha/Sucesso</li><li>mensagens: Lista com mensagens de erros encontradas na integração</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public IntegrarPessoaResponse Integrar(IntegrarPessoaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await IntegrarAsync(request, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Integrar ou atualizar registro pessoa ou estabelecimento.
        /// Para integrações com finalidade de vincular cartão a portador, execute a API "/Operacoes/VincularPortador" para maior facilidade.</summary>
        /// <param name="request">Dados cadastrais da pessoa.
        ///             <li>nome: Nome da pessoa ou razão social para empresa</li><li>cpfCnpj: Documento CPF/CNPJ</li><li>nomeFantasia: Nome comercialmente de identificação do cliente (Apelido)</li><li>sexo: Para pessoas física: M = Masculino / F = Feminino. Para pessoas jurídica não há validade esta informação.</li><li>cidade: Código IBGE da cidade</li></param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Resultado da integração.
        ///             <li>status: Enum Falha/Sucesso</li><li>mensagens: Lista com mensagens de erros encontradas na integração</li></returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<IntegrarPessoaResponse> IntegrarAsync(IntegrarPessoaRequest request, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/Integrar");
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    var content_ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request, _settings.Value));
                    content_.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json");
                    request_.Content = content_;
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(IntegrarPessoaResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<IntegrarPessoaResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(IntegrarPessoaResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Função administrativa não sendo liberada a todos os consumidores.
        /// Bloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.
        /// No caso de um portador, não será mais possível vincular novos cartões, não siginifica que o cartão atual será desativado.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa a ser desativada</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Sucesso no bloqueio da pessoa</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ApiActionResponse> BloquearAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return BloquearAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Função administrativa não sendo liberada a todos os consumidores.
        /// Bloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.
        /// No caso de um portador, não será mais possível vincular novos cartões, não siginifica que o cartão atual será desativado.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa a ser desativada</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Sucesso no bloqueio da pessoa</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ApiActionResponse Bloquear(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await BloquearAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Função administrativa não sendo liberada a todos os consumidores.
        /// Bloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.
        /// No caso de um portador, não será mais possível vincular novos cartões, não siginifica que o cartão atual será desativado.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa a ser desativada</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Sucesso no bloqueio da pessoa</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ApiActionResponse> BloquearAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/Bloquear?");
            if (documento != null) urlBuilder_.Append("documento=").Append(System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ApiActionResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ApiActionResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ApiActionResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Função administrativa não sendo liberada a todos os consumidores.
        /// Desbloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.
        /// No caso de um portador, este poderá voltar a possuir novos cartões de vinculo, não significa que os cartões bloqueados serão reativados.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa a ser ativada</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Sucesso no desbloqueio da pessoa</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<ApiActionResponse> DesbloquearAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return DesbloquearAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Função administrativa não sendo liberada a todos os consumidores.
        /// Desbloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.
        /// No caso de um portador, este poderá voltar a possuir novos cartões de vinculo, não significa que os cartões bloqueados serão reativados.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa a ser ativada</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Sucesso no desbloqueio da pessoa</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public ApiActionResponse Desbloquear(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await DesbloquearAsync(documento, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Função administrativa não sendo liberada a todos os consumidores.
        /// Desbloquear utilização dos recursos da plataforma da pessoa ou estabelecimento como o documento indicado.
        /// No caso de um portador, este poderá voltar a possuir novos cartões de vinculo, não significa que os cartões bloqueados serão reativados.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa a ser ativada</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Sucesso no desbloqueio da pessoa</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<ApiActionResponse> DesbloquearAsync(string documento, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/Desbloquear?");
            if (documento != null) urlBuilder_.Append("documento=").Append(System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture))).Append("&");
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Content = new System.Net.Http.StringContent(string.Empty, System.Text.Encoding.UTF8, "application/json");
                    request_.Method = new System.Net.Http.HttpMethod("POST");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(ApiActionResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<ApiActionResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(ApiActionResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Buscar cartões ativos do portador</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa</param>
        /// <param name="idProdutos">Produtos para listas os cartões. Vazio para listar todos</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<CartaoVinculadoPessoaListResponse> CartoesVinculadosAsync(string documento, System.Collections.Generic.IEnumerable<int> idProdutos, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return CartoesVinculadosAsync(documento, idProdutos, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Buscar cartões ativos do portador</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa</param>
        /// <param name="idProdutos">Produtos para listas os cartões. Vazio para listar todos</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public CartaoVinculadoPessoaListResponse CartoesVinculados(string documento, System.Collections.Generic.IEnumerable<int> idProdutos, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await CartoesVinculadosAsync(documento, idProdutos, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Buscar cartões ativos do portador</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa</param>
        /// <param name="idProdutos">Produtos para listas os cartões. Vazio para listar todos</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Success</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<CartaoVinculadoPessoaListResponse> CartoesVinculadosAsync(string documento, System.Collections.Generic.IEnumerable<int> idProdutos, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (documento == null)
                throw new System.ArgumentNullException("documento");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/CartoesVinculados/{documento}?");
            urlBuilder_.Replace("{documento}", System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture)));
            if (idProdutos != null) foreach (var item_ in idProdutos) { urlBuilder_.Append("idProdutos=").Append(System.Uri.EscapeDataString(ConvertToString(item_, System.Globalization.CultureInfo.InvariantCulture))).Append("&"); }
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(CartaoVinculadoPessoaListResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(CartaoVinculadoPessoaListResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        /// <summary>Listar histórico dos cartões já vinculados ao portador, exibindo os cartões ativos e os desativados.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa</param>
        /// <param name="idProdutos">Produtos para listas os cartões. Vazio para listar todos</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com todos os cartões já vinculados ao portador de acordo com a requisição</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public System.Threading.Tasks.Task<HistoricoCartaoPessoaListResponse> HistoricoCartoesAsync(string documento, System.Collections.Generic.IEnumerable<int> idProdutos, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return HistoricoCartoesAsync(documento, idProdutos, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None);
        }
    
        /// <summary>Listar histórico dos cartões já vinculados ao portador, exibindo os cartões ativos e os desativados.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa</param>
        /// <param name="idProdutos">Produtos para listas os cartões. Vazio para listar todos</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com todos os cartões já vinculados ao portador de acordo com a requisição</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        public HistoricoCartaoPessoaListResponse HistoricoCartoes(string documento, System.Collections.Generic.IEnumerable<int> idProdutos, string x_auth_token, string x_audit_user_doc, string x_audit_user_name)
        {
            return System.Threading.Tasks.Task.Run(async () => await HistoricoCartoesAsync(documento, idProdutos, x_auth_token, x_audit_user_doc, x_audit_user_name, System.Threading.CancellationToken.None)).GetAwaiter().GetResult();
        }
    
        /// <summary>Listar histórico dos cartões já vinculados ao portador, exibindo os cartões ativos e os desativados.</summary>
        /// <param name="documento">Documento CPF/CNPJ da pessoa</param>
        /// <param name="idProdutos">Produtos para listas os cartões. Vazio para listar todos</param>
        /// <param name="x_auth_token">Token de identificação da aplicação + empresa + administradora</param>
        /// <param name="x_audit_user_doc">Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.</param>
        /// <param name="x_audit_user_name">Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação</param>
        /// <returns>Lista com todos os cartões já vinculados ao portador de acordo com a requisição</returns>
        /// <exception cref="SwaggerException">A server side error occurred.</exception>
        /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
        public async System.Threading.Tasks.Task<HistoricoCartaoPessoaListResponse> HistoricoCartoesAsync(string documento, System.Collections.Generic.IEnumerable<int> idProdutos, string x_auth_token, string x_audit_user_doc, string x_audit_user_name, System.Threading.CancellationToken cancellationToken)
        {
            if (documento == null)
                throw new System.ArgumentNullException("documento");
    
            var urlBuilder_ = new System.Text.StringBuilder();
            urlBuilder_.Append(BaseUrl != null ? BaseUrl.TrimEnd('/') : "").Append("/Pessoas/HistoricoCartoes/{documento}?");
            urlBuilder_.Replace("{documento}", System.Uri.EscapeDataString(ConvertToString(documento, System.Globalization.CultureInfo.InvariantCulture)));
            if (idProdutos != null) foreach (var item_ in idProdutos) { urlBuilder_.Append("idProdutos=").Append(System.Uri.EscapeDataString(ConvertToString(item_, System.Globalization.CultureInfo.InvariantCulture))).Append("&"); }
            urlBuilder_.Length--;
    
            var client_ = new System.Net.Http.HttpClient();
            try
            {
                using (var request_ = new System.Net.Http.HttpRequestMessage())
                {
                    if (x_auth_token == null)
                        throw new System.ArgumentNullException("x_auth_token");
                    request_.Headers.TryAddWithoutValidation("x-auth-token", ConvertToString(x_auth_token, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_doc == null)
                        throw new System.ArgumentNullException("x_audit_user_doc");
                    request_.Headers.TryAddWithoutValidation("x-audit-user-doc", ConvertToString(x_audit_user_doc, System.Globalization.CultureInfo.InvariantCulture));
                    if (x_audit_user_name != null)
                        request_.Headers.TryAddWithoutValidation("x-audit-user-name", ConvertToString(x_audit_user_name, System.Globalization.CultureInfo.InvariantCulture));
                    request_.Method = new System.Net.Http.HttpMethod("GET");
                    request_.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
    
                    PrepareRequest(client_, request_, urlBuilder_);
                    var url_ = urlBuilder_.ToString();
                    request_.RequestUri = new System.Uri(url_, System.UriKind.RelativeOrAbsolute);
                    PrepareRequest(client_, request_, url_);
    
                    var response_ = await client_.SendAsync(request_, System.Net.Http.HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
                    try
                    {
                        var headers_ = System.Linq.Enumerable.ToDictionary(response_.Headers, h_ => h_.Key, h_ => h_.Value);
                        foreach (var item_ in response_.Content.Headers)
                            headers_[item_.Key] = item_.Value;
    
                        ProcessResponse(client_, response_);
    
                        var status_ = ((int)response_.StatusCode).ToString();
                        if (status_ == "200") 
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            var result_ = default(HistoricoCartaoPessoaListResponse); 
                            try
                            {
                                result_ = Newtonsoft.Json.JsonConvert.DeserializeObject<HistoricoCartaoPessoaListResponse>(responseData_, _settings.Value);
                                return result_; 
                            } 
                            catch (System.Exception exception_) 
                            {
                                throw new SwaggerException("Could not deserialize the response body.", status_, responseData_, headers_, exception_);
                            }
                        }
                        else
                        if (status_ != "200" && status_ != "204")
                        {
                            var responseData_ = await response_.Content.ReadAsStringAsync().ConfigureAwait(false); 
                            throw new SwaggerException("The HTTP status code of the response was not expected (" + (int)response_.StatusCode + ").", status_, responseData_, headers_, null);
                        }
            
                        return default(HistoricoCartaoPessoaListResponse);
                    }
                    finally
                    {
                        if (response_ != null)
                            response_.Dispose();
                    }
                }
            }
            finally
            {
                if (client_ != null)
                    client_.Dispose();
            }
        }
    
        private string ConvertToString(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (value is System.Enum)
            {
                string name = System.Enum.GetName(value.GetType(), value);
                if (name != null)
                {
                    var field = System.Reflection.IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
                    if (field != null)
                    {
                        var attribute = System.Reflection.CustomAttributeExtensions.GetCustomAttribute(field, typeof(System.Runtime.Serialization.EnumMemberAttribute)) 
                            as System.Runtime.Serialization.EnumMemberAttribute;
                        if (attribute != null)
                        {
                            return attribute.Value;
                        }
                    }
                }
            }
            else if (value is byte[])
            {
                return System.Convert.ToBase64String((byte[]) value);
            }
            else if (value.GetType().IsArray)
            {
                var array = System.Linq.Enumerable.OfType<object>((System.Array) value);
                return string.Join(",", System.Linq.Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
            }
        
            return System.Convert.ToString(value, cultureInfo);
        }
    }
    
    

    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private System.DateTime? _dataDesvinculo;
        private string _portador;
        private ProdutoResponse _produto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesvinculo
        {
            get { return _dataDesvinculo; }
            set 
            {
                if (_dataDesvinculo != value)
                {
                    _dataDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("portador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Portador
        {
            get { return _portador; }
            set 
            {
                if (_portador != value)
                {
                    _portador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ProdutoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int _id;
        private string _nome;
        private bool _isMultiplasContas;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
        public int Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Always)]
        [System.ComponentModel.DataAnnotations.Required]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("isMultiplasContas", Required = Newtonsoft.Json.Required.Always)]
        public bool IsMultiplasContas
        {
            get { return _isMultiplasContas; }
            set 
            {
                if (_isMultiplasContas != value)
                {
                    _isMultiplasContas = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ProdutoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ProdutoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class VincularRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
        private IntegrarPessoaRequest _pessoa;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IntegrarPessoaRequest Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static VincularRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VincularRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IdentificadorCartao : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private int? _produto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IdentificadorCartao FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IdentificadorCartao>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IntegrarPessoaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nome;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private string _sexo;
        private int? _cidade;
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sexo
        {
            get { return _sexo; }
            set 
            {
                if (_sexo != value)
                {
                    _sexo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IntegrarPessoaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IntegrarPessoaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class VincularResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private VincularResponseStatus? _status;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public VincularResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static VincularResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VincularResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ApiProcessingStateOnServer : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServerState? _state;
        private string _errorMessage;
    
        [Newtonsoft.Json.JsonProperty("state", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ApiProcessingStateOnServerState? State
        {
            get { return _state; }
            set 
            {
                if (_state != value)
                {
                    _state = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("errorMessage", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set 
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ApiProcessingStateOnServer FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ApiProcessingStateOnServer>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DesvincularRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
        private string _motivoDesvinculo;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motivoDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotivoDesvinculo
        {
            get { return _motivoDesvinculo; }
            set 
            {
                if (_motivoDesvinculo != value)
                {
                    _motivoDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DesvincularRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DesvincularRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class DesvincularResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private DesvincularResponseStatus? _status;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public DesvincularResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static DesvincularResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<DesvincularResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class VincularCartaoVirtualRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _produto;
        private IntegrarPessoaRequest _pessoa;
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("pessoa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IntegrarPessoaRequest Pessoa
        {
            get { return _pessoa; }
            set 
            {
                if (_pessoa != value)
                {
                    _pessoa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static VincularCartaoVirtualRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VincularCartaoVirtualRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class VincularCartaoVirtualResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private VincularCartaoVirtualResponseStatus? _status;
        private string _mensagem;
        private int? _cartaoId;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public VincularCartaoVirtualResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? CartaoId
        {
            get { return _cartaoId; }
            set 
            {
                if (_cartaoId != value)
                {
                    _cartaoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static VincularCartaoVirtualResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<VincularCartaoVirtualResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarExtratoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
        private string _tipo;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set 
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set 
            {
                if (_dataFim != value)
                {
                    _dataFim = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarExtratoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarExtratoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarExtratoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarExtratoResponseStatus? _status;
        private string _mensagem;
        private decimal? _saldoInicialPeriodo;
        private decimal? _saldoFinalPeriodo;
        private System.Collections.Generic.List<ConsultarExtratoDetalheResponse> _detalhes;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarExtratoResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("saldoInicialPeriodo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? SaldoInicialPeriodo
        {
            get { return _saldoInicialPeriodo; }
            set 
            {
                if (_saldoInicialPeriodo != value)
                {
                    _saldoInicialPeriodo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("saldoFinalPeriodo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? SaldoFinalPeriodo
        {
            get { return _saldoFinalPeriodo; }
            set 
            {
                if (_saldoFinalPeriodo != value)
                {
                    _saldoFinalPeriodo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("detalhes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<ConsultarExtratoDetalheResponse> Detalhes
        {
            get { return _detalhes; }
            set 
            {
                if (_detalhes != value)
                {
                    _detalhes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarExtratoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarExtratoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarExtratoDetalheResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private System.DateTime? _data;
        private string _descricaoProcessadora;
        private string _tipo;
        private decimal? _valor;
        private int? _historicoId;
        private string _historico;
        private long? _protocoloRequisicao;
        private int? _protocoloProcessamento;
    
        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? Data
        {
            get { return _data; }
            set 
            {
                if (_data != value)
                {
                    _data = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("descricaoProcessadora", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DescricaoProcessadora
        {
            get { return _descricaoProcessadora; }
            set 
            {
                if (_descricaoProcessadora != value)
                {
                    _descricaoProcessadora = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historicoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? HistoricoId
        {
            get { return _historicoId; }
            set 
            {
                if (_historicoId != value)
                {
                    _historicoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Historico
        {
            get { return _historico; }
            set 
            {
                if (_historico != value)
                {
                    _historico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloProcessamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloProcessamento
        {
            get { return _protocoloProcessamento; }
            set 
            {
                if (_protocoloProcessamento != value)
                {
                    _protocoloProcessamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarExtratoDetalheResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarExtratoDetalheResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSaldoCartaoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSaldoCartaoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoCartaoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarSaldoCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarSaldoCartaoResponseStatus? _status;
        private string _mensagem;
        private decimal? _valorLimiteCredito;
        private decimal? _valorSaldoDisponivel;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarSaldoCartaoResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorLimiteCredito", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorLimiteCredito
        {
            get { return _valorLimiteCredito; }
            set 
            {
                if (_valorLimiteCredito != value)
                {
                    _valorLimiteCredito = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valorSaldoDisponivel", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? ValorSaldoDisponivel
        {
            get { return _valorSaldoDisponivel; }
            set 
            {
                if (_valorSaldoDisponivel != value)
                {
                    _valorSaldoDisponivel = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarSaldoCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarProtocoloRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _protocoloRequisicao;
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarProtocoloRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarProtocoloRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarProtocoloResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarProtocoloResponseStatus? _status;
        private string _mensagem;
        private int? _protocoloProcessamento;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarProtocoloResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloProcessamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloProcessamento
        {
            get { return _protocoloProcessamento; }
            set 
            {
                if (_protocoloProcessamento != value)
                {
                    _protocoloProcessamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarProtocoloResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarProtocoloResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CarregarValorRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _empresa;
        private IdentificadorCartao _cartao;
        private int? _historico;
        private decimal? _valor;
        private long? _protocoloRequisicao;
        private System.Collections.Generic.Dictionary<string, string> _informacoesAdicionais;
    
        [Newtonsoft.Json.JsonProperty("empresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Empresa
        {
            get { return _empresa; }
            set 
            {
                if (_empresa != value)
                {
                    _empresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Historico
        {
            get { return _historico; }
            set 
            {
                if (_historico != value)
                {
                    _historico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("informacoesAdicionais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> InformacoesAdicionais
        {
            get { return _informacoesAdicionais; }
            set 
            {
                if (_informacoesAdicionais != value)
                {
                    _informacoesAdicionais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CarregarValorRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CarregarValorRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CarregarValorResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private CarregarValorResponseStatus? _status;
        private string _mensagem;
        private int? _protocoloProcessamento;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CarregarValorResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloProcessamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloProcessamento
        {
            get { return _protocoloProcessamento; }
            set 
            {
                if (_protocoloProcessamento != value)
                {
                    _protocoloProcessamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CarregarValorResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CarregarValorResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarCargaCartaoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _empresa;
        private int? _numeroTransacaoParaEstorno;
        private long? _protocoloRequisicao;
    
        [Newtonsoft.Json.JsonProperty("empresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Empresa
        {
            get { return _empresa; }
            set 
            {
                if (_empresa != value)
                {
                    _empresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroTransacaoParaEstorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumeroTransacaoParaEstorno
        {
            get { return _numeroTransacaoParaEstorno; }
            set 
            {
                if (_numeroTransacaoParaEstorno != value)
                {
                    _numeroTransacaoParaEstorno = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarCargaCartaoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarCargaCartaoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarCargaCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private EstornarCargaCartaoResponseStatus? _status;
        private string _mensagem;
        private long? _numeroTransacao;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public EstornarCargaCartaoResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroTransacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? NumeroTransacao
        {
            get { return _numeroTransacao; }
            set 
            {
                if (_numeroTransacao != value)
                {
                    _numeroTransacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarCargaCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarCargaCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransferirValorCartaoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _empresa;
        private IdentificadorCartao _cartaoOrigem;
        private IdentificadorCartao _cartaoDestino;
        private int? _historico;
        private decimal? _valor;
        private long? _protocoloRequisicao;
        private System.Collections.Generic.Dictionary<string, string> _informacoesAdicionais;
    
        [Newtonsoft.Json.JsonProperty("empresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Empresa
        {
            get { return _empresa; }
            set 
            {
                if (_empresa != value)
                {
                    _empresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao CartaoOrigem
        {
            get { return _cartaoOrigem; }
            set 
            {
                if (_cartaoOrigem != value)
                {
                    _cartaoOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartaoDestino", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao CartaoDestino
        {
            get { return _cartaoDestino; }
            set 
            {
                if (_cartaoDestino != value)
                {
                    _cartaoDestino = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Historico
        {
            get { return _historico; }
            set 
            {
                if (_historico != value)
                {
                    _historico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("informacoesAdicionais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> InformacoesAdicionais
        {
            get { return _informacoesAdicionais; }
            set 
            {
                if (_informacoesAdicionais != value)
                {
                    _informacoesAdicionais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransferirValorCartaoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransferirValorCartaoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransferirValorCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private TransferirValorCartaoResponseStatus? _status;
        private string _mensagem;
        private int? _protocoloProcessamento;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TransferirValorCartaoResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloProcessamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloProcessamento
        {
            get { return _protocoloProcessamento; }
            set 
            {
                if (_protocoloProcessamento != value)
                {
                    _protocoloProcessamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransferirValorCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransferirValorCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarTransferenciaCartaoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _empresa;
        private int? _numeroTransacaoParaEstorno;
        private long? _protocoloRequisicao;
    
        [Newtonsoft.Json.JsonProperty("empresa", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Empresa
        {
            get { return _empresa; }
            set 
            {
                if (_empresa != value)
                {
                    _empresa = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroTransacaoParaEstorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumeroTransacaoParaEstorno
        {
            get { return _numeroTransacaoParaEstorno; }
            set 
            {
                if (_numeroTransacaoParaEstorno != value)
                {
                    _numeroTransacaoParaEstorno = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarTransferenciaCartaoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarTransferenciaCartaoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarTransferenciaCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private EstornarTransferenciaCartaoResponseStatus? _status;
        private string _mensagem;
        private long? _numeroTransacao;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public EstornarTransferenciaCartaoResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroTransacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? NumeroTransacao
        {
            get { return _numeroTransacao; }
            set 
            {
                if (_numeroTransacao != value)
                {
                    _numeroTransacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarTransferenciaCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarTransferenciaCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransferirValorContaBancariaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartaoOrigem;
        private IdentificadorContaBancaria _contaBancaria;
        private string _documentoFavorecido;
        private int? _historico;
        private decimal? _valor;
        private long? _protocoloRequisicao;
        private System.Collections.Generic.Dictionary<string, string> _informacoesAdicionais;
    
        [Newtonsoft.Json.JsonProperty("cartaoOrigem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao CartaoOrigem
        {
            get { return _cartaoOrigem; }
            set 
            {
                if (_cartaoOrigem != value)
                {
                    _cartaoOrigem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("contaBancaria", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorContaBancaria ContaBancaria
        {
            get { return _contaBancaria; }
            set 
            {
                if (_contaBancaria != value)
                {
                    _contaBancaria = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoFavorecido", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoFavorecido
        {
            get { return _documentoFavorecido; }
            set 
            {
                if (_documentoFavorecido != value)
                {
                    _documentoFavorecido = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Historico
        {
            get { return _historico; }
            set 
            {
                if (_historico != value)
                {
                    _historico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("informacoesAdicionais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> InformacoesAdicionais
        {
            get { return _informacoesAdicionais; }
            set 
            {
                if (_informacoesAdicionais != value)
                {
                    _informacoesAdicionais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransferirValorContaBancariaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransferirValorContaBancariaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IdentificadorContaBancaria : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeConta;
        private string _codigoBacenBanco;
        private string _agencia;
        private string _conta;
        private int? _digitoConta;
    
        [Newtonsoft.Json.JsonProperty("nomeConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeConta
        {
            get { return _nomeConta; }
            set 
            {
                if (_nomeConta != value)
                {
                    _nomeConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoBacenBanco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoBacenBanco
        {
            get { return _codigoBacenBanco; }
            set 
            {
                if (_codigoBacenBanco != value)
                {
                    _codigoBacenBanco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Agencia
        {
            get { return _agencia; }
            set 
            {
                if (_agencia != value)
                {
                    _agencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("conta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Conta
        {
            get { return _conta; }
            set 
            {
                if (_conta != value)
                {
                    _conta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("digitoConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? DigitoConta
        {
            get { return _digitoConta; }
            set 
            {
                if (_digitoConta != value)
                {
                    _digitoConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IdentificadorContaBancaria FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IdentificadorContaBancaria>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class TransferirValorContaBancariaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private TransferirValorContaBancariaResponseStatus? _status;
        private string _mensagem;
        private int? _protocoloProcessamento;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public TransferirValorContaBancariaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloProcessamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloProcessamento
        {
            get { return _protocoloProcessamento; }
            set 
            {
                if (_protocoloProcessamento != value)
                {
                    _protocoloProcessamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static TransferirValorContaBancariaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TransferirValorContaBancariaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarTransferenciaContaBancariaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _numeroTransacaoParaEstorno;
        private long? _protocoloRequisicao;
    
        [Newtonsoft.Json.JsonProperty("numeroTransacaoParaEstorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumeroTransacaoParaEstorno
        {
            get { return _numeroTransacaoParaEstorno; }
            set 
            {
                if (_numeroTransacaoParaEstorno != value)
                {
                    _numeroTransacaoParaEstorno = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarTransferenciaContaBancariaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarTransferenciaContaBancariaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarTransferenciaContaBancariaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private EstornarTransferenciaContaBancariaResponseStatus? _status;
        private string _mensagem;
        private long? _numeroTransacao;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public EstornarTransferenciaContaBancariaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroTransacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? NumeroTransacao
        {
            get { return _numeroTransacao; }
            set 
            {
                if (_numeroTransacao != value)
                {
                    _numeroTransacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarTransferenciaContaBancariaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarTransferenciaContaBancariaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CompraRedeCredenciadaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cnpjEstabelecimento;
        private IdentificadorCartao _cartao;
        private int? _historico;
        private decimal? _valor;
        private long? _protocoloRequisicao;
        private System.Collections.Generic.Dictionary<string, string> _informacoesAdicionais;
    
        [Newtonsoft.Json.JsonProperty("cnpjEstabelecimento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CnpjEstabelecimento
        {
            get { return _cnpjEstabelecimento; }
            set 
            {
                if (_cnpjEstabelecimento != value)
                {
                    _cnpjEstabelecimento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Historico
        {
            get { return _historico; }
            set 
            {
                if (_historico != value)
                {
                    _historico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("informacoesAdicionais", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.Dictionary<string, string> InformacoesAdicionais
        {
            get { return _informacoesAdicionais; }
            set 
            {
                if (_informacoesAdicionais != value)
                {
                    _informacoesAdicionais = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CompraRedeCredenciadaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CompraRedeCredenciadaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CompraRedeCredenciadaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private CompraRedeCredenciadaResponseStatus? _status;
        private string _mensagem;
        private int? _protocoloProcessamento;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CompraRedeCredenciadaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloProcessamento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? ProtocoloProcessamento
        {
            get { return _protocoloProcessamento; }
            set 
            {
                if (_protocoloProcessamento != value)
                {
                    _protocoloProcessamento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CompraRedeCredenciadaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CompraRedeCredenciadaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarCompraRedeCredenciadaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _numeroTransacaoParaEstorno;
        private long? _protocoloRequisicao;
    
        [Newtonsoft.Json.JsonProperty("numeroTransacaoParaEstorno", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? NumeroTransacaoParaEstorno
        {
            get { return _numeroTransacaoParaEstorno; }
            set 
            {
                if (_numeroTransacaoParaEstorno != value)
                {
                    _numeroTransacaoParaEstorno = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("protocoloRequisicao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? ProtocoloRequisicao
        {
            get { return _protocoloRequisicao; }
            set 
            {
                if (_protocoloRequisicao != value)
                {
                    _protocoloRequisicao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarCompraRedeCredenciadaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarCompraRedeCredenciadaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class EstornarCompraRedeCredenciadaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private EstornarCompraRedeCredenciadaResponseStatus? _status;
        private string _mensagem;
        private long? _numeroTransacao;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public EstornarCompraRedeCredenciadaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("numeroTransacao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public long? NumeroTransacao
        {
            get { return _numeroTransacao; }
            set 
            {
                if (_numeroTransacao != value)
                {
                    _numeroTransacao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static EstornarCompraRedeCredenciadaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<EstornarCompraRedeCredenciadaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarCompraRedeCredenciadaRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private string _cnpjEstabelecimento;
        private System.DateTime? _dataInicio;
        private System.DateTime? _dataFim;
    
        [Newtonsoft.Json.JsonProperty("cnpjEstabelecimento", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CnpjEstabelecimento
        {
            get { return _cnpjEstabelecimento; }
            set 
            {
                if (_cnpjEstabelecimento != value)
                {
                    _cnpjEstabelecimento = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataInicio", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataInicio
        {
            get { return _dataInicio; }
            set 
            {
                if (_dataInicio != value)
                {
                    _dataInicio = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataFim", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataFim
        {
            get { return _dataFim; }
            set 
            {
                if (_dataFim != value)
                {
                    _dataFim = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarCompraRedeCredenciadaRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarCompraRedeCredenciadaRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarCompraRedeCredenciadaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ConsultarCompraRedeCredenciadaResponseStatus? _status;
        private string _mensagem;
        private System.Collections.Generic.List<CompraRedeCredenciadaDetalhes> _compras;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ConsultarCompraRedeCredenciadaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("compras", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<CompraRedeCredenciadaDetalhes> Compras
        {
            get { return _compras; }
            set 
            {
                if (_compras != value)
                {
                    _compras = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarCompraRedeCredenciadaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarCompraRedeCredenciadaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CompraRedeCredenciadaDetalhes : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _id;
        private System.DateTime? _data;
        private decimal? _valor;
        private IdentificadorCartao _cartao;
        private string _documentoPortador;
        private CompraRedeCredenciadaDetalhesTipo? _tipo;
        private int? _historicoId;
        private string _historico;
    
        [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Id
        {
            get { return _id; }
            set 
            {
                if (_id != value)
                {
                    _id = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("data", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? Data
        {
            get { return _data; }
            set 
            {
                if (_data != value)
                {
                    _data = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("valor", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public decimal? Valor
        {
            get { return _valor; }
            set 
            {
                if (_valor != value)
                {
                    _valor = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("documentoPortador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string DocumentoPortador
        {
            get { return _documentoPortador; }
            set 
            {
                if (_documentoPortador != value)
                {
                    _documentoPortador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("tipo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public CompraRedeCredenciadaDetalhesTipo? Tipo
        {
            get { return _tipo; }
            set 
            {
                if (_tipo != value)
                {
                    _tipo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historicoId", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? HistoricoId
        {
            get { return _historicoId; }
            set 
            {
                if (_historicoId != value)
                {
                    _historicoId = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("historico", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Historico
        {
            get { return _historico; }
            set 
            {
                if (_historico != value)
                {
                    _historico = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CompraRedeCredenciadaDetalhes FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CompraRedeCredenciadaDetalhes>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ValidarSenhaCartaoRequest : System.ComponentModel.INotifyPropertyChanged
    {
        private IdentificadorCartao _cartao;
        private string _senha;
    
        [Newtonsoft.Json.JsonProperty("cartao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public IdentificadorCartao Cartao
        {
            get { return _cartao; }
            set 
            {
                if (_cartao != value)
                {
                    _cartao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("senha", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Senha
        {
            get { return _senha; }
            set 
            {
                if (_senha != value)
                {
                    _senha = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ValidarSenhaCartaoRequest FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ValidarSenhaCartaoRequest>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ValidarSenhaCartaoResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ValidarSenhaCartaoResponseStatus? _status;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ValidarSenhaCartaoResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ValidarSenhaCartaoResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ValidarSenhaCartaoResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ConsultarPessoaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private string _nome;
        private string _cpfCnpj;
        private string _nomeFantasia;
        private string _sexo;
        private bool? _ativo;
        private int? _cidade;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nome", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Nome
        {
            get { return _nome; }
            set 
            {
                if (_nome != value)
                {
                    _nome = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cpfCnpj", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CpfCnpj
        {
            get { return _cpfCnpj; }
            set 
            {
                if (_cpfCnpj != value)
                {
                    _cpfCnpj = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeFantasia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeFantasia
        {
            get { return _nomeFantasia; }
            set 
            {
                if (_nomeFantasia != value)
                {
                    _nomeFantasia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("sexo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Sexo
        {
            get { return _sexo; }
            set 
            {
                if (_sexo != value)
                {
                    _sexo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("ativo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public bool? Ativo
        {
            get { return _ativo; }
            set 
            {
                if (_ativo != value)
                {
                    _ativo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cidade", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Cidade
        {
            get { return _cidade; }
            set 
            {
                if (_cidade != value)
                {
                    _cidade = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ConsultarPessoaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ConsultarPessoaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class PessoaContaBancariaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private string _nomeConta;
        private string _codigoBacenBanco;
        private string _nomeBanco;
        private string _agencia;
        private string _conta;
        private int? _digitoConta;
    
        [Newtonsoft.Json.JsonProperty("nomeConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeConta
        {
            get { return _nomeConta; }
            set 
            {
                if (_nomeConta != value)
                {
                    _nomeConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("codigoBacenBanco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string CodigoBacenBanco
        {
            get { return _codigoBacenBanco; }
            set 
            {
                if (_codigoBacenBanco != value)
                {
                    _codigoBacenBanco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("nomeBanco", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string NomeBanco
        {
            get { return _nomeBanco; }
            set 
            {
                if (_nomeBanco != value)
                {
                    _nomeBanco = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("agencia", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Agencia
        {
            get { return _agencia; }
            set 
            {
                if (_agencia != value)
                {
                    _agencia = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("conta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Conta
        {
            get { return _conta; }
            set 
            {
                if (_conta != value)
                {
                    _conta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("digitoConta", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? DigitoConta
        {
            get { return _digitoConta; }
            set 
            {
                if (_digitoConta != value)
                {
                    _digitoConta = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static PessoaContaBancariaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<PessoaContaBancariaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class IntegrarPessoaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private IntegrarPessoaResponseStatus? _status;
        private System.Collections.Generic.List<ApiResponseValidation> _mensagens;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public IntegrarPessoaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagens", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<ApiResponseValidation> Mensagens
        {
            get { return _mensagens; }
            set 
            {
                if (_mensagens != value)
                {
                    _mensagens = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static IntegrarPessoaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<IntegrarPessoaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ApiResponseValidation : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiResponseValidationType? _type;
        private string _message;
        private string _field;
    
        [Newtonsoft.Json.JsonProperty("type", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ApiResponseValidationType? Type
        {
            get { return _type; }
            set 
            {
                if (_type != value)
                {
                    _type = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("message", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Message
        {
            get { return _message; }
            set 
            {
                if (_message != value)
                {
                    _message = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("field", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Field
        {
            get { return _field; }
            set 
            {
                if (_field != value)
                {
                    _field = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ApiResponseValidation FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ApiResponseValidation>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class ApiActionResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private ApiActionResponseStatus? _status;
        private string _mensagem;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public ApiActionResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("mensagem", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string Mensagem
        {
            get { return _mensagem; }
            set 
            {
                if (_mensagem != value)
                {
                    _mensagem = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static ApiActionResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ApiActionResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoVinculadoPessoaListResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private System.Collections.Generic.List<CartaoVinculadoPessoaResponse> _cartoes;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartoes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<CartaoVinculadoPessoaResponse> Cartoes
        {
            get { return _cartoes; }
            set 
            {
                if (_cartoes != value)
                {
                    _cartoes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoVinculadoPessoaListResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class CartaoVinculadoPessoaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private ProdutoResponse _produto;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static CartaoVinculadoPessoaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<CartaoVinculadoPessoaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class HistoricoCartaoPessoaListResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private ApiProcessingStateOnServer _processingStateOnServer;
        private System.Collections.Generic.List<HistoricoCartaoPessoaResponse> _cartoes;
    
        [Newtonsoft.Json.JsonProperty("processingStateOnServer", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ApiProcessingStateOnServer ProcessingStateOnServer
        {
            get { return _processingStateOnServer; }
            set 
            {
                if (_processingStateOnServer != value)
                {
                    _processingStateOnServer = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("cartoes", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.Collections.Generic.List<HistoricoCartaoPessoaResponse> Cartoes
        {
            get { return _cartoes; }
            set 
            {
                if (_cartoes != value)
                {
                    _cartoes = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static HistoricoCartaoPessoaListResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<HistoricoCartaoPessoaListResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public partial class HistoricoCartaoPessoaResponse : System.ComponentModel.INotifyPropertyChanged
    {
        private int? _identificador;
        private System.DateTime? _dataVinculo;
        private System.DateTime? _dataDesvinculo;
        private ProdutoResponse _produto;
        private string _motivoDesvinculo;
        private HistoricoCartaoPessoaResponseStatus? _status;
        private string _statusDescricao;
    
        [Newtonsoft.Json.JsonProperty("identificador", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public int? Identificador
        {
            get { return _identificador; }
            set 
            {
                if (_identificador != value)
                {
                    _identificador = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataVinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataVinculo
        {
            get { return _dataVinculo; }
            set 
            {
                if (_dataVinculo != value)
                {
                    _dataVinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("dataDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public System.DateTime? DataDesvinculo
        {
            get { return _dataDesvinculo; }
            set 
            {
                if (_dataDesvinculo != value)
                {
                    _dataDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("produto", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public ProdutoResponse Produto
        {
            get { return _produto; }
            set 
            {
                if (_produto != value)
                {
                    _produto = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("motivoDesvinculo", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string MotivoDesvinculo
        {
            get { return _motivoDesvinculo; }
            set 
            {
                if (_motivoDesvinculo != value)
                {
                    _motivoDesvinculo = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("status", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public HistoricoCartaoPessoaResponseStatus? Status
        {
            get { return _status; }
            set 
            {
                if (_status != value)
                {
                    _status = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        [Newtonsoft.Json.JsonProperty("statusDescricao", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        public string StatusDescricao
        {
            get { return _statusDescricao; }
            set 
            {
                if (_statusDescricao != value)
                {
                    _statusDescricao = value; 
                    RaisePropertyChanged();
                }
            }
        }
    
        public string ToJson() 
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
        
        public static HistoricoCartaoPessoaResponse FromJson(string data)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<HistoricoCartaoPessoaResponse>(data);
        }
    
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            var handler = PropertyChanged;
            if (handler != null) 
                handler(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum VincularResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ApiProcessingStateOnServerState
    {
        [System.Runtime.Serialization.EnumMember(Value = "Ok")]
        Ok = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum DesvincularResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum VincularCartaoVirtualResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarExtratoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarSaldoCartaoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarProtocoloResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum CarregarValorResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum EstornarCargaCartaoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TransferirValorCartaoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum EstornarTransferenciaCartaoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum TransferirValorContaBancariaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum EstornarTransferenciaContaBancariaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum CompraRedeCredenciadaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum EstornarCompraRedeCredenciadaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "NaoProcessado")]
        NaoProcessado = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Erro")]
        Erro = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "ProtocoloExistente")]
        ProtocoloExistente = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ConsultarCompraRedeCredenciadaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum CompraRedeCredenciadaDetalhesTipo
    {
        [System.Runtime.Serialization.EnumMember(Value = "Compra")]
        Compra = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Estorno")]
        Estorno = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ValidarSenhaCartaoResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum IntegrarPessoaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ApiResponseValidationType
    {
        [System.Runtime.Serialization.EnumMember(Value = "Information")]
        Information = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Warning")]
        Warning = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "Validation")]
        Validation = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "Error")]
        Error = 3,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum ApiActionResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "Falha")]
        Falha = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "Sucesso")]
        Sucesso = 1,
    
    }
    
    [System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "********* (Newtonsoft.Json v9.0.0.0)")]
    public enum HistoricoCartaoPessoaResponseStatus
    {
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoRemessaParaCliente")]
        AguardandoRemessaParaCliente = 0,
    
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoRemessaParaPontoDistribuicao")]
        AguardandoRemessaParaPontoDistribuicao = 1,
    
        [System.Runtime.Serialization.EnumMember(Value = "EmTransitoParaPontoDistribuicao")]
        EmTransitoParaPontoDistribuicao = 2,
    
        [System.Runtime.Serialization.EnumMember(Value = "EmTransitoRemessaRejeitada")]
        EmTransitoRemessaRejeitada = 3,
    
        [System.Runtime.Serialization.EnumMember(Value = "AguardandoVinculacao")]
        AguardandoVinculacao = 4,
    
        [System.Runtime.Serialization.EnumMember(Value = "Vinculado")]
        Vinculado = 5,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoExtravio")]
        CanceladoExtravio = 6,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoTrocaCartao")]
        CanceladoTrocaCartao = 7,
    
        [System.Runtime.Serialization.EnumMember(Value = "CanceladoManual")]
        CanceladoManual = 8,
    
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException : System.Exception
    {
        public string StatusCode { get; private set; }

        public string Response { get; private set; }

        public System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> Headers { get; private set; }

        public SwaggerException(string message, string statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, System.Exception innerException) 
            : base(message, innerException)
        {
            StatusCode = statusCode;
            Response = response; 
            Headers = headers;
        }

        public override string ToString()
        {
            return string.Format("HTTP Response: \n\n{0}\n\n{1}", Response, base.ToString());
        }
    }

    [System.CodeDom.Compiler.GeneratedCode("NSwag", "11.15.4.0 (NJsonSchema v********* (Newtonsoft.Json v9.0.0.0))")]
    public partial class SwaggerException<TResult> : SwaggerException
    {
        public TResult Result { get; private set; }

        public SwaggerException(string message, string statusCode, string response, System.Collections.Generic.Dictionary<string, System.Collections.Generic.IEnumerable<string>> headers, TResult result, System.Exception innerException) 
            : base(message, statusCode, response, headers, innerException)
        {
            Result = result;
        }
    }

}
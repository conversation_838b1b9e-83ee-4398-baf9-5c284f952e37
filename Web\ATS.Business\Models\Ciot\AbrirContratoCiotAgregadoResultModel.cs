using ATS.Domain.Entities;
using ATS.Domain.Models.Comum;

namespace ATS.Domain.Models.Ciot
{
    public class AbrirContratoCiotAgregadoResultModel : BaseResultModel
    {
        public AbrirContratoCiotAgregadoResultModel()
        {
        }

        public AbrirContratoCiotAgregadoResultModel(string mensagemFalha) : base(mensagemFalha)
        {
        }

        public ContratoCiotAgregado ContratoAgregado { get; set; }
        public DeclararOperacaoTransporteModel CiotResult { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface INotificacaoService : IService<Notificacao>
    {
        Notificacao Get(int id);
        Notificacao GetWithAllChilds(int id);
        Notificacao Add(Notificacao notificacao);
        IQueryable<Notificacao> GetPorDataEnvio(DateTime dataBase);
        IQueryable<Notificacao> ConsultarRecebidos(int idUsuario, DateTime dataInicial, DateTime dataFinal, int tipo);
        ValidationResult SetWithRecebido(List<int> id);
        ValidationResult SetWithLido(int id);
        bool InserirDapper(string idusuario, string tipo, string conteudo, DateTime datahoraenvio, int idTipoNotificacao);

        /// <summary>
        /// Retorna lista de notificações novas
        /// </summary>
        /// <param name="idUsuario">Id do Usuario</param>
        /// <returns></returns>
        ICollection<Notificacao> GetNotificacoesNovas(int idUsuario);

        /// <summary>
        /// Retorna todas as notificações para o idusuario pelo filtro de data
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <param name="dataFiltro"></param>
        /// <returns></returns>
        ICollection<Notificacao> GetNotificacoesPeloUsuario(int idUsuario);

        ValidationResult SetRecebidoNovo(List<int> ids);
        IQueryable<Notificacao> ObterNotificacoesNaoLidas(int idUsuario);
    }
}
using System;
using System.Collections.Generic;
using System.ComponentModel;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;

namespace ATS.Domain.Models.ViagemModels
{
    public enum StatusRetorno
    {
        [Description("Não realizado.")]
        NaoRealizado,
        [Description("Compra de pedágio solicitada com sucesso.")]
        Sucesso,
        [Description("Erro ao realizar compra de pedágio.")]
        Erro
    }

    public class ViagemIntegrarItemResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; } = string.Empty;
    }

    public class ViagemIntegrarResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }

        public ViagemIntegrarItemResponse CIOT { get; set; }
        public ViagemIntegrarItemResponse Pedagio { get; set; }
        public ViagemIntegrarItemResponse Cartao { get; set; }
    }

    public class ViagemCalcularValorPedagioResponse
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public decimal CustoTotal { get; set; }
        public decimal CustoTotalTag { get; set; }
        public Guid IdentificadorHistorico { get; set; }
        public IList<ViagemCalcularValorPedagioPracaResponse> Pracas { get; set; }
        public long? DistanciaTotalKm { get; set; }
    }

    public class ViagemCalcularValorPedagioPracaResponse
    {
        public string Nome { get; set; }
        public string Endereço { get; set; }
        public decimal Valor { get; set; }
    }

    public class ViagemConsultarResponse
    {
        public int IdViagem { get; set; }
        public int IdEmpresa { get; set; }
        public DateTime DataLancamento { get; set; }
        public DateTime? DataEmissao { get; set; }
        public EStatusViagem StatusViagem { get; set; }
        public bool HabilitarDeclaracaoCiot { get; set; }
        public bool CiotSucesso { get; set; }
        public string NumeroCIOT { get; set; }
        public EStatusDeclaracaoCiot DeclaracaoCiot { get; set; }
        public string SenhaCIOT { get; set; }
        public bool? CiotAgregado { get; set; }
        public bool? CiotEncerrado { get; set; }
        public int? IdProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string CPFCNPJProprietario { get; set; }
        public string RNTRC { get; set; }
        public string DocumentoCliente { get; set; }
        public DateTime? DataInicioFrete { get; set; }
        public DateTime? DataFimFrete { get; set; }
        public decimal? IRRPF { get; set; }
        public decimal? SESTSENAT { get; set; }
        public decimal? INSS { get; set; }
        public string NaturezaCarga { get; set; }
        public int? IdMotorista { get; set; }
        public string CpfMotorista { get; set; }
        public string NomeMotorista { get; set; }
        public int? IdFilial { get; set; }
        public string NomeFilial { get; set; }
        public string CepOrigem { get; set; }
        public string CepDestino { get; set; }
        public int? CodigoTipoCarga { get; set; }
        public string DescricaoTipoCarga { get; set; }
        public int? DistanciaViagem { get; set; }
        public bool AlgumaParcelaPaga { get; set; } = false;
        public ViagemConsultarVeiculoResponse Veiculo { get; set; }
        public IList<ViagemConsultarVeiculoResponse> Carretas { get; set; }
        public ViagemConsultarRotaResponse Rota { get; set; }
        public ViagemConsultarPedagioResponse Pedagio { get; set; }
        public IList<ViagemConsultarDocumentoResponse> Documentos { get; set; }
        public IList<ViagemConsultarParcelaResponse> Parcelas { get; set; }
        public ViagemIntegrarDadosPagamentoResponse DadosPagamento { get; set; }
        public ViagemIntegrarDadosAnttResponse DadosAntt { get; set; }
        public ViagemIntegrarDadosBancarioResponse DadosBancario{ get; set; }
        public DateTime? DataAtualizacao { get; set; }
        public bool DesabilitarBotoesViagem { get; set; }
        public bool DesabilitarBotaoParcela { get; set; }
    }

    public class ViagemConsultarVeiculoResponse
    {
        public int IdVeiculo { get; set; }
        public string Placa { get; set; }
        public string RENAVAM { get; set; }
        public string Tipo { get; set; }
        public string Modelo { get; set; }
        public int Eixos { get; set; }
    }

    public class ViagemConsultarRotaResponse
    {
        public IList<ViagemConsultarRotaPontoResponse> Pontos { get; set; }
    }

    public class ViagemConsultarRotaPontoResponse
    {
        public int Sequencia { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Cidade { get; set; }
        public string Estado { get; set; }
        public string Pais { get; set; }
    }

    public class ViagemConsultarPedagioResponse
    {
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public FornecedorEnum Fornecedor { get; set; }
        public EResultadoCompraPedagio StatusPedagio { get; set; }
        public decimal Valor { get; set; }
        public string ProtocoloValePedagio { get; set; }
        public string ProtocoloEnvioValePedagio { get; set; }
        public string MensagemStatus { get; set; }
        public string CNPJFornecedor { get; set; }
    }

    public class ViagemConsultarDocumentoResponse
    {
        public int Id { get; set; }
        public int IdClienteOrigem { get; set; }
        public string CnpjClienteOrigem { get; set; }
        public string ClienteOrigem { get; set; }
        public int IdClienteDestino { get; set; }
        public string CnpjClienteDestino { get; set; }
        public string ClienteDestino { get; set; }
        public string NumeroDocumento { get; set; }
        public string Serie { get; set; }
        public decimal PesoSaida { get; set; }
        public decimal Valor { get; set; }
        public ETipoDocumento TipoDocumento { get; set; }
        public string Chave { get; set; }
    }

    public class ViagemConsultarParcelaResponse
    {
        public int? IdViagemEvento { get; set; }
        public string Token { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public EViagemEventoFormaPagamento FormaPagamento { get; set; }
        public string FormaPagamentoString => FormaPagamento.GetDescription();
        public DateTime? DataPagamento { get; set; }
        public EStatusViagemEvento Status { get; set; }
        public string MensagemTransacao { get; set; }
        public string Instrucao { get; set; }
        public decimal Valor { get; set; }

        /// <inheritdoc cref="Domain.Entities.ViagemEvento.DataAgendamentoPagamento"/>
        public DateTime? DataAgendamentoPagamento { get; set; }

        public IList<ViagemConsultarParcelaDocumentoResponse> Documentos { get; set; }
        public IList<ViagemConsultarParcelaAcrescimoDescontoResponse> AcrescimosDescontos { get; set; }
    }
    public class ViagemIntegrarDadosPagamentoResponse
    {
        public EViagemFormaPagamento FormaPagamento { get; set; } = EViagemFormaPagamento.Outros;

        public string CodigoBacen { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }
    }
    
    public class ViagemIntegrarDadosAnttResponse
    {
        public bool? AltoDesempenho { get; set; }

        public bool? DestinacaoComercial { get; set; }

        public bool? FreteRetorno { get; set; }

        public string CepRetorno { get; set; }

        public int? DistanciaRetorno { get; set; }
    }

    public class ViagemConsultarParcelaDocumentoResponse
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public string Numero { get; set; }
        public ETipoDocumento TipoDocumento { get; set; }
        public bool AnexoObrigatorio { get; set; }
    }

    public class ViagemConsultarParcelaAcrescimoDescontoResponse
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public string NumeroDocumento { get; set; }
        public decimal Valor { get; set; }
        public ETipoValorAdicional Tipo { get; set; }
    }

    public class ViagemBaixarEventoResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }
        public int IdViagemEvento { get; set; }
        public decimal ValorPagamento { get; set; }
        public DateTime? DataPagamento { get; set; }
        public EStatusViagemEvento StatusEvento { get; set; }
        public EViagemEventoFormaPagamento FormaPagamento { get; set; }
    }

    public class ViagemCancelamentoEventoResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }
        public int IdViagemEvento { get; set; }
        public EStatusViagemEvento StatusEvento { get; set; }
    }

    public class ViagemBloquearEventoResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }
        public int IdViagemEvento { get; set; }
        public EStatusViagemEvento StatusEvento { get; set; }
    }

    public class ViagemDesbloquearEventoResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }
        public int IdViagemEvento { get; set; }
        public EStatusViagemEvento StatusEvento { get; set; }
    }
    
    public class ViagemCancelarResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }
    }
    
    public class ViagemBaixarResponse
    {
        public StatusRetorno Status { get; set; }
        public string Mensagem { get; set; }
    }

    public class ViagemDadosCiotResponse
    {
        public EResultadoDeclaracaoCiot CiotStatus { get; set; } 
        public string CiotMensagem { get; set; } 
        public string Numero { get; set; }
        public string Verificador { get; set; }
        public string Senha { get; set; }
    }

    public class ViagemIntegrarDadosBancarioResponse
    {
        public string Agencia { get; set; }
        public EViagemFormaPagamento? FormaPagamento { get; set; }
        public ETipoConta? TipoConta { get; set; }
        public string ContaCorrente { get; set; }
        public int? IdBanco { get; set; }
        public string DescricaoBanco { get; set; }
    }
    
    public class ConsultarFilialEDadosPagamentoResponse
    {
        public bool Sucesso { get; set; }
        public DadosFilialEPagamento Conteudo { get; set; }
    }

    public class DadosFilialEPagamento
    {
        public string CnpjFilial { get; set; }
        public string TipoConta { get; set; } 
        public string Banco { get; set; }
        public string Agencia { get; set; } 
        public string NumConta { get; set; }
        public string TipoDeclaracao { get; set; }
        public string MensagemFormaPagamento { get; set; }
        public string NomeFilial { get; set; }
    }

    public class ConsultarUltimaViagemEmpresaResponse
    {
        public int IdViagem { get; set; }
        public string DataEmissao { get; set; }
        public string DataLancamento { get; set; }
        public EStatusViagem StatusViagem { get; set; }
        public string NomeClienteOrigem { get; set; }
        public string NomeClienteDestino { get; set; }
    }

}


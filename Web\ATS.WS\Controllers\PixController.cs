﻿using ATS.WS.Controllers.Base;
using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO.Pix;
using ATS.Domain.DTO.PrestacaoContas;
using ATS.WS.Attributes;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Mobile.Common;

namespace ATS.WS.Controllers
{
    public class PixController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly ISolicitacaoChavePixService _solicitacaoChavePixService;
        
        public PixController(BaseControllerArgs baseArgs,
            IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp,
            ISolicitacaoChavePixService solicitacaoChavePixService) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _solicitacaoChavePixService = solicitacaoChavePixService;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string CadastrarChaveProprietario(CadastrarChavePixProprietarioRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                var retorno = _solicitacaoChavePixService.CadastrarChaveProprietario(@params);

                return new JsonResult().Responde(new Retorno<object>(retorno.Success, retorno.Value?.Mensagem ?? retorno.Messages.FirstOrDefault()));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
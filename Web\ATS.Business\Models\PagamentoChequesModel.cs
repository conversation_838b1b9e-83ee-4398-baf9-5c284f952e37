﻿using System;
using System.ComponentModel.DataAnnotations;
using ATS.CrossCutting.IoC.Validation;
using ATS.Domain.Enum;
using ATS.Domain.Exceptions;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class PagamentoChequesModel : IValidatedEntity
    {
        public int IdPagamentoChequeAgrupador { get; set; }

        public bool Processado { get; set; }

        public string NumeroBanco { get; set; }

        public string Serie { get; set; }

        public DateTime Data { get; set; }

        public decimal Valor { get; set; }

        public int NumeroCheque { get; set; }

        public ENominalCheque Nominal { get; set; }

        public void Validate()
        {
            if (!Processado)
                throw new PagamentoChequesInvalidException($"Cheque {NumeroCheque} não processado.");

            if (string.IsNullOrEmpty(NumeroBanco))
                throw new PagamentoChequesInvalidException($"Número do banco não informado.");

            if (string.IsNullOrEmpty(Serie))
                throw new PagamentoChequesInvalidException($"Série do cheque não informado.");

            if (Valor <= 0)
                throw new PagamentoChequesInvalidException($"Valor do cheque não informado.");

            //if (int.TryParse(NumeroCheque.ToString(), out _))
            //    throw new PagamentoChequesInvalidException($"Número do cheque não informado.");

            if (!System.Enum.IsDefined(typeof(ENominalCheque), Nominal))
                throw new PagamentoChequesInvalidException($"Nominal do cheque inválido.");
        }
    }
}
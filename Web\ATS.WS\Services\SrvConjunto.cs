using ATS.Domain.Entities;
using System.Linq;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;

namespace ATS.WS.Services
{
    public class SrvConjunto : SrvBase
    {
        private readonly IProprietarioApp _proprietarioApp;

        public SrvConjunto(IProprietarioApp proprietarioApp)
        {
            _proprietarioApp = proprietarioApp;
        }


        /*public Retorno<object> ConsultarPorPlaca(ConjuntoModel conjuntoModel)
        {

            var conjuntoApp = _conjuntoApp;

            if (string.IsNullOrEmpty(conjuntoModel.PlacaCavalo))
                return new Retorno<object>("Nenhum cavalo foi informado!");

            if (string.IsNullOrEmpty(conjuntoModel.CPF))
                return new Retorno<object>("Nenhum documento foi informado!");

            if (conjuntoModel.Carretas == null)
                conjuntoModel.Carretas = new List<ConjuntoCarretaModel>();

            // Procura o conjunto montado
            var conjuntos = conjuntoApp.ConsultarPorPlaca(conjuntoModel.PlacaCavalo, conjuntoModel.CPF);

            // Se não achar nenhum conjunto desmontado, daí sim, retorna a mensagem de objeto nulo
            if (conjuntos == null)
                return new Retorno<object>("Conjunto não encontrado!");

            return new Retorno<object>(true, conjuntos.Select(conjunto => new ConsultaVeiculoPorPlacaResponseModel
            {
                IdConjunto = conjunto.IdConjunto,
                Placa = conjunto.PlacaCavalo,
                CPF = conjunto.MotoristaBase.CPF,
                IdTipoCavalo = conjunto.IdTipoCavalo,
                NumeroEixos = conjunto.TipoCavalo.NumeroEixos,
                Marca = conjuntoApp.GetConjuntoEmpresa(conjunto.IdConjunto)?.Veiculo.Marca,
                Modelo = conjuntoApp.GetConjuntoEmpresa(conjunto.IdConjunto)?.Veiculo.Modelo,
                RENAVAN = conjuntoApp.GetConjuntoEmpresa(conjunto.IdConjunto)?.Veiculo?.RENAVAM,
                Chassi = conjuntoApp.GetConjuntoEmpresa(conjunto.IdConjunto)?.Veiculo?.Chassi,
                Carretas = conjunto.Carretas.Select(x => new CarretaConjuntoResponseModel()
                {
                    Placa = x.Placa,
                    Capacidade = x.TipoCarreta?.Capacidade,
                    IdTipoCarreta = x.IdTipoCarreta,
                    RENAVAN = GetVeiculo(conjuntoApp.GetConjuntoEmpresa(conjunto.IdConjunto), x.Placa)?.RENAVAM,
                    Chassi = GetVeiculo(conjuntoApp.GetConjuntoEmpresa(conjunto.IdConjunto), x.Placa)?.Chassi,
                    NumeroEixos = x.TipoCarreta?.QtdeEixos
                }).ToList()
            }));
        }*/

        public Veiculo GetVeiculo( ConjuntoEmpresa conjuntoEmpresa,  string placa)
        {
            return conjuntoEmpresa?.ConjuntosCarrata?
                            .FirstOrDefault(x => x.Veiculo.Placa == placa)?.Veiculo;
        }

        /*public Conjunto GetConjuntoMontado(string aCpf, bool withIncludes = true)
        {
            return _conjuntoApp.GetConjuntoMontado(aCpf, withIncludes);
        }*/

        /*public DadosProprietarioOrdemCarregamento VerificarCavaloPossuiProprietario(string cpf, string placaCavalo)
        {
            return _conjuntoApp.VerificarCavaloPossuiProprietario(cpf, placaCavalo);
        }*/
    }
}
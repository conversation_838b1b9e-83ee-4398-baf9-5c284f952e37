﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using NLog;

namespace ATS.Domain.Service
{
    public class RotaService : ServiceBase, IRotaService
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IRotaRepository _rotaRepository;
        private readonly IRotaTrajetoRepository _rotaTrajetoRepository;
        private readonly IRotaEstabelecimentoRepository _rotaEstabelecimentoRepository;

        public RotaService(IRotaRepository rotaRepository, IRotaTrajetoRepository rotaTrajetoRepository, IRotaEstabelecimentoRepository rotaEstabelecimentoRepository, IUserIdentity userIdentity)
        {
            _rotaRepository = rotaRepository;
            _rotaTrajetoRepository = rotaTrajetoRepository;
            _rotaEstabelecimentoRepository = rotaEstabelecimentoRepository;
            _userIdentity = userIdentity;
        }

        public Rota Get(int id)
        {
            return _rotaRepository
                .Find(x => x.IdRota == id)
                .Include(x => x.Empresa)
                .Include(x => x.RotaTrajeto)
                .Include(x => x.CidadeOrigem)
                .Include(x => x.CidadeOrigem.Estado)
                .Include(x => x.CidadeDestino)
                .Include(x => x.CidadeDestino.Estado)
                .Include(x => x.RotaEstabelecimento)
                .Include(x => x.RotaEstabelecimento.Select(re => re.Estabelecimento.Cidade.Estado))                
                .FirstOrDefault();
        }

        public List<Rota> ConsultarRotas(int idEmpresa, DateTime? dataBase)
        {
            var rotas = _rotaRepository
                .Find(x => x.IdEmpresa == idEmpresa)
                .Include(x => x.CidadeDestino)
                .Include(x => x.CidadeOrigem);

            if (dataBase.HasValue)
            {
                dataBase = new DateTimeHelper().StartOfDay(dataBase.Value);
                rotas = rotas.Where(x => x.DataBase > dataBase);
            }

            return rotas.ToList();
        }

        public void Reativar(int idRota)
        {
            var q = _rotaRepository.Where(x => x.IdRota == idRota);

            if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                q = q.Where(c => c.IdEmpresa == _userIdentity.IdEmpresa);

            var registro = q.FirstOrDefault();

            if (registro == null) throw new Exception($"Nenhum registro encontrado para o id {idRota}");

            registro.Ativo = true;

            _rotaRepository.Update(registro);
        }

        public void Inativar(int idRota)
        {
            var q = _rotaRepository.Where(x => x.IdRota == idRota);

            if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                q = q.Where(c => c.IdEmpresa == _userIdentity.IdEmpresa);

            var registro = q.FirstOrDefault();

            if (registro == null) throw new Exception($"Nenhum registro encontrado para o id {idRota}");

            registro.Ativo = false;

            _rotaRepository.Update(registro);
        }

        public object ConsultarGrid(int? idEmpresa, bool? listarInativos, string descricao, int take, 
            int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var tipos = _rotaRepository.GetAll()
                .Include(x => x.Empresa)
                .Include(x => x.CidadeOrigem)
                .Include(x => x.CidadeDestino);

            if (!listarInativos.HasValue || !listarInativos.Value) tipos = tipos.Where(x => x.Ativo); 

            if (idEmpresa.HasValue) tipos = tipos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (descricao != null) tipos = tipos.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                tipos = tipos.OrderBy(x => x.IdRota);
            else
                tipos = tipos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            // Aplica para todos os campos de string
           tipos =  tipos.AplicarFiltrosDinamicos<Rota>(filters);

            return new
            {
                totalItems = tipos.Count(),
                items = tipos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdRota,
                    x.Descricao,
                    x.Ativo,
                    TotalKm = x.TotalKm+" km",
                    CidadeOrigem = x.CidadeOrigem?.Nome,
                    CidadeDestino = x.CidadeDestino?.Nome,
                    RazaoSocialEmpresa = x.Empresa != null ? x.Empresa.RazaoSocial : string.Empty,
                })
            };
        }

        /*public int Inserir(RotaModel @params)
        {
            try
            {
                if (@params.TotalKm == 0)
                    throw new Exception("Não foi possível gerar a rota, verifique os endereços de origem e destino.");

                var trajetos = new List<RotaTrajeto>();
                if (@params.Segmentos.Any())
                {
                    @params.Segmentos.ForEach(seg =>
                    {
                        seg.Steps.ForEach(step =>
                        {
                            trajetos.Add(new RotaTrajeto
                            {
                                Descricao = step.Instructions,
                                Latitude = step.StartLocation.Latitude,
                                Longitude = step.StartLocation.Longitude,
                                DistanciaViagem = step.Distance.Text,
                                DistanciaViagemMetros = Convert.ToInt32(step.Distance.Value),
                                DuracaoViagem = step.Duration.Text,
                                DuracaoViagemSegundos = Convert.ToInt32(step.Duration.Value),
                                GoogleIcon = step.Icon
                            });
                        });
                    });
                }
                
                var estabelecimentosRota = _estabelecimentoService.ConsultarEstabelecimentosRota(@params.IdEmpresa,
                    @params.Inicial.Latitude, @params.Inicial.Longitude, @params.Final.Latitude, @params.Final.Longitude, null ,null, true).ToList();
               
                
                Rota r = new Rota
                {
                    Descricao = @params.Descricao,
                    IdCidadeOrigem = _cidadeService.GetIdCidadePorNome(@params.Inicial.Cidade),
                    IdCidadeDestino = _cidadeService.GetIdCidadePorNome(@params.Final.Cidade),
                    Ativo = true,
                    IdEmpresa = @params.IdEmpresa,
                    RotaTrajeto = trajetos,
                    From = @params.Inicial.Endereco,
                    FromLatitude = @params.Inicial.Latitude,
                    FromLongitude = @params.Inicial.Longitude,
                    To = @params.Final.Endereco,
                    ToLatitude = @params.Final.Latitude,
                    ToLongitude = @params.Final.Longitude,
                    TotalKm = @params.TotalKm,
                    TotalSegundos = @params.TotalTempo.Value,
                    TotalTempoViagem = @params.TotalTempo.StrValue,
                };
    
                var ocorrenciasNovas = from ocorrencia in @params.Ocorrencias
                                       where (ocorrencia.IdTipoOcorrencia != 0) && 
                                       (!ocorrencia.IdRotaOcorrencia.HasValue || ocorrencia.IdRotaOcorrencia < 1)
                                       select ocorrencia;
    
    
    
    
                _rotaRepository.Add(r);
                var rotestabRepo = _rotaEstabelecimentoRepository;
                foreach (var itemEstabelecimento in estabelecimentosRota)
                {
                    rotestabRepo.Add(new RotaEstabelecimento()
                    {
                        IdEstabelecimento = itemEstabelecimento.IdEstabelecimento,
                        IdRota = r.IdRota
                    });
                }
                return r.IdRota;
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception("Não foi possível gerar a rota.");
            }
        }*/

        /*public void Atualizar(RotaModel @params)
        {
            try
            {
                var rep = _rotaRepository;
                var row = Get(@params.IdRota.Value);
                if (row == null)
                    throw new Exception($"Nenhum registro encontrado para o id {@params.IdRota}");
    
                #region Rota TrajetoInserirSteps
                // Apaga todos..
                if (@params.ExcluirRotas)
                {
                    foreach (var item in row.RotaTrajeto.ToList())
                        row.RotaTrajeto.Remove(item);
                }
    
                // Insere novamente..
                if (@params.Segmentos.Any())
                {
                    foreach (var seg in @params.Segmentos)
                    {
                        foreach (var step in seg.Steps)
                        {
                            if(step == null)
                                continue;
                            
                            row.RotaTrajeto.Add(new RotaTrajeto
                            {
                                Descricao = step.Instructions,
                                Latitude = step.StartLocation.Latitude,
                                Longitude = step.StartLocation.Longitude,
                                DistanciaViagem = step.Distance.Text,
                                DistanciaViagemMetros = Convert.ToInt32(step.Distance.Value),
                                DuracaoViagem = step.Duration.Text,
                                DuracaoViagemSegundos = Convert.ToInt32(step.Duration.Value),
                                GoogleIcon = step.Icon
                            });
                        }
                    }
                    
                }
                #endregion
    
                row.Descricao = @params.Descricao;
                row.TotalKm = @params.TotalKm;
                row.TotalSegundos = @params.TotalTempo.Value;
                row.TotalTempoViagem = @params.TotalTempo.StrValue;

                var estabelecimentosRota = _estabelecimentoService.ConsultarEstabelecimentosRota(
                    row.IdEmpresa,
                    row.FromLatitude, row.FromLongitude,
                    row.ToLatitude, row.ToLongitude, null, null, true).ToList();
                
                var rotestabRepo = _rotaEstabelecimentoRepository;
                foreach (var itemEstabelecimento in estabelecimentosRota)
                {
                    if(!row.RotaEstabelecimento.Any(x => x.IdEstabelecimento == itemEstabelecimento.IdEstabelecimento && x.IdRota == row.IdRota))
                        rotestabRepo.Add(new RotaEstabelecimento()
                        {
                            IdEstabelecimento = itemEstabelecimento.IdEstabelecimento,
                            IdRota = row.IdRota
                        });
                }
                
                rep.Update(row);
    
                /*var toDelete = _rotogramaOcorrenciaRepository
                    .Include(x => x.Rotograma)
                    .Where(x => @params.IdsRemover.Contains(x.IdRotaOcorrencia.Value)
                    && x.Rotograma.Status == ERotogramaStatus.Aberto);
                
                
                foreach (var item in toDelete)
                    _rotogramaOcorrenciaRepository.Delete(item);#1#
                _logger.Info("Excluindo ocorrencia 1");
                var idsRemove = @params.OcorrenciasMapaRemover.Select(c => c.IdRotaOcorrencia).ToList();
                _logger.Info("Excluindo ocorrencia 2 " + idsRemove.Count);

                //#1#Remove de todas as rotas 
                /*var toDeleteTest = _rotogramaOcorrenciaRepository
                    .Include(x => x.Rotograma)
                    .Where(x => idsRemove.Contains(x.IdRotaOcorrencia.Value)
                                && x.Rotograma.Status == ERotogramaStatus.Aberto);
                //item ocorrência remover
                foreach (var itemOcorrenciaRemover in toDeleteTest)
                    _rotogramaOcorrenciaRepository.Delete(itemOcorrenciaRemover);#1#
                
                
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"Não foi possível salvar a rota.");
            }
        }*/

        public void Excluir(int idRota)
        {
            var q = _rotaRepository
                .Include(c => c.RotaTrajeto)
                .Include(c => c.RotaEstabelecimento)
                .Where(c => c.IdRota == idRota);

            if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                q = q.Where(c => c.IdEmpresa == _userIdentity.IdEmpresa);

            var rota = q.FirstOrDefault();

            if (rota == null) throw new Exception("Rota não encontrada");
            
            var trajetos = rota.RotaTrajeto.ToList();
            var estabelecimentosrota = rota.RotaEstabelecimento.ToList();
            
            _rotaTrajetoRepository.DeleteRange(trajetos);
            _rotaEstabelecimentoRepository.DeleteRange(estabelecimentosrota);
            _rotaRepository.Delete(rota);
        }                
    }
}

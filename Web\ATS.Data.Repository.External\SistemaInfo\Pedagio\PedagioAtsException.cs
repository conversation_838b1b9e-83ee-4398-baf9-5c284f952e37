﻿using System;
using System.Runtime.Serialization;

namespace ATS.Data.Repository.External.SistemaInfo.Pedagio
{
    public class PedagioAtsException : Exception
    {
        public PedagioAtsException()
        {
        }

        public PedagioAtsException(string message) : base(message)
        {
        }

        public PedagioAtsException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected PedagioAtsException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}

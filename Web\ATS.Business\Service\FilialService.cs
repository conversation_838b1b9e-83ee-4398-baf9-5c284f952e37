﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.Reports.Empresa.RelatorioListaEmpresa;
using ATS.CrossCutting.Reports.Filial;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class FilialService : ServiceBase, IFilialService
    {
        private readonly IFilialRepository _repository;
        private readonly IFilialContatosRepository _filialContatosRepository;
        private readonly ICidadeService _cidadeService;

        public FilialService(IFilialRepository repository, IFilialContatosRepository filialContatosRepository, ICidadeService cidadeService)
        {
            _repository = repository;
            _filialContatosRepository = filialContatosRepository;
            _cidadeService = cidadeService;
        }

        public ValidationResult Update(Filial filial)
        {
            try
            {
                FormatValues(filial);
                var validationResult = IsValid(filial, EProcesso.Update);

                if (!validationResult.IsValid)
                    return validationResult;

                _repository.Update(filial);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }
        }

        public Filial Get(int id)
        {
            return _repository
                .Include(o => o.FilialContatos)
                .Include(o => o.Empresa)
                .FirstOrDefault(x => x.IdFilial == id);
        }

        public FilialCrudResponse GetFilialCadastro(int id)
        {
            return _repository.Select(p => new FilialCrudResponse
            {
                IdFilial = p.IdFilial,
                CNPJEmpresa = p.Empresa.CNPJ,
                RazaoSocialEmpresa = p.Empresa.RazaoSocial,
                CNPJ = p.CNPJ,
                RazaoSocial = p.RazaoSocial,
                NomeFantasia = p.NomeFantasia,
                Sigla = p.Sigla,
                CEP = p.CEP,
                CodigoFilial = p.CodigoFilial,
                Endereco = p.Endereco,
                Complemento = p.Complemento,
                Numero = p.Numero,
                Bairro = p.Bairro,
                Telefone = p.Telefone,
                Email = p.Email,
                IdEmpresa = p.IdEmpresa,
                Ativo = p.Ativo,
                Latitude = p.Latitude,
                Longitude = p.Longitude,
                IdFilialMae = p.IdFilialMae,
                PontoApoio = p.PontoApoio,
                IdCidade = p.IdCidade,
                IdEstado = p.IdEstado,
                IdPais = p.IdPais,
                EmailSsl = p.EmailSsl,
                EmailUsuario = p.EmailUsuario,
                EmailSenha = p.EmailSenha,
                EmailServidor = p.EmailServidor,
                EmailPorta = p.EmailPorta,
                EmailEndereco = p.EmailEndereco,
                EmailNome = p.EmailNome,
                FilialContatos = p.FilialContatos,
                IdSistemaExterno = p.IdSistemaExterno,
                PontoReferencia = p.PontoReferencia
            }).FirstOrDefault(x => x.IdFilial == id);
        }

        public Filial Get(string cnpj)
        {
            return _repository
                .Include(o => o.Cidade)
                .Include(o => o.Estado)
                .Include(o => o.Empresa)
                .FirstOrDefault(x => x.CNPJ == cnpj);
        }

        public ValidationResult Add(Filial filial)
        {
            try
            {
                FormatValues(filial);
                var validationResult = IsValid(filial, EProcesso.Create);
                
                if (!validationResult.IsValid)
                    return validationResult;

                filial.DataHoraUltimaAtualizacao = DateTime.Now;

                _repository.Add(filial);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }
        }

        public IQueryable<Filial> Consultar(string razaoSocial, int? idEmpresa)
        {
            if (string.IsNullOrWhiteSpace(razaoSocial))
                razaoSocial = string.Empty;

            if (idEmpresa == null)
                idEmpresa = 0;

            return _repository.Consultar(razaoSocial, (int)idEmpresa);
        }

        public ValidationResult Inativar(int idFilial)
        {
            try
            {
                var filial = _repository.Get(idFilial);

                if (!filial.Ativo)
                    return new ValidationResult().Add("Filial já desativado na base de dados.");

                filial.Ativo = false;
                filial.DataHoraUltimaAtualizacao = DateTime.Now;

                _repository.Update(filial);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Reativar(int idFilial)
        {
            try
            {
                var filial = _repository.Get(idFilial);

                if (filial.Ativo)
                    return new ValidationResult().Add("Filial já ativado na base de dados.");

                filial.Ativo = true;
                filial.DataHoraUltimaAtualizacao = DateTime.Now;

                _repository.Update(filial);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult AlterarStatus(int idFilial)
        {
            try
            {
                var repository = _repository;
                var filial = repository.FirstOrDefault(o => o.IdFilial == idFilial);

                if (filial == null)
                    return new ValidationResult().Add("Filial não encontrada.");

                filial.Ativo = !filial.Ativo;
                repository.Update(filial);
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public IQueryable<Filial> GetListaFilialPorEmpresa(int idEmpresa)
        {
            return _repository.GetFiliaisPorEmpresa(idEmpresa);
        }

        public Filial GetFilialPorEmpresa(int idEmpresa, int idFilial)
        {
            return _repository.GetFilialPorEmpresa(idEmpresa, idFilial);
        }

        public int? GetIdPorCodigoFilial(int idEmpresa, string cnpjFilial, string codigoFilial)
        {
            var query = _repository.All()
                .Where(c => c.IdEmpresa == idEmpresa && c.CodigoFilial == codigoFilial);

            if (!string.IsNullOrWhiteSpace(cnpjFilial))
                query = query.Where(f => f.CNPJ == cnpjFilial);

            var id = query.Select(c => c.IdFilial).FirstOrDefault();

            if (id == 0)
                return null;
            
            return id;
        }

        public string GetCnpjPorId(int idFilial)
        {
            var cnpj = _repository.GetCnpjPorId(idFilial);
            return cnpj;
        }

        public IEnumerable<Filial> GetFiliaisAtualizadas(int? idEmpresa, DateTime dataAtualizacao)
        {
            return _repository.GetIdsFiliaisAtualizadas(dataAtualizacao, idEmpresa).ToList();
        }

        public int? GetIdPorCnpj(string cpnj)
        {
            var cpnjOnlyNumbers = cpnj.OnlyNumbers();
            var idFilial = _repository.Find(x => x.CNPJ == cpnjOnlyNumbers && x.Ativo)?.Select(x => x.IdFilial).FirstOrDefault();

            return idFilial.GetValueOrDefault(0) <= 0 ? (int?)null : idFilial.GetValueOrDefault();
        }

        public int? GetIdPorCnpjTodos(string cnpj)
        {
            var cnpjOnlyNumbers = cnpj.OnlyNumbers();
            var idFilial = _repository.Find(x => x.CNPJ == cnpjOnlyNumbers)?.Select(x => x.IdFilial).FirstOrDefault();

            return idFilial.GetValueOrDefault(0) <= 0 ? (int?)null : idFilial.GetValueOrDefault();
        }

        public string GetCnpj(int idFilial)
        {
            return _repository.Find(f => f.IdFilial == idFilial).Select(f => f.CNPJ).FirstOrDefault().OnlyNumbers();
        }

        public List<string> GetCnpjList(int[] idFilialList)
        {
            return _repository.Find(f => idFilialList.Contains(f.IdFilial)).Select(f => f.CNPJ).ToList();
        }

        public object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var filiais = _repository.GetAll()
                .Include(x => x.Empresa);
                
            if (idEmpresa.HasValue)
                filiais = filiais.Where(o => o.IdEmpresa == idEmpresa.Value);

            filiais = string.IsNullOrWhiteSpace(order?.Campo)
                    ? filiais.OrderBy(x => x.IdFilial)
                    : filiais.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            filiais = filiais.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = filiais.Count(),
                items = filiais.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdFilial,
                    x.NomeFantasia,
                    CNPJ = x.CNPJ.ToCNPJFormato(),
                    x.Sigla,
                    x.RazaoSocial,
                    PontoApoio = x.PontoApoio ? "Sim" : "Não",
                    RazaoSocialEmpresa = x.Empresa?.RazaoSocial,
                    x.Ativo
                })
            };
        }

        public List<FilialContatos> GetContatosByFilial(int idFilial)
        {
            var contatos = _filialContatosRepository.Find(c => c.IdFilial == idFilial);
            return contatos.ToList();
        }

        public byte[] GerarRelatorioGridFilial(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var listaFiliais = new List<RelatorioFiliaisDataType>();
            var filiais = GetDataToGridAndReport(idEmpresa, filters);

            foreach (var filial in filiais)
            {
                listaFiliais.Add(new RelatorioFiliaisDataType
                {
                    NomeFantasia = filial.NomeFantasia,
                    RazaoSocial = filial.RazaoSocial,
                    CNPJ = filial.CNPJ?.ToCNPJFormato(),
                    Email = filial.Email,
                    Endereco = RetornaEnderecoAgendamento(filial),
                    IdFilial = filial.IdFilial,
                    Sigla = filial.Sigla,
                    Telefone = filial.Telefone?.ToTelefoneFormato(),
                    Ativo = filial.Ativo ? "Sim" : "Não"
                });
            }

            return new RelatorioFilial().GetReport(listaFiliais, tipoArquivo, logo);
        }

		public IQueryable<Filial> GetQuery(int? idEmpresa)
        {
            var query = _repository.GetAll();
            
            if (idEmpresa.HasValue)
                query = query.Where(f => f.IdEmpresa == idEmpresa);
            
            return query;
        }

        public IQueryable<Filial> QueryById(int id)
        {
            return _repository.Find(f => f.IdFilial == id);
        }

        private ValidationResult IsValid(Filial filial, EProcesso processo)
        {
            var validationResult = new ValidationResult();

            if (string.IsNullOrWhiteSpace(filial.CNPJ))
                validationResult.Add("CNPJ da filial não informado");
            if (string.IsNullOrWhiteSpace(filial.CEP))
                validationResult.Add("CEP da filial não informado");
            if (string.IsNullOrWhiteSpace(filial.Email))
                validationResult.Add("E-mail da filial não informado");
            if (string.IsNullOrWhiteSpace(filial.Telefone))
                validationResult.Add("Telefone da filial não informado");

            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(AssertionConcern.AssertArgumentIsValidCNPJ(filial.CNPJ, @"CNPJ inválido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidCEP(filial.CEP, @"CEP deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(filial.Email, @"E-mail deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidTelefone(filial.Telefone, @"Telefone deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidLatitude(filial.Latitude, @"Latitude deve possuir um valor válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidLongitude(filial.Longitude, @"Longitude deve possuir um valor válido"));

            validationResult.Add(AssertionConcern.AssertArgumentNotEmpty(filial.Sigla, @"Sigla é obrigatório"));

            #region Validações de Email

            if (!string.IsNullOrWhiteSpace(filial.EmailNome) || !string.IsNullOrWhiteSpace(filial.EmailEndereco) ||
                filial.EmailPorta != null || !string.IsNullOrWhiteSpace(filial.EmailServidor) ||
                !string.IsNullOrWhiteSpace(filial.EmailUsuario) || !string.IsNullOrWhiteSpace(filial.EmailSenha))
            {
                if (string.IsNullOrWhiteSpace(filial.EmailNome))
                    validationResult.Add("Contato do e-mail não informado");

                if (string.IsNullOrWhiteSpace(filial.EmailEndereco))
                    validationResult.Add("Endereço de e-mail não informado");

                validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(filial.EmailEndereco, @"E-mail deve ser válido"));

                if (filial.EmailPorta == null)
                    validationResult.Add("Porta do e-mail não informado");

                if (string.IsNullOrWhiteSpace(filial.EmailServidor))
                    validationResult.Add("Servidor de e-mail não informado");

                if (string.IsNullOrWhiteSpace(filial.EmailUsuario))
                    validationResult.Add("E-mail do usuário não informado");

                validationResult.Add(AssertionConcern.AssertArgumentIsValidEmail(filial.EmailUsuario, @"E-mail do usuário deve ser válido"));

                if (string.IsNullOrWhiteSpace(filial.EmailSenha))
                    validationResult.Add("Senha do usuário não informado");
            }

            #endregion

            #region CNPJ

            var cnpj = filial.CNPJ.OnlyNumbers();
            var numCnpj = _repository.Find(t => t.CNPJ == cnpj && t.Ativo).Count();
            
            if (processo == EProcesso.Create && numCnpj > 0 && string.IsNullOrEmpty(filial.CodigoFilial))
                validationResult.Add("CNPJ informado já cadastrado para outra filial");

            if (processo == EProcesso.Update && numCnpj > 1 && cnpj != GetCnpj(filial.IdFilial) && string.IsNullOrEmpty(filial.CodigoFilial))
                validationResult.Add("CNPJ não pode ser alterado");

            #endregion

            #region Sigla

            if (!validationResult.Errors.Any(e => e.Message.StartsWith("Sigla")))
            {
                switch (processo)
                {
                    case EProcesso.Create:
                        if (_repository.Any(f => f.IdEmpresa == filial.IdEmpresa && f.Sigla == filial.Sigla))
                            validationResult.Add("Sigla informada já cadastrada para outra filial");
                        break;

                    case EProcesso.Update:
                        if (_repository.Any(f => f.IdEmpresa == filial.IdEmpresa && f.IdFilial != filial.IdFilial && f.Sigla == filial.Sigla))
                            validationResult.Add("Sigla informada já cadastrada para outra filial");
                        break;
                }
            }


            if (!string.IsNullOrEmpty(filial.CodigoFilial) && processo == EProcesso.Create)
            {
                if (_repository.Any(f => f.IdEmpresa == filial.IdEmpresa && f.CodigoFilial == filial.CodigoFilial))
                    validationResult.Add("Ja existe uma filial com este código");
            }


            if (filial.PontoApoio)
            {
                var filiais = _repository.GetAll().Where(o => o.IdFilialMae == filial.IdFilial);

                if (filiais.Any())
                    validationResult.Add(
                        "Esta filial não pode ser um ponto de apoio, a mesma já é Filial Mãe de outras filiais.");
                else
                    if (!filial.IdFilialMae.HasValue)
                    validationResult.Add("Filial mãe não informada.");
                else if (filial.IdFilialMae.Value <= 0)
                    validationResult.Add("Filial mãe não informada.");
            }


            #endregion

            var contator = 1;

            foreach (var contato in filial.FilialContatos)
            {
                if (string.IsNullOrEmpty(contato.Nome))
                    validationResult.Add($"O Nome do contato da linha {contator} não foi preenchido.");
                else if (contato.Nome.Length > 100)
                    validationResult.Add($"O Nome do contato da linha {contator} possui mais de 100 caracteres.");

                if (contato.Telefone.OnlyNumbers().Length > 11 || contato.Telefone.OnlyNumbers().Length < 10)
                    validationResult.Add($"O Telefone do contato da linha {contator} está inválido.");

                contator++;
            }

            return validationResult;
        }
        
        private static void FormatValues(Filial filial)
        {
            filial.CNPJ = filial.CNPJ?.OnlyNumbers();
            filial.CEP = filial.CEP?.OnlyNumbers();
            filial.Sigla = filial.Sigla?.ToUpper();
            filial.Telefone = filial.Telefone?.OnlyNumbers();
        }
        
        private string RetornaEnderecoAgendamento(Filial item)
        {
            var endereco = "";

            if (item.Endereco != "")
                endereco = item.Endereco;

            if (item.Numero != null && item.Numero > 0)
            {
                if(endereco == "")
                {
                    endereco += item.Numero ?? 0;
                }
                else
                {
                    endereco += "," + item.Numero;
                }
            }
                
            if (!string.IsNullOrEmpty(item.Bairro))
                endereco += (endereco == "" ? item.Bairro : " - " + item.Bairro);

            var cidade = _cidadeService.Get(item.IdCidade);
            
            if (cidade != null)
            {
                endereco += (endereco == "" ? cidade.Nome : " - " + cidade.Nome);
                endereco += (endereco == "" ? cidade.Estado.Sigla : " - " + cidade.Estado.Sigla);
            }

            return endereco;
        }
        
        private IEnumerable<Filial> GetDataToGridAndReport(int? idEmpresa, List<QueryFilters> filters)
        {
            var usuariosEmpresa = _repository.GetAll();

            if (idEmpresa.HasValue)
                usuariosEmpresa = usuariosEmpresa.Where(x => x.IdEmpresa == idEmpresa.Value);

            usuariosEmpresa = usuariosEmpresa.AplicarFiltrosDinamicos(filters);
            return usuariosEmpresa;
        }
    }
}

using System;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2DadosIniciais
    {
        public ViagemV2DadosIniciais()
        {
            RealizarIntegracoesPreViagem = false;
        }

        /// <summary>
        /// Número de controle para não duplicação da viagem
        /// </summary>
        public string NumeroControle { get; set; }
        
        /// <summary>
        /// Enumeração para declaração de Ciot
        /// Indica que a nossa plataforma deve realizar a gestão do CIOT, veriricar a obrigatoriedade e declarar a operação caso positivo.
        /// O cliente consumidor da plataforma possui a opção de enviar este campo como 0, pois o mesmo pode ter realizado o CIOT por outro fornecedor,
        /// e estar consumindo nosso serviço apenas para o pagamento de cartão.
        /// </summary>
        public EDeclaracaoCiotViagem DeclaracaoCiot { get; set; }
        
        /// <summary>
        /// Enumeração do status da viagem
        /// </summary>
        public EStatusViagem StatusViagem { get; set; }

        /// <summary>
        /// Enumeração do Status de Integração
        /// </summary>
        public EStatusIntegracao StatusIntegracao { get; set; }
        
        /// <summary>
        /// Código da carga
        /// </summary>
        public int? CargaId { get; set; }

        /// <summary>
        /// Descrição do produto da viagem
        /// </summary>
        public string Produto { get; set; }

        /// <summary>
        /// Enumeração da unidade de medida do produto
        /// </summary>
        public EUnidadeMedida UnidadeMedidaProduto { get; set; }

        /// <summary>
        /// Código nacional de natureza de carga (Utilizado para a emissão de ciot)
        /// </summary>
        public int? NaturezaCarga { get; set; }
        
        /// <summary>
        /// Número do cartão para pagamento da viagem
        /// </summary>
        public string NumeroCartao { get; set; }

        /// <summary>
        /// Parâmetro responsável por verificar se a integração deve consumir a rotina de integrações pré viagem
        /// </summary>
        public bool RealizarIntegracoesPreViagem { get; set; }
        
        public int? DistanciaViagem { get; set; }

        public int? CodigoTipoCarga { get; set; }

        /// <summary>
        /// Flag utilizada para não precisar da consulta no cadastro do proprietário para gerar CIOT do tipo TAC-Agregado, além disso atualiza o registro do proprietario com o que for informado
        /// </summary>
        public bool? GerarCiotTacAgregado { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (!string.IsNullOrEmpty(NumeroControle))
                if (NumeroControle.Length > 300)
                    return new ValidationResult().Add("A tag Número de controle não pode ter mais de 300 caracteres.", EFaultType.Error);
            
            if (!Enum.IsDefined(typeof(EDeclaracaoCiotViagem), DeclaracaoCiot))
                return new ValidationResult().Add("A tag de Declaração de Ciot está com um valor incorreto.", EFaultType.Error);
            
            if (StatusIntegracao > 0)
                if (!Enum.IsDefined(typeof(EStatusIntegracao), StatusIntegracao))
                    return new ValidationResult().Add("A tag de Status de Integração está com um valor incorreto.", EFaultType.Error);
            
            if (!Enum.IsDefined(typeof(EUnidadeMedida), UnidadeMedidaProduto))
                return new ValidationResult().Add("A tag de Unidade de Medida do produto está com um valor incorreto.", EFaultType.Error);
            
            if (DistanciaViagem.HasValue)
                if (DistanciaViagem.Value < 0)
                    return new ValidationResult().Add("Distância da viagem não pode ser um valor negativo.", EFaultType.Error);
            
            if (CodigoTipoCarga.HasValue)
                if (CodigoTipoCarga.Value < 0)
                    return new ValidationResult().Add("Código do tipo de carga não pode ser um valor negativo.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
﻿using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class LocalizacaoUsuarioMap : EntityTypeConfiguration<LocalizacaoUsuario>
    {
        public LocalizacaoUsuarioMap()
        {
            ToTable("LOCALIZACAO_USUARIO");
            HasKey(x => x.Id);

            Property(t => t.Latitude).IsRequired();
            Property(t => t.Longitude).IsRequired();
            Property(t => t.IPV4).HasMaxLength(20).IsRequired();
            Property(t => t.DataCadastro).IsOptional();
            Property(t => t.DataAtualizacao).IsOptional();

            Ignore(t => t.UsuarioCadastro);
            Ignore(t => t.UsuarioAtualizacao);

            HasRequired(c => c.<PERSON>ua<PERSON>)
                .WithMany()
                .HasForeignKey(c => c.IdUsuario);
        }
    }
}
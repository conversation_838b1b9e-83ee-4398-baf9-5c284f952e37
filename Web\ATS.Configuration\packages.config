﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net48" />
  <package id="Autofac" version="8.2.0" targetFramework="net48" />
  <package id="Autofac.Mvc5" version="6.1.0" targetFramework="net48" />
  <package id="Dapper" version="1.50.5" targetFramework="net48" />
  <package id="MassTransit" version="8.2.0" targetFramework="net48" />
  <package id="MassTransit.Abstractions" version="8.2.0" targetFramework="net48" />
  <package id="MassTransit.Autofac" version="7.3.1" targetFramework="net48" />
  <package id="MassTransit.RabbitMQ" version="8.2.0-develop.1655" targetFramework="net48" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.6" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.2.6" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.6" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.2" targetFramework="net48" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Diagnostics.HealthChecks" version="6.0.1" targetFramework="net48" />
  <package id="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" version="6.0.1" targetFramework="net48" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.1" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="NLog" version="4.7.2" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.2" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.2" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.2" targetFramework="net48" />
  <package id="System.Threading.Channels" version="7.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="WebGrease" version="1.6.0" targetFramework="net48" />
</packages>
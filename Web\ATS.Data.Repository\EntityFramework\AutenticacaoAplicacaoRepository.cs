﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Data.Context;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.EntityFramework
{
    public class AutenticacaoAplicacaoRepository : Repository<AutenticacaoAplicacao>, IAutenticacaoAplicacaoRepository
    {
        private readonly IUserIdentity _userIdentity;
        
        public AutenticacaoAplicacaoRepository(AtsContext context, IUserIdentity userIdentity) : base(context)
        {
            _userIdentity = userIdentity;
        }
        
        public bool AcessoConcedido(string cnpjAplicacao, string token)
        {
            var autenticacaoAplicacao =  All()
                .FirstOrDefault(x => x.Ativo && x.CNPJAplicacao == cnpjAplicacao && x.Token == token);

            if (autenticacaoAplicacao == null) return false;

            _userIdentity.IdEmpresa = autenticacaoAplicacao.IdEmpresa;
            _userIdentity.Perfil = (int)EPerfil.Empresa;
            _userIdentity.CnpjEmpresa = cnpjAplicacao;
            
            return true;
        }


        public IQueryable<AutenticacaoAplicacao> GetAutenticacaoAplicacaoPorCNPJAplicacao(string cnpjAplicacao, string token)
        {
            return from autenticacao in All()
                        .Include(x => x.Empresa)
                   where autenticacao.Ativo &&
                         autenticacao.Token == token &&
                         autenticacao.CNPJAplicacao == cnpjAplicacao
                   select autenticacao;
        }

        public AutenticacaoAplicacao Get(string cnpjAplicacao)
        {
            return (from autenticacaoAplicacao in All()
                    where autenticacaoAplicacao.CNPJAplicacao == cnpjAplicacao
                    select autenticacaoAplicacao)?.FirstOrDefault();
        }
    }
}

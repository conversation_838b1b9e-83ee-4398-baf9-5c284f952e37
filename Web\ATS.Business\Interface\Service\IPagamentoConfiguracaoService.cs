﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IPagamentoConfiguracaoService : IService<PagamentoConfiguracao>
    {
        ValidationResult Add(PagamentoConfiguracao pagamentoConfiguracao);
        ValidationResult Update(PagamentoConfiguracao pagamentoConfiguracao);
        PagamentoConfiguracao ConsultarPorId(int idPagamentoConfiguracao);
        IEnumerable<PagamentoConfiguracao> GetPorEmpresa(int idEmpresa, int? idFilial);
        object ConsultarGrid(int? idEmpresa, int? idFilial, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult Inativar(int idPagamentoConfiguracao);
        void InativaTodas(int IdEmpresa, int? idFilial, int? idException = new Nullable<int>());
        ValidationResult Reativar(int idPagamentoConfiguracao);
    }
}

﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models.DestinoRotaModelo;
using ATS.Domain.Models.DestinoRotaModelo.ATS.Domain.Models.DestinoRotaModelo;
using ATS.Domain.Validation;


namespace ATS.Domain.Interface.Service
{
    public interface IRotaModeloService : IService<RotaModelo>
    {
        RotaModelo GetWithChilds(int idRota);
        ValidationResult Add(RotaModelo rotaModelo);
        object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        List<DestinoRotaModeloModel> SetarRetornoDestino(RotaModelo rotaModelo);
        List<DetalhesRotaModeloModel> ConsultarDetalhes(RotaModelo rotaModelo);
        RotaModelo GetByIdOrNomeRota(int? idRota, string nomeRota,int? idEmpresa);
        ValidationResult Editar(RotaModelo rotaPadrao);
        ValidationResult AtualizarPontos(RotaModelo rotaPadrao);
        ValidationResult AtualizarPracas(RotaModelo rotaPadrao);
    }
}
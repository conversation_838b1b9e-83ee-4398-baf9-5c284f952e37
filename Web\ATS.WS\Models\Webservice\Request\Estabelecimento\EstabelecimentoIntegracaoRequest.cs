﻿using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Request.Estabelecimento
{
    public class EstabelecimentoIntegracaoRequest
    {
        public int? IdEstabelecimento { get; set; }
        public string CNPJEstabelecimento { get; set; }
        public bool? Credenciado { get; set; }
        public string Descricao { get; set; }
        public string CNPJEmpresa { get; set; }
        public DateTime? DataUltimaAtualizacao { get; set; }
        public int? IdTipoEstabelecimento { get; set; }
        public int? CodigoBACENPais { get; set; }
        public int? CodigoIBGEEstado { get; set; }
        public int? CodigoIBGECidade { get; set; }
        public string Bairro { get; set; }
        public string Logradouro { get; set; }
        public int? Numero { get; set; }
        public string Complemento { get; set; }
        public string CEP { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string Email { get; set; }
        public int AdministradoraPlataforma { get; set; }
        
        public List<EstabelecimentoProdutoRequest> Produtos { get; set; }
    }

    public class EstabelecimentoProdutoRequest
    {
        public int? IdProduto { get; set; }
        public int IdProdutoBase { get; set; }
        public string Descricao { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? PrecoUnitario { get; set; }
        public decimal? PrecoPromocional { get; set; }
        public bool Contrato { get; set; }
    }
}
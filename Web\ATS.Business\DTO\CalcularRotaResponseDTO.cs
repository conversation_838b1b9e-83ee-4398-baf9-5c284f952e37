using System;
using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.DTO
{
    public class CalcularRotaResponseDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public ObjetoCalcularRotaResponseDTO Objeto { get; set; }
    }

    public class ObjetoCalcularRotaResponseDTO
    {
        public decimal? CustoTotal { get; set; }
        
        public decimal? CustoTotalTag { get; set; }
        public Guid? IdentificadorHistorico { get; set; }
        public List<Praca> Pracas { get; set; }
    }
}
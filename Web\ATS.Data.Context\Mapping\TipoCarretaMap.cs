﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TipoCarretaMap : EntityTypeConfiguration<TipoCarreta>
    {
        public TipoCarretaMap()
        {
            ToTable("TIPO_CARRETA");

            HasKey(t => t.IdTipoCarreta);

            Property(t => t.IdTipoCarreta)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Categoria)
                .IsRequired();

            Property(t => t.IdEmpresa)
               .IsOptional();

            Property(t => t.Ativo)
                .IsRequired();

            HasOptional(t => t.Empresa)
                .WithMany(t => t.TiposCarreta)
                .HasForeignKey(d => d.IdEmpresa);

            Property(x => x.DataHoraUltimaAtualizacao)
                .IsRequired();

            Property(x => x.Destacar)
                .IsRequired();
        }
    }
}
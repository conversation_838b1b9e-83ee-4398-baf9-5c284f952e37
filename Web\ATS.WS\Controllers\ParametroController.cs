﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Webservice.Request;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Web.Http.Results;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Models.LocalizacaoUsuarios;
using ATS.Domain.Models;
using ATS.Domain.Models.DespesaUsuario;
using ATS.WS.Attributes;
using ATS.WS.Models.Mobile.Response;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using System.Threading.Tasks;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.WS.Controllers
{
    public class ParametroController : BaseController
    {
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public ParametroController(BaseControllerArgs baseArgs, IParametrosGenericoService parametrosGenericoService, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _parametrosGenericoService = parametrosGenericoService;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }
        
        /*[HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string PublicKey(string token, string cnpjAplicacao)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                var parametro = _parametrosGenericoService.GetParametro<string>(GLOBAL.ChaveRsaPublica, 0);

                if (parametro == null) throw new InvalidOperationException("Chave não encontrada.");

                return new JsonResult().Responde(parametro);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }*/
    }
}
using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class ConsultarAtendimentoResponseDTO
    {
        public List<ListaAtendimentoDTO> ConsultaAtendimento { get; set; }
    }

    public class ListaAtendimentoDTO
    {
    
        public int? IdAtendimentoPortador { get; set; }
        public string Cnpjcpf { get; set; }
        public string Observacao { get; set; }
        public string DataInicio { get; set; }
        public string DataFinal { get; set; }
        public DateTime DataInicioFT { get; set; }
        public DateTime? DataFinalFT { get; set; }
        public int? IdUsuario { get; set; }
        public string UsuarioNome { get; set; }
        public EStatusAtendimentoPortador Status { get; set; }
        public string StatusNome { get; set; }
        public string Protocolo { get; set; }
        public int? IdMotivoFinalizacaoAtendimento { get; set; }
        public string DescricaoMotivoAtendimento { get; set; }
        public double TotalData { get; set; }
    }
}
﻿using System;
using System.Linq;

namespace ATS.Domain.FileUploader
{
    public class Base64File
    {
        public string Content = String.Empty;
        public MimeType MimeType;

        public Base64File(string dataContent)
        {
            if (dataContent == String.Empty || dataContent == null || dataContent.Length == 0)
                throw new Exception("Não é possível integrar um arquivo sem conteúdo");

            Content = dataContent;
            MimeType = GetMimeType();
        }


        private MimeType GetMimeType()
        {
            return new MimeType(Content);
        }
    }

    public class MimeType
    {
        public MongoDB.Enum.EMediaType Tipo { get; set; }
        public string Descricao { get; set; }


        public MimeType(string data)
        {
            var typeArray = data.Split(';');

            if (typeArray != null && typeArray.Count() == 0) throw new Exception("Conteúdo do arquivo sem um mime-type válido!");

            var type = typeArray[0];

            switch (typeArray[0].ToLower())
            {
                case "data:image/png":
                case "image/png":
                    Tipo = MongoDB.Enum.EMediaType.PNG;
                    break;
                case "data:image/jpeg":
                case "image/jpeg":
                    Tipo = MongoDB.Enum.EMediaType.JPEG;
                    break;
                case "data:application/pdf":
                case "application/pdf":
                    Tipo = MongoDB.Enum.EMediaType.PDF;
                    break;
                case "data:application/msword":
                case "application/msword":
                    Tipo = MongoDB.Enum.EMediaType.DOC;
                    break;
                case "data:application/vnd.ms-excel":
                case "application/vnd.ms-excel":
                    Tipo = MongoDB.Enum.EMediaType.XLS;
                    break;
                case "data:application/json":
                case "application/json":
                    Tipo = MongoDB.Enum.EMediaType.JSON;
                    break;
                case "data:text/plain":
                case "text/plain":
                    Tipo = MongoDB.Enum.EMediaType.Text;
                    break;
                default:
                    Tipo = MongoDB.Enum.EMediaType.PNG;
                    break;
            }

            Descricao = Helpers.EnumHelpers.GetDescription(Tipo);
        }
    }
}
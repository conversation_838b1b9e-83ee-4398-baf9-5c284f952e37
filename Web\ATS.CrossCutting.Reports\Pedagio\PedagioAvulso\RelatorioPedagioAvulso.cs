﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Pedagio.PedagioAvulso
{
    public class RelatorioPedagioAvulso
    {
        public byte[] GetReport(string tipo, RelatorioConsultaPedagioAvulsoType dadosRelatorio, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DtoConsultaPedagioAvulso"
                });

                var path = ReportUtils.CreateLogo(logo);

                var parametros = new List<ReportParameter>();
                parametros.Add(new ReportParameter("Logo", "file:///" + path));

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Pedagio.PedagioAvulso.RelatorioPedagioAvulso.rdlc";

                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}

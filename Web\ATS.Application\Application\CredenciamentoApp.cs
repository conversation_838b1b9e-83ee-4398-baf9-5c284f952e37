﻿using ATS.Application.Application.Common;
using ATS.CrossCutting.Reports.Credenciamento;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using System.Web.Configuration;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class CredenciamentoApp : AppBase, ICredenciamentoApp
    {
        private readonly ICredenciamentoService _service;
        private readonly IEmpresaApp _empresaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly ICredenciamentoService _credenciamentoService;

        public CredenciamentoApp(ICredenciamentoService service, IEmpresaApp empresaApp, IUsuarioApp usuarioApp, IEstabelecimentoBaseService estabelecimentoBaseService, ICredenciamentoService credenciamentoService)
        {
            _service = service;
            _empresaApp = empresaApp;
            _usuarioApp = usuarioApp;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _credenciamentoService = credenciamentoService;
        }

        public ValidationResult Add(EstabelecimentoBase estabelecimentoBase, List<int> idsEmpresa, int? idEstabelecimentoBase, int? idEstabelecimento, EStatusCredenciamento status, EStatusDocumentacaoCredenciamento statusDocumentacao, int administradoraPlataforma, bool emailSolicitacaoRecebida = false)
        {
            var validationResult = new ValidationResult();
            var estabelecimentoBaseService = _estabelecimentoBaseService;
            string chaveCadastroUsuario = null;
            var listaIdCredenciamtos = new List<int>();

            foreach (var idEmpresa in idsEmpresa)
            {
                var credenciamento = new Credenciamento(idEmpresa, idEstabelecimentoBase, idEstabelecimento, status, statusDocumentacao);
                var validationResultCredenciamento = Add(credenciamento, administradoraPlataforma);
                listaIdCredenciamtos.Add(credenciamento.IdCredenciamento);

                if (!validationResultCredenciamento.IsValid)
                    validationResult.Add(validationResultCredenciamento);
                else if (emailSolicitacaoRecebida)
                {
                    if (chaveCadastroUsuario == null)
                        chaveCadastroUsuario = credenciamento.ChaveCadastroUsuario;

                    // Neste momento enviaremos um e-mail para este estabelecimento recém cadastrado informando que sua solicitação foi recebida pela empresa;
                    var ret = estabelecimentoBaseService.EmailSolicitacaoRecebida(estabelecimentoBase, idEmpresa, administradoraPlataforma);

                    if (!ret.IsValid)
                        validationResult.Add(ret.ToString(), EFaultType.Alert);
                }
            }

            var validacaoEmailUsuario = EnviarEmailCredenciamentoCadastroUsuario(estabelecimentoBase, idsEmpresa.First(), chaveCadastroUsuario, administradoraPlataforma);
            if (!validacaoEmailUsuario.IsValid)
                validationResult.Add(validacaoEmailUsuario.ToString(), EFaultType.Alert);
            else
            {
                var validacao = _service.AtualizaEmailEnviado(listaIdCredenciamtos);
                if (!validacao.IsValid)
                    validationResult.Add(validacao.ToString(), EFaultType.Error);
            }

            return validationResult;
        }

        public ValidationResult EnviarEmailCredenciamentoCadastroUsuario(EstabelecimentoBase estabelecimentoBase, int idEmpresa, string chave, int administradoraPlataforma)
        {
            var empresa = _empresaApp.GetQuery(idEmpresa).Select(x => new
            {
                x.NomeFantasia,
                x.Logo,
                x.HorasValidadeChaveCadastroUsuario
            }).First();

            return _service.EnviarEmailCredenciamentoCadastroUsuario(estabelecimentoBase.Descricao, estabelecimentoBase.Email, empresa.NomeFantasia,
                empresa.Logo, null, chave, estabelecimentoBase.IdEstabelecimento, DateTime.Now.AddHours(empresa.HorasValidadeChaveCadastroUsuario),
                WebConfigurationManager.AppSettings.Get("LINK_WEB_NOVO"), administradoraPlataforma);
        }

        public List<int> GetIdsEmpresaSolicitadoCredenciamento(int idEstabelecimentoBase)
        {
            return _service.GetIdsEmpresaSolicitadoCredenciamento(idEstabelecimentoBase);
        }

        public ValidationResult Add(Credenciamento credenciamento, int administradoraPlataforma)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _credenciamentoService.Add(credenciamento, administradoraPlataforma);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(Credenciamento credenciamento, int administradoraPlataforma)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _credenciamentoService.Update(credenciamento, administradoraPlataforma);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public object ConsultarImagemPorToken(string Token)
        {
            return _service.ConsultarImagemPorToken(Token);
        }

        public Credenciamento Get(int IdCredenciamento)
        {
            return _service.Get(IdCredenciamento);
        }

        public object ConsultarGridPendentes(int IdEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _service.ConsultarGridPendentes(IdEstabelecimento, take, page, order, filters);
        }

        public object ConsultarGridAprovados(int IdEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _service.ConsultarGridAprovados(IdEstabelecimento, take, page, order, filters);
        }

        public ValidationResult Cancelar(int IdCredenciamento)
        {
            return _service.Cancelar(IdCredenciamento);
        }

        public bool AtualizarStatusDocumentacao(int idCredenciamento_, EStatusDocumentacaoCredenciamento status_, int administradoraPlataforma, string motivo = null)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    bool ret = _credenciamentoService.AtualizarStatusDocumentacao(idCredenciamento_, status_, administradoraPlataforma, motivo);
                    if (!ret)
                        return ret;

                    transaction.Complete();

                    return ret;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public object ConsultarAnexosCredenciamento(int idEstabelecimento)
        {
            return _service.ConsultarAnexosCredenciamento(idEstabelecimento);
        }

        public object ConsultarCredenciamentosPorEmpresa(int idEmpresa, DateTime? dtIni, DateTime? dtFim, int administradoraPlataforma, bool aberto = false, bool aprovado = false, bool rejeitado = false, bool bloqueado = false, bool regular = true, bool irregular = true, bool aguardando = true, int? idEstabelecimento = null)
        {
            return _service.ConsultarCredenciamentosPorEmpresa(idEmpresa, dtIni, dtFim, administradoraPlataforma, aberto, aprovado, rejeitado, bloqueado, regular, irregular, aguardando, idEstabelecimento);
        }

        public DataModel<CredenciamentoModel> ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> Filters)
        {
            return _service.ConsultaGrid(idEmpresa, take, page, orderFilters, Filters);
        }

        public ValidationResult AprovarCredenciamento(int idCredenciamento, string linkWebNovo, int administradoraPlataforma)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validacao = _service.AprovarCredenciamento(idCredenciamento, linkWebNovo, administradoraPlataforma);

                transaction.Complete();
                return validacao;
            }
        }

        public void RejeitarCredenciamento(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _service.RejeitarCredenciamento(idCredenciamento, idMotivo, detalhamento, administradoraPlataforma);

                transaction.Complete();
            }
        }

        public void Descredenciar(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _service.Descredenciar(idCredenciamento, idMotivo, detalhamento, administradoraPlataforma);

                transaction.Complete();
            }
        }

        public void BloquearCredenciamento(int idCredenciamento)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _service.BloquearCredenciamento(idCredenciamento);

                transaction.Complete();
            }

        }

        public object ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            throw new NotImplementedException();
        }

        public void DesbloquearCredenciamento(int idCredenciamento)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                   new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _service.DesbloquearCredenciamento(idCredenciamento);

                transaction.Complete();
            }
        }

        public Credenciamento GetCredenciamentoPorProtocolo(int idProtocolo)
        {
            return _service.GetCredenciamentoPorProtocolo(idProtocolo);
        }

        public Credenciamento Get(int idEmpresa, int idEstabelecimento)
        {
            return _service.Get(idEmpresa, idEstabelecimento);
        }

        public bool VerificarEstabelecimentoBaseAssociacao(int idEstabelecimentoBase)
        {
            return _service.VerificarEstabelecimentoBaseAssociado(idEstabelecimentoBase);
        }

        public void ReenviarEmailCredenciamentoUsuarioCadastro(int id, string linkWebNovo, int administradoraPlataforma)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                  new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _service.ReenviarEmailCredenciamentoUsuarioCadastro(id, linkWebNovo, administradoraPlataforma);

                transaction.Complete();
            }
        }

        public bool HasDocumentacaoVencendo(int idCredenciamento)
        {
            return _service.HasDocumentacaoVencendo(idCredenciamento);
        }

        public bool ChaveValida(string chaveEmail)
        {
            return _service.ChaveValida(chaveEmail);
        }

        public ValidationResult ValidarNovoUsuarioEstabelecimento(string chaveEmail, int idEstabelecimento)
        {
            var valido = new ValidationResult();

            var nomeUsuario = _usuarioApp.BuscaUsuarioMasterEstabelecimento(idEstabelecimento);
            if (!string.IsNullOrWhiteSpace(nomeUsuario))
                return valido.Add($"Já existe um usuário para o credenciamento com esta empresa no nome de {nomeUsuario}, utilize o mesmo para criar novos usuários");

            var chaveExpirada = _service.ChaveValida(chaveEmail);
            if (!chaveExpirada)
                return valido.Add($"A chave {chaveEmail} não é mais válida! Entre em contato com o administrador do sistema para receber uma nova chave!");

            return valido;
        }

        public Credenciamento GetByChaveEmail(string chaveEmail)
        {
            return _service.GetByChaveEmail(chaveEmail);
        }

        public List<CredenciamentoMotivo> GetAllMotivos(int idCredencimento_)
        {
            return _service.GetAllMotivos(idCredencimento_);
        }

        public bool GetAllCredenciamentosIrregulares(List<int> idsEstabelecimentos_)
        {
            return _credenciamentoService.GetAllCredenciamentosIrregulares(idsEstabelecimentos_);
        }

        public object GetDetalhesRejeicaoCredenciamento(int idCredenciamento)
        {
            return _service.GetDetalhesRejeicaoCredenciamento(idCredenciamento);
        }

        public byte[] GerarRelatorioGridCredenciamento(int? idEmpresa,
                                        int Take,
                                        int Page,
                                        OrderFilters Order,
                                        List<QueryFilters> Filters, string extensao, string logo)
        {
            return _service.GerarRelatorioGridCredenciamento(idEmpresa, Take, Page, Order, Filters, extensao, logo);
        }

        public bool EstabelecimentoCredenciado(int idEstabelecimento, int idEmpresa)
        {
            return _service.EstabelecimentoCredenciado(idEstabelecimento, idEmpresa);
        }
    }
}

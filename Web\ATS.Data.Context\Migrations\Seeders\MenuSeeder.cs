﻿using ATS.Domain.Entities;
using System.Collections.Generic;
using System.Data.Entity.Migrations;
using System.Linq;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class MenuSeeder
    {
        public void Execute(AtsContext context)
        {

            var menusPais = new List<Menu>
            {
                new Menu {
                    Sequencia =  1, Descricao = "Administração",
                    Perfis = "", Link = null,
                    IdentificadorPermissao = null, IsMenuPai = true
                },
                new Menu {
                    Sequencia =  2, Descricao = "Cadastros",
                    Perfis = "", Link = null,
                    IdentificadorPermissao = null, IsMenuPai = true
                },
                new Menu {
                    Sequencia =  3, Descricao = "Movimentações",
                    Perfis = "", Link = null,
                    IdentificadorPermissao = null, IsMenuPai = true
                },
                new Menu {
                    Sequencia =  4, Descricao = "Relatórios",
                    Perfis = "", Link = null,
                    IdentificadorPermissao = null, IsMenuPai = true
                },
            };

            menusPais.ForEach(menu => context.Menu.AddOrUpdate(x => new { x.Sequencia, x.Descricao }, menu));
            context.SaveChanges();

            var menusPaisCadastrados = context.Menu;
            var idMenuAdm = menusPaisCadastrados.FirstOrDefault(x => x.Descricao == "Administração")?.IdMenu;
            var idMenuCad = menusPaisCadastrados.FirstOrDefault(x => x.Descricao == "Cadastros")?.IdMenu;
            var idMenuMov = menusPaisCadastrados.FirstOrDefault(x => x.Descricao == "Movimentações")?.IdMenu;

            var menus = new List<Menu>
            {
                /* Configuração */
                new Menu {
                    Sequencia =  1, IdMenuPai = idMenuAdm,
                    Descricao = "País", Perfis = "1", IdentificadorPermissao = 1, LinkNovo = "configuracao.pais",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  2, IdMenuPai = idMenuAdm,
                    Descricao = "Estado", Perfis = "1",
                    Link = "Estado/Index", IdentificadorPermissao = 2, LinkNovo = "configuracao.estado",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  2, IdMenuPai = idMenuCad,
                    Descricao = "Tipo de documento", Perfis = "1",
                    Link = "TipoDocumento/Index", IdentificadorPermissao = 2,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  3, IdMenuPai = idMenuAdm,
                    Descricao = "Cidade",   Perfis = "1",
                    Link = "Cidade/Index", IdentificadorPermissao = 3, LinkNovo = "configuracao.cidade",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  4, IdMenuPai = idMenuAdm,
                    Descricao = "Módulo",   Perfis = "1",
                    Link = "Modulo/Index", IdentificadorPermissao = 4, LinkNovo = "configuracao.modulo",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  5, IdMenuPai = idMenuAdm,
                    Descricao = "Menu",   Perfis = "1",
                    Link = "Menu/Index", IdentificadorPermissao = 5,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  6, IdMenuPai = idMenuAdm,
                    Descricao = "Espécie",   Perfis = "1",
                    Link = "Especie/Index", LinkNovo="configuracao.especie" ,IdentificadorPermissao = 6,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, new List<string> { "Configuração", "Oferta de cargas" })
                },
                new Menu {
                    Sequencia =  7, IdMenuPai = idMenuAdm,
                    Descricao = "Tipo de Veículo",   Perfis = "1",
                    Link = "TipoCavalo/Index", IdentificadorPermissao = 7,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Oferta de cargas")
                },
                new Menu {
                    Sequencia =  8, IdMenuPai = idMenuAdm,
                    Descricao = "Tipo de Carreta",   Perfis = "1",
                    Link = "TipoCarreta/Index", IdentificadorPermissao = 8,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Oferta de cargas")
                },
                new Menu {
                    Sequencia =  14, IdMenuPai = idMenuAdm,
                    Descricao = "Autorização empresa",   Perfis = "1",
                    Link = "AutorizacaoEmpresa/Index", IdentificadorPermissao = 37,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  9, IdMenuPai = idMenuCad,
                    Descricao = "Tipo de Estabelecimento",   Perfis = "1,2",
                    LinkNovo = "tipo-estabelecimento.index",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  18, IdMenuPai = idMenuCad,
                    Descricao = "Cartões",   Perfis = "1,2",
                    LinkNovo = "cartoes.index",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  10, IdMenuPai = idMenuCad,
                    Descricao = "Estabelecimento",   Perfis = "1,2",
                    LinkNovo = "estabelecimento.index",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  18, IdMenuPai = idMenuAdm,
                    Descricao = "Consulta de integrações", Perfis = "1",
                    Link = "Callback/Consultar", IdentificadorPermissao = 1, LinkNovo = "callbacks.consultar",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },

                /* Cadastros */
                new Menu {
                    Sequencia =  1, IdMenuPai = idMenuCad,
                    Descricao = "Usuários", Perfis = "1,2,3,5,6",
                    Link = "Usuario/Index", IdentificadorPermissao = 10,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  2, IdMenuPai = idMenuCad,
                    Descricao = "Empresa", Perfis = "1,2",
                    Link = "Empresa/Index", IdentificadorPermissao = 11,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  3, IdMenuPai = idMenuCad,
                    Descricao = "Filial", Perfis = "1,2",
                    Link = "Filial/Index", IdentificadorPermissao = 12,
                    LinkNovo = "filial.index",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  4, IdMenuPai = idMenuCad,
                    Descricao = "Grupo Usuário",   Perfis = "1,2,6",
                    Link = "GrupoUsuario/Index", IdentificadorPermissao = 13,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  5, IdMenuPai = idMenuCad,
                    Descricao = "Motorista",   Perfis = "1,2,5",
                    Link = "Motorista/Index", IdentificadorPermissao = 14,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, new List<string> { "Configuração", "Oferta de cargas", "Gestão logística" })
                },
                new Menu {
                    Sequencia =  7, IdMenuPai = idMenuCad,
                    Descricao = "Veículo",    Perfis = "1,2,5",
                    Link = "Veiculo/Index", IdentificadorPermissao = 16,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia =  8, IdMenuPai = idMenuCad,
                    Descricao = "Cliente",   Perfis = "1,2",
                    Link = "Cliente/Index", IdentificadorPermissao = 17,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },
                new Menu {
                    Sequencia = 11, IdMenuPai = idMenuCad,
                    Descricao = "Proprietário",   Perfis = "1,2,5",
                    Link = "Proprietario/Index", IdentificadorPermissao = 20,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, new List<string> { "Configuração", "Oferta de cargas", "Gestão logística" })
                },
                new Menu {
                    Sequencia = 16, IdMenuPai = idMenuCad,
                    Descricao = "Ocorrência",   Perfis = "1,2",
                    Link = "Ocorrencia/Index", IdentificadorPermissao = 40,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Gestão de entregas")
                },

                new Menu {
                    Sequencia = 20, IdMenuPai = idMenuCad,
                    Descricao = "Sinergia",   Perfis = "1,2,5,8", Link = "Relacao-Lotes/Index", IdentificadorPermissao = 18,
                    IsMenuMobile = false, IsMenuPai = false,
                    LinkNovo = "relacao-lotes.index",  ModuloMenus = GetModuloMenuListaPorDescricao(context, "Oferta de cargas")
                },
                /* Relatórios */                     
                
                new Menu {
                    Sequencia =  4, IdMenuPai = idMenuCad,
                    Descricao = "Cadastro de produto",   Perfis = "1,2",
                    Link = "Protuto/Index", IdentificadorPermissao = 31,
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Oferta de cargas")
                },

                #region Credenciamento

	            new Menu {
                     Sequencia =  5, IdMenuPai = idMenuCad,
                     Descricao = "Documento", Perfis = "1,2",
                     LinkNovo = "credenciamento.documento", ModuloMenus = GetModuloMenuListaPorDescricao(context, "Credenciamento")
                },
                new Menu {
                     Sequencia =  6, IdMenuPai = idMenuCad,
                     Descricao = "Motivo", Perfis = "1,2",
                     LinkNovo = "credenciamento.motivo", ModuloMenus = GetModuloMenuListaPorDescricao(context, "Credenciamento")
                },

                new Menu {
                     Sequencia =  7, IdMenuPai = idMenuCad,
                     Descricao = "Estabelecimento", Perfis = "6",
                     LinkNovo = "estabelecimento-base.index", ModuloMenus = GetModuloMenuListaPorDescricao(context, "Configuração")
                },

                new Menu {
                     Sequencia =  8, IdMenuPai = idMenuCad,
                     Descricao = "Triagem de credenciamento", Perfis = "1,2",
                     LinkNovo = "credenciamento-solicitacoes.index", ModuloMenus = GetModuloMenuListaPorDescricao(context, "Credenciamento")
                },

                new Menu {
                     Sequencia =  9, IdMenuPai = idMenuCad,
                     Descricao = "Solicitação de credenciamento", Perfis = "1,6",
                     LinkNovo = "credenciamento.credenciamento-empresa", ModuloMenus = GetModuloMenuListaPorDescricao(context, "Credenciamento")
                },
                #endregion

                #region Pagamento de frete
                new Menu {
                    Sequencia = 1, IdMenuPai = idMenuCad,
                    Descricao = "Configuração", Perfis = "1,2",
                    LinkNovo  = "pagamento-frete.configuracao",
                    ModuloMenus = new List<ModuloMenu>
                    {
                        GetModuloMenuListaPorDescricao(context, "Pagamento de frete").FirstOrDefault(),
                        GetModuloMenuListaPorDescricao(context, "Credenciamento").FirstOrDefault()
                    },
                },
                new Menu {
                    Sequencia = 2, IdMenuPai = idMenuCad,
                    Descricao = "Pagamento de frete", Perfis = "6",
                    LinkNovo  = "pagamento-frete.pagamento-frete-crud",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 3, IdMenuPai = idMenuMov,
                    Descricao = "Geração de protocolo", Perfis = "1,6",
                    LinkNovo  = "pagamento-frete.protocolo",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 4, IdMenuPai = idMenuMov,
                    Descricao = "Triagem de protocolo", Perfis = "1,2,6",
                    LinkNovo  = "pagamento-frete.triagem-protocolo",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 4, IdMenuPai = idMenuMov,
                    Descricao = "Recebimento de protocolo", Perfis = "1,2,6",
                    LinkNovo  = "pagamento-frete.recebimento-protocolo",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 5, IdMenuPai = idMenuMov,
                    Descricao = "Triagem de antecipação", Perfis = "1,2,6",
                    LinkNovo  = "pagamento-frete.triagem-antecipacao-protocolo",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 5, IdMenuPai = idMenuMov,
                    Descricao = "Dashboard", Perfis = "1,2",
                    LinkNovo  = "pagamento-frete.dashboard",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 3, IdMenuPai = idMenuCad,
                    Descricao = "Consulta de pagamento", Perfis = "3,5",
                    LinkNovo  = "pagamento-frete.consulta-pagamento",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                new Menu {
                    Sequencia = 3, IdMenuPai = idMenuCad,
                    Descricao = "Lib. pagto. de evento sem chave", Perfis = "3,5",
                    LinkNovo  = "pagamento-frete.liberacao-pagamento-evento-sem-chave",
                    ModuloMenus = GetModuloMenuListaPorDescricao(context, "Pagamento de frete")
                },
                #endregion
            };

            foreach (var menu in menus)
                context.Menu.AddOrUpdate(p => new { p.Descricao, p.LinkNovo }, menu);

            context.SaveChanges();
        }

        private List<ModuloMenu> GetModuloMenuListaPorDescricao(AtsContext context, List<string> descricaoModulo)
        {
            var retorno = new List<ModuloMenu>();
            var id = context.Modulo.Where(x => descricaoModulo.Contains(x.Descricao)).ToList();
            if (!id.Any())
                return null;
            else
                id.ForEach(__ => retorno.Add(new ModuloMenu { IdModulo = __.IdModulo }));

            return retorno;
        }

        private List<ModuloMenu> GetModuloMenuListaPorDescricao(AtsContext context, string descricaoModulo)
        {
            var id = context.Modulo.FirstOrDefault(x => x.Descricao == descricaoModulo)?.IdModulo;
            if (!id.HasValue)
                return null;
            else
                return new List<ModuloMenu> { new ModuloMenu { IdModulo = id.Value } };
        }
    }
}
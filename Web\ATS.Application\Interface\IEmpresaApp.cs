﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Interface
{
    public interface IEmpresaApp : IAppBase<Empresa>
    { 
        ValidationResult Add(Empresa empresa);
        ValidationResult AddEmpresaIndicadores(EmpresaIndicadores empresaIndicadores);
        ValidationResult Update(Empresa empresa);
        ValidationResult UpdateEmpresaIndicadores(EmpresaIndicadores empresaIndicadores);
        Empresa Get(int id, int? idUsuarioLogOn);
        IQueryable<Empresa> GetQuery(int idEmpresa);
        Empresa GetWithChilds(int id);
        int? GetIdPorCnpj(string cnpj);
        string GetCnpj(int id);
        IQueryable<Empresa> Consultar(string razaoSocial, int? idUsuarioLogOn);
        List<ConsultaTodasEmpresasDto> ConsultarTodas(bool? ativo = null);
        IQueryable<Empresa> All();
        Empresa GetAsNoTracking(int id);
        bool GetPermissaoUsuarioJuridicoEmpresa(int idEmpresa);
        string GetTokenMicroServices(int id);
        bool AnyRntrc(int idEmpresa);
        bool AnyById(int id);
        bool EmpresaAgrupaProtocoloMesmoEvento(int id);
        int? GetDiasExpiracaoCompraPedagio(int idEmpresa);
        int? GetIdProdutoCartaoFretePadrao(int idEmpresa);
        string GetLogoPorId(int idEmpresa);
        bool EmpresaValidaPagamentoFrete(int id);
        Empresa Get(int idEmpresa);
        EmpresaIndicadores GetEmpresaIndicadores(int idEmpresa);
        bool ValidaChaveBaixaEvento(int idEmpresa);
        bool ValidaChaveMhBaixaEvento(int idEmpresa);
        string GetLogoPorCnpj(string cnpjEmpresa);
        object GetParametrosPorCnpj(string cnpjEmpresa);
        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, List<int> idsEstabelecimento, int? idEmpresa = null);
        Empresa Get(string cnpj);
        List<Empresa> GetTodas();
        object GetTokenAdministradora();
        ValidationResult AlterarStatus(int idEmpresa);
        object ConsultarGridEmpresa(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        byte[] GerarRelatorioGridEmpresas(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao);
        EmpresaParametrosViagemDto GetParametrosViagem(int idEmpresa);
        EmpresaParametrosVeiculoDto GetParametroVeiculoEmpresa(int? idEmpresa);
        ConsultarParametroResponse GetParametrosPedagioEmpresa(int idEmpresa, string usuarioDocAudit = null, string nomeUsuarioAudit = null);
        object ConsultarGridEmpresasHubPedagio(int take, int page, OrderFilters order, List<QueryFilters> filters);
    }
}
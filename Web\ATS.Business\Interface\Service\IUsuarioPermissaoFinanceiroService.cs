using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioPermissaoFinanceiroService
    {
        ValidationResult Integrar(int idUsuario, EBloqueioFinanceiroTipo idBloqueioFinanceiroTipo, bool empresa);
        UsuarioPermissaoFinanceiro GetParametroPermissaoFinanceiro(int idUsuario, EBloqueioFinanceiroTipo idBloqueioGestorTipo);
        bool PossuiPermissao(int idUsuario, EBloqueioFinanceiroTipo bloqueioFinanceiroTipo);
        IQueryable<UsuarioPermissaoFinanceiro> GetByEmpresa(int idEmpresa);
    }
}
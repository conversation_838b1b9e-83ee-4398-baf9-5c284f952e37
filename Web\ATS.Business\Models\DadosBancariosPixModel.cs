﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.Enum;
using ATS.Domain.Models.Parametro;

namespace ATS.Domain.Models
{
    public class DadosBancariosPixModel
    {
        public ETipoChavePix? TipoChavePix { get; set; }
        public string ChavePix { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodBanco { get; set; }
        public string IspbBanco { get; set; }
        public string NomeBanco { get; set; }
    }
}

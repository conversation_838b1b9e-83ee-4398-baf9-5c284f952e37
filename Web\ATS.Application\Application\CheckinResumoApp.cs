using System;
using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.EntityFramework;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class CheckinResumoApp : AppBase, ICheckinResumoApp
    {
        private readonly ICheckinResumoRepository _checkinResumoRepository;
        private readonly ICheckinResumoService _checkinResumoService;

        public CheckinResumoApp(ICheckinResumoRepository checkinResumoRepository, ICheckinResumoService checkinResumoService)
        {
            _checkinResumoRepository = checkinResumoRepository;
            _checkinResumoService = checkinResumoService;
        }
        
        public void SalvarOuEditarResumo(CheckIn checkin)
        {
            var resumoExistente =
                _checkinResumoRepository.ConsultarResumoExistente(checkin.IdUsuario, checkin.IdEmpresa);

            if (resumoExistente != null)
            {
                resumoExistente.DataHora = checkin.DataHora;
                resumoExistente.IdCheckin = checkin.IdCheckIn;
                resumoExistente.IdViagem = checkin.IdViagem;
                resumoExistente.RegiaoBrasil = checkin.RegiaoBrasil;
                resumoExistente.TipoEvento = checkin.TipoEvento;
                resumoExistente.IdCidadeCheckin = checkin.IdCidadeCheckIn;
                resumoExistente.Latitude = checkin.Latitude;
                resumoExistente.Longitude = checkin.Longitude;
                
                _checkinResumoRepository.Update(resumoExistente);
                return;
            }

            _checkinResumoRepository.Salvar(ArrangeNewResumo(checkin));
        }

        public CheckinResumoConsultaModel ConsultarCheckinResumosPaginado(int? empresaId, int? itensPorPagina, int? pagina, DateTime? dataInicio, DateTime? dataFim)
        {
            _checkinResumoService.AplicarRegrasConsultaResumoPaginado(empresaId, itensPorPagina, pagina, dataInicio, dataFim);
            return _checkinResumoRepository.ConsultarCheckinResumosPaginado(empresaId ?? 0, itensPorPagina ?? 0, pagina ?? 0, dataInicio, dataFim);
        }

        private static CheckinResumo ArrangeNewResumo(CheckIn checkin)
        {
            var newResumo = new CheckinResumo
            {
                IdCheckin = checkin.IdCheckIn, Latitude = checkin.Latitude, Longitude = checkin.Longitude, DataHora = checkin.DataHora, IdEmpresa = checkin.IdEmpresa, IdMotorista = checkin.IdMotorista, IdUsuario = checkin.IdUsuario, IdViagem = checkin.IdViagem, RegiaoBrasil = checkin.RegiaoBrasil, TipoEvento = checkin.TipoEvento, CpfCnpjUsuario = checkin.CpfCnpjUsuario, IdCidadeCheckin = checkin.IdCidadeCheckIn, IdMotoristaBase = checkin.IdMotoristaBase
            };

            return newResumo;
        }
    }
}
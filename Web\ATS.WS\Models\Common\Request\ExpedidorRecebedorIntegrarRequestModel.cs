﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;

namespace ATS.WS.Models.Common.Request
{
    public class ExpedidorRecebedorIntegrarRequestModel : RequestBase
    {
        public int IdCliente { get; set; }
        public int Cep { get; set; }
        public string Endereco { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public int TipoCliente { get; set; }
        public int IBGECidade { get; set; }
        public int TipoClienteContr { get; set; }
    }
}
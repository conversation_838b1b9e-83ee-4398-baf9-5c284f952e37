﻿using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class BloqueioGestorTipoApp : AppBase, IBloqueioGestorTipoApp
    {
        private readonly IBloqueioGestorTipoService _bloqueioGestorTipoService;

        public BloqueioGestorTipoApp(IBloqueioGestorTipoService bloqueioGestorTipoService)
        {
            _bloqueioGestorTipoService = bloqueioGestorTipoService;
        }

        public IQueryable<BloqueioGestorTipo> GetAll()
        {
            return _bloqueioGestorTipoService.GetAll();
        }
    }
}
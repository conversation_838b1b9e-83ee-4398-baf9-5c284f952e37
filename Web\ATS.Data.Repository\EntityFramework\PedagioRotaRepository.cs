using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class PedagioRotaRepository : Repository<PedagioRota>, IPedagioRotaRepository
    {
        public PedagioRotaRepository(AtsContext context) : base(context)
        {
        }
        
        public PedagioRota Add(PedagioRota entity)
        {
            var dataatual = DateTime.Now;
            
            entity.DataCadastro = dataatual;
            entity.DataAtualizacao = dataatual;

            foreach (var pedagioRotaPonto in entity.Pontos)
            {
                pedagioRotaPonto.DataCadastro = dataatual;
                pedagioRotaPonto.DataAtualizacao = dataatual;
            }

            return base.Add(entity, false);
        }

        public PedagioRota Update(PedagioRota entity)
        {
            var dataatual = DateTime.Now;
            
            entity.DataAtualizacao = dataatual;
            
            foreach (var pedagioRotaPonto in entity.Pontos)
            {
                pedagioRotaPonto.DataCadastro = dataatual;
                pedagioRotaPonto.DataAtualizacao = dataatual;
            }

            base.Update(entity, false);

            return entity;
        }

        /*[Obsolete("Não utilizar estet método")]
        public override PedagioRota Add(PedagioRota entity, bool autoSaveChanges = true)
        {
            throw new NotImplementedException();
        }*/

        /*[Obsolete("Não utilizar estet método")]
        public override void Update(PedagioRota entity, bool autoSaveChanges = true)
        {
            throw new NotImplementedException();
        }*/

        /*[Obsolete("Não utilizar estet método")]
        public override PedagioRota Get(int id)
        {
            throw new NotImplementedException("Não utilizar o get diretamente, utilize o método GetQuery");
        }*/

        public IQueryable<PedagioRota> Get(Expression<Func<PedagioRota, bool>> predicate)
        {
            return Where(predicate);
        }

        /*[Obsolete("Não utilizar estet método")]
        public override void Delete(PedagioRota entity, bool autoSaveChanges = true)
        {
            throw new NotImplementedException();
        }*/

        /*[Obsolete("Não utilizar estet método")]
        public override void DeleteRange(List<PedagioRota> entities, bool autoSaveChanges = true)
        {
            throw new NotImplementedException();
        }*/
    }
}
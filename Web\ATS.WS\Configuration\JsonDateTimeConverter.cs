﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Globalization;

namespace ATS.WS.Configuration
{
    public class JsonDateTimeConverter : DateTimeConverterBase
    {
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            return DateTime.ParseExact(reader.Value.ToString(), "yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture);
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, object value, JsonSerializer serializer)
        {
            // Exemplo: 2015-09-16T10:20:30
            writer.WriteValue(((DateTime) value).ToString("yyyy-MM-ddTHH:mm:ss"));
        }
    }
}
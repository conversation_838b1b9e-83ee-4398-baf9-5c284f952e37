﻿using System.Collections.Generic;
using ATS.Data.Repository.External.Extratta.Biz.Models;

namespace ATS.Domain.DTO.Pix
{
    public class CadastrarPlanilhaChavePixProprietarioRequest
    {
        public List<CadastrarPlanilhaChavePixProprietarioRequestItem> Itens { get; set; }
    }
    public class CadastrarPlanilhaChavePixProprietarioRequestItem
    {
        public string Chave { get; set; }
        public ETipoChavePix TipoChave { get; set; }
        public string CPFCNPJProprietario { get; set; }
    }
}
﻿//Created by <PERSON><PERSON><PERSON><PERSON> on 02-04-2018

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Triggers;
using Autofac;

namespace ATS.Domain.Trigger.Base
{
    public delegate TEntity TriggerAction<TEntity>(ILifetimeScope serviceProvider, TEntity @new, TEntity @old);
    
    public class TriggerMethod<TEntity> where TEntity : class
    {
        public TriggerAction<TEntity> Method { get; set; }

        public string Name { get; set; }

        public EOperationTrigger Operation { get; set; }
    }

    public abstract class Trigger<TEntity> : ITrigger<TEntity> where TEntity : class
    {
        // Register all after triggers
        private readonly List<TriggerMethod<TEntity>> RegistredAfterTriggers = new List<TriggerMethod<TEntity>>();

        // Register all before triggers
        private readonly List<TriggerMethod<TEntity>> RegistredBeforeTriggers = new List<TriggerMethod<TEntity>>();

        #region ctor

        public Trigger()
        {
        }

        #endregion

        public bool RegisterAfterTrigger(EOperationTrigger operation, TriggerAction<TEntity> method, string name)
        {
            if (method == null || (!System.Enum.IsDefined(typeof(EOperationTrigger), operation)))
                return false;

            var methodName = name == null ? method.GetType().FullName : name;

            // Check if a trigger was already registred 
            if (RegistredAfterTriggers.Any(x => x.Name == methodName && x.Operation == operation))
                return false;

            RegistredAfterTriggers.Add(new TriggerMethod<TEntity>()
            {
                Method = method,
                Name = methodName,
                Operation = operation
            });

            return true;
        }

        public bool RegisterBeforeTrigger(EOperationTrigger operation, TriggerAction<TEntity> method, string name)
        {
            if (method == null || (!System.Enum.IsDefined(typeof(EOperationTrigger), operation)))
                return false;

            var methodName = name == null ? method.GetType().FullName : name;

            // Check if a trigger was already registred 
            if (RegistredBeforeTriggers.Any(x => x.Name == methodName && x.Operation == operation))
                return false;

            RegistredBeforeTriggers.Add(new TriggerMethod<TEntity>()
            {
                Method = method,
                Name = methodName,
                Operation = operation
            });
            return true;
        }

        public bool ExecuteBeforeTrigger(EOperationTrigger operation, ILifetimeScope serviceProvider, TEntity entity, TEntity old)
        {
            var triggersToRun = RegistredBeforeTriggers?.Where(x => x.Operation == operation);

            if (triggersToRun == null)
                return false;

            try
            {
                foreach (var trigger in triggersToRun)
                {
                    var result = trigger.Method(serviceProvider, entity, old);

                    //TODO Verify methods return to track error 
                }
            }
            catch (Exception)
            {
                // Just supress the return to avoid erros em system stack 
                //TODO Log it into an log service or even in nlog
            }

            return true;
        }

        public bool ExecuteAfterTrigger(EOperationTrigger operation, ILifetimeScope serviceProvider, TEntity entity, TEntity old)
        {
            var triggersToRun = RegistredAfterTriggers?.Where(x => x.Operation == operation);

            if (triggersToRun == null)
                return false;

            try
            {
                foreach (var trigger in triggersToRun)
                {
                    var result = trigger.Method(serviceProvider, entity, old);

                    //TODO Verify methods return to track error 
                }
            }
            catch (Exception)
            {
                // Just supress the return to avoid erros em system stack 
                //TODO Log it into an log service or even in nlog
            }

            return true;
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Entities
{
    public class MensagemGrupoUsuario
    {

        /// <summary>
        /// Id grupo de usuário
        /// </summary>
        public int IdGrupoUsuario { get; set; }

        /// <summary>
        /// Nome do grupo de usuário
        /// </summary>
        public string NomeGrupoUsuario { get; set; }

        /// <summary>
        /// Qual usuário realizou o cadastro deste grupo.
        /// </summary>
        public int IdUsuario { get; set; }
        
        #region Referências
        
        public virtual ICollection<MensagemGrupoDestinatario> MensagemGrupoDestinatario { get; set; }
        public virtual ICollection<GruposUsuarios> GrupoUsuarios { get; set; }

        #endregion
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Common
{
    public class ViagemModel
    {
        /// <summary>
        /// Código da Viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código da Filial
        /// </summary>
        public int? IdFilial { get; set; }

        /// <summary>
        /// Código do proprietario
        /// </summary>
        public int? IdProprietario { get; set; }

        /// <summary>
        /// Placa que esta realizando o transporte
        /// </summary>
        public string Placa { get; set; }

        /// <summary>
        /// Código do Cliente de origem
        /// </summary>
        public int IdClienteOrigem { get; set; }

        /// <summary>
        /// Código do Cliente de destino
        /// </summary>
        public int IdClienteDestino { get; set; }

        /// <summary>
        /// Código do cliente que realiza o pagamento
        /// </summary>
        public int? IdClienteTomador { get; set; }

        /// <summary>
        /// Data de coleta da viagem
        /// </summary>
        public DateTime? DataColeta { get; set; }

        /// <summary>
        /// Endereço de coleta
        /// </summary>
        public string Coleta { get; set; }

        /// <summary>
        /// Endereço de entrega
        /// </summary>
        public string Entrega { get; set; }

        /// <summary>
        /// Data de previsão de entrega
        /// </summary>
        public DateTime DataPrevisaoEntrega { get; set; }

        /// <summary>
        /// Data de finalização da viagem
        /// </summary>
        public DateTime? DataFinalizacao { get; set; }

        /// <summary>
        /// Peso apresentado durante a saida da viagem
        /// </summary>
        public decimal? PesoSaida { get; set; }

        /// <summary>
        /// Peso apresentado durante a chegada da viagem
        /// </summary>
        public decimal? PesoChegada { get; set; }

        /// <summary>
        /// Informa o peso de chegada original, caso tenha sofrido alterações durante o processo de pagamento
        /// </summary>
        public decimal? PesoChegadaOriginal { get; set; }

        /// <summary>
        /// Diferenca entre o peso de saida e chegada da viagem
        /// </summary>
        public decimal? PesoDiferenca { get; set; }

        /// <summary>
        /// Valor de frete da viagem
        /// </summary>
        public decimal? ValorMercadoria { get; set; }

        /// <summary>
        /// Valor de quebra de tarifa da viagem
        /// </summary>
        public decimal? DifFreteMotorista { get; set; }

        /// <summary>
        /// Valor de quebra de mercadoria da viagem
        /// </summary>
        public decimal? ValorQuebraMercadoria { get; set; }

        /// <summary>
        /// Data de lancamento
        /// </summary>
        public DateTime? DataLancamento { get; set; }

        /// <summary>
        /// Nome do motorista que esta realizando a viagem
        /// </summary>
        public string NomeMotorista { get; set; }

        /// <summary>
        /// CPF do motorista que esta realizando a viagem
        /// </summary>
        public string CPFMotorista { get; set; }

        /// <summary>
        /// Nome do proprietaario
        /// </summary>
        public string NomeProprietario { get; set; }

        /// <summary>
        /// CPF do motorista que esta realizando a viagem
        /// </summary>
        public string CPFCNPJProprietario { get; set; }

        /// <summary>
        /// Código de RNTRC do proprietario
        /// </summary>
        public int? RNTRC { get; set; }

        /// <summary>
        /// CNH do motorista que esta realizando a viagem
        /// </summary>
        public string CNHMotorista { get; set; }

        /// <summary>
        /// Status da viagem
        /// </summary>
        public EStatusViagem StatusViagem { get; set; } = EStatusViagem.Aberto;

        /// <summary>
        /// Numero do documento
        /// </summary>
        public String NumeroDocumento { get; set; }

        /// <summary>
        /// Data de emissão
        /// </summary>
        public DateTime? DataEmissao { get; set; }

        /// <summary>
        /// CNPJFiliall
        /// </summary>
        public string CNPJFilial { get; set; }

        /// <summary>
        /// Razão social da filial
        /// </summary>
        public string RazaoSocialFilial { get; set; }

        /// <summary>
        /// Produto
        /// </summary>
        public string Produto { get; set; }

        /// <summary>
        /// Unidade de medida 
        /// </summary>
        public EUnidadeMedida Unidade { get; set; }

        /// <summary>
        /// Unidade de medida 
        /// </summary>
        public string Origem { get; set; }

        /// <summary>
        /// Quantidade
        /// </summary>
        public decimal Quantidade { get; set; }

        /// <summary>
        /// Status da integração do registro entre o ATS e TMS
        /// </summary>
        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Pendente;

        public string DocumentoCliente { get; set; }

        /// <summary>
        /// Número do cartão utilizado para o pagamento do pedágio da viagem
        /// </summary>
        public string NumeroCartao { get; set; }

        public virtual ICollection<ViagemEventoModel> ViagemEventos { get; set; }
    }
}
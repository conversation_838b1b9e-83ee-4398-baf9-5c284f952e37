using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Domain.Enum;

namespace ATS.Domain.Models.ViagemModels
{
    public class ViagemCalcularValorPedagioRequest
    {
        public IList<ViagemPedagioPontoRequest> Pontos { get; set; }
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public List<string> Placas { get; set; }
        public bool ExibirDetalhes { get; set; } = false;
        public int? CodModeloRota { get; set; }
    }

    public class ViagemPedagioPontoRequest
    {
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
    }

    // Utilizada na integração de viagem do front end
    public class ViagemIntegrarRequest
    {
        public int? IdViagem { get; set; }
        public EStatusViagem Status { get; set; }
        public string DocumentoCliente { get; set; }
        public DateTime? DataEmissao { get; set; }
        public DateTime? DataInicioFrete { get; set; }
        public DateTime? DataFimFrete { get; set; }
        public int IdProprietario { get; set; }
        public int IdMotorista { get; set; }
        public int? IdFilial { get; set; }
        public bool HabilitarDeclaracaoCiot { get; set; }
        public bool ForcarDeclaracaoCiot { get; set; }
        public int? NaturezaCarga { get; set; }
        public decimal? IRRPF { get; set; }
        public decimal? INSS { get; set; }
        public decimal? SESTSENAT { get; set; }
        public string CepOrigem { get; set; }
        public string CepDestino { get; set; }
        public int? CodigoTipoCarga { get; set; }
        public int? DistanciaViagem { get; set; }
        public ViagemIntegrarVeiculoRequest Veiculo { get; set; }
        public IList<ViagemIntegrarVeiculoRequest> Carretas { get; set; }
        public ViagemIntegrarPedagioRequest Pedagio { get; set; }
        public IList<ViagemIntegrarDocumentoRequest> Documentos { get; set; }
        public IList<ViagemIntegrarParcelaRequest> Parcelas { get; set; }
        public ViagemIntegrarDadosBancarioSemCartaoRequest DadosBancario { get; set; }
        public ViagemIntegrarDadosPagamentoRequest DadosPagamento { get; set; }
        public ViagemIntegrarDadosAnttRequest DadosAntt { get; set; }
        public DateTime? DataAtualizacao { get; set; }

        public KeyValuePair<bool, string> Validar()
        {
            if (Pedagio != null && Pedagio.Fornecedor != FornecedorEnum.Desabilitado && Pedagio.Fornecedor != FornecedorEnum.Moedeiro)
            {
                if (Pedagio.Localizacoes == null || !Pedagio.Localizacoes.Any())
                {
                    return new KeyValuePair<bool, string>(false, "Necessário informar ao menos 2 pontos para a rota do pedágio.");
                }
            }
            
            if (Pedagio != null && Pedagio.Localizacoes != null && Pedagio.Localizacoes.Any())
            {
                if (Pedagio.Localizacoes.Count < 2)
                {
                    return new KeyValuePair<bool, string>(false, "Necessário informar ao menos 2 pontos para a rota do pedágio.");
                }
            }

            if (!IdViagem.HasValue)
            {
                if (Documentos == null || !Documentos.Any())
                {
                    return new KeyValuePair<bool, string>(false, "É obrigatório informar ao menos 1 documento para a viagem.");
                }
            }

            if (Parcelas == null || !Parcelas.Any())
            {
                return new KeyValuePair<bool, string>(false, "É obrigatório informar ao menos 1 parcela para a viagem.");
            }
            
            return new KeyValuePair<bool, string>(true, "");
        }
    }

    public class ViagemIntegrarVeiculoRequest
    {
        public int IdVeiculo { get; set; }
        public string RNTRC { get; set; }
    }

    public class ViagemIntegrarPedagioRequest
    {
        public Guid? IdentificadorHistorico { get; set; }
        public FornecedorEnum Fornecedor { get; set; }
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public bool Roteirizar { get; set; }
        public decimal? ValorPedagio { get; set; }
        public IList<ViagemIntegrarPedagioLocalizacaoRequest> Localizacoes { get; set; }
        public string NomeRota { get; set; }
        public int? IdRotaModelo { get; set; }
        public int? CodPolyline { get; set; }

        public bool Istag()
        {
            return Fornecedor == FornecedorEnum.Veloe || Fornecedor == FornecedorEnum.MoveMais || Fornecedor == FornecedorEnum.ViaFacil || Fornecedor == FornecedorEnum.ExtrattaTag;
        }
    }
    
    public class ViagemIntegrarPedagioLocalizacaoRequest
    {
        public int IbgeCidade { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
    }

    public class ViagemIntegrarDocumentoRequest
    {
        public int? Id { get; set; }
        public int? IdClienteOrigem { get; set; }
        public int? IdClienteDestino { get; set; }
        public string NumeroDocumento { get; set; }
        public string Serie { get; set; }
        public decimal? PesoSaida { get; set; }
        public decimal? Valor { get; set; }
        public ETipoDocumento TipoDocumento { get; set; }
        public string Chave { get; set; }
    }

    public class ViagemIntegrarParcelaRequest
    {
        public int? IdViagemEvento { get; set; }
        public string Token { get; set; }
        /// <summary>
        /// Usado pelo portal
        /// </summary>
        public EViagemEventoFormaPagamento FormaPagamento { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public DateTime? DataPagamento { get; set; }
        public decimal? Valor { get; set; }
        public EStatusViagemEvento Status { get; set; }
        public string Instrucao { get; set; }
        /// <inheritdoc cref="ViagemEvento.DataAgendamentoPagamento"/>
        public DateTime? DataAgendamentoPagamento { get; set; }
        public IList<ViagemIntegrarParcelaDocumentoRequest> Documentos { get; set; }
        public IList<ViagemIntegrarParcelaAcrescimentoDescontoRequest> AcrescimosDescontos { get; set; }
    }
    
    public class ViagemIntegrarDadosPagamentoRequest
    {
        public EViagemFormaPagamento FormaPagamento { get; set; } = EViagemFormaPagamento.Outros;

        public string CodigoBacen { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }
    }
    
    public class ViagemIntegrarDadosBancarioSemCartaoRequest
    {
        public string ContaCorrente { get; set; }
        public string Agencia { get; set; }
        public EViagemFormaPagamento FormaPagamento { get; set; }
        public int IdBanco { get; set; }
        public ETipoConta TipoConta { get; set; }
        public string DescricaoBanco { get; set; }
    }
    
    public class ViagemIntegrarDadosAnttRequest
    {
        public bool? AltoDesempenho { get; set; }

        public bool? DestinacaoComercial { get; set; }

        public bool? FreteRetorno { get; set; }

        public string CepRetorno { get; set; }

        public int? DistanciaRetorno { get; set; }
    }
    
    public class ViagemIntegrarParcelaDocumentoRequest
    {
        public int? Id { get; set; }
        public string Descricao { get; set; }
        public string Numero { get; set; }
        public ETipoDocumento TipoDocumento { get; set; }
        public bool AnexoObrigatorio { get; set; }
    }

    public class ViagemIntegrarParcelaAcrescimentoDescontoRequest
    {
        public int? Id { get; set; }
        public string Descricao { get; set; }
        public string NumeroDocumento { get; set; }
        public decimal? Valor { get; set; }
        public ETipoValorAdicional Tipo { get; set; }
    }

    public class ViagemBaixarEventoRequest
    {
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public bool PagarCartao { get; set; }
        public EViagemEventoFormaPagamento FormaPagamento { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }
    
    public class ViagemCancelamentoEventoRequest
    {
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }
    
    public class ViagemBloquearEventoRequest
    {
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }
    
    public class ViagemDesbloquearEventoRequest
    {
        public int IdViagem { get; set; }
        public int IdViagemEvento { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }

    public class ViagemCancelarRequest
    {
        public int IdViagem { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }
    
    public class ViagemBaixarRequest
    {
        public int IdViagem { get; set; }
        public DateTime? DataAtualizacao { get; set; }
    }
}
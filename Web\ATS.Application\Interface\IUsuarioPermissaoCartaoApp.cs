﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IUsuarioPermissaoCartaoApp : IAppBase<UsuarioPermissaoCartao>
    {
        ValidationResult Integrar(int idUsuario, EBloqueioCartaoTipo idBloqueioGestorTipo, bool bloqueioFinanceiro);
        UsuarioPermissaoCartao GetParametroPermissaoCartao(int idUsuario, EBloqueioCartaoTipo idBloqueioGestorTipo);
        bool <PERSON>ssui<PERSON>ermis<PERSON>(int idUsuario, EBloqueioCartaoTipo bloqueioFinanceiroTipo);
    }
}
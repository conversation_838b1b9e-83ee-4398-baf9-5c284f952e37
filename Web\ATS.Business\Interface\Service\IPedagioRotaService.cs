using System.Collections.Generic;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models.PedagioRota;

namespace ATS.Domain.Interface.Service
{
    public interface IPedagioRotaService : IBaseService<IPedagioRotaRepository>
    {
        PedagioRotaSalvarResponse Salvar(PedagioRotaSalvarRequest request);
        IList<PedagioRotaGridResponse> ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        PedagioRotaDetalheResponse ConsultarDetalhes(int idRota);
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class EstabelecimentoBaseProdutoService : IEstabelecimentoBaseProdutoService
    {
        private readonly IEstabelecimentoBaseProdutoRepository _estabelecimentoBaseProdutoRepository;

        public EstabelecimentoBaseProdutoService(IEstabelecimentoBaseProdutoRepository estabelecimentoBaseProdutoRepository)
        {
            _estabelecimentoBaseProdutoRepository = estabelecimentoBaseProdutoRepository;
        }

        public List<EstabelecimentoBaseProduto> GetProdutos(List<int> ids)
        {
            return _estabelecimentoBaseProdutoRepository.Find(x => ids.Contains(x.IdProduto)).ToList();
        }

        public int GetUltimoIdProduto()
        {
            return _estabelecimentoBaseProdutoRepository.All().Select(c => c.IdProduto).OrderByDescending(c => c).FirstOrDefault();
        }
    }
}

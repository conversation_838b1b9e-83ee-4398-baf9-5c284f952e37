﻿using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using NLog;
using System.Text;
using System.Web.Configuration;
using ATS.Domain.DTO.Firebase;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Validation;
using Google.Apis.Auth.OAuth2;

namespace ATS.Domain.Service
{
    public class PushService : IPushService
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly HttpClient _httpClient;
        private static readonly string FCM_URI = "https://fcm.googleapis.com/v1/projects/extratta-app/messages:send";
        private static readonly string FCM_FILE = AppDomain.CurrentDomain.BaseDirectory + @"\Content\Private\extratta-app-firebase-adminsdk.json";
        private static readonly IEnumerable<string> SCOPES = new List<string>(new[] { "https://www.googleapis.com/auth/firebase.messaging" });
        private bool _logEnvioPush; 

        public PushService(IUsuarioRepository usuarioRepository)
        {
            _usuarioRepository = usuarioRepository;
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri(FCM_URI)
            };
            var config = WebConfigurationManager.AppSettings["LogEnvioPush"];
            if (!string.IsNullOrWhiteSpace(config))
            {
                if (bool.TryParse(config, out var enable))
                    _logEnvioPush = enable;
            }
        }

        public ValidationResult EnviarPorDocumento(string documento, string titulo, string mensagem, object data = null, ETipoMensagemPush? tipoMensagem = ETipoMensagemPush.MessageGeneric)
        {
            var validationResult = new ValidationResult();
            
            try
            {
                var usuario = _usuarioRepository
                    .Find(x => x.CPFCNPJ == documento)
                    .Select(x => new
                    {
                        x.IdUsuario,
                        x.IdPush
                    })
                    .FirstOrDefault();

                if (string.IsNullOrWhiteSpace(usuario?.IdPush))
                {
                    if(_logEnvioPush) LogManager.GetCurrentClassLogger().Info($"Documento {documento.ToCpfOrCnpj()} não possui idpush");
                    return validationResult;
                }
                
                return Enviar(new List<string> {usuario.IdPush}, titulo, mensagem, data, tipoMensagem);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                validationResult.Add("Erro ao enviar Push: " + e.GetBaseException().Message, EFaultType.Error);
            }

            return validationResult;
        }

        public ValidationResult Enviar(List<string> idsPush, string title, string message, object data = null, ETipoMensagemPush? messageType = ETipoMensagemPush.MessageGeneric)
        {
            var validationResult = new ValidationResult();
            
            try
            {
                var idsPushComErros = string.Empty;
                
                foreach (var idPush in idsPush)
                {
                    if (string.IsNullOrEmpty(idPush)) continue;
                    if (_logEnvioPush) LogManager.GetCurrentClassLogger().Info($"Iniciando envio de push para o id {idPush}");

                    var googleNotification = new GoogleNotification()
                    {
                        message = new GoogleNotificationMessage()
                        {
                            token = idPush,
                            notification = new GoogleNotificationPayload()
                            {
                                body = message,
                                title = title
                            },
                            data = data
                        }
                    };
                    
                    var json = JsonConvert.SerializeObject(googleNotification);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var token = GetAccessToken();
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    var httpResponse = _httpClient.PostAsync(string.Empty, content).Result;
                    var result = httpResponse.Content.ReadAsStringAsync().Result;
                    
                    var response = JsonConvert.DeserializeObject<FirebaseNotificationResponse>(result);
                    if (response.Error != null)
                    {
                        if (string.IsNullOrWhiteSpace(idsPushComErros))
                        {
                            idsPushComErros += $"Não foi possível enviar a notificação para {idPush.Substring(0, 9)}";
                        }
                        else
                        {
                            idsPushComErros += $", {idPush.Substring(0, 9)}";
                        }
                        
                        if (httpResponse.StatusCode == HttpStatusCode.Unauthorized) idsPushComErros += " (iOS?)";

                        continue;
                    }

                    if (_logEnvioPush) LogManager.GetCurrentClassLogger().Info($"Finalizando push para o id {idPush}, resposta: {result}");
                }

                if (!string.IsNullOrWhiteSpace(idsPushComErros))
                {
                    if (_logEnvioPush) LogManager.GetCurrentClassLogger().Info($"Ids push com erro ao enviar push: {idsPushComErros}");
                    throw new Exception(idsPushComErros);
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                validationResult.Add("Erro ao enviar Push: " + e.GetBaseException().Message, EFaultType.Error);
            }
            
            return validationResult;
        }

        private static string GetAccessToken()
        {
            var googleCredential = GoogleCredential
                .FromFile(FCM_FILE)
                .CreateScoped(SCOPES);
            
            var token = googleCredential.UnderlyingCredential.GetAccessTokenForRequestAsync()?.Result;
            
            return token;
        }
    }
}

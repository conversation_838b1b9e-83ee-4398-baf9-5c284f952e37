﻿using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using NLog;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using System;
using System.Web;
using Newtonsoft.Json;
using RestSharp.Extensions;
using Sistema.Framework.Util.Extension;
using SwaggerException = SistemaInfo.MicroServices.Rest.Ciot.ApiClient.SwaggerException;

namespace ATS.Data.Repository.External.SistemaInfo.Ciot
{
    public class CiotAuthRequestParams
    {
        public string CnpjEmpresa { get; set; }
        public string Token { get; set; }
        public string AuditUserDoc { get; set; } = "00000000000";
        public string AuditUserName { get; set; }
    }

    public class CiotExternalRepository
    {
        private const string ServicoIndisponivelResultMessage = "Serviço de CIOT indisponível.";
        private const string PrefixoMensagemExcecao = "[Serviço de CIOT] ";

        private readonly AnttClient _anttClient;
        private readonly AnttClient _anttConsultaTransportador;
        private readonly CiotClient _ciotClient;
        private readonly ConsultasClient _consultasClient;

        protected CiotAuthRequestParams AuthParams { get; }

        public CiotExternalRepository(CiotAuthRequestParams authParams)
        {
            AuthParams = authParams ??
                         throw new ArgumentNullException(nameof(authParams),
                             "Indique os dados de autenticação da empresa requisítando integração com CIOT");

            _anttClient = new AnttClient(HttpContext.Current) {BaseUrl = SistemaInfoConsts.CiotApiUrl};
            
            //TODO: remover esta tag quando a versão da antt de homologação for igual a de produção
            _anttConsultaTransportador = new AnttClient(HttpContext.Current)
            {
                BaseUrl = SistemaInfoConsts.UrlCiotConsultaTransportador != null
                ? SistemaInfoConsts.UrlCiotConsultaTransportador.SetEndChars("/") + "Ciot/Api"
                    : SistemaInfoConsts.CiotApiUrl
            };

            _consultasClient = new ConsultasClient(HttpContext.Current) {BaseUrl = SistemaInfoConsts.CiotApiUrl};
            _ciotClient = new CiotClient(HttpContext.Current) {BaseUrl = SistemaInfoConsts.CiotApiUrl};
        }

        public ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario)
        {
            bool servicoOnline = false;
            try
            {
                var request = new ConsultarSituacaoTransportadorRequest
                {
                    CpfCnpjInteressado = AuthParams.CnpjEmpresa,
                    CpfCnpjTransportador = cpfCnpjProprietario,
                    RntrcTransportador = rntrcProprietario
                };
                var antt = _anttClient.ConsultarSituacaoTransportador(request,
                    AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                servicoOnline = true;

                var retorno = new ConsultarSituacaoTransportadorInternalResponse();
                if (antt?.Sucesso != null && antt.Sucesso.Value)
                {
                    retorno.Retorno = antt.EquiparadoTAC == true
                        ? ERetornoConsultaTAC.Equiparado
                        : ERetornoConsultaTAC.NaoEquiparado;
                }
                else
                {
                    retorno.Retorno = ERetornoConsultaTAC.Erro;
                    retorno.FalhaComunicacaoAntt = antt?.FalhaComunicacaoAntt ?? false;

                    if (antt?.Excecao != null)
                            retorno.Mensagem = antt.Excecao.Mensagem;
                    else
                        retorno.Mensagem = "Erro ao consultar se proprietário é equiparado a TAC na ANTT.";
                }

                return retorno;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"Erro ao consultar proprietário equiparado a TAC. CNPJ: {cpfCnpjProprietario} - RNTRC: {rntrcProprietario}");
                
                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }

                return new ConsultarSituacaoTransportadorInternalResponse
                {
                    Retorno = ERetornoConsultaTAC.Erro,
                    Mensagem = exceptionMessage ?? ServicoIndisponivelResultMessage,
                    FalhaComunicacaoAntt = !servicoOnline
                };
            }
        }

        public RetificarOperacaoTransporteReponse RetificarOperacaoTransporte(RetificarOperacaoTransporteRequest request)
        {
            try
            {
                var result = _anttClient.RetificarOperacaoTransporte(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao declara CIOT: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new RetificarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }

        public AtualizarCiotResponse AtualizarOperacaoTransorte(AtualizarCiotRequest request)
        {
            try
            {
                var result = _ciotClient.Atualizar(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao atualizar CIOT: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new AtualizarCiotResponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = exceptionMessage ?? ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = e.GetBaseException().Message
                };
            }
        }

        public DeclararOperacaoTransporteReponse DeclararOperacaoTransporte(DeclararOperacaoTransporteRequest request)
        {
            try
            {
                var result = _anttClient.DeclararOperacaoTransporte(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao declara CIOT: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response.Trim());
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new DeclararOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }

        public CancelarOperacaoTransporteReponse CancelarOperacaoTransporte(CancelarOperacaoTransporteRequest request)
        {
            try
            {
                var result = _anttClient.CancelarOperacaoTransporte(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao cancelar CIOT: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new CancelarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }
        
        public EncerrarOperacaoTransporteReponse EncerrarOperacaoTransporte(EncerrarOperacaoTransporteRequest request)
        {
            try
            {
                var result = _anttClient.EncerrarOperacaoTransporte(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao encerrar CIOT: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new EncerrarOperacaoTransporteReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }
        
        public ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request)
        {
            try
            {
                var result = _anttConsultaTransportador.ConsultarFrotaTransportador(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao consultar frota: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new ConsultarFrotaTransportadorReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }

        public ConsultarListaCiotsResponse ConsultarListaCiots(ConsultarListaCiotsRequest request)
        {
            try
            {
                var result = _consultasClient.ConsultarCiots(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao encerrar CIOT: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new ConsultarListaCiotsResponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }
        
        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request)
        {
            try
            {
                var result = _anttClient.ConsultarSituacaoTransportador(request, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao consultar frota: " + request.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new ConsultarSituacaoTransportadorReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }

        public ConsultarSituacaoCiotReponse ConsultarSituacaoCiot(ConsultarSituacaoCiotRequest ciotRequest)
        {
            try
            {
                var result = _consultasClient.SituacaoCiot(ciotRequest, AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);
                return result;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao consultar frota: " + ciotRequest.ToJson());

                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new ConsultarSituacaoCiotReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = ServicoIndisponivelResultMessage,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }

        public ConsultarSituacaoTransportadorReponse ConsultarSituacaoProprietarioAntt(string documento, string rntrc)
        {
            try
            {
                var request = new ConsultarSituacaoTransportadorRequest
                {
                    CpfCnpjInteressado = AuthParams.CnpjEmpresa,
                    CpfCnpjTransportador = documento,
                    RntrcTransportador = rntrc
                };

                var retorno = _anttConsultaTransportador.ConsultarSituacaoTransportador(request, AuthParams.Token, AuthParams.AuditUserDoc,
                    AuthParams.AuditUserName);

                return retorno;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, $"Erro ao consultar situação transportador: documento - {documento} - rntrc: {rntrc}");
                
                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new ConsultarSituacaoTransportadorReponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = e.Message,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }

        public ConsultarTiposCargaResponse ConsultarTiposCarga()
        {
            try
            {
                var resposta =
                    _anttClient.ConsultarTiposCarga(AuthParams.Token, AuthParams.AuditUserDoc, AuthParams.AuditUserName);

                return resposta;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, $"Erro ao consultar tipos de carga");
                
                string exceptionMessage = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Contains("ProcessingStateOnServer"))
                {
                    var apiResponse = JsonConvert.DeserializeObject<ApiErrorResponseDto>(swaggerException.Response);
                    if (apiResponse?.Mensagem != null)
                        exceptionMessage = PrefixoMensagemExcecao + apiResponse.Mensagem;
                }
                
                return new ConsultarTiposCargaResponse
                {
                    Sucesso = false,
                    Excecao = new ExcecaoResponse
                    {
                        Codigo = "-1",
                        Mensagem = e.Message,
                        Tipo = ExcecaoResponseTipo.Aplicacao
                    },
                    ExceptionMessage = exceptionMessage
                };
            }
        }
    }
}

﻿using System.Linq;
using System.Net;
using System.Web.Mvc;
using ATS.Domain.Interface.Database;

namespace ATS.WS.Filters
{
    public class BlacklistIpFilter : ActionFilterAttribute, IActionFilter
    {
        void IActionFilter.OnActionExecuting(ActionExecutingContext filterContext)
        {
            var blacklistIpRepository = DependencyResolver.Current.GetService<IBlacklistIpRepository>();
            var ip = GetIpAddress(filterContext);
            var blacklisted = blacklistIpRepository.GetByIpv4(ip).Any();
            if (!blacklisted) return;
            filterContext.Result = new HttpStatusCodeResult(HttpStatusCode.Forbidden);
            filterContext.Result.ExecuteResult(filterContext);
        }
        
        private string GetIpAddress(ActionExecutingContext filterContext)
        {
            var ip = filterContext.HttpContext.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] ?? 
                     filterContext.HttpContext.Request.Headers["X-Forwarded-For"] ?? 
                     filterContext.HttpContext.Request.Headers["X-Real-Ip"];

            if (ip != null)
            {
                var addresses = ip.Split(',');
                if (addresses.Length != 0) return addresses[0];
            }

            return filterContext.HttpContext.Request.ServerVariables["REMOTE_ADDR"];
        }
    }
}

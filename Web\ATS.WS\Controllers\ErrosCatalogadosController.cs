using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Web.Mvc;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.WS.Attributes;
using ATS.WS.Models.ErrosCatalogados;

namespace ATS.WS.Controllers
{
    public class ErrosCatalogadosController : Controller
    {
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public ActionResult Index()
        {
            var listaCatalogos = new List<Catalogo> {GetErrosCargaAvulsa()};

            ViewBag.ColunaTabela1 = "Código";
            ViewBag.ColunaTabela2 = "Descrição";
            ViewBag.Catalogos = listaCatalogos;
            
            return View();
        }

        private static Catalogo GetErrosCargaAvulsa()
        {
            var catalogo = new Catalogo {NomeCatalogo = "CARGA AVULSA", Erros = new List<Erro>()};

            foreach (var value in EnumHelper<EValidationCargaAvulsa>.GetValues(EValidationCargaAvulsa.ErroNaoCatalogado))
                catalogo.Erros.Add(new Erro
                    {Codigo = (int) value, Descricao = EnumHelper<EValidationCargaAvulsa>.GetDisplayValue(value)});

            return catalogo;
        }
    }

    #region
    public static class EnumHelper<T>
    {
        public static IList<T> GetValues(Enum value)
        {
            return value.GetType().GetFields(BindingFlags.Static | BindingFlags.Public)
                .Select(fi => (T) Enum.Parse(value.GetType(), fi.Name, false)).ToList();
        }

        public static T Parse(string value)
        {
            return (T) Enum.Parse(typeof(T), value, true);
        }

        public static IList<string> GetNames(Enum value)
        {
            return value.GetType().GetFields(BindingFlags.Static | BindingFlags.Public).Select(fi => fi.Name).ToList();
        }

        public static IList<string> GetDisplayValues(Enum value)
        {
            return GetNames(value).Select(obj => GetDisplayValue(Parse(obj))).ToList();
        }

        private static string LookupResource(IReflect resourceManagerProvider, string resourceKey)
        {
            foreach (var staticProperty in resourceManagerProvider.GetProperties(
                BindingFlags.Static | BindingFlags.NonPublic | BindingFlags.Public))
            {
                if (staticProperty.PropertyType != typeof(System.Resources.ResourceManager)) continue;
                var resourceManager = (System.Resources.ResourceManager) staticProperty.GetValue(null, null);
                return resourceManager.GetString(resourceKey);
            }

            return resourceKey;
        }

        public static string GetDisplayValue(T value)
        {
            var fieldInfo = value.GetType().GetField(value.ToString());

            var descriptionAttributes = fieldInfo.GetCustomAttributes(
                typeof(DisplayAttribute), false) as DisplayAttribute[];

            if (descriptionAttributes != null && descriptionAttributes[0].ResourceType != null)
                return LookupResource(descriptionAttributes[0].ResourceType, descriptionAttributes[0].Name);

            if (descriptionAttributes == null) return string.Empty;
            return (descriptionAttributes.Length > 0) ? descriptionAttributes[0].Name : value.ToString();
        }
    }
    #endregion
}
﻿using System;
using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Models.ViagemModels;

namespace ATS.WS.Models.Webservice.Request.Pedagio
{
    public class PedagioAvulsoRequest
    {
        public string DocumentoCliente { get; set; }
        public DateTime? DataEmissao { get; set; }
        public DateTime? DataInicioFrete { get; set; }
        public DateTime? DataFimFrete { get; set; }
        public int? IdProprietario { get; set; }
        public int? IdFilial { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdMotorista { get; set; }
        public int? CodigoTipoCarga { get; set; }
        public int? DistanciaViagem { get; set; }
        public ViagemIntegrarVeiculoRequest Veiculo { get; set; }
        public List<ViagemIntegrarVeiculoRequest> Carretas { get; set; }
        public ViagemIntegrarPedagioRequest Pedagio { get; set; }
        public UsuarioAudit UsuarioAudit { get; set; }
    }

    public class UsuarioAudit
    {
        public int IdUsuario { get; set; }
        public string DocumentoUsuarioAudit { get; set; }
        public string NomeUsuarioAudit { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Globalization;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class ExtratoConsolidadoModelResponse
    {
        public List<ExtratoConsolidadoModelResponseItem> Itens { get; set; }
        public int TotalItens { get; set; }
    }
    
    public class ExtratoConsolidadoModelResponseItem
    {
        public Guid? Id { get; set; }
        public DateTime? DataTransacao { get; set; }
        public string DataTransacaoString => DataTransacao?.ToString("dd-MM-yyyy HH:mm:ss");
        public long? BizId { get; set; }
        public int? Conta { get; set; }
        public int? Produto { get; set; }
        public string NumeroCartao { get; set; }
        public int? Identificador { get; set; }
        public decimal? Valor { get; set; }
        public decimal? ValorConvertido { get; set; }
        public string ValorConvertidoString => ValorConvertido?.ToString("C", new CultureInfo("pt-br"));
        public string DebitoCredito { get; set; }
        public string DebitoCreditoDescricao => DebitoCredito switch
        {
            "D" => "Débito",
            "C" => "Crédito",
            _ => "Não Informado"
        };
        public string Estabelecimento { get; set; }
        public long? Plano { get; set; }
        public string DescricaoPlano { get; set; }
        public long Movimento { get; set; }
        public long? MCC { get; set; }
        public string DescricaoMCC { get; set; }
        public string MoedaSimbolo { get; set; }
        public string MoedaNome { get; set; }
        public int? ModoEntrada { get; set; }
        public string CodigoAutorizacao { get; set; }
        public long? NSU { get; set; }
        public int? IdEmpresa { get; set; }
        public string Empresa { get; set; }
        public int? IdPortador { get; set; }
        public string Portador { get; set; }
        public string PortadorDocumento { get; set; }
    }
}
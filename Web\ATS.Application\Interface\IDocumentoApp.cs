﻿using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Application.Interface
{
    public interface IDocumentoApp 
    {
        ValidationResult Add(Documento pagamentoDocumento);
        ValidationResult Update(Documento pagamentoDocumento);
        ValidationResult Inativar(int idPagamentoDocumento);
        ValidationResult Reativar(int idPagamentoDocumento);
        List<int> GetIdsDocumentosEmpresa(int idEmpresa);
        IEnumerable<Documento> GetPorEmpresa(int idEmpresa, int? idFilial);
        Documento Get(int idDocumento);
    }
}

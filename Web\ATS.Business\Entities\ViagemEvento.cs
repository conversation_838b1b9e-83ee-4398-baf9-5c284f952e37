﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ViagemEvento
    {
        /// <summary>
        /// Código do evento da viagem
        /// </summary>
        public int IdViagemEvento { get; set; }

        public string NumeroControle { get; set; }

        /// <summary>
        /// Código da viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Habilitar pagamento de cartão
        /// </summary>
        public bool HabilitarPagamentoCartao { get; set; }

        /// <summary>
        /// Marca um evento pago por Pix
        /// </summary>
        public bool? HabilitarPagamentoPix { get; set; }

        /// <summary>
        /// Salvar a informação de que o usuário modificou a forma de pagameneto do evento de carta frete para cartão.
        /// Este campo também é utilizado em regras da triagem relacionado ao pedágio no momento que é apresentado o compo com o vaor do pagamento em tela. 
        /// </summary>
        /// <returns></returns>
        public bool? ModificouFormaPagamentoCartaFreteParaCartao { get; set; }

        /// <summary>
        /// Somente para posto autorizado a trabalhar offline e baixar eventos em outro horário sem o motorista presente.
        /// Poucos postos da SOTRAN são autorizados a trabalhar neste modelo, pois este estão assumindo riscos e podem permitir fraudes com pagamentos duplicados neste cenário.
        /// </summary>
        public bool? ModificouFormaPagamentoCartaoParaCartaFrete { get; set; }

        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código do protocolo
        /// </summary>
        public int? IdProtocolo { get; set; }

        /// <summary>
        /// Código do estabelecimento base
        /// </summary>
        public int? IdEstabelecimentoBase { get; set; }

        /// <summary>
        /// Tipo de evento da viagem
        /// </summary>
        public ETipoEventoViagem TipoEventoViagem { get; set; }

        /// <summary>
        /// Valor do pagamento relacionado ao evento da viagem
        /// </summary>
        public decimal ValorPagamento { get; set; }

        /// <summary>
        /// Valor total do pagamento relacionado ao evento da viagem
        /// </summary>
        public decimal? ValorTotalPagamento { get; set; }

        /// <summary>
        /// Valor do saldo calculado no momento do pagamento
        /// </summary>
        public decimal? ValorTotalPagamentoCalculado { get; set; }

        /// <summary>
        /// Data e hora de pagamento relacionado ao evento da viagem
        /// </summary>
        public DateTime? DataHoraPagamento { get; set; }

        /// <summary>
        /// Data e hora de pagamento relacionado ao cancelamento do evento da viagem
        /// </summary>
        public DateTime? DataHoraCancelamento { get; set; }

        /// <summary>
        /// Propriedade com nome ambiguo, esta propriedade indica que o posto solicitou o abono.
        /// Para CF: O posto assume o risco e já paga o abono. Caso haver problemas a triagem irá descontar da fatura do posto;
        /// Para MH: O posto solicita o pagamento do abono, mas não paga o valor da quebra neste momento. A triagem irá finalizar o processo na analise do protocolo e decidir se confirma ou recusa a solicitação.
        /// </summary>
        public bool? QuebraMercadoriaAbonada { get; set; } = false;

        /// <summary>
        /// Data de validade do evento da viagem
        /// </summary>
        public DateTime? DataValidade { get; set; }

        /// <summary>
        /// Número do recibo relacionado ao evento da viagem
        /// </summary>
        public string NumeroRecibo { get; set; }

        [SkipTracking] public string Instrucao { get; set; }

        /// <summary>
        /// Status do evento da viagem
        /// </summary>
        public EStatusViagemEvento Status { get; set; }

        public EOrigemIntegracao OrigemPagamento { get; set; }

        /// <summary>
        /// Token da Viagem
        /// </summary>
        public string Token { get; set; }

        public int? IdMotivo { get; set; }
        public virtual Motivo Motivo { get; set; }
        [SkipTracking]
        public string MotivoBloqueio { get; set; }

        public virtual List<TransacaoCartao> TransacaoCartao { get; set; }

        // Impostos
        public decimal IRRPF { get; set; } = 0;
        public decimal INSS { get; set; } = 0;
        public decimal SESTSENAT { get; set; } = 0;
        public decimal ValorBruto { get; set; } = 0;

        public int? IdEstabLibSemChave { get; set; }
        public int? IdUsuarioLibSemChave { get; set; }

        //Rejeição do abono
        public int? IdMotivoRejeicaoAbono { get; set; }
        public string DescricaoRejeicaoAbono { get; set; }
        public DateTime? DataHoraRejeicaoAbono { get; set; }
        public int? IdUsuarioRejeicaoAbono { get; set; }

        public DateTime? DataLibSemChave { get; set; }
        public DateTime? DataValidadeChaveToken { get; set; }
        public bool LiberarPagtoSemChave { get; set; }
        public string ObsLibSemChave { get; set; }
        public string ChaveToken { get; set; }
        public string CartaoDestino { get; set; }
        public string CartaoOrigem { get; set; }
        public string TokenAnexoAbono { get; set; }

        public int? IdViagemEventoOrigem { get; set; }

        public ViagemEvento ViagemEventoOrigem { get; set; }

        public Motivo MotivoRejeicaoAbono { get; set; }
        public Usuario UsuarioRejeicaoAbono { get; set; }

        /// <summary>
        /// Usuário que realizou a baixa em cheque
        /// </summary>
        public int? IdUsuarioBaixaCheque { get; set; }

        /// <summary>
        /// Data da realização da baixa em cheque
        /// </summary>
        public DateTime? DataBaixaCheque { get; set; }

        /// <summary>
        /// Código PK do pagamento evento
        /// </summary>
        public int? IdPagamentoChequeAgrupador { get; set; }

        /// <summary>
        /// Campo de auditoria
        /// </summary>
        public string CpfUsuario
        {
            get { return _cpfUsuario; }
            set { _cpfUsuario = string.IsNullOrEmpty(value) ? null : value.OnlyNumbers(); }
        }

        private string _cpfUsuario { get; set; }

        /// <summary>
        /// Campo de auditoria
        /// </summary>
        public string NomeUsuario { get; set; }

        /// <summary>
        /// Data de emissão da viagem
        /// </summary>
        public DateTime? DataEmissaoViagem { get; set; }

        public int? IdUsuarioBaixaEvento { get; set; }

        /// <summary>
        /// Quando habilitado o parametro <see cref="Empresa.HabilitarAgendamentoPagamentoFrete"/> na empresa,
        /// é permitido que o cliente realize o agendamento para pagamento automátcio da parcela em datas posteriores a atual.
        /// </summary>
        public DateTime? DataAgendamentoPagamento { get; set; }

        public int? IdAbastecimentoticket { get; set; }

        public bool HabilitaPagamentoTicket { get; set; }

        #region Virtual Fields

        public virtual Viagem Viagem { get; set; }
        public virtual Protocolo Protocolo { get; set; }
        public virtual EstabelecimentoBase EstabelecimentoBase { get; set; }
        public virtual Usuario UsuarioLibSemChave { get; set; }

        public virtual Usuario UsuarioBaixaCheque { get; set; }

        public virtual Usuario UsuarioBaixaEvento { get; set; }

        public virtual ICollection<ViagemDocumento> ViagemDocumentos { get; set; }
        public virtual ICollection<ViagemValorAdicional> ViagemValoresAdicionais { get; set; }
        public virtual ICollection<ProtocoloEvento> ProtocoloEventos { get; set; }
        public virtual ICollection<ViagemSolicitacaoAbono> ViagemSolicitacaoAbono { get; set; }
        public virtual ICollection<ViagemEvento> ViagemsEventoOrigem { get; set; }

        public virtual ICollection<ViagemEventoProtocoloAnexo> ViagemEventoProtocoloAnexos { get; set; }
        public virtual ICollection<TransacaoPix> TransacoesPix { get; set; }

        #endregion
    }
}
﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IEstabelecimentoBaseContaBancariaService : IService<EstabelecimentoBaseContaBancaria>
    {
        ValidationResult AddOrUpdateListContasBancarias(List<EstabelecimentoBaseContaBancaria> contasBancarias, int idEstabelecimentoBase);

        void DeleteListContasBancarias(List<int> idsContasBancarias);

        EstabelecimentoBaseContaBancaria GetById(int id);

        List<BancosFebrabanModel> GetBancosFebraban();
    }
}

﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Data.Repository.EntityFramework
{
    public class ModuloRepository : Repository<Modulo>, IModuloRepository
    {
        public ModuloRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado para consultar Módulo.
        /// </summary>
        /// <param name="descricao">Descrição do Módulo</param>
        /// <returns>IQueryable de Módulo</returns>
        public IQueryable<Modulo> Consultar(string descricao)
        {
            IQueryable<Modulo> retConsulta = All();

            if (!string.IsNullOrWhiteSpace(descricao))
                retConsulta = retConsulta.Where(m => m.Descricao.Contains(descricao));

            return retConsulta;
        }


        public IQueryable<Modulo> GetModulosUsuario(int idUsuario)
        {
            var idsModulos = (from moduloMenu in ((AtsContext)Context).ModuloMenu
                              from grupoUsuarioMenu in ((AtsContext)Context).GrupoUsuarioMenu
                              from usuario in ((AtsContext)Context).Usuario
                              from modulosEmpresa in ((AtsContext)Context).EmpresaModulo
                              where usuario.IdUsuario == idUsuario
                                 && usuario.IdGrupoUsuario == grupoUsuarioMenu.IdGrupoUsuario
                                 && moduloMenu.IdMenu == grupoUsuarioMenu.IdMenu
                                 && modulosEmpresa.IdEmpresa == usuario.IdEmpresa
                                 && modulosEmpresa.IdModulo == moduloMenu.IdModulo
                              select moduloMenu.IdModulo).Distinct();

            var modulos = from modulo in All()
                          where idsModulos.Contains(modulo.IdModulo)
                          select modulo;

            return modulos;
        }
    }
}
using System;
using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class CartaoBloqueadoPessoaListResponseDto
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public List<CartaoBloqueadoPessoaResponseDto> Objeto { get; set; }
    }
    
    public class CartaoBloqueadoPessoaResponseDto 
    {
        public int? Identificador { get; set; }
        public DateTime? DataVinculo { get; set; }
        public ProdutoResponseDto Produto { get; set; }
        public int? CartaoMestreId { get; set; }
        public string Status { get; set; } = StatusCartao.Bloqueado.ToString();
    }
}
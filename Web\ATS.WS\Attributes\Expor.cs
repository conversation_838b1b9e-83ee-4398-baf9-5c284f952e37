﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Security;
using ATS.WS.Security.Configuration;
using System;
using System.Linq;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Models;
using System.Collections.Generic;
using ATS.Domain.Helpers;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class Expor : ActionFilterAttribute
    {
        public EApi[] ApisPermitidas { get; set; }

        public Expor()
        {
        }

        public Expor(params EApi[] apis)
        {
            ApisPermitidas = apis;
        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var customAttributes = filterContext.ActionDescriptor.GetCustomAttributes(true);

            if (customAttributes == null || !customAttributes.Any() || customAttributes.All(x => x.GetType() != typeof(Expor)))
            {
                return;
            }

            var exporAttribute = (Expor)customAttributes.FirstOrDefault(x => x.GetType() == typeof(Expor));

            if (exporAttribute != null) { 

                string[] lista = WebConfigurationManager.AppSettings.Get("EXPOR_API").Split(',');
                EApi[] listaApis = new EApi[lista.Length];
                for (int i = 0; i < lista.Length; i++)
                {
                    listaApis[i] = (EApi)Enum.Parse(typeof(EApi), lista[i]);
                }

                bool existe = exporAttribute.ApisPermitidas.Intersect(listaApis).Any();
                if (!existe) {
                    var sessionKey = filterContext.HttpContext.Request.Headers["SessionKey"];
                    var refreshKey = filterContext.HttpContext.Request.Headers["RefreshKey"];
                    var keycloak = new KeycloakHelper();
                    if(sessionKey != null && refreshKey != null) keycloak.LogoutUserSession(sessionKey, refreshKey);
                    filterContext.Result = new HttpUnauthorizedResult("Método não exposto nesta API!");
                }
            }
        }
    }
}
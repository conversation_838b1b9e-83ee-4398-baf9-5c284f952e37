using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class PINMap : EntityTypeConfiguration<PIN>
    {
        public PINMap()
        {
            ToTable("PIN");

            HasKey(t => t.IdPIN);

            Property(t => t.IdPIN)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Celular)
                .IsRequired();

            Property(t => t.DataValidacao)
             .HasColumnType("datetime2");

        }
    }
}
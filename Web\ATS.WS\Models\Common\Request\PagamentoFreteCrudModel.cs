﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models;

namespace ATS.WS.Models.Common.Request
{
    public class PagamentoFreteCrudModel
    {
        /// <summary>
        ///     Este token é a chave do pagamento do frete
        /// </summary>
        public string Token { get; set; }

        #region Leitura apenas

        public string NomeFantasiaEmpresa { get; set; }
        public string NomeMotorista { get; set; }
        public string CNH { get; set; }
        public string PlacaVeiculo { get; set; }
        public decimal PesoSaida { get; set; }
        public decimal ValorMercadoria { get; set; }
        public List<PagamentoFreteEvento> Eventos { get; set; }
        public List<PagamentoFreteAnexoModel> Anexos { get; set; }

        #endregion

        #region Crud - Para inserir e alterar

        public ETipoEventoViagem TipoEvento { get; set; }
        public float PesoChegada { get; set; }

        #endregion
    }

    public class PagamentoFreteValorAdicional
    {
        public int NumeroDocumento { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
    }

    public class PagamentoFreteEvento
    {
        public string Descricao { get; set; }
        public ETipoEventoViagem Valor { get; set; }
    }

    public class CalculoValoresPagamentoFreteRetorno
    {
        public string DataEmissao { get; set; }
        public double DifFreteMotorista { get; set; }
        public double QuebraMercadoria { get; set; }
        public decimal ValorPagamento { get; set; }
        public decimal ValorTotalPagamento { get; set; }

        public bool ValorPagamentoBloqueado { get; set; }
        public bool ValorTotalPagamentoBaixado { get; set; }
    }

    public class CalculoValorTotalPagamentoFreteRetorno
    {
        public double ValorTotalPagamento { get; set; }
        public bool ValorTotalPagamentoBaixado { get; set; }
        public DateTime DataEmissao { get; set; }
    }
}
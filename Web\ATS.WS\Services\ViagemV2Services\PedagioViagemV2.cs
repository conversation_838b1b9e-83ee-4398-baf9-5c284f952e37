using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Request;
using ATS.WS.Services.ViagemServices;
using AutoMapper;
using Sistema.Framework.Util.Helper;

namespace ATS.WS.Services.ViagemV2Services
{
    public class PedagioViagemV2
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IParametrosApp _parametrosApp;
        private readonly PedagioViagem _pedagioViagem;
        private readonly IEmpresaApp _empresaApp;

        public PedagioViagemV2(IVersaoAnttLazyLoadService versaoAntt, IViagemApp viagemApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IParametrosApp parametrosApp, PedagioViagem pedagioViagem, IEmpresaApp empresaApp)
        {
            _versaoAntt = versaoAntt;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _parametrosApp = parametrosApp;
            _pedagioViagem = pedagioViagem;
            _empresaApp = empresaApp;
        }

        public Retorno<object> ComprarPedagio(ViagemV2CompraPedagioRequestModel request)
        {
            var validationResult = request.ValidarEntrada();
            
            if (!validationResult.IsValid)
                return new Retorno<object>(validationResult, null, "Falha ao comprar pedágio.");

            var empresaId = _empresaApp.GetIdPorCnpj(request.CNPJAplicacao);
            var viagem = _viagemApp.ObterViagemPorEmpresa(request.ViagemId ?? 0, empresaId ?? 0);
            
            if (viagem == null)
                return new Retorno<object>(new ValidationResult().Add($"Viagem {request.ViagemId} não localizada na base de dados.", EFaultType.Error), null, "Falha ao comprar pedágio.");

            if (viagem.StatusViagem == EStatusViagem.Cancelada)
                return new Retorno<object>(new ValidationResult().Add("Viagem cancelada, não é permitido realizar alterações em viagens com este status.", EFaultType.Error), null, "Falha ao comprar pedágio.");
            
            if (viagem.ResultadoCompraPedagio.In(EResultadoCompraPedagio.CompraSolicitada, EResultadoCompraPedagio.CompraConfirmada))
            {
                var compraPedagioJaRealizada = new SolicitarCompraPedagioResponseDTO
                {
                    Status = viagem.ResultadoCompraPedagio,
                    Mensagem = viagem.MensagemCompraPedagio,
                    Valor = viagem.ValorPedagio,
                    ProtocoloRequisicao = viagem.IdViagem,
                    ProtocoloProcessamento = viagem.NumeroProtocoloPedagio.ToIntNullable(),
                    ProtocoloValePedagio = viagem.ProtocoloValePedagio,
                    ProtocoloEnvioValePedagio = viagem.ProtocoloEnvioValePedagio,
                    EstornoSaldoResidualSolicitado = viagem.EstornoSaldoResidualPedagioSolicitado.GetValueOrDefault(),
                    Fornecedor = _viagemApp.GetFornecedorViagemRota(viagem.IdViagem)
                };

                return new Retorno<object>(true, string.Empty, compraPedagioJaRealizada);
            }

            var ciot = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa)
                : _ciotV3App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);

            validationResult.Add(_viagemApp.AdicionarAtualizarViagemRota(viagem.IdViagem, viagem.IdEmpresa, request.Localizacoes?.ToList(), 
                request.IdentificadorHistorico, request.Fornecedor, request.TipoVeiculoPedagio, request.QuantidadeEixos, 
                request.DocumentoUsuarioAudit, request.NomeUsuarioAudit));
            
            if (!validationResult.IsValid) 
                return new Retorno<object>(false,  "Falha ao comprar pedágio: " + validationResult, null);

            var pedagioModel = new ViagemIntegrarRequestModel
            {
                CNPJAplicacao = request.CNPJAplicacao, CNPJEmpresa = request.CNPJEmpresa, Pedagio = Mapper.Map<PedagioModel>(request)
            };

            var resultadoCompraPedagio =
                _pedagioViagem.SolicitarCompraPedagio(pedagioModel, viagem,
                    ciot);
            
            if (resultadoCompraPedagio.Status == EResultadoCompraPedagio.Erro || resultadoCompraPedagio.Status == EResultadoCompraPedagio.NaoRealizado)
                return new Retorno<object>(new ValidationResult().Add(resultadoCompraPedagio.Mensagem, EFaultType.Error), null, "Falha ao comprar pedágio.");
                
            resultadoCompraPedagio.Fornecedor = request.Fornecedor;
            
            if (resultadoCompraPedagio.Valor.HasValue) 
                viagem.ValorPedagio =  resultadoCompraPedagio.Valor.ToDecimal();
            viagem.ResultadoCompraPedagio = resultadoCompraPedagio.Status;
            viagem.MensagemCompraPedagio = resultadoCompraPedagio.Mensagem;
            viagem.NumeroProtocoloPedagio = resultadoCompraPedagio.ProtocoloProcessamento;
            viagem.EstornoSaldoResidualPedagioSolicitado = resultadoCompraPedagio.EstornoSaldoResidualSolicitado;
            viagem.ProtocoloValePedagio = resultadoCompraPedagio.ProtocoloValePedagio;
            viagem.ProtocoloEnvioValePedagio = resultadoCompraPedagio.ProtocoloEnvioValePedagio;
            validationResult.Add(_viagemApp.Update(viagem));
            
            if(validationResult.IsValid)
                return new Retorno<object>(true, "Operação realizada com sucesso.", resultadoCompraPedagio);
            else
                return new Retorno<object>(validationResult, null, "Falha ao comprar pedágio.");
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.67285cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.75738cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.16472cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.66743cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.94257cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.02726cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.07007cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.34463cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.82573cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.47136cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.8296cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Token</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox6">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data de lançamento</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox6</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox8">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Ct-e Série</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox8</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox10">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data de liberação</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox10</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox12">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Evento</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox12</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox14">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Valor</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox14</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox16">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Status</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox16</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox18">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Placa</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox18</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox20">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Estabelecimento</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox5</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox49">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Motorista</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox49</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.54179cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Token">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Token.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Token</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataLancamento">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataLancamento.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataLancamento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CteSerie">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CteSerie.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>CteSerie</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataLiberacao">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataLiberacao.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataLiberacao</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Evento">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Evento.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Evento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Valor">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Valor.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Valor</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Status">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Status.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Status</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Placa">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Placa.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Placa</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Estabelecimento2">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Estabelecimento.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Estabelecimento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Motorista">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Motorista.Value</Value>
                              <Style>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Motorista</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember>
              <FixedData>true</FixedData>
            </TablixMember>
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <FixedData>true</FixedData>
              <KeepWithGroup>After</KeepWithGroup>
              <RepeatOnNewPage>true</RepeatOnNewPage>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>pagamentosModel</DataSetName>
        <Top>0.07056cm</Top>
        <Height>1.37139cm</Height>
        <Width>19.94401cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.5677in</Height>
    <Style>
      <Border>
        <Style>None</Style>
      </Border>
    </Style>
  </Body>
  <Width>7.85197in</Width>
  <Page>
    <PageHeader>
      <Height>1.43433cm</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Rectangle Name="Rectangle1">
          <ReportItems>
            <Textbox Name="Textbox3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Pagamentos</Value>
                      <Style>
                        <FontSize>14pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <ListLevel>1</ListLevel>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox3</rd:DefaultName>
              <Height>1.43433cm</Height>
              <Width>19.94401cm</Width>
              <Style>
                <Border>
                  <Style>Solid</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <KeepTogether>true</KeepTogether>
          <Height>1.43433cm</Height>
          <Width>19.94401cm</Width>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </Rectangle>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>0.5cm</LeftMargin>
    <RightMargin>0.5cm</RightMargin>
    <TopMargin>0.5cm</TopMargin>
    <BottomMargin>0.5cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="pagamentosModel">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>7d92a643-305b-4fb7-a96a-84866a7ecc43</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPagamentoFretePagamentos">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>1a322eba-81d8-4d55-9527-5256425bb678</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPagamentoFrete">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>62effb8d-885b-42ff-b66e-ae8e7adfd1c0</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="pagamentosHeaderModel">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPagamentoFrete</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Descricao">
          <DataField>Descricao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.PagamentoFrete</rd:DataSetName>
        <rd:TableName>PagamentoFreteReciboAcrescimosDescontosModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboAcrescimosDescontosModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="pagamentosModel">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPagamentoFretePagamentos</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CteSerie">
          <DataField>CteSerie</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataLancamento">
          <DataField>DataLancamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataLiberacao">
          <DataField>DataLiberacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataPagamento">
          <DataField>DataPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Estabelecimento">
          <DataField>Estabelecimento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Evento">
          <DataField>Evento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Motorista">
          <DataField>Motorista</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Placa">
          <DataField>Placa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Status">
          <DataField>Status</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Token">
          <DataField>Token</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos</rd:DataSetName>
        <rd:TableName>PagamentosModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos.PagamentosModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>792354a2-fb6c-4d22-b8d0-386fc31f1925</rd:ReportID>
</Report>
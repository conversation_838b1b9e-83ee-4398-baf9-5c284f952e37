﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;

namespace ATS.WS.Models.Common.Request
{
    public class IntegrarCTeRequest : RequestBase
    {
        public string Contrato { get; set; }

        public string Placa { get; set; }

        public string CPFMotorista { get; set; }

        public DateTime? DataEmissao { get; set; } = null;

        public string Carga { get; set; }

        public decimal? ValorCarga { get; set; }

        public decimal? ValorSeguro { get; set; }

        public decimal? ValorFrete { get; set; }

        public decimal? ValorPedagio { get; set; }

        public decimal? Quantidade { get; set; }

        public DateTime? DataPrevisaoEntrega { get; set; }

        public EUnidadeMedida Unidade { get; set; }

        public OrigemModel Origem { get; set; }

        public DestinoModel Destino { get; set; }

        public int? IdUsuarioCadastro { get; set; }

        public int? IdUsuarioCancelamento { get; set; }

        public string NumeroCTe { get; set; }

        public string NumeroOriginal { get; set; }

        public string ChaveCTe { get; set; }

        public string ChaveCTeOriginal { get; set; }

        public EStatusCTe StatusCTe { get; set; }

        public ETipoCTe TipoCTe { get; set; }

        public ETipoDocumento TipoDocto { get; set; }
    }

    public class OrigemModel
    {
        public string CNPJ { get; set; }

        public string Nome { get; set; }

        public EnderecoCTEModel Endereco { get; set; }

    }

    public class DestinoModel
    {
        public string CNPJ { get; set; }

        public string Nome { get; set; }

        public EnderecoCTEModel Endereco { get; set; }
    }

    public class EnderecoCTEModel
    {
        public string Logradouro { get; set; }

        public string Numero { get; set; }

        public string IbgeCidade { get; set; }

        public string CEP { get; set; }

        public string UF { get; set; }

        public string BACENPais { get; set; }
    }

}
﻿// <auto-generated />
namespace ATS.Data.Context.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.2.0-61023")]
    public sealed partial class InclusaoCampoIntegrarUsuarioEmpresa : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(InclusaoCampoIntegrarUsuarioEmpresa));
        
        string IMigrationMetadata.Id
        {
            get { return "202206151637359_InclusaoCampoIntegrarUsuarioEmpresa"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}

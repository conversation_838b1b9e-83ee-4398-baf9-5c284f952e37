﻿namespace ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia
{
    public class ReciboTransferenciaDataType
    {
        public string NomeOrigem { get; set; }
        public string CpfCnpjOrigem { get; set; }
        public string CartaoOrigem { get; set; }
        public string NomeDest<PERSON> { get; set; }
        public string CpfCnpj<PERSON>estino { get; set; }
        public string CartaoDestino { get; set; }
        public string DataTransacao { get; set; }
        public string Descricao { get; set; }
        public string Informacoes { get; set; }
        public string ValorFormatado { get; set; }
        public string DataAtual { get; set; }
        
        public string Instituicao { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string Dv { get; set; }
    }
}
﻿using System;
using System.Linq;
using System.Linq.Dynamic;
using System.Web.Configuration;
using System.Collections.Generic;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Entities;
using ATS.Domain.Service.Common;
using ATS.Domain.Interface.Database;
using ATS.CrossCutting.IoC;
using ATS.Domain.Extensions;
using ATS.Domain.Models;
using System.Data.Entity;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class LogSmsService : ServiceBase, ILogSmsService
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly ILogSmsRepository _logSmsRepository;
        private readonly IEmpresaRepository _empresaRepository;

        public LogSmsService(ILogSmsRepository logSmsRepository, IUsuarioRepository usuarioRepository, IEmpresaRepository empresaRepository)
        {
            _logSmsRepository = logSmsRepository;
            _usuarioRepository = usuarioRepository;
            _empresaRepository = empresaRepository;
        }

        public void Add(LogSms logSms, int? idempresa = null)
        {
            try
            {
                if (logSms.IdUsuarioEnvio.HasValue)
                {
                    logSms.IdEmpresa = _usuarioRepository.GetWithRelationships(logSms.IdUsuarioEnvio ?? 0)?.IdEmpresa ?? 0;
                    logSms.Perfil = _usuarioRepository.GetWithRelationships(logSms.IdUsuarioEnvio ?? 0)?.Perfil;
                }
                else
                {
                    if (!idempresa.HasValue)
                        idempresa = _empresaRepository.GetIdPorCnpj(WebConfigurationManager.AppSettings["CNPJ_EMPRESA"]);

                    logSms.IdEmpresa = idempresa.Value;
                }


                logSms.Texto = StringUtil.ReplaceNewLinePerSpace(logSms.Texto);
                _logSmsRepository.Add(logSms);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
}

﻿using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class EstabelecimentoBaseProduto
    {
        public int IdEstabelecimentoBase { get; set; }
        public int IdProduto { get; set; }
        public string Descricao { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? PrecoUnitario { get; set; }
        public decimal? PrecoPromocional { get; set; }

        #region Relacionalmentos

        public virtual EstabelecimentoBase EstabelecimentoBase { get; set; }

        public virtual ICollection<EstabelecimentoProduto> EstabelecimentoProduto { get; set; }
        #endregion
        
    }
}

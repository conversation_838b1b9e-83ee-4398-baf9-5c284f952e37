﻿using Autofac;
using Autofac.Core;
using Autofac.Integration.Mvc;
using NLog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Utils;
using MassTransit;

namespace ATS.CrossCutting.IoC
{
    public static class IoC
    {
        public static Func<Type[]> OnContainerBuilder { get; set; }
        public static Func<ContainerBuilder, bool> GenericRegistration { get; set; }

        private static IContainer _container;
        private static object _containerLock = new object();

        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public static IContainer Container => _container;

        private static readonly string[] _ignoredTypesOnAutomaticRegistrationScan = new string[]
        {
            "ATS.Domain.Service.CartoesService",
            "ATS.Application.Application.CartoesApp",
            "ATS.Domain.Service.PedagioService",
            "ATS.Application.Application.PedagioApp",
            "ATS.Application.Application.RotasCacheApp", 
            "ATS.Domain.Service.RotasCacheService",
            "MassTransit.IMessageDataRepository",
            "ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.TagExtrattaExternalRepository"
        };
        private static readonly string[] _ignoredTypesOnValidatieRegistrations = new string[]
        {
            "ATS.Domain.Interface.Dapper.Common.IQueryDapper",
            "ATS.Domain.Interface.Database.Common.IRepositoryDapper",
            "ATS.Application.Interface.Common.IAppBase",
            "ATS.Application.Interface.Common.IBaseApp",
            "ATS.Domain.Interface.Service.Common.IBaseService",
            "ATS.Domain.Interface.Service.Common.IService",
            "ATS.Domain.Interface.Database.Common.IRepository",
            "MassTransit.IMessageDataRepository",
            "ATS.Data.Repository.External.SistemaInfo.ExtrattaTAG.TagExtrattaExternalRepository"
        };

        private static Assembly[] GetAssemblies()
        {
            // Caso não exista uma sessão HttpContext para carregar a pasta de origem
            // dos assemblies, irá carregar através do AppDomain.
            if (HttpContext.Current?.Server == null)
                return Directory.GetFiles(Path.Combine(AppDomain.CurrentDomain.BaseDirectory), "*.dll")
                    .Where(x => !x.Contains(".Controllers")).Select(Assembly.LoadFrom).ToArray();

            return Directory.GetFiles(HttpContext.Current.Server.MapPath("~/bin"), "*.dll")
                .Where(x => !x.Contains(".Controllers")).Select(Assembly.LoadFrom).ToArray();
        }

        public static void  Initialize()
        {
            if (_container != null)
                return;

            lock (_containerLock)
            {
                if (_container != null)
                    return;

                try
                {
                    //OnePerRequest = false;

                    var builder = new ContainerBuilder();

                    Assembly[] assemblies = GetAssemblies();

                    builder.RegisterAssemblyTypes(assemblies)
                        .Where(x => 
                            x.Name.EndsWith("Repository") && x.Namespace != null && 
                            !x.Namespace.Contains("ClosedXML.Excel.Caching") &&
                            !x.Namespace.Contains("WebApiThrottle")) 
                        .AsImplementedInterfaces()
                        .InstancePerLifetimeScope()
                        .PropertiesAutowired();

                    builder.RegisterAssemblyTypes(assemblies)
                        .Where(x => x.IsVisible && x.Namespace != null && x.Namespace.StartsWith("ATS.Domain.Service") && x.Name.EndsWith("Service"))
                        .Where(x => !_ignoredTypesOnAutomaticRegistrationScan.Contains(x.FullName))
                        .AsImplementedInterfaces()
                        .InstancePerLifetimeScope()
                        .PropertiesAutowired();

                    builder.RegisterAssemblyTypes(assemblies)
                        .Where(x => x.IsVisible && x.Namespace != null && x.Namespace.StartsWith("ATS.Application.Application") && x.Name.EndsWith("App"))
                        .Where(x => !_ignoredTypesOnAutomaticRegistrationScan.Contains(x.FullName))
                        .AsImplementedInterfaces()
                        .InstancePerLifetimeScope()
                        .PropertiesAutowired();

                    builder.RegisterAssemblyTypes(assemblies)
                        .Where(x => x.Name.EndsWith("Dapper"))
                        .AsImplementedInterfaces()
                        .InstancePerLifetimeScope()
                        .PropertiesAutowired();

                    builder.RegisterAssemblyTypes(assemblies)
                        .Where(x => x.Name.EndsWith("Auditoria"))
                        .AsImplementedInterfaces()
                        .InstancePerLifetimeScope()
                        .PropertiesAutowired();

                    builder.RegisterGeneric(typeof(List<>));

                    if (OnContainerBuilder != null)
                    {
                        var lTypes = OnContainerBuilder.Invoke();
                        foreach (var lType in lTypes)
                        {
                            if (lType.GetGenericArguments().Any())
                                builder.RegisterGeneric(lType).InstancePerLifetimeScope();
                            else
                                builder.RegisterType(lType).InstancePerLifetimeScope();
                        }
                    }

                    builder.Register(c => 
                            Bus.Factory.CreateUsingRabbitMq(sbc => {
                            sbc.Host(WebConfigurationManager.AppSettings["UrlRabbtmq"]);
                            sbc.UseMessageRetry(retry =>
                            {
                                var retryMastransit = WebConfigurationManager.AppSettings["MassTransitRetry"].ToIntSafe() ?? 5;
                                var massTransitSecondsRetry = WebConfigurationManager.AppSettings["MassTransitSecondsRetry"].ToIntSafe() ?? 10;

                                retry.Interval(retryMastransit, TimeSpan.FromSeconds(massTransitSecondsRetry));
                            });
                            
                            sbc.UseDelayedMessageScheduler();
                    }))
                        .As<IBusControl>()
                        .As<IBus>()
                        .As<IPublishEndpoint>()
                        .As<ISendEndpointProvider>()
                        .SingleInstance();

                    GenericRegistration?.Invoke(builder);

                    _container = builder.Build();
                }
                catch (Exception e)
                {
                    _logger.Error(e, "Erro ao inicializar AutoFac");
                    throw;
                }
            }
        }

        public static void ValidateRegistrations()
        {
            var watch = Stopwatch.StartNew();
            _logger.Info("Iniciando validação de dependências registradas");
            var index = 0;
            var total = _container.ComponentRegistry.Registrations.Count();
            try
            {
                using (var scope = _container.BeginLifetimeScope())
                {
                    foreach (var componentRegistration in _container.ComponentRegistry.Registrations)
                    {
                        foreach (var registrationService in componentRegistration.Services)
                        {
                            var registeredTargetType = registrationService.Description;
                            if (_ignoredTypesOnValidatieRegistrations.Any(ignoreType => registeredTargetType.StartsWith(ignoreType)))
                                continue;

                            var type = GetType(registeredTargetType);
                            if (type == null)
                                throw new DependencyResolutionException($"Failed to parse type '{registeredTargetType}' ({index}/{total})");
                            var instance = _container.Resolve(type);
                            if (instance == null)
                                throw new DependencyResolutionException($"Null resolved instance for type '{registeredTargetType}' ({index}/{total})");

                            if (!componentRegistration.Activator.LimitType.IsAssignableFrom(instance.GetType()))
                                throw new DependencyResolutionException($"Invalid resolved instance for type '{registeredTargetType}' ({index}/{total})");
                        }
                        index++;
                    }
                }

            }
            catch (Exception e)
            {
                _logger.Fatal(e, $"Erro ao validar dependências ({index}/{total})");
                throw new DependencyResolutionException($"Erro ao validar dependências ({index}/{total})", e);
            }
            finally
            {
                watch.Stop();
                _logger.Info($"Validação das dependências executada em {watch.Elapsed}. Total: {total}");
            }
        }

        private static Type GetType(string typeName)
        {
            var type = Type.GetType(typeName);
            if (type != null)
            {
                return type;
            }
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                type = assembly.GetType(typeName);
                if (type != null)
                {
                    return type;
                }
            }
            return null;
        }
    }
}
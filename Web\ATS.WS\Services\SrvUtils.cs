﻿using ATS.Application.Application;
using ATS.Application.Interface;

namespace ATS.WS.Services
{
    public class SrvUtils
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEstadoApp _estadoApp;
        private readonly IPaisApp _paisApp;

        public SrvUtils(IUsuarioApp usuarioApp, ICidadeApp cidadeApp, IEmpresaApp empresaApp, IEstadoApp estadoApp, IPaisApp paisApp)
        {
            _usuarioApp = usuarioApp;
            _cidadeApp = cidadeApp;
            _empresaApp = empresaApp;
            _estadoApp = estadoApp;
            _paisApp = paisApp;
        }

        /// <summary>
        /// Retorna o código da cidade de acordo com o código do IBGE
        /// </summary>
        /// <param name="ibge">Código do IBGE</param>
        /// <returns></returns>
        public int? GetIdCidadePorIBGE(int ibge)
        {
            return _cidadeApp.GetPorIBGE(ibge)?.IdCidade;
        }

        /// <summary>
        /// Retorna o código do estado de acordo com o código do IBGE
        /// </summary>
        /// <param name="ibge">Código do IBGE</param>
        /// <returns></returns>
        public int? GetIdEstadoPorIBGE(int ibge)
        {
            return _estadoApp.GetPorIBGE(ibge)?.IdEstado;
        }

        /// <summary>
        /// Retorna o código do país de acordo com o código do IBGE
        /// </summary>
        /// <param name="ibge">Código do IBGE</param>
        /// <returns></returns>
        public int? GetIdPaisPorBACEN(int ibge)
        {
            return _paisApp.GetPaisPorBACEN(ibge)?.IdPais;
        }

        /// <summary>
        /// Retorna o código do usuário a partir do CPF
        /// </summary>
        /// <param name="cpf">CPF do usuário</param>
        /// <returns></returns>
        public int? GetIdUsuarioPorCPF(string cpf)
        {
            return _usuarioApp.GetIdPorCNPJCPF(cpf);
        }

        public int? GetIdEmpresaPorCnpj(string cnpj)
        {
            return _empresaApp.GetIdPorCnpj(cnpj);
        }
    }
}
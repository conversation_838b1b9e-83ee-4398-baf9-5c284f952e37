﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using AutoMapper;
using TagExtrattaClient;

namespace ATS.Application.Application
{
    public class VeiculoApp : AppBase, IVeiculoApp
    {
        private readonly IVeiculoService _veiculoService;
        private readonly IEmpresaApp _empresaApp;
        private readonly IUserIdentity _userIdentity;
        private readonly ITagExtrattaApp _tagExtrattaApp;
        public VeiculoApp(IVeiculoService veiculoService, IEmpresaApp empresaApp, IUserIdentity userIdentity, ITagExtrattaApp tagExtrattaApp)
        {
            _veiculoService = veiculoService;
            _empresaApp = empresaApp;
            _userIdentity = userIdentity;
            _tagExtrattaApp = tagExtrattaApp;
        }

        public Veiculo GetWithAllChilds(int idVeiculo)
        {
            return _veiculoService.GetWithAllChilds(idVeiculo);
        }

        public ValidationResult Add(Veiculo veiculo)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _veiculoService.Add(veiculo);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Update(Veiculo veiculo)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _veiculoService.Update(veiculo);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public Veiculo GetVeiculoPorPlaca(string placa, int? idEmpresa = null)
        {
            return _veiculoService.GetVeiculoPorPlaca(placa, idEmpresa);
        }

        public IQueryable<Veiculo> Query(string placa, int? idEmpresa = null, bool? ativo = true, bool somenteTerceiros = false)
        {
            return _veiculoService.Query(placa, idEmpresa, ativo, somenteTerceiros);
        }
        
        public IQueryable<Veiculo> GetVeiculosByListIdVeiculos(List<int> lIdsVeiculo, int? idEmpresa = null)
        {
            return _veiculoService.GetVeiculosByListIdVeiculos(lIdsVeiculo,idEmpresa);
        }

        public bool VeiculoValidoIntegracao(string placa, int? idEmpresa = null)
        {
            return _veiculoService.VeiculoValidoIntegracao(placa, idEmpresa);
        }

        public IEnumerable<Veiculo> GetTodosVeiculosPorPlaca(string placa, int idEmpresa)
        {
            return _veiculoService.GetTodosVeiculosPorPlaca(placa, idEmpresa).ToList();
        }

        public Veiculo GetVeiculoPorEmpresaMotorista(int idEmpresa, int idMotorista)
        {
            return _veiculoService.GetVeiculoPorEmpresaMotorista(idEmpresa, idMotorista);
        }

        public IQueryable<Veiculo> GetVeiculosPorNumerosFrotas(List<long> numeroFrota)
        {
            IQueryable<Veiculo> veiculosPorNumerosFrotas;
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                veiculosPorNumerosFrotas = _veiculoService.GetVeiculosPorNumerosFrotas(numeroFrota);
                transaction.Complete();
            }

            return veiculosPorNumerosFrotas;
        }

        public IQueryable<Veiculo> GetVeiculosPorPlaca(List<string> placas)
        {
            return _veiculoService.GetVeiculosPorPlaca(placas);
        }

        public List<string> GetPlacasPorEmpresa(int idEmpresa, int? idOperacao)
        {
            return _veiculoService.GetPlacasPorEmpresa(idEmpresa, false, idOperacao);
        }

        public object ConsultarGrid(int? idEmpresa, bool comTracao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _veiculoService.ConsultarGrid(idEmpresa, comTracao, take, page, orderFilters, filters);
        }

        public ConsultaDadosVeiculoResponseDTO ConsultaDadosVeiculo(int idVeiculo)
        {
            return _veiculoService.ConsultaDadosVeiculo(idVeiculo);
        }

        public IQueryable<Veiculo> QueryById(int idVeiculo)
        {
            return _veiculoService.QueryById(idVeiculo);
        }

        public ConsultaPlacaDTO PlacaExistente(string placa, int? idEmpresa)
        {
            return _veiculoService.PlacaExistente(placa, idEmpresa);
        }

        public Veiculo GetVeiculoTerceiro(string placa)
        {
            return _veiculoService.GetVeiculoTerceiro(placa);
        }

        public ValidationResult AlterarStatus(int idVeiculo)
        {
            try
            {
                return _veiculoService.AlterarStatus(idVeiculo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public VeiculoGridResponse ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var utilizaExtrattaTag = true;
            
            if (_userIdentity.Perfil != (int) EPerfil.Administrador)
                utilizaExtrattaTag = _empresaApp.GetParametroVeiculoEmpresa(idEmpresa).UtilizaExtrattaTag;
            
            var veiculos = _veiculoService.GetDataToGridAndReport(idEmpresa, orderFilters, filters);
            
            var result = new VeiculoGridResponse
            {
                Parametros = new ParametrosGridVeiculo()
                {
                    UtilizaExtrattaTag  = utilizaExtrattaTag
                },
                totalItems = veiculos.Count(),
                items = veiculos
                        .Skip((page - 1) * take)
                        .Take(take)
                        .ToList()
                        .Select(
                            o =>
                                new VeiculoItemGridResponse
                                {
                                    IdVeiculo = o.IdVeiculo,
                                    Placa = o.Placa.ToPlacaFormato(),
                                    Marca = o.Marca,
                                    Modelo = o.Modelo,
                                    ComTracao = o.ComTracao,
                                    QtdEixos = o.QuantidadeEixos,
                                    RazaoSocialFilial = o.Filial?.RazaoSocial,
                                    RazaoSocialEmpresa = o.Empresa?.RazaoSocial,
                                    Ativo = o.Ativo,
                                    NumeroFrota = o.NumeroFrota,
                                    CpfCnpjProprietario = o.Proprietario?.CNPJCPF?.FormatarCpfCnpj(),
                                    RazaoSocial = o.Proprietario?.RazaoSocial,
                                    RNTRC = o.Proprietario?.RNTRC,
                                    CiotAgregado = o.HabilitarContratoCiotAgregado == null
                                        ? "Conforme proprietário"
                                        : o.HabilitarContratoCiotAgregado.Value
                                            ? "Sempre agregar este veículo ao embarcador"
                                            : "Nunca agregar este veículo ao embarcador",
                                    TipoVeiculo = o.IdTipoCarreta.HasValue ? "Carreta" : o.IdTipoCavalo.HasValue ? "Cavalo" : string.Empty,
                                    SerialNumber = string.IsNullOrEmpty(o.Tag?.SerialNumber) ? null : o.Tag?.SerialNumber,
                                    Status = o.Tag?.Status
                                })
                        .ToList()
            };

            return result;
        }

        public byte[] GerarRelatorioGridVeiculos(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters,
            string logo, string extensao)
        {
            return _veiculoService.GerarRelatorioGridVeiculos(idEmpresa, orderFilters, filters, logo, extensao);
        }

        public object ConsultarGridVeiculoEmpresa(int? idEmpresa, int? idFilial, int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, bool marcarTodos, int apertou)
        {
            return _veiculoService.ConsultarGridVeiculoEmpresa(idEmpresa, idFilial, take, page, orderFilters, filters, marcarTodos, apertou);
        }

        public bool PertenceAEmpresa(int idempresa, int idVeiculo)
        {
            return _veiculoService.QueryById(idVeiculo).Any(c => c.IdEmpresa == idempresa);
        }
    }
}
﻿using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem;
using ATS.CrossCutting.Reports.DespesasViagem.RelatorioListaDespesasViagem;
using AutoMapper;

namespace ATS.Domain.Service
{
    public class DespesasViagemService : ServiceBase, IDespesasViagemService
    {
        private readonly RelatorioDespesasViagem _relatorioDespesasViagem;
        private readonly RelatorioExtratoDespesasViagem _relatorioExtratoDespesasViagem;
        private readonly RelatorioExtratoDetalhadoDespesasViagem _relatorioExtratoDetalhadoDespesasViagem;

        public DespesasViagemService()
        {
            _relatorioDespesasViagem = new RelatorioDespesasViagem();
            _relatorioExtratoDespesasViagem = new RelatorioExtratoDespesasViagem();
            _relatorioExtratoDetalhadoDespesasViagem = new RelatorioExtratoDetalhadoDespesasViagem();
        }
        
        public byte[] GerarRelatorioGridDespesaViagem(string logo, string extensao,List<RelatorioDespesasViagemDataType> relatorio)
        {
            return _relatorioDespesasViagem.GetReport(relatorio, extensao, logo);
        }
        
        public byte[] GerarRelatorioExtratoGridDespesaViagem(string logo, string extensao,List<RelatorioExtratoDespesasViagemDataType> relatorio)
        {
            return _relatorioExtratoDespesasViagem.GetReport(relatorio, extensao, logo);
        }

        public byte[] GerarRelatorioExtratoV2GridDespesaViagem(string logo, string extensao, List<RelatorioExtratoDetalhadoDespesasViagemDataType> relatorio)
        {
            return _relatorioExtratoDetalhadoDespesasViagem.GetReport(relatorio, extensao, logo);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using Sistema.Framework.Util.Enumerate;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class ChavesPixResponse
    {
        public List<ChavesPixResponseItem> Chaves { get; set; }
    }

    public class ChavesPixResponseItem
    {
        public ETipoChavePix Tipo { get; set; }
        public string TipoString => EnumHelper.GetDescriptionToString(Tipo);
        public string Chave { get; set; }
        public DateTime? DataCriacao { get; set; }
        public string DataCriacaoString => DataCriacao?.ToString("dd/MM/yyyy HH:mm");
    }

    public enum ETipoChavePix
    {
        [EnumMember, Description("CPF")]
        Cpf = 0,
        [EnumMember, Description("CNPJ")]
        Cnpj = 1,
        [EnumMember, Description("Telefone")]
        Phone = 2,
        [EnumMember, Description("Email")]
        Email = 3,
        [EnumMember, Description("Aleatória")]
        Evp = 4,
    }
}
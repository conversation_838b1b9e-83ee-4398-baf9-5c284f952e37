﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IVeiculoRepository : IRepository<Veiculo>
    {
        Veiculo GetWithAllChilds(int idVeiculo);
        IQueryable<Veiculo> GetVeiculosPorNumerosFrotas(List<long> numeroFrota);
        IQueryable<Veiculo> GetVeiculosPorPlaca(List<string> placas);
        string ConvertToPadraoMercosul(string placa);
        string ConvertToPadraoBrasil(string placa);
        string GetRntrcProprietarioVeiculo(string placa, int idEmpresa);
        string GetRntrcProprietarioVeiculo(int idVeiculo);
        Veiculo GetVeiculoPorPlacaWithoutIncludes(string placa, int idEmpresa);
    }
}
﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO.CadastroRotas;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models.DestinoRotaModelo;
using ATS.Domain.Models.DestinoRotaModelo.ATS.Domain.Models.DestinoRotaModelo;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IRotaModeloApp : IAppBase<RotaModelo>
    {
        ValidationResult Add(RotaModelo rotaModelo);
        ValidationResult Editar(RotaModelo rotaModelo);
        object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        BusinessResult<RotaModeloResponse> GetById(int idRota);
        RotaModelo GetWithChilds(int idRota);
        List<DestinoRotaModeloModel> SetarRetornoDestino(RotaModelo rotaModelo);
        List<DetalhesRotaModeloModel> ConsultarDetalhes(RotaModelo rotaModelo);
        RotaModelo GetByIdOrNomeRota(int? idRota, string nomeRota, int? idEmpresa);
        BusinessResult<RotaModeloParametrosResponse> ConsultarParametros(int idEmpresa);

    }
}
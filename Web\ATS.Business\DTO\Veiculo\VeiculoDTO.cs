namespace ATS.Domain.DTO.Veiculo
{
    /// <summary>
    /// Objeto simples e pequeno para utilização nas regras de negócio interna das camandas do sistema.
    /// </summary>
    public class VeiculoDTO
    {
        public int IdVeiculo { get; set; }
        public string Placa { get; set; }
        public int? IdFilial { get; set; }
        public string NomeFantasiaEmpresa { get; set; }
        public string NomeFantasiaFilial { get; set; }
        public string RazaoSocialFilial { get; set; }
        public int? IdEmpresa { get; set; }
        public int? AvisoSonoroFraco { get; set; }
        public int? AvisoSonoroModerado { get; set; }
        public int? AvisoSonoroForte { get; set; }
        public int? EmpresaAvisoSonoroFraco { get; set; }
        public int? EmpresaAvisoSonoroModerado { get; set; }
        public int? EmpresaAvisoSonoroForte { get; set; }
        public long? NumeroFrota { get; set; }


        // Onda 2
        public int? IdProprietario { get; set; }
        public int? AnoFabricacao { get; set; }
        public string Modelo { get; set; }
    }
}
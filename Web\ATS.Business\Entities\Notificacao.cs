﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class Notificacao
    {
        /// <summary>
        /// Código da Notificação
        /// </summary>
        public int IdNotificacao { get; set; }

        /// <summary>
        /// Código do empresa
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Tipo
        /// </summary>
        public int Tipo { get; set; }

        /// <summary>
        /// Corpo da mensagem
        /// </summary>
        public string Conteudo { get; set; }

        /// <summary>
        /// Data e Hora de envio
        /// </summary>
        public DateTime DataHoraEnvio { get; set; }

        /// <summary>
        /// Seta se a notificação foi recebida ou não
        /// </summary>
        public bool Recebida { get; set; } = false;

        public bool RecebidaNovo { get; set; } = false;

        public bool? Lida { get; set; } = false;
        //<summary>
        //Link genérico para redirecionar para outra pagina
        //</summary>
        public string Link { get; set; }

        public int IdTipoNotificacao { get; set; }

        public EFuncaoOrigem FuncaoOrigem { get; set; } = EFuncaoOrigem.Indefinido;

        //Não foi criado relacionamento pois esse campo pode conter diferentes ids conforme enum Processo.  
        public int? IdParent { get; set; }


        #region Referências

        public virtual Usuario usuario { get; set; }
        public virtual TipoNotificacao TipoNotificacao { get; set; }

        #endregion
    }
}
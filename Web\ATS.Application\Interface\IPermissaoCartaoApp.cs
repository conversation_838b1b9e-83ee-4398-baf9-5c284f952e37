﻿using ATS.Domain.Models.PermissaoCartao;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IPermissaoCartaoApp 
    {
        BusinessResult SaqueHabilitar(int identificador, int produto);
        BusinessResult CompraFisicaDesabilitar(int identificador, int produto);
        BusinessResult CompraFisicaHabilitar(int identificador, int produto);
        BusinessResult CompraInternacionalDesabilitar(int identificador, int produto);
        BusinessResult CompraInternacionalHabilitar(int identificador, int produto);
        BusinessResult CompraOnlineDesabilitar(int identificador, int produto);
        BusinessResult CompraOnlineHabilitar(int identificador, int produto);
        BusinessResult SaqueDesabilitar(int identificador, int produto);
        BusinessResult<PermissaoCartaoModelResponse> ConsultarPermissoes(int identificador, int produto);
    }
}
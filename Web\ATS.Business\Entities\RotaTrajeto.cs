﻿namespace ATS.Domain.Entities
{
    public class RotaTrajeto
    {
        public int IdTrajeto { get; set; }
        public int IdRota { get; set; }
        public string Descricao { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        public int DuracaoViagemSegundos { get; set; }
        public string  DuracaoViagem { get; set; }

        public int DistanciaViagemMetros { get; set; }
        public string DistanciaViagem { get; set; }

        public virtual Rota Rota { get; set; }
        public string GoogleIcon { get; set; }
    }
}

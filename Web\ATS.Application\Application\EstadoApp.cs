﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class EstadoApp : AppBase, IEstadoApp
    {
        private readonly IEstadoService _estadoService;

        public EstadoApp(IEstadoService estadoService)
        {
            _estadoService = estadoService;
        }

        /// <summary>
        /// Método utilizado para buscar Estado.
        /// </summary>
        /// <param name="id">Id de Pais</param>
        /// <returns>Entidade Pais</returns>
        public Estado Get(int id)
        {
            return _estadoService.Get(id);
        }
        
        public List<Estado> GetTodos()
        {
            return _estadoService.GetTodos();
        }

        /// <summary>
        /// Obtém estado pela sigla
        /// </summary>
        /// <param name="sigla"></param>
        /// <returns></returns>
        public Estado GetEstadoBySigla(string sigla)
        {
            return _estadoService.GetEstadoBySigla(sigla);
        }

        /// <summary>
        /// Método utilizado para incluir Estado.
        /// </summary>
        /// <param name="entity">Entidade de Estado</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Estado entity)
        {
            ValidationResult validationResult;

            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    validationResult = _estadoService.Add(entity);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return validationResult;
        }

        /// <summary>
        /// Método utilizado para alterar Estado.
        /// </summary>
        /// <param name="entity">Entidade de Estado</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Estado entity)
        {
            ValidationResult validationResult;

            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    validationResult = _estadoService.Update(entity);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return validationResult;
        }

        /// <summary>
        /// Método utilizado para consultar Estado.
        /// </summary>
        /// <param name="nome">Nome de Estado</param>
        /// <returns>IQueryable de Estado</returns>
        public IQueryable<Estado> Consultar(string nome = null)
        {
            return _estadoService.Consultar(nome);
        }

        public IQueryable<Estado> ConsultarPorIdPais(int idPais)
        {
            return _estadoService.ConsultarPorIdPais(idPais);
        }

        /// <summary>
        /// Retorna o objeto de Estado informando o código do IBGE
        /// </summary>
        /// <param name="nIBGE">Código do IBGE</param>
        /// <returns></returns>
        public Estado GetPorIBGE(int nIBGE)
        {
            return _estadoService.GetPorIBGE(nIBGE);
        }

        public int GetIdPorIBGE(int nIBGE)
        {
            return _estadoService.GetIdPorIBGE(nIBGE);
        }

        /// <summary>
        /// Retorna o Estado via pesquisa por sigla da UF
        /// </summary>
        /// <param name="uf"></param>
        /// <returns></returns>
        public Estado GetPorSigla(string uf)
        {
            return _estadoService.GetPorSigla(uf);
        }

        public int? GetIdEstado(string uf)
        {
            return _estadoService.GetIdEstado(uf);
        }

        /// <summary>
        /// Inativa um estado
        /// </summary>
        /// <param name="idEstado">ID do estado a ser inativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idEstado)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _estadoService.Inativar(idEstado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa um estado
        /// </summary>
        /// <param name="idEstado">ID do estado a ser reativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idEstado)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _estadoService.Reativar(idEstado);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna todos os estados ativos
        /// </summary>
        /// <returns>IQueryable de Estado</returns>
        public IQueryable<Estado> All()
        {
            return _estadoService.All();
        }

        /// <summary>
        /// Retorna o código do estado a partir do país e sigla
        /// </summary>
        /// <param name="idPais"></param>
        /// <param name="sigla"></param>
        /// <returns></returns>
        public int? GetIdEstadoPorSigla(int idPais, string sigla)
        {
            return _estadoService.GetIdEstadoPorSigla(idPais, sigla);
        }

        public int GetIdEstadoPorIbge(int codigoIbge)
        {
            return _estadoService.GetIdEstadoPorIbge(codigoIbge);
        }

        /// <summary>
        /// Retorna a lista de estados atualizados a partir da data
        /// </summary>
        /// <param name="dataBase">Data base para filtro</param>
        /// <returns></returns>
        public IQueryable<Estado> GetEstadosAtualizados(DateTime dataBase, ERegiaoBrasil? Regiao)
        {
            return _estadoService.GetEstadosAtualizados(dataBase, Regiao);
        }

        public object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _estadoService.ConsultarGrid(take, page, order, filters);
        }

        public bool ValidarIbgeCadastrado(int ibge)
        {
            return _estadoService.ValidarIbgeCadastrado(ibge);
        }
    }
}
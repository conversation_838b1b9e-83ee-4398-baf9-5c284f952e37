﻿using ATS.Application.Interface;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.ControllersATS
{
    public class PrestacaoContasAtsController : DefaultController
    {
        private readonly IPrestacaoContasApp _app;
        private readonly IUserIdentity _userIdentity;

        public PrestacaoContasAtsController(IPrestacaoContasApp app, IUserIdentity userIdentity)
        {
            _app = app;
            _userIdentity = userIdentity;
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, EStatusPrestacaoContas[] status = null)
        {
            try
            {
                var retorno = _app.ConsultarGrid(take, page, order, filters, status);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [EnableLogRequest]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int id, EStatusPrestacaoContas status, string observacao = null)
        {
            try
            {
                _app.AlterarStatus(id, status, observacao);
                return ResponderSucesso("Operação realizada com sucesso.");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }

    }
}
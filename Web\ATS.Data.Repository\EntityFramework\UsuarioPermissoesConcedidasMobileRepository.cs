using System;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioPermissoesConcedidasMobileRepository : Repository<UsuarioPermissoesConcedidasMobile>, IUsuarioPermissoesConcedidasMobileRepository
    {
        public UsuarioPermissoesConcedidasMobileRepository(AtsContext context) : base(context)
        {
        }
        
        public ValidationResult Salvar(UsuarioPermissoesConcedidasMobile usuarioPermissoesConcedidasMobile)
        {
            usuarioPermissoesConcedidasMobile.DataAtualizacao = DateTime.Now;
            Add(usuarioPermissoesConcedidasMobile);
            return new ValidationResult();
        }

        public ValidationResult Editar(UsuarioPermissoesConcedidasMobile usuarioPermissoesConcedidasMobile)
        {
            usuarioPermissoesConcedidasMobile.DataAtualizacao = DateTime.Now;
            Update(usuarioPermissoesConcedidasMobile);
            return new ValidationResult();
        }

        public UsuarioPermissoesConcedidasMobile ConsultarPorUsuario(int usuarioId)
        {
            return Where(o => o.IdUsuario == usuarioId).FirstOrDefault();
            /*return new UsuarioPermissoesConcedidasMobileModel
            {
                DataAtualizacao = permissoes.DataAtualizacao, IdUsuario = permissoes.IdUsuario, IdUsuarioPermissaoConcediadMobile = permissoes.IdUsuarioPermissaoConcediadMobile, PermiteAcessoLocalizacao = permissoes.PermiteAcessoLocalizacao, PermiteBuscarContatos = permissoes.PermiteBuscarContatos, PermiteEnviarSms = permissoes.PermiteEnviarSms, PermiteEscreverCalendario = permissoes.PermiteEscreverCalendario, PermiteEscreverContatos = permissoes.PermiteEscreverContatos, PermiteGravarAudio = permissoes.PermiteGravarAudio, PermiteLerCalendario = permissoes.PermiteLerCalendario, PermiteLerContatos = permissoes.PermiteLerContatos, PermiteLerSms = permissoes.PermiteLerSms, PermiteRealizarChamada = permissoes.PermiteRealizarChamada, PermiteReceberMms = permissoes.PermiteReceberMms, PermiteReceberSms = permissoes.PermiteReceberSms, PermiteUsarCamera = permissoes.PermiteUsarCamera, PermiteUsarSip = permissoes.PermiteUsarSip, PermiteAdicionarCorreioVoz = permissoes.PermiteAdicionarCorreioVoz, PermiteEscreverArmazenamentoExterno = permissoes.PermiteEscreverArmazenamentoExterno, PermiteEscreverLogChamada = permissoes.PermiteEscreverLogChamada, PermiteLerArmazenamentoExterno = permissoes.PermiteLerArmazenamentoExterno, PermiteLerEstadoTelefone = permissoes.PermiteLerEstadoTelefone, PermiteLerLogChamada = permissoes.PermiteLerLogChamada, PermiteProcessarChamadaSaida = permissoes.PermiteProcessarChamadaSaida, PermiteReceberWapPush = permissoes.PermiteReceberWapPush, PermiteUsarSensoresComporais = permissoes.PermiteUsarSensoresComporais
            };*/
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.CrossCutting.Reports.Cartoes.ReciboTransferencia;
using ATS.CrossCutting.Reports.DespesasViagem.RelatorioListaDespesasViagem;
using ATS.CrossCutting.Reports.Pedagio.Recibo;
using ATS.CrossCutting.Reports.Pedagio.ReciboPagamento;
using ATS.CrossCutting.Reports.TagExtratta.ConsultaSituacaoTags;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.SistemaInfo.Cartao.DTO;
using ATS.Data.Repository.External.SistemaInfo.Pedagio.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Webservice.Request.Credenciamento;
using ATS.WS.Models.Webservice.Request.Empresa;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using ATS.WS.Models.Webservice.Response;
using ATS.WS.Models.Webservice.Response.Cartao;
using ATS.WS.Models.Webservice.Response.Estabelecimento;
using ATS.WS.Models.Webservice.Response.Motorista;
using ATS.WS.Models.Webservice.Response.Notificacao;
using ATS.WS.Models.Webservice.Response.PagamentoConfiguracao;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using SistemaInfo.MicroServices.Rest.Infra.ApiClient;
using Viagem = ATS.Domain.Entities.Viagem;
using CargaAvulsa = ATS.Domain.Entities.CargaAvulsa;
using Documento = ATS.WS.Models.Webservice.Request.Estabelecimento.Documento;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Banner;
using ATS.Domain.DTO.Campanha;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.DTO.Estabelecimento;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Extensions;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DespesasViagem;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Models.PermissaoCartao;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.ViagemV2.Response;
using ATS.WS.Models.Webservice.Response.TipoCavalo;
using Autofac;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cadastros.ApiClient;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using TagExtrattaClient;
using ApiProcessingStateOnServerState = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ApiProcessingStateOnServerState;
using ConsultarEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarEmpresaResponse;
using ConsultarEmpresaResponseStatus = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarEmpresaResponseStatus;
using ConsultarRemessaResponse = ATS.Domain.DTO.TagExtratta.ConsultarRemessaResponse;
using CustomFilterFieldType = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilterFieldType;
using CustomFilterOperator = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilterOperator;
using EOperador = ATS.Domain.Enum.EOperador;
using VeiculoTagResponse = ATS.Domain.DTO.TagExtratta.VeiculoTagResponse;

namespace ATS.WS.AutoMapper
{
    public class DomainToViewModelMappingProfile : Profile
    {
        protected override void Configure()
        {
            CreateMap<BloqueioGestorValor, BloqueioGestorValorItem>()
                .ForMember(u => u.IdFilial, opts => opts.MapFrom(u => u.IdFilial))
                .ForMember(u => u.IdBloqueioGestorTipo, opts => opts.MapFrom(u => u.IdBloqueioGestorTipo))
                .ForMember(u => u.DescricaoBloqueioGestorTipo, opts => opts.MapFrom(u => u.BloqueioGestorTipo.Descricao))
                .ForMember(u => u.Origem, opts => opts.MapFrom(u => u.IdBloqueioOrigemTipo))
                .ForMember(u => u.Valor, opts => opts.MapFrom(u => u.Valor))
                .ForMember(u => u.IdEmpresa, opts => opts.MapFrom(u => u.IdEmpresa))
                .ForMember(u => u.IdBloqueioGestorValor, opts => opts.MapFrom(u => u.IdBloqueioGestorValor));
            
            CreateMap<DespesaCriarAplicativoModel, DespesaUsuarioAddModel>()
                .ForMember(u => u.IdUsuario, opts => opts.MapFrom(u => u.IdUsuario))
                .ForMember(u => u.Descricao, opts => opts.MapFrom(u => u.Despesa.Descricao))
                .ForMember(u => u.HashId, opts => opts.MapFrom(u => u.Despesa.HashId))
                .ForMember(u => u.IdCategoria, opts => opts.MapFrom(u => u.Despesa.IdCategoria))
                .ForMember(u => u.URL, opts => opts.MapFrom(u => u.Despesa.LinkNota))
                .ForMember(u => u.Latitude, opts => opts.MapFrom(u => u.Localizacao.Latitude))
                .ForMember(u => u.Longitude, opts => opts.MapFrom(u => u.Localizacao.Longitude));

            #region Usuario

            CreateMap<Usuario, UsuarioModel>()
                .ForMember(u => u.HorariosCheckIn, u => u.Ignore())
                .ForMember(u => u.TipoCobranca, u => u.Ignore())
                .ForMember(u => u.Veiculos, u => u.Ignore())
                .ForMember(u => u.HorariosNotificacao, u => u.Ignore());

            CreateMap<Usuario, UsuarioIntegrarMobRequestModel>()
                .ForMember(u => u.HorariosCheckIn, u => u.Ignore())
                .ForMember(u => u.HorariosNotificacao, u => u.Ignore());


            CreateMap<Usuario, UsuarioModelAppAntigo>()
                .ForMember(u => u.HorariosCheckIn, u => u.Ignore())
                .ForMember(u => u.TipoCobranca, u => u.Ignore())
                .ForMember(u => u.Veiculos, u => u.Ignore())
                .ForMember(u => u.HorariosNotificacao, u => u.Ignore());

            CreateMap<Usuario, UsuarioMobileModel>();
            CreateMap<UsuarioFilial, UsuarioMobileModel>();
            CreateMap<UsuarioContato, UsuarioMobileModel>();
            CreateMap<UsuarioEndereco, UsuarioMobileModel>();
            CreateMap<GrupoUsuario, GrupoUsuarioModel>();
            CreateMap<Menu, MenuModel>();
            CreateMap<GrupoUsuarioMenu, GrupoUsuarioMenuModel>();
            CreateMap<UsuarioPreferencias, UsuarioPreferenciasModel>();
            CreateMap<Usuario, UsuarioIntegrarMobRequestModel>();

            CreateMap<Usuario, UsuarioMicroServicoInstanciaAppDto>()
                .ForMember(u => u.TokenMicroServices, opts => opts.MapFrom(u => u.Empresa.TokenMicroServices));

            CreateMap<Usuario, UsuarioFotoDto>()
                .ForMember(u => u.Cpfcnpj, opts => opts.MapFrom(u => u.CPFCNPJ))
                .ForMember(u => u.FotoByte, opts => opts.MapFrom(u => u.Foto))
                .ForMember(u => u.Foto, opts => opts.Ignore());
            #endregion

            #region Empresa

            CreateMap<Empresa, EmpresaModel>();
            CreateMap<EmpresaLayout, EmpresaLayoutModel>();
            CreateMap<Empresa, ConsultaTodasEmpresasDto>();
            #endregion

            #region Filial

            CreateMap<Filial, FilialModel>();
            CreateMap<Filial, FilialIntegrarResponseModel>();

            #endregion

            #region Motorista

            CreateMap<Motorista, MotoristaModel>()
                .ForMember(u => u.Veiculos, u => u.Ignore());

            CreateMap<Motorista, MotoristaPorCpfModel>()
                // .ForMember(u => u.DataValidade, z => z.MapFrom(w => _usuarioApp.GetDocumentoCnhPorCpfMot(w.CPF).Validade));
                .ForMember(u => u.DataValidade, z => z.MapFrom(w => GetValidadeCnhMotorista(w.CPF)));

                CreateMap<Motorista, MotoristaPorCpfPamcardModel>()
                // .ForMember(u => u.DataValidade, z => z.MapFrom(w => _usuarioApp.GetDocumentoCnhPorCpfMot(w.CPF).Validade));
                .ForMember(u => u.DataValidade, z => z.MapFrom(w => GetValidadeCnhMotorista(w.CPF)));

            CreateMap<Motorista, MotoristaResponse>();

            #endregion

            #region País, Estado e Cidade

            CreateMap<Estado, EstadoModel>();
            CreateMap<Cidade, CidadeModel>()
                .ForMember(d => d.Latitude, opts => opts.MapFrom(s => s.Latitude ?? 0))
                .ForMember(d => d.Longitude, opts => opts.MapFrom(s => s.Longitude ?? 0));
            CreateMap<Pais, PaisModel>();

            #endregion

            #region Veículo, Tipo de Veículo e Carreta

            CreateMap<Veiculo, VeiculoModel>();
            CreateMap<Veiculo, UsuarioIntegrarMobRequestModel>();
            
            CreateMap<Veiculo, ConsultaDadosVeiculoResponseDTO>()
                .ForMember(p => p.TipoCavalo, opts => opts.MapFrom(c => c.TipoCavalo.Nome))
                .ForMember(p => p.TipoCarreta, opts => opts.MapFrom(c => c.TipoCarreta.Nome));

            CreateMap<TipoCavalo, TipoCavaloModel>();
            CreateMap<TipoCarreta, TipoCarretaModel>();

            #endregion

            #region Mensagem

            CreateMap<Mensagem, MensagemModel>();
            CreateMap<Mensagem, NotificacaoRequestModel>();

            #endregion

            #region Cliente

            CreateMap<Cliente, ClienteModel>();
            CreateMap<Cliente, ClienteRequestModel>();

            #endregion

            #region Proprietário

            CreateMap<Proprietario, ProprietarioModel>();
            CreateMap<ProprietarioContato, ProprietarioContatoModel>();
            CreateMap<ProprietarioEndereco, ProprietarioEnderecoModel>();
            CreateMap<Proprietario, ProprietarioAnttDto>();

            CreateMap<Proprietario, ProprietarioViagemDto>();
            CreateMap<Veiculo, ProprietarioViagemDto>()
                .ForMember(p => p.IdProprietario, opts => opts.MapFrom(c => c.Proprietario.IdProprietario))
                .ForMember(p => p.CNPJCPF, opts => opts.MapFrom(c => c.Proprietario.CNPJCPF))
                .ForMember(p => p.NomeFantasia, opts => opts.MapFrom(c => c.Proprietario.NomeFantasia))
                .ForMember(p => p.RNTRC, opts => opts.MapFrom(c => c.Proprietario.RNTRC));

            #endregion

            #region Espécie

            CreateMap<Especie, EspecieModel>();

            #endregion

            #region Viagem

            //CreateMap<Viagem, ConsultarGridParamsAtsNovo>();
            CreateMap<Viagem, ViagemModel>();
            CreateMap<ViagemEvento, ViagemEventoModel>();
            CreateMap<ViagemPendenteGestor, GestaoViagensGridResponse>()
                .ForMember(gv => gv.StatusStr, opts => opts.MapFrom(v => v.Status == 0 ? "Pendente" : v.Status == 1 ? "Liberado" : "Bloqueado"))
                .ForMember(gv => gv.TipoBloqueio, opts => opts.MapFrom(v => v.BloqueioGestorTipo.Descricao))
                .ForMember(gv => gv.UsuarioStatus, opts => opts.MapFrom(v => v.UsuarioDesbloqueio.Nome))
                .ForMember(gv => gv.DataCadastro, opts => opts.MapFrom(v => v.DataCadastro.ToString("dd/MM/yyyy HH:mm")));
            CreateMap<Viagem, DadosFilialEPagamento>()
                .ForMember(dfp => dfp.TipoConta,
                    opts => opts.MapFrom(
                        v => v.TipoConta != null ? Enum.GetName(typeof(ETipoConta), v.TipoConta) : null))
                .ForMember(dfp => dfp.NumConta, opts => opts.MapFrom((v => v.ContaCorrente)))
                .ForMember(dfp => dfp.Banco, opts => opts.MapFrom((v => v.DescricaoBanco)));
            CreateMap<DadosFilialEPagamento, ConsultarFilialEDadosPagamentoResponse>()
                .ForMember(cfdp => cfdp.Conteudo, opts => opts.MapFrom(dfp => dfp))
                .ForMember(cfdp => cfdp.Sucesso, opts => opts.MapFrom(src => true));

            CreateMap<Viagem, ConsultarUltimaViagemEmpresaResponse>();

            CreateMap<Viagem, ReciboPefDadosResponse>()
                .ForMember(rp => rp.DataImpressao, opts => opts.MapFrom(v => DateTime.Now))
                .ForMember(rp => rp.Filial, opts => opts.MapFrom(v => v.Filial.RazaoSocial))
                .ForMember(rp => rp.DocumentoProprietario, opts => opts.MapFrom(v => v.Proprietario.CNPJCPF))
                .ForMember(rp => rp.Contratado, opts => opts.MapFrom(v => v.Proprietario.RazaoSocial))
                .ForMember(rp => rp.Contratante, opts => opts.MapFrom(v => v.Empresa.RazaoSocial))
                .ForMember(rp => rp.CnpjContratante, opts => opts.MapFrom(v => v.Empresa.CNPJ))
                .ForMember(rp => rp.CnpjFilial, opts => opts.MapFrom(v => v.Filial.CNPJ))
                .ForMember(rp => rp.Ciot,
                    opts => opts.MapFrom(v => v.DeclaracaoCiot.Ciot + "/" + v.DeclaracaoCiot.Verificador))
                .ForMember(rp => rp.DataInicioViagem, opts => opts.MapFrom(v => v.DataEmissao))
                .ForMember(rp => rp.PlacaCavalo, opts => opts.MapFrom(v => v.Placa))
                .ForMember(rp => rp.PlacasCarreta, opts => opts.MapFrom(v => v.ViagemCarretas))
                .ForMember(rp => rp.Placas, opts => opts.MapFrom(v => v.Placa))
                .ForMember(rp => rp.Rntrc, opts => opts.MapFrom(v => v.RNTRC))
                .ForMember(rp => rp.Motorista, opts => opts.MapFrom(v => v.NomeMotorista))
                .ForMember(rp => rp.DocumentoMotoristra, opts => opts.MapFrom(v => v.CPFMotorista))
                .ForMember(rp => rp.Parcelas, opts => opts.MapFrom(v => v.ViagemEventos))
                .ForMember(rp => rp.RazaoSocialRemetente, opts => opts.MapFrom(v => v.ClienteOrigem.RazaoSocial))
                .ForMember(rp => rp.CnpjCpfRemetente, opts => opts.MapFrom(v => v.ClienteOrigem.CNPJCPF))
                .ForMember(rp => rp.RazaoSocialDestinatario, opts => opts.MapFrom(v => v.ClienteDestino.RazaoSocial))
                .ForMember(rp => rp.CnpjCpfDestinatario, opts => opts.MapFrom(v => v.ClienteDestino.CNPJCPF));

            CreateMap<ViagemCarreta, PlacaCarreta>();
            
            CreateMap<ViagemEvento, Parcelas>()
                .ForMember(pr => pr.IdEvento, opts => opts.MapFrom(ve => ve.IdViagemEvento))
                .ForMember(pr => pr.TipoEvento, opts => opts.MapFrom(ve => ve.TipoEventoViagem))
                .ForMember(pr => pr.DataPagamento, opts => opts.MapFrom(ve => ve.DataHoraPagamento))
                .ForMember(pr => pr.Instrucoes, opts => opts.MapFrom(ve => ve.Instrucao))
                .ForMember(pr => pr.StatusEvento, opts => opts.MapFrom(ve => ve.Status))
                .ForMember(pr => pr.Valor, opts => opts.MapFrom(ve => ve.ValorPagamento))
                .ForMember(pr => pr.PagamentoCartao, opts => opts.MapFrom(ve => ve.HabilitarPagamentoCartao));

            #endregion

            #region Tipo notificação

            CreateMap<TipoNotificacaoRequestModel, TipoNotificacao>();
            CreateMap<TipoNotificacaoResponse, TipoNotificacao>();

            #endregion

            #region Notificação

            CreateMap<ConsultarMobileResponse, Notificacao>();

            #endregion

            #region Notificação Push

            CreateMap<NotificacaoPushRequestModel, NotificacaoPush>()
                .ForMember(u => u.Items, u => u.Ignore());

            #endregion

            #region Estabelecimento

            CreateMap<EstabelecimentoBaseDocumento, Documento>();
            CreateMap<EstabelecimentoCrud, Estabelecimento>();
            
            CreateMap<EstabelecimentoProdutoCrud, EstabelecimentoBaseProduto>()
                .ForMember(d => d.PrecoPromocional, opts => opts.MapFrom(s => s.PrecoPromocional.ToDecimalSafe(null)))
                .ForMember(d => d.PrecoUnitario, opts => opts.MapFrom(s => s.PrecoUnidade.ToDecimalSafe(null)));
            CreateMap<Associados, EstabelecimentoBaseAssociacao>()
                .ForMember(d => d.IdAssociacao, opts => opts.MapFrom(s => s.IdEstabelecimento))
                .ForMember(d => d.IdEstabelecimento, opts => opts.Ignore());
            CreateMap<ContasBancarias, EstabelecimentoBaseContaBancaria>()
                .ForMember(d => d.IdEstabelecimentoBase, opts => opts.MapFrom(s => s.IdEstabelecimentoBaseContaBancaria));
            CreateMap<EstabelecimentoCrud, EstabelecimentoBase>()
                .ForMember(d => d.AssociacoesBaseEstabelecimento, opts => opts.MapFrom(s => s.Associacoes))
                .ForMember(d => d.EstabelecimentoBaseProdutos, opts => opts.MapFrom(s => s.Produtos))
                .ForMember(d => d.EstabelecimentoBaseContasBancarias, opts => opts.MapFrom(s => s.EstabelecimentoContasBancarias));
            
            CreateMap<Estabelecimento, ConsultaEstabelecimentoResponse>()
                .ForMember(d => d.TipoEstabelecimento, opts => opts.MapFrom(s => s.TipoEstabelecimento.Descricao))
                .ForMember(d => d.Estabelecimento, opts => opts.MapFrom(s => s.Descricao))
                .ForMember(d => d.Pais, opts => opts.MapFrom(s => s.Pais.Nome))
                .ForMember(d => d.CodigoBACENPais, opts => opts.MapFrom(s => s.Pais.BACEN))
                .ForMember(d => d.Estado, opts => opts.MapFrom(s => s.Estado.Nome))
                .ForMember(d => d.CodigoIBGEEstado, opts => opts.MapFrom(s => s.Estado.IBGE))
                .ForMember(d => d.Cidade, opts => opts.MapFrom(s => s.Cidade.Nome))
                .ForMember(d => d.CodigoIBGECidade, opts => opts.MapFrom(s => s.Cidade.IBGE))
                .ForMember(d => d.Produtos, opts => opts.MapFrom(s => s.EstabelecimentoProdutos))
                .ForMember(d => d.Email, opts => opts.MapFrom(s => s.Email))
                .AfterMap((estabelecimento, response) =>
                {
                    response.Icone = !string.IsNullOrWhiteSpace(estabelecimento?.TipoEstabelecimento?.Icone?.TokenIcone)
                        ? GetIconeTipoEstabelecimentoCacheable(estabelecimento.TipoEstabelecimento?.Icone.TokenIcone)
                        : string.Empty;
                });

            CreateMap<EstabelecimentoProduto, EstabelecimentoProdutoResponse>()
                .ForMember(d => d.Produto, opts => opts.MapFrom(s => s.Descricao));

            CreateMap<EstabelecimentoBase, Estabelecimento>();

            CreateMap<EstabelecimentoBase, AssociacoesEmpresaDto>();

            #endregion

            #region credenciamento

            CreateMap<TipoMotivo, TipoMotivoRequest>();
            CreateMap<Motivo, MotivoRequestModel>()
                .ForMember(p => p.RazaoSocialEmpresa, opts => opts.MapFrom(c => c.Empresa.RazaoSocial))
                .ForMember(p => p.RazaoSocialFilial, opts => opts.MapFrom(c => c.Filial.RazaoSocial));

            #endregion

            #region Configuração

            CreateMap<PagamentoConfiguracao, PagamentoConfiguracaoResponse>();

            #endregion

            #region Produto

            CreateMap<Produto, ProdutoModel>();

            #endregion

            #region Proprietario

            //CreateMap<Conjunto, ConjuntoModel>();
            //CreateMap<ConjuntoCarreta, ConjuntoCarretaModel>();
            CreateMap<ClienteEndereco, ClienteEnderecoModel>();
            CreateMap<ClienteEndereco, ClienteEnderecoResponseModel>();

            #endregion

            #region Empresa

            CreateMap<Empresa, EmpresaCreateRequest>();
            CreateMap<EmpresaCreateModuloRequest, EmpresaCreateModuloRequest>();

            #endregion

            #region

            CreateMap<Webhook, NotificacaoWebhookApiRequest>();

            #endregion

            #region Pedágio

            CreateMap<CompraPedagioDTOResponse, CompraPedagioAtsResponse>()
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCadastro.ParaFormatoBrasileiroStr(string.Empty)))
                .ForMember(p => p.DataConfirmacao, opts => opts.MapFrom(c => c.DataConfirmacao.ParaFormatoBrasileiroStr(string.Empty)))
                .ForMember(p => p.Status, opts => opts.MapFrom(c => ((CompraStatusTipo) c.Status.Value).GetDescription()))
                .ForMember(p => p.Fornecedor, opts => opts.MapFrom(c => c.FornecedorStr))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Placa.ToPlacaFormato()));

            CreateMap<ConsultaCompraPedagioResponse, CartaoConsultarPedagioAtsResponse>();

            CreateMap<GetStatusPedagioResponse, GetStatusPedagioAtsResponse>();

            CreateMap<ConsultaRotaResponseDto, ObjetoCalcularRotaResponseDTO>()
                .ForMember(p => p.CustoTotal, opts => opts.MapFrom(c => c.CustoTotal))
                .ForMember(p => p.IdentificadorHistorico, opts => opts.MapFrom(c => c.IdentificadorHistorico))
                .ForMember(p => p.Pracas, opts => opts.MapFrom(c => c.Pracas));

            CreateMap<ConsultaRotaResponseDto, CalcularRotaResponseDTO>()
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c))
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c =>
                        c.Status == ConsultaRotaResponseDtoStatus.Sucesso &&
                        c.ProcessingStateOnServer.State == SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<ComprovanteValePedagioResponse, ComprovanteCompraPedagioDataType>()
                .ForMember(p => p.CompraId, opts => opts.MapFrom(c => c.CompraId.ToString()))
                .ForMember(p => p.ProtocoloValePedagio, opts => opts.MapFrom(c => c.ProtocoloResposta))
                .ForMember(p => p.ProtocoloEnvioValePedagio, opts => opts.MapFrom(c => c.ProtocoloEnvio))
                .ForMember(p => p.Categoria, opts => opts.MapFrom(c => $"{c.QuantidadeEixos ?? 0} eixos"))
                .ForMember(p => p.CartaoFrete, opts => opts.MapFrom(c => (c.Cartao ?? 0).ToString()))
                .ForMember(p => p.Status, opts => opts.MapFrom(c => c.StatusCompra))
                .ForMember(p => p.DataExpiracaoCompraPedagio, opts => opts.MapFrom(c => c.DataExpiracao));
            
            CreateMap<ComprovanteValePedagioPracaResponse, ComprovanteCompraPedagioHistoricoPracasDataType>()
                .ForMember(p => p.Nome, opts => opts.MapFrom(c => c.Nome))
                .ForMember(p => p.Valor, opts => opts.MapFrom(c => c.Valor));

            CreateMap<ViagemDadosValePedagio, ComprovanteValePedagioResponse>();

            CreateMap<ReciboPefDadosResponse, ReciboPagamentoDataType>()
                .ForMember(rp => rp.Rntrc,
                    opts => opts.MapFrom(rpd => rpd.Rntrc.HasValue ? rpd.Rntrc.ToStringSafe() : string.Empty))
                .ForMember(rp => rp.DataImpressao,
                    opts => opts.MapFrom(rpd => rpd.DataImpressao.ToString("dd/MM/yyyy HH:mm")))
                .ForMember(rp => rp.IdViagem, opts => opts.MapFrom(rpd => rpd.IdViagem.ToString()))
                .ForMember(rp => rp.DataInicioViagem,
                    opts => opts.MapFrom(rpd =>
                        rpd.DataInicioViagem.HasValue
                            ? rpd.DataInicioViagem.Value.ToString("dd/MM/yyyy")
                            : string.Empty))
                .ForMember(rp => rp.ValorTotalParcelas,
                    opts => opts.MapFrom(rpd => rpd.ValorTotalParcelas.ToString("C")))
                .ForMember(rp => rp.ValorComprovanteCarga,
                    opts => opts.MapFrom(rpd => rpd.ValorComprovanteCarga.ToString("C")));

            CreateMap<Parcelas, ReciboPagamentoHistoricoParcelasDataType>()
                .ForMember(p => p.IdEvento, opts => opts.MapFrom(cp => cp.IdEvento.ToString()))
                .ForMember(p => p.TipoEvento, opts => opts.MapFrom(cp => cp.TipoEvento.GetDescription()))
                .ForMember(p => p.DataPagamento, opts => opts.MapFrom(cp => cp.DataPagamento.HasValue ? cp.DataPagamento.Value.ToString("dd/MM/yyyy") : string.Empty))
                .ForMember(p => p.StatusEvento, opts => opts.MapFrom(cp => cp.StatusEvento == EStatusViagemEvento.Baixado ? "Efetivado" : cp.StatusEvento.GetDescription()))
                .ForMember(p => p.Valor, opts => opts.MapFrom(cp => cp.Valor.ToString("C")))
                .ForMember(p => p.PagamentoCartao, opts => opts.MapFrom(cp => cp.PagamentoCartao ? "Sim" : "Não"));

            CreateMap<PassagensPracaVeiculoGetModelResponse, PracasPedagioVeiculoResponseDTO>()
                .ForMember(p => p.QuantidadePeriodo, opts => opts.MapFrom(cp => cp.Itens.Count))
                .ForMember(p => p.ValorTotalPeriodo, opts => opts.MapFrom(cp => cp.Itens.Sum(x => x.Valor)));

            CreateMap<PassagensPracaVeiculoItemGetModelResponse, PracasPedagioVeiculoItemResponseDTO>()
                .ForMember(p => p.DataEmissao, opts => opts.MapFrom(cp => cp.DataEmissao.HasValue ? cp.DataEmissao.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty))
                .ForMember(p => p.Valor, opts => opts.MapFrom(cp => cp.Valor.FormatMoney()));

            #endregion

            #region Carga avulsa

            CreateMap<CargaAvulsa, CargaAvulsaRequestModel>()
                .ForMember(p => p.CPFUsuario, opts => opts.MapFrom(c => c.CPFCNPJUsuario));

            #endregion

            CreateMap<QueryFilters, SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.CustomFilter>()
                .ForMember(p => p.Field, opts => opts.MapFrom(c => c.Campo))
                .ForMember(p => p.Operator, opts => opts.MapFrom(c => c.Operador))
                .ForMember(p => p.Value, opts => opts.MapFrom(c => c.Valor))
                .ForMember(p => p.FieldType, opts => opts.MapFrom(c => c.CampoTipo))
                .ForMember(p => p.ServerFieldCollection, opts => opts.MapFrom(c => c.ServerFieldCollection));

            CreateMap<DeclararOperacaoTransporteReponse, DeclararOperacaoTransporteModel>();

            CreateMap<EncerrarContratoAgregadoRequestModel, EncerrarContratoAgregadoModel>();
//            CreateMap<EncerrarContratoAgregadoRequestModel.ViagemAgregado, EncerrarContratoAgregadoModel.ViagemAgregado>();
//            CreateMap<EncerrarContratoAgregadoRequestModel.ValoresEfetivosAgregado, EncerrarContratoAgregadoModel.ValoresEfetivosAgregado>();


            CreateMap<EncerrarContratoAgregadoModel, EncerrarOperacaoTransporteRequest>();
//            CreateMap<EncerrarContratoAgregadoModel.ViagemAgregado, EncerrarOperacaoTransporteViagemRequest>();
//            CreateMap<EncerrarContratoAgregadoModel.ValoresEfetivosAgregado, ValoresFreteRequest>();

            CreateMap<ContratoCiotAgregado, ConsultaContratoAgregadoModel>()
                .ForMember(p => p.IdContratoCiotAgregado, opts => opts.MapFrom(c => c.IdContratoCiotAgregado))
                .ForMember(p => p.DataCadastro,
                    opts => opts.MapFrom(c =>
                        c.DataCadastro.HasValue ? c.DataCadastro.Value.ToString("dd/MM/yyyy") : string.Empty))
                .ForMember(p => p.DataInicio,
                    opts => opts.MapFrom(c =>
                        c.DataInicio.HasValue ? c.DataInicio.Value.ToString("dd/MM/yyyy") : string.Empty))
                .ForMember(p => p.DataFinal,
                    opts => opts.MapFrom(c =>
                        c.DataFinal.HasValue ? c.DataFinal.Value.ToString("dd/MM/yyyy") : string.Empty))
                .ForMember(p => p.StatusContratoAgregadoDescricao,
                    opts => opts.MapFrom(c => GetStatusContratoAgregado(c)))
                .ForMember(p => p.StatusContratoAgregado, opts => opts.MapFrom(c => (int) c.Status))
                .ForMember(p => p.ResultadoDeclaracaoCiot, opts => opts.MapFrom(c => (int) c.ResultadoDeclaracaoCiot))
                .ForMember(p => p.AvisoContratoAgregadoDescricao,
                    opts => opts.MapFrom(c => GetAvisoContratoAgregado(c)))
                .ForMember(p => p.NomeProprietario, opts => opts.MapFrom(c => c.Proprietario.RazaoSocial))
                .ForMember(p => p.Ciot,
                    opts => opts.MapFrom(c =>
                        c.IdDeclaracaoCiot.HasValue ? $"{c.DeclaracaoCiot.Ciot}/{c.DeclaracaoCiot.Verificador}" : null))
                .ForMember(p => p.NumeroCiot,
                    opts => opts.MapFrom(c => c.IdDeclaracaoCiot.HasValue ? c.DeclaracaoCiot.Ciot : null))
                .ForMember(p => p.Senha, opts => opts.MapFrom(c => c.DeclaracaoCiot.Senha))
                .ForMember(p => p.NomeEmpresa, opts => opts.MapFrom(c => c.Empresa.RazaoSocial))
                .ForMember(p => p.EmpresaId, opts => opts.MapFrom(c => c.Empresa.IdEmpresa));

            CreateMap<ContratoCiotAgregado, CarregarContratoAgregadoModel>()
                .ForMember(p => p.DeclaracaoCiot, opts => opts.MapFrom(c => c.DeclaracaoCiot))
                .ForMember(p => p.Proprietario, opts => opts.MapFrom(c => c.Proprietario))
                .ForMember(p => p.Veiculos, opts => opts.MapFrom(c => c.ContratoCiotAgregadoVeiculos.Select(e => e.Veiculo)))
                .ForMember(p => p.MensagemDeclaracaoCiot, opts => opts.MapFrom(c => !c.IdDeclaracaoCiot.HasValue || c.ResultadoDeclaracaoCiot == EResultadoDeclaracaoCiot.Erro ? c.MensagemDeclaracaoCiot : null))
                .ForMember(p => p.CIOT, opts => opts.MapFrom(c => c.DeclaracaoCiot != null ? string.Concat(c.DeclaracaoCiot.Ciot, "/", c.DeclaracaoCiot.Verificador) : null));

            CreateMap<CarregarContratoAgregadoModel, ContratoCiotAgregado>()
                .ForMember(p => p.DeclaracaoCiot, opts => opts.Ignore())
                .ForMember(p => p.Proprietario, opts => opts.MapFrom(c => c.Proprietario))
                .ForMember(p => p.ContratoCiotAgregadoVeiculos, opts => opts.Ignore());

            CreateMap<DeclaracaoCiot, DeclaracaoCiotModelAgregado>();
            
            CreateMap<DeclaracaoCiot, DeclararCiotResult.Declaracao>()
                .ForMember(p => p.DataDeclaracaoDateTime, opts => opts.MapFrom(c => c.DataDeclaracao))
                .ForMember(p => p.DataDeclaracao, opts => opts.Ignore());

            CreateMap<Viagem, ViagemModelAgregado>()
                .ForMember(p => p.Quantidade, opts => opts.MapFrom(c => c.Quantidade))
                .ForMember(p => p.PesoSaida, opts => opts.MapFrom(c => c.PesoSaida))
                .ForMember(p => p.ValorImpostos, opts => opts.MapFrom(c => c.SESTSENAT + c.INSS + c.IRRPF))
                .ForMember(p => p.MunicipioOrigem, opts => opts.MapFrom(c => c.ClienteOrigem.Cidade.Nome + " - " + c.ClienteOrigem.Estado.Sigla))
                .ForMember(p => p.MunicipioDestino, opts => opts.MapFrom(c => c.ClienteDestino.Cidade.Nome + " - " + c.ClienteDestino.Estado.Sigla))
                .ForMember(p => p.CepOrigem, opts => opts.MapFrom(c => c.CepOrigem.ToCepFormato()))
                .ForMember(p => p.CepDestino, opts => opts.MapFrom(c => c.CepDestino.ToCepFormato()));

            CreateMap<Viagem, ViagemV2ConsultaResponseModel>()
                .ForMember(dm => dm.ViagemId, mo => mo.MapFrom(o => o.IdViagem))
                .ForMember(dm => dm.ClienteOrigem, mo => mo.MapFrom(o => o.ClienteOrigem.RazaoSocial))
                .ForMember(dm => dm.ClienteOrigemDocumento,
                    mo => mo.MapFrom(o => o.ClienteOrigem.CNPJCPF.ToCpfOrCnpj()))
                .ForMember(dm => dm.ClienteDestino, mo => mo.MapFrom(o => o.ClienteDestino.RazaoSocial))
                .ForMember(dm => dm.ClienteDestinoDocumento,
                    mo => mo.MapFrom(o => o.ClienteDestino.CNPJCPF.ToCpfOrCnpj()))
                .ForMember(dm => dm.Filial, mo => mo.MapFrom(o => o.Filial.RazaoSocial))
                .ForMember(dm => dm.FilialDocumento, mo => mo.MapFrom(o => o.Filial.CNPJ.ToCNPJFormato()))
                .ForMember(dm => dm.DataColeta, mo => mo.MapFrom(o => o.DataColeta.HasValue ? o.DataColeta.Value.ToString("dd/MM/yyyy") : string.Empty))
                .ForMember(dm => dm.DataPrevisaoEntrega, mo => mo.MapFrom(o => o.DataPrevisaoEntrega.ToString("dd/MM/yyyy")))
                .ForMember(dm => dm.Motorista, mo => mo.MapFrom(o => o.NomeMotorista))
                .ForMember(dm => dm.MotoristaDocumento, mo => mo.MapFrom(o => o.CPFMotorista.ToCPFFormato()))
                .ForMember(dm => dm.Proprietario, mo => mo.MapFrom(o => o.NomeProprietario))
                .ForMember(dm => dm.ProprietarioDocumento, mo => mo.MapFrom(o => o.CPFCNPJProprietario.ToCpfOrCnpj()))
                .ForMember(dm => dm.StatusViagem, mo => mo.MapFrom(o => o.StatusViagem))
                .ForMember(dm => dm.StatusViagemDescricao, mo => mo.MapFrom(o => o.StatusViagem.DescriptionAttr()))
                .ForMember(dm => dm.PesoSaida, mo => mo.MapFrom(o => o.PesoSaida))
                .ForMember(dm => dm.PesoChegada, mo => mo.MapFrom(o => o.PesoChegada))
                .ForMember(dm => dm.EnderecoColeta, mo => mo.MapFrom(o => o.Coleta))
                .ForMember(dm => dm.EnderecoEntrega, mo => mo.MapFrom(o => o.Entrega))
                .ForMember(dm => dm.Produto, mo => mo.MapFrom(o => o.Produto))
                .ForMember(dm => dm.NaturezaCarga, mo => mo.MapFrom(o => o.NaturezaCarga))
                .ForMember(dm => dm.Irrpf, mo => mo.MapFrom(o => o.IRRPF))
                .ForMember(dm => dm.Inss, mo => mo.MapFrom(o => o.INSS))
                .ForMember(dm => dm.SestSenat, mo => mo.MapFrom(o => o.SESTSENAT))
                .ForMember(dm => dm.Eventos, mo => mo.MapFrom(o => o.ViagemEventos))
                .ForMember(dm => dm.EstabelecimentosAutorizados, mo => mo.MapFrom(o => o.ViagemEstabelecimentos));

            CreateMap<ViagemEvento, ViagemV2EventosConsultaResponseModel>()
                .ForMember(dm => dm.Token, mo => mo.MapFrom(o => o.Token))
                .ForMember(dm => dm.NumeroControle, mo => mo.MapFrom(o => o.NumeroControle))
                .ForMember(dm => dm.ValorBruto, mo => mo.MapFrom(o => o.ValorBruto))
                .ForMember(dm => dm.TipoEventoViagem, mo => mo.MapFrom(o => o.TipoEventoViagem))
                .ForMember(dm => dm.ViagemEventoId, mo => mo.MapFrom(o => o.IdViagemEvento))
                .ForMember(dm => dm.TipoEventoViagemDescricao, mo => mo.MapFrom(o => o.TipoEventoViagem.DescriptionAttr()));

            CreateMap<ViagemEstabelecimento, ViagemV2EstabelecimentosAutorizadosResponseModel>()
                .ForMember(dm => dm.TipoEvento, mo => mo.MapFrom(o => o.TipoEventoViagem))
                .ForMember(dm => dm.Cnpj, mo => mo.MapFrom(o => o.Estabelecimento.CNPJEstabelecimento))
                .ForMember(dm => dm.Nome, mo => mo.MapFrom(o => o.Estabelecimento.Descricao));

            CreateMap<Empresa, ContratanteCiotV2Dto>()
                .ForMember(p => p.IBGE, opts => opts.MapFrom(c => c.Cidade.IBGE));
            CreateMap<Cliente, RemetenteCiotV2Dto>()
                .ForMember(p => p.IBGE, opts => opts.MapFrom(c => c.Cidade.IBGE));
            CreateMap<Cliente, DestinatarioCiotV2Dto>()
                .ForMember(p => p.IBGE, opts => opts.MapFrom(c => c.Cidade.IBGE));

            CreateMap<Viagem, DadosCiotV2Dto>()
                .ForMember(p => p.Contratante, opts => opts.MapFrom(c => c.Empresa))
                .ForMember(p => p.Remetente, opts => opts.MapFrom(c => c.ClienteOrigem))
                .ForMember(p => p.Destinatario, opts => opts.MapFrom(c => c.ClienteDestino));

            CreateMap<Empresa, ContratanteCiotV3Dto>()
                .ForMember(p => p.IBGE, opts => opts.MapFrom(c => c.Cidade.IBGE));
            CreateMap<Cliente, RemetenteCiotV3Dto>()
                .ForMember(p => p.IBGE, opts => opts.MapFrom(c => c.Cidade.IBGE));
            CreateMap<Cliente, DestinatarioCiotV3Dto>()
                .ForMember(p => p.IBGE, opts => opts.MapFrom(c => c.Cidade.IBGE));

            CreateMap<Viagem, DadosCiotV3Dto>()
                .ForMember(p => p.Contratante, opts => opts.MapFrom(c => c.Empresa))
                .ForMember(p => p.Remetente, opts => opts.MapFrom(c => c.ClienteOrigem))
                .ForMember(p => p.Destinatario, opts => opts.MapFrom(c => c.ClienteDestino));

            CreateMap<ViagemEvento, DadosAtualizacaoCiotViagemEventoDto>();
            CreateMap<Viagem, DadosAtualizacaoCiotViagemDto>()
                .ForMember(p => p.ViagemEventos, opts => opts.MapFrom(c => c.ViagemEventos));
            CreateMap<DeclaracaoCiot, DadosAtualizacaoCiotDto>()
                .ForMember(p => p.ViagensVinculadas, opts => opts.MapFrom(c => c.ViagensVinculadas));

//            CreateMap<Viagem, EncerrarOperacaoTransporteViagemRequest>()
//                .ForMember(p => p.QuantidadeViagens, opts => opts.MapFrom(c => c.Quantidade))
//                .ForMember(p => p.PesoCarga, opts => opts.MapFrom(c => c.PesoSaida))
//                .ForMember(p => p.ValorImpostos, opts => opts.MapFrom(c => c.SESTSENAT + c.INSS + c.IRRPF))
//                .ForMember(p => p.CodigoMunicipioOrigem, opts => opts.MapFrom(c => c.ClienteOrigem.Cidade.Nome + " - " + c.ClienteOrigem.Estado.Sigla ))
//                .ForMember(p => p.CodigoMunicipioDestino, opts => opts.MapFrom(c => c.ClienteDestino.Cidade.Nome + " - " + c.ClienteDestino.Estado.Sigla ));

            CreateMap<Proprietario, ProprietarioModelAgregado>();
            CreateMap<Veiculo, VeiculoModelAgregado>()
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Placa.ToPlacaFormato()))
                .ForMember(p => p.QtdEixos, opts => opts.MapFrom(c => c.QuantidadeEixos));

            CreateMap<VeiculoModelAgregado, Veiculo>()
                .ForMember(p => p.QuantidadeEixos, opts => opts.MapFrom(c => c.QtdEixos));
            
            CreateMap<DespesasViagemGridResponse, RelatorioDespesasViagemDataType>();
            
            #region Tag

            CreateMap<GridTagItemResponse, RelatorioConsultaSituacaoTagsDataType>()
                .ForMember(p => p.Serial, opts => opts.MapFrom(c => c.SerialNumber));

            CreateMap<RemessaGetModelResponse, GridRemessaEnvioItemResponse>()
                .ForMember(p => p.UsuarioBaixa, opts => opts.MapFrom(c => c.UsuarioBaixa))
                .ForMember(p => p.UsuarioCadastro, opts => opts.MapFrom(c => c.UsuarioCriacao))
                .ForMember(p => p.QuantidadeTags, opts => opts.MapFrom(c => c.TagsRemessa.Count))
                .ForMember(p => p.NomeEmpresa, opts => opts.MapFrom(c => c.RazaoSocialEmpresa))
                .ForMember(p => p.DataBaixa, opts => opts.MapFrom(c => c.DataBaixa.HasValue ? c.DataBaixa.Value.ToString("dd/MM/yyyy HH:mm:ss") : null))
                .ForMember(p => p.Status, opts => opts.MapFrom(c => c.Status.ToString()))
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.Id, opts => opts.MapFrom(c => c.Id));

            CreateMap<RemessaGetModelResponse, GridRemessaRecebimentoItemResponse>()
                .ForMember(p => p.UsuarioBaixa, opts => opts.MapFrom(c => c.UsuarioBaixa))
                .ForMember(p => p.UsuarioCadastro, opts => opts.MapFrom(c => c.UsuarioCriacao))
                .ForMember(p => p.QuantidadeTags, opts => opts.MapFrom(c => c.TagsRemessa.Count))
                .ForMember(p => p.NomeEmpresa, opts => opts.MapFrom(c => c.RazaoSocialEmpresa))
                .ForMember(p => p.DataBaixa, opts => opts.MapFrom(c => c.DataBaixa.HasValue ? c.DataBaixa.Value.ToString("dd/MM/yyyy HH:mm:ss") : null))
                .ForMember(p => p.Status, opts => opts.MapFrom(c => c.Status.ToString()))
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.Id, opts => opts.MapFrom(c => c.Id));

            CreateMap<RemessaGetModelResponse, ConsultarRemessaResponse>()
                .ForMember(p => p.Remessa, opts => opts.MapFrom(c => c.TagsRemessa))
                .ForMember(p => p.IdEmpresa, opts => opts.MapFrom(c => c.IdEmpresa))
                .ForMember(p => p.NomeEmpresa, opts => opts.MapFrom(c => c.RazaoSocialEmpresa))
                .ForMember(p => p.DataBaixa, opts => opts.MapFrom(c => c.DataBaixa.HasValue ? c.DataBaixa.Value.ToString("dd/MM/yyyy HH:mm:ss") : null))
                .ForMember(p => p.Status, opts => opts.MapFrom(c => c.Status.ToString()))
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.IdRemessa, opts => opts.MapFrom(c => c.Id));

            CreateMap<RemessaTagsModelResponse,ConsultarRemessaTagResponse>()
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.SerialNumber, opts => opts.MapFrom(c => c.Serial));

            CreateMap<TagGetModelResponse, TagReduzidaResponse>()
                .ForMember(p => p.SerialNumber, opts => opts.MapFrom(c => c.SerialNumber))
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")));

            CreateMap<TagGetModelResponse, GridTagItemResponse>()
                .ForMember(p => p.DataVinculo, opts => opts.MapFrom(c =>
                    c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._1) == null ? null : c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._1).DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.DataCancelamento, opts => opts.MapFrom(c =>
                    c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._5) == null ? null : c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._5).DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.UsuarioVinculo, opts => opts.MapFrom(c =>
                    c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._1) == null ? null : c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._1).UsuarioCriacao))
                .ForMember(p => p.UsuarioCancelamento, opts => opts.MapFrom(c =>
                    c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._5) == null ? null : c.Eventos.FirstOrDefault(x => x.Evento == EEventoTag._5).UsuarioCriacao))
                .ForMember(p => p.UsuarioCadastro, opts => opts.MapFrom(c => c.UsuarioCriacao))
                .ForMember(p => p.NomeEmpresa, opts => opts.MapFrom(c => c.RazaoSocial))
                .ForMember(p => p.SerialNumber, opts => opts.MapFrom(c => c.SerialNumber))
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")));

            CreateMap<VeiculoGetModelResponse, VeiculoTagResponse>()
                .ForMember(p => p.Desbloqueado, opts => opts.MapFrom(c => c.Desbloqueado))
                .ForMember(p => p.ModeloDescricao, opts => opts.MapFrom(c => c.ModeloDescricao))
                .ForMember(p => p.Id, opts => opts.MapFrom(c => c.Id))
                .ForMember(p => p.TagId, opts => opts.MapFrom(c => c.TagId))
                .ForMember(p => p.Serial, opts => opts.MapFrom(c => c.Serial))
                .ForMember(p => p.Status, opts => opts.MapFrom(c => c.Status))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Placa));

            CreateMap<ModelosVeiculoMoveMaisItemGetModelResponse, ModeloMoveMaisItem>()
                .ForMember(p => p.Id, opts => opts.MapFrom(c => c.Id))
                .ForMember(p => p.Descricao, opts => opts.MapFrom(c => c.Descricao));

            CreateMap<SalvarTagRequest, TagVincularModelRequest>()
                .ForMember(p => p.ModeloId, opts => opts.MapFrom(c => c.ModeloId));

            CreateMap<VincularTagRequestModel, SalvarTagRequest>()
                .ForMember(p => p.ModeloId, opts => opts.MapFrom(c => c.ModeloId));

            CreateMap<BloqueioGetModelResponse, BloqueiosTagUsuarioResponse>()
                .ForMember(p => p.BloqueiosTag, opts => opts.MapFrom(c => c.Bloqueios));

            CreateMap<BloqueioGetModelResponseItem, BloqueiosTagUsuarioItemResponse>();

            CreateMap<GridValePedagioHubItemModeResponse, GridValePedagioHubItemResponse>()
                .ForMember(p => p.RodagemDupla, opts => opts.MapFrom(c => 
                    c.RodagemDupla.HasValue 
                        ? (c.RodagemDupla.Value 
                            ? "Sim" 
                            : "Não") 
                        : string.Empty))
                .ForMember(p => p.Id, opts => opts.MapFrom(c => c.CompraId))
                .ForMember(p => p.Valor,
                    opts => opts.MapFrom(c =>
                        c.Valor.HasValue ? c.Valor.Value.ToString("C") : string.Empty))
                .ForMember(p => p.Placa,
                    opts => opts.MapFrom(c =>
                        c.Placa.ToPlacaFormato()))
                .ForMember(p => p.Cnpj,
                    opts => opts.MapFrom(c =>
                        c.Cnpj.ToCNPJFormato()))
                .ForMember(p => p.DataEmissao,
                    opts => opts.MapFrom(c =>
                        c.DataEmissao.HasValue ? c.DataEmissao.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty));

            CreateMap<PagamentosItemGetModelResponse, PagamentosItemTagResponse>()
                .ForMember(p => p.ValorDecimal,
                    opts => opts.MapFrom(c =>
                        c.Valor))
                .ForMember(p => p.TaxaDecimal,
                    opts => opts.MapFrom(c =>
                        c.Taxa))
                .ForMember(p => p.Valor,
                                    opts => opts.MapFrom(c =>
                                        c.Valor.HasValue ? c.Valor.Value.ToString("C") : string.Empty))
                .ForMember(p => p.Taxa,
                                    opts => opts.MapFrom(c =>
                                        c.Taxa.HasValue ? c.Taxa.Value.ToString("C") : string.Empty))
                .ForMember(p => p.Placa,
                    opts => opts.MapFrom(c =>
                        c.Placa.ToPlacaFormato()))
                .ForMember(p => p.Cnpj,
                    opts => opts.MapFrom(c =>
                        c.Cnpj.ToCNPJFormato()))
                .ForMember(p => p.DataEmissao,
                    opts => opts.MapFrom(c =>
                        c.DataEmissao.HasValue ? c.DataEmissao.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty))
                .ForMember(p => p.DataCadastroCargaAvulsaTaxa,
                    opts => opts.MapFrom(c =>
                        c.DataCadastroCargaAvulsaTaxa.HasValue ? c.DataEmissao.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty))
                .ForMember(p => p.DataCadastroCargaAvulsaValor,
                opts => opts.MapFrom(c =>
                    c.DataCadastroCargaAvulsaValor.HasValue ? c.DataEmissao.Value.ToString("dd/MM/yyyy HH:mm:ss") : string.Empty));

            CreateMap<PassagensPedagioCompraHubGetModelResponse, PracasPedagioResponseDTO>()
                .ForMember(p => p.Pracas, opts => opts.MapFrom(c => c.Praca))
                .ForMember(p => p.ValorTotalEstimado,
                    opts => opts.MapFrom(c => c.Praca.Sum(x => x.PrecoEstimado).Value.ToString("C")))
                .ForMember(p => p.ValorTotalPassagem,
                    opts => opts.MapFrom(c => c.Praca.Sum(x => x.PrecoPassagem).Value.ToString("C")));

            CreateMap<ItemPracasCompraPedagioResponse, PracasPedagioItemResponseDTO>()
                .ForMember(p => p.PrecoEstimado,
                    opts => opts.MapFrom(c =>
                        c.PrecoEstimado.HasValue ? c.PrecoEstimado.Value.ToString("C") : string.Empty))
                .ForMember(p => p.PrecoPassagem,
                    opts => opts.MapFrom(c =>
                        c.PrecoPassagem.HasValue ? c.PrecoPassagem.Value.ToString("C") : string.Empty))
                .ForMember(p => p.DataPassagem,
                    opts => opts.MapFrom(c =>
                        c.DataPassagem.HasValue ? c.DataPassagem.Value.ToString() : string.Empty));
            
            CreateMap<ItemPracasCompraPedagioModelResponse, PracasPedagioItemResponseDTO>()
                .ForMember(p => p.PrecoEstimado,
                    opts => opts.MapFrom(c =>
                        c.PrecoEstimado.HasValue ? c.PrecoEstimado.Value.ToString("C") : string.Empty))
                .ForMember(p => p.PrecoPassagem,
                    opts => opts.MapFrom(c =>
                        c.PrecoPassagem.HasValue ? c.PrecoPassagem.Value.ToString("C") : string.Empty))
                .ForMember(p => p.DataPassagem,
                    opts => opts.MapFrom(c =>
                        c.DataPassagem.HasValue ? c.DataPassagem.Value.ToString() : string.Empty));
            
            CreateMap<PagamentosItemGetModelResponse, ConsultarPagamentoResponse>()
                .ForMember(p => p.ValorDecimal,
                    opts => opts.MapFrom(c => c.Valor))
                .ForMember(p => p.TaxaDecimal,
                    opts => opts.MapFrom(c => c.Taxa))
                .ForMember(p => p.Valor,
                    opts => opts.MapFrom(c =>
                        c.Valor.HasValue ? c.Valor.Value.ToString("C") : string.Empty))
                .ForMember(p => p.Taxa,
                    opts => opts.MapFrom(c =>
                        c.Taxa.HasValue ? c.Taxa.Value.ToString("C") : string.Empty));

            CreateMap<PagamentoTagRequest, PagamentoManualRequest>()
                .ForMember(p => p.Tipo, opts => opts.MapFrom(c => c.Tipo));

            CreateMap<EstornoTagRequest, PagamentoManualRequest>()
                .ForMember(p => p.Tipo, opts => opts.MapFrom(c => c.Tipo));
            
            CreateMap<FaturamentoGetItemModelResponse, FaturamentoTagItemResponse>()
                .ForMember(p => p.Cnpj,
                    opts => opts.MapFrom(c => 
                        c.Cnpj.ToCNPJFormato()))
                .ForMember(p => p.ValorTotalPagoValePedagio,
                    opts => opts.MapFrom(c => 
                        c.ValorTotalPagoValePedagio.ToString("C")))
                .ForMember(p => p.ValorTotalNaoPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalNaoPagoValePedagio.ToString("C")))
                .ForMember(p => p.ValorEstornoTotalPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorEstornoTotalPagoValePedagio.ToString("C")))
                .ForMember(p => p.ValorEstornoTotalNaoPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorEstornoTotalNaoPagoValePedagio.ToString("C")))
                .ForMember(p => p.ValorTotalPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalPagoPedagio.ToString("C")))
                .ForMember(p => p.ValorTotalNaoPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalNaoPagoPedagio.ToString("C")))
                .ForMember(p => p.ValorEstornoTotalPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorEstornoTotalPagoPedagio.ToString("C")))
                .ForMember(p => p.ValorEstornoTotalNaoPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.ValorEstornoTotalNaoPagoPedagio.ToString("C")))
                .ForMember(p => p.TaxaTotalPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaTotalPagoValePedagio.ToString("C")))
                .ForMember(p => p.TaxaTotalNaoPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaTotalNaoPagoValePedagio.ToString("C")))
                .ForMember(p => p.TaxaEstornoTotalPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaEstornoTotalPagoValePedagio.ToString("C")))
                .ForMember(p => p.TaxaEstornoTotalNaoPagoValePedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaEstornoTotalNaoPagoValePedagio.ToString("C")))
                .ForMember(p => p.TaxaTotalPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaTotalPagoPedagio.ToString("C")))
                .ForMember(p => p.TaxaTotalNaoPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaTotalNaoPagoPedagio.ToString("C")))
                .ForMember(p => p.TaxaEstornoTotalPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaEstornoTotalPagoPedagio.ToString("C")))
                .ForMember(p => p.TaxaEstornoTotalNaoPagoPedagio,
                    opts => opts.MapFrom(c =>
                        c.TaxaEstornoTotalNaoPagoPedagio.ToString("C")))
                .ForMember(p => p.ValorTotal,
                    opts => opts.MapFrom(c =>
                       c.ValorTotal.ToString("C")));

            CreateMap<FaturamentoTotalizadorGetModelResponse, FaturamentoTotalizadorResponse>()
                .ForMember(p => p.ValorTotalEstornoProvisionado,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalEstornoProvisionado.ToString("C")))
                .ForMember(p => p.ValorTotalEstornoNaoProvisionado,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalEstornoNaoProvisionado.ToString("C")))
                .ForMember(p => p.ValorTotalProvisionado,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalProvisionado.ToString("C")))
                .ForMember(p => p.ValorTotalNaoProvisionado,
                    opts => opts.MapFrom(c =>
                        c.ValorTotalNaoProvisionado.ToString("C")))
                .ForMember(p => p.ValorTotal,
                    opts => opts.MapFrom(c =>
                        c.ValorTotal.ToString("C")));


            #endregion

            #region Cartão

            CreateMap<QueryFilters, SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilter>()
                .ForMember(p => p.Field, opts => opts.MapFrom(c => c.Campo))
                .ForMember(p => p.Operator, opts => opts.MapFrom(c => EOperadorResolver(c.Operador)))
                .ForMember(p => p.Value, opts => opts.MapFrom(c => c.Valor))
                .ForMember(p => p.FieldType, opts => opts.MapFrom(c => EFieldTipoResolver(c.CampoTipo)))
                .ForMember(p => p.ServerFieldCollection, opts => opts.MapFrom(c => c.ServerFieldCollection));

            CreateMap<ConsultarExtratoDetalheResponse, ConsultarExtratoDetalheResponseDTO>();
            CreateMap<ConsultarExtratoResponse, ObjetoExtratoResponseDTO>()
                .ForMember(p => p.SaldoInicialPeriodo, opts => opts.MapFrom(c => c.SaldoInicialPeriodo))
                .ForMember(p => p.SaldoFinalPeriodo, opts => opts.MapFrom(c => c.SaldoFinalPeriodo))
                .ForMember(p => p.Detalhes, opts => opts.MapFrom(c => c.Detalhes));

            CreateMap<ConsultarExtratoResponse, ConsultarExtratoResponseDTO>()
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c))
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == ConsultarExtratoResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<ConsultarSaldoCartaoResponse, ObjetoSaldoCartaoResponseDTO>()
                .ForMember(p => p.ValorLimiteCredito, opts => opts.MapFrom(c => c.ValorLimiteCredito))
                .ForMember(p => p.ValorSaldoDisponivel, opts => opts.MapFrom(c => c.ValorSaldoDisponivel));

            CreateMap<ConsultarSaldoCartaoResponse, ConsultarSaldoCartaoResponseDTO>()
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c))
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == ConsultarSaldoCartaoResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<ProdutoResponse, ProdutoResponseDto>();
            CreateMap<CartaoVinculadoPessoaResponse, CartaoVinculadoPessoaResponseDto>()
                .ForMember(p => p.Produto, opts => opts.MapFrom(c => c.Produto));
            CreateMap<CartaoBloqueadoPessoaResponse, CartaoBloqueadoPessoaResponseDto>()
                .ForMember(p => p.Produto, opts => opts.MapFrom(c => c.Produto));
            CreateMap<CartaoVinculadoPessoaListResponse, CartaoVinculadoPessoaListResponseDto>()
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c.Cartoes))
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == CartaoVinculadoPessoaListResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));
            
            CreateMap<CartaoBloqueadoPessoaListResponse, CartaoBloqueadoPessoaListResponseDto>()
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c.Cartoes))
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == CartaoBloqueadoPessoaListResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<MotivoBloqueioModel, MotivoBloqueioCartaoDto>();
            CreateMap<HistoricoCartaoPessoaResponse, HistoricoCartaoPessoaResponseDto>()
                .ForMember(p => p.Produto, opts => opts.MapFrom(c => c.Produto))
                .ForMember(p => p.MotivoBloqueio, opts => opts.MapFrom(c => c.MotivoBloqueio));
            CreateMap<HistoricoCartaoPessoaListResponse, HistoricoCartaoPessoaListResponseDto>()
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c.Cartoes))
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == HistoricoCartaoPessoaListResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<PessoaContaBancariaResponse, PessoaContaBancariaResponseDTO>();
            CreateMap<ConsultarContasBancariasResponse, ConsultarContasBancariasResponseDTO>()
                .ForMember(p => p.Sucesso, opts => opts.MapFrom(c => c.Status == ConsultarContasBancariasResponseStatus.Sucesso))
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c.ContasBancarias));

            CreateMap<InativarContaBancariaResponse, InativarContaBancariaAtsResponseDTO>()
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == InativarContaBancariaResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p=> p.Mensagem, opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));
            
            CreateMap<ValidarSenhaCartaoResponse, ValidarSenhaCartaoResponseDto>()
                .ForMember(p=> p.Sucesso, opts => opts.MapFrom(c => c.Status == ValidarSenhaCartaoResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p=> p.Mensagem, opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<IntegrarPessoaTipoFlagsRequestDTO, IntegrarPessoaTipoFlagsRequest>();
            CreateMap<IntegrarPessoaInfoRequestDTO, IntegrarPessoaInfoRequest>();
            CreateMap<IntegrarPessoaEnderecoRequestDTO, IntegrarPessoaEnderecoRequest>()
                .ForMember(p => p.Numero, opts => opts.MapFrom(c => c.Numero.ToString()));
            CreateMap<IntegrarPessoaRequestDTO, IntegrarPessoaRequest>()
                .ForMember(p => p.Endereco, opts => opts.MapFrom(c => c.Endereco))
                .ForMember(p => p.Info, opts => opts.MapFrom(c => c.Info))
                .ForMember(p => p.Flags, opts => opts.MapFrom(c => c.Flags));

            CreateMap<ResgatarCartaoResponseDTO, ResgateCartaoAtendimento>();

            CreateMap<ConsultarEmpresaResponse, ConsultaDetalhesEmpresaMeioHomologadoReponseDTO>()
                .ForMember(p => p.Sucesso,
                    opts => opts.MapFrom(c => c.Status == ConsultarEmpresaResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p => p.Mensagem,
                    opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)));

            CreateMap<GetOrGenerateCartaoAppTokenApiResponse, EmpresaTokenMicroServicoDto>()
                .ForMember(p=> p.Sucesso, opts => opts.MapFrom(c => c.Status == GetOrGenerateCartaoAppTokenApiResponseStatus.Sucesso && c.ProcessingStateOnServer.State == ApiProcessingStateOnServerState.Ok))
                .ForMember(p=> p.Mensagem, opts => opts.MapFrom(c => c.Mensagem != null && c.Mensagem != string.Empty ? c.Mensagem : (c.ProcessingStateOnServer != null ? c.ProcessingStateOnServer.ErrorMessage : string.Empty)))
                .ForMember(p=> p.IdEmpresa, opts => opts.Ignore());

            CreateMap<ReciboTransferenciaDto, ReciboTransferenciaDataType>()
                .ForMember(r => r.CpfCnpjOrigem,
                    opts => opts.MapFrom(x => x.CpfCnpjOrigem.FormatarCpfCnpjSafe()))
                .ForMember(r => r.CpfCnpjDestino,
                    opts => opts.MapFrom(x => x.CpfCnpjDestino.FormatarCpfCnpjSafe()))
                .ForMember(r => r.DataAtual,
                    opts => opts.MapFrom(x => DateTime.Now.ToString("dd/MM/yyyy")));

            #region Conciliação de transações

            CreateMap<Viagem, RelatorioConciliacaoAtsDto>()
                .ForMember(p => p.DataCompraPedagio, opts => opts.MapFrom(c => c.DataConfirmacaoCreditoPedagio ?? c.DataConfirmacaoPedagio))
                .ForMember(p => p.DataCancelamentoPedagio, opts => opts.MapFrom(c => c.DataConfirmacaoEstornoPedagio ?? c.DataCancelamentoPedagio))
                .ForMember(p => p.DataConfirmacaoCompraPedagio, opts => opts.MapFrom(c => c.DataConfirmacaoCreditoPedagio))
                .ForMember(p => p.DataConfirmacaoCancelamentoPedagio, opts => opts.MapFrom(c => c.DataConfirmacaoEstornoPedagio))
                .ForMember(p => p.NumeroRecibo, opts => opts.MapFrom(c => c.DocumentoCliente))
                .ForMember(p => p.NumeroCiot, opts => opts.MapFrom(c => c.DeclaracaoCiot.Ciot))
                .ForMember(p => p.VerificadorCiot, opts => opts.MapFrom(c => c.DeclaracaoCiot.Verificador))
                .ForMember(p => p.Filial, opts => opts.MapFrom(c => c.Filial.NomeFantasia))
                .ForMember(p => p.IdViagem, opts => opts.MapFrom(c => c.IdViagem));

            CreateMap<TransacaoCartao, RelatorioConciliacaoAtsDto>()
                .ForMember(p => p.ValorCartao, opts => opts.MapFrom(c => c.ValorMovimentado))
                .ForMember(p => p.DataCartao, opts => opts.MapFrom(c => c.DataConfirmacaoMeioHomologado ?? c.DataCriacao))
                .ForMember(p => p.DataConfirmacaoCartao, opts => opts.MapFrom(c => c.DataConfirmacaoMeioHomologado))
                .ForMember(p => p.IdViagemEvento, opts => opts.MapFrom(c => c.ViagemEvento.IdViagemEvento))
                .ForMember(p => p.TipoEventoViagem, opts => opts.MapFrom(c => c.ViagemEvento.TipoEventoViagem))
                .ForMember(p => p.NumeroCiot, opts => opts.MapFrom(c => c.ViagemEvento.Viagem.DeclaracaoCiot.Ciot))
                .ForMember(p => p.VerificadorCiot, opts => opts.MapFrom(c => c.ViagemEvento.Viagem.DeclaracaoCiot.Verificador))
                .ForMember(p => p.NomeUsuario, opts => opts.MapFrom(c => c.CargaAvulsa.NomeUsuario))
                .ForMember(p => p.DocumentoUsuario, opts => opts.MapFrom(c => c.CargaAvulsa.CPFCNPJUsuario))
                .ForMember(p => p.NumeroRecibo, opts => opts.MapFrom(c => c.CargaAvulsa.TipoCarga == ETipoCarga.Provisionamento ? c.CargaAvulsa.Viagem.DocumentoCliente : c.CargaAvulsa.NroControleIntegracao ?? c.ViagemEvento.Viagem.DocumentoCliente))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.CargaAvulsa.PlacaCavalo ?? c.ViagemEvento.Viagem.Placa))
                .ForMember(p => p.Filial, opts => opts.MapFrom(c => c.CargaAvulsa.Filial.NomeFantasia ?? c.ViagemEvento.Viagem.Filial.NomeFantasia))
                .ForMember(p => p.TipoCarga, opts => opts.MapFrom(c => c.CargaAvulsa.TipoCarga))
                .ForMember(p => p.IdViagem, opts => opts.MapFrom(c => c.CargaAvulsa.IdViagemProvisionada ?? c.ViagemEvento.IdViagem));

            CreateMap<ViagemEvento, RelatorioConciliacaoAtsDto>()
                .ForMember(p => p.StatusEvento, opts => opts.MapFrom(c => c.Status))
                .ForMember(p => p.ValorCartao, opts => opts.MapFrom(c => c.ValorTotalPagamento))
                .ForMember(p => p.DataCartao, opts => opts.MapFrom(c => c.Status == EStatusViagemEvento.Baixado ? c.DataHoraPagamento : c.DataHoraCancelamento))
                .ForMember(p => p.NumeroRecibo, opts => opts.MapFrom(c => c.Viagem.DocumentoCliente))
                .ForMember(p => p.NumeroCiot, opts => opts.MapFrom(c => c.Viagem.DeclaracaoCiot.Ciot))
                .ForMember(p => p.VerificadorCiot, opts => opts.MapFrom(c => c.Viagem.DeclaracaoCiot.Verificador))
                .ForMember(p => p.Filial, opts => opts.MapFrom(c => c.Viagem.Filial.NomeFantasia))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Viagem.Placa))
                .ForMember(p => p.IdViagem, opts => opts.MapFrom(c => c.Viagem.IdViagem));

            CreateMap<RelatorioConciliacaoDto, RelatorioConciliacaoAnaliticoTransacaoDataType>()
                .ForMember(p => p.ProtocoloRequisicaoPedagio, opts => opts.MapFrom(c => c.DadosAts.IdViagem))
                .ForMember(p => p.NomePortador, opts => opts.MapFrom(c => c.DadosMeioHomologado.NomePortador))
                .ForMember(p => p.DocumentoPortador,
                    opts => opts.MapFrom(c => c.DadosMeioHomologado.DocumentoPortador.ToCpfOrCnpj()))
                .ForMember(p => p.NumeroCartao, opts => opts.MapFrom(c => c.DadosMeioHomologado.Cartao.Identificador))
                .ForMember(p => p.TipoTransacao, opts => opts.MapFrom(c => c.DadosMeioHomologado.TipoProcessadora))
                .ForMember(p => p.Informacoes,
                    opts => opts.MapFrom(c =>
                        c.DadosMeioHomologado.Informacoes != null && c.DadosMeioHomologado.Informacoes != string.Empty
                            ? c.DadosMeioHomologado.Informacoes
                            : c.DadosAts.MotivoAts))
                .ForMember(p => p.Conta, opts => opts.MapFrom(c => c.DadosMeioHomologado.Cartao.Conta))

                //O ProtocoloRequisicaoPedagio no MH é o IdViagem, quando ocorre um cancelamento para não duplicar o protocolo é utilizado o valor negativo
                .ForMember(p => p.ProtocoloRequisicaoPedagio,
                    opts => opts.MapFrom(c =>
                        c.DadosMeioHomologado.TipoProcessadora == ConciliacaoItemTipoProcessadora.EstornoPedagioPortador
                            ? c.DadosAts.IdViagem * -1
                            : c.DadosAts.IdViagem))
                .ForMember(p => p.ProtocoloRequisicao, opts => opts.MapFrom(c => c.DadosAts.IdTransacaoCartao))
                .ForMember(p => p.NumeroRecibo, opts => opts.MapFrom(c => c.DadosAts.NumeroRecibo))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.DadosAts.Placa.ToPlacaFormato()))
                .ForMember(p => p.Filial, opts => opts.MapFrom(c => c.DadosAts.Filial))
                .ForMember(p => p.NomeUsuario, opts => opts.MapFrom(c => c.DadosAts.NomeUsuario))
                .ForMember(p => p.DocumentoUsuario,
                    opts => opts.MapFrom(c => c.DadosAts.DocumentoUsuario.ToCpfOrCnpj()))
                .ForMember(p => p.TipoEventoViagem, opts => opts.MapFrom(c => (int?) c.DadosAts.TipoEventoViagem))
                .ForMember(p => p.IdParaCsv, opts => opts.MapFrom(c => c.DadosAts.IdViagemEvento))
                .ForMember(p => p.Ciot,
                    opts => opts.MapFrom(c =>
                        c.DadosAts.NumeroCiot != null && c.DadosAts.NumeroCiot != string.Empty
                            ? string.Concat(c.DadosAts.NumeroCiot, "/", c.DadosAts.VerificadorCiot)
                            : null))
                .ForMember(p => p.Valor, opts => opts.MapFrom(c => c.ValorDecimal.ToString("C")))
                .ForMember(p => p.Data,
                    opts => opts.MapFrom(c =>
                        c.DataDt.HasValue ? c.DataDt.Value.ToString("dd/MM/yyyy HH:mm:ss") : null))
                .ForMember(p => p.ConciliacaoItem,
                    opts => opts.MapFrom(c =>
                        c.DadosMeioHomologado))
                .ForMember(p => p.IdCargaAvulsa,
                    opts => opts.MapFrom(c =>
                        c.DadosAts.IdCargaAvulsa));

            #endregion

            #region Portado de cartão

            CreateMap<ConsultarPessoaDetalhadaResponse, AtsPortadorRequest>()
                .ForMember(p => p.Documento, opts => opts.MapFrom(c => c.CpfCnpj))
                .ForMember(p => p.SexoFormatado, opts => opts.MapFrom(c => c.Sexo == "M" ? "Masculino" : "Feminino"))
                .ForMember(p => p.DataNascimentoFormatado, opts => opts.MapFrom(c => c.DataNascimento.FormatDateBr()))
                .ForMember(p => p.TipoPessoaFormatado, opts => opts.MapFrom(c => c.CpfCnpj.Length == 11 ? "Física" : "Jurídica"))
                .ForMember(p => p.Contato, opts => opts.MapFrom(c => c.Celular != null && c.Celular != string.Empty ? c.Celular : c.Telefone));

            #endregion

            #endregion

            #region Cadastros

            CreateMap<ConsultarBancoResponse, BancoDTO>();
            CreateMap<ConsultarListaBancosResponse, ConsultarBancoResponseDTO>()
                .ForMember(p => p.Sucesso, opts => opts.MapFrom(c => c.Status == ConsultarListaBancosResponseStatus.Sucesso))
                .ForMember(p => p.Objeto, opts => opts.MapFrom(c => c.Bancos));

            #endregion

            #region Permissões Mobile

            CreateMap<UsuarioPermissoesConcedidasMobile, UsuarioPermissoesConcedidasMobileModel>();

            #endregion
            
            #region Tipo Cavalo

            CreateMap<TipoCavalo, TipoCavaloEditarResponse>()
                .ForMember(o => o.NomeFantasiaEmpresa, opt => opt.MapFrom(o => o.Empresa != null ? o.Empresa.NomeFantasia : null));

            #endregion
            
            #region Campanha

            CreateMap<Campanha, CampanhaConsultaResponse>();
            CreateMap<CampanhaResposta, CampanhaRespostaResponse>();

            #endregion
            
            #region Banner

            CreateMap<Banner, BannerConsultarResponse>()
                .ForMember(p => p.NomeUsuarioAtivacao, opts => opts.MapFrom(c => c.UsuarioAtivacao.Nome))
                .ForMember(p => p.NomeUsuarioCadastro, opts => opts.MapFrom(c => c.UsuarioCadastro.Nome))
                .ForMember(p => p.NomeUsuarioDesativacao, opts => opts.MapFrom(c => c.UsuarioDesativacao.Nome))
                .ForMember(p => p.DataCadastroValue, opts => opts.MapFrom(c => c.DataCadastro))
                .ForMember(p => p.DataCadastro, opts => opts.Ignore())
                .ForMember(p => p.DataAtivacaoValue, opts => opts.MapFrom(c => c.DataAtivacao))
                .ForMember(p => p.DataAtivacao, opts => opts.Ignore())
                .ForMember(p => p.DataDesativacaoValue, opts => opts.MapFrom(c => c.DataDesativacao))
                .ForMember(p => p.DataDesativacao, opts => opts.Ignore());
            
            CreateMap<CampanhaResposta, CampanhaRespostaResponse>();

            #endregion

            #region PermissaoCartao
            CreateMap<ConsultarPermissaoModelResponse, PermissaoCartaoModelResponse>();
            #endregion

            CreateMap<PassagemPracaGetModelResponse, GridPassagemWebhookItemResponse>()
                .ForMember(p => p.Valor, opts => opts.MapFrom(c => c.Amount.FormatMoney()))
                .ForMember(p => p.CnpjEmpresa, opts => opts.MapFrom(c => c.CnpjEmpresa.FormatarCpfCnpjSafe()))
                .ForMember(p => p.CnpjEmissorVp, opts => opts.MapFrom(c => c.CnpjEmissorVp.FormatarCpfCnpjSafe()))
                .ForMember(p => p.Categoria, opts => opts.MapFrom(c => c.CategoryAxesName))
                .ForMember(p => p.Tipo, opts => opts.MapFrom(c => c.Historic))
                .ForMember(p => p.Lancamento, opts => opts.MapFrom(c => c.Description))
                .ForMember(p => p.DataOcorrencia, opts => opts.MapFrom(c => c.OccurrenceDate.Value.ToString("dd/MM/yyyy HH:mm")))
                .ForMember(p => p.DataProcessamento, opts => opts.MapFrom(c => c.InsertDate.Value.ToString("dd/MM/yyyy HH:mm")))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Plate.ToPlacaFormato()));


            CreateMap<FaturamentoGridDto, FaturamentoItemGridModelResponse>()
                .ForMember(p => p.Adiantamento, opts => opts.MapFrom(c => c.Adiantamento.FormatMoney()))
                .ForMember(p => p.Saldo, opts => opts.MapFrom(c => c.Saldo.FormatMoney()))
                .ForMember(p => p.TarifaANTT, opts => opts.MapFrom(c => c.TarifaANTT.FormatMoney()))
                .ForMember(p => p.Estadias, opts => opts.MapFrom(c => c.Estadias.FormatMoney()))
                .ForMember(p => p.Abastecimento, opts => opts.MapFrom(c => c.Abastecimento.FormatMoney()))
                .ForMember(p => p.Pedagio, opts => opts.MapFrom(c => c.Pedagio.FormatMoney()))
                .ForMember(p => p.CargaAvulsa, opts => opts.MapFrom(c => c.CargaAvulsa.FormatMoney()))
                .ForMember(p => p.Total, opts => opts.MapFrom(c => c.Total.FormatMoney()))
                .ForMember(p => p.Estornos, opts => opts.MapFrom(c => c.Estornos.FormatMoney()));

            #region Pix

            CreateMap<TransferenciaPixGridTimelineResponse, TransferenciaPixGridResponse>()
                .ForMember(p => p.totalItems, opts => opts.MapFrom(c => c.TotalItems))
                .ForMember(p => p.totalPagamento, opts => opts.MapFrom(c => c.TotalPagamento.ToString("C2", new CultureInfo("pt-br"))))
                .ForMember(p => p.totalRecebimento, opts => opts.MapFrom(c => c.TotalRecebimento.ToString("C2", new CultureInfo("pt-br"))))
                .ForMember(p => p.totalMovimentado, opts => opts.MapFrom(c => c.TotalMovimentado.ToString("C2", new CultureInfo("pt-br"))));
            CreateMap<ConsultarTimelinePixAppResponseItem, TransferenciaPixGridResponseItem>();

            #endregion
            

        }

        #region Métodos privados

        private string GetAvisoContratoAgregado(ContratoCiotAgregado c)
        {
            switch (c.Status)
            {
                case EStatusContratoAgregado.Vigente:
                    if (!c.DataInicio.HasValue || !c.DataFinal.HasValue)
                        return null;
                    
                    if (!c.IdDeclaracaoCiot.HasValue)
                        return null;

                    var dataHoje = DateTime.Now.StartOfDay();

                    if (!c.DeclaracaoCiot.ViagensVinculadas.Any() || c.DeclaracaoCiot.ViagensVinculadas.All(v => v.StatusViagem == EStatusViagem.Cancelada))
                    {
                        var diasParaCancelar = (c.DataInicio.Value.AddDays(5) - dataHoje).Days;
                        if (diasParaCancelar > 1)
                            return $"CIOT será cancelado automaticamente em {diasParaCancelar} dias";
                        
                        if (diasParaCancelar == 1)
                            return $"CIOT será cancelado automaticamente em 1 dia";
                        
                        if(diasParaCancelar == 0)
                            return $"CIOT será cancelado automaticamente hoje";
                    }

                    if (c.DeclaracaoCiot.ViagensVinculadas.Any(v => v.StatusViagem != EStatusViagem.Cancelada) && c.DataFinal < dataHoje)
                    {
                        var diasParaEncerrar = (c.DataFinal.Value.AddDays(30) - dataHoje).Days;
                        if (diasParaEncerrar > 1)
                            return $"CIOT será encerrado automaticamente em {diasParaEncerrar} dias";
                        
                        if (diasParaEncerrar == 1)
                            return $"CIOT será encerrado automaticamente em 1 dia";
                        
                        if(diasParaEncerrar == 0)
                            return $"CIOT será encerrado automaticamente hoje";
                    }
                    
                    break;
                case EStatusContratoAgregado.Encerrado:
                case EStatusContratoAgregado.Cancelado:
                    return null;
            }

            return null;
        }

        private string GetStatusContratoAgregado(ContratoCiotAgregado c)
        {
            switch (c.ResultadoDeclaracaoCiot)
            {
                case EResultadoDeclaracaoCiot.Sucesso:
            
                    switch (c.Status)
                    {
                        case EStatusContratoAgregado.Vigente:
                            if (c.DeclaracaoCiot != null)
                            {
                                if (!c.DeclaracaoCiot.ViagensVinculadas.Any() || c.DeclaracaoCiot.ViagensVinculadas.All(v => v.StatusViagem == EStatusViagem.Cancelada))
                                    return EStatusContratoAgregado.AguardandoViagem.GetDescription();
                            }
                            
                            if (c.DataFinal >= DateTime.Now.StartOfDay())
                                return EStatusContratoAgregado.Vigente.GetDescription();
            
                            if (c.DataFinal < DateTime.Now.StartOfDay())
                                return EStatusContratoAgregado.AguardandoEncerramento.GetDescription();
            
                            return "Inesperado";
            
                        case EStatusContratoAgregado.Encerrado:
                            return EStatusContratoAgregado.Encerrado.GetDescription();
                        
                        case EStatusContratoAgregado.Cancelado:
                            if (c.CanceladoAutomaticamente)
                                return EStatusContratoAgregado.CanceladoAutomaticamente.GetDescription();
            
                            return EStatusContratoAgregado.Cancelado.GetDescription();
                    }
                            
                    break;
                        
                case EResultadoDeclaracaoCiot.Erro:
                case EResultadoDeclaracaoCiot.NaoHabilitado:
                    switch (c.Status)
                    {
                        case EStatusContratoAgregado.Cancelado:
                            return !c.CanceladoAutomaticamente 
                                ? EStatusContratoAgregado.Cancelado.GetDescription()
                                : EStatusContratoAgregado.CanceladoAutomaticamente.GetDescription();
                        default:
                            return EStatusContratoAgregado.NaoAprovado.GetDescription();
                    }
                default:
            
                    return "Inesperado";
            }
            
            return "Inesperado";
        }
        
        private CustomFilterOperator? EOperadorResolver(EOperador operador)
        {
            if (operador == EOperador.StartsWith)
                return CustomFilterOperator.StartsWith;
            if (operador == EOperador.EndsWith)
                return CustomFilterOperator.EndsWith;

            if (operador == EOperador.Contains)
                return CustomFilterOperator.Contains;

            if (operador == EOperador.Exact)
                return CustomFilterOperator.Equals;
            if (operador == EOperador.NotEqual)
                return CustomFilterOperator.NotEquals;

            if (operador == EOperador.GreaterThan)
                return CustomFilterOperator.GreaterThan;
            if (operador == EOperador.GreaterThanOrEqual)
                return CustomFilterOperator.GreaterThanOrEqual;
            if (operador == EOperador.LessThan)
                return CustomFilterOperator.LessThan;
            if (operador == EOperador.LessThanOrEqual)
                return CustomFilterOperator.LessThanOrEqual;

            return null;
        }

        private CustomFilterFieldType EFieldTipoResolver(EFieldTipo tipo)
        {
            if (tipo == EFieldTipo.Date)
                return CustomFilterFieldType.Date;

            if (tipo == EFieldTipo.Number)
                return CustomFilterFieldType.Number;

            if (tipo == EFieldTipo.String)
                return CustomFilterFieldType.String;

            if (tipo == EFieldTipo.Intervalo)
                return CustomFilterFieldType.Intervalo;

            return CustomFilterFieldType.Auto;
        }

        private DateTime? GetValidadeCnhMotorista(string cpf)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var usuarioApp = scope.Resolve<IUsuarioApp>();
                var mot = usuarioApp.GetDocumentoCnhPorCpfMot(cpf);
                return mot.Validade;
            }
        }

        private string GetIconeTipoEstabelecimentoCacheable(string tokenIcone)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var dataMediaServerApp = scope.Resolve<IDataMediaServerApp>();
                return dataMediaServerApp.GetIconeTipoEstabelecimentoCacheable(tokenIcone);
            }
        } 

        #endregion
    }
}
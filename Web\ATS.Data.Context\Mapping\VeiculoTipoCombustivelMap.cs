﻿using System.ComponentModel.DataAnnotations.Schema;
using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class VeiculoTipoCombustivelMap : EntityTypeConfiguration<VeiculoTipoCombustivel>
    {
        public VeiculoTipoCombustivelMap()
        {
            ToTable("VEICULO_TIPO_COMBUSTIVEL");

            HasKey(t => new { t.IdVeiculo, t.IdTipoCombustivel });

            Property(t => t.IdVeiculo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdTipoCombustivel)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdVeiculo)
                .IsRequired();

            Property(t => t.IdTipoCombustivel)
                .IsRequired();

            HasRequired(a => a.Veiculo)
                .WithMany(b => b.TipoCombustiveis)
                .HasForeignKey(c => c.IdVeiculo);

            HasRequired(a => a.TipoCombustivel)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => c.IdTipoCombustivel);
        }
    }
}
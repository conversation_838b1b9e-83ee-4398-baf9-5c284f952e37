﻿using System;
using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class CteApp : AppBase, ICteApp
    {
        private readonly ICteService _cteService;

        public CteApp(ICteService cteService)
        {
            _cteService = cteService;
        }

        public IQueryable<Cte> Consultar(string cpfMotorista, DateTime dataInicial, DateTime? dataFinal)
        {
            return _cteService.ConsultaCte( cpfMotorista, dataInicial, dataFinal);
        }

        public int GetTotalCtes(string cPFCNPJUsuario)
        {
            return _cteService.GetTotalCtes(cPFCNPJUsuario);
        }

        public Cte ConsultaPorId(int idCte)
        {
            return _cteService.ConsultaPorId(idCte); 
        }

        public ValidationResult Update(Cte cte)
        {
            try
            {
                _cteService.Update(cte);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Add(Cte cte)
        {
            try
            {
                return _cteService.Add(cte);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

        }

    }
}

﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class LocalizacaoUsuarioPortalMap : EntityTypeConfiguration<LocalizacaoUsuarioPortal>
    {
        public LocalizacaoUsuarioPortalMap()
        {
            ToTable("LOCALIZACAO_USUARIO_PORTAL");
            HasKey(x => x.Id);

            Property(t => t.Id).HasColumnName("id").HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.DataCadastro).HasColumnName("datacriacao").IsRequired();
            Property(t => t.IdUsuario).HasColumnName("idusuario").IsRequired();
            Property(t => t.Cidade).HasColumnName("cidade").HasMaxLength(100).IsRequired();
            Property(t => t.Estado).HasColumnName("estado").HasMaxLength(100).IsRequired();
            Property(t => t.Ip).HasColumnName("ip").HasMaxLength(100).IsRequired();
            Property(t => t.Latitude).HasColumnName("latitude").IsRequired();
            Property(t => t.Longitude).HasColumnName("longitude").IsRequired();
            Property(t => t.Provedor).HasColumnName("provedor").HasMaxLength(200).IsRequired();

            HasRequired(c => c.Usuario)
                .WithMany(c => c.LocalizacaoUsuarioPortal)
                .HasForeignKey(c => c.IdUsuario);
        }
    }
}
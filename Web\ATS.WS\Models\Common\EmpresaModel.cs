﻿using Newtonsoft.Json;
using System;

namespace ATS.WS.Models.Common
{
    public class EmpresaModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdEmpresa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJ { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RazaoSocial { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string NomeFantasia { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Ativo { get; set; } = true;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? Latitude { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? Longitude { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? RoteirizarCarga { get; set; } = true;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? ObrigarValorTerceiro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Logo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string EmailSugestoes { get; internal set; }

        #region Endereço

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CEP { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Endereco { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Complemento { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Numero { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Bairro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdCidade { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdEstado { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdPais { get; set; }

        #endregion

        #region Contato

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Telefone { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Email { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Celular { get; set; }

        #endregion

        #region Parâmetros Cooperado

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? PrioridadeCooperadoCargas { get; set; } = false;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? MinutosPrioridade { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public double? RaioCooperado { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TimeSpan PeriodoIniEnvCheckInCooperado { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TimeSpan PeriodoFimEnvCheckInCooperado { get; set; }

        #endregion

        #region Parâmetros Terceiro

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public double? RaioTerceiro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TimeSpan PeriodoIniEnvCheckInTerceiro { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TimeSpan PeriodoFimEnvCheckInTerceiro { get; set; }

        #endregion

        // Referências
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual EmpresaLayoutModel Layout { get; set; }
        
    }
}
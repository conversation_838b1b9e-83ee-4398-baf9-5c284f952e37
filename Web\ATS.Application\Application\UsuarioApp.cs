﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Models.Usuario;
using ATS.Domain.Validation;
using Sistema.Framework.Util.Extension;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Transactions;
using ATS.Application.Helpers;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Helpers;
using AutoMapper;

namespace ATS.Application.Application
{
    public class UsuarioApp : AppBase, IUsuarioApp
    {
        private const string Md5HashPassword = "|2d331cca-f6c0-40c0-bb43-6e32989c2881";
        private readonly IUsuarioService _service;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IGrupoUsuarioApp _grupoUsuarioApp;
        private readonly IUsuarioDocumentoApp _usuarioDocumentoApp;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioClienteService _usuarioClienteService;
        private readonly IMotoristaService _motoristaService;
        private readonly ILayoutApp _layoutApp;
        private readonly IMenuApp _menuApp;
        private readonly KeycloakHelper _keycloak;
        private readonly IUserIdentity _userIdentity;
        private readonly IVeiculoService _veiculoService;
        private readonly IProprietarioService _proprietarioService;
        private readonly ICidadeService _cidadeService;
        private readonly IAutenticacaoAplicacaoService _autenticacaoAplicacaoService;
        private readonly IEmpresaService _empresaService;
        private readonly ITipoDocumentoService _tipoDocumentoService;
        private readonly IFilialService _filialService;
        private readonly IRsaCryptoService _rsaCryptoService;

        public UsuarioApp(IUsuarioRepository usuarioRepository,
            IUsuarioService service,
            IGrupoUsuarioApp grupoUsuarioApp,
            IUsuarioDocumentoApp usuarioDocumentoApp,
            IMotoristaApp motoristaApp,
            IUsuarioClienteService usuarioClienteService,
            IMotoristaService motoristaService,
            ILayoutApp layoutApp,
            IMenuApp menuApp,
            KeycloakHelper keycloak,
            IUserIdentity userIdentity,
            IVeiculoService veiculoService,
            IProprietarioService proprietarioService,
            ICidadeService cidadeService,
            IAutenticacaoAplicacaoService autenticacaoAplicacaoService,
            IEmpresaService empresaService,
            ITipoDocumentoService tipoDocumentoService,
            IFilialService filialService, IRsaCryptoService rsaCryptoService)
        {
            _usuarioRepository = usuarioRepository;
            _service = service;
            _grupoUsuarioApp = grupoUsuarioApp;
            _usuarioDocumentoApp = usuarioDocumentoApp;
            _motoristaApp = motoristaApp;
            _usuarioClienteService = usuarioClienteService;
            _motoristaService = motoristaService;
            _keycloak = keycloak;
            _userIdentity = userIdentity;
            _veiculoService = veiculoService;
            _proprietarioService = proprietarioService;
            _cidadeService = cidadeService;
            _autenticacaoAplicacaoService = autenticacaoAplicacaoService;
            _empresaService = empresaService;
            _tipoDocumentoService = tipoDocumentoService;
            _filialService = filialService;
            _rsaCryptoService = rsaCryptoService;
            _cidadeService = cidadeService;
            _layoutApp = layoutApp;
            _menuApp = menuApp;
        }

        /// <summary>
        /// Alterar a senha do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <param name="senha">Senha</param>
        /// <param name="novasenha">Nova senha</param>
        /// <param name="idUsuarioLogon">Código do usuário logado que esta executando o processo</param>
        /// <param name="verificarSenhaAtual"></param>
        /// <returns></returns>
        public ValidationResult AlterarSenha(int idUsuario, string senha, string novasenha, int idUsuarioLogon, bool verificarSenhaAtual)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _service.UpdateSenha(idUsuario, senha, novasenha,
                        idUsuarioLogon, verificarSenhaAtual);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
                //atualizar o usuario no keycloak
                var usuario = Get(idUsuario);
                var contato = usuario.Contatos.FirstOrDefault();
                var email = contato?.Email ?? string.Empty;
                var tempPass = (usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario);
                _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, novasenha, usuario.Ativo, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                        { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                        { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { usuario.Nome.ToString() } },
                        { "Modified", new List<string> { "updated pw at " + DateTime.Now.ToString() + " by " + idUsuarioLogon.ToString()} },
                    }, false);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult SalvarLocalizacao(LocalizacaoUsuarioPortal localizacao)
        {
            return _service.SalvarLocalizacao(localizacao);
        }

        public bool ExisteLocalizacaoPorUsuarioIp(int idUsuario, string ip)
        {
            return _service.ExisteLocalizacaoPorUsuarioIp(idUsuario, ip);
        }
        public IQueryable<Usuario> Find(Expression<Func<Usuario, bool>> predicate)
        {
            return _service.Find(predicate);
        }

        public UsuarioDocumento GetDocumentoCnhPorCpfMot(string cpfMotorista)
        {
            var usuarioMot = GetPorCNPJCPF(cpfMotorista);
            if (usuarioMot != null)
            {
                var ret = _usuarioDocumentoApp.GetDocCNH(usuarioMot.IdUsuario);
                if (ret == null)
                    return new UsuarioDocumento()
                    {
                        Validade = null
                    };

                return new UsuarioDocumento()
                {
                    Validade = ret.Validade
                };
            }

            var motorista = _motoristaApp.GetPorCpf(cpfMotorista);
            if (motorista != null)
            {
                return new UsuarioDocumento()
                {
                    Validade = motorista.ValidadeCNH
                };
            }

            return new UsuarioDocumento()
            {
                Validade = null
            };
        }

        public object ConsultaGrid(int? idEmpresa, int? idEstabelecimentoBase, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            return _grupoUsuarioApp.ConsultarGrid(idEmpresa, idEstabelecimentoBase, take, page, order, filters);
        }

        public ValidationResult AlterarSenha(int idUsuario, string novasenha, bool senhaTemporaria = true)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _service.UpdateSenha(idUsuario, novasenha);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
                //atualizar o usuario no keycloak
                var usuario = Get(idUsuario);
                var contato = usuario.Contatos.FirstOrDefault();
                var email = contato?.Email ?? string.Empty;
                var tempPass = senhaTemporaria ? (usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario) : senhaTemporaria;
                _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, novasenha, usuario.Ativo, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                        { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                        { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { usuario.Nome.ToString() } },
                        { "Modified", new List<string> { "updated pw at " + DateTime.Now.ToString() + " by " + idUsuario.ToString()} },
                    }, false);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna o elemento com todos os filhos carregados
        /// </summary>
        /// <param name="id">Código do registro</param>
        /// <returns></returns>
        public Usuario GetAllChilds(int id)
        {
            return _service.GetAllChilds(id);
        }

        /// <summary>
        /// Irá validar o usuário, verificando se encontra algum com o login e senha informado
        /// </summary>
        /// <param name="login">Login</param>
        /// <param name="senha">Senha</param>
        /// <param name="idFacebook"></param>
        /// <returns></returns>
        public UsuarioTokenDTO ValidarUsuario(string login, string senha, long? idFacebook)
        {
            return _service.ValidarUsuario(login, senha, idFacebook);
        }
        public UsuarioTokenDTO ValidarCodigoAcesso(string code, string session_state)
        {
            return _service.ValidarCodigoAcesso(code, session_state);
        }

        public List<UsuarioEstabelecimento> GetEstabelecimentos(int idUsuario)
        {
            return _service.GetEstabelecimentos(idUsuario);
        }

        /// <summary>
        /// Irá validar o usuário, verificando se encontra algum com o CPF e senha informado
        /// </summary>
        /// <param name="cnpjCpf">CPF</param>
        /// <param name="senha">Senha</param>
        /// <returns></returns>
        public Usuario ValidarUsuarioPorCPF(string cnpjCpf, string senha)
        {
            return _service.ValidarUsuarioPorCNPJCPF(cnpjCpf, senha);
        }

        /// <summary>
        /// Irá validar o usuário, verificando se encontra algum com o CPF e senha informado
        /// </summary>
        /// <param name="usuario"></param>
        /// <param name="senha">Senha</param>
        /// <returns></returns>
        public Usuario ValidarUsuarioPorUsuario(string usuario, string senha)
        {
            return _service.ValidarUsuarioPorUsuario(usuario, senha);
        }

        /// <summary>
        /// Adicionar o usuário a base de dados
        /// </summary>
        /// <param name="usuario">Dados do usuário</param>
        /// <param name="idUsuarioLogon">Código do usuário logado no momento</param>
        /// <param name="cnpjEmpresa"></param>
        /// <returns></returns>
        public ValidationResult Add(Usuario usuario, int? idUsuarioLogon, string cnpjEmpresa = "")
        {
            try
            {
                #region Senha

                var novaSenha = usuario.Senha;
                usuario.Senha = MD5Hash.Hash($"{usuario.Senha}{Md5HashPassword}");

                #endregion

                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _service.Add(usuario, idUsuarioLogon, cnpjEmpresa);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }

                //inserir o usuario no keycloak
                var contato = usuario.Contatos.FirstOrDefault();
                var email = contato?.Email ?? string.Empty;
                //a senha recebida na criação deve ser randomica e enviada por email
                var tempPass = (usuario.Perfil != EPerfil.Motorista && usuario.Perfil == EPerfil.Proprietario);
                _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, novaSenha ?? "", usuario.Ativo, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                        { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                        { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { usuario.Nome.ToString() } },
                        { "Modified", new List<string> { "created at " + DateTime.Now.ToString() + " by " + idUsuarioLogon.ToString()} },
                    }, false);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorn os dados completos do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <param name="asNoTracking"></param>
        /// <returns></returns>
        public Usuario Get(int id, bool asNoTracking)
        {
            return _service.Get(id, asNoTracking);
        }

        /// <summary>
        /// Retorn os dados completos do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public Usuario Get(int id)
        {
            return _service.Get(id, false);
        }

        /// <summary>
        /// Atualizar o registro do usuário
        /// </summary>
        /// <param name="usuario">Dados do usuário</param>
        /// <param name="idUsuarioLogon">Código do usuário logado no momento</param>
        /// <param name="cnpjEmpresa"></param>
        /// <returns></returns>
        public ValidationResult Update(Usuario usuario, int idUsuarioLogon, string cnpjEmpresa)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var usuarioClientes =
                        _usuarioClienteService.GetUsuarioClientePorIdUsuario(usuario.IdUsuario).ToList();
                    foreach (var usuarioCliente in usuarioClientes)
                    {
                        var validationCliente = _usuarioClienteService.Delete(usuarioCliente);
                        if (!validationCliente.IsValid)
                            return validationCliente;
                    }

                    ValidationResult validationResult =
                        _service.Update(usuario, idUsuarioLogon, cnpjEmpresa);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public bool UpdateUsuarioEmpresaVinculada(Usuario usuario, int idUsuarioLogon, string cnpjEmpresa = "")
        {
            _service.Update(usuario, idUsuarioLogon, cnpjEmpresa);

            return true;
        }

        /// <summary>
        /// Atualizar o registro do usuário
        /// </summary>
        /// <param name="usuario">Dados do usuário</param>
        /// <param name="idUsuarioLogon">Código do usuário logado no momento</param>
        /// <returns></returns>
        public ValidationResult Update(Usuario usuario, int idUsuarioLogon)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _service.Update(usuario, idUsuarioLogon);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                    //atualizar o usuario no keycloak
                    var contato = usuario.Contatos.FirstOrDefault();
                    var email = contato?.Email ?? string.Empty;
                    var tempPass = (usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario);
                    _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, "", usuario.Ativo, new Dictionary<string, object>()
                    {
                        { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                        { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                        { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                        { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                        { "Nome", new List<string> { usuario.Nome.ToString() } },
                        { "Modified", new List<string> { "updated at " + DateTime.Now.ToString() + " by " + idUsuarioLogon.ToString()} },
                    }, false);
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna a lista de usuario no formato UsuarioGrid para consulta dentro do sistema
        /// </summary>
        /// <param name="nome">Nome do usuário (like)</param>
        /// <param name="idEmpresa">Código do empresa</param>
        /// <param name="idUsuarioLogOn">Código do usuário logado</param>
        /// <param name="listarTerceiros"></param>
        /// <returns></returns>
        public IQueryable<Usuario> Consultar(string nome, int? idEmpresa, int idUsuarioLogOn,
            bool listarTerceiros = false)
        {
            return _service.Consultar(nome, idEmpresa, idUsuarioLogOn, listarTerceiros);
        }

        public IQueryable<Usuario> Consultar(int idUsuario)
        {
            return _service.Consultar(idUsuario);
        }

        /// <summary>
        /// Inativar o usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário a ser desativado</param>
        /// <param name="idUsuarioRequisicao">Código do usuário que requisitou a desativação</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idUsuario, int idUsuarioRequisicao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _service.Inativar(idUsuario, idUsuarioRequisicao);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
                var usuario = Get(idUsuario);
                var contato = usuario.Contatos.FirstOrDefault();
                var email = contato?.Email ?? string.Empty;
                _keycloak.CreateOrUpdateUser(usuario.LoginDesabilitado, usuario.Nome, email, "", false, new Dictionary<string, object>()
                        {
                            { "CPFCNPJ", new List<string> { usuario.CpfCnpjDesabilitado } },
                            { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                            { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                            { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                            { "Nome", new List<string> { usuario.Nome.ToString() } },
                            { "Modified", new List<string> { "disabled at " + DateTime.Now.ToString() + " by " + idUsuarioRequisicao.ToString() } },
                        }, false);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar o usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário a ser desativado</param>
        /// <param name="idUsuarioRequisicao">Código do usuário que requisitou a desativação</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idUsuario, int idUsuarioRequisicao)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _service.Reativar(idUsuario, idUsuarioRequisicao);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
                var usuario = Get(idUsuario);
                var contato = usuario.Contatos.FirstOrDefault();
                var email = contato?.Email ?? string.Empty;
                _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, "", true, new Dictionary<string, object>()
                        {
                            { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                            { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                            { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                            { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                            { "Nome", new List<string> { usuario.Nome.ToString() } },
                            { "Modified", new List<string> { "reactivated at " + DateTime.Now.ToString() + " by " + idUsuarioRequisicao.ToString() } },
                        }, false);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna a foto do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public byte[] GetFoto(int id)
        {
            return _service.GetFoto(id);
        }

        /// <summary>
        /// Retorna o código do usuário a partir do CPF
        /// </summary>
        /// <param name="cnpjCpf">CPF do usuário</param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public int? GetIdPorCNPJCPF(string cnpjCpf, int? idEmpresa = null)
        {
            return _usuarioRepository.GetIdPorCNPJCPF(cnpjCpf, idEmpresa);
        }


        /// <summary>
        /// Retorna o código do usuário a partir do CPF
        /// </summary>
        /// <param name="tokenFirebase"></param>
        /// <returns></returns>
        public int? GetIdByTokenFirebase(string tokenFirebase)
        {
            return _service.GetIdByTokenFirebase(tokenFirebase);
        }

        /// <summary>
        /// Retorna o tipo de perfil do usuário
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public EPerfil GetPerfil(int idUsuario)
        {
            return _usuarioRepository.GetPerfil(idUsuario);
        }

        /// <summary>
        /// Retorna o código do CPF do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public string GetCPFCNPJ(int idUsuario)
        {
            return _usuarioRepository.GetCNPJCPF(idUsuario);
        }

        public string GetNome(int id)
        {
            return _service.GetNome(id);
        }

        /// <summary>
        /// Retorna o status do usuário
        /// </summary>
        /// <param name="cnpjCpf">CPF do usuário a ser validado</param>
        /// <returns></returns>
        public EStatusUsuario GetStatusUsuario(string cnpjCpf)
        {
            return _service.GetStatus(cnpjCpf.OnlyNumbers());
        }

        /// <summary>
        /// Retorna lista de usuarios por empresa
        /// </summary>
        /// <param name="idEmpresa">Id de empresa</param>
        /// <returns>IQueryable de Usuario</returns>
        public IQueryable<Usuario> GetUsuariosPorEmpresa(int idEmpresa)
        {
            return _service.GetUsuariosPorEmpresa(idEmpresa);
        }

        /// <summary>
        /// Retorna o Usuário a partir do código do CPF
        /// </summary>
        /// <param name="cnpjCpf">CPF</param>
        /// <param name="aWithIncludes"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public Usuario GetPorCNPJCPF(string cnpjCpf, bool aWithIncludes = true, int? idEmpresa = null)
        {
            return _usuarioRepository.GetPorCNPJCPF(cnpjCpf, aWithIncludes, idEmpresa);
        }

        public IQueryable<Usuario> GetQueryPorCnpjcpf(string cnpjCpf)
        {
            return _service.GetQueryPorCNPJCPF(cnpjCpf);
        }

        /// <summary>
        /// Retorna a senha criptografada do usuário
        /// </summary>
        /// <param name="id">ID do usuário</param>
        /// <returns></returns>
        public string GetSenha(int id)
        {
            return _service.GetSenha(id);
        }

        /// <summary>
        /// Retorna o status do usuário
        /// </summary>
        /// <param name="login">Login do usuário</param>
        /// <param name="senha">Senha do usuário</param>
        /// <returns></returns>
        public EStatusUsuario GetStatusUsuario(string login, string senha)
        {
            return _service.GetStatus(login, senha);
        }

        /// <summary>
        /// Retorna informando se o usuário possui permissão para acessar o determinado menu.
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <param name="idMenu"></param>
        /// <returns></returns>
        public bool HasPermissaoAcessoMenu(int idUsuario, int idMenu)
        {
            return _service.HasPermissaoAcessoMenu(idUsuario, idMenu);
        }

        public Usuario GetUsuarioFromFacebook(long idFacebook)
        {
            return _service.GetUsuarioFromFacebook(idFacebook);
        }

        /// <summary>
        /// Atualiza a data do último acesso do usuário no sistema.
        /// </summary>
        /// <param name="idUsuario">Id do usuário</param>
        /// <param name="tipoAcesso">Tipo de acesso</param>
        /// <returns></returns>
        public ValidationResult AtualizarDataUltimoAcesso(int idUsuario, ETipoAcessoSistema tipoAcesso)
        {
            try
            {
                ValidationResult validationResult =
                    _service.AtualizarDataUltimoAcesso(idUsuario, tipoAcesso);
                if (!validationResult.IsValid)
                    return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult VincularUsuarioComEmpresa(Motorista motoristaSerAdicionado, EProcesso processoMotorista,
            Usuario usuarioVinculado)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = Update(usuarioVinculado, usuarioVinculado.IdUsuario);
                    if (!validationResult.IsValid)
                        return validationResult;
                    if (motoristaSerAdicionado != null && processoMotorista == EProcesso.Create)
                    {
                        ValidationResult validationMotorista =
                            _motoristaService.Add(motoristaSerAdicionado, usuarioVinculado.IdUsuario, true);
                        if (!validationMotorista.IsValid)
                            return validationMotorista;
                    }
                    else if (motoristaSerAdicionado != null && processoMotorista == EProcesso.Update)
                    {
                        motoristaSerAdicionado.Ativo = true;
                        ValidationResult validationMotorista =
                            _motoristaService.Update(motoristaSerAdicionado, usuarioVinculado.IdUsuario, true);
                        if (!validationMotorista.IsValid)
                            return validationMotorista;
                    }

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }
        }

        public List<Usuario> GetUsuarioByIdPonto(int idPonto)
        {
            return _service.GetUsuarioByIdPonto(idPonto);
        }

        public bool ValidarSessaoUsuario(string key)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    bool retorno = _service.ValidaSessaoUsuario(key);

                    transaction.Complete();

                    return retorno;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public Usuario GetUsuarioByKey(string key)
        {
            return _service.GetUsuarioByKey(key);
        }

        public Usuario GetClientesByUsuario(int? idUsuario)
        {
            return _service.GetClientesByUsuario(idUsuario);
        }

        public List<UsuarioPreferencias> GetUsuarioPreferencias(int idUsuario, string campo = null)
        {
            return _service.GetUsuarioPreferencias(idUsuario, campo);
        }

        public ValidationResult SaveUsuarioPreferencias(List<UsuarioPreferencias> usuarioPreferencias)
        {
            return _service.SaveUsuarioPreferencias(usuarioPreferencias);
        }

        public List<UsuarioPreferencias> GetPreferenciasUsuarioPrefixLike(int idUsuario, string prefix)
        {
            return _service.GetPreferenciasUsuarioPrefixLike(idUsuario, prefix);
        }

        public List<dynamic> ConsultarUsuarios(int idEmpresa, bool vistoriador)
        {
            return _service.ConsultarUsuarios(idEmpresa, vistoriador);
        }

        public Usuario ValidarUsuarioPorToken(string tokenFirebase)
        {
            return _service.ValidarUsuarioPorToken(tokenFirebase);
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, IUserIdentity userIdentity)
        {
            return _service.ConsultarGrid(take, page, order, filters, userIdentity);
        }

        public IEnumerable<UsuarioEstabelecimento> GetUsuariosEstabelecimento(int idUsuario)
        {
            return _service.GetUsuariosEstabelecimento(idUsuario);
        }

        /* remover esse metodo ????????????????
        //public void ResetarSenhaPorCpfEmail(string cpf, string email, string dominio = null)
        {
            // A senha nova é setada na propriedade senha do retorno do método ResetarSenhaUsuario
            var usuario = _service.ResetarSenhaUsuario(cpf, email);
            var novaSenha = usuario.Senha;
            // Agora enviamos o e-mail
            ValidationResult validationResult = AlterarSenha(usuario.IdUsuario, usuario.Senha);
            if (!validationResult.IsValid)
                throw new Exception("Não foi possível alterar a senha" + validationResult);

            _service.EnviarEmailRecuperacaoSenha(usuario, novaSenha, dominio);
        }*/

        public bool ResetarSenha(int idUsuario, int idUsuarioLogon)
        {
            return _service.ResetarSenha(idUsuario, idUsuarioLogon, "");
        }

        public bool Resetar2FA(int idUsuario)
        {
            return _service.Resetar2FA(idUsuario);
        }

        public bool PertenceAEmpresa(int idempresa, int idUsuario)
        {
            return _usuarioRepository.Any(c => c.IdEmpresa == idempresa && c.IdUsuario == idUsuario);
        }

        public UsuarioCadastrarV2Response CadastrarV2(UsuarioCadastrarV2Request request)
        {
            if (request.Carreteiro == false && !string.IsNullOrWhiteSpace(request.Placa))
                throw new InvalidOperationException("Não é possível cadastrar um veículo para um usuário que não é carreteiro.");

            if (request.Perfil == EPerfil.Administrador)
                throw new InvalidOperationException("Não é permitido cadastrar um usuário com perfil administrador.");

            if (string.IsNullOrWhiteSpace(request.Senha))
                throw new InvalidOperationException("Senha é obrigatória.");

            try
            {
                request.Senha = _rsaCryptoService.Decrypt(request.Senha);
                if (string.IsNullOrEmpty(request.Senha)) throw new Exception();
            }
            catch (Exception)
            {
                throw new InvalidOperationException("Senha é obrigatória.");
            }

            if (string.IsNullOrWhiteSpace(request.CNPJEmpresa)) request.CNPJEmpresa = request.CNPJAplicacao;

            request.CPFCNPJ = request.CPFCNPJ.OnlyNumbers();
            request.CNPJEmpresa = request.CNPJEmpresa.OnlyNumbers();
            request.CNPJFilial = request.CNPJFilial.OnlyNumbers();

            #region Usuário

            int? idEmpresa = null;
            if (!string.IsNullOrWhiteSpace(request.CNPJEmpresa))
            {
                idEmpresa = _empresaService.GetIdByCnpj(request.CNPJEmpresa);
                if (!idEmpresa.HasValue) throw new InvalidOperationException("CNPJ da Empresa inválido.");
            }

            var usuarioExistente = _usuarioRepository
                .Where(c => c.CPFCNPJ == request.CPFCNPJ && c.IdEmpresa == idEmpresa.Value)
                .Select(c => c.IdUsuario)
                .FirstOrDefault();

            if (usuarioExistente != 0) return new UsuarioCadastrarV2Response(usuarioExistente, true);

            var usuario = Mapper.Map<UsuarioCadastrarV2Request, Usuario>(request);
            if (usuario == null)
                throw new InvalidOperationException("Problemas ao realizar a conversão do objeto. Verifique a entrada de dados.");

            if (idEmpresa.HasValue) usuario.IdEmpresa = idEmpresa.Value;

            var idsGruposUsuarioEmpresa = _grupoUsuarioApp.GetPorEmpresa(usuario.IdEmpresa, null)
                .Select(o => o.IdGrupoUsuario);

            if (usuario.IdGrupoUsuario.HasValue)
                if (!idsGruposUsuarioEmpresa.Contains((int)usuario.IdGrupoUsuario))
                    throw new InvalidOperationException($"IdGrupoUsuario {usuario.IdGrupoUsuario} não encontrado na base.");

            #region Validação de imagem

            if (!string.IsNullOrWhiteSpace(request.FotoBase64))
            {
                usuario.Foto = Convert.FromBase64String(request.FotoBase64);
            }

            #endregion

            if (request.ValidadeCNH.HasValue)
            {
                usuario.Documentos = new List<UsuarioDocumento>();

                var tpDocCnh = _tipoDocumentoService.All().FirstOrDefault(x => x.Descricao == "CNH");
                if (tpDocCnh != null)
                    usuario.Documentos.Add(new UsuarioDocumento
                    {
                        Validade = request.ValidadeCNH.Value,
                        IdTipoDocumento = tpDocCnh.IdTipoDocumento
                    });
            }

            // Sempre será 'ativo' no momento do cadastro
            usuario.Ativo = true;
            
            // Demais perfis deverão ser cadastrados via portal
            usuario.Perfil = EPerfil.Motorista;

            #endregion

            #region Veículo

            if (!string.IsNullOrWhiteSpace(request.Placa))
            {
                usuario.Veiculos = new List<Veiculo>
                {
                    Mapper.Map<UsuarioCadastrarV2Request, Veiculo>(request)
                };

                if (request.TipoContrato != (int)ETipoContrato.Terceiro)
                    usuario.Veiculos.First().IdEmpresa = usuario.IdEmpresa;
                usuario.Veiculos.First().DataUltimaAtualizacao = DateTime.Now;
            }

            #endregion

            #region Contato

            if (!string.IsNullOrWhiteSpace(request.Telefone) || !string.IsNullOrWhiteSpace(request.Celular) ||
                !string.IsNullOrWhiteSpace(request.Email))
            {
                usuario.Contatos = new List<UsuarioContato>
                {
                    Mapper.Map<UsuarioCadastrarV2Request, UsuarioContato>(request)
                };
            }

            #endregion

            #region Filiais

            if (!string.IsNullOrWhiteSpace(request.CNPJFilial))
            {
                var idFilial = _filialService.GetIdPorCnpj(request.CNPJFilial);
                if (idFilial != null)
                {
                    usuario.Filiais = new List<UsuarioFilial>();
                    usuario.Filiais.Add(new UsuarioFilial
                    {
                        IdFilial = idFilial.Value
                    });
                }
            }

            if (request.Filiais != null && request.Filiais.Count > 0)
            {
                if (usuario.Filiais == null) usuario.Filiais = new List<UsuarioFilial>();

                foreach (var filial in request.Filiais)
                {
                    if (filial == null || filial <= 0 || usuario.Filiais.Any(x => x.IdFilial == filial)) 
                        continue;
                    
                    usuario.Filiais.Add(new UsuarioFilial()
                    {
                        IdFilial = filial.Value
                    });
                }
            }


            #endregion

            #region Endereço

            if (!string.IsNullOrWhiteSpace(request.Endereco) || !string.IsNullOrWhiteSpace(request.Bairro) ||
                !string.IsNullOrWhiteSpace(request.Complemento))
            {
                usuario.Enderecos = new List<UsuarioEndereco>
                {
                    Mapper.Map<UsuarioCadastrarV2Request, UsuarioEndereco>(request)
                };

                var cidade = _cidadeService.GetQueryByIBGE(request.IBGECidade).Include(c => c.Estado).FirstOrDefault();
                if (cidade == null)
                    throw new InvalidOperationException($"Cidade inválida.");
                usuario.Enderecos.First().IdCidade = cidade.IdCidade;
                usuario.Enderecos.First().IdEstado = cidade.IdEstado;
                usuario.Enderecos.First().IdPais = cidade.Estado.IdPais;
            }

            #endregion

            #region Horários CheckIn

            if (request.HorariosCheckIn != null && request.HorariosCheckIn.Count > 0)
            {
                // Lista sempre será sobrescrita
                usuario.HorariosCheckIn = new List<UsuarioHorarioCheckIn>();

                // Percorre a lista, inserindo no elemento
                foreach (var horario in request.HorariosCheckIn)
                {
                    if (string.IsNullOrWhiteSpace(horario)) 
                        continue;
                    
                    usuario.HorariosCheckIn.Add(new UsuarioHorarioCheckIn
                    {
                        Horario = Convert.ToInt32(horario.Replace(@":", "").Trim())
                    });
                }
            }

            #endregion

            var validationResult = Add(usuario, null, request.CNPJEmpresa);
            if (!validationResult.IsValid)
                throw new InvalidOperationException(validationResult.ToFormatedMessage());

            return new UsuarioCadastrarV2Response(usuario.IdUsuario);
        }

        public object ConsultaGrid(int? idEmpresa,
            string nome,
            int take,
            int page,
            OrderFilters order,
            List<QueryFilters> filters,
            EPerfil prfUsuLogado, int? idUsuarioLogOn, bool listarTerceiros, bool usuariosAtivos)
        {
            return _service.ConsultaGrid(idEmpresa, take, page, order, filters, prfUsuLogado,
                idUsuarioLogOn, listarTerceiros, usuariosAtivos);
        }

        public ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosLiderados(int idUsuario, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _service.ConsultarGridUsuariosLiderados(idUsuario, take, page, order, filters);
        }

        public ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosDisponiveisParaSeremLiderados(int idUsuario, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _service.ConsultarGridUsuariosDisponiveisParaSeremLiderados(idUsuario, idEmpresa, take, page, order, filters);
        }

        public ValidationResult IncluirUsuarioLiderado(int idUsuario, int? idEmpresa, int idUsuarioParaIncluir, int idUsuarioCadastro)
        {
            return _service.IncluirUsuarioLiderado(idUsuario, idEmpresa, idUsuarioParaIncluir, idUsuarioCadastro);
        }

        public ValidationResult RemoverUsuarioLiderado(int idUsuario, int idUsuarioParaRemover)
        {
            return _service.RemoverUsuarioLiderado(idUsuario, idUsuarioParaRemover);
        }

        public DateTime? GetUltimoAcessoUsuarioAplivativo(int idUsuario)
        {
            return _service.GetUltimoAcessoUsuarioAplivativo(idUsuario);
        }


        public byte[] GerarRelatorioGridUsuarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters,
            EPerfil perfilUsuarioLogado, int? idUsuario, bool listarTerceiros, string tipoArquivo, string logo)
        {
            return _service.GerarRelatorioGridUsuarios(idEmpresa, orderFilters, filters,
                perfilUsuarioLogado,
                idUsuario, listarTerceiros, tipoArquivo, logo);
        }

        public KeyValuePair<ValidationResult, string> GerarKeyCodeTransaction(int idUsuario, string cpf)
        {
            return _service.GerarKeyCodeTransaction(idUsuario, cpf);
        }

        public List<Usuario> GetByPerfil(EPerfil perfil, int? idFilial, int? idEmpresa)
        {
            return _service.GetByPerfil(perfil, idFilial, idEmpresa);
        }

        public ValidationResult AtualizarContatado(string aCpf, int aStatus)
        {
            return _service.AtualizarContatado(aCpf, aStatus);
        }

        public IQueryable<GridContatosModel> GetEmailsUsuarioByListaId(int[] listaIds)
        {
            return _service.GetEmailsUsuarioByListaId(listaIds);
        }

        public bool HasVeiculoCadastrado(string placa)
        {
            return _service.HasVeiculoCadastrado(placa);
        }

        public int? GetIdEstabelecimentoBase(int idUsuario)
        {
            return _service.GetIdEstabelecimentoBase(idUsuario);
        }

        public IEnumerable<ConsultaVistoriadores> ConsultarVistoriadores(string cpfUsuario)
        {
            return _service.ConsultarVistoriadores(cpfUsuario);
        }

        public Usuario GetComEstabelecimentos(int idUsuario)
        {
            return _service.GetComEstabelecimentos(idUsuario);
        }

        public bool HasEstabelecimento(int idUsuario, int? idEstabelecimentoBase)
        {
            return _service.HasEstabelecimento(idUsuario, idEstabelecimentoBase);
        }

        public Usuario GetUsuarioGestaoEntregas(string cpfCnpj)
        {
            return _service.GetUsuarioGestaoEntregas(cpfCnpj);
        }

        public object ConsultaGridPorEmpresa(int? idEmpresa,
            int? idFilial,
            int? idOperacao,
            int take,
            int page,
            OrderFilters order,
            List<QueryFilters> filters, bool marcarTodos, int apertou,
            List<int> grupoUsuarios)
        {
            return _service.ConsultaGridPorEmpresa(idEmpresa, idFilial, idOperacao, take, page, order,
                filters, marcarTodos, apertou, grupoUsuarios);
        }

        public ValidationResult AtualizarDataUltimaAberturaAplicativo(int usuarioId)
        {
            return _usuarioRepository.AtualizarDataUltimaAberturaAplicativo(usuarioId);
        }

        public ConsultaInformacoesMobileModel ConsultarInformacoesMobile(int itensPorPagina, int pagina, int? empresaId, string documento)
        {
            return _usuarioRepository.ConsultarInformacoesMobile(itensPorPagina, pagina, empresaId, documento);
        }

        public UsuarioMicroServicoInstanciaAppDto UsuarioMicroServicoInstanciaApp(int idUsuario)
        {
            return _service.UsuarioMicroServicoInstanciaApp(idUsuario);
        }

        public UsuarioFotoResponse GetFotoUsuario(string cpfcnpj)
        {
            var usuario = _service.GetFotoUsuario(cpfcnpj);

            if (usuario == null)
                return new UsuarioFotoResponse(false, "Usuário não encontrado");

            return new UsuarioFotoResponse(true, !string.IsNullOrWhiteSpace(usuario.Foto) ? string.Empty : "Usuário não possui foto")
            {
                Objeto = new UsuarioFotoResponseDto
                {
                    IdUsuario = usuario.IdUsuario,
                    Cpfcnpj = cpfcnpj,
                    Foto = usuario.Foto
                }
            };
        }

        public UsuarioAplicativoGetInformacoesResponse UsuarioAplicativoGetInformacoes()
        {
            var usuario = Find(c => c.IdUsuario == _userIdentity.IdUsuario && c.Ativo)
                .Include(c => c.Contatos)
                .Include(c => c.Enderecos)
                .Include(c => c.Empresa)
                .AsNoTracking()
                .FirstOrDefault();

            if (usuario == null)
                throw new InvalidOperationException("Usuário não encontrado.");

            int? ibgeCidade = null;
            var endereco = usuario.Enderecos?.FirstOrDefault();
            if (endereco != null) ibgeCidade = _cidadeService.All().AsNoTracking()
                .Where(c => c.IdCidade == endereco.IdCidade).Select(c => c.IBGE).FirstOrDefault();
            var contato = usuario.Contatos?.FirstOrDefault(c => c.Email != null);
            var autenticaco = usuario.IdEmpresa.HasValue ? _autenticacaoAplicacaoService
                    .GetPorIdEmpresa(usuario.IdEmpresa.Value).AsNoTracking().FirstOrDefault(c => c.Ativo) : null;
            var infos = new UsuarioAplicativoGetInformacoesResponse()
            {
                Ativo = usuario.Ativo,
                Login = usuario.Login,
                Nome = usuario.Nome,
                Perfil = usuario.Perfil,
                IdUsuario = usuario.IdUsuario,
                ReceberNotificacao = usuario.ReceberNotificacao,
                CPFCNPJ = usuario.CPFCNPJ,
                Celular = contato?.Celular,
                Email = contato?.Email,
                IBGECidade = ibgeCidade,
                CEP = endereco?.CEP,
                Bairro = endereco?.Bairro,
                Endereco = endereco?.Endereco,
                Numero = endereco?.Numero,
                Carreteiro = usuario.Carreteiro,
                Empresa = null,
                Autenticacao = new UsuarioAplicativoGetInformacoesAutenticacaoResponse()
                {
                    Token = autenticaco?.Token,
                    CNPJAplicacao = autenticaco?.CNPJAplicacao,
                }
            };

            if (usuario.IdEmpresa != null)
            {
                infos.Empresa = new UsuarioAplicativoGetInformacoesEmpresaResponse()
                {
                    Ativo = usuario.Empresa?.Ativo,
                    RazaoSocial = usuario.Empresa?.RazaoSocial,
                    CNPJ = usuario.Empresa?.CNPJ,
                    Email = usuario.Empresa?.Email,
                    EmailSugestoes = usuario.Empresa?.EmailSugestoes,
                    IdEstado = usuario.Empresa?.IdEstado,
                    IdCidade = usuario.Empresa?.IdCidade,
                    CEP = usuario.Empresa?.CEP,
                    Bairro = usuario.Empresa?.Bairro,
                    ObrigarValorTerceiro = usuario.Empresa?.ObrigarValorTerceiro,
                };
            }

            if (_userIdentity.Perfil == (int)EPerfil.Motorista)
            {
                var infosMot = _motoristaService.GetPorCpfQueryable(usuario.CPFCNPJ)
                    .Where(c => c.Ativo && c.IdEmpresa == usuario.IdEmpresa)
                    .AsNoTracking().Select(c => new {c.TipoContrato, c.IdMotorista}).FirstOrDefault();
                infos.TipoContrato = infosMot?.TipoContrato;
                if (infosMot?.IdMotorista != null)
                {
                    var veiculo = _veiculoService.GetAll().AsNoTracking()
                        .FirstOrDefault(c => c.IdMotorista == infosMot.IdMotorista && c.Ativo && c.IdEmpresa == usuario.IdEmpresa);
                    infos.TipoRodagem = veiculo?.TipoRodagem;
                    infos.ComTracao = veiculo?.ComTracao;
                    infos.IdTipoCarreta = veiculo?.IdTipoCarreta;
                    infos.IdTipoCavalo = veiculo?.IdTipoCavalo;
                }
            }

            if (_userIdentity.Perfil == (int)EPerfil.Proprietario)
            {
                if (!usuario.IdEmpresa.HasValue)
                    throw new InvalidOperationException("Usuário sem empresa vinculada.");
                var infosProp = _proprietarioService.Find(c => c.Ativo && c.CNPJCPF == usuario.CPFCNPJ && c.IdEmpresa == usuario.IdEmpresa.Value)
                    .AsNoTracking().Select(c => new {c.TipoContrato, c.IdProprietario}).FirstOrDefault();
                infos.TipoContrato = infosProp?.TipoContrato;
                if (infosProp?.IdProprietario != null)
                {
                    var veiculo = _veiculoService.GetAll().AsNoTracking()
                        .FirstOrDefault(c => c.IdProprietario == infosProp.IdProprietario && c.Ativo && c.IdEmpresa == usuario.IdEmpresa.Value);
                    infos.TipoRodagem = veiculo?.TipoRodagem;
                    infos.ComTracao = veiculo?.ComTracao;
                    infos.IdTipoCarreta = veiculo?.IdTipoCarreta;
                    infos.IdTipoCavalo = veiculo?.IdTipoCavalo;
                }
            }

            return infos;
        }

        /// <summary>
        /// Atualizar o registro do IdPush do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <param name="idPush">Identificação do Push</param>
        /// <param name="sistemaOperacional">Identificação do SO do aplicativo mobile</param>
        /// <returns></returns>
        public ValidationResult UpdatePush(int idUsuario, string idPush, ESistemaOperacional? sistemaOperacional = null)
        {
            var validationResult = new ValidationResult();
            var usuario = Get(idUsuario);

            if (usuario == null)
                return validationResult.Add($"Usuário inválido.");

            //if (usuario.IdPush == idPush)
            //    return new ValidationResult();

            // Atualizar o registro de IdPush
            usuario.IdPush = idPush;
            usuario.SistemaOperacional = sistemaOperacional;

            validationResult.Add(_service.UpdatePush(usuario.IdUsuario, idPush, sistemaOperacional));

            return validationResult;
        }

        public string BuscaUsuarioMasterEstabelecimento(int idEstabelecimento)
        {
            return _service.BuscaUsuarioMasterEstabelecimento(idEstabelecimento);
        }

        public bool UserGroupIsActive(int idUsuario)
        {
            return _service.UserGroupIsActive(idUsuario);
        }

        public bool AutenticarMenu(string linkFront,int idUser)
        {
            var listLinksPermetidos = new List<string>();

            var menus = _menuApp.GetMenusPermitidos(idUser);

            menus.menus.ForEach(modulo =>
            {
                modulo.MenuEstruturaModel.ForEach(menuPai =>
                    menuPai.Menus.ToList().ForEach(menu =>
                    {
                        if (!string.IsNullOrWhiteSpace(menu.LinkNovo))
                            listLinksPermetidos.Add(menu.LinkNovo.Split('.')[0]);
                    })
                );
            });

            return listLinksPermetidos.Contains(linkFront);
        }

        public MenusUsuarioPermitidoDto GetMenusAcessoUsuario(int idUser)
        {
            var retorno = new MenusUsuarioPermitidoDto();

            var menus = _menuApp.GetMenusPermitidos(idUser);

            menus.menus.ForEach(modulo =>
            {
                retorno.ModulosPermitidos.Add(modulo.NomeModulo);

                modulo.MenuEstruturaModel.ForEach(menuPai =>
                    menuPai.Menus.ToList().ForEach(menu =>
                    {
                        var link = string.Empty;

                        if (!string.IsNullOrWhiteSpace(menu.Link))
                            link = menu.Link;

                        if (!string.IsNullOrWhiteSpace(menu.LinkNovo))
                            link = menu.LinkNovo;

                        link = link.Replace(".", "/").ToLower();
                        if (link.Contains("/"))
                            link = link.Split('/')[0];

                        if(!string.IsNullOrWhiteSpace(link) && !retorno.LinksPermitidos.Contains(link))
                            retorno.LinksPermitidos.Add(link);
                    })
                );
            });

            return retorno;
        }
    }
}
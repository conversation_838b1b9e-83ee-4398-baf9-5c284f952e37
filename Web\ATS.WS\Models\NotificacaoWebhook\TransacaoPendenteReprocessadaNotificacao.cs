using System;

namespace ATS.WS.Models.NotificacaoWebhook
{
    /// <summary>
    /// Notificação de webhook para transações pendentes reprocessadas.
    /// Enviado para o ATS atualizar seu status interno.
    /// </summary>
    public class TransacaoPendenteReprocessadaNotificacao : TransacaoPendenteNotificacao
    {
    }

    public class TransacaoPendenteFalhaNotificacao : TransacaoPendenteNotificacao
    {
    }

    public class TransacaoPendenteNotificacao
    {
        public long ProtocoloRequisicao { get; set; }
        public int ProtocoloProcessamento { get; set; }
        public DateTime DataTransacao { get; set; }
        public string MensagemStatus { get; set; }
    }
}
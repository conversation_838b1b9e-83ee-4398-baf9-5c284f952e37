﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class MotivoService : ServiceBase, IMotivoService
    {
        private readonly IMotivoRepository _motivoRepository;
        
        public MotivoService(IMotivoRepository motivoRepository)
        {
            _motivoRepository = motivoRepository;
        }
        
        public IQueryable<Motivo> GetAtivos(int idEmpresa, ETipoMotivo? tipo = null)
        {
            return _motivoRepository.GetAtivos(idEmpresa, tipo);
        }

        public Motivo Get(int idMotivo)
        {
            return _motivoRepository.Get(idMotivo);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database.Common;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface ITipoCarretaRepository : IRepository<TipoCarreta>
    {
        IQueryable<TipoCarretaGrid> Consultar(string nome);
        IQueryable<TipoCarreta> GetPorCategoria(ECategoriaTipoCarreta categoria);
        IQueryable<TipoCarreta> GetTipoCarretaAtualizadas(DateTime dataAtualizacao, List<int> idsEmpresa);
        object ConsultarSemEmpresa();
    }
}
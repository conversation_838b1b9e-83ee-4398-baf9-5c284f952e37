﻿using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Webservice.Request.Cartoes;
using ATS.WS.Services;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Controllers
{
    public class CartoesController : BaseController
    {
        private readonly SrvCartoes _srvCartoes;
        private readonly SrvIP _srvIP;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public CartoesController(BaseControllerArgs baseArgs, SrvCartoes srvCartoes, SrvIP srvIP, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies) : base(baseArgs)
        {
            _srvCartoes = srvCartoes;
            _srvIP = srvIP;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult VincularCartao(CartaoVincularPortadorExternalRequest request)
        {
            try
            {
                if  (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(request.CNPJEmpresa) ? request.CNPJAplicacao : request.CNPJEmpresa, this.GetRealIp()))
                    return NaoAutorizado();
                
                var response = _srvCartoes.VincularCartaoPortador(request);

                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(e);
            }
        }
        
        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult CartoesVinculados(CartoesVinculadosAtsRequest request)
        {
            try
            {
                var ip = GetRealIp();

                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(request.CNPJEmpresa) ? request.CNPJAplicacao : request.CNPJEmpresa, ip))
                    return NaoAutorizado();

                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new CartaoVinculadoPessoaListResponseDto
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                // Sempre irá buscar pela administradora
                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, string.Empty, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var cartoesVinculados = cartoesApp.GetCartoesVinculados(request.Documento, request.Produtos, false, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                var response = Mapper.Map<CartaoVinculadoPessoaListResponseDto>(cartoesVinculados);
                return Responde(response);
            }
            catch (Exception e)
            {
                return Responde(new CartaoVinculadoPessoaListResponseDto
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }

        [HttpGet]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult ConsultarExtrato(ConsultarExtratoAtsRequest request)
        {
            try
            {
                var ip = GetRealIp();

                if (!_srvIP.AnalisarIP(string.IsNullOrWhiteSpace(request.CNPJEmpresa) ? request.CNPJAplicacao : request.CNPJEmpresa, ip))
                    return NaoAutorizado();

                var validacaoChamada = request.ValidaRequest();

                if (!validacaoChamada.IsValid)
                    return Responde(new ConsultarExtratoResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = validacaoChamada.ToString()
                    });

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                IdentificadorCartao cartao;
                if (request.Identificador == null)
                    cartao = cartoesApp.GetUltimoCartao(request.Documento, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);
                else
                    cartao = new IdentificadorCartao()
                    {
                        Identificador = request.Identificador,
                        Produto = request.Produto
                    };

                if(cartao?.Identificador == null || cartao?.Produto == null)
                    return Responde(new ConsultarExtratoResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = $"Não foi possível encontrar um cartão para o documento {request.Documento}"
                    });

                var consultarExtratoRequest = new ConsultarExtratoRequest()
                {
                    Cartao = cartao,
                    DataInicio = request.DataInicio,
                    DataFim = request.DataFim,
                    Tipo = request.Tipo,
                    ExibirMetadados = request.ExibirMetadados
                };

                var extrato = cartoesApp.ConsultarExtrato(consultarExtratoRequest, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                return Responde(extrato);
            }
            catch (Exception e)
            {
                return Responde(new ConsultarExtratoResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Webservice.Response.Estabelecimento;
using ATS.WS.Services.ViagemServices;
using AutoMapper.QueryableExtensions;
using Newtonsoft.Json;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.ControllersATS
{
    public class RoteirizadorAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IViagemApp _viagemApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly PedagioViagem _pedagioViagem;
        private readonly IEmpresaApp _empresaApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;

        public RoteirizadorAtsController(IUserIdentity userIdentity, IViagemApp viagemApp, IParametrosApp parametrosApp, IVersaoAnttLazyLoadService versaoAntt, PedagioViagem pedagioViagem, IEmpresaApp empresaApp, ICidadeApp cidadeApp, IEstabelecimentoApp estabelecimentoApp)
        {
            _userIdentity = userIdentity;
            _viagemApp = viagemApp;
            _parametrosApp = parametrosApp;
            _versaoAntt = versaoAntt;
            _pedagioViagem = pedagioViagem;
            _empresaApp = empresaApp;
            _cidadeApp = cidadeApp;
            _estabelecimentoApp = estabelecimentoApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCustoPedagioRota(CustoPedagioRotaRequestModel custoPedagioRotaRequestModel)
        {
            try
            {
                custoPedagioRotaRequestModel.CNPJEmpresa = _empresaApp.GetCnpj(_userIdentity.IdEmpresa ?? 0);

                var response = _pedagioViagem.ConsultarCustoPedagioRota(custoPedagioRotaRequestModel);

                if (response.Status == ConsultaRotaResponseDtoStatus.Sucesso)
                    return ResponderSucesso(response);

                return ResponderErro(response.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCodigoIbgeCidade(List<CidadeIbgeRequestModel> cidadesIbgeRequestModel)
        {
            try
            {
                var response = _cidadeApp.GetIbgeCidade(cidadesIbgeRequestModel);
                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCodigoIbgeCidadeRotas(List<CidadeIbgeRequestModel> cidadesIbgeRequestModel)
        {
            try
            {
                var response = _cidadeApp.GetIbgeCidade(cidadesIbgeRequestModel,true);
                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPostosRota(ConsultarEstabelecimentosRotaRequestModel request)
        {
            try
            {
                var response = _estabelecimentoApp.ConsultarEstabelecimentosRota(_userIdentity.IdEmpresa ?? 0, request.LatitudeOrigem, request.LongitudeOrigem, request.LatitudeDestino, request.LongitudeDestino, request.Raio);
                return ResponderSucesso(response.ProjectTo<ConsultaEstabelecimentoResponse>().ToArray());
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GerarRelatorioRoteirizador(string json)
        {
            try
            {
                var dados = JsonConvert.DeserializeObject<RelatorioRoteirizadorModel>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
                var report = new RoteirizadorService().GerarRelatorio(dados, GetLogo(dados.IdEmpresa.GetValueOrDefault()), dados.Mapa);

                Response.AddHeader("Content-Disposition", "inline; filename=Roteirizador.pdf");
                return File(report, "application/pdf");
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
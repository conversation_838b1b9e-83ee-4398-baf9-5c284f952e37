using System;
using System.Web.Script.Serialization;
using ATS.Domain.Enum;

namespace ATS.Domain.Models.Ciot
{
    public class EncerradoCanceladoCiotResult
    {
        // status Erro / NaoObrigatorio / Sucesso / Desabilitado
        public EResultadoDeclaracaoCiot Resultado { get; set; }
        
        /// <summary>
        /// Indicar se o CIOT foi declarado
        /// </summary>
        public bool EncerradoCancelado { get; set; }        
        public string Mensagem { get; set; }
        public Declaracao Dados { get; set; }
        public int QtdCancelados { get; set; }
        public int QtdEncerrados { get; set; }

        public class Declaracao
        {
            public string Ciot { get; set; }
            public string Verificador { get; set; }
            public string Senha { get; set; }
            public bool EmContigencia { get; set; }

            /// <summary>
            /// Propriedade ignorada na serialização por causa do formato \Date(5235235)/
            /// </summary>
            [ScriptIgnore]
            public DateTime DataDeclaracaoDateTime { get; set; }
            public string DataDeclaracao => DataDeclaracaoDateTime.ToString("yyyy-MM-dd HH:mm:ss");

            public string AvisoTransportador { get; set; }
        }
    }
}
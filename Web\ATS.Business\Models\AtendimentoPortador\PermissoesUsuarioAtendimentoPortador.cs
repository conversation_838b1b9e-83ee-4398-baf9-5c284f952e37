namespace ATS.Domain.Models.AtendimentoPortador
{
    public class PermissoesUsuarioAtendimentoPortador
    {
        public bool? BloquearCartao { get; set; }
        public bool? DesbloquearCartao { get; set; }
        public bool? AlterarSenhaCartao { get; set; }
        public bool? TransferenciaBancaria { get; set; }
        public bool? TransferenciaCartoes { get; set; }
        public bool? Resgate { get; set; }
        public bool? EstornoResgate { get; set; }
        public bool MostrarAbaCadastroUsuario 
        {
            get 
            { 
                if (BloquearCartao.HasValue)
                    return true;
                if (DesbloquearCartao.HasValue)
                    return true;
                if (AlterarSenhaCartao.HasValue)
                    return true;
                if (TransferenciaBancaria.HasValue)
                    return true;
                if (TransferenciaCartoes.HasValue)
                    return true;
                if (Resgate.HasValue)
                    return true;
                if (EstornoResgate.HasValue)
                    return true;
                
                return false;
            }
        }
    }
}
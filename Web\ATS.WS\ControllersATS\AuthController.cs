﻿using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.Domain.Helpers;
using ATS.WS.Attributes;

namespace ATS.WS.ControllersATS
{
    public class AuthController : DefaultController
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly ILayoutApp _layoutApp;
        private readonly KeycloakHelper _keycloak;
        

        public AuthController(IUsuarioApp usuarioApp, ILayoutApp layoutApp,KeycloakHelper keycloak)
        {
            _usuarioApp = usuarioApp;
            _layoutApp = layoutApp;
            _keycloak = keycloak;
        }

        [HttpGet]
        public JsonResult GetConfiguracaoLayoutPorDominio(string dominio)
        {
            try
            {
                var layout = _layoutApp.GetConfiguracaoLayoutPorDominio(dominio);

                if (layout == null)
                    return ResponderErro($"Nenhuma configuração encontrada para o domínio {dominio}");

                return ResponderSucesso(new
                {
                    css = layout.CSS,
                    logo = layout.Image,
                    bg = layout.Background
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex);
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [IgnoreAuthSessionValidation]
        public JsonResult Login(string usuario, string senha, string dominio)
        {
            try
            {
                var usuarioLogin = _usuarioApp.ValidarUsuario(usuario, senha, null);

                if (usuarioLogin?.usuario == null)
                    return ResponderErro($"Acesso negado: Usuário ou senha inválidos");

                if (usuarioLogin.usuario.Empresa != null)
                {
                    if (!usuarioLogin.usuario.Empresa.Ativo)
                    {
                        return ResponderErro($"Acesso negado: Usuário ou senha inválidos");
                    }
                }

                if (!string.IsNullOrWhiteSpace(dominio))
                {
                    if (usuarioLogin.usuario.IdEmpresa.HasValue)
                    {
                        var idEmpresaLayout = _layoutApp.GetConfiguracaoLayoutPorDominio(dominio)?.IdEmpresa;
                        if (idEmpresaLayout.HasValue && idEmpresaLayout != usuarioLogin.usuario.IdEmpresa)
                        {
                            return ResponderErro("Seu usuário não possui permissão para acessar este site. ");
                        }
                    }
                }
                
                return ResponderSucesso($"Bem vindo(a) {usuarioLogin.usuario.Nome}", new
                {
                    token = usuarioLogin.token.AccessToken,
                    refresh_token = usuarioLogin.token.RefreshToken,
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [EnableLogAudit]
        [HttpPost]
        [IgnoreAuthSessionValidation]
        public JsonResult AuthorizeCode(string code, string session_state)
        {
            try
            {
                var usuarioLogin = _usuarioApp.ValidarCodigoAcesso(code, session_state);

                if (usuarioLogin.usuario == null)
                {
                    if (usuarioLogin.token == null || string.IsNullOrEmpty(usuarioLogin.token.AccessToken))
                        return ResponderErro($"Acesso negado: Usuário ou senha inválidos!");
                    
                    return ResponderErro($"Acesso negado: Usuário ou senha inválidos!");
                }                    
                
                if (usuarioLogin.usuario.Empresa != null)
                {
                    if (!usuarioLogin.usuario.Empresa.Ativo)
                        return ResponderErro($"Acesso negado: Empresa desativada!");
                }
                
                _usuarioApp.AtualizarDataUltimoAcesso(usuarioLogin.usuario.IdUsuario, ETipoAcessoSistema.Web);

                return ResponderSucesso($"Bem vindo(a) {usuarioLogin.usuario.Nome}", new
                {
                    token = usuarioLogin.token.AccessToken,
                    expires_in = usuarioLogin.token.ExpiresIn,
                    refresh_token = usuarioLogin.token.RefreshToken
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        /// <summary>
        /// Atualiza a sessão para obter um novo período de expiração
        /// </summary>
        /// <param name="refreshToken"></param>
        /// <returns></returns>
        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult RefreshToken(string refreshToken)
        {
            var token = _keycloak.RefreshUserAccessToken(refreshToken);

            return ResponderSucesso("Ok", new
            {
                token = token.AccessToken,
                refresh_token = token.RefreshToken,
                expires_in = token.ExpiresIn
            });
        }

        [HttpPost]
        [Expor(EApi.Portal)]
        public JsonResult Logout(string refreshToken, string accessToken)
        {
            var at = accessToken ?? Request.Headers["SessionKey"];
            var rt = refreshToken ?? Request.Headers["RefreshKey"];
            if (string.IsNullOrWhiteSpace(at) || string.IsNullOrWhiteSpace(rt))
                return ResponderErro("Sessão não informada.");

            if (_keycloak.LogoutUserSession(at, rt))
            {
                return ResponderSucesso("Ok");
            }

            return ResponderErro("Sessão inválida, logout não efetuado.");
        }

        [HttpGet]
        [IgnoreAuthSessionValidation]
        public JsonResult GetLayout(string dominio)
        {
            var layoutBase = _layoutApp.GetConfiguracaoLayoutPorDominio(dominio);
            if (layoutBase == null)
                return ResponderSucesso("Consulta realizada com sucesso!");

            return ResponderSucesso(new
            {
                idEmpresaBase = layoutBase.IdEmpresa > 0 ? layoutBase.IdEmpresa.Value : (int?) null,
                customLogoDomain = layoutBase.Image,
                customCssDomain = layoutBase.CSS,
                logoTitle = layoutBase.LogoTitle,
                htmlTitle = layoutBase.HTMLTitle,
                favicon = layoutBase.Favicon
            });
        }

        //funcao interna
        private Tuple<bool, object> GetBaseObjectForLoginAndGetMenuMethods(string dominio, int idUsuario)
        {
            var usuario = _usuarioApp.GetComEstabelecimentos(idUsuario);
            var estabelecimento = usuario.UsuarioEstabelecimentos?.FirstOrDefault();
            var layoutBase = _layoutApp.GetConfiguracaoLayoutPorDominio(dominio);
            var idEmpresaBase = layoutBase?.IdEmpresa > 0 ? layoutBase.IdEmpresa.Value : (int?) null;
            var customCssDomain = layoutBase?.CSS;

            List<int> idsBloqueados = new List<int>();
            var dataCfgListar = WebConfigurationManager.AppSettings["IdsEmpresaHabilitarListarTerceiros"];

            if (!string.IsNullOrWhiteSpace(dataCfgListar))
            {
                var datalen = dataCfgListar.Split(',');
                foreach (var item in datalen)
                    idsBloqueados.Add(Convert.ToInt32(item));
            }
            
            var retorno = new
            {
                idEstabelecimento = estabelecimento?.IdEstabelecimento
            };

            return new Tuple<bool, object>(true, retorno);
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult Validate2fa(string code)
        {
            try
            {
                if (Request.Headers.AllKeys.Contains("SessionKey"))
                {
                    var accessToken = Request.Headers["SessionKey"];
                    var valid = _keycloak.Validate2fa(accessToken, code);
                    if (valid == "OK")
                    {
                        return ResponderSucesso("OK");
                    }
                    return ResponderErro(valid);
                }
                return ResponderErro("Código inválido");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
           
        }
    }
}
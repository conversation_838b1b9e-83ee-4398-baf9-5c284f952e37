﻿using System;

namespace ATS.Domain.Entities
{
    public class EstabelecimentoBaseDocumento
    {
        public int IdEstabelecimentoBaseDocumento { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public int? IdDocumento { get; set; }
        public string Token { get; set; }
        public DateTime? DataValidade { get; set; }
        public string Descricao { get; set; }
        public bool PermiteEditarData { get; set; }
        public int? DiasValidade { get; set; }

        public virtual EstabelecimentoBase EstabelecimentoBase { get; set; }

        public DateTime? SetDataValidade(DateTime? dataValidade, bool permiteEditar, int? diasValidade, string token)
        {
            if (!string.IsNullOrEmpty(token))
                return dataValidade;
            
            if (permiteEditar)
                return null;
            
            return DateTime.Now.AddDays(Convert.ToDouble(diasValidade));
        }
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
     public class SerproCacheMap : EntityTypeConfiguration<SerproCache>
     {
          public SerproCacheMap()
          {
              ToTable("SERPRO_CACHE");

              HasKey(x => x.Id);
              Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
              Property(c => c.DataCadastro).HasColumnType("datetime2").IsRequired();
              Property(c => c.CpfCnpj).HasMaxLength(20).IsRequired();
          }
     }
}

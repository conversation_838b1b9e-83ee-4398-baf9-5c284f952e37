﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ClienteEnderecoMap : EntityTypeConfiguration<ClienteEndereco>
    {
        public ClienteEnderecoMap()
        {
            ToTable("CLIENTE_ENDERECO");

            HasKey(x => new { x.IdClienteEndereco, x.IdCliente });
            
            Property(t => t.IdClienteEndereco)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            
            Property(x => x.Endereco)
                .IsRequired();
            
            HasRequired(x => x.Pais)
                .WithMany(x => x.ClienteEnderecos)
                .HasForeignKey(x => x.IdPais);

            HasRequired(x => x.Cidade)
                .WithMany(x => x.ClienteEnderecos)
                .HasForeignKey(x => x.IdCidade);

            HasRequired(x => x.Estado)
                .WithMany(x => x.ClienteEnderecos)
                .HasForeignKey(x => x.IdEstado);

            HasRequired(x => x.Cliente)
                .WithMany(x => x.ClienteEnderecos)
                .HasForeignKey(x => x.IdCliente);

        }
    }
}

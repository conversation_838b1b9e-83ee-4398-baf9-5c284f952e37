﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Alter_Columns_LatLng_Pontos : DbMigration
    {
        public override void Up()
        {
            AlterColumn("dbo.PONTOS_ROTA_MODELO", "latitude", c => c.Decimal(precision: 18, scale: 7));
            AlterColumn("dbo.PONTOS_ROTA_MODELO", "longitude", c => c.Decimal(precision: 18, scale: 7));
            AlterColumn("dbo.ROTA_MODELO", "origemlatitude", c => c.Decimal(precision: 18, scale: 7));
            AlterColumn("dbo.ROTA_MODELO", "origemlongitude", c => c.Decimal(precision: 18, scale: 7));
            AlterColumn("dbo.ROTA_MODELO", "destinolatitude", c => c.Decimal(precision: 18, scale: 7));
            AlterColumn("dbo.ROTA_MODELO", "destinolongitude", c => c.Decimal(precision: 18, scale: 7));
        }
        
        public override void Down()
        {
            AlterColumn("dbo.ROTA_MODELO", "destinolongitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.ROTA_MODELO", "destinolatitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.ROTA_MODELO", "origemlongitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.ROTA_MODELO", "origemlatitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.PONTOS_ROTA_MODELO", "longitude", c => c.Decimal(precision: 10, scale: 7));
            AlterColumn("dbo.PONTOS_ROTA_MODELO", "latitude", c => c.Decimal(precision: 10, scale: 7));
        }
    }
}

﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Clientes;

namespace ATS.Application.Application
{
    public class ClienteApp : BaseApp<IClienteService>, IClienteApp
    {
        public ClienteApp(IClienteService service) : base(service)
        {
        }

        public Cliente GetAllChilds(int id)
        {
            return Service.GetChilds(id);
        }

        public ValidationResult Add(Cliente cliente)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = Service.Add(cliente);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Update(Cliente cliente)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = Service.Update(cliente);

                    if (!validationResult.IsValid)
                    {
                        return validationResult;
                    }
                    
                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public Cliente Get(int id)
        {
            return Service.Get(id);
        }

        public IQueryable<Cliente> GetAll()
        {
            return Service.GetAll();
        }

        public int? GetIdPorCpfCnpj(string cpfCnpj, int idEmpresa)
        {
            return Service.GetIdPorCpfCnpj(cpfCnpj, idEmpresa);
        }

        public IQueryable<Cliente> All()
        {
            return Service.All();
        }

        public ValidationResult Inativar(int idCliente)
        {
            try
            {
                using (var transactionScope = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = Service.Inativar(idCliente);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transactionScope.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Reativar(int idCliente)
        {
            try
            {
                using (TransactionScope transactionScope = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = Service.Reativar(idCliente);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transactionScope.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ClienteDetalhesModel ConsultarDetalhes(int idCliente)
        {
            return Service.ConsultarDetalhes(idCliente);
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarGrid(take, page, order, filters);
        }

        public byte[] GerarRelatorioGridClientes(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string logo, string extensao)
        {
            return Service.GerarRelatorioGridClientes(idEmpresa, order, filters, logo, extensao);
        }

        public bool VerificarClienteCadastrado(int idCliente)
        {
            return Service.VerificarClienteCadastrado(idCliente);
        }

        public IQueryable<Cliente> GetQuery(int idCliente)
        {
            return Service.GetQuery(idCliente);
        }

        public IQueryable<Cliente> GetClientesPorEmpresa(int idEmpresa)
        {
            return Service.GetClientesPorEmpresa(idEmpresa);
        }
    }
}
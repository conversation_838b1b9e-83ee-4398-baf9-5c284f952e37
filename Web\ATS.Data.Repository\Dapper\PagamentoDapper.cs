﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using ATS.Data.Context;
using ATS.Domain.DTO;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.Dapper.Common
{
    public class PagamentoDapper : DapperFactory<ViagemEvento>, IPagamentoDapper
    {
	    private readonly AtsContext _context;

	    public PagamentoDapper(AtsContext context)
	    {
		    _context = context;
	    }

	    public IEnumerable<TotalEstabelecimentoModel> ConsultarTotalCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, int page_, int take_, int idempresa)
        {
            var filterUf_ = "";
            if (!string.IsNullOrEmpty(UF_))
                filterUf_ = $" AND e.sigla LIKE '%{UF_}%' ";

            var query_ = string.Format(@"SET DATEFORMAT ymd; 
                                        SELECT
	                                        *
                                        FROM
	                                        (
		                                        SELECT
			                                        ROW_NUMBER () OVER (
				                                        ORDER BY
					                                        SUM (ve.valortotalpagamento) DESC
			                                        ) AS ranking,
			                                        ve.idestabelecimentobase,
			                                        eb.descricao AS NomeFantasia,
			                                        e.sigla AS UF,
			                                        c.nome AS Cidade,
			                                        eb.bairro AS Bairro,
			                                        eb.cnpjestabelecimento AS CNPJ,
			                                        SUM (ve.valortotalpagamento) AS Valor
		                                        FROM
			                                        VIAGEM_EVENTO ve
												JOIN VIAGEM v ON v.idviagem = ve.idviagem 
		                                        JOIN ESTABELECIMENTO_BASE eb ON eb.idestabelecimento = ve.idestabelecimentobase
		                                        JOIN ESTADO e ON e.idestado = eb.idestado
		                                        JOIN CIDADE c ON c.idcidade = eb.idcidade
		                                        WHERE
			                                        status = 2
		                                        AND datahorapagamento BETWEEN '{0:yyyy-MM-dd HH:mm:ss}'
		                                        AND '{1:yyyy- MM-dd HH:mm:ss}'
		                                        {2}
												AND v.idempresa = {5}

		                                        GROUP BY
			                                        ve.idestabelecimentobase,
			                                        eb.descricao,
			                                        e.sigla,
			                                        c.nome,
			                                        eb.bairro,
			                                        eb.cnpjestabelecimento
	                                        ) TOTAIS
                                        WHERE
	                                        ranking >= {3}
                                        AND ranking <= {4}", dataInicio_, dataFim_, filterUf_, page_, (take_ * page_), idempresa);

            AtsContext context_ = _context;

            var retorno = context_.Database.SqlQuery<TotalEstabelecimentoModel>(query_).ToList();

            retorno.ForEach(x => {
                x.ValorStrss = x.Valor.ToString("N");
            });

            return retorno;

        }

        public int CountTotalCurvaABC(DateTime dataInicio_, DateTime dataFim_, string UF_, int idempresa)
        {
            var filterUf_ = "";
            if (!string.IsNullOrEmpty(UF_))
                filterUf_ = $" AND e.sigla LIKE '%{UF_}%' ";

            var query_ = string.Format(@"SET DATEFORMAT ymd; 
                                         SELECT
	                                        count(1)
                                        FROM
	                                        (
		                                        SELECT
			                                        ve.idestabelecimentobase
		                                        FROM
			                                        VIAGEM_EVENTO ve
												JOIN VIAGEM v ON v.idviagem = ve.idviagem 
		                                        JOIN ESTABELECIMENTO_BASE eb ON eb.idestabelecimento = ve.idestabelecimentobase
		                                        JOIN ESTADO e ON e.idestado = eb.idestado
		                                        JOIN CIDADE c ON c.idcidade = eb.idcidade
		                                        WHERE
			                                        status = 2
		                                        AND datahorapagamento BETWEEN '{0:yyyy-MM-dd HH:mm:ss}'
		                                        AND '{1:yyyy-MM-dd HH:mm:ss}'
		                                        {2}
												AND v.idempresa = {3}

		                                        GROUP BY
			                                        ve.idestabelecimentobase,
			                                        eb.descricao,
			                                        e.sigla,
			                                        c.nome,
			                                        eb.bairro,
			                                        eb.cnpjestabelecimento
	                                        ) TOTAIS
	                                    ", dataInicio_, dataFim_, filterUf_, idempresa);

            AtsContext context_ = _context;

            return context_.Database.SqlQuery<int>(query_).FirstOrDefault();

        }

        public decimal SumValorTotalABC(DateTime dataInicio_, DateTime dataFim_, string UF_, double take_, double skip_, int idempresa)
        {
            var filterUf_ = "";
            if (!string.IsNullOrEmpty(UF_))
                filterUf_ = $" AND e.sigla LIKE '%{UF_}%' ";

            var query_ = string.Format(@"SET DATEFORMAT ymd; 
                                        SELECT
	                                        sum(Valor)
                                        FROM
	                                        (
		                                        SELECT
			                                        ROW_NUMBER () OVER (
				                                        ORDER BY
					                                        SUM (ve.valortotalpagamento) DESC
			                                        ) AS ranking,
			                                        ve.idestabelecimentobase,
			                                        eb.descricao AS NomeFantasia,
			                                        e.sigla AS UF,
			                                        c.nome AS Cidade,
			                                        eb.bairro AS Bairro,
			                                        eb.cnpjestabelecimento AS CNPJ,
			                                        SUM (ve.valortotalpagamento) AS Valor
		                                        FROM
			                                        VIAGEM_EVENTO ve
												JOIN VIAGEM v ON v.idviagem = ve.idviagem
		                                        JOIN ESTABELECIMENTO_BASE eb ON eb.idestabelecimento = ve.idestabelecimentobase
		                                        JOIN ESTADO e ON e.idestado = eb.idestado
		                                        JOIN CIDADE c ON c.idcidade = eb.idcidade
		                                        WHERE
			                                        status = 2
		                                        AND datahorapagamento BETWEEN '{0:yyyy-MM-dd HH:mm:ss}'
		                                        AND '{1:yyyy-MM-dd HH:mm:ss}'
		                                        {2}
												AND v.idempresa = {5}
		                                        GROUP BY
			                                        ve.idestabelecimentobase,
			                                        eb.descricao,
			                                        e.sigla,
			                                        c.nome,
			                                        eb.bairro,
			                                        eb.cnpjestabelecimento
	                                        ) TOTAIS
                                        WHERE
	                                        ranking > {3}
                                        AND ranking <= {4}", dataInicio_, dataFim_, filterUf_, skip_, take_, idempresa);

            AtsContext context_ = _context;

            return context_.Database.SqlQuery<decimal?>(query_).FirstOrDefault()??0;

        }

        public List<TotalEventoModel> GetTotalPagamento(DateTime dataInicio_, DateTime dataFinal_, int? UF_,
	        EStatusViagemEvento status_, int HabilitarPagamentoCartao, int? idEstabelecimento, int? idEmpresa)
        {

            var query_ = "";

            if (status_ == EStatusViagemEvento.Aberto)
            {
                query_ = string.Format(@"SET DATEFORMAT ymd; 
                    SELECT
	                    sum(ve.valorpagamento) as total,
	                    ve.tipoeventoviagem,
                        v.valorpedagio as Pedagio
                    FROM
	                    VIAGEM_EVENTO ve
                    JOIN VIAGEM v on v.idviagem = ve.idviagem
                    WHERE
	                    v.dataemissao > '{0}'
                    AND v.dataemissao < '{1}'
                    AND ve.status = {2}
                    AND ve.HABILITARPAGAMENTOCARTAO = {3}
					{4} 

                    GROUP BY 
	                    ve.tipoeventoviagem, v.valorpedagio", dataInicio_.ToString("yyyy-MM-dd HH:mm:ss"), dataFinal_.ToString("yyyy-MM-dd HH:mm:ss"), (int)status_, HabilitarPagamentoCartao, idEmpresa.HasValue && idEmpresa != 0 ? $" AND ve.idempresa = {idEmpresa} " : "");

            }  else
            {
                var estado = (UF_ != null) ? $" AND eb.idestado = {UF_} " : "";
                var estabelecimento = (idEstabelecimento != null) ? $" AND ve.idestabelecimentobase = {idEstabelecimento} " : "";

                query_ = string.Format(@" SET DATEFORMAT ymd; 
                        SELECT
	                        sum(ve.valortotalpagamento) as total,
	                        ve.tipoeventoviagem,
                            v.valorpedagio as Pedagio
                        FROM
	                        VIAGEM_EVENTO ve
                        JOIN VIAGEM v on v.idviagem = ve.idviagem
                        LEFT JOIN ESTABELECIMENTO_BASE eb ON eb.idestabelecimento = ve.idestabelecimentobase
                        WHERE
	                        ve.datahorapagamento > '{0}'
                        AND ve.datahorapagamento < '{1}'
                        AND ve.status = {2}
                        AND ve.HABILITARPAGAMENTOCARTAO = {3}
                        {4}
                        {5}
                        {6}
                        GROUP BY
	                        ve.tipoeventoviagem, v.valorpedagio", dataInicio_.ToString("yyyy-MM-dd HH:mm:ss"), dataFinal_.ToString("yyyy-MM-dd HH:mm:ss"), (int)status_, HabilitarPagamentoCartao, estabelecimento, estado, idEmpresa.HasValue && idEmpresa != 0 ? $" AND ve.idempresa = {idEmpresa} " : "");
            }
            
            AtsContext context_ = _context;

            return context_.Database.SqlQuery<TotalEventoModel>(query_)
                .ToList();
        }

        public IList<FaturamentoGridDto> RelatorioFaturamento(DateTime dataInicial, DateTime datafinal, int? empresaFiltro)
        {
	          string sSql = $@"DECLARE  @datainicial DATETIME2 = '{dataInicial:yyyy-MM-dd HH:mm:ss}',
                                        @datafinal DATETIME2 = '{datafinal:yyyy-MM-dd HH:mm:ss}';

                                WITH 
                                CTE_CargaAvulsa AS (
                                    SELECT 
                                        ca.idempresa,
                                        SUM(tc.valormovimentado) AS TotalCargaAvulsa
                                    FROM 
                                        TRANSACAO_CARTAO tc
                                    JOIN 
                                        CARGA_AVULSA ca ON ca.idcargaavulsa = tc.idcargaavulsa
                                    WHERE 
                                        tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                        AND tc.historico = 5 
                                        AND ca.tipocarga <> 3
                                    GROUP BY 
                                        ca.idempresa
                                ),

                                CTE_Pedagio AS (
                                    SELECT 
                                        v.idempresa,
                                        SUM(v.valorpedagio) AS TotalPedagio
                                    FROM 
                                        VIAGEM v
                                    WHERE 
                                        v.dataconfirmacaocreditopedagio IS NOT NULL
                                        AND v.dataconfirmacaocreditopedagio BETWEEN @datainicial AND @datafinal
                                    GROUP BY 
                                        v.idempresa
                                ),

                                CTE_Adiantamento AS (
                                    SELECT 
                                        ve.idempresa,
                                        SUM(tc.valormovimentado) AS TotalAdiantamento
                                    FROM 
                                        TRANSACAO_CARTAO tc
                                    JOIN 
                                        VIAGEM_EVENTO ve ON ve.idviagemevento = tc.idviagemevento
                                    WHERE 
                                        ve.tipoeventoviagem = 0 
                                        AND tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                        AND tc.historico = 1
                                    GROUP BY 
                                        ve.idempresa
                                ),

                                CTE_Saldo AS (
                                    SELECT 
                                        ve.idempresa,
                                        SUM(tc.valormovimentado) AS TotalSaldo
                                    FROM 
                                        TRANSACAO_CARTAO tc
                                    JOIN 
                                        VIAGEM_EVENTO ve ON ve.idviagemevento = tc.idviagemevento
                                    WHERE 
                                        ve.tipoeventoviagem = 1 
                                        AND tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                        AND tc.historico = 2
                                    GROUP BY 
                                        ve.idempresa
                                ),

                                CTE_Estadia AS (
                                    SELECT 
                                        ve.idempresa,
                                        SUM(tc.valormovimentado) AS TotalEstadia
                                    FROM 
                                        TRANSACAO_CARTAO tc
                                    JOIN 
                                        VIAGEM_EVENTO ve ON ve.idviagemevento = tc.idviagemevento
                                    WHERE 
                                        ve.tipoeventoviagem = 2 
                                        AND tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                        AND tc.historico = 46
                                    GROUP BY 
                                        ve.idempresa
                                ),

                                CTE_TarifaAntt AS (
                                    SELECT 
                                        ve.idempresa,
                                        SUM(tc.valormovimentado) AS TotalTarifaAntt
                                    FROM 
                                        TRANSACAO_CARTAO tc
                                    JOIN 
                                        VIAGEM_EVENTO ve ON ve.idviagemevento = tc.idviagemevento
                                    WHERE 
                                        ve.tipoeventoviagem = 4 
                                        AND tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                        AND tc.historico = 4
                                    GROUP BY 
                                        ve.idempresa
                                ),

                                CTE_Abastecimento AS (
                                    SELECT 
                                        ve.idempresa,
                                        SUM(tc.valormovimentado) AS TotalAbastecimento
                                    FROM 
                                        TRANSACAO_CARTAO tc
                                    JOIN 
                                        VIAGEM_EVENTO ve ON ve.idviagemevento = tc.idviagemevento
                                    WHERE 
                                        ve.tipoeventoviagem = 5 
                                        AND tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                        AND tc.historico = 3
                                    GROUP BY 
                                        ve.idempresa
                                ),

                                CTE_Viagens AS (
                                    SELECT 
                                        v.idempresa,
                                        COUNT(1) AS TotalViagens
                                    FROM 
                                        VIAGEM v 
                                    WHERE 
                                        v.datalancamento BETWEEN @datainicial AND @datafinal
                                    GROUP BY 
                                        v.idempresa
                                ),

                                CTE_Estorno AS (
                                    SELECT 
                                        idempresa,
                                        SUM(valormovimentado) AS TotalEstorno
                                    FROM (
                                        SELECT 
                                            ca.idempresa,
                                            tc.valormovimentado
                                        FROM 
                                            TRANSACAO_CARTAO tc
                                        JOIN 
                                            CARGA_AVULSA ca ON ca.idcargaavulsa = tc.idcargaavulsa
                                        WHERE 
                                            tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                            AND tc.historico IN (8, 6, 10, 47, 33, 7, 9)
                                            AND ca.tipocarga <> 3

                                        UNION ALL

                                        SELECT 
                                            ve.idempresa,
                                            tc.valormovimentado
                                        FROM 
                                            TRANSACAO_CARTAO tc 
                                        JOIN 
                                            VIAGEM_EVENTO ve ON ve.idviagemevento = tc.idviagemevento
                                        WHERE 
                                            tc.dataconfirmacaomeiohomologado BETWEEN @datainicial AND @datafinal
                                            AND tc.historico IN (8, 6, 10, 47, 33, 7, 9)
                                            
                                        UNION ALL
                                        
                                        SELECT 
                                            v.idempresa,
                                            SUM(v.valorpedagio) AS valormovimentado 
                                        FROM 
                                            VIAGEM v 
                                        JOIN 
                                            viagem_rota vr ON vr.idviagem = v.idviagem 
                                        WHERE 
                                            v.datacancelamentopedagio BETWEEN @datainicial AND @datafinal
                                            AND v.datacancelamentopedagio IS NOT NULL 
                                            AND vr.fornecedorpedagio = 1
                                        GROUP BY 
                                            v.idempresa
                                    ) AS CombinedResults
                                    GROUP BY 
                                        idempresa
                                )

                                SELECT
                                    e.cnpj AS Cnpj,
                                    e.razaosocial AS RazaoSocial,
                                    e.ativo as Ativo,
                                    COALESCE(ca.TotalCargaAvulsa, 0) AS 'CargaAvulsa',
                                    COALESCE(pd.TotalPedagio, 0) AS 'Pedagio',
                                    COALESCE(ad.TotalAdiantamento, 0) AS 'Adiantamento',
                                    COALESCE(ts.TotalSaldo, 0) AS 'Saldo',
                                    COALESCE(te.TotalEstadia, 0) AS 'Estadias',
                                    COALESCE(tt.TotalTarifaAntt, 0) AS 'TarifaANTT',
                                    COALESCE(ta.TotalAbastecimento, 0) AS 'Abastecimento',
                                    COALESCE(tv.TotalViagens, 0) AS 'QtdViagens',
                                    COALESCE(teo.TotalEstorno, 0) AS 'Estornos',
                                    CASE WHEN e.ativo = 1 THEN 'Ativo' ELSE 'Inativo' END AS StatusEmpresa,
                                    (COALESCE(ca.TotalCargaAvulsa, 0) + COALESCE(pd.TotalPedagio, 0) + COALESCE(ad.TotalAdiantamento, 0) +
                                     COALESCE(ts.TotalSaldo, 0) + COALESCE(te.TotalEstadia, 0) + COALESCE(tt.TotalTarifaAntt, 0) + 
                                     COALESCE(ta.TotalAbastecimento, 0)) AS 'Total'
                                FROM
                                    EMPRESA e
                                LEFT JOIN
                                    CTE_CargaAvulsa ca ON ca.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Pedagio pd ON pd.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Adiantamento ad ON ad.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Saldo ts ON ts.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Estadia te ON te.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_TarifaAntt tt ON tt.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Abastecimento ta ON ta.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Viagens tv ON tv.idempresa = e.idempresa
                                LEFT JOIN
                                    CTE_Estorno teo ON teo.idempresa = e.idempresa";

            if (empresaFiltro.HasValue)
                sSql += $" WHERE e.idempresa = {empresaFiltro}";
            
            var faturamentoList = RunSelect<FaturamentoGridDto>(sSql);
            
            return faturamentoList.ToList();
        }
    }
}

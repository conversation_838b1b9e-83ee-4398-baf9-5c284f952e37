using System;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class CombustivelJSLService : ServiceBase, ICombustivelJSLService
    {
        private readonly ICombustivelJSLRepository _combustivelJSLRepository;
        private readonly ICombustivelJSLEstabelecimentoBaseRepository _combustivelJSLEstabelecimentoBaseRepository;

        public CombustivelJSLService(ICombustivelJSLRepository combustivelJslRepository, ICombustivelJSLEstabelecimentoBaseRepository combustivelJslEstabelecimentoBaseRepository)
        {
            _combustivelJSLRepository = combustivelJslRepository;
            _combustivelJSLEstabelecimentoBaseRepository = combustivelJslEstabelecimentoBaseRepository;
        }

        public IQueryable<CombustivelJSL> GetQuery(int idcombustivel)
        {
            return _combustivelJSLRepository.Where(c => c.IdCombustivelJSL == idcombustivel);
        }

        public IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByCombustivel(int idcombustivel)
        {
            return _combustivelJSLEstabelecimentoBaseRepository.Where(c => c.IdCombustivelJSL == idcombustivel);
        }

        public IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByEstabelecimento(int idestabelecimento)
        {
            return _combustivelJSLEstabelecimentoBaseRepository.Where(c => c.IdEstabelecimentoBase == idestabelecimento);
        }

        public bool SincronizaATS(int idcombustivel)
        {
            return _combustivelJSLRepository.Where(c => c.IdCombustivelJSL == idcombustivel).Select(c => c.SincronizarATS).FirstOrDefault();
        }

        public bool CombustivelExistenteEstabelecimento(int idcombustivel, int idestabelecimento)
        {
            return _combustivelJSLEstabelecimentoBaseRepository.Where(c => c.IdCombustivelJSL == idcombustivel && c.IdEstabelecimentoBase == idestabelecimento).Any();
        }
        
        public ValidationResult Integrar(int idcombustivel, int idproduto, int idestabelecimento)
        {
            var validation = new ValidationResult();
            
            try
            {
                var repository = _combustivelJSLEstabelecimentoBaseRepository;
                
                var combustivelJslEstabelecimento = new CombustivelJSLEstabelecimentoBase();

                if (repository.Any(c => c.IdCombustivelJSL == idcombustivel && c.IdEstabelecimentoBase == idestabelecimento))
                    return validation;

                combustivelJslEstabelecimento.IdEstabelecimentoBase = idestabelecimento;
                combustivelJslEstabelecimento.IdProduto = idproduto;
                combustivelJslEstabelecimento.IdCombustivelJSL = idcombustivel;

                repository.Add(combustivelJslEstabelecimento);

                return validation;
            }
            catch (Exception e)
            {
                validation.Add(e.Message);
            }

            return validation;
        }

        public ValidationResult Deletar(int idproduto, int idestabelecimento)
        {
            var validation = new ValidationResult();
            
            try
            {
                var repository = _combustivelJSLEstabelecimentoBaseRepository;

                var registro = repository.Where(c => c.IdProduto == idproduto && c.IdEstabelecimentoBase == idestabelecimento).FirstOrDefault();

                if (registro == null)
                    return validation;
                
                repository.Delete(registro);
            }
            catch (Exception e)
            {
                validation.Add(e.Message);
            }

            return validation;
        }
    }
}
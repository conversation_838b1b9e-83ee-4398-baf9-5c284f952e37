﻿using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class TipoEstabelecimentoApp : AppBase, ITipoEstabelecimentoApp
    {
        private readonly ITipoEstabelecimentoService _tipoEstabelecimentoService;

        public TipoEstabelecimentoApp(ITipoEstabelecimentoService tipoEstabelecimentoService)
        {
            _tipoEstabelecimentoService = tipoEstabelecimentoService;
        }

        public ValidationResult Add(TipoEstabelecimento tipoEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoEstabelecimentoService.Add(tipoEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }
                
        public ValidationResult Update(TipoEstabelecimento tipoEstabelecimento, List<EUsoTipoEstabelecimento> usos)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoEstabelecimentoService.Update(tipoEstabelecimento, usos);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }
        
        public ValidationResult Inativar(int idTipoEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoEstabelecimentoService
                        .Inativar(idTipoEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }
        
        public ValidationResult Reativar(int idTipoEstabelecimento)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoEstabelecimentoService.Reativar(idTipoEstabelecimento);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasTiposBase, EUsoTipoEstabelecimento? uso)
        {
            return _tipoEstabelecimentoService.ConsultaGrid(idEmpresa, descricao, take, page, orderFilters, filters, apenasTiposBase, uso);
        }

        public TipoEstabelecimento Get(int idTipoEstabelecimento)
        {
            return _tipoEstabelecimentoService.Get(idTipoEstabelecimento);
        }
        
        public IQueryable<TipoEstabelecimento> GetTiposEstabelecimentoPorEmpresa(int? idEmpresa)
        {
            return _tipoEstabelecimentoService.GetTiposEstabelecimentoPorEmpresa(idEmpresa);
        }
    }
}

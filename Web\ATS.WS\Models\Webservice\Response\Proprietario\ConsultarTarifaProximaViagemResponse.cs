﻿namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class ConsultarTarifaProximaViagemResponse
    {
        /// <summary>
        /// Indica se a próxima viagem integrada irá declarar um novo CIOT.
        /// </summary>
        public bool GerarCiot { get; set; }

        /// <summary>
        /// Indica se a próxima viagem integrada para o proprietário e placa irá gerar CIOT e tarifa.
        /// </summary>
        public bool GerarTarifa { get; set; }

        /// <summary>
        /// Indica se o proprietário é equiparado a TAC
        /// </summary>
        public bool EquiparadoTac { get; set; }

        /// <summary>
        /// TAC, ETC ou CTC
        /// </summary>
        public string TipoProprietario { get; set; }

        /// <summary>
        /// Veículo pertence a frota do proprietário indicado.
        /// Tirar nullable quando implementar integração com ANTT.
        /// </summary>
        public bool? VeiculoPertenceFrota { get; set; }

        /// <summary>
        /// Quantidade total de tarifas de saque e transferência que serão pagas
        /// </summary>
        public int QuantidadeTotalTarifas { get; set; }

        /// <summary>
        /// Valor total de tarifas de saque e transferência que serão pagas
        /// </summary>
        public decimal ValorTotalTarifas { get; set; }

        /// <summary>
        /// Mensagem adicional informando motivos para a geração ou não geração das tarifas.
        /// </summary>
        /// <example>
        /// - Esta pessoa/empresa está equiparada a Transportador Autônomo de Cargas, e, portanto, está OBRIGADA a cumprir as disposições da Resolução ANTT 3658/11.
        /// - Esta viagem não irá gerar CIOT, pois já existe um contrato TAC/Agregado aberto para este proprietário.
        /// - Esta é uma viagem de transbordo/reentrega, portanto, irá retificar o CIOT já declarado anteriormente.
        /// - Esta viagem não irá gerar CIOT pois é para cliente internacional.
        /// - Veículo de placa \"{0}\" não está vinculado ao proprietario {1}.
        /// </example>
        public string MensagemInformativa { get; set; }
        
        /// <summary>
        /// Dado informativo para ter log da origem da informação.
        /// ANTT: Consulta online na ANTT
        /// Offline: Consulta baseada no cache do ATS
        /// </summary>
        public string Fonte { get; set; }
    }
}
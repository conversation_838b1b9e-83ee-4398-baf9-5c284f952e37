using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Common.Request
{
    public class AlterarValoresViagemRequestModel : RequestBase
    {        
        public int? IdViagem { get; set; }        
        public int? IdViagemEvento { get; set; }
        public string NumeroControle { get { return _numeroControle;} set { _numeroControle = value?.Trim(); } }        
        private string _numeroControle { get; set; }
        public string NumeroDocumento { get { return _numeroDocumento;} set { _numeroDocumento = value?.Trim(); } }
        private string _numeroDocumento { get; set; }
        public List<ViagemEventoValoresModel> ViagemEventos { get; set; }
        
        public ValidationResult Valida()
        {
            var validation = new ValidationResult();
           
            if (!IdViagem.HasValue && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControle) && string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("É obrigatório o preenchimento de ao menos dois campos: NumeroControle e NumeroDocumento ou IdViagem e IdViagemEvento", EFaultType.Error);
            
            if (IdViagem.HasValue && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControle) && string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("O campo IdViagemEvento é obrigatório e não foi preenchido", EFaultType.Error);
            
            if (!IdViagem.HasValue && IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControle) && string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("O campo IdViagem é obrigatório e não foi preenchido", EFaultType.Error);
            
            if (!IdViagem.HasValue && !IdViagemEvento.HasValue && !string.IsNullOrWhiteSpace(NumeroControle) && string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("O campo NumeroDocumento é obrigatório e não foi preenchido", EFaultType.Error);
            
            if (!IdViagem.HasValue && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControle) && !string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("O campo NumeroControle é obrigatório e não foi preenchido", EFaultType.Error);
            
            if (IdViagem.HasValue && !string.IsNullOrWhiteSpace(NumeroDocumento) && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroControle))
                validation.Add("É obrigatório o preenchimento de ao menos dois campos: NumeroControle e NumeroDocumento ou IdViagem e IdViagemEvento", EFaultType.Error);
            
            if (IdViagemEvento.HasValue && !string.IsNullOrWhiteSpace(NumeroControle) && !IdViagem.HasValue && string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("É obrigatório o preenchimento de ao menos dois campos: NumeroControle e NumeroDocumento ou IdViagem e IdViagemEvento", EFaultType.Error);
            
            if (IdViagemEvento.HasValue  && !string.IsNullOrWhiteSpace(NumeroDocumento) && !IdViagem.HasValue &&  string.IsNullOrWhiteSpace(NumeroControle))
                validation.Add("É obrigatório o preenchimento de ao menos dois campos: NumeroControle e NumeroDocumento ou IdViagem e IdViagemEvento", EFaultType.Error);
            
            if (IdViagem.HasValue  && !string.IsNullOrWhiteSpace(NumeroControle) && !IdViagemEvento.HasValue && string.IsNullOrWhiteSpace(NumeroDocumento))
                validation.Add("É obrigatório o preenchimento de ao menos dois campos: NumeroControle e NumeroDocumento ou IdViagem e IdViagemEvento", EFaultType.Error);

            if (CNPJEmpresa?.OnlyNumbers().Length != 14)
                validation.Add("O campo CNPJEmpresa deve conter 14 caracteres", EFaultType.Error);
            
            if (CNPJAplicacao?.OnlyNumbers().Length != 14)
                validation.Add("O campo CNPJAplicacao deve conter 14 caracteres", EFaultType.Error);
            
            if (!string.IsNullOrWhiteSpace(NumeroControle) && NumeroControle.Length > 300)
                validation.Add("O campo NumeroControle não pode conter mais de 300 caracteres", EFaultType.Error);
            
            if (!string.IsNullOrWhiteSpace(NumeroDocumento) && NumeroDocumento.Length > 300)
                validation.Add("O campo NumeroDocumento não pode conter mais de 300 caracteres", EFaultType.Error);
            
            return validation;
        }

    }

    public class ViagemEventoValoresModel
    {
        public decimal ValorPagamento { get; set; } = 0;
        public decimal ValorTotalPagamento { get; set; } = 0;
        public int? IdViagemEvento { get; set; }
        public string NumeroControle { get; set; }
        public EStatusViagemEvento? Status { get; set; } = EStatusViagemEvento.Aberto;

        /// <summary>
        /// Utilizado apenas para criar novo evento. Não deve permitir mudar o tipo de um evento já inserido.
        /// ConnectUs utiliza este campo. 
        /// </summary>
        public ETipoEventoViagem? TipoEvento { get; set; }
        
        /// <summary>
        /// Utilizado apenas para criar novo evento. Não deve permitir mudar o tipo de um evento já inserido.
        /// ConnectUs utiliza este campo. 
        /// </summary>
        public bool? HabilitarPagamentoCartao { get; set; }

    }
}
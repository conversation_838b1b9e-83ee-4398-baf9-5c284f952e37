﻿using System.ComponentModel;
using System.Runtime.Serialization;

namespace Extratta.ImportacaoDadosConsumer.Events
{
    public class VeiculoIntegrarFilaEvent
    {
        public string CPFCNPJProprietario { get; set; }
        public string CNPJFilial { get; set; }
        public string CPFMotorista { get; set; }
        public string <PERSON>laca { get; set; }
        public string Chassi { get; set; }
        public int? AnoFabricacao { get; set; }
        public int? AnoModelo { get; set; }
        public string Marca { get; set; }
        public ETipoContratoFilaEvent TipoContrato { get; set; }
        public string Modelo { get; set; }
        public bool? ComTracao { get; set; }
        public ETipoRodagemFilaEvent TipoRodagem { get; set; }
        public int? IdTipoCavalo { get; set; }
        public int? IdTipoCarreta { get; set; }
        public int? QuantidadeEixos { get; set; }
        public long? NumeroFrota { get; set; }
        public int? CodigoDaOperacao { get; set; }
        public string Municipio { get; set; }
        public int? IBGECidade { get; set; }
        public int? IBGEEstado { get; set; }
        public string RENAVAM { get; set; }
        public string CNPJEmpresa { get; set; }
        public string Token { get; set; }
    }
    
    public enum ETipoRodagemFilaEvent
    {
        [EnumMember, Description("Simples")]
        Simples = 1,

        [EnumMember, Description("Duplo")]
        Duplo = 2
    }
}
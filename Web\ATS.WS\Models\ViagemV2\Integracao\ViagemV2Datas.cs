using System;
using System.Globalization;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2Datas
    {
        /// <summary>
        /// Data da coleta da carga
        /// </summary>
        public DateTime? DataColeta { get; set; }

        /// <summary>
        /// Data de previsão de entrega da carga
        /// </summary>
        public DateTime? DataPrevisaoEntrega { get; set; }

        /// <summary>
        /// Data que foi emitido o documento fiscal
        /// </summary>
        public DateTime? DataEmissaoDocumentoFiscal { get; set; }

        public ValidationResult ValidarEntrada()
        {
            DateTime d;

            if (DataColeta.HasValue)
            {
                if (!DateTime.TryParse(DataColeta.Value.ToString(), out d))
                    return new ValidationResult().Add("Data de coleta inválida.", EFaultType.Error);

                if (DataColeta.Value < DateTime.Now)
                    return new ValidationResult().Add("Data de coleta não pode ser menor que a data atual.", EFaultType.Error);
            }

            if (DataPrevisaoEntrega.HasValue)
            {
                if (!DateTime.TryParse(DataPrevisaoEntrega.Value.ToString(), out d))
                    return new ValidationResult().Add("Data de previsão de entrega inválida.", EFaultType.Error);

                if (DataPrevisaoEntrega.Value < DateTime.Now)
                    return new ValidationResult().Add("Data de previsão de entrega não pode ser menor que a data atual.", EFaultType.Error);
            }

            if (DataEmissaoDocumentoFiscal.HasValue)
                if (!DateTime.TryParse(DataEmissaoDocumentoFiscal.Value.ToString(), out d))
                    return new ValidationResult().Add("Data de emissão do documento fiscal inválida.", EFaultType.Error);

            return new ValidationResult();
        }
    }
}
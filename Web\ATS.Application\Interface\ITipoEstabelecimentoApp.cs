﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ITipoEstabelecimentoApp
    {
        ValidationResult Add(TipoEstabelecimento tipoEstabelecimento);
        ValidationResult Update(TipoEstabelecimento tipoEstabelecimento, List<EUsoTipoEstabelecimento> usos);
        ValidationResult Inativar(int idTipoEstabelecimento);
        ValidationResult Reativar(int idTipoEstabelecimento);
        object ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasTiposBase, EUsoTipoEstabelecimento? uso);
        TipoEstabelecimento Get(int idTipoEstabelecimento);
        IQueryable<TipoEstabelecimento> GetTiposEstabelecimentoPorEmpresa(int? idEmpresa);
    }
}
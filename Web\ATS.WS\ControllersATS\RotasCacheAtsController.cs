﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using ATS.Application.Application;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class RotasCacheAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;

        public RotasCacheAtsController(IUserIdentity userIdentity)
        {
            _userIdentity = userIdentity;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult ConsultarGrid(DateTime dataInicio,DateTime dataFim, int take, int page, 
            OrderFiltersPedagio order, List<QueryFiltersPedagio> filters)
        {
            try
            {
                return ResponderSucesso(RotasCacheApp.
                    CreateByUsuario(_userIdentity).ConsultarGrid(dataInicio, dataFim, take, page, order, filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult DeletarRotaCache(Guid guidRota)
        {
            try
            {
                RotasCacheApp.CreateByUsuario(_userIdentity).DeletarCache(guidRota);
                return ResponderSucesso($"Rota cache {guidRota} limpa com sucesso.");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
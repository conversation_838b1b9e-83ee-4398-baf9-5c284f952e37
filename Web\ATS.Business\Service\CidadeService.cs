﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using ATS.Domain.Interface.Dapper;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Domain.Enum;
using ATS.Domain.Models.CidadeModels;
using NLog;

namespace ATS.Domain.Service
{
    public class CidadeService : ServiceBase, ICidadeService
    {
        private readonly ICidadeRepository _cidadeRepository;
        private readonly ICidadeDapper _cidadeDapper;

        public CidadeService(ICidadeRepository cidadeRepository, ICidadeDapper cidadeDapper)
        {
            _cidadeRepository = cidadeRepository;
            _cidadeDapper = cidadeDapper;
        }

        /// <summary>
        /// Retorna as informações da cidade
        /// </summary>
        /// <param name="id"><PERSON><PERSON><PERSON> da cidade</param>
        /// <returns></returns>
        public Cidade Get(int id)
        {
            return _cidadeRepository
                .Include(x => x.Estado)
                .Include(x => x.Estado.Pais)
                .FirstOrDefault(x => x.IdCidade == id);
        }

        public object ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var cidades = _cidadeRepository.All()
                .Include(x => x.Estado);

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                cidades = cidades.OrderBy(x => x.IdEstado);
            else
                cidades = cidades.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            cidades = cidades.AplicarFiltrosDinamicos<Cidade>(filters);

            return new
            {
                totalItems = cidades.Count(),
                items = cidades.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdCidade,
                    x.Ativo,
                    x.Nome,
                    x.IBGE,
                    NomeEstado = x.Estado?.Nome
                })
            };
        }

        /// <summary>
        /// Retorna as informações da cidade com os objetos filhos inclusos
        /// </summary>
        /// <param name="id">Código da cidade</param>
        /// <returns></returns>
        public Cidade GetWithAllChilds(int id)
        {
            return _cidadeRepository.GetWithAllChilds(id);
        }

        /// <summary>
        /// Inserir o registro de cidade
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ValidationResult Add(Cidade entity)
        {
            try
            {
                _cidadeRepository.Add(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro da cidade
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ValidationResult Update(Cidade entity)
        {
            try
            {
                _cidadeRepository.Update(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        public int? GetIdCidade(int nIBGE)
        {
            return _cidadeRepository.Find(x => x.IBGE == nIBGE)
                .FirstOrDefault()?.IdCidade;
        }

        /// <summary>
        /// Método utilizado para listar as cidade
        /// </summary>
        /// <param name="nome">Nome da cidade</param>
        /// <returns>IQueryable de CidadeGrid</returns>
        public IQueryable<CidadeGrid> Consultar(string nome)
        {
            if (nome == null)
                nome = string.Empty;

            return _cidadeRepository.Consultar(nome);
        }

        /// <summary>
        /// Retorna o objeto de Cidade a partir do código IBGE da Cidade
        /// </summary>
        /// <param name="nIBGE">Código do IBGE</param>
        /// <returns></returns>
        public Cidade GetCidadeFromIBGE(int nIBGE)
        {
            return _cidadeRepository
                .Find(x => x.IBGE == nIBGE && x.Ativo)
                .Include(x => x.Estado)
                .Include(x => x.Estado.Pais)
                .FirstOrDefault();
        }

        public IQueryable<Cidade> GetQueryByIBGE(int ibge)
        {
            return _cidadeRepository.All().Where(c => c.IBGE == ibge);
        }

        /// <summary>
        /// Retorna a lista de cidades pelo código do estado
        /// </summary>
        /// <param name="idEstado"></param>
        /// <returns></returns>
        public List<Cidade> GetTodos()
        {
            return _cidadeRepository.All().ToList();
        }

        /// <summary>
        /// Retorna a lista de cidade a partir do código do estado
        /// </summary>
        /// <param name="idEstado">Código do estado</param>
        /// <returns></returns>
        public IQueryable<Cidade> GetCidades(int idEstado)
        {
            return _cidadeRepository.Find(x => x.IdEstado == idEstado && x.Ativo);
        }

        /// <summary>
        /// Retorna a lista de cidades atualizadas a partir da data
        /// </summary>
        /// <param name="uf">Sigla do estado</param>
        /// <param name="dataBase">Data base para filtro</param>
        /// <returns></returns>
        public IQueryable<Cidade> GetCidadesAtualizadas(string uf, DateTime dataBase)
        {
            /*return _cidadeRepository
                .Find(x => x.DataAlteracao > dataBase && (uf == null || x.Estado.Sigla == uf));*/
            
            var cidades = _cidadeDapper.GetCidades(uf, dataBase);
            return cidades;
        }

        /// <summary>
        /// Retorna todas as cidades ativas
        /// </summary>
        /// <returns>IQueryable de cidade</returns>
        public IQueryable<Cidade> All()
        {
            return _cidadeRepository.All().Where(x => x.Ativo);
        }

        /// <summary>
        /// Retorna todas as cidades parecidas com #
        /// </summary>
        /// <returns>IQueryable de cidade</returns>
        public List<Cidade> WhereNomeLike(string nome)
        {
            var cidDapper = _cidadeDapper.GetCidadesPorNome(nome);

            return cidDapper;

            //var cidRep = _cidadeRepository
            //    .Include(x => x.Estado)
            //    .Include(x => x.Estado.Pais);

            //var cidades = cidRep.Where(x => x.Ativo).ToList();

            //foreach (var item in cidades)
            //{
            //    string comAcentos = "ÄÅÁÂÀÃäáâàãÉÊËÈéêëèÍÎÏÌíîïìÖÓÔÒÕöóôòõÜÚÛüúûùÇç";
            //    string semAcentos = "AAAAAAaaaaaEEEEeeeeIIIIiiiiOOOOOoooooUUUuuuuCc";
            //
            //    for (int i = 0; i < comAcentos.Length; i++)
            //    {
            //        nome = nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
            //        item.Nome = item.Nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
            //        item.Nome = item.Nome.ToLower();
            //    }
            //}
            //
            //var cidsIDs = cidades.Where(p => p.Nome.ToLower().Contains(nome.ToLower()) || p.Nome.ToLower().StartsWith(nome.ToLower()) || p.Nome.ToLower().EndsWith(nome.ToLower())).ToList().Select(x => x.IdCidade);
            //
            //return cidRep.Where(x => x.Ativo && cidsIDs.Contains(x.IdCidade)).ToList();
        }

        public Cidade WhereNomeIs(string nome)
        {
            var cidRep = _cidadeRepository
                .Include(x => x.Estado)
                .Include(x => x.Estado.Pais);

            var cidades = cidRep.Where(x => x.Ativo).ToList();

            //foreach (var item in cidades)
            //{
            //    string comAcentos = "ÄÅÁÂÀÃäáâàãÉÊËÈéêëèÍÎÏÌíîïìÖÓÔÒÕöóôòõÜÚÛüúûùÇç";
            //    string semAcentos = "AAAAAAaaaaaEEEEeeeeIIIIiiiiOOOOOoooooUUUuuuuCc";

            //    for (int i = 0; i < comAcentos.Length; i++)
            //    {
            //        nome = nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
            //        item.Nome = item.Nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
            //        item.Nome = item.Nome.ToLower();
            //    }
            //}

            var cidadeId = cidades.FirstOrDefault(p => p.Nome.ToLower() == nome.ToLower());

            return cidRep.FirstOrDefault(x => x.Ativo && x.IdCidade == cidadeId.IdCidade);
        }

        #region Verifica String do Ponto Referencia
        public Cidade VerificaString(string nome)
        {
            var cidRep = _cidadeRepository
                .Include(x => x.Estado)
                .Include(x => x.Estado.Pais);

            var cidades = cidRep.Where(x => x.Ativo).ToList();

            foreach (var item in cidades)
            {
                string comAcentos = "ÄÅÁÂÀÃäáâàãÉÊËÈéêëèÍÎÏÌíîïìÖÓÔÒÕöóôòõÜÚÛüúûùÇç";
                string semAcentos = "AAAAAAaaaaaEEEEeeeeIIIIiiiiOOOOOoooooUUUuuuuCc";

                for (int i = 0; i < comAcentos.Length; i++)
                {
                    nome = nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString().RemoveSpecialCharacters().RemoveDiacritics().ToLower());
                    item.Nome = item.Nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
                    item.Nome = item.Nome.ToLower().RemoveDiacritics().RemoveSpecialCharacters().RemoveDiacritics().ToLower();
                }
            }

            var cidadeId = cidades.FirstOrDefault(p => p.Nome.ToLower().RemoveDiacritics().RemoveSpecialCharacters() == nome.ToLower().RemoveDiacritics().RemoveSpecialCharacters());

            return cidRep.FirstOrDefault(x => x.Ativo && x.IdCidade == cidadeId.IdCidade);
        }

        #endregion

        /// <summary>
        /// Atualiza o registro da cidade, inserindo a latitude e longitude
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <param name="latitude">Latitude</param>
        /// <param name="longitude">Longitude</param>
        /// <returns></returns>
        public ValidationResult UpdateLocalizao(int idCidade, decimal latitude, decimal longitude)
        {
            Cidade cidade = Get(idCidade);
            if (cidade == null)
                return new ValidationResult().Add($"Cidade não localizada.");

            cidade.Latitude = latitude;
            cidade.Longitude = longitude;

            return Update(cidade);
        }

       

        /// <summary>
        /// Retorna a latitude e longitude da cidade
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <returns></returns>
        public LocalizacaoModel GetLocalizacao(int idCidade, EOrigemConsumoServicoExterno origemConsumoServicoExterno)
        {
            // Deverá buscar a localização dos valores da tabela de cidade
            // Não existindo valor, deverá consutlar no WebService da Google

            LocalizacaoModel localizacao = _cidadeRepository.Find(x => x.IdCidade == idCidade && x.Ativo)
                .Select(x => new LocalizacaoModel
                {
                    Latitude = x.Latitude,
                    Longitude = x.Longitude
                })?.FirstOrDefault();

            if (localizacao != null && (!localizacao.Latitude.HasValue || !localizacao.Longitude.HasValue))
            {
                Cidade cidade = this.Get(idCidade);
                if (cidade != null)
                {
                    localizacao = new GoogleMapsHelper().GetLocalizacao($"{cidade.Nome},{cidade.Estado.Sigla},{cidade.Estado.Pais.Sigla}", 
                        origemConsumoServicoExterno);
                }
            }

            return localizacao;
        }

        /// <summary>
        /// Retorna o código da cidade a partir do estado e nome
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="nome"></param>
        /// <returns></returns>
        public int? GetIdCidadePorNome(int idEstado, string nome)
        {
            var semAcento = nome.ToLower().RemoveDiacritics();
            return _cidadeRepository
                .Find(x => x.IdEstado == idEstado && x.Nome.ToLower() == nome.ToLower() && x.Ativo)?
                .Select(x => x.IdCidade)?.FirstOrDefault();
        }

        public int GetIdCidadePorNome(string nome)
        {
            var semAcento = nome.ToLower().RemoveDiacritics().RemoveSpecialCharacters();
            var cidade = _cidadeRepository
                .Find(x => (x.Nome.ToLower() == nome.ToLower() || x.Nome.ToLower() == semAcento) && x.Ativo).FirstOrDefault();

            if (cidade == null) throw new Exception($"Cidade {nome} não encontrada na base de dados!");

            return cidade.IdCidade;
        }

        /// <summary>
        /// Inativa uma cidade na base de dados
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idCidade)
        {
            try
            {
                ICidadeRepository repository = _cidadeRepository;

                Cidade cidade = repository.Get(idCidade);
                if (!cidade.Ativo)
                    return new ValidationResult().Add($"Cidade já inativa na base de dados.");

                cidade.Ativo = false;

                repository.Update(cidade);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa uma cidade na base de dados
        /// </summary>
        /// <param name="idCidade">ID da cidade</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idCidade)
        {
            try
            {
                ICidadeRepository repository = _cidadeRepository;

                Cidade cidade = repository.Get(idCidade);
                if (cidade.Ativo)
                    return new ValidationResult().Add($"Cidade já ativa na base de dados.");

                cidade.Ativo = true;

                repository.Update(cidade);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public CidadeDetalhesResponse GetDetalhes(int idcidade)
        {
            var repository = _cidadeRepository;

            var response = repository.Where(c => c.IdCidade == idcidade)
                .Select(c => new CidadeDetalhesResponse
                {
                    Cidade = c.Nome,
                    Estado = c.Estado.Sigla,
                    Pais = c.Estado.Pais.Nome,
                    CodigoIbge = c.IBGE,
                    Latitude = c.Latitude,
                    Longitude = c.Longitude
                }).FirstOrDefault();

            return response;
        }

        public List<Cidade> ConsultarPaginadas(int? IBGEEstado, int? ibge, DateTime? dataBase, int? take, int? skip)
        {
            var cidades = _cidadeRepository.GetAll()
                .Include(x => x.Estado)
                .Include(x => x.Estado.Pais);

            if (IBGEEstado.HasValue)
                cidades = cidades.Where(x => x.Estado.IBGE == IBGEEstado);
            if (ibge.HasValue && ibge > 0)
                cidades = cidades.Where(x => x.IBGE == ibge);
            if (dataBase.HasValue)
                cidades = cidades.Where(x => x.DataAlteracao > dataBase);
            if (skip.HasValue && skip > 0)
                cidades = cidades.OrderBy(x => x.IdCidade).Skip(skip.Value);
            if (take.HasValue)
                cidades = cidades.OrderBy(x => x.IdCidade).Take(take.Value);
            return cidades.ToList();
        }

        public Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude)
        {
            return _cidadeDapper.GetCidadeMaisProxima(latitude, longitude);
        }
        
        public int GetIdPorIBGE(int ibge)
        {
            return _cidadeRepository
                .Find(x => x.IBGE == ibge && x.Ativo)
                .Select(x => x.IdCidade)
                .FirstOrDefault();
        }

        public Cidade getByNameState(string nome, string state)
        {
            try
            {
                var cidRep = _cidadeRepository
                                .Include(x => x.Estado);

                var cidades = cidRep.Where(x => x.Ativo).ToList();

                foreach (var item in cidades)
                {
                    string comAcentos = "ÄÅÁÂÀÃäáâàãÉÊËÈéêëèÍÎÏÌíîïìÖÓÔÒÕöóôòõÜÚÛüúûùÇç";
                    string semAcentos = "AAAAAAaaaaaEEEEeeeeIIIIiiiiOOOOOoooooUUUuuuuCc";

                    for (int i = 0; i < comAcentos.Length; i++)
                    {
                        nome = nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
                        item.Nome = item.Nome.Replace(comAcentos[i].ToString(), semAcentos[i].ToString());
                        item.Nome = item.Nome.ToLower();
                    }
                }

                //var cidadeId = cidades.FirstOrDefault(p => p.Nome.ToLower() == nome.ToLower());
                var cidadeState = cidades.Find(x => x.Nome.ToLower() == nome.ToLower() && x.Estado.Sigla == state);

                return cidRep.FirstOrDefault(x => x.Ativo && x.IdCidade == cidadeState.IdCidade);
            }
            catch (Exception)
            {

                return null;
            }

        }
        
        public List<int> GetIbgeCidade(List<CidadeIbgeRequestModel> cidadesIbgeRequestModel,bool errorCidadeNotFound = false)
        {
            var listaItems = new List<int>();

            if (cidadesIbgeRequestModel == null)
                throw new Exception("Localidade não disponível para consulta de rota!");

            foreach (var cidadeIbgeRequestModel in cidadesIbgeRequestModel)
            {
                int? ibge;
                
                ibge = _cidadeRepository
                    .Find(o => o.Ativo && o.Nome == cidadeIbgeRequestModel.NomeCidade && o.Estado.Sigla == cidadeIbgeRequestModel.SiglaEstado)
                    .Select(o => o.IBGE).FirstOrDefault();

                if (ibge.HasValue && ibge.Value > 0)
                {
                    listaItems.Add(ibge.Value);
                    continue;
                }

                throw new Exception($"Cidade {cidadeIbgeRequestModel.NomeCidade},{cidadeIbgeRequestModel.SiglaEstado} não encontrada na base," +
                                    $"Cadastre-a para utilização da mesma.");
            }

            return listaItems;
        }
        
        public int GetIbgeCidade(CidadeIbgeRequestModel cidadesIbgeRequestModel)
        {
            int ibge = 0;

            if (!string.IsNullOrEmpty(cidadesIbgeRequestModel.NomeCidade) && !string.IsNullOrEmpty(cidadesIbgeRequestModel.SiglaEstado))
            {
                ibge = _cidadeRepository
                    .Find(o => o.Nome == cidadesIbgeRequestModel.NomeCidade && o.Estado.Sigla == cidadesIbgeRequestModel.SiglaEstado)
                    .Select(o => o.IBGE).FirstOrDefault() ?? 0;
            }

            if (ibge == 0)
            {
                LogManager.GetCurrentClassLogger().Info("Rota modelo => Cidade {cidadeIbgeRequestModel.NomeCidade}" +
                                                        $"/ Estado {cidadesIbgeRequestModel.SiglaEstado} não encontrada!");
            }
               
            return ibge;
        }

        public bool ValidarIbgeCadastrado(int ibge)
        {
            var ibgeCadastrado = _cidadeRepository.Where(o => o.IBGE == ibge).FirstOrDefault();
            return ibgeCadastrado != null;
        }
    }
}
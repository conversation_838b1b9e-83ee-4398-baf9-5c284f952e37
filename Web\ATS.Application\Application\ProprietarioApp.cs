﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Transactions;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Proprietarios;

namespace ATS.Application.Application
{
    public class ProprietarioApp : BaseApp<IProprietarioService>, IProprietarioApp
    {
        private readonly IProprietarioRepository _proprietarioRepository;
        public ProprietarioApp(IProprietarioService service, IProprietarioRepository proprietarioRepository) : base(service)
        {
            _proprietarioRepository = proprietarioRepository;
        }
        
        /// <summary>
        /// Retorna o objeto de proprietário contendo os registros filhos
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Proprietario Get(int id)
        {
            return Service.Get(id);
        }

        /// <summary>
        /// Buscar Proprietário
        /// </summary>
        /// <param name="id">Código de Proprietário</param>
        /// <returns>Objeto Proprietario</returns>
        public Proprietario GetAllChilds(int id)
        {
            return Service.GetAllChilds(id);
        }

        /// <summary>
        /// Adicionar o proprietario a base de dados
        /// </summary>
        /// <param name="proprietario">Dados do Proprietário</param>
        /// <returns>Objeto de validação ValidationResult</returns>
        public ValidationResult Add(Proprietario proprietario)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = Service.Add(proprietario);
                    
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        /// <summary>
        /// Atualizar o registro de Proprietário
        /// </summary>
        /// <param name="proprietario">Dados de Proprietário</param>
        /// <returns>Objeto de validação ValidationResult</returns>
        public ValidationResult Update(Proprietario proprietario)
        {
            try
            {
                using (var transaction =
                    new TransactionScope(TransactionScopeOption.Required,
                        new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = Service.Update(proprietario);
                
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        /// <summary>
        /// Buscar Proprietário por CPF ou CNPJ
        /// </summary>
        /// <param name="cpfCnpj">Documento CPF ou CNPJ</param>
        /// <returns>Objeto de Proprietário</returns>
        public Proprietario GetPorCpfCnpj(string cpfCnpj)
        {
            return _proprietarioRepository.GetPorCpfCnpj(cpfCnpj);
        }

        /// <summary>
        /// Buscar Proprietário por CPF ou uma lista de CNPJs
        /// </summary>
        /// <param name="cpf"></param>
        /// <param name="cnpjs"></param>
        /// <returns></returns>
        public Proprietario GetPorCpfCnpj(string cpf, List<string> cnpjs)
        {
            return _proprietarioRepository.GetPorCpfCnpj(cpf, cnpjs);
        }
        
        /// <summary>
        /// Consultar por CpfCnpj e/ou IdEmpresa
        /// </summary>
        /// <param name="cpfCnpj"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public Proprietario GetPorCpfCnpj(string cpfCnpj, int? idEmpresa)
        {
            return _proprietarioRepository.GetPorCpfCnpj(cpfCnpj, idEmpresa);
        }

        /// <summary>
        /// Retorna o ID do proprietário
        /// </summary>
        /// <param name="cpfCnpj">Documento CPF ou CNPJ</param>
        /// <param name="idEmpresa"></param>
        /// <returns>Objeto de Proprietário</returns>
        public int? GetIdPorCpfCnpj(string cpfCnpj, int? idEmpresa = null)
        {
            return _proprietarioRepository.GetIdPorCpfCnpj(cpfCnpj, idEmpresa);
        }

        /// <summary>
        /// Buscar Id do proprietário por CNPJ e empresa
        /// </summary>
        /// <param name="cpfCnpj"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public int? GetIdByCpfCnpjWithEmpresa(string cpfCnpj, int? idEmpresa)
        {
            return Service.GetIdByCpfCnpjWithEmpresa(cpfCnpj, idEmpresa);
        }

        /// <summary>
        /// Consultar detalhes para a viaigem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ProprietarioConsultaDetalheViagemResponse ConsultaDetalheParaViagem(ProprietarioConsultaDetalheViagemRequest request)
        {
            return Service.ConsultaDetalheParaViagem(request);
        }

        /// <summary>
        /// Atualizar RNTRC do proprietario
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ValidationResult AtualizarRntrc(ProprietarioAtualizarRNTRCRequest request)
        {
            return Service.AtualizarRntrc(request);
        }

        /// <summary>
        /// Atualizar Base Equiparado Tac
        /// </summary>
        /// <param name="cnpjcpf"></param>
        /// <returns></returns>
        public AtualizaBaseEquiparadoTacResponse AtualizarBaseEquiparadoTac(string cnpjcpf = null)
        {
            return Service.AtualizarBaseEquiparadoTac(cnpjcpf);
        }
        
        /// <summary>
        /// Atualizar Cadastro Servico Cartao
        /// </summary>
        /// <param name="cnpjcpf"></param>
        /// <returns></returns>
        public AtualizaBaseEquiparadoTacResponse AtualizarCadastroServicoCartao(List<string> cnpjcpf)
        {
            return Service.AtualizarCadastroServicoCartao(cnpjcpf);
        }

        /// <summary>
        /// Listar todos os proprietários ativos
        /// </summary>
        /// <returns>IQueryable de Proprietario</returns>
        public IQueryable<Proprietario> All()
        {
            return _proprietarioRepository.All();
        }

        /// <summary>
        /// Retorna os proprietários a partir dos dados do filtro
        /// </summary>
        /// <param name="razaoSocial">Razão Social do Proprietário</param>
        /// <param name="idEmpresa">Id do empresa</param>
        /// <param name="idUsuarioLogOn">Código do usuário logado</param>
        /// <param name="onlyAtivo">Indica se deve buscar somente os ativos</param>
        /// <returns>IQueryable de ProprietarioGrid</returns>
        public IQueryable<Proprietario> Consultar(string razaoSocial, int idEmpresa, int? idUsuarioLogOn, bool? onlyAtivo)
        {
            return Service.Consultar(razaoSocial, idEmpresa, idUsuarioLogOn, onlyAtivo);
        }
        
        public IQueryable<Proprietario> Find(Expression<Func<Proprietario, bool>> predicate, bool @readonly = false)
        {
            return Service.Find(predicate, @readonly);
        }

        /// <summary>
        /// Retorna o ID do proprietário, informando o CPF/CNPJ do proprietário
        /// </summary>
        /// <param name="idEmpresa">ID do empresa</param>
        /// <param name="cpfCnpjEmpresa">CPF/CNPJ do Empresa</param>
        /// <returns></returns>
        public int? GetIdProprietario(int idEmpresa, string cpfCnpjEmpresa)
        {
            return Service.GetIdProprietario(idEmpresa, cpfCnpjEmpresa);
        }

        /// <summary>
        /// Ativar ou inativar
        /// </summary>
        /// <returns></returns>
        public ValidationResult AlterarStatus(int idProprietario)
        {
            try
            {
                return Service.AlterarStatus(idProprietario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        /// <summary>
        /// Retorna os dados para a grid do Web Novo
        /// </summary>
        /// <returns></returns>
        public object ConsultarGridProprietarios(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return Service.ConsultarGridProprietarios(idEmpresa, take, page, orderFilters, filters);
        }

        /// <summary>
        /// Geração do relatório a partir da grid
        /// </summary>
        /// <returns></returns>
        public byte[] GerarRelatorioGridProprietarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao)
        {
            return Service.GerarRelatorioGridProprietarios(idEmpresa, orderFilters, filters, logo,  extensao);
        }

        /*public ETipoCarregamentoFrete GetComportamentoProcessoCartao(string documentoProprietario)
        {
            var proprietarioService = Service;
            var proprietarioId = GetIdPorCpfCnpj(documentoProprietario);

            return proprietarioService.GetComportamentoProcessoCartao(proprietarioId ?? 0);
        }*/

        /// <summary>
        /// Consultar as propriedades (IdProprietario, CpfCnpj, Nome, Rntrc) por Id do Proprietário
        /// </summary>
        /// <param name="idProprietario"></param>
        /// <returns></returns>
        public ConsultaProprietarioPorCnpjCpfResponseDTO ConsultarPorId(int idProprietario)
        {
            var consulta = _proprietarioRepository.AllAtivos().Where(c => c.IdProprietario == idProprietario).Select(a => new ConsultaProprietarioPorCnpjCpfResponseDTO
            {
                IdProprietario = a.IdProprietario,
                CpfCnpj = a.CNPJCPF,
                Nome = a.NomeFantasia,
                RNTRC = a.RNTRC
            }).FirstOrDefault();

            return consulta;
        }

        /// <summary>
        /// Consultar as propriedades (IdProprietario, CpfCnpj, Nome, Rntrc) por CPF/CNPJ e Empresa
        /// </summary>
        /// <param name="cnpjCpf"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public ConsultaProprietarioPorCnpjCpfResponseDTO ConsultarPorCnpjCpf(string cnpjCpf, int idEmpresa)
        {
            var consulta = _proprietarioRepository.AllAtivos().Where(c => c.CNPJCPF == cnpjCpf && c.IdEmpresa == idEmpresa).Select(a => new ConsultaProprietarioPorCnpjCpfResponseDTO
            {
                IdProprietario = a.IdProprietario,
                CpfCnpj = a.CNPJCPF,
                Nome = a.NomeFantasia,
                RNTRC = a.RNTRC
            }).FirstOrDefault();

            return consulta;
        }

        public bool Any(string cnpjCpf, int idEmpresa)
        {
            return Service.Any(cnpjCpf, idEmpresa);
        }
        //public decimal GetPorcentagemTransferenciaMotorista(string documentoProprietario)
        //{
        //    var proprietarioService = Service;

        //    var proprietarioId = GetIdPorCPFCNPJ(documentoProprietario);

        //    return proprietarioService.GetPorcentagemTransferenciaMotorista(proprietarioId ?? 0);
        //}

        /// <summary>
        /// Consultar se o Proprietário é equiparado a TAC
        /// </summary>
        /// <param name="cpfCnpjProprietario"></param>
        /// <param name="rntrcProprietario"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <returns></returns>
        public ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario, string cnpjEmpresa)
        {
            return Service.EquiparadoTac(cpfCnpjProprietario, rntrcProprietario, cnpjEmpresa);
        }

        /// <summary>
        /// Consultar situação do proprietário na ANTT
        /// </summary>
        /// <param name="cpfCnpj"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <returns></returns>
        public ConsultaSituacaoTransportadorResponse ConsultarSituacaoAntt(string cpfCnpj, string cnpjEmpresa)
        {
            return Service.ConsultarSituacaoAntt(cpfCnpj, cnpjEmpresa);
        }
        
        /// <summary>
        /// Buscar dados do Proprietário ANTT por CPF/CNPJ
        /// </summary>
        /// <param name="cnpjCpf"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <returns></returns>
        public ProprietarioAnttDto GetDadosProprietarioAntt(string cnpjCpf, string cnpjEmpresa)
        {
            return Service.GetDadosProprietarioAntt(cnpjCpf, cnpjEmpresa);
        }
        
        /// <summary>
        /// Verifica se permite transferir entre proprietario e motorista sem viagens vinculadas 
        /// </summary>
        /// <param name="cnpjCpf"></param>
        /// <returns></returns>
        public bool PermiteTransferenciaSemCartaFrete(string cnpjCpf)
        {
            return _proprietarioRepository.PermiteTransferenciaSemCartaFrete(cnpjCpf);
        }

        public bool PertenceAEmpresa(int idempresa, int idProprietario)
        {
            return _proprietarioRepository.Any(c => c.IdEmpresa == idempresa && c.IdProprietario == idProprietario);
        }
    }
}

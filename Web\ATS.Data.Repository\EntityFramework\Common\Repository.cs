﻿using ATS.CrossCutting.IoC;
using ATS.Data.Context;
using ATS.Data.Context.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Linq.Expressions;
using NLog;
using System.Threading.Tasks;

namespace ATS.Data.Repository.EntityFramework.Common
{
    public class Repository<TEntity> : IRepository<TEntity> where TEntity : class
    {

        public static Logger _logger = LogManager.GetCurrentClassLogger();

        #region Constructor

        public Repository(AtsContext context)
        {
            // Context = new IoC ().Get<ContextManager<AtsContext>>().GetContext();
            Context = context;
        }

        protected IDbContext Context { get; set; }

        #endregion

        #region IDispose

        public void Dispose()
        {
            Dispose(true);

            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposing)
                return;

            Context?.Dispose();
        }

        #endregion

        #region CRUD

        public virtual TEntity Add(TEntity entity, bool autoSaveChanges = true)
        {
            try
            {
                Context.Set<TEntity>().Add(entity);

                var beginingTime_ = DateTime.Now;

                if (autoSaveChanges)
                    SaveChanges(entity);

                var endTime_ = DateTime.Now;

                var diffTime_ = endTime_ - beginingTime_;

                if (diffTime_.Seconds > 20)
                {
                    _logger.Error("Tempo de duração do comando executado excedeu o limite:" + entity.GetType().Name + " Tempo de duração: " + diffTime_.Seconds.ToString());
                }

                return entity;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao adicionar registro");

                var msg = e.Message;
                if (!string.IsNullOrWhiteSpace(e.InnerException?.Message) && !msg.Contains(e.InnerException.Message))
                    msg += " / " + e.InnerException.Message;

                var mensagemBase = e.GetBaseException()?.Message;
                if (!string.IsNullOrEmpty(mensagemBase) && !msg.Contains(mensagemBase))
                    msg += " / " + mensagemBase;

                throw new Exception(msg);
            }
        }

        public virtual void Update(TEntity entity, bool autoSaveChanges = true)
        {
            try
            {
            if (Context.Entry(entity).State != EntityState.Added)
                Context.Entry(entity).State = EntityState.Modified;

            var beginingTime_ = DateTime.Now;

            if (autoSaveChanges)
                SaveChanges(entity);

            var endTime_ = DateTime.Now;

            var diffTime_ = endTime_ - beginingTime_;

            if (diffTime_.Seconds > 20)
            {
                _logger.Error("Tempo de duração do comando executado excedeu o limite:" + entity.GetType().Name + " Tempo de duração: " + diffTime_.Seconds.ToString());
            }
        }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao atualizar registro");
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual void Delete(TEntity entity, bool autoSaveChanges = true)
        {
            try
            {
                Context.Entry(entity).State = EntityState.Deleted;
                Context.Set<TEntity>().Remove(entity);

                var beginingTime_ = DateTime.Now;

                if (autoSaveChanges)
                    SaveChanges(entity);

                var endTime_ = DateTime.Now;

                var diffTime_ = endTime_ - beginingTime_;

                if (diffTime_.Seconds > 20)
                {
                    _logger.Error("Tempo de duração do comando executado excedeu o limite:" + entity.GetType().Name +
                                  " Tempo de duração: " + diffTime_.Seconds.ToString());
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao remover registro");
                throw;
            }
        }

        public virtual void DeleteRange(List<TEntity> entities, bool autoSaveChanges = true)
        {
            try
            {
//                Context.Entry(entities).State = EntityState.Deleted;
                Context.Set<TEntity>().RemoveRange(entities);

                if (autoSaveChanges)
                    Context.SaveChanges();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, "Erro ao remover faixa de registros");
                throw;
            }
        }

        #endregion

        #region Selectors

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public virtual TEntity Get(int id)
        {
            return Context.Set<TEntity>().Find(id);
        }

        public IQueryable<TEntity> All(bool @readonly = false)
        {
            return @readonly
                 ? Context.Set<TEntity>().AsNoTracking().AsQueryable()
                 : Context.Set<TEntity>().AsQueryable();
        }

        public IQueryable<TEntity> Find(Expression<Func<TEntity, bool>> predicate, bool @readonly = false)
        {
            return @readonly
                 ? Context.Set<TEntity>().Where(predicate).AsNoTracking().AsQueryable()
                 : Context.Set<TEntity>().Where(predicate).AsQueryable();
        }

        public IQueryable<TEntity> GetAll()
        {
            return Context.Set<TEntity>();
        }

        public IQueryable<TEntity> AsNoTracking()
        {
            return Context.Set<TEntity>().AsNoTracking();
        }

        public IQueryable<TEntity> Include<TProperty>(Expression<Func<TEntity, TProperty>> aPath)
        {
            return Context.Set<TEntity>().Include(aPath);
        }

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public TEntity GetById(params object[] keyValues)
        {
            return Context.Set<TEntity>().Find(keyValues);
        }

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public TEntity GetByIdDetached(params object[] keyValues)
        {
            TEntity lEntity = GetById(keyValues);

            if (lEntity != null)
                Detach(lEntity);

            return lEntity;
        }

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public TEntity First(Expression<Func<TEntity, bool>> predicate = null)
        {
            if (predicate == null)
                return Context.Set<TEntity>().First();

            return Context.Set<TEntity>().First(predicate);
        }

        public TResult First<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector)
        {
            if (predicate == null)
                return Context.Set<TEntity>().Select(selector).First();

            return Context.Set<TEntity>().Where(predicate).Select(selector).First();
        }

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public TEntity FirstOrDefault(Expression<Func<TEntity, bool>> predicate = null)
        {
            if (predicate == null)
                return Context.Set<TEntity>().FirstOrDefault();

            return Context.Set<TEntity>().FirstOrDefault(predicate);
        }

        public TResult FirstOrDefault<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector)
        {
            if (predicate == null)
                return Context.Set<TEntity>().Select(selector).FirstOrDefault();

            return Context.Set<TEntity>().Where(predicate).Select(selector).FirstOrDefault();
        }

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public TEntity Single(Expression<Func<TEntity, bool>> predicate = null)
        {
            if (predicate == null)
                return Context.Set<TEntity>().Single();

            return Context.Set<TEntity>().Single(predicate);
        }

        public TResult Single<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector)
        {
            if (predicate == null)
                return Context.Set<TEntity>().Select(selector).Single();

            return Context.Set<TEntity>().Where(predicate).Select(selector).Single();
        }

        [Obsolete("Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public TEntity SingleOrDefault(Expression<Func<TEntity, bool>> predicate = null)
        {
            if (predicate == null)
                return Context.Set<TEntity>().SingleOrDefault();

            return Context.Set<TEntity>().SingleOrDefault(predicate);
        }

        public TResult SingleOrDefault<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector)
        {
            if (predicate == null)
                return Context.Set<TEntity>().Select(selector).SingleOrDefault();

            return Context.Set<TEntity>().Where(predicate).Select(selector).SingleOrDefault();
        }

        public IQueryable<TEntity> Where(Expression<Func<TEntity, bool>> predicate)
        {
            return Context.Set<TEntity>().Where(predicate);
        }

        public IQueryable<TResult> Select<TResult>(Expression<Func<TEntity, TResult>> selector)
        {
            return Context.Set<TEntity>().Select(selector);
        }

        public bool Any(Expression<Func<TEntity, bool>> predicate)
        {
            return Context.Set<TEntity>().Any(predicate);
        }

        public IQueryable<TEntity> Query()
        {
            return Context.Set<TEntity>().AsQueryable();
        }

        public IQueryable<TEntity> Consultar(List<QueryFilters> Filters, OrderFilters Order)
        {

            Expression<Func<TEntity, bool>> expressao = null;

            var context = Context.Set<TEntity>().AsQueryable();

            foreach (var item in Filters)
            {
                switch (item.Operador)
                {
                    case EOperador.Contains:
                        expressao = StringContains(item.Campo, item.Valor);
                        context = context.Where(expressao);
                        break;
                    case EOperador.GreaterThanOrEqual:
                        expressao = GetExpression(ExpressionType.GreaterThan, item.Campo, Expression.Constant(item.Valor));
                        context = context.Where(expressao);
                        break;
                    case EOperador.LessThanOrEqual:
                        expressao = GetExpression(ExpressionType.LessThan, item.Campo, Expression.Constant(item.Valor));
                        context = context.Where(expressao);
                        break;
                    case EOperador.Exact:
                        expressao = GetExpression(ExpressionType.Equal, item.Campo, Expression.Constant(item.Valor));
                        context = context.Where(expressao);
                        break;

                }
            }

            return context;
        }

        private Expression<Func<TEntity, bool>> GetExpression(ExpressionType operation, string fieldName, Expression value)
        {
            var type = typeof(TEntity);
            var parameter = Expression.Parameter(type);
            var member = Expression.PropertyOrField(parameter, fieldName);
            var expressao = Expression.MakeBinary(operation, member, value);
            var expression = Expression.Lambda<Func<TEntity, bool>>(expressao, parameter);
            return expression;
        }

        private static Expression<Func<TEntity, bool>> StringContains(string fieldName, object contains)
        {
            var parameter = Expression.Parameter(typeof(TEntity), "x");
            var property = Expression.Property(parameter, fieldName);
            var value = Expression.Constant(contains);
            var type = value.Type;
            var containsmethod = type.GetMethod("Contains", new[] { typeof(string) });
            var call = Expression.Call(property, containsmethod, value);
            return Expression.Lambda<Func<TEntity, bool>>(call, parameter);
        }

        public void SaveChanges(TEntity entity)
        {
            Context.SaveChanges<TEntity>(entity);
        }

        public void SaveChanges()
        {
            Context.SaveChanges();
        }

        public Task SaveChangesAsync()
        {
            return Context.SaveChangesAsync();
        }

        #endregion

        #region Entity Framework

        public DbPropertyValues OriginalValues(TEntity entity)
        {
            object values = Context.Entry(entity).OriginalValues[""];

            if (values != null)
                return (DbPropertyValues) values;

            return null;
        }

        public void Detach(TEntity entity)
        {
            Context.Entry(entity).State = EntityState.Detached;
        }

        public void Detach(List<TEntity> entities)
        {
            foreach (TEntity entity in entities)
                Detach(entity);
        }

        #endregion
    }
}
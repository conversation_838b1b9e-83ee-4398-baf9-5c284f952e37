using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Database
{
    public interface IParametrosRepository: IRepository<Parametros>
    {
        ValidationResult SetValue<T>(T valor, string nomeTabela, string chave, int idRegistro, int? idEmpresa);
        ValidationResult SetValue<T>(T valor, string nomeTabela, string chave, string idRegistro, int? idEmpresa);
        Dictionary<string, object> GetValues<T>(string nomeTabela, IList<string> Chaves, int idRegistro, int? idEmpresa);
        Dictionary<string, object> GetValues<T>(string nomeTabela, IList<string> Chaves, string idRegistro, int? idEmpresa);
        object GetValue<T>(string nomeTabela, string chave, int idRegistro, int? idEmpresa);
        object GetValue<T>(string nomeTabela, string chave, string idRegistro, int? idEmpresa);
        bool AnyParametro(string nomeTabela, string chave, int idRegistro, int? idEmpresa);
        bool AnyParametro(string nomeTabela, string chave, string idRegistro, int? idEmpresa);
        Parametros GetParametro(string nomeTabela, string chave, int idRegistro, int? idEmpresa);
        Parametros GetParametro(string nomeTabela, string chave, string idRegistro, int? idEmpresa);
    }
}
﻿using ATS.MongoDB.Enum;
using MongoDB.Bson;
namespace ATS.MongoDB.Context.Entities
{
    public class Media
    {
        // Chave primária para integração com a base do ATS
        public ObjectId _id { get; set; }
        // Coluna com os dados em base 64.., video, imagem,texto....
        public string Data { get; set; }
        // Inteiro para classificar o registro por algum tipo numerico
        public EMediaType Type { get; set; }

        //Nome do arquivo para download
        public string FileName { get; set; }
        public string MimeType { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;

namespace Extratta.ImportacaoDadosConsumer.Events
{
    public class ProprietarioIntegrarFilaEvent
    {
        public string CnpjCpf { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string IE { get; set; }
        public string RNTRC { get; set; }
        public ETipoContratoFilaEvent TipoContrato { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string Endereco { get; set; }
        public string Inss { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public string CNPJEmpresa { get; set; }
        public string Token { get; set; }
        public List<ProprietarioContatoIntegrarFilaEvent> Contatos { get; set; }
        public List<ProprietarioEnderecoIntegrarFilaEvent> Enderecos { get; set; }
    }

    public class ProprietarioContatoIntegrarFilaEvent
    {
        public string Telefone  { get; set; }
        public string Email     { get; set; }
        public string Celular { get; set; }
    }
    
    public class ProprietarioEnderecoIntegrarFilaEvent
    {
        public int IBGEEstado    { get; set; }
        public int IBGECidade    { get; set; }
        public string CEP           { get; set; }
        public string Endereco      { get; set; }
        public int? Numero          { get; set; }
        public string Bairro        { get; set; }
    }
}
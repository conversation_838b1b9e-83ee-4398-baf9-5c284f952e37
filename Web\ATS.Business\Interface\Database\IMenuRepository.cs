﻿using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IMenuRepository : IRepository<Menu>
    {
        IQueryable<MenuGrid> Consultar(string descricao);
        IQueryable<Menu> GetPorIdGrupoUsuario(int idGrupoUsuario);
        IQueryable<Menu> GetMenusPermissao(int? idGrupoUsuario);
        IQueryable<GrupoUsuarioMenuGrid> GetArvoreMenuPorIdGrupoUsuario(int idGrupoUsuario);
        IQueryable<AutorizacaoEmpresaMenuGrid> GetArvoreMenuPorIdEmpresa(int idEmpresa);
        IQueryable<GrupoUsuarioMenuGrid> GetMenusPorModulo(int idModulo, int idGrupoUsuario, int idEmpresa, int perfil);
        List<GrupoUsuarioMenuGrid> GetMenusDisponiveisPorEmpresaModuloAutorizacao(int idModulo, int idEmpresa, bool verificarAutorizacaoEmpresa);
    }
}
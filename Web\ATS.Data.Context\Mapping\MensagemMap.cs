using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class MensagemMap : EntityTypeConfiguration<Mensagem>
    {
        public MensagemMap()
        {
            ToTable("MENSAGEM");

            HasKey(t => t.IdMensagem);

            Property(t => t.IdMensagem)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Assunto)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Conteudo)
                .IsRequired()
                .HasColumnType("text")
                .IsMaxLength();

            Property(t => t.ConteudoMobile)
               .HasColumnType("text")
               .IsMaxLength();

            HasRequired(t => t.Remetente)
                .WithMany(t => t.Remetente)
                .HasForeignKey(t => t.IdUsuarioRemetente);
            
        }
    }
}
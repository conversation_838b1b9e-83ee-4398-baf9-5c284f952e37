﻿using System;
using System.Collections.Generic;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Autofac;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Common.Request
{
    public class ClienteRequestModel : RequestBase
    {
        public int? IdCliente { get; set; }
        public int BACENPais { get; set; }
        public int IBGEEstado { get; set; }
        public int IBGECidade { get; set; }

        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public ETipoPessoa TipoPessoa { get; set; }
        public string CNPJCPF { get; set; }
        public string RG { get; set; }
        public string OrgaoExpedidorRG { get; set; }
        public string IE { get; set; }
        public byte[] Logo { get; set; }

        public string Celular { get; set; }
        public string Email { get; set; }

        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public int? Numero { get; set; }
        public string Bairro { get; set; }

        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public decimal? RaioLocalizacao { get; set; }

        public List<ClienteEnderecoModel> ClienteEnderecos { get; set; }
        
        public bool ObrigarCPFReceber { get; set; }
        public bool PermitirAlterarData { get; set; }
        public bool EnviarSMSConfirmacao { get; set; }
        public bool EnviarEmailConfirmacao { get; set; }
        public bool AutenticarCodigoBarraNF { get; set; }

        public List<ProdutoEspecieModel> ListaProdutoEspecie { get; set; }

        public ValidationResult ValidarEntrada(ETipoCliente tipoCliente)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var cidadeApp = scope.Resolve<ICidadeApp>();
                var estadoApp = scope.Resolve<IEstadoApp>();
                var paisApp = scope.Resolve<IPaisApp>();
                
                var validation = new ValidationResult();
                string tipoClienteDescricao;

                switch (tipoCliente)
                {
                    case ETipoCliente.Origem:
                        tipoClienteDescricao = "cliente de origem";
                        break;
                    case ETipoCliente.Destino:
                        tipoClienteDescricao = "cliente de destino";
                        break;
                    case ETipoCliente.Ambos:
                        tipoClienteDescricao = "cliente";
                        break;
                    default:
                        tipoClienteDescricao = "cliente";
                        break;
                }

                if (BACENPais == 0)
                    validation.Add($"Código Bacen do {tipoClienteDescricao} não informado.", EFaultType.Error);
                else if (!paisApp.VerificarBacenCadastrado(BACENPais))
                    validation.Add($"Código Bacen do {tipoClienteDescricao} inválido.", EFaultType.Error);

                if (IBGEEstado == 0)
                    validation.Add($"Código IBGE do estado do {tipoClienteDescricao} não informado.", EFaultType.Error);
                else if (!estadoApp.ValidarIbgeCadastrado(IBGEEstado))
                    validation.Add($"Código IBGE do estado do {tipoClienteDescricao} inválido.", EFaultType.Error);

                if (IBGECidade == 0)
                    validation.Add($"Código IBGE da cidade do {tipoClienteDescricao} não informado.", EFaultType.Error);
                else if (!cidadeApp.ValidarIbgeCadastrado(IBGECidade))
                    validation.Add($"Código IBGE da cidade do {tipoClienteDescricao} inválido.", EFaultType.Error);

                if (string.IsNullOrEmpty(CNPJCPF))
                    validation.Add($"Documento do {tipoClienteDescricao} não informado.", EFaultType.Error);
                else if (CNPJCPF.OnlyNumbers().Length > 14)
                    validation.Add($"Documento do {tipoClienteDescricao} não pode ter mais de 14 caracteres.", EFaultType.Error);
                else if (!CNPJCPF.ValidateDocument())
                    validation.Add($"Documento do {tipoClienteDescricao} é inválido");

                if (string.IsNullOrEmpty(RazaoSocial))
                    validation.Add($"Razão Social do {tipoClienteDescricao}, não foi informada.", EFaultType.Error);
                else if (RazaoSocial.Length > 100)
                    validation.Add($"Razão Social do {tipoClienteDescricao} não pode ter mais de 100 caracteres.", EFaultType.Error);

                if (string.IsNullOrEmpty(CEP))
                    validation.Add($"CEP do {tipoClienteDescricao} não informado.", EFaultType.Error);
                else if (CEP.Length > 8)
                    validation.Add($"CEP do {tipoClienteDescricao} não pode ter mais de 8 caracteres", EFaultType.Error);

                if (!Enum.IsDefined(typeof(ETipoPessoa), TipoPessoa))
                    validation.Add($"Tipo de pessoa do {tipoClienteDescricao} inválido.", EFaultType.Error);

                if (string.IsNullOrEmpty(Endereco))
                    validation.Add($"Endereço do {tipoClienteDescricao} não informado", EFaultType.Error);
                else if (Endereco.Length > 100)
                    validation.Add($"Endereço do {tipoClienteDescricao} não pode ter mais de 100 caracteres.", EFaultType.Error);

                if (string.IsNullOrEmpty(Bairro))
                    validation.Add($"Bairro do {tipoClienteDescricao} não informado", EFaultType.Error);
                else if (Bairro.Length > 100)
                    validation.Add($"Bairro do {tipoClienteDescricao} não pode ter mais de 100 caracteres.", EFaultType.Error);

                if (!string.IsNullOrEmpty(NomeFantasia))
                    if (NomeFantasia.Length > 100)
                        validation.Add($"Nome Fantasia do {tipoClienteDescricao} não pode ter mais de 100 caracteres.", EFaultType.Error);

                return validation;
            }
        }
    }
    
    public class ClienteEnderecoModel
    {
        public int BACENPais { get; set; }

        public int IBGEEstado { get; set; }

        public int IBGECidade { get; set; }

        public string CEP { get; set; }

        public string Endereco { get; set; }

        public string Complemento { get; set; }

        public int? Numero { get; set; }

        public string Bairro { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

    }

    public class ClienteEnderecoResponseModel
    {
        public int BACENPais { get; set; }

        public int IBGEEstado { get; set; }

        public int IBGECidade { get; set; }

        public string CEP { get; set; }

        public string Endereco { get; set; }

        public string Complemento { get; set; }

        public int? Numero { get; set; }

        public string Bairro { get; set; }
    }

}
﻿using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class PracasPedagioResponseDTO
    {
        public string ValorTotalPassagem { get; set; }
        public string ValorTotalEstimado { get; set; }
        public List<PracasPedagioItemResponseDTO> Pracas { get; set; }
    }
    
    public class PracasPedagioItemResponseDTO
    {
        public string PrecoEstimado { get; set; }
        public string PrecoPassagem { get; set; }
        public string Descricao { get; set; }
        public string Concessionaria { get; set; }
        public string DataPassagem { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public bool Planejado { get; set; }
    }
}
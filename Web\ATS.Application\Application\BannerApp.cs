﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Mail;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.Banner;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;

namespace ATS.Application.Application
{
    public class BannerApp : BaseApp<IBannerService>, IBannerApp
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IEmailService _emailService;
        
        public BannerApp(IBannerService service, IUsuarioRepository usuarioRepository, IUserIdentity userIdentity, IParametrosGenericoService parametrosGenericoService, IEmailService emailService) : base(service)
        {
            _usuarioRepository = usuarioRepository;
            _userIdentity = userIdentity;
            _parametrosGenericoService = parametrosGenericoService;
            _emailService = emailService;
        }

        public BannerConsultarResponse ConsultarAtual()
        {
            return Service.ConsultarAtual();
        }

        public BannerConsultarResponse ConsultarPorId(int idBanner)
        {
            return Service.ConsultarPorId(idBanner);
        }

        public BannerGridResponse ConsultarBanners()
        {
            return Service.ConsultarBanners();
        }

        public ValidationResult Visualizar(BannerVisualizarRequest request)
        {
            return Service.Visualizar(request);
        }
        
        public ValidationResult Integrar(BannerIntegrarRequest request)
        {
            return Service.Integrar(request);
        }

        public BannerAlterarStatusResponse AlterarStatus(BannerAlterarStatusRequest request)
        {
            return Service.AlterarStatus(request);
        }
    }
}

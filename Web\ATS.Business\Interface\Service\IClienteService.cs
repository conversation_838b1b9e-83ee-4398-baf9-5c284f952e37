﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models.Clientes;

namespace ATS.Domain.Interface.Service
{
    public interface IClienteService : IBaseService<IClienteRepository>
    {
        Cliente GetChilds(int id);
        IQueryable<Cliente> All();
        ValidationResult Add(Cliente cliente);
        ValidationResult Update(Cliente cliente);
        ValidationResult Inativar(int idCliente);
        ValidationResult Reativar(int idCliente);
        Cliente Get(int id);
        int? GetIdPorCpfCnpj(string cpfCnpj, int idEmpresa);
        string GetCnpjCpf(int idCliente);
        IQueryable<Cliente> GetAll();
        IQueryable<Cliente> GetClientesPorEmpresa(int idEmpresa);
        byte[] GerarRelatorioGridClientes(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string logo, string extensao);
        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        bool ClienteValidoIntegracao(int? id, int? idempresa);
        ClienteDetalhesModel ConsultarDetalhes(int idCliente);
        bool VerificarClienteCadastrado(int idCliente);
        IQueryable<Cliente> GetQuery(int idCliente);
    }
}
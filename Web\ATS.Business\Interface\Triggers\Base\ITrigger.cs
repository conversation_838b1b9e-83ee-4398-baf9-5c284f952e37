﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Enum;
using ATS.Domain.Trigger.Base;
using Autofac;

namespace ATS.Domain.Interface.Triggers
{
    public interface ITrigger<TEntity> 
        where TEntity : class 
    { 
        bool RegisterAfterTrigger(EOperationTrigger operation, TriggerAction<TEntity> method, string name);
        bool RegisterBeforeTrigger(EOperationTrigger operation, TriggerAction<TEntity> method, string name);
        bool ExecuteAfterTrigger( EOperationTrigger operation, ILifetimeScope serviceProvider, TEntity entity, TEntity old);
        bool ExecuteBeforeTrigger(EOperationTrigger operation, ILifetimeScope serviceProvider, TEntity entity, TEntity old);

    }
}

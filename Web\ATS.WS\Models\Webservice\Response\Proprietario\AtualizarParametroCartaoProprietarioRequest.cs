﻿using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Models.Parametro;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    /// <summary>
    /// Atualizar parâmetros relacionado ao cartão do proprietário
    /// </summary>
    public class AtualizarParametroCartaoProprietarioRequest : RequestBase
    {
        public string CnpjCpfProprietario { get; set; }

        /// <summary>
        /// Percentual de transferência automatica aos motorista quando efetuada a carga da viagem
        /// </summary>
        public IList<PercentualTransferenciaMotorista> PercentuaisTransferenciaMotorista { get; set; }
    }
}
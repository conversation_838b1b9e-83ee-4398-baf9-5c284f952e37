﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Entities
{
    public class PlanoEmpresa
    {
        public int IdPlanoEmpresa { get; set; }
        public int IdEmpresa { get; set; }
        public int IdPlano { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime DataAtualizacao { get; set; }
        public int IdUsuarioCadastro { get; set; }
        public int IdUsuarioAtualizacao { get; set; }
        public bool Ativo { get; set; }

        #region Relationships
        public virtual Plano Plano { get; set; }
        public virtual Empresa Empresa { get; set; }
        public virtual Usuario Usuario { get; set; }

        #endregion
    }
}

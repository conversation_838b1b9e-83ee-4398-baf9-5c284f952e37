﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ViagemEventoCampoCartaoOrigemCartaoDestino : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.VIAGEM_EVENTO", "cartaodestino", c => c.String(maxLength: 100, unicode: false));
            AddColumn("dbo.VIAGEM_EVENTO", "cartaoorigem", c => c.String(maxLength: 100, unicode: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.VIAGEM_EVENTO", "cartaoorigem");
            DropColumn("dbo.VIAGEM_EVENTO", "cartaodestino");
        }
    }
}

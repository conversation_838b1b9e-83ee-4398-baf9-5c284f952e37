﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Grid;
using ATS.Domain.Models.Menu;

namespace ATS.Application.Interface
{
    public interface IModuloApp : IAppBase<Modulo>
    {
        Modulo Get(int id);
        ValidationResult Add(Modulo modulo);
        ValidationResult Update(Modulo modulo);
        IQueryable<Modulo> All();
        IQueryable<Modulo> Consultar(string descricao);
        ValidationResult Inativar(int id);
        ValidationResult Reativar(int id);

        object GetModulosPorUsuario(int idUsuario);

        /// <summary>
        /// Retorna todos os módulos ao qual a empresa possui.
        /// </summary>
        /// <returns></returns>
        List<Modulo> GetModulosPorEmpresa(int idEmpresa);

        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        List<ModulosCadastroMenu> GetModulosCadastroMenu();
    }
}
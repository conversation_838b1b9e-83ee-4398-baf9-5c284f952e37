﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Protocolo
{
    public class ProtocoloReport
    {
        public byte[] GerarRelatorio(ProtocoloModel headerModel, List<ProtocoloListModel> itens)
        {
            var report = new LocalReport { EnableExternalImages = true };
            try
            {
                report.DataSources.Add(new ReportDataSource
                {
                    Value = new List<ProtocoloModel> { headerModel },
                    Name = "ProtocoloModel"
                });
                report.DataSources.Add(new ReportDataSource
                {
                    Value = itens,
                    Name = "ProtocoloListModel"
                });
                report.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Protocolo.Protocolo.rdlc";
                report.Refresh();
                return report.Render("PDF");
            }
            finally
            {
                report.ReleaseSandboxAppDomain();
                report = null;
            }
        }

        public byte[] GerarRelatorioProtocolosComOcorrencia(List<RelatorioProtocoloComOcorrencia> itens)
        {
            var report = new LocalReport { EnableExternalImages = true };
            try
            {
               
                report.DataSources.Add(new ReportDataSource
                {
                    Value = itens,
                    Name = "DataSet2"
                });
                report.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Protocolo.RelatorioProtocoloComOcorrencia.rdlc";
                report.Refresh();
                return report.Render("PDF");
            }
            finally
            {
                report.ReleaseSandboxAppDomain();
                report = null;
            }
        }

        public byte[] GerarRelatorioProtocolosEventosOcorrencia(List<RelatorioProtocoloOcorrencia> itens, string type)
        {
            var report = new LocalReport { EnableExternalImages = true };
            try
            {

                var tipoRelatorio = string.Empty;

                if (type == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (type == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                report.DataSources.Add(new ReportDataSource
                {
                    Value = itens,
                    Name = "ProtocoloEventoOcorrencia"
                });
                report.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Protocolo.RelatorioProtocoloEventosOcorrencia.rdlc";
                report.Refresh();
                return report.Render(tipoRelatorio);
            }
            finally
            {
                report.ReleaseSandboxAppDomain();
                report = null;
            }
        }

        public byte[] GerarRelatorioProtocolos(ProtocoloHeaderRelatorioModel headerModel, List<ProtocoloRelatorioModel> itens, int tipoRelatorio)
        {
            var report = new LocalReport { EnableExternalImages = true };
            try
            {
                report.DataSources.Add(new ReportDataSource
                {
                    Value = new List<ProtocoloHeaderRelatorioModel> { headerModel },
                    Name = "ProtocoloModel"
                });

                report.DataSources.Add(new ReportDataSource
                {
                    Value = itens,
                    Name = "ProtocoloListModel"
                });

                report.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Protocolo.RelatorioProtocolo.rdlc";
                report.Refresh();

                if (tipoRelatorio == 2)
                    return report.Render("Excel");
                else
                    return report.Render("PDF");
            }
            finally
            {
                report.ReleaseSandboxAppDomain();
                report = null;
            }
        }

        public byte[] GerarRelatorioEventosVinculados(List<RelatorioEventosVinculados> itens, string type)
        {
            var report = new Microsoft.Reporting.WebForms.LocalReport { EnableExternalImages = true };
            try
            {

                var tipoRelatorio = string.Empty;

                if (type == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (type == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                report.DataSources.Add(new Microsoft.Reporting.WebForms.ReportDataSource
                {
                    Value = itens,
                    Name = "EventosVinculados"
                });
                report.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Protocolo.RelatorioProtocoloEventosVinculado.rdlc";
                report.Refresh();
                return report.Render(tipoRelatorio);
    }
            finally
            {
                report.ReleaseSandboxAppDomain();
                report = null;
}
        }
    }
}

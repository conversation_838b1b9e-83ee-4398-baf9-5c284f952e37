﻿using System;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.CargaAvulsa.ReciboCargaAvulsa
{
    public class RelatorioReciboCargaAvulsaDto
    {
        public RelatorioReciboCargaAvulsaDto(
            string razaoSocialEmpresa,
            string cpnjEmpresa,
            string enderecoEmpresa,
            string numeroEmpresa,
            string bairroEmpresa,
            string cepEmpresa,
            string nomeCidadeEmpresa,
            string siglaEstadoEmpresa,
            string telefoneEmpresa,
            string nomeFavorecido,
            string documentoFavorecido,
            string cartaoFavorecido,
            string contatoFavorecido,
            string placaCavalo,
            string placaCarreta1,
            string placaCarreta2,
            string placaCarreta3,
            int codigoCargaAvulsa,
            string numeroControleCargaAvulsa,
            decimal valorCargaAvulsa,
            DateTime dataCadastroCargaAvulsa,
            string statusCargaAvulsa,
            DateTime? dataCargaCartao,
            string observacaoCargaAvulsa)
        {
            RazaoSocialEmpresa = razaoSocialEmpresa;
            CpnjEmpresa = cpnjEmpresa?.ToDocument();
            EnderecoEmpresa = FormatarEnderecoEmpresa(enderecoEmpresa, numeroEmpresa, bairroEmpresa, cepEmpresa,
                nomeCidadeEmpresa, siglaEstadoEmpresa);
            TelefoneEmpresa = telefoneEmpresa?.FormatAsTelefone();
            NomeFavorecido = nomeFavorecido;
            DocumentoFavorecido = documentoFavorecido?.ToDocument();
            CartaoFavorecido = cartaoFavorecido;
            ContatoFavorecido = contatoFavorecido?.FormatAsTelefone();
            Placas = FormatarPlacas(placaCavalo, placaCarreta1, placaCarreta2, placaCarreta3);
            CodigoCargaAvulsa = codigoCargaAvulsa.ToString();
            NumeroControleCargaAvulsa = numeroControleCargaAvulsa;
            ValorCargaAvulsa = $"R$ {valorCargaAvulsa:N}";
            DataCadastroCargaAvulsa = dataCadastroCargaAvulsa.ToString("dd/MM/yyyy HH:mm");
            StatusCargaAvulsa = statusCargaAvulsa;
            DataCargaCartao = dataCargaCartao.HasValue
                ? dataCargaCartao.Value.ToString("dd/MM/yyyy HH:mm")
                : string.Empty;
            ObservacaoCargaAvulsa = observacaoCargaAvulsa;
        }

        public string RazaoSocialEmpresa { get; }
        public string CpnjEmpresa { get; }
        public string EnderecoEmpresa { get; }
        public string TelefoneEmpresa { get; }
        public string NomeFavorecido { get; }
        public string DocumentoFavorecido { get; }
        public string CartaoFavorecido { get; }
        public string ContatoFavorecido { get; }
        public string Placas { get; }
        public string CodigoCargaAvulsa { get; }
        public string NumeroControleCargaAvulsa { get; }
        public string ValorCargaAvulsa { get; }
        public string DataCadastroCargaAvulsa { get; }
        public string StatusCargaAvulsa { get; }
        public string DataCargaCartao { get; }
        public string ObservacaoCargaAvulsa { get; }

        public string FormatarEnderecoEmpresa(string enderecoEmpresa, string numeroEmpresa, string bairroEmpresa,
            string cepEmpresa, string nomeCidadeEmpresa, string siglaEstadoEmpresa)
        {
            return
                $"Rua {enderecoEmpresa}, Nº {numeroEmpresa}, {bairroEmpresa}, {nomeCidadeEmpresa} - {siglaEstadoEmpresa}, CEP: {cepEmpresa?.ToCepFormato()}";
        }

        public string FormatarPlacas(string placaCavalo, string placaCarreta1, string placaCarreta2,
            string placaCarreta3)
        {
            var placas = string.Empty;

            if (!string.IsNullOrEmpty(placaCavalo))
                placas = $"{placaCavalo.FormatarPlaca()}";

            if (!string.IsNullOrEmpty(placaCarreta1) && !string.IsNullOrEmpty(placas))
                placas += $", {placaCarreta1.FormatarPlaca()}";
            else
                placas += $"{placaCarreta1.FormatarPlaca()}";

            if (!string.IsNullOrEmpty(placaCarreta2) && !string.IsNullOrEmpty(placas))
                placas += $", {placaCarreta2.FormatarPlaca()}";
            else
                placas += $"{placaCarreta2.FormatarPlaca()}";

            if (!string.IsNullOrEmpty(placaCarreta3) && !string.IsNullOrEmpty(placas))
                placas += $", {placaCarreta3.FormatarPlaca()}";
            else
                placas += $"{placaCarreta3.FormatarPlaca()}";

            return placas;
        }
    }

    public class RelatorioReciboCargaAvulsaRodapeDto
    {
        public RelatorioReciboCargaAvulsaRodapeDto(string numeroCartao, string nomeFavorecido, string documentoFavorecido, decimal valor)
        {
            TextoRodape = $"Carga avulsa efetuada no cartão {numeroCartao}, para o portador {nomeFavorecido}, documento {documentoFavorecido?.ToDocument()}, no valor de R$ {valor:N}.";
        }

        public string TextoRodape { get; }
    }
}
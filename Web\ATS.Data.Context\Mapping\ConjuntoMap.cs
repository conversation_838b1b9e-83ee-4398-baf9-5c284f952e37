﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ConjuntoMap : EntityTypeConfiguration<Conjunto>
    {
        public ConjuntoMap()
        {
            ToTable("CONJUNTO");

            HasKey(t => new {t.IdConjunto});

            Property(t => t.IdConjunto)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(t => t.TipoCavalo)
                .WithMany(t => t.Conjuntos)
                .HasForeignKey(t => t.IdTipoCavalo);

            Property(t => t.DataCadastro)
                .HasColumnType("datetime2");

            Property( t => t.DataAtualizacao)
                .HasColumnType("datetime2");

            Property(t => t.DataIntegracaoGR)
                .HasColumnType("datetime2");
        }
    }
}
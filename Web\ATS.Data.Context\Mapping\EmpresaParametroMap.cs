using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EmpresaParametroMap : EntityTypeConfiguration<EmpresaParametro>
    {
        public EmpresaParametroMap()
        {
            ToTable("EMPRESA_PARAMETRO");
            HasKey(t => new {t.IdEmpresa, t.Nome});

            Property(t => t.IdEmpresa).IsRequired();
            Property(t => t.Nome).IsRequired().HasMaxLength(200);
            Property(t => t.ValorText).IsOptional().HasColumnType("varchar(max)");
            Property(t => t.ValorInt).IsOptional();
            Property(t => t.ValorDecimal).IsOptional();
            Property(t => t.ValorDateTime).IsOptional();
            Property(t => t.ValorTime).IsOptional();                       
        }
    }
}
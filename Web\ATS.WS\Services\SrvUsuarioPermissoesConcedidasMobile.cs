using System;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Models;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;

namespace ATS.WS.Services
{
    public class SrvUsuarioPermissoesConcedidasMobile
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly IUsuarioPermissoesConcedidasMobileApp _usuarioPermissoesConcedidasMobileApp;

        public SrvUsuarioPermissoesConcedidasMobile(IUsuarioApp usuarioApp, IUsuarioPermissoesConcedidasMobileApp usuarioPermissoesConcedidasMobileApp)
        {
            _usuarioApp = usuarioApp;
            _usuarioPermissoesConcedidasMobileApp = usuarioPermissoesConcedidasMobileApp;
        }

        public Retorno<object> SalvarOuEditar(UsuarioPermissoesConcedidasMobileRequest request)
        {
            try
            {
                var usuarioCpf = _usuarioApp.GetCPFCNPJ(request.IdUsuario);

                if (string.IsNullOrEmpty(usuarioCpf))
                    return new Retorno<object>(false, $"Usuario de id {request.IdUsuario} não encontrado na base.",
                        null);
                
                var retorno = _usuarioPermissoesConcedidasMobileApp.SalvarOuEditar(
                    Mapper.Map<UsuarioPermissoesConcedidasMobileRequest, UsuarioPermissoesConcedidasMobile>(request));
                
                return retorno.IsValid
                    ? new Retorno<object>(true, "Operação realizada com sucesso.", null)
                    : new Retorno<object>(false, retorno.Errors.FirstOrDefault()?.Message, null);
            }
            catch (Exception e)
            {
                return new Retorno<object>(false, e.Message, null);
            }
        }

        public Retorno<UsuarioPermissoesConcedidasMobileModel> ConsultarPorUsuario(int? usuarioId)
        {
            if (!usuarioId.HasValue)
                throw new Exception("Id do usuário não informado");
            
            var permissoes = _usuarioPermissoesConcedidasMobileApp.ConsultarPorUsuario(usuarioId.Value);

            return permissoes == null
                ? new Retorno<UsuarioPermissoesConcedidasMobileModel>(false, $"Permissões não encontradas para o usuário id: {usuarioId}", null)
                : new Retorno<UsuarioPermissoesConcedidasMobileModel>(true, null, permissoes);
        }
    }
}
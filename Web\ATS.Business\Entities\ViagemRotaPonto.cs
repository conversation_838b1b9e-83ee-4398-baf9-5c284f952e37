using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class ViagemRotaPonto
    {   
        public int IdViagemRotaPonto { get; set; }
        public int IdViagemRota { get; set; }
        public int Sequencia { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public int? IdCidade { get; set; }

        public virtual ViagemRota ViagemRota { get; set; }
        public virtual Cidade Cidade { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IViagemEventoApp
    {
        List<ViagemEvento> GetEventosViagem(int idViagem);
        List<ViagemEvento> GetEventosViagem(List<int> idsViagemEvento);
        List<DeclaracaoCiot> GetCiotViagem(int idViagem);
        ViagemEvento Get(int idViagemEvento);
        IQueryable<ViagemEvento> GetQueryable(int idViagemEvento);
        ValidationResult Update(ViagemEvento viagemEvento);
        object ConsultaGridSemChave(int take, int page, OrderFilters order, List<QueryFilters> filters);
        IQueryable<ViagemEvento> GetEventoPorTokenPagSemChave(string token);
        IEnumerable<ViagemEvento> GetEventosViagemSemChave(int idEmpresa);
        IQueryable<ViagemEvento> Find(Expression<Func<ViagemEvento, bool>> predicate, bool @readonly = false);
    }
}
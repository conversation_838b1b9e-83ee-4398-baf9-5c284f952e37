﻿using System.Collections.Generic;
using ATS.Domain.Entities;

namespace ATS.WS.Models.Webservice.Request.Credenciamento
{
    public class MotivoRequestModel
    {
        public int? IdMotivo { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdFilial { get; set; }
        public string RazaoSocialEmpresa { get; set; }
        public string RazaoSocialFilial { get; set; }
        public string Descricao { get; set; }
        public List<TipoMotivoRequest> TipoMotivo { get; set; }
    }
    
    public class TipoMotivoRequest
    {
        public int? IdTipoMotivo { get; set; }
        public int? IdMotivo { get; set; }
        public ETipoMotivo Tipo { get; set; }
    }
}
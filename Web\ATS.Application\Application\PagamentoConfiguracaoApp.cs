﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Transactions;

namespace ATS.Application.Application
{
    public class PagamentoConfiguracaoApp : AppBase, IPagamentoConfiguracaoApp
    {

        private IPagamentoConfiguracaoService Service { get; }

        public PagamentoConfiguracaoApp(IPagamentoConfiguracaoService service)
        {
            Service = service;
        }

        public ValidationResult Add(PagamentoConfiguracao pagamentoConfiguracao)
        {            
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = Service.Add(pagamentoConfiguracao);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public ValidationResult Inativar(int idPagamentoConfiguracao)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = Service.Inativar(idPagamentoConfiguracao);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public ValidationResult Reativar(int idPagamentoConfiguracao)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = Service.Reativar(idPagamentoConfiguracao);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public ValidationResult Update(PagamentoConfiguracao pagamentoConfiguracao)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = Service.Update(pagamentoConfiguracao);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public IEnumerable<PagamentoConfiguracao> GetPorEmpresa(int idEmpresa, int? idFilial)
        {
            return Service.GetPorEmpresa(idEmpresa, idFilial);
        }

        public object ConsultarGrid(int? idEmpresa, int? idFilial, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return Service.ConsultarGrid(idEmpresa, idFilial, take, page, order, filters);
        }

        public PagamentoConfiguracao ConsultarPorId(int idPagamentoConfiguracao)
        {
            return Service.ConsultarPorId(idPagamentoConfiguracao);
        }

    }
}

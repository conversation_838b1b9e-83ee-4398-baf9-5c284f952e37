﻿using System;
using ATS.Application.Application.Common;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Cartao;
using ATS.Data.Repository.External.SistemaInfo.Cartao.DTO;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic;
using System.Web.Configuration;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.CrossCutting.Reports.Cartoes.SituacaoDosCartoes;
using ATS.CrossCutting.Reports.Cartoes.TransferenciaContasBancarias;
using ATS.Data.Repository.External.SistemaInfo.Pedagio.DTO;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using AutoMapper;
using NLog;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ConsultarExtratoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarExtratoRequest;
using ConsultarSaldoCartaoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoRequest;
using ConsultarSaldoCartaoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoResponse;
using ConsultarSaldoEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoEmpresaResponse;
using CustomFilter = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.CustomFilter;
using Empresa = ATS.Domain.Entities.Empresa;
using IdentificadorCartao = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IdentificadorCartao;
using ProdutoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ProdutoResponse;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.Models;

namespace ATS.Application.Application
{
    public class CartoesAppFactoryDependencies
    {
        public CartoesAppFactoryDependencies(IParametrosApp parametrosApp, IProprietarioApp proprietarioApp,
            IResgateCartaoAtendimentoApp resgatarCartaoApp, ICidadeRepository cidadeRepository, IMotoristaApp motoristaApp,
            IUsuarioApp usuarioApp, IEmpresaService empresaService, CartoesServiceArgs cartoesServiceArgs,
            ICidadeApp cidadeApp, IEmpresaApp empresaApp, IEmpresaRepository empresaRepository,
            IDespesaUsuarioApp despesaUsuarioApp, ISerproApp serproApp, IParametrosEmpresaService parametrosEmpresaService, 
            ICargaAvulsaRepository cargaAvulsaRepository)
        {
            ParametrosApp = parametrosApp;
            ProprietarioApp = proprietarioApp;
            ResgatarCartaoApp = resgatarCartaoApp;
            CidadeRepository = cidadeRepository;
            MotoristaApp = motoristaApp;
            UsuarioApp = usuarioApp;
            EmpresaService = empresaService;
            CartoesServiceArgs = cartoesServiceArgs;
            CidadeApp = cidadeApp;
            EmpresaApp = empresaApp;
            EmpresaRepository = empresaRepository;
            DespesaUsuarioApp = despesaUsuarioApp;
            SerproApp = serproApp;
            ParametrosEmpresaService = parametrosEmpresaService;
            CargaAvulsaRepository = cargaAvulsaRepository;
        }

        public IParametrosApp ParametrosApp { get; }
        public IProprietarioApp ProprietarioApp { get; }
        public IResgateCartaoAtendimentoApp ResgatarCartaoApp { get; }
        public ICidadeRepository CidadeRepository { get; }
        public IMotoristaApp MotoristaApp { get; }
        public IUsuarioApp UsuarioApp { get; }
        public IEmpresaService EmpresaService { get; }
        public CartoesServiceArgs CartoesServiceArgs { get; }
        public ICidadeApp CidadeApp { get; }
        public IEmpresaApp EmpresaApp { get; }
        public IEmpresaRepository EmpresaRepository { get; }
        public IDespesaUsuarioApp DespesaUsuarioApp { get; }
        public ISerproApp SerproApp { get; }
        public IParametrosEmpresaService ParametrosEmpresaService { get; }
        public ICargaAvulsaRepository CargaAvulsaRepository { get; }
    }

    public class CartoesApp : AppBase, ICartoesApp
    {
        private readonly CartoesService _cartoesService;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IResgateCartaoAtendimentoApp _resgatarCartaoApp;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEmpresaService _empresaService;
        private readonly ICidadeApp _cidadeApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IDespesaUsuarioApp _despesaUsuarioApp;
        private readonly ISerproApp _serproApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;


        public CartoesApp(int? idEmpresa, string token, string documentoUsuarioAudit, string nomeUsuarioAudit, CartoesAppFactoryDependencies dependencies)
        {
            _parametrosApp = dependencies.ParametrosApp;
            _proprietarioApp = dependencies.ProprietarioApp;
            _resgatarCartaoApp = dependencies.ResgatarCartaoApp;
            _cidadeRepository = dependencies.CidadeRepository;
            _motoristaApp = dependencies.MotoristaApp;
            _usuarioApp = dependencies.UsuarioApp;
            _cidadeApp = dependencies.CidadeApp;
            _empresaApp = dependencies.EmpresaApp;
            _despesaUsuarioApp = dependencies.DespesaUsuarioApp;
            _parametrosEmpresa = dependencies.ParametrosEmpresaService;
            _empresaService = dependencies.EmpresaService;
            _empresaRepository = dependencies.EmpresaRepository;
            _cartoesService = new CartoesService(dependencies.CartoesServiceArgs, idEmpresa, token, documentoUsuarioAudit, nomeUsuarioAudit);
            _serproApp = dependencies.SerproApp;
        }

        #region Factory

        public static CartoesApp CreateByEmpresa(CartoesAppFactoryDependencies args, string cnpj, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var serv = args.EmpresaRepository;
            var token = string.IsNullOrEmpty(cnpj) ? SistemaInfoConsts.TokenAdministradora : serv.GetTokenMicroServices(cnpj);
            var idEmp = serv.GetIdPorCnpj(cnpj);
            return new CartoesApp(idEmp, token, documentoUsuarioAudit, nomeUsuarioAudit, args);
        }

        public static CartoesApp CreateByEmpresa(CartoesAppFactoryDependencies args, int id, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var serv = args.EmpresaRepository;
            var token = serv.GetTokenMicroServices(id);
            return new CartoesApp(id, token, documentoUsuarioAudit, nomeUsuarioAudit, args);
        }

        public static CartoesApp CreateByEmpresa(CartoesAppFactoryDependencies args, int? idEmpresa, int idUsuario, bool enableAdmLogin)
        {
            Usuario usuario = null;
            if (idUsuario > 0)
                usuario = args.UsuarioApp.Get(idUsuario);

            if (idEmpresa.HasValue)
                return CreateByEmpresa(args, idEmpresa.Value, usuario?.CPFCNPJ, usuario?.Nome);
            if (usuario?.IdEmpresa != null)
                return CreateByEmpresa(args, usuario.IdEmpresa.Value, usuario.CPFCNPJ, usuario.Nome);
            if (enableAdmLogin)
                return new CartoesApp(null, SistemaInfoConsts.TokenAdministradora, usuario?.CPFCNPJ, usuario?.Nome, args);

            throw new CartaoAtsException("Não foi possívei instância CartoesApp (Id.Emp: {0} - Id.Usuario: {1})"
                .FormatEx((int?) null, idUsuario));
        }

        public static CartoesApp CreateByAdministradora(CartoesAppFactoryDependencies args, int administradoraId, int? idEmpresa, string cpfCnpj, string nome)
        {
            if (idEmpresa != null)
                return new CartoesApp(idEmpresa, args.ParametrosApp.GetTokenAdministradora(administradoraId), cpfCnpj, nome, args);

            throw new CartaoAtsException("Não foi possívei instância CartoesApp (Usuario: {0})"
                .FormatEx(nome));
        }

        public static CartoesApp CreateByUsuario(CartoesAppFactoryDependencies args, Usuario usuarioLogado)
        {
            if (usuarioLogado.Empresa != null)
                return new CartoesApp(usuarioLogado.IdEmpresa, usuarioLogado.Empresa.TokenMicroServices, usuarioLogado.CPFCNPJ, usuarioLogado.Nome, args);

            if (usuarioLogado.IdEmpresa.HasValue)
                return CreateByEmpresa(args, usuarioLogado.IdEmpresa.Value, usuarioLogado.CPFCNPJ, usuarioLogado.Nome);

            if (usuarioLogado.Perfil == EPerfil.Administrador)
                return new CartoesApp(usuarioLogado.IdEmpresa, SistemaInfoConsts.TokenAdministradora, usuarioLogado.CPFCNPJ, usuarioLogado.Nome, args);

            throw new CartaoAtsException("Não foi possívei instância CartoesApp (Usuario: {0})"
                .FormatEx(usuarioLogado.IdUsuario));
        }

        public static CartoesApp CreateByUsuarioAtendimento(CartoesAppFactoryDependencies args, UsuarioMicroServicoInstanciaAppDto usuarioLogado)
        {
            if (usuarioLogado.Perfil != EPerfil.Empresa || !usuarioLogado.IdEmpresa.HasValue)
                throw new CartaoAtsException("Funcionalidade disponível apenas para usuários de perfil empresa.");

            var token = args.ParametrosApp.GetTokenMicroServicoCentralAtendimento(usuarioLogado.IdEmpresa.Value);

            if (string.IsNullOrWhiteSpace(token))
            {
                var serv = args.EmpresaRepository;
                token = serv.GetTokenMicroServices(usuarioLogado.IdEmpresa.Value);
            }

            return new CartoesApp(usuarioLogado.IdEmpresa.Value, token, usuarioLogado.CPFCNPJ, usuarioLogado.Nome, args);
        }

        public static CartoesApp CreateByUsuario(CartoesAppFactoryDependencies args, int idUsuario, bool enableAdmLogin, int? idEmpresa)
        {
            var usuario = args.UsuarioApp.Get(idUsuario);
            if (usuario == null)
                throw new CartaoAtsException("Usuário não localizado. Id: " + idUsuario);

            if (enableAdmLogin && usuario.Perfil == EPerfil.Administrador)
                return new CartoesApp(idEmpresa, SistemaInfoConsts.TokenAdministradora, usuario.CPFCNPJ, usuario.Nome, args);

            if (idEmpresa.HasValue)
                return CreateByEmpresa(args, idEmpresa.Value, usuario.CPFCNPJ, usuario.Nome);

            if (usuario.IdEmpresa.HasValue)
                return CreateByEmpresa(args, usuario.IdEmpresa.Value, usuario.CPFCNPJ, usuario.Nome);

            throw new CartaoAtsException("Não foi possívei instância CartoesApp (Id.Usuario: {0} - Id.Emp: {1})"
                .FormatEx(idUsuario, (int?) null));
        }

        #endregion

        public IntegrarPessoaResponse IntegrarPessoaMicroServico(Estabelecimento estabelecimento)
        {
            return _cartoesService.IntegrarPessoaMicroServico(estabelecimento);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="documento"></param>
        /// <param name="idProdutos"></param>
        /// <param name="ativarCartaoVirtualCasoNaoPossuir">Caso o portador não possuir nenhum cartão, ativida automaticamente um virtual e retorna. Utilizado apenas na integração de viagem, caso o proprietário for diferente do motorista.</param>
        /// <param name="documentoUsuarioAudit"></param>
        /// <param name="nomeUsuarioAudit"></param>
        /// <param name="buscarCartoesBloqueados">Para integração de viagem, caso os cartões vinculados venham vazios, serão buscados também os cartões bloqueados para impedir que cartões virtuais sejam gerados para depósito</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public CartaoVinculadoPessoaListResponse GetCartoesVinculados(string documento, List<int> idProdutos,
            bool ativarCartaoVirtualCasoNaoPossuir = false, string documentoUsuarioAudit = null,
            string nomeUsuarioAudit = null, bool buscarCartoesBloqueados = false)
        {
            return _cartoesService.GetCartoesVinculados(documento, idProdutos, ativarCartaoVirtualCasoNaoPossuir,
                documentoUsuarioAudit, nomeUsuarioAudit, buscarCartoesBloqueados);
        }

        public List<ProdutoResponse> GetCartaoProdutos()
        {
            return _cartoesService.GetCartaoProdutos();
        }

        public List<ProdutoResponse> FiltrarProdutosMestre()
        {
            return _cartoesService.FiltrarProdutosMestre();
        }

        public ProdutoResponse GetCartaoProduto(int id)
        {
            return _cartoesService.GetCartaoProduto(id);
        }

        public ConsultaCartaoResponse GetCartaoProcessadora(int identificador, int produto)
        {
            return _cartoesService.GetCartaoProcessadora(identificador, produto);
        }

        public ConsultaCartaoAdministradoraResponse AdministradoraCartao(int identificador, int idproduto)
        {
            return _cartoesService.AdministradoraCartao(identificador, idproduto);
        }

        public ConsultaDetalhesEmpresaMeioHomologadoReponseDTO ConsultarEmpresa(string cnpj)
        {
            return _cartoesService.ConsultarEmpresa(cnpj);
        }

        public ResgatarValorResponseDTO ResgatarValor(ResgatarValorDTO request)
        {
            return _cartoesService.ResgatarValor(request);
        }

        public EstornarResgateValorResponseDTO EstornarResgateValor(EstornarResgateValorDTO request, int usuario)
        {
            return _cartoesService.EstornarResgateValor(request, usuario);
        }

        public HistoricoCartaoPessoaListResponse GetCartaoHistorico(string documento, List<int> idProdutos)
        {
            return _cartoesService.GetHistoricoCartoes(documento, idProdutos);
        }

        public HistoricoCartaoPessoaListResponseDto GetCartaoHistoricoGrid(string documento, List<int> idProdutos)
        {
            return _cartoesService.GetHistoricoCartoesGrid(documento, idProdutos);
        }

        public CartaoVinculadoPessoaListResponseDto GetCartoesVinculadosGrid(string documento, List<int> idProdutos)
        {
            return _cartoesService.GetCartoesVinculadosGrid(documento, idProdutos);
        }

        public CartaoBloqueadoPessoaListResponseDto GetCartoesBloqueadoGrid(string documento, List<int> idProdutos)
        {
            return _cartoesService.GetCartoesBloqueadoGrid(documento, idProdutos);
        }

        public FilteredResultOfMotivoBloqueioModel BuscarMotivosBloquearCartao(int take, int page, OrderFilters order, List<SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilter> filters)
        {
            return _cartoesService.BuscarMotivosBloquearCartao(take,page,order, filters);
        }

        public BloquearCartaoResponse BloquearCartao(BloquearCartaoRequest request)
        {
            return _cartoesService.BloquearCartao(request);
        }

        public BloquearCartaoResponse BloquearCartaoParametrizacaoEmpresa(BloquearCartaoRequest request)
        {
            return _cartoesService.BloquearCartaoParametrizacaoEmpresa(request);
        }

        public DesbloquearCartaoResponse DesbloquearCartao(DesbloquearCartaoRequest request)
        {
            return _cartoesService.DesbloquearCartao(request);
        }

        public AtsPortadorRequest CarregarInformacoesPortador(string documento)
        {
            var proprietario = _proprietarioApp.GetPorCpfCnpj(documento);
            if (proprietario != null)
            {
                if (!proprietario.Enderecos.Any())
                    throw new Exception("Portador não possui endereço cadastrado!");

                var endereco = proprietario.Enderecos.First();
                var cidade = _cidadeRepository.Include(c => c.Estado).First(c => c.IdCidade == endereco.IdCidade);
                var contato = proprietario.Contatos.First();
                var tipoPessoa = proprietario.CNPJCPF.Length.Equals(11)
                    ? SistemaInfoConsts.TipoPessoaFisica
                    : SistemaInfoConsts.TipoPessoaJuridica;
                return new AtsPortadorRequest()
                {
                    Nome = proprietario.RazaoSocial,
                    Documento = proprietario.CNPJCPF,
                    NomeFantasia = proprietario.NomeFantasia,
                    TipoPessoa = tipoPessoa,
                    TipoPessoaFormatado = tipoPessoa == SistemaInfoConsts.TipoPessoaFisica ? "Física" : "Jurídica",
                    Contato = contato.Celular,
                    Email = contato.Email,
                    Telefone = contato.Celular,

                    Endereco = endereco.Endereco,
                    CodigoIbge = cidade.IBGE,
                    Cidade = cidade.Nome,
                    Estado = cidade.Estado.Nome,
                    Bairro = endereco.Bairro,
                    Cep = endereco.CEP.ToCEPFormato(),
                    Complemento = endereco.Complemento,
                    Logradouro = endereco.Endereco,
                    Numero = endereco.Numero,

                    RG = proprietario.RG,
                    NomeMae = proprietario.NomeMae,
                    NomePai = proprietario.NomePai,
                    DataNascimento = proprietario.DataNascimento,
                    DataNascimentoFormatado = proprietario.DataNascimento.FormatDateBr(),

                    //TODO Validar posteriormente uma solução
                    Sexo = SistemaInfoConsts.Masculino,
                    SexoFormatado = "Masculino",
                    //Quando é CPF motorista vai ser EquiparadoTac false, e proprietário true
                    EquiparadoTac = tipoPessoa == SistemaInfoConsts.TipoPessoaFisica ? true : proprietario.EquiparadoTac
                };
            }

            var motorista = _motoristaApp.GetWithChilds(documento, null);
            if (motorista != null)
            {
                var cidade = _cidadeRepository.Include(c => c.Estado).First(c => c.IdCidade == motorista.IdCidade);
                return new AtsPortadorRequest()
                {
                    Nome = motorista.Nome,
                    Documento = motorista.CPF,
                    TipoPessoa = SistemaInfoConsts.TipoPessoaFisica,
                    TipoPessoaFormatado = "Física",
                    Sexo = motorista.Sexo,
                    SexoFormatado = SistemaInfoConsts.Masculino.Equals(motorista.Sexo) ? "Masculino" : "Feminino",
                    Contato = motorista.Celular,
                    Email = motorista.Email,
                    Telefone = motorista.Celular,
                    NomeFantasia = motorista.Nome,

                    Endereco = motorista.Endereco,
                    CodigoIbge = cidade.IBGE,
                    Cidade = cidade.Nome,
                    Estado = cidade.Estado.Nome,
                    Bairro = motorista.Bairro,
                    Cep = motorista.CEP.ToCEPFormato(),
                    Complemento = motorista.Complemento,
                    Logradouro = motorista.Endereco,
                    Numero = motorista.Numero.ToIntSafe(),

                    RG = motorista.RG,
                    NomeMae = motorista.NomeMae,
                    NomePai = motorista.NomePai,
                    DataNascimento = motorista.DataNascimento,
                    DataNascimentoFormatado = motorista.DataNascimento.FormatDateBr(),
                    //Quando é CPF motorista vai ser EquiparadoTac false, e proprietário true
                    EquiparadoTac = false
                };
            }
            else
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(documento);
                if (usuario == null) return null;

                var usuarioEndereco = usuario.Enderecos.First();
                var cidade = _cidadeRepository.Include(c => c.Estado).First(c => c.IdCidade == usuarioEndereco.IdCidade);
                var usuarioContato = usuario.Contatos.First();
                var tipoPessoa = usuario.CPFCNPJ.Length.Equals(11)
                    ? SistemaInfoConsts.TipoPessoaFisica
                    : SistemaInfoConsts.TipoPessoaJuridica;

                return new AtsPortadorRequest()
                {
                    Nome = usuario.Nome,
                    NomeFantasia = usuario.Nome,
                    Documento = usuario.CPFCNPJ,
                    TipoPessoa = tipoPessoa,
                    TipoPessoaFormatado = tipoPessoa == SistemaInfoConsts.TipoPessoaFisica ? "Física" : "Jurídica",
                    Contato = usuarioContato.Celular,
                    Email = usuarioContato.Email,
                    Telefone = usuarioContato.Celular,

                    Endereco = usuarioEndereco.Endereco,
                    CodigoIbge = cidade.IBGE,
                    Bairro = usuarioEndereco.Bairro,
                    Cidade = cidade.Nome,
                    Estado = cidade.Estado.Nome,
                    Cep = usuarioEndereco.CEP.ToCEPFormato(),
                    Complemento = usuarioEndereco.Complemento,
                    Logradouro = usuarioEndereco.Endereco,
                    Numero = usuarioEndereco.Numero,

                    RG = usuario.RG,
                    NomeMae = usuario.NomeMae,
                    NomePai = usuario.NomePai,
                    DataNascimento = usuario.DataNascimento,
                    DataNascimentoFormatado = usuario.DataNascimento.FormatDateBr(),

                    //TODO Validar posteriormente uma solução
                    Sexo = SistemaInfoConsts.Masculino,
                    SexoFormatado = "Masculino",
                    //Quando é CPF motorista vai ser EquiparadoTac false, e proprietário true
                    EquiparadoTac = false
                };
            }
        }

        public VincularResponse VincularCartaoPortador(int identificador, int idProdutos, AtsPortadorRequest atsPortadorRequest, int idEmpresa)
        {
            if (idEmpresa != 0 && atsPortadorRequest.Documento.OnlyNumbers().Length == 11 &&
                _parametrosEmpresa.GetPermiteVincularCartaoComCpfFicticio(idEmpresa) == false)
            {
                var validacaoSerpro = _serproApp.ValidarPortador(atsPortadorRequest.Documento);
                if (!validacaoSerpro.IsValid)
                    return new VincularResponse()
                    {
                        Status = VincularResponseStatus.Falha,
                        Mensagem = validacaoSerpro.Errors.Select(e => e.Message).FirstOrDefault()
                    };
            }

            return _cartoesService.VincularCartaoPortador(identificador, idProdutos, atsPortadorRequest);
        }

        public DesvincularResponse DesvincularCartaoPortador(int identificador, int idProdutos, string motivoDesvinculo, AtsPortadorRequest atsPortadorRequest, int? cartaoMestreId)
        {
            return _cartoesService.DesvincularCartaoPortador(identificador, idProdutos, motivoDesvinculo,
                atsPortadorRequest, cartaoMestreId);
        }

        public ETipoProcessamentoCartao GetTipoProcessamentoCartaoCarga(ETipoEventoViagem tipoEventoViagem, ETipoOperacaoCartao tipoOperacao)
        {
            return _cartoesService.GetTipoProcessamentoCartaoCarga(tipoEventoViagem, tipoOperacao);
        }

        public OperacaoCartaoResponseDTO RealizarCargaFrete(Viagem viagem, ViagemEvento viagemEvento)
        {
            var produtos = new List<int>();
            produtos.Add(GetIdProdutoCartaoFretePadrao());

            var cartoesProprietario = GetCartoesVinculados(viagem.CPFCNPJProprietario, produtos, buscarCartoesBloqueados:true);
            var cartoesMotorista = GetCartoesVinculados(viagem.CPFMotorista, produtos, buscarCartoesBloqueados:true);

            return RealizarCargaFrete(viagem, viagemEvento, cartoesMotorista, cartoesProprietario);
        }

        public OperacaoCartaoResponseDTO RealizarCargaFrete(Viagem viagem, ViagemEvento viagemEvento, CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario)
        {
            return _cartoesService.RealizarCargaFrete(viagem, viagemEvento, cartaoVinculadoMotorista, cartaoVinculadoProprietario);
        }

        public OperacaoCartaoResponseDTO RealizarEstornoCargaFrete(ViagemEvento viagemEvento, int idEmpresa, string cpfMotorista, string cpfCnpjProprietario, EOrigemTransacaoCartao origem)
        {
            return _cartoesService.RealizarEstornoCargaFrete(viagemEvento, idEmpresa, cpfMotorista, cpfCnpjProprietario, origem);
        }

        public OperacaoCartaoResponseDTO TransferirValorCartao(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem, string senha, int? administradoraId = null)
        {
            return _cartoesService.TransferirValorCartao(documentoOrigem, documentoDestino, valor, cnpjEmpresa, origem, senha, administradoraId);
        }

        public OperacaoCartaoResponseDTO TransferirValorCartao(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem)
        {
            return _cartoesService.TransferirValorCartao(documentoOrigem, documentoDestino, valor, cnpjEmpresa, origem);
        }

        public ConsultarSaldoCartaoResponse ConsultarSaldoCartao(string documento)
        {
            return _cartoesService.ConsultarSaldoCartao(documento);
        }

        public ConsultarSaldoCartaoResponseDTO ConsultarSaldoCartao(ConsultarSaldoCartaoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            return _cartoesService.ConsultarSaldoCartao(request, documentoUsuarioAudit, nomeUsuarioAudit);
        }

        public AlterarSenhaCartaoResponse AlterarSenhaCartao(AlterarSenhaCartaoRequestDTO requestDto)
        {
            return _cartoesService.AlterarSenhaCartao(requestDto);
        }

        public OperacaoCartaoResponseDTO TransferirValorContaBancaria(TransferirContaBancariaRequestDTO request, EOrigemTransacaoCartao origem)
        {
            return _cartoesService.TransferirValorContaBancaria(request, origem);
        }

        public List<ConsultarPontoDistribuicaoResponse> GetPontosDistribuicao(List<string> cnpjList)
        {
            return _cartoesService.GetPontoDistribuicao(cnpjList);
        }
        public List<PessoasCartoesVinculadosItem> ConsultarListaPortadorCartaoComSaldo(CartoesVinculadosListaSaldoRequest request)
        {
            return _cartoesService.ConsultarListaPortadorCartaoComSaldo(request)?.Pessoas;
        }

        public RelatorioPortadorCartaoResponse RelatorioPortadorCartaoPorListaDocumento(RelatorioPortadorCartaoRequest request)
        {
            return _cartoesService.RelatorioPortadorCartaoPorListaDocumento(request);
        }

        public EnvioRemessaResponse EnviarRemessaCartoes(EnvioRemessaRequest request)
        {
            return _cartoesService.EnviarRemessaCartoes(request);
        }

        public EnvioRemessaResponse ValidarCartaoRemessa(ValidarCartaoRequest request)
        {
            return _cartoesService.ValidarCartaoRemessa(request);
        }

        public EnvioRemessaResponse ValidarLoteCartaoRemessa(ValidarCartoesLoteRequest request)
        {
            return _cartoesService.ValidarLoteCartaoRemessa(request);
        }

        public BaixaRemessaResponse ReceberRemessaCartoes(BaixaRemessaRequest request)
        {
            return _cartoesService.ReceberRemessaCartoes(request);
        }

        public ConsultarRemessaResponse ConsultarCartoesLote(int loteId)
        {
            return _cartoesService.ConsultarCartoesLote(loteId);
        }

        public ConsultarRemessaResponseDTO CarregarRemessaEmpresa(List<string> cnpjList, bool filtrarEmpresaOrigem, int take, int page, OrderFilters order, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim)
        {
            return _cartoesService.CarregarRemessaEmpresa(cnpjList, filtrarEmpresaOrigem, take, page, order, filters, dataInicio, dataFim);
        }

        public int GetIdProdutoCartaoFretePadrao()
        {
            return _cartoesService.GetIdProdutoCartaoFretePadrao();
        }

        #region Pedágio

        public ConsultaCompraPedagioResponse ConsultarCompraPedagio(int pageSize, int pageIndex, IEnumerable<object> orderBy, List<CustomFilter> customFilter, ConsultaCompraPedagioRequest request)
        {
            var resultado = _cartoesService.ConsultarCompraPedagio(pageSize, pageIndex, orderBy, null, request);

            if (customFilter != null && customFilter.Any())
                AplicarCustomFilters(customFilter, resultado);

            return resultado;
        }

        public CancelarCompraPedagioResponse CancelarCompraPedagio(CancelarCompraPedagioRequest request)
        {
            return _cartoesService.CancelarCompraPedagio(request);
        }

        public GetStatusPedagioResponse GetStatusCompraPedagio()
        {
            return _cartoesService.ValoresCompraStatusTipoDictionary();
        }

        public ConsultaRotaResponseDto ConsultarCustoRota(ConsultaRotaRequest request)
        {
            return _cartoesService.ConsultarCustoRota(request);
        }

        public SolicitarCompraPedagioResponseDTO SolicitarCompraPedagio(int empresaId, SolicitarCompraPedagioRequest request, IList<LocalizacaoDTO> localizacoes)
        {
            if (localizacoes != null)
            {
                var locationList = new List<LocationDTO>();
                foreach (var localizacao in localizacoes)
                {
                    var cidade = _cidadeApp.GetCidadeByIBGE(localizacao.IbgeCidade);
                    if (cidade == null && (!localizacao.Latitude.HasValue || !localizacao.Longitude.HasValue))
                        return new SolicitarCompraPedagioResponseDTO
                        {
                            Status = EResultadoCompraPedagio.Erro,
                            Mensagem = $"Cidade com IBGE {localizacao.IbgeCidade} não localizada. Informe uma cidade com IBGE válido ou a latitide / longitude."
                        };

                    var locationDto = new LocationDTO
                    {
                        Latitude = cidade != null ? cidade.Latitude : localizacao.Latitude,
                        Longitude = cidade != null ? cidade.Longitude : localizacao.Longitude,
                        Ibge = localizacao.IbgeCidade
                    };
                    locationList.Add(locationDto);
                    request.Localizacoes = locationList;
                }
            }

            SolicitarCompraPedagioResponseDTO solicitarCompraPedagio;
            if (ValidarSolicitarCompraRequest(request, out solicitarCompraPedagio)) return solicitarCompraPedagio;

            var response = _cartoesService.SolicitarCompraPedagio(request);

            if (response.Status == SolicitarCompraPedagioResponseStatus.Falha || response.CompraPedagio == null)
                return new SolicitarCompraPedagioResponseDTO
                {
                    Status = EResultadoCompraPedagio.Erro,
                    Mensagem = response.Mensagem
                };

            var solicitarCompraPedagioResponseModel = new SolicitarCompraPedagioResponseDTO
            {
                Status = request.Fornecedor == SolicitarCompraPedagioRequestFornecedor.Moedeiro
                    ? EResultadoCompraPedagio.CompraSolicitada
                    : EResultadoCompraPedagio.CompraConfirmada,
                ProtocoloProcessamento = response.CompraPedagio.Id,
                ProtocoloRequisicao = response.CompraPedagio.ProtocoloRequisicao,
                Valor = response.CompraPedagio.Valor,
                Mensagem = response.Mensagem,
                ProtocoloValePedagio = response.CompraPedagio.ProtocoloValePedagio,
                ProtocoloEnvioValePedagio = response.CompraPedagio.ProtocoloEnvioValePedagio,
                AvisoTransportador = response.CompraPedagio.AvisoTransportador,
                Fornecedor = (FornecedorEnum?)request.Fornecedor,
                CnpjFornecedor = response.CompraPedagio.CnpjFornecedor,
                CompraCredenciaisExtratta = response.CompraPedagio.CompraCredenciaisExtratta ?? false,
            };

            // Enviar push
            if (response.CompraPedagio.Valor.GetValueOrDefault(0) > 0)
                EnviarPushCompraPedagioMoedeiro(_empresaService, empresaId, request.DocumentoFavorecido, response.CompraPedagio.Valor.Value, request.Fornecedor, false);

            return solicitarCompraPedagioResponseModel;
        }

        public static void EnviarPushCompraPedagioMoedeiro(IEmpresaService empresaServiceArg, int empresaId, string documentoMotorista, decimal valor,
            SolicitarCompraPedagioRequestFornecedor? fornecedor, bool confirmado, bool isCancelamentoViagem = false)
        {
            const string titulo = "Crédito de pedágio {0}";
            const string tituloEstorno = "Estorno de pedágio {0}";

            const string mensagemMoedeiro = "Crédito de pedágio pendente de carga: {0}\n" +
                                            "Favor dirigir-se ao embarcador ou filial para efetivar a carga em seu cartão.\n" +
                                            "Transportadora: {1}";

            const string mensagemMoedeiroConfirmado = "Crédito de pedágio carregado: {0}\n" +
                                            "Transportadora: {1}";

            const string mensagemOnline = "Crédito de pedágio realizado: {0}\n" +
                                          "Transportadora: {1}";

            const string mensagemCancelamentoMoedeiro = "Contrato de frete cancelado, você possui crédito de pedágio em seu cartão moedeiro: {0}\n" +
                                                        "Favor dirigir-se ao embarcador ou filial para efetivar o estorno em seu cartão.\n" +
                                                        "Transportadora: {1}";

            const string mensagemCancelamentoMoedeiroConfirmado = "Crédito de pedágio estornado: {0}\n" +
                                                      "Transportadora: {1}";

            const string mensagemCancelamentoOnline = "Estorno de pedágio realizado: {0}\n" +
                                                      "Transportadora: {1}";

            try
            {
                var empresaService = empresaServiceArg;

                if (!string.IsNullOrWhiteSpace(documentoMotorista))
                {
                    var nomeEmpresa = empresaService.All()
                        .Where(e => e.IdEmpresa == empresaId)
                        .Select(e => e.NomeFantasia ?? e.RazaoSocial)
                        .FirstOrDefault();

                    string tituloPush;
                    string mensagem;
                    if (isCancelamentoViagem)
                    {
                        tituloPush = tituloEstorno.FormatEx(valor.FormatMoney());
                        mensagem = fornecedor == SolicitarCompraPedagioRequestFornecedor.Moedeiro
                            ? (confirmado
                                ? mensagemCancelamentoMoedeiroConfirmado.FormatEx(valor.FormatMoney(), nomeEmpresa)
                                : mensagemCancelamentoMoedeiro.FormatEx(valor.FormatMoney(), nomeEmpresa))
                            : mensagemCancelamentoOnline.FormatEx(valor.FormatMoney(), nomeEmpresa);
                    }
                    else
                    {
                        tituloPush = titulo.FormatEx(valor.FormatMoney());
                        mensagem = fornecedor == SolicitarCompraPedagioRequestFornecedor.Moedeiro
                            ? (confirmado
                                ? mensagemMoedeiroConfirmado.FormatEx(valor.FormatMoney(), nomeEmpresa)
                                : mensagemMoedeiro.FormatEx(valor.FormatMoney(), nomeEmpresa))
                            : mensagemOnline.FormatEx(valor.FormatMoney(), nomeEmpresa);
                    }

                    /*pushService.Send(
                        new List<string> {idPush},
                        tituloPush,
                        ETipoMensagemPush.Warning,
                        mensagem,
                        null);*/
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e,
                        $"Erro ao enviar push de pedágio. Motorista: {documentoMotorista} - Valor: {valor.FormatMoney()}");
            }
        }

        public ConsultaHistoricoRotaResponse ConsultaHistoricoRota(ConsultaHistoricoRotaRequest request)
        {
            return _cartoesService.ConsultaHistoricoRota(request);
        }

        private static bool ValidarSolicitarCompraRequest(SolicitarCompraPedagioRequest request, out SolicitarCompraPedagioResponseDTO solicitarCompraPedagio)
        {
            if (request.DocumentoFavorecido == null)
            {
                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
                {
                    Fornecedor = (FornecedorEnum?)request.Fornecedor,
                    Status = EResultadoCompraPedagio.Erro,
                    Mensagem = "Por favor, informe o documento do favorecido"
                };
                return true;
            }

            if (request.NomeFavorecido == null)
            {
                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
                {
                    Fornecedor = (FornecedorEnum?)request.Fornecedor,
                    Status = EResultadoCompraPedagio.Erro,
                    Mensagem = "Por favor, informe o nome do favorecido"
                };
                return true;
            }

            /*if (request.NumeroCIOT == null)
            {
                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
                {
                    Status = SolicitarCompraPedagioResponseStatus.Falha,
                    Mensagem = "Por favor, informe o número do CIOT"
                };
                return true;
            }*/

            if (request.Placa == null)
            {
                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
                {
                    Fornecedor = (FornecedorEnum?)request.Fornecedor,
                    Status = EResultadoCompraPedagio.Erro,
                    Mensagem = "Por favor, informe a placa do veículo"
                };
                return true;
            }

            //            if (request.TipoVeiculo == null)
            //            {
            //                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
            //                {
            //                    Status = SolicitarCompraPedagioResponseStatus.Falha,
            //                    Mensagem = "Por favor, informe o tipo de veículo"
            //                };
            //                return true;
            //            }
            //
            //            if (request.QtdEixos.Value <= 0)
            //            {
            //                var veiculo = _veiculoApp.GetVeiculoPorPlaca(request.Placa, request.EmpresaId);
            //                if (veiculo.QuantidadeEixos > 0)
            //                {
            //                    request.QtdEixos = veiculo.QuantidadeEixos;
            //                }
            //                else
            //                {
            //                    solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
            //                    {
            //                        Status = SolicitarCompraPedagioResponseStatus.Falha,
            //                        Mensagem = "Por favor, informe a quantidade de eixos do veículo"
            //                    };
            //                    return true;
            //                }
            //            }

            if (request.Fornecedor == null)
            {
                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
                {
                    Status = EResultadoCompraPedagio.Erro,
                    Mensagem = "Por favor, informe o fornecedor de pedágio"
                };
                return true;
            }

            //            if (request.Localizacoes == null)
            //            {
            //                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
            //                {
            //                    Status = SolicitarCompraPedagioResponseStatus.Falha,
            //                    Mensagem = "Por favor, informe as localizações"
            //                };
            //                return true;
            //            }
            //
            //            if (request.Localizacoes.Count < 2 || request.Localizacoes.Count > 20)
            //            {
            //                solicitarCompraPedagio = new SolicitarCompraPedagioResponseDTO
            //                {
            //                    Status = SolicitarCompraPedagioResponseStatus.Falha,
            //                    Mensagem = "deve possuir no mínimo dois e no máximo vinte IBGEs informados"
            //                };
            //                return true;
            //            }


            solicitarCompraPedagio = null;
            return false;
        }

        #endregion

        #region Relatórios

        public object RelatorioSituacaoCartao(RelatorioCartaoApiRequest request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var cartoes = _cartoesService.RelatorioSituacaoCartao(request, take, page, order, filters);

            return cartoes;
        }

        public RelatorioConciliacaoAnaliticoDataType RelatorioConciliacaoAnalitico(RelatorioConciliacaoAnaliticoTransacoesDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var requestRelatorio = new RelatorioConciliacaoAnaliticoRequest();

            var dateHelper = new DateTimeHelper();
            requestRelatorio.DataInicio = dateHelper.StartOfDay(request.DataInicial);
            requestRelatorio.DataFim = dateHelper.EndOfDay(request.DataFinal);
            requestRelatorio.ExibirMetadados = true;
            //var retorno = cartoesService.RelatorioConciliacaoAnalitico(requestRelatorio, request.SomenteDivergencia, take, page, order, filters);
            var retorno = _cartoesService.GetRelatorioConciliacaoAnaliticoDataType(requestRelatorio, request.SomenteDivergencia, request.Pesquisa, take, page, order, filters);

            return retorno;
        }

        public object RelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaDTO request, int take,
            int page, OrderFilters order, List<QueryFilters> filters)
        {
            var requestRelatorio = new RelatorioTransferenciasContaBancariaRequest();

            requestRelatorio.Produto = request.Produto;
            requestRelatorio.DataInicio = request.DataInicio.Date;
            requestRelatorio.DataFim = request.DataFim.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
            switch (request.Status)
            {
                case StatusExportacao.Todos:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Todos;
                    break;
                case StatusExportacao.NaoExportados:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.NaoExportados;
                    break;
                case StatusExportacao.Exportado:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Exportados;
                    break;
                case StatusExportacao.Sucesso:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Sucesso;
                    break;
                case StatusExportacao.Erro:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Erro;
                    break;
            }

            switch (request.TipoTransacao)
            {
                case TipoTransacao.Todos:
                    requestRelatorio.TipoTransacao = RelatorioTransferenciasContaBancariaRequestTipoTransacao.Todos;
                    break;
                case TipoTransacao.Transferencias:
                    requestRelatorio.TipoTransacao =
                        RelatorioTransferenciasContaBancariaRequestTipoTransacao.Transferencias;
                    break;
                case TipoTransacao.Estornos:
                    requestRelatorio.TipoTransacao = RelatorioTransferenciasContaBancariaRequestTipoTransacao.Estornos;
                    break;
            }


            var retorno = _cartoesService.RelatorioTransferenciasContasBancarias(requestRelatorio, take, page, order, filters);

            return retorno;
        }

        #endregion

        public byte[] GerarRelatorioCnab(RelatorioConciliacaoAnaliticoTransacoesDTO request, OrderFilters order, List<QueryFilters> filters, EExtensaoArquivoRelatorio extensao, int idEmpresa)
        {
            bool? mostrarHeaderArquivoTxt = _parametrosApp.GetMostrarHeaderArquivoCsv(request.IdEmpresa ?? 0);
            var separadorArquivoTxt = "";
            var serv = _empresaRepository;
            Empresa emp = serv.Get(request.IdEmpresa ?? 0);
            String espaco;

            var dateHelper = new DateTimeHelper();
            var requestRelatorio = new RelatorioConciliacaoAnaliticoRequest
            {
                DataInicio = dateHelper.StartOfDay(request.DataInicial),
                DataFim = dateHelper.EndOfDay(request.DataFinal),
                ExibirMetadados = true,
            };

            var list = _cartoesService.GetRelatorioConciliacaoAnaliticoDataType(requestRelatorio, request.SomenteDivergencia, request.Pesquisa, 0, 0, order, filters);
            var txtBuilder = new TxtBuilderHelper(separadorArquivoTxt, mostrarHeaderArquivoTxt ?? false, false);
            var count = 0;
            string valor;
            //Header
            txtBuilder.AddRow();
            txtBuilder[count.ToString()] = emp.CodigoBancoCompensacao.ToStringSafe().PadLeft(3, ' ') +
            //Lote de Serviço
            "0000".ToStringSafe().PadLeft(4, ' ') +
            //Tipo de Registro
            "1".ToStringSafe().PadLeft(1, ' ') +
            //Tipo da Operação
            "E".ToStringSafe().PadLeft(1, ' ') +
            //Tipo da Servico
            "04".ToStringSafe().PadLeft(2, ' ') +
            //Forma de Lançamento
            "".ToStringSafe().PadLeft(2, ' ') +
            //Nº da Versão do Layout do Lote
            "033".ToStringSafe().PadLeft(3, ' ') +
            //Exclusivo
            "".ToStringSafe().PadLeft(1, ' ') +
            //Tipo de Inscrição da Empresa
            emp.TipoInscricaoEmpresa.ToStringSafe().PadLeft(1, ' ') +
            //Número de Inscrição da Empresa
            emp.NumeroInscricaoEmpresa.ToStringSafe().PadLeft(14, ' ') +
            //Cód do Convênio no Banco
            emp.CodigoConvenioBanco.ToStringSafe().PadLeft(20, ' ') +
            //Ag Mantenedora da Conta
            emp.AgenciaMantenedora.ToStringSafe().PadLeft(5, ' ') +
            //Dígito Verificador da Agência
            "".ToStringSafe().PadLeft(1, ' ') +
            //Número da Conta Corrente
            "".ToStringSafe().PadLeft(12, ' ') +
            //Dígito Verificador da Conta
            emp.DigitoVerificaConta.ToStringSafe().PadLeft(1, ' ') +
            //Dígito Verificador da Ag/Conta
            emp.DigitoVerificaContaAgConta.ToStringSafe().PadLeft(1, ' ') +
            //Nome da Empresa
            (emp.NomeFantasia.Length > 30 ? emp.NomeFantasia.Substring(0, 30) : emp.NomeFantasia.ToStringSafe().PadLeft(30, ' ')) +
            //Exclusivo1
            "".ToStringSafe().PadLeft(40, ' ') +
            //Data do Saldo Inicial
            "2".ToStringSafe().PadRight(8, ' ') +
            //Valor do Saldo Inicial"
            "".ToStringSafe().PadLeft(18, ' ') +
            //Situação do Saldo Inicial
            "".ToStringSafe().PadLeft(1, ' ') +
            //Posição do Saldo Inicial
            "".ToStringSafe().PadLeft(1, ' ') +
            //Moeda Referenciada no Extrato
            "".ToStringSafe().PadLeft(3, ' ') +
            //Número de Seqüência do Extrato
            "".ToStringSafe().PadLeft(5, ' ') +
            //Exclusivo2
            "".ToStringSafe().PadLeft(62, ' ');

            foreach (var item in list.items)
            {
                count++;
                espaco = item.TipoEvento == null ? "" : " ";
                valor = item.Valor == null ? "" : item.Valor;
                //Segmento
                txtBuilder.AddRow();
                txtBuilder[count.ToString()] = emp.CodigoBancoCompensacao.ToStringSafe().PadLeft(3, ' ') +
                //Lote de Servico
                "".ToStringSafe().ToStringSafe().PadLeft(4, ' ') +
                //Tipo de Registro
                "3".ToStringSafe().ToStringSafe().PadLeft(1, ' ') +
                //N Sequencial do Registro no Lote
                "".ToStringSafe().PadLeft(5, ' ') +
                //Codigo Segmento do Reg. Detalhe
                "E".ToStringSafe().PadLeft(1, ' ') +
                //Exclusivo3
                "".ToStringSafe().PadLeft(3, ' ') +
                //Tipo de Inscricao da Empresa
                emp.TipoInscricaoEmpresa.ToStringSafe().PadLeft(1, ' ') +
                //Numero de Inscricao da Empresa
                emp.NumeroInscricaoEmpresa.ToStringSafe().PadLeft(14, ' ') +
                //Código do Convênio no Banco
                emp.CodigoConvenioBanco.ToStringSafe().PadLeft(20, ' ') +
                //Agência Mantenedora da Conta
                emp.AgenciaMantenedora.ToStringSafe().PadLeft(5, ' ') +
                //Dígito Verificador da Agência
                "".ToStringSafe().PadLeft(1, ' ') +
                //Número da Conta Corrente
                item.NumeroCartao.ToStringSafe().PadLeft(12, ' ') +
                //Dígito Verificador da Conta
                emp.DigitoVerificaConta.ToStringSafe().PadLeft(1, ' ') +
                //Dígito Verificador da Ag-Conta
                emp.DigitoVerificaContaAgConta.ToStringSafe().PadLeft(1, ' ') +
                //Nome da Empresa
                (emp.NomeFantasia.Length > 30 ? emp.NomeFantasia.Substring(0, 30) : emp.NomeFantasia.ToStringSafe().PadLeft(30, ' ')) +
                //Exclusivo4
                "".ToStringSafe().PadLeft(6, ' ') +
                //Natureza do Lançamento
                "DPV".ToStringSafe().PadLeft(3, ' ') +
                //Tipo do Complemento Lançamento
                "00".ToStringSafe().PadLeft(2, ' ') +
                //Complemento do Lançamento
                "".ToStringSafe().PadLeft(20, ' ') +
                //Identificação de Isenção do CPMF
                "".ToStringSafe().PadLeft(1, ' ') +
                //Data Contábil
                "".ToStringSafe().PadLeft(8, ' ') +
                //Data do Lançamento
                item.Data.Substring(0, 10).Replace("/", "").PadLeft(8, ' ') +
                //Valor do Lançamento
                valor.Replace("R$", string.Empty).Replace(",", string.Empty).Replace(".", string.Empty).PadLeft(18, ' ') +
                //Tipo Lançamento: Valor a Déb./Créd
                (BuscarTipoTransacao(item.TipoTransacao) == ETipoTransacao.Credito ? "C" : "D".ToStringSafe().PadLeft(1, ' ')) +
                //Categoria do Lançamento
                "".ToStringSafe().PadLeft(3, ' ') +
                //Código Histórico no Banco
                "000".ToStringSafe().PadLeft(4, ' ') +
                //Descrição Histórico Lcto. no Banco
                (item.TipoTransacaoDescricao + espaco + item.TipoEvento).ToStringSafe().PadLeft(25, ' ') +
                //Número Documento/Complemento
                item.NumeroRecibo.ToStringSafe().PadRight(39, ' ');
            }

            count++;
            //Rodape
            txtBuilder.AddRow();
            txtBuilder[(count).ToString()] =
            //COD BANCO COMPENSACAO
            emp.CodigoBancoCompensacao.ToStringSafe().PadLeft(3, ' ') +
            //Lote de Serviço
            "".ToStringSafe().ToStringSafe().PadLeft(4, ' ') +
            //Tipo de Registro
            "5".ToStringSafe().PadLeft(1, ' ') +
            //Exclusivo5
            "".ToStringSafe().PadLeft(9, ' ') +
            //Tipo d e Inscrição da Empresa
            emp.TipoInscricaoEmpresa.ToStringSafe().PadLeft(1, ' ') +
            //Número de Inscrição da Empresa
            emp.NumeroInscricaoEmpresa.ToStringSafe().PadLeft(14, ' ') +
            //Código do Convênio no Banco
            emp.CodigoConvenioBanco.ToStringSafe().PadLeft(20, ' ') +
            //Agência Mantenedora da Conta
            emp.AgenciaMantenedora.ToStringSafe().PadLeft(5, ' ') +
            //Dígito Verificador da Agência
            "".ToStringSafe().PadLeft(1, ' ') +
            //Número da Conta Corrente
            "".ToStringSafe().PadLeft(12, ' ') +
            //Dígito Verificador da Conta
            emp.DigitoVerificaConta.ToStringSafe().PadLeft(1, ' ') +
            //Dígito Verificador da Ag/Conta
            emp.DigitoVerificaContaAgConta.ToStringSafe().PadLeft(1, ' ') +
            //Exclusivo6
            "".ToStringSafe().PadLeft(16, ' ') +
            //Saldo Bloqueado Acima 24 horas
            "".ToStringSafe().PadLeft(18, ' ') +
            //Limite da Conta
            "".ToStringSafe().PadLeft(18, ' ') +
            //Saldo Bloqueado até 24 Horas
            "".PadLeft(18, ' ') +
            //Data do Saldo Final
            "".ToStringSafe().PadLeft(8, ' ') +
            //Valor do Saldo Final
            "".ToStringSafe().PadLeft(18, ' ') +
            //Situação do Saldo Final
            "".ToStringSafe().PadLeft(1, ' ') +
            //Posição do Saldo Final
            "".ToStringSafe().PadLeft(1, ' ') +
            //Quantidade de Registros do Lote
            "".ToStringSafe().PadLeft(6, ' ') +
            //Somatória dos Valores a Débito
            "".ToStringSafe().PadLeft(18, ' ') +
            //Somatória dos Valores a Crédito
            "".ToStringSafe().PadLeft(18, ' ') +
            //Exclusivo7
            "".ToStringSafe().PadLeft(28, ' ');

            return txtBuilder.ExportToBytes();
        }

        public byte[] GerarRelatorioConciliacaoAnalitico(RelatorioConciliacaoAnaliticoTransacoesDTO request, OrderFilters order, List<QueryFilters> filters, EExtensaoArquivoRelatorio extensao, int idEmpresa)
        {
            bool? mostrarHeaderArquivoCsv = true;
            var separadorArquivoCsv = string.Empty;
            string saldoEmpresa;
            string logo;
            ConsultarSaldoEmpresaResponse saldoResponse = null;

            if (extensao == EExtensaoArquivoRelatorio.Csv)
            {
                mostrarHeaderArquivoCsv = _parametrosApp.GetMostrarHeaderArquivoCsv(request.IdEmpresa ?? 0);
                separadorArquivoCsv = _parametrosApp.GetSeparadorArquivoCsv(request.IdEmpresa ?? 0);

                if (string.IsNullOrEmpty(separadorArquivoCsv))
                    separadorArquivoCsv = ";";
            }

            var empresa = _empresaApp.Get(idEmpresa, null);

            if (empresa != null)
            {
                logo = empresa.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);
                saldoResponse = ConsultarSaldoEmpresa(empresa.CNPJ);
                saldoEmpresa = saldoResponse.SaldoConta.FormatMoney();
            }
            else
            {
                logo = ConstantesUtils.SemImagem;
                saldoEmpresa = "R$ 0,00";
            }

            var dateHelper = new DateTimeHelper();
            var requestRelatorio = new RelatorioConciliacaoAnaliticoRequest
            {
                DataInicio = dateHelper.StartOfDay(request.DataInicial),
                DataFim = dateHelper.EndOfDay(request.DataFinal),
                ExibirMetadados = true
            };

            var list = _cartoesService.GetRelatorioConciliacaoAnaliticoDataType(requestRelatorio, request.SomenteDivergencia, request.Pesquisa, 0, 0, order, filters);

            if (extensao == EExtensaoArquivoRelatorio.Csv)
            {
                var csvBuilder = new CsvBuilderHelper(separadorArquivoCsv, mostrarHeaderArquivoCsv ?? false);
                var nomeBanco = WebConfigurationManager.AppSettings["NOME_BANCO_EXPORTACAO_CONCILIACAO_CSV"];

                foreach (var item in list.items)
                {
                    if (item.StatusGeral == EStatusTransacaoConciliacao.Existente)
                    {
                        csvBuilder.AddRow();
                        csvBuilder["DATA DA CARGA"] = item.DataDt?.ToString("dd/MM/yyyy");
                        csvBuilder["DESCRIÇÃO"] = $"{item.TipoTransacaoDescricao} | {item.TipoEvento}";
                        csvBuilder["VALOR"] = BuscarValorPorTipoTransacao(item.TipoTransacao, item.ValorDecimal ?? 0).ToString(CultureInfo.GetCultureInfo("pt-BR"));
                        csvBuilder["TIPO"] = BuscarTipoTransacao(item.TipoTransacao) == ETipoTransacao.Credito ? "C" : "D";
                        csvBuilder["REF01"] = !string.IsNullOrEmpty(nomeBanco) ? nomeBanco : "OMNI";
                        csvBuilder["REF02"] = item.NumeroRecibo;
                        csvBuilder["REF03"] = item.IdParaCsv;
                        csvBuilder["DOC PROPRIETARIO"] = item.DocumentoPortador;
                        csvBuilder["NOME PROPRIETARIO"] = item.NomePortador;
                        csvBuilder["CARTAO PROPRIETARIO"] = item.NumeroCartao;
                        csvBuilder["INFORMAÇÕES"] = item.Informacoes;
                    }
                }

                return csvBuilder.ExportToBytes();
            }

            if (extensao == EExtensaoArquivoRelatorio.Ofx)
            {
                if (!_parametrosEmpresa.GetUtilizaRelatoriosOfx(idEmpresa)) return new byte[] { };
                
                var req = new OfxReportRequest
                {
                    Conta = list.items.Where(c => c.Conta != null).Select(c => c.Conta).FirstOrDefault().ToStringSafe(),
                    DataFim = request.DataFinal,
                    DataInicio = request.DataInicial,
                    Saldo = saldoResponse?.SaldoConta ?? 0,
                    Itens = new List<OfxReportRequestItem>(),
                    ContaParametrizado = _parametrosApp.GetCodigoOfx(empresa?.IdEmpresa ?? 0)
                };

                foreach (var item in list.items)
                {
                    var status = "Sucesso";

                    if (item.StatusAts == EStatusTransacaoConciliacao.Existente && item.StatusMeioHomologado == EStatusTransacaoConciliacao.Existente && item.StatusProcessadora == EStatusTransacaoConciliacao.Existente) 
                        status = "Sucesso";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Existente && item.StatusMeioHomologado == EStatusTransacaoConciliacao.Existente && item.StatusProcessadora == EStatusTransacaoConciliacao.Inexistente) 
                        status = "Falha na processadora";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Existente && item.StatusMeioHomologado == EStatusTransacaoConciliacao.Existente && item.StatusProcessadora == EStatusTransacaoConciliacao.Pendente) 
                        status = "Pendente na processadora";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Existente && item.StatusMeioHomologado == EStatusTransacaoConciliacao.Inexistente) 
                        status = "Falha no meio homologado";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Existente && item.StatusMeioHomologado == EStatusTransacaoConciliacao.Pendente) 
                        status = "Pendente no meio homologado";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Inexistente && item.StatusMeioHomologado == EStatusTransacaoConciliacao.Inexistente) 
                        status = "Falha na Plataforma e meio homologado";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Inexistente) 
                        status = "Falha na Plataforma";
                    else if (item.StatusAts == EStatusTransacaoConciliacao.Pendente) 
                        status = "Pendente na Plataforma";

                    var detalhes = status;
                    if (!string.IsNullOrWhiteSpace(item.TipoTransacaoDescricao)) detalhes += $" | {item.TipoTransacaoDescricao}";
                    if (!string.IsNullOrWhiteSpace(item.TipoEvento)) detalhes += $" | {item.TipoEvento}";

                    var ofxItem = new OfxReportRequestItem()
                    {
                        Valor = BuscarTipoTransacao(item.TipoTransacao) == ETipoTransacao.Credito 
                            ? item.ValorDecimal ?? 0 
                            : -(item.ValorDecimal ?? 0),
                        DataHoraTransacao = item.DataDt ?? DateTime.Now,
                        Detalhes = detalhes
                    };
                    
                    req.Itens.Add(ofxItem);
                }

                return OfxHelper.GenerateOfxResponse(req);
            }

            var relatorio = new RelatorioConciliacaoAnalitico().GetReport(extensao.DescriptionAttr(), list, logo,
                request.Saldoinicial, request.SaldoFinal, request.TotalProcessadora, request.TotalNaoConcilicado
                , saldoEmpresa, empresa?.RazaoSocial, empresa?.CNPJ);
            return relatorio;
        }

        public byte[] GerarSolicitarCompraPedagio(ConsultaCompraPedagioRequest request, int take, int page, IEnumerable<object> order, List<QueryFilters> filters, string extensao, int idEmpresa)
        {

            ConsultarCompraPedagio(take, page, order, null, request);
            var relatorio = new byte[1];
            return relatorio;
        }

        public byte[] GerarRelatorioSituacaoCartoes(RelatorioCartaoApiRequest request, int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao, int idEmpresa)
        {
            var logoEmpresa = _empresaApp.Get(idEmpresa, null)?.Logo;
            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);
            var list = _cartoesService.GetRelatorioSituacaoCartao(request, take, page, order, filters);
            var relatorio = new RelatorioSituacaoDosCartoes().GetReport(extensao, list, logo);
            return relatorio;
        }

        public SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponse IntegrarEmpresa(Empresa empresa)
        {
            return _cartoesService.IntegrarEmpresa(empresa);
        }

        public byte[] GerarRelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters,string extensao, int idEmpresa)
        {
            var requestRelatorio = new RelatorioTransferenciasContaBancariaRequest();

            requestRelatorio.Produto = request.Produto;
            requestRelatorio.DataInicio = request.DataInicio.Date;
            requestRelatorio.DataFim = request.DataFim.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
            switch (request.Status)
            {
                case StatusExportacao.Todos:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Todos;
                    break;
                case StatusExportacao.NaoExportados:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.NaoExportados;
                    break;
                case StatusExportacao.Exportado:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Exportados;
                    break;
                case StatusExportacao.Sucesso:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Sucesso;
                    break;
                case StatusExportacao.Erro:
                    requestRelatorio.Status = RelatorioTransferenciasContaBancariaRequestStatus.Erro;
                    break;
            }

            switch (request.TipoTransacao)
            {
                case TipoTransacao.Todos:
                    requestRelatorio.TipoTransacao = RelatorioTransferenciasContaBancariaRequestTipoTransacao.Todos;
                    break;
                case TipoTransacao.Transferencias:
                    requestRelatorio.TipoTransacao =
                        RelatorioTransferenciasContaBancariaRequestTipoTransacao.Transferencias;
                    break;
                case TipoTransacao.Estornos:
                    requestRelatorio.TipoTransacao = RelatorioTransferenciasContaBancariaRequestTipoTransacao.Estornos;
                    break;
            }
            var logoEmpresa = _empresaApp.Get(idEmpresa, null)?.Logo;
            var logo = logoEmpresa == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(logoEmpresa);
            var list = _cartoesService.GetRelatorioTransferenciasContaBancaria(requestRelatorio, take, page, order, filters);
            var relatorio = new RelatorioTransferenciaContasBancarias().GetReport(extensao, list, logo);
            return relatorio;
        }

        public List<PessoaContaBancariaResponse> GetContaBancaria(string documento)
        {
            return _cartoesService.GetContaBancaria(documento);
        }

        /// <summary>
        /// Método utilizado pelo aplicativo
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cpfCnpj"></param>
        /// <param name="documentoUsuarioAudit"></param>
        /// <param name="nomeUsuarioAudit"></param>
        /// <returns></returns>
        public ConsultarExtratoResponseDTO ConsultarExtrato(ConsultarExtratoRequest request,string cpfCnpj = null,string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            var extrato = _cartoesService.ConsultarExtrato(request, documentoUsuarioAudit, nomeUsuarioAudit);
            var usuario = _usuarioApp.GetPorCNPJCPF(cpfCnpj);
            var lHashId = _despesaUsuarioApp.GetListHashId(usuario?.IdUsuario);
            extrato.Objeto.Detalhes.ForEach(x => x.ArquivosAnexados = lHashId.Contains(x.HashId));

            return extrato;
        }

        /// <summary>
        /// Método utilizado pelo portal
        /// </summary>
        /// <param name="request"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        public ObjetoGridExtratoResponseDTO ConsultarExtrato(ConsultaExtratoDTORequest request, OrderFilters order, List<QueryFilters> filters)
        {
            if (request.Identificador == 0) return null;

            var requestApi = new ConsultarExtratoRequest
            {
                DataFim = request.DataFim?.Date.EndOfDay() ?? DateTime.Today.EndOfDay(),
                DataInicio = request.DataInicio?.Date.StartOfDay() ?? DateTime.Today.AddDays(-30).StartOfDay(),
                Tipo = request.Tipo,
                Cartao = new IdentificadorCartao
                {
                    Produto = request.Produto,
                    Identificador = request.Identificador
                },
                SomenteTransferencia = request.SomenteTransferencia
            };

            //Validação de data
            if(requestApi.DataInicio != null && requestApi.DataFim != null)
            {
                if(requestApi.DataInicio.Value.StartOfDay() > requestApi.DataFim.Value.StartOfDay())
                    throw new Exception("Data inicial não pode ser maior que a final!");
                if(requestApi.DataFim.Value.StartOfDay() > DateTime.Now.StartOfDay())
                    throw new Exception("Data final não pode ser maior que a data atual!");
                if(requestApi.DataFim.Value.StartOfDay() > requestApi.DataInicio.Value.AddDays(60).StartOfDay())
                    throw new Exception("Não é permitido consultar um periodo maior que 60 dias!");
            }

            var extratoCartao = _cartoesService.ConsultarExtrato(requestApi,null,null);

            if (!extratoCartao.Sucesso)
                throw new Exception(extratoCartao.Mensagem);

            var hashIdList = extratoCartao.Objeto.Detalhes.Select(x => x.HashId);

            var despesaUsuarioList = _despesaUsuarioApp
                .GetAll().Where(x => hashIdList.Contains(x.HashId))
                .ToList();

            return new ObjetoGridExtratoResponseDTO()
            {
                totalItems = extratoCartao.Objeto.Detalhes.Count,
                items = extratoCartao.Objeto.Detalhes
                    .AsQueryable()
                    .Select(x => new ConsultarExtratoGridResponseDTO
                    {
                        ValorFormatado = x.Valor.FormatMoney(),
                        Descricao = x.Historico.DefaultIfNullOrWhiteSpace(x.DescricaoProcessadora),
                        InformacoesAdicionais = x.InformacoesAdicionais,
                        Metadados = x.Metadados,
                        ProtocoloProcessamento = x.ProtocoloProcessamento,
                        ProtocoloRequisicao = x.ProtocoloRequisicao,
                        DataTransacao = x.Data.FormatDateTimeBr(),
                        Informacoes = string.Join(" / ", x.InformacoesAdicionais.Select(i => i.Key + ": " + i.Value)),
                        SaldoFinal = x.SaldoFinal.FormatMoney(),
                        Tipo = x.Tipo == "D" ? "Débito" : "Crédito",
                        Valor = x.Valor.FormatMoney(),
                        NomeDestino = x.NomeDestino,
                        CpfCnpjDestino = x.CpfCnpjDestino,
                        CartaoDestino = x.CartaoDestino,
                        Instituicao = x.Instituicao,
                        Agencia = x.Agencia,
                        Conta = x.Conta,
                        Dv = x.Dv,
                        HashId = despesaUsuarioList.FirstOrDefault(y => y.HashId == x.HashId) != null
                            ? x.HashId
                            : null,
                        Url = despesaUsuarioList
                            .Where(y => y.HashId == x.HashId && y.URL != null)
                            .Select(y => y.URL)
                            .ToList()
                    })
                    .AplicarFiltrosDinamicos(filters)
                    .AplicarOrderByDinamicos(order)
                    .Skip((request.Page - 1) * request.Take)
                    .Take(request.Take)
                    .ToList()
            };
        }

        public BusinessResult<GetExtratoBizGridResponse> ConsultarExtratoV2(ConsultaExtratoV2DTORequest request)
        {
            if (request.Identificador == 0) return null;
            
            //Validação de data
            request.DataInicio = request.DataInicio.StartOfDay();
            request.DataFim = request.DataFim.EndOfDay();
            if (request.DataInicio > request.DataFim)
                return BusinessResult<GetExtratoBizGridResponse>.Error("Data inicial não pode ser maior que a final!");
            if (request.DataFim > DateTime.Now.EndOfDay())
                return BusinessResult<GetExtratoBizGridResponse>.Error("Data final não pode ser maior que a data atual!");
            if (request.DataFim > request.DataInicio.AddDays(30).EndOfDay())
                return BusinessResult<GetExtratoBizGridResponse>.Error("Não é permitido consultar um periodo maior que 30 dias!");

            var extratoCartao = _cartoesService.ConsultarExtratoV2(request);

            if (!extratoCartao.Success)
                return BusinessResult<GetExtratoBizGridResponse>.Error(extratoCartao.Messages.FirstOrDefault());

            return BusinessResult<GetExtratoBizGridResponse>.Valid(new GetExtratoBizGridResponse()
            {
                TotalItens = extratoCartao.Value.TotalItens,
                Itens = extratoCartao.Value.Itens
                    .AsQueryable()
                    .Select(x => new GetExtratoBizItemGridResponse
                    {
                        DataHoraTransacao = x.DataHoraTransacao.FormatDateTimeBr(),
                        ValorTransacao = x.SimboloMoedaOrigem + " " + x.ValorOrigem.ToString("N2"),
                        DescricaoPlanoVendas = x.DescricaoPlanoVendas,
                        NumeroCartao = x.NumeroCartao,
                        DocumentoPortador = x.DocumentoPortador,
                        NumeroConta = x.NumeroConta,
                        Mcc = x.Mcc,
                        DescricaoMcc = x.DescricaoMcc,
                        DescricaoGrupoMcc = x.DescricaoGrupoMcc,
                        ValorTransacaoDolar = x.SimboloMoedaDolar + " " + x.ValorTransacaoDolar.ToString("N2"),
                        CodigoMoedaDolar = x.CodigoMoedaDolar,
                        CotacaoDolarnoDia = x.SimboloMoedaDolar + " " + x.CotacaoDolarnoDia.ToString("N2"),
                        ValorLocal = x.SimboloMoedaLocal + " " + x.ValorLocal.ToString("N2"),
                        CodigoMoedaLocal = x.CodigoMoedaLocal,
                        TerminalId = x.TerminalId,
                    })
                    .ToList()
            });
        }

        public AtsPortadorRequest ConsultarPortadorAtendimento(string documento)
        {
            var portadorAts = CarregarInformacoesPortador(documento);
            if (portadorAts?.Documento != null)
                return portadorAts;

            var portadorMs = _cartoesService.ConsultarPortadorDetalhado(documento);

            return portadorMs?.CpfCnpj != null ? Mapper.Map<AtsPortadorRequest>(portadorMs) : null;
        }

        public ConsultarSaldoCartaoResponse ConsultarSaldoCartaoAtendimento(ConsultarSaldoCartaoRequest request)
        {
            return _cartoesService.ConsultarSaldoCartaoAtendimento(request);
        }

        private static decimal BuscarValorPorTipoTransacao(ConciliacaoItemTipoProcessadora? tipoTransacao, decimal valor)
        {
            if (!tipoTransacao.HasValue)
                return -valor;

            if (!Enum.IsDefined(typeof(ConciliacaoItemTipoProcessadora), tipoTransacao))
                return -valor;

            if (BuscarTipoTransacao(tipoTransacao) == ETipoTransacao.Credito)
                return valor;

            if (BuscarTipoTransacao(tipoTransacao) == ETipoTransacao.Debito)
                return -valor;

            return -valor;
        }

        private static ETipoTransacao? BuscarTipoTransacao(ConciliacaoItemTipoProcessadora? tipoTransacao)
        {
            if (!tipoTransacao.HasValue)
                return null;

            return tipoTransacao.Value switch
            {
                ConciliacaoItemTipoProcessadora.CargaPortador => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.EstornoPortador => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.CargaPedagioPortador => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.EstornoPedagioPortador => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.EstornoDepositoEmpresa => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.DepositoEmpresa => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.OutrosDebitos => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.OutrosCreditos => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.Inesperado => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.ResgateSaldoResidualPedagio => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.CargaPedagioManual => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.EstornoPedagioManual => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.TransferenciaViaPixDebito => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.TransferenciaViaPixCredito => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.PagamentoDeContas => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.EstornoPagamentoDeContas => ETipoTransacao.Credito,
                ConciliacaoItemTipoProcessadora.ResgateValorDebito => ETipoTransacao.Debito,
                ConciliacaoItemTipoProcessadora.ResgateValorCredito => ETipoTransacao.Credito,
                _ => throw new ArgumentOutOfRangeException(nameof(tipoTransacao), tipoTransacao, "Tipo de transação inesperada para definição entre crédito e débito.")
            };
        }

        public IdentificadorCartao GetUltimoCartao(string documento, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            var produtoId = new List<int> { GetIdProdutoCartaoFretePadrao() };
            var cartoesPessoa = GetCartoesVinculados(documento, produtoId, false, documentoUsuarioAudit, nomeUsuarioAudit);

            if (cartoesPessoa?.Cartoes == null || !cartoesPessoa.Cartoes.Any())
                return null;

            var cartao = cartoesPessoa.Cartoes.Last();

            return new IdentificadorCartao()
            {
                Identificador = cartao.Identificador,
                Produto = cartao.Produto.Id
            };
        }

        public ConsultarContasBancariasResponseDTO ContasBancarias(string documento, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            return _cartoesService.ContasBancarias(documento, documentoUsuarioAudit, nomeUsuarioAudit);
        }

        public InativarContaBancariaAtsResponseDTO InativarContaBancaria(InativarContaBancariaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            return _cartoesService.InativarContaBancaria(request, documentoUsuarioAudit, nomeUsuarioAudit);
        }

        public ValidarSenhaCartaoResponseDto ValidarSenhaCartao(int identificador, int produto, string senha)
        {
            return _cartoesService.ValidarSenhaCartao(identificador, produto, senha);
        }

        public object GetResgatarValor(ConsultarResgateValorDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var response = _resgatarCartaoApp.ConsultarGrid(request, take, page, order, filters);

            return response;
        }

        public OperacaoCartaoResponseDTO TransferirValorCartaoAtendimento(string documentoOrigem, string documentoDestino, decimal valor, string cnpjEmpresa, EOrigemTransacaoCartao origem, string senha, IdentificadorCartao cartaoVinculadoOrigem, IdentificadorCartao cartaoVinculadoDestino, int? administradoraId = null)
        {
            return _cartoesService.TransferirValorCartaoAtendimento(documentoOrigem, documentoDestino, valor, cnpjEmpresa, origem, senha, cartaoVinculadoOrigem, cartaoVinculadoDestino, administradoraId);
        }

        public EmpresaTokenMicroServicoDto GetOrGenerateTokenEmpresa(string cnpjEmpresa, string appName, int grupoContabilizacao)
        {
            return _cartoesService.GetOrGenerateTokenEmpresa(cnpjEmpresa, appName, grupoContabilizacao);
        }

        public byte[] GerarRelatorioExtratoGrid(ConsultarExtratoRequest request, RelatorioGridExtratoDTO requestGrid)
        {
            return _cartoesService.GerarRelatorioExtratoGrid(request, requestGrid);
        }

        public ConsultarSaldoEmpresaResponse ConsultarSaldoEmpresa(string cnpj)
        {
            return _cartoesService.ConsultarSaldoEmpresa(cnpj);
        }

        public CancelarCartaoRemessaResponse CancelarCartaoRemessa(CancelarCartaoRemessaRequest cancelarCartaoRemessaAtsRequest)
        {
            return _cartoesService.CancelarCartaoRemessa(cancelarCartaoRemessaAtsRequest);
        }

        private void AplicarCustomFilters(List<CustomFilter> customFilter, ConsultaCompraPedagioResponse resultado)
        {
            var dtoRetorno = resultado.CompraPedagioDTOList.AsQueryable();

            var filtroNomeEmpresa = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("NomeEmpresa"));
            if (filtroNomeEmpresa != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.NomeEmpresa) && x.NomeEmpresa.ToUpper().Contains(filtroNomeEmpresa.Value.ToUpper()));

            var filtroCidadeOrigem = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("CidadeOrigem"));
            if (filtroCidadeOrigem != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.CidadeOrigem) && x.CidadeOrigem.ToUpper().Contains(filtroCidadeOrigem.Value.ToUpper()));

            var filtroCidadeDestino = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("CidadeDestino"));
            if (filtroCidadeDestino != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.CidadeDestino) && x.CidadeDestino.ToUpper().Contains(filtroCidadeDestino.Value.ToUpper()));

            var filtroNomeFavorecido = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("NomeFavorecido"));
            if (filtroNomeFavorecido != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.NomeFavorecido) && x.NomeFavorecido.ToUpper().Contains(filtroNomeFavorecido.Value.ToUpper()));

            var filtroPlaca = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("Placa"));
            if (filtroPlaca != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.Placa) && x.Placa.ToUpper().RemoverCaracteresEspeciais().Contains(filtroPlaca.Value.ToUpper().RemoverCaracteresEspeciais()));

            var filtroQtdEixos = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("QtdEixos"));
            if (filtroQtdEixos != null) dtoRetorno = dtoRetorno.Where(x => x.QtdEixos.HasValue && x.QtdEixos.Value == filtroQtdEixos.Value.OnlyNumbers().ToIntNullable(0));

            var filtroStatus = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("Status"));
            if (filtroStatus != null) dtoRetorno = dtoRetorno.Where(x => x.Status != null && x.Status.GetDescription().ToUpper().Contains(filtroStatus.Value.ToUpper()));

            var filtroValor = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("Valor"));
            if (filtroValor != null) dtoRetorno = dtoRetorno.Where(x => x.Valor.HasValue && x.Valor.ToString().OnlyNumbers().Contains(filtroValor.Value.OnlyNumbers()));

            var filtroValorResgatado = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("ValorResgatado"));
            if (filtroValorResgatado != null) dtoRetorno = dtoRetorno.Where(x => x.ValorResgatado.HasValue && x.ValorResgatado.ToString().OnlyNumbers().Contains(filtroValorResgatado.Value.OnlyNumbers()));

            var filtroNumeroCiot = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("NumeroCIOT"));
            if (filtroNumeroCiot != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.NumeroCIOT) && x.NumeroCIOT.ToUpper().Contains(filtroNumeroCiot.Value.ToUpper()));

            var filtroFornecedor = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("Fornecedor"));
            if (filtroFornecedor != null) dtoRetorno = dtoRetorno.Where(x => x.Fornecedor != null && x.Fornecedor.GetDescription().ToUpper().Contains(filtroFornecedor.Value.ToUpper()));

            var filtroDocumentoProprietario = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("DocumentoProprietario"));
            if (filtroDocumentoProprietario != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.DocumentoProprietario) && x.DocumentoProprietario.OnlyNumbers().Contains(filtroDocumentoProprietario.Value.OnlyNumbers()));

            var filtroNomeProprietario = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("NomeProprietario"));
            if (filtroNomeProprietario != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.NomeProprietario) && x.NomeProprietario.ToUpper().Contains(filtroNomeProprietario.Value.ToUpper()));

            var filtroProtocoloValePedagio = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("ProtocoloValePedagio"));
            if (filtroProtocoloValePedagio != null) dtoRetorno = dtoRetorno.Where(x => !string.IsNullOrEmpty(x.ProtocoloValePedagio) && x.ProtocoloValePedagio.OnlyNumbers().Contains(filtroProtocoloValePedagio.Value.OnlyNumbers()));

            var filtroProtocoloRequisicao = customFilter.FirstOrDefault(c => c.Field.EqualsIgnoreCase("ProtocoloRequisicao"));
            if (filtroProtocoloRequisicao != null) dtoRetorno = dtoRetorno.Where(x => x.ProtocoloRequisicao.HasValue && x.ProtocoloRequisicao == Convert.ToInt64(filtroProtocoloRequisicao.Value.OnlyNumbers()));

            resultado.CompraPedagioDTOList = dtoRetorno.ToList();
        }

        public PessoaCartaoResponse GetCartaoFretePorVinculoDaEmpresa(string documento)
        {
            return _cartoesService.GetCartaoFretePorVinculoDaEmpresa(documento);
        }
        
        public BusinessResult CadastrarEmbarcadorViaFacilSTP(int idEmpresa)
        {
            try
            {
                var request = _empresaApp
                    .All()
                    .Include(x => x.Estado)
                    .Include(x => x.Cidade)
                    .Where(c => c.IdEmpresa ==  idEmpresa).Select(c => new EmbarcadorEmpresa
                    {
                        Cnpj = c.CNPJ,
                        RazaoSocial = c.RazaoSocial,
                        NomeFantasia = c.NomeFantasia,
                        Numero = c.Numero ?? 0,
                        Endereco = c.Endereco,
                        Telefone = c.Telefone,
                        Estado = c.Estado.Sigla,
                        Email = c.Email,
                        Cidade = c.Cidade.Nome,
                        Cep = c.CEP,
                        Bairro = c.Bairro
                    }).FirstOrDefault();

                if (request == null)
                    return BusinessResult.Error("Empresa não Encontrada na base de dados!");
                
                var result =  _cartoesService.CadastrarEmbarcadorViaFacilSTP(request);

                if (result.Status != CadastrarEmbarcadorViaFacilSTPResponseStatus.Sucesso)
                    return BusinessResult.Error(result.Mensagem);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult<PedagioGridTransportadorViaFacilResponse> GridTransportadorRntrcViaFacil(PedagioGridTransportadorViaFacilRequest request)
        {
            try
            {
                var empresa = _empresaApp.Get(request.IdEmpresa ?? 0);

                if (empresa == null)
                    return BusinessResult<PedagioGridTransportadorViaFacilResponse>.Valid(new PedagioGridTransportadorViaFacilResponse());

                var result =  _cartoesService.ConsultarTransportadorRntrcViaFacil(empresa?.CNPJ);

                if (result.Status != ConsultaTransportadorViaFacilResponseStatus.Sucesso)
                    return BusinessResult<PedagioGridTransportadorViaFacilResponse>.Error(result.Mensagem);

                var resultGrid = string.IsNullOrWhiteSpace(request.Order?.Campo)
                    ? result.Itens.OrderByDescending(x => x.Placa)
                    : result.Itens.OrderBy($"{request.Order?.Campo} {request.Order?.Operador.DescriptionAttr()}");
                
                resultGrid = resultGrid.AsQueryable().AplicarFiltrosDinamicos(request.Filters);
                
                return BusinessResult<PedagioGridTransportadorViaFacilResponse>.Valid(new PedagioGridTransportadorViaFacilResponse
                {
                    Modalidade = result.Modalidade,
                    totalItems = resultGrid.Count(),
                    items = resultGrid.Skip((request.Page - 1) * request.Take).Take(request.Take)
                        .Select(x => new PedagioGridItemTransportadorViaFacilResponse
                        {
                            CnpjEmbarcador = empresa?.CNPJ.FormatarCpfCnpjSafe(),
                            CpfCnpjTransportador = x.CpfCnpjTransportador.FormatarCpfCnpjSafe(),
                            Placa = x.Placa.FormatarPlaca(),
                            CodigoParceiro = x.CodigoParceiro
                        }).ToList()
                });
            }
            catch (Exception e)
            {
                return BusinessResult<PedagioGridTransportadorViaFacilResponse>.Error(e.Message);
            }
        }
        
        public BusinessResult DeletarTransportadorRntrcViaFacil(PedagioDeletarTransportadorViaFacilRequest request)
        {
            try
            {
                var result =  _cartoesService.DeletarTransportadorRntrcViaFacil(new DeletarTransportadorViaFacilRequest
                {
                    Placa = request.Placa.Replace("-", ""),
                    CpfCnpjTransportador = request.CpfCnpjTransportador.OnlyNumbers(),
                    CnpjEmbarcador = request.CnpjEmbarcador.OnlyNumbers()
                });

                if (result.Status != DeletarTransportadorViaFacilResponseStatus.Sucesso)
                    return BusinessResult.Error(result.Mensagem);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult CadastrarTransportadorRntrcViaFacil(PedagioCadastrarTransportadorViaFacilRequest request)
        {
            try
            {
                var placaFormatada = request.Placa.Replace("-", "");
                var empresa = _empresaApp.Get(request.IdEmpresa);

                if (empresa == null)
                    return BusinessResult.Error("Empresa não localizada na base!");
                
                var proprietario = _proprietarioApp.Get(request.IdProprietario);

                if (proprietario == null)
                    return BusinessResult.Error("Proprietário não localizada na base!");

                var resultConsultar =  _cartoesService.ConsultarTransportadorRntrcViaFacil(empresa.CNPJ);

                if (resultConsultar.Itens != null)
                {
                    if (resultConsultar.Itens.Any(x => x.Placa == placaFormatada && x.CpfCnpjTransportador == proprietario.CNPJCPF && x.CnpjEmbarcador == empresa.CNPJ))
                        return BusinessResult.Valid();
                    if (resultConsultar.Itens.Any(x => x.Placa == placaFormatada && x.CnpjEmbarcador == empresa.CNPJ))
                    {
                        var resultEditar =  _cartoesService.EditarTransportadorRntrcViaFacil(new EditarTransportadorViaFacilRequest
                        {
                            Placa = placaFormatada,
                            CpfCnpjTransportador = proprietario.CNPJCPF,
                            CnpjEmbarcador = empresa.CNPJ
                        });

                        if (resultEditar.Status != EditarTransportadorViaFacilResponseStatus.Sucesso)
                            return BusinessResult.Error(resultEditar.Mensagem);
                    
                        return BusinessResult.Valid();
                    }
                }

                var result =  _cartoesService.CadastrarTransportadorRntrcViaFacil(new CadastrarTransportadorViaFacilRequest
                {
                    Placa = placaFormatada,
                    CpfCnpjTransportador = proprietario.CNPJCPF,
                    CnpjEmbarcador = empresa.CNPJ
                });

                if (result.Status != CadastrarTransportadorViaFacilResponseStatus.Sucesso)
                    return BusinessResult.Error(result.Mensagem);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }
        
        public BusinessResult<PedagioConsultarTransportadorViaFacilResponse> ConsultarTransportadorRntrcViaFacil(PedagioConsultarTransportadorViaFacilRequest request)
        {
            try
            {
                var empresa = _empresaApp.Get(request.CnpjEmbarcador.OnlyNumbers());

                if (empresa == null)
                    return BusinessResult<PedagioConsultarTransportadorViaFacilResponse>.Error("Empresa não localizada na base!");
                
                var proprietario = _proprietarioApp.GetPorCpfCnpj(request.CpfCnpjTransportador.OnlyNumbers());

                if (proprietario == null)
                    return BusinessResult<PedagioConsultarTransportadorViaFacilResponse>.Error("Proprietário não localizada na base!");
                
                return BusinessResult<PedagioConsultarTransportadorViaFacilResponse>.Valid(new PedagioConsultarTransportadorViaFacilResponse()
                {
                    IdEmpresa = empresa.IdEmpresa,
                    IdProprietario = proprietario.IdProprietario,
                    RazaoSocialProp = proprietario.RazaoSocial,
                    RazaoSocialEmp = empresa.RazaoSocial
                });
            }
            catch (Exception e)
            {
                return BusinessResult<PedagioConsultarTransportadorViaFacilResponse>.Error(e.Message);
            }
        }
    }
}


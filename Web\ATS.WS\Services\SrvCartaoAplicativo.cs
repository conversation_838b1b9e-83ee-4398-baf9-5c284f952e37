﻿using System;
using System.Collections.Generic;
using System.Text;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Mobile.Response;
using ATS.WS.Models.Webservice.Request.Cartoes;
using ATS.WS.Models.Webservice.Response.Cartao;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Services
{
    public class SrvCartaoAplicativo : SrvBase
    {
        private readonly IViagemApp _viagemApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IEmpresaApp _empresaApp;
        private readonly ILimiteTransacaoPortadorApp _limiteApp;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly IRsaCryptoService _rsaCryptoService;

        public SrvCartaoAplicativo(IViagemApp viagemApp, IParametrosApp parametrosApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IEmpresaApp empresaApp, ILimiteTransacaoPortadorApp limiteApp, ITransacaoCartaoApp transacaoCartaoApp, IRsaCryptoService rsaCryptoService)
        {
            _viagemApp = viagemApp;
            _parametrosApp = parametrosApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _empresaApp = empresaApp;
            _limiteApp = limiteApp;
            _transacaoCartaoApp = transacaoCartaoApp;
            _rsaCryptoService = rsaCryptoService;
        }

        public Retorno<CartaoTransferenciaAplicativoResponse> TransferirValorCartao(CartaoTransferenciaAplicativoModel request)
        {
            if (string.IsNullOrWhiteSpace(request.DocumentoOrigem))
                return new Retorno<CartaoTransferenciaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Documento de origem é obrigatório."
                };
            
            if (string.IsNullOrWhiteSpace(request.DocumentoDestino))
                return new Retorno<CartaoTransferenciaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Documento de destino é obrigatório."
                };
            
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

            if (string.IsNullOrWhiteSpace(request.Senha) && string.IsNullOrWhiteSpace(request.SenhaCrypt))
                return new Retorno<CartaoTransferenciaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Necessário informar a senha!"
                };

            if (request.DocumentoOrigem == request.DocumentoDestino)
                return new Retorno<CartaoTransferenciaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Não é permitido fazer transferência entre o mesmo documento"
                };

            if (!ValidarLimiteUnitarioTransferenciaCartao(request.DocumentoOrigem, request.Valor))
                return new Retorno<CartaoTransferenciaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Valor ultrapassa o seu limite unitário para transferências!"
                };

            if (!ValidarLimiteDiarioTransferenciaCartao(request.DocumentoOrigem, request.Valor))
                return new Retorno<CartaoTransferenciaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Valor ultrapassa o seu limite diário para transferências!"
                };

            var senhaParaValidacao = request.Senha;
            
            if (!string.IsNullOrWhiteSpace(request.SenhaCrypt))
            {
                string senhaDecrypt;
                try
                {
                    senhaDecrypt = _rsaCryptoService.Decrypt(request.SenhaCrypt);
                    if (string.IsNullOrEmpty(senhaDecrypt)) throw new Exception();
                }
                catch (Exception)
                {
                    throw new InvalidOperationException("Senha criptografada incorretamente.");
                }
                var senhaDecryptBytes = Encoding.UTF8.GetBytes(senhaDecrypt);
                senhaParaValidacao = Convert.ToBase64String(senhaDecryptBytes);
            }

            var retorno = cartoesApp.TransferirValorCartao(request.DocumentoOrigem, 
                request.DocumentoDestino, request.Valor, request.CNPJEmpresa, EOrigemTransacaoCartao.ManualAplicativo, 
                senhaParaValidacao);

            var response = new Retorno<CartaoTransferenciaAplicativoResponse>
            {
                Sucesso = retorno.Status == EStatusPagamentoCartao.Baixado || retorno.Status == EStatusPagamentoCartao.Pendente,
                Mensagem = retorno.Mensagem
            };

            response.Objeto = new CartaoTransferenciaAplicativoResponse
            {
                Protocolo = retorno.Protocolo.GetValueOrDefault(0)
            };

            return response;
        }

        public Retorno<CartaoTransferenciaContaBancariaAplicativoResponse> TransferirContaBancaria(CartaoTransferenciaContaBancariaAplicativoModel request)
        {
            if (string.IsNullOrWhiteSpace(request.DocumentoFavorecido))
                return new Retorno<CartaoTransferenciaContaBancariaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Documento do favorecido é obrigatório."
                };

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

            if (string.IsNullOrWhiteSpace(request.Senha) && string.IsNullOrWhiteSpace(request.SenhaCrypt))
                return new Retorno<CartaoTransferenciaContaBancariaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Necessário informar a senha!"
                };

            if (!ValidarLimiteUnitarioTransferenciaTED(request.DocumentoFavorecido, request.Valor))
                return new Retorno<CartaoTransferenciaContaBancariaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Valor ultrapassa o seu limite unitário para transferências!"
                };

            if (!ValidarLimiteDiarioTransferenciaTED(request.DocumentoFavorecido, request.Valor))
                return new Retorno<CartaoTransferenciaContaBancariaAplicativoResponse>
                {
                    Sucesso = false,
                    Mensagem = "Valor ultrapassa o seu limite diário para transferências!"
                };

            var senhaParaValidacao = request.Senha;
            
            if (!string.IsNullOrWhiteSpace(request.SenhaCrypt))
            {
                string senhaDecrypt;
                try
                {
                    senhaDecrypt = _rsaCryptoService.Decrypt(request.SenhaCrypt);
                    if (string.IsNullOrEmpty(senhaDecrypt)) throw new Exception();
                }
                catch (Exception)
                {
                    throw new InvalidOperationException("Senha criptografada incorretamente.");
                }
                var senhaDecryptBytes = Encoding.UTF8.GetBytes(senhaDecrypt);
                senhaParaValidacao = Convert.ToBase64String(senhaDecryptBytes);
            }
            
            var transferencia = new TransferirContaBancariaRequestDTO();

            // Quando o app não envia, é realizado o processo com o produto padrão da empresa.
            // App com perfil terceiro, não possui empresa vinculada.
            // App's especificos de empresa (SOTRAN), já utilizam o token e cnpj de empresa
            if (request.CartaoOrigem != null)
            {
                transferencia.CartaoOrigem = new CartaoIdentificacaoDto
                {
                    Identificador = request.CartaoOrigem.Identificador,
                    Produto = request.CartaoOrigem.Produto
                };
            }

            transferencia.Valor = request.Valor;
            transferencia.CnpjEmpresa = request.CNPJEmpresa;
            transferencia.ContaBancaria = request.ContaBancaria;
            transferencia.DocumentoFavorecido = request.DocumentoFavorecido;
            transferencia.Senha = senhaParaValidacao;

            var retorno = cartoesApp.TransferirValorContaBancaria(transferencia, EOrigemTransacaoCartao.ManualAplicativo);

            var response = new Retorno<CartaoTransferenciaContaBancariaAplicativoResponse>
            {
                Sucesso = retorno.Status == EStatusPagamentoCartao.Baixado || retorno.Status == EStatusPagamentoCartao.Pendente,
                Mensagem = retorno.Mensagem
            };

            response.Objeto = new CartaoTransferenciaContaBancariaAplicativoResponse
            {
                Protocolo = retorno.Protocolo.GetValueOrDefault(0)
            };

            return response;
        }

        public Retorno<object> MotoristaValidoParaTransferencia(MotoristaValidoParaTransferenciaRequest request)
        {
            var empresa = _empresaApp.Get(request.CNPJEmpresa);
            if (empresa == null)
                return new Retorno<object>(false, "Empresa não localizada com CNPJ: " + request.CNPJEmpresa);

            var idEmpresa = empresa.IdEmpresa;

            var valido = _viagemApp.ExisteCartaFreteParaCombinacao(
                request.CpfCnpjProprietario,
                request.CpfMotorista,
                idEmpresa, 3);

            return new Retorno<object>(valido);
        }

        public Retorno<CartaoParametroReponse> CartaoParametros(CartaoParametrosRequest request)
        {
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

            var response = cartoesApp.GetCartaoProduto(cartoesApp.GetIdProdutoCartaoFretePadrao());

            var cartao = new CartaoParametroReponse();
            cartao.Taxas =  Mapper.Map<List<ProdutoTaxa>, List<TaxasValoresResponse>>(response.Taxas);

            var idempresa = _empresaApp.GetIdPorCnpj(request.CNPJEmpresa);

            if (idempresa != null)
            {
                cartao.InformacoesTransferenciaBancaria = _parametrosApp.GetInformacoesTransferenciaBancaria(idempresa.Value);
            }

            return new Retorno<CartaoParametroReponse>
            {
                Sucesso = true,
                Mensagem = "Operação realizada com sucesso",
                Objeto = cartao
            };
        }

        #region Métodos privados

        private bool ValidarLimiteDiarioTransferenciaTED(string documentoPortador, decimal Valor)
        {
            var limiteDiario = _limiteApp.GetLimite(documentoPortador, ETipoLimiteTransacaoPortador.DiarioTransferenciaTED);

            if (limiteDiario <= 0)
                return true;

            var totalDiario = _transacaoCartaoApp.GetValorTotalTransferenciasTEDUltimas24horas(documentoPortador);

            return totalDiario + Valor <= limiteDiario;
        }

        private bool ValidarLimiteDiarioTransferenciaCartao(string documentoPortador, decimal Valor)
        {
            var limiteDiario = _limiteApp.GetLimite(documentoPortador, ETipoLimiteTransacaoPortador.DiarioTransferenciaCartoes);

            if (limiteDiario <= 0)
                return true;

            var totalDiario = _transacaoCartaoApp.GetValorTotalTransferenciasCartaoUltimas24horas(documentoPortador);

            return totalDiario + Valor <= limiteDiario;
        }

        private bool ValidarLimiteUnitarioTransferenciaTED(string documentoPortador, decimal Valor)
        {
            var limiteunitario = _limiteApp.GetLimite(documentoPortador, ETipoLimiteTransacaoPortador.UnitarioTransferenciaTED);

            if (limiteunitario <= 0)
                return true;

            return Valor <= limiteunitario;
        }

        private bool ValidarLimiteUnitarioTransferenciaCartao(string documentoPortador, decimal Valor)
        {
            var limiteunitario = _limiteApp.GetLimite(documentoPortador, ETipoLimiteTransacaoPortador.UnitarioTransferenciaCartoes);

            if (limiteunitario <= 0)
                return true;

            return Valor <= limiteunitario;
        }

        #endregion
    }
}
using System;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.Banner;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.DataMediaServer;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;
using AutoMapper.QueryableExtensions;

namespace ATS.Domain.Service
{
    public class BannerService : ServiceBase, IBannerService
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IBannerRepository _bannerRepository;
        private readonly IBannerUsuarioRepository _bannerUsuarioRepository;
        private readonly IDataMediaServerRepository _mongoRepository;

        public BannerService(IBannerRepository BannerRepository, IUserIdentity userIdentity, IDataMediaServerRepository mongoRepository, IBannerUsuarioRepository bannerUsuarioRepository)
        {
            _bannerRepository = BannerRepository;
            _userIdentity = userIdentity;
            _mongoRepository = mongoRepository;
            _bannerUsuarioRepository = bannerUsuarioRepository;
        }

        public BannerConsultarResponse ConsultarAtual()
        {
            var bannerAtual = _bannerRepository
                .GetAtivo()
                .ProjectTo<BannerConsultarResponse>()
                .FirstOrDefault();

            if (bannerAtual == null)
                throw new Exception("Nenhum banner ativo.");
            
            var naoMostrarNovamente = _bannerUsuarioRepository.NaoMostrarNovamente(bannerAtual.Id, _userIdentity.IdUsuario);

            if (naoMostrarNovamente)
                throw new Exception("Nenhum banner ativo.");
            
            var imagemB64 = _mongoRepository.GetMedia(bannerAtual.IdImagemMongo);

            bannerAtual.ImagemB64 = imagemB64?.Data;

            return bannerAtual;
        }

        public BannerConsultarResponse ConsultarPorId(int idBanner)
        {
            var bannerAtual = _bannerRepository
                .Where(c => c.Id == idBanner)
                .ProjectTo<BannerConsultarResponse>()
                .FirstOrDefault();

            if (bannerAtual == null)
                throw new Exception("Banner não encontrado.");
            
            var imagemB64 = _mongoRepository.GetMedia(bannerAtual.IdImagemMongo);

            bannerAtual.ImagemB64 = imagemB64?.Data;
            //bannerAtual.ImagemNome = imagemB64?.FileName;

            return bannerAtual;
        }

        public ValidationResult Visualizar(BannerVisualizarRequest request)
        {
            var bannerAtual = _bannerRepository
                .GetAtivo()
                .FirstOrDefault();

            if (bannerAtual == null)
                throw new Exception("Nenhum banner ativo.");

            var visualizacaoExistente = _bannerUsuarioRepository
                .Where(c => c.IdUsuario == _userIdentity.IdUsuario && c.IdBanner == bannerAtual.Id)
                .FirstOrDefault();

            if (visualizacaoExistente != null && request.NaoMostrarNovamente)
            {
                visualizacaoExistente.NaoMostrarNovamente = true;
                visualizacaoExistente.DataNaoMostrarNovamente = DateTime.Now;
                _bannerUsuarioRepository.Update(visualizacaoExistente);
                return new ValidationResult();
            }

            if (visualizacaoExistente != null) return new ValidationResult();

            var visualizacao = new BannerUsuario()
            {
                IdBanner = bannerAtual.Id,
                IdUsuario = _userIdentity.IdUsuario,
                Visualizado = true,
                DataVisualizacao = DateTime.Now
            };

            if (request.NaoMostrarNovamente)
            {
                visualizacao.NaoMostrarNovamente = true;
                visualizacao.DataNaoMostrarNovamente = DateTime.Now;
            }

            _bannerUsuarioRepository.Add(visualizacao);
            
            return new ValidationResult();
        }
        
        #region Administração
        
        public BannerGridResponse ConsultarBanners()
        {
            var banners = _bannerRepository
                .GetAll()
                .ProjectTo<BannerConsultarResponse>()
                .ToList();

            var retorno = new BannerGridResponse()
            {
                totalItems = banners.Count,
                items = banners
            };
            
            return retorno;
        }

        public ValidationResult Integrar(BannerIntegrarRequest request)
        {
            var validation = new ValidationResult();

            #region Cadastro
            
            if (request.Id == null)
            {
                var bannerNovo = new Banner()
                {
                    Link = request.Link,
                    Descricao = request.Descricao,
                    Titulo = request.Titulo
                };
                
                var bannerAtual = _bannerRepository.GetAtivo().FirstOrDefault();
                
                bannerNovo.Ativo = bannerAtual == null;
                if(bannerNovo.Ativo) bannerNovo.IdUsuarioAtivacao = _userIdentity.IdUsuario;
                
                bannerNovo.IdUsuarioCadastro = _userIdentity.IdUsuario;
                bannerNovo.DataCadastro = DateTime.Now;
                var imagemId = _mongoRepository.Add(1, request.ImagemB64, Guid.NewGuid().ToString()).ToString();
                bannerNovo.IdImagemMongo = imagemId;

                _bannerRepository.Add(bannerNovo);

                return validation;
            }
            
            #endregion
            
            #region Edicao
            
            var banner = _bannerRepository.GetById(request.Id.Value).FirstOrDefault();

            if (banner == null)
                throw new InvalidOperationException("Banner não encontrado.");

            banner.Descricao = request.Descricao;
            banner.Titulo = request.Titulo;
            banner.Link = request.Link;
            
            if (request.ImagemAlterada)
            {
                var imagemId = _mongoRepository.Add(1, request.ImagemB64, Guid.NewGuid().ToString()).ToString();
                banner.IdImagemMongo = imagemId;
            }

            _bannerRepository.SaveChanges();

            return validation;
            
            #endregion
        }

        public BannerAlterarStatusResponse AlterarStatus(BannerAlterarStatusRequest request)
        {
            var banner = _bannerRepository.GetById(request.Id).FirstOrDefault();

            if (banner == null)
                throw new InvalidOperationException("Banner não encontrado.");

            var bannerAtual = _bannerRepository.GetAtivo().FirstOrDefault();

            if (bannerAtual != null && bannerAtual.Id != banner.Id && !banner.Ativo)
                throw new InvalidOperationException($"Já existe um banner ativo (Id {bannerAtual.Id}). Desative-o primeiro.");

            if (!banner.Ativo)
            {
                banner.IdUsuarioAtivacao = _userIdentity.IdUsuario;
                banner.DataAtivacao = DateTime.Now;
                banner.Ativo = true;
            }
            else
            {
                banner.IdUsuarioDesativacao = _userIdentity.IdUsuario;
                banner.DataDesativacao = DateTime.Now;
                banner.Ativo = false;
            }

            _bannerRepository.SaveChanges();

            return new BannerAlterarStatusResponse(true, $"Banner {(banner.Ativo ? "ativado" : "desativado")} com sucesso.");
        }
        
        #endregion
    }
}
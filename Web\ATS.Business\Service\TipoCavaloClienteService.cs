﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class TipoCavaloClienteService : ServiceBase, ITipoCavaloClienteService
    {
        ITipoCavaloClienteRepository _tipoCavaloClienteRepository;
        public TipoCavaloClienteService(ITipoCavaloClienteRepository tipoCavaloClienteRepository)
        {
            _tipoCavaloClienteRepository = tipoCavaloClienteRepository;
        }
        public List<TipoCavaloCliente> GetByTipoCavalo(int idTipoCavalo)
        {
            var resultado = _tipoCavaloClienteRepository.Find(o => o.IdTipoCavalo == idTipoCavalo)
                .Include(o => o.Cliente);

            return resultado.ToList();
        }

        public ValidationResult Remover(int idTipoCavalo, int idCliente)
        {
            var tipoCavaloCliente = _tipoCavaloClienteRepository.FirstOrDefault(x => x.IdCliente == idCliente && x.IdTipoCavalo == idTipoCavalo);
            if (tipoCavaloCliente != null)
                _tipoCavaloClienteRepository.Delete(tipoCavaloCliente);
            else
                return new ValidationResult().Add("Registro não encontrado.");
            return new ValidationResult();
        }

        public ValidationResult Add(TipoCavaloCliente tipoCavaloCliente)
        {
            var tipoCavaloClienteEntity = _tipoCavaloClienteRepository.FirstOrDefault(x => x.IdTipoCavalo == tipoCavaloCliente.IdTipoCavalo && x.IdCliente == tipoCavaloCliente.IdCliente);
            if (tipoCavaloClienteEntity != null)
                _tipoCavaloClienteRepository.Delete(tipoCavaloClienteEntity);
            _tipoCavaloClienteRepository.Add(tipoCavaloCliente);

            return new ValidationResult();
        }
    }
}

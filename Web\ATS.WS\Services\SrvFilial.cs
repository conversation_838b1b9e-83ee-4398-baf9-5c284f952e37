﻿using ATS.Application.Application;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.Helpers;
using Filial = ATS.Domain.Entities.Filial;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services
{
    public class SrvFilial : SrvBase
    {
        private readonly SrvUtils _srvUtils;
        private readonly IEmpresaApp _empresaApp;
        private readonly IFilialApp _filialApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IBloqueioGestorValorApp _bloqueioGestorValorApp;
        private readonly LocationHelper _locationHelper;
        private readonly IParametrosApp _parametrosApp;
        private readonly IUserIdentity _userIdentity;

        public SrvFilial(SrvUtils srvUtils, IEmpresaApp empresaApp, IFilialApp filialApp, ICidadeApp cidadeApp, IBloqueioGestorValorApp bloqueioGestorValorApp, LocationHelper locationHelper, IParametrosApp parametrosApp, IUserIdentity userIdentity)
        {
            _srvUtils = srvUtils;
            _empresaApp = empresaApp;
            _filialApp = filialApp;
            _cidadeApp = cidadeApp;
            _bloqueioGestorValorApp = bloqueioGestorValorApp;
            _locationHelper = locationHelper;
            _parametrosApp = parametrosApp;
            _userIdentity = userIdentity;
        }

        public Retorno<FilialModel> Integrar(FilialIntegrarRequestModel @params)
        {
            try
            {
                var filial = Mapper.Map<FilialIntegrarRequestModel, Filial>(@params);

                var idRetPais = _srvUtils.GetIdPaisPorBACEN(@params.CodigoBacenPais);
                if (idRetPais.HasValue && idRetPais > 0)
                    filial.IdPais = idRetPais.GetValueOrDefault();

                var idRetEstado = _srvUtils.GetIdEstadoPorIBGE(@params.CodigoIbgeEstado);
                if (idRetEstado.HasValue && idRetEstado > 0)
                    filial.IdEstado = idRetEstado.GetValueOrDefault();

                var idRetCidade = _srvUtils.GetIdCidadePorIBGE(@params.CodigoIbgeCidade);
                if (idRetCidade.HasValue && idRetCidade > 0)
                    filial.IdCidade = idRetCidade.GetValueOrDefault();

                var idEmpresa = _srvUtils.GetIdEmpresaPorCnpj(@params.CNPJEmpresa);
                if (idEmpresa.HasValue && idEmpresa.Value > 0)
                    filial.IdEmpresa = idEmpresa.Value;

                filial.FilialContatos = new List<FilialContatos>();

                if (!string.IsNullOrEmpty(@params.Telefone))
                {
                    filial.FilialContatos.Add(new FilialContatos()
                    {
                        Nome = @params.NomeFantasia,
                        Telefone = @params.Telefone
                    });
                }

                var idFilial = _filialApp.GetIdPorCnpj(@params.Cnpj);
                if (idFilial.HasValue)
                {
                    filial.PontoApoio = true;
                    filial.IdFilialMae = idFilial.Value;
                }

                if ((@params.Longitude == null || @params.Longitude == 0) && (@params.Latitude == null || @params.Latitude == 0))
                {
                    decimal longitude;
                    decimal latitude;

                    _locationHelper.SetCoordinatesObject(@params.Numero ?? 0, @params.Endereco, @params.Bairro, @params.CodigoIbgeCidade, @params.CodigoIbgeEstado,
                        @params.CodigoBacenPais, out latitude, out longitude);

                    filial.Latitude = latitude;
                    filial.Longitude = longitude;
                }

                var validationResult = _filialApp.Add(filial);

                if (!validationResult.IsValid)
                    throw new Exception(validationResult.ToFormatedMessage());

                return new Retorno<FilialModel>(true, Mapper.Map<Filial, FilialModel>(filial));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<FilialModel>(false, e.Message, null);
            }
        }

        public Retorno<List<FilialModel>> ConsultarAtualizadas(string cnpjEmpresa, DateTime dataBase)
        {
            try
            {
                int? idEmpresa = null;

                if (!string.IsNullOrWhiteSpace(cnpjEmpresa))
                    idEmpresa = _empresaApp.GetIdPorCnpj(cnpjEmpresa);

                if (idEmpresa == null)
                    throw new Exception("Empresa não cadastrada no sistema! ");

                var filiais = _filialApp.GetFiliaisAtualizadas(idEmpresa, dataBase).ToList();
                var retFiliais = new List<FilialModel>();

                foreach (var filial in filiais)
                {
                    var filialModel = Mapper.Map<Filial, FilialModel>(filial);

                    if (filialModel != null)
                    {
                        if (!filialModel.Latitude.HasValue || !filialModel.Longitude.HasValue)
                        {
                            var localizacao = _cidadeApp
                                .GetLocalizacao(filial.Cidade.IdCidade, EOrigemConsumoServicoExterno.ConsultarFiliaisAtualizadas);

                            if (localizacao != null)
                            {
                                filialModel.Latitude = localizacao.Latitude;
                                filialModel.Longitude = localizacao.Longitude;
                            }
                        }

                        filialModel.CNPJEmpresa = filial.Empresa.CNPJ.OnlyNumbers();
                        filialModel.RazaoSocialEmpresa = filial.Empresa.RazaoSocial;

                        retFiliais.Add(filialModel);
                    }
                }

                return new Retorno<List<FilialModel>>(true, retFiliais);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<FilialModel>>($"{nameof(ConsultarAtualizadas)} >> {e.Message}");
            }
        }

        public byte[] GerarRelatorioGridFiliais(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string extensao)
        {
            return _filialApp.GerarRelatorioGridFiliais(idEmpresa, order, filters, extensao, GetLogo(idEmpresa));
        }

        public ValidationResult Cadastrar(FilialIntegrarRequestModel model)
        {
            var filial = Mapper.Map<FilialIntegrarRequestModel, Filial>(model);

            filial.FilialContatos = new List<FilialContatos>();
            filial.DataHoraUltimaAtualizacao = DateTime.Now;

            if (model.Contatos != null)
                foreach (var filialRequest in model.Contatos)
                    filial.FilialContatos.Add(new FilialContatos
                    {
                        Email = filialRequest.Email,
                        IdFilial = filial.IdFilial,
                        Nome = filialRequest.Nome,
                        IdFilialContato = filialRequest.IdFilialContato,
                        Telefone = filialRequest.Telefone,
                        Filial = filial
                    });

            var result = _filialApp.Add(filial);

            if (result.IsValid)
            {
                var permiteAlteracaoAlcadasLimites = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(_userIdentity.IdUsuario);
            
                if (permiteAlteracaoAlcadasLimites.PermiteFilial)
                {
                    model.AlcadasBloqueioGestorValor.Portal.ForEach(x => x.IdEmpresa = filial.IdEmpresa);
                    model.AlcadasBloqueioGestorValor.Api.ForEach(x => x.IdEmpresa = filial.IdEmpresa);
                    model.AlcadasBloqueioGestorValor.Portal.ForEach(x => x.IdFilial = filial.IdFilial);
                    model.AlcadasBloqueioGestorValor.Api.ForEach(x => x.IdFilial = filial.IdFilial);
                
                    _bloqueioGestorValorApp.IntegrarValores(model.AlcadasBloqueioGestorValor);
                }
            }
            
            return result;
        }

        public ValidationResult Editar(FilialIntegrarRequestModel model)
        {
            var permiteAlteracaoAlcadasLimites = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(_userIdentity.IdUsuario);
            
            if (permiteAlteracaoAlcadasLimites.PermiteFilial)
            {
                _bloqueioGestorValorApp.IntegrarValores(model.AlcadasBloqueioGestorValor);
            }

            var filial = Mapper.Map<FilialIntegrarRequestModel, Filial>(model);

            filial.FilialContatos = new List<FilialContatos>();
            filial.DataHoraUltimaAtualizacao = DateTime.Now;

            if (model.Contatos != null)
                foreach (var filialRequest in model.Contatos)
                    filial.FilialContatos.Add(new FilialContatos()
                    {
                        Email = filialRequest.Email,
                        IdFilial = filial.IdFilial,
                        Nome = filialRequest.Nome,
                        IdFilialContato = filialRequest.IdFilialContato,
                        Telefone = filialRequest.Telefone,
                        Filial = filial
                    });

            var result = _filialApp.Update(filial);
            return result;
        }

        public BloqueioGestorValorDto GetBloqueioGestorValor(int? idEmpresa, int? idFilial)
        {
            return _bloqueioGestorValorApp.GetBloqueioGestorValor(idEmpresa ?? 0, idFilial ?? 0);
        }
    }
}
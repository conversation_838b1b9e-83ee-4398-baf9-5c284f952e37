﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class UsuarioDocumentoApp : AppBase, IUsuarioDocumentoApp
    {
        private readonly IUsuarioDocumentoService _usuarioDocumentoService;

        public UsuarioDocumentoApp(IUsuarioDocumentoService usuarioDocumentoService)
        {
            _usuarioDocumentoService = usuarioDocumentoService;
        }

        public IQueryable<UsuarioDocumento> GetDocumentos(int idUsuario)
        {
            return _usuarioDocumentoService.GetDocumentos(idUsuario);
        }

        public UsuarioDocumento GetDocCNH(int idUsuario)
        {
            return _usuarioDocumentoService.GetDocCNH(idUsuario);
        }
    }
}
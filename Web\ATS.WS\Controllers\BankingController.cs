using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using System;
using System.Web.Http;
using ATS.Application.Interface;
using ATS.WS.Models.Webservice.Request.BizWebhook;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class BankingController : BaseController
    {
        private readonly IBizWebhookApp _bizWebhookApp;

        public BankingController(BaseControllerArgs baseArgs, IBizWebhookApp bizWebhookApp) : base(baseArgs)
        {
            _bizWebhookApp = bizWebhookApp;
        }

        //criar outro metodo (cada webhook deve ser a parte na biz, um método cadastrado pra cada tipo) pro BizWebhookCreatedAccountRequest quando necessario

        [System.Web.Mvc.HttpPost]
        [System.Web.Mvc.AllowAnonymous]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        public JsonResult Webhook([FromBody] BizWebhookTransactionOcurredRequest request)
        {
            try
            {
                if (request?.AccountId == null)
                    throw new InvalidOperationException("Requisição inválida.");
                _bizWebhookApp.PushTransactionOccurred(request);
                return Responde("Notificação enviada com sucesso.");
            }
            catch (Exception e)
            {
                return Responde(e.Message);
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoAssociacaoMap : EntityTypeConfiguration<EstabelecimentoAssociacao>
    {
        public EstabelecimentoAssociacaoMap()
        {
            ToTable("ESTABELECIMENTO_ASSOCIACAO");

            HasKey(x => new { x.IdEstabelecimento, x.IdAssociacao });

            HasRequired(x => x.Estabelecimento)
                .WithMany(x => x.EstabelecimentoAssociacoesEstabelecimento)
                .HasForeignKey(x => x.IdEstabelecimento);

            HasRequired(x => x.Associacao)
                .WithMany(x => x.EstabelecimentoAssociacoesAssociacao)
                .HasForeignKey(x => x.IdAssociacao);
        }
    }
}

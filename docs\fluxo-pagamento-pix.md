# **Fluxo de Pagamento PIX**

<br><h4>Documentação completa para implementação de pagamentos via PIX na API Extratta. Este guia explica como configurar e processar pagamentos PIX de forma automática para proprietários de veículos.</h4>

<br>

## **📋 Visão Geral**

O pagamento via PIX na Extratta permite que empresas realizem transferências instantâneas para proprietários de veículos de forma automática durante a criação de viagens. O sistema detecta automaticamente quando usar PIX e processa o pagamento em tempo real.



---

## **🔧 Pré-requisitos**

### **✅ Requisitos Obrigatórios**
- Proprietário deve ter **chave PIX cadastrada** no sistema
- Empresa deve ter **saldo suficiente** para o pagamento
- Configurar evento com **HabilitarPagamentoCartao = false**

### **📋 Informações Necessárias**
- CPF/CNPJ do proprietário
- Chave PIX válida (CPF, CNPJ, e-mail, telefone ou chave aleatória)
- Valor do pagamento
- Dados da viagem completos

---


## **1️⃣ Cadastro de Chave PIX**

Para que um proprietário possa receber pagamentos via PIX, é necessário cadastrar uma chave PIX válida no sistema.

### **📡 Endpoint:** `POST /Pix/CadastrarChaveProprietario`

### **🔑 Tipos de Chave Suportados**

| Tipo | Código | Descrição | Formato | Exemplo |
|------|--------|-----------|---------|---------|
| **CPF** | 0 | Documento CPF | 11 dígitos | "12345678901" |
| **CNPJ** | 1 | Documento CNPJ | 14 dígitos | "12345678000195" |
| **E-mail** | 3 | Endereço de e-mail | <EMAIL> | "<EMAIL>" |
| **Telefone** | 2 | Celular com DDD | 11 dígitos | "11987654321" |
| **Chave Aleatória** | 4 | UUID gerado automaticamente | 36 caracteres | Sistema gera automaticamente |

### **📝 Exemplo 1: Cadastro com CPF**

**Request:**
```json
{
  "CNPJAplicacao": "12345678000195",
  "Token": "seu-token-de-autenticacao",
  "CPFCNPJProprietario": "12345678901",
  "Chave": "12345678901",
  "Tipo": 0,
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Sistema Integração"
}
```

### **📝 Exemplo 2: Cadastro com E-mail**

**Request:**
```json
{
  "CNPJAplicacao": "12345678000195",
  "Token": "seu-token-de-autenticacao",
  "CPFCNPJProprietario": "12345678901",
  "Chave": "<EMAIL>",
  "Tipo": 3,
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Sistema Integração"
}
```

### **📝 Exemplo 3: Cadastro com Telefone**

**Request:**
```json
{
  "CNPJAplicacao": "12345678000195",
  "Token": "seu-token-de-autenticacao",
  "CPFCNPJProprietario": "12345678901",
  "Chave": "11987654321",
  "Tipo": 2,
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Sistema Integração"
}
```

### **📝 Exemplo 4: Cadastro com Chave Aleatória**

**Request:**
```json
{
  "CNPJAplicacao": "12345678000195",
  "Token": "seu-token-de-autenticacao",
  "CPFCNPJProprietario": "12345678901",
  "Chave": "",
  "Tipo": 4,
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Sistema Integração"
}
```

### **✅ Resposta de Sucesso**

```json
{
  "Sucesso": true,
  "Mensagem": "Chave PIX cadastrada com sucesso",
  "Objeto": {
    "ChavePix": "12345678901",
    "Tipo": 0,
    "Status": "Ativa"
  }
}
```

### **🔍 Validações do Sistema**

Quando você cadastra uma chave PIX, o sistema executa validações em diferentes níveis para garantir a segurança:

#### **📋 Validação de Formato (Automática)**
O sistema valida automaticamente o formato da chave antes de criar a solicitação:

- **CPF:** Deve ter exatamente 11 dígitos numéricos (apenas números, sem pontos ou hífens)
- **CNPJ:** Deve ter exatamente 14 dígitos numéricos (apenas números, sem pontos, barras ou hífens)
- **E-mail:** Deve conter "@" e "." (validação básica de formato)
- **Telefone:** Deve ter exatamente 11 dígitos numéricos (DDD + número, apenas números)
- **Chave Aleatória (EVP):** Deve ter exatamente 36 caracteres incluindo os hífens (formato UUID)

#### **👨‍💼 Validação N1 - Gestor Nível 1**
Após a validação de formato, a solicitação fica **"Pendente Nível 1"** e deve ser aprovada por um **Gestor Nível 1** no portal:

- ✅ **Análise Manual:** Gestor verifica se os dados estão corretos
- ✅ **Validação DICT:** Sistema consulta o Banco Central para verificar se a chave pertence ao CPF/CNPJ
- ✅ **Aprovação/Rejeição:** Gestor pode aprovar ou rejeitar a solicitação

#### **👨‍💼 Validação N2 - Gestor Nível 2 (Opcional)**
Se a empresa tiver configurado **Gestor Nível 2**, a solicitação aprovada pelo N1 fica **"Pendente Nível 2"**:

- ✅ **Segunda Análise:** Gestor Nível 2 faz uma segunda verificação
- ✅ **Validação DICT:** Sistema consulta novamente o Banco Central
- ✅ **Aprovação Final:** Apenas após aprovação do N2 a chave é efetivamente cadastrada

#### **🏦 Validação DICT (Banco Central)**
A validação DICT é executada **sempre que um gestor aprova** uma solicitação:

- ✅ **Propriedade da Chave:** Verifica se a chave PIX realmente pertence ao CPF/CNPJ informado
- ✅ **Status da Chave:** Confirma se a chave está ativa no sistema bancário
- ✅ **Dados Bancários:** Valida se a conta de destino está funcionando
- ✅ **Titularidade:** Confirma se o proprietário é o titular da chave PIX


#### **⚠️ Exemplo de Erro - Chave não pertence ao CPF**

```json
{
  "Sucesso": false,
  "Mensagem": "Chave PIX não pertence ao CPF informado",
  "Faults": [
    {
      "Type": 1,
      "Code": "PIX007",
      "Message": "Validação DICT: Chave PIX 12345678901 não está vinculada ao CPF 98765432100"
    }
  ]
}
```

### **❌ Outros Possíveis Erros**

#### **Chave já cadastrada**
```json
{
  "Sucesso": false,
  "Mensagem": "Chave PIX já cadastrada para outro proprietário",
  "Faults": [
    {
      "Type": 1,
      "Code": "PIX001",
      "Message": "Chave PIX já existe no sistema"
    }
  ]
}
```

#### **Formato inválido**
```json
{
  "Sucesso": false,
  "Mensagem": "Chave de tipo CPF com formato inválido. Deve ter 11 caracteres numéricos.",
  "Faults": [
    {
      "Type": 1,
      "Code": "PIX006",
      "Message": "Chave de tipo CPF com formato inválido. Deve ter 11 caracteres numéricos."
    }
  ]
}
```

#### **Chave inativa no Banco Central**
```json
{
  "Sucesso": false,
  "Mensagem": "Chave PIX inativa ou inexistente",
  "Faults": [
    {
      "Type": 1,
      "Code": "PIX008",
      "Message": "Chave PIX não encontrada no DICT do Banco Central"
    }
  ]
}
```

### **🔐 Fluxo Completo de Cadastro**

#### **📋 Fluxo de Aprovação por Gestores**

1. **📤 Solicitação:** Cliente envia chave PIX via API
2. **🔍 Validação de Formato:** Sistema valida formato automaticamente
3. **📋 Criação da Solicitação:** Status fica "Pendente Nível 1"
4. **📧 Notificação:** Gestores Nível 1 recebem e-mail
5. **👨‍💼 Aprovação N1:** Gestor Nível 1 aprova no portal
6. **🏦 Validação DICT:** Sistema consulta Banco Central
7. **📋 Status Atualizado:**
   - Se empresa tem N2: Status fica "Pendente Nível 2"
   - Se empresa não tem N2: Status fica "Aprovada"
8. **👨‍💼 Aprovação N2 (se aplicável):** Gestor Nível 2 aprova no portal
9. **🏦 Validação DICT Final:** Sistema consulta Banco Central novamente
10. **✅ Chave Cadastrada:** Proprietário habilitado para receber PIX

---
#### **🏦 O que é o DICT?**
O **DICT (Diretório de Identificadores de Contas Transacionais)** é o sistema do Banco Central que armazena todas as chaves PIX do Brasil. É como um "catálogo telefônico" das chaves PIX.

#### **⏱️ Tempo de Processamento**
- **Validação de formato:** < 1 segundo
- **Consulta DICT:** 2-5 segundos
- **Aprovação pelos gestores:** Depende da disponibilidade dos gestores
- **Timeout DICT:** 30 segundos (em caso de instabilidade)

#### **🛡️ Segurança**
O sistema garante que:
- Apenas o verdadeiro dono da chave PIX pode cadastrá-la
- Não há risco de pagamentos para pessoas erradas
- Todos os dados são validados pelo Banco Central
- Sistema está sempre atualizado com informações oficiais

#### **📊 Status das Solicitações**

| Status | Código | Descrição | Próxima Ação |
|--------|--------|-----------|---------------|
| **Pendente Nível 1** | 1 | Aguardando aprovação do Gestor N1 | Gestor N1 deve aprovar/rejeitar |
| **Pendente Nível 2** | 2 | Aguardando aprovação do Gestor N2 | Gestor N2 deve aprovar/rejeitar |
| **Aprovada** | 3 | Chave cadastrada com sucesso | Proprietário pode receber PIX |
| **Recusada** | 4 | Solicitação rejeitada | Cadastrar nova solicitação |

#### **👥 Configuração de Gestores**

Para que o sistema funcione, a empresa deve ter gestores configurados:

- **Gestor Nível 1:** Obrigatório para todas as empresas
- **Gestor Nível 2:** Opcional, para empresas que querem dupla validação

> **💡 Configuração:** Os gestores são configurados através de parâmetros da empresa no sistema.

---

## **2️⃣ Configuração de Viagem com PIX**

Para que o pagamento seja processado via PIX automaticamente, configure o evento da viagem seguindo as regras abaixo.

### **🎯 Regras para PIX Automático**

✅ **Configurações Obrigatórias:**
- `HabilitarPagamentoCartao = false`
- Proprietário deve ter chave PIX cadastrada
- Valor deve ser maior que zero
- Status deve ser `Aberto` (0) ou `Baixado` (2)

❌ **Configurações que IMPEDEM PIX:**
- `HabilitarPagamentoCartao = true`
- Proprietário sem chave PIX cadastrada
- Status diferente de `Aberto` ou `Baixado`

### **🤖 Detecção Automática**

O sistema detecta automaticamente quando usar PIX baseado nas seguintes condições:
- **NÃO** é pagamento por cartão (`HabilitarPagamentoCartao = false`)
- **E** proprietário tem chave PIX cadastrada

> **💡 Dica:** Não é obrigatório informar `HabilitarPagamentoPix = true`. O sistema detecta automaticamente!

### **📝 Exemplo: Integração V1 com PIX**

**Endpoint:** `POST /Viagem/Integrar`

**Request:**
```json
{
  "Token": "seu-token-de-autenticacao",
  "CNPJAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "CPFCNPJProprietario": "12345678901",
  "CPFMotorista": "12345678901",
  "Placa": "ABC1234",
  "NumeroControle": "VIAGEM001",
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Sistema Integração",
  "ViagemEventos": [
    {
      "TipoEvento": 0,
      "ValorPagamento": 800.00,
      "Status": 2,
      "HabilitarPagamentoCartao": false,
      "NumeroControle": "ADT001",
      "Instrucao": "Adiantamento via PIX"
    },
    {
      "TipoEvento": 1,
      "ValorPagamento": 1500.00,
      "Status": 2,
      "HabilitarPagamentoCartao": false,
      "NumeroControle": "SLD001",
      "Instrucao": "Saldo via PIX"
    }
  ]
}
```

### **📝 Exemplo: Integração V2 com PIX**

**Endpoint:** `POST /ViagemV2/Integrar`

**Request:**
```json
{
  "Token": "seu-token-de-autenticacao",
  "CnpjAplicacao": "12345678000195",
  "CNPJEmpresa": "98765432000198",
  "DocumentoUsuarioAudit": "12345678901",
  "NomeUsuarioAudit": "Sistema Integração",
  "DadosViagem": {
    "DadosIniciais": {
      "NumeroControle": "VIAGEM001"
    },
    "Documentos": {
      "MotoristaDocumento": "12345678901",
      "ProprietarioDocumento": "12345678901"
    },
    "Veiculo": {
      "Placa": "ABC1234"
    },
    "ViagemEventos": [
      {
        "TipoEvento": 1,
        "ValorPagamento": 1500.00,
        "Status": 2,
        "HabilitarPagamentoCartao": false,
        "NumeroControle": "SLD001",
        "Instrucao": "Pagamento de frete via PIX"
      }
    ]
  }
}
```

---

## **3️⃣ Processamento Automático do PIX**

Quando você envia uma viagem com as configurações corretas, o sistema processa o PIX automaticamente em tempo real.

### **🔄 Fluxo de Processamento**

1. **📋 Validação Inicial**
   - Verifica se proprietário tem chave PIX cadastrada
   - Valida se há saldo suficiente na conta

2. **💰 Verificação de Limites**
   - Limite por transação (padrão: R$ 5.000,00)
   - Limite diário acumulado (padrão: R$ 20.000,00)
   - Se exceder → Evento vai para aprovação manual

3. **🚀 Execução do PIX**
   - Sistema cria transação PIX automaticamente
   - Envia comando para o Banco Central
   - PIX é processado em tempo real (máximo 10 segundos)

4. **✅ Confirmação**
   - Evento fica com status "Baixado"
   - Data/hora do pagamento é registrada
   - Comprovante é gerado automaticamente



## **4️⃣ Respostas da API**

### **✅ Resposta de Sucesso**

Quando o PIX é processado com sucesso, você recebe:

```json
{
  "Sucesso": true,
  "Mensagem": "Viagem integrada com sucesso",
  "Objeto": {
    "IdViagem": 12345,
    "NumeroDocumento": "DOC123456",
    "Eventos": [
      {
        "IdViagemEvento": 67890,
        "NumeroControle": "SLD001",
        "Token": "evento-token-abc123",
        "TipoEventoViagem": 1,
        "Status": 2,
        "DataHoraPagamento": "2024-01-15T10:30:00",
        "OperacaoCartao": {
          "Status": 1,
          "Mensagem": "PIX processado com sucesso",
          "TipoOperacao": "PIX",
          "ValorProcessado": 1500.00,
          "DataProcessamento": "2024-01-15T10:30:00",
          "EndToEndId": "E12345678202401151030123456789"
        }
      }
    ]
  }
}
```

### **❌ Possíveis Erros**

#### **Erro 1: Proprietário sem chave PIX**
```json
{
  "Sucesso": false,
  "Mensagem": "Proprietário não possui chave PIX cadastrada",
  "Faults": [
    {
      "Type": 1,
      "Code": "PIX002",
      "Message": "Proprietário sem permissão para receber pagamentos Pix pela empresa"
    }
  ]
}
```



#### **Erro 2: Limite excedido**
```json
{
  "Sucesso": false,
  "Mensagem": "Valor excede o limite unitário de R$ 5.000,00. Parcela foi bloqueada e enviada para aprovação do gestor.",
  "Objeto": {
    "IdViagem": 12345,
    "Eventos": [
      {
        "IdViagemEvento": 67890,
        "Status": 1,
        "MotivoBloqueio": "Valor excede o limite unitário de R$ 5.000,00"
      }
    ]
  }
}
```

### **📋 Tabela de Erros Comuns**

| Erro | Causa | Solução |
|------|-------|---------|

| "Proprietário sem permissão para receber pagamentos Pix" | Chave PIX não cadastrada | Cadastrar chave PIX do proprietário |
| "Status do evento está diferente de aberto ou baixado" | Status inválido | Usar status 0 (Aberto) ou 2 (Baixado) |
| "Valor excede o limite unitário" | Valor acima do limite | Reduzir valor ou solicitar aprovação |
| "Valor excede o limite diário" | Limite diário atingido | Aguardar próximo dia ou solicitar aprovação |
| "Transferência Pix já efetuada" | PIX duplicado | Verificar se já foi processado |

---

## **5️⃣ Códigos de Erro PIX**

### **🔍 Referência Completa de Códigos**

| Código | Descrição | Causa | Solução |
|--------|-----------|-------|---------|
| **PIX001** | Chave PIX já existe | Chave já cadastrada para outro proprietário | Usar chave diferente ou verificar proprietário |
| **PIX002** | Proprietário sem chave PIX | Chave não cadastrada no sistema | Cadastrar chave PIX do proprietário |

| **PIX004** | Limite excedido | Valor acima do limite configurado | Reduzir valor ou solicitar aprovação |
| **PIX005** | Saldo insuficiente | Empresa sem saldo para pagamento | Verificar saldo da empresa |
| **PIX006** | Formato de chave inválido | Chave não atende formato exigido | Corrigir formato da chave |
| **PIX007** | Chave não pertence ao CPF/CNPJ | Validação DICT falhou | Verificar titularidade no banco |
| **PIX008** | Chave inativa no Banco Central | Chave não encontrada no DICT | Ativar chave no banco ou usar outra |
| **PIX009** | Erro na consulta DICT | Falha na comunicação com BC | Tentar novamente em alguns minutos |
| **PIX010** | Timeout na validação | Demora na resposta do BC | Tentar novamente |

### **🚨 Códigos Críticos**

Os códigos abaixo indicam problemas que impedem o processamento:


- **PIX007:** Chave PIX não pertence ao proprietário informado
- **PIX008:** Chave PIX inativa ou inexistente no sistema bancário

### **⚠️ Códigos de Atenção**

Os códigos abaixo podem ser resolvidos com ajustes:

- **PIX001:** Usar chave diferente
- **PIX004:** Reduzir valor ou solicitar aprovação
- **PIX006:** Corrigir formato da chave

---

## **6️⃣ Resumo Executivo**

### **🎯 O que é o PIX Extratta**

O PIX Extratta é uma solução de pagamento instantâneo que permite às empresas realizarem transferências automáticas para proprietários de veículos durante a criação de viagens. O sistema detecta automaticamente quando usar PIX e processa o pagamento em tempo real.

### **🔐 Processo de Segurança**

1. **Cadastro de Chave:** Solicitação via API
2. **Validação N1:** Aprovação por Gestor Nível 1 + validação DICT
3. **Validação N2:** Aprovação por Gestor Nível 2 + validação DICT (se aplicável)
4. **Pagamento:** PIX automático durante criação de viagens

### **📞 Suporte**

- **Suporte Técnico:** <EMAIL>
- **Documentação:** docs.extratta.com.br

---

**📋 Versão:** 1.0 | **📅 Atualização:** Janeiro 2024 | **👨‍💻 Autor:** Equipe Técnica Extratta

**⚡ PIX Extratta - Pagamentos Instantâneos com Segurança Máxima!**
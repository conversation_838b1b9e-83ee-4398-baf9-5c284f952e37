﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class BlacklistIpMap: EntityTypeConfiguration<BlacklistIp>
    {
        public BlacklistIpMap()
        {
            ToTable("BLACK_LIST_IP");
            HasKey(x => x.Id);

            Property(t => t.Id).HasColumnName("id").HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.DataCadastro).HasColumnName("datacadastro").IsRequired().HasColumnType("datetime");
            Property(t => t.Ipv4).IsRequired().HasMaxLength(20);
        }
    }
}
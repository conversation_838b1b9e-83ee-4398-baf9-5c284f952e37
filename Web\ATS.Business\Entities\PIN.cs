using ATS.Domain.Enum;
using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class PIN
    {
        public int IdPIN { get; set; }
        
        public string Codigo { get; set; }

        public string Celular { get; set; }
        
        public DateTime DataValidade { get; set; }
    
        public DateTime DataCriacao { get; set; }

        public bool Enviado { get; set; } = false;

        public ESMSDeliveredStatus StatusEnvio { get; set; }

        public DateTime? DataEnvio { get; set; }

        public bool Validado { get; set; } = false;
        
        public DateTime? DataValidacao { get; set; }

        public bool Inativo { get; set; } = false;

        public string IdSMS { get; set; }

        public bool EnviadoOperadora { get; set; } = false;

        public ESMSSendStatus StatusOperadora { get; set; }

        public DateTime? DataEnvioOperadora { get; set; }
        
    }
}
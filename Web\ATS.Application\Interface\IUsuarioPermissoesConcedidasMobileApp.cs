using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IUsuarioPermissoesConcedidasMobileApp : IAppBase<UsuarioPermissoesConcedidasMobile>
    {
        ValidationResult SalvarOuEditar(UsuarioPermissoesConcedidasMobile permissoes);
        UsuarioPermissoesConcedidasMobileModel ConsultarPorUsuario(int usuarioId);
    }
}
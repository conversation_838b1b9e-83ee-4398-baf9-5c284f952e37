﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioClienteMap : EntityTypeConfiguration<UsuarioCliente>
    {
        public UsuarioClienteMap()
        {
            ToTable("USUARIO_CLIENTE");

            HasKey(t => new { t.IdCliente, t.IdUsuario });

            Property(t => t.IdCliente)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(x => x.IsChecked)
                .IsRequired();
        }
    }
}

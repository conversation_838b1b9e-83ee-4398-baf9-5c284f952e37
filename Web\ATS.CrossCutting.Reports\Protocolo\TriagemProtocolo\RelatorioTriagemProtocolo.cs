﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Protocolo.TriagemProtocolo
{
    public class RelatorioTriagemProtocolo
    {
        public byte[] GetReport(List<RelatorioTriagemProtocoloDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametrizes = new Tuple<string, string, bool>[1];
            parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var bytes = new Base.Reports().GetReport(listaDados, parametrizes, true, "DtsTriagemProtocolo",
                "ATS.CrossCutting.Reports.Protocolo.TriagemProtocolo.RelatorioTriagemProtocolo.rdlc",
                tipoArquivo);

            return bytes;
        }
    }
}

﻿using ATS.CrossCutting.IoC.Utils;

namespace ATS.Domain.Extensions
{
    public static class StringExtensions
    {
        public static bool IsValidCPF(this string aValue)
        {
            if (aValue == null)
                return false;

            int[] lMt1 = new int[9] { 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            int[] lMt2 = new int[10] { 11, 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            string lTempCPF;
            string lDigito;
            int lSoma;
            int lResto;

            aValue = aValue.OnlyNumbers2().Trim();

            if (aValue.Length != 11)
                return false;

            lTempCPF = aValue.Substring(0, 9);
            lSoma = 0;
            for (int i = 0; i < 9; i++)
                lSoma += int.Parse(lTempCPF[i].ToString()) * lMt1[i];

            lResto = lSoma % 11;
            if (lResto < 2)
                lResto = 0;
            else
                lResto = 11 - lResto;

            lDigito = lResto.ToString();
            lTempCPF = lTempCPF + lDigito;
            lSoma = 0;

            for (int i = 0; i < 10; i++)
                lSoma += int.Parse(lTempCPF[i].ToString()) * lMt2[i];

            lResto = lSoma % 11;
            if (lResto < 2)
                lResto = 0;
            else
                lResto = 11 - lResto;

            lDigito = lDigito + lResto;
            if (!aValue.EndsWith(lDigito))
                return false;

            if (aValue[0] == aValue[1] && aValue[0] == aValue[2] && aValue[0] == aValue[3] && aValue[0] == aValue[4] && aValue[0] == aValue[5] &&
                aValue[0] == aValue[6] && aValue[0] == aValue[7] && aValue[0] == aValue[8] && aValue[0] == aValue[9] && aValue[0] == aValue[10])
                return false;

            return true;
        }

        public static bool IsValidCNPJ(this string aValue)
        {
            if (aValue == null)
                return false;

            aValue = aValue.OnlyNumbers2();

            if (aValue.Length < 14)
                return false;

            int[] lDigitos, lSoma, lResultado;
            int lNrDig;
            string lFtmt;
            bool[] lCNPJOk;

            lFtmt = "6543298765432";
            lDigitos = new int[14];
            lSoma = new int[2];
            lSoma[0] = 0;
            lSoma[1] = 0;
            lResultado = new int[2];
            lResultado[0] = 0;
            lResultado[1] = 0;
            lCNPJOk = new bool[2];
            lCNPJOk[0] = false;
            lCNPJOk[1] = false;

            for (lNrDig = 0; lNrDig < 14; lNrDig++)
            {
                lDigitos[lNrDig] = int.Parse(aValue.Substring(lNrDig, 1));

                if (lNrDig <= 11)
                    lSoma[0] += (lDigitos[lNrDig] * int.Parse(lFtmt.Substring(lNrDig + 1, 1)));

                if (lNrDig <= 12)
                    lSoma[1] += (lDigitos[lNrDig] * int.Parse(lFtmt.Substring(lNrDig, 1)));
            }

            for (lNrDig = 0; lNrDig < 2; lNrDig++)
            {
                lResultado[lNrDig] = (lSoma[lNrDig] % 11);
                if ((lResultado[lNrDig] == 0) || (lResultado[lNrDig] == 1))
                    lCNPJOk[lNrDig] = (lDigitos[12 + lNrDig] == 0);
                else
                    lCNPJOk[lNrDig] = (lDigitos[12 + lNrDig] == (11 - lResultado[lNrDig]));
            }

            if (!(lCNPJOk[0] && lCNPJOk[1]))
                return false;

            return true;
        }
    }
}

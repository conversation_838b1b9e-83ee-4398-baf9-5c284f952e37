﻿using System;

namespace ATS.Data.Repository.External.Utils
{
    public static class ConvertUtils
    {
        public static int? ToIntNullable(this object value, int? defaultValue = null)
        {
            if (value == null)
                return defaultValue;

            try
            {
                return Convert.ToInt32(value);
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}

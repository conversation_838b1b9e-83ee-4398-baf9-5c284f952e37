﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Enum;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IEstadoService : IService<Estado>
    {
        Estado Get(int id);
        ValidationResult Update(Estado entity);
        ValidationResult Add(Estado entity);
        Estado GetPorIBGE(int nIBGE);
        IQueryable<Estado> Consultar(string nome);
        IQueryable<Estado> All();
        Estado GetPorSigla(string uf);
        int? GetIdEstadoPorSigla(int idPais, string sigla);
        ValidationResult Inativar(int idEstado);
        ValidationResult Reativar(int idEstado);
        int GetIdEstadoPorIbge(int codigoIbge);
        List<Estado> GetTodos();

        Estado GetEstadoBySigla(string sigla);

        int GetIdPorIBGE(int nIBGE);

        int? GetIdEstado(string uf);

        /// <summary>
        /// Retorna a lista de estados atualizados a partir da data
        /// </summary>
        /// <param name="dataBase">Data base para filtro</param>
        /// <returns></returns>
        IQueryable<Estado> GetEstadosAtualizados(DateTime dataBase, ERegiaoBrasil? Regiao);

        IQueryable<Estado> ConsultarPorIdPais(int idPais);
        object ConsultarGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        bool ValidarIbgeCadastrado(int ibge);
    }
}
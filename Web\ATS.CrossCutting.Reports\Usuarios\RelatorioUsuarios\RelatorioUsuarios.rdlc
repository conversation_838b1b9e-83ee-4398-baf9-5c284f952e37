<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.60055cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>8.78405cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.08208cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.02917cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.49458cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.05021cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.68521cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.25646cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.57396cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.25306cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>6.72912cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.52646cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.59702cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.16486cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.38875cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.09684cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>3.42076cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.6cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="IdUsuario">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!IdUsuario.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>IdUsuario</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Nome">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Nome.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Nome</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CpfCnpj">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CpfCnpj.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>CpfCnpj</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Cnh">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Cnh.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Cnh</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CategoriaCnh">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CategoriaCnh.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>CategoriaCnh</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ValidadeCnh">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ValidadeCnh.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ValidadeCnh</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Rntrc">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Rntrc.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Rntrc</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Ativo">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Ativo.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Ativo</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Carreteiro">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Carreteiro.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Carreteiro</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataCadastro">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataCadastro.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataCadastro</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Empresa">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Empresa.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Empresa</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataUltimoAcessoApp">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataUltimoAcessoApp.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataUltimoAcessoApp</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DataUltimoAcessoWeb">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!DataUltimoAcessoWeb.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DataUltimoAcessoWeb</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                        </TopBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Perfil">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Perfil.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Perfil</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Gestor">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Gestor.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Gestor</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Vistoriador">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Vistoriador.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Vistoriador</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="TipoCliente">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!TipoCliente.Value</Value>
                              <Style>
                                <FontFamily>Calibri</FontFamily>
                                <FontSize>8pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>TipoCliente</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                          <Width>0.25pt</Width>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details1" />
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DtsUsuarios</DataSetName>
        <Left>0.3cm</Left>
        <Height>0.6cm</Height>
        <Width>49.73315cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.09854cm</Height>
    <Style>
      <Border>
        <Style>None</Style>
      </Border>
    </Style>
  </Body>
  <Width>50.32232cm</Width>
  <Page>
    <PageHeader>
      <Height>2.05542cm</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Image Name="Logo">
          <Source>External</Source>
          <Value>=Parameters!Logo.Value</Value>
          <MIMEType>image/png</MIMEType>
          <Sizing>FitProportional</Sizing>
          <Top>0.25542cm</Top>
          <Left>0.3cm</Left>
          <Height>1.2cm</Height>
          <Width>2.60055cm</Width>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <TopBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </LeftBorder>
          </Style>
        </Image>
        <Textbox Name="Textbox1">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>RELATÓRIO DE USUÁRIOS</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>14pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Center</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox1</rd:DefaultName>
          <Top>0.25542cm</Top>
          <Left>2.90055cm</Left>
          <Height>1.2cm</Height>
          <Width>43.71183cm</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox2">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Página:  </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>=Globals!PageNumber</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value> de </Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
                <TextRun>
                  <Value>=Globals!TotalPages</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox2</rd:DefaultName>
          <Top>0.25542cm</Top>
          <Left>46.61239cm</Left>
          <Height>0.6cm</Height>
          <Width>3.42076cm</Width>
          <ZIndex>2</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <TopBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </TopBorder>
            <BottomBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <RightBorder>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </RightBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox3">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>="Emissão: " &amp; Now()</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>7pt</FontSize>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style>
                <TextAlign>Right</TextAlign>
              </Style>
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox2</rd:DefaultName>
          <Top>0.85542cm</Top>
          <Left>46.61239cm</Left>
          <Height>0.6cm</Height>
          <Width>3.42076cm</Width>
          <ZIndex>3</ZIndex>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <BottomBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </BottomBorder>
            <RightBorder>
              <Color>Black</Color>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </RightBorder>
            <VerticalAlign>Middle</VerticalAlign>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox14">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Código</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox12</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>0.3cm</Left>
          <Height>0.6cm</Height>
          <Width>2.60055cm</Width>
          <ZIndex>4</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox17">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Nome</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox13</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>2.90055cm</Left>
          <Height>0.6cm</Height>
          <Width>8.78405cm</Width>
          <ZIndex>5</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox18">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>CPF/CNPJ</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox15</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>11.68461cm</Left>
          <Height>0.6cm</Height>
          <Width>3.08208cm</Width>
          <ZIndex>6</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox20">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>CNH</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox16</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>14.76669cm</Left>
          <Height>0.6cm</Height>
          <Width>3.02917cm</Width>
          <ZIndex>7</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox22">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Categoria</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox19</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>17.79586cm</Left>
          <Height>0.6cm</Height>
          <Width>1.49458cm</Width>
          <ZIndex>8</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox23">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Validade</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox21</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>19.29044cm</Left>
          <Height>0.6cm</Height>
          <Width>2.05021cm</Width>
          <ZIndex>9</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox25">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>RNTRC</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox24</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>21.34065cm</Left>
          <Height>0.6cm</Height>
          <Width>2.68521cm</Width>
          <ZIndex>10</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox28">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Ativo</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox27</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>24.02586cm</Left>
          <Height>0.6cm</Height>
          <Width>1.25646cm</Width>
          <ZIndex>11</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox32">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Carreteiro</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox31</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>25.28232cm</Left>
          <Height>0.6cm</Height>
          <Width>1.57396cm</Width>
          <ZIndex>12</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox36">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Data de cadastro</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox35</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>26.85627cm</Left>
          <Height>0.6cm</Height>
          <Width>2.25306cm</Width>
          <ZIndex>13</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox40">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Empresa</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox39</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>29.10933cm</Left>
          <Height>0.6cm</Height>
          <Width>6.72912cm</Width>
          <ZIndex>14</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox43">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Último acesso app</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox42</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>35.83845cm</Left>
          <Height>0.6cm</Height>
          <Width>2.52646cm</Width>
          <ZIndex>15</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox54">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Perfil</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox53</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>40.96193cm</Left>
          <Height>0.6cm</Height>
          <Width>2.16486cm</Width>
          <ZIndex>16</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox57">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Gestor</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox56</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>43.12679cm</Left>
          <Height>0.6cm</Height>
          <Width>1.38875cm</Width>
          <ZIndex>17</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox60">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Vistoriador</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox59</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>44.51554cm</Left>
          <Height>0.6cm</Height>
          <Width>2.09684cm</Width>
          <ZIndex>18</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox62">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Tipo de cliente</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox61</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>46.61239cm</Left>
          <Height>0.6cm</Height>
          <Width>3.42076cm</Width>
          <ZIndex>19</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="Textbox44">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Último acesso web</Value>
                  <Style>
                    <FontFamily>Calibri</FontFamily>
                    <FontSize>8pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox42</rd:DefaultName>
          <Top>1.45542cm</Top>
          <Left>38.36491cm</Left>
          <Height>0.6cm</Height>
          <Width>2.59702cm</Width>
          <ZIndex>20</ZIndex>
          <Style>
            <Border>
              <Style>Solid</Style>
              <Width>0.25pt</Width>
            </Border>
            <TopBorder>
              <Color>Black</Color>
            </TopBorder>
            <BackgroundColor>WhiteSmoke</BackgroundColor>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <PageHeight>21cm</PageHeight>
    <PageWidth>50.8cm</PageWidth>
    <InteractiveHeight>21cm</InteractiveHeight>
    <InteractiveWidth>29.7cm</InteractiveWidth>
    <LeftMargin>0.2cm</LeftMargin>
    <RightMargin>0.2cm</RightMargin>
    <TopMargin>0cm</TopMargin>
    <BottomMargin>0cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsUsuárioRelatorioListaUsuario">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>ef8d04d2-7ac9-4f79-b840-49bcb6bb1410</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsUsuariosRelatorioUsuarios">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>a0c174ed-e3d4-482c-829c-928589c9276a</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsUsuariosRelatorioUsuarios1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>f074c6f2-d89c-402a-8574-6b8351812c19</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsUsuariosRelatorioUsuarios2">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c741e627-885a-4573-9474-8db86534b79e</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DtsUsuarios">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsUsuariosRelatorioUsuarios2</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Ativo">
          <DataField>Ativo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Carreteiro">
          <DataField>Carreteiro</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CategoriaCnh">
          <DataField>CategoriaCnh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Cnh">
          <DataField>Cnh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CpfCnpj">
          <DataField>CpfCnpj</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataCadastro">
          <DataField>DataCadastro</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataUltimoAcessoApp">
          <DataField>DataUltimoAcessoApp</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataUltimoAcessoWeb">
          <DataField>DataUltimoAcessoWeb</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Empresa">
          <DataField>Empresa</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Gestor">
          <DataField>Gestor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IdUsuario">
          <DataField>IdUsuario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Nome">
          <DataField>Nome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Perfil">
          <DataField>Perfil</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="RecebeNotificacao">
          <DataField>RecebeNotificacao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Rntrc">
          <DataField>Rntrc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TipoCliente">
          <DataField>TipoCliente</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValidadeCnh">
          <DataField>ValidadeCnh</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Vistoriador">
          <DataField>Vistoriador</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.Usuarios.RelatorioUsuarios</rd:DataSetName>
        <rd:TableName>RelatorioUsuariosDataType</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.Usuarios.RelatorioUsuarios.RelatorioUsuariosDataType, ATS.CrossCutting.Reports, Version=*******, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="Logo">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>c612e303-6323-43fd-a674-a0a61b69a03f</rd:ReportID>
</Report>
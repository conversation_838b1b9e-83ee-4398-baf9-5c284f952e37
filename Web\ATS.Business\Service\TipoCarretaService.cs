﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;

namespace ATS.Domain.Service
{
    public class TipoCarretaService : ServiceBase, ITipoCarretaService
    {
        private readonly  ITipoCarretaRepository _tipoCarretaRepository;
        private readonly ITipoCarretaDapper _tipoCarretaDapper;

        public TipoCarretaService(ITipoCarretaRepository tipoCarretaRepository, ITipoCarretaDapper tipoCarretaDapper)
        {
            _tipoCarretaRepository = tipoCarretaRepository;
            _tipoCarretaDapper = tipoCarretaDapper;
        }

        /// <summary>
        /// Retorna o registro de Tipo de Carreta a partir do ID
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        public TipoCarreta Get(int id)
        {
            return _tipoCarretaRepository
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.IdTipoCarreta == id);
        }

        /// <summary>
        /// Retorna apenas o objeto de tipo carreta
        /// </summary>
        /// <param name="id"></param>
        /// <returns>TipoCarreta</returns>
        public TipoCarreta GetTipoCarreta(int id)
        {
            return _tipoCarretaRepository.FirstOrDefault(x => x.IdTipoCarreta == id);
        }

        /// <summary>
        /// Adicionar um registro de Tipo de Carreta
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ValidationResult Add(TipoCarreta entity)
        {
            try
            {
                entity.DataHoraUltimaAtualizacao = DateTime.Now;

                ITipoCarretaRepository tipoCarretaRepository = _tipoCarretaRepository;
                tipoCarretaRepository.Add(entity);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro de Tipo de Carreta
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ValidationResult Update(TipoCarreta entity)
        {
            try
            {
                entity.DataHoraUltimaAtualizacao = DateTime.Now;
                _tipoCarretaRepository.Update(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>,
        /// Método utilizado para consultar Tipo de Carreta.
        /// </summary>
        /// <param name="nome">Nome de Tipo de Carreta.</param>
        /// <returns>IQueryable de TipoCarretaGrid</returns>
        public IQueryable<TipoCarretaGrid> Consultar(string nome, int? idEmpresa)
        {
            if (nome == null)
                nome = string.Empty;

            var retorno = _tipoCarretaRepository.Consultar(nome);

            if (idEmpresa.HasValue)
                retorno = retorno.Where(x => x.IdEmpresa == idEmpresa || x.IdEmpresa == null);

            return retorno;
        }

        /// <summary>
        /// Inativar o tipo de carreta
        /// </summary>
        /// <param name="idTipoCarreta">Código do tipo de carreta a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idTipoCarreta)
        {
            try
            {
                var tipoCarretaRepository = _tipoCarretaRepository;
                var tipoCarreta = tipoCarretaRepository.Get(idTipoCarreta);

                if (!tipoCarreta.Ativo)
                    return new ValidationResult().Add($"Tipo de Carreta já desativado na base de dados.");

                tipoCarreta.Ativo = false;
                tipoCarreta.DataHoraUltimaAtualizacao = DateTime.Now;

                tipoCarretaRepository.Update(tipoCarreta);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar o tipo de carreta
        /// </summary>
        /// <param name="idTipoCarreta">Código do tipo de carreta a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idTipoCarreta)
        {
            try
            {
                var tipoCarretaRepository = _tipoCarretaRepository;
                var tipoCarreta = tipoCarretaRepository.Get(idTipoCarreta);

                if (tipoCarreta.Ativo)
                    return new ValidationResult().Add($"Tipo de Carreta já ativado na base de dados.");

                tipoCarreta.Ativo = true;
                tipoCarreta.DataHoraUltimaAtualizacao = DateTime.Now;

                tipoCarretaRepository.Update(tipoCarreta);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Carreta por categoria
        /// </summary>
        /// <param name="categoria">Categoria de Tipo de Carreta</param>
        /// <returns>IQueryable de Tipo de Carreta</returns>
        public IQueryable<TipoCarreta> GetPorCategoria(ECategoriaTipoCarreta categoria)
        {
            return _tipoCarretaRepository.GetPorCategoria(categoria);
        }

        public object ConsultarSemEmpresa()
        {
            return _tipoCarretaRepository.ConsultarSemEmpresa();
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Carreta Atualizados
        /// </summary>
        /// <param name="dataAtualizacao">Data atualização</param>
        /// <returns>IQueryable de TipoCarreta</returns>
        public IEnumerable<TipoCarreta> GetIdsAtualizados(DateTime dataAtualizacao, List<int> idsEmpresa)
        {
            return _tipoCarretaRepository.GetTipoCarretaAtualizadas(dataAtualizacao, idsEmpresa);
        }

        /// <summary>
        /// Método utilizado para listar todos os Tipos de Carretas ativos
        /// </summary>
        /// <returns>IQueryable de TipoCarreta</returns>
        public IQueryable<TipoCarreta> All()
        {
            return _tipoCarretaRepository.Find(x => x.Ativo);
        }

        public TipoCarreta GetPorDescricao(string nome, int idEmpresa)
        {
            return _tipoCarretaDapper
                .GetPorDescricao(nome, idEmpresa);
        }

        public object ConsultaGrid(int? idEmpresa, int? idTipoCarreta, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var tipoCarreta = _tipoCarretaRepository
                .GetAll()
                .Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                tipoCarreta = tipoCarreta.Where(x => x.IdEmpresa == idEmpresa.Value || x.IdEmpresa == null);

            if (idTipoCarreta.HasValue)
                tipoCarreta = tipoCarreta.Where(x => x.IdTipoCarreta == idTipoCarreta.Value);

            if (!string.IsNullOrWhiteSpace(descricao))
                tipoCarreta = tipoCarreta.Where(x => x.Nome.Contains(descricao));

            // Aplica a ordenação solicitada pela Sotran
            tipoCarreta = tipoCarreta.OrderByDescending(x => x.Destacar)
                                     .ThenBy(x => x.Nome);

            if (!string.IsNullOrWhiteSpace(orderFilters?.Campo) && orderFilters?.Campo != "IdTipoCarreta")
                tipoCarreta = tipoCarreta.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            tipoCarreta = tipoCarreta.AplicarFiltrosDinamicos<TipoCarreta>(filters);

            return new
            {
                totalItems = tipoCarreta.Count(),
                items = tipoCarreta.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdTipoCarreta,
                    x.Nome,
                    x.Categoria,
                    x.QtdeEixos,
                    x.Ativo,
                    Empresa = x.Empresa?.RazaoSocial
                })
            };
        }
    }
}
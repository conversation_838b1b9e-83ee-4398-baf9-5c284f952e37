﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IEstabelecimentoApp
    {
        Estabelecimento GetByIdEstabelecimentoBase(int idEstabelecimentoBase, int idEmpresa);
        List<KeyValuePair<int, int>> GetIdEstabelecimentosAssociadosLiberacaoProtocolo(int idEstabelecimentoBase);
        Estabelecimento GetByIdEstabelecimentoEmpresa(int idEstabelecimentoBase, int idEmpresa);
        IQueryable<EstabelecimentoProduto> GetQueryProduto();
        IQueryable<Estabelecimento> GetEstabelecimentoPorIdBase(int idEstabelecimentoBase);
        ValidationResult RemoverProdutos(IList<int> idprodutobase);
        ValidationResult Add(Estabelecimento estabelecimento);
        ValidationResult Update(Estabelecimento estabelecimento, List<EstabelecimentoProduto> produtosExclusao);
        ValidationResult Inativar(int idEstabelecimento);
        ValidationResult Reativar(int idEstabelecimento);
        Estabelecimento Get(int idTipoEstabelecimento);
        Estabelecimento Get(string cnpj, int idEmpresa);
        KeyValuePair<int, bool> GetEstabelecimentoGeracaoProtocolo(int idEstabelecimentoBase, int idEmpresa);
        int? GetByBase(int idBase);
        int? GetIdBase(int idestabelecimento);
        IEnumerable<ConsultaEstabelecimentoModelResponse> ConsultarPorEmpresa(int idEmpresa, string cnpjEstabelecimento);

        IQueryable<Estabelecimento> ConsultarEstabelecimentosRota(
            int idEmpresa,
            decimal latitudeOrigem, decimal longitudeOrigem, decimal? latitudeDestino = null,
            decimal? longitudeDestino = null, decimal? raio = 50, int[] idsTipoEstabelecimento = null, bool buscaCredenciados = false);

        Estabelecimento ConsultarPorId(int idEstabelecimento);
        object ConsultarAssociacoes(int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        object GetEstabelecimentosPorViagem(string tokenViagemEvento, int take, int page, OrderFilters order, List<QueryFilters> filters);
        bool IsRegular(int idEstabelecimento);
        bool EstaCredenciado(int idEstabelecimento);
        bool ValidarChaveTokenPorHorarario(int idEstabelecimentoBase, int idEmpresa);
        ValidationResult ReplicarProdutoBase(EstabelecimentoBaseProduto estabelecimentoBaseProduto);
        IQueryable<Estabelecimento> GetQueryByCnpj(string cnpj);
        List<Estabelecimento> EstabelecimentosDaRota(int idRota);
        int? GetIdEstabelecimentoPorCnpj(string cnpjEstabelecimento, string cnpjEmpresa);
        bool GetEstabelecimentoAtivo(int idEstabelecimento);
        bool PertenceAEmpresa(int idempresa, int idEstabelecimento);
    }
}
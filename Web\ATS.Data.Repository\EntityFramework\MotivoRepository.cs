﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class MotivoRepository : Repository<Motivo>, IMotivoRepository
    {
        public MotivoRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<Motivo> GetAtivos(int idEmpresa, ETipoMotivo? tipo = null)
        {
            var query =  All()
                .Where(m => m.IdEmpresa == idEmpresa && m.Ativo);

            if (tipo.HasValue)
            {
                var idMotivoList = Context.Set<TipoMotivo>()
                    .Where(x => x.Tipo == tipo || x.Tipo == ETipoMotivo.Todos)
                    .Select(x => x.IdMotivo)
                    .Distinct();

                query = query.Where(m => idMotivoList.Any(x => x == m.IdMotivo));
            }
            
            return query.OrderBy(m => m.Descricao);
        }
    }
}
﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Security;
using ATS.WS.Security.Configuration;
using System;
using System.Linq;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Models;
using System.Collections.Generic;
using ATS.Application.Interface;
using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using Newtonsoft.Json;
using ATS.WS.Services;
using ATS.Domain.Helpers;
using System.Net;
using ATS.Domain.Models;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Reflection;
using System.IO;
using System.Text;
using ATS.WS.Attributes.Models;
using Newtonsoft.Json.Linq;
using NLog;
using Sistema.Framework.Util.Extension;
using TrackerEnabledDbContext.Common.Extensions;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class AutorizarIntegracao : ActionFilterAttribute
    {
        public static Logger _logger = LogManager.GetCurrentClassLogger();
        public EOrigemRequisicao OrigensPermitida { get; set; } = EOrigemRequisicao.Todos;
        public bool UsingIdentity { get; set; }
        public IUserIdentity UserIdentity { get; set; }

        public AutorizarIntegracao()
        {
        }

        public AutorizarIntegracao(EOrigemRequisicao origempermitida = EOrigemRequisicao.Todos)
        {
            OrigensPermitida = origempermitida;
        }
        
        public AutorizarIntegracao(EOrigemRequisicao origempermitida = EOrigemRequisicao.Todos,bool usingIdentity = false)
        {
            OrigensPermitida = origempermitida;
            UsingIdentity = usingIdentity;
        }
        
        public AutorizarIntegracao(bool usingIdentity = false)
        {
            UsingIdentity = usingIdentity;
        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var customAttributes = filterContext.ActionDescriptor.GetCustomAttributes(true);

            if (!customAttributes.Any() || customAttributes.All(x => x.GetType() != typeof(AutorizarIntegracao)))
            {
                return;
            }

            var controller = (BaseController) filterContext.Controller;
            var autenticacaoAplicacaoApp = DependencyResolver.Current.GetService<IAutenticacaoAplicacaoApp>();
            var srvIP = DependencyResolver.Current.GetService<SrvIP>();
            var keycloak = DependencyResolver.Current.GetService<KeycloakHelper>();
            var empresaApp = DependencyResolver.Current.GetService<IEmpresaApp>();

            var scope = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName + "." + filterContext.ActionDescriptor.ActionName;

            // //validação nova baseada em Authorization com token gerado pelo Keycloak
            // string token = filterContext.HttpContext.Request.Headers["Authorization"];
            // string cnpjAplicacao = "";
            // string cnpjEmpresa = "";

            var cnpjEmpresa = "";
            var cnpjAplicacao = "";
            var token = "";

            if (filterContext.HttpContext.Request.QueryString.HasKeys())
            {
                foreach (var key in filterContext.HttpContext.Request.QueryString.AllKeys)
                {
                    if (key.ToLower().Equals("cnpjaplicacao", StringComparison.OrdinalIgnoreCase))
                        cnpjAplicacao = filterContext.HttpContext.Request.QueryString[key];

                    if (key.ToLower().Equals("cnpjempresa", StringComparison.OrdinalIgnoreCase))
                        cnpjEmpresa = filterContext.HttpContext.Request.QueryString[key];

                    if (key.ToLower().Equals("token", StringComparison.OrdinalIgnoreCase))
                        token = filterContext.HttpContext.Request.QueryString[key];
                }
            }

            if (string.IsNullOrWhiteSpace(cnpjAplicacao) || string.IsNullOrWhiteSpace(cnpjEmpresa) || string.IsNullOrWhiteSpace(token))
            {
                filterContext.HttpContext.Request.InputStream.Position = 0;
                using (var reader = new StreamReader(filterContext.HttpContext.Request.InputStream))
                {
                    var requestBody = reader.ReadToEnd();
                    if (!string.IsNullOrEmpty(requestBody))
                    {
                        try
                        {
                            var json = JObject.Parse(requestBody);

                            foreach (var property in json.Properties())
                            {
                                var key = property.Name;
                                object value = property.Value;
                                if (string.IsNullOrWhiteSpace(cnpjAplicacao) && key.ToLower().Equals("cnpjaplicacao", StringComparison.OrdinalIgnoreCase))
                                    cnpjAplicacao = value.ToString();

                                if (string.IsNullOrWhiteSpace(cnpjEmpresa) && key.ToLower().Equals("cnpjempresa", StringComparison.OrdinalIgnoreCase))
                                    cnpjEmpresa = value.ToString();

                                if (string.IsNullOrWhiteSpace(token) && key.ToLower().Equals("token", StringComparison.OrdinalIgnoreCase))
                                    token = value.ToString();
                            }
                        }
                        catch (Exception)
                        {
                            // Log ou trate a exceção conforme necessário
                        }
                    }
                }
            }

            if (string.IsNullOrWhiteSpace(cnpjAplicacao) || string.IsNullOrWhiteSpace(cnpjEmpresa) || string.IsNullOrWhiteSpace(token))
            {
                foreach (var item in filterContext.ActionParameters)
                {
                    var itemValue = item.Value;

                    if (itemValue is string || itemValue is int)
                    {
                        var key = item.Key.ToLower();

                        switch (key)
                        {
                            case "token":
                                if (string.IsNullOrWhiteSpace(token))
                                    token = itemValue?.ToStringSafe();
                                break;
                            case "cnpjaplicacao":
                                if (string.IsNullOrWhiteSpace(cnpjAplicacao))
                                    cnpjAplicacao = itemValue?.ToStringSafe();
                                break;
                            case "cnpjempresa":
                                if (string.IsNullOrWhiteSpace(cnpjEmpresa))
                                    cnpjEmpresa = itemValue?.ToStringSafe();
                                break;
                        }
                    }
                    else if (itemValue != null && itemValue.GetType().IsClass)
                    {
                        // Caso seja um objeto, iterar pelas propriedades
                        foreach (var property in itemValue.GetType().GetProperties())
                        {
                            try
                            {
                                var key = property.Name.ToLower();
                                var value = property.GetValue(itemValue);

                                switch (key)
                                {
                                    case "token":
                                        if (string.IsNullOrWhiteSpace(token))
                                            token = value?.ToStringSafe();
                                        break;
                                    case "cnpjaplicacao":
                                        if (string.IsNullOrWhiteSpace(cnpjAplicacao))
                                            cnpjAplicacao = value?.ToStringSafe();
                                        break;
                                    case "cnpjempresa":
                                        if (string.IsNullOrWhiteSpace(cnpjEmpresa))
                                            cnpjEmpresa = value?.ToStringSafe();
                                        break;
                                }
                            }
                            catch (Exception)
                            {
                                // Ignorar exceção
                            }
                        }
                    }
                }
            }

            if (!controller.ValidarToken(token) && !autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
            {
                var result = new JsonResult();
                result.Data = new DefaultResponse
                {
                    message = "Token inválido.",
                    success = false,
                    warning = false
                };
                result.JsonRequestBehavior = JsonRequestBehavior.AllowGet;
                filterContext.Result = result;
                return;
            }
            
            //Preenchimento Identity na API
            if (UsingIdentity)
            {
                var cnpjEmpresaQuery = string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa;
                
                var empresa = empresaApp.All().Select(x => new
                {
                    x.CNPJ,
                    x.IdEmpresa,
                    x.RazaoSocial
                }).FirstOrDefault(x => x.CNPJ == cnpjEmpresaQuery);
                    
                if (empresa == null)
                {
                    var result = new JsonResult()
                    {
                        Data = new
                        {
                            message = "Empresa não localizada!",
                            success = false,
                            warning = false
                        },
                        JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                    };
                    
                    filterContext.Result = result;
                    return;
                }

                UserIdentity.IdEmpresa = empresa.IdEmpresa;
                UserIdentity.Perfil = 2;
                UserIdentity.CpfCnpj = empresa.CNPJ;
            }

            //validacao de ip
            var ip = controller.GetRealIp();
            if (!srvIP.AnalisarIP(string.IsNullOrWhiteSpace(cnpjEmpresa) ? cnpjAplicacao : cnpjEmpresa, ip, OrigensPermitida))
            {
                var result = new JsonResult();
                result.Data = new DefaultResponse
                {
                    message = "Integração não autorizada, entre em contato com o suporte.",
                    success = false,
                    warning = false
                };
                result.JsonRequestBehavior = JsonRequestBehavior.AllowGet;
                filterContext.Result = result;
                return;
            }

            try
            {
                if (!string.IsNullOrWhiteSpace(cnpjAplicacao))
                {
                    //auto cadastramento de clientes de integracao no keycloak
                    KeycloakClientModel client = keycloak.GetClient(cnpjAplicacao);
                    if (client == null)
                    {
                        //cadastrar o cliente se ainda nao existe no keycloak
                        var autenticacao = autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token).FirstOrDefault();
                        var empresa = empresaApp.Get(autenticacao.IdEmpresa);
                        HttpStatusCode status = keycloak.CreateClient(cnpjAplicacao, empresa.RazaoSocial);

                        if (status == HttpStatusCode.OK)
                        {
                            client = keycloak.GetClient(cnpjAplicacao);
                        }
                    }

                    if (client != null)
                    {
                        //vincular o escopo ao cliente, se ainda não vinculou
                        keycloak.AddScopeToClient(client.ClientId, scope, "default");

                        //autenticar no keycloak para registrar
                        KeycloakOpenIdTokenModel kcToken = keycloak.GetClientAccessToken("", cnpjAplicacao, client.Secret, false);

                        if (kcToken.StatusCode != HttpStatusCode.OK)
                        {
                            var result = new JsonResult();
                            result.Data = new DefaultResponse
                            {
                                message = "(" + kcToken.StatusCode + ") Falha na autenticação.",
                                success = false,
                                warning = false
                            };
                            result.JsonRequestBehavior = JsonRequestBehavior.AllowGet;
                            //filterContext.Result = result;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                var msg = e.ToString();
                _logger.Error("AUTORIZACAO INTEGRACAO - EXCECAO: {msg}", msg);

                var result = new JsonResult();
                result.Data = new DefaultResponse
                {
                    message = "(" + e.Message + ") Falha na autenticação.",
                    success = false,
                    warning = false
                };
                result.JsonRequestBehavior = JsonRequestBehavior.AllowGet;
            }
        }
    }
}

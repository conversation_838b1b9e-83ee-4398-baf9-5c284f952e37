﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class RETIRED_REQUIRED_CAMPOS_VIAGEM : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.LogMetadata",
                c => new
                    {
                        id = c.<PERSON>(nullable: false, identity: true),
                        auditlogid = c.<PERSON>(nullable: false),
                        key = c.String(maxLength: 100, unicode: false),
                        value = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.AuditLog", t => t.auditlogid)
                .Index(t => t.auditlogid, name: "IX_AuditLogId");
            
            AlterColumn("dbo.VIAGEM", "dataprevisaoentrega", c => c.DateTime());
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.LogMetadata", "auditlogid", "dbo.AuditLog");
            DropIndex("dbo.LogMetadata", "IX_AuditLogId");
            AlterColumn("dbo.VIAGEM", "dataprevisaoentrega", c => c.DateTime(nullable: false));
            DropTable("dbo.LogMetadata");
        }
    }
}

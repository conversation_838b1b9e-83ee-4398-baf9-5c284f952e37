﻿using ATS.Domain.Grid;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Request.Usuario
{
    public class UsuarioAtsConsultaGridFilters
    {
        public int? idEmpresa { get; set; }
        public string nome { get; set; }
        public int Take { get; set; }
        public int Page { get; set; }
        public OrderFilters Order { get; set; }
        public List<QueryFilters> Filters { get; set; }
    }
}
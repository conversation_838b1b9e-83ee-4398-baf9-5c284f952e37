using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class RotaEstabelecimentoMap : EntityTypeConfiguration<RotaEstabelecimento>
    {
        public RotaEstabelecimentoMap()
        {
            ToTable("ROTA_ESTABELECIMENTO");

            HasKey(x => new
            {
                x.IdRota,
                x.IdEstabelecimento
            });

            HasRequired(x => x.Rota)
                .WithMany(x => x.RotaEstabelecimento)
                .HasForeignKey(x => x.IdRota);

            HasRequired(x => x.Estabelecimento)
                .WithMany(x => x.RotaEstabelecimento)
                .HasForeignKey(x => x.IdEstabelecimento);
        }
    }
}
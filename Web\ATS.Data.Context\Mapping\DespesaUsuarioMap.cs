﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class DespesaUsuarioMap : EntityTypeConfiguration<DespesaUsuario>
    {
        public DespesaUsuarioMap()
        {
            ToTable("DESPESA_USUARIO");
            HasKey(x => x.IdDespesaUsuario);

            Property(t => t.IdDespesaUsuario).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.DataCadastro).IsOptional();
            Property(t => t.DataAtualizacao).IsOptional();
            Property(t => t.HashId).HasMaxLength(150).IsRequired();
            Property(t => t.Descricao).HasMaxLength(500).IsOptional();
            Property(t => t.URL).HasMaxLength(1000).IsOptional();
            Property(t => t.ImageToken).HasMaxLength(100).IsOptional();
            Property(t => t.Latitude).IsRequired();
            Property(t => t.Longitude).IsRequired();
            Property(t => t.IdPrestacaoContas).IsOptional();

            Ignore(t => t.UsuarioCadastro);
            Ignore(t => t.UsuarioAtualizacao);

            HasRequired(c => c.Usuario)
                .WithMany()
                .HasForeignKey(c => c.IdUsuario);

            HasRequired(c => c.Categoria)
                .WithMany()
                .HasForeignKey(c => c.IdCategoria);

            HasOptional(c => c.PrestacaoContas)
                .WithMany(c => c.DespesasUsuario)
                .HasForeignKey(c => c.IdPrestacaoContas);
        }
    }
}
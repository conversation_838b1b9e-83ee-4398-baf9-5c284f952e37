using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Domain.Models.Parametro
{
    public class PercentualTransferenciaFreteViagemParametro
    {
        public decimal? Adiantamento { get; set; }
        public decimal? Saldo { get; set; }
        public decimal? Estadia { get; set; }
        public decimal? RPA { get; set; }
        public decimal? TarifaAntt { get; set; }
        public decimal? Abastecimento { get; set; }
        public decimal? Abono { get; set; }
    }
    
    /// <summary>
    /// Ação a ser realizada com o saldo residual do cartão pedágio ao realizar novo crédito de pedágio pela aplicação do moedeiro
    /// </summary>
    public enum AcaoSaldoResidualNovoCreditoCartaoPedagio
    {
        /// <summary>
        /// No ponto de vista do proprietário, obtem a informação da empresa que está realizando o comando de crédito
        /// </summary>
        [EnumMember, Description("Padrão da empresa")]
        PadraoEmpresa = 0,
        
        /// <summary>
        /// Estornar saldo residual do cartão pedágio para administradora do cartão (Sistema Info ou SOTRAN).
        /// Apenas primeiro credito do dia deve ser estornado (Primeiro do dia e não uma vez a cada 24 horas).
        /// </summary>
        [EnumMember, Description("Estornar saldo para administradora")]
        EstornarParaAdministradora = 1,

        /// <summary>
        /// Mantem o saldo no cartão e realiza nova carga com o valor integral solicitado 
        /// </summary>
        [EnumMember, Description("Manter saldo e creditar valor integral")]
        Manter = 2
    }
}
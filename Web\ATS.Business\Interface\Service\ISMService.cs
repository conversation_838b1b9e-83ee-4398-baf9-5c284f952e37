﻿using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Service
{
    public interface ISMService
    {
        void Send(List<string> idsPush, string title, ETipoMensagemPush messageType, string message, object data);
        SMSResponse EnviarSMS(string celular, string message, EProcessoEnvio? processoenvio = EProcessoEnvio.LocalNaoMapeado, int? idUsuarioEnvio = null, int? idPreUsuarioEnvio = null, int? idempresa = null);
    }
}
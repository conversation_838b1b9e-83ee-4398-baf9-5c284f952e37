using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class VeiculoDigitosMercosulSeeder
    {
        public void Execute(AtsContext context)
        {
            var veiculoDigitosMercosuls = new List<VeiculoDigitosMercosul>
            {
                new VeiculoDigitosMercosul {NumeroOrigem = 0, LetraDestino = "A"},
                new VeiculoDigitosMercosul {NumeroOrigem = 1, LetraDestino = "B"},
                new VeiculoDigitosMercosul {NumeroOrigem = 2, LetraDestino = "C"},
                new VeiculoDigitosMercosul {NumeroOrigem = 3, LetraDestino = "D"},
                new VeiculoDigitosMercosul {NumeroOrigem = 4, LetraDestino = "E"},
                new VeiculoDigitosMercosul {NumeroOrigem = 5, LetraDestino = "F"},
                new VeiculoDigitosMercosul {NumeroOrigem = 6, LetraDestino = "G"},
                new VeiculoDigitosMercosul {NumeroOrigem = 7, LetraDestino = "H"},
                new VeiculoDigitosMercosul {NumeroOrigem = 8, LetraDestino = "I"},
                new VeiculoDigitosMercosul {NumeroOrigem = 9, LetraDestino = "J"}
            };

            foreach (var veiculoDigitosMercosul in veiculoDigitosMercosuls)
            {
                if (!context.VeiculoDigitosMercosul.Any(c => c.NumeroOrigem == veiculoDigitosMercosul.NumeroOrigem))
                {
                    context.VeiculoDigitosMercosul.Add(veiculoDigitosMercosul);
                    context.SaveChanges();
                }
            }
        }
    }
}
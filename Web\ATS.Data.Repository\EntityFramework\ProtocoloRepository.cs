﻿using System.Data.Entity;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.Domain.Enum;
using System.Collections.Generic;
using System;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class ProtocoloRepository : Repository<Protocolo>, IProtocoloRepository
    {
        public ProtocoloRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<Protocolo> Consultar(int idEmpresa, bool includeEstabelecimentoBase = false)
        {
            var query = from protocolo in All()
                    .Include(x => x.ProtocoloEventos)
                    .Include(x => x.ProtocoloAnexos)
                    .Include(x => x.ProtocoloAntecipacoes)
                    .Include(x => x.ProtocoloEventos.Select(y => y.ViagemEvento))
                    .Include(x => x.ProtocoloEventos.Select(y => y.ViagemEvento).Select(z => z.Viagem))
                where protocolo.IdEmpresa == idEmpresa
                      && !protocolo.Processado &&
                      protocolo.ProtocoloEventos.All(x => x.Status != EStatusProtocoloEvento.Gerado)
                select protocolo;

            if (includeEstabelecimentoBase)
                query = query.Include(p => p.EstabelecimentoBase);

            return query;
        }


        public IQueryable<Protocolo> ConsultarProtocolosPorEstabelecimentos(List<int> idsEstabelecimentoBase, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal)
        {
            var protocolos = from protocolo in All()
                   .Include(x => x.ProtocoloEventos)
                   .Include(x => x.ProtocoloEventos.Select(y => y.ViagemEvento))
                   .Include(x => x.ProtocoloEventos.Select(y => y.ViagemEvento).Select(z => z.Viagem))
                   .Include(x => x.ProtocoloAnexos)
                   .Include(x => x.ProtocoloAntecipacoes)
                             where idsEstabelecimentoBase.Contains(protocolo.IdEstabelecimentoBase)
                             select protocolo;

            if (dataPagamentoInicial.HasValue)
                protocolos = protocolos.Where(x => x.DataPagamento > dataPagamentoInicial.Value);
            if (dataPagamentoFinal.HasValue)
                protocolos = protocolos.Where(x => x.DataPagamento < dataPagamentoFinal.Value);
            if (dataGeracaoInicial.HasValue)
                protocolos = protocolos.Where(x => x.DataGeracao > dataGeracaoInicial.Value);
            if (dataGeracaoFinal.HasValue)
                protocolos = protocolos.Where(x => x.DataGeracao < dataGeracaoFinal.Value);

            return protocolos;
        }
    }
}
﻿using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class PagamentoConfiguracao
    {
        /// <summary>
        /// Código
        /// </summary>
        public int IdPagamentoConfiguracao { get; set; }
        /// <summary>
        /// Código do empresa
        /// </summary>
        public int IdEmpresa { get; set; }
        /// <summary>
        /// Código do filial
        /// </summary>
        public int? IdFilial { get; set; }
        /// <summary>
        /// Valor da taxa
        ///  </summary>
        public double ValorTaxa { get; set; }
        /// <summary>
        /// Data de cadastro
        ///  </summary>
        public DateTime DataCadastro { get; set; }
        /// <summary>
        /// Data de atualização
        /// </summary>
        public DateTime DataUltimaAtualizaca { get; set; }

        public bool Ativo { get; set; }

        /// <summary>
        /// Bonifica o motorista por peso de chegada ser maior que o peso de saída durante uma viagem no pagamento deste frete.
        /// </summary>
        public bool BonificarMotorista { get; set; } = false;

        #region Referência
        /// <summary>
        /// Empresa a qual a justificativa esta cadastrada
        /// </summary>
        public virtual Empresa Empresa { get; set; }

        public virtual Filial Filial { get; set; }
        #endregion

        #region Reverse navigation
        public virtual ICollection<PagamentoConfiguracaoProcesso> PagamentoConfiguracoesProcesso { get; set; }
        #endregion
    }
}
﻿using System.Collections.Generic;
using ATS.Domain.DTO.Proprietario;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Dapper
{
    public interface IProprietarioDapper : IRepositoryDapper<ProprietarioCadastroServicoCartaoDto>
    {
        void AtualizaEquiparadoTac(List<int> idsProprietario, bool equiparadoTac);
        void AtualizaEquiparadoTac(int proprietarioId, bool equiparadoTac);
        List<ProprietarioCadastroServicoCartaoDto> ProprietariosSemEquiparadoTac(string cnpjcpf);
        List<ProprietarioCadastroServicoCartaoDto> ProprietariosComEquiparadoTac(List<string> cnpjcpf);
    }
}
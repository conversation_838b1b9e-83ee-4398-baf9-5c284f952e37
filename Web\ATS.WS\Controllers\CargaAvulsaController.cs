using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request;
using ATS.WS.Services;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class CargaAvulsaController : BaseApiController<ICargaAvulsaApp>
    {
        private readonly SrvCargaAvulsa _srvCargaAvulsa;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvIP _srvIP;

        public CargaAvulsaController(BaseControllerArgs baseArgs, ICargaAvulsaApp app, SrvCargaAvulsa srvCargaAvulsa, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvIP srvIP) : base(baseArgs, app)
        {
            _srvCargaAvulsa = srvCargaAvulsa;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvIP = srvIP;
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Carregar(CargaAvulsaRequest request)
        {
            try
            {
                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                return Responde(_srvCargaAvulsa.Add(request, this.GetRealIp()));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public JsonResult Estornar(EstornoCargaAvulsaRequest estornoCargaAvulsaRequest)
        {
            try
            {
                if (!ValidarToken(estornoCargaAvulsaRequest.Token) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(estornoCargaAvulsaRequest.CNPJAplicacao, estornoCargaAvulsaRequest.Token))
                    return Mensagem("Falha na autenticação.");

                return Responde(_srvCargaAvulsa.EstornarCargaAvusa(estornoCargaAvulsaRequest));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }


        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult Aprovar(AprovarCargaAvulsaRequest request)
        {
            try
            {
                Logger.Info("Aprovação de carga avulsa: {IdCargaAvulsa}", request.IdCargaAvulsa);
                var ip = GetRealIp();

                if  (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var response = App.Aprovar(request.IdCargaAvulsa, request.CNPJAplicacao);
                Logger.Info("Response Aprovação de carga avulsa: {IdCargaAvulsa}, {response}", request.IdCargaAvulsa, response);


                return response.IsValid
                    ? Responde(new Retorno()
                    {
                        Sucesso = true,
                        Mensagem = "Operação realizada com sucesso!"
                    })
                    : Mensagem(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }

        [HttpPatch]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult Processar(ProcessarCargaAvulsaRequest request)
        {
            try
            {
                Logger.Info("Processar de carga avulsa: {IdCargaAvulsa}", request.IdCargaAvulsa);

                if (!ValidarToken(request.Token, request.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var response = App.Processar(request.IdCargaAvulsa, request.CNPJAplicacao);

                Logger.Info("Response Processar de carga avulsa: {IdCargaAvulsa}, {response}", request.IdCargaAvulsa, response);

                return response.IsValid
                    ? Responde(new Retorno()
                    {
                        Sucesso = true,
                        Mensagem = "Operação realizada com sucesso!"
                    })
                    : Mensagem(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
        
        [HttpPost]
        [EnableLogRequest]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao(EOrigemRequisicao.ApenasInterno)]
        public JsonResult Reprovar(ReprovarCargaAvulsaRequest request)
        {
            try
            {
                if  (!ValidarToken(request.Token) && !_autenticacaoAplicacaoApp.AcessoConcedido(request.CNPJAplicacao, request.Token))
                    return TokenInvalido();

                var response = App.GestorReprovar(request.IdCargaAvulsa,null,false);

                return response.Success
                    ? Responde(new Retorno()
                    {
                        Sucesso = true,
                        Mensagem = "Operação realizada com sucesso!"
                    })
                    : Mensagem(response.Messages.FirstOrDefault());
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Mensagem(e.Message);
            }
        }
    }
}
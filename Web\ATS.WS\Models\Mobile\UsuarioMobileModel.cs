﻿namespace ATS.WS.Models.Mobile
{
    public class UsuarioMobileModel
    {
        public int IdUsuario                 { get; set; }
        public int? IdEmpresa          { get; set; }
        public string Nome                   { get; set; }
        public string Login                  { get; set; }
        public string CPF                    { get; set; }
        public int? IdGrupoUsuario           { get; set; }
        public string FotoBase64             { get; set; }
        public byte Perfil                   { get; set; }
        public bool ReceberNotificacao       { get; set; } = false;

        // Veiculo
        public string Placa                  { get; set; }
        public string Chassi                 { get; set; }
        public int? AnoFabricacao            { get; set; }
        public int? AnoModelo                { get; set; }
        public string RNTRC                  { get; set; }
        public string Marca                  { get; set; }
        public string Modelo                 { get; set; }
        public bool ComTracao                { get; set; }
        public int TipoRodagem               { get; set; }
        public string TecnologiaRastreamento { get; set; }
        public int IdTipoCavalo              { get; set; }
        public int IdTipoCarreta             { get; set; }

        // Filial
        public int IdFilial                  { get; set; }

        // Endereço
        public string CEP                    { get; set; }
        public string Endereco               { get; set; }
        public string Complemento            { get; set; }
        public int? Numero                   { get; set; }
        public string Bairro                 { get; set; }
        public int IdPais                    { get; set; }
        public int IdEstado                  { get; set; }
        public int IdCidade                  { get; set; }

        // Contato
        public string Telefone               { get; set; }
        public string Email                  { get; set; }
    }
}
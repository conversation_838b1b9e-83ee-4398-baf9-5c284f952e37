﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\build\Microsoft.Diagnostics.Tracing.TraceEvent.props" Condition="Exists('..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\build\Microsoft.Diagnostics.Tracing.TraceEvent.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9A3D3CF8-48DD-4623-B276-41C70B9C24BF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ATS.Data.Context</RootNamespace>
    <AssemblyName>ATS.Data.Context</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <LangVersion>9</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=8.2.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.8.2.0\lib\netstandard2.0\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=1.50.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.50.5\lib\net451\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="Dia2Lib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\lib\net45\Dia2Lib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.MappingAPI, Version=6.1.0.9, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.MappingAPI.6.1.0.9\lib\net45\EntityFramework.MappingAPI.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.2\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Diagnostics.FastSerialization, Version=1.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\lib\net45\Microsoft.Diagnostics.FastSerialization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Diagnostics.Tracing.TraceEvent, Version=2.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\lib\net45\Microsoft.Diagnostics.Tracing.TraceEvent.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OSExtensions, Version=1.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\lib\net45\OSExtensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks.Dataflow, Version=4.6.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\System.Threading.Tasks.Dataflow.4.9.0\lib\netstandard2.0\System.Threading.Tasks.Dataflow.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="TraceReloggerLib, Version=0.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\lib\net45\TraceReloggerLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="TrackerEnabledDbContext, Version=3.6.1.0, Culture=neutral, PublicKeyToken=4f92af0b908c4a0a, processorArchitecture=MSIL">
      <HintPath>..\packages\TrackerEnabledDbContext.3.6.1\lib\net45\TrackerEnabledDbContext.dll</HintPath>
    </Reference>
    <Reference Include="TrackerEnabledDbContext.Common, Version=3.6.1.0, Culture=neutral, PublicKeyToken=4f92af0b908c4a0a, processorArchitecture=MSIL">
      <HintPath>..\packages\TrackerEnabledDbContext.Common.3.6.1\lib\net45\TrackerEnabledDbContext.Common.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ATSContext.cs" />
    <Compile Include="Config\BaseDbContext.cs" />
    <Compile Include="ContextManager.cs" />
    <Compile Include="AtsContextMigrator.cs" />
    <Compile Include="Conventions\LowerCaseConvention.cs" />
    <Compile Include="Mapping\AdministradoraPlataformaMapping.cs" />
    <Compile Include="Mapping\AtendimentoPortadorMap.cs" />
    <Compile Include="Mapping\AtendimentoPortadorTramiteMap.cs" />
    <Compile Include="Mapping\AutenticacaoAplicacaoMap.cs" />
    <Compile Include="Mapping\AutorizacaoEmpresaMap.cs" />
    <Compile Include="Mapping\AvaliacaoPlanilhaGestorCargaAvulsaMap.cs" />
    <Compile Include="Mapping\BannerMap.cs" />
    <Compile Include="Mapping\BannerUsuarioMap.cs" />
    <Compile Include="Mapping\BlacklistIpMap.cs" />
    <Compile Include="Mapping\BloqueioCartaoTipoMap.cs" />
    <Compile Include="Mapping\BloqueioFinaceiroTipoMap.cs" />
    <Compile Include="Mapping\CampanhaMap.cs" />
    <Compile Include="Mapping\CampanhaRespostaMap.cs" />
    <Compile Include="Mapping\BloqueioOrigemTipoMap.cs" />
    <Compile Include="Mapping\CategoriaMap.cs" />
    <Compile Include="Mapping\CheckinResumoMap.cs" />
    <Compile Include="Mapping\DespesaUsuarioMap.cs" />
    <Compile Include="Mapping\EmpresaIndicadoresMap.cs" />
    <Compile Include="Mapping\FornecedorCnpjPedagioMap.cs" />
    <Compile Include="Mapping\GestorUsuarioMap.cs" />
    <Compile Include="Mapping\LimiteTransacaoPortadorMap.cs" />
    <Compile Include="Mapping\LocalizacaoUsuarioMap.cs" />
    <Compile Include="Mapping\PedagioRotaMap.cs" />
    <Compile Include="Mapping\PedagioRotaPontoMap.cs" />
    <Compile Include="Mapping\ConsumoServicoExternoMap.cs" />
    <Compile Include="Mapping\PlanoEmpresaMap.cs" />
    <Compile Include="Mapping\PlanoMap.cs" />
    <Compile Include="Mapping\PontosRotaModeloMap.cs" />
    <Compile Include="Mapping\PracasRotaModeloMap.cs" />
    <Compile Include="Mapping\PrestacaoContasEventoMap.cs" />
    <Compile Include="Mapping\PrestacaoContasMap.cs" />
    <Compile Include="Mapping\ProjetoFirebaseMapping.cs" />
    <Compile Include="Mapping\ResgateCartaoAtendimentoMap.cs" />
    <Compile Include="Mapping\ClienteEnderecoMap.cs" />
    <Compile Include="Mapping\ClienteProdutoEspecieMap.cs" />
    <Compile Include="Mapping\CombustivelJSLEstabelecimentoBaseMap.cs" />
    <Compile Include="Mapping\CombustivelJSLMap.cs" />
    <Compile Include="Mapping\ContratoCiotAgregadoMap.cs" />
    <Compile Include="Mapping\ContratoCiotAgregadoVeiculoMap.cs" />
    <Compile Include="Mapping\CteMap.cs" />
    <Compile Include="Mapping\BloqueioGestorTipoMap.cs" />
    <Compile Include="Mapping\BloqueioGestorValorMap.cs" />
    <Compile Include="Mapping\DeclaracaoCiotMap.cs" />
    <Compile Include="Mapping\EmpresaContaBancariaMap.cs" />
    <Compile Include="Mapping\CredenciamentoMotivoMap.cs" />
    <Compile Include="Mapping\EstabelecimentoBaseContaBancariaMap.cs" />
    <Compile Include="Mapping\EstabelecimentoBaseDocumentoMap.cs" />
    <Compile Include="Mapping\EstabelecimentoContaBancariaMap.cs" />
    <Compile Include="Mapping\MotivoMap.cs" />
    <Compile Include="Mapping\ParametrosMap.cs" />
    <Compile Include="Mapping\EstabelecimentoBaseAssociacaoMap.cs" />
    <Compile Include="Mapping\LogSmsMap.cs" />
    <Compile Include="Mapping\CargaAvulsaMap.cs" />
    <Compile Include="Mapping\ProdutoDadosCargaMap.cs" />
    <Compile Include="Mapping\ContratoMap.cs" />
    <Compile Include="Mapping\ConjuntoCarretaMap.cs" />
    <Compile Include="Mapping\ConjuntoCarretaEmpresaMap.cs" />
    <Compile Include="Mapping\ConjuntoEmpresaMap.cs" />
    <Compile Include="Mapping\LayoutCartaoItemMap.cs" />
    <Compile Include="Mapping\LayoutCartaoMap.cs" />
    <Compile Include="Mapping\PINMap.cs" />
    <Compile Include="Mapping\CredenciamentoAnexoMap.cs" />
    <Compile Include="Mapping\CredenciamentoMap.cs" />
    <Compile Include="Mapping\EstabelecimentoAssociacaoMap.cs" />
    <Compile Include="Mapping\EstabelecimentoBaseMap.cs" />
    <Compile Include="Mapping\ConfiguracaoProcessoMap.cs" />
    <Compile Include="Mapping\EstabelecimentoBaseProdutoMap.cs" />
    <Compile Include="Mapping\FilialContatosMap.cs" />
    <Compile Include="Mapping\RotaEstabelecimentoMap.cs" />
    <Compile Include="Mapping\RotaModeloMap.cs" />
    <Compile Include="Mapping\SerproCacheMap.cs" />
    <Compile Include="Mapping\SerproCacheResultadoMap.cs" />
    <Compile Include="Mapping\SolicitacaoChavePixEventoMap.cs" />
    <Compile Include="Mapping\SolicitacaoChavePixMap.cs" />
    <Compile Include="Mapping\SolicitacaoChavePixStatusMap.cs" />
    <Compile Include="Mapping\TagMap.cs" />
    <Compile Include="Mapping\TipoMotivoMap.cs" />
    <Compile Include="Mapping\TransacaoCartaoMap.cs" />
    <Compile Include="Mapping\TipoCavaloClienteMap.cs" />
    <Compile Include="Mapping\TransacaoPixMap.cs" />
    <Compile Include="Mapping\TransacaoPixStatusMap.cs" />
    <Compile Include="Mapping\UsoTipoEstabelecimentoMap.cs" />
    <Compile Include="Mapping\UsuarioDocumentoMap.cs" />
    <Compile Include="Mapping\TipoDocumentoMap.cs" />
    <Compile Include="Mapping\LayoutMap.cs" />
    <Compile Include="Mapping\EstabelecimentoProdutoMap.cs" />
    <Compile Include="Mapping\AuthSessionMap.cs" />
    <Compile Include="Mapping\Common\ContatoBaseMap.cs" />
    <Compile Include="Mapping\Common\VeiculoBaseMap.cs" />
    <Compile Include="Mapping\GruposUsuarioMap.cs" />
    <Compile Include="Mapping\IconeMap.cs" />
    <Compile Include="Mapping\MensagemDestinatarioMap.cs" />
    <Compile Include="Mapping\MensagemGrupoDestinatarioMap.cs" />
    <Compile Include="Mapping\MensagemGrupoUsuarioMap.cs" />
    <Compile Include="Mapping\ModuloMenuMap.cs" />
    <Compile Include="Mapping\NotificacaoPushGrupoUsuarioMap.cs" />
    <Compile Include="Mapping\NotificacaoPushItemMap.cs" />
    <Compile Include="Mapping\DocumentoMap.cs" />
    <Compile Include="Mapping\PagamentoConfiguracaoMap.cs" />
    <Compile Include="Mapping\ProdutoMap.cs" />
    <Compile Include="Mapping\RotaMap.cs" />
    <Compile Include="Mapping\RotaTrajetoMap.cs" />
    <Compile Include="Mapping\NotificacaoMap.cs" />
    <Compile Include="Mapping\ProprietarioContatoMap.cs" />
    <Compile Include="Mapping\ProprietarioEnderecoMap.cs" />
    <Compile Include="Mapping\EmpresaLayoutMap.cs" />
    <Compile Include="Mapping\NotificacaoPushMap.cs" />
    <Compile Include="Mapping\EstabelecimentoMap.cs" />
    <Compile Include="Mapping\TipoEstabelecimentoMap.cs" />
    <Compile Include="Mapping\TipoNotificacaoMap.cs" />
    <Compile Include="Mapping\UsuarioClienteMap.cs" />
    <Compile Include="Mapping\UsuarioContatoMap.cs" />
    <Compile Include="Mapping\UsuarioEstabelecimentoMap.cs" />
    <Compile Include="Mapping\ConjuntoMap.cs" />
    <Compile Include="Mapping\UsuarioHorarioCheckInMap.cs" />
    <Compile Include="Mapping\UsuarioLocalizacaoMap.cs" />
    <Compile Include="Mapping\UsuarioPermissaoCartaoMap.cs" />
    <Compile Include="Mapping\UsuarioPermissaoFinanceiroMap.cs" />
    <Compile Include="Mapping\UsuarioPermissaoGestorMap.cs" />
    <Compile Include="Mapping\UsuarioPermissoesConcedidasMobileMap.cs" />
    <Compile Include="Mapping\UsuarioPreferenciasMap.cs" />
    <Compile Include="Mapping\VeiculoDigitosMercosulMap.cs" />
    <Compile Include="Mapping\VeiculosHistoricoEmpresaMap.cs" />
    <Compile Include="Mapping\ViagemCarretaMap.cs" />
    <Compile Include="Mapping\ViagemCheckMap.cs" />
    <Compile Include="Mapping\Common\EnderecoBaseMap.cs" />
    <Compile Include="Mapping\EspecieMap.cs" />
    <Compile Include="Mapping\UsuarioEnderecoMap.cs" />
    <Compile Include="Mapping\ViagemCargaMap.cs" />
    <Compile Include="Mapping\ViagemDocumentoFiscalMap.cs" />
    <Compile Include="Mapping\ViagemDocumentoMap.cs" />
    <Compile Include="Mapping\ProtocoloAntecipacaoMap.cs" />
    <Compile Include="Mapping\ProtocoloAnexoMap.cs" />
    <Compile Include="Mapping\ProtocoloEventoMap.cs" />
    <Compile Include="Mapping\ProtocoloMap.cs" />
    <Compile Include="Mapping\ViagemPagamentoContaMap.cs" />
    <Compile Include="Mapping\ViagemPendenteGestorMap.cs" />
    <Compile Include="Mapping\ViagemRotaMap.cs" />
    <Compile Include="Mapping\ViagemRotaPontoMap.cs" />
    <Compile Include="Mapping\ViagemEventoProtocoloAnexoMap.cs" />
    <Compile Include="Mapping\ViagemSolicitacaoAbonoMap.cs" />
    <Compile Include="Mapping\ViagemValorAdicionalMap.cs" />
    <Compile Include="Mapping\ViagemEventoMap.cs" />
    <Compile Include="Mapping\ViagemEstabelecimentoMap.cs" />
    <Compile Include="Mapping\ViagemRegraMap.cs" />
    <Compile Include="Mapping\ViagemVirtualMap.cs" />
    <Compile Include="Mapping\WebHookMap.cs" />
    <Compile Include="Mapping\WhiteListIPMap.cs" />
    <Compile Include="Migrations\202007071128418_Versao1.cs" />
    <Compile Include="Migrations\202007071128418_Versao1.Designer.cs">
      <DependentUpon>202007071128418_Versao1.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202202251728218_AddCamposEmpresa.cs" />
    <Compile Include="Migrations\202202251728218_AddCamposEmpresa.Designer.cs">
      <DependentUpon>202202251728218_AddCamposEmpresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202203221256541_InsercaoCamposAbaFinanceiroUsuario.cs" />
    <Compile Include="Migrations\202203221256541_InsercaoCamposAbaFinanceiroUsuario.Designer.cs">
      <DependentUpon>202203221256541_InsercaoCamposAbaFinanceiroUsuario.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202204082002425_CriacaoTabelaFornecedorPedagioCnpj.cs" />
    <Compile Include="Migrations\202204082002425_CriacaoTabelaFornecedorPedagioCnpj.Designer.cs">
      <DependentUpon>202204082002425_CriacaoTabelaFornecedorPedagioCnpj.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202204181736476_FlagTagPedagioCriacaoDoCampo.cs" />
    <Compile Include="Migrations\202204181736476_FlagTagPedagioCriacaoDoCampo.Designer.cs">
      <DependentUpon>202204181736476_FlagTagPedagioCriacaoDoCampo.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202205161803140_InclusaoFlagEmpresaDespesaViagem.cs" />
    <Compile Include="Migrations\202205161803140_InclusaoFlagEmpresaDespesaViagem.Designer.cs">
      <DependentUpon>202205161803140_InclusaoFlagEmpresaDespesaViagem.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202205311343412_AdicaoCampoAcessoExtratoEmpresa.cs" />
    <Compile Include="Migrations\202205311343412_AdicaoCampoAcessoExtratoEmpresa.Designer.cs">
      <DependentUpon>202205311343412_AdicaoCampoAcessoExtratoEmpresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202206021441136_InclusaoCampoParcelaViagemAbertaEmpresa.cs" />
    <Compile Include="Migrations\202206021441136_InclusaoCampoParcelaViagemAbertaEmpresa.Designer.cs">
      <DependentUpon>202206021441136_InclusaoCampoParcelaViagemAbertaEmpresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202206151637359_InclusaoCampoIntegrarUsuarioEmpresa.cs" />
    <Compile Include="Migrations\202206151637359_InclusaoCampoIntegrarUsuarioEmpresa.Designer.cs">
      <DependentUpon>202206151637359_InclusaoCampoIntegrarUsuarioEmpresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202206172011420_InclusaoBloqueadoParcelaEmpresa.cs" />
    <Compile Include="Migrations\202206172011420_InclusaoBloqueadoParcelaEmpresa.Designer.cs">
      <DependentUpon>202206172011420_InclusaoBloqueadoParcelaEmpresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202206271229424_CriacaoTabelaRotaModelo.cs" />
    <Compile Include="Migrations\202206271229424_CriacaoTabelaRotaModelo.Designer.cs">
      <DependentUpon>202206271229424_CriacaoTabelaRotaModelo.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202207261723496_ViagemEventoCampoCartaoOrigemCartaoDestino.cs" />
    <Compile Include="Migrations\202207261723496_ViagemEventoCampoCartaoOrigemCartaoDestino.Designer.cs">
      <DependentUpon>202207261723496_ViagemEventoCampoCartaoOrigemCartaoDestino.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202208091943393_DadosBancariosPagamentoSemCartao.cs" />
    <Compile Include="Migrations\202208091943393_DadosBancariosPagamentoSemCartao.Designer.cs">
      <DependentUpon>202208091943393_DadosBancariosPagamentoSemCartao.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202208161707221_CampoTransferirEntreCartoes.cs" />
    <Compile Include="Migrations\202208161707221_CampoTransferirEntreCartoes.Designer.cs">
      <DependentUpon>202208161707221_CampoTransferirEntreCartoes.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202209142100333_MigrationDesvinculoCiot.cs" />
    <Compile Include="Migrations\202209142100333_MigrationDesvinculoCiot.Designer.cs">
      <DependentUpon>202209142100333_MigrationDesvinculoCiot.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202211041739242_Add_DesvinculoCiot.cs" />
    <Compile Include="Migrations\202211041739242_Add_DesvinculoCiot.Designer.cs">
      <DependentUpon>202211041739242_Add_DesvinculoCiot.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202211041743260_Add_Column_BloquearNovaViagem.cs" />
    <Compile Include="Migrations\202211041743260_Add_Column_BloquearNovaViagem.Designer.cs">
      <DependentUpon>202211041743260_Add_Column_BloquearNovaViagem.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202211281808530_AddParametroEmpresaCartaoVinculo.cs" />
    <Compile Include="Migrations\202211281808530_AddParametroEmpresaCartaoVinculo.Designer.cs">
      <DependentUpon>202211281808530_AddParametroEmpresaCartaoVinculo.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202301171802297_AddColumn_IdaEVolta.cs" />
    <Compile Include="Migrations\202301171802297_AddColumn_IdaEVolta.Designer.cs">
      <DependentUpon>202301171802297_AddColumn_IdaEVolta.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202302140150030_AlterProprietario_AddFormaPagamentoFretePadrao.cs" />
    <Compile Include="Migrations\202302140150030_AlterProprietario_AddFormaPagamentoFretePadrao.Designer.cs">
      <DependentUpon>202302140150030_AlterProprietario_AddFormaPagamentoFretePadrao.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202303170451390_AgendamentoPagamentoFrete.cs" />
    <Compile Include="Migrations\202303170451390_AgendamentoPagamentoFrete.Designer.cs">
      <DependentUpon>202303170451390_AgendamentoPagamentoFrete.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202303210239422_AlterEmpresa_AddWebhook.cs" />
    <Compile Include="Migrations\202303210239422_AlterEmpresa_AddWebhook.Designer.cs">
      <DependentUpon>202303210239422_AlterEmpresa_AddWebhook.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202302201744089_AddColumns_LatLng.cs" />
    <Compile Include="Migrations\202302201744089_AddColumns_LatLng.Designer.cs">
      <DependentUpon>202302201744089_AddColumns_LatLng.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202302202011025_AddColumns_LatLngDestino.cs" />
    <Compile Include="Migrations\202302202011025_AddColumns_LatLngDestino.Designer.cs">
      <DependentUpon>202302202011025_AddColumns_LatLngDestino.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202302281726576_Alter_Column_DestOrigemLatLng.cs" />
    <Compile Include="Migrations\202302281726576_Alter_Column_DestOrigemLatLng.Designer.cs">
      <DependentUpon>202302281726576_Alter_Column_DestOrigemLatLng.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202302281750017_Alter_Columns_LatLng_Pontos.cs" />
    <Compile Include="Migrations\202302281750017_Alter_Columns_LatLng_Pontos.Designer.cs">
      <DependentUpon>202302281750017_Alter_Columns_LatLng_Pontos.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202304261110303_test.cs" />
    <Compile Include="Migrations\202304261110303_test.Designer.cs">
      <DependentUpon>202304261110303_test.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202306052021136_RETIRED_REQUIRED_CAMPOS_VIAGEM.cs" />
    <Compile Include="Migrations\202306052021136_RETIRED_REQUIRED_CAMPOS_VIAGEM.Designer.cs">
      <DependentUpon>202306052021136_RETIRED_REQUIRED_CAMPOS_VIAGEM.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202306052213536_ADD_COLUMN_ISPEDAGIO_AVUSLO.cs" />
    <Compile Include="Migrations\202306052213536_ADD_COLUMN_ISPEDAGIO_AVUSLO.Designer.cs">
      <DependentUpon>202306052213536_ADD_COLUMN_ISPEDAGIO_AVUSLO.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202306271759321_Aumentado_TamanhoCampo_RotaModelo.cs" />
    <Compile Include="Migrations\202306271759321_Aumentado_TamanhoCampo_RotaModelo.Designer.cs">
      <DependentUpon>202306271759321_Aumentado_TamanhoCampo_RotaModelo.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202307172049204_Parametro_DesabilitaCacheRotas_Empresa.cs" />
    <Compile Include="Migrations\202307172049204_Parametro_DesabilitaCacheRotas_Empresa.Designer.cs">
      <DependentUpon>202307172049204_Parametro_DesabilitaCacheRotas_Empresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202310051949158_AddMotoristaIntegradoViagemEmpresa.cs" />
    <Compile Include="Migrations\202310051949158_AddMotoristaIntegradoViagemEmpresa.Designer.cs">
      <DependentUpon>202310051949158_AddMotoristaIntegradoViagemEmpresa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202310161949146_RollbackEmpresaMotoristaIntegradoViagem.cs" />
    <Compile Include="Migrations\202310161949146_RollbackEmpresaMotoristaIntegradoViagem.Designer.cs">
      <DependentUpon>202310161949146_RollbackEmpresaMotoristaIntegradoViagem.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202310181715467_AddEmpresaGerarCiotViagemInternacional.cs" />
    <Compile Include="Migrations\202310181715467_AddEmpresaGerarCiotViagemInternacional.Designer.cs">
      <DependentUpon>202310181715467_AddEmpresaGerarCiotViagemInternacional.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202311081816138_AddMensagemAntifraudeCargaAvulsa.cs" />
    <Compile Include="Migrations\202311081816138_AddMensagemAntifraudeCargaAvulsa.Designer.cs">
      <DependentUpon>202311081816138_AddMensagemAntifraudeCargaAvulsa.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202311092002377_AddWhiteListIP.cs" />
    <Compile Include="Migrations\202311092002377_AddWhiteListIP.Designer.cs">
      <DependentUpon>202311092002377_AddWhiteListIP.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202311221824176_TabelasExtrattaPay.cs" />
    <Compile Include="Migrations\202311221824176_TabelasExtrattaPay.Designer.cs">
      <DependentUpon>202311221824176_TabelasExtrattaPay.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\Seeders\LayoutCartaoSeeder.cs" />
    <Compile Include="Migrations\Seeders\EspecieSeeder.cs" />
    <Compile Include="Interface\IContextManager.cs" />
    <Compile Include="Interface\IDbContext.cs" />
    <Compile Include="Mapping\CheckInMap.cs" />
    <Compile Include="Mapping\CidadeMap.cs" />
    <Compile Include="Mapping\MensagemMap.cs" />
    <Compile Include="Mapping\ClienteAcessoMap.cs" />
    <Compile Include="Mapping\ClienteMap.cs" />
    <Compile Include="Mapping\EstadoMap.cs" />
    <Compile Include="Mapping\FilialMap.cs" />
    <Compile Include="Mapping\GrupoUsuarioMap.cs" />
    <Compile Include="Mapping\GrupoUsuarioMenuMap.cs" />
    <Compile Include="Mapping\MenuMap.cs" />
    <Compile Include="Mapping\ModuloMap.cs" />
    <Compile Include="Mapping\EmpresaModuloMap.cs" />
    <Compile Include="Mapping\MotoristaMap.cs" />
    <Compile Include="Mapping\MotoristaMovelMap.cs" />
    <Compile Include="Mapping\PaisMap.cs" />
    <Compile Include="Mapping\ProprietarioMap.cs" />
    <Compile Include="Mapping\TipoCarretaMap.cs" />
    <Compile Include="Mapping\TipoCavaloMap.cs" />
    <Compile Include="Mapping\TipoCombustivelMap.cs" />
    <Compile Include="Mapping\EmpresaMap.cs" />
    <Compile Include="Mapping\UsuarioFilialMap.cs" />
    <Compile Include="Mapping\UsuarioMap.cs" />
    <Compile Include="Mapping\VeiculoTipoCombustivelMap.cs" />
    <Compile Include="Mapping\VeiculoConjuntoMap.cs" />
    <Compile Include="Mapping\VeiculoMap.cs" />
    <Compile Include="Mapping\ViagemMap.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Migrations\Seeders\CidadeSeeder.cs" />
    <Compile Include="Migrations\Seeders\EstadoSeeder.cs" />
    <Compile Include="Migrations\Seeders\MenuSeeder.cs" />
    <Compile Include="Migrations\Seeders\ModuloSeeder.cs" />
    <Compile Include="Migrations\Seeders\LayoutSeeder.cs" />
    <Compile Include="Migrations\Seeders\SolicitacaoAcessoSeeder.cs" />
    <Compile Include="Migrations\Seeders\PaisSeeder.cs" />
    <Compile Include="Migrations\Seeders\TipoCarretaSeeder.cs" />
    <Compile Include="Migrations\Seeders\TipoDocumentoSeeder.cs" />
    <Compile Include="Migrations\Seeders\TipoCavaloSeeder.cs" />
    <Compile Include="Migrations\Seeders\TipoCombustivelSeeder.cs" />
    <Compile Include="Migrations\Seeders\TipoEatabelecimentoSeeder.cs" />
    <Compile Include="Migrations\Seeders\VeiculoDigitosMercosulSeeder.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Trigger\base\ITriggerContext.cs" />
    <Compile Include="Trigger\TriggerManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Business\ATS.Domain.csproj">
      <Project>{2810ACEC-3610-4AAD-8E20-D01E14466D87}</Project>
      <Name>ATS.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Data.Repository.External\ATS.Data.Repository.External.csproj">
      <Project>{8a62a98c-9d32-48ee-b100-0259b1fdf08a}</Project>
      <Name>ATS.Data.Repository.External</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sistema.Framework\Sistema.Framework.Util\Sistema.Framework.Util.csproj">
      <Project>{2a5da508-d09f-4dc8-b0d6-e21a6e1a7ae6}</Project>
      <Name>Sistema.Framework.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Migrations\202007071128418_Versao1.resx">
      <DependentUpon>202007071128418_Versao1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202202251728218_AddCamposEmpresa.resx">
      <DependentUpon>202202251728218_AddCamposEmpresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202203221256541_InsercaoCamposAbaFinanceiroUsuario.resx">
      <DependentUpon>202203221256541_InsercaoCamposAbaFinanceiroUsuario.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202204082002425_CriacaoTabelaFornecedorPedagioCnpj.resx">
      <DependentUpon>202204082002425_CriacaoTabelaFornecedorPedagioCnpj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202204181736476_FlagTagPedagioCriacaoDoCampo.resx">
      <DependentUpon>202204181736476_FlagTagPedagioCriacaoDoCampo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202205161803140_InclusaoFlagEmpresaDespesaViagem.resx">
      <DependentUpon>202205161803140_InclusaoFlagEmpresaDespesaViagem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202205311343412_AdicaoCampoAcessoExtratoEmpresa.resx">
      <DependentUpon>202205311343412_AdicaoCampoAcessoExtratoEmpresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202206021441136_InclusaoCampoParcelaViagemAbertaEmpresa.resx">
      <DependentUpon>202206021441136_InclusaoCampoParcelaViagemAbertaEmpresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202206151637359_InclusaoCampoIntegrarUsuarioEmpresa.resx">
      <DependentUpon>202206151637359_InclusaoCampoIntegrarUsuarioEmpresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202206172011420_InclusaoBloqueadoParcelaEmpresa.resx">
      <DependentUpon>202206172011420_InclusaoBloqueadoParcelaEmpresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202206271229424_CriacaoTabelaRotaModelo.resx">
      <DependentUpon>202206271229424_CriacaoTabelaRotaModelo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202207261723496_ViagemEventoCampoCartaoOrigemCartaoDestino.resx">
      <DependentUpon>202207261723496_ViagemEventoCampoCartaoOrigemCartaoDestino.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202208091943393_DadosBancariosPagamentoSemCartao.resx">
      <DependentUpon>202208091943393_DadosBancariosPagamentoSemCartao.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202208161707221_CampoTransferirEntreCartoes.resx">
      <DependentUpon>202208161707221_CampoTransferirEntreCartoes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202209142100333_MigrationDesvinculoCiot.resx">
      <DependentUpon>202209142100333_MigrationDesvinculoCiot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202211041739242_Add_DesvinculoCiot.resx">
      <DependentUpon>202211041739242_Add_DesvinculoCiot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202211041743260_Add_Column_BloquearNovaViagem.resx">
      <DependentUpon>202211041743260_Add_Column_BloquearNovaViagem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202211281808530_AddParametroEmpresaCartaoVinculo.resx">
      <DependentUpon>202211281808530_AddParametroEmpresaCartaoVinculo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202301171802297_AddColumn_IdaEVolta.resx">
      <DependentUpon>202301171802297_AddColumn_IdaEVolta.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202302140150030_AlterProprietario_AddFormaPagamentoFretePadrao.resx">
      <DependentUpon>202302140150030_AlterProprietario_AddFormaPagamentoFretePadrao.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202302201744089_AddColumns_LatLng.resx">
      <DependentUpon>202302201744089_AddColumns_LatLng.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202302202011025_AddColumns_LatLngDestino.resx">
      <DependentUpon>202302202011025_AddColumns_LatLngDestino.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202302281726576_Alter_Column_DestOrigemLatLng.resx">
      <DependentUpon>202302281726576_Alter_Column_DestOrigemLatLng.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202302281750017_Alter_Columns_LatLng_Pontos.resx">
      <DependentUpon>202302281750017_Alter_Columns_LatLng_Pontos.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202303170451390_AgendamentoPagamentoFrete.resx">
      <DependentUpon>202303170451390_AgendamentoPagamentoFrete.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202303210239422_AlterEmpresa_AddWebhook.resx">
      <DependentUpon>202303210239422_AlterEmpresa_AddWebhook.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202304261110303_test.resx">
      <DependentUpon>202304261110303_test.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202306052021136_RETIRED_REQUIRED_CAMPOS_VIAGEM.resx">
      <DependentUpon>202306052021136_RETIRED_REQUIRED_CAMPOS_VIAGEM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202306052213536_ADD_COLUMN_ISPEDAGIO_AVUSLO.resx">
      <DependentUpon>202306052213536_ADD_COLUMN_ISPEDAGIO_AVUSLO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202306271759321_Aumentado_TamanhoCampo_RotaModelo.resx">
      <DependentUpon>202306271759321_Aumentado_TamanhoCampo_RotaModelo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202307172049204_Parametro_DesabilitaCacheRotas_Empresa.resx">
      <DependentUpon>202307172049204_Parametro_DesabilitaCacheRotas_Empresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202310051949158_AddMotoristaIntegradoViagemEmpresa.resx">
      <DependentUpon>202310051949158_AddMotoristaIntegradoViagemEmpresa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202310161949146_RollbackEmpresaMotoristaIntegradoViagem.resx">
      <DependentUpon>202310161949146_RollbackEmpresaMotoristaIntegradoViagem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202310181715467_AddEmpresaGerarCiotViagemInternacional.resx">
      <DependentUpon>202310181715467_AddEmpresaGerarCiotViagemInternacional.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202311081816138_AddMensagemAntifraudeCargaAvulsa.resx">
      <DependentUpon>202311081816138_AddMensagemAntifraudeCargaAvulsa.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202311092002377_AddWhiteListIP.resx">
      <DependentUpon>202311092002377_AddWhiteListIP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202311221824176_TabelasExtrattaPay.resx">
      <DependentUpon>202311221824176_TabelasExtrattaPay.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105.The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\build\Microsoft.Diagnostics.Tracing.TraceEvent.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Diagnostics.Tracing.TraceEvent.2.0.2\build\Microsoft.Diagnostics.Tracing.TraceEvent.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
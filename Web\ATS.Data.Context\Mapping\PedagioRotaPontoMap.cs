using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class PedagioRotaPontoMap : EntityTypeConfiguration<PedagioRotaPonto>
    {
        public PedagioRotaPontoMap()
        {
            ToTable("PEDAGIO_ROTA_PONTO");
            HasKey(c => new {c.IdPedagioRota, c.Sequencia});

            Property(c => c.Latitude);
            Property(c => c.Longitude);
            Ignore(t => t.UsuarioCadastro);
            Ignore(t => t.UsuarioAtualizacao);

            HasOptional(c => c.Cidade)
                .WithMany()
                .HasForeignKey(c => c.IdCidade);

            HasRequired(c => c.PedagioRota)
                .WithMany(c => c.Pontos)
                .HasForeignKey(c => c.IdPedagioRota);
        }
    }
}
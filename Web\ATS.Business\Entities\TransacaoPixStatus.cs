﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class TransacaoPixStatus
    {
        public ETransacaoPixStatus IdTransacaoPixStatus { get; set; }
        
        public string Descricao { get; set; }
    }
}
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2VeiculoModel
    {
        /// <summary>
        /// Registro nacional de transpordes rodoviários de cargas
        /// </summary>
        public int? Rntrc { get; set; }

        /// <summary>
        /// Placa do veículo
        /// </summary>
        public string Placa { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (string.IsNullOrEmpty(Placa))
                return new ValidationResult().Add("Placa não informada.", EFaultType.Error);
            
            if (Placa.Length > 7)
                return new ValidationResult().Add("Placa não pode conter maias que 8 caracteres.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
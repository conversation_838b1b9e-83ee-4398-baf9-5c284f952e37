using System;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO.Campanha;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;
using AutoMapper.QueryableExtensions;

namespace ATS.Domain.Service
{
    public class CampanhaService : ServiceBase, ICampanhaService
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ICampanhaRepository _campanhaRepository;
        private readonly ICampanhaRespostaRepository _campanhaRespostaRepository;

        public CampanhaService(ICampanhaRespostaRepository campanhaRespostaRepository,
            ICampanhaRepository campanhaRepository, IUserIdentity userIdentity)
        {
            _campanhaRespostaRepository = campanhaRespostaRepository;
            _campanhaRepository = campanhaRepository;
            _userIdentity = userIdentity;
        }

        public CampanhaConsultaResponse ConsultarAtual()
        {
            var campanhaAtual = _campanhaRepository.GetAtual().ProjectTo<CampanhaConsultaResponse>().FirstOrDefault();

            if (campanhaAtual == null) return null;

            var respondida = _campanhaRespostaRepository.ExisteResposta(campanhaAtual.Id, _userIdentity.IdUsuario);

            if (respondida) return null;

            return campanhaAtual;
        }
        
        public CampanhaRespostaResponse Responder(ResponderCampanhaRequest request)
        {
            var campanhaAtual = _campanhaRepository.GetAtual().ProjectTo<CampanhaConsultaResponse>().FirstOrDefault();

            if (campanhaAtual == null)
                throw new Exception("Nenhuma campanha em andamento.");

            var respondida = _campanhaRespostaRepository.ExisteResposta(campanhaAtual.Id, _userIdentity.IdUsuario);

            if (respondida)
                throw new Exception("Campanha já respondida.");
            
            var resposta = Mapper.Map<CampanhaResposta>(request);
            
            resposta.IdCampanha = campanhaAtual.Id;
            resposta.IdUsuario = _userIdentity.IdUsuario;
            resposta.IdEmpresa = _userIdentity.IdEmpresa;
            resposta.DataCadastro = DateTime.Now;
            if (resposta.Respondida) resposta.DataResposta = DateTime.Now;

            _campanhaRespostaRepository.Add(resposta);
            _campanhaRespostaRepository.SaveChanges();
            
            //Se o usuario marcou o não perguntar novamente retorna sucesso false pro front
            if(!resposta.Respondida) 
                throw new Exception("Resposta não informada.");
            
            return Mapper.Map<CampanhaRespostaResponse>(resposta);
        }
        
        #region Administração
        
        public CampanhaGridResponse ConsultarCampanhas()
        {
            var campanhas = _campanhaRepository.GetAll().ProjectTo<CampanhaConsultaResponse>().ToList();

            var retorno = new CampanhaGridResponse()
            {
                totalItems = campanhas.Count,
                items = campanhas
            };
            
            return retorno;
        }

        public ValidationResult Integrar(IntegrarCampanhaRequest request)
        {
            var validation = new ValidationResult();

            #region Cadastro
            
            if (request.Id == null)
            {
                var campanhaAtual = _campanhaRepository.GetAtual().ProjectTo<CampanhaConsultaResponse>().FirstOrDefault();

                if (campanhaAtual != null)
                    return validation.Add($"Já existe uma campanha em andamento (Id {campanhaAtual.Id}). Desative-a primeiro.");

                var campanhaNova = Mapper.Map<Campanha>(request);

                campanhaNova.Ativa = request.Ativar;
                campanhaNova.IdUsuarioCadastro = _userIdentity.IdUsuario;
                campanhaNova.DataCadastro = DateTime.Now;

                _campanhaRepository.Add(campanhaNova);
                _campanhaRepository.SaveChanges();

                return validation;
            }
            
            #endregion
            
            #region Edicao
            
            var campanha = _campanhaRepository.GetById(request.Id.Value).FirstOrDefault();

            if (campanha == null)
                return validation.Add("Campanha não encontrada.");

            campanha.Ativa = request.Ativar;
            campanha.DataInicio = request.DataInicio;
            campanha.DataFim = request.DataFim;
            campanha.Descricao = request.Descricao;

            if (campanha.Ativa)
            {
                campanha.IdUsuarioDesativacao = _userIdentity.IdUsuario;
                campanha.DataDesativacao = DateTime.Now;
                campanha.Ativa = false;
            }
            else
            {
                campanha.IdUsuarioAtivacao = _userIdentity.IdUsuario;
                campanha.DataAtivacao = DateTime.Now;
                campanha.Ativa = true;
            }
            
            _campanhaRepository.SaveChanges();

            return validation;
            
            #endregion
        }

        public AlterarStatusCampanhaResponse AlterarStatus(AlterarStatusCampanhaRequest request)
        {
            var campanha = _campanhaRepository.GetById(request.Id).FirstOrDefault();

            if (campanha == null)
                return new AlterarStatusCampanhaResponse(false, "Campanha não encontrada.");

            var campanhaAtual = _campanhaRepository.GetAtual().ProjectTo<CampanhaConsultaResponse>().FirstOrDefault();

            if (campanhaAtual != null && campanhaAtual.Id != campanha.Id)
                return new AlterarStatusCampanhaResponse(false,
                    $"Já existe uma campanha em andamento (Id {campanhaAtual.Id}). Desative-a primeiro.");

            if (campanha.Ativa)
            {
                campanha.IdUsuarioDesativacao = _userIdentity.IdUsuario;
                campanha.DataDesativacao = DateTime.Now;
                campanha.Ativa = false;
            }
            else
            {
                campanha.IdUsuarioAtivacao = _userIdentity.IdUsuario;
                campanha.DataAtivacao = DateTime.Now;
                campanha.Ativa = true;
            }

            _campanhaRepository.SaveChanges();

            return new AlterarStatusCampanhaResponse(true,
                $"Campanha {(campanha.Ativa ? "ativada" : "desativada")} com sucesso.");
        }
        
        #endregion
    }
}
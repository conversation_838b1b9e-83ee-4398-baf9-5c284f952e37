﻿using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.WS.Models.Common
{
    public enum CompraStatusTipo
    {
        /// <summary>
        /// Compra de pedágio pendente
        /// </summary>
        ///
        [EnumMember, Description("Pendente")]
        Pendente = 0,

        /// <summary>
        /// Cartão de compra de pedágio carregado
        /// </summary>
        [EnumMember, Description("Confirmado")]
        Confirmado = 1,

        /// <summary>
        /// Compra de pedágio cancelada.
        /// </summary>
        [EnumMember, Description("Cancelado")]
        Cancelado = 2,

        /// <summary>
        /// Compra de pedágio cancelada.
        /// </summary>
        [EnumMember, Description("Pendente de cancelamento")]
        PendenteCancelamento = 3,

        /// <summary>
        /// Será utilizado quando expirar ou quando for cancelado o moedeiro antes de ser carregado.
        /// </summary>
        [EnumMember, Description("Bloqueado")]
        Bloqueado = 4


    }
}

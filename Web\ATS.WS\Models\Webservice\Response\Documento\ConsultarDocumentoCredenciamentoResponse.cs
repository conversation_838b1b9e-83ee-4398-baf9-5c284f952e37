using System;
using Newtonsoft.Json;

namespace ATS.WS.Models.Webservice.Response.Documento
{
    public class ConsultarDocumentoCredenciamentoResponse
    {
        public int? IdDocumento { get; set; }
        public string Descricao { get; set; }
        public bool Obrigatorio { get; set; }
        public bool PermiteEditarData { get; set; }
        public string DataValidade { get; set; }
        public bool Anexou { get; set; } = false;
        [JsonIgnore]
        public int? DiasValidade { get; set; }
    }
}
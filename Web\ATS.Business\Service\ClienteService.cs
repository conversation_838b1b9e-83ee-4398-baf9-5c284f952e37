﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.Reports.Clientes.RelatorioListaClientes;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Models.Clientes;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    public class ClienteService : BaseService<IClienteRepository>, IClienteService
    {
        private readonly IEmpresaRepository _empresaRepository;

        public ClienteService(IClienteRepository repository, IUserIdentity sessionUser, IEmpresaRepository empresaRepository) 
            : base(repository, sessionUser)
        {
            _empresaRepository = empresaRepository;
        }

        public Cliente GetChilds(int id)
        {
            return Repository
                    .Find(c => c.IdCliente == id)
                    .Include(c => c.Empresa)
                    .Include(c => c.Pais)
                    .Include(c => c.Estado)
                    .Include(c => c.Cidade)
                    .Include(c => c.Acessos)
                    .Include(c => c.ClienteEnderecos)
                    .Include(c => c.ClienteProdutoEspecie)
                    .FirstOrDefault();
        }

        public IQueryable<Cliente> All()
        {
            return Repository.All().Where(x => x.Ativo);
        }

        public IQueryable<Cliente> GetAll()
        {
            return Repository.GetAll().Where(x => x.Ativo);
        }

        public ValidationResult Add(Cliente cliente)
        {
            try
            {
                ConfigurarValores(cliente);
                var validationResult = IsValidCrud(cliente, EProcesso.Create);
                
                if (!validationResult.IsValid)
                    return validationResult;
                
                cliente.DataAtualizacao = DateTime.Now;
                Repository.Add(cliente);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Update(Cliente cliente)
        {
            try
            {
                ConfigurarValores(cliente);
                var validationResult = IsValidCrud(cliente, EProcesso.Update);
                
                if (!validationResult.IsValid)
                    return validationResult;

                Repository.Update(cliente);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Inativar(int idCliente)
        {
            try
            {
                var cliente = Get(idCliente);
                
                if(SessionUser.Perfil != (int)EPerfil.Administrador && cliente.IdEmpresa != SessionUser.IdEmpresa)
                    return new ValidationResult().Add("Usuário não autenticado.");
                
                if (!cliente.Ativo)
                    return new ValidationResult().Add("Cliente já desativado na base de dados.");

                cliente.Ativo = false;

                Repository.Update(cliente);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }
        }

        public ValidationResult Reativar(int idCliente)
        {
            try
            {
                var cliente = Get(idCliente);

                if (cliente.Ativo)
                    return new ValidationResult().Add("Cliente já ativado na base de dados.");

                if(SessionUser.Perfil != (int)EPerfil.Administrador && cliente.IdEmpresa != SessionUser.IdEmpresa)
                    return new ValidationResult().Add("Usuário não autenticado.");

                cliente.Ativo = true;

                Repository.Update(cliente);
                return new ValidationResult();
            }
            catch (Exception ex)
            {
                return new ValidationResult().Add(ex.Message);
            }
        }

        public Cliente Get(int id)
        {
            return Repository
                .Find(x => x.IdCliente == id)
                .Include(x => x.TipoCavaloCliente)
                .Include(x => x.ClienteEnderecos)
                .FirstOrDefault();
        }

        public bool ClienteValidoIntegracao(int? id, int? idempresa)
        {
            if (!id.HasValue)
                return false;
            
            return Repository.Where(c => c.IdCliente == id && c.IdEmpresa == idempresa).Any();
        }

        public int? GetIdPorCpfCnpj(string cpfcnpj, int idEmpresa)
        {
            var cpfcnpjOnlyNumbers =!string.IsNullOrEmpty(cpfcnpj) ? cpfcnpj.OnlyNumbers() : string.Empty;
            return Repository.Find(x => x.CNPJCPF == cpfcnpjOnlyNumbers && x.IdEmpresa == idEmpresa)?.FirstOrDefault()?.IdCliente;
        }

        public string GetCnpjCpf(int idCliente)
        {
            return Repository.Find(e => e.IdCliente == idCliente).Select(e => e.CNPJCPF).FirstOrDefault().OnlyNumbers();
        }

        public IQueryable<Cliente> GetClientesPorEmpresa(int idEmpresa)
        {
            return Repository.Find(x => x.IdEmpresa == idEmpresa && x.Ativo);
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var clientes = GetDataToGridAndReport(null, order, filters);

            return new
            {
                totalItems = clientes.Count(),
                items = clientes.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdCliente,
                    x.RazaoSocial,
                    EmpresaRazaoSocial = x.Empresa?.RazaoSocial,
                    CNPJ = x.CNPJCPF.Length == 11 ? x.CNPJCPF.ToCPFFormato() : x.CNPJCPF.ToCNPJFormato(),
                    Cidade = x.Cidade.Nome,
                    RazaoSocialComDocumento = (x.CNPJCPF.Length == 11 ? x.CNPJCPF.ToCPFFormato() : x.CNPJCPF.ToCNPJFormato() + " " + x.RazaoSocial),
                    x.Ativo,
                    CNPJCPF = x.CNPJCPF.Length == 11 ? x.CNPJCPF.ToCPFFormato() : x.CNPJCPF.ToCNPJFormato()
                })
            };
        }

        public byte[] GerarRelatorioGridClientes(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string logo, string extensao)
        {
            var listaDados = new List<RelatorioClientesDataType>();
            var clientes = GetDataToGridAndReport(idEmpresa, order, filters);

            foreach (var cliente in clientes)
            {
                listaDados.Add(new RelatorioClientesDataType
                {
                    IdCliente = cliente.IdCliente.ToString(),
                    Celular = cliente.Celular?.ToTelefoneFormato(),
                    CpfCnpj = cliente.CNPJCPF?.ToCpfOrCnpj(),
                    Empresa = cliente.Empresa?.RazaoSocial,
                    RazaoSocial = cliente.RazaoSocial
                });
            }

            return new RelatorioClientes().GetReport(listaDados, extensao, logo);
        }

        private IQueryable<Cliente> GetDataToGridAndReport(int? idEmpresa, OrderFilters order, List<QueryFilters> filters)
        {
            var clientes = Repository.GetAll()
                .Include(x => x.Cidade)
                .Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                clientes = clientes.Where(o => o.IdEmpresa == idEmpresa);

            clientes = string.IsNullOrWhiteSpace(order?.Campo)
                ? clientes.OrderBy(x => x.IdCliente)
                : clientes.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            clientes = clientes.AplicarFiltrosDinamicos(filters);

            return clientes;
        }

        public ClienteDetalhesModel ConsultarDetalhes(int idCliente)
        {
            var registro = Repository.Where(c => c.IdCliente == idCliente).Select(c => new
            {
                c.IdCliente,
                c.CNPJCPF,
                c.RazaoSocial,
                c.NomeFantasia,
                c.Email,
                c.Ativo,
                c.CEP,
                c.IdEmpresa
            }).FirstOrDefault();
            
            if (registro == null)
                return new ClienteDetalhesModel();
            
            if(SessionUser.Perfil != (int)EPerfil.Administrador && registro.IdEmpresa != SessionUser.IdEmpresa)
                throw new Exception("Cliente não vinculado à empresa do usuário.");
            
            return new ClienteDetalhesModel
            {
                IdCliente = registro.IdCliente,
                RazaoSocial = registro.RazaoSocial,
                NomeFantasia = registro.NomeFantasia,
                Email = registro.Email,
                Ativo = registro.Ativo,
                CpfCnpj = registro.CNPJCPF.ToCpfOrCnpj(),
                Cep = registro.CEP
            };
        }

        public bool VerificarClienteCadastrado(int idCliente)
        {
            var cliente = Repository.Find(o => o.IdCliente == idCliente)
                .FirstOrDefault();

            return cliente != null;
        }
        
        public IQueryable<Cliente> GetQuery(int idCliente)
        {
            return Repository.Where(c => c.IdCliente == idCliente);
        }
        
        private ValidationResult IsValidCrud(Cliente cliente, EProcesso processo)
        {
            var validationResult = new ValidationResult();
            var empresaService = _empresaRepository;
            var emailEmpresa = empresaService.Get(cliente.IdEmpresa).Email;
            var celularEmpresa = empresaService.Get(cliente.IdEmpresa).Telefone;
            
            validationResult.Add(cliente.TipoPessoa == ETipoPessoa.Fisica
                ? AssertionConcern.AssertArgumentIsValidCPF(cliente.CNPJCPF?.OnlyNumbers(), "CPF inválido.")
                : AssertionConcern.AssertArgumentIsValidCNPJ(cliente.CNPJCPF?.OnlyNumbers(), "CNPJ inválido."));

            validationResult.Add(AssertionConcern.AssertArgumentIsValidCEP(cliente.CEP, @"CEP deve ser válido"));

            if (!string.IsNullOrWhiteSpace(cliente.Email) && cliente.Email.Length > 100)
            {
                if (!cliente.Email.Contains(";"))
                {
                    cliente.Email = string.IsNullOrWhiteSpace(emailEmpresa) ? "<EMAIL>" : emailEmpresa;
                }
                else
                {
                    cliente.Email = cliente.Email.Substring(0, cliente.Email.IndexOf(';'));
                    if (cliente.Email.Length > 100)
                    {
                        cliente.Email = string.IsNullOrWhiteSpace(emailEmpresa) ? "<EMAIL>" : emailEmpresa;
                    }
                }
            }
            
            if (!string.IsNullOrWhiteSpace(cliente.Email))
            {
                if (AssertionConcern.AssertArgumentIsValidEmail(cliente.Email, @"E-mail deve ser válido") != null)
                {
                    cliente.Email = string.IsNullOrWhiteSpace(cliente.Email) ? "<EMAIL>" : emailEmpresa;
                }
            }
            
            if (string.IsNullOrWhiteSpace(cliente.Email))
            {
                cliente.Email = string.IsNullOrWhiteSpace(emailEmpresa) ? "<EMAIL>" : emailEmpresa;
            }
            
            if (!string.IsNullOrWhiteSpace(cliente.Celular)){
                if (AssertionConcern.AssertArgumentIsValidTelefone(cliente.Celular,@"Celular deve ser válido" ) != null)
                {
                    cliente.Celular = string.IsNullOrEmpty(celularEmpresa) ? "999999999" : celularEmpresa;
                }
            }
            if (string.IsNullOrWhiteSpace(cliente.Celular)){
                cliente.Celular = string.IsNullOrEmpty(celularEmpresa) ? "999999999" : celularEmpresa;
            }

            validationResult.Add(AssertionConcern.AssertArgumentIsValidLatitude(cliente.Latitude, @"Latitude deve ser válido"));
            validationResult.Add(AssertionConcern.AssertArgumentIsValidLongitude(cliente.Longitude, @"Longitude deve ser válido"));

            #region Unicidade do registro

            var cpfcnpj = cliente.CNPJCPF?.OnlyNumbers();

            var numCnpj = Repository.Find(t => t.CNPJCPF == cpfcnpj && t.IdEmpresa == cliente.IdEmpresa).Count();
            
            if (processo == EProcesso.Create && numCnpj >= 1)
                validationResult.Add(string.Concat(cliente.TipoPessoa == ETipoPessoa.Juridica ? "CNPJ" : "CPF", " informado já cadastrado para outro cliente"));

            if (processo == EProcesso.Update && cpfcnpj != GetCnpjCpf(cliente.IdCliente))
                validationResult.Add(string.Concat(cliente.TipoPessoa == ETipoPessoa.Juridica ? "CNPJ" : "CPF", " não pode ser alterado"));

            #endregion

            return validationResult;
        }
        
        private static void ConfigurarValores(Cliente cliente)
        {
            cliente.CNPJCPF = cliente.CNPJCPF.OnlyNumbers();
            cliente.Celular = cliente.Celular?.OnlyNumbers();
            cliente.CEP = cliente.CEP?.OnlyNumbers();
        }
    }
}
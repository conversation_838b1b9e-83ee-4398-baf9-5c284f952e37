﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.ProvisaoPagamento
{
    public class RelatorioProvisaoPagamento
    {
        public byte[] GetReport(List<RelatorioProvisaoPagamentoDataType> listaDados, string tipoArquivo, string logo, string titulo, string tituloData, bool mostrarEstabelecimento)
        {
            var parametros = new Tuple<string, string, bool>[4];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);
            parametros[1] = new Tuple<string, string, bool>("Titulo", titulo, true);
            parametros[2] = new Tuple<string, string, bool>("TituloData", tituloData, true);
            parametros[3] = new Tuple<string, string, bool>("MostrarEstabelecimento", "", true);

            var file = mostrarEstabelecimento
                ? "ATS.CrossCutting.Reports.ProvisaoPagamento.RelatorioProvisaoPagamentoBaixado.rdlc"
                : "ATS.CrossCutting.Reports.ProvisaoPagamento.RelatorioProvisaoPagamento.rdlc";

            var bytes = new Base.Reports().GetReport(listaDados, parametros, true, "DtsProvisaoPagamento", file,
                tipoArquivo);

            return bytes;
        }
    }
}

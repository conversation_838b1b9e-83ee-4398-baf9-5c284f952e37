﻿using System.Net.Http;

// ReSharper disable once CheckNamespace
namespace SistemaInfo.MicroServices.Rest.Cartao.WebClient
{
    public partial class AcessoPortadorClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class AtualizarClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class AuthClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class Client
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class CartoesClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class DashboardClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class EmpresasClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class ProcessadoraClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class TipoTransacaoClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }
}

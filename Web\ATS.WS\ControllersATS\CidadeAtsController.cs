﻿using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice;
using ATS.WS.Models.Webservice.Request;
using ATS.WS.Services;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.ControllersATS
{
    public class CidadeAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ICidadeApp _cidadeApp;
        private readonly WebServiceHelperWs _webServiceHelperWs;
        private readonly SrvCidade _srvCidade;

        public CidadeAtsController(IUserIdentity userIdentity, ICidadeApp cidadeApp, WebServiceHelperWs webServiceHelperWs, SrvCidade srvCidade)
        {
            _userIdentity = userIdentity;
            _cidadeApp = cidadeApp;
            _webServiceHelperWs = webServiceHelperWs;
            _srvCidade = srvCidade;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var cidades = _cidadeApp.ConsultaGrid(take, page, order, filters);

                return ResponderSucesso(cidades);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idCidade)
        {
            try
            {
                _cidadeApp.Inativar(idCidade);

                return ResponderSucesso("Cidade inativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idCidade)
        {
            try
            {
                _cidadeApp.Reativar(idCidade);

                return ResponderSucesso("Cidade reativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int? idEstado)
        {
            try
            {
                var cidades = idEstado.HasValue ? _cidadeApp.GetListaCidade(idEstado.Value).Select(x => new { x.IdCidade, x.Nome }) : null;
                //var cidades = _cidadeApp
                //    .Consultar()
                //    .Where(x => x.IdEstado == idEstado)
                //    .OrderBy(x => x.Nome)
                //    .Select(x => new { x.IdCidade, x.Nome});

                return ResponderSucesso(cidades);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ConsultarPorEstado(int idEstado)
        {
            try
            {
                var cidades = _cidadeApp.GetListaCidade(idEstado)
                    .OrderBy(x => x.Nome)
                    .Select(x => new { Codigo = x.IdCidade, Descricao = x.Nome });

                return ResponderSucesso(cidades);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetIdPorDescricao(string cidade)
        {
            try
            {
                var ret = _cidadeApp.WhereNomeLike(cidade).FirstOrDefault();

                return ResponderSucesso(ret?.IdCidade);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(CadastroCidadeRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");


                var validationResult = new ValidationResult();

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    validationResult.Add("Usuário não possui permissão para editar este registro.");

                var cidade = _cidadeApp.Get(@params.IdCidade);
                if (cidade == null)
                    validationResult.Add("Não foi possível localizar o estado.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                ReflectionHelper.CopyProperties(@params, cidade);
                validationResult = _cidadeApp.Update(cidade);


                if (validationResult.IsValid)
                    return ResponderPadrao(true, "Dados incluídos com sucesso.");

                return ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(CadastroCidadeRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro($"Dados da requisição em formato incorreto.");
                
                var validationResult = new ValidationResult();

                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    validationResult.Add("Usuário não possui permissão para editar este registro.");

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                var retorno = _srvCidade.Cadastrar(@params);
                
                if (retorno.IsValid)
                    return ResponderPadrao(true, "Dados incluídos com sucesso.");

                return ResponderErro(retorno.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idCidade)
        {
            try
            {
                Cidade cidade = _cidadeApp.Get(idCidade);

                var retorno = new
                {
                    cidade.IdCidade,
                    cidade.Nome,
                    cidade.IBGE,
                    cidade.IdEstado,
                    cidade.Estado?.IdPais,
                    cidade.Latitude,
                    cidade.Longitude
                };
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetPosicionamento(int idCidade)
        {
            try
            {
                var cidade = _cidadeApp.Get(idCidade);

                var retorno = new
                {
                    cidade?.Latitude, cidade?.Longitude
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTodas()
        {
            try
            {
                var cidades = _cidadeApp.GetTodos().Select(o => new
                {
                    Codigo = o.IdCidade,
                    Descricao = o.Nome
                });

                return ResponderSucesso(cidades);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetDadosRota(GetDadosDaRotaRequest @params)
        {
            try
            {

                var dadosRota = _webServiceHelperWs.GetDadosRota(@params.EnderecoCidadeOrigem, @params.IdCidadeOrigem, @params.EnderecoCidadeDestino, @params.IdCidadeDestino);

                return ResponderSucesso(new Retorno<GoogleMatrixRespose>(true, dadosRota));

            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetDetalhes(int idCidade)
        {
            try
            {
                var dados = _cidadeApp.GetDetalhes(idCidade);
                
                return ResponderSucesso("Operação realizada com sucesso", dados);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
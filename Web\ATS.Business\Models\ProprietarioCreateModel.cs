﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models.Parametro;

namespace ATS.Domain.Models
{
    public class ProprietarioCreateModel
    {
        public int IdProprietario { get; set; }

        public int IdEmpresa { get; set; }

        public string CNPJCPF { get; set; }

        public string RazaoSocial { get; set; }

        public string NomeFantasia { get; set; }

        public string RG { get; set; }

        public string RGOrgaoExpedidor { get; set; }

        public string IE { get; set; }

        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Integrado;

        public bool Ativo { get; set; } = true;

        public string RNTRC { get; set; }

        public string RazaoSocialEmpresa { get; set; }

        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Terceiro;

        public List<ProprietarioContatoCreateModel> Contatos { get; set; }

        public List<ProprietarioEnderecoCreateModel> Enderecos { get; set; }

        public DateTime? DataNascimento { get; set; }

        public string Inss { get; set; }

        public string Referencia1 { get; set; }

        public string Referencia2 { get; set; }

        public ETipoCarregamentoFrete? TipoCarregamentoFrete { get; set; }

        public decimal? PercentualTransferenciaMotorista { get; set; }

        public string DataMascimentoStr { get; set; }

        public List<ProprietarioParametrosTransferencia> ParametrosTransferencia { get; set; }
        
        public AcaoSaldoResidualNovoCreditoCartaoPedagio AcaoSaldoResidualNovoCreditoCartaoPedagio { get; set; }
        public bool HabilitarContratoCiotAgregado { get; set; }
        public bool TransferirEntreCartoes { get; set; }
        public string NomePai { get; set; }
        public string NomeMae { get; set; }
        public bool? PermiteReceberPagamentoPix { get; set; }
        public DadosBancariosPixModel DadosBancariosPix { get; set; }
    }
    
    public class ProprietarioParametrosTransferencia
    {
        public string Chave { get; set; }
        public string Texto { get; set; }
        public decimal Valor { get; set; }
    }

    public class ProprietarioContatoCreateModel
    {
        public int IdProprietario { get; set; }

        public int IdEmpresa { get; set; }

        public int IdContato { get; set; }

        public string Telefone { get; set; }

        public string Celular { get; set; }

        public string Email { get; set; }
    }

    public class ProprietarioEnderecoCreateModel
    {
        public int IdProprietario { get; set; }

        public int IdEmpresa { get; set; }

        public int IdEndereco { get; set; }

        public string CEP { get; set; }

        public string Endereco { get; set; }

        public string Complemento { get; set; }

        public int? Numero { get; set; }

        public string Bairro { get; set; }

        public int IdCidade { get; set; }

        public int IdEstado { get; set; }

        public int IdPais { get; set; }
    }
}

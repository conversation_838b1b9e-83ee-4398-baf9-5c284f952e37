﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Models.Webservice.Request.Especie;
using JsonResult = ATS.WS.Models.Mobile.Common.JsonResult;
using ATS.WS.Attributes;
using ATS.Domain.Enum;

namespace ATS.WS.Controllers
{
    public class EspecieController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvEspecie _srvEspecie;

        public EspecieController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvEspecie srvEspecie) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvEspecie = srvEspecie;
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string IntegrarAlterarEspecie(IntegrarAlterarEspecieRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvEspecie.IntegrarAlterarEspecie(@params));
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarEspecie(string token, string cnpjAplicacao, int? idEspecie)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvEspecie.ConsultarEspecie(idEspecie));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Retorna as filiais cadastradas/atualizadas a partir da data base de filtro
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string Consultar(EspecieConsultarRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvEspecie.GetEspeciesAtualizadas(@params.DataBase));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
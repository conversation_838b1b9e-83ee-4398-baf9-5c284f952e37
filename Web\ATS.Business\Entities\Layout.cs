﻿namespace ATS.Domain.Entities
{
    public class Layout
    {
        public int IdLayout { get; set; }
        public string Href { get; set; }
        public string CSS { get; set; }
        public string Image { get; set; }
        public string Background { get; set; }
        public int? IdEmpresa { get; set; }
        public int? IdGrupoUsuarioMotorista { get; set; }
        public int? IdGrupoUsuarioProprietario { get; set; }
        public string Favicon { get; set; }
        public string HTMLTitle { get; set; }
        public string LogoTitle { get; set; }
        public string MailFooterCompanyName { get; set; }

        /// <summary>
        /// Nome do aplicativo que está realizando a lógica (Extratta, Sotran Tech, Etc)
        /// </summary>
        public string NomeAplicativo { get; set; }

        public virtual Empresa Empresa { get; set; }
        public virtual GrupoUsuario GrupoUsuarioMotorista { get; set; }
        public virtual GrupoUsuario GrupoUsuarioProprietario { get; set; }
    }
}

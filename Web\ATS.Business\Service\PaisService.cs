﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;

namespace ATS.Domain.Service
{
    public class PaisService : ServiceBase, IPaisService
    {
        private readonly IPaisRepository _paisRepository;

        public PaisService(IPaisRepository paisRepository)
        {
            _paisRepository = paisRepository;
        }

        /// <summary>
        /// Validar o objeto para os processos de CRUD
        /// </summary>
        /// <param name="pais">Entidade</param>
        /// <param name="processo">Processo</param>
        /// <returns></returns>
        private ValidationResult IsValid(Pais pais, EProcesso processo)
        {
            ValidationResult validationResult = new ValidationResult();

            if (!string.IsNullOrEmpty(pais.Sigla))
            {
                // Validar se a sigla possui somente caracteres em caixa alta e que sejam letras
                validationResult.Add(AssertionConcern.AssertArgumentMatches("^[A-Z]+$", pais.Sigla,
                        "Sigla deve possuir somente letras."));

                switch (processo)
                {
                    case EProcesso.Create:
                        if (_paisRepository.Any(p => p.Sigla == pais.Sigla))
                            validationResult.Add($"Sigla já cadastrada para outro país.");
                        break;

                    case EProcesso.Update:
                        if (_paisRepository.Any(p => p.Sigla == pais.Sigla && p.IdPais != pais.IdPais))
                            validationResult.Add($"Sigla já cadastrada para outro país.");
                        break;
                }
            }

            if (!string.IsNullOrEmpty(pais.BACEN.ToString()))
            {
                switch (processo)
                {
                    case EProcesso.Create:
                        if (_paisRepository.Any(p => p.BACEN == pais.BACEN))
                            validationResult.Add($"BACEN já cadastrado para outro país.");
                        break;

                    case EProcesso.Update:
                        if (_paisRepository.Any(p => p.BACEN == pais.BACEN && p.IdPais != pais.IdPais))
                            validationResult.Add($"BACEN já cadastrado para outro país.");
                        break;
                }
            }



            return validationResult;
        }

        public Pais BuscarBrasil()
        {
            return _paisRepository.Find(x => x.BACEN == 1058).FirstOrDefault();
        }

        /// <summary>
        /// Formatar os valores de acordo com as regras de negócio
        /// </summary>
        /// <param name="pais"></param>
        private void FormatValues(Pais pais)
        {
            pais.Sigla = pais.Sigla?.ToUpper();
        }

        /// <summary>
        /// Método utilizado para buscar País.
        /// </summary>
        /// <param name="id">Id de Pais</param>
        /// <returns>Entidade Pais</returns>
        public Pais Get(int id)
        {
            return _paisRepository.Get(id);
        }

        /// <summary>
        /// Retorna o país
        /// </summary>
        /// <param name="sigla">Sigla do país</param>
        /// <returns></returns>
        public Pais Get(string sigla)
        {
            return _paisRepository
                .Find(x => x.Sigla == sigla).FirstOrDefault();
        }

        public int? GetIdPais(string sigla)
        {
            return _paisRepository
                .Find(x => x.Sigla == sigla).FirstOrDefault()?.IdPais;
        }

        /// <summary>
        /// Método utilizado para incluir País.
        /// </summary>
        /// <param name="pais">Entidade de Pais</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Pais pais)
        {
            ValidationResult validationResult;

            try
            {
                FormatValues(pais);

                validationResult = IsValid(pais, EProcesso.Create);
                if (validationResult.IsValid)
                    _paisRepository.Add(pais);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return validationResult;
        }

        /// <summary>
        /// Método utilizado para alterar País.
        /// </summary>
        /// <param name="pais">Entidade de Pais</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Pais pais)
        {
            ValidationResult validationResult;

            try
            {
                FormatValues(pais);

                validationResult = IsValid(pais, EProcesso.Update);
                if (validationResult.IsValid)
                    _paisRepository.Update(pais);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return validationResult;
        }

        /// <summary>
        /// Método utilizado para buscar Pais por código do BACEN.
        /// </summary>
        /// <param name="nBACEN">Código do BACEN</param>
        /// <returns>Entidade Pais</returns>
        public Pais GetPorCodigoBACEN(int nBACEN)
        {
            return _paisRepository
                .Find(x => x.BACEN == nBACEN).FirstOrDefault();
        }

        public int GetIdPaisPorBACEN(int nBACEN)
        {
            return _paisRepository
                .Find(x => x.BACEN == nBACEN).Select(x => x.IdPais)
                .FirstOrDefault();
        }

        public Pais GetPorNome(string pais)
        {
            return _paisRepository
                .Find(x => x.Nome.ToLower() == pais.ToLower()).FirstOrDefault();
        }

        /// <summary>
        /// Método utilizado para consultar País.
        /// </summary>
        /// <param name="nome">Nome de Pais</param>
        /// <returns>IQueryable de Pais</returns>
        public IQueryable<Pais> Consultar(string nome)
        {
            if (nome == null)
                nome = String.Empty;
            
            return _paisRepository.Consultar(nome);
        }

        /// <summary>
        /// Inativa um país
        /// </summary>
        /// <param name="idPais">ID do país a ser inativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idPais)
        {
            try
            {
                IPaisRepository paisRepository = _paisRepository;

                Pais pais = paisRepository.Get(idPais);

                if (!pais.Ativo)
                    return new ValidationResult().Add($"País já desativado na base de dados.");

                pais.Ativo = false;

                paisRepository.Update(pais);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa um país
        /// </summary>
        /// <param name="idPais">ID do país a ser reativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idPais)
        {
            try
            {
                IPaisRepository paisRepository = _paisRepository;

                Pais pais = paisRepository.Get(idPais);

                if (pais.Ativo)
                    return new ValidationResult().Add($"País já ativo na base de dados.");

                pais.Ativo = true;

                paisRepository.Update(pais);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }


        public object ConsultarGrid(string nome, string sigla, int? bacen, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var paises = _paisRepository.GetAll();

            if (nome != null) paises = paises.Where(x => x.Nome.Contains(nome));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                paises = paises.OrderBy(x => x.IdPais);
            else
                paises = paises.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            if (bacen.HasValue)
                paises = paises.Where(x => x.BACEN == bacen.Value);

            // Aplica para todos os campos de string
            paises = paises.AplicarFiltrosDinamicos<Pais>(filters);

            return new
            {
                totalItems = paises.Count(),
                items = paises.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdPais,
                    x.Nome,
                    x.BACEN,
                    x.Sigla,
                    x.Ativo
                })
            };
        }

        public bool VerificarBacenCadastrado(int codigo)
        {
            var id = _paisRepository.Where(x => x.BACEN == codigo).Select(x => x.BACEN)
                .FirstOrDefault();
            
            return id != null;
        }
    }
}
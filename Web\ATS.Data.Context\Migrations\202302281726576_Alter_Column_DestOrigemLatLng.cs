﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Alter_Column_DestOrigemLatLng : DbMigration
    {
        public override void Up()
        {
            AlterColumn("dbo.ROTA_MODELO", "origemlatitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.ROTA_MODELO", "origemlongitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.ROTA_MODELO", "destinolatitude", c => c.Decimal(precision: 18, scale: 6));
            AlterColumn("dbo.ROTA_MODELO", "destinolongitude", c => c.Decimal(precision: 18, scale: 6));
        }
        
        public override void Down()
        {
            AlterColumn("dbo.ROTA_MODELO", "destinolongitude", c => c.Decimal(precision: 18, scale: 2));
            AlterColumn("dbo.ROTA_MODELO", "destinolatitude", c => c.Decimal(precision: 18, scale: 2));
            AlterColumn("dbo.ROTA_MODELO", "origemlongitude", c => c.Decimal(precision: 18, scale: 2));
            AlterColumn("dbo.ROTA_MODELO", "origemlatitude", c => c.Decimal(precision: 18, scale: 2));
        }
    }
}

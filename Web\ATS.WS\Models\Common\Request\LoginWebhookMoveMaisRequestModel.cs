﻿using ATS.Domain.Validation;

namespace ATS.WS.Models.Common.Request
{
    public class LoginWebhookMoveMaisRequestModel
    {
      public string user { get; set; }
      public string password { get; set; }

      public BusinessResult ValidRequest()
      {
          if (string.IsNullOrEmpty(password))
              return BusinessResult.Error("Campo senha é obrigatório!");
          
          if (string.IsNullOrEmpty(user))
              return BusinessResult.Error("Campo user é obrigatório!");

          return BusinessResult.Valid();
      }
    }
}
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Data.Repository.Dapper
{
    public class AtendimentoDapper: DapperFactory<AtendimentoPortador>, IAtendimentoDapper
    {
        public IList<ConsultaAtendimentoInternalResponseDTO> GetAtendimentos(ConsultaRelatorioAtendimentoDTO request)
        {
            string sSql = $@"select
                                p.IdAtendimentoPortador,
                                p.cnpjcpf,
                                p.Observa<PERSON>,
                                p.DataInicio,
                                p.DataFinal,
                                p.idusuario as IdUsuario,
                                u.nome as UsuarioNome,
                                p.Status,
                                p.Protocolo,
                                p.IdMotivoFinalizacaoAtendimento,
                                m.descricao as DescricaoMotivoAtendimento,
                                format(p.datafinal - p.datainicio, 'HH:mm:ss') as TotalData
                            from ATENDIMENTO_PORTADOR p
                            left join USUARIO u on p.idusuario = u.idusuario
                            left join MOTIVO m on p.idmotivofinalizacaoatendimento = m.idmotivo
                            where p.datainicio >= '{request.DataInicio.StartOfDay():yyyyMMdd HH:mm:ss}' 
                              AND p.datafinal <=  '{request.DataFim.EndOfDay():yyyyMMdd HH:mm:ss}'";

            if (request.Atendente != 0)
            {
                sSql += $" AND p.idusuario = {request.Atendente.Value} ";
            }
            
            if (request.Portador != 0)
            {
                sSql += $" AND p.IdAtendimentoPortador = {request.Portador} ";
            }
            
            if (!string.IsNullOrEmpty(request.Protocolo))
            {
                sSql += $" AND p.protocolo = {request.Protocolo} ";
            }

            sSql += $" ORDER BY p.IdAtendimentoPortador DESC ";
            
            var repository = this; 
            
            var atendimento = repository.RunSelect<ConsultaAtendimentoInternalResponseDTO>(sSql);
            
            return atendimento.ToList();
        }
    }
}
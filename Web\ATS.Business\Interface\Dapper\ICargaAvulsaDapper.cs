using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Dapper
{
    public interface ICargaAvulsaDapper
    {
        List<int> GetIdsCargaAvulsaParaProcessar();

        List<CargaAvulsa> GetCargaAvulsasParaProcessar(int numeroRegistros);

        ValidationResult SetarStatusProcessamentoEmAndamento();

        List<CargaAvulsaProcessadaModel> GetCargasAvulsasProcessadas();

        List<CargaAvulsaProcessadaModel> GetCargasAvulsasProcessadasPorId(List<int> cargaAvulsaIds);

        void AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa statusCargaAvulsa, string mensagem, string mensagemantifraude);

        ReciboCargaAvulsaModel ConsultarDadosReciboCargaAvulsa(int cargaAvulsaId);

        bool HasCargaAvulsaMesmoDocumento(int idEmpresa, string cpfMotorista, int intervalo);

        bool HasCargaAvulsaMesmoNroControle(int idEmpresa, string nroControle);
        
        bool HasCargaAvulsaMesmoValor(int idEmpresa, decimal valor, string cpfMotorista, int idCargaAvulsa);

        CargaAvulsaProcessadaModel GetCargaAvulsaProcessadaPorId(int cargaAvulsaId);
    }
}
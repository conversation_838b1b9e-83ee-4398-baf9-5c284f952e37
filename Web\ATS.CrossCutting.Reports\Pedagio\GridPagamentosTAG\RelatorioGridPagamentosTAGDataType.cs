﻿using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Pedagio.GridPagamentosTAG
{
    public class RelatorioGridPagamentosTagDataType
    {
        public List<RelatorioGridPagamentosTagItemDataType> items { get; set; }
    }

    public class RelatorioGridPagamentosTagItemDataType
    { 
        public long Id { get; set; }
        public string DataCriacao { get; set; }
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string Placa { get; set; }
        public long? SerialNumber { get; set; }
        public string Praca { get; set; }
        public string Tipo { get; set; }
        public string Origem { get; set; }
        public string ValorTaxa { get; set; }
        public string ValorPassagem { get; set; }
        public string StatusTaxa { get; set; }
        public string StatusPassagem { get; set; }
        public int? ViagemId { get; set; }
        public string ProtocoloValePedagio { get; set; }
    }
}
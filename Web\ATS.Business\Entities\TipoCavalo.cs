﻿using System;
using ATS.Domain.Enum;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class TipoCavalo
    {
        public int IdTipoCavalo { get; set; }
        public string Nome { get; set; }
        public int?  NumeroEixos { get; set; }
        public ECategoriaTipoCavalo Categoria { get; set; }
        public bool Ativo { get; set; } = true;
        public int? IdEmpresa { get; set; }
        public int? Capacidade { get; set; }
        [SkipTracking]
        public DateTime? DataHoraUltimaAtualizacao { get; set; }

 
        public virtual ICollection<Veiculo> Veiculos { get; set; }
        public virtual ICollection<TipoCavaloCliente> TipoCavaloCliente { get; set; }
        public ICollection<Conjunto> Conjuntos { get; set; }
        public virtual Empresa Empresa { get; set; }
    }
}
﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Helpers;
using ATS.WS.Security.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Data.Entity;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Security.Principal;
using System.Web.Http.Controllers;
using System.Web.Mvc;
using ATS.Domain.Interface.Database;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Security
{
    public class Autenticacao
    {
        public string GerarToken(Usuario usuario, TokenConfigurations tokenConfigurations, SigningConfigurations signingConfigurations)
        {
            ClaimsIdentity identity = new ClaimsIdentity(new GenericIdentity(Convert.ToString(usuario.IdUsuario), "Login"), new[]
            {
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString("N")),
                new Claim("IdUsuario", Convert.ToString(usuario.IdUsuario), ClaimValueTypes.Integer),
                new Claim("Nome", Convert.ToString(usuario.Nome), ClaimValueTypes.String),
                new Claim("Perfil", Convert.ToString((int)usuario.Perfil), ClaimValueTypes.Integer),
                new Claim("CPFCNPJ", usuario.CPFCNPJ ?? string.Empty),
                new Claim("IdEmpresa", usuario.IdEmpresa.HasValue ? Convert.ToString(usuario.IdEmpresa) : string.Empty),

            });

            var dataCriacao = DateTime.UtcNow;
            var dataExpiracao = dataCriacao.AddSeconds(tokenConfigurations.Seconds);

            var handler = new JwtSecurityTokenHandler();
            var securityToken = handler.CreateToken(new SecurityTokenDescriptor
            {
                Issuer = tokenConfigurations.Issuer,
                Audience = tokenConfigurations.Audience,
                SigningCredentials = signingConfigurations.SigningCredentials,
                Subject = identity,
                NotBefore = dataCriacao,
                Expires = dataExpiracao
            });

            var token = handler.WriteToken(securityToken);
            return token;
        }

        public void ValidarToken(string token, TokenConfigurations tokenConfigurations, SigningConfigurations signingConfigurations,
            IUserIdentity usuario, ActionExecutingContext actionExecutingContext)
        {
            try
            {
                SecurityToken validatedToken = null;
                ClaimsPrincipal tokenClaims;
                KeycloakHelper keycloak = new KeycloakHelper();

                //se der erro na validacao, vai levantar excecao
                if (keycloak.ValidateToken(token, out validatedToken, out tokenClaims))
                {
                    foreach (var responseClaim in tokenClaims.Claims)
                    {
                        switch (responseClaim.Type)
                        {
                            case "IdUsuario":
                                //if (responseClaim.ValueType == ClaimValueTypes.Integer)
                                usuario.IdUsuario = Convert.ToInt32(responseClaim.Value);
                                break;
                            case "Nome":
                                usuario.Nome = responseClaim.Value;
                                break;
                            case "CPFCNPJ":
                                usuario.CpfCnpj = responseClaim.Value;
                                break;
                            case "Perfil":
                                //if (responseClaim.ValueType == ClaimValueTypes.Integer)
                                usuario.Perfil = Convert.ToInt32(responseClaim.Value);
                                break;
                            case "IdEmpresa":
                                usuario.IdEmpresa = null;
                                int idEmpresa;
                                if (int.TryParse(responseClaim.Value, out idEmpresa))
                                    usuario.IdEmpresa = idEmpresa;
                                if (idEmpresa != 0)
                                {
                                    var empresaRep = DependencyResolver.Current.GetService<IEmpresaRepository>();
                                    var empresa = idEmpresa;
                                    var cnpj = empresaRep.Where(t => t.IdEmpresa == empresa)
                                        .Select(t => t.CNPJ).FirstOrDefault()?.OnlyNumbers();
                                    if (!string.IsNullOrWhiteSpace(cnpj))
                                        usuario.CnpjEmpresa = cnpj;
                                }
                                break;
                        }
                    }
                }
            }
            catch (SecurityTokenExpiredException)
            {
                actionExecutingContext.Result = new HttpUnauthorizedResult("jwt expired");
            }
            catch (SecurityTokenInvalidSignatureException)
            {
                //Esta exception ocorrerá sempre que o token estiver com a assinatura inválida.
                actionExecutingContext.Result = new HttpUnauthorizedResult("Tentativa de solicitação bloqueada, requisição não confiável.");
            }
            catch (Exception)
            {
                actionExecutingContext.Result = new HttpUnauthorizedResult("Não foi possível validar a autenticação do usuário.");
            }
        }
    }
}
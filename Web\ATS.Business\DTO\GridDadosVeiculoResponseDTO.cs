using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class GridDadosVeiculoResponseDTO
    {
        public int IdVeiculo { get; set; }
        public string Placa { get; set; }
        public string Marca { get; set; }
        public string Modelo { get; set; }
        public ETipoContrato TipoContrato { get; set; }
        public long? NumeroFrota { get; set; }
        public string Municipio { get; set; }
        public int QuantidadeEixos { get; set; }
        public bool? HabilitarContratoCiotAgregado { get; set; }
        public int? IdTipoCarreta { get; set; }
        public int? IdTipoCavalo { get; set; }
        public int? IdEmpresa { get; set; }
        public bool Ativo { get; set; }
        public bool ComTracao { get; set; }

        public GridDadosFilialVeiculoResponseDTO Filial { get; set; } 
        public GridDadosMotoristaVeiculoResponseDTO Motorista { get; set; } 
        public GridDadosVeiculoProprietarioResponseDTO Proprietario { get; set; } 
        public GridDadosEmpresaVeiculoResponseDTO Empresa { get; set; } 
        public GridDadosTagVeiculoResponseDTO Tag { get; set; }
    }
    
    public class GridDadosFilialVeiculoResponseDTO
    {
        public string NomeFantasia { get; set; }
        public string RazaoSocial { get; set; }
    }
    
    public class GridDadosMotoristaVeiculoResponseDTO
    {
        public string Nome { get; set; }
    }
    
    public class GridDadosVeiculoProprietarioResponseDTO
    {
        public string RNTRC { get; set; }
        public string NomeFantasia { get; set; }
        public string CNPJCPF { get; set; }
        public string RazaoSocial { get; set; }
    }
    
    public class GridDadosTagVeiculoResponseDTO
    {
        public string SerialNumber { get; set; }
        public string Status { get; set; }
    }
    
    public class GridDadosEmpresaVeiculoResponseDTO
    {
        public string RazaoSocial { get; set; }
    }
}
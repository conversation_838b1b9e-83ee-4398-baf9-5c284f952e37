﻿using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using AutoMapper.QueryableExtensions;

namespace ATS.Data.Repository.EntityFramework
{
    public class DeclaracaoCiotRepository : Repository<DeclaracaoCiot>, IDeclaracaoCiotRepository
    {
        public DeclaracaoCiotRepository(AtsContext context) : base(context)
        {
        }
        
        public DeclaracaoCiot ConsultarCiot(int viagemid)
        {
            return Where(c => c.IdViagem == viagemid).FirstOrDefault();
        }
        
        public int? GetIdContratoAgregadoPorCiot(int idEmpresa, string ciot)
        {
            return Where(x => x.IdEmpresa == idEmpresa && x.Ciot == ciot)
                .Select(x => x.IdContratoCiotAgregado)
                .FirstOrDefault();
        }
        
        public DadosAtualizacaoCiotDto GetDeclaracaoCiotIncludeViagens(int idDeclaracaoCiot) 
        {
            return Where(o => o.IdDeclaracaoCiot == idDeclaracaoCiot)
                .Include(o => o.ViagensVinculadas)
                .Include(o => o.ViagensVinculadas.Select(viagem => viagem.ViagemEventos))
                .ProjectTo<DadosAtualizacaoCiotDto>()
                .FirstOrDefault();
        }

        public DeclaracaoCiot GetByCiotDesvinculo(int idViagemDesnvinculada)
        {
            return Where(c => c.IdViagemCiotDesvinculado == idViagemDesnvinculada).FirstOrDefault();
        }
        
        public string GetCiot(int idviagem, string separadorVerificador = "/")
        {
            var ciot = Where(c => c.IdViagem == idviagem)
                .OrderByDescending(x => x.IdDeclaracaoCiot)
                .Select(c => new { c.Ciot, c.Verificador })
                .FirstOrDefault();
            return ciot != null ? $"{ciot.Ciot}/{ciot.Verificador}" : "";
        }
        
        public DeclaracaoCiot GetDeclaracaoCiot(int idviagem)
        {
            return Where(c => c.IdViagem == idviagem)
                .OrderByDescending(x => x.IdDeclaracaoCiot)
                .FirstOrDefault();
        }
    }
}
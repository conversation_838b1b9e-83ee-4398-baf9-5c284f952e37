using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class ViagemPagamentoConta
    {
        /*public int IdViagemPagamentoConta { get; set; }*/

        public int IdViagem { get; set; }

        public int IdEmpresa { get; set; }

        public string CpfCnpjConta { get; set; }

        public string CodigoBacenBanco { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }
        
        public virtual Viagem Viagem { get; set; }
    }
}
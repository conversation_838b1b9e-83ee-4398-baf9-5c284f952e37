﻿using ATS.Domain.Enum;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class ProtocoloIntegrationModel
    {
        public int IdProtocolo { get; set; }
        public int IdEstabelecimentoBase { get; set; }
        public decimal ValorProtocolo { get; set; }
        public string DataGeracao { get; set; }
        public string DataPagamento { get; set; }
        public string DataPrevisaoPagamento { get; set; }
        public bool Processado { get; set; }
        public int StatusProtocolo { get; set; }
        public string DescricaoStatusProtocolo { get; set; }
        public string CNPJEstabelecimento { get; set; }
        public string CpfUsuarioAprovacao { get; set; }
        public int? IdUsuarioAprovacao { get; set; }
        public List<ProtocoloEventoIntegrationModel> Eventos { get; set; }
        public List<ProtocoloAnexoIntegrationModel> Anexos { get; set; }
        public List<ProtocoloAntecipacaoIntegrationModel> Antecipacoes { get; set; }
    }

    public class ProtocoloEventoIntegrationModel
    {
        public int IdProtocoloEvento { get; set; }
        public int IdProtocolo { get; set; }
        public int IdViagemEvento { get; set; }
        public int? IdViagem { get; set; }
        public int? IdMotivo { get; set; }
        public EStatusProtocoloEvento Status { get; set; }
        public ETipoEventoViagem? TipoEventoViagem { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public string DataHoraPagamento { get; set; }
        public string NumeroRecibo { get; set; }
        public decimal? PesoChegada { get; set; }
        public decimal? PesoDiferenca { get; set; }
        public decimal? ValorDifFreteMotorista { get; set; }
        public decimal? ValorQuebraMercadoria { get; set; }
        public decimal? ValorQuebraAbonada { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? ValorDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DescricaoMotivoDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdMotivoDesconto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ObservacaoDesconto { get; set; }
    }

    public class ProtocoloAnexoIntegrationModel
    {
        public int IdProtocoloAnexo { get; set; }
        public int IdProtocolo { get; set; }
        public int IdDocumento { get; set; }
        public string Token { get; set; }
    }

    public class ProtocoloAntecipacaoIntegrationModel
    {
        public int IdProtocoloAntecipacao { get; set; }
        public int IdProtocolo { get; set; }
        public EStatusProtocoloAntecipacao Status { get; set; }
        public string DataSolicitacao { get; set; }
        public decimal ValorPagamentoAntecipado { get; set; }
        public int? IdMotivo { get; set; }
    }
}

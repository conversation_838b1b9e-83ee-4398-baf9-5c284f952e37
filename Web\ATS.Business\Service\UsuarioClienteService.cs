﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class UsuarioClienteService : ServiceBase, IUsuarioClienteService
    {
        private readonly IUsuarioClienteRepository _usuarioClienteRepository;

        public UsuarioClienteService(IUsuarioClienteRepository usuarioClienteRepository)
        {
            _usuarioClienteRepository = usuarioClienteRepository;
        }

        public ValidationResult Delete(UsuarioCliente usuarioCliente)
        {
            try
            {
                _usuarioClienteRepository.Remove(usuarioCliente);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public IQueryable<UsuarioCliente> GetUsuarioClientePorIdUsuario(int idUsuario)
        {
            return _usuarioClienteRepository
                .Find(x => x.IdUsuario == idUsuario)
                .Include(x => x.Usuario)
                .Include(x => x.Cliente);
        }
    }
}

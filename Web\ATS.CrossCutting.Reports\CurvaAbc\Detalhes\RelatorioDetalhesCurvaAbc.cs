﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.CurvaAbc.Detalhes
{
    public class RelatorioDetalhesCurvaAbc
    {
        public byte[] GetReport(List<RelatorioDetalhesCurvaAbcDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var bytes = new Base.Reports().GetReport(listaDados, parametros, true, "DtsDetalhesCurvaAbc",
                "ATS.CrossCutting.Reports.CurvaAbc.Detalhes.RelatorioDetalhesCurvaAbc.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

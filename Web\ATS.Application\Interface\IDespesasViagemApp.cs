﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Models.DespesasViagem;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IDespesasViagemApp : IAppBase<DespesasViagem>
    {
        DespesasViagemGridSaldoResponse ConsultarPortadorGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, int? idUsuario);
        byte[] GerarRelatorioGridDespesaViagem(int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa, int? idUsuario);        
        ExtratoImagensVinculadasResponse ConsultarImagensVinculadasExtrato(string hashID);
        BusinessResult<ResgatePortadorResponse> ConsultarResgatePortador(string cpfCnpjPortador);
        BusinessResult<ResgatarValorResponseDTO> ResgatarValor(ResgatarValorDespesasViagemRequest request);
        byte[] ConsultarRelatorioExtratoGrid(RelatorioGridExtratoDTO request);
        byte[] ConsultarRelatorioExtratoV2Grid(RelatorioGridExtratoDetalhadoDTO request);
        BusinessResult GerarRelatorioExtratoDetalhadoOfx(RelatorioGridExtratoDetalhadoDTO request);

    }
}
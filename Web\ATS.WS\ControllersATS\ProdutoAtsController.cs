﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Produto;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Web.Mvc;
using ATS.Application.Helpers;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class ProdutoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IClienteProdutoEspecieApp _clienteProdutoEspecieApp;
        private readonly IProdutoApp _produtoApp;
        private readonly IProdutoService _produtoService;

        public ProdutoAtsController(IUserIdentity userIdentity, IClienteProdutoEspecieApp clienteProdutoEspecieApp, IProdutoApp produtoApp, IProdutoService produtoService)
        {
            _userIdentity = userIdentity;
            _clienteProdutoEspecieApp = clienteProdutoEspecieApp;
            _produtoApp = produtoApp;
            _produtoService = produtoService;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(ProdutoCrud produtoCrud)
        {
            try
            {
                var validacoes = ValidarPropriedades(produtoCrud);

                if (!validacoes.IsValid)
                    return ResponderErro(validacoes.ToFormatedMessage());

                var produto = Mapper.Map<ProdutoCrud, Produto>(produtoCrud);

                if (produto.IdProduto > 0)
                {
                    var resultadoUpdate = _produtoApp.Update(produto);

                    return !resultadoUpdate.IsValid
                        ? ResponderErro(resultadoUpdate.ToFormatedMessage())
                        : ResponderSucesso("Edição realizada com sucesso!");
                }

                var resultado = _produtoApp.Add(produto);

                return !resultado.IsValid
                    ? ResponderErro(resultado.ToFormatedMessage())
                    : ResponderSucesso("Cadastro realizado com sucesso!");
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idProduto, string descricao, int Take, int Page, OrderFilters Order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var produtos = _produtoApp
                    .ConsultaGrid(idEmpresa, idProduto, descricao, Take, Page, Order, filters);

                return ResponderSucesso(produtos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetTodosProdutosPorClienteForUiGrid(int? idEmpresa, int? idProduto, string descricao, int Take, int Page, OrderFilters Order, List<QueryFilters> filters)
        {
            try
            {
                var produtos = _clienteProdutoEspecieApp
                    .GetTodosProdutosPorClienteForUiGrid(Take, Page, Order, filters);

                return ResponderSucesso(produtos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idProduto)
        {
            try
            {
                var validationResult = _produtoApp.Inativar(idProduto);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Produto inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idProduto)
        {
            try
            {
                var validationResult = _produtoApp.Reativar(idProduto);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Produto reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idProduto)
        {
            try
            {
                var produto = _produtoService.Get(idProduto);

                if (produto == null)
                    throw new Exception("Não foi possível encontrar o registro desejado");

                return ResponderSucesso(new
                {
                    produto.IdProduto,
                    produto.Descricao,
                    produto.Empresa.IdEmpresa,
                    RazaoSocialEmpresa = produto.Empresa.RazaoSocial
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        private static ValidationResult ValidarPropriedades(ProdutoCrud produtoCrud)
        {
            var retorno = new ValidationResult();

            if (!produtoCrud.IdEmpresa.HasValue)
                retorno.Add("Empresa é obrigatória.");

            if (string.IsNullOrWhiteSpace(produtoCrud.Descricao))
                retorno.Add("Descrição é obrigatória.");

            return retorno;
        }
    }
}
﻿using System.Collections.Generic;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    public class Produto
    {
        public int IdProduto { get; set; }

        public int IdEmpresa { get; set; }

        public string Descricao { get; set; }

        public bool Ativo { get; set; }

        #region Relacionamentos

        public virtual Empresa Empresa { get; set; }

        #endregion

        #region Navegação Inversa
        public virtual ICollection<ClienteProdutoEspecie> ClienteProdutoEspecie { get; set; }
        public virtual ICollection<ProdutoDadosCarga> ProdutoDadosCarga { get; set; }

        #endregion
    }
}

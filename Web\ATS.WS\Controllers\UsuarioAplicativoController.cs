﻿using ATS.WS.Controllers.Base;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Models.Webservice.Request.Cartoes;
using ATS.Domain.DTO;
using ATS.WS.Attributes;
using ATS.Domain.Enum;
using ATS.WS.Models.Mobile.Common;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class UsuarioAplicativoController : BaseController
    {
        private readonly IUsuarioApp _app;

        public UsuarioAplicativoController(BaseControllerArgs baseArgs, IUsuarioApp app) : base(baseArgs)
        {
            _app = app;
        }

        [HttpGet]
        [EnableLogRequest]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        public JsonResult Foto(GetUsuarioFotoRequest request)
        {
            try
            {
                var validacaoChamada = request.ValidaRequest();
               
                if (!validacaoChamada.IsValid)
                    return Responde(new UsuarioFotoResponse(false, validacaoChamada.ToString()));

                var resposta = _app.GetFotoUsuario(request.Cpfcnpj);
                
                return Responde(resposta);
            }
            catch (Exception e)
            {
                return Responde(new UsuarioFotoResponse(false, e.GetBaseException().Message));
            }
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        public JsonResult GetInformacoes()
        {
            try
            {
                var response = _app.UsuarioAplicativoGetInformacoes();
                return Responde(new Retorno<UsuarioAplicativoGetInformacoesResponse>(true, response));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(new Retorno<UsuarioAplicativoGetInformacoesResponse>(false, e.Message, null));
            }
        }
    }
}
namespace ATS.Domain.DTO
{
    public class ResgatarValorDTO
    {
        public string Empresa { get; set; }
        public int Cartao { get; set; }
        public int Produto { get; set; }
        public int? Historico { get; set; }
        public decimal? Valor { get; set; }
        public long? ProtocoloRequisicao { get; set; }
        public bool? PermitirTransacaoPendente { get; set; }
        public string InformacoesAdicionais { get; set; }
        public string Metadados { get; set; }
        public string Documento { get; set; }
        public string Especificacao { get; set; }
        public string CpfUsuario { get; set; }
    }
}
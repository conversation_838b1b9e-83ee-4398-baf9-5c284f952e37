﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TransacaoPixStatusMap : EntityTypeConfiguration<TransacaoPixStatus>
    {
        public TransacaoPixStatusMap()
        {
            ToTable("TRANSACAO_PIX_STATUS");

            HasKey(x => x.IdTransacaoPixStatus);

            Property(x => x.Descricao).HasMaxLength(100);
        }
    }
}

﻿using ATS.Domain.Enum;
using System;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class TransacaoCartao
    {
        public int IdTransacaoCartao { get; set; }
        public int LineId { get; set; }
        public DateTime DataCriacao { get; set; }
        public int? IdViagemEvento { get; set; }
        public EStatusPagamentoCartao StatusPagamento { get; set; }
        
        [SkipTracking]
        public string MensagemProcessamentoWs { get; set; }
        public long NumeroProtocoloWs { get; set; }
        public ETipoProcessamentoCartao TipoProcessamentoCartao { get; set; }
        public decimal ValorMovimentado { get; set; }
        public int Historico { get; set; }
        public string CnpjCpfOrigem { get; set; }
        public string CnpjCpfDestino { get; set; }
        public EOrigemTransacaoCartao OrigemTransacaoCartao { get; set; } = EOrigemTransacaoCartao.Automatico;
        public int? IdCargaAvulsa { get; set; }
        public int? IdResgate { get; set; }
        public DateTime? DataConfirmacaoMeioHomologado { get; set; }
        public int? IdentificadorCartao { get; set; }

        public virtual ViagemEvento ViagemEvento { get; set; }
        public virtual CargaAvulsa CargaAvulsa { get; set; }
    }
}

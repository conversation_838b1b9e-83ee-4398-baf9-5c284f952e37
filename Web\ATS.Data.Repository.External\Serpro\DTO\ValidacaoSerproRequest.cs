using System;

namespace ATS.Data.Repository.External.Serpro.DTO
{
    public class ValidacaoSerproRequest
    {
        /// <summary>
        /// Precisa ser 11 caracteres
        /// </summary>
        public string Cpf { get; set; }
        /// <summary>
        /// Data de nascimento
        /// </summary>
        public string BirthDate { get; set; }
        /// <summary>
        /// Nome da mãe
        /// </summary>
        public string MotherName { get; set; }
        /// <summary>
        /// CNH (número do registro), maximo de 15 caracteres
        /// </summary>
        public string DriverLicenseRegisterNumber { get; set; }
        /// <summary>
        /// Data de emissão da primeira CNH, não obrigatório
        /// </summary>
        public string FirstDriverLicenseDate { get; set; }
        /// <summary>
        /// Selfie do motorista em string b64
        /// </summary>
        public string PhotoBase64 { get; set; }
    }
}
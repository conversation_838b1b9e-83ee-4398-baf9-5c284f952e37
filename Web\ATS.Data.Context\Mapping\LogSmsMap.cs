﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Data.Context.Mapping
{
    public class LogSmsMap : EntityTypeConfiguration<LogSms>
    {
        public LogSmsMap()
        {
            ToTable("LOG_SMS");

            HasKey(t => new { t.IdLogSms });

            Property(t => t.IdLogSms)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.CelularEnvio)
                .HasMaxLength(20)
                .IsRequired();

            Property(t => t.Texto)
                .HasMaxLength(255)
                .IsRequired();

            Property(t => t.ProcessoEnvio)
                .IsRequired();

            Property(t => t.DataHoraEnvio)
                .IsRequired();

            Property(t => t.Perfil)
                .IsOptional();

            HasRequired(x => x.Empresa)
                .WithMany(x => x.LogSms)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Usuario)
                .WithMany(x => x.LogSms)
                .HasForeignKey(x => x.IdUsuarioEnvio);
        }
    }
}

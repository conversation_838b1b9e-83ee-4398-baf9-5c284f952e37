using ATS.Application.Interface.Common;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Application.Application.Common
{
    public abstract class BaseApp<IEntityService> : IBaseApp<IEntityService> where IEntityService : IBaseService
    {
        public readonly IEntityService Service; 
        
        protected BaseApp(IEntityService service)
        {
            Service = service;
        }

        public void Dispose()
        {
            Service?.Dispose();
        }
    }
}
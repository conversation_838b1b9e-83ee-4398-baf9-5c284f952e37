﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models.DestinoRotaModelo;

namespace ATS.Domain.DTO.CadastroRotas
{
    public class RotaModeloResponse
    {
        public int IdRotaModelo { get; set; }
        public List<decimal> CodigosIbge { get; set; }
        public List<DestinoRotaModeloModel> Destinos { get; set; }
        public int QtdeEixos { get; set; }
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public ETipoRodagem TipoRodagem { get; set; }
        public string NomeRota { get; set; }
        public DateTime DataCadastro { get; set; }
        public bool IdaEVolta { get; set; }
        public string <PERSON>yline { get; set; }
        public int? IdEmpresa { get; set; }
    }

}

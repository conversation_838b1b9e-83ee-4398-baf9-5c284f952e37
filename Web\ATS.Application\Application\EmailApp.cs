﻿using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class EmailApp  : IEmailApp
    {
        private readonly IEmailService _emailService;

        public EmailApp(IEmailService emailService)
        {
            _emailService = emailService;
        }

        public ValidationResult EnviarEmail(EmailModel emailMode, int? administradora = null)
        {
            try
            {
                _emailService.EnviarEmail(emailMode, administradora: administradora);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

        }
		
		public ValidationResult TestarEmail(string emailNome, string emailEndereco, int porta, bool usaSSL, string servidor, string logon, string senha)
        {
            return _emailService.TestarEmail(emailNome, emailEndereco, porta, usaSSL, servidor, logon, senha); 
        }

        public EmailConfiguration GetEmailConfiguration(int? idFilial, int idEmpresa)
        {
            return _emailService.GetEmailConfiguration(idFilial, idEmpresa);
        }

        public void EnviarEmailAsync(EmailModel emailModel, ConfiguracaoEnvioEmail config = null)
        {
            _emailService.EnviarEmailAsync(emailModel, config);
        }
    }
}

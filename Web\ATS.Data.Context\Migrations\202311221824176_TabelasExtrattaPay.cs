﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class TabelasExtrattaPay : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.CATEGORIA",
                c => new
                    {
                        idcategoria = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 200, unicode: false),
                        ativo = c<PERSON>(nullable: false),
                        datacadastro = c.DateTime(),
                        usuariocadastro = c.String(maxLength: 200, unicode: false),
                        dataatualizacao = c.DateTime(),
                        usuarioatualizacao = c.String(maxLength: 200, unicode: false),
                    })
                .PrimaryKey(t => t.idcategoria);
            
            CreateTable(
                "dbo.DESPESA_USUARIO",
                c => new
                    {
                        iddespesausuario = c.Int(nullable: false, identity: true),
                        idusuario = c.Int(nullable: false),
                        idcategoria = c.Int(nullable: false),
                        hashid = c.String(nullable: false, maxLength: 150, unicode: false),
                        descricao = c.String(maxLength: 500, unicode: false),
                        url = c.String(maxLength: 150, unicode: false),
                        imagetoken = c.String(maxLength: 100, unicode: false),
                        latitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        longitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        datacadastro = c.DateTime(),
                        dataatualizacao = c.DateTime(),
                    })
                .PrimaryKey(t => t.iddespesausuario)
                .ForeignKey("dbo.CATEGORIA", t => t.idcategoria)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idcategoria, name: "IX_IdCategoria");
            
            CreateTable(
                "dbo.LOCALIZACAO_USUARIO",
                c => new
                    {
                        id = c.Guid(nullable: false),
                        idusuario = c.Int(nullable: false),
                        latitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        longitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        ipv4 = c.String(nullable: false, maxLength: 20, unicode: false),
                        datacadastro = c.DateTime(),
                        dataatualizacao = c.DateTime(),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            AlterColumn("dbo.PEDAGIO_ROTA", "datacadastro", c => c.DateTime());
            AlterColumn("dbo.PEDAGIO_ROTA", "dataatualizacao", c => c.DateTime());
            AlterColumn("dbo.PEDAGIO_ROTA_PONTO", "datacadastro", c => c.DateTime());
            AlterColumn("dbo.PEDAGIO_ROTA_PONTO", "dataatualizacao", c => c.DateTime());
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.LOCALIZACAO_USUARIO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.DESPESA_USUARIO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.DESPESA_USUARIO", "idcategoria", "dbo.CATEGORIA");
            DropIndex("dbo.LOCALIZACAO_USUARIO", "IX_IdUsuario");
            DropIndex("dbo.DESPESA_USUARIO", "IX_IdCategoria");
            DropIndex("dbo.DESPESA_USUARIO", "IX_IdUsuario");
            AlterColumn("dbo.PEDAGIO_ROTA_PONTO", "dataatualizacao", c => c.DateTime(nullable: false));
            AlterColumn("dbo.PEDAGIO_ROTA_PONTO", "datacadastro", c => c.DateTime(nullable: false));
            AlterColumn("dbo.PEDAGIO_ROTA", "dataatualizacao", c => c.DateTime(nullable: false));
            AlterColumn("dbo.PEDAGIO_ROTA", "datacadastro", c => c.DateTime(nullable: false));
            DropTable("dbo.LOCALIZACAO_USUARIO");
            DropTable("dbo.DESPESA_USUARIO");
            DropTable("dbo.CATEGORIA");
        }
    }
}

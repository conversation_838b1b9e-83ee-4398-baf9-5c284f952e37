﻿using System.ComponentModel.DataAnnotations;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class PagamentoFreteAnexoModel
    {
        public int IdViagemDocumento { get; set; }
        public int IdViagemEvento { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public string Descricao { get; set; }
        public bool ObrigaAnexo { get; set; }
        public ETipoDocumento TipoDocumento { get; set; } = 0;
        public int NumeroDocumento { get; set; }
        /// <summary>
        /// Este campo representa que todos os arquivos obrigatórios
        /// para este documento já foram anexados ou não
        /// </summary>
        public bool StatusOk { get; set; }
        public string TokenAnexo { get; set; }
        public int? IdDocumento { get; set; }
        public bool ObrigaDocOriginal { get; internal set; }
    }
}

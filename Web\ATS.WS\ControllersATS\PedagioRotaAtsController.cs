using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.PedagioRota;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice;

namespace ATS.WS.ControllersATS
{
    public class PedagioRotaAtsController : BaseAtsController<IPedagioRotaApp>
    {
        private readonly IEstadoApp _estadoApp;

        public PedagioRotaAtsController(IPedagioRotaApp app, IUserIdentity userIdentity, IEstadoApp estadoApp) : base(app, userIdentity)
        {
            _estadoApp = estadoApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult Salvar(PedagioRotaSalvarRequest request)
        {
            try
            {
                var retorno = App.Salvar(request);

                if (!retorno.Resultado.IsValid)
                    return ResponderErro(retorno.Resultado.Errors.FirstOrDefault()?.Message);

                var resultado = ResponderSucesso("Operação realizada com sucesso!", retorno);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var retorno = App.ConsultarGrid(take, page, order, filters);
                
                var resultado = new
                {
                    totalItems = retorno.Count,
                    items = retorno
                };

                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDetalhes(int idRota)
        {
            try
            {
                var retorno = App.ConsultarDetalhes(idRota);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult GetEnderecoViaCep(string cep)
        {
            try
            {
                var request = (HttpWebRequest)WebRequest.Create("http://viacep.com.br/ws/" + cep + "/json/");
                request.Method = "GET";
                request.ContentType = "application/json";


                var response = (HttpWebResponse)request.GetResponse();
                var responseString = new StreamReader(response.GetResponseStream()).ReadToEnd();
                var endereco = new JavaScriptSerializer().Deserialize<EnderecoViaCep>(responseString);

                endereco.estado = _estadoApp.GetEstadoBySigla(endereco.uf)?.Nome;

                return ResponderSucesso(string.Empty, endereco);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
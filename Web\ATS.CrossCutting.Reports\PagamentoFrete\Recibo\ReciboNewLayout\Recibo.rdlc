<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsPagamentoFrete">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>3623e11a-c341-4c6d-9784-b174c1f55257</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPagamentoFrete1">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>73691430-702f-4e44-bd34-d27cb6cc7f42</rd:DataSourceID>
    </DataSource>
    <DataSource Name="ATSCrossCuttingReportsPagamentoFrete2">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>68ccf7a3-a264-4968-9ee9-72cc6c455a5d</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DtsRecibo">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPagamentoFrete</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Barcode">
          <DataField>Barcode</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
        <Field Name="Ciot">
          <DataField>Ciot</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DataDescarga">
          <DataField>DataDescarga</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DocumentoCliente">
          <DataField>DocumentoCliente</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EmpresaLogo">
          <DataField>EmpresaLogo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EstabelecimentoCnpj">
          <DataField>EstabelecimentoCnpj</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EstabelecimentoNome">
          <DataField>EstabelecimentoNome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INSS">
          <DataField>INSS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Instrucoes">
          <DataField>Instrucoes</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IRRF">
          <DataField>IRRF</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ISSQN">
          <DataField>ISSQN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MotoristaNome">
          <DataField>MotoristaNome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NomeProprietario">
          <DataField>NomeProprietario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NumeroCte">
          <DataField>NumeroCte</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NumeroFilialExterno">
          <DataField>NumeroFilialExterno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Outros">
          <DataField>Outros</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PedagioInclusoNoAdiantamento">
          <DataField>PedagioInclusoNoAdiantamento</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="ProprietarioCpfCnpj">
          <DataField>ProprietarioCpfCnpj</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ProprietarioRntrc">
          <DataField>ProprietarioRntrc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SESTSENAT">
          <DataField>SESTSENAT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Usuario">
          <DataField>Usuario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ValorPedagio">
          <DataField>ValorPedagio</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemDifFreteMotorista">
          <DataField>ViagemDifFreteMotorista</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoDataHoraPagamento">
          <DataField>ViagemEventoDataHoraPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoNumeroRecibo">
          <DataField>ViagemEventoNumeroRecibo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoTipo">
          <DataField>ViagemEventoTipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoValorTotalPagamento">
          <DataField>ViagemEventoValorTotalPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoValorTotalPagamentoComPedagio">
          <DataField>ViagemEventoValorTotalPagamentoComPedagio</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemPesoChegada">
          <DataField>ViagemPesoChegada</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemPesoSaida">
          <DataField>ViagemPesoSaida</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemQuebraMercadoria">
          <DataField>ViagemQuebraMercadoria</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemToken">
          <DataField>ViagemToken</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.PagamentoFrete</rd:DataSetName>
        <rd:TableName>PagamentoFreteReciboModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DtsAcrescimos">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPagamentoFrete1</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Descricao">
          <DataField>Descricao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.PagamentoFrete</rd:DataSetName>
        <rd:TableName>PagamentoFreteReciboAcrescimosDescontosModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboAcrescimosDescontosModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DtsDescontos">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPagamentoFrete2</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Descricao">
          <DataField>Descricao</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Valor">
          <DataField>Valor</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.PagamentoFrete</rd:DataSetName>
        <rd:TableName>PagamentoFreteReciboAcrescimosDescontosModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboAcrescimosDescontosModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.175cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox39">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>TIPO:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </TopBorder>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ViagemEventoTipo1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ViagemEventoTipo.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ViagemEventoTipo1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox22">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>RECIBO</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox22</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ViagemEventoNumeroRecibo">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ViagemEventoNumeroRecibo.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ViagemEventoNumeroRecibo</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox30">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>PEDÁGIO:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox30</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ValorPedagio">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ValorPedagio.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ValorPedagio</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox38">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="VALOR " &amp; UCase(Fields!ViagemEventoTipo.Value) &amp; ":"</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <BackgroundColor>=IIF(Not(Fields!PedagioInclusoNoAdiantamento.Value), "Orange", "No Color")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ViagemEventoValorTotalPagamento">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ViagemEventoValorTotalPagamento.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ViagemEventoValorTotalPagamento</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>=IIF(Not(Fields!PedagioInclusoNoAdiantamento.Value), "Orange", "No Color")</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox47">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>TOTAL PAGO:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox47</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <BackgroundColor>Orange</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ViagemEventoValorTotalPagamentoComPedagio">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ViagemEventoValorTotalPagamentoComPedagio.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ViagemEventoValorTotalPagamentoComPedagio</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <BackgroundColor>Orange</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox55">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>DATA DA OPERAÇÃO:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox55</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ViagemEventoDataHoraPagamento">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ViagemEventoDataHoraPagamento.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ViagemEventoDataHoraPagamento</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox63">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>OPERADOR:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox63</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Usuario">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Usuario.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Usuario</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox71">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>TRANSPORTADOR:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox71</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="MotoristaNome">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!MotoristaNome.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>MotoristaNome</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox79">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>CPF / CNPJ:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox79</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ProprietarioCpfCnpj">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ProprietarioCpfCnpj.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ProprietarioCpfCnpj</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox87">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>RNTRC:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox87</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ProprietarioRntrc">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ProprietarioRntrc.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ProprietarioRntrc</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox95">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>CIOT:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox95</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Ciot">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Ciot.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Ciot</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox103">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>FILIAL / CT-e / SÉRIE:</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox103</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DocumentoCliente">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!DocumentoCliente.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DocumentoCliente</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>None</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>4</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox212">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>INSTRUÇÕES</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox212</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </TopBorder>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>8</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Instrucoes">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Instrucoes.Value</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Instrucoes</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>8</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details" />
                  <TablixMembers>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember>
                      <Visibility>
                        <Hidden>=Not(Fields!PedagioInclusoNoAdiantamento.Value)</Hidden>
                      </Visibility>
                    </TablixMember>
                    <TablixMember />
                    <TablixMember>
                      <Visibility>
                        <Hidden>=Not(Fields!PedagioInclusoNoAdiantamento.Value)</Hidden>
                      </Visibility>
                    </TablixMember>
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DtsRecibo</DataSetName>
            <Left>1.8cm</Left>
            <Height>8.4cm</Height>
            <Width>17.4cm</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Tablix Name="Tablix2">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox52">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>ACRÉSCIMOS</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox52</rd:DefaultName>
                          <Visibility>
                            <Hidden>=IIF(IsNothing(Fields!Descricao.Value), True, False)</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>6</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Descricao">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="&lt;b&gt;" &amp; Fields!Descricao.Value &amp; "&lt;/b&gt;: " &amp; Fields!Valor.Value</Value>
                                  <MarkupType>HTML</MarkupType>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Descricao</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>6</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details2" />
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DtsAcrescimos</DataSetName>
            <Top>8.4cm</Top>
            <Left>1.8cm</Left>
            <Height>1.2cm</Height>
            <Width>17.4cm</Width>
            <ZIndex>1</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Tablix Name="Tablix4">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.9cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox48">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>DESCONTOS</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox46</rd:DefaultName>
                          <Visibility>
                            <Hidden>=IIF(IsNothing(Fields!Descricao.Value), True, False)</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <TopBorder>
                              <Style>None</Style>
                            </TopBorder>
                            <BackgroundColor>WhiteSmoke</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>6</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox53">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="&lt;b&gt;" &amp; Fields!Descricao.Value &amp; "&lt;/b&gt;: " &amp; Fields!Valor.Value</Value>
                                  <MarkupType>HTML</MarkupType>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>8pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox53</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>Solid</Style>
                            </Border>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>6</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details1" />
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                  <Visibility>
                    <Hidden>=iif(Fields!Descricao.Value = Previous(Fields!Descricao.Value), True, False)</Hidden>
                  </Visibility>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DtsDescontos</DataSetName>
            <Top>9.6cm</Top>
            <Left>1.8cm</Left>
            <Height>1.2cm</Height>
            <Width>17.4cm</Width>
            <ZIndex>2</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
          <Textbox Name="Textbox4">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>Assinatura</Value>
                    <Style>
                      <FontFamily>Calibri</FontFamily>
                      <FontSize>9pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style>
                  <TextAlign>Center</TextAlign>
                </Style>
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>Textbox1</rd:DefaultName>
            <Top>13.58053cm</Top>
            <Left>6.26264cm</Left>
            <Height>0.70583cm</Height>
            <Width>8.47473cm</Width>
            <ZIndex>3</ZIndex>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <TopBorder>
                <Color>Black</Color>
                <Style>Solid</Style>
                <Width>0.25pt</Width>
              </TopBorder>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
        </ReportItems>
        <Height>14.28636cm</Height>
        <Style />
      </Body>
      <Width>21cm</Width>
      <Page>
        <PageHeader>
          <Height>5.64246cm</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox1">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>COMPROVANTE DE TRANSAÇÃO</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>2.19677cm</Top>
              <Left>1.8cm</Left>
              <Height>0.6cm</Height>
              <Width>13.37341cm</Width>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Image Name="EmpresaLogo">
              <Source>Database</Source>
              <Value>=First(Fields!EmpresaLogo.Value, "DtsRecibo")</Value>
              <MIMEType>image/png</MIMEType>
              <Sizing>FitProportional</Sizing>
              <Top>2.19677cm</Top>
              <Left>15.17341cm</Left>
              <Height>1.8cm</Height>
              <Width>4.02659cm</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <TopBorder>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </TopBorder>
                <RightBorder>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </RightBorder>
                <PaddingLeft>18pt</PaddingLeft>
              </Style>
            </Image>
            <Textbox Name="Textbox2">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=UCase(First(Fields!EstabelecimentoNome.Value, "DtsRecibo"))</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>2.79677cm</Top>
              <Left>1.8cm</Left>
              <Height>0.6cm</Height>
              <Width>13.37341cm</Width>
              <ZIndex>2</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <TopBorder>
                  <Style>None</Style>
                </TopBorder>
                <BottomBorder>
                  <Style>None</Style>
                </BottomBorder>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox9">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EstabelecimentoCnpj.Value, "DtsRecibo")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>3.39677cm</Top>
              <Left>1.8cm</Left>
              <Height>0.6cm</Height>
              <Width>13.37341cm</Width>
              <ZIndex>3</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <BottomBorder>
                  <Style>None</Style>
                </BottomBorder>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox10">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox1</rd:DefaultName>
              <Top>3.99677cm</Top>
              <Left>1.8cm</Left>
              <Height>1.64569cm</Height>
              <Width>17.4cm</Width>
              <ZIndex>4</ZIndex>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Width>0.25pt</Width>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Image Name="Image1">
              <Source>Database</Source>
              <Value>=Parameters!BarCode.Value</Value>
              <MIMEType>image/jpeg</MIMEType>
              <Sizing>Fit</Sizing>
              <Top>4.13789cm</Top>
              <Left>7.84886cm</Left>
              <Height>1.37662cm</Height>
              <Width>5.30227cm</Width>
              <ZIndex>5</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Image>
            <Textbox Name="Textbox3">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="Em caso de dúvida entre em contato:" &amp; System.Environment.NewLine &amp; "Telefone: " &amp; Parameters!Telefones.Value</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>8pt</FontSize>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style />
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox3</rd:DefaultName>
              <Top>1.335cm</Top>
              <Left>1.8cm</Left>
              <Height>0.86177cm</Height>
              <Width>8.20302cm</Width>
              <ZIndex>6</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox6">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Parameters!PagoNoCartao.Value</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>14pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>Red</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox5</rd:DefaultName>
              <Top>1.335cm</Top>
              <Left>10.49284cm</Left>
              <Height>0.86177cm</Height>
              <Width>8.70716cm</Width>
              <ZIndex>7</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <VerticalAlign>Middle</VerticalAlign>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageHeight>29.7cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0cm</LeftMargin>
        <RightMargin>0cm</RightMargin>
        <TopMargin>0cm</TopMargin>
        <BottomMargin>0cm</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="BarCode">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="Telefones">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="PagoNoCartao">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <AllowBlank>true</AllowBlank>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>BarCode</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>Telefones</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>PagoNoCartao</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <EmbeddedImages>
    <EmbeddedImage Name="CARTAO">
      <MIMEType>image/png</MIMEType>
      <ImageData>iVBORw0KGgoAAAANSUhEUgAAAmMAAAImCAIAAACQPxr1AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAEUPSURBVHhe7d3pViLLEobhc//XpDLPCM6zOM+goowZmVEHtLZCMYhoq+j7rDh7dZNZ1efft7IqK/J/HgAAGI2kBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoA+EyuIloT/y/4FUhKAPg0dm/fRONm8cDd1P2fMPtISgD4HO7s3MyF/Mps2dMHfwAzjqQEgE+g9/evMflcyRXZv/OHMctISgD4MHVuLR1Myk5Fc2ajrG3rT8NsIikB4BO43WWJDCRlp0IJs3yhj21/HmYQSQkAn6LhSmsSG0jK5yocunLDn4hZQ1ICwGdp6fmeTQzE5HNld+zZoz8RM4WkBIBP1Na7U5sKmfmBpOxUalUO7v2JmB0kJQB8LvGaZZuLSmggKTsVW5StsifOn4tZQFICwKfrBGHNrWWH7/EJJ83KpdaMPxc/HkkJAP9Iw+2tjNzjUzx2laY/ET8bSQkA/05Tz3Zl1B6f3K69YI/PDCApAeCfamvlxCZH7fFZk0P2+Px0JCUATMqVG/b0Rg4u7UHZXrZVPDXqqT86mmj9xmbCEh5Iyk7FC7Jd8ezbd8F3ISkBYCLupmIWMq8JF0qbUNyk92XvphucN61O1unIwHOe1t1udviT2HDK
rF5rg7O6fiiSEgDepvV6MN76KmbCWRMpmPyR7F/Z41t7Z9R28jG44tTjNZsOXPtU8xFTPHF37PH5iUhKAHibCUeD2Tau4iaWN7Els3wu+7f2vOoenhLzKTX1smTzgfn/VX7fXta6k/CTkJQA8AbJ5oOR9q5aiJtE0WTWZL3cCUJ313blS1ccsccnvWGPq/4/jJ+BpASAcezaRjDMPliRJVnal+K+JAeGnitRlN3bCTYK4YuQlAAwWrtl89lgknVqPizRhM09/7l/aNKKmrnwwI//VTznHvz/C/h2JCUAjGaNlorBGHuu9LrdPtT7U7eVtpmkJKISmPCRWki4C3b3/BQkJQCM1ay6lZAZ2u48uSxbV0/bW+vew7lellypaGMRiUdlITztWvOpwkn/X8cPQFICwFuk5TYTwzu4htImf+xaL59CGs9ZT8ve7bGe7ridRUmEJBodHrSjS+sc+/yDkJQAMBEtFSUVjLRuzcdMat/dDWab81zLsw2vcamnW2532S5241bCYbMwcJOe0puyfwP8DCQlAExKz7ZtNhhsfiW27EV17H5V47Vrnml59VM9XHabhc6tuq82e1Kz81d3dOxPx49BUgLAO2j5xBZes62vYqu2VJn04w4nXvPeq5X1ct1tL7mNRbe9open/ih+EpISAN5HHypuecQen0hBNi6n+hLSeJ71/4gfhqQEgPdrN9xGTKIDSdmpcMYUTrRN7P0eJCUATMntLw7vs7MQN6mSq/JB5C9BUgLA9PRk02YGkvK5ktv26oGedL8ASQkAH6LXR3ZxICafK75mD28Jy1lHUgL409yj5xqiHztEWavXbqnve4/Xihaf+vhghpGUAP4ud3YhibSZy8v6qT2tOuf/Po1Wza1HJDKQlJ0KZ2XpVOUjd8d3IikB/FF6d9cfaVnJrMj2rb1ra2Oqnauqbjcnid57/lcLCZM+cI8tfyZmCkkJ4E9qt4Nh9lzzUTMXM7l9W7p299M8k9Xj9RF7
fMImuWOvH3kSO3NISgB/kcSSA0kWqJhJLknhyJbbWmvre/JNrw5sPnC3/yq+7u75emTGkJQA/hwpLAUDbGRFumvB+RXZvrAX9cnjUu8vu3t8Bg/eisZd9dGfhBlBUgL4W/Ty2Azdd/NmhfOS2ZCDmmsYNRNsz2k8uNVQYI+P3d3zRzE7SEoAf4uWz7onePTWfFg6NbQ13WB1X2QuytKhPaq4NxeZzrqdjMT9a+3yqv87ZgpJCeCPaVfdauo1+Tq1EJGdsp5vu/WCHdqdbnglTHpFVs7tg2jTjclMPVq1+YQtLPl/x6whKQH8OXp7GIy9hbSrtjop6lUv9XzTxkMSCQfnDK3nvbLpXdm7dDftkXnZrnnv2hSEn4SkBPD3mJrbTPRvt4l0DwBpPr99NJ4V73bPrRZsunfOWxUrSmbHnlttCLH4m5CUAP6kRkVCkf6oi9jzB3/U57zqhZ5v21zIhCdbYnaqs8pcWJONU3tyT1z+DiQlgD9J297pkoT7Qy6x6e7b/oRX1jNNr3bo1vI2G+ubP74W8rJ97t8Ds4ykBPBXObGpRCDeZPdSR38AotcDLzjHlt3a96/ELCMpAfxZzrsvSaz/GWx40V6M7AxgQtG+yWNLMjn/Msw4khLAX9a2S9lgwi3tu8FHsJ4n2Xxgpt1ec6V1mxsWn6EIm11/DZISwN9WPZJUIOoSsnfjj/7Hrm30zwnZ1fWnEfEad95dyRbikny9j9ZqT6P4DUhKAH+cut3lYNee9Jqtvh685UrB15MDT1Zd5z5e7dTtr7mNglau/Z/xK5CUAP68dtkW+7v2zEVl+fD54am7vOof6vb0Gf1k1Xoy7NEtZhlJCQCenu8FvxiJ5u1FSx8e+358Kp6s/jUkJYDfqLPkc9r974RszW0GN+yYxIqZDzYccFc8Wf1zSEoAv4hTNWKvH2X3TjbOZOfcnjx043KCyNTro+DbyoFyB4f+bPwlJCWA38J6slIykfRT1/LnbIuYUMbE
1+2N6NtvD+taGnfCs13f9CfijyEpAfwG7vRWUvluNA4knJnrPkG1B1Vt+pNHeijbWOBavyS36M/B30NSAph5dnu/u5QciLf+CsvBzVtPYUVvdvvPGHmqUFSNPwN/EEkJYLbZ3UOzEGzfOqIW7VnVv2yUds0VAld1KmUv37oQvxdJCWCGufOLgVQbV1Lc05Z/7Uj3JQkPPMVd3He1114E+FNISgCzSu/vg3n2VJIKyYjXjSaStuWGf/0o0nIryYFnsFHZv/In4I8hKQHMJmP6k6xbdnNFKxde886rVfRizSbigQlmPmJP3gw89drXkhy4NrFqb+i/8xeRlABmkiQC/edC7uikE5/+cIdar35i88GdPnIwwenKqnowcM5z59qVEgeE/EEkJYDZI8XlQIbZrW1/rJ+eb0n/c1R7POFD1LotBA/kMuGcPX/zWxP8NiQlgBljt3cDASaFJX9siAdJxnomx93FrT/yFr3ek3B/N7tQ1J3Tze7PISkBzBJ3etYXXZ2KJvyxodo1SSdfJkum6GpGm23XMGqcNu3YDyXrbrWvGWxnLeuP4C8hKQHMDL297c0tv4on+jhyo43e3ZhI7ycfKclum0jGhNImFDehvCmU7P61rbS77x/d80U9Koc24V8ribT/I/4YkhLAjLBiMyM6DBQO3fBvP6y7Pg5OHlJxE8ub1Jrs3bty3dXEv7rL6cH6c+d0T3p/xx9CUgKYFW27mulPuJ7K7tizR3/iC2uGtKZ7o5ImtS4rx3JcV3FqPc/c6c2pPg7cHH8GSQlgZujtqVsLfhzyWqlVObjzpz6R1OhkfaPCJpQ0oaLs3mjTDXssiz+EpAQwU2oVt502C8EDlv2K5WWr7Ek32OzyanB0iopk3D2fhfx1JCWAH82Vqy7w4LNd11JOYkMP2AqZcNKsXNqNncDvEo3ZXMIuxm08LpmERPtGR1Unbv1/FH8YSQng59Jy2czFTKooh3U1PY9A1enJqqR7P5QcW5GY
Plx5arrlWc8+eLcnerJpMyHpLE8XBuY/laSy/j+Hv42kBPBDaaPxmlvhhOzeaauvlZxe7trCROdtabPZme5f9ko6y1Ovden2lu1yNti7bj7sOV5PooukBPBDmXBwyWgvH/yx/2jlxK2+NhYYWlqu+LOHe07QRy2f6PmWTYUkEpFYVB/Y7AofSQngJ5LcYiDwOmW3z/zhXg83bis98muQSFq2K56drK+5iidNr3mtdc5txiuSEsCPY9c3g4E3N7a5a+tR97MSHbXHJ2VWr7VB3wBMiaQE8LO4g8Ng1HViMp7yh0dxosfLkhqxx2c+Yoon7o7vPTANkhLAD+KuroMh91Ree1wj8xd6sW3zAycwv1R+z17W/KnAxEhKAD+FPtaC2fZUenfvz5iAlo/cyug9PukNe8Q7SLwPSQngZ1A1C0NeNLqzc3/C5KpXbjMduM9rJYqye9v55/zJwFtISgA/gmRywUibC8nG3pDPICfRqOpeRvrO2+qpSMasXWuTPT6YCEkJ4PvZ1fVgmHUrbJKr9uHpQI8p2LYeFSUZHbjtUy1EzdKpu2/5k4HRSEoA38zul4Ix9loRM5+zV3Wddvmn55s2N3KPj2xdTBnD+EtISgDfyV1cBtJroMJmLi1HFdf2L3kvvTmwy8P3+NjVXX8SMBpJCeDbaPUhEF2jKyVbZ27q7yHvL9zGwMGWkbg/CoxFUgL4JtYGo+upZOCX/yohS/u20QlY/wbvU7/T3YwJvx5s+dQ2HXgbSQnge0gy8xJaL2V3t/Ria3RYxkxq3dY8ne6QD9PSw0VJRCUc08r4tunAK5ISwDewSysDKRiyq+tPYw3vbl9io86ejJrQor1pTr3Hx7s/07tb/8/ABEhKAF/N7e4N5F9IMjl/uKMTg/VjmxvVPSBs5jJycjf1Hh/gXUhKAF/MuIO1YPgtRIY0zWlcuLXF0U9iU7Jz4fgeEv8eSQngy9Vu3HrfTlStjWhc3iq7vZXRYZmUlQPX8OcC
/whJCeA71G91J2PC3W5z7vra/3Eoc69nm6PDMm4yG9Pv8QEmQFIC+CbS9BrHejFBA3Spe5U9iYzoSzcXNZGCrbSn3+MDjEVSApgFzni1I5sd6B7g19Men7OqTnSKJfA+JCWA2dE4dyv50U9i07J3yR4ffDqSEsCXcJ6KU/HUaYf/4xRaN253edwen7VD9vjgc5GUAP6lTkDWW658L9unkl8x8axZ2DDLx1K6the17vpvitBs3+nJxpg9PpLbsvVpm94BA0hKAP+Getoydrts5sPd6r5K7M2ziAlnTKIo23eu1n73zlWpaXlXQiMOap6LmfhKZwnrTwY+hqQE8A+o527bJpIx86M2rP5X8520K8pB2T36l07Ktb3aoU0PP07LpBe1wfYefA6SEsDns7t7JjaqF93wkpUDe/XO3TiqXuPMLuUGn8S6yyt/DvBhJCWAT2bX90xkVH/zsZVeticNz73zBWPz2m0v9Yal3Sv5Q8BnICkBfCa7f9B9B9mTW++rWEZKNW298xVj+1aP1rthGQnZtTX/R+CTkJQAPo27uAwm30uFwpJO2GxcogNDgZqPyHZZH9/ZcUcetXLozp7O7QI+FUkJ4HPow0Mw855KCjm9OdbGvdeuea177/FGK7suF5N4YDdsX8nONT0E8EOQlAA+g3OSzgbSrlPuZF+bjwPfNtrO//Riyy5lRn8WmZCdc+1OBL4ZSQngE7ijQxMJvp60+yXPG/sQtXXj9kcfqhXK2YNbfybwfUhKAB8mYgtLgZyzq5PtrHE1vT2w/de+VnrV3fsTge9CUgL4KHd2Gky4REab/ugE2l7teNShWlI48GcB34SkBPBB1u1vBuNt/ajbFmByzniPh5KKB+7TrUjOltv+NOA7kJQAPsa0JRZcDrrKe3vTPans2+xgWEZk6ZB25/hGJCWAD6pLuD8pIxlXnfLgKz3akFDPrZ4rXqDZOb4RSQngY5pXEu7f9RpbdI/TPi+1D24t33e3Ts0n
7Y0/Dnw9khLAh+jjpQkk5ULClev+8Pvp1cHgdyOyV+YBLL4LSQngYxp3waTsBNvhB4KtXZHkwA03Tv1R4MuRlAA+pl03oWBfOimUXGPqd4vGrgRP7JKtQ38Q+HIkJYCPkZaND56xFbdHFX/Cu4nbCLyqDMs6SYlvQ1IC+CCrR4tmoTfYniqxbi+n2gFrjV1d7LvVfNge0aoH34akBPBhjYoMNH3tlCzvuTt/yju0WhIOLlK1Mv0WIeCDSEoAH2aNWxl8ANstyW/bd2acls9MqD93E6v6aPxh4MuRlAAmop2SMZt0riU5rBfdXMwkNp24SXvbqUqu/9HrXMhu0voV34mkBDCW89zdgy2dmdSyWTy2N7XhkaeqB0WJ9iXcf9VZbq7Yy5pOsCy0WzuByyWR04exR3cB/xhJCWAkrbfc8Y2J9C4Wi7Z0PWJ9aNxGccRhk2Ezl5btSze6Haw6545PghcuRGRjy58BfBOSEsBw2mzJ8mowujoVWbKHIzbquFu3UQjO7ykp7Njjh27Q9j/H1ceaPTgMTO5WKKOtlj8J+CYkJYDhTCxh5oMtBcx8yGaT7uzCnzTo4cKtJINX9VY4a+IrsnNvL6rusmYvarJ71llxBqc9lZZv/dsC34ekBDCEZAfalIdDdnXZHe159Qd/0ijVK7c5PPkGKjHwy2u54xP/hsC3IikBBNmNrUBoSS7njvc8nbhBXaOqe5mhH1lOWHZr278V8N1ISgB99PpM4n1LPUlnPfP+zxmlrUdFSQ3/znJMSTRqNzb9mwA/AEkJoIczdm+jL7oWIp77wDnKtwdufXHEhtghJbGkO9j3rwV+BpISQI9mLRBd7qbmD03NVfVi3+b6bjuk5sM2GvUajQ8FM/APkJQAXrnDLTPfk16RtN5P9JGGmk4geq7i2Xvj7pva9H//j/UaVe921+VTkopKqOef6KwjO5VP6U3Za03VUR34x0hKAK/sxkpvhplk4c3GOva6JuslkyyacMos
xLodecJZKezKzrWTToT60548LRZbN3p9qDd7erSsZ5t6d+I17j3ho0n8XCQlgBdiV4t9SRnO2KvhGabi2YNLya+Y8KivJ5MmsWIfnFr/kn7uHTtpgW9FUgL4jzWuGOywI/ntbqD1LA215dnto25GdleQfZMHKmLmsvaqPml7dOBHIikBvLBua2kg7ZImtipHj/a6Zo/LslmSzPLAnDEV7raKvR7d7xX48UhKAK/0YlcWAlHXqeeedvGnU0F6f5+4cnvucfhDWODnIykB9DBVWxzXYW7aikjp2v8ngFlDUgLoo6c7kzcKeK35kMRjNpWQ2MDQc2W31fG6EjOJpATQTx/0cNhhWyNKwlG7VHClLa924zXvvdqtd74q0WhgmpnLumrb/yeAmUJSAhjQuNOj5b4WBMNKYlG7XNCb8+D3HtbocVHCgRO7Yvb6rUNIgB+JpAQwjKl7d3s2lQz00+nWQkgSCbeS0/srf/IQLUnH+y+M2osR5z8DPxtJCWCUzkrxXo833faSLSRtMi7ZpF3O6/GO1272fWI5RNsW+pMyFHf3H24hC3wHkhLAm4xnG5413XRU503QR0DrDxIKHk6pTVrWYSaRlAA+n1YqgZiUeEFb7OjBTCIpAXw+SaQCSWmPTvwxYNaQlAA+mRSD7e4kVXBVHr1iVpGUAD6NNlt2aycQk2Y+aje2/RnADCIpAXwOd1GR4lYwJjsVTvszgNlEUgL4ELXO3T7a/ROzMNiXp1tar/tTgdlEUgKYnt4+yOq+mRvZVN3dlP2pwMwiKYFfTsXTunE1zzVEWxJoPPch6txSeFTTO0ln3fmFPxOYZSQl8Dtp23PnFbt1ILk1E0uZ+YiZWzTpDVk5kN2yfVRtf0ZmNis2GgmePbIQtmsrWuGYLfwSJCXw67inzTWLe33p1VvzMRNKmJVze/PYWXF+TOf6a5dNP5//LOGQFAvuoNRt6wP8FiQl8KtorWkPLs3c8M01A5WTjRN37187Le22
hz3d1pMdvT1/+ivwq5CUwO+hjebgV/9vluTX7UmDgANGISmB38OE82Y+cCrkZBXLSqn2OW8ugV+HpAR+Ccnkg/n3rlqIynZFax99bwn8PiQl8BvYtfVg8j2VJKJ2MeXW8m49Y1NhE3pjxSlrp65q/ZsCeEJSAjPPHRwGAq9bsbjdWfO8WneLjXaq7Zm6Z87dVs5mxu336e7xoZk50IOkBGab3lcGj7jqlDbKnhuxOqwcua1C8CPI10rK6sEEpzUDfwVJCcwysXZreyDqQlqtdj+rHMPc6/mWHbjQr3BOdiv+TODPIymBWSZiQsFHqRP3kGt5jVMbGfEkNll0j/484I8jKYEZ5rY3Awknqzva9kcnIF7r1GaGPLw1cxGTPfAcD2EBkhKYYcYuF/vibSFqS+9tSq7ew7FdHBaWkbR9ICkBkhKYWVqtSKz/2Wk4odNtW73rhGW871bdipilU5aVAEkJzCp9vAlmWzSnZsqvIfV0RwbPzwoX1dC4B38dSQnMrPpp8GzI5Io2p22yI1W3keu7W6cW4q5CIwL8dSQlMKv07tCEIn3BFs65SsMffj+9POi7W6fmI3JU94eBv4qkBGaVPlyYSH9SzkXtwY0/PIX6jcR679Yt2bvjmBH8cSQlMLMatxINfg0pS6Xpe9Fpy+b77tYpe0oLAvx1JCXw/bQt7qr27pWba9tEIhBsZi5h96deVhq7mOy/W8QeXfqDwF9FUgLfz0TjJrYiu+9NOOe2U8FNPZ1KrNjLqdaV0rT5TN+tQgl3Tqse/HUkJfDNJF/wYyletOfvTLjmlcSHtKOT3Jq966xV/VkT0vKNhGN9t4oktemPAn8WSQl8q8aNjbwmkxR3/N8n5lazL5f3VNgkV+3jez6FtNYd7Qfvk9ryhO8p8deRlMD3amll1yz8d8BycundbyvNpc0Ovq3sVMTMr9nHpk74PWTbDNwh5K46K1PgryMpge+len/+Gk7hrPf+Jjt6uCLR13jrqU4A
5+Wo4ib4xlLiwdavUljXxrR9DIBfhKQEvol66tQ9tHuPzZLi9lTt6Fpub230ycxxWT1wtyMfoqqIFJaCV0Xi9vjUnwH8bSQl8A3U82zpSvJrJtq7kou5kyt/xnvJndsdSLveSq/ITsVZp8a/okNV3dmlLK0EJ8+FJb/hTwL+PJIS+Gp271DSBTMX3LNqN7b8GdOp3biNoSdN/lfzcTOXkMKRbJ/bnWvZPJbCemfFGZzWqXDMvycAkhL4YnZtwywEWtCFJBS1+aI/4yOt4xr3upsx4f/2B42rId+WvJQ2pm8eC/w+JCXwddzBYSCTTDgi2aSelvwZnSjdOZHiia142rTThKa09LAgiXFBOKZkPqzlsn8rAE9ISuCL6H1FEj2rvXDUFpN6d+xZ8dTPxO7Omu6KM2LmIyaxYw/LrmIm/cyjh55tDPSle7skk3QX9K4DgkhK4Euo6uVlbyxpo+65vgy0Wzu9E/xKLEluU0qPrin6rk82Hi9cadUO9robUXZlTa+v/WsB9CApgS+iO2svsSRrh/6v/3Enpy+jQ2ohZubzsnlhLx5cz+bVt9T1/swthyQaHf0NSUiSCbex7tkpvk4B/gSSEvgietazZMysuQf/9w6t3L4Oja9QzmQ3ZPnM1py2Jukzp55te60zt120S2mJh7qRuRA2kYgk4jabsTtrXpPWrsA4JCXwVR4uexZ2Ebt94v/ebpnhHXbGVKzbfyd/YE8qrjrxvh+pebWK17zybg+0euqZ+85P/hCA0UhK4Ku4uh4Wn1u8Sjzp/9hZ9F2dSGiS7zqGVtik12TpxF40tSlT7P0B8CaSEvhC7bpbC0si7rXb/i8d7Vtb6O8YEIpIfsmtJ6Tzh4m25ES7LzIjq7J35a7q5CXwuUhK4Gup8yT4XlCPtoI7bnIb7rEz7cbtLdpCesx+nGBFClLYkYNH17baE8cApkZSAj+AVOxKuj/z4rJ2/t/orV4duu3s02ac3jmjq9u4LmqWT+3Z
nav7twEwHZIS+BH0shR80BpftLc9O25soxuZBwW7mJZQ/8xxlTDZdVm/sI8yZdMf4M8jKYEfouH2AoeBRExmR4ekW1WvSm63YDtzJlxizsXMQrTb9Odq8p2yAHwkJfBjVK9soj/hQkk5ruvQzyZdw2ve68mSCQf7rY8pya57woYf4H1ISuDnsHo10NBuIe/qI7vyDGm5PrbcFf3qgHcjKYGfpFm1yf54m4/K+pW2hzw07cRe38zu5LBNxCXc/+N/1YlV/0oA70FSAl/E1cwEn21YrZSeuxP0VNze1Pzx/+hjrX9Ot/Sh6rmqnm659bxE+obs+oZ/JYB3IimBr+AuO+u/pGxeuMpbaWkabjluAvtgs/vusWcfrHODx0H3PVm1j3p/oUdLNh6VeESKi/7vAN6PpAT+OX18fI20xW17Ob4juXqta4nFXi/pVlgOyy9PYCWd7R8d8WS1e+xIw7u78v8KYCokJfCPddZ/831PUyW75sYvLJ3oQSH40WRs1d10I9auvJ7e9Vx2jSerwD9EUgL/1uD6z8zF3HHFHx6pbvOBrj0hWT+S7VLwx2zevwLAv0FSAv/Q4PqvU3Z51R8eS8/WB3axBh7Jhkw45s8G8M+QlMC/Yvf2g8HWWQKmsv7w25puKRe4PFBab/hzAfwzJCXwT7jzi0CqdWs+7LmhHXdGqBxI4PPKntKbsj8NwL9EUgKfT6vVQKo9lz48+jMm5nZXAzd5Lnd47M8A8I+RlMBnEwmk2nO5y6m+1qhf2cLg68mUa9DqHPgiJCXwySQR3LPaKbt74A+/n54MNIOdj5r8ibbf8yAXwLRISuAzydJKMNW6FZXdC9fy57xbo2KXEsF7LsTsddNjYQn8eyQl8Gnszm4wz14rKasHbsqdqqrlIwneMGzim+5x5DEjAD4LSQl8Dnd61p9kgxU32U1b70z1L3kHqbnd/MANQ7JfVo6bBP4xkhL4BHp7F8iwERU1kaK9bU8Rb1orB44H6dZC
3pU72QvgHyIpgQ9rtYMBNq4iZi4j51V973NTNXq1aUKBA7m6y0reVgL/FEkJfJTEkoH06pTbXXPLuYGXiy+Vlv2rd+/xMXUbjgZupQ8P/iiAf4OkBD5EFouB6OqU3djqjrWu3c7y6LBMytqRG38AV5B45S2Jvi4r3dm5PwLgnyEpgenZre2X0HopyRf84Y72nR5vjA7LuOS3u3t8Jn98qs4V0883tLv7/o8A/iWSEphaw6YGnrtG4v7gC6npza4sBN8v/lcxE1u29zLxHh/15MqtFO36pv8DgH+MpASm5rzrbUn3tZrT5rDHqa7lPR7a1ED3AL8iZi5rLx7fs8dn6i4GAN6NpAQ+RC937GL8OfO0Mvp8ZnVe49QWs+P2+BzcuLY/HcDPQVICH1Y91+MNPT71/zpG88ptFcft8dk4eeceHwD/HEkJfIqJn5y2b/VofXRYJqSw+749PgD+MZIS+HLyqFc7o8MyZhKr9sHRpg74IUhK4H20YVylbs+r9rhq760aN83yzza9hwOb8F9wDlTEzOfsVV3Fnw7gG5GUwGTU02rN7p6auZfjJ2MmnDQLG/biQac4JKSzZqyf2MXMiMVluLvH56jCHh/g25GUwNu0LZ1FZDcag3n2XFHZPHPTNZVrXrqNwugnsSnZOeeVJfC9SErgbZLdMAvBhquBkpXSlOu/dsUdrI4My/y2Z6Y4pgvApyEpgTdIds2E3ojJp0rI8ol/zXuZB73YGhqWrjz6G00AX4KkBMaxq+tPrwyDATa8Yov2atodq7bh3ZdsrO8Brzs88kcBfB+SEhjJ7h/05pZfoZCZD5mFgd+fSlaP/YunoOLVj23+acdQOERnV+CHICmB4dzlVW8EdmshbAtZvT/xmjd6UBy+bTW7+tENOM0Lt7vqDlb9vwL4biQlMIQ+PAQjcC6kt7eeaXe/F3nWvHKbA4dTJvJsVQV+GZISGGBt9/lqfwS66xt/tFf9yub7uwdE0qpkJfCrkJRA
kKQyfeE3bmeN06ON3pmSWtE2nXWAX4WkBPrY5dXe5OuU3Ri3s0ZPd3p399jSWWdF2S1x3UZ3fAkJzD6SEnhld/dfMs+vcNLd1P3hYezWWs/8mBR2ZO3QpJdNfE0K+7J2JPu3zmknOP0LAMwakhLwafm6J/N6KrttT0e0qpO6XcoH5wdqPmUiaZM7skd3rtLSiY/nAvBDkJTAM9Xbk2DIvVRyRfbv/Ik93PVpcOb4ihZMbkNWzm21rXVh6w8wE0hK4D/1O93JmNCIjjyxnNksq3ltwaO1WnDOpPXUiCexJftX7qbl3w7AT0VSAj1MQw/yEh/R5TWUMMsX+vjUB13VhCLBCe+usGydsusH+OFISiBA9XTNZkYdsBUyhUNXbkgmF/hdIiGJhSURk1hsVK+7wXLn1/4/C+CnIimBIfRqzxYTgVR7rcjLYc5+2eWi3px6XtPzHrzald4c6dGKnQ934jMws7fszq7/7wH4wUhKYDi9PXVrqUC2DS3J5D1voNuAtj3X9u5KbnvJFlODHWJlacWfCeBnIymB0R7LbjttFsaeuhWK+pNHEq99r3enbi9jU3GJhjs3lETaHwTw45GUwFitmpayEhu9ead4rPXJvpFU63kNvdxxlyXP0vEOmBkkJfAW5/RkRdKj9/gUj91t0588Eb6jBGYJSQlMpLMWtIuj9/jkdu1FzZ8K4HchKYFJaeXErSaDGflS6XU5vPenAvhFSEr8JR9/6vlw47bSg6dX+hUvyE7FczxcBX4VkhJ/hd5XZa9qL6vu4WNJ1nzU/axER+zxiaTN6rU22LAD/B4kJf4EbTTMwn/ZltmU9TN31z2GecpOclb0eFlSI/b4zEdM8cTdvWuPD4Cfi6TEn2DCgVSLmmjGJDbl4NZVGt3PN95PL7ZtPt5/257K79tL9vgAvwFJid9PsovBGOutyJKslOxxTdV77xJTy0duZcwenw1X5TEsMPNISvxydn0jGGBDK5Q2kaJZu7DXD9rw
r51I9cptBtvA+hVOuvKII6ABzA6SEr+ZOz0MptfblTCLO7J95Tp5aSfb+9Oo6l5GIsE9PnZtw58AYJaRlPi9TNstB8/GmrjiJpo2i6edJaa7a78dmNLWo6IkXw+2lGzeHwIw40hK/F7q9GTg0WsiZQsJm4kPHu4xosImviZrR/ay1cnL8S8y9XzT5uJmPixvt00HMDNISvxq9TsbeCgaislZTeuXerztVhLdvBzVRqCvIiaSMfENOa5298qO7oiuNwd6tOs1+EQE+D1ISvxuRi+3+rMwbMJL7rHdHWw/eI1ztxmX9FNkvl3Px29lZWlP9m9dZ3059PMSN9VHJwB+KpISv12r5rK9adct2brW3s837I0eb7m1zMSPZDuV6n6RuXZtKzX3yKcgwG9GUuLXU695KtHXvTbPOWevHv3xF+ZBHy51Ny3JuIR6J4+vpElvytYlzV6B34qkxB9gjdtOSuB9ZKHkasMek3Yb9tzpyYZbzfbNH1+hjD0r+3cA8LuQlPgjqjYdaKYTldK1PziMJEY33xmsaNy/DMCvQ1Lir9CjteAz1cSaKz9t7Rlgl1f7Zs6FJJkY80hWm2x2BX4tkhJ/hqvaQiaQcLJ2qAMvGO3uXnBavuCZO70+ctuZ7lPc/sjUyq1/JYDfiKTEH6KX2xLrCzkTztvjvtas7uy8b0KnIj1PVm3Tkzs9WOqErkRCJhpyx6f+EIBfiqTE7HOetr3Jtp623NZSMAhzG+6/R7B6fx8cHfVkVapaPtPKpf9XAL8XSYkZpk1x13eyfSq5PcmX7GNnzecPjVQ9s9n+L0bmE7Jx1R0ypu/3p9JK5ekyAH8XSYmZpG3rrqsmk+9JtagJJ+1pS1v+nBGcO9kOdhiI5+2jSjzV9+McT1YBdJGUmD3abMtKKZBqLyUn928cyNy6tfnAVREzH+v/JWS3dvz5AP42khKzx2TzZj54GORrRXKuWvOnjqBX+8Gr+ksKS/5UAH8eSYkZI7nFQKo9
l0Qikk3ZwqLbXfOnjtGuuo3gs9aXknjSnwYAJCVmi13fDKRapyQWs0sFrZx2P+GYcA9sZ97D1ah+6F57eDsCAH8TSYmZ4S7OTTj4NlHiCb058We8i7b0eNks9N2te8Pt4zdecwL4Y0hKzAatN+zSkOeuXrs1+ToyqN2Q6MD7zsiSu677EwCApMSs0Nu7YKTNhfTu3h+ejhM9X5Hw8/nMryWrR874UwCApMRskFgikGd2t+S5Dz8ndS2bHzgzZD5lD2nlCsBHUmIWmAdJBpIy7i4+JcycVz2QROCc55DJbroarysBdJGUmAF6eRJMskRWzZud6yblNheD5zzPxWSdBj0AukhK/HzOne72x1jIRJJT7+MZon1jFweewaZX3KM/DuAvIynx46lzBwOfUUYLKp8YlZ4ebAQ+r5R80R8D8LeRlJgB7mC9N8O6FUlp/aNJ6coP7qXtnZTtUvr1/gsRb/CIZwB/EkmJGaAn268Z5idZTI4eP/IAVh/vJZaS3Lo9aXiueyO92HtZVurjG51jAfwdJCVmgF4fDXSeC5v4mpoPbE+9u/RvFctIqabdDyjrergusaS7un6eAgAdJCVmQbMsqd6YfKpQ3J62p35E6vbWzMt+14WoveysLDu/Nr02DXoA9CEpMQucdRu514x8raKrNv0576O2WOi5T1iKByr+GAD0IikxG/SyNNjNvPsMNlty9+8++kPvriXa18ROFjf9MQDoR1JiRrTu7GKwod1zyeqxu31Pn1bnbKnUd5OFqL1mCw+A4UhKzArbuzc1ULJ66CZulq7VauByE4prkzMpAQxHUmJ2tKtuY6CTzktlVl++9xhHJHjhXMgdXvijADCApMRMqd/bIW8r/6toxqxeu/rIDbFaq0uip73AU0mmqFXa1gEYiaTEbDHew6HEYoG0e635iJlbkoMbV27531o6T62ndWO39yWZD84Px+zu0fNEABiKpMSsceLd70t6+O6e/ypsFvImvWIyW5Ir
SWbDJHJmPnhic6ckk/dvCwAjkJSYTeU9m40HYm9YDUnH1wpF/bsBwGgkJWaVXpfs0viV5RulddrxAHgbSYlZdnfudgZePU5Qkk5ppeLfBADGIikx41pVvdiwkai8NHF9q+zKmpbpgQ5gUiQlZp8znld2G3mbi49qTfBckkq6rR2vTZMBAO9AUuLXUK9xred7bjXRyUuJRiUS6v6h899oyC7n3MGWNu78uQAwMZIS30Gf6p9wnjQ89+DVbrRyqDdH2rj0lJ07AKZHUuKrdKLRdZvn2Ou67B7LWkmWT6V0ZysN99DmxCsAPxZJiS/izuqSXTKRlJmL/PfisPOHsJmLm/iyLO/bk9q/WmcCwAeQlPjntK6SXzfhYMPVYEWzJrJlK21t+RcCwE9AUuLfcqcX3aVkIBRHVmeVGZH9iqvyNBbAT0FS4h9yR2cSTw3E4dslxX17zdISwI9AUuJfcZdXZm70oR9vVnbdnjX8ewHA9yEp8U/o42Mw+XorFJZoREIDvwcqsSiHdTX+8VkA8C1ISvwDzpmFYUdcJRJub11vjrzatVc91Yt9V1qxkZAZ04gunLDHDb4hAfCNSEp8PltcDgbeXMhurmn1pjPoT/K1vOajlnKSGHOEVtyeP2jgOgD4KiQlPpk7O5FY8PWkXV7thuIo6rzaiV3KjezaupB31zV/MgB8LZISn0rErq4Hck5SWX90PKnowfrIsMztugc6EwD4BiQlPpOWy8GEmw9rY/ItOTW92BkVlrJ74c8CgC9EUuITOVfaDMbb5pHKezavyqNe73TyNXCfbsWWbYWPLAF8NZISn0glngjEmzsv+4OTk4Z3uSaxaOBW3fY9W2f+HAD4KiQlPo95CO7liaRddapVoBO3k5eFnls9V3qNd5UAvhhJic/zcBUMtmjWNaf9FtLd29xgJ7yMuzb+BAD4EiQlPo1WB5JyIeoa0webnm/IQFMCe3DjDwPAlyAp8Wm0VgmkmpmPyPlj9wzn6bRu
JBF8Wylbp/4oAHwJkhKfp1U14ZdTmp8rLMVDbU8dlW1bCD6AlY1DfxAAvgRJic/TerCpwaZ0MVuu+xPeTdx6pv9uYdlm+yuAL0VS4hMZVyr2B1s320xqz91NuQPWFtOBG9qTij8KAF+CpMRn0srZ0A47sn3lHv057yKxgQ80L+/9MQD4EiQlPpV5dMW+YHsp2Tx171xYavlcIv0faCaW9YEjuAB8KZIS7+PuavZq3Icf+nDal22vlZDVw3f0tXPOrg00W1/c9EcB4KuQlHgHvb83nUVeLGc2y2pGnBhpG7qbNqG+hPuv0pLbt7XuKVtvcqdnwcvDca3RogfAVyMpMTFjXkMrlDDLF/rY9ocCTN1m4yOOBImacMGeNXXMk1hVd3s7cGFIVje9UfEMAP8MSYlJSXygt1zh0JUb/nAf5+m1zQ72onst2bp05eaQFaIxbvDork6FU50VrT8HAL4QSYmJ6N2JhAfSq1PZHXs+Ylfr3YHNDX5e2VOJVVk5tBfGNa3WxD06e1U18aIJDZ4iEnJHfEYJ4HuQlJhMvWJTITPQhbVbqVU5GL7a0+t9Wwx+5hGshaSZj5hQzoQTTxk55GRKu7Hl3xEAvhxJiQmJ1yzbXFSGbtWJLcpW2Rt6YvPdmVsb9xj2zZJ8wb8VAHwHkhKT6wRhza1mJRIMs26Fk2blUmvDPiCpVdx2xiwMWSy+UQsRyeb9mwDANyEp8S7qeQ23tyKxgVR7ruKxu236c3u1697Fmk0nR2yIHV52Y9UzI7bXAsBXISkxhaae7kgiGGx+5XbtRc2f2Ec9uXbby5IcuGSgOoGq1zdem5gE8P1ISkynrZVj28m8oXt80utyOOqLDqOVEz1csaHQ0M20koi7/T2947hmAD8FSYmpidavbSY8vB1PvCA7Fc+NaqnT8kzDqx64vSW3teRWs3Yz74439fpU6/eee0fPOwD410hKfIT13KNbSQ/f4xNJm9VrbbzZ0Nx40vIczXcA/FAk
JYbpLAUnbbDamVd3O0sSHUjKTs1HTPHE3Q3b4wMAM4KkxCttGnt4K8u7ZvXKHlbt1aN7fErMt1Oz6Y63Ru7xye/by6F7fABgBpCUeGXCWTPf0yVnIWGSy5LblOUre3bvyvVu27nnqUOys623F93DKYfv8dmwx1V/IgDMFJISPskuBuPttZ6zM2sya5Lb7644rxvuruUGP+JoVN3q8E2tJlGU3duJH+oCwE9BUqLLrm8Eg21cdYIzYsJ5k9+SpV3Zqthy2z0YfQ5OK247LfHAJU8VyZi1G22+uccHAH4QkhKeOzgMRtq7aj7S/W+0KMU92Tiz58a1bbePT2pgZqcWombpzFXHnE4JAD8LSfnXaaUSDLMP1kLczOfM8rEkU2ZhYPS5FkuuQlgCmA0k5V+np6VgjD1XPGnzMUnHh790nKiGHDP5UrJ97v8/AICfjaT88+6PbX7YEZILUbN8rlenerHvdgs2HpVwRKY4D2RESfHQ+28jLQD8ZCQlPL3YtdlgkvmVL9mLqnabzzU970bPNt32si0mukeCdFJz1MPVCcrd3Pr/PAD8bCQlurR8YgvBMPMrtmoPKj2rP/HkwWveefVzPVpzGwWbj3ePd54f8SXlsHKHx/7NAODHIynh04eyWw4Nb3ceLcjG5YhHpcZr3mvt2rvedKsZu5iyme6ZWcE79JTd2PIvBYBZQFKiR7vhNmLDO7iGM6ZwomZMH/NOkjrPtr1GRe8u9HzVLSdtNvF6t86KMxyx+YI/HQBmBEmJILe/OPyw5YW4SZVcdbJ25/p0cpap6u2pXu7p0ZJbz+n10dMYAMwSkhJD6MmmzQwk5XMlt+3V4/s3rXaCs3PR+68DgO9GUmI4vT60iwMx+VzxNXt4S+gB+CNISoyk1Wu3FBr+KUi0KFtXhCWAv4CkxFjNmluLSGQgKTsVzsrSqcrT+0gA+L1ISrxF1e3mhp/SvJAw6QP3SAdXAL8ZSYmJ6PG6TQ8kZbfCJrnjbqbY
4wMAs4GkxKT0qmTzgZj8r+Lr9viOsATwK5GUeAe9u3TFEV3rokuyc01YAvh9SEq8U+PBrYZG7PHJyfKZOuISwK9CUuL9nHU7GYkPJGWnQknZvvEISwC/CEn52/2zzNLDlSF7fObDdmfPnwEAvwJJ+RtZpy1jq217UbVHNXtxby9r7vlTjk8NTr3Ys7m+pJRkxh8DgN+CpPxdjHOVtuQ3zHzSzEc6K7ynAIuZUMYkl0xmW44f9MH4kz+D3p7b4mtSenbMYSMAMJNIyt9DW54kVk0o8ZJbA/UUnKlt2b9yDf+qT1C/dyshu7ioD4/+LwDwi5CUv4Q9upBkzs/Ctyshixu28nkPY531LJ16APxOJOVvYLf3TCw5EIdvVTwnpZq2adwKAOOQlDPP7h+YuXgwBSeshahsV7Qm/r0AAANIytnmLi6D4TdYQ1vq9JSsnbq7z9zmAwC/CUk5w7T6EMi855K5kNtZ1PNdvTzQqz3dydl0UsLBab0lxT13T7sAABiCpJxZ1ko4Ggi8Ttn9Ta1WPO/lgarz1Hneo55t2aVx7zJlef8zN8QCwG9BUs4qt7NqQpFA2tn9Pc81/RmDajd6utFZcQau+q/iUiyxrgSAAJJyNtXqks4Fos6urvujY0jdqx3bSHR4XoYzsnvXWYUCAF6QlDPJ7ZcCISep3MQJJ553Y7PpwB38iqadoc8OALwiKWdQu2EXs33xNh+2V7X3tBFQr3Ful4Or0qeKmvypGp7CAoCPpJxBzYEtr6GE1ka/nhyleuLWhoXlQspV2/4cAPjzSMrZoxf7ZqGva50UNnW65gH35zY7sIF2PiK7N/4EAPjzSMrZ4/Y3A9kmyzv+2LtZPd8dsrsntKGsKgHgCUk5e9xBMCnN4s707xVb925lyHeW+kiLOwDoIilnjzseSMrYsmtOn5V6vhW84XzYlWlDAABdJOXs0audYLDNxe3pgz/8flq9lFD/Decj9rLuDwPA30ZSzqDbkvTv6OmU
LO25qR+XNiqS7LubWYjbR76qBIAuknIWPdh0rC/YutmWlu1pN6zW7yTWf7f5mPJFJQA8ISlnklvP9wXbc8ULttwZ8+dMTh9ug7eKpLXBjh4A6CIpZ9PdYXAV+FzxrL13+s7npm5n08z3f6C5XHo9jAQA/jaSckapWyv0Ztt/FTHzOXvbeMej01ZLMv298eaitnThjwLAn0dS/mBOvTGrw8czmxtyPqWZ66wOV+z5vZusdYDd2g7cQTJFz/ijAACS8udxntZb7qYhu+eyfiJ7N6PePOrpjoT7Qq6nUrJ15u79maO445PghaGo3T/zhwEAJOWPY8Qe3phQ3MxHnlaHITOfMZktW+sEoz+lR0NPtkafzByW/IY9a3XXpgO01bIXlwOXhCSZ8dqsKAHgFUn5g7gHZ6IJs9DJyECARU14xTWHBZipamlpYH5PRTMmfWiv665qtOXUeNo0rlKVxbXgzG6FtfzWOhQA/hiS8qewG4cmHthZ81oSjsp+2Z8a0LjX3YwJB3sR9NTTUHTZZNZMcr27zbW703XIfHd84t8TAPAfkvJHsJvbJhIP5Fa35sMSi9vlvJ6X/KlDSUsPC5IYusFn0rJbUx9IAgC/GUn5/VzpMBBafkVibmfZa1Y957wJWubo2YbNDovbNysctUsr/l0AAP1Iym/mLq+CufVUNpf0Gg3Pva+JgN6U3EZ+9B6fYRWJ261l/3oAwACS8jtpvR7MradyJ6dec9yhV1pr2lvj2nbISrN9pzd7NhYygeNBBms+JPNhrdx6lmboADASSfl9nNpMLtBGrlPu/OKN6DLGzMXMQtzMr9vb2rDHssZrP+rBos2nR35wGQq77TWt1SZ5rgsAfxlJ+W3c6bkJB48EsXv7/vBokkj1XLJkT29HZ92DXu3r0YZbCtlwRFJxW0i7nWW9PtHHe5aSADAJkvK7GLu83BN43ZLiqj84mhSDV5m5Rdm+GHeCiDqv29rOeKbumYbnTOcnfwgA8BaS
8nvo1WnwPWI0qXdvtGq127t9l7xWVopH7z9uCwDwNpLyW4g73AmknWQ2PDsu7NzpWeCS/kqaxI4zw/b4AAA+gKT8DtKWcLBLgLu680eH0duBw5aHVMzMrdn7OmEJAJ+IpPwOUpVoICkT7q7ujw7QVqt/8pgKm7lle35HWALAZyEpv8PDqUT6Ey6S18bIEzwkluib3FmAHmy5reLoDgMF2b0iLAHgU5CU30CvBtrXRdKuJf5wP1ksBibbnd3uQPtWj9ZHh2VWlo/Z4wMAH0dSfgO9Pg0G23zE1YdsfNXqje1vHSC9DVrlUa92RodlSpbP+B4EAD6IpPwGWrkOptp81JYf/eE+Lb3ee8lCSaT9n1/YpvdwYBPDG6NLdoWgBIAPIim/gd5XTCjQxC4ihVOVYbkmde9230ajEgp5MuwJrVqvfmIXM/037JZki/4cAMC0SMrvUL+zQ86STLnHlj8hqBOQj9qo+X8bRi9KAzcMjVinAgDegaT8DrbldvKBVOt+4LF8pk1/yvtYsYVC8IaRjD6OO5AEADAJkvJ76M3App6nkuO7ac72qNUkGnxV6Q5P/FEAwAeQlN+kceeKfcH2X+Xt6bv7Bgy2TZfFJW0M/+wEAPAuJOW30Zshbxa7FVu1++XJw3JI2/RQwu2d+8MAgI8hKb9P+9Etx818f8g9V6Qg6xdvhqW22vboOHhtZ0GZY8srAHwakvIbqde6sZHBTbBPFcqY+I69bGl9yHnLaj13eyerww/h0gYbeQDg05CU30u82qGkhvcNeGp3HjKLJbt/ZS8arm61btxdw57fy8qemQ98kemX3pT9ewMAPgNJ+e1UzzckOTz2XiucMZFU8MeBckfH/l0BAJ+EpPw8dvoeq3q8btPB2HtfhWNuc9u/HQDg85CUn8NdXsnauS1du8qU32boVcnmB/JvspJE0u1t+jcCAHwqkvITaOX2NbcSS5It2WtPm6LvDE29v3TbabPw1pPY/pJ4Um8u/FsA
AD4bSflR2moFoqu7E2chaqLLsnluzx/cyBOahzENr7pvMykJBe45UPOhTqbatVy3bfo0fX0AABMhKT/KRBPBDOutcM7kNmT33jVEzYR51plW1eNNt5G30aebdEKx/7NLicfc1pLWqt77chgA8G4k5YdIvtgbYCNrIW7mMmbxwB6V3YN/7QSaXu3Wuz90Oxm3s+S2inq4ppfb+nCjzbpnaVYHAF+BpJyeK20N77AzrqImvSLFE1tVbQ5pKTCa85x0Ow5Mv8EWADANknJa0nbF3EAQTlhRMx8xmX17eGOvGur8WwIAfiCSclpq9XIruE81mbYreVtMSe+P4ytSkNyWHNW1Jd0VIwDghyEpP6BdtwNfdMjRnbZu9ebIbaVlISwLfaMjKmwWYiZUlJ0re1Z10x3mDAD4N0jKjxCvsi2R/rAML7nKU4Ny2/T0Vg+KtpiRd7zOTJnMqqxduqbVFq8kAeD7kZQfY40rBL8SkfVT7f12Q+/1+kh3F7uPZN/8StKvWPe/xWN7eusqbQITAL4RSflB6rXPJfUUbK+Vsqd3/vgL1/BMVY+X7GJGIr2Tx1fMxJekeExYAsB3ISk/gdt7Wi/2Vn7HPY7a0lrT6wO3/p59s6GUO6v4VwMAvhZJ+SmqtpDpj7eYbJ/7g4NUzUKkf/64kmzevxAA8OVIys+hJ1vBTq3xZXvd9of7SSa4oOw+kg33/fJa4ah/GQDgO5CUn8Q+uNVg/klhd/D9ol1dD0yzaxuePOrDlR4WbDIeeIup9bp/JQDgO5CUn0avDyTWF3ImnJGDh972c3a/1Dch8GS1e0xXTc+33EahcyuJh/Sm7A8BAL4JSfmJ2q60GghCk1x6Oe3DXVwGR0Mjnqy6uvdY8R4HNtACAL4cSfmpHq9sLtqXhfNxWblUUa0+9P3+VDxZBYCfj6QcS9UT51n1XOfP/m9jWb3cD34xEk7acrPvl6dyNzf+RQCAH4ykHEGsttr24FqyO5LflMyyKZzZ
s5q7H76d9VWravOBUIw8Vd+P7vDYnw8A+NlIygHqaduT9LoJxbu9y1/j7enPqS1buhm/vNT7o56rhpTd2PKnAgB+PJIyyF5cSjLbn5GBysry8bgzJaXuthKjDnmWfMGfBgCYBSRlH7u9I7FkINuGVcokd50dHZetW4n0b+15rkjcnwAAmBEk5Su7UzJzwYNBRlfMJPa1OeLwZTXe2fJg2x3ZPFaanQPATCEpfe7sPJBqb9d8RI6rOmphqc6mBnI3UrTnNX8CAGAWkJRdencXjLTO+i8WcusptxyzmeTIpqyhgqu2/LsEqNOLdYkG33dKcfelFwEA4OcjKT2v3Q6EWafc0b7Wqp6T7gS50+MtuzjsveNcyJbGdNJp25WB07XmU7JLjzoAmBkkpSfx4BYed3Lq2YEXkNUrtz5ss09uV2X01p7qsU0HznkOmdSyrfG6EgBmw19PSiksBWLMbu/4YwP08dYu9E3uVnRJm2Mep6rbWw127ZmLSvFosqY/AIBv9qeT0m7t9AdYSIrL/thwRq+2zEL/q8dITmtjG/e0y3Y51XdJp9JFx84eAJgFfzcp3fFJML0WovbswR8epVWVeH9SxrLaGteHoENPdwLLSru67o8BAH62v5qU9UcJBXuxdiu5IuN26HieNCTRf8l8znNvPUg1d24t/XKJJDP+7wCAH++vJmW7aYYmZadiebNZVjNimWiawdXh9kn3yJG3aPnE/nfJkO1CAICf6q8mpW3p0aLER4RlKGFWLrU2ZJ+O3pVNuPdzkYg7n/DwrIZebNlkWh/eesALAPhJ/vCOHlU9XbWZgU84Xqpw5CoNf/J/pLDcO0eKG/rWMVyv1Hg0HQCAWfOHk/KJXu3awuher7kde/7oT+02ht3tG43G7dG5PwYA+KX+elJ26O2JWx34iuOlUmtycN+Z5k7P+ofCki0+3wEA8IuRlE8ey247PepESRNfNCtDDmfW1uQPXgEAs4qk/E+rpqWsxEbs8Rko
vb31LwQA/GokZQ9n9WRFBtu0DpQ7PfMvAQD8diRlkF7u2MV4IBp7S4ob/lQAwB9AUg6hlWO3MuzYkOdKr9ujqj8VAPDbkZQjPFy7zdf+c8GKF2Wn8nYTOwDA7CMpR2s+6H5WIiP2+ETSZu1am09HPQMAfi+Scixr9HhJUr3t63pqPmqWTt19y58MAPiNSMq3dfu15kfv8cnv2yuOmgSAX4uknIiWD+3y6D0+mU17zB4fAPidSMqJVS/dxug9PoklW+YxLAD8QiTlezTudTcj4XAwJju1EHdnZX8aAOAXISnfSVp6VJBkcI+PLNItHQB+J5JyGnq+YXOve3wklvAHAAC/Dkk5Jb0p2aXuwZbSPVSEN5QA8GuRlB9wf66ne1pl1ysA/GYk5ceo9f8AAPilSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIBxSEoAAMYhKQEAGIekBABgHJISAIDRPO//TQriftxM39cAAAAASUVORK5CYII=</ImageData>
    </EmbeddedImage>
  </EmbeddedImages>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>0586eea1-7f4e-4fde-b46f-380732985fe4</rd:ReportID>
</Report>
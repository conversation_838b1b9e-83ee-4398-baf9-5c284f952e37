﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class SolicitacaoChavePixStatusMap : EntityTypeConfiguration<SolicitacaoChavePixStatus>
    {
        public SolicitacaoChavePixStatusMap()
        {
            ToTable("SOLICITACAO_CHAVE_PIX_STATUS");

            HasKey(x => x.IdSolicitacaoChavePixStatus);

            Property(x => x.Descricao).HasMaxLength(100);
        }
    }
}

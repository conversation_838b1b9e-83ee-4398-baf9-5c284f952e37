﻿using ATS.Application.Application;
using ATS.WS.Controllers.Base;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Models.Common;
using ATS.Domain.Enum;
using ATS.WS.Attributes;

namespace ATS.WS.Controllers
{
    public class PinController : BaseController
    {
        private readonly IPinApp _pinApp;

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string AlterarStatusOperadora( SMSEnviadoOperadoraModel @params)
        {
            try
            {
                //por que nao valida token aqui ??????????????
                return new Models.Mobile.Common.JsonResult().Responde(
                    _pinApp.SetEnviadoOperadora(@params.correlationId, @params.sentStatusCode == ESMSSendStatus.SENT_SUCCESS, @params.sentStatusCode, @params.sentDate)); 
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new Models.Mobile.Common.JsonResult().Mensagem(e.Message);
            }
        }

        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string AlterarStatusUsuario(SMSEnviadoUsuarioModel @params)
        {
            try
            {
                //por que nao valida token aqui ??????????????
                return new Models.Mobile.Common.JsonResult().Responde(
                              _pinApp.SetEnviado(@params.correlationId, @params.deliveredStatusCode == ESMSDeliveredStatus.DELIVERED_SUCCESS, @params.deliveredStatusCode, @params.deliveredDate));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new Models.Mobile.Common.JsonResult().Mensagem(e.Message);
            }
        }

        public PinController(BaseControllerArgs baseArgs, IPinApp pinApp) : base(baseArgs)
        {
            _pinApp = pinApp;
        }
    }
}
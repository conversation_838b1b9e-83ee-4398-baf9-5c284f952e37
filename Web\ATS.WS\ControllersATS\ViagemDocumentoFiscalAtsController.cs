﻿using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersATS
{
    public class ViagemDocumentoFiscalAtsController : DefaultController
    {
        private readonly IViagemDocumentoFiscalApp _viagemDocumentoFiscalApp;

        public ViagemDocumentoFiscalAtsController(IViagemDocumentoFiscalApp viagemDocumentoFiscalApp)
        {
            _viagemDocumentoFiscalApp = viagemDocumentoFiscalApp;
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetDocumentosFiscaisByViagem(int idViagem)
        {
            try
            {
                return ResponderSucesso(string.Empty, _viagemDocumentoFiscalApp.GetByViagem(idViagem));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
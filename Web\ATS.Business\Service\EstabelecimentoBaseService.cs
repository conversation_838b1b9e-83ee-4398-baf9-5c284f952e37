﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.IoC.Validation;
using ATS.Domain.DTO.Estabelecimento;
using ATS.Domain.Enum;
using Autofac;
using AutoMapper;

namespace ATS.Domain.Service
{
    public class EstabelecimentoBaseService : ServiceBase, IEstabelecimentoBaseService
    {
        private readonly ILifetimeScope _scope;
        private readonly IEstabelecimentoBaseDocumentoService _estabelecimentoBaseDocumentoService;
        private readonly IEstabelecimentoBaseRepository _repository;
        private readonly IParametrosService _parametrosService;
        private readonly IUsuarioService _usuarioService;
        private readonly IParametrosAdministradoraPlataformaService _parametrosAdministradoraPlataformaService;
        private readonly ICredenciamentoRepository _credenciamentoRepository;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IEmailService _emailService;
        private readonly IEstabelecimentoBaseAssociacaoRepository _estabelecimentoBaseAssociacaoRepository;
        private readonly IUsuarioEstabelecimentoRepository _usuarioEstabelecimentoRepository;
        private readonly ICredenciamentoService _credenciamentoService;
        private readonly ICombustivelJSLEstabelecimentoBaseRepository _combustivelJslEstabelecimentoBaseRepository;
        private readonly IEstabelecimentoBaseContaBancariaService _estabelecimentoBaseContaBancariaService;
        private readonly ICombustivelJSLService _combustivelJslService;
        private readonly IEstabelecimentoBaseProdutoRepository _estabelecimentoBaseProdutoRepository;
        private readonly IEstabelecimentoProdutoRepository _estabelecimentoProdutoRepository;
        private readonly IWebhookService _webhookService;
        private readonly WebhookActionDependencies _webhookActionDependencies;
        
        private IEstabelecimentoService _estabelecimentoService => _scope.Resolve<IEstabelecimentoService>();

        public EstabelecimentoBaseService(ILifetimeScope scope, IEstabelecimentoBaseDocumentoService estabelecimentoBaseDocumentoService, IEstabelecimentoBaseRepository repository,
            IParametrosService parametrosService, IUsuarioService usuarioService, IParametrosAdministradoraPlataformaService parametrosAdministradoraPlataformaService,
            ICredenciamentoRepository credenciamentoRepository, IEmpresaRepository empresaRepository, IEmailService emailService,
            IEstabelecimentoBaseAssociacaoRepository estabelecimentoBaseAssociacaoRepository, IUsuarioEstabelecimentoRepository usuarioEstabelecimentoRepository,
            ICredenciamentoService credenciamentoService, ICombustivelJSLEstabelecimentoBaseRepository combustivelJslEstabelecimentoBaseRepository,
            IEstabelecimentoBaseContaBancariaService estabelecimentoBaseContaBancariaService, ICombustivelJSLService combustivelJslService,
            IEstabelecimentoBaseProdutoRepository estabelecimentoBaseProdutoRepository, IEstabelecimentoProdutoRepository estabelecimentoProdutoRepository, IWebhookService webhookService, WebhookActionDependencies webhookActionDependencies)
        {
            _scope = scope;
            _estabelecimentoBaseDocumentoService = estabelecimentoBaseDocumentoService;
            _repository = repository;
            _parametrosService = parametrosService;
            _usuarioService = usuarioService;
            _parametrosAdministradoraPlataformaService = parametrosAdministradoraPlataformaService;
            _credenciamentoRepository = credenciamentoRepository;
            _empresaRepository = empresaRepository;
            _emailService = emailService;
            _estabelecimentoBaseAssociacaoRepository = estabelecimentoBaseAssociacaoRepository;
            _usuarioEstabelecimentoRepository = usuarioEstabelecimentoRepository;
            _credenciamentoService = credenciamentoService;
            _combustivelJslEstabelecimentoBaseRepository = combustivelJslEstabelecimentoBaseRepository;
            _estabelecimentoBaseContaBancariaService = estabelecimentoBaseContaBancariaService;
            _combustivelJslService = combustivelJslService;
            _estabelecimentoBaseProdutoRepository = estabelecimentoBaseProdutoRepository;
            _estabelecimentoProdutoRepository = estabelecimentoProdutoRepository;
            _webhookService = webhookService;
            _webhookActionDependencies = webhookActionDependencies;
        }
        
        public ValidationResult Add(EstabelecimentoBase estabelecimento)
        {
            try
            {
                var validationResult = new ValidationResult();
                
                validationResult.Add(AssertionConcern.AssertArgumentIsValidCNPJ(estabelecimento.CNPJEstabelecimento, "CNPJ informado é inválido."));
                if (!validationResult.IsValid)
                    return validationResult;

                var estabelecimentoExistente = _repository
                    .Find(x => x.CNPJEstabelecimento == estabelecimento.CNPJEstabelecimento);
                if (estabelecimentoExistente != null && estabelecimentoExistente.Any())
                    validationResult.Add("Já existe um estabelecimento cadastrado para o mesmo CNPJ");

                if (!validationResult.IsValid)
                    return validationResult;

                if (estabelecimento.EstabelecimentoBaseContasBancarias != null && estabelecimento.EstabelecimentoBaseContasBancarias.Any())
                    foreach (var contaBancaria in estabelecimento.EstabelecimentoBaseContasBancarias)
                    {
                        EstabelecimentoBaseContaBancariaService.ValidarCnpjTitular(contaBancaria.CnpjTitular, estabelecimento.CNPJEstabelecimento);
                        Validator.Validate(contaBancaria);
                    }

                var added = _repository.Add(estabelecimento);

                var credenciamentoAprovado = _credenciamentoRepository
                    .Include(x => x.Estabelecimento)
                    .FirstOrDefault(x => x.Status == EStatusCredenciamento.Aprovado && x.Estabelecimento.CNPJEstabelecimento == estabelecimento.CNPJEstabelecimento);

                if (credenciamentoAprovado != null)
                {
                    credenciamentoAprovado.IdEstabelecimentoBase = added.IdEstabelecimento;
                    _credenciamentoRepository.Update(credenciamentoAprovado);
                }

                return validationResult;
            }
            catch (Exception e)
            {
                _logger.Error(e.GetBaseException().Message);
                return new ValidationResult().Add(e.GetBaseException().Message);
            }
        }

        public ValidationResult EmailSolicitacaoRecebida(EstabelecimentoBase added, int? idEmpresa, int administradoraPlataforma)
        {
            try
            {
                string nomeAplicativo = string.Empty;
                if (string.IsNullOrWhiteSpace(added.Email))
                    return new ValidationResult().Add("Não foi possível obter o endereço de e-mail do estabelecimento", EFaultType.Alert);

                var empresa = idEmpresa.HasValue ? _empresaRepository.Get(idEmpresa.Value) : null;
                
                _logger.Info($"administradoraPlataforma: {administradoraPlataforma}");

                nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;
                
                _logger.Info($"nomeAplicativo: {nomeAplicativo}");

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel { Assunto = $"Solicitação de credenciamento enviada - {nomeAplicativo}" };
                var caminhoLogo = _parametrosAdministradoraPlataformaService.GetCaminhoLogo(administradoraPlataforma);
                
                _logger.Info($"caminhoAplicacao: {caminhoAplicacao}");
                _logger.Info($"caminhoLogo: {caminhoLogo}");

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\solicitacao-credenciamento-enviada.html"))
                {
                    var logoHeader = empresa?.Logo != null
                        ? new LinkedResource(new MemoryStream(empresa.Logo))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource($@"{caminhoLogo}" /*caminhoAplicacao + @"\Content\Image\logo-ats-login.png"*/)
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logo = new LinkedResource($@"{caminhoLogo}")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    //var fileName = administradoraPlataforma == 1 ? @"\Content\Image\ats-tudo-certo.png" : @"\Content\Image\ats-tudo-certo-extratta.png";
                    var bannerTudoCerto = new LinkedResource(caminhoAplicacao + @"\Content\Image\ats-tudo-certo-extratta.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerTudoCerto.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", added.RazaoSocial);
                    _logger.Info($"Usuario: {added.RazaoSocial}");
                    html = html.Replace("{EMPRESA}", empresa?.RazaoSocial ?? string.Empty);
                    _logger.Info($"EMPRESA: {empresa?.RazaoSocial ?? string.Empty}");
                    html = html.Replace("{logoATS}", logo.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);
                    _logger.Info($"NomeAplicativo: {nomeAplicativo}");

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerTudoCerto);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logo);
                    emailModel.AlternateView = view;

                }
                emailModel.Destinatarios = added.Email?.Split(';').ToList();
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                _logger.Info($"Erro: {e.GetBaseException()}");
                _logger.Error($"{e.Message} {e.InnerException}");
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        public List<EstabelecimentoBase> GetAssociacoes(int? ignoreId)
        {
            var assocs = _repository.Find(x => x.Associacao).Include(o => o.AssociacoesBaseEstabelecimento);

            if (ignoreId > 0)
                assocs = assocs.Where(x => ignoreId != x.IdEstabelecimento);

            return assocs.ToList();
        }
        
        public List<AssociacoesEmpresaDto> GetAssociacoesEmpresas(List<int> idsEmpresa)
        {
            return _repository.GetAssociacoesEmpresas(idsEmpresa);
        }

        public List<EstabelecimentoBaseAssociacao> GetEstabelecimentosAssociacoes(int? idEstabelecimentoBase)
        {
            return _estabelecimentoBaseAssociacaoRepository.Find(o => o.IdEstabelecimento == idEstabelecimentoBase && o.Associacao.Associacao && o.Associacao.Credenciamentos.Any(c => c.Status == EStatusCredenciamento.Aprovado)).Include(o => o.Associacao)
                .Include(o => o.EstabelecimentoBase).Include(o => o.Associacao.Credenciamentos).ToList();
        }

        public ValidationResult Inativar(int idEstabelecimento)
        {
            try
            {
                IEstabelecimentoBaseRepository estabelecimentoRepository =
                    _repository;

                EstabelecimentoBase estabelecimento = estabelecimentoRepository
                    .Get(idEstabelecimento);
                estabelecimento.Ativo = false;
                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                estabelecimentoRepository.Update(estabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(
                    $"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public bool UsuarioTemEstabelecimento(int idUsuario)
        {
            return _usuarioEstabelecimentoRepository.Any(x => x.IdUsuario == idUsuario);
        }

        public int GetEstabelecimentoPorUsuario(int idUsuario)
        {
            var estab = _usuarioEstabelecimentoRepository.FirstOrDefault(x => x.IdUsuario == idUsuario);
            if (estab == null)
                throw new Exception("Nenhum estabelecimento encontrado para o usuário informado!");

            return estab.IdEstabelecimento;
        }

        public ValidationResult Reativar(int idEstabelecimento)
        {
            try
            {
                IEstabelecimentoBaseRepository estabelecimentoRepository =
                    _repository;

                EstabelecimentoBase estabelecimento = estabelecimentoRepository
                    .Get(idEstabelecimento);
                estabelecimento.Ativo = true;
                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                estabelecimentoRepository.Update(estabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(
                    $"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public IQueryable<EstabelecimentoBase> GetQueryByCnpj(string cnpj)
        {
            var repository = _repository;

            return repository.All().Where(c => c.CNPJEstabelecimento == cnpj);
        }

        public IQueryable<EstabelecimentoBase> GetQueryById(int id)
        {
            var repository = _repository;

            return repository.All().Where(c => c.IdEstabelecimento == id);
        }

        public IQueryable<EstabelecimentoBase> GetEstabelecimentosJSL()
        {
            return _repository.Where(c => c.PertenceRedeJsl);
        }

        public ValidationResult IntegrarEstabelecimentoJSL(EstabelecimentoBase estabelecimentoBase)
        {
            var validationResult = new ValidationResult();
            
            try
            {
                var estabelecimentoBaseRepository = _repository;
            
                var existente = estabelecimentoBaseRepository.Any(c => c.IdEstabelecimento == estabelecimentoBase.IdEstabelecimento);

                if (existente)
                {
                    estabelecimentoBaseRepository.Update(estabelecimentoBase);
                    EnviarAtualizacaoEstabelecimentoWebHook(estabelecimentoBase);
                }
                else
                {
                    estabelecimentoBaseRepository.Add(estabelecimentoBase);
                }
            }
            catch (Exception e)
            {
                validationResult.Add(e.Message);
            }

            return validationResult;
        }

        public Task<ValidationResult> AutorizarEstabelecimentoJslEmpresas(EstabelecimentoBase estabelecimentoBase)
        {
            var validationResult = new ValidationResult();
            var empresaService = _empresaRepository;
            var estabelecimentoService = _estabelecimentoService;
            
            var idsEmpresasAutorizadas = empresaService.All().Select(c => c.IdEmpresa).ToList();

            for (int i = 0; i < idsEmpresasAutorizadas.Count; i++)
            {              
                try
                {
                    var idEmpresa = idsEmpresasAutorizadas[i];
                    var empresaAutorizaJsl = _parametrosService.GetAutorizaEstabelecimentosRedeJSL(idEmpresa);
                
                    if (!empresaAutorizaJsl)
                        continue;
                    
                    var empresa = empresaService.GetQuery(idEmpresa).Select(x => new
                    {
                        x.IdEmpresa,
                        x.RazaoSocial,
                        x.HorasValidadeChaveCadastroUsuario
                    }).FirstOrDefault();

                    if (empresa == null)
                        continue;

                    if (estabelecimentoService.GetQueryByEmpresa(estabelecimentoBase.IdEstabelecimento, idEmpresa).Any())
                    {
                        var estabelecimento = estabelecimentoService.GetQueryByEmpresa(estabelecimentoBase.IdEstabelecimento, idEmpresa)
                                                                .Include(c => c.EstabelecimentoProdutos).First();

                        estabelecimento.Ativo = true;
                        estabelecimento.Credenciado = true;

                        foreach (var estabelecimentoBaseEstabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
                        {
                            if (estabelecimento.EstabelecimentoProdutos.All(c => c.IdProdutoBase != estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto))
                            {
                                var estabelecimentoProduto = new EstabelecimentoProduto();

                                estabelecimentoProduto.Descricao = estabelecimentoBaseEstabelecimentoBaseProduto.Descricao;
                                estabelecimentoProduto.IdProdutoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto;
                                estabelecimentoProduto.IdEstabelecimentoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdEstabelecimentoBase;
                                estabelecimentoProduto.PrecoUnitario = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoUnitario;
                                estabelecimentoProduto.PrecoPromocional = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoPromocional;
                                estabelecimentoProduto.UnidadeMedida = estabelecimentoBaseEstabelecimentoBaseProduto.UnidadeMedida;
                                
                                estabelecimento.EstabelecimentoProdutos.Add(estabelecimentoProduto);
                            }
                        }
                        
                        validationResult.Add(estabelecimentoService.Update(estabelecimento));
                    }
                    else
                    {
                        var estabelecimento = Mapper.Map<Estabelecimento>(estabelecimentoBase);

                        estabelecimento.IdEmpresa = idEmpresa;
                        estabelecimento.IdEstabelecimentoBase = estabelecimentoBase.IdEstabelecimento;
                        estabelecimento.Credenciado = true;
                        estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();

                        foreach (var estabelecimentoBaseEstabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
                        {
                            var estabelecimentoProduto = new EstabelecimentoProduto();

                            estabelecimentoProduto.Descricao = estabelecimentoBaseEstabelecimentoBaseProduto.Descricao;
                            estabelecimentoProduto.IdProdutoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto;
                            estabelecimentoProduto.IdEstabelecimentoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdEstabelecimentoBase;
                            estabelecimentoProduto.PrecoUnitario = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoUnitario;
                            estabelecimentoProduto.PrecoPromocional = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoPromocional;
                            estabelecimentoProduto.UnidadeMedida = estabelecimentoBaseEstabelecimentoBaseProduto.UnidadeMedida;

                            estabelecimento.EstabelecimentoProdutos.Add(estabelecimentoProduto);
                        }
                        
                        validationResult.Add(estabelecimentoService.Add(estabelecimento));
                    }
                }
                catch (Exception e)
                {
                    validationResult.Add(e.Message);
                    Console.WriteLine(e);
                }
            }
            
            return Task.FromResult(validationResult);
        }

        public Task<ValidationResult> CredenciaEstabelecimentosJsl(EstabelecimentoBase estabelecimentoBase, int administradoraPlataforma)
        {
            var validationResult = new ValidationResult();
            var credenciamentoService = _credenciamentoService;
            var estabelecimentoService = _estabelecimentoService;
            var empresaService = _empresaRepository;

            string chaveNovoUsuario = null;
            DateTime? validadeChaveNovoUsuario = null;
            var nomeEmpresas = new List<string>();

            var estabelecimentos = estabelecimentoService.GetEstabelecimentoPorIdBase(estabelecimentoBase.IdEstabelecimento)
                .Select(x => new
                {
                    x.IdEstabelecimento,
                    x.IdEmpresa
                }).ToList();

            foreach (var estabelecimento in estabelecimentos)
            {
                var empresa = empresaService.GetQuery(estabelecimento.IdEmpresa).Select(x => new
                {
                    x.RazaoSocial,
                    x.HorasValidadeChaveCadastroUsuario
                }).First();
                
                nomeEmpresas.Add(empresa.RazaoSocial);
                
                var query = credenciamentoService.GetQuery().Where(c => c.IdEstabelecimento == estabelecimento.IdEstabelecimento && c.IdEmpresa == estabelecimento.IdEmpresa && c.IdEstabelecimentoBase == estabelecimentoBase.IdEstabelecimento);

                if (query.Any())
                {
                    var credenciamento = query.First();
                    if (credenciamento.Status == EStatusCredenciamento.Aprovado)
                        continue;

                    credenciamento.DataSolicitacao = DateTime.Now;
                    credenciamento.DataAtualizacao = DateTime.Now;
                    credenciamento.Status = EStatusCredenciamento.Aprovado;
                    credenciamento.ModoRegistro = EModoRegistro.Automático;
                    credenciamento.ChaveCadastroUsuario = Guid.NewGuid().ToString().Replace("-", "");
                    credenciamento.DataValidadeChave = DateTime.Now.AddHours(empresa.HorasValidadeChaveCadastroUsuario);

                    validationResult.Add(credenciamentoService.Update(credenciamento, administradoraPlataforma));
                    if (credenciamento.Status != EStatusCredenciamento.Aprovado)
                        _credenciamentoService.EnviarCredenciamentoWebHook(credenciamento);

                    chaveNovoUsuario = credenciamento.ChaveCadastroUsuario;
                    validadeChaveNovoUsuario = credenciamento.DataValidadeChave;
                }
                else
                {
                    var credenciamento = new Credenciamento
                    {
                        IdEmpresa = estabelecimento.IdEmpresa,
                        IdEstabelecimento = estabelecimento.IdEstabelecimento,
                        IdEstabelecimentoBase = estabelecimentoBase.IdEstabelecimento,
                        DataSolicitacao = DateTime.Now,
                        DataAtualizacao = DateTime.Now,
                        Status = EStatusCredenciamento.Aprovado,
                        ModoRegistro = EModoRegistro.Automático,
                        ChaveCadastroUsuario = Guid.NewGuid().ToString().Replace("-", ""),
                        DataValidadeChave = DateTime.Now.AddHours(empresa.HorasValidadeChaveCadastroUsuario)
                    };

                    validationResult.Add(credenciamentoService.Add(credenciamento, administradoraPlataforma));

                    chaveNovoUsuario = credenciamento.ChaveCadastroUsuario;
                    validadeChaveNovoUsuario = credenciamento.DataValidadeChave;
                }
            }

            if (!_usuarioService.Find(x =>
            x.UsuarioEstabelecimentos.Any(y =>
                y.IdEstabelecimento == estabelecimentoBase.IdEstabelecimento))
                .Any())
            {
                var validacaoEmail = EnviarEmailCredenciamentoCadastroUsuario(estabelecimentoBase.Email, nomeEmpresas, estabelecimentoBase.Descricao,
                    estabelecimentoBase.IdEstabelecimento, chaveNovoUsuario, validadeChaveNovoUsuario);
                if (!validacaoEmail.IsValid)
                    validationResult.Add($"Erro ao enviar e-mail de criação de usuário: {validacaoEmail}", EFaultType.Alert);
            }

            return Task.FromResult(validationResult);
        }

        private ValidationResult EnviarEmailCredenciamentoCadastroUsuario(string emailDestinatario, List<string> empresas, string nomeEstab, int idEstabelecimentoBase, string chaveNovoUsuario, DateTime? dataValidadeChave)
        {
            var validacao = new ValidationResult();
            
            if (string.IsNullOrWhiteSpace(emailDestinatario)) 
                return validacao.Add("endereço de e-mail de destinatário não informado");

            var nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var emailModel = new EmailModel { Assunto = $"Aprovação de cadastro de estabelecimento - {nomeAplicativo}" };

            using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\credenciamentoJsl-cadastro-usuario.html"))
            {
                var logoHeader = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                    { ContentId = Guid.NewGuid().ToString() };

                var logoAts = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                    { ContentId = Guid.NewGuid().ToString() };

                var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    { ContentId = Guid.NewGuid().ToString() };

                var banner = new LinkedResource(caminhoAplicacao + @"\Content\Image\header-credenciamentoJsl.png")
                    { ContentId = Guid.NewGuid().ToString() };

                var nomesEmpresas = string.Empty;
                foreach (var empresa in empresas)
                {
                    if (string.IsNullOrEmpty(nomesEmpresas))
                        nomesEmpresas += empresa;
                    else
                    {
                        //não queremos renomear a razão social de uma empresa "nome e outronome" para "nome, outronome"
                        nomesEmpresas = nomesEmpresas.Replace("{_e_}", ", ");
                        nomesEmpresas += "{_e_}" + empresa;
                    }
                }
                nomesEmpresas = nomesEmpresas.Replace("{_e_}", " e ");
                
                var linkWebNovo = WebConfigurationManager.AppSettings.Get("LINK_WEB_NOVO");
                var linkNovoUsuario = $"{linkWebNovo}/usuario/novo-estabelecimento/{chaveNovoUsuario}/{idEstabelecimentoBase}";

                var html = ms.ReadToEnd();
                html = html.Replace("{0}", logoHeader.ContentId);
                html = html.Replace("{1}", banner.ContentId);
                html = html.Replace("{2}", facebook.ContentId);
                html = html.Replace("{Estabelecimento}", nomeEstab);
                html = html.Replace("{Empresas}", nomesEmpresas);
                html = html.Replace("{logoATS}", logoAts.ContentId);
                html = html.Replace("{NomeAplicativo}", nomeAplicativo);
                html = html.Replace("{validoAte}", dataValidadeChave?.ToString("dd/MM/yyyy HH:mm:ss"));
                html = html.Replace("{linkCadastro}", linkNovoUsuario);

                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                view.LinkedResources.Add(logoHeader);


                view.LinkedResources.Add(facebook);
                view.LinkedResources.Add(banner);
                view.LinkedResources.Add(logoAts);
                emailModel.AlternateView = view;

            }
            
            emailModel.Destinatarios = emailDestinatario.Split(';').ToList();
            emailModel.NomeVisualizacao = nomeAplicativo;
            emailModel.Prioridade = MailPriority.High;

            validacao.Add(_emailService.EnviarEmail(emailModel));
            return validacao;
        }

        public ValidationResult AutorizarEstabelecimentoJSLParaEmpresa(int idEstabelecimento, int idempresa, int administradoraPlataforma)
        {
            var validationResult = new ValidationResult();
            var parametroService = _parametrosService;
            var estabelecimentoService = _estabelecimentoService;
            var credenciamentoService = _credenciamentoService;

            var estabelecimentoBase = GetQueryById(idEstabelecimento).Include(c => c.EstabelecimentoBaseProdutos).First();

            try
            {
                #region Estabelecimento

                Estabelecimento estabelecimento;

                if (estabelecimentoService.GetQueryByEmpresa(idEstabelecimento, idempresa).Any())
                {
                    estabelecimento = estabelecimentoService.GetQueryByEmpresa(idEstabelecimento, idempresa)
                        .Include(c => c.EstabelecimentoProdutos).First();

                    estabelecimento.Ativo = true;

                    foreach (var estabelecimentoBaseEstabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
                    {
                        if (estabelecimento.EstabelecimentoProdutos.All(c => c.IdProdutoBase != estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto))
                        {
                            var estabelecimentoProduto = new EstabelecimentoProduto();

                            estabelecimentoProduto.Descricao = estabelecimentoBaseEstabelecimentoBaseProduto.Descricao;
                            estabelecimentoProduto.IdProdutoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto;
                            estabelecimentoProduto.IdEstabelecimentoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdEstabelecimentoBase;
                            estabelecimentoProduto.PrecoUnitario = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoUnitario;
                            estabelecimentoProduto.PrecoPromocional = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoPromocional;
                            estabelecimentoProduto.UnidadeMedida = estabelecimentoBaseEstabelecimentoBaseProduto.UnidadeMedida;

                            estabelecimento.EstabelecimentoProdutos.Add(estabelecimentoProduto);
                        }
                    }

                    validationResult = estabelecimentoService.Update(estabelecimento);
                }
                else
                {
                    var empresaAutorizaJsl = parametroService.GetAutorizaEstabelecimentosRedeJSL(idempresa);

                    if (!empresaAutorizaJsl)
                        return validationResult;

                    estabelecimento = Mapper.Map<Estabelecimento>(estabelecimentoBase);

                    estabelecimento.IdEmpresa = idempresa;
                    estabelecimento.IdEstabelecimentoBase = estabelecimentoBase.IdEstabelecimento;
                    estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();

                    foreach (var estabelecimentoBaseEstabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
                    {
                        var estabelecimentoProduto = new EstabelecimentoProduto();

                        estabelecimentoProduto.Descricao = estabelecimentoBaseEstabelecimentoBaseProduto.Descricao;
                        estabelecimentoProduto.IdProdutoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto;
                        estabelecimentoProduto.IdEstabelecimentoBase = estabelecimentoBaseEstabelecimentoBaseProduto.IdEstabelecimentoBase;
                        estabelecimentoProduto.PrecoUnitario = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoUnitario;
                        estabelecimentoProduto.PrecoPromocional = estabelecimentoBaseEstabelecimentoBaseProduto.PrecoPromocional;
                        estabelecimentoProduto.UnidadeMedida = estabelecimentoBaseEstabelecimentoBaseProduto.UnidadeMedida;

                        estabelecimento.EstabelecimentoProdutos.Add(estabelecimentoProduto);
                    }

                    validationResult = estabelecimentoService.Add(estabelecimento);
                }

                #endregion
                
                #region Credenciamento

                var query = credenciamentoService.GetQuery().Where(c => c.IdEstabelecimento == estabelecimento.IdEstabelecimento && c.IdEmpresa == idempresa && c.IdEstabelecimentoBase == estabelecimento.IdEstabelecimentoBase);
                    
                if (query.Any())
                {
                    var credenciamento = query.First();
                        
                    credenciamento.DataSolicitacao = DateTime.Now;
                    credenciamento.DataAtualizacao = DateTime.Now;
                    credenciamento.Status = EStatusCredenciamento.Aprovado;
                    credenciamento.ModoRegistro = EModoRegistro.Automático;
                        
                    credenciamentoService.Update(credenciamento, administradoraPlataforma);    
                }
                else
                {
                    var credenciamento = new Credenciamento();

                    credenciamento.IdEmpresa = idempresa;
                    credenciamento.IdEstabelecimento = estabelecimento.IdEstabelecimento;
                    credenciamento.IdEstabelecimentoBase = estabelecimento.IdEstabelecimentoBase;
                    credenciamento.DataSolicitacao = DateTime.Now;
                    credenciamento.DataAtualizacao = DateTime.Now;
                    credenciamento.Status = EStatusCredenciamento.Aprovado;
                    credenciamento.ModoRegistro = EModoRegistro.Automático;

                    credenciamentoService.Add(credenciamento, administradoraPlataforma);
                    _credenciamentoService.EnviarCredenciamentoWebHook(credenciamento);
                }

                #endregion
            }
            catch (Exception e)
            {
                validationResult.Add(e.Message);
                Console.WriteLine(e);
            }

            return validationResult;
        }

        public ValidationResult RemoverCombustiveisJSL(IList<int> idprodutobase)
        {
            try
            {
                var repository = _combustivelJslEstabelecimentoBaseRepository;

                foreach (var id in idprodutobase)
                {
                    var combustivel = repository.Where(c => c.IdProduto == id).FirstOrDefault();

                    if (combustivel == null)
                        continue;
                    
                    repository.Delete(combustivel);
                }
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Update(EstabelecimentoBase estabelecimento, List<EstabelecimentoBaseProduto> produtos, List<EstabelecimentoBaseContaBancaria> contasBancarias, int administradoraPlataforma,
            List<int> idsProdutosBaseExcluir = null, List<int> idsContasBancariasExcluir = null, List<EstabelecimentoBaseDocumento> documentos = null)
        {
            try
            {
                //Realizamos a atualização de todos os produtos relacionados aos estabelecimentos das empresas
                //Credenciadas a este estabelecimento base
                var validationUpdate = AtualizarProdutosEstabelecimento(produtos);
                if (!validationUpdate.IsValid)
                    return validationUpdate;

                var validationUpdateProdutosBase = AtualizarPropriedadesProdutosEstabelecimentoBase(estabelecimento, produtos);
                if (!validationUpdate.IsValid)
                    return validationUpdateProdutosBase;

                if (contasBancarias != null && contasBancarias.Any())
                {
                    foreach (var conta in contasBancarias)
                        EstabelecimentoBaseContaBancariaService.ValidarCnpjTitular(conta.CnpjTitular, estabelecimento.CNPJEstabelecimento);

                    _estabelecimentoBaseContaBancariaService.AddOrUpdateListContasBancarias(contasBancarias, estabelecimento.IdEstabelecimento);
                }

                if (documentos != null)
                {
                    var validationUpdateDocumentos = _estabelecimentoBaseDocumentoService.AddOrUpdateDocumentos(documentos, estabelecimento.IdEstabelecimento, administradoraPlataforma);
                    
                    if (!validationUpdateDocumentos.IsValid)
                        return validationUpdateDocumentos;
                }
                
                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                _repository.Update(estabelecimento);
                EnviarAtualizacaoEstabelecimentoWebHook(estabelecimento);

                ValidationResult resultadoAtualizacaoEstabelecimento;

                //Caso tudo tenha ocorrido ok e não seja necessário deletar nenhum produto, finalizamos o processo;
                if ((idsProdutosBaseExcluir == null || !idsProdutosBaseExcluir.Any()) && (idsContasBancariasExcluir == null || !idsContasBancariasExcluir.Any()))
                {                   
                    resultadoAtualizacaoEstabelecimento = _estabelecimentoService.UpdateByEstabelecimentoBase(estabelecimento.IdEstabelecimento);
                    return !resultadoAtualizacaoEstabelecimento.IsValid ? resultadoAtualizacaoEstabelecimento : new ValidationResult();
                }

                if (idsProdutosBaseExcluir != null)
                {
                    foreach (var id in idsProdutosBaseExcluir)
                    {
                        var combustivelService = _combustivelJslService.Deletar(id, estabelecimento.IdEstabelecimento);       
                        if (!combustivelService.IsValid)
                            return combustivelService;
                    }
                    
                    var validationProdutoEstabelecimento = DeletarProdutosEstabelecimento(idsProdutosBaseExcluir);
                    if (!validationProdutoEstabelecimento.IsValid)
                        return validationProdutoEstabelecimento;
                }

                if (idsContasBancariasExcluir != null && idsContasBancariasExcluir.Any())
                {
                    _estabelecimentoBaseContaBancariaService.DeleteListContasBancarias(idsContasBancariasExcluir);
                }

                if (idsProdutosBaseExcluir != null)
                {
                    // Remove os produtos deletados pela tela
                    var prodBaseRepo = _estabelecimentoBaseProdutoRepository;
                    idsProdutosBaseExcluir.ForEach(a =>
                    {
                        var registroBanco = prodBaseRepo.FirstOrDefault(x => x.IdProduto == a);
                        if (registroBanco != null) prodBaseRepo.Delete(registroBanco);
                    });
                }

                resultadoAtualizacaoEstabelecimento = _estabelecimentoService.UpdateByEstabelecimentoBase(estabelecimento.IdEstabelecimento);

                return !resultadoAtualizacaoEstabelecimento.IsValid ? resultadoAtualizacaoEstabelecimento : new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public object ConsultaGrid(int? idTipoEstabelecimento, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var estabelecimentos = _repository
                .GetAll();

            if (idTipoEstabelecimento.HasValue)
                estabelecimentos = estabelecimentos.Where(x => x.IdTipoEstabelecimento == idTipoEstabelecimento.Value);

            if (!string.IsNullOrWhiteSpace(descricao))
                estabelecimentos = estabelecimentos.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                estabelecimentos = estabelecimentos.OrderBy(x => x.IdEstabelecimento);
            else
                estabelecimentos =
                    estabelecimentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            estabelecimentos = estabelecimentos.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = estabelecimentos.Count(),
                items = estabelecimentos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdEstabelecimento,
                        x.Descricao,
                        x.Ativo,
                        x.RazaoSocial,
                        CNPJ = x.CNPJEstabelecimento
                    })
            };
        }

        public EstabelecimentoBase Get(int idEstabelecimento)
        {
            return _repository
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.EstabelecimentoBaseProdutos)
                .Include(x => x.EstabelecimentoBaseContasBancarias)
                .Include(x => x.Pais)
                .Include(x => x.Estado)
                .Include(x => x.Cidade)

                .Include(x => x.AssociacoesBaseEstabelecimento)
                .Include(x => x.AssociacoesBaseEstabelecimento.Select(p => p.Associacao))
                .Include(x => x.AssociacoesBaseEstabelecimento.Select(p => p.EstabelecimentoBase))
                .Include(x => x.Documentos)
                .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento);
        }

        public EstabelecimentoBase Get(string cnpj)
        {
            return _repository
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.EstabelecimentoBaseProdutos)
                .FirstOrDefault(x => x.CNPJEstabelecimento == cnpj);
        }

        /// <summary>
        /// Realiza a exclusão de todos os produtos do estabelecimento cadastrados por empresa
        /// que não são de contrato, mas possuem produtos relacionados com o estabelecimento base.
        /// </summary>
        /// <returns></returns>
        private ValidationResult DeletarProdutosEstabelecimento(List<int> idsProdutoBase)
        {
            try
            {
                var estabelecimentoProdutoRepo = _estabelecimentoProdutoRepository;

                var estabelecimentosContrato = estabelecimentoProdutoRepo
                    .Find(x => x.IdProdutoBase.HasValue && idsProdutoBase.Contains(x.IdProdutoBase.Value) &&
                               x.Contrato);

                foreach (var produtoContrato in estabelecimentosContrato)
                {
                    produtoContrato.IdEstabelecimentoBase = null;
                    produtoContrato.IdProdutoBase = null;
                    estabelecimentoProdutoRepo.Update(produtoContrato);
                }

                var estabelecimentosProdutos = estabelecimentoProdutoRepo
                    .Find(x => x.IdProdutoBase.HasValue && idsProdutoBase.Contains(x.IdProdutoBase.Value) &&
                               !x.Contrato);

                foreach (var estabelecimentosProduto in estabelecimentosProdutos)
                    estabelecimentoProdutoRepo.Delete(estabelecimentosProduto);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        private ValidationResult AtualizarProdutosEstabelecimento(List<EstabelecimentoBaseProduto> produtosEstabelecimento)
        {
            try
            {
                if (produtosEstabelecimento == null) return new ValidationResult();

                var idsProdutosBase = produtosEstabelecimento.Select(x => x.IdProduto);
                var estabelecimentoProdutoRepo = _estabelecimentoProdutoRepository;
                var estabelecimentosProdutos = estabelecimentoProdutoRepo
                    .Find(x => x.IdProdutoBase.HasValue && !x.Contrato &&
                               idsProdutosBase.Contains(x.IdProdutoBase.Value) && x.Estabelecimento.Credenciado);

                foreach (var estabelecimentosProduto in estabelecimentosProdutos)
                {
                    var produtoBase = produtosEstabelecimento.FirstOrDefault(x => x.IdProduto == estabelecimentosProduto.IdProdutoBase);

                    if (produtoBase == null) continue;

                    estabelecimentosProduto.Descricao = produtoBase.Descricao;
                    estabelecimentosProduto.PrecoPromocional = produtoBase.PrecoPromocional;
                    estabelecimentosProduto.PrecoUnitario = produtoBase.PrecoUnitario;
                    estabelecimentosProduto.UnidadeMedida = produtoBase.UnidadeMedida;
                    estabelecimentosProduto.IdProdutoBase = produtoBase.IdProduto;

                    estabelecimentoProdutoRepo.Update(estabelecimentosProduto);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        private ValidationResult AtualizarPropriedadesProdutosEstabelecimentoBase(EstabelecimentoBase estabelecimento, List<EstabelecimentoBaseProduto> produtosEstabelecimento)
        {
            try
            {
                //Para cada produto enviado por parametro
                //Verificamos se já existe na base, caso sim, o atualizamos, caso contrário adicionamos um novo.
                foreach (var estabelecimentosProduto in produtosEstabelecimento)
                {
                    EstabelecimentoBaseProduto produtoBase = null;

                    // Se o IdProduto for 0 significa que o produto é novo
                    if (estabelecimentosProduto.IdProduto != 0)
                    {
                        produtoBase = estabelecimento.EstabelecimentoBaseProdutos.FirstOrDefault(x => x.IdProduto == estabelecimentosProduto.IdProduto);   
                    }

                    if (produtoBase != null)
                    {
                        produtoBase.Descricao = estabelecimentosProduto.Descricao;
                        produtoBase.PrecoPromocional = estabelecimentosProduto.PrecoPromocional;
                        produtoBase.PrecoUnitario = estabelecimentosProduto.PrecoUnitario;
                        produtoBase.UnidadeMedida = estabelecimentosProduto.UnidadeMedida;
                    }
                    else
                    {
                        estabelecimento.EstabelecimentoBaseProdutos.Add(new EstabelecimentoBaseProduto
                        {
                            Descricao = estabelecimentosProduto.Descricao,
                            PrecoPromocional = estabelecimentosProduto.PrecoPromocional,
                            PrecoUnitario = estabelecimentosProduto.PrecoUnitario,
                            UnidadeMedida = estabelecimentosProduto.UnidadeMedida
                        });
                    }

                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public void EnviarAtualizacaoEstabelecimentoWebHook(EstabelecimentoBase newEstabelecimentoBase)
        {
            var token = WebConfigurationManager.AppSettings["MS_TOKEN_ADMINISTRADORA"];
            try
            {
                var credenciamentos = _credenciamentoService.GetCredenciadosByIdEstabelecimentoBase(newEstabelecimentoBase.IdEstabelecimento);

                foreach (var credenciamento in credenciamentos)
                {
                    var webhook = _webhookService.GetByIdRegistro(credenciamento.IdEmpresa, WebhookTipoEvento.IntegracaoEstabelecimento);

                    if (webhook != null)
                    {
                        var empresa = _empresaRepository.Get(credenciamento.IdEmpresa);

                        if (empresa == null || string.IsNullOrWhiteSpace(webhook.Endpoint))
                            return;

                        var retorno = _credenciamentoService.OrganizarObjetoEstabelecimentoCredenciadoWebHook(credenciamento);
                        
                        webhook.EnviarNotificacao(
                            _webhookActionDependencies,
                            "Atualização de cadastro de estabelecimento: " + credenciamento.IdCredenciamento,
                            retorno,
                            token, credenciamento.IdEmpresa);                        
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error(e, $"Erro ao notificar web hook de atualização de dados do estabelecimento: {newEstabelecimentoBase.IdEstabelecimento}");
            }
        }
        public EstabelecimentoBase GetWithDocumentos(int idEstabelecimento)
        {
            return _repository
                .Include(x => x.Documentos)
                .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento);
        }
        
        public int GetIdByCnpj(string cnpj)
        {
            return _repository.GetIdByCnpj(cnpj);
        }
    }
}

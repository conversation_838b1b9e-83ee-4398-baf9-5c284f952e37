﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Protocolo.RecebimentoProtocolo
{
    public class RelatorioRecebimentoProtocolo
    {
        public byte[] GetReport(List<RelatorioRecebimentoProtocoloDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametrizes = new Tuple<string, string, bool>[1];
            parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var bytes = new Base.Reports().GetReport(listaDados, parametrizes, true, "DtsRecebimentoProtocolo",
                "ATS.CrossCutting.Reports.Protocolo.RecebimentoProtocolo.RelatorioRecebimentoProtocolo.rdlc",
                tipoArquivo);

            return bytes;
        }
    }
}

﻿namespace ATS.CrossCutting.Reports.Estabelecimento
{
    public class EstabelecimentoModel
    {
        public int IdEstabelecimento { get; set; }
        public string Descricao { get; set; }
        public bool Ativo { get; set; }
        public int? IdEstabelecimentoBase { get; set; }
        public string Empresa { get; set; }
        public string <PERSON><PERSON>enciado { get; set; }
        public string CNPJ { get; set; }
        public string Associacao { get; set; }
        public string AtivoDescricao { get; set; }
        public string Endereco { get; set; }
        public string RazaoSocial { get; set; }
    }
}

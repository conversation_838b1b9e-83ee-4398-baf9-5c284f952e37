﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.IoC.Validation;
using ATS.Domain.Exceptions;
using ATS.Domain.Validation;
using ATS.Domain.Helpers;

namespace ATS.Domain.Entities
{
    public class ValidationKeyTransaction : IValidatedEntity
    {
        public string Cpf { get; set; }

        public string Celular { get; set; }

        public string Chave { get; set; }

        public string PublicKey { get; set; }

        public Usuario UsuarioByCpfAndCelular { get; set; }

        public int? MinutosValidadeHash { get; set; }

        public ValidationKeyTransaction(string cpf, string celular, string chave, int? minutosValidadeHash)
        {
            chave = chave.Trim().Replace(" ", "");
            Cpf = cpf;
            Celular = celular;
            PublicKey = string.IsNullOrEmpty(chave) ? chave : chave.Substring(chave.Length - 4, 4);
            Chave = string.IsNullOrEmpty(chave) ? chave :  chave.Substring(0, chave.Length - 4);
            MinutosValidadeHash = minutosValidadeHash;
        }

        public ValidationKeyTransaction(string cpf, string celular, string chave, Usuario preUsuarioByCpfAndCelular, int? minutosValidadeHash)
        {
            chave = chave.Trim().Replace(" ", "");
            Cpf = cpf;
            Celular = celular;
            PublicKey = string.IsNullOrEmpty(chave) ? chave : chave.Substring(chave.Length - 4, 4);
            Chave = string.IsNullOrEmpty(chave) ? chave : chave.Substring(0, chave.Length - 4);
            UsuarioByCpfAndCelular = preUsuarioByCpfAndCelular;
            MinutosValidadeHash = minutosValidadeHash;
        }

        public void Validate()
        {
            if (string.IsNullOrEmpty(Cpf))
                throw new ValidationKeyTransactionInvalidException("CPF não informado.");

            if (string.IsNullOrEmpty(Celular))
                throw new ValidationKeyTransactionInvalidException("Celular não informado.");

            if (string.IsNullOrEmpty(Chave))
                throw new ValidationKeyTransactionInvalidException("Chave não informada.");
        }


        public void DeShuffleChave(string KeyCodeTransaction)
        {
            Chave = Chave.DeShuffle((Convert.ToInt32(KeyCodeTransaction) + Convert.ToInt32(PublicKey)));
        }

        public KeyValuePair<ValidationResult, int?> ValiadateKey()
        {
            try
            {
                Validate();
                
                if (UsuarioByCpfAndCelular == null)
                    return new KeyValuePair<ValidationResult, int?>(new ValidationResult().Add($"Motorista da viagem não encontrado pelo CPF {Cpf.FormatCpf()} e Celular {Celular}."), null);

                var keyDecode = DescodificarChave(UsuarioByCpfAndCelular.KeyCodeTransaction);

                DeShuffleChave(keyDecode);

                var digitoVerificadorChave = GetDigitoVerificador();

                var chaveFormatadaSemDigitos = RemoverPrimeiroUltimoCaractere();
                var chaveSubtraida = Convert.ToInt64(chaveFormatadaSemDigitos) - Convert.ToInt64(keyDecode);
                
                var idUsuario = BuscarIdUsuario(chaveSubtraida.ToString());
                var horaGeracao = GetHora(chaveSubtraida.ToString()).FormatTime();

                if (!TimeSpan.TryParse(horaGeracao, out _))
                    return new KeyValuePair<ValidationResult, int?>(new ValidationResult().Add("Chave inválida."), 0);

                var horaGeracaoTimeSpan = TimeSpan.Parse(horaGeracao);

                var dataHoraGeracao = MontarDateTimePorHoraGeracao(horaGeracaoTimeSpan);

                var diferencaMinutos = DateTime.Now.Subtract(dataHoraGeracao).Minutes;

                if (diferencaMinutos > MinutosValidadeHash || diferencaMinutos < 0)
                    return new KeyValuePair<ValidationResult, int?>(new ValidationResult().Add("O tempo limite de validade da chave expirou."), null);

                var digitoVerificador = DigitoModulo11(chaveSubtraida);

                if (digitoVerificador != digitoVerificadorChave)
                    return new KeyValuePair<ValidationResult, int?>(new ValidationResult().Add("Dígito verificador da chave inválido"), null);

                return new KeyValuePair<ValidationResult, int?>(new ValidationResult(), idUsuario);
            }
            catch (Exception e)
            {
                return new KeyValuePair<ValidationResult, int?>(new ValidationResult().Add(e.Message), null);
            }
        }

        public string RemoverPrimeiroDigito()
        {
            return Chave.Remove(0, 1);
        }

        public int GetDigitoVerificador()
        {
            return (int) Convert.ToInt64(Chave.Substring(Chave.Length - 1, 1));
        }

        public string DescodificarChave(string valor)
        {
            var encodedDataAsBytes = Convert.FromBase64String(valor);
            var returnValue = Encoding.ASCII.GetString(encodedDataAsBytes);

            return returnValue;
        }

        public string RemoverPrimeiroUltimoCaractere()
        {
            var semPrimeiro = Chave.Remove(0, 1);
            var semUltimo = semPrimeiro.Remove(semPrimeiro.Length - 1);
            return semUltimo;
        }

        public int BuscarIdUsuario(string valor)
        {
            return Convert.ToInt32(valor.Remove(valor.Length - 4));
        }

        public string GetHora(string codigo)
        {
            return codigo.Substring(codigo.Length - 4, 4);
        }

        private static DateTime MontarDateTimePorHoraGeracao(TimeSpan horaGeracao)
        {
            return new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, horaGeracao.Hours,
                horaGeracao.Minutes, 00);
        }

        public int DigitoModulo11(long intNumero)
        {
            int[] intPesos = { 2, 3, 4, 5, 6, 7, 8, 9, 2, 3, 4, 5, 6, 7, 8, 9 };
            var strText = intNumero.ToString();

            if (strText.Length > 16)
                throw new Exception("Número não suportado pela função!");

            var intSoma = 0;
            var intIdx = 0;

            for (var intPos = strText.Length - 1; intPos >= 0; intPos--)
            {
                intSoma += Convert.ToInt32(strText[intPos].ToString()) * intPesos[intIdx];
                intIdx++;
            }

            var intResto = (intSoma * 10) % 11;
            var intDigito = intResto;

            if (intDigito >= 10)
                intDigito = 0;

            return intDigito;
        }

    }
}

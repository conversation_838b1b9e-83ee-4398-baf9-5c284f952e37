using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IPedagioRotaRepository : IRepository<PedagioRota>
    {
        PedagioRota Add(PedagioRota entity);
        PedagioRota Update(PedagioRota entity);
        IQueryable<PedagioRota> Get(Expression<Func<PedagioRota, bool>> predicate);
    }
}
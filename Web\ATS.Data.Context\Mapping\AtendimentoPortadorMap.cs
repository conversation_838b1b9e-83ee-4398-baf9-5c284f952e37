using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class AtendimentoPortadorMap : EntityTypeConfiguration<AtendimentoPortador>
    {
        public AtendimentoPortadorMap()
        {
            ToTable("ATENDIMENTO_PORTADOR");

            HasKey(t => t.IdAtendimentoPortador);
            
            Property(x => x.Observacao)
                .HasColumnName("observacao")
                .HasMaxLength(200)
                .IsOptional();
            
            Property(t => t.CNPJCPF)
                .IsOptional()
                .HasMaxLength(14);
            
            Property(x => x.DataInicio)
                .HasColumnName("datainicio")
                .IsRequired()
                .HasColumnType("datetime");

            Property(x => x.DataFinal)
                .HasColumnName("datafinal")
                .IsOptional()
                .HasColumnType("datetime");

            Property(x => x.IdUsuario)
                .HasColumnName("idusuario")
                .IsOptional();

            Property(x => x.Status)
                .HasColumnName("status")
                .IsRequired();
            
            Property(x => x.Protocolo)
                .HasColumnName("protocolo")
                .IsRequired();
            
            Property(x => x.IdMotivoFinalizacaoAtendimento)
                .HasColumnName("idmotivofinalizacaoatendimento")
                .IsOptional();
            
            HasOptional(x => x.Motivo)
                .WithMany()
                .HasForeignKey(x => x.IdMotivoFinalizacaoAtendimento);
            
            HasOptional(x => x.Usuario)
                .WithMany()
                .HasForeignKey(x => x.IdUsuario);
            
            HasOptional(x => x.Empresa)
                .WithMany()
                .HasForeignKey(x => x.IdEmpresa);
        }
    }
}
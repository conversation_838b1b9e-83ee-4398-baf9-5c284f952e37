﻿using ATS.Domain.Enum;

namespace ATS.Domain.Models.PagamentoFrete
{
    public class CheckTokenModel
    {
        public int IdEmpresa { get; set; }
        public bool LiberarPagtoSemChave { get; set; }
        public bool PagamentoViaMeioHomologado { get; set; }
        public EStatusViagemEvento? StatusViagemEvento { get; set; }
        public bool EmpresaValidaChaveMHBaixaEvento { get; set; }
        public int TempoValidadeChave { get; set; }
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using Sistema.Framework.Util.Helper;

namespace ATS.Domain.Service
{
    public class TipoEstabelecimentoService : ServiceBase, ITipoEstabelecimentoService
    {
        private readonly ITipoEstabelecimentoRepository _repository;
        private readonly IUsoTipoEstabelecimentoRepository _usoRepository;
        
        public TipoEstabelecimentoService(ITipoEstabelecimentoRepository repository, IUsoTipoEstabelecimentoRepository usoRepository)
        {
            _repository = repository;
            _usoRepository = usoRepository;
        }
        
        public ValidationResult Add(TipoEstabelecimento tipoEstabelecimento)
        {
            try
            {
                tipoEstabelecimento.DataUltimaAtualizacao = DateTime.Now;
                _repository.Add(tipoEstabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public ValidationResult Inativar(int idTipoEstabelecimento)
        {
            try
            {
                TipoEstabelecimento tipoEstabelecimento = _repository.Get(idTipoEstabelecimento);
                tipoEstabelecimento.Ativo = false;
                tipoEstabelecimento.DataUltimaAtualizacao = DateTime.Now;
                _repository.Update(tipoEstabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public ValidationResult Reativar(int idTipoEstabelecimento)
        {
            try
            {
                TipoEstabelecimento tipoEstabelecimento = _repository.Get(idTipoEstabelecimento);
                tipoEstabelecimento.Ativo = true;
                tipoEstabelecimento.DataUltimaAtualizacao = DateTime.Now;
                _repository.Update(tipoEstabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public ValidationResult Update(TipoEstabelecimento tipoEstabelecimento, List<EUsoTipoEstabelecimento> usos)
        {
            var validationResult = new ValidationResult();
            try
            {
                validationResult.Add(UpdateUsoTipoEstabelecimento(tipoEstabelecimento.IdTipoEstabelecimento, usos));

                if (!validationResult.IsValid)
                    return validationResult;
                
                tipoEstabelecimento.DataUltimaAtualizacao = DateTime.Now;
                _repository.Update(tipoEstabelecimento);

            }
            catch (Exception e)
            {
                validationResult.Add($"Exceção Update: {e.GetBaseException().Message}");
            }
            
            return validationResult;
        }
        
        private ValidationResult UpdateUsoTipoEstabelecimento(int idTipoEstabelecimento, List<EUsoTipoEstabelecimento> usos)
        {
            var validationResult = new ValidationResult();
            try
            {
                if(usos == null)
                    usos = new List<EUsoTipoEstabelecimento>();
                
                var usosDb = _usoRepository.GetAll()
                    .Where(x => x.IdTipoEstabelecimento == idTipoEstabelecimento)
                    .Select(x => x.Uso).ToList();
                
                var listToAdd = usos.Except(usosDb).ToList();
                foreach (var uso in listToAdd)
                    _usoRepository.Add(new UsoTipoEstabelecimento(){IdTipoEstabelecimento = idTipoEstabelecimento, Uso = uso});
                
                var listToDelete = usosDb.Except(usos).ToList();
                foreach (var uso in listToDelete)
                    _usoRepository.Delete(uso, idTipoEstabelecimento);
            }
            catch (Exception e)
            {
                validationResult.Add($"Exceção UpdateUsoTipoEstabelecimento: {e.GetBaseException().Message}");
            }

            return validationResult;
        }

        public object ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool apenasTiposBase, EUsoTipoEstabelecimento? uso)
        {
            var tiposEstabelecimentos = _repository.GetAll()
                .Include(x => x.Empresa);


            if (!string.IsNullOrWhiteSpace(descricao))
                tiposEstabelecimentos = tiposEstabelecimentos.Where(x => x.Descricao.Contains(descricao));

            tiposEstabelecimentos = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? tiposEstabelecimentos.OrderBy(x => x.IdTipoEstabelecimento)
                : tiposEstabelecimentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            tiposEstabelecimentos = tiposEstabelecimentos.AplicarFiltrosDinamicos(filters);


            if (idEmpresa.HasValue)
                tiposEstabelecimentos = tiposEstabelecimentos.Where(x => x.IdEmpresa == idEmpresa.Value || x.IdEmpresa == null);

            // Caso sim, trás apenas os bases
            // se não, busca apenas os com idEmpresa
            if (apenasTiposBase)
                tiposEstabelecimentos = tiposEstabelecimentos.Where(x => x.IdEmpresa == null);

            if (uso.HasValue)
                tiposEstabelecimentos = tiposEstabelecimentos.Where(x => 
                    x.UsoTipoEstabelecimento.Any(y => y.Uso == EUsoTipoEstabelecimento.Todos || y.Uso == uso.Value ));

            return new
            {
                totalItems = tiposEstabelecimentos.Count(),
                items = tiposEstabelecimentos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdTipoEstabelecimento,
                    x.Descricao,
                    x.Ativo,
                    Empresa = x.Empresa?.RazaoSocial
                })
            };
        }

        public TipoEstabelecimento Get(int idTipoEstabelecimento)
        {
            return _repository
                .Include(x => x.UsoTipoEstabelecimento)
                .Include(x => x.Empresa)
                .Include(x => x.Icone)
                .FirstOrDefault(x => x.IdTipoEstabelecimento == idTipoEstabelecimento);
        }

        public IQueryable<TipoEstabelecimento> GetTiposEstabelecimentoPorEmpresa(int? idEmpresa)
        {
            var tiposEstabelecimentos = _repository.All()
                .Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                tiposEstabelecimentos = tiposEstabelecimentos.Where(x => x.IdEmpresa == idEmpresa.Value);

            return tiposEstabelecimentos;
        }
        
        public IQueryable<TipoEstabelecimento> GetTiposEstabelecimentoPorDescricao(string descricao)
        {
            return _repository.All().Where(x => x.Descricao == descricao && x.Ativo);
    }
}
}

﻿using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IAutenticacaoAplicacaoRepository : IRepository<AutenticacaoAplicacao>
    {
        bool AcessoConcedido(string cnpjAplicacao, string token);
        IQueryable<AutenticacaoAplicacao> GetAutenticacaoAplicacaoPorCNPJAplicacao(string cnpjAplicacao, string token);
        AutenticacaoAplicacao Get(string cnpjAplicacao);
    }
}

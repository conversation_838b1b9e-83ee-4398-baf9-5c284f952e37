﻿using System.Collections.Generic;
using ATS.Domain.DTO.PrestacaoContas;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IPrestacaoContasService : IService<PrestacaoContas>
    {
        ValidationResult Novo(PrestacaoContasNovoRequest request, int idEmpresa);
        bool PertenceAEmpresa(int prestacaoContasId, int? empresaId);
        ValidationResult AlterarStatus(int prestacaoContasId, EStatusPrestacaoContas status, string observacao = null);
        PrestacaoContasGridResponse ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, EStatusPrestacaoContas[] status = null);
        int? VerificarPrestacaoContasAberta(int idUsuario);
        public void GerarEventoPrestacaoConta(int prestacaoContasId, EStatusPrestacaoContasEvento statusEvento);
    }
}
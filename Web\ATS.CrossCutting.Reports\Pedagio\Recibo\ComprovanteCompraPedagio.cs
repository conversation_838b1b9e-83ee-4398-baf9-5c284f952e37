﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.Pedagio.Recibo
{
    public class ComprovanteCompraPedagio
    {
        public byte[] GetReport(ComprovanteCompraPedagioDataType comprovanteCompraPedagio, List<ComprovanteCompraPedagioHistoricoPracasDataType> pracasPedagio, 
            string valorTotal)
        {
            try
            {
                var parametrizes = new Tuple<string, string, bool>[1];
                parametrizes[0] = new Tuple<string, string, bool>("ValorTotal", valorTotal, true);

                var dataSources = new List<Tuple<object, string>>
                {
                    new Tuple<object, string>(new List<ComprovanteCompraPedagioDataType> {comprovanteCompraPedagio}, "ComprovanteCompraPedagioDts"), 
                    new Tuple<object, string>(pracasPedagio, "ComprovanteCompraPedagioHistoricoPracaDts")
                };

                var bytes = 
                    new Base.Reports().GetReport(dataSources, parametrizes, true, "ATS.CrossCutting.Reports.Pedagio.Recibo.ComprovanteCompraPedagio.rdlc", ConstantesUtils.FormatoPdfMinusculo);

                return bytes;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
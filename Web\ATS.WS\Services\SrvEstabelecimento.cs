﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using ATS.WS.Models.Webservice.Response.Estabelecimento;
using AutoMapper.QueryableExtensions;
using NLog;
using Sistema.Framework.Util.Extension;
using EstabelecimentoProduto = ATS.Domain.Entities.EstabelecimentoProduto;

namespace ATS.WS.Services
{
    public class SrvEstabelecimento : SrvBase
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IDataMediaServerApp _dataMediaServerApp;
        private readonly ITipoEstabelecimentoApp _tipoEstabelecimentoApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly ICombustivelJSLApp _combustivelJslApp;
        private readonly IEstabelecimentoService _estabelecimentoService;
        private readonly IPaisApp _paisApp;
        private readonly IEstadoApp _estadoApp;

        public SrvEstabelecimento(IParametrosApp parametrosApp, IEstabelecimentoBaseService estabelecimentoBaseService, IEmpresaApp empresaApp, IEstabelecimentoApp estabelecimentoApp,
            IDataMediaServerApp dataMediaServerApp, ITipoEstabelecimentoApp tipoEstabelecimentoApp, ICidadeApp cidadeApp, ICombustivelJSLApp combustivelJslApp, IEstabelecimentoService estabelecimentoService, IPaisApp paisApp, IEstadoApp estadoApp)
        {
            _parametrosApp = parametrosApp;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _empresaApp = empresaApp;
            _estabelecimentoApp = estabelecimentoApp;
            _dataMediaServerApp = dataMediaServerApp;
            _tipoEstabelecimentoApp = tipoEstabelecimentoApp;
            _cidadeApp = cidadeApp;
            _combustivelJslApp = combustivelJslApp;
            _estabelecimentoService = estabelecimentoService;
            _paisApp = paisApp;
            _estadoApp = estadoApp;
        }
        
        public Retorno<List<ConsultaEstabelecimentoResponse>> Consultar(EstabelecimentoConsulta @params)
        {
            try
            {
                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                if (!idEmpresa.HasValue)
                    return new Retorno<List<ConsultaEstabelecimentoResponse>>(
                        "Não foi possível identificar a empresa. ");

                var estabelecimentosQuery = _estabelecimentoApp.ConsultarPorEmpresa(idEmpresa.Value, @params.CNPJEstabelecimento);
                
                var listaRetorno = new List<ConsultaEstabelecimentoResponse>();

                
                foreach (var estabelecimento in estabelecimentosQuery)
                {
                    var produtos = new List<EstabelecimentoProdutoResponse>();
                    
                    if (estabelecimento.Produtos != null)
                        foreach (var produto in estabelecimento.Produtos)
                        {
                            produtos.Add(new EstabelecimentoProdutoResponse
                            {
                                UnidadeMedida = produto.UnidadeMedida,
                                Produto = produto.Produto,
                                IdProduto = produto.IdProduto,
                                PrecoPromocional = produto.PrecoPromocional,
                                PrecoUnitario = produto.PrecoUnitario
                            });
                        }
                    
                    listaRetorno.Add(new ConsultaEstabelecimentoResponse
                    {
                        Ativo = estabelecimento.Ativo, 
                        Cidade = estabelecimento.Cidade, 
                        Latitude = estabelecimento.Latitude,
                        Longitude = estabelecimento.Longitude,
                        IdEstabelecimento = estabelecimento.IdEstabelecimento,
                        Bairro = estabelecimento.Bairro,
                        Complemento = estabelecimento.Complemento, 
                        Email = estabelecimento.Email,
                        Estabelecimento = estabelecimento.Estabelecimento,
                        Estado = estabelecimento.Estado,
                        Icone = !string.IsNullOrWhiteSpace(estabelecimento.TokenIcone)
                            ? _dataMediaServerApp.GetMedia(estabelecimento.TokenIcone).Data
                            : string.Empty,
                        Logradouro = estabelecimento.Logradouro,
                        Numero = estabelecimento.Numero,
                        Pais = estabelecimento.Pais,
                        Telefone = estabelecimento.Telefone,
                        TipoEstabelecimento = estabelecimento.TipoEstabelecimento,
                        CEP = estabelecimento.CEP,
                        CNPJEstabelecimento = estabelecimento.CNPJEstabelecimento,
                        CodigoIBGECidade = estabelecimento.CodigoIBGECidade,
                        CodigoIBGEEstado = estabelecimento.CodigoIBGEEstado,
                        CodigoBACENPais = estabelecimento.CodigoBACENPais,
                        Produtos = produtos
                    });
                }
                
                /*var retorno = estabelecimentosQuery.ProjectTo<ConsultaEstabelecimentoResponse>()
                    .OrderBy(o => o.IdEstabelecimento)
                    .ToList();*/

                return new Retorno<List<ConsultaEstabelecimentoResponse>>(true, listaRetorno);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e, $"Erro ao consultar estabelecimentos. CNPJEmpresa: {@params.CNPJEmpresa} - CNPJEstabelecimento: {@params.CNPJEstabelecimento}");
                return new Retorno<List<ConsultaEstabelecimentoResponse>>
                {
                    Sucesso = false,
                    Mensagem = e.Message
                };
            }
        }

        public ConsultarEstabelecimentosRotaResponse ConsultarEstabelecimentosRota(ConsultarEstabelecimentosRotaRequest @params, bool buscaCredenciados = false)
        {
            
            int idEmpresa;
            if(@params.IdEmpresa == null || @params.IdEmpresa == 0)
                idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa) ?? 0;
            else
                idEmpresa = @params.IdEmpresa ?? 0;
            
            if (idEmpresa== 0)
                return new ConsultarEstabelecimentosRotaResponse("Não foi possível identificar a empresa. ");

            var estabelecimentosQuery = _estabelecimentoApp.ConsultarEstabelecimentosRota(
                    idEmpresa,
                    @params.LatitudeOrigem,
                    @params.LongitudeOrigem,
                    @params.LatitudeDestino,
                    @params.LongitudeDestino,
                    @params.Raio,
                    null,
                    buscaCredenciados)
                .OrderBy(e => e.IdEstabelecimento);

            var estabelecimentos = estabelecimentosQuery
                .ProjectTo<ConsultaEstabelecimentoResponse>()
                .ToArray();


            foreach (var itemEstabQuery in estabelecimentosQuery)
            {
                foreach (var estab in estabelecimentos)
                {
                    if (estab.IdEstabelecimento == itemEstabQuery.IdEstabelecimento)
                        estab.Icone = itemEstabQuery.TipoEstabelecimento.Icone.TokenIcone;
                }
            }
            /*foreach (var item in estabelecimentos)            
                item.Icone = null;*//**/

            var retorno = new List<ConsultaEstabelecimentoResponse>(estabelecimentos);
            return new ConsultarEstabelecimentosRotaResponse(true, retorno);
        }

        public Retorno<object> Integrar(EstabelecimentoIntegracaoRequest @params)
        {
            //Primeiro ponto é verificar se todos os campos obrigatórios foram informados e se foram informados de maneira correta.
            var validacoes = ValidarIntegracao(@params);
            if (!validacoes.IsValid)
                throw new Exception(validacoes.ToFormatedMessage());

            #region Validações

            //Segundo ponto é verificar se os valores informados obrigatórios são validos com os registros cadastrados na base.
            var idempresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            if (!idempresa.HasValue)
                throw new Exception("Não foi possível identificar a empresa pelo CNPJ informado. ");
            //Tipo do estabelecimento informado
            TipoEstabelecimento tipoEstabelecimento = null;
            if(@params.IdTipoEstabelecimento.HasValue)
                tipoEstabelecimento = _tipoEstabelecimentoApp.Get(@params.IdTipoEstabelecimento.Value);
            
            if (tipoEstabelecimento == null)
                throw new Exception("Não foi possível identificar o Tipo de estabelecimento pelo código informado.");
            //País
            var pais = _paisApp.GetPaisPorBACEN(@params.CodigoBACENPais ?? 0);
            var idPais = pais?.IdPais;
            if (!idPais.HasValue)
                throw new Exception("Não foi possível identificar o País pelo código BACEN informado.");
            //Estado
            var estado = _estadoApp.GetPorIBGE(@params.CodigoIBGEEstado ?? 0);
            var idEstado = estado?.IdEstado;
            if (!idEstado.HasValue)
                throw new Exception("Não foi possível identificar o Estado pelo código IBGE informado. ");
            //Cidade
            var cidade = _cidadeApp.GetPorIBGE(@params.CodigoIBGECidade ?? 0);
            var idCidade = cidade?.IdCidade;
            if (!idCidade.HasValue)
                throw new Exception("Não foi possível identificar a Cidade pelo código IBGE informado.");

            #endregion

            //Eealizamos a criação do objeto base do registro
            var estabelecimento = new Estabelecimento();
            estabelecimento.CNPJEstabelecimento = @params.CNPJEstabelecimento;
            estabelecimento.IdEmpresa = idempresa.Value;
            estabelecimento.Credenciado = @params.Credenciado ?? false;
            estabelecimento.Descricao = @params.Descricao;
            estabelecimento.IdEmpresa = idempresa.Value;
            estabelecimento.DataUltimaAtualizacao = DateTime.Now;
            estabelecimento.IdTipoEstabelecimento = tipoEstabelecimento.IdTipoEstabelecimento;
            estabelecimento.IdPais = idPais.Value;
            estabelecimento.IdEstado = idEstado.Value;
            estabelecimento.IdCidade = idCidade.Value;
            estabelecimento.Bairro = @params.Bairro;
            estabelecimento.Logradouro = @params.Logradouro;
            estabelecimento.Numero = @params.Numero;
            estabelecimento.CEP = @params.CEP;
            estabelecimento.Email = @params.Email;
            estabelecimento.Latitude = @params.Latitude;
            estabelecimento.Longitude = @params.Longitude;
            estabelecimento.Complemento = @params.Complemento;

            if (@params.Produtos != null && @params.Produtos.Any())
            {
                estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();
                foreach (var item in @params.Produtos)
                {
                    if (string.IsNullOrWhiteSpace(item.Descricao))
                        return new Retorno<object>(false, "Descrição do produto é obrigatória. ");
                    if (item.Descricao?.Length > 100)
                        return new Retorno<object>(false, "Descrição dos produtos devem possuir no máximo 100 caracteres. ");
                    if (string.IsNullOrWhiteSpace(item.UnidadeMedida))
                        return new Retorno<object>(false, "Unidade de medida é obrigatória para os produtos do estabelecimento. ");
                    estabelecimento.EstabelecimentoProdutos
                        .Add(new EstabelecimentoProduto
                        {
                            Descricao = item.Descricao,
                            UnidadeMedida = item.UnidadeMedida,
                            PrecoUnitario = item.PrecoUnitario,
                            PrecoPromocional = item.PrecoPromocional
                        });
                }
            }

            if (!estabelecimento.Latitude.HasValue && !estabelecimento.Longitude.HasValue &&
                !string.IsNullOrWhiteSpace(estabelecimento.Logradouro))
            {
                var retornoGoogle = new GoogleMapsHelper().GetLocalizacao(
                    $"{estabelecimento.Logradouro},{estabelecimento.Numero},{estabelecimento.Bairro},{cidade.Nome},{estado.Sigla},{pais.Sigla}",
                    EOrigemConsumoServicoExterno.IntegrarEstabelecimento);
                estabelecimento.Latitude = retornoGoogle.Latitude;
                estabelecimento.Longitude = retornoGoogle.Longitude;
            }

            if (estabelecimento.Credenciado)
            {
                var estabelecimentoBase = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(estabelecimento.CNPJEstabelecimento);
                estabelecimento.Credenciamentos = new List<Credenciamento>();
                estabelecimento.Credenciamentos.Add(new Credenciamento
                {
                    DataAtualizacao = DateTime.Now,
                    DataSolicitacao = DateTime.Now,
                    IdEmpresa = estabelecimento.IdEmpresa,
                    ModoRegistro = EModoRegistro.Integrado,
                    Status = EStatusCredenciamento.Aprovado,
                    IdEstabelecimentoBase = estabelecimentoBase?.IdEstabelecimento,
                    StatusDocumentacao = EStatusDocumentacaoCredenciamento.Irregular
                });
            }

            var add = _estabelecimentoApp.Add(estabelecimento);
            if (!add.IsValid)
                throw new Exception(add.ToFormatedMessage());

            return new Retorno<object>(true, new { estabelecimento.IdEstabelecimento });
        }

        public async Task<Retorno<object>> IntegrarEstabelecimentoJsl(EstabelecimentoIntegracaoRequest request)
        {
            #region Inicializacoes

            var estabelecimentoBaseApp = new EstabelecimentoBaseApp(_estabelecimentoBaseService);

            #endregion
            
            #region Validações

            if (!request.CodigoIBGECidade.HasValue)
                return new Retorno<object>(false, "O código da cidade deve ser informado.", null);

            var cidadeQuery = _cidadeApp.GetQueryByIBGE(request.CodigoIBGECidade.Value);
            var idTipoEstabelecimentoPadraoJsl = _parametrosApp.GetIdTipoEstabelecimentoPadraoJSL();

            if (!cidadeQuery.Any())
                return new Retorno<object>(false, "Cidade informada não é válida.", null);

            var cidade = cidadeQuery.Include(c => c.Estado).Select(c => new
            {
                c.IdCidade,
                c.Estado.IdEstado,
                c.Estado.IdPais
            }).First();
            
            if (idTipoEstabelecimentoPadraoJsl == 0)
                return new Retorno<object>(false, "Tipo de estabelecimento padrão para JSL não configurado. Fale com o responsável pelo ATS.", null);

            #endregion
            
            CombustivelWebHookJSLRequest combustivelWebHookJslRequest = new CombustivelWebHookJSLRequest();
            combustivelWebHookJslRequest.Combustiveis = new List<CombustiveisJSLRequest>();
            combustivelWebHookJslRequest.CnpjEstabelecimento = request.CNPJEstabelecimento;
            ValidationResult result;
            
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                EstabelecimentoBase estabelecimentoBase;

                var queryable = estabelecimentoBaseApp.GetQueryByCnpj(request.CNPJEstabelecimento);

                if (queryable.Any())
                {
                    estabelecimentoBase = queryable.Include(c => c.Estabelecimento)
                        .Include(c => c.EstabelecimentoBaseProdutos).First();

                    estabelecimentoBase.Ativo = true;
                    estabelecimentoBase.PertenceRedeJsl = true;

                    if (request.Produtos != null)
                    {
                        foreach (var estabelecimentoProdutoRequest in request.Produtos)
                        {
                            if (!estabelecimentoProdutoRequest.IdProduto.HasValue)
                                continue;
                        
                            var combustivelRequest = new CombustiveisJSLRequest();
                            combustivelRequest.Id = estabelecimentoProdutoRequest.IdProduto.Value;
                            combustivelRequest.Descricao = estabelecimentoProdutoRequest.Descricao;
                            combustivelRequest.Valor = estabelecimentoProdutoRequest.PrecoUnitario ?? estabelecimentoProdutoRequest.PrecoPromocional ?? 0;
                        
                            combustivelWebHookJslRequest.Combustiveis.Add(combustivelRequest);
                        }
                    }

                    result = estabelecimentoBaseApp.IntegrarEstabelecimentoJSL(estabelecimentoBase);
                }
                else
                {
                    estabelecimentoBase = new EstabelecimentoBase();

                    estabelecimentoBase.Associacao = false;
                    estabelecimentoBase.RazaoSocial = request.Descricao;
                    estabelecimentoBase.Descricao = request.Descricao;
                    estabelecimentoBase.CNPJEstabelecimento = request.CNPJEstabelecimento.OnlyNumbers();
                    estabelecimentoBase.IdTipoEstabelecimento = idTipoEstabelecimentoPadraoJsl;

                    estabelecimentoBase.IdPais = cidade.IdPais;
                    estabelecimentoBase.IdEstado = cidade.IdEstado;
                    estabelecimentoBase.IdCidade = cidade.IdCidade;

                    estabelecimentoBase.Bairro = request.Bairro;
                    estabelecimentoBase.Logradouro = request.Logradouro;
                    estabelecimentoBase.Numero = request.Numero;
                    estabelecimentoBase.Complemento = request.Complemento;
                    estabelecimentoBase.CEP = request.CEP;
                    estabelecimentoBase.Email = request.Email;
                    estabelecimentoBase.EmailProtocolo = request.Email;
                    estabelecimentoBase.Latitude = request.Latitude;
                    estabelecimentoBase.Longitude = request.Longitude;
                    estabelecimentoBase.Ativo = true;
                    estabelecimentoBase.PertenceRedeJsl = true;

                    result = estabelecimentoBaseApp.IntegrarEstabelecimentoJSL(estabelecimentoBase);

                    if (!result.IsValid)
                        return new Retorno<object>(true, result.ToFormatedMessage(), null);

                    estabelecimentoBase = estabelecimentoBaseApp.GetQueryByCnpj(estabelecimentoBase.CNPJEstabelecimento).First();

                    estabelecimentoBase.EstabelecimentoBaseProdutos = new List<EstabelecimentoBaseProduto>();

                    if (request.Produtos != null)
                    {
                        foreach (var estabelecimentoProdutoRequest in request.Produtos)
                        {
                            if (!estabelecimentoProdutoRequest.IdProduto.HasValue)
                                continue;
                        
                            var combustivelRequest = new CombustiveisJSLRequest();
                            combustivelRequest.Id = estabelecimentoProdutoRequest.IdProduto.Value;
                            combustivelRequest.Descricao = estabelecimentoProdutoRequest.Descricao;
                            combustivelRequest.Valor = estabelecimentoProdutoRequest.PrecoUnitario ?? estabelecimentoProdutoRequest.PrecoPromocional ?? 0;
                        
                            combustivelWebHookJslRequest.Combustiveis.Add(combustivelRequest);
                        }   
                    }
                }

                if (!result.IsValid)
                    return new Retorno<object>(true, result.ToFormatedMessage(), null);

                result = await estabelecimentoBaseApp.AutorizarEstabelecimentoJSLEmpresas(estabelecimentoBase.IdEstabelecimento, request.AdministradoraPlataforma);

                transaction.Complete();
            }
            
            if (result.IsValid && combustivelWebHookJslRequest.Combustiveis.Any())
                IntegrarCombustivelJsl(combustivelWebHookJslRequest);

            return new Retorno<object>(result.IsValid, result.ToString(), null);
        }

        public Retorno<object> IntegrarCombustivelJsl(CombustivelWebHookJSLRequest request)
        {
            var combustivelApp = _combustivelJslApp;
            var estabelecimentoBaseApp = new EstabelecimentoBaseApp(_estabelecimentoBaseService);
            var estabelecimentoApp = _estabelecimentoApp;

            var queryable = estabelecimentoBaseApp.GetQueryByCnpj(request.CnpjEstabelecimento).Include(c => c.EstabelecimentoBaseProdutos).Include(c => c.EstabelecimentoBaseContasBancarias).Include(o => o.Pais).Include(o => o.Estado).Include(o => o.Cidade).Include(o => o.TipoEstabelecimento);

            if (!queryable.Any())
                return new Retorno<object>(false, $"Estabelecimento Base com CNPJ {request.CnpjEstabelecimento} não cadastrado.");

            var estabelecimentoBase = queryable.First();

            //var lastProdutoId = estabelecimentoBaseProdutoApp.GetUltimoIdProduto();

            var listaAuxiliar = estabelecimentoBase.EstabelecimentoBaseProdutos.ToList();

            var produtosRemover = new List<int>();

            foreach (var produto in listaAuxiliar)
            {
                var query = combustivelApp.GetQueryEstabelecimentoByEstabelecimento(estabelecimentoBase.IdEstabelecimento).Where(c => c.IdProduto == produto.IdProduto);

                if (!query.Any())
                    continue;

                var combustivelJsl = query.Select(c => c.IdCombustivelJSL).First();

                if (request.Combustiveis.All(c => c.Id != combustivelJsl))
                {
                    produtosRemover.Add(produto.IdProduto);
                    //estabelecimentoBase.EstabelecimentoBaseProdutos.Remove(produto);
                }
            }

            estabelecimentoApp.RemoverProdutos(produtosRemover);
            estabelecimentoBaseApp.RemoverCombustiveisJSL(produtosRemover);

            foreach (var combustiveisJslRequest in request.Combustiveis)
            {
                var sincronizaAts = combustivelApp.SincronizaATS(combustiveisJslRequest.Id);

                if (!sincronizaAts)
                    continue;

                if (combustivelApp.CombustivelExistenteEstabelecimento(combustiveisJslRequest.Id, estabelecimentoBase.IdEstabelecimento))
                {
                    var produtoId = combustivelApp.GetQueryEstabelecimentoByCombustivel(combustiveisJslRequest.Id).Where(c => c.IdEstabelecimentoBase == estabelecimentoBase.IdEstabelecimento).Select(c => c.IdProduto).First();

                    foreach (var estabelecimentoBaseEstabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
                    {
                        if (estabelecimentoBaseEstabelecimentoBaseProduto.IdProduto != produtoId)
                            continue;

                        estabelecimentoBaseEstabelecimentoBaseProduto.PrecoPromocional = combustiveisJslRequest.Valor;
                        estabelecimentoBaseEstabelecimentoBaseProduto.PrecoUnitario = combustiveisJslRequest.Valor;
                    }
                }
                else
                {
                    //lastProdutoId++;

                    var produto = new EstabelecimentoBaseProduto();
                    produto.Descricao = combustiveisJslRequest.Descricao;
                    //produto.IdProduto = lastProdutoId;
                    produto.UnidadeMedida = "L";
                    produto.PrecoPromocional = combustiveisJslRequest.Valor;
                    produto.PrecoUnitario = combustiveisJslRequest.Valor;

                    estabelecimentoBase.EstabelecimentoBaseProdutos.Add(produto);

                    estabelecimentoBaseApp.IntegrarEstabelecimentoJSL(estabelecimentoBase);

                    combustivelApp.Integrar(combustiveisJslRequest.Id, produto.IdProduto, estabelecimentoBase.IdEstabelecimento);
                }
            }

            var listaProdutos = estabelecimentoBase.EstabelecimentoBaseProdutos.ToList();
            var listaContasBancarias = estabelecimentoBase.EstabelecimentoBaseContasBancarias.ToList();
            
            estabelecimentoBaseApp.Update(estabelecimentoBase, listaProdutos, listaContasBancarias, request.AdministradoraPlataforma, produtosRemover);

            //if (!retorno.IsValid)
             //   return new Retorno<object>(true, retorno.ToFormatedMessage());

            foreach (var estabelecimentoBaseEstabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
            {
                estabelecimentoApp.ReplicarProdutoBase(estabelecimentoBaseEstabelecimentoBaseProduto);
            }

            return new Retorno<object>(true, string.Empty);
        }

        public Retorno<object> Editar(EstabelecimentoIntegracaoRequest @params, int idEstabelecimento)
        {
            var empresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa) ?? 0;
            var estabelecimento = _estabelecimentoApp.Get(idEstabelecimento);
            if (estabelecimento == null || estabelecimento.IdEmpresa != empresa)
                throw new Exception("Estabelecimento não encontrado ou não vinculado a empresa informada.");

            SetarPropriedades(@params, estabelecimento);

            if (!estabelecimento.Latitude.HasValue && !estabelecimento.Longitude.HasValue &&
                !string.IsNullOrWhiteSpace(estabelecimento.Logradouro))
            {
                var retornoGoogle = new GoogleMapsHelper().GetLocalizacao(
                    $"{estabelecimento.Logradouro},{estabelecimento.Numero},{estabelecimento.Bairro},{estabelecimento.Cidade?.Nome},{estabelecimento.Estado?.Sigla},{estabelecimento.Pais?.Sigla}", EOrigemConsumoServicoExterno.EditarEstabelecimento);
                estabelecimento.Latitude = retornoGoogle.Latitude;
                estabelecimento.Longitude = retornoGoogle.Longitude;
            }

            if (@params.Produtos != null && @params.Produtos.Any())
            {
                foreach (var item in @params.Produtos)
                {
                    if (string.IsNullOrWhiteSpace(item.Descricao))
                        return new Retorno<object>(false, "Descrição do produto é obrigatória. ");
                    if (item.Descricao.Length > 100)
                        throw new Exception("Descrição dos produtos devem possuir no máximo 100 caracteres. ");
                    if (string.IsNullOrWhiteSpace(item.UnidadeMedida))
                        return new Retorno<object>(false, "Unidade de medida é obrigatória para os produtos do estabelecimento. ");
                    if (item.IdProduto.HasValue)
                    {
                        var produtosEdicao = estabelecimento.EstabelecimentoProdutos.Where(
                            x => x.IdProduto == item.IdProduto).ToList();

                        if (produtosEdicao.Count < 1)
                            throw new Exception("Não foi possível identificar o produto pela IdProduto informada.");

                        foreach (var estabelecimentoProduto in produtosEdicao)
                        {
                            estabelecimentoProduto.Descricao = item.Descricao;
                            estabelecimentoProduto.PrecoPromocional = item.PrecoPromocional;
                            estabelecimentoProduto.PrecoUnitario = item.PrecoUnitario;
                            estabelecimentoProduto.UnidadeMedida = item.UnidadeMedida;
                        }

                        if (produtosEdicao.Any())
                            continue;
                    }

                    estabelecimento.EstabelecimentoProdutos
                        .Add(new EstabelecimentoProduto
                        {
                            Descricao = item.Descricao,
                            UnidadeMedida = item.UnidadeMedida,
                            PrecoUnitario = item.PrecoUnitario,
                            PrecoPromocional = item.PrecoPromocional
                        });
                }
            }

            var result = _estabelecimentoApp.Update(estabelecimento, null);
            if (!result.IsValid)
                throw new Exception(result.ToFormatedMessage());

            return new Retorno<object>(true, new { estabelecimento.IdEstabelecimento });
        }

        private void SetarPropriedades(EstabelecimentoIntegracaoRequest @params, Estabelecimento entity)
        {
            if (@params.Latitude.HasValue)
                entity.Latitude = @params.Latitude;
            if (!string.IsNullOrWhiteSpace(@params.Bairro))
                entity.Bairro = @params.Bairro;
            if (!string.IsNullOrWhiteSpace(@params.CEP))
            {
                if (@params.CEP.Length < 8)
                    throw new Exception("CEP deve conter 8 dígitos.");
                entity.CEP = @params.CEP;
            }

            if (!string.IsNullOrWhiteSpace(@params.CNPJEstabelecimento))
                entity.CNPJEstabelecimento = @params.CNPJEstabelecimento;
            if (@params.CodigoBACENPais.HasValue)
            {
                var idPais = _paisApp.GetPaisPorBACEN(@params.CodigoBACENPais.Value)?.IdPais;
                if (idPais.HasValue)
                    entity.IdPais = idPais.Value;
            }

            if (@params.CodigoIBGEEstado.HasValue)
            {
                var idEstado = _estadoApp.GetPorIBGE(@params.CodigoIBGEEstado.Value)?.IdEstado;
                if (idEstado.HasValue)
                    entity.IdCidade = idEstado.Value;
            }

            if (@params.CodigoIBGECidade.HasValue)
            {
                var idCidade = _cidadeApp.GetPorIBGE(@params.CodigoIBGECidade.Value)?.IdCidade;
                if (idCidade.HasValue)
                    entity.IdCidade = idCidade.Value;
            }

            if (@params.Credenciado.HasValue)
                entity.Credenciado = @params.Credenciado.Value;


            if (!string.IsNullOrWhiteSpace(@params.Descricao))
                entity.Descricao = @params.Descricao;

            if (@params.IdTipoEstabelecimento.HasValue)
                entity.IdTipoEstabelecimento = @params.IdTipoEstabelecimento.Value;

            if (!string.IsNullOrWhiteSpace(@params.Logradouro))
                entity.Logradouro = @params.Logradouro;
            if (@params.Longitude.HasValue)
                entity.Longitude = @params.Longitude;
            if (@params.Numero.HasValue)
                entity.Numero = @params.Numero;
            if (!string.IsNullOrWhiteSpace(@params.Complemento))
                entity.Complemento = @params.Complemento;

        }

        /// <summary>
        /// Valida os campos obrigatórios para a integração de um estabelecimento.
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        private ValidationResult ValidarIntegracao(EstabelecimentoIntegracaoRequest @params)
        {
            var validation = new ValidationResult();

            if (!@params.IdTipoEstabelecimento.HasValue)
                validation.Add("O Id do tipo de estabelecimento é obrigatório. ");
            if (!@params.CodigoBACENPais.HasValue)
                validation.Add("Código BACEN do País não informado. ");
            if (!@params.CodigoIBGEEstado.HasValue)
                validation.Add("Código IBGE do Estado não informado. ");
            if (!@params.CodigoIBGECidade.HasValue)
                validation.Add("Código IBGE do Estado não informado. ");
            if (string.IsNullOrWhiteSpace(@params.Descricao))
                validation.Add("Descrição do estabelecimento é obrigatória. ");
            if (string.IsNullOrWhiteSpace(@params.Bairro))
                validation.Add("Bairro não informado.");
            if (string.IsNullOrWhiteSpace(@params.Logradouro))
                validation.Add("Logradouro não informado.");
            if (!string.IsNullOrWhiteSpace(@params.CNPJEmpresa))
                validation.Add(AssertionConcern.AssertArgumentIsValidCNPJ(@params.CNPJEmpresa, "CNPJ da empresa é inválido."));
            else
                validation.Add("CNPJ da empresa é obrigatório.");

            if (!string.IsNullOrWhiteSpace(@params.CNPJEstabelecimento))
                validation.Add(AssertionConcern.AssertArgumentIsValidCNPJ(@params.CNPJEstabelecimento, "CNPJ do estabelecimento é inválido."));
            else
                validation.Add("CNPJ do estabelecimento é obrigatório.");

            validation.Add(AssertionConcern.AssertArgumentIsValidEmail(@params.Email, "E-mail é obrigatório"));

            return validation;
        }

        public byte[] GerarRelatorioGridEstabelecimento(int? idEmpresa, int? idFilial, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao)
        {
            return _estabelecimentoService.GerarRelatorioGridEstabelecimento(idEmpresa, idFilial, descricao, take, page, order, filters, extensao, GetLogo(idEmpresa));
        }
    }
}
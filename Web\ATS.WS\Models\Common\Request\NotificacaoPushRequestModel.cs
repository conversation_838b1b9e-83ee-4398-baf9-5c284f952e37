﻿using ATS.Domain.Enum;
using System.Collections.Generic;

namespace ATS.WS.Models.Common.Request
{
    public class NotificacaoPushRequestModel
    {
        public int? IdNotificacaoPush { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdFilial { get; set; }
        public int IdTipoNotificacao { get; set; }
        public ENotificacaoPushExecutarRegra MomentoExecucao { get; set; }
        public string Descricao { get; set; }
        public string DescricaoMensagem { get; set; }
        public string Sql { get; set; }
        public List<string[]>Items { get; set; }
        public List<int> IdsGruposUsuario { get; set; }
    }
}
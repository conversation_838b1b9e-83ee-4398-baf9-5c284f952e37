﻿using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    /// <summary>
    /// Especifica os horários de check-in automático do usuário
    /// </summary>
    [TrackChanges]
    public class UsuarioHorarioCheckIn
    {
        /// <summary>
        /// Código do usuário
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Sequencial do registro
        /// </summary>
        public int IdHorario { get; set; }

        /// <summary>
        /// Horário de check-in
        /// </summary>
        public int Horario { get; set; }

        #region Navegação Inversa

        public virtual Usuario Usuario { get; set; }

        #endregion
    }
}
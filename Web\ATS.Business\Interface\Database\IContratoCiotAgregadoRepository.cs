﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;

namespace ATS.Domain.Interface.Database
{
    public interface IContratoCiotAgregadoRepository : IRepository<ContratoCiotAgregado>
    {
        IQueryable<ContratoCiotAgregado> ConsultarContratos(DateTime dataInicio, DateTime dataFinal, int? idEmpresa);
        bool AnyContratoAberto(int idProprietario, int idEmpresa);
        List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa);
    }
}
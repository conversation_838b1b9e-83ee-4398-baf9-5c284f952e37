﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Base
{
    public class Reports : ReportBase
    {
        public byte[] GetReport(List<Tuple<object, string>> dataSources, Tuple<string, string, bool>[] reportParametros, bool enableExternalImages, string reportPath, string tipoArquivo)
        {
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipoArquivo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipoArquivo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                foreach (var dataSource in dataSources)
                    LocalReport.DataSources.Add(new ReportDataSource
                    {
                        Value = dataSource.Item1,
                        Name = dataSource.Item2
                    });

                var parametros = new ReportParameter[reportParametros.Length];

                if (parametros.Length > 0)
                    for (var i = 0; i < reportParametros.Length; i++)
                    {
                        parametros[i] = new ReportParameter(reportParametros[i].Item1, reportParametros[i].Item2,
                            reportParametros[i].Item3);
                    }

                LocalReport.EnableExternalImages = enableExternalImages;
                LocalReport.ReportEmbeddedResource = reportPath;

                if (parametros.Length > 0)
                    LocalReport.SetParameters(parametros);

                LocalReport.Refresh();
                
                return LocalReport.Render(tipoRelatorio);
            }
            catch (Exception)
            {
                return new byte[0];
            }
            finally
            {
                Dispose();
            }
        }

        public byte[] GetReport(object listaDados, Tuple<string, string, bool>[] reportParametros,
            bool enableExternalImages, string dataSourceName, string reportPath, string tipoArquivo, Margins margins = null)
        {
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipoArquivo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipoArquivo == "xlsx" || tipoArquivo == "xls")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                LocalReport.DataSources.Add(new ReportDataSource
                {
                    Value = listaDados,
                    Name = dataSourceName
                });

                var parametros = new ReportParameter[reportParametros.Length];

                if (parametros.Length > 0)
                    for (var i = 0; i < reportParametros.Length; i++)
                    {
                        parametros[i] = new ReportParameter(reportParametros[i].Item1, reportParametros[i].Item2,
                            reportParametros[i].Item3);
                    }

                

                LocalReport.EnableExternalImages = enableExternalImages;
                LocalReport.ReportEmbeddedResource = reportPath;

                if (parametros.Length > 0)
                    LocalReport.SetParameters(parametros);

                LocalReport.Refresh();

                if (margins != null)
                {
                    LocalReport.GetDefaultPageSettings().Margins.Top = margins.Top;
                    LocalReport.GetDefaultPageSettings().Margins.Bottom = margins.Botton;
                    LocalReport.GetDefaultPageSettings().Margins.Left = margins.Left;
                    LocalReport.GetDefaultPageSettings().Margins.Right = margins.Right;
                }

                

                return LocalReport.Render(tipoRelatorio);
            }
            catch (Exception)
            {
                return new byte[0];
            }
            finally
            {
                Dispose();
            }
        }
    }
}

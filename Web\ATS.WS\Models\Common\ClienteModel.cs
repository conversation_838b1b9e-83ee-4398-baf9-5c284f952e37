﻿using System.Collections.Generic;
using Newtonsoft.Json;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Models.Common
{
    public class ClienteModel
    {
        public int IdCliente { get; set; }
        public int IdEmpresa { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public ETipoPessoa TipoPessoa { get; set; }
        public string CNPJCPF { get; set; }
        public string RG { get; set; }
        public string OrgaoExpedidorRG { get; set; }
        public string IE { get; set; }
        public byte[] Logo { get; set; }

        public string Celular { get; set; }
        public string Email { get; set; }

        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public int IdPais { get; set; }
        public int IdEstado { get; set; }
        public int IdCidade { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? Latitude { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? Longitude { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public decimal? RaioLocalizacao { get; set; }

        public bool ObrigarCPFReceber { get; set; }
        public bool PermitirAlterarData { get; set; }
        public bool EnviarSMSConfirmacao { get; set; }
        public bool EnviarEmailConfirmacao { get; set; }
        public bool AutenticarCodigoBarraNF { get; set; }
        public EStatusIntegracao StatusIntegracao { get; set; } = EStatusIntegracao.Pendente;
        public bool Ativo { get; set; } = true;
        public List<ClienteProdutoEspecieModel> ListaClienteProdutoEspecie { get; set; }
        public List<ClienteEnderecoResponseModel> ClienteEnderecos { get; set; }
    }
}
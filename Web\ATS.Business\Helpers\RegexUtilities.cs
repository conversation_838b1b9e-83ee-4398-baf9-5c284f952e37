﻿using System;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace ATS.Domain.Helpers
{
    public static class RegexUtilities
    {
        #region Propriedade

        /// <summary>
        /// Referente ao processo de validação do e-mail
        /// </summary>
        private static bool invalid = false;

        #endregion

        #region Private

        /// <summary>
        /// Processo referente em especifico ao domínio do e-mail.
        /// </summary>
        /// <param name="match"></param>
        /// <returns></returns>
        private static string DomainMapper(Match match)
        {
            IdnMapping idn = new IdnMapping();

            string domainName = match.Groups[2].Value;

            try
            {
                domainName = idn.GetAscii(domainName);
            }
            catch (ArgumentException)
            {
                invalid = true;
            }

            return match.Groups[1].Value + domainName;
        }

        #endregion

        /// <summary>
        /// Remover todos os caracteres especiais de uma cadeia de caracteres
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string RemoveSpecialCharacters(this string value)
        {
            if (string.IsNullOrWhiteSpace(value)) return value;
            return Regex.Replace(value, "[^a-zA-Z0-9_.]+", "", RegexOptions.Compiled);
        }

        /// <summary>
        /// Verifica se o valor inserido é um e-mail válido
        /// </summary>
        /// <param name="strIn">E-mail</param>
        /// <returns></returns>
        public static bool IsValidEmail(string strIn)
        {
            invalid = false;

            if (string.IsNullOrWhiteSpace(strIn))
                return false;

            try
            {
                strIn = Regex.Replace(strIn, @"(@)(.+)$", DomainMapper,
                    RegexOptions.None, TimeSpan.FromMilliseconds(200));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }

            if (invalid)
                return false;

            try
            {
                return Regex.IsMatch(strIn,
                      @"^([\w+-.%]+@[\w-.]+\.[A-Za-z]{2,4};?)+$",
                      RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        }
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class PaisMap : EntityTypeConfiguration<Pais>
    {
        public PaisMap()
        {
            ToTable("PAIS");

            HasKey(t => t.IdPais);

            Property(t => t.IdPais)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.BACEN)
                .IsRequired();

            Property(t => t.DataAlteracao)
                .IsRequired()
                .HasColumnType("datetime2");

            Property(t => t.Sigla)
                .IsOptional()
                .IsFixedLength()
                .HasMaxLength(3);
        }
    }
}
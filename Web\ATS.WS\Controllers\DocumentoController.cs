﻿using System;
using System.Web.Http;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Documento;
using ATS.WS.Services;

namespace ATS.WS.Controllers
{
    public class DocumentoController : BaseController
    {
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly SrvDocumento _srvDocumento;

        public DocumentoController(BaseControllerArgs baseArgs, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, SrvDocumento srvDocumento) : base(baseArgs)
        {
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _srvDocumento = srvDocumento;
        }

        /// <summary>
        /// Método responsável por listar os documentos solicitados no pagamento de frete ou protocolo
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarDocumentos(ConsultarDocumentosRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvDocumento.ConsultarDocumento(@params));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }

        /// <summary>
        /// Método responsável por fazer o envio dos documentos para a persistência no banco
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string EnviarDocumento(EnviarDocumentoRequest @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvDocumento.EnviarDocumento(@params));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
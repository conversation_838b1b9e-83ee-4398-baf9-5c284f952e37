﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class UsoTipoEstabelecimentoMap : EntityTypeConfiguration<UsoTipoEstabelecimento>
    {
        public UsoTipoEstabelecimentoMap()
        {
            ToTable("TIPO_ESTABELECIMENTO_USO");

            HasKey(x => x.IdUsoTipoEstabelecimento);

            HasRequired(x => x.TipoEstabelecimento)
                .WithMany(x => x.UsoTipoEstabelecimento)
                .HasForeignKey(x => x.IdTipoEstabelecimento);

            Property(o => o.Uso)
                .IsRequired();
        }
    }
}
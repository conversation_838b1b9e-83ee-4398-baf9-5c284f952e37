﻿using ATS.Domain.Enum;
using System;

namespace ATS.WS.Models.Common.Request
{
    public class CargaAvulsaRequestModel
    {
        public int IdCargaAvulsa { get; set; }
        
        public DateTime DataCadastro { get; set; }

        public int IdUsuariocadastro { get; set; }

        public string CPFMototista { get; set; }

        public string NomeMotorista { get; set; }

        public string PlacaCavalo { get; set; }

        public string PlacaCarreta1 { get; set; }

        public string PlacaCarreta2 { get; set; }

        public string PlacaCarreta3 { get; set; }

        public string Observacao { get; set; }

        public decimal Valor { get; set; }

        public ETipoCarga TipoCarga { get; set; }
        
        public int? IdFilial { get; set; }
        public string CPFUsuario { get; set; }
        public int? IdMotivo { get; set; }
        public bool? IgnorarValidacaoDuplicada { get; set; }
    }
}
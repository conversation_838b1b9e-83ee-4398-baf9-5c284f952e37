﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Protocolo;

namespace ATS.WS.Services
{
    public class SrvProtocolo : SrvBase
    {
        private readonly IEmpresaApp _empresaApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IPagamentoFreteService _pagamentoFreteService;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly IProtocoloService _protocoloService;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public SrvProtocolo(IEmpresaApp empresaApp, IEstabelecimentoApp estabelecimentoApp, IPagamentoFreteService pagamentoFreteService, IViagemEventoApp viagemEventoApp,
            IProtocoloApp protocoloApp, IProtocoloService protocoloService, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp)
        {
            _empresaApp = empresaApp;
            _estabelecimentoApp = estabelecimentoApp;
            _pagamentoFreteService = pagamentoFreteService;
            _viagemEventoApp = viagemEventoApp;
            _protocoloApp = protocoloApp;
            _protocoloService = protocoloService;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        public KeyValuePair<ValidationResult, List<int>> Add(ProtocoloModel @params, int? IdUsuario = null)
        {
            try
            {
                var listaIdsProtocolos = new List<int>();

                var empresaApp = _empresaApp;

                if (@params.idEstabelecimentoBase == 0)
                    throw new Exception("Informe uma estabelecimento para continuar!");

                if (@params.idEmpresa == 0)
                    throw new Exception("Informe uma empresa para continuar!");

                if (@params.tipoDest == ETipoDestinatario.Empresa)
                    if (!_empresaApp.AnyById(@params.idEmpresa))
                        throw new Exception($"A empresa selecionada é inválida!");

                if (@params.tipoDest == ETipoDestinatario.Associacao)
                {
                    var estabelecimento = _estabelecimentoApp.GetEstabelecimentoGeracaoProtocolo(@params.idAssociacao, @params.idEmpresa);

                    if (estabelecimento.Key == 0 || estabelecimento.Value == false)
                        throw new Exception($"O estabelecimento selecionado é inválido!");

                    @params.idAssociacao = estabelecimento.Key;
                }

                var empresaAgrupado = empresaApp.EmpresaAgrupaProtocoloMesmoEvento(@params.idEmpresa);

                if (empresaAgrupado)
                    @params.eventos.GroupBy(x => x.TipoEvento).ToList().ForEach(grp =>
                    {
                        var eventos = @params.eventos.Where(x => x.TipoEvento == grp.Key).ToList();

                        var resposta = ProcessarAdicaoProtocolo(@params, eventos, IdUsuario);

                        listaIdsProtocolos.Add(resposta);
                    });
                else
                    {
                    var resposta = ProcessarAdicaoProtocolo(@params, @params.eventos, IdUsuario);

                        listaIdsProtocolos.Add(resposta);
            }
                    //foreach (var viagemEventosModel in @params.eventos)
                    //{
                    //    var resposta = ProcessarAdicaoProtocolo(@params, @params.eventos.Where(c => c.IdViagemEvento == viagemEventosModel.IdViagemEvento).ToList());

                    //    listaIdsProtocolos.Add(resposta);
                    //}

                return new KeyValuePair<ValidationResult, List<int>>(new ValidationResult(), listaIdsProtocolos);
            }
            catch (Exception e)
            {
                return new KeyValuePair<ValidationResult, List<int>>(new ValidationResult().Add(e.Message), null);
            }
        }

        private int ProcessarAdicaoProtocolo(ProtocoloModel @params, ICollection<ViagemEventosModel> viagemEventos, int? IdUsuario)
        {
            Protocolo protocolo = new Protocolo
            {
                IdEmpresa = @params.idEmpresa,
                IdEstabelecimentoBase = @params.idEstabelecimentoBase,
                DataGeracao = DateTime.Now,
                TipoDestinatario = @params.tipoDest,
                ValorProtocolo = @params.eventos != null ? viagemEventos.Where(x => !x.PagoCartao).Sum(c => c.Valor) : 0,
                ProtocoloEventos = new List<ProtocoloEvento>(),
                ProtocoloAnexos = new List<ProtocoloAnexo>()
            };

            if (@params.tipoDest == ETipoDestinatario.Associacao)
            {
               protocolo.IdEstabelecimentoDestinatario = @params.idAssociacao;
            }

            if (@params.tipoDest == ETipoDestinatario.Empresa)
                protocolo.IdEmpresaDestinatario = @params.IdEmpresaDestinatario;

            //Criado o tipoDest estabelecimento para controlar onde está o protocolo pois devido ao fato de poder transitar entre Estab/Associa/Empresa não se sabia onde ele estava.
            //O que definirá se tem associação é o fáto de ter valor no campo IdEstabDest.
            //@params.tipoDest = ETipoDestinatario.Estabelecimento;
            protocolo.TipoDestinatario = ETipoDestinatario.Estabelecimento;

            if (@params.anexos != null && @params.anexos.Any())
            {
                foreach (var documento in @params.anexos)
                {
                    protocolo.ProtocoloAnexos.Add(new ProtocoloAnexo()
                    {
                        IdDocumento = documento.idDocumento,
                        Token = documento.token,
                    });
                }
            }

            var estabelecimento_ = _estabelecimentoApp.GetByIdEstabelecimentoBase(protocolo.IdEstabelecimentoBase, protocolo.IdEmpresa);


            if (estabelecimento_ != null)
                protocolo.EstabPagamentoAntecipado = estabelecimento_.PagamentoAntecipado;
            
            var pagamentoService = _pagamentoFreteService;

            if (@params.eventos != null && viagemEventos.Count > 0)
            {
                foreach (var evento in viagemEventos)
                {
                    var eventFromDB = _viagemEventoApp.Get(evento.IdViagemEvento);
                    var somarPedagio = pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(eventFromDB, eventFromDB.Viagem);
                    
                    protocolo.ProtocoloEventos.Add(new ProtocoloEvento()
                    {
                        IdViagemEvento = eventFromDB.IdViagemEvento,
                        Status = EStatusProtocoloEvento.Gerado,
                        PesoChegada = eventFromDB.Viagem?.PesoChegada,
                        ValorTotalPagamento = (eventFromDB.ValorTotalPagamento ?? 0) + (somarPedagio ? eventFromDB.Viagem.ValorPedagio : 0),
                        ValorPagamento = eventFromDB.ValorPagamento + (somarPedagio ? eventFromDB.Viagem.ValorPedagio : 0),
                        ValorBruto = eventFromDB.ValorBruto,
                        ValorQuebraMercadoria = eventFromDB.Viagem?.ValorQuebraMercadoria,
                        ValorDifFreteMotorista = eventFromDB.Viagem?.DifFreteMotorista,
                        AnaliseAbono = EStatusAnaliseAbono.NaoAnalisado
                    });
                }
            }

            var ret = _protocoloApp.Add(protocolo, IdUsuario);

            if (!ret.Key.IsValid)
                throw new Exception(ret.Key.ToFormatedMessage());

            if (ret.Value != null)
                return ret.Value.Value;

            throw new Exception("Não foi possível gerar o protocolo.");
        }

        private List<int> AgrupaAdiciona(List<ProtocoloEvento> eventos, int idEstabelecimento ) 
        {
            var listaIds = new List<int>();
            var idEmpresa = eventos.Select(x => x.Protocolo.IdEmpresa).FirstOrDefault();
            var empresa = _empresaApp.Get(idEmpresa);
     
            if (empresa.AgrupaProtocoloMesmoEvento)
            {
                eventos.GroupBy(x => x.ViagemEvento.TipoEventoViagem).ToList()
                    .ForEach(x =>
                    {
                        var evt = eventos.Where(y => y.ViagemEvento.TipoEventoViagem == x.Key).ToList();

                        var respostaGroup = AddProtocolo(evt, idEstabelecimento, idEmpresa);
                        listaIds.Add(respostaGroup);
                    });

                return listaIds;
            }
            
            var resposta = AddProtocolo(eventos, idEstabelecimento, idEmpresa);
            listaIds.Add(resposta);
            return listaIds;
        }

        private List<ProtocoloAnexo> GetProtocoloAnexos(List<ProtocoloEvento> eventos)
        {
            List<ProtocoloAnexo> retorno = new List<ProtocoloAnexo>();

            eventos.GroupBy(x => x.IdProtocolo).ToList()
                .ForEach(grp =>
                {
                    var anexos = _protocoloService.GetAnexos(grp.Key);
                    retorno = retorno.Concat(anexos).ToList();
                });

            return retorno;

        }
        
        private int AddProtocolo(List<ProtocoloEvento> eventos, int idEstabelecimento, int idEmpresa)
        {
            var estabelecimento = _estabelecimentoApp.GetByIdEstabelecimentoBase(idEstabelecimento, idEmpresa);
            
            if (estabelecimento == null)
            {
                var empresa = _empresaApp.Get(idEmpresa);
                throw new Exception($"Verifique se o estabelecimento está credenciado para a empresa {empresa.NomeFantasia}");
            }
            
            Protocolo protocolo = new Protocolo
            {
                IdEmpresa = idEmpresa,
                IdEstabelecimentoBase = idEstabelecimento,
                DataGeracao = DateTime.Now,
                TipoDestinatario = ETipoDestinatario.Associacao,
                ProtocoloEventos = new List<ProtocoloEvento>(),
                ProtocoloAnexos = new List<ProtocoloAnexo>(), 
                IdEstabelecimentoDestinatario = estabelecimento.IdEstabelecimento,
                IdEmpresaDestinatario = idEmpresa,
                GeradoAssociacao = true
            };

            var anexos = GetProtocoloAnexos(eventos);

            protocolo.ProtocoloAnexos = anexos;

            var estabelecimento_ = _estabelecimentoApp.GetByIdEstabelecimentoBase(protocolo.IdEstabelecimentoBase, protocolo.IdEmpresa);

            if (estabelecimento_ != null)
                protocolo.EstabPagamentoAntecipado = estabelecimento_.PagamentoAntecipado;

            if (eventos != null && eventos.Count > 0)
            {
                foreach (var evento in eventos)
                {

                    protocolo.ValorProtocolo += evento.ViagemEvento.HabilitarPagamentoCartao ? 0 : evento.ValorTotalPagamento??0; 

                    protocolo.ProtocoloEventos.Add(new ProtocoloEvento()
                    {
                        IdViagemEvento = evento.IdViagemEvento,
                        Status = EStatusProtocoloEvento.Gerado,
                        PesoChegada = evento?.PesoChegada,
                        ValorTotalPagamento = evento.ValorTotalPagamento,
                        ValorPagamento = evento.ValorPagamento,
                        ValorBruto = evento.ValorBruto,
                        ValorQuebraMercadoria = evento?.ValorQuebraMercadoria,
                        ValorDifFreteMotorista = evento?.ValorDifFreteMotorista,
                        IdProtocoloEvento_Vinculado = evento.IdProtocoloEvento, 
                        IdProtocoloOrigem = evento.IdProtocoloEvento_Vinculado == null ? evento.IdProtocolo : evento.IdProtocoloOrigem
                    });
                }
            }

            var ret = _protocoloApp.Add(protocolo);
            if (!ret.Key.IsValid)
                throw new Exception(ret.Key.ToFormatedMessage());

            var idProtocolos = eventos.Select(x => x.IdProtocolo).Distinct().ToList();

            idProtocolos.ForEach(id =>
            {
                _protocoloApp.SetProtocoloAsAprovado(id);
            });

            if (ret.Value != null)
                return ret.Value.Value;

            throw new Exception("Não foi possível gerar o protocolo.");
        }

        public List<int> ProcessarAdicaoProtocoloAssociacao (List<int> Eventos,  int idEstabelecimentoBase)
        {
            if (Eventos == null)
                Eventos = new List<int>();

            var listaIds = new List<int>();
            var eventos = _protocoloApp.GetProtocolosEventos(Eventos);

            eventos.GroupBy(x => x.Protocolo.IdEmpresa)
            .ToList()
            .ForEach(grp  =>
            {
                var evts = eventos.Where(x => x.Protocolo.IdEmpresa == grp.Key).ToList();

                    var ids = AgrupaAdiciona(evts, idEstabelecimentoBase);

                    foreach (var id in ids)
                        listaIds.Add(id);
            });  

            return listaIds;
        }

        //public byte[] GerarRelatorioProtocolo(string sessionKey, List<int> idsProtocolo, ETipoArquivo? tipoRelatorio)
        //{
        //    var usuario = new AuthSessionApp().GetByToken(sessionKey)?.Usuario;


        //    var protocoloHeaderRelatorioModel = new CrossCutting.Reports.Protocolo.ProtocoloHeaderRelatorioModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy"), Usuario = usuario?.Nome };
        //    var protocoloRelatorioModel = new List<CrossCutting.Reports.Protocolo.ProtocoloRelatorioModel>();

        //    //falta esse
        //    var viagemEvento = _viagemEventoApp.GetEventosViagem(idsProtocolo);

        //    #region Itens do protocolo

        //    foreach (var item in viagemEvento)
        //    {
        //        var model = new ProtocoloRelatorioModel
        //        {

        //        };
        //        protocoloRelatorioModel.Add(model);
        //    }

        //    #endregion

        //    if (!tipoRelatorio.HasValue)
        //        tipoRelatorio = ETipoArquivo.PDF;
        //    //falta esse
        //    var report = new CrossCutting.Reports.PagamentoFrete.Pagamentos.PagamentosReport().Relatorio(protocoloRelatorioModel, protocoloHeaderRelatorioModel, (int)tipoRelatorio);
        //    return report;
        //}

        public Retorno<string> AgendarPrevisaoPagamento(int idProtocolo, string cNPJEstabelecimento, string CNPJAplicacao, string token, DateTime dataPrevisaoPagamento)
        {
            try
            {
                var protocolo = _protocoloApp.Get(idProtocolo);
                var empresa = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(CNPJAplicacao, token);

                if (empresa == null || !empresa.Any(x => x.IdEmpresa == protocolo.IdEmpresa))
                    return new Retorno<string>(false, "Você não possui permissão para alterar este protocolo.");

                _protocoloApp.AgendarPrevisaoPagamento(idProtocolo, dataPrevisaoPagamento);
            }
            catch (Exception e)
            {
                return new Retorno<string>(false, e.Message);
            }

            return new Retorno<string>(true, "Previsão de agendamento de pagamento realizada com sucesso!");
        }

        public void Processar(string cnpjAplicacao, string token, int idProtocolo)
        {
            _protocoloApp.Processar(idProtocolo);
        }

        /// <summary>
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public Retorno<ValidationResult> Update(ProtocoloModel @params)
        {
            try
            {

                if (@params.idEstabelecimentoBase == 0)
                    throw new Exception("Informe uma estabelecimento para continuar!");

                if (@params.idEmpresa == 0)
                    throw new Exception("Informe uma empresa para continuar!");


                var protocolo = _protocoloApp.Get(@params.idProtocolo.Value);

                if (protocolo != null)
                {
                    protocolo.IdEmpresa = @params.idEmpresa;
                    protocolo.IdEstabelecimentoBase = @params.idEstabelecimentoBase;
                    protocolo.ValorProtocolo = Convert.ToDecimal(@params.eventos?.Where(p => !p.PagoCartao).Sum(x => x.Valor));
                };


                if (@params.anexos != null && @params.anexos.Count() > 0)
                {
                    foreach (var documento in @params.anexos)
                    {
                        protocolo.ProtocoloAnexos.Add(new ProtocoloAnexo()
                        {
                            IdProtocolo = @params.idProtocolo.Value,
                            IdDocumento = documento.idDocumento,
                            Token = documento.token,
                        });
                    }
                }

                if (@params.eventos != null && @params.eventos.Count() > 0)
                {
                    foreach (var evento in @params.eventos)
                    {
                        var eventFromDB = _viagemEventoApp.Get(evento.IdViagemEvento);
                        protocolo.ProtocoloEventos.Add(new ProtocoloEvento()
                        {
                            IdProtocolo = @params.idProtocolo.Value,
                            IdViagemEvento = eventFromDB.IdViagemEvento,
                            Status = EStatusProtocoloEvento.Gerado
                        });
                    }
                }

                var ret = _protocoloApp.Add(protocolo);

                return new Retorno<ValidationResult>(true, ret.Key);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<ValidationResult>($"{nameof(Add)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Realiza a consulta dos protocolos
        /// </summary>
        /// <param name="cnpjAplicacao"></param>
        /// <param name="token"></param>
        /// <param name="cnpjEmpresa"></param>
        /// <returns></returns>
        public Retorno<List<ProtocoloConsultaModel>> Consultar(string cnpjAplicacao, string token, string cnpjEmpresa, int? idProtocolo, bool retornarEventosPagosNoCartao = false)
        {
            var empresa = _empresaApp.Get(cnpjEmpresa);
            
            if (!string.IsNullOrWhiteSpace(cnpjAplicacao))
            {
                var autenticacao = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);
                if (autenticacao == null || !autenticacao.Any(x => x.IdEmpresa == empresa.IdEmpresa))
                    throw new ApplicationException("A Empresa não possui autorização para realizar esta consulta. ");
            }

            var retorno = _protocoloService.ConsultarProtocolos(empresa?.IdEmpresa ?? 0, idProtocolo ?? 0, false, false, retornarEventosPagosNoCartao);
            return new Retorno<List<ProtocoloConsultaModel>>(true, retorno);
        }

        public void IniciarTransito(int idProtocolo, string codigoRastreamento, EPerfil usuLogado)
        {
            var prot = _protocoloApp.GetComEvento(idProtocolo);

            if (prot == null)
                throw new Exception($"Nenhum protocolo encontrado para o id {idProtocolo}!");

            if (prot.StatusProtocolo != EStatusProtocolo.Gerado && prot.StatusProtocolo != EStatusProtocolo.Rejeitado && prot.StatusProtocolo != EStatusProtocolo.EmTransito)
                throw new Exception("Apenas protocolos com status gerado, em trânsito ou rejeitado podem entrar em trânsito!");

            foreach (var item in prot.ProtocoloEventos)
            {
                item.Status = EStatusProtocoloEvento.Gerado;
                item.IdMotivo = null;
                item.Detalhamento = string.Empty;
            }

            prot.StatusProtocolo = EStatusProtocolo.EmTransito;
            if (prot.DataRecebidoEmpresa.HasValue && (prot.TipoDestinatario == ETipoDestinatario.Associacao || (prot.TipoDestinatario == ETipoDestinatario.Estabelecimento && !prot.IdEstabelecimentoDestinatario.HasValue)))
                prot.StatusProtocolo = EStatusProtocolo.Recebido;
            prot.EmpresaDestinatario = _empresaApp.Get(prot.IdEmpresa);

            if (prot.Empresa.NaoValidarProtocoloRecebidoEmpresa)
            {
                if ((prot.TipoDestinatario == ETipoDestinatario.Estabelecimento && !prot.IdEstabelecimentoDestinatario.HasValue) || prot.TipoDestinatario == ETipoDestinatario.Associacao)
                {
                prot.StatusProtocolo = EStatusProtocolo.Recebido;
                prot.DataRecebidoEmpresa = DateTime.Now;
            }
            }
            
            //Ao iniciar transito se estiver com estabelecimento (prot.TipoDestinatario == ETipoDestinatario.Estabelecimento) e tiver id de associacao (prot.IdEstabelecimentoDestinatario.HasValue)
            //mando o protocolo pra associacao
            if (prot.TipoDestinatario == ETipoDestinatario.Estabelecimento && prot.IdEstabelecimentoDestinatario.HasValue)
                prot.TipoDestinatario = ETipoDestinatario.Associacao;
            //caso esteja com estabelecimento e nao tenha caido no if anterior irá enviar para empresa, pois nao tem associacao no protocolo.
            else if (prot.TipoDestinatario == ETipoDestinatario.Estabelecimento)
                prot.TipoDestinatario = ETipoDestinatario.Empresa;
            //caso esteja com associacao ela só pode enviar para a empresa
            else if (prot.TipoDestinatario == ETipoDestinatario.Associacao)
                prot.TipoDestinatario = ETipoDestinatario.Empresa;



            prot.CodigoRastreamento = codigoRastreamento;
            prot.DataTransito = DateTime.Now;

            var updt = _protocoloApp.Update(prot);

            if (!updt.IsValid)
                throw new Exception(updt.ToFormatedMessage());
        }

        public object GetQRCodeProtocolo(int idEmpresa, string cpfUsuario, string tokenHorario)
        {
            var empresa = _empresaApp.Get(idEmpresa);
            if (empresa == null) throw new Exception("Empresa não identificada para geração do QR Code");

            var tokenQRCode = $"{cpfUsuario}-{empresa.CNPJ}-{tokenHorario}";
            string qrCodeBase64 = "data:image/png;base64,";
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

            if (new FileInfo(caminhoAplicacao + @"\Content\Image\logo-ats-login.png").Exists)
            {
                var image = (Bitmap)Image.FromFile(caminhoAplicacao + @"\Content\Image\logo-ats-login.png");
                qrCodeBase64 += new QRCodeHelper().GerarQRCode(image, tokenQRCode);
            }
            else
                qrCodeBase64 += new QRCodeHelper().GerarQRCode(null, tokenQRCode);


            return new { qrCode = qrCodeBase64, Token = tokenQRCode };
        }

        /// <summary>
        /// Realiza a consulta dos protocolos por Estabelecimento
        /// </summary>
        /// <param name="token"></param>
        /// <param name="cnpjAplicacao"></param>
        /// <param name="cnpjEstabelecimento"></param>
        /// <param name="dataPagamentoInicial"></param>
        /// <param name="dataPagamentoFinal"></param>
        /// <param name="dataGeracaoInicial"></param>
        /// <param name="dataGeracaoFinal"></param>
        /// <returns></returns>
        public Retorno<List<ProtocoloConsultaModel>> ConsultarPorEstabelecimento(string token, string cnpjAplicacao, string cnpjEstabelecimento, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, int? idProtocolo)
        {
            var retorno = new List<ProtocoloConsultaModel>();
            if (string.IsNullOrWhiteSpace(cnpjAplicacao))
                throw new ApplicationException("CNPJ de aplicação é obrigatório.");

            var autenticacao = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);
            if (autenticacao == null || !autenticacao.Any())
                throw new ApplicationException("Empresa não possui permissão para realizar esta consulta.");

            var estabelecimento = _estabelecimentoApp.Get(cnpjEstabelecimento, autenticacao.FirstOrDefault().IdEmpresa);
            var idsEstabelecimentosBase = estabelecimento?.Credenciamentos?
                .Where(x => x.IdEstabelecimentoBase.HasValue)
                .Select(x => x.IdEstabelecimentoBase.Value)
                .ToList();

            if (idsEstabelecimentosBase == null || !idsEstabelecimentosBase.Any())
                return new Retorno<List<ProtocoloConsultaModel>>(true);

            if (dataPagamentoInicial.HasValue)
                dataPagamentoInicial = new DateTimeHelper().StartOfDay(dataPagamentoInicial.Value);
            if (dataPagamentoFinal.HasValue)
                dataPagamentoFinal = new DateTimeHelper().EndOfDay(dataPagamentoFinal.Value);
            if (dataGeracaoInicial.HasValue)
                dataGeracaoInicial = new DateTimeHelper().StartOfDay(dataGeracaoInicial.Value);
            if (dataGeracaoFinal.HasValue)
                dataGeracaoFinal = new DateTimeHelper().EndOfDay(dataGeracaoFinal.Value);

            var protocolos = _pagamentoFreteService.ConsultarProtocolosPorEstabelecimentos(idsEstabelecimentosBase, dataPagamentoInicial, dataPagamentoFinal, dataGeracaoInicial, dataGeracaoFinal, idProtocolo);

            foreach (var protocolo in protocolos)
            {
                var protocoloModel = new ProtocoloConsultaModel
                {
                    IdEstabelecimentoBase = protocolo.IdEstabelecimentoBase,
                    IdProtocolo = protocolo.IdProtocolo,
                    DataPagamento = protocolo.DataPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                    DataGeracao = protocolo.DataGeracao.ToString("yyyy-MM-dd HH:mm:ss"),
                    ValorProtocolo = protocolo.ValorProtocolo,
                    Processado = protocolo.Processado,
                    StatusProtocolo = (int)protocolo.StatusProtocolo,
                    DescricaoStatusProtocolo = EnumHelpers.GetDescription(protocolo.StatusProtocolo),
                    Anexos = protocolo.ProtocoloAnexos.Select(y => new ProtocoloAnexoConsultaModel
                    {
                        Token = y.Token,
                        IdProtocolo = y.IdProtocolo,
                        IdDocumento = y.IdDocumento,
                        IdProtocoloAnexo = y.IdProtocoloAnexo
                    }).ToList(),
                    Antecipacoes = protocolo.ProtocoloAntecipacoes.Select(y => new ProtocoloAntecipacaoConsultaModel
                    {
                        IdProtocoloAntecipacao = y.IdProtocoloAntecipacao,
                        Status = y.Status,
                        IdProtocolo = y.IdProtocolo,
                        IdMotivo = y.IdMotivo,
                        DataSolicitacao = y.DataSolicitacao.ToString("yyyy-MM-dd HH:mm:ss"),
                        ValorPagamentoAntecipado = y.ValorPagamentoAntecipado
                    }).ToList(),
                    Eventos = protocolo.ProtocoloEventos.Where(z => z.Status != EStatusProtocoloEvento.Rejeitado).Select(y => new ProtocoloEventoConsultaModel
                    {
                        Status = y.Status,
                        IdViagemEvento = y.IdViagemEvento,
                        IdProtocolo = y.IdProtocolo,
                        IdMotivo = y.IdMotivo,
                        IdProtocoloEvento = y.IdProtocoloEvento,
                        TipoEventoViagem = y.ViagemEvento?.TipoEventoViagem,
                        ValorTotalPagamento = y.ViagemEvento?.ValorTotalPagamento,
                        DataHoraPagamento = y.ViagemEvento?.DataHoraPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                        NumeroRecibo = y.ViagemEvento?.NumeroRecibo,
                        PesoChegada = y.ViagemEvento?.Viagem.PesoChegada,
                        PesoDiferenca = y.ViagemEvento?.Viagem.PesoDiferenca,
                        ValorDifFreteMotorista = y.ViagemEvento?.Viagem.DifFreteMotorista,
                        ValorQuebraMercadoria = y.ViagemEvento?.Viagem.ValorQuebraMercadoria,
                        IdViagem = y.ViagemEvento?.Viagem.IdViagem,
                        ValorDesconto = y.ValorDesconto,
                        IdMotivoDesconto = y.IdMotivoDesconto,
                        DescricaoMotivoDesconto = y.DescricaoMotivoDesconto,
                        ObservacaoDesconto = y.ObservacaoDesconto
                    }).ToList()
                };

                retorno.Add(protocoloModel);
            }

            return new Retorno<List<ProtocoloConsultaModel>>(true, retorno);
        }

        /// <summary>
        /// Realiza o pagamento de um protocolo
        /// </summary>
        /// <param name="idProtocolo"></param>
        /// <param name="token"></param>
        /// <param name="cnpjAplicacao"></param>
        /// <returns></returns>
        public Retorno<object> RealizarPagamento(int idProtocolo, string token, string cnpjAplicacao)
        {
            try
            {
                var protocolo = _protocoloApp.Get(idProtocolo);
                var empresa = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);

                if (empresa == null || !empresa.Any(x => x.IdEmpresa == protocolo.IdEmpresa))
                    return new Retorno<object>(false, "Você não possui permissão para alterar este protocolo.", new { Pago = false, IdProtocolo = idProtocolo });

                var validationResult = _protocoloApp.RealizarPagamento(idProtocolo);
                return validationResult.IsValid
                    ? new Retorno<object>(true, new { Pago = true, IdProtocolo = idProtocolo })
                    : new Retorno<object>(false, validationResult.ToString(), new { Pago = false, IdProtocolo = idProtocolo });
            }
            catch (Exception)
            {
                return new Retorno<object>(false, "Requisição não finalizada. Erro interno.", new { Pago = false, IdProtocolo = idProtocolo });
            }

        }

        public ValidationResult VincularPagamentoProtocolo(int idProtocolo, string token, int idUsuario)
        {
            return _protocoloApp.VincularPagamentoProtocolo(idProtocolo, token, idUsuario);
        }

        public byte[] GerarRelatorioGrid(int? idEstabelecimento, int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string tipoArquivo)
        {
            return _protocoloApp.GerarRelatorioGrid(idEstabelecimento, idEmpresa, order, filters, tipoArquivo, GetLogo(idEmpresa ?? 0));
    }

        public byte[] GerarRelatorioGridRecebimentoProtocolo(int? idEmpresa, OrderFilters order, List<QueryFilters> filters, string tipoArquivo)
        {
            return _protocoloApp.GerarRelatorioGridRecebimentoProtocolo(order, filters, tipoArquivo,
                GetLogo(idEmpresa ?? 0));
        }

        public byte[] GerarRelatorioTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo)
        {
            return _protocoloApp.GerarRelatorioTriagemProtocolo(idEmpresa, idsEstabelecimentosBase,
                idEstabelecimento, idAssociacao, dataGeracaoInicial, dataGeracaoFinal, dataPagamentoInicial,
                dataPagamentoFinal, associacoesPorEmpresa, order, filters, tipoArquivo, GetLogo(idEmpresa ?? 0));
        }
    }
}
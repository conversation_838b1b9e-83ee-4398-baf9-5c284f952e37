﻿using System.Collections.Generic;
using System.Text.Json.Serialization;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;

namespace ATS.Domain.Models.Usuario
{
    public class ConsultarGridUsuariosLideradosResponse
    {
        public List<ConsultarGridUsuariosLideradosResponseItem> items { get; set; } = new();
        public int totalItems { get; set; }
    }
    public class ConsultarGridUsuariosLideradosResponseItem
    {
        public int IdUsuario { get; set; }
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public string Login { get; set; }
        private EPerfil _perfilEnum;
        [JsonIgnore]
        public EPerfil PerfilEnum
        {
            get => _perfilEnum;
            set
            {
                _perfilEnum = value;
                Perfil = _perfilEnum.GetDescription();
            }
        }
        public string Perfil { get; set; }
    }
}

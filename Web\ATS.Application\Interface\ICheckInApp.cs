﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.Models;

namespace ATS.Application.Interface
{
    public interface ICheckInApp : IAppBase<CheckIn>
    {
        ValidationResult Add(CheckIn checkIn);
        IQueryable<CheckIn> GetCheckInsHoje(int? idTipoCavalo, int? idTipoCarreta);
        CheckIn GetLast(int idUsuario);
        IQueryable<CheckIn> GetCheckInsPeriodo(DateTime dataInicial, DateTime? dataFinal = null);
        List<CheckinConsultaModel> GetByPlaca(int idEmpresa, DateTime dataInicial, DateTime dataFinal, string placa);
        List<CheckinConsultaModel> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, int IdMotoristaVeiculo);
        List<CheckinConsultaModel> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf);
    }
}
﻿using System;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Configuration;
using ATS.Domain.Enum;
using ATS.Domain.Service;

namespace ATS.Data.Repository.EntityFramework
{
    public class EstabelecimentoRepository : Repository<Estabelecimento>, IEstabelecimentoRepository
    {
        public EstabelecimentoRepository(AtsContext context) : base(context)
        {
        }
        
        /// <inheritdoc />
        public IEnumerable<int> GetIdsEstabelecimentoNoRaio(decimal latitude, decimal longitude, decimal raioKm)
        {
            if (raioKm == 0)
                return new List<int>();

            var context = (AtsContext) Context;
            if (context.Database.Connection.State == ConnectionState.Closed)
                context.Database.Connection.Open();

            var sql = @"select e.IdEstabelecimento
                        from [dbo].[estabelecimento] e
                        where e.ativo = 1
                        and   coalesce(e.latitude, 0) <> 0
                        and   coalesce(e.longitude, 0) <> 0
                        and   [dbo].[GetDistanciaKm](@latBase, @lonBase, e.latitude, e.longitude) <= @raioKm";

            var ids = context.Database.SqlQuery<int>(sql,
                    new SqlParameter("@latBase", SqlDbType.Decimal) {Value = latitude},
                    new SqlParameter("@lonBase", SqlDbType.Decimal) {Value = longitude},
                    new SqlParameter("@raioKm", SqlDbType.Decimal) {Value = raioKm})
                .ToArray();

            return ids;
        }

        /// <inheritdoc />
        public IEnumerable<int> GetIdsEstabelecimentoNoPercurso(
            decimal latitudeOrigem, decimal longitudeOrigem,
            decimal latitudeDestino, decimal longitudeDestino,
            decimal raioKm)
        {
            var result = new List<int>();

            // Buscar locais próximos do ponto inicial
            var proximos = GetIdsEstabelecimentoNoRaio(latitudeOrigem, longitudeOrigem, raioKm);
            result.AddRange(new List<int>());
            result.AddRange(proximos);

            // Buscar locais próximos do ponto final
            proximos = GetIdsEstabelecimentoNoRaio(latitudeDestino, longitudeDestino, raioKm);
            result.AddRange(proximos);

            // Buscar locais próximos das coordenadas indicadas pelo Google para o trajeto principal
            /*var googleResult = new GoogleMapsHelper()
                .GetDirections(
                    new GeoPoint(latitudeOrigem, longitudeOrigem),
                    new GeoPoint(latitudeDestino, longitudeDestino));*/

            AtualizaConsumoServicoExterno(new ConsumoServicoExterno()
            {
                Uri = "https://maps.googleapis.com/maps/api/directions/json?origin=latitde/longitude&key=" + WebConfigurationManager.AppSettings.Get("GOOGLE_KEY"),
                Descricao = EOrigemConsumoServicoExterno.GetIdsEstabelecimentoNoPercurso.GetDescription(),
                OrigemConsumoServicoExterno = EOrigemConsumoServicoExterno.GetIdsEstabelecimentoNoPercurso,
                DataConsumo = DateTime.Now
            });
            
            /*if (googleResult.routes.Any() && googleResult.routes[0].legs.Any() && googleResult.routes[0].legs[0].steps.Any())
                foreach (var step in googleResult.routes[0].legs[0].steps)
                {
                    var kmRaio = Convert.ToDecimal(step.distance.value / 1000);

                    if (kmRaio < raioKm) kmRaio = raioKm;

                    proximos = GetIdsEstabelecimentoNoRaio(
                        step.start_location.lat, step.start_location.lng, kmRaio);
                    if (proximos.Any())
                        result.AddRange(proximos);
                }*/

            result = result.Distinct().ToList();
            return result;
        }
        
        public int? GetIdPorCnpj(string cnpj, int idEmpresa)
        {
            var idEstabelecimento = Where(x => x.CNPJEstabelecimento == cnpj && x.IdEmpresa == idEmpresa)
                .Select(x => x.IdEstabelecimento).FirstOrDefault();

            return idEstabelecimento > 0 ? idEstabelecimento : (int?) null;
        }
        
        private void AtualizaConsumoServicoExterno(ConsumoServicoExterno consumoServicoExterno)
        {
            try
            {
                /*var atualizaConsumoServicoExterno = WebConfigurationManager.AppSettings.Get("ATUALIZA_CONSUMO_SERVICO_EXTERNO");
                if(atualizaConsumoServicoExterno == "true")
                    new ConsumoServicoExternoService().Add(consumoServicoExterno);*/
            }
            catch (Exception)
            {
                // ignored
            }
        }
    }
}

using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Domain.Enum
{
    public enum EDeclaracaoCiotViagem
    {
        [EnumMember, Description("Não habilita a declaração de Ciot na viagem")]
        NaoHabilitarDeclaracaoCiot = 0,
        
        [EnumMember, Description("Habilita a declaração de Ciot na viagem")]
        HabilitarDeclaracaoCiot = 1,
        
        [EnumMember, Description("Força habilitação de declaração de Ciot para não equiparados na viagem")]
        ForcarDeclaracaoCiotNaoEquiparado = 2
    }
}
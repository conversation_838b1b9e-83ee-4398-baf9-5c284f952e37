﻿using System;
using System.Runtime.Serialization;

namespace ATS.Data.Repository.External.SistemaInfo.Cartao
{
    public class CartaoAtsException : Exception
    {
        public CartaoAtsException()
        {
        }

        public CartaoAtsException(string message) : base(message)
        {
        }

        public CartaoAtsException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected CartaoAtsException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}

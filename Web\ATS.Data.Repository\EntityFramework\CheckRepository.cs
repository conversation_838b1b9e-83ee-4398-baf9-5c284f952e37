﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class CheckRepository : Repository<ViagemCheck>, ICheckRepository
    {
        public CheckRepository(AtsContext context) : base(context)
        {
        }
        
        public int GetNewId()
        {
            return int.MinValue;
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace ATS.Domain.Interface.Service
{
    public interface ITipoCavaloService : IService<TipoCavalo>
    {
        TipoCavalo Get(int id);
        ValidationResult Add(TipoCavalo tipoCavalo);
        ValidationResult Update(TipoCavalo tipoCavalo);
        IQueryable<TipoCavaloGrid> Consultar(string nome, int? idEmpresa);
        ValidationResult Inativar(int idTipoCavalo);
        ValidationResult Reativar(int idTipoCavalo);
        IQueryable<TipoCavalo> GetPorCategoria(ECategoriaTipoCavalo categoria);
        IQueryable<TipoCavalo> List(Expression<Func<TipoCavalo, bool>> where);
        IEnumerable<TipoCavalo> GetRegistrosAtualizados(DateTime dataAtualizacao, List<int> idsEmpresa);
        IQueryable<TipoCavalo> All();
        object ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);

        /// <summary>
        /// Retorna apenas o objeto de tipo cavalo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        TipoCavalo GetTipoCavalo(int id);

        object ConsultarSemEmpresa();

        TipoCavalo GetPorDescricao(string nome, int idEmpresa);
        int? GetCapacidadePorPlaca(string placa);
        ValidationResult AlterarStatus(int tipoCavaloId);
    }
}
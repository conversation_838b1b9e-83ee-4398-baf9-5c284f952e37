﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Validation;
using ATS.Domain.Entities;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using Newtonsoft.Json;
using Sistema.Framework.Util.Helper;

namespace ATS.Domain.Service
{
    public class EstabelecimentoBaseContaBancariaService : ServiceBase, IEstabelecimentoBaseContaBancariaService
    {
        private readonly IEstabelecimentoBaseContaBancariaRepository _repository;

        public EstabelecimentoBaseContaBancariaService(IEstabelecimentoBaseContaBancariaRepository repository)
        {
            _repository = repository;
        }

        public ValidationResult AddOrUpdateListContasBancarias(List<EstabelecimentoBaseContaBancaria> contasBancarias, int idEstabelecimentoBase)
        {
            foreach (var conta in contasBancarias)
            {
                Validator.Validate(conta);

                if (conta.IdEstabelecimentoBaseContaBancaria == 0)
                {
                    conta.IdEstabelecimentoBase = idEstabelecimentoBase;
                    _repository.Add(conta);
                    continue;
                }

                var persistedConta = GetById(conta.IdEstabelecimentoBaseContaBancaria);
                ReflectionHelper.CopyProperties(conta, persistedConta);

                _repository.Update(persistedConta);
            }

            return new ValidationResult();
        }

        public void DeleteListContasBancarias(List<int> idsContasBancarias)
        {
            foreach (var id in idsContasBancarias)
            {
                var conta = GetById(id);

                if (conta != null)
                    _repository.Delete(conta);
            }
        }

        public EstabelecimentoBaseContaBancaria GetById(int id)
        {
            return _repository.FirstOrDefault(o => o.IdEstabelecimentoBaseContaBancaria == id);
        }

        public List<BancosFebrabanModel> GetBancosFebraban()
        {
            try
            {
                var url = ConfigurationManager.AppSettings["MS_URL"];

                var request = (HttpWebRequest)WebRequest.Create($"{url}/Cadastros/Api/Bancos");
                request.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;

                using (var response = (HttpWebResponse)request.GetResponse())
                using (var stream = response.GetResponseStream())

                using (var reader = new StreamReader(stream ?? throw new InvalidOperationException()))
                {
                    var json = reader.ReadToEnd();
                    var dados = JsonConvert.DeserializeObject<List<BancosFebrabanModel>>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

                    return dados.ToList().OrderBy(o => o.nome).ToList();
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
            finally
            {
                Dispose();
            }
        }

        public static void ValidarCnpjTitular(string cnpjTitular, string cnpjEstabelecimento)
        {
            var cnpjTitularRaiz = cnpjTitular?.Substring(0, 8);
            var cnpjEstabelecimentoRaiz = cnpjEstabelecimento?.Substring(0, 8);

            if (cnpjTitularRaiz != cnpjEstabelecimentoRaiz)
                throw new Exception($"O CNPJ {cnpjTitular.FormatarCpfCnpj()} não corresponde com a raiz do CNPJ do estabelecimento.");
        }
    }
}

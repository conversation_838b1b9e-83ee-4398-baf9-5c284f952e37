﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;

namespace ATS.Domain.Service
{
    public class TipoCavaloService : ServiceBase, ITipoCavaloService
    {
        private readonly ITipoCavaloRepository _tipoCavaloRepository;
        private readonly ITipoCavaloDapper _tipoCavaloDapper;
        private readonly IVeiculoRepository _veiculoRepository;
        private readonly IUserIdentity _userIdentity;

        public TipoCavaloService(ITipoCavaloRepository tipoCavaloRepository, ITipoCavaloDapper tipoCavaloDapper, IVeiculoRepository veiculoRepository, IUserIdentity userIdentity)
        {
            _tipoCavaloRepository = tipoCavaloRepository;
            _tipoCavaloDapper = tipoCavaloDapper;
            _veiculoRepository = veiculoRepository;
            _userIdentity = userIdentity;
        }

        /// <summary>
        /// Retorna o objeto de Tipo de Veículo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public TipoCavalo Get(int id)
        {
            return _tipoCavaloRepository
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.IdTipoCavalo == id);
        }

        /// <summary>
        /// Retorna apenas o objeto de tipo cavalo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public TipoCavalo GetTipoCavalo(int id)
        {
            return _tipoCavaloRepository.FirstOrDefault(x => x.IdTipoCavalo == id);
        }

        /// <summary>
        /// Adicionar o registro
        /// </summary>
        /// <param name="tipoCavalo"></param>
        /// <returns></returns>
        public ValidationResult Add(TipoCavalo tipoCavalo)
        {
            try
            {
                if (tipoCavalo.NumeroEixos < 0)
                    return new ValidationResult().Add("Número de eixos não pode ser um valor negativo.");


                _tipoCavaloRepository.Add(tipoCavalo);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro de Tipo de veículo
        /// </summary>
        /// <param name="tipoCavalo"></param>
        /// <returns></returns>
        public ValidationResult Update(TipoCavalo tipoCavalo)
        {
            try
            {
                _tipoCavaloRepository.Update(tipoCavalo);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para consultar Tipo de Veículo.
        /// </summary>
        /// <param name="nome">Nome de Tipo de Veículo.</param>
        /// <returns>IQueryable de TipoCavaloGrid</returns>
        public IQueryable<TipoCavaloGrid> Consultar(string nome, int? idEmpresa)
        {
            if (nome == null)
                nome = string.Empty;

            var result = _tipoCavaloRepository.Consultar(nome);

            if (idEmpresa.HasValue)
                result = result.Where(x => x.IdEmpresa == idEmpresa);

            return result;
        }

        /// <summary>
        /// Inativar o tipo de veículo
        /// </summary>
        /// <param name="idTipoCavalo">Código do tipo de veículo a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idTipoCavalo)
        {
            try
            {
                ITipoCavaloRepository tipoCavaloRepository = _tipoCavaloRepository;

                TipoCavalo tipoCavalo = tipoCavaloRepository.Get(idTipoCavalo);
                if (!tipoCavalo.Ativo)
                    return new ValidationResult().Add($"Tipo de Veículo já desativado na base de dados.");

                tipoCavalo.Ativo = false;

                tipoCavaloRepository.Update(tipoCavalo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar o tipo de veículo
        /// </summary>
        /// <param name="idTipoCavalo">Código do tipo de veículo a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idTipoCavalo)
        {
            try
            {
                ITipoCavaloRepository tipoCavaloRepository = _tipoCavaloRepository;

                TipoCavalo tipoCavalo = tipoCavaloRepository.Get(idTipoCavalo);
                if (tipoCavalo.Ativo)
                    return new ValidationResult().Add($"Tipo de Veículo já ativado na base de dados.");

                tipoCavalo.Ativo = true;

                tipoCavaloRepository.Update(tipoCavalo);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Cavalo por categoria
        /// </summary>
        /// <param name="categoria">Categoria de Tipo de Veículo</param>
        /// <returns>IQueryable de Tipo de Veículo</returns>
        public IQueryable<TipoCavalo> GetPorCategoria(ECategoriaTipoCavalo categoria)
        {
            return _tipoCavaloRepository.GetPorCategoria(categoria);
        }

        public object ConsultarSemEmpresa()
        {
            return _tipoCavaloRepository
                .Where(x => x.IdEmpresa == null)
                .Select(x => new
                {
                    x.Nome,
                    x.IdTipoCavalo
                });
        }

        /// <summary>
        ///  Método utilizado para listar os Tipos de Cavalo
        /// </summary>
        /// <param name="where"></param>
        /// <returns>IQueryable de Tipo de Veículo</returns>
        public IQueryable<TipoCavalo> List(Expression<Func<TipoCavalo, bool>> where)
        {
            return _tipoCavaloRepository.Find(where).AsQueryable();
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Cavalo atualizados
        /// </summary>
        /// <param name="dataAtualizacao">Data de atualização</param>
        /// <returns>IQueryable de TipoCavalo</returns>
        public IEnumerable<TipoCavalo> GetRegistrosAtualizados(DateTime dataAtualizacao, List<int> idsEmpresa)
        {
            return _tipoCavaloRepository.GetTipoCavaloAtualizado(dataAtualizacao, idsEmpresa);
        }

        /// <summary>
        /// Método utilizado para listar todos os Tipos de Cavalos ativos
        /// </summary>
        /// <returns>IQueryable de TipoCavalo</returns>
        public IQueryable<TipoCavalo> All()
        {
            return _tipoCavaloRepository.Find(x => x.Ativo);
        }

        public TipoCavalo GetPorDescricao(string nome, int idEmpresa)
        {
            return _tipoCavaloDapper
                .GetPorDescricao(nome, idEmpresa);
        }

        public int? GetCapacidadePorPlaca(string placa)
        {
            var placaUnformat = placa.RemoveSpecialCharacters();

            var capacidade = _veiculoRepository
                .Find(x => x.Placa == placaUnformat).Select(x => x.TipoCavalo.Capacidade)
                .FirstOrDefault();
            if (capacidade.HasValue)
                capacidade = capacidade.Value * 1000;

            return capacidade;
        }
        
        public object ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var tiposCavalo = _tipoCavaloRepository
                .GetAll()
                .Include(x => x.Empresa);

            if (idEmpresa.HasValue)
                tiposCavalo = tiposCavalo.Where(x => x.IdEmpresa == idEmpresa.Value || x.IdEmpresa == null);
            
            tiposCavalo = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? tiposCavalo.OrderByDescending(x => x.IdTipoCavalo)
                : tiposCavalo.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            tiposCavalo = tiposCavalo.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = tiposCavalo.Count(),
                items = tiposCavalo.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdTipoCavalo,
                        x.Nome,
                        x.Categoria,
                        x.Ativo,
                        Empresa = x.Empresa?.RazaoSocial
                    })
            };
        }
        
        public ValidationResult AlterarStatus(int tipoCavaloId)
        {
            try
            {
                var tipoCavaloRepository = _tipoCavaloRepository;
                var tipoCavalo = tipoCavaloRepository.Get(tipoCavaloId);
                
                if (tipoCavalo == null)
                    return new ValidationResult().Add($"Tipo de cavalo {tipoCavaloId} não encontrado na base de dados.");
            
                if(_userIdentity.Perfil != (int)EPerfil.Administrador && tipoCavalo.IdEmpresa != _userIdentity.IdEmpresa)
                    return new ValidationResult().Add($"Usuário não autenticado.");
                
                tipoCavalo.Ativo = !tipoCavalo.Ativo;
                tipoCavaloRepository.Update(tipoCavalo);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CampanhaMap : EntityTypeConfiguration<Campanha>
    {
        public CampanhaMap()
        {
            ToTable("CAMPANHA");

            HasKey(t => t.Id);

            Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.Descricao).IsOptional().HasMaxLength(300);
            Property(t => t.DataCadastro).HasColumnType("datetime2");
            Property(t => t.DataInicio).HasColumnType("datetime2");
            Property(t => t.DataFim).IsOptional().HasColumnType("datetime2");
            Property(t => t.DataDesativacao).IsOptional().HasColumnType("datetime2");
            Property(t => t.DataAtivacao).IsOptional().HasColumnType("datetime2");

            HasMany(c => c.<PERSON>)
                .WithRequired(c => c.<PERSON>)
                .HasForeignKey(c => c.IdCampanha);

            HasOptional(c => c.UsuarioCadastro)
                .WithMany()
                .HasForeignKey(c => c.IdUsuarioCadastro);

            HasOptional(c => c.UsuarioDesativacao)
                .WithMany()
                .HasForeignKey(c => c.IdUsuarioDesativacao);

            HasOptional(c => c.UsuarioAtivacao)
                .WithMany()
                .HasForeignKey(c => c.IdUsuarioAtivacao);
        }
    }
}
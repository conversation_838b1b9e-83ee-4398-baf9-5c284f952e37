using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class GrupoUsuarioMap : EntityTypeConfiguration<GrupoUsuario>
    {
        public GrupoUsuarioMap()
        {
            ToTable("GRUPO_USUARIO");

            HasKey(t => t.IdGrupoUsuario);

            Property(t => t.IdGrupoUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.IdEmpresa)
                .IsOptional();

            HasOptional(t => t.Empresa)
                .WithMany(t => t.GruposUsuario)
                .HasForeignKey(d => d.IdEmpresa);
            
            HasOptional(t => t.EstabelecimentoBase)
                .WithMany()
                .HasForeign<PERSON>ey(d => d.IdEstabelecimentoBase);
        }
    }
}
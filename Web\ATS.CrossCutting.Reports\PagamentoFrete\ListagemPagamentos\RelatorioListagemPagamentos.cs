﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.PagamentoFrete.ListagemPagamentos
{
    public class RelatorioListagemPagamentos
    {
        public byte[] GetReport(List<RelatorioListagemPagamentosDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametrizes = new Tuple<string, string, bool>[1];
            parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var bytes = new Base.Reports().GetReport(listaDados, parametrizes, true, "DtsListagemPagamentos",
                "ATS.CrossCutting.Reports.PagamentoFrete.ListagemPagamentos.RelatorioListagemPagamentos.rdlc",
                tipoArquivo);

            return bytes;
        }
    }
}

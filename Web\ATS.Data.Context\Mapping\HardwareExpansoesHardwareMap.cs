﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class HardwareExpansoesHardwareMap : EntityTypeConfiguration<HardwareExpansoesHardware>
    {
        public HardwareExpansoesHardwareMap()
        {
            ToTable("HARDWARE_EXPANSOES_HARDWARE");

            HasKey(t => new { t.IdExpansao, t.IdHardware });

            Property(t => t.IdExpansao)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdHardware)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            HasRequired(a => a.Expansao)
                .WithMany(b => b.HardwareExpansoesHardware)
                .HasForeignKey(c => c.IdExpansao);

            HasRequired(a => a.Hardware)
                .WithMany(b => b.ExpansoesHardware)
                .HasForeignKey(c => c.IdHardware);
        }
    }
}
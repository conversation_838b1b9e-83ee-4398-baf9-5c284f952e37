﻿using ATS.Domain.Entities.Common;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping.Common
{
    public class VeiculoBaseMap<TEntity> : EntityTypeConfiguration<TEntity> where TEntity : VeiculoBase
    {
        public VeiculoBaseMap()
        {
            Property(t => t.Placa)
                .IsRequired()
                .HasMaxLength(10);

            Property(t => t.<PERSON>ssi)
                .IsRequired()
                .HasMaxLength(22);

            Property(t => t.Marca)
               .IsRequired()
               .HasMaxLength(50);

            Property(t => t.Modelo)
               .IsRequired()
               .HasMaxLength(50);

            Property(t => t.RENAVAM)
                .IsOptional()
                .HasMaxLength(11);

            Property(t => t.TecnologiaRastreamento)
                .IsOptional()
                .HasMaxLength(100);
        }
    }
}
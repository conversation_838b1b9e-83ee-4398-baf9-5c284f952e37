﻿
using System;

namespace ATS.CrossCutting.Reports.Protocolo
{
    public class ProtocoloHeaderRelatorioModel
    {
        public string DataGeracao { get; set; }
        public string Usuario { get; set; }
    }

    public class ProtocoloRelatorioModel
    {
        public int IdProtocolo { get; set; }
        public string Estabelecimento { get; set; }
        public string Associacao { get; set; }
        public string ValorProtocolo { get; set; }
        public string DataGeracao { get; set; }
        public string DataTransito { get; set; }
        public string DataRecebimento { get; set; }
        public string DataAprovacao { get; set; }
        public string DataPrevisaoPagamento { get; set; }
        public string DataPagamento { get; set; }
        public string DataRejeicao { get; set; }
        public string Status { get; set; }
    }
}

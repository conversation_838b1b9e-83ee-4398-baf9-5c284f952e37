﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO.PrestacaoContas;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IPrestacaoContasApp : IAppBase<PrestacaoContas>
    {
        ValidationResult Novo(PrestacaoContasNovoRequest request);
        ValidationResult AlterarStatus(int prestacaoContasId, EStatusPrestacaoContas status, string observacao = null);
        PrestacaoContasGridResponse ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, EStatusPrestacaoContas[] status = null);
        BusinessResult<bool> HasAberta();
        BusinessResult<string> SolicitarEncerramento();
    }
}
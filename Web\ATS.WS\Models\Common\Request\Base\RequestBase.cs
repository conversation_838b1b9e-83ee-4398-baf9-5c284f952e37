﻿using System.Web.Configuration;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Models;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using Autofac;
using Newtonsoft.Json;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Common.Request.Base
{
    public class RequestBase
    {
        /// <summary>
        /// Esque token pode ser tanto para autenticação de consultas internas (Mobile) tanto quanto consultas
        /// Externas, o que fará uso do CNPJ da aplicação que está realizando a requisição
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Token { get; set; }

        /// <summary>
        /// Autenticação de uso do webservice
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJAplicacao
        {
            get { return _cnpjAplicacao ?? CNPJEmpresa; }
            set {_cnpjAplicacao = value.OnlyNumbers();}
        }
        private string _cnpjAplicacao { get; set; }

        /// <summary>
        /// CNPJ da empresa que está realizando a consulta
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CNPJEmpresa
        {
            get { return _cnpjEmpresa; }
            set {_cnpjEmpresa = value.OnlyNumbers();}
        }
        private string _cnpjEmpresa { get; set; }
        
        /// <summary>
        /// CPF ou CNPJ do usuário está realizando a consulta
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DocumentoUsuarioAudit
        {
            get { return !string.IsNullOrEmpty(_documentoUsuarioAudit) ? _documentoUsuarioAudit : CartoesService.AuditDocIntegracao; }
            set {_documentoUsuarioAudit = !string.IsNullOrEmpty(value) ? value.OnlyNumbers() : null;}
        }
        private string _documentoUsuarioAudit { get; set; }
        
        /// <summary>
        /// Nome do usuário ou nome da empresa que está realizando a consulta
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string NomeUsuarioAudit 
        {
            get { return _nomeUsuarioAudit; }
            set {_nomeUsuarioAudit = StringUtil.RemoveAccents(value).RemoveSpecialCaracter(true);}
        }
        private string _nomeUsuarioAudit { get; set; }
        
        public ValidationResult ValidaRequestBase(bool validaDadosAudit = true)
        {
            using (var scope = IoC.Container.BeginLifetimeScope())
            {
                var autenticacaoAplicacaoApp = scope.Resolve<IAutenticacaoAplicacaoApp>();
                ValidationResult validacao = new ValidationResult();

                if (!string.IsNullOrEmpty(Token) && !ValidarToken(Token) && !autenticacaoAplicacaoApp.AcessoConcedido(CNPJAplicacao, Token))
                    validacao.Add("Token inválido");

                if (!string.IsNullOrEmpty(CNPJEmpresa) && CNPJEmpresa.Length != 14)
                    validacao.Add("O campo CNPJEmpresa deve conter 14 dígitos");

                if (!string.IsNullOrEmpty(CNPJAplicacao) && CNPJAplicacao.Length != 14)
                    validacao.Add("O campo CNPJAplicacao deve conter 14 dígitos");

                if (validaDadosAudit || (!string.IsNullOrEmpty(DocumentoUsuarioAudit) && !string.IsNullOrEmpty(NomeUsuarioAudit)))
                {
                    if (string.IsNullOrEmpty(DocumentoUsuarioAudit))
                        validacao.Add("É obrigatório o envio do campo DocumentoUsuarioAudit");
                    else if (DocumentoUsuarioAudit.Length != 11 && DocumentoUsuarioAudit.Length != 14)
                        validacao.Add("O campo DocumentoUsuarioAudit deve conter 11 ou 14 dígitos");

                    if (string.IsNullOrEmpty(NomeUsuarioAudit))
                        validacao.Add("É obrigatório o envio do campo NomeUsuarioAudit");
                }

                return validacao;
            }
        }
        
        private bool ValidarToken(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            var userIdentity = DependencyResolver.Current.GetService<IUserIdentity>();
            userIdentity.CpfCnpj = token.ToLower();
            userIdentity.Origem = EUserOrigin.AtsWs;

            if (string.IsNullOrWhiteSpace(token))
                return false;
            
            string tokenFromWebConfig = WebConfigurationManager.AppSettings["Token"];
            if (token.ToLower() == tokenFromWebConfig.ToLower())
                return true;

            return false;
        }


    }
}
using System;
using Newtonsoft.Json;

namespace ATS.Domain.DTO.Banner
{
    public class BannerConsultarResponse
    {
        public int Id { get; set; }
        public int? IdUsuarioCadastro { get; set; }
        public string NomeUsuarioCadastro { get; set; }
        public int? IdUsuarioAtivacao { get; set; }
        public string NomeUsuarioAtivacao { get; set; }
        public int? IdUsuarioDesativacao { get; set; }
        public string NomeUsuarioDesativacao { get; set; }
        [JsonIgnore]
        public string IdImagemMongo { get; set; }
        public string ImagemB64 { get; set; }
        //public string ImagemNome { get; set; }
        [JsonIgnore]
        public DateTime DataCadastroValue { get; set; }
        [JsonIgnore]
        public DateTime? DataDesativacaoValue { get; set; }
        [JsonIgnore]
        public DateTime? DataAtivacaoValue { get; set; }
        private string _dataCadastro;
        public string DataCadastro
        {
            get => DataCadastroValue.ToString("dd/MM/yyyy HH:mm");
            set => _dataCadastro = value;
        }
        private string _dataDesativacao;
        public string DataDesativacao
        {
            get => DataDesativacaoValue is null ? string.Empty : DataDesativacaoValue.Value.ToString("dd/MM/yyyy HH:mm");
            set => _dataDesativacao = value;
        }

        private string _dataAtivacao;
        public string DataAtivacao
        {
            get => DataAtivacaoValue is null ? string.Empty : DataAtivacaoValue.Value.ToString("dd/MM/yyyy HH:mm");
            set => _dataAtivacao = value;
        }
        public string Descricao { get; set; }
        public string Titulo { get; set; }
        public string Link { get; set; }
        public bool Ativo { get; set; }
    }
}
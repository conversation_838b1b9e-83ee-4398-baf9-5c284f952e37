﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class LayoutCartaoApp : AppBase, ILayoutCartaoApp
    {
        private readonly ILayoutCartaoService _layoutCartaoService;

        public LayoutCartaoApp(ILayoutCartaoService layoutCartaoService)
        {
            _layoutCartaoService = layoutCartaoService;
        }

        public IList<LayoutCartao> GetAll()
        {
            return _layoutCartaoService.GetAll();
        }
    }
}

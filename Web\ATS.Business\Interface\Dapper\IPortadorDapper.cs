using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Dapper
{
    public interface IPortadorDapper : IRepositoryDapper<PortadorDto>
    {
        PortadorDto ConsultarPortadoresPaginado(int? idEmpresa, string documento, string nome, int itensPorPagina, int pagina);
        
        MotoristaCargaAvulsaModel ConsultarPortadorCadastrado(string documento);
    }
}
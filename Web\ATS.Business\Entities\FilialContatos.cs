﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class FilialContatos
    {
        public int IdFilialContato { get; set; }

        public int IdFilial { get; set; }

        public string Nome { get; set; }

        public string Telefone { get; set; }

        public string Email { get; set; }

        #region Relacionamentos

        public virtual Filial Filial { get; set; }

        #endregion
    }
}

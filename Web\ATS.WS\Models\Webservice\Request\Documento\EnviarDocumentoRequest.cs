﻿using ATS.MongoDB.Enum;
using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Webservice.Request.Documento
{
    public class EnviarDocumentoRequest : RequestBase
    {
        public string TokenEvento { get; set; }

        public int? IdViagemDocumento { get; set; }
        public int? IdDocumento { get; set; }

        public EProcessoDocumento Processo { get; set; }

        public EMediaType Type { get; set; }

        public string DocumentoBase64 { get; set; }

        public string NomeArquivo { get; set; }
    }
}
using System;
using System.Web.Http;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services;

namespace ATS.WS.Controllers
{
    public class CheckinResumoController : BaseController
    {
        private readonly SrvCheckinResumo _srvCheckinResumo;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public CheckinResumoController(BaseControllerArgs baseArgs, SrvCheckinResumo srvCheckinResumo, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _srvCheckinResumo = srvCheckinResumo;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public string ConsultarCheckinResumos(string token, string cnpjAplicacao, int? itensPorPagina, int? pagina, DateTime? dataInicio, DateTime? dataFim)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(
                    _srvCheckinResumo.ConsultarCheckinResumosPaginado(cnpjAplicacao, itensPorPagina, pagina, dataInicio, dataFim));
            }
            catch (Exception e)
            {
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
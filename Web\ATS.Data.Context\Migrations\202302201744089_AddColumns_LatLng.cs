﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddColumns_LatLng : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.PONTOS_ROTA_MODELO", "latitude", c => c.Decimal(precision: 10, scale: 7));
            AddColumn("dbo.PONTOS_ROTA_MODELO", "longitude", c => c.Decimal(precision: 10, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "latitude", c => c.Decimal(precision: 10, scale: 7));
            AddColumn("dbo.ROTA_MODELO", "longitude", c => c.Decimal(precision: 10, scale: 7));
        }
        
        public override void Down()
        {
            DropColumn("dbo.ROTA_MODELO", "longitude");
            DropColumn("dbo.ROTA_MODELO", "latitude");
            DropColumn("dbo.PONTOS_ROTA_MODELO", "longitude");
            DropColumn("dbo.PONTOS_ROTA_MODELO", "latitude");
        }
    }
}

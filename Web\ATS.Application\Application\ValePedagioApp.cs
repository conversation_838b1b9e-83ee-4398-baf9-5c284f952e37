﻿using System;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class ValePedagioApp : AppBase, IValePedagioApp
    {
        private readonly PedagioAppFactoryDependencies _pedagioAppFactoryDependencies;
        private readonly IEmpresaService _empresaService;

        public ValePedagioApp(PedagioAppFactoryDependencies pedagioAppFactoryDependencies, IEmpresaService empresaService)
        {
            _pedagioAppFactoryDependencies = pedagioAppFactoryDependencies;
            _empresaService = empresaService;
        }

        public ObterExtratoSemPararResponseDTO GetExtratoSemParar(DateTime datainicio, DateTime datafim, string cnpjAplicacao)
        {
            var idEmpresa = _empresaService.GetIdByCnpj(cnpjAplicacao);

            if (!idEmpresa.HasValue)
                throw new Exception("Nenhuma empresa encontrada com o Cnpj informado");

            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, idEmpresa.Value, CartoesService.AuditDocIntegracao, null);

            return pedagioApp.GetExtratoSemParar(datainicio, datafim);
        }

        public ConsultarStatusVeiculoSemPararDTO GetStatusVeiculoSemParar(string placa, string cnpjAplicacao)
        {
            var idEmpresa = _empresaService.GetIdByCnpj(cnpjAplicacao);

            if (!idEmpresa.HasValue)
                throw new Exception("Nenhuma empresa encontrada com o Cnpj informado");

            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, idEmpresa.Value, CartoesService.AuditDocIntegracao, null);

            return pedagioApp.ConsultarStatusVeiculoSemParar(placa);
        }

        public ConsultarStatusVeiculoTaggyEdenredDTO GetStatusVeiculoTaggyEdenred(string placa, string cnpjAplicacao)
        {
            var idEmpresa = _empresaService.GetIdByCnpj(cnpjAplicacao);

            if (!idEmpresa.HasValue)
                throw new Exception("Nenhuma empresa encontrada com o Cnpj informado");

            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, idEmpresa.Value, CartoesService.AuditDocIntegracao, null);

            return pedagioApp.ConsultarStatusVeiculoTaggyEdenred(placa);
        }
        
        public ConsultarPolylineDTO GetPolyline(int codPolyline, string cnpjAplicacao)
        {
            var idEmpresa = _empresaService.GetIdByCnpj(cnpjAplicacao);

            if (!idEmpresa.HasValue)
            {
                return new ConsultarPolylineDTO
                {
                    Sucesso = false,
                    Mensagem = "Nenhuma empresa encontrada com o Cnpj informado"
                };
            }

            var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, idEmpresa.Value, CartoesService.AuditDocIntegracao, null);

            return pedagioApp.GetPolyline(codPolyline);
        }
    }
}
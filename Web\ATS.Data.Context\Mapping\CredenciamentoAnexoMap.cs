﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CredenciamentoAnexoMap : EntityTypeConfiguration<CredenciamentoAnexo>
    {
        public CredenciamentoAnexoMap()
        {
            ToTable("CREDENCIAMENTO_ANEXO");

            HasKey(x => x.IdCredenciamentoAnexo);

            HasOptional(x => x.Credenciamento)
                .WithMany(x => x.CredenciamentoAnexo)
                .HasForeignKey(x => x.IdCredenciamento);

            HasOptional(x => x.Documento)
                .WithMany(x => x.CredenciamentoAnexo)
                .HasForeignKey(x => x.IdDocumento);
        }
    }
}

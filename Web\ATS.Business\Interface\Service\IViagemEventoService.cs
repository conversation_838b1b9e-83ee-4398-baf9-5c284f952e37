﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IViagemEventoService : IService<ViagemEvento>
    {
        ViagemEvento Get(int IdViagemEveto);
        IQueryable<ViagemEvento> Find(Expression<Func<ViagemEvento, bool>> predicate, bool @readonly = false);
        
        
        ViagemEvento CreateAbonoEvent(decimal valor, ViagemEvento viagemEVentoSaldo);
        List<ViagemEvento> GetEventosViagem(int idViagem);
        List<ViagemEvento> GetEventosViagem(List<int> idsViagemEvento);
        List<DeclaracaoCiot> GetCiotViagem(int idViagem);
        IQueryable<ViagemEvento> GetEventosViagemQuery(List<int> idsViagemEvento);
        object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        IEnumerable<ViagemEvento> GetEventosViagemSemChave(int idEmpresa);
        IQueryable<ViagemEvento> GetEventoPorTokenPagSemChave(string token);
        IQueryable<ViagemEvento> GetQueryable(int idViagemEvento);
        IQueryable<ViagemEvento> GetQuery();
        ViagemEvento GetByToken(string token);
        ValidationResult Update(ViagemEvento viagemEvento);
        ViagemEvento AfterUpdate(ViagemEvento viagemEvento, ViagemEvento oldEvento);
        bool GetTipoPagamento(int idViagemEvento);
        bool IsReicidente(int idViagemEvento);
        ViagemEvento GetByTokenAndEmpresa(int empresaId, string token);
    }
}
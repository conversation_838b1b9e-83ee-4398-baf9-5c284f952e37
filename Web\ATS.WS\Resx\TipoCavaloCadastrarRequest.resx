<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">

        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>

    <data name="NomeVazio" xml:space="preserve">
        <value>O campo Nome não pode estar vazio</value>
    </data>
    
    <data name="NomeTamanhoMax" xml:space="preserve">
        <value>O campo Nome deve ter entre 1 e 150 caracteres</value>
    </data>

    <data name="NumeroEixos" xml:space="preserve">
        <value>O campo Número de eixos caso preenchido deve ser maior que 1</value>
    </data>

    <data name="CategoriaInvalida" xml:space="preserve">
        <value>O campo Categoria está inválido</value>
    </data>

    <data name="Capacidade" xml:space="preserve">
        <value>O campo Capacidade caso preenchido deve ser maior que 1</value>
    </data>
</root>
﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class CriacaoTabelaRotaModelo : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.PONTOS_ROTA_MODELO",
                c => new
                    {
                        idrotamodelo = c.Int(nullable: false),
                        idponto = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        ibge = c.Decimal(nullable: false, precision: 18, scale: 2),
                    })
                .PrimaryKey(t => new { t.idrotamodelo, t.idponto })
                .ForeignKey("dbo.ROTA_MODELO", t => t.idrotamodelo)
                .Index(t => t.idrotamodelo, name: "IX_IdRotaModelo");
            
            CreateTable(
                "dbo.ROTA_MODELO",
                c => new
                    {
                        idrotamodelo = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(),
                        datacadastro = c.DateTime(nullable: false),
                        origemdescricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        destinodescricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        origemibge = c.Decimal(nullable: false, precision: 18, scale: 2),
                        destinoibge = c.Decimal(nullable: false, precision: 18, scale: 2),
                        distanciatotal = c.Single(nullable: false),
                        custorota = c.Decimal(nullable: false, precision: 18, scale: 2),
                        custorotatag = c.Decimal(nullable: false, precision: 18, scale: 2),
                        nomerota = c.String(nullable: false, maxLength: 100, unicode: false),
                        qtdeeixos = c.Int(nullable: false),
                        tipoveiculo = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idrotamodelo)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.PRACA_ROTA_MODELO",
                c => new
                    {
                        idrotamodelo = c.Int(nullable: false),
                        idpraca = c.Int(nullable: false, identity: true),
                        descricao = c.String(maxLength: 100, unicode: false),
                        valor = c.Decimal(nullable: false, precision: 18, scale: 2),
                        valortag = c.Decimal(nullable: false, precision: 18, scale: 2),
                    })
                .PrimaryKey(t => new { t.idrotamodelo, t.idpraca })
                .ForeignKey("dbo.ROTA_MODELO", t => t.idrotamodelo)
                .Index(t => t.idrotamodelo, name: "IX_IdRotaModelo");
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.PRACA_ROTA_MODELO", "idrotamodelo", "dbo.ROTA_MODELO");
            DropForeignKey("dbo.PONTOS_ROTA_MODELO", "idrotamodelo", "dbo.ROTA_MODELO");
            DropForeignKey("dbo.ROTA_MODELO", "idempresa", "dbo.EMPRESA");
            DropIndex("dbo.PRACA_ROTA_MODELO", "IX_IdRotaModelo");
            DropIndex("dbo.ROTA_MODELO", "IX_IdEmpresa");
            DropIndex("dbo.PONTOS_ROTA_MODELO", "IX_IdRotaModelo");
            DropTable("dbo.PRACA_ROTA_MODELO");
            DropTable("dbo.ROTA_MODELO");
            DropTable("dbo.PONTOS_ROTA_MODELO");
        }
    }
}

﻿using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoBaseContaBancariaMap : EntityTypeConfiguration<EstabelecimentoBaseContaBancaria>
    {
        public EstabelecimentoBaseContaBancariaMap()
        {
            ToTable("ESTABELECIMENTO_BASE_CONTA_BANCARIA");

            HasKey(t => t.IdEstabelecimentoBaseContaBancaria);

            HasRequired(o => o.EstabelecimentoBase)
                .WithMany(o => o.EstabelecimentoBaseContasBancarias)
                .HasForeignKey(o => o.IdEstabelecimentoBase);

            Property(o => o.NomeConta)
               .IsRequired()
                .HasMaxLength(100);

            Property(o => o.CodigoBanco)
                .IsRequired()
               .HasMaxLength(10);
            Property(o => o.NomeBanco)
                .IsRequired()
                .HasMaxLength(100);

            Property(o => o.Agencia)
                .IsRequired()
                .HasMaxLength(10);

            Property(o => o.Conta)
                .IsRequired()
                .HasMaxLength(30);

            Property(o => o.DigitoConta)
                .IsOptional()
                .HasMaxLength(10);

            Property(o => o.TipoConta)
                .IsRequired();

            Property(o => o.CnpjTitular)
                .IsRequired()
                .HasMaxLength(14);

            Property(o => o.NomeTitular)
                .IsOptional()
                .HasMaxLength(100);
        }
    }
}

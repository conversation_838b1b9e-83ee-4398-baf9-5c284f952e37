﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class EspecieAtsController : DefaultController
    {
        private readonly IClienteProdutoEspecieApp _clienteProdutoEspecieApp;
        private readonly IEspecieApp _especieApp;

        public EspecieAtsController(IClienteProdutoEspecieApp clienteProdutoEspecieApp, IEspecieApp especieApp)
        {
            _clienteProdutoEspecieApp = clienteProdutoEspecieApp;
            _especieApp = especieApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(Especie especie)
        {
            try
            {
                var mensagem = especie.IdEspecie > 0 ? "atualizada" : "cadastrada";
                var response = especie.IdEspecie > 0 ? _especieApp.Update(especie) : _especieApp.Add(especie);

                return response.IsValid
                    ? ResponderSucesso($"Espécie {mensagem} com sucesso!")
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var especies = _especieApp.ConsultaGrid(take, page, order, filters);
                return ResponderSucesso(especies);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int idEspecie)
        {
            try
            {
                var response = _especieApp.AlterarStatus(idEspecie);

                return !response.IsValid
                    ? ResponderErro(response.Errors.FirstOrDefault()?.Message)
                    : ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idEspecie)
        {
            try
            {
                return ResponderSucesso(_especieApp.Get(idEspecie));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetTodasEspeciesPorClienteForUiGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var especies = _clienteProdutoEspecieApp
                    .GetTodasEspeciesPorClienteForUiGrid(take, page, order, filters);

                return ResponderSucesso(especies);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
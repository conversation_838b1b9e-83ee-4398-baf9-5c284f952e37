﻿using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using TagExtrattaClient;
using GridValePedagioHubRequest = ATS.Domain.DTO.TagExtratta.GridValePedagioHubRequest;
using VeiculoTagResponse = ATS.Domain.DTO.TagExtratta.VeiculoTagResponse;

namespace ATS.Application.Interface
{
    public interface ITagExtrattaApp : IAppBase
    {
        byte[] GerarRelatorioConsultaSituacaoTags(int take, int page, OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa, int? idUsuario, DateTime? dataInicio, DateTime? dataFim);
        BusinessResult<GridRemessaEnvioResponse> GridRemessaEnvio(GridRemessaEnvioRequest request);
        BusinessResult<ConsultarRemessaResponse> GetRemessa(int id);
        BusinessResult<GridRemessaRecebimentoResponse> GridRemessaRecebimento(GridRemessaRecebimentoRequest request);
        BusinessResult<List<TagReduzidaResponse>> ConsultarLoteEstoque(long serialInicial, long serialFinal);
        BusinessResult<string> EnviarRemessa(TagRemessaEnvioRequest request);
        BusinessResult<TagReduzidaResponse> GetTagSerialEstoque(long serial);
        BusinessResult<string> ReceberRemessa(int idRemessa);
        BusinessResult<TagReduzidaGridResponse> GridTagsAtivas(int take, int page, OrderFilters order, List<QueryFilters> filters, List<long> tagsSelecionadas);
        BusinessResult<GridTagResponse> GridTags(int? take, int? page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, DateTime? dataInicio, DateTime? dataFim);
        BusinessResult<VeiculoTagResponse> GetVeiculo(string placa);
        BusinessResult<TagReduzidaGridResponse> GridTagsReduzida(int take, int page, OrderFilters order, List<QueryFilters> filters);
        BusinessResult<ModeloMoveMaisGridResponse> GridModeloMoveMais(int take, int page, OrderFilters order, List<QueryFilters> filters);
        BusinessResult<string> Vincular(SalvarTagRequest request);
        BusinessResult<string> Bloquear(long serial);
        BusinessResult<string> Desbloquear(long serial);
        BusinessResult<string> Desvincular(long serial);
        BusinessResult<BloqueiosTagUsuarioResponse> GetBloqueios(int usuarioId);
        BusinessResult<string> CadastrarBloqueios(BloqueiosTagUsuarioRequest request);
        BusinessResult<ModeloMoveMaisGridResponse> GetModelosMovemais(int? take, int? page);
        BusinessResult<GridValePedagioHubResponse> GridValePedagioHub(GridValePedagioHubRequest request);
        byte[] GerarRelatorioPassagensPedagioCompraHub(ReportPassagensPedagioCompraHub data);
        byte[] GerarRelatorioGridValePedagioHub(OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa,DateTime dataInicio,DateTime dataFim);
        BusinessResult<string> CadastrarModelosMoveMais();
        BusinessResult<string> CadastrarEstoqueTags();
        BusinessResult<string> GenerateTokenWebhook(string user, string password);
        string GetWebhookToken();
        BusinessResult<Guid> NotificarPassagemPraca(PassagemPracaPedagioModelRequest requestModel);
        BusinessResult<GridPagamentosTagResponse> GridPagamentos(GridPagamentosTagRequest request);
        byte[] GerarRelatorioGridPagamentos(OrderFilters order, List<QueryFilters> filters, string extensao, int? idEmpresa, DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor);
        BusinessResult<ConsultarPagamentoResponse> GetPagamento(long id);
        BusinessResult<string> PagamentoManualEventoTag(PagamentoTagRequest request);
        BusinessResult<string> EstornoManualEventoTag(EstornoTagRequest request);
        BusinessResult<GridPassagensWebhookResponse> GridPassagensWebhook(int? take, int? page, OrderFilters order, List<QueryFilters> filters, DateTime? dataInicio, DateTime? dataFim,int? empresaId);
        byte[] GerarRelatorioGridPassagemWebhook(OrderFilters order, List<QueryFilters> filters, string extensao,DateTime dataInicio,DateTime dataFim,int? idEmpresa);
        BusinessResult<string> DesvincularEmpresa(long serial);
        BusinessResult<GridFaturamentoTagResponse> GridFaturamento(int? take, int? page, OrderFilters order, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor);
        BusinessResult<FaturamentoTotalizadorResponse> GetTotalizadorFaturamento(FaturamentoTotalizadorRequest request);
        byte[] GerarRelatorioGridFaturamento(OrderFilters order, List<QueryFilters> filters, string extensao, DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor);
        byte[] GerarFatura(FaturaRequest request);
        BusinessResult<FaturaTagGetResponse> ConsultaFatura(DateTime dataInicio, DateTime dataFim, int empresaId, FornecedorTagEnum fornecedorTag);
        BusinessResult<string> PagamentoEventoTag(PagamentoTagRequest request);
        BusinessResult<PlacaFornecedorResponse> ConsultarPlacasFornecedor(string placa, FornecedorEnum fornecedor);
        BusinessResult<PracasPedagioResponseDTO> ConsultarPassagensPedagioCompraHub(int compraId, FornecedorEnum fornecedor);
        BusinessResult<SaldoValePedagioVeiculoTagResponse> ConsultarSaldoValePedagioVeiculo(string placa);
        BusinessResult<ConsultarSituacaoTagResponseModel> ConsultarSituacaoTagPlaca(string placa, Fornecedor2 fornecedor);
        BusinessResult<PracasPedagioVeiculoResponseDTO> ConsultarPassagensVeiculo(DateTime? dataInicio, DateTime? dataFim, string placa);
        BusinessResult ContestarPassagem(ContestarPassagemRequest request);
    }
}
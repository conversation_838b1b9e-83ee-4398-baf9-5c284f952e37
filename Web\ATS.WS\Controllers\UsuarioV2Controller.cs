﻿using ATS.WS.Controllers.Base;
using ATS.WS.Models.Mobile.Common;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.Controllers
{
    public class UsuarioV2Controller : BaseController
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public UsuarioV2Controller(BaseControllerArgs baseArgs
            , IUsuarioApp usuarioApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _usuarioApp = usuarioApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }
        
        [HttpPost]
        [Expor(EApi.Mobile)]
        [EnableLogRequest]
        [AllowAnonymous]
        public JsonResult Cadastrar(UsuarioCadastrarV2Request @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) && !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return Responde(new Retorno<UsuarioCadastrarV2Response>(false, "Token inválido", null));
                var response = _usuarioApp.CadastrarV2(@params);
                return Responde(new Retorno<UsuarioCadastrarV2Response>(true, response));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return Responde(new Retorno<UsuarioCadastrarV2Response>(false, e.Message, null));
            }
        }
    }
}
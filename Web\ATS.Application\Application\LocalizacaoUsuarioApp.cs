﻿using System.Threading.Tasks;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.LocalizacaoUsuarios;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class LocalizacaoUsuarioApp : BaseApp<ILocalizacaoUsuarioService>, ILocalizacaoUsuarioApp
    {
        public LocalizacaoUsuarioApp(ILocalizacaoUsuarioService service) : base(service)
        {
        }

        public BusinessResult Add(LocalizacaoUsuarioAddModel model)
        {
            return Service.Add(model);
        }
    }
}
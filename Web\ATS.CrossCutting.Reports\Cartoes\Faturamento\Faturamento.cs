﻿using System;
using System.Collections.Generic;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico
{
    public class RelatorioConciliacaoAnalitico
    {
        public byte[] GetReport(string tipo, RelatorioConciliacaoAnaliticoDataType dadosRelatorio, string logo, string saldoInicial, string saldoFinal, string totalMovimentado, string totalNaoConciliado)
        {
            var localReport = new LocalReport();
            try
            {
                var parametrizes = new Tuple<string, string, bool>[6];
                parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);
                parametrizes[1] = new Tuple<string, string, bool>("SaldoInicial", saldoInicial, true);
                parametrizes[2] = new Tuple<string, string, bool>("SaldoFinal", saldoFinal, true);
                parametrizes[3] = new Tuple<string, string, bool>("TotalMovimentado", totalMovimentado, true);
                parametrizes[4] = new Tuple<string, string, bool>("TotalNaoConciliado", totalNaoConciliado, true);
                parametrizes[5] = new Tuple<string, string, bool>("MostrarTotalNaoConciliado", totalNaoConciliado == "R$0,00" || totalNaoConciliado == "R$ 0,00" ? "Não" : "Sim", true);
                
                var dados = new List<RelatorioConciliacaoAnaliticoDataTypeNew>();
                var cargasEstornos = new List<RelatorioConciliacaoAnaliticoCargasEstornosDataType>();

                foreach (var item in dadosRelatorio.items)
                {
                    dados.Add(new RelatorioConciliacaoAnaliticoDataTypeNew
                    {
                        Cartao = item.NumeroCartao,
                        Ciot = item.Ciot,
                        Data = item.Data,
                        Documento = item.NumeroRecibo,
                        Filial = item.Filial,
                        Placa = item.Placa,
                        Situacao = item.StatusGeral.ToString(),
                        Transacao = item.TipoTransacaoDescricao,
                        Valor = item.Valor,
                        DocPortador = item.DocumentoPortador,
                        DocumentoUsuario = item.DocumentoUsuario,
                        NomePortador = item.NomePortador,
                        NomeUsuario = item.NomeUsuario,
                        TipoEvento = item.TipoEvento,
                        Informacoes = item.Informacoes
                    });
                }

                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "TARIFA ANTT",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaTarifa,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoTarifa
                });
                
                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "ADIANTAMENTOS",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaAdiantamento,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoAdiantamento
                });
                
                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "SALDOS",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaSaldo,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoSaldo
                });
                
                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "RPAs",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaRpa,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoRpa
                });
                
                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "ESTADIAS",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaEstadia,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoEstadia
                });
                
                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "PEDÁGIOS",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaPedagio,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoPedagio
                });
                
                var listaDataSources = new List<Tuple<object, string>>
                {
                    new Tuple<object, string>(dados, "DtsConciliAnalitico"),
                    new Tuple<object, string>(new List<RelatorioConciliacaoAnaliticoTotalizadorDataType>(), "DtsTotalizadores"),
                    new Tuple<object, string>(cargasEstornos, "DtsCargasEstornos"),
                };
                
                cargasEstornos.Add(new RelatorioConciliacaoAnaliticoCargasEstornosDataType
                {
                    Label = "CARGAS AVULSAS",
                    Carga = dadosRelatorio.Resumo.ValorTotalCargaAvulsa,
                    Estorno = dadosRelatorio.Resumo.ValorTotalEstornoAvulsa
                });

                byte[] bytes;

                if (tipo == "xlsx")
                {
                    bytes = new Base.Reports().GetReport(listaDataSources, parametrizes, true,
                        "ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico.RelatorioConciliacaoAnaliticoNewExcel.rdlc", tipo);
                }
                else
                {
                    bytes = new Base.Reports().GetReport(listaDataSources, parametrizes, true,
                        "ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico.RelatorioConciliacaoAnaliticoNewPdf.rdlc", tipo);   
                }

                return bytes;
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }
    }
}

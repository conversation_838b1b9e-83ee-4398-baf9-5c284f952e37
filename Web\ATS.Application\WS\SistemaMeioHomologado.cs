﻿using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Application.WS
{
    public class SistemaMeioHomologado
    {
        private readonly string LinkWebservice;

        public string EndPoint { get; set; }
        public HttpVerb Method { get; set; }
        public string ContentType { get; set; }
        public string PostData { get; set; }
        public Encoding Encoding { get; set; }

        #region Propriedades

        public static Logger Logger = LogManager.GetCurrentClassLogger();

        #endregion

        public SistemaMeioHomologado()
        {
            LinkWebservice = System.Configuration.ConfigurationManager.AppSettings["WS_MEIO_HOMOLOGADO"];
        }

        /// <summary>
        /// Pegar o token
        /// </summary>
        /// <returns>Token para envio do push</returns>
        public static string GetToken()
        {
            // Pegar da empresa

            return string.Empty;
        }

        public void CarregarCartao()
        {
            try
            {
                var wsReq = (HttpWebRequest) WebRequest.Create(LinkWebservice);
                wsReq.ContentType = "application/json";
                wsReq.Method = "POST";

               
            }
            catch (Exception ex)
            {

            }
        }

        private string MakeRequest(string parameters)
        {
            if (Method == HttpVerb.GET)
                EndPoint += "?";

            var request = (HttpWebRequest)WebRequest.Create(EndPoint + parameters);

            request.Method = Method.ToString();
            request.ContentLength = 0;
            request.ContentType = ContentType;

            if (!string.IsNullOrEmpty(PostData) && Method == HttpVerb.POST)
            {
                var bytes = Encoding.GetBytes(PostData);
                request.ContentLength = bytes.Length;

                using (var writeStream = request.GetRequestStream())
                {
                    writeStream.Write(bytes, 0, bytes.Length);
                }
            }

            using (var response = (HttpWebResponse)request.GetResponse())
            {
                var responseValue = string.Empty;

                if (response.StatusCode != HttpStatusCode.OK)
                {
                    var message = String.Format("Request failed. Received HTTP {0}", response.StatusCode);
                    throw new ApplicationException(message);
                }

                // grab the response
                using (var responseStream = response.GetResponseStream())
                {
                    if (responseStream != null)
                        using (var reader = new StreamReader(responseStream))
                        {
                            responseValue = reader.ReadToEnd();
                        }
                }

                return responseValue;
            }
        }
    }
}

using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class AdministradoraPlataformaMapping: EntityTypeConfiguration<AdministradoraPlataforma>
    {
        public AdministradoraPlataformaMapping()
        {
            ToTable("ADMINISTRADORA_PLATAFORMA");

            HasKey(x => x.IdAdministradoraPlataforma);
            
            Property(x => x.IdAdministradoraPlataforma)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None)
                .IsRequired();
            
            Property(x => x.Nome).HasMaxLength(100);
            Property(x => x.IdAdministradoraMeioHomologado);
        }
    }
}
﻿using ATS.Domain.Enum;
using ATS.MongoDB.Context.Entities;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using System;
using System.Web.Mvc;

namespace ATS.WS.ControllersATS
{
    public class MediaContextAtsController : DefaultController
    {
        public readonly SrvDataMediaServer AppLayer;

        public MediaContextAtsController(SrvDataMediaServer appLayer)
        {
            AppLayer = appLayer;
        }

        [HttpGet]
        //[IgnoreAuthSessionValidation]
        [Autorizar]
        [Expor(EApi.Portal)]
        public ActionResult ViewByToken(string token) 
        {
            if (token == null || token.Length <= 0)
                return null;

            Media retorno = AppLayer.GetMedia(token);

            if (retorno == null) return null;

            var data = retorno.Data;
            if (retorno.Data.Contains(","))
            {
                var splitted = retorno.Data.Split(',');
                data = splitted[1];
            }

            return File(Convert.FromBase64String(data), retorno.Type.ToString());
        }
    }
}
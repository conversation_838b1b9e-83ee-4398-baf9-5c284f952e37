﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class LayoutMap : EntityTypeConfiguration<Layout>
    {
        public LayoutMap()
        {
            ToTable("LAYOUT");
            HasKey(x => x.IdLayout);

            HasOptional(x => x.Empresa)
                .WithMany(x => x.LayoutSite)
                .HasForeignKey(x => x.IdEmpresa);

            Property(x => x.CSS)
                .HasMaxLength(2000);

            HasOptional(x => x.GrupoUsuarioMotorista)
                .WithMany(x => x.LayoutMotorista)
                .HasForeignKey(x => x.IdGrupoUsuarioMotorista);

            HasOptional(x => x.GrupoUsuarioProprietario)
                .WithMany(x => x.LayoutProprietario)
                .HasForeignKey(x => x.IdGrupoUsuarioProprietario);

        }

    }
}

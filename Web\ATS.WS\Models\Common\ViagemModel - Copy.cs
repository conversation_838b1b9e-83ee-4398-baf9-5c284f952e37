﻿using ATS.Domain.Enum;
using ATS.Domain.Enum;
using System;
using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class ConsultarGridParamsAtsNovo
    {
        public int IdViagem                         { get; set; } 
        public int IdEmpresa                        { get; set; }
        public string Placa                         { get; set; }
        public int IdClienteOrigem                  { get; set; }
        public int IdClienteDestino                 { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? DataColeta                 { get; set; }
        public DateTime DataPrevisaoEntrega         { get; set; }
        public string CPFMotorista                  { get; set; }
        public EStatusViagem StatusViagem           { get; set; }
        public EStatusIntegracao StatusIntegracao   { get; set; }
    }
}
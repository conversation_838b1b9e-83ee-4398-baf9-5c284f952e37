using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class FornecedorCnpjPedagioMap: EntityTypeConfiguration<FornecedorCnpjPedagio>
    {
        public FornecedorCnpjPedagioMap()
        {
            ToTable("FORNECEDOR_PEDAGIO_CNPJ");

            HasKey(t => t.Id);

            Property(c => c.Cnpj).IsRequired();
            Property(c => c.IdFornecedor).IsRequired();

        }
    }
}
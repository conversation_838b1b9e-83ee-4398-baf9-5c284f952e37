﻿using System;
using System.Collections.Generic;
using TagExtrattaClient;

namespace ATS.CrossCutting.Reports.Pedagio.FaturaTAG
{
    public class RelatorioFaturaTagDataType
    {
        public List<PassagensVPFornecedorTagDataTypeResponse> PassagensVPFornecedor { get; set; }
        public List<PassagensPedagioTagDataTypeResponse> PassagensPedagio { get; set; }
        public List<MensalidadeTagDataTypeResponse> MensalidadeTag { get; set; }
        public EmpresaFaturamentoTagDataTypeResponse Empresa { get; set; }
        public DadosGeraisTagDataTypeResponse DadosGerais { get; set; }
        public TributaveisTagDataTypeResponse Tributaveis { get; set; }
        public NaoTributaveisTagDataTypeResponse NaoTributaveis { get; set; }
        public TotalizadorTagDataTypeResponse Totalizador { get; set; }
    }

    public class PassagensVPFornecedorTagDataTypeResponse
    {
        public FornecedorTagEnum Fornecedor { get; set; }
        public List<PassagensValePedagioTagDataType> PassagensValePedagio { get; set; }
    }


    public class PassagensValePedagioTagDataType
    {
        public int? ViagemId { get; set; }
        public string DataCriacao { get; set; }
        public string Tipo { get; set; }
        public string Placa { get; set; }
        public long? Serial { get; set; }
        public string Valor { get; set; }
        public string Taxa { get; set; }
        public string StatusTaxa { get; set; } 
        public string StatusValor { get; set; } 
    }

    public class PassagensPedagioTagDataTypeResponse
    {
        public string DataPassagem { get; set; }
        public string DescricaoPassagem { get; set; }
        public long? Serial { get; set; }
        public string Valor { get; set; }
        public string Taxa { get; set; }
        public string StatusTaxa { get; set; } 
        public string StatusValor { get; set; } 
        public string Tipo { get; set; }
        public string Placa { get; set; }
    }

    public class MensalidadeTagDataTypeResponse
    {
        public string Placa { get; set; }
        public long Serial { get; set; }
        public string MesReferente { get; set; }
        public string Valor { get; set; }
    }

    public class EmpresaFaturamentoTagDataTypeResponse
    {
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string Email { get; set; }
        public string Telefone { get; set; }
        public string Endereco { get; set; }
    }

    public class DadosGeraisTagDataTypeResponse
    {
        public string Vigencia { get; set; }
        public string Emissao { get; set; }
        public string Vencimento { get; set; }
        public string NumeroFatura { get; set; }
    }

    public class TributaveisTagDataTypeResponse
    {
        public int? MensalidadeQtd { get; set; }
        public string MensalidadeValorTotal { get; set; }
        public string ValorTotal { get; set; }
    }

    public class NaoTributaveisTagDataTypeResponse
    {
        public int? PassagensValePedagioQtd { get; set; }
        public string PassagensValePedagioValorTotal { get; set; }
        public int? PassagensPedagioQtd { get; set; }
        public string PassagensPedagioValorTotal { get; set; }
        public int? EstornoValePedagioQtd { get; set; }
        public string EstornoValePedagioTotal { get; set; }
        public int? EstornoPedagioQtd { get; set; }
        public string EstornoPedagioTotal { get; set; }
        public string ValorTotal { get; set; }
    }

    public class TotalizadorTagDataTypeResponse
    {
        public string EstornoPago { get; set; }
        public string EstornoNaoPago { get; set; }
        public string TotalPago { get; set; }
        public string TotalNaoPago { get; set; }
        public string ValorFatura { get; set; }
    }
}
﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class EstabelecimentoAssociacaoService : ServiceBase, IEstabelecimentoAssociacaoService
    {
        private readonly IEstabelecimentoAssociacaoRepository _repository;

        public EstabelecimentoAssociacaoService(IEstabelecimentoAssociacaoRepository repository)
        {
            _repository = repository;
        }

        public EstabelecimentoAssociacao GetById(int id, int idEstabelecimento)
        {
            return _repository.FirstOrDefault(o => o.IdAssociacao == id && o.IdEstabelecimento == idEstabelecimento);
        }

        public void DeleteById(List<int> ids, int idEstabelecimento)
        {
            foreach (var id in ids)
            {
                var associacao = GetById(id, idEstabelecimento);

                if (associacao != null)
                    _repository.Delete(associacao);
            }
        }
    }
}

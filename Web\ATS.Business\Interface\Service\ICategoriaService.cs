﻿using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models.Categoria;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Service
{
    public interface ICategoriaService : IBaseService<ICategoriaRepository>
    {
        public BusinessResult<CategoriaAddModelResponse> Add(CategoriaAddModel model);
        public BusinessResult<CategoriaUpdateModelResponse> Update(CategoriaUpdateModel model);
        public BusinessResult<CategoriaEnableModelResponse> Enable(CategoriaEnableModel model);
        public BusinessResult<CategoriaDisableModelResponse> Disable(CategoriaDisableModel model);
        public BusinessResult<CategoriaGetModelResponse> Get(int categoriaId);
        public BusinessResult<IList<CategoriaGetModelResponse>> GetAll(bool filterActive);
        public BusinessResult<CategoriaGetGridModelResponse> GetGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
    }
}

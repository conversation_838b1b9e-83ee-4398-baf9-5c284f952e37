﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Mobile.Request
{
    public class UsuarioIntegrarMobRequestModel : RequestBase
    {
        public string Nome { get; set; }
        public string Login { get; set; }
        public string Senha { get; set; }
        public string CPFCNPJ { get; set; }
        public int? IdGrupoUsuario { get; set; }
        public string FotoBase64 { get; set; }
        public EPerfil Perfil { get; set; }
        public bool Ativo { get; set; } = true;
        public string IdPush { get; set; }
        public bool ReceberNotificacao { get; set; } = false;
        public string RNTRC { get; set; }
        public bool Carreteiro { get; set; }
        public string TokenFirebase { get; set; }
        public int? IdUsuarioCadastro { get; set; } = null;
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string Referencia1 { get; set; }
        public string Referencia2 { get; set; }
        public bool ReceberRelatorioOC { get; set; } = false;
        public string NomeMae { get; set; }        
        public string NomePai { get; set; }        
        public DateTime? DataNascimento { get; set; }
        public bool? Matriz { get; set; } = false;

        #region Veículo

        public string Placa { get; set; }
        public string Chassi { get; set; }
        public int? AnoFabricacao { get; set; }
        public int? AnoModelo { get; set; }
        public string Marca { get; set; }
        public string Modelo { get; set; }
        public string RENAVAM { get; set; }
        public bool ComTracao { get; set; }
        public int TipoContrato { get; set; } = (int)ETipoContrato.Terceiro;
        public int QuantidadeEixos { get; set; } = 2;  
        public int TipoRodagem { get; set; }
        public string TecnologiaRastreamento { get; set; }
        public int? IdTipoCavalo { get; set; }
        public List<int?> Filiais { get; set; }
        public int? IdTipoCarreta { get; set; }
        public int NumeroFrota { get; set; }
        

        #endregion

        #region Filial

        public string CNPJFilial { get; set; }

        #endregion

        #region Endereço

        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public int? Numero { get; set; }
        public string Bairro { get; set; }
        public int IBGECidade { get; set; }

        #endregion

        #region Contato

        public string Telefone { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }

        #endregion

        #region Horarios

        public List<string> HorariosCheckIn { get; set; }
        public List<string> HorariosNotificacao { get; set; }

        #endregion

        public int? IdHorario { get; set; }
        public string CNH { get; set; }
        public string CNHCategoria { get; set; }
        public DateTime? ValidadeCNH { get; set; }
        public bool? Vistoriador { get; set; }
        public bool? VistoriadorMaster { get; set; }
    }
}
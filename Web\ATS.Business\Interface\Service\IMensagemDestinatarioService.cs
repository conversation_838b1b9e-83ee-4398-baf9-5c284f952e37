﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface IMensagemDestinatarioService : IService<MensagemDestinatario>
    {
        MensagemDestinatario Get(int id);
        MensagemDestinatario GetWithAllChilds(int id);
        MensagemDestinatario Add(MensagemDestinatario destinatario);
        IQueryable<MensagemDestinatario> GetDestinatarios(int idMensagem);
        IQueryable<Mensagem> GetMensagensEnviadas(int idUsuarioDestinatario);
        void Update(MensagemDestinatario mensagemDestinatario);
        void Delete(MensagemDestinatario mensagemDestinatario);

    }
}

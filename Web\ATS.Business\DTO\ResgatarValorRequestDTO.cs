using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class ResgatarValorRequestDTO
    {
        public string Empresa { get; set; }
        public IdentificadorCartao Cartao { get; set; }
        public int Historico { get; set; }
        public decimal Valor { get; set; }
        public long ProtocoloRequisicao { get; set; }
        public bool PermitirTransacaoPendente { get; set; }
        public string InformacoesAdicionais { get; set; }
        public string Metadados { get; set; }
        public bool IgnorarValidacaoTransacaoExistente { get; set; }
        public int? GrupoContabilizacao { get; set; }
    }
}
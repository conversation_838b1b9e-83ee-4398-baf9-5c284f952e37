﻿using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;


namespace ATS.Data.Repository.EntityFramework
{
    public class EstabelecimentoBaseDocumentoRepository : Repository<EstabelecimentoBaseDocumento>, IEstabelecimentoBaseDocumentoRepository
    {
        public List<EstabelecimentoBaseDocumento> GetDocumentos(int idEstabelecimentoBase)
        {
            return Where(x => x.IdEstabelecimentoBase == idEstabelecimentoBase).ToList();
        }

        public EstabelecimentoBaseDocumentoRepository(AtsContext context) : base(context)
        {
        }
    }
}

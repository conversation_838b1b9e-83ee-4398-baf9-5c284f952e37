﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Transactions;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class PagamentoConfiguracaoProcessoApp : AppBase, IPagamentoConfiguracaoProcessoApp
    {
        private readonly IPagamentoConfiguracaoProcessoService _pagamentoConfiguracaoProcessoService;

        public PagamentoConfiguracaoProcessoApp(IPagamentoConfiguracaoProcessoService pagamentoConfiguracaoProcessoService)
        {
            _pagamentoConfiguracaoProcessoService = pagamentoConfiguracaoProcessoService;
        }

        public ValidationResult Add(PagamentoConfiguracaoProcesso pagamentoConfiguracaoProcesso)
        {            
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _pagamentoConfiguracaoProcessoService.Add(pagamentoConfiguracaoProcesso);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }

        public ValidationResult Update(PagamentoConfiguracaoProcesso pagamentoConfiguracaoProcesso)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _pagamentoConfiguracaoProcessoService.Update(pagamentoConfiguracaoProcesso);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();

                return new ValidationResult();
            }
        }
        
        public IEnumerable<PagamentoConfiguracaoProcesso> GetItemsByProcess(int idConfiguracao, EProcessoPgtoFrete processo)
        {
            return _pagamentoConfiguracaoProcessoService.GetItemsByProcess(idConfiguracao, processo);
        }

        public void Remove(PagamentoConfiguracaoProcesso doctoProcesso)
        {
            
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                _pagamentoConfiguracaoProcessoService.Remove(doctoProcesso);
                transaction.Complete();
            }
        }

        public List<PagamentoConfiguracaoProcesso> GetbyPagtoDocto( int idDoctoPgto)
        {
            return _pagamentoConfiguracaoProcessoService.GetbyPagtoDocto(idDoctoPgto);
        }

    }
}

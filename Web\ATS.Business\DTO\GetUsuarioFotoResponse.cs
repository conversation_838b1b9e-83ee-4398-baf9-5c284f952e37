using System;
using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class UsuarioFotoResponse
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public UsuarioFotoResponseDto Objeto { get; set; }

        public UsuarioFotoResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }
    }

    public class UsuarioFotoResponseDto
    {
        public int IdUsuario { get; set; }
        public string Cpfcnpj { get; set; }
        public string Foto { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioConciliacaoAnaliticoTransacoesDTO
    {
        public int? IdEmpresa { get; set; }

        public int Take { get; set; }

        public int Page { get; set; }

        public OrderFilters Order { get; set; }

        public List<QueryFilters> Filters { get; set; }

        public EExtensaoArquivoRelatorio Extensao { get; set; }

        public DateTime DataInicial { get; set; }
        public DateTime DataFinal { get; set; }
        public bool SomenteDivergencia { get; set; }
        public string Pesquisa { get; set; }
        public int Produto { get; set; }
        public int IdUsuario { get; set; }
        public string Nome { get; set; }

        public string Saldoinicial { get; set; }

        public string SaldoFinal { get; set; }

        public string TotalProcessadora { get; set; }

        public string TotalNaoConcilicado { get; set; }
        public int EmpresaFiltro { get; set; }
        public bool CarregarSaldoAtualEmpresa { get; set; } = true;
    }
}
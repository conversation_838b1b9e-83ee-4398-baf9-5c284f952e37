﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class ViagemDocumentoFiscalService : ServiceBase, IViagemDocumentoFiscalService
    {
        private readonly IViagemDocumentoFiscalRepository _repository;
        private readonly IViagemRepository _viagemRepository;
        private readonly IUserIdentity _userIdentity;

        public ViagemDocumentoFiscalService(IViagemDocumentoFiscalRepository repository, IViagemRepository viagemRepository, IUserIdentity userIdentity)
        {
            _repository = repository;
            _viagemRepository = viagemRepository;
            _userIdentity = userIdentity;
        }

        public ValidationResult Add(ViagemDocumentoFiscal viagemDocumentoFiscal)
        {
            try
            {
                _repository.Add(viagemDocumentoFiscal);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Update(ViagemDocumentoFiscal viagemDocumentoFiscal)
        {
            try
            {
                _repository.Update(viagemDocumentoFiscal);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public List<object> GetByViagem(int idViagem)
        {
            var empresaIdViagem = _viagemRepository.GetQuery()
                .Where(c => c.IdViagem == idViagem)
                .Select(c => c.IdEmpresa)
                .FirstOrDefault();
            
            if (_userIdentity.IdEmpresa.HasValue && empresaIdViagem != _userIdentity.IdEmpresa)
                throw new InvalidOperationException("Viagem não pertence à empresa do usuário.");
            
            var documentos = _repository.GetAll().Where(o => o.IdViagem == idViagem).ToList();
            var objects = new List<object>();

            foreach (var documento in documentos)
                objects.Add(new { NumeroDocumento = $"{documento.NumeroDocumento}", documento.Serie, TipoDocumento = documento.TipoDocumento.DescriptionAttr(), PesoSaida = $"{documento.PesoSaida:N}",
                    documento.Chave });

            return objects;
        }
    }
}

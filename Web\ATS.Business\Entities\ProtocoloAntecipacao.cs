﻿using ATS.Domain.Enum;
using System;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ProtocoloAntecipacao
    {
        /// <summary>
        /// Código de antecipação do protocolo
        /// </summary>
        public int IdProtocoloAntecipacao { get; set; }

        /// <summary>
        /// Código do protocolo
        /// </summary>
        public int IdProtocolo { get; set; }

        /// <summary>
        /// Status de antecipação do protocolo
        /// </summary>
        public EStatusProtocoloAntecipacao Status { get; set; } = 0;

        /// <summary>
        /// Data da solicitação da antecipação do protocolo
        /// </summary>
        public DateTime DataSolicitacao { get; set; }

        /// <summary>
        /// Valor do pagamento antecipado
        /// </summary>
        public decimal ValorPagamentoAntecipado { get; set; }
        /// <summary>
        /// Data do pagamento antecipado
        /// </summary>
        public DateTime DataPagamentoAntecipado { get; set; }

        /// <summary>
        /// Data do pagamento antecipado
        /// </summary>
        public decimal TaxaPagamentoAntecipado { get; set; }

        /// <summary>
        /// Código do motivo
        /// </summary>
        public int? IdMotivo { get; set; }
        
        public string Detalhamento { get; set; }

        #region Virtual Fields
        public virtual Protocolo Protocolo { get; set; }
        public virtual Motivo Motivo { get; set; }
        #endregion
    }
}
using System;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Dapper
{
    public interface ICheckinResumoDapper : IRepositoryDapper<CheckinResumo>
    {
        CheckinResumoConsultaModel ConsultarCheckinResumosPaginado(int empresaId, int itensPorPagina, int pagina, DateTime? dataInicio, DateTime? dataFim);
    }
}
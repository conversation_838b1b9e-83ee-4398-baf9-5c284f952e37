using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class ConsultaEstabelecimentoModelResponse
    {
        public int IdEstabelecimento { get; set; }

        public string TipoEstabelecimento { get; set; }

        public string Icone { get; set; }

        public string TokenIcone { get; set; }

        public string Estabelecimento { get; set; }

        public string Pais { get; set; }

        public int? CodigoBACENPais { get; set; }

        public string Estado { get; set; }

        public int? CodigoIBGEEstado { get; set; }

        public string Cidade { get; set; }

        public int? CodigoIBGECidade { get; set; }

        public string Bairro { get; set; }

        public string Logradouro { get; set; }

        public string CEP { get; set; }

        public int? Numero { get; set; }

        public string Complemento { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        public List<EstabelecimentoProdutoModelResponse> Produtos { get; set; }
        public bool Ativo { get; set; }
        public string CNPJEstabelecimento { get; set; }

        public string Telefone { get; set; }

        public string Email { get; set; }
    }
    
    public class EstabelecimentoProdutoModelResponse
    {
        public int IdProduto { get; set; }
        public string Produto { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? PrecoUnitario { get; set; }
        public decimal? PrecoPromocional { get; set; }
    }
}
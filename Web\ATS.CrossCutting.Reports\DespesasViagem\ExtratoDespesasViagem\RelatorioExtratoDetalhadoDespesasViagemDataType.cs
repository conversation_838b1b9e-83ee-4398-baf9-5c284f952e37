﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem
{
    public class RelatorioExtratoDetalhadoDespesasViagemDataType
    {
        public string DataHoraTransacao { get; set; }
        public string ValorTransacao { get; set; }
        public string DescricaoPlanoVendas { get; set; }
        public string NumeroCartao { get; set; }
        public string DocumentoPortador { get; set; }
        public int NumeroConta { get; set; }
        public int Mcc { get; set; }
        public string DescricaoMcc { get; set; }
        public string DescricaoGrupoMcc { get; set; }
        public string ValorTransacaoDolar { get; set; }
        public int CodigoMoedaDolar { get; set; }
        public string CotacaoDolarnoDia { get; set; }
        public string ValorLocal { get; set; }
        public int CodigoMoedaLocal { get; set; }
        public string TerminalId { get; set; }
    }
}

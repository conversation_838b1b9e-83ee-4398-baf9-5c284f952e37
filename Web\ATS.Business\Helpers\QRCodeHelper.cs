﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Service;
using QRCoder;

namespace ATS.Domain.Helpers
{
    public class QRCodeHelper
    {
        public string GerarQRCode(Bitmap logo, string data)
        {

            QRCodeGenerator qrGenerator = new QRCodeGenerator();
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
            Base64QRCode base64QrCode = new Base64QRCode(qrCodeData);

            string qrCodeImageAsBase64 = string.Empty;
            if (logo != null)
                qrCodeImageAsBase64 = base64QrCode.GetGraphic(20, Color.Black, Color.White, logo);
            else
                qrCodeImageAsBase64 = base64QrCode.GetGraphic(20);

            return qrCodeImageAsBase64;
        }
    }
}

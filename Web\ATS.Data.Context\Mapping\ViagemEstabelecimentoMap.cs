﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemEstabelecimentoMap : EntityTypeConfiguration<ViagemEstabelecimento>
    {
        public ViagemEstabelecimentoMap()
        {
            ToTable("VIAGEM_ESTABELECIMENTO");

            HasKey(x => x.IdViagemEstabelecimento);

            HasRequired(t => t.Estabelecimento)
                .WithMany(t => t.ViagemEstabelecimentos)
                .HasForeignKey(t => t.IdEstabelecimento);

            HasRequired(x => x.Viagem)
                .WithMany(x => x.ViagemEstabelecimentos)
                .HasForeignKey(x => new { x.IdViagem, x.IdEmpresa });
        }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class AutenticacaoAplicacaoMap : EntityTypeConfiguration<AutenticacaoAplicacao>
    {
        public AutenticacaoAplicacaoMap()
        {
            ToTable("AUTENTICACAOAPLICACAO");

            HasKey(x => x.IdAutenticacaoEmpresa);
            
            Property(x => x.Ativo)
                .IsRequired();

            Property(x => x.CNPJAplicacao)
                .IsRequired();

            Property(x => x.Token)
                .IsRequired();

            HasRequired(x => x.Empresa)
                .WithMany(x => x.AutenticacaoAplicacao)
                .HasForeignKey(x => x.IdEmpresa);
        }
    }
}
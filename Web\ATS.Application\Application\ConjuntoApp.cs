﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;

namespace ATS.Application.Application
{
    public class ConjuntoApp : AppBase, IConjuntoApp
    {

        private readonly IConjuntoService _service;

        public ConjuntoApp(IConjuntoService service)
        {
            _service = service;
        }

        public Conjunto Get(int id)
        {
            return _service.Get(id);
        }

        public List<Conjunto> GetByPlaca(string placa)
        {
            return _service.GetMotoristasBaseByPlaca(placa);
        }

        public string GetByCPFPlacaConjuntoMontado(string CPF)
        {
            return _service.GetByCPFPlacaConjuntoMontado(CPF);
        }

        public Conjunto GetConjuntoByPlacaCPF(string CPF, string placa)
        {
            return _service.GetConjuntoByPlacaCPF(CPF, placa);
        }

        public ValidationResult Add(Conjunto entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.Add(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }


        /*public void UpdatestatusMotorista(int idMotoristaBase, EStatusGR StatusGR)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {

                transaction.Complete();

            }
        }*/
        public ValidationResult MontarConjunto(string CPF, int IdConjunto)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.MontarConjunto(CPF, IdConjunto);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /*public ValidationResult CriarConjuntoEmpresa(ConjuntoModel alterarStatusGRModel, int IdConjunto)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                /*ValidationResult validationResult = _service.CriarConjuntoEmpresa(alterarStatusGRModel,  IdConjunto);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();#1#
            }

            return new ValidationResult();
        }*/

        /*public ValidationResult CriarNovoConjunto(ConjuntoModel conjuntoModel, int? idProprietarioBase = null)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.CriarNovoConjunto(conjuntoModel, idProprietarioBase);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }*/

        public ValidationResult Update(Conjunto entity)
        {
            try
            {
               using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _service.Update(entity);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }

                return new ValidationResult();
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }
        }

        /*public Conjunto Get(string placa, string cpf, List<string> carretas, bool HasConsultaGR = true, bool isMontado = false)
        {
            return _service.Get(placa, cpf, carretas, HasConsultaGR, isMontado);
        }*/

        /*public IQueryable<Conjunto> GetQuery(string placa, string cpf, List<string> carretas, bool HasConsultaGR = true, bool isMontado = false)
        {
            return _service.GetQuery(placa, cpf, carretas, HasConsultaGR, isMontado);
        }*/

        /*public Conjunto GetConjunto(string placa, string cpf, List<string> carretas, bool HasConsultaGR = true,
            bool isMontado = false)
        {
            return _service.GetConjunto(placa, cpf, carretas, HasConsultaGR, isMontado);
        }*/

        /*public List<Conjunto> ConsultarPorPlaca(string placa, string cpf)
        {
            return _service.ConsultarPorPlaca(placa, cpf);
        }*/

        public ValidationResult AddGestorFrota(int IdConjunto, int IdGestorFrota)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.AddGestorFrota(IdConjunto, IdGestorFrota);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();

        }

        public ValidationResult RemoveGestorFrota(List<int> IdsConjunto, int IdGestorFrota)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.RemoveGestorFrota(IdsConjunto, IdGestorFrota);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        public ValidationResult AtulizarStatusConjunto(int IdConjunto, string CPF)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.AtulizarStatusConjunto(IdConjunto, CPF);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();

        }

        public ValidationResult AlterarTipoVeiculoConjunto(int IdConjunto, string placa, bool ehCavalo, int? tipoVeiculo)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.AlterarTipoVeiculoConjunto(IdConjunto, placa, ehCavalo, tipoVeiculo);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /*public Conjunto GetConjuntoMontado( string cpf, bool withIncludes = true )
        {
            return _service.GetConjuntoMontado( cpf, false, withIncludes );
        }*/

        /*public IQueryable<Conjunto> GetQueryConjuntoMontado(string cpf)
        {
            return _service.GetQueryConjuntoMontado( cpf );            
        }*/
                
        public List<Conjunto> GetByIdGestor(int IdGestorFrota)
        {
            return _service.GetByIdGestor(IdGestorFrota);
        }

        public List<Conjunto> GetByCPFCNPJGestor(List<string> CPFCNPJs)
        {
            return _service.GetByCPFCNPJGestor(CPFCNPJs);
        }

        /*public void Update(ProprietarioBase pb)
        {
            /*using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                 new PreUsuarioService().Update(pb);

                 transaction.Complete();
            }#1#
        }*/

        public ConjuntoEmpresa GetConjuntoEmpresa( int IdConjunto)
        {
            return _service.GetConjuntoEmpresa( IdConjunto );
        }

        /*public ValidationResult  CriarNovoConjunto( ConjuntoModel conujuntoModel)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.CriarNovoConjunto(conujuntoModel);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }*/

        /*public DadosProprietarioOrdemCarregamento VerificarCavaloPossuiProprietario(string cpf, string placaCavalo)
        {
            return _service.VerificarCavaloPossuiProprietario(cpf, placaCavalo);
		}*/
        public ValidationResult AtualizarTipos(int IdConjunto, Conjunto conjunto)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.AtualizarTipos(IdConjunto, conjunto);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

    }
}
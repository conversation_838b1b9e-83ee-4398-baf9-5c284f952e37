﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemValorAdicionalMap : EntityTypeConfiguration<ViagemValorAdicional>
    {
        public ViagemValorAdicionalMap()
        {
            ToTable("VIAGEM_VLADICIONAL");

            HasKey(t => t.IdViagemValorAdicional);

            Property(t => t.Valor)
                .HasPrecision(10, 2);

            HasRequired(t => t.ViagemEvento)
                .WithMany(t => t.ViagemValoresAdicionais)
                .HasForeignKey(t => t.IdViagemEvento);
        }
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Enum;
using ATS.WS.ControllersATS.Default;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Attributes;

namespace ATS.WS.ControllersATS
{
    public class ViagemSolicitacaoAbonoAtsController : DefaultController
    {
        private readonly IViagemSolicitacaoAbonoApp _viagemSolicitacaoAbonoApp;

        public ViagemSolicitacaoAbonoAtsController(IViagemSolicitacaoAbonoApp viagemSolicitacaoAbonoApp)
        {
            _viagemSolicitacaoAbonoApp = viagemSolicitacaoAbonoApp;
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetSolicitacoes(string token, string numero, DateTime? dataInicio, DateTime? dataFim, EStatusAbono? status)
        {
            try
            {
                var solicitacaoes = _viagemSolicitacaoAbonoApp.GetSolicitacoes( token, numero, dataInicio, dataFim, status);

                return ResponderSucesso(solicitacaoes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus( int idViagemAbono, EStatusAbono status)
        {
            try
            {
                var solicitacao = _viagemSolicitacaoAbonoApp.AlterarStatus(idViagemAbono, status);

                return ResponderSucesso(solicitacao);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
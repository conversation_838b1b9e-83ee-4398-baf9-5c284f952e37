﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class GrupoUsuarioMenu
    {
        /// <summary>
        /// Código do Grupo de Usuário
        /// </summary>
        public int IdGrupoUsuario { get; set; }

        /// <summary>
        /// Código do menu
        /// </summary>
        public int IdMenu { get; set; }

        #region Referências

        [ForeignKey("IdGrupoUsuario")]
        public virtual GrupoUsuario GrupoUsuario { get; set; }

        [ForeignKey("IdMenu")]
        public virtual Menu Menu { get; set; }

        #endregion
    }
}
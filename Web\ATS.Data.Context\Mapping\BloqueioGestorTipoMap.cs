using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class BloqueioGestorTipoMap : EntityTypeConfiguration<BloqueioGestorTipo>
    {
        public BloqueioGestorTipoMap()
        {
            ToTable("BLOQUEIO_GESTOR_TIPO");

            HasKey(t => t.IdBloqueioGestorTipo);
            Property(t => t.IdBloqueioGestorTipo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            
            Property(t => t.IdBloqueioGestorTipo).IsRequired();
            Property(t => t.HabilitarPorEmpresa).IsRequired();
            Property(t => t.HabilitarPorFilial).IsRequired();
        }
    }
}
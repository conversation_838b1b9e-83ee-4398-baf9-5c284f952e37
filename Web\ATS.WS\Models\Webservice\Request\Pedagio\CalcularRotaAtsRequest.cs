using System.Collections.Generic;
using System.Linq.Dynamic;
using ATS.Application.Interface;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Request.Base;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.Models.Webservice.Request.Pedagio
{
    public class CalcularRotaAtsRequest : RequestBase
    {
        public List<LocationDTO> Localizacoes { get; set; }
        public ConsultaRotaRequestTipoVeiculo? TipoVeiculo { get; set; }
        public int? QtdEixos { get; set; }
        public bool ExibirDetalhes { get; set; } = false;

        public ValidationResult ValidaRequest()
        {
            ValidationResult validationResult = ValidaRequestBase();
            
            if (Localizacoes == null || !Localizacoes.Any() || Localizacoes.Count < 2)
                validationResult.Add("É obrigatório o envio de ao menos duas localizações, sendo elas origem e destino da rota");
            
            if (!TipoVeiculo.HasValue)
                validationResult.Add("O campo TipoVeiculo é obrigatório para o cálculo do valor do pedágio");
            
            if (!QtdEixos.HasValue)
                validationResult.Add("O campo QtdEixos é obrigatório para o cálculo do valor do pedágio");
            
            return validationResult;
        }
    }
}
﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class BloqueioFinanceiroTipoRepository : Repository<BloqueioFinanceiroTipo>, IBloqueioFinanceiroTipoRepository
    {
        public BloqueioFinanceiroTipoRepository(AtsContext context) : base(context)
        {
        }
    }
}
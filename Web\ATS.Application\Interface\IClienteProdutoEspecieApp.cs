﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IClienteProdutoEspecieApp
    {
        List<ClienteProdutoEspecie> GetTodosPorCliente(int idCliente);
        object GetTodosProdutosPorClienteForUiGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        object GetTodasEspeciesPorClienteForUiGrid(int Take, int Page, OrderFilters Order, List<QueryFilters> Filters);
    }
}
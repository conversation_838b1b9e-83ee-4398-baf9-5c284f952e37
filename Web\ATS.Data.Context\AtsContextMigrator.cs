﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Migrations;
using System.Data.Entity.Migrations.Infrastructure;
using System.Linq;
using System.Text;
using NLog;
using Configuration = ATS.Data.Context.Migrations.Configuration;

namespace ATS.Data.Context
{
    public class AtsContextMigrator
    {
        private readonly Lazy<DbMigrator> _migrator;

        public DbMigrationsConfiguration<AtsContext> Configuration { get; }

        /// <summary>
        /// Obter versão atual da base de dados
        /// </summary>
        public string CurrentDatabaseVersion => _migrator.Value.GetDatabaseMigrations().FirstOrDefault(); // Lista em ordem descrescente

        /// <summary>
        /// Obter último arquivo de migration compilado na aplicação (Versão detino para atualização)
        /// </summary>
        public string LastApplicationMigration => _migrator.Value.GetLocalMigrations().Last(); // Lista em ordem crescente

        /// <summary>
        /// Obter migrations aplicados na base de dados
        /// </summary>
        public IEnumerable<string> DatabaseMigrations => _migrator.Value.GetDatabaseMigrations();

        /// <summary>
        /// Obter migrations pendentes para aplicar na base de dados
        /// </summary>
        public IEnumerable<string> PendingMigrations => _migrator.Value.GetPendingMigrations();

        public bool HasPendingMigrations => PendingMigrations.Any();

        /// <summary>
        /// Contrutor
        /// </summary>
        /// <param name="configuration"></param>
        public AtsContextMigrator()
        {
            Configuration = new Configuration();
            _migrator = new Lazy<DbMigrator>(() => new DbMigrator(this.Configuration));
        }

        /// <summary>
        /// Obter script para atualizar base de dados para o ultimo arquivo de migration compilado na aplicação
        /// </summary>
        /// <returns></returns>
        public string GetUpdateScriptToLastMigration()
        {
            // Ao instanciar o MigratorScriptingDecorator acontece alguma coisa que faz não funcionar mais a atualização da base de dados com a instância atual do _migrator.         
            var script = new MigratorScriptingDecorator(_migrator.Value);
            return script.ScriptUpdate(null, LastApplicationMigration);
        }

        /// <summary>
        /// Obter script para atualizar base de dados para o ultimo arquivo de migration compilado na aplicação como array de byte para ser utilizado como download do script
        /// </summary>
        /// <returns></returns>
        public byte[] GetUpdateScriptToLastMigrationAsByteArray()
        {
            var data = Encoding.UTF8.GetBytes(GetUpdateScriptToLastMigration());
            var result = Encoding.UTF8.GetPreamble().Concat(data).ToArray();
            return result;
        }

        /// <summary>
        /// Atualizar base de dados para o ultimo arquivo de migration compilado na aplicação
        /// </summary>
        public void UpdateDatabaseToLastMigration()
        {
            if (Configuration.AutomaticMigrationsEnabled)
                throw new Exception($"Não é permitido aplicar migrations com a propriedade \"{nameof(Configuration.AutomaticMigrationsEnabled)}\" habilitada.");
            if (Configuration.AutomaticMigrationDataLossAllowed)
                throw new Exception($"Não é permitido aplicar migrations com a propriedade \"{nameof(Configuration.AutomaticMigrationDataLossAllowed)}\" habilitada.");
            
            // Não sei porque, mas está sendo necessário criar uma nova instância do DbMigrator para aplicar os migrations.
            // Ao tentar utilizar a instância armazena em "_migrator" não é aplicado os migrations, mas também não ocorre erro.
            var migrator = new DbMigrator(Configuration);
            migrator.Update(LastApplicationMigration);
        }

        public void UpdateDatabaseToLastMigrationIfPending()
        {
            if (!HasPendingMigrations)
                return;
            
            var logger = LogManager.GetLogger("migrations");
            try
            {
                logger.Info($"Iniciando atualização da base de dados.");
                logger.Info($"Versão atual  : {CurrentDatabaseVersion}.");
                logger.Info($"Versão destino: {LastApplicationMigration}.");
                logger.Info($"Pendentes     : {string.Join(", ", PendingMigrations)}");

                var script = GetUpdateScriptToLastMigration();
                logger.Info($"{new string('-', 30)} SCRIPT {new string('-', 30)}");
                logger.Info(script);
                logger.Info($"{new string('-', 30)} FIM {new string('-', 30)}");

                UpdateDatabaseToLastMigration();

                logger.Info("Atualização concluída");
            }
            catch (Exception e)
            {
                logger.Fatal(e, "Erro ao atualizar base de dados");
                throw;
            }
        }
    }
}
﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Security;
using ATS.WS.Security.Configuration;
using System;
using System.Linq;
using System.Net;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Models;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Helpers;
using ATS.WS.Helpers;
using Newtonsoft.Json;
using NLog;
using ActionFilterAttribute = System.Web.Mvc.ActionFilterAttribute;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class Autorizar : ActionFilterAttribute
    {
        public EPerfil[] PerfisPermitidos { get; set; }

        public IUserIdentity UserIdentity { get; set; }
        
        private static readonly object Lock = new object();
        
        private readonly bool _habilitarValidacao2Fa = WebConfigurationManager.AppSettings["HABILITAR_VALIDACAO_2FA"].ToBoolSafe();

        public Autorizar()
        {
        }

        public Autorizar(params EPerfil[] perfils)
        {
            PerfisPermitidos = perfils;
        }

        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var customAttributes = filterContext.ActionDescriptor.GetCustomAttributes(true);

            if (!customAttributes.Any() || customAttributes.All(x => x.GetType() != typeof(Autorizar)))
            {
                return;
            }

            var token = filterContext.HttpContext.Request.Headers["Authorization"];

            token = string.IsNullOrEmpty(token)
                ? filterContext.HttpContext.Request.Headers["SessionKey"] ?? filterContext.HttpContext.Request.Params["SessionKey"]
                : token.Split(' ')[1];

            new Autenticacao().ValidarToken(token,
                new TokenConfigurations
                {
                    Audience = WebConfigurationManager.AppSettings["ATS_Audience"],
                    Issuer = WebConfigurationManager.AppSettings["ATS_Issuer"],
                    Seconds = Convert.ToInt32(WebConfigurationManager.AppSettings["ATS_TempoSessao"])
                }, new SigningConfigurations(), UserIdentity, filterContext);

            if (filterContext.Result != null) return;

            UserIdentity.Origem = EUserOrigin.AtsWeb;

            filterContext.HttpContext.Session["Usuario"] = UserIdentity;

            if (PerfisPermitidos?.All(x => x != (EPerfil)UserIdentity.Perfil) == true)
            {
                var sessionKey = filterContext.HttpContext.Request.Headers["SessionKey"];
                var refreshKey = filterContext.HttpContext.Request.Headers["RefreshKey"];
                var keycloak = DependencyResolver.Current.GetService<KeycloakHelper>();
                if(sessionKey != null && refreshKey != null) keycloak.LogoutUserSession(sessionKey, refreshKey);
                filterContext.Result = new HttpStatusCodeResult(HttpStatusCode.Forbidden,"Perfil de usuario nao tem acesso ao recurso");
                return;
            }
            
            var twofactorAttribute = (Validate2FA)customAttributes.FirstOrDefault(x => x.GetType() == typeof(Validate2FA));
            if (twofactorAttribute != null && _habilitarValidacao2Fa)
            {
                Validate2FA.ExecutarValidacao(filterContext, UserIdentity);
                if (filterContext.Result != null) return;
            }

            lock (Lock)
            {
                var ip = GetIpAddress(filterContext);
                UserIdentity.IpUsuario = ip;

                LogManager.GetCurrentClassLogger().Info($"IP DO USUARIO {UserIdentity.IdUsuario}: {ip}");
                
                if (ip == null || ip == "::1") return;

                var locationHelper = DependencyResolver.Current.GetService<LocationHelper>();

                if (locationHelper.ExistePorUsuarioIp(ip)) return;
                    
                if (UserIdentity.Latitude == 0 && UserIdentity.Longitude == 0)
                {
                    var loc = locationHelper.GetLocalizacao(ip);
                    if (!loc.Success) return;
                    UserIdentity.Latitude = loc.Lat;
                    UserIdentity.Longitude = loc.Lon;
                    UserIdentity.Cidade = loc.City;
                    UserIdentity.Provedor = loc.Org;
                    UserIdentity.Estado = loc.RegionName;
                }

                if (UserIdentity.Latitude == 0 || UserIdentity.Longitude == 0 || UserIdentity.Cidade == null ||
                    UserIdentity.Estado == null || UserIdentity.Provedor == null)
                    return;

                locationHelper.SalvarLocalizacao(ip);
            }
        }
        
        private string GetIpAddress(ActionExecutingContext filterContext)
        {
            var ip = filterContext.HttpContext.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] ?? 
                     filterContext.HttpContext.Request.Headers["X-Forwarded-For"] ?? 
                     filterContext.HttpContext.Request.Headers["X-Real-Ip"];

            if (ip != null)
            {
                var addresses = ip.Split(',');
                if (addresses.Length != 0) return addresses[0];
            }

            return filterContext.HttpContext.Request.ServerVariables["REMOTE_ADDR"];
        }
    }
}
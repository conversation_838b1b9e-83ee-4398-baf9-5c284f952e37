using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ICombustivelJSLService: IService<CombustivelJSL>
    {
        IQueryable<CombustivelJSL> GetQuery(int idcombustivel);
        IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByCombustivel(int idcombustivel);
        IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByEstabelecimento(int idestabelecimento);
        bool SincronizaATS(int idcombustivel);
        bool CombustivelExistenteEstabelecimento(int idcombustivel, int idestabelecimento);
        ValidationResult Integrar(int idcombustivel, int idproduto, int idestabelecimento);
        ValidationResult Deletar(int idproduto, int idestabelecimento);
    }
}
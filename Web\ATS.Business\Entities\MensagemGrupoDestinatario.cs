﻿using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class MensagemGrupoDestinatario
    {
        /// <summary>
        /// Id grupo de destinatário
        /// </summary>
        public int IdGrupoDestinatario { get; set; }

        /// <summary>
        /// Id usuário membro do grupo
        /// </summary>
        public int IdUsuarioDestinatario { get; set; }

        public int IdGrupoUsuario { get; set; }

        #region Referência
        public virtual MensagemGrupoUsuario MensagemGrupoUsuario { get; set; }
        public virtual Usuario UsuarioMembroGrupo { get; set; }
        #endregion
    }
}

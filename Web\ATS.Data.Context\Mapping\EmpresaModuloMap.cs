﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class EmpresaModuloMap : EntityTypeConfiguration<EmpresaModulo>
    {
        public EmpresaModuloMap()
        {
            ToTable("EMPRESA_MODULO");

            HasKey(t => new { IdEmpresa = t.IdEmpresa, t.IdModulo});

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdModulo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
        }
    }
}
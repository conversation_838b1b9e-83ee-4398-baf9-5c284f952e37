﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class PaisApp : AppBase, IPaisApp
    {
        private readonly IPaisService _paisService;

        public PaisApp(IPaisService paisService)
        {
            _paisService = paisService;
        }

        public object ConsultaGrid(string nome, string sigla, int? bacen, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _paisService
                .ConsultarGrid(nome, sigla, bacen, take, page, order, filters);
        }

        /// <summary>
        /// Método utilizado para buscar País.
        /// </summary>
        /// <param name="id">Id de Pais</param>
        /// <returns>Entidade Pais</returns>
        public Pais Get(int id)
        {
            return _paisService.Get(id);
        }

        public Pais BuscarBrasil()
        {
            return _paisService.BuscarBrasil();
        }

        /// <summary>
        /// Método utilizado para incluir País.
        /// </summary>
        /// <param name="entity">Entidade de Pais</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Pais entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                ValidationResult validationResult = _paisService.Add(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para alterar País.
        /// </summary>
        /// <param name="entity">Entidade de Pais</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Pais entity)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _paisService.Update(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para consultar País.
        /// </summary>
        /// <param name="nome">Nome de Pais</param>
        /// <returns>IQueryable de Pais</returns>
        public IQueryable<Pais> Consultar(string nome)
        {
            return _paisService.Consultar(nome);
        }

        /// <summary>
        /// Retorna a cidade por código do IBGE
        /// </summary>
        /// <param name="bacen"></param>
        /// <returns></returns>
        public Pais GetPaisPorBACEN(int bacen)
        {
            return _paisService.GetPorCodigoBACEN(bacen);
        }

        /// <summary>
        /// Retorna os dados do país
        /// </summary>
        /// <param name="sigla">Sigla do país</param>
        /// <returns></returns>
        public Pais Get(string sigla)
        {
            return _paisService.Get(sigla);
        }

        /// <summary>
        /// Processo de desativar o registro
        /// </summary>
        /// <param name="idPais">Código do páis</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idPais)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _paisService.Inativar(idPais);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Processo de reativar o registro
        /// </summary>
        /// <param name="idPais">Código do páis</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idPais)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _paisService.Reativar(idPais);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Pais GetPorNome(string pais)
        {
            return _paisService.GetPorNome(pais);
        }

        public bool VerificarBacenCadastrado(int codigo)
        {
            return _paisService.VerificarBacenCadastrado(codigo);
        }
    }
}
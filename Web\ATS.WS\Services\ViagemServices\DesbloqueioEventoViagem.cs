using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Common.Request;

namespace ATS.WS.Services.ViagemServices
{
    public class DesbloqueioEventoViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly SrvViagem _srvViagem;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IViagemEventoApp _viagemEventoApp;

        public DesbloqueioEventoViagem(IVersaoAnttLazyLoadService versaoAntt, IClienteApp clienteApp, IParametrosApp parametrosApp, IViagemApp viagemApp, ICiotV2App ciotV2App,
            ICiotV3App ciotV3App, IProprietarioApp proprietarioApp, ICadastrosApp cadastrosApp, IEmpresaRepository empresaRepository, SrvViagem srvViagem, ITransacaoCartaoApp transacaoCartaoApp, IEmpresaApp empresaApp, IViagemEventoApp viagemEventoApp)
        {
            _versaoAntt = versaoAntt;
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _empresaRepository = empresaRepository;
            _srvViagem = srvViagem;
            _transacaoCartaoApp = transacaoCartaoApp;
            _empresaApp = empresaApp;
            _viagemEventoApp = viagemEventoApp;
        }

        public DesbloquearEventoResponseModel DesbloquearEvento(DesbloquearEventoRequestModel @params, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return DesbloquearEventoV2(@params, isApi);
                case EVersaoAntt.Versao3:
                    return DesbloquearEventoV3(@params);
                default:
                    return DesbloquearEventoV2(@params, isApi);
            }
        }
        
        private DesbloquearEventoResponseModel DesbloquearEventoV2(DesbloquearEventoRequestModel @params, bool isApi = false)
        {
            var empresaApp = _empresaApp;
            var empresaId = empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            
            var queryViagemEvento = _viagemEventoApp.Find(x => x.IdEmpresa == (empresaId ?? 0) && x.Status != EStatusViagemEvento.Cancelado);

            if (@params.IdViagem.HasValue)
                queryViagemEvento = queryViagemEvento.Where(x => x.IdViagem == @params.IdViagem && x.IdViagemEvento == @params.IdViagemEvento);
            else
                queryViagemEvento = queryViagemEvento.Where(x => x.Viagem.NumeroControle == @params.NumeroControleViagem && x.NumeroControle == @params.NumeroControleEvento);

            var viagemAtual = queryViagemEvento.Select(c => new
            {
                c.Status,
                c.HabilitarPagamentoCartao,
                c.Viagem.DataAtualizacao
            }).FirstOrDefault();
            
            /*if(viagemAtual?.DataAtualizacao.HasValue == true && @params.DataAtualizacao.HasValue && 
               @params.DataAtualizacao < viagemAtual.DataAtualizacao)
                return new DesbloquearEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };*/
            
            if (viagemAtual?.Status == EStatusViagemEvento.Aberto)
            {
                var mensagemCartaoViagemAtual = string.Empty;
                var statusOperacaoCartaoAtual = ERetornoOperacaoCartao.NaoHabilitado;
                
                if (viagemAtual.HabilitarPagamentoCartao)
                {
                    var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == @params.IdViagemEvento).OrderByDescending(x => x.IdTransacaoCartao);

                    var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                    mensagemCartaoViagemAtual = transacaoCartao?.MensagemProcessamentoWs;
                    
                    if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Erro;
                    else
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Sucesso;
                }
                
                return new DesbloquearEventoResponseModel
                {
                    Sucesso = true,
                    Mensagem = "",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = statusOperacaoCartaoAtual,
                        Mensagem = mensagemCartaoViagemAtual
                    },
                    Token = string.Empty
                };
            }
            
            var bloqueioEvento = queryViagemEvento
                .Select(x => new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = @params.CNPJEmpresa,
                    CNPJAplicacao = @params.CNPJAplicacao,
                    Token = @params.Token,
                    DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                    NomeUsuarioAudit = @params.NomeUsuarioAudit,
                    IdViagem = x.IdViagem,
                    NumeroControle = x.Viagem.NumeroControle,
                    PesoSaida = x.Viagem.PesoSaida,
                    PedagioBaixado = x.Viagem.PedagioBaixado,
                    ValorPedagio = x.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = x.Viagem.HabilitarDeclaracaoCiot,
                    NaturezaCarga = x.Viagem.NaturezaCarga,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>
                    {
                        new ViagemEventoIntegrarModel
                        {
                            IdViagemEvento = x.IdViagemEvento,
                            NumeroControle = x.NumeroControle,
                            CpfUsuario = @params.DocumentoUsuarioAudit,
                            NomeUsuario = @params.NomeUsuarioAudit,
                            TipoEvento = x.TipoEventoViagem,
                            IdViagem = x.IdViagem,
                            Status = EStatusViagemEvento.Aberto,
                            HabilitarPagamentoCartao = x.HabilitarPagamentoCartao,
                            HabilitarPagamentoPix = x.HabilitarPagamentoPix
                        }
                    }
                }).FirstOrDefault();

            if (bloqueioEvento == null || bloqueioEvento.IdViagem <= 0 || bloqueioEvento.ViagemEventos[0].IdViagemEvento <= 0)
                return new DesbloquearEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = ERetornoOperacaoCartao.Erro,
                        Mensagem = string.Empty
                    },
                    Token = string.Empty
                };

            var retorno = _srvViagem.Alterar(bloqueioEvento, isApi);

            var statusOperacaoCartao = ERetornoOperacaoCartao.NaoHabilitado;
            var mensagemProcessamentoWs = string.Empty;
            var idViagemEvento = bloqueioEvento.ViagemEventos[0].IdViagemEvento.Value;

            //TODO: para não precisar ir no banco de novo para o retorno teria que ter um DTO do retorno da viagem, pois não da pra fazer select nas propriedades de objeto anônimo fora do método de declaração dele
            if (bloqueioEvento.ViagemEventos[0].HabilitarPagamentoCartao)
            {
                var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                    .OrderByDescending(x => x.IdTransacaoCartao);

                var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                mensagemProcessamentoWs = transacaoCartao?.MensagemProcessamentoWs;
                if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                    statusOperacaoCartao = ERetornoOperacaoCartao.Erro;
                else
                    statusOperacaoCartao = ERetornoOperacaoCartao.Sucesso;
            }

            var viagemEvento = _viagemEventoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                .Select(x => new
                {
                    x.Status,
                    x.Token
                }).First();

            return new DesbloquearEventoResponseModel
            {
                Sucesso = retorno.Sucesso && viagemEvento.Status == EStatusViagemEvento.Aberto && statusOperacaoCartao != ERetornoOperacaoCartao.Erro,
                Mensagem = retorno.Mensagem,
                IdViagem = bloqueioEvento.IdViagem ?? 0,
                NumeroControle = bloqueioEvento.NumeroControle,
                IdViagemEvento = bloqueioEvento.ViagemEventos[0].IdViagemEvento ?? 0,
                NumeroControleEvento = bloqueioEvento.ViagemEventos[0].NumeroControle,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = statusOperacaoCartao,
                    Mensagem = mensagemProcessamentoWs
                },
                Token = viagemEvento.Token
            };
        }
        
        private DesbloquearEventoResponseModel DesbloquearEventoV3(DesbloquearEventoRequestModel @params)
        {
            var empresaApp = _empresaApp;
            var empresaId = empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            
            var queryViagemEvento = _viagemEventoApp.Find(x =>
                x.IdEmpresa == (empresaId ?? 0) && x.Status != EStatusViagemEvento.Cancelado);

            if (@params.IdViagem.HasValue)
                queryViagemEvento = queryViagemEvento.Where(x =>
                    x.IdViagem == @params.IdViagem && x.IdViagemEvento == @params.IdViagemEvento);
            else
                queryViagemEvento = queryViagemEvento.Where(x =>
                    x.Viagem.NumeroControle == @params.NumeroControleViagem &&
                    x.NumeroControle == @params.NumeroControleEvento);

            var viagemAtual = queryViagemEvento.Select(c => new
            {
                c.Status,
                c.HabilitarPagamentoCartao,
                c.Viagem.FormaPagamento,
                c.Viagem.DataAtualizacao
            }).FirstOrDefault();
            
            if(viagemAtual?.DataAtualizacao.HasValue == true && @params.DataAtualizacao.HasValue && 
               @params.DataAtualizacao < viagemAtual.DataAtualizacao)
                return new DesbloquearEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };

            if (viagemAtual?.Status == EStatusViagemEvento.Aberto)
            {
                var mensagemCartaoViagemAtual = string.Empty;
                var statusOperacaoCartaoAtual = ERetornoOperacaoCartao.NaoHabilitado;

                if (viagemAtual.HabilitarPagamentoCartao)
                {
                    var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == @params.IdViagemEvento)
                        .OrderByDescending(x => x.IdTransacaoCartao);

                    var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento})
                        .FirstOrDefault();

                    mensagemCartaoViagemAtual = transacaoCartao?.MensagemProcessamentoWs;

                    if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Erro;
                    else
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Sucesso;
                }

                return new DesbloquearEventoResponseModel
                {
                    Sucesso = true,
                    Mensagem = "",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = statusOperacaoCartaoAtual,
                        Mensagem = mensagemCartaoViagemAtual
                    },
                    Token = string.Empty
                };
            }

            var bloqueioEvento = queryViagemEvento
                .Select(x => new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = @params.CNPJEmpresa,
                    CNPJAplicacao = @params.CNPJAplicacao,
                    Token = @params.Token,
                    DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                    NomeUsuarioAudit = @params.NomeUsuarioAudit,
                    IdViagem = x.IdViagem,
                    NumeroControle = x.Viagem.NumeroControle,
                    PesoSaida = x.Viagem.PesoSaida,
                    PedagioBaixado = x.Viagem.PedagioBaixado,
                    ValorPedagio = x.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = x.Viagem.HabilitarDeclaracaoCiot,
                    NaturezaCarga = x.Viagem.NaturezaCarga,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>
                    {
                        new ViagemEventoIntegrarModel
                        {
                            IdViagemEvento = x.IdViagemEvento,
                            NumeroControle = x.NumeroControle,
                            CpfUsuario = @params.DocumentoUsuarioAudit,
                            NomeUsuario = @params.NomeUsuarioAudit,
                            TipoEvento = x.TipoEventoViagem,
                            IdViagem = x.IdViagem,
                            Status = EStatusViagemEvento.Aberto,
                        }
                    }
                }).FirstOrDefault();

            if (bloqueioEvento == null || bloqueioEvento.IdViagem <= 0 ||
                bloqueioEvento.ViagemEventos[0].IdViagemEvento <= 0)
                return new DesbloquearEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem =
                        "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = ERetornoOperacaoCartao.Erro,
                        Mensagem = string.Empty
                    },
                    Token = string.Empty
                };

            var retorno = _srvViagem.Alterar(bloqueioEvento);

            var statusOperacaoCartao = ERetornoOperacaoCartao.NaoHabilitado;
            var mensagemProcessamentoWs = string.Empty;
            var idViagemEvento = bloqueioEvento.ViagemEventos[0].IdViagemEvento.Value;

            //TODO: para não precisar ir no banco de novo para o retorno teria que ter um DTO do retorno da viagem, pois não da pra fazer select nas propriedades de objeto anônimo fora do método de declaração dele
            if (viagemAtual.FormaPagamento == EViagemFormaPagamento.Cartao)
            {
                var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                    .OrderByDescending(x => x.IdTransacaoCartao);

                var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento})
                    .FirstOrDefault();

                mensagemProcessamentoWs = transacaoCartao?.MensagemProcessamentoWs;
                if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                    statusOperacaoCartao = ERetornoOperacaoCartao.Erro;
                else
                    statusOperacaoCartao = ERetornoOperacaoCartao.Sucesso;
            }

            var viagemEvento = _viagemEventoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                .Select(x => new
                {
                    x.Status,
                    x.Token
                }).First();

            return new DesbloquearEventoResponseModel
            {
                Sucesso = retorno.Sucesso && viagemEvento.Status == EStatusViagemEvento.Aberto &&
                          statusOperacaoCartao != ERetornoOperacaoCartao.Erro,
                Mensagem = retorno.Mensagem,
                IdViagem = bloqueioEvento.IdViagem ?? 0,
                NumeroControle = bloqueioEvento.NumeroControle,
                IdViagemEvento = bloqueioEvento.ViagemEventos[0].IdViagemEvento ?? 0,
                NumeroControleEvento = bloqueioEvento.ViagemEventos[0].NumeroControle,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = statusOperacaoCartao,
                    Mensagem = mensagemProcessamentoWs
                },
                Token = viagemEvento.Token
            };
        }
    }
}
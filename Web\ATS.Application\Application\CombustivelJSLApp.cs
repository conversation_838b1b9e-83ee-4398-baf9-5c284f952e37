using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class CombustivelJSLApp : AppBase, ICombustivelJSLApp
    {
        private readonly ICombustivelJSLService _combustivelJSLService;

        public CombustivelJSLApp(ICombustivelJSLService combustivelJslService)
        {
            _combustivelJSLService = combustivelJslService;
        }

        public IQueryable<CombustivelJSL> GetQuery(int idcombustivel)
        {
            return _combustivelJSLService.GetQuery(idcombustivel);
        }

        public bool SincronizaATS(int idcombustivel)
        {
            return _combustivelJSLService.SincronizaATS(idcombustivel);
        }

        public bool CombustivelExistenteEstabelecimento(int idCombustivelJSL, int idestabelecimento)
        {
            return _combustivelJSLService.CombustivelExistenteEstabelecimento(idCombustivelJSL, idestabelecimento);
        }

        public ValidationResult Integrar(int idcombustivel, int idproduto, int idestabelecimento)
        {
            return _combustivelJSLService.Integrar(idcombustivel, idproduto, idestabelecimento);
        }

        public IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByCombustivel(int idcombustivel)
        {
            return _combustivelJSLService.GetQueryEstabelecimentoByCombustivel(idcombustivel);
        }

        public IQueryable<CombustivelJSLEstabelecimentoBase> GetQueryEstabelecimentoByEstabelecimento(int idestabelecimento)
        {
            return _combustivelJSLService.GetQueryEstabelecimentoByEstabelecimento(idestabelecimento);
        }
    }
}
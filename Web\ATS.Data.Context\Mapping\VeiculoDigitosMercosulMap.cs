using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class VeiculoDigitosMercosulMap: EntityTypeConfiguration<VeiculoDigitosMercosul>
    {
        public VeiculoDigitosMercosulMap()
        {
            ToTable("VEICULO_DIGITOS_MERCOSUL");

            HasKey(t => t.NumeroOrigem);

            Property(t => t.NumeroOrigem).HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            Property(t => t.LetraDestino).IsRequired().HasMaxLength(1);
        }
    }
}
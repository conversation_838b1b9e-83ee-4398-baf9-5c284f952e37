using System;
using ATS.Domain.Entities.Common;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Estabelecimento : EstabelecimentoBaseClass
    {
        public int IdEmpresa { get; set; }
        public bool Credenciado { get; set; }
        public double TaxaAntecipacao { get; set; } = 0;
        public int? IdEstabelecimentoBase { get; set; }
        public bool Associacao { get; set; } = false;
        public string RazaoSocial { get; set; }
        public bool PagamentoAntecipado { get; set; } = false;
        public bool? PontoReferencia { get; set; } = false;

        /// <summary>
        /// Indica se a associação pode ou não liberar protocolos de um estabelecimento para a empresa
        /// </summary>
        public bool LiberaProtocolos { get; set; } = false;
        /// <summary>
        /// Indica se a associação pode ou não alterar documentação e peso de chegada de um estabelecimento para a empresa
        /// </summary>
        public bool PermiteAlterarDocumentosPesoChegada { get; set; }

        /// <summary>
        /// Parâmetro que obriga o anexo de documentos na tela de pagamento de frete
        /// </summary>
        public bool ObrigaDocumentosPagamento { get; set; } = true;

        public TimeSpan? HoraInicialSemValidarChave { get; set; }

        public TimeSpan? HoraFinalSemValidarChave { get; set; }

        public bool RealizaPagamentoComCheque { get; set; }
        
        /// <summary>
        /// Código da filial do ATS para o Maxy's gerar o processo de caixa de pagamento de cheque pelas filiais (Recurso utiliado para filiais cadastradas como posto).
        /// </summary>
        public int? IdFilialProcessoCaixaTms { get; set; }

        #region Relacionamentos

        public virtual Empresa Empresa { get; set; }
        public virtual TipoEstabelecimento TipoEstabelecimento { get; set; }
        public virtual Pais Pais { get; set; }
        public virtual Estado Estado { get; set; }
        public virtual Cidade Cidade { get; set; }
        public virtual EstabelecimentoBase EstabelecimentoBase { get; set; }

        #endregion

        #region Navegação inversa

        public ICollection<EstabelecimentoProduto> EstabelecimentoProdutos { get; set; }
        public virtual ICollection<Credenciamento> Credenciamentos { get; set; }
        public virtual ICollection<ViagemEstabelecimento> ViagemEstabelecimentos { get; set; }
        /// <summary>
        /// Associações vinculadas ao estabelecimento
        /// </summary>
        public virtual ICollection<EstabelecimentoAssociacao> EstabelecimentoAssociacoesAssociacao { get; set; }

        /// <summary>
        /// Associados ao estabelecimento (sendo este uma associação)
        /// </summary>
        public virtual ICollection<EstabelecimentoAssociacao> EstabelecimentoAssociacoesEstabelecimento { get; set; }

        public virtual ICollection<Protocolo> ProtocolosDestinatario { get; set; }

        /// <summary>
        /// Contas bancarias do estabelecimento
        /// </summary>
        public virtual ICollection<EstabelecimentoContaBancaria> EstabelecimentoContasBancarias { get; set; }

        public virtual ICollection<RotaEstabelecimento> RotaEstabelecimento { get; set; }
        
        #endregion
    }
}

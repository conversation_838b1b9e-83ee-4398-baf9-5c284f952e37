﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service.Common;
using System.Linq;

namespace ATS.Domain.Service
{
    /// <summary>
    ///     Classe responsável por validar as regras de negócio e persistir os dados da tabela de Veículo Conjunto.
    /// </summary>
    public class VeiculoConjuntoService : ServiceBase
    {
        private readonly IVeiculoConjuntoRepository _veiculoConjuntoRepository;

        public VeiculoConjuntoService(IVeiculoConjuntoRepository veiculoConjuntoRepository)
        {
            _veiculoConjuntoRepository = veiculoConjuntoRepository;
        }

        /// <summary>
        /// Retorna a lista de conjuntos a partir do código do veículo
        /// </summary>
        /// <param name="idVeiculo"></param>
        /// <returns></returns>
        public IQueryable<VeiculoConjunto> GetByIdVeiculoWithAllChilds(int idVeiculo)
        {
            return _veiculoConjuntoRepository.GetByIdVeiculoWithAllChilds(idVeiculo);
        }
    }
}
﻿using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class ConsultarStatusVeiculoSemPararDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get => _mensagem;
            set => _mensagem = value?.Trim();
        }
        private string _mensagem;
        public bool VeiculoPossuiTagSemParar { get; set; }
        public bool VeiculoExistenteSemParar { get; set; }
        public EStatusVeiculoSemParar VeiculoStatusSemParar { get; set; } = EStatusVeiculoSemParar.Indisponivel;
    }
}
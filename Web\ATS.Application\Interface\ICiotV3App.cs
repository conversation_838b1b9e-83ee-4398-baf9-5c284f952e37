using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Application.Interface
{
    public interface ICiotV3App : IBaseApp<ICiotV3Service>
    {
        DeclararCiotResult DeclararCiotFromIntegracaoViagem(Viagem viagem, string cnpjEmpresa, bool habilitarDeclaracaoCiot, bool retificarCiot, bool? gerarCiotTacAgregado);

        AbrirContratoCiotAgregadoResultModel AbrirContratoCiotAgregado(ContratoAberturaModel requestModel,
            int idEmpresa);

        DeclararCiotResult EncerrarCiotAgregado(int idEmpresa, EncerrarContratoAgregadoModel requestModel);

        EncerradoCanceladoCiotResult EncerrarCancelarCiotAgregadoExpirados();

        RetificarContratoAgregadoResponseDto RetificarCiotAgregadoPorTela(int idEmpresa, RetificarContratoAgregadoModel requestModel);

        CancelarCiotAgregadoViagensResult CancelarCiotAgregado(int idEmpresa, int idContrato);

        object ConsultarContratosAgregado(int take, int page, OrderFilters order, List<QueryFilters> filters,
            int? idEmpresa);

        CarregarContratoAgregadoModel CarregarContratoAgregado(int idContratoAgregado, int idEmpresa);

        CancelarCiotResult CancelarCiot(Viagem viagem);

        CancelarCiotResult CancelarCiotAgregado(CancelarContratoAgregadoModel requestModel, int idEmpresa);

        /// <summary>
        ///     Resolve o link para imprimir comprovante de contrato.
        /// </summary>
        string LinkComprovanteContrato(ImprimirComprovanteContratoAgregadoModel requestModel);

        DeclararCiotResult GetCiotResult(int idViagem, int idEmpresa);

        ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request);

        ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request);

        ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario, string cnpjEmpresa);
        
        ValidationResult NotificarCiotContingencia(NotificarCiotContingencia notificarCiotContingencia);

        DeclararCiotResult EncerrarCiotPadrao(Viagem viagem, DeclaracaoCiot declaracaoCiot);
        DeclaracaoCiot ObterDeclaracaoCiot(int idViagem);
        ETipoDeclaracao ObterTipoDeclaracao(int viagemId);
        ValidationResult Encerrar(string requestCnpjEmpresa, string requestCiot);
        bool AlgumContratoAberto(int idProprietario, int idEmpresa);
        List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa);
        void Avisar();
        bool IsDeclaracaoTacAgregado(string placa, int idEmpresa, bool habilitarContratoCiotAgregado);
        AtualizarCiotResponse AtualizarCiot(int idDeclaracaoCiot);
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class LayoutCartaoItemMap : EntityTypeConfiguration<LayoutCartaoItem>
    {
        public LayoutCartaoItemMap()
        {
            ToTable("LAYOUT_CARTAO_ITEM");
            HasKey(x => new { x.IdLayout, x.Key } );

            Property(x => x.IdLayout)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None)
                .IsRequired();

            Property(x => x.Key)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None)
                .IsRequired();

            Property(x => x.ItemCartao)
                .IsRequired();

            HasRequired(c => c.LayoutCartao)
                .WithMany(c => c.Itens)
                .HasForeignKey(c => c.IdLayout);
        }
    }
}
﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioHorarioCheckInMap : EntityTypeConfiguration<UsuarioHorarioCheckIn>
    {
        public UsuarioHorarioCheckInMap()
        {
            ToTable("USUARIO_HORARIO_CHECKIN");

            HasKey(t => new { t.IdUsuario, t.IdHorario });

            Property(t => t.IdUsuario)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdHorario)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Ho<PERSON>io)
                .IsRequired();

            HasRequired(t => t.Usuario)
                .WithMany(t => t.HorariosCheckIn)
                .HasForeignKey(d => d.IdUsuario);
        }
    }
}
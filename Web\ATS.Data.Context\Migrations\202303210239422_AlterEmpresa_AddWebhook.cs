﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AlterEmpresa_AddWebhook : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.EMPRESA", "webhookendpoint", c => c.String(maxLength: 255, unicode: false));
            AddColumn("dbo.EMPRESA", "webhookheaders", c => c.String(maxLength: 1000, unicode: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.EMPRESA", "webhookheaders");
            DropColumn("dbo.EMPRESA", "webhookendpoint");
        }
    }
}

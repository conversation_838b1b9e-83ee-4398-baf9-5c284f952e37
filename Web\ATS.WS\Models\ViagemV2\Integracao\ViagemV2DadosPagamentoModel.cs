using System;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2DadosPagamentoModel
    {
        public EViagemFormaPagamento FormaPagamento { get; set; } = EViagemFormaPagamento.Outros;

        public string CodigoBacen { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }
        public ETipoConta TipoConta { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (!Enum.IsDefined(typeof(EViagemFormaPagamento), FormaPagamento))
                return new ValidationResult().Add("Valor inválido para a forma de pagamento.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(CodigoBacen))
                if (CodigoBacen.Length > 5)
                    return new ValidationResult().Add("Código Bacen não pode ter mais que 5 caracteres.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(Agencia))
                if (Agencia.Length > 6)
                    return new ValidationResult().Add("Agência não pode conter mais que 6 caracteres", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(Conta))
                if (Agencia.Length > 20)
                    return new ValidationResult().Add("Conta não pode conter mais que 20 caracteres", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
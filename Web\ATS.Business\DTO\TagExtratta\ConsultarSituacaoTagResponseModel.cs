﻿using Newtonsoft.Json;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.DTO.TagExtratta
{
    public class ConsultarSituacaoTagResponseModel
    {
        [JsonIgnore]
        public bool Sucesso { get; set; }
        [JsonIgnore]
        public string MensagemRetorno { get; set; }
        public ConsultarSituacaoTagPlacaResponseFornecedor? Fornecedor { get; set; }
        public bool TagAtiva { get; set; }
        public string MensagemStatusTag { get; set; }
        public int? CodigoStatusTag { get; set; }
    }
}
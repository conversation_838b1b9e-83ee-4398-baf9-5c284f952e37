﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    public class CarregarContratoAgregadoModel
    {
        public int IdContratoCiotAgregado { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdProprietario { get; set; }
        public DateTime? DataInicio { get; set; }
        public DateTime? DataFinal { get; set; }
        public int? IdDeclaracaoCiot { get; set; }
        public DateTime? DataCadastro { get; set; }
        public int? IdUsuario { get; set; }
        public EStatusContratoAgregado Status { get; set; }
        public string MensagemDeclaracaoCiot { get; set; }
        public string CIOT { get; set; }
        public int? TotalViagens { get; set; }

        public ProprietarioModelAgregado Proprietario { get; set; }
        public DeclaracaoCiotModelAgregado DeclaracaoCiot { get; set; }
        public ICollection<ViagemModelAgregado> Viagens { get; set; }
        public ICollection<VeiculoModelAgregado> Veiculos { get; set; }
        public ValoresViagemModel Valores { get; set; }
    }

    public class DeclaracaoCiotModelAgregado
    {
        public ETipoDeclaracao TipoDeclaracao { get; set; }
        public EStatusDeclaracaoCiot Status { get; set; }
        public string Ciot { get; set; }
        public string Verificador { get; set; }
        public string Senha { get; set; }
    }

    public class ProprietarioModelAgregado
    {
        public int IdProprietario { get; set; }
        public string CNPJCPF { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string IE { get; set; }
        public string RNTRC { get; set; }
    }
}
﻿using System;
using System.Globalization;

namespace ATS.CrossCutting.IoC.Utils
{
    public static class ConvertUtils
    {
        public static bool ToBoolSafe(this object value, bool defaultValue = false)
        {
            if (value == null)
                return defaultValue;
            try
            {
                return Convert.ToBoolean(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        public static int? ToIntNullable(this object value, int? defaultValue = null)
        {
            return ToIntSafe(value, defaultValue);
        }
        
        public static int? ToIntSafe(this object value, int? defaultValue = null)
        {
            if (value == null)
                return defaultValue;

            try
            {
                return Convert.ToInt32(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        public static decimal? ToDecimalSafe(this object value, IFormatProvider provider, decimal? defaultValue = null)
        {
            if (value == null)
                return defaultValue;

            try
            {
                return Convert.ToDecimal(value, provider);
            }
            catch
            {
                return defaultValue;
            }
        }

        public static decimal? ToDecimalSafe(this object value, decimal? defaultValue = null)
        {
            return ToDecimalSafe(value, CultureInfo.InvariantCulture, defaultValue);
        }

        public static decimal ToDecimal(this object value)
        {
            var valor = ToDecimalSafe(value, CultureInfo.InvariantCulture, 0);
            return valor ?? 0;
        }
        
        public static int ToInt(this object value)
        {
            return Convert.ToInt32(value);
        }
        
        public static DateTime? ToDateTimeSafe(this object value, DateTime? defaultValue = null)
        {
            if (value == null)
                return defaultValue;

            try
            {
                return Convert.ToDateTime(value);
            }
            catch
            {
                return defaultValue;
            }
        }
        
        public static short? ToShortSafe(this object value, short? defaultValue = null)
        {
            if (value == null)
                return defaultValue;

            try
            {
                return Convert.ToInt16(value);
            }
            catch
            {
                return defaultValue;
            }
        }
        
        public static double? toDoubleSafe(this object value, double? defaultValue = null)
        {
            if (value == null)
                return defaultValue;

            try
            {
                return Convert.ToDouble(value);
            }
            catch
            {
                return defaultValue;
            }
        }

    }
}

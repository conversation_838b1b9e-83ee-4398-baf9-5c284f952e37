﻿namespace ATS.CrossCutting.Reports.Proprietario.RelatorioListaProprietarios
{
    public class RelatorioProprietarioDataType
    {
        public string IdProprietario { get; set; }

        public string CpfCnpj { get; set; }

        public string Empresa { get; set; }

        public string RazaoSocial { get; set; }

        public string NomeFantasia { get; set; }

        public string Rg { get; set; }

        public string RgOrgaoExpedidor { get; set; }

        public string Ie { get; set; }

        public string Rntrc { get; set; }

        public string StatusIntegracao { get; set; }

        public string TipoContrato { get; set; }

        public string Ativo { get; set; }

        public string Endereco { get; set; }

        public string Telefone { get; set; }
        public string <PERSON>c<PERSON>g<PERSON><PERSON> { get; set; }
    }
}

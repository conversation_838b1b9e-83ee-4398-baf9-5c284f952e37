﻿using System;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using System.Collections.Generic;
using System.Device.Location;

namespace ATS.Domain.Helpers
{
    public class MapHelper
    {
        public ERegiaoBrasil GetRegiaoFromUf(string uf)
        {
            switch (uf.ToUpper())
            {
                case "RR":
                case "AC":
                case "AP":
                case "AM":
                case "RO":
                case "PA":
                case "TO":
                    return ERegiaoBrasil.Norte;
                case "MT":
                case "MS":
                case "GO":
                    return ERegiaoBrasil.CentroOeste;
                case "PR":
                case "SC":
                case "RS":
                    return ERegiaoBrasil.Sul;
                case "SP":
                case "RJ":
                case "MG":
                case "ES":
                    return ERegiaoBrasil.Sudeste;
                case "BA":
                case "SE":
                case "AL":
                case "PI":
                case "MA":
                case "CE":
                case "RN":
                case "PE":
                case "PB":
                    return ERegiaoBrasil.Nordeste;
                default:
                    return ERegiaoBrasil.NaoEncontrada;
            }
        }


        /// <summary>
        /// Verifica se uma determinada posição está dentro do raio
        /// </summary>
        /// <param name=""></param>
        /// <param name="latitudePonto">Latitude atual</param>
        /// <param name="longitudePonto">Longitude atual</param>
        /// <param name="latitudeSelecionada">Latitude selecionada</param>
        /// <param name="longitudeSelecionada">Longitude selecionada</param>
        /// <param name="raio">Raio para o cálculo em metros</param>
        /// <returns></returns>
        public bool VerificarRaio(double latitudePonto, double longitudePonto, double latitudeSelecionada, double longitudeSelecionada, int raio)
        {
            var minhaPosicao = new GeoCoordinate(latitudePonto, longitudePonto);
            var posicaoSelecionada = new GeoCoordinate(latitudeSelecionada, longitudeSelecionada);
            var distanciaDoisPontos = minhaPosicao.GetDistanceTo(posicaoSelecionada);

            return distanciaDoisPontos < raio;
        }
    }
}

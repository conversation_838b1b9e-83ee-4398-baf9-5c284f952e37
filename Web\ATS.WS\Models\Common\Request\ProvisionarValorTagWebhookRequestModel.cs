﻿using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.Common.Request
{
    public class ProvisionarValorTagWebhookRequestModel
    {
        public string Token { get; set; }
        public long? EventoSaldoTagId { get; set; }
        public decimal? Valor { get; set; }
        public int? IdEmpresa { get; set; }
        public string CnpjEmpresa { get; set; }
        public string Praca { get; set; }
        public string Placa { get; set; }
        public int? ViagemId { get; set; }
        public string Recibo { get; set; }
        public FornecedorEnum? Fornecedor { get; set; }

    public BusinessResult ValidRequest()
      {
          if(string.IsNullOrWhiteSpace(Placa))
              return BusinessResult.Error("Placa é obrigatório!");
          
          if(string.IsNullOrWhiteSpace(CnpjEmpresa))
              return BusinessResult.Error("CnpjEmpresa é obrigatório!");
          
          if(!IdEmpresa.HasValue)
              return BusinessResult.Error("IdEmpresa é obrigatório!");
          
          if(!Valor.HasValue)
              return BusinessResult.Error("Valor é obrigatório!");
          
          if(!EventoSaldoTagId.HasValue)
              return BusinessResult.Error("EventoSaldoTagId é obrigatório!");
          
          if(!Fornecedor.HasValue)
              return BusinessResult.Error("Fornecedor é obrigatório!");
          
          return BusinessResult.Valid();
      }
    }
}
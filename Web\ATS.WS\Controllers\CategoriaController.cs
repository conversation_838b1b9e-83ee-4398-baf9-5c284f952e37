using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.WS.Controllers.Base;
using System;
using System.Web.Mvc;

namespace ATS.WS.Controllers
{
    public class CategoriaController : BaseController
    {
        private readonly ICategoriaApp _app;

        public CategoriaController(BaseControllerArgs baseArgs, ICategoriaApp app) : base(baseArgs)
        {
            _app = app;
        }

        [HttpGet]
        public JsonResult GetAll()
        {
            try
            {
                var retorno = _app.GetAll(true);

                if (!retorno.Success)
                    return Responde(new CategoriaGetAllResponseDTO
                    {
                        Sucesso = false,
                        Mensagem = retorno.ToString()
                    });

                return Responde(new CategoriaGetAllResponseDTO
                {
                    Sucesso = true,
                    Objeto = retorno.Value
                });
            }
            catch (Exception ex)
            {
                var message = ex.ToString();
                var datetime = DateTime.Now;

                Logger.Error("Ocorreu um erro interno:" +
                                "Portal Extratta:" +
                                "Message: {message}\n" +
                                "Date:{datetime}\n",
                                message,
                                datetime);

                return Responde(new CategoriaGetAllResponseDTO
                {
                    Sucesso = false,
                    Mensagem = $"Ocorreu um erro interno: {message}"
                });
            }
        }
    }
}
﻿using ATS.Data.Context.Trigger;
using System;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Threading.Tasks;

namespace ATS.Data.Context.Interface
{
    public interface IDbContext : ITriggerContext
    {
        int? CurrentUserId { get; }
        bool IsDead { get; set; }
        DbSet<TEntity> Set<TEntity>() where TEntity : class;
        DbEntityEntry<TEntity> Entry<TEntity>(TEntity entity) where TEntity : class;
        int SaveChanges();
        Task<int> SaveChangesAsync();
        void Dispose();
    }
}
﻿using ATS.Domain.Enum;
using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class EstadoModel
    {
        public int IdEstado { get; set; }
        public int IdPais { get; set; }
        public string Nome { get; set; }
        public string Sigla { get; set; }
        public ERegiaoBrasil Regiao { get; set; } = ERegiaoBrasil.NaoEncontrada;

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IBGE { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual PaisModel Pais { get; set; }
    }
}
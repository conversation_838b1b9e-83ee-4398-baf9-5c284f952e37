using System.Collections.Generic;
using System.Web;
using ATS.Application.Interface.Common;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ICargaAvulsaLoteApp : IBaseApp<ICargaAvulsaService>
    {
        CargaAvulsaResultadoValidacaoPlanilha ValidarCargaAvulsaPlanilha(HttpPostedFileBase file, int idEmpresa);
        ValidationResult CadastrarCargaAvulsaPlanilha(CargaAvulsaValidacaoPlanilha cargaAvulsa);
        void RealizarCargasAvulsaPendentes();
        ValidationResult<EValidationCargaAvulsa> ValidarAlcadasLimites(CargaAvulsaValidacaoAlcadasLimitesPlanilha request);
    }
}
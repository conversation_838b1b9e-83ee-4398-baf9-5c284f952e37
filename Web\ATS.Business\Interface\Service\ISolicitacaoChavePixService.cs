﻿using System.Collections.Generic;
using System.Web;
using ATS.Domain.DTO.Pix;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ISolicitacaoChavePixService : IService<TransacaoPix>
    {
        BusinessResult<CadastrarChavePixProprietarioResponse> CadastrarChaveProprietario(CadastrarChavePixProprietarioRequest request);
        BusinessResult<CadastrarChavePixProprietarioResponse> CadastrarPlanilhaChavePixProprietario(CadastrarPlanilhaChavePixProprietarioRequest request);
        BusinessResult<SolicitacaoChavePixGridResponse> ConsultarGridSolicitacaoChavePixProprietario(int page, int take, List<QueryFilters> filters, OrderFilters order);
        BusinessResult AlterarStatusSolicitacaoChavePixProprietario(SolicitacaoChavePixAlterarStatusAppRequest request);
        BusinessResult<ValidarPlanilhaSolicitacaoChavePixResponse> ValidarPlanilha(HttpPostedFileBase file);

    }
}
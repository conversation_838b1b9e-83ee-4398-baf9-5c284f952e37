﻿namespace ATS.WS.Models.Webservice.Request.Empresa
{
    public class ContaBancariaRequest
    {
        public string NomeConta { get; set; }
        public string CodigoBacenBanco { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public int? DigitoConta { get; set; }
        public EContasBancariasTipoContaRequest? TipoConta { get; set; }
    }

    public enum EContasBancariasTipoContaRequest
    {
        [System.Runtime.Serialization.EnumMember(Value = "Indefinido")] Indefinido = 0,


        [System.Runtime.Serialization.EnumMember(Value = "Corrente")] Corrente = 1,


        [System.Runtime.Serialization.EnumMember(Value = "Poupanca")] Poupanca = 2,

    }

}
﻿using System;
using System.Diagnostics;
using System.Linq.Dynamic;
using System.Threading.Tasks;
using System.Web;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.WS.Services.ViagemServices;
using Newtonsoft.Json;
using NLog;
using Quartz;

namespace ATS.WS.Jobs
{
    /// <summary>
    /// Baixar parcelas agendadas que atingiram a data programada para as empresas com a flag <see cref="Empresa.HabilitarAgendamentoPagamentoFrete"/> ativa.
    /// </summary>
    public class ProcessarPagamentoAgendadoJob : IJob
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly BaixaEventoViagem _baixaEventoViagem;

        public ProcessarPagamentoAgendadoJob(IViagemEventoRepository viagemEventoRepository, BaixaEventoViagem baixaEventoViagem)
        {
            _viagemEventoRepository = viagemEventoRepository;
            _baixaEventoViagem = baixaEventoViagem;
        }

        public Task Execute(IJobExecutionContext context)
        {
            var watch = Stopwatch.StartNew();
            _logger.Info("Iniciando pagamento de eventos agendado");
            var total = 0;
            try
            {
                var eventos = _viagemEventoRepository.GetIdsParaProcessarAgendamentoDePagamento(
                    dataAgendamentoInicio: DateTime.Today.AddDays(-7),
                    dataAgendamentoFim: DateTime.Today.EndOfDay());
                total = eventos.Count();
                _logger.Info($"{total} pagamentos para realizar");
                foreach (var evento in eventos)
                {
                    if (evento.DataLancamento == null || evento.DataLancamento > DateTime.Now.AddMinutes(-5))
                    {
                        // Tratamento para não concorrer com possível insert em execução nos testes.
                        // Em produção teóricamente nunca deve entrar aqui pois só é possível agendar viagem para o próximo dia.
                        _logger.Info($"IdViagemEvento {evento.IdViagemEvento} ignorado neste loop. Motivo: Registro inserido a menos de 5 minutos.");
                        continue;
                    }
                    _logger.Info($"Processando pagamento agendado IdViagemEvento: {evento.IdViagemEvento}");
                    try
                    {
                        var command = new Models.Common.Request.BaixarEventoRequestModel
                        {
                            DocumentoUsuarioAudit = Consts.DocumentoIntegracaoMs_ProcessarPagamentoAgendadoJob,
                            NomeUsuarioAudit= nameof(ProcessarPagamentoAgendadoJob),
                            IdViagem = evento.IdViagem,
                            IdViagemEvento = evento.IdViagemEvento,
                            HabilitarPagamentoCartao = true,
                            CNPJEmpresa = evento.CnpjEmpresa,
                            DataAtualizacao = DateTime.Now
                        };
                        _logger.Info($"Pagar IdViagemEvento {evento.IdViagemEvento} command => " + JsonConvert.SerializeObject(command));
                        var validacao = command.Valida();
                        if (!string.IsNullOrWhiteSpace(validacao))
                            _logger.Info($"Pagar IdViagemEvento {evento.IdViagemEvento} result  => " + validacao);
                        else
                        {
                            var result = _baixaEventoViagem.BaixarEvento(command);
                            _logger.Info($"Pagar IdViagemEvento {evento.IdViagemEvento} result  => " + JsonConvert.SerializeObject(result));
                        }
                    }
                    catch(Exception ex)
                    {
                        _logger.Error(ex, $"Erro ao processar pagamento agendado (IdViagemEvento: {evento.IdViagemEvento})");
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Fatal(e, "Erro ao processar pagamento de eventos agendado");
            }
            watch.Stop();
            _logger.Info($"Pagamento de eventos agendados concluído em {watch.Elapsed}. Total: {total}");
            return Task.CompletedTask;
        }
    }
}
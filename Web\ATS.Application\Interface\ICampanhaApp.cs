﻿using ATS.Application.Interface.Common;
using ATS.Domain.DTO.Campanha;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ICampanhaApp : IAppBase
    {
        CampanhaConsultaResponse ConsultarAtual();
        CampanhaGridResponse ConsultarCampanhas();
        ValidationResult Responder(ResponderCampanhaRequest request);
        ValidationResult Integrar(IntegrarCampanhaRequest request);
        AlterarStatusCampanhaResponse AlterarStatus(AlterarStatusCampanhaRequest request);
    }
}
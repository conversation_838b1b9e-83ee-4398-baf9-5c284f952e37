﻿using System.Collections.Generic;
using ATS.MongoDB.Context.Entities;
using MongoDB.Bson;

namespace ATS.Domain.Interface.Service
{
    public interface IDataMediaServerService
    {
        ObjectId Add(int type, string base64Data, string fileName, string mimeType = null);
        Media GetMedia(string id);
        void DeleteByToken(string token);
        object VisualizarMedia(string token);
        string GetIconeTipoEstabelecimentoCacheable(string mediaId);
        List<Media> GetListMedia(List<string> listId);
    }
}
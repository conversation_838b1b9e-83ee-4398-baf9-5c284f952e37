﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class UsuarioPermissaoCartaoApp : AppBase, IUsuarioPermissaoCartaoApp
    {
        private readonly IUsuarioPermissaoCartaoService _usuarioPermissaoCartaoService;

        public UsuarioPermissaoCartaoApp(IUsuarioPermissaoCartaoService usuarioPermissaoCartaoService)
        {
            _usuarioPermissaoCartaoService = usuarioPermissaoCartaoService;
        }

        public ValidationResult Integrar(int idUsuario, EBloqueioCartaoTipo idBloqueioGestorTipo,bool bloqueioFinanceiro)
        {
            return _usuarioPermissaoCartaoService.Integrar(idUsuario, idBloqueioGestorTipo, bloqueioFinanceiro);
        }

        public UsuarioPermissaoCartao GetParametroPermissaoCartao(int idUsuario, EBloqueioCartaoTipo idBloqueioGestorTipo)
        {
            return _usuarioPermissaoCartaoService.GetParametroPermissaoCartao(idUsuario, idBloqueioGestorTipo);
        }

        public bool PossuiPermissao(int idUsuario, EBloqueioCartaoTipo bloqueioFinanceiroTipo)
        {
            return _usuarioPermissaoCartaoService.PossuiPermissao(idUsuario, bloqueioFinanceiroTipo);
        }
    }
}
﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class EstabelecimentoBaseContaBancariaApp : AppBase, IEstabelecimentoBaseContaBancariaApp
    {
        private readonly IEstabelecimentoBaseContaBancariaService _service;

        public EstabelecimentoBaseContaBancariaApp(IEstabelecimentoBaseContaBancariaService service)
        {
            _service = service;
        }

        public List<BancosFebrabanModel> GetBancosFebraban()
        {
            return _service.GetBancosFebraban();
        }
    }
}

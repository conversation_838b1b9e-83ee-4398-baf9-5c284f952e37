using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class FornecedorCnpjPedagioRepository : Repository<FornecedorCnpjPedagio>, IFornecedorCnpjPedagioRepository
    {
        public string GetCnpjFornecedor(int idFornecedor)
        {
            var cnpj = Where(c => c.IdFornecedor == idFornecedor);
            return cnpj.FirstOrDefault()?.Cnpj;
        }

        public string GetCnpjFornecedor(FornecedorEnum fornecedor) => GetCnpjFornecedor((int)fornecedor);

        public FornecedorCnpjPedagioRepository(AtsContext context) : base(context)
        {
        }
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class BannerMap : EntityTypeConfiguration<Banner>
    {
        public BannerMap()
        {
            ToTable("BANNER");

            HasKey(t => t.Id);

            Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.Descricao).IsOptional().HasMaxLength(600);
            Property(t => t.Titulo).IsOptional().HasMaxLength(600);
            Property(t => t.Link).IsOptional().HasMaxLength(600);
            Property(t => t.IdImagemMongo).IsOptional().HasMaxLength(600);
            Property(t => t.DataCadastro).HasColumnType("datetime2");
            Property(t => t.DataDesativacao).IsOptional().HasColumnType("datetime2");
            Property(t => t.DataAtivacao).IsOptional().HasColumnType("datetime2");
            Property(t => t.IdUsuarioAtivacao).HasColumnName("idusuarioativacao").IsOptional().HasColumnType("int");
            Property(t => t.IdUsuarioCadastro).HasColumnName("idusuariocadastro").IsOptional().HasColumnType("int");
            Property(t => t.IdUsuarioDesativacao).HasColumnName("idusuariodesativacao").IsOptional().HasColumnType("int");

            HasMany(c => c.BannerUsuarios)
                .WithRequired(c => c.Banner)
                .HasForeignKey(c => c.IdBanner);

            HasOptional(c => c.UsuarioCadastro)
                .WithMany(c => c.BannersCadastrados)
                .HasForeignKey(c => c.IdUsuarioCadastro);

            HasOptional(c => c.UsuarioDesativacao)
                .WithMany(c => c.BannersDesativados)
                .HasForeignKey(c => c.IdUsuarioDesativacao);

            HasOptional(c => c.UsuarioAtivacao)
                .WithMany(c => c.BannersAtivados)
                .HasForeignKey(c => c.IdUsuarioAtivacao);
        }
    }
}
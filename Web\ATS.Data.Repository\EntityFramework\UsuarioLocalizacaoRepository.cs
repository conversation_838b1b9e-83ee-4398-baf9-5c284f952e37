﻿using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class LocalizacaoUsuarioPortalRepository : Repository<LocalizacaoUsuarioPortal>, ILocalizacaoUsuarioPortalRepository
    {
        public LocalizacaoUsuarioPortalRepository(AtsContext context) : base(context)
        {
        }

        public bool AnyByUsuarioLatLong(int idUsuario, decimal latitude, decimal longitude)
        {
            return Where(c => c.IdUsuario == idUsuario && c.Latitude == latitude && c.Longitude == longitude).Any();
        }

        public bool AnyByUsuarioIp(int idUsuario, string ip)
        {
            return Where(c => c.IdUsuario == idUsuario && c.Ip == ip).Any();
        }
    }
}
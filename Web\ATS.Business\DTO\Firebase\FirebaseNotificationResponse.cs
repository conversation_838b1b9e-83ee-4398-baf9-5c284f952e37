using System.Collections.Generic;
using Newtonsoft.Json;

namespace ATS.Domain.DTO.Firebase
{
    public class FirebaseNotificationResponse
    {
        [JsonProperty("error")]
        public FireBaseNotificationError Error { get; set; }
        
        [JsonProperty("name")]
        public string Name { get; set; }
    }
    
    public class FireBaseNotificationError
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("details")]
        public List<FireBaseNotificationErrorDetail> Details { get; set; }
    }
    
    public class FireBaseNotificationErrorDetail
    {
        [JsonProperty("@type")]
        public string Type { get; set; }

        [JsonProperty("errorCode")]
        public string ErrorCode { get; set; }
    }
}
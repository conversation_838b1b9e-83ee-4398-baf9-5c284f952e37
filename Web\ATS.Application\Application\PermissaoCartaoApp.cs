﻿using System;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.PermissaoCartao;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class PermissaoCartaoApp : IPermissaoCartaoApp
    {
        private IPermissaoCartaoService _service;
        private IUsuarioPermissaoCartaoApp _usuarioPermissao;
        private IUserIdentity _userIdentity;
        public PermissaoCartaoApp(IPermissaoCartaoService service, IUsuarioPermissaoCartaoApp usuarioPermissao, IUserIdentity userIdentity)
        {
            _service = service;
            _usuarioPermissao = usuarioPermissao;
            _userIdentity = userIdentity;
        }

        public BusinessResult SaqueHabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.Saque);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.SaqueHabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CompraFisicaDesabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.CompraEstabelecimento);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.CompraFisicaDesabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CompraFisicaHabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.CompraEstabelecimento);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.CompraFisicaHabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CompraInternacionalDesabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.CompraInternacional);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.CompraInternacionalDesabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CompraInternacionalHabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.CompraInternacional);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.CompraInternacionalHabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CompraOnlineDesabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.CompraOnline);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.CompraOnlineDesabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult CompraOnlineHabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.CompraOnline);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.CompraOnlineHabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult SaqueDesabilitar(int identificador, int produto)
        {
            try
            {
                var validacaoPermissaoUsuario = UsuarioPossuiPermissao(EBloqueioCartaoTipo.Saque);
                
                if(!validacaoPermissaoUsuario.Success)      
                    return BusinessResult.Error(validacaoPermissaoUsuario.Messages);
                
                var result = _service.SaqueDesabilitar(identificador, produto);
                    
                if(!result.Success)      
                    return BusinessResult.Error(result.Messages);
                
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }

        public BusinessResult<PermissaoCartaoModelResponse> ConsultarPermissoes(int identificador, int produto)
        {
            try
            {
                return _service.ConsultarPermissoes(identificador,produto);
            }
            catch (Exception e)
            {
                return BusinessResult<PermissaoCartaoModelResponse>.Error(e.Message);
            }
        }

        private BusinessResult UsuarioPossuiPermissao(EBloqueioCartaoTipo tipo)
        {
            try
            {
                var permissao = _usuarioPermissao.PossuiPermissao(_userIdentity.IdUsuario, tipo);
                
                if(!permissao)
                    return BusinessResult.Error("Usuário não possui permissão para realizar esta ação.");
                    
                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                return BusinessResult.Error(e.Message);
            }
        }
    }
}
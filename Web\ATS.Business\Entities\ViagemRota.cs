using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class ViagemRota
    {
        public int IdViagem { get; set; }
        public int IdViagemRota { get; set; }
        public FornecedorEnum FornecedorPedagio { get; set; }
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public Guid? IdentificadorHistorico { get; set; }
        public virtual List<ViagemRotaPonto> Pontos { get; set; }
    }
}
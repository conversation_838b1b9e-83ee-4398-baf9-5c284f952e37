﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemEventoMap : EntityTypeConfiguration<ViagemEvento>
    {
        public ViagemEventoMap()
        {
            ToTable("VIAGEM_EVENTO");

            HasKey(t => t.IdViagemEvento);

            Property(t => t.ValorPagamento)
                .HasPrecision(10, 2);

            Property(t => t.ValorTotalPagamento)
                .HasPrecision(10, 2);
            
            Property(t => t.ValorTotalPagamentoCalculado)
                .HasPrecision(10, 2);

            // Impostos
            Property(x => x.IRRPF)
                .HasPrecision(10, 2);
            Property(x => x.INSS)
                .HasPrecision(10, 2);
            Property(x => x.SESTSENAT)
                .HasPrecision(10, 2);
            //

            Property(x => x.Instrucao)
                .HasMaxLength(700);

            Property(t => t.NumeroRecibo)
                .HasMaxLength(20);
            
            Property(t => t.NumeroControle)
                .IsOptional()
                .HasMaxLength(300);

            HasRequired(t => t.Viagem)
                .WithMany(t => t.ViagemEventos)
                .HasForeignKey(x => new { x.IdViagem, x.IdEmpresa });

            HasOptional(t => t.UsuarioLibSemChave)
                .WithMany(t => t.ViagemEventos)
                .HasForeignKey(t => t.IdUsuarioLibSemChave);

            HasOptional(t => t.Protocolo)
                .WithMany(t => t.ViagemEventos)
                .HasForeignKey(t => t.IdProtocolo);

            HasOptional(t => t.EstabelecimentoBase)
                .WithMany(t => t.ViagemEventos)
                .HasForeignKey(t => t.IdEstabelecimentoBase);

            HasOptional(t => t.Motivo)
                .WithMany(t => t.ViagemEvento)
                .HasForeignKey(t => t.IdMotivo);

            HasOptional(t => t.ViagemEventoOrigem)
                .WithMany(t => t.ViagemsEventoOrigem)
                .HasForeignKey(t => t.IdViagemEventoOrigem);

            HasOptional(t => t.MotivoRejeicaoAbono)
                .WithMany(x => x.ViagemEventosReijeitadas)
                .HasForeignKey(y => y.IdMotivoRejeicaoAbono);

            HasOptional(t => t.UsuarioRejeicaoAbono)
                .WithMany(x => x.EventosRejeitados)
                .HasForeignKey(y => y.IdUsuarioRejeicaoAbono);

            HasMany(t => t.TransacoesPix)
                .WithRequired(x => x.ViagemEvento)
                .HasForeignKey(y => y.IdViagemEvento);

            Property(t => t.CpfUsuario)
                .HasMaxLength(14)
                .IsOptional();
            
            Property(t => t.NomeUsuario)
                .HasMaxLength(100)
                .IsOptional();

            Property(t => t.IdAbastecimentoticket)
                .IsOptional();

            Property(t => t.HabilitarPagamentoCartao);

            HasOptional(o => o.UsuarioBaixaEvento)
                .WithMany(o => o.ViagemEventoUsuariosBaixa)
                .HasForeignKey(o => o.IdUsuarioBaixaEvento);

            Ignore(o => o.DataEmissaoViagem);

            Property(x => x.DataAgendamentoPagamento).IsOptional();
        }
    }
}
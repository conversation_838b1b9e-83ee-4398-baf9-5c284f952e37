﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IDespesasViagemRepository : IRepository<DespesasViagem>
    {
        int? GetIdMotoristaPorCpf(string cpf);
        Motorista GetFromAllTables(string cpfCnpj);
        IQueryable<Motorista> GetIdsMotoristasAtualizados(DateTime dataAtualizacao);
    }
}
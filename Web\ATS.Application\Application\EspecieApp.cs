﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class EspecieApp : AppBase, IEspecieApp
    {
        private readonly IEspecieService _especieService;

        public EspecieApp(IEspecieService especieService)
        {
            _especieService = especieService;
        }

        /// <summary>
        /// Buscar espécie
        /// </summary>
        /// <param name="id">Código da espécie</param>
        /// <returns>Objeto Especie</returns>
        public Especie Get(int id)
        {
            return _especieService.Get(id);
        }

        /// <summary>
        /// Adicionar a espécie a base de dados
        /// </summary>
        /// <param name="especie">Dados do espécie</param>
        /// <returns></returns>
        public ValidationResult Add(Especie especie)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    var validationResult = _especieService.Add(especie);

                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro da espécie
        /// </summary>
        /// <param name="especie">Dados da espécie</param>
        /// <returns></returns>
        public ValidationResult Update(Especie especie)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _especieService.Update(especie);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna as espécies a partir dos dados de filtro
        /// </summary>
        /// <param name="descricao">Descrição da espécie</param>
        /// <returns>IQueryable de Especie</returns>
        public IQueryable<Especie> Consultar(string descricao)
        {
            return _especieService.Consultar(descricao);
        }

        /// <summary>
        /// Retorna as espécies atualizadas a partir da data informada
        /// </summary>
        /// <param name="dataBase">Data base</param>
        /// <returns></returns>
        public List<Especie> GetEspeciesAtualizadas(DateTime dataBase)
        {
            return _especieService.GetEspeciesAtualizadas(dataBase);
        }

        /// <summary>
        /// Inativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idEspecie)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _especieService.Inativar(idEspecie);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar a espécie
        /// </summary>
        /// <param name="idEspecie">Código da espécie a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idEspecie)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _especieService.Reativar(idEspecie);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna todas as especies ativas
        /// </summary>
        /// <returns>IQueryable de Especie</returns>
        public IQueryable<Especie> All()
        {
            return _especieService.All();
        }

        public object ConsultaGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _especieService.ConsultarGrid(take, page, order, filters);
        }

        public ValidationResult AlterarStatus(int idEspecie)
        {
            return _especieService.AlterarStatus(idEspecie);
        }

        public Especie GetPorDescricao(string especie)
        {
            return _especieService.GetPorDescricao(especie);
        }

        public Especie AddReturningObject(Especie especie)
        {
            return _especieService.AddReturningObject(especie);
        }

        public Especie UpdateReturningObject(Especie especie)
        {
            return _especieService.UpdateReturningObject(especie);
        }
    }
}
﻿using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.Models.Webservice.Response.Cartao
{
    public class CartaoConsultarPedagioAtsResponse
    {
        public List<CompraPedagioAtsResponse> CompraPedagioDTOList { get; set; }
        public FilteredOptions FilteredOptions { get; set; }
    }
    public class CompraPedagioAtsResponse {
        public int? Id { get; set; }
        public string CnpjEmpresa { get; set; }
        public string NomeEmpresa { get; set; }
        public string Status { get; set; }
        public string Fornecedor { get; set; }
        public string DocumentoFavorecido { get; set; }
        public string NomeFavorecido { get; set; }        
        public string DocumentoProprietario { get; set; }
        public string NomeProprietario { get; set; }
        public string Placa { get; set; }
        public int? QtdEixos { get; set; }
        public decimal? Valor { get; set; }
        public decimal? ValorResgatado { get; set; }
        public long? ProtocoloRequisicao { get; set; }
        public string NumeroCIOT { get; set; }
        //public System.DateTime? DataCadastro;
        public string DataCadastro { get; set; }
        public string DataConfirmacao { get; set; }
        public int? IbgeOrigem { get; set; }
        public string CidadeOrigem { get; set; }
        public string EstadoOrigem { get; set; }
        public int? IbgeDestino { get; set; }
        public string CidadeDestino { get; set; }
        public string EstadoDestino { get; set; }

    }

    public class GetStatusPedagioAtsResponse
    {
        public Dictionary<string, string> Values { get; set; }
    }
}

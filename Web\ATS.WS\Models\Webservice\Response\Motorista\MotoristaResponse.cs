﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ATS.WS.Models.Webservice.Response.Motorista
{
    public class MotoristaResponse
    {
        public int IdMotorista { get; set; }
        public int? IdEmpresa { get; set; }
        public string RazaoSocial { get; set; }
        public string Nome { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string CPF { get; set; }
        public string Sexo { get; set; }
        public string CNH { get; set; }
        public string CNHCategoria { get; set; }
        public DateTime? DataNascimento { get; set; }
        public DateTime? ValidadeCNH { get; set; }
        public string Celular { get; set; }
        public ETipoContrato TipoContrato { get; set; } = ETipoContrato.Frota;
        public string Email { get; set; }
        public string Imagem { get; set; }
        public bool Ativo { get; set; } = true;

        #region Endereço

        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public int IdPais { get; set; }
        public int IdCidade { get; set; }
        public int IdEstado { get; set; }

        #endregion

        #region Tabelas Filhas
        
        public virtual ICollection<MotoristaAvaliacaoResponse> Avaliacoes { get; set; }
    
        #endregion
    }

    public class MotoristaAvaliacaoResponse
    {
        public int IdAvaliacao { get; set; }
        public decimal Nota { get; set; }
        public string Observacao { get; set; }
    }
}
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IUsuarioPermissaoCartaoService
    {
        ValidationResult Integrar(int idUsuario, EBloqueioCartaoTipo idBloqueioFinanceiroTipo, bool empresa);
        UsuarioPermissaoCartao GetParametroPermissaoCartao(int idUsuario, EBloqueioCartaoTipo idBloqueioGestorTipo);
        bool PossuiPermissao(int idUsuario, EBloqueioCartaoTipo bloqueioFinanceiroTipo);
    }
}
﻿using System;

namespace ATS.WS.Models.Common.Request
{
    public enum TipoConfirmacaoPedagio
    {
        Compra = 0,
        Estorno = 1,
        ResgateSaldoResidual = 2
    }

    /// <summary>
    /// Recepção da webhook enviada pelo serviço de pedágio ao confirmar carga, estorno ou resgate de saldo de saldo residual
    /// </summary>
    public class NotificacaoConfirmacaoPedagioModel
    {
        public TipoConfirmacaoPedagio TipoConfirmacao { get; set; }
        public int ProtocoloRequisicaoPedagio { get; set; }
        public DateTime DataConfirmacao { get; set; }
        public string CnpjEstabelecimento { get; set; }
        public decimal ValorConfirmado { get; set; }
        public string DocumentoPortador { get; set; }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;

namespace ATS.Domain.Interface.Dapper
{
    public interface IViagemDapper : IRepositoryDapper<Viagem>
    {
        IList<ConsultaViagemInternalResponseDTO> GetViagens(ConsultaViagemRequestDTO request);
        decimal ConsultarValorPedagiosCiotAgregado(int idContratoCiotAgregado);
        ViagemDadosValePedagio GetDadosValePedagio(int idviagem);
        ComprovanteCargaResponse GetDadosComprovanteCarga(int idviagem);
        IQueryable<ConsultaPagamentosItemModel> GetPagamentos(int idempresa, DateTime datainicio, DateTime datafim);
    }
}
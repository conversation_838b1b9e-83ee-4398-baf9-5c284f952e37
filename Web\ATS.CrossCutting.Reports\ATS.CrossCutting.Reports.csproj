﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3F484548-F9A3-4E22-A74D-8DD198C7ABAB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ATS.CrossCutting.Reports</RootNamespace>
    <AssemblyName>ATS.CrossCutting.Reports</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <LangVersion>9</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.2\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Design, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.Design.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebDesign, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.WebDesign.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.140.1000.523\lib\net40\Microsoft.ReportViewer.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RestSharp, Version=112.1.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.112.1.0\lib\net48\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.2\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.2\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.2\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Telerik.Reporting, Version=12.2.18.1017, Culture=neutral, PublicKeyToken=a9d7983dfcc261be">
      <HintPath>..\ATS.WS\Tools\Telerik.Reporting.12.2.18.1017\lib\net40\Telerik.Reporting.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Reporting.OpenXmlRendering.2.7.2, Version=12.2.18.1017, Culture=neutral, PublicKeyToken=a9d7983dfcc261be">
      <HintPath>..\ATS.WS\Tools\Telerik.Reporting.12.2.18.1017\lib\net40\Telerik.Reporting.OpenXmlRendering.2.7.2.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Reporting.XpsRendering, Version=12.2.18.1017, Culture=neutral, PublicKeyToken=a9d7983dfcc261be">
      <HintPath>..\ATS.WS\Tools\Telerik.Reporting.12.2.18.1017\lib\net40\Telerik.Reporting.XpsRendering.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Atendimento\ConsultaAtendimento\RelatorioConsultaAtendimento.cs" />
    <Compile Include="Atendimento\ConsultaAtendimento\RelatorioConsultaAtendimentoDataType.cs" />
    <Compile Include="Base\FullTrustReportviewer.cs" />
    <Compile Include="Base\Margins.cs" />
    <Compile Include="Base\ReportBase.cs" />
    <Compile Include="Base\Reports.cs" />
    <Compile Include="CargaAvulsa\ReciboCargaAvulsa\RelatorioReciboCargaAvulsa.cs" />
    <Compile Include="CargaAvulsa\ReciboCargaAvulsa\RelatorioReciboCargaAvulsaDto.cs" />
    <Compile Include="CargaAvulsa\RelatorioCargaAvulsa.cs" />
    <Compile Include="CargaAvulsa\RelatorioCargaAvulsaDataType.cs" />
    <Compile Include="Cartoes\ConciliacaoAnalitico\RelatorioConciliacaoAnalitico.cs" />
    <Compile Include="Cartoes\ConciliacaoAnalitico\RelatorioConciliacaoAnaliticoDataType.cs" />
    <Compile Include="Cartoes\ConciliacaoAnalitico\RelatorioConciliacaoAnaliticoDataTypeNew.cs" />
    <Compile Include="Cartoes\ExtratoConsolidado\RelatorioExtratoConsolidado.cs" />
    <Compile Include="Cartoes\ExtratoConsolidado\RelatorioExtratoConsolidadoDataType.cs" />
    <Compile Include="Cartoes\ReciboTransferencia\ReciboTransferencia.cs" />
    <Compile Include="Cartoes\ReciboTransferencia\ReciboTransferenciaDataType.cs" />
    <Compile Include="Cartoes\SituacaoDosCartoes\RelatorioSituacaoDosCartoes.cs" />
    <Compile Include="Cartoes\SituacaoDosCartoes\RelatorioSituacaoDosCartoesDataType.cs" />
    <Compile Include="Cartoes\TransferenciaContasBancarias\RelatorioTransferenciaContasBancarias.cs" />
    <Compile Include="Cartoes\TransferenciaContasBancarias\RelatorioTransferenciaContasBancariasDataType.cs" />
    <Compile Include="Clientes\RelatorioListaClientes\RelatorioClientes.cs" />
    <Compile Include="Clientes\RelatorioListaClientes\RelatorioClientesDataType.cs" />
    <Compile Include="Credenciamento\CredenciamentoModel.cs" />
    <Compile Include="Credenciamento\RelatorioCredenciamento.cs" />
    <Compile Include="CurvaAbc\Detalhes\RelatorioDetalhesCurvaAbc.cs" />
    <Compile Include="CurvaAbc\Detalhes\RelatorioDetalhesCurvaAbcDataType.cs" />
    <Compile Include="CurvaAbc\RelatorioCurvaAbc.cs" />
    <Compile Include="CurvaAbc\RelatorioCurvaAbcDataType.cs" />
    <Compile Include="DespesasViagem\ExtratoDespesasViagem\RelatorioExtratoDespesasViagem.cs" />
    <Compile Include="DespesasViagem\ExtratoDespesasViagem\RelatorioExtratoDespesasViagemDataType.cs" />
    <Compile Include="DespesasViagem\ExtratoDespesasViagem\RelatorioExtratoDetalhadoDespesasViagemDataType.cs" />
    <Compile Include="DespesasViagem\ExtratoDespesasViagem\RelatorioExtratoDetalhadoDespesasViagem.cs" />
    <Compile Include="DespesasViagem\RelatorioListaDespesasViagem\RelatorioDespesasViagemDataType.cs" />
    <Compile Include="DespesasViagem\RelatorioListaDespesasViagem\RelatorioDespesasViagem.cs" />
    <Compile Include="Documento\DocumentoModel.cs" />
    <Compile Include="Documento\RelatorioDocumento.cs" />
    <Compile Include="Empresa\RelatorioListaEmpresa\RelatorioEmpresa.cs" />
    <Compile Include="Empresa\RelatorioListaEmpresa\RelatorioEmpresaDataType.cs" />
    <Compile Include="Empresa\RelatorioListaEmpresa\RelatorioFilial.cs" />
    <Compile Include="Estabelecimento\EstabelecimentoModel.cs" />
    <Compile Include="Estabelecimento\RelatorioEstabelecimento.cs" />
    <Compile Include="Faturamento\RelatorioFaturamentoDataType.cs" />
    <Compile Include="Faturamento\RelatorioFaturamento.cs" />
    <Compile Include="Filial\RelatorioFiliaisDataType.cs" />
    <Compile Include="GrupoUsuario\GrupoUsuarioModel.cs" />
    <Compile Include="GrupoUsuario\RelatorioGrupoUsuario.cs" />
    <Compile Include="PagamentoFrete\Cheque\ImpressaoCheque\RelatorioImpressaoCheque.cs" />
    <Compile Include="PagamentoFrete\Cheque\ImpressaoCheque\RelatorioImpressaoChequeDataType.cs" />
    <Compile Include="PagamentoFrete\Cheque\ImpressaoCheque\RelatorioImpressaoChequeTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="PagamentoFrete\Cheque\ImpressaoCheque\RelatorioImpressaoChequeTelerik.Designer.cs">
      <DependentUpon>RelatorioImpressaoChequeTelerik.cs</DependentUpon>
    </Compile>
    <Compile Include="PagamentoFrete\Cheque\MedidasImpressao\RelatorioMedidasImpressao.cs" />
    <Compile Include="PagamentoFrete\Cheque\MedidasImpressao\RelatorioMedidasImpressaoDataType.cs" />
    <Compile Include="PagamentoFrete\ListagemPagamentos\RelatorioListagemPagamentos.cs" />
    <Compile Include="PagamentoFrete\ListagemPagamentos\RelatorioListagemPagamentosDataType.cs" />
    <Compile Include="PagamentoFrete\Pagamentos\PagamentosModelExcel.cs" />
    <Compile Include="PagamentoFrete\Pagamentos\PagamentosReportSemChave.cs" />
    <Compile Include="MotivoCredenciamento\MotivoCredenciamentoModel.cs" />
    <Compile Include="MotivoCredenciamento\RelatorioMotivosCredenciamento.cs" />
    <Compile Include="Motorista\RelatorioListaMotoristas\RelatorioMotoristaDataType.cs" />
    <Compile Include="Motorista\RelatorioListaMotoristas\RelatorioMotoristas.cs" />
    <Compile Include="PagamentoFrete\Pagamentos\PagamentosModel.cs" />
    <Compile Include="PagamentoFrete\Pagamentos\PagamentosReport.cs" />
    <Compile Include="PagamentoFrete\Recibo\PagamentoFreteReciboModel.cs" />
    <Compile Include="PagamentoFrete\Recibo\PagamentoFreteReciboReport.cs" />
    <Compile Include="Pedagio\FaturaTAG\RelatorioFaturaTAG.cs" />
    <Compile Include="Pedagio\FaturaTAG\RelatorioFaturaTAGDataType.cs" />
    <Compile Include="Pedagio\GridConciliacaoTAG\RelatorioGridConciliacaoTAG.cs" />
    <Compile Include="Pedagio\GridConciliacaoTAG\RelatorioGridConciliacaoTAGDataType.cs" />
    <Compile Include="Pedagio\GridFaturamentoTAG\RelatorioGridFaturamentoTAG.cs" />
    <Compile Include="Pedagio\GridFaturamentoTAG\RelatorioGridFaturamentoTAGDataType.cs" />
    <Compile Include="Pedagio\GridFaturamentoTAG\TotalizadorFaturamentoTAGDataType.cs" />
    <Compile Include="Pedagio\GridPagamentosTAG\RelatorioGridPagamentosTAG.cs" />
    <Compile Include="Pedagio\GridPagamentosTAG\RelatorioGridPagamentosTAGDataType.cs" />
    <Compile Include="Pedagio\GridPassagemWebhookTAG\RelatorioGridPassagemWebhookTAG.cs" />
    <Compile Include="Pedagio\GridPassagemWebhookTAG\GridPassagemWebhookTAGDataType.cs" />
    <Compile Include="Pedagio\PedagioAvulso\RelatorioPedagioAvulso.cs" />
    <Compile Include="Pedagio\PedagioAvulso\RelatorioPedagioAvulsoDataType.cs" />
    <Compile Include="Pedagio\PracasPedagioMoveMais\RelatorioPracasPedagioMoveMais.cs" />
    <Compile Include="Pedagio\PracasPedagioMoveMais\RelatorioPracasPedagioMoveMaisDataType.cs" />
    <Compile Include="Pedagio\ReciboPagamento\ReciboPagamento.cs" />
    <Compile Include="Pedagio\ReciboPagamento\ReciboPagamentoHistoricoParcelasDataType.cs" />
    <Compile Include="Pedagio\ReciboPagamento\ReciboPagamentoDataType.cs" />
    <Compile Include="Pedagio\Recibo\ComprovanteCompraPedagio.cs" />
    <Compile Include="Pedagio\Recibo\ComprovanteCompraPedagioDataType.cs" />
    <Compile Include="Pedagio\Recibo\ComprovanteCompraPedagioHistoricoPracasDataType.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Proprietario\RelatorioListaProprietarios\RelatorioProprietarioDataType.cs" />
    <Compile Include="Proprietario\RelatorioListaProprietarios\RelatorioProprietarios.cs" />
    <Compile Include="Protocolo\Capa\RelatorioProtocoloCapa.cs" />
    <Compile Include="Protocolo\Capa\RelatorioProtocoloCapaDataType.cs" />
    <Compile Include="Protocolo\Etiquetas\RelatorioProtocoloEtiquetas.cs" />
    <Compile Include="Protocolo\Etiquetas\RelatorioProtocoloEtiquetasDataType.cs" />
    <Compile Include="Protocolo\RecebimentoProtocolo\RelatorioRecebimentoProtocolo.cs" />
    <Compile Include="Protocolo\RecebimentoProtocolo\RelatorioRecebimentoProtocoloDataType.cs" />
    <Compile Include="Protocolo\RelatorioProtocoloOcorrencia.cs" />
    <Compile Include="Protocolo\RelatorioProtocolos.cs" />
    <Compile Include="Protocolo\ProtocoloModel.cs" />
    <Compile Include="Protocolo\ProtocoloReport.cs" />
    <Compile Include="Roteirizador\RelatorioRoteirizador.cs" />
    <Compile Include="Roteirizador\RelatorioRoteirizadorEntradasSaidasDataType.cs" />
    <Compile Include="Roteirizador\RelatorioRoteirizadorFiltrosDataType.cs" />
    <Compile Include="Roteirizador\RelatorioRoteirizadorPostosDataType.cs" />
    <Compile Include="Roteirizador\RelatorioRoteirizadorPracasDataType.cs" />
    <Compile Include="Protocolo\TriagemProtocolo\RelatorioTriagemProtocolo.cs" />
    <Compile Include="Protocolo\TriagemProtocolo\RelatorioTriagemProtocoloDataType.cs" />
    <Compile Include="ProvisaoPagamento\RelatorioProvisaoPagamento.cs" />
    <Compile Include="ProvisaoPagamento\RelatorioProvisaoPagamentoDataType.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="TagExtratta\ConsultaSituacaoTags\RelatorioConsultaSituacaoTags.cs" />
    <Compile Include="TagExtratta\ConsultaSituacaoTags\RelatorioConsultaSituacaoTagsDataType.cs" />
    <Compile Include="TransferenciaPix\RelatorioTransferenciaPix.cs" />
    <Compile Include="Usuarios\RelatorioUsuarios\RelatorioUsuarios.cs" />
    <Compile Include="Usuarios\RelatorioUsuarios\RelatorioUsuariosDataType.cs" />
    <Compile Include="Veiculos\RelatorioListaVeiculos\RelatorioVeiculoDataType.cs" />
    <Compile Include="Veiculos\RelatorioListaVeiculos\RelatorioVeiculos.cs" />
    <Compile Include="Viagem\ConsultaViagem\RelatorioConsultaViagem.cs" />
    <Compile Include="Viagem\ConsultaViagem\RelatorioConsultaViagemDataType.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Atendimento\ConsultaAtendimento\RelatorioConsultaAtendimento.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CargaAvulsa\ReciboCargaAvulsa\RelatorioReciboCargaAvulsa.rdlc" />
    <EmbeddedResource Include="Cartoes\ConciliacaoAnalitico\RelatorioConciliacaoAnalitico.rdlc" />
    <EmbeddedResource Include="Cartoes\ExtratoConsolidado\RelatorioExtratoConsolidado.rdlc" />
    <EmbeddedResource Include="Cartoes\ReciboTransferencia\ReciboTransferencia.rdlc" />
    <EmbeddedResource Include="Cartoes\ReciboTransferencia\ReciboTransferenciaBancaria.rdlc" />
    <EmbeddedResource Include="Clientes\RelatorioListaClientes\RelatorioClientes.rdlc" />
    <EmbeddedResource Include="Credenciamento\RelatorioCredenciamento.rdlc" />
    <EmbeddedResource Include="CargaAvulsa\RelatorioCargaAvulsa.rdlc" />
    <EmbeddedResource Include="CargaAvulsa\RelatorioGridCargaAvulsa.rdlc" />
    <EmbeddedResource Include="Cartoes\SituacaoDosCartoes\RelatorioSituacaoDosCartoes.rdlc" />
    <EmbeddedResource Include="Cartoes\TransferenciaContasBancarias\RelatorioTransferenciaContasBancarias.rdlc" />
    <EmbeddedResource Include="CurvaAbc\Detalhes\RelatorioDetalhesCurvaAbc.rdlc" />
    <EmbeddedResource Include="CurvaAbc\RelatorioCurvaAbc.rdlc" />
    <EmbeddedResource Include="DespesasViagem\ExtratoDespesasViagem\RelatorioExtratoDetalhadoDespesasViagem.rdlc" />
    <EmbeddedResource Include="DespesasViagem\ExtratoDespesasViagem\RelatorioExtratoDespesasViagem.rdlc" />
    <EmbeddedResource Include="DespesasViagem\RelatorioListaDespesasViagem\RelatorioDespesasViagem.rdlc" />
    <EmbeddedResource Include="Documento\RelatorioDocumento.rdlc" />
    <EmbeddedResource Include="Empresa\RelatorioListaEmpresa\RelatorioEmpresa.rdlc" />
    <EmbeddedResource Include="Estabelecimento\RelatorioEstabelecimento.rdlc" />
    <EmbeddedResource Include="Faturamento\RelatorioFaturamento.rdlc" />
    <EmbeddedResource Include="Filial\RelatorioFilial.rdlc" />
    <EmbeddedResource Include="GrupoUsuario\RelatorioGrupoUsuario.rdlc" />
    <EmbeddedResource Include="MotivoCredenciamento\RelatorioMotivosCredenciamento.rdlc" />
    <EmbeddedResource Include="Motorista\RelatorioListaMotoristas\RelatorioMotoristas.rdlc" />
    <EmbeddedResource Include="PagamentoFrete\ListagemPagamentos\RelatorioListagemPagamentos.rdlc" />
    <EmbeddedResource Include="PagamentoFrete\Pagamentos\PagamentosDetalhado.rdlc">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="PagamentoFrete\Recibo\Recibo.rdlc" />
    <EmbeddedResource Include="PagamentoFrete\Recibo\ReciboNewLayout\Recibo.rdlc" />
    <EmbeddedResource Include="PagamentoFrete\Recibo\ReciboNewLayout\ReciboRPA.rdlc" />
    <EmbeddedResource Include="PagamentoFrete\Recibo\ReciboNewLayout\ReciboSaldo.rdlc" />
    <EmbeddedResource Include="PagamentoFrete\Recibo\ReciboRPA.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PagamentoFrete\Recibo\ReciboSaldo.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\FaturaTAG\FaturaTAGFornecedores.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\FaturaTAG\FaturaTAG.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\GridConciliacaoTAG\RelatorioGridConciliacaoTAG.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\GridFaturamentoTAG\RelatorioGridFaturamentoTAG.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\GridPagamentosTAG\RelatorioGridPagamentosTAG.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\GridPassagemWebhookTAG\RelatorioGridPassagemWebhookTAG.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\PedagioAvulso\RelatorioPedagioAvulso.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\PracasPedagioMoveMais\RelatorioPracasPedagioMoveMais.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pedagio\ReciboPagamento\ReciboPagamento.rdlc" />
    <EmbeddedResource Include="Pedagio\ReciboPagamento\ReciboPagamentoComprovanteCarga.rdlc" />
    <EmbeddedResource Include="Proprietario\RelatorioListaProprietarios\RelatorioProprietarios.rdlc" />
    <EmbeddedResource Include="Protocolo\Capa\RelatorioProtocoloCapa.rdlc" />
    <EmbeddedResource Include="Protocolo\Etiquetas\RelatorioProtocoloEtiquetas.rdlc" />
    <EmbeddedResource Include="Protocolo\RecebimentoProtocolo\RelatorioRecebimentoProtocolo.rdlc" />
    <EmbeddedResource Include="Protocolo\RelatorioProtocoloEventosVinculado.rdlc" />
    <EmbeddedResource Include="Protocolo\RelatorioProtocoloEventosOcorrencia.rdlc" />
    <EmbeddedResource Include="Protocolo\RelatorioProtocoloComOcorrencia.rdlc" />
    <EmbeddedResource Include="Protocolo\RelatorioProtocolo.rdlc" />
    <EmbeddedResource Include="Protocolo\Protocolo_bkp_colunas.rdlc" />
    <EmbeddedResource Include="Protocolo\Protocolo.rdlc" />
    <EmbeddedResource Include="TagExtratta\ConsultaSituacaoTags\RelatorioConsultaSituacaoTags.rdlc" />
    <EmbeddedResource Include="TransferenciaPix\RelatorioTransferenciaPix.rdlc" />
    <EmbeddedResource Include="Usuarios\RelatorioUsuarios\RelatorioUsuarios.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Veiculos\RelatorioListaVeiculos\RelatorioVeiculos.rdlc" />
    <EmbeddedResource Include="Protocolo\TriagemProtocolo\RelatorioTriagemProtocolo.rdlc" />
    <EmbeddedResource Include="ProvisaoPagamento\RelatorioProvisaoPagamento.rdlc" />
    <EmbeddedResource Include="ProvisaoPagamento\RelatorioProvisaoPagamentoBaixado.rdlc" />
    <EmbeddedResource Include="Viagem\ConsultaViagem\RelatorioConsultaViagemPdf.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Viagem\ConsultaViagem\RelatorioConsultaViagemExcel.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <EmbeddedResource Include="PagamentoFrete\Pagamentos\Pagamentos.rdlc">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="PagamentoFrete\Pagamentos\PagamentosSemChave.rdlc">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.IoC.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.AppSettingsUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.ConstantesUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.ConvertUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.DateUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.ImagemUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.NumberUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.ReportUtils.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.IoC.Utils.StringUtil.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Clientes.RelatorioListaClientes.RelatorioClientesDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.CurvaAbc.Detalhes.RelatorioDetalhesCurvaAbcDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.CurvaAbc.RelatorioCurvaAbcDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Dashboards.TempoSemUsoMobile.TempoSemUsoMobileDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Empresa.RelatorioListaEmpresa.RelatorioEmpresaDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Empresa.RelatorioListaEmpresa.RelatorioEmpresaDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Filial.RelatorioFiliaisDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.GestaoEntrega.ListagemGestaoEntrega.RelatorioListagemGestaoEntregaDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.HistoricoLocalizacao.ListaHistorico.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.LogSms.RelatorioLogSmsDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Models.CabecalhoPortaAberta.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Models.JornadaMotoristaListModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Models.JornadaMotoristaModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Models.PortaAbertaModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.ListagemPagamentos.RelatorioListagemPagamentosDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboAcrescimosDescontosModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos.PagamentosModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.PagamentosSemChave.PagamentosHeaderModelSemChave.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.PagamentosSemChave.PagamentosModelSemChave.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.PagamentoFrete.PagamentosSemChave.PagamentosSemChaveReport.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Protocolo.Etiquetas.RelatorioProtocoloEtiquetasDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Protocolo.ProtocoloListModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Protocolo.ProtocoloModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Protocolo.ProtocoloRelatorioModel.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Protocolo.RecebimentoProtocolo.RelatorioRecebimentoProtocoloDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Protocolo.TriagemProtocolo.RelatorioTriagemProtocoloDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.ProtocoloOcorrencia.RelatorioEventosVinculados.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.ProtocoloOcorrencia.RelatorioProtocoloComOcorrencia.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.ProtocoloOcorrencia.RelatorioProtocoloOcorrencia.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.ProvisaoPagamento.RelatorioProvisaoPagamentoDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Usuarios.RelatorioUsuarios.RelatorioUsuariosDataType.datasource" />
    <None Include="Properties\DataSources\ATS.CrossCutting.Reports.Veiculos.RelatorioListaVeiculos.RelatorioVeiculoDataType.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Name>ATS.CrossCutting.IoC</Name>
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Name>ATS.CrossCutting.IoC</Name>
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Name>ATS.CrossCutting.IoC</Name>
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Data.Repository.External\ATS.Data.Repository.External.csproj">
      <Project>{8a62a98c-9d32-48ee-b100-0259b1fdf08a}</Project>
      <Name>ATS.Data.Repository.External</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sistema.Framework\Sistema.Framework.Util\Sistema.Framework.Util.csproj">
      <Project>{2a5da508-d09f-4dc8-b0d6-e21a6e1a7ae6}</Project>
      <Name>Sistema.Framework.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Roteirizador\RelatorioRoteirizador.rdlc" />
    <EmbeddedResource Include="Pedagio\Recibo\ComprovanteCompraPedagio.rdlc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="ExtratoCartao\RelatorioExtratoCartao\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
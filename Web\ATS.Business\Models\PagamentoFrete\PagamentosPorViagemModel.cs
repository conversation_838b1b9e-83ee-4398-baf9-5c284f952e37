using System;
using ATS.Domain.Enum;

namespace ATS.Domain.Models.PagamentoFrete
{
    public class PagamentosPorViagemModel
    {        
        public int IdViagemEvento { get; set; }
        public string Token { get; set; }
        public string DataHoraPagamento { get; set; }
        public string Evento { get; set; }
        public string AbonoDocument { get; set; }
        public string ValorPagamento { get; set; }
        public decimal? Valor { get; set; }
        public string Status { get; set; }
        public string MotoristaCNH { get; set; }
        public string Placa { get; set; }
        public string Numero { get; set; }
        public string DataLancamento { get; set; }
        public string NumeroNota { get; set; }
        public string Estabelecimento { get; set; }
        public bool HasAssociacao { get; set; }
        public int? IdProtocolo { get; set; }
        public int IdViagem { get; set; }
        public string CpfMotorista { get; set; }
        public string NomeMotorista { get; set; }
        public string CpfCnpjProprietario { get; set; }
        public string CNPJFilial { get; set; }
        public string RazaoSocialFilial { get; set; }
        public string NomeProprietario { get; set; }
        public string ValorQuebra { get; set; }
        public string ValorPedagio { get; set; }
        public string CIOT { get; set; }
        public string StatusAbono { get; set; }
        public string JustificativaAbono { get; set; }
        public string UsuarioAbono { get; set; }
        public string possuiProtocolo { get; set; }
        public ViagemSolicitacaoAbonoModel ViagemSolicitacaoAbono { get; set; }
        public string HabilitarPagamentoCartao { get; set; }
        public FornecedorEnum? FornecedorPedagio { get; set; } 
        public string FornecedorPedagioStr { get; set; } 
        public string CartaoMotorista { get; set; } 
        public string CartaoProprietario { get; set; }

        /// <inheritdoc cref="Entities.ViagemEvento.DataAgendamentoPagamento"/>
        public string DataAgendamentoPagamento { get; set; }

        #region classes aninhadas

        public class ViagemSolicitacaoAbonoModel
        {
            public int? IdMotivo { get; set; }
            public string DescricaoMotivo { get; set; }
            public string NomeUsuario { get; set; }
            public string DataHoraSolicitacao { get; set; }
            public string Detalhamento { get; set; }
            public DateTime? DataSolicitacao { get; set; }
        }
        
        #endregion
    }
}
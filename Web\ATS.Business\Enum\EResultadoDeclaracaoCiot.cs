﻿namespace ATS.Domain.Enum
{
    public enum EResultadoDeclaracaoCiot
    {
        /// <summary>
        /// Declarado com sucesso
        /// </summary>
        Sucesso = 0,

        /// <summary>
        /// Erro ao declarar CIOT na ANTT, ou erro interno do sistema
        /// </summary>
        Erro = 1,

        /// <summary>
        /// O proprietário não é equiparado a TAC, não sendo obrigado a gerar CIOT
        /// </summary>
        ///[Obsolete("Alteração da ANTT faz com que seja obrigatório a emissão do CIOT para todas as viagens")]
        NaoObrigatorio = 2,

        /// <summary>
        /// Integração de CIOT desabilitada durante a integração (Parâmetro HabilitarDeclaracaoCiot = false)    
        /// </summary>
        NaoHabilitado = 3
    }
}
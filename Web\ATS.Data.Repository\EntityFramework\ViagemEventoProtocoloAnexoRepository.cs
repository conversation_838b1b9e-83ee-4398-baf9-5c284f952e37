using ATS.Data.Context;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Data.Repository.EntityFramework.Common;

namespace ATS.Data.Repository.EntityFramework
{
    public class ViagemEventoProtocoloAnexoRepository : Repository<ViagemEventoProtocoloAnexo>, IViagemEventoProtocoloAnexoRepository
    {
        public ViagemEventoProtocoloAnexoRepository(AtsContext context) : base(context)
        {
        }
    }
}
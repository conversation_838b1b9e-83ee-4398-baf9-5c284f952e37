using System.ComponentModel;
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Infrastructure.Annotations;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;

namespace ATS.Data.Context.Mapping
{
    public class EmpresaMap : EntityTypeConfiguration<Empresa>
    {
        public EmpresaMap()
        {
            ToTable("EMPRESA");

            HasKey(t => t.IdEmpresa);

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.ApresentaPontoReferencia).IsRequired();

            Property(t => t.CNPJ)
                .IsRequired()
                .HasMaxLength(14);

            Property(t => t.RazaoSocial)
                .IsRequired()
                .HasMaxLength(150);

            Property(t => t.NomeFantasia)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.ObrigarNumeroFrota)
                .IsRequired();

            Property(t => t.ObrigarValorTerceiro)
                .IsRequired();

            Property(t => t.IntervaloConsultaFretesConcorrente)
                .IsRequired();

            Property(t => t.TokenMicroServices)
                .IsOptional();

            #region Endereço

            Property(t => t.CEP)
                .IsRequired()
                .HasMaxLength(8);

            Property(t => t.Endereco)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Complemento)
                .HasMaxLength(100);

            Property(t => t.Bairro)
                .IsRequired()
                .HasMaxLength(100);

            #endregion

            #region Contato

            Property(t => t.Telefone)
                .IsRequired()
                .HasMaxLength(20);

            Property(t => t.Email)
                .IsRequired()
                .HasMaxLength(500);

            Property(t => t.Celular)
                .IsOptional()
                .HasMaxLength(20);

            #endregion

            #region Meio Homologado

            Property(x => x.IdLayoutCartao);

            Property(x => x.TipoCarregamentoFrete);

            Property(x => x.TempoExpiracaoCreditoPedagio);

            HasOptional(x => x.LayoutCartao)
                .WithMany()
                .HasForeignKey(x => x.IdLayoutCartao);

            #endregion
            //Property(x => x.IdEmpresaContaBancaria).IsOptional();

            //HasOptional(x => x.EmpresaContaBancaria)
            //    .WithMany()
            //    .HasForeignKey(x => x.IdEmpresaContaBancaria);

            Property(t => t.TipoNotificacaoCandidatura)
                .IsOptional();

            Property(t => t.TipoNotificacaoLotesInativosMesa)
                .IsOptional();


            Property(t => t.TemperaturaInicial)
                .IsOptional()
                .HasPrecision(5,2);

            Property(t => t.ListaPreCarga)
                .IsRequired();

            Property(t => t.TemperaturaFinal)
                .IsOptional()
                .HasPrecision(5, 2);

            Property(x => x.LimiteKmFreteCurto)
                .IsOptional();

            Property(x => x.EmailsCheckList)
                .HasMaxLength(1000);

            Property(x => x.ModeloEmailCancelamentoOc)
                .IsOptional()
                .HasMaxLength(1000);

            Property(x => x.ModeloEmailGeracaoOc)
                .IsOptional()
                .HasMaxLength(1000);

            Property(x => x.ModeloEmailRelatorioOc)
                .IsOptional()
                .HasMaxLength(1000);

            Property(x => x.ModeloEmailProtocoloRejeitado)
                .IsOptional()
                .HasMaxLength(1000);

            Property(x => x.CNTRC)
                .IsOptional();

            Property(x => x.EmailContatoGR)
                .HasMaxLength(1000);

            Property(o => o.EnviaEmailTempoExcedido)
                .IsRequired();

            Property(o => o.EnviaEmailCadastroOcorrencia)
                .IsRequired();

            Property(o => o.DataAtualizacao)
                .IsOptional();

            Property(o => o.SomentePainelGestaoEncerraNota)
                .IsRequired();
            
            Property(o => o.GerarCiotViagemInternacional)
                .IsRequired();

            Property(o => o.HoraEnvEmailMotNaoEmb)
               .IsOptional()
               .HasMaxLength(2);

            Property(o => o.DiasBuscaMotNaoEmb)
               .IsOptional();

            Property(o => o.ToleranciaHorasInatividade)
              .IsOptional();

            #region Parâmetros de perimetro (Localização)

            Property(x => x.AlertaArea)
                .IsRequired();

            Property(x => x.Acuracia)
                .IsRequired()
                .HasPrecision(10, 0);


            #endregion

            #region Parâmetros de servidor de e-mail

            Property(x => x.EmailNome)
                .IsOptional()
                .HasMaxLength(50);

            Property(x => x.EmailEndereco)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.EmailPorta)
                .IsOptional()
                .HasPrecision(4,0);

            Property(x => x.EmailServidor)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.EmailSsl)
                .IsRequired();

            Property(x => x.EmailUsuario)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.EmailSenha)
                .IsOptional()
                .HasMaxLength(100);
            #endregion

            #region Tecnologia de rastreamento
            Property(x => x.UrlRastreamento)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.LoginRastreamento)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.SenhaRastreamento)
                .IsOptional()
                .HasMaxLength(100);
            #endregion

            #region Meio Homologado

            Property(x => x.IdLayoutCartao);

            Property(x => x.TipoCarregamentoFrete);

            Property(x => x.PercentualTransferenciaMotorista);

            HasOptional(x => x.LayoutCartao)
                .WithMany()
                .HasForeignKey(x => x.IdLayoutCartao);
            
            Property(x => x.VinculoNovoCartaoBloqueadoPortador);
            Property(x => x.VinculoNovoCartaoPortador);
            #endregion
            
            #region CNAB
            Property(x => x.TipoInscricaoEmpresa).IsOptional();
            Property(x => x.CodigoBancoCompensacao).IsOptional();
            Property(x => x.NumeroInscricaoEmpresa).IsOptional();
            Property(x => x.CodigoConvenioBanco).IsOptional();
            Property(x => x.AgenciaMantenedora).IsOptional();
            Property(x => x.DigitoVerificaConta).IsOptional();
            Property(x => x.DigitoVerificaContaAgConta).IsOptional();
            #endregion
            
            Property(x => x.DesabilitaCacheRotas);
            
            Property(x => x.IdOCcancGR).IsOptional();
            Property(x => x.IdOCcancCTe).IsOptional();
            Property(x => x.IdOCcancViagem).IsOptional();
            Property(x => x.IdOCRemocaoFila).IsOptional();
            Property(x => x.BateriaFraca).IsOptional();
            Property(x => x.BateriaForte).IsOptional();
            Property(x => x.DistanciaEntradaRaio).IsOptional();
            Property(x => x.DistanciaSaidaRaio).IsOptional();
            Property(x => x.DistanciaRaioGr).IsOptional();

            Property(x => x.AtualizarStatusLoteAutomaticamente).IsOptional();

            Property(x => x.IdSistemaExterno).HasMaxLength(15);

            HasOptional(x => x.OCcancGR)
                .WithMany()
                .HasForeignKey(x => x.IdOCcancGR);

            HasOptional(x => x.OCcancCTe)
                .WithMany()
                .HasForeignKey(x => x.IdOCcancCTe);

            HasOptional(x => x.OCcancViagem)
                .WithMany()
                .HasForeignKey(x => x.IdOCcancViagem);

            HasOptional(x => x.OCRemocaoFIla)
                .WithMany()
                .HasForeignKey(x => x.IdOCRemocaoFila);

            Property(o => o.UtilizaAgendamento)
                .IsRequired();

            Property(o => o.NaoValidarProtocoloRecebidoEmpresa)
                .IsRequired();

            Property(o => o.MinutosValidadeHash)
                .IsOptional();

            Property(o => o.ValidaChaveMHBaixaEvento)
                .IsRequired();

            Property(o => o.Telefone0800)
                .IsOptional()
                .HasMaxLength(20);

            Property(o => o.EnviaEmailGrRemocaoLote)
                .IsRequired();
            
            Property(t => t.PeriodoBloqueioEventosAberto)
                .IsOptional()
                .HasMaxLength(85);

            Property(t => t.WebHookEndpoint)
                .IsOptional()
                .HasMaxLength(255);

            Property(t => t.WebHookHeaders)
                .IsOptional()
                .HasMaxLength(1000);
        }
    }
}

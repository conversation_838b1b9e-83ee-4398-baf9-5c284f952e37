﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Especie
    {
        public int IdEspecie    { get; set; }
        public string Descricao { get; set; }
        public bool Ativo       { get; set; } = true;

        /// <summary>
        /// Data e hora em que o registro foi atualizado pela ultima vez
        /// </summary>
        [SkipTracking]
        public DateTime? DataHoraUltimaAtualizacao { get; set; }

        public int? CodigoFreteBras { get; set; }

        public virtual ICollection<ClienteProdutoEspecie> ClienteProdutoEspecie { get; set; }
    }
}
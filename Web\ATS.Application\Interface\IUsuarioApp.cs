﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Models.Usuario;

namespace ATS.Application.Interface
{
    public interface IUsuarioApp : IAppBase<Usuario>
    {
        ValidationResult AlterarSenha(int idUsuario, string senha, string novasenha, int idUsuarioLogon, bool verificarSenhaAtual);
        ValidationResult AlterarSenha(int idUsuario, string novasenha, bool senhaTemporaria = true);
        ValidationResult SalvarLocalizacao(LocalizacaoUsuarioPortal localizacao);
        bool ExisteLocalizacaoPorUsuarioIp(int idUsuario, string ip);
        IQueryable<Usuario> Find(Expression<Func<Usuario, bool>> predicate);
        Usuario GetAllChilds(int id);
        UsuarioTokenDTO ValidarUsuario(string login, string senha, long? idFacebook);
        UsuarioTokenDTO ValidarCodigoAcesso(string code, string session_state);
        Usuario ValidarUsuarioPorCPF(string cnpjCPF, string senha);
        ValidationResult Add(Usuario usuario, int? idUsuarioLogon, string cnpjEmpresa = "");
        Usuario Get(int id);
        Usuario Get(int id, bool asNoTracking);
        Usuario GetUsuarioFromFacebook(long idFacebook);
        ValidationResult Update(Usuario usuario, int idUsuarioLogon, string cnpjEmpresa = "");
        bool UpdateUsuarioEmpresaVinculada(Usuario usuario, int idUsuarioLogon, string cnpjEmpresa = "");
        IQueryable<Usuario> Consultar(string nome, int? idEmpresa, int idUsuarioLogOn, bool listarTerceiros);
        IQueryable<Usuario> Consultar(int idUsuario);
        ValidationResult Inativar(int idUsuario, int idUsuarioRequisicao);
        ValidationResult Reativar(int idUsuario, int idUsuarioRequisicao);
        byte[] GetFoto(int id);
        int? GetIdPorCNPJCPF(string cnpjCPF, int? idEmpresa = null);
        EPerfil GetPerfil(int idUsuario);
        string GetCPFCNPJ(int idUsuario);
        string GetNome(int id);
        EStatusUsuario GetStatusUsuario(string cnpjCPF);
        IQueryable<Usuario> GetUsuariosPorEmpresa(int idEmpresa);
        Usuario GetPorCNPJCPF(string cnpjCPF, bool nWithIncludes = true, int? idEmpresa = null);
        //string GetSenha(int id);
        EStatusUsuario GetStatusUsuario(string login, string senha);
        bool HasPermissaoAcessoMenu(int idUsuario, int idMenu);
        ValidationResult UpdatePush(int idUsuario, string idPush, ESistemaOperacional? aSistemaOperacional = null);
        ValidationResult AtualizarDataUltimoAcesso(int idUsuario, ETipoAcessoSistema tipoAcesso);
        //ETipoContrato GetTipoContrato(int idUsuario, int? idEmpresa);
        List<Usuario> GetUsuarioByIdPonto(int IdPonto);

        bool ValidarSessaoUsuario(string key);
        DateTime? GetUltimoAcessoUsuarioAplivativo(int IdUsuario);
        ValidationResult AtualizarContatado(string aCPF, int aStatus);
        Usuario GetComEstabelecimentos(int idUsuario);
        List<Usuario> GetByPerfil(EPerfil perfil, int? idFilial, int? idEmpresa);

        object ConsultaGrid(int? idEmpresa, string nome, int take, int page, OrderFilters order, List<QueryFilters> filters, EPerfil prfUsuLogado, int? idUsuarioLogOn, bool listarTerceiros, bool usuariosAtivos);
        ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosLiderados(int idUsuario, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosDisponiveisParaSeremLiderados(int idUsuario, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        ValidationResult IncluirUsuarioLiderado(int idUsuario, int? idEmpresa, int idUsuarioParaIncluir, int idUsuarioCadastro);
        ValidationResult RemoverUsuarioLiderado(int idUsuario, int idUsuarioParaIncluir);

        object ConsultaGrid(int? idEmpresa, int? idEstabelecimentoBase, int take, int page, OrderFilters order,
            List<QueryFilters> filters);

        IQueryable<GridContatosModel> GetEmailsUsuarioByListaId(int[] listaIds);
        Usuario GetClientesByUsuario(int? idUsuario);

        Usuario GetUsuarioGestaoEntregas(string cpfCnpj);

        object ConsultaGridPorEmpresa(int? idEmpresa, int? idFilial, int? idOperacao, int take, int page, OrderFilters order, List<QueryFilters> filters, bool marcarTodos, int apertou, List<int> grupoUsuarios);
        UsuarioMicroServicoInstanciaAppDto UsuarioMicroServicoInstanciaApp(int idUsuario);
        UsuarioFotoResponse GetFotoUsuario(string cpfcnpj);
        UsuarioAplicativoGetInformacoesResponse UsuarioAplicativoGetInformacoes();
        string BuscaUsuarioMasterEstabelecimento(int idEstabelecimento);
        UsuarioDocumento GetDocumentoCnhPorCpfMot(string cpfMotorista);
        Usuario ValidarUsuarioPorUsuario(string usuario, string senha);
        Usuario ValidarUsuarioPorToken(string tokenFirebase);
        bool HasVeiculoCadastrado(string placa);
        KeyValuePair<ValidationResult, string> GerarKeyCodeTransaction(int idUsuario, string cpf);
        int? GetIdByTokenFirebase(string tokenFirebase);

        ValidationResult VincularUsuarioComEmpresa(Motorista motoristaSerAdicionado, EProcesso processoMotorista,
            Usuario usuarioVinculado);

        List<UsuarioPreferencias> GetPreferenciasUsuarioPrefixLike(int idUsuario, string prefix);
        List<UsuarioPreferencias> GetUsuarioPreferencias(int idUsuario, string campo = null);
        ValidationResult SaveUsuarioPreferencias(List<UsuarioPreferencias> usuarioPreferencias);

        byte[] GerarRelatorioGridUsuarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters,
            EPerfil perfilUsuarioLogado, int? idUsuario, bool listarTerceiros, string tipoArquivo, string logo);

        ValidationResult AtualizarDataUltimaAberturaAplicativo(int usuarioId);
        ConsultaInformacoesMobileModel ConsultarInformacoesMobile(int itensPorPagina, int pagina, int? empresaId, string documento);
        List<UsuarioEstabelecimento> GetEstabelecimentos(int idUsuario);
        bool HasEstabelecimento(int idUsuario, int? idEstabelecimentoBase);
        IEnumerable<UsuarioEstabelecimento> GetUsuariosEstabelecimento(int idUsuario);
        IQueryable<Usuario> GetQueryPorCnpjcpf(string cnpjCpf);
        IEnumerable<ConsultaVistoriadores> ConsultarVistoriadores(string cpfUsuario);
        Usuario GetUsuarioByKey(string key);
        List<dynamic> ConsultarUsuarios(int idEmpresa, bool vistoriador);
        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, IUserIdentity userIdentity);
        //remover ?????????
        //void ResetarSenhaPorCpfEmail(string cpf, string email, string dominio = null);
        int? GetIdEstabelecimentoBase(int idUsuario);

        bool UserGroupIsActive(int idUsuario);
        bool AutenticarMenu(string link, int idUser);
        MenusUsuarioPermitidoDto GetMenusAcessoUsuario(int idUser);
        bool ResetarSenha(int idUsuario, int idUsuarioLogon);
        bool Resetar2FA(int idUsuario);
        bool PertenceAEmpresa(int idempresa, int idUsuario);
        UsuarioCadastrarV2Response CadastrarV2(UsuarioCadastrarV2Request request);

    }
}
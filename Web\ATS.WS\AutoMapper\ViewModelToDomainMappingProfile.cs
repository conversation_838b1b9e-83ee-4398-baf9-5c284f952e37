﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.CrossCutting.Reports.Pedagio.FaturaTAG;
using ATS.CrossCutting.Reports.Pedagio.GridConciliacaoTAG;
using ATS.CrossCutting.Reports.Pedagio.GridFaturamentoTAG;
using ATS.CrossCutting.Reports.Pedagio.GridPagamentosTAG;
using ATS.CrossCutting.Reports.Pedagio.GridPassagemWebhookTAG;
using ATS.CrossCutting.Reports.Pedagio.PracasPedagioMoveMais;
using ATS.Domain.Entities;
using ATS.Domain.Models;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Request;
using ATS.WS.Models.Webservice.Request.Cartoes;
using ATS.WS.Models.Webservice.Request.Credenciamento;
using ATS.WS.Models.Webservice.Request.Empresa;
using ATS.WS.Models.Webservice.Request.Estabelecimento;
using ATS.WS.Models.Webservice.Request.Motorista;
using ATS.WS.Models.Webservice.Request.PagamentoChequesAgrupador;
using ATS.WS.Models.Webservice.Request.Produto;
using ATS.WS.Models.Webservice.Request.Veiculo;
using ATS.WS.Models.Webservice.Response.Notificacao;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using CargaAvulsa = ATS.Domain.Entities.CargaAvulsa;
using ATS.WS.Models.Webservice.Request.Cte;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Campanha;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Models.Menu;
using ATS.Domain.Models.ViagemModels;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using Documento = ATS.WS.Models.Webservice.Request.Estabelecimento.Documento;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.ViagemV2.Integracao;
using ATS.WS.Models.ViagemV2.Request;
using ATS.WS.Models.ViagemV2.Response;
using ATS.WS.Models.Webservice.Request.CadastroRotas;
using ATS.WS.Models.Webservice.Request.Pedagio;
using ATS.WS.Models.Webservice.Request.TipoCavalo;
using ATS.WS.Models.Webservice.Request.Usuario;
using ATS.WS.Services;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using ContaBancariaRequest = ATS.WS.Models.Webservice.Request.Empresa.ContaBancariaRequest;
using Menu = ATS.Domain.Entities.Menu;
using PontosRotaModelo = ATS.Domain.Entities.PontosRotaModelo;
using Sistema.Framework.Util.Extension;
using TagExtrattaClient;
using EmpresaFaturamento = ATS.CrossCutting.Reports.Faturamento.EmpresaFaturamento;

namespace ATS.WS.AutoMapper
{
    public class ViewModelToDomainMappingProfile : Profile
    {
        protected override void Configure()
        {
            CreateMap<DespesaUsuarioAddModel, DespesaUsuario>();

            #region Cidade

            CreateMap<CadastroCidadeRequestModel, Cidade>();

            #endregion
            
            #region Atendimento

            CreateMap<AtendimentoPortadorTramiteRequest, AtendimentoPortadorTramite>();

            #endregion

            #region Usuario

            CreateMap<UsuarioVeiculoRequestModel, Veiculo>();
            CreateMap<UsuarioFilialRequestModel, UsuarioFilial>();
            CreateMap<UsuarioEnderecoRequestModel, UsuarioEndereco>();
            CreateMap<UsuarioContatoRequestModel, UsuarioContato>();
            CreateMap<UsuarioPreferenciasModel, UsuarioPreferencias>();
            CreateMap<UsuarioIntegrarMobRequestModel, Usuario>()
                .ForMember(u => u.Filiais, u => u.Ignore());
            CreateMap<UsuarioIntegrarMobRequestModel, UsuarioContato>();
            CreateMap<UsuarioIntegrarMobRequestModel, UsuarioEndereco>();
            CreateMap<UsuarioIntegrarMobRequestModel, Usuario>();

            CreateMap<UsuarioAtualizarMobRequestModel, Usuario>()
                .ForMember(u => u.HorariosCheckIn, u => u.Ignore())
                .ForMember(u => u.Filiais, u => u.Ignore());

            CreateMap<UsuarioCadastrarV2Request, Usuario>()
                .ForMember(u => u.Filiais, u => u.Ignore());
            CreateMap<UsuarioCadastrarV2Request, UsuarioContato>();
            CreateMap<UsuarioCadastrarV2Request, UsuarioEndereco>();
            CreateMap<UsuarioCadastrarV2Request, Usuario>();
            
            #endregion

            #region Mensagem

            CreateMap<NotificacaoRequestModel, Mensagem>();

            #endregion

            #region Veiculo

            CreateMap<VeiculoRequestModel, Veiculo>();
            CreateMap<VeiculoIntegrarRequestModel, Veiculo>();
            CreateMap<UsuarioIntegrarMobRequestModel, Veiculo>();
            CreateMap<VeiculoCreateRequest, Veiculo>()
                .ForMember(u => u.TipoCavalo, z => z.Ignore());

            CreateMap<VeiculoTipoCombustivelCreateRequest, VeiculoTipoCombustivel>();
            CreateMap<VeiculoConjuntosCreateRequest, Veiculo>();

            CreateMap<UsuarioCadastrarV2Request, Veiculo>();
            
            #endregion

            #region Cliente

            CreateMap<ClienteRequestModel, Cliente>();

            #endregion

            #region Filial

            CreateMap<FilialIntegrarRequestModel, Filial>();

            #endregion

            #region Motorista

            CreateMap<MotoristaIntegrarRequestModel, Motorista>()
                .ForMember(o => o.ValidadeCNH, options => options.Condition(x => x.ValidadeCnh != null))
                .ForMember(o => o.DataNascimento, options => options.Condition(x => x.DataNascimento != null));
            CreateMap<MotoristaPorCpfModel, Motorista>()
                .ForMember(u => u.ValidadeCNH, z => z.MapFrom(w => w.DataValidade));
            CreateMap<MotoristaIntegrarRequest, Motorista>();

            #endregion
            
            #region RotaModeloPadrao

            CreateMap<RotasModeloIntegrarRequest, RotaModelo>()
                .ForMember(o => o.OrigemIbge, options => options.MapFrom(x => x.CodigosIbge.FirstOrDefault()))
                .ForMember(o => o.DestinoIbge, options => options.MapFrom(x => x.CodigosIbge.LastOrDefault()))
                .ForMember(o => o.OrigemDescricao, options => options.MapFrom(x => x.Destinos.FirstOrDefault().Local))
                .ForMember(o => o.DestinoDescricao, options => options.MapFrom(x => x.Destinos.LastOrDefault().Local))
                .ForMember(o => o.OrigemLatitude, options => options.MapFrom(x => x.Destinos.FirstOrDefault().Latitude))
                .ForMember(o => o.OrigemLongitude, options => options.MapFrom(x => x.Destinos.FirstOrDefault().Longitude))
                .ForMember(o => o.DestinoLatitude, options => options.MapFrom(x => x.Destinos.LastOrDefault().Latitude))
                .ForMember(o => o.DestinoLongitude, options => options.MapFrom(x => x.Destinos.LastOrDefault().Longitude))
                .ForMember(o => o.DataCadastro, options => options.MapFrom(x => DateTime.Now));

            CreateMap<PontosRotaModeloRequest, PontosRotaModelo>()
                .ForMember(o => o.Descricao, options => options.MapFrom(x => x.Descricao))
                .ForMember(o => o.Ibge, options => options.MapFrom(x => x.Ibge))
                .ForMember(o => o.Latitude, options => options.MapFrom(x => x.Latitude))
                .ForMember(o => o.Longitude, options => options.MapFrom(x => x.Longitude))
                .ForMember(o => o.IdRotaModelo, options => options.MapFrom(x => x.IdRotaModelo));
            
           
            CreateMap<PracasRotaModeloRequest, PracasRotaModelo>()
                .ForMember(o => o.Descricao, options => options.MapFrom(x => x.Descricao))
                .ForMember(o => o.Valor, options => options.MapFrom(x => x.Valor))
                .ForMember(o => o.ValorTag, options => options.MapFrom(x => x.ValorTag))
                .ForMember(o => o.IdRotaModelo, options => options.MapFrom(x => x.IdRotaModelo));
            
            
            #endregion

            #region Proprietário

            CreateMap<ProprietarioIntegrarRequestModel, Proprietario>();
            CreateMap<ProprietarioIntegrarContatoRequestModel, ProprietarioContato>();
            CreateMap<ProprietarioIntegrarEnderecoRequestModel, ProprietarioEndereco>();

            #endregion

            #region Viagem e CheckIn
            CreateMap<PedagioAvulsoRequest, Viagem>()
                .ForMember(member => member.IsPedagioAvulso, opt => opt.MapFrom(o => true))
                .ForMember(member => member.StatusViagem, opt => opt.MapFrom(o =>  EStatusViagem.Aberto))
                .ForMember(member => member.StatusIntegracao, opt => opt.MapFrom(o => EStatusIntegracao.Integrado))
                .ForMember(member => member.AltoDesempenho, opt => opt.MapFrom(o => false))
                .ForMember(member => member.DestinacaoComercial, opt => opt.MapFrom(o => false))
                .ForMember(member => member.FreteRetorno, opt => opt.MapFrom(o => false))
                .ForMember(member => member.FormaPagamento, opt => opt.MapFrom(o => EViagemFormaPagamento.Indefinido))
                .ForMember(member => member.ViagemEstabelecimentos, opt => opt.Ignore())
                .ForMember(member => member.DataPrevisaoEntrega, opt =>  opt.MapFrom(o => DateTime.Now.AddDays(1)))
                .ForMember(member => member.DataEmissao, opt =>  opt.MapFrom(o => DateTime.Now))
                .ForMember(member => member.DataColeta, opt =>  opt.MapFrom(o => DateTime.Now.AddHours(1)))
                .ForMember(member => member.DataLancamento, opt =>  opt.MapFrom(o => DateTime.Now))
                .ForMember(member => member.ViagemRegras, opt => opt.Ignore())
                .ForMember(member => member.ViagemEventos, opt => opt.Ignore());


            CreateMap<ViagemIntegrarRequestModel, Viagem>()
                .ForMember(member => member.ViagemEstabelecimentos, opt => opt.Ignore())
                .ForMember(member => member.ViagemRegras, opt => opt.Ignore())
                .ForMember(member => member.ViagemEventos, opt => opt.Ignore());

            CreateMap<IntegrarCheckInRequestModel, CheckIn>();

            CreateMap<ViagemEventoIntegrarModel, ViagemEvento>()
                .ForMember(member => member.TipoEventoViagem, opt => opt.MapFrom(x => x.TipoEvento))
                .ForMember(member => member.ViagemDocumentos, opt => opt.Ignore())
                .ForMember(member => member.ViagemSolicitacaoAbono, opt => opt.Ignore())
                .ForMember(member => member.ViagemValoresAdicionais, opt => opt.Ignore());

            CreateMap<ViagemV2DadosPagamentoModel, ViagemDadosPagamentoIntegrarModel>();
            CreateMap<ViagemV2DadosAnttModel, ViagemDadosAnttIntegrarModel>();


            #region Integração Viagem V2 para Viagem

            CreateMap<ViagemV2AutorizacaoEstabelecimentoModel, ViagemEstabelecimentoIntegrarModel>()
                .ForMember(dm => dm.TipoEventoViagem, mo => mo.MapFrom(o => o.TipoEvento));

            CreateMap<ViagemV2IntegracaoRequestModel, ViagemIntegrarRequestModel>()
                .ForMember(dm => dm.CNPJFilial, mo => mo.MapFrom(o => o.DadosViagem.Documentos.FilialDocumento))
                .ForMember(dm => dm.CPFCNPJClienteDestino,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ClienteDestinoDocumento))
                .ForMember(dm => dm.CPFCNPJClienteOrigem,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ClienteOrigemDocumento))
                .ForMember(dm => dm.CPFMotorista, mo => mo.MapFrom(o => o.DadosViagem.Documentos.MotoristaDocumento))
                .ForMember(dm => dm.CPFCNPJProprietario,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ProprietarioDocumento))
                .ForMember(dm => dm.CPFCNPJClienteTomador,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ClienteTomadorDocumento))
                .ForMember(dm => dm.CodigoTipoCarga, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.CodigoTipoCarga))
                .ForMember(dm => dm.GerarCiotTacAgregado,
                    mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.GerarCiotTacAgregado))
                .ForMember(dm => dm.RNTRC, mo => mo.MapFrom(o => o.DadosViagem.Veiculo.Rntrc))
                .ForMember(dm => dm.IdCarga, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.CargaId))
                .ForMember(dm => dm.Placa, mo => mo.MapFrom(o => o.DadosViagem.Veiculo.Placa))
                .ForMember(dm => dm.CarretasViagemV2, mo => mo.MapFrom(o => o.DadosViagem.CadastrosPreViagem.Carretas))
                .ForMember(dm => dm.DataColeta, mo => mo.MapFrom(o => o.DadosViagem.Datas.DataColeta))
                .ForMember(dm => dm.DataPrevisaoEntrega, mo => mo.MapFrom(o => o.DadosViagem.Datas.DataPrevisaoEntrega))
                .ForMember(dm => dm.DocumentoCliente,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.NumeroDocumentoNotaCliente))
                .ForMember(dm => dm.Coleta, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.EnderecoColeta))
                .ForMember(dm => dm.Entrega, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.EnderecoEntrega))
                .ForMember(dm => dm.CepOrigem, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.CepOrigem))
                .ForMember(dm => dm.CepDestino, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.CepDestino))
                .ForMember(dm => dm.NumeroDocumento,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.NumeroDocumentoFiscal))
                .ForMember(dm => dm.DataEmissao, mo => mo.MapFrom(o => o.DadosViagem.Datas.DataEmissaoDocumentoFiscal))
                .ForMember(dm => dm.Produto, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.Produto))
                .ForMember(dm => dm.NaturezaCarga, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.NaturezaCarga))
                .ForMember(dm => dm.Unidade, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.UnidadeMedidaProduto))
                .ForMember(dm => dm.Quantidade, mo => mo.MapFrom(o => o.DadosViagem.Valores.Quantidade))
                .ForMember(dm => dm.PesoSaida, mo => mo.MapFrom(o => o.DadosViagem.Valores.PesoSaida))
                .ForMember(dm => dm.ValorMercadoria, mo => mo.MapFrom(o => o.DadosViagem.Valores.ValorMercadoria))
                .ForMember(dm => dm.IRRPF, mo => mo.MapFrom(o => o.DadosViagem.Valores.Irrpf))
                .ForMember(dm => dm.INSS, mo => mo.MapFrom(o => o.DadosViagem.Valores.Inss))
                .ForMember(dm => dm.SESTSENAT, mo => mo.MapFrom(o => o.DadosViagem.Valores.SestSenat))
                .ForMember(dm => dm.NumeroControle, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.NumeroControle))
                .ForMember(dm => dm.HabilitarDeclaracaoCiot,
                    mo => mo.MapFrom(o =>
                        o.DadosViagem.DadosIniciais.DeclaracaoCiot != EDeclaracaoCiotViagem.NaoHabilitarDeclaracaoCiot))
                .ForMember(dm => dm.ForcarCiotNaoEquiparado,
                    mo => mo.MapFrom(o =>
                        o.DadosViagem.DadosIniciais.DeclaracaoCiot ==
                        EDeclaracaoCiotViagem.ForcarDeclaracaoCiotNaoEquiparado))
                .ForMember(dm => dm.ViagemEventos, mo => mo.MapFrom(o => o.DadosViagem.ViagemEventos))
                .ForMember(dm => dm.Pedagio, mo => mo.MapFrom(o => o.DadosViagem.Pedagio))
                .ForMember(dm => dm.Webhook, mo => mo.MapFrom(o => o.DadosViagem.Webhook))
                .ForMember(dm => dm.DocumentosFiscais, mo => mo.MapFrom(o => o.DadosViagem.DocumentosFiscais))
                .ForMember(dm => dm.ViagemRegra, mo => mo.MapFrom(o => o.DadosViagem.ViagemRegra))
                .ForMember(dm => dm.DadosBancarioPagamentoSemCartao,
                    mo => mo.MapFrom(o => o.DadosViagem.DadosPagamento))
                .ForMember(dm => dm.DadosAntt, mo => mo.MapFrom(o => o.DadosViagem.DadosAntt))
                .ForMember(dm => dm.DistanciaViagem,
                    mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.DistanciaViagem))
                .ForMember(dm => dm.ViagemEstabelecimentos,
                    mo => mo.MapFrom(o => o.DadosViagem.AutorizacaoEstabelecimentos))
                .ForMember(dm => dm.CarretasV2, mo => mo.MapFrom(o => o.DadosViagem.CadastrosPreViagem.Carretas)); 

            CreateMap<ViagemV2DadosPagamentoModel, ViagemIntegrarDadosBancarioSemCartao>()
                .ForMember(dm => dm.IdBanco, mo => mo.MapFrom(o => o.CodigoBacen))
                .ForMember(dm => dm.ContaCorrente, mo => mo.MapFrom(o => o.Conta))
                .ForMember(dm => dm.TipoConta, mo => mo.MapFrom(o => o.TipoConta))
                .ForMember(dm => dm.FormaPagamentoSemCartao, mo => mo.MapFrom(o => o.FormaPagamento));

            CreateMap<ViagemCarretasV2IntegrarModel, ViagemCarretasIntegrarModel>()
                .ForMember(dm => dm.Placa, mo => mo.MapFrom(o => o.Placa))
                .ForMember(dm => dm.Rntrc, mo => mo.MapFrom(o => o.Rntrc))
                .ForMember(dm => dm.CPFCNPJProprietario, mo => mo.MapFrom(o => o.CPFCNPJProprietario));
            
            #endregion

            #region Alteração Viagem V2 para Viagem

            CreateMap<ViagemV2AlteracaoRequestModel, ViagemIntegrarRequestModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.ViagemId))
                .ForMember(dm => dm.CNPJFilial, mo => mo.MapFrom(o => o.DadosViagem.Documentos.FilialDocumento))
                .ForMember(dm => dm.CPFCNPJClienteDestino,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ClienteDestinoDocumento))
                .ForMember(dm => dm.CPFCNPJClienteOrigem,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ClienteOrigemDocumento))
                .ForMember(dm => dm.CPFMotorista, mo => mo.MapFrom(o => o.DadosViagem.Documentos.MotoristaDocumento))
                .ForMember(dm => dm.CPFCNPJProprietario,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ProprietarioDocumento))
                .ForMember(dm => dm.CPFCNPJClienteTomador,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.ClienteTomadorDocumento))
                .ForMember(dm => dm.CodigoTipoCarga, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.CodigoTipoCarga))
                .ForMember(dm => dm.GerarCiotTacAgregado, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.GerarCiotTacAgregado))
                .ForMember(dm => dm.RNTRC, mo => mo.MapFrom(o => o.DadosViagem.Veiculo.Rntrc))
                .ForMember(dm => dm.IdCarga, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.CargaId))
                .ForMember(dm => dm.Placa, mo => mo.MapFrom(o => o.DadosViagem.Veiculo.Placa))
                .ForMember(dm => dm.CarretasViagemV2, mo => mo.MapFrom(o => o.DadosViagem.CadastrosPreViagem.Carretas))
                .ForMember(dm => dm.DataColeta, mo => mo.MapFrom(o => o.DadosViagem.Datas.DataColeta))
                .ForMember(dm => dm.DataPrevisaoEntrega, mo => mo.MapFrom(o => o.DadosViagem.Datas.DataPrevisaoEntrega))
                .ForMember(dm => dm.DocumentoCliente,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.NumeroDocumentoNotaCliente))
                .ForMember(dm => dm.Coleta, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.EnderecoColeta))
                .ForMember(dm => dm.Entrega, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.EnderecoEntrega))
                .ForMember(dm => dm.CepOrigem, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.CepOrigem))
                .ForMember(dm => dm.CepDestino, mo => mo.MapFrom(o => o.DadosViagem.Enderecos.CepDestino))
                .ForMember(dm => dm.NumeroDocumento,
                    mo => mo.MapFrom(o => o.DadosViagem.Documentos.NumeroDocumentoFiscal))
                .ForMember(dm => dm.DataEmissao, mo => mo.MapFrom(o => o.DadosViagem.Datas.DataEmissaoDocumentoFiscal))
                .ForMember(dm => dm.Produto, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.Produto))
                .ForMember(dm => dm.NaturezaCarga, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.NaturezaCarga))
                .ForMember(dm => dm.Unidade, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.UnidadeMedidaProduto))
                .ForMember(dm => dm.Quantidade, mo => mo.MapFrom(o => o.DadosViagem.Valores.Quantidade))
                .ForMember(dm => dm.PesoSaida, mo => mo.MapFrom(o => o.DadosViagem.Valores.PesoSaida))
                .ForMember(dm => dm.ValorMercadoria, mo => mo.MapFrom(o => o.DadosViagem.Valores.ValorMercadoria))
                .ForMember(dm => dm.IRRPF, mo => mo.MapFrom(o => o.DadosViagem.Valores.Irrpf))
                .ForMember(dm => dm.INSS, mo => mo.MapFrom(o => o.DadosViagem.Valores.Inss))
                .ForMember(dm => dm.SESTSENAT, mo => mo.MapFrom(o => o.DadosViagem.Valores.SestSenat))
                .ForMember(dm => dm.NumeroControle, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.NumeroControle))
                .ForMember(dm => dm.HabilitarDeclaracaoCiot, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.DeclaracaoCiot != EDeclaracaoCiotViagem.NaoHabilitarDeclaracaoCiot))
                .ForMember(dm => dm.ForcarCiotNaoEquiparado, mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.DeclaracaoCiot == EDeclaracaoCiotViagem.ForcarDeclaracaoCiotNaoEquiparado))
                .ForMember(dm => dm.ViagemEventos, mo => mo.MapFrom(o => o.DadosViagem.ViagemEventos))
                .ForMember(dm => dm.Pedagio, mo => mo.MapFrom(o => o.DadosViagem.Pedagio))
                .ForMember(dm => dm.Webhook, mo => mo.MapFrom(o => o.DadosViagem.Webhook))
                .ForMember(dm => dm.DocumentosFiscais, mo => mo.MapFrom(o => o.DadosViagem.DocumentosFiscais))
                .ForMember(dm => dm.ViagemRegra, mo => mo.MapFrom(o => o.DadosViagem.ViagemRegra))
                .ForMember(dm => dm.DadosPagamento, mo => mo.MapFrom(o => o.DadosViagem.DadosPagamento))
                .ForMember(dm => dm.DadosAntt, mo => mo.MapFrom(o => o.DadosViagem.DadosAntt))
                .ForMember(dm => dm.DistanciaViagem,
                    mo => mo.MapFrom(o => o.DadosViagem.DadosIniciais.DistanciaViagem))
                .ForMember(dm => dm.ViagemEstabelecimentos, mo => mo.MapFrom(o => o.DadosViagem.AutorizacaoEstabelecimentos));

            #endregion


            CreateMap<ViagemIntegrarResponseModel, ViagemV2IntegracaoResponseModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.IdViagem))
                .ForMember(dm => dm.NumeroDocumento, mo => mo.MapFrom(o => o.NumeroDocumento))
                .ForMember(dm => dm.Irrpf, mo => mo.MapFrom(o => o.IRRPF))
                .ForMember(dm => dm.Inss, mo => mo.MapFrom(o => o.INSS))
                .ForMember(dm => dm.SestSenat, mo => mo.MapFrom(o => o.SESTSENAT))
                .ForMember(dm => dm.Ciot, mo => mo.MapFrom(o => o.CIOT))
                .ForMember(dm => dm.Pedagio, mo => mo.MapFrom(o => o.Pedagio))
                .ForMember(dm => dm.IdsViagemEstabelecimento, mo => mo.MapFrom(o => o.IdsViagemEstabelecimento))
                .ForMember(dm => dm.Eventos, mo => mo.MapFrom(o => o.Eventos))
                .ForMember(dm => dm.IntegracoesPreViagem, mo => mo.Ignore());

            CreateMap<ViagemV2BaixarEventoRequestModel, BaixarEventoRequestModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.ViagemId))
                .ForMember(dm => dm.IdViagemEvento, mo => mo.MapFrom(o => o.ViagemEventoId))
                .ForMember(dm => dm.HabilitarPagamentoCartao, mo => mo.Ignore())
                .ForMember(dm => dm.NumeroControleEvento, mo => mo.Ignore())
                .ForMember(dm => dm.NumeroControleViagem, mo => mo.Ignore());

            CreateMap<ViagemV2CancelarEventoRequestModel, CancelarEventoRequestModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.ViagemId))
                .ForMember(dm => dm.IdViagemEvento, mo => mo.MapFrom(o => o.ViagemEventoId))
                .ForMember(dm => dm.NumeroControleEvento, mo => mo.Ignore())
                .ForMember(dm => dm.NumeroControleViagem, mo => mo.Ignore());

            CreateMap<ViagemV2BloquearEventoRequestModel, BloquearEventoRequestModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.ViagemId))
                .ForMember(dm => dm.IdViagemEvento, mo => mo.MapFrom(o => o.ViagemEventoId))
                .ForMember(dm => dm.NumeroControleEvento, mo => mo.Ignore())
                .ForMember(dm => dm.NumeroControleViagem, mo => mo.Ignore());

            CreateMap<ViagemV2DesbloquearEventoRequestModel, DesbloquearEventoRequestModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.ViagemId))
                .ForMember(dm => dm.IdViagemEvento, mo => mo.MapFrom(o => o.ViagemEventoId))
                .ForMember(dm => dm.NumeroControleEvento, mo => mo.Ignore())
                .ForMember(dm => dm.NumeroControleViagem, mo => mo.Ignore());

            CreateMap<ViagemV2AdicionarEventosRequestModel, AdicionarEventosViagemRequestModel>()
                .ForMember(dm => dm.IdViagem, mo => mo.MapFrom(o => o.ViagemId))
                .ForMember(dm => dm.NumeroControle, mo => mo.MapFrom(o => o.NumeroControle))
                .ForMember(dm => dm.ViagemEventos, mo => mo.MapFrom(o => o.Eventos));

            CreateMap<ViagemV2AdicionarEventoRequestModel, ViagemEventoValoresModel>()
                .ForMember(dm => dm.Status, mo => mo.MapFrom(o => o.Status))
                .ForMember(dm => dm.NumeroControle, mo => mo.MapFrom(o => o.NumeroControle))
                .ForMember(dm => dm.TipoEvento, mo => mo.MapFrom(o => o.TipoEvento))
                .ForMember(dm => dm.ValorPagamento, mo => mo.MapFrom(o => o.ValorPagamento))
                .ForMember(dm => dm.HabilitarPagamentoCartao, mo => mo.MapFrom(o => o.HabilitarPagamentoCartao))
                .ForMember(dm => dm.IdViagemEvento, mo => mo.Ignore())
                .ForMember(dm => dm.ValorTotalPagamento, mo => mo.MapFrom(o => o.ValorTotalPagamento));


            CreateMap<ViagemV2CompraPedagioRequestModel, PedagioModel>()
                .ForMember(dm => dm.ValorPedagio, mo => mo.MapFrom(o => o.ValorPedagio))
                .ForMember(dm => dm.Fornecedor, mo => mo.MapFrom(o => o.Fornecedor))
                .ForMember(dm => dm.Localizacoes, mo => mo.MapFrom(o => o.Localizacoes))
                .ForMember(dm => dm.IdentificadorHistorico, mo => mo.MapFrom(o => o.IdentificadorHistorico))
                .ForMember(dm => dm.QtdEixos, mo => mo.MapFrom(o => o.QuantidadeEixos))
                .ForMember(dm => dm.TipoVeiculo, mo => mo.MapFrom(o => o.TipoVeiculoPedagio));

            #endregion

            #region Tipo notificação

            CreateMap<TipoNotificacao, TipoNotificacaoRequestModel>();
            CreateMap<TipoNotificacao, TipoNotificacaoResponse>();

            #endregion

            #region Notificação

            CreateMap<Notificacao, ConsultarMobileResponse>();

            #endregion

            #region Notificação Push

            CreateMap<NotificacaoPush, NotificacaoPushRequestModel>();

            #endregion

            #region Estabelecimento

            CreateMap<Estabelecimento, EstabelecimentoCrud>();
            CreateMap<Documento, EstabelecimentoBaseDocumento>();
            CreateMap<EstabelecimentoBase, EstabelecimentoCrud>();

            #endregion

            #region credenciamento

            CreateMap<TipoMotivoRequest, TipoMotivo>();
            CreateMap<MotivoRequestModel, Motivo>();

            #endregion

            #region Produto

            CreateMap<ProdutoModel, Produto>();
            CreateMap<ProdutoCrud, Produto>();

            #endregion

            #region Proprietario

            CreateMap<ClienteEnderecoModel, ClienteEndereco>();

            #endregion


            #region Empresa

            CreateMap<EmpresaCreateRequest, Empresa>();
            CreateMap<ContaBancariaRequest, EmpresaContaBancaria>();

            CreateMap<EmpresaCreateModuloRequest, EmpresaModulo>();

            #endregion
            
            #region Pedagio

            CreateMap<CartaoConsultarPedagioAtsRequest, ConsultaCompraPedagioRequest>()
                .ForMember(c => c.Fornecedor, opts => opts.Ignore())
                .ForMember(c => c.Id, opts => opts.Ignore())
                .ForMember(c => c.Status, opts => opts.MapFrom(c => c.StatusCompraList));

            #endregion

            #region Carga avulsa

            CreateMap<CargaAvulsaRequestModel, CargaAvulsa>()
                .ForMember(d => d.CPFCNPJUsuario, opts => opts.MapFrom(s => s.CPFUsuario))
                .ForMember(d => d.CPFMototista, opts => opts.MapFrom(s => s.CPFMototista.OnlyNumbers()));

            #endregion

            #region Expedição

            CreateMap<IntegrarCteRequest, Cte>();

            #endregion

            ContratoCiotAgregadoMap();
            ConsultaFrotaTransportadorMap();

            #region Permissões Mobile

            CreateMap<UsuarioPermissoesConcedidasMobileModel, UsuarioPermissoesConcedidasMobile>();
            CreateMap<UsuarioPermissoesConcedidasMobileRequest, UsuarioPermissoesConcedidasMobile>();

            #endregion
            
            #region Tipo Cavalo

            CreateMap<TipoCavaloCadastrarRequest, TipoCavalo>()
                .ForMember(o => o.Ativo, opt => opt.MapFrom(o => true))
                .ForMember(o => o.DataHoraUltimaAtualizacao, opt => opt.MapFrom(o => DateTime.Now));

            #endregion

            #region Menu

            CreateMap<MenuCadastroRequest, Menu>();

            #endregion
            
            #region Despesas Viagem

            CreateMap<ResgatarValorDespesasViagemRequest, ResgatarValorDTO>();

            #endregion
            
            #region Campanha

            CreateMap<IntegrarCampanhaRequest, Campanha>();
            CreateMap<ResponderCampanhaRequest, CampanhaResposta>();

            #endregion
            
             #region TAG
            CreateMap<QueryFilters, ApiQueryFilters>();
            
            CreateMap<TagRemessaEnvioRequest, RemessaCadastrarModelRequest>()
                .ForMember(p => p.EmpresaId, opts => opts.MapFrom(c => c.IdEmpresa))
                .ForMember(p => p.TagsRemessa, opts => opts.MapFrom(c => c.Tags));
            
            CreateMap<TagRemessaEnvioItem, TagRemessaItem>();
            
            CreateMap<TagGetSerialModelResponse, TagReduzidaResponse>()
                .ForMember(p => p.DataCadastro, opts => opts.MapFrom(c => c.DataCriacao.ToString("dd/MM/yyyy HH:mm:ss")))
                .ForMember(p => p.SerialNumber, opts => opts.MapFrom(c => c.SerialNumber));

            CreateMap<List<permissaoTag>, BloqueiosTagUsuarioRequest>()
                .ForMember(p => p.BloqueiosTag, opts => opts.MapFrom(c => c));

            CreateMap<permissaoTag, BloqueiosTagUsuarioItemRequest>();

            CreateMap<TagRemessaEnvioItem, TagRemessaItem>();
            
            CreateMap<BloqueiosTagUsuarioRequest, BloqueioCadastrarModelRequest>()
                .ForMember(p => p.Bloqueio, opts => opts.MapFrom(c => c.BloqueiosTag));
            
            CreateMap<BloqueiosTagUsuarioItemRequest, BloqueioCadastrarItemAppRequest>();
            
            CreateMap<PracasPedagioResponseDTO, RelatorioPracasPedagioMoveMaisDataType>()
                .ForMember(p => p.items, opts => opts.MapFrom(c => c.Pracas));

            CreateMap<PracasPedagioItemResponseDTO, RelatorioPracasPedagioMoveMaisItemDataType>()
                .ForMember(p => p.Planejado, opts => opts.MapFrom(c => c.Planejado ? "Sim" : "Não"));
            
            CreateMap<GridValePedagioHubItemResponse, RelatorioGridConciliacaoTagItemDataType>();
            CreateMap<GridPassagemWebhookItemResponse, RelatorioGridPassagemWebhookTagItemDataType>();

            CreateMap<ProvisionarValorTagWebhookRequestModel, ProvisionarRequest>()
                .ForMember(p => p.IdViagem, opts => opts.MapFrom(c => c.ViagemId))
                .ForMember(p => p.Origem, opts => opts.MapFrom(c => c.ViagemId.HasValue ? OrigemProvisionamento.ValePedagio : OrigemProvisionamento.Pedagio));

            CreateMap<NotificarPassagemRequestModel, PassagemPracaPedagioModelRequest>()
                .ForMember(p => p.ProtocoloVp, opts => opts.MapFrom(c => c.ProcotocoloVp));
            
            CreateMap<PagamentosItemTagResponse, RelatorioGridPagamentosTagItemDataType>()
                .ForMember(p => p.DataCriacao, opts => opts.MapFrom(c => c.DataEmissao))
                .ForMember(p => p.ValorTaxa, opts => opts.MapFrom(c => c.Taxa))
                .ForMember(p => p.ValorPassagem, opts => opts.MapFrom(c => c.Valor));
            CreateMap<FornecedorEnum, FornecedorTagEnum>().ConvertUsing(src => (FornecedorTagEnum)(int)src);
            CreateMap<StatusPlaca, StatusPlacaFornecedor>().ConvertUsing(src => (StatusPlacaFornecedor)(int)src);
            CreateMap<ContestarPassagemRequest,ContestacaoPassagemPedagioModelRequest>();
            #endregion

            #region Faturamento

            CreateMap<FaturamentoGridDto, EmpresaFaturamento>()
                .ForMember(p => p.Adiantamento, opts => opts.MapFrom(c => c.Adiantamento.FormatMoney()))
                .ForMember(p => p.Saldo, opts => opts.MapFrom(c => c.Saldo.FormatMoney()))
                .ForMember(p => p.TarifaANTT, opts => opts.MapFrom(c => c.TarifaANTT.FormatMoney()))
                .ForMember(p => p.Estadias, opts => opts.MapFrom(c => c.Estadias.FormatMoney()))
                .ForMember(p => p.Abastecimento, opts => opts.MapFrom(c => c.Abastecimento.FormatMoney()))
                .ForMember(p => p.Pedagio, opts => opts.MapFrom(c => c.Pedagio.FormatMoney()))
                .ForMember(p => p.CargaAvulsa, opts => opts.MapFrom(c => c.CargaAvulsa.FormatMoney()))
                .ForMember(p => p.Total, opts => opts.MapFrom(c => c.Total.FormatMoney()))
                .ForMember(p => p.Estornos, opts => opts.MapFrom(c => c.Estornos.FormatMoney()));

             CreateMap<FaturamentoTotalizadorRequest, FaturamentoTotalizadorGetRequest>()
                 .ForMember(p => p.FornecedorTag, opts => opts.MapFrom(c => (FornecedorTagEnum)c.Fornecedor));
             
            CreateMap<FaturamentoTagItemResponse, RelatorioGridFaturamentoTagItemDataType>();
            CreateMap<FaturamentoTotalizadorResponse, TotalizadorFaturamentoTagDataType>();
            
            CreateMap<Totalizador, TotalizadorTagResponse>();
            CreateMap<PassagensVPFornecedor, PassagensVPFornecedorTagResponse>();
            CreateMap<PassagensValePedagio, PassagensValePedagioTag>();
            CreateMap<PassagensPedagio, PassagensPedagioTagResponse>();
            CreateMap<MensalidadeTag, MensalidadeTagResponse>();
            CreateMap<EmpresaFaturamentoTag, EmpresaFaturamentoTagResponse>();
            CreateMap<DadosGerais, DadosGeraisTagResponse>();
            CreateMap<Tributaveis, TributaveisTagResponse>();
            CreateMap<NaoTributaveis, NaoTributaveisTagResponse>();
            
            CreateMap<FaturaGetModelResponse, FaturaTagGetResponse>();
            CreateMap<FaturaTagGetResponse, RelatorioFaturaTagDataType>();
            CreateMap<PassagensVPFornecedorTagResponse, PassagensVPFornecedorTagDataTypeResponse>();

            CreateMap<PassagensValePedagioTag, PassagensValePedagioTagDataType>()
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Placa == null ? "Nenhuma vinculada" : c.Placa.FormatarPlaca()))
                .ForMember(p => p.DataCriacao, opts => opts.MapFrom(c => c.DataCriacao.FormatDateTimeBr()))
                .ForMember(p => p.Valor, opts => opts.MapFrom(c => c.Valor.FormatMoney()))
                .ForMember(p => p.Taxa, opts => opts.MapFrom(c => c.Taxa.FormatMoney()));
            
            CreateMap<PassagensPedagioTagResponse, PassagensPedagioTagDataTypeResponse>()
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Placa == null ? "Nenhuma vinculada" : c.Placa.FormatarPlaca()))
                .ForMember(p => p.DataPassagem, opts => opts.MapFrom(c => c.DataPassagem.FormatDateTimeBr()))
                .ForMember(p => p.Valor, opts => opts.MapFrom(c => c.Valor.FormatMoney()))
                .ForMember(p => p.Taxa, opts => opts.MapFrom(c => c.Taxa.FormatMoney()));

            CreateMap<MensalidadeTagResponse, MensalidadeTagDataTypeResponse>()
                .ForMember(p => p.Valor, opts => opts.MapFrom(c => c.Valor.FormatMoney()))
                .ForMember(p => p.Placa, opts => opts.MapFrom(c => c.Placa == null ? "Nenhuma vinculada" : c.Placa.FormatarPlaca()));
           
            CreateMap<EmpresaFaturamentoTagResponse, EmpresaFaturamentoTagDataTypeResponse>()
                .ForMember(p => p.Cnpj, opts => opts.MapFrom(c => c.Cnpj.ToCnpjFormato()))
                .ForMember(p => p.Telefone, opts => opts.MapFrom(c => c.Telefone.FormatAsTelefone()));

            CreateMap<DadosGeraisTagResponse, DadosGeraisTagDataTypeResponse>()
                .ForMember(p => p.Emissao, opts => opts.MapFrom(c => c.Emissao.FormatDateTimeBr()))
                .ForMember(p => p.Vencimento, opts => opts.MapFrom(c => c.Vencimento.FormatDateTimeBr()));

            CreateMap<TributaveisTagResponse, TributaveisTagDataTypeResponse>()
                .ForMember(p => p.MensalidadeValorTotal, opts => opts.MapFrom(c => c.MensalidadeValorTotal.FormatMoney()))
                .ForMember(p => p.ValorTotal, opts => opts.MapFrom(c => c.ValorTotal.FormatMoney()));

            CreateMap<NaoTributaveisTagResponse, NaoTributaveisTagDataTypeResponse>()
                .ForMember(p => p.PassagensValePedagioValorTotal, opts => opts.MapFrom(c => c.PassagensValePedagioValorTotal.FormatMoney()))
                .ForMember(p => p.PassagensPedagioValorTotal, opts => opts.MapFrom(c => c.PassagensPedagioValorTotal.FormatMoney()))
                .ForMember(p => p.EstornoValePedagioTotal, opts => opts.MapFrom(c => c.EstornoValePedagioTotal.FormatMoney()))
                .ForMember(p => p.EstornoPedagioTotal, opts => opts.MapFrom(c => c.EstornoPedagioTotal.FormatMoney()))
                .ForMember(p => p.ValorTotal, opts => opts.MapFrom(c => c.ValorTotal.FormatMoney()));
                
            CreateMap<TotalizadorTagResponse, TotalizadorTagDataTypeResponse>()
                .ForMember(p => p.EstornoPago, opts => opts.MapFrom(c => c.EstornoPago.FormatMoney()))
                .ForMember(p => p.EstornoNaoPago, opts => opts.MapFrom(c => c.EstornoNaoPago.FormatMoney()))
                .ForMember(p => p.TotalPago, opts => opts.MapFrom(c => c.TotalPago.FormatMoney()))
                .ForMember(p => p.TotalNaoPago, opts => opts.MapFrom(c => c.TotalNaoPago.FormatMoney()))
                .ForMember(p => p.ValorFatura, opts => opts.MapFrom(c => c.ValorFatura.FormatMoney()));
            #endregion
        }

        private void ContratoCiotAgregadoMap()
        {
            CreateMap<ContratoAberturaModel, ContratoCiotAgregado>()
                //.ForMember(d => d.DataCadastro, opts => opts.NullSubstitute(DateTime.Now))
                .ForMember(d => d.ContratoCiotAgregadoVeiculos, opts => opts.MapFrom(s => s.Veiculos))
                .ForAllMembers(m => m.Condition(context => context.SourceValue != null));
            CreateMap<VeiculoModelAgregado, ContratoCiotAgregadoVeiculo>();

            CreateMap<ObterExtratoCreditoResponseItem, ObterExtratoSemPararItemResponseDTO>();
        }

        private void ConsultaFrotaTransportadorMap()
        {
            CreateMap<ConsultarFrotaTransportadorRequestModel, ConsultarFrotaTransportadorRequest>();
        }
    }
}
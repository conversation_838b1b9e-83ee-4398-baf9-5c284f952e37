﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.Menu;

namespace ATS.Application.Application
{
    public class ModuloApp : AppBase, IModuloApp
    {
        private readonly IModuloService _moduloService;

        public ModuloApp(IModuloService moduloService)
        {
            _moduloService = moduloService;
        }

        /// <summary>
        /// Método utilizado para buscar Módulo.
        /// </summary>
        /// <param name="id">Id de Módulo</param>
        /// <returns>Entidade Modulo</returns>
        public Modulo Get(int id)
        {
            return _moduloService.Get(id);
        }

        /// <summary>
        /// Método utilizado para incluir Módulo.
        /// </summary>
        /// <param name="modulo">Entidade de Módulo</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Add(Modulo modulo)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _moduloService.Add(modulo);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para alterar Módulo.
        /// </summary>
        /// <param name="modulo">Entidade de Módulo</param>
        /// <returns>Objeto ValidationResult</returns>
        public ValidationResult Update(Modulo modulo)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _moduloService.Update(modulo);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Método utilizado para listar todos os Módulos.
        /// </summary>
        /// <returns>IQueryable de Modulo</returns>
        public IQueryable<Modulo> All()
        {
            return _moduloService.All();
        }

        /// <summary>
        /// Método utilizado para consultar Módulo.
        /// </summary>
        /// <param name="descricao">Descrição do Módulo</param>
        /// <returns>IQueryable de Módulo</returns>
        public IQueryable<Modulo> Consultar(string descricao)
        {
            return _moduloService.Consultar(descricao);
        }

        /// <summary>
        /// Inativa um módulo
        /// </summary>
        /// <param name="id">ID do módulo</param>
        /// <returns></returns>
        public ValidationResult Inativar(int id)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _moduloService.Inativar(id);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativa um módulo
        /// </summary>
        /// <param name="id">ID do módulo</param>
        /// <returns></returns>
        public ValidationResult Reativar(int id)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _moduloService.Reativar(id);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public object GetModulosPorUsuario(int idUsuario)
        {
            return _moduloService.GetModulosPorUsuario(idUsuario);
        }

        /// <summary>
        /// Retorna todos os módulos ao qual a empresa possui.
        /// </summary>
        /// <returns></returns>
        public List<Modulo> GetModulosPorEmpresa(int idEmpresa)
        {
            return _moduloService.GetModulosPorEmpresa(idEmpresa);
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _moduloService.ConsultarGrid(take, page, order, filters);
        }

        public List<ModulosCadastroMenu> GetModulosCadastroMenu()
        {
            return _moduloService.All().OrderBy(o => o.Descricao).Select(o => new ModulosCadastroMenu
            {
                Codigo = o.IdModulo,
                Descricao = o.Descricao,
                Marcado = false
            }).ToList();
        }
    }
}
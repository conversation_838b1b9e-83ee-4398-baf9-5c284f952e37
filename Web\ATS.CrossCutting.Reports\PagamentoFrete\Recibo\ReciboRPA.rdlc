<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Tablix3">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>6.88259cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>8.62229cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox12">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Tipo:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox12</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ViagemEventoTipo">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ViagemEventoTipo.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ViagemEventoTipo</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox26">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Recibo:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox26</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ViagemEventoNumeroRecibo">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ViagemEventoNumeroRecibo.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ViagemEventoNumeroRecibo</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox28">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Valor Líquido:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox28</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ViagemEventoValorTotalPagamento">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ViagemEventoValorTotalPagamento.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ViagemEventoValorTotalPagamento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox32">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Data Operação:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox32</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Color>LightGrey</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ViagemEventoDataHoraPagamento">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ViagemEventoDataHoraPagamento.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ViagemEventoDataHoraPagamento</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                        </TopBorder>
                        <LeftBorder>
                          <Color>White</Color>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.6cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox2">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Usuario:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox2</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>White</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Color>LightGrey</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Usuario">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Usuario.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Usuario</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                        </TopBorder>
                        <LeftBorder>
                          <Color>White</Color>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox34">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Transportador:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox34</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="MotoristaNome">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!MotoristaNome.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>MotoristaNome</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox36">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>CPF / CNPJ:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox36</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ProprietarioCpfCnpj">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ProprietarioCpfCnpj.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ProprietarioCpfCnpj</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox38">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>RNTRC:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox38</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ProprietarioRntrc">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ProprietarioRntrc.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ProprietarioRntrc</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.81754cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox14">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Impostos</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox14</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox11">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>IRRF:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox11</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="IRRF">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!IRRF.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>IRRF</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox9">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>INSS:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox9</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="INSS">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!INSS.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>INSS</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox21">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>SEST/SENAT:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox21</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="SESTSENAT">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!SESTSENAT.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>SESTSENAT</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>White</Color>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.60587cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox19">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>ISSQN:</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox19</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>White</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>White</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ISSQN">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ISSQN.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ISSQN</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>White</Color>
                        </TopBorder>
                        <LeftBorder>
                          <Color>White</Color>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.81754cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox31">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Instruções</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox31</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.85458cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Instrucoes">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Instrucoes.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <ListLevel>1</ListLevel>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Instrucoes</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <LeftBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>1pt</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>2</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Detalhes" />
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>pagamentoFreteReciboModel</DataSetName>
        <Height>9.75423cm</Height>
        <Width>15.50488cm</Width>
        <Style>
          <Border>
            <Color>Gray</Color>
            <Style>Solid</Style>
          </Border>
          <TopBorder>
            <Color>Gray</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </TopBorder>
          <BottomBorder>
            <Color>Gray</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </BottomBorder>
          <LeftBorder>
            <Color>Gray</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </LeftBorder>
          <RightBorder>
            <Color>Gray</Color>
            <Style>Solid</Style>
            <Width>1pt</Width>
          </RightBorder>
        </Style>
      </Tablix>
      <Line Name="Line3">
        <Top>11.06863cm</Top>
        <Left>4.0856cm</Left>
        <Height>0cm</Height>
        <Width>7.91577cm</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>Solid</Style>
          </Border>
        </Style>
      </Line>
      <Textbox Name="Textbox1">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>Assinatura</Value>
                <Style>
                  <FontSize>9pt</FontSize>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox1</rd:DefaultName>
        <Top>11.10391cm</Top>
        <Left>4.0856cm</Left>
        <Height>0.70583cm</Height>
        <Width>7.91577cm</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
    </ReportItems>
    <Height>4.75723in</Height>
    <Style />
  </Body>
  <Width>6.10428in</Width>
  <Page>
    <PageHeader>
      <Height>3.28245cm</Height>
      <PrintOnFirstPage>true</PrintOnFirstPage>
      <PrintOnLastPage>true</PrintOnLastPage>
      <ReportItems>
        <Image Name="Image1">
          <Source>Database</Source>
          <Value>=Fields!Barcode.Value</Value>
          <MIMEType>image/jpeg</MIMEType>
          <Sizing>Fit</Sizing>
          <Top>1.90583cm</Top>
          <Height>1.37662cm</Height>
          <Width>8.55204cm</Width>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </TopBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </LeftBorder>
          </Style>
        </Image>
        <Image Name="EmpresaLogo">
          <Source>Database</Source>
          <Value>=First(Fields!EmpresaLogo.Value, "pagamentoFreteReciboModel")</Value>
          <MIMEType>image/png</MIMEType>
          <Sizing>FitProportional</Sizing>
          <Left>9.79558cm</Left>
          <Height>1.90583cm</Height>
          <Width>5.7093cm</Width>
          <ZIndex>1</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </LeftBorder>
            <RightBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </RightBorder>
          </Style>
        </Image>
        <Textbox Name="Textbox17">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>Comprovante de transação</Value>
                  <Style>
                    <FontSize>12pt</FontSize>
                    <FontWeight>Bold</FontWeight>
                  </Style>
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>Textbox17</rd:DefaultName>
          <Height>0.6cm</Height>
          <Width>9.79558cm</Width>
          <ZIndex>2</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </LeftBorder>
            <RightBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </RightBorder>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Textbox Name="EstabelecimentoCnpj">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=First(Fields!EstabelecimentoCnpj.Value, "pagamentoFreteReciboModel")</Value>
                  <Style />
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>EstabelecimentoCnpj</rd:DefaultName>
          <Top>1.27056cm</Top>
          <Height>0.63527cm</Height>
          <Width>6.5103cm</Width>
          <ZIndex>3</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </LeftBorder>
            <RightBorder>
              <Color>White</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </RightBorder>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line2">
          <Top>1.91037cm</Top>
          <Left>8.55641cm</Left>
          <Height>0cm</Height>
          <Width>1.23917cm</Width>
          <ZIndex>4</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </LeftBorder>
            <RightBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </RightBorder>
          </Style>
        </Line>
        <Textbox Name="EstabelecimentoNome">
          <CanGrow>true</CanGrow>
          <KeepTogether>true</KeepTogether>
          <Paragraphs>
            <Paragraph>
              <TextRuns>
                <TextRun>
                  <Value>=First(Fields!EstabelecimentoNome.Value, "pagamentoFreteReciboModel")</Value>
                  <Style />
                </TextRun>
              </TextRuns>
              <Style />
            </Paragraph>
          </Paragraphs>
          <rd:DefaultName>EstabelecimentoNome</rd:DefaultName>
          <Top>0.6cm</Top>
          <Height>0.67056cm</Height>
          <Width>9.79558cm</Width>
          <ZIndex>5</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </LeftBorder>
            <RightBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </RightBorder>
            <PaddingLeft>2pt</PaddingLeft>
            <PaddingRight>2pt</PaddingRight>
            <PaddingTop>2pt</PaddingTop>
            <PaddingBottom>2pt</PaddingBottom>
          </Style>
        </Textbox>
        <Line Name="Line1">
          <Top>1.90583cm</Top>
          <Left>15.50488cm</Left>
          <Height>1.37662cm</Height>
          <Width>0cm</Width>
          <ZIndex>6</ZIndex>
          <Style>
            <Border>
              <Color>Gray</Color>
              <Style>Solid</Style>
            </Border>
            <TopBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </TopBorder>
            <BottomBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </BottomBorder>
            <LeftBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </LeftBorder>
            <RightBorder>
              <Color>Gray</Color>
              <Style>Solid</Style>
              <Width>1pt</Width>
            </RightBorder>
          </Style>
        </Line>
      </ReportItems>
      <Style>
        <Border>
          <Style>None</Style>
        </Border>
      </Style>
    </PageHeader>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>1.5cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="ATSCrossCuttingReportsPagamentoFrete">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>ccf22def-53e1-43a3-b0f0-5acc0538d1b2</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="pagamentoFreteReciboModel">
      <Query>
        <DataSourceName>ATSCrossCuttingReportsPagamentoFrete</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="Barcode">
          <DataField>Barcode</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
        <Field Name="EmpresaLogo">
          <DataField>EmpresaLogo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EstabelecimentoCnpj">
          <DataField>EstabelecimentoCnpj</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="EstabelecimentoNome">
          <DataField>EstabelecimentoNome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="INSS">
          <DataField>INSS</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Instrucoes">
          <DataField>Instrucoes</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IRRF">
          <DataField>IRRF</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ISSQN">
          <DataField>ISSQN</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MotoristaNome">
          <DataField>MotoristaNome</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NomeProprietario">
          <DataField>NomeProprietario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Outros">
          <DataField>Outros</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ProprietarioCpfCnpj">
          <DataField>ProprietarioCpfCnpj</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ProprietarioRntrc">
          <DataField>ProprietarioRntrc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="SESTSENAT">
          <DataField>SESTSENAT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Usuario">
          <DataField>Usuario</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemDifFreteMotorista">
          <DataField>ViagemDifFreteMotorista</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoDataHoraPagamento">
          <DataField>ViagemEventoDataHoraPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoNumeroRecibo">
          <DataField>ViagemEventoNumeroRecibo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoTipo">
          <DataField>ViagemEventoTipo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemEventoValorTotalPagamento">
          <DataField>ViagemEventoValorTotalPagamento</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemPesoChegada">
          <DataField>ViagemPesoChegada</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemPesoSaida">
          <DataField>ViagemPesoSaida</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemQuebraMercadoria">
          <DataField>ViagemQuebraMercadoria</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ViagemToken">
          <DataField>ViagemToken</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>ATS.CrossCutting.Reports.PagamentoFrete</rd:DataSetName>
        <rd:TableName>PagamentoFreteReciboModel</rd:TableName>
        <rd:ObjectDataSourceType>ATS.CrossCutting.Reports.PagamentoFrete.PagamentoFreteReciboModel, ATS.CrossCutting.Reports, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</rd:ObjectDataSourceType>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>3811d8cb-3ce2-4406-9f5e-c1b7a91aab55</rd:ReportID>
</Report>
﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO.Banner;

namespace ATS.WS.ControllersATS
{
    public class BannerAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IBannerApp _bannerApp;

        public BannerAtsController(IBannerApp BannerApp, IUserIdentity userIdentity)
        {
            _userIdentity = userIdentity;
            _bannerApp = BannerApp;
        }

        /// <summary>
        /// Consulta o banner atual
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAtual()
        {
            try
            {
                var resultado = _bannerApp.ConsultarAtual();
                return ResponderSucesso("Banner ativo encontrado.", resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Consulta o banner para edição
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idBanner)
        {
            try
            {
                var resultado = _bannerApp.ConsultarPorId(idBanner);
                return ResponderSucesso("Banner ativo encontrado.", resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarBanners()
        {
            try
            {
                var resultado = _bannerApp.ConsultarBanners();
                return ResponderSucesso("Banners encontrados.", resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }


        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Visualizar(BannerVisualizarRequest request)
        {
            try
            {
                _bannerApp.Visualizar(request);
                return ResponderSucesso(null);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar( EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Integrar(BannerIntegrarRequest request)
        {
            try
            {
                var resultado = _bannerApp.Integrar(request);
                return ResponderSucesso("Banner salvo com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar( EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(BannerAlterarStatusRequest request)
        {
            try
            {
                var resultado = _bannerApp.AlterarStatus(request);
                return ResponderSucesso(resultado.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Models
{
    public class KeycloakUserModel
    {
        public string id { get; set; }
        public long createdTimestamp { get; set; }
        public string username { get; set; }
        public bool enabled { get; set; }
        public bool totp { get; set; }
        public bool emailVerified { get; set; }
        public string firstName { get; set; }
        public string lastName { get; set; }
        public string email { get; set; }
        //"disableableCredentialTypes": [],
        //"requiredActions": [],
        //"notBefore": 1662462653,
        public KeycloakOpenIdTokenModel access { get; set; }
        public List<KeycloakUserCredential> credentials { get; set; }
        public Dictionary<string, object> attributes { get; set; }
        public List<string> requiredActions { get; set; }
    }
}

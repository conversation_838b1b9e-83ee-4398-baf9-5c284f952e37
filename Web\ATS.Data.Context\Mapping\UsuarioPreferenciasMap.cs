﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioPreferenciasMap : EntityTypeConfiguration<UsuarioPreferencias>
    {
        public UsuarioPreferenciasMap()
        {
            ToTable("USUARIO_PREFERENCIAS");
            HasKey(x => new { x.IdUsuario, x.Campo });
            Property(x => x.IdUsuario).HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            Property(x => x.Campo).HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            Property(x => x.Valor).HasMaxLength(8000).HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
            HasRequired(x => x.Usuario).WithMany(x => x.UsuarioPreferencias).HasForeignKey(x => x.IdUsuario);
        }
    }
}

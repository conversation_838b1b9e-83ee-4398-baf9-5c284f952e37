﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Transactions;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class TipoCavaloApp : AppBase, ITipoCavaloApp
    {
        private readonly ITipoCavaloService _tipoCavaloService;

        public TipoCavaloApp(ITipoCavaloService tipoCavaloService)
        {
            _tipoCavaloService = tipoCavaloService;
        }

        /// <summary>
        /// Retorna o Tipo de Veículo a partir do ID
        /// </summary>
        /// <param name="id">ID do registro</param>
        /// <returns></returns>
        public TipoCavalo Get(int id)
        {
            return _tipoCavaloService.Get(id);
        }

        public IQueryable<TipoCavalo> GetQuery(int id)
        {
            return _tipoCavaloService.All().Where(c => c.IdTipoCavalo == id);
        }

        /// <summary>
        /// Retorna apenas o objeto de tipo cavalo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public TipoCavalo GetTipoCavalo(int id)
        {
            return _tipoCavaloService.GetTipoCavalo(id);
        }

        /// <summary>
        /// Retorna uma lista de tipos de cavalo ativos
        /// </summary>
        /// <returns>Lista de TipoCavalo</returns>
        public List<TipoCavalo> GetTodos(int? aIdEmpresa = null)
        {
            return _tipoCavaloService.All().Where(x => x.Ativo && (aIdEmpresa == null ||
                                                                        (aIdEmpresa != null && x.IdEmpresa == aIdEmpresa
                                                                        ))).ToList();
        }

        /// <summary>
        /// Adicionar um Tipo de Veículo
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ValidationResult Add(TipoCavalo entity)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                validationResult = _tipoCavaloService.Add(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return validationResult;
        }

        /// <summary>
        /// Atualizar um registro de Tipo de Veículo
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ValidationResult Update(TipoCavalo entity)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                validationResult = _tipoCavaloService.Update(entity);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return validationResult;
        }

        /// <summary>
        /// Método utilizado para consultar Tipo de Veículo.
        /// </summary>
        /// <param name="nome">Nome de Tipo de Veículo.</param>
        /// <param name="idEmpresa"></param>
        /// <returns>IQueryable de TipoCavaloGrid</returns>
        public IQueryable<TipoCavaloGrid> Consultar(string nome, int? idEmpresa)
        {
            return _tipoCavaloService.Consultar(nome, idEmpresa);
        }

        /// <summary>
        /// Inativar o Tipo de Veículo
        /// </summary>
        /// <param name="idTipoCavalo">Código do tipo de veículo a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idTipoCavalo)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _tipoCavaloService.Inativar(idTipoCavalo);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Reativar o tipo de veículo
        /// </summary>
        /// <param name="idTipoCavalo">Código do tipo de veículo a ser desativado</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idTipoCavalo)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    ValidationResult validationResult = _tipoCavaloService.Reativar(idTipoCavalo);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public object ConsultarSemEmpresa()
        {
            return _tipoCavaloService.ConsultarSemEmpresa();
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Cavalo por categoria
        /// </summary>
        /// <param name="categoria">Categoria de Tipo de Veículo</param>
        /// <returns>IQueryable de Tipo de Veículo</returns>
        public IQueryable<TipoCavalo> GetPorCategoria(ECategoriaTipoCavalo categoria)
        {
            return _tipoCavaloService.GetPorCategoria(categoria);
        }

        /// <summary>
        ///  Método utilizado para listar os Tipos de Cavalo
        /// </summary>
        /// <param name="where"></param>
        /// <returns>IQueryable de Tipo de Veículo</returns>
        public IQueryable<TipoCavalo> List(Expression<Func<TipoCavalo, bool>> where)
        {
            return _tipoCavaloService.List(where);
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Cavalo atualizados
        /// </summary>
        /// <param name="dataBase">Data de atualização</param>
        /// <param name="idsEmpresa"></param>
        /// <returns>IQueryable de IQueryable</returns>
        public IEnumerable<TipoCavalo> GetRegistrosAtualizados(DateTime dataBase, List<int> idsEmpresa)
        {
            return _tipoCavaloService.GetRegistrosAtualizados(dataBase, idsEmpresa);
        }

        /// <summary>
        /// Método utilizado para listar todos os Tipos de Cavalo ativos
        /// </summary>
        /// <returns>IQueryable de TipoCavalo</returns>
        public IQueryable<TipoCavalo> All()
        {
            return _tipoCavaloService.All();
        }

        public TipoCavalo GetPorDescricao(string nome, int idEmpresa)
        {
            return _tipoCavaloService.GetPorDescricao(nome, idEmpresa);
        }

        public List<TipoCavalo> GetTiposCavaloPorEmpresa(int? aIdEmpresa = null)
        {
            var tiposCavalo = _tipoCavaloService.All().Where(x => x.Ativo);
            if (aIdEmpresa.HasValue)
                return tiposCavalo.Where(x => x.IdEmpresa == aIdEmpresa).OrderBy(x => x.Nome).ToList();
            return tiposCavalo.Where(x => x.IdEmpresa == null).OrderBy(x => x.Nome).ToList();
        }

        public int? GetCapacidadePorPlaca(string placa)
        {
            return _tipoCavaloService.GetCapacidadePorPlaca(placa);
        }

        public object ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _tipoCavaloService.ConsultaGrid(idEmpresa, take, page, orderFilters, filters);
        }

        public ValidationResult AlterarStatus(int tipoCavaloId)
        {
            return _tipoCavaloService.AlterarStatus(tipoCavaloId);
        }
    }
}
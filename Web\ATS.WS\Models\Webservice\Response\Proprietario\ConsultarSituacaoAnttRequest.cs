using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class ConsultarSituacaoAnttRequest : RequestBase
    {
        public string CpfCnpj
        {
            get { return !string.IsNullOrWhiteSpace(_cpfCnpj) ? _cpfCnpj : CnpjCpfProprietario.OnlyNumbers(); }
            set { _cpfCnpj = value.OnlyNumbers(); }
        }

        private string _cpfCnpj { get; set; }

        /// <summary>
        /// Foi colocado por engano este campo no manual, então foi criado na chamada como um apelido para CpfCnpj
        /// </summary>
        public string CnpjCpfProprietario
        {
            get { return _cnpjCpfProprietario; }
            set {  _cnpjCpfProprietario = value.OnlyNumbers(); }
        }
        private string _cnpjCpfProprietario { get; set; }
        
        public ValidationResult ValidaRequest()
        {
            var validacao = ValidaRequestBase(false);

            if (string.IsNullOrWhiteSpace(CpfCnpj))
                validacao.Add("É obrigatório o envio do campo CpfCnpj");
            else if (CpfCnpj.Length != 11 && CpfCnpj.Length != 14)
                validacao.Add("O campo CpfCnpj deve conter 11 ou 14 dígitos");

            return validacao;
        }
    }
}
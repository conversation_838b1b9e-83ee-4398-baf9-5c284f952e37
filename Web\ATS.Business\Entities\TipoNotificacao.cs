﻿using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class TipoNotificacao
    {
        public int IdTipoNotificacao { get; set; }
        public string Descricao { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdFilial { get; set; }
        public DateTime DataUltimaAtualizacao { get; set; }
        public bool Ativo { get; set; }

        public virtual Empresa Empresa { get; set; }
        public virtual Filial Filial { get; set; }


        public virtual ICollection<NotificacaoPush> NotificacoesPush { get; set; }
        public virtual ICollection<Notificacao> Notificacao { get; set; }
    }
}

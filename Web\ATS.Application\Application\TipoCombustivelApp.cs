﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class TipoCombustivelApp : AppBase, ITipoCombustivelApp
    {
        private readonly ITipoCombustivelService _tipoCombustivelService;

        public TipoCombustivelApp(ITipoCombustivelService tipoCombustivelService)
        {
            _tipoCombustivelService = tipoCombustivelService;
        }

        public TipoCombustivel Get(int id)
        {
            return _tipoCombustivelService.Get(id);
        }

        public IQueryable<TipoCombustivel> All()
        {
            return _tipoCombustivelService.All();
        }
    }
}
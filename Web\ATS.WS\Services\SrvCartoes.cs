﻿using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Cartoes;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Services
{
    public class SrvCartoes : SrvBase
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public SrvCartoes(IParametrosApp parametrosApp, IEmpresaApp empresaApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies)
        {
            _parametrosApp = parametrosApp;
            _empresaApp = empresaApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
        }

        public Retorno<object> VincularCartaoPortador(CartaoVincularPortadorExternalRequest request)
        {
            if(string.IsNullOrWhiteSpace(request.Documento))
                return new Retorno<object>(false, @"Documento não informado.", null);

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

            var empresa = _empresaApp.Get(request.CNPJEmpresa);
            if (empresa == null)
                return new Retorno<object>(false, @"Empresa inválida.", null);

            var produtoPadrao = _empresaApp.GetIdProdutoCartaoFretePadrao(empresa.IdEmpresa);
            if (!produtoPadrao.HasValue)
                return new Retorno<object>(false, @"Empresa informada sem produto de meio homologado padrão parametrizado.", null);

            var requestVinculo = new CartaoVincularPortadorAtsRequest();
            requestVinculo.Identificador = request.NumeroCartao;
            requestVinculo.ProdutoId = (int)produtoPadrao;
            requestVinculo.Documento = request.Documento.OnlyNumbers();

            var portador = cartoesApp.CarregarInformacoesPortador(requestVinculo.Documento);
            if (portador == null)
                return new Retorno<object>(false, $"Portador não encontrado.", null);

            var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
            var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
            var resultado = cartoesApp.GetCartaoHistoricoGrid(portador.Documento.OnlyNumbers(), cartaoIdArray);
            if (!resultado.Sucesso)
                return new Retorno<object>(false, "Não foi possível consultar o historíco de cartões do usuário!", null);

            if (resultado.Objeto != null && resultado.Objeto.Any())
            {
                var mesmoCartaoJaVinculado = resultado.Objeto
                    .FirstOrDefault(x =>
                        x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado &&
                        x.Identificador == request.NumeroCartao);
                if (mesmoCartaoJaVinculado != null)
                    return new Retorno<object>(true, "Cartão vinculado com sucesso!", null);

                var mesmoCartaoJaBloqueado = resultado.Objeto
                    .FirstOrDefault(x =>
                        x.Status == HistoricoCartaoPessoaResponseStatus.Bloqueado &&
                        x.Identificador == request.NumeroCartao);
                if (mesmoCartaoJaBloqueado != null)
                    return new Retorno<object>(true,
                        $"Cartão vinculado com sucesso ao portador documento {requestVinculo.Documento.FormatMaskCpfCnpj()}. " +
                        $"O cartão encontra-se BLOQUEADO. Para realizar o desbloqueio entrar em contato com a EXTRATTA.", null);

                var cartaoVinculado = resultado.Objeto
                    .FirstOrDefault(x =>
                        x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado &&
                        x.Identificador != request.NumeroCartao);

                if (cartaoVinculado != null && !empresa.VinculoNovoCartaoPortador)
                    return new Retorno<object>(false,
                        $"O portador já possui um cartão vinculado. " +
                        $"Para realizar o vínculo de um novo cartão é necessário desvincular o atual. " +
                        $"Em caso de dúvidas entre em contato com a Extratta!", null);

                cartaoVinculado = resultado.Objeto
                    .FirstOrDefault(x =>
                        x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado &&
                        x.Identificador == request.NumeroCartao);

                if (cartaoVinculado != null && empresa.VinculoNovoCartaoBloqueadoPortador)
                    return new Retorno<object>(true, "Cartão vinculado com sucesso!", null);

                var cartaoBloqueado = resultado.Objeto
                    .FirstOrDefault(x =>
                        x.Status == HistoricoCartaoPessoaResponseStatus.Bloqueado &&
                        x.Identificador != request.NumeroCartao);

                if (cartaoBloqueado != null)
                    return new Retorno<object>(false,
                        $"Não foi possível vincular o cartão, " +
                        $"pois o portador já possui um cartão com status bloqueado. " +
                        $"Efetue o desbloqueio do cartão para o vínculo de um novo cartão!", null);

                var cartaoCancelado = resultado.Objeto
                    .FirstOrDefault(x =>
                        x.Status == HistoricoCartaoPessoaResponseStatus.Cancelado &&
                        x.Identificador == request.NumeroCartao);

                if (cartaoCancelado != null)
                    return new Retorno<object>(false, $"Cartao encontra-se cancelado.", null);
            }

            var vincularResponse = cartoesApp.VincularCartaoPortador(requestVinculo.Identificador, requestVinculo.ProdutoId, portador, empresa.IdEmpresa);
            if (vincularResponse.Status != VincularResponseStatus.Sucesso)
                return new Retorno<object>(false, vincularResponse.Mensagem, null);

            if (!empresa.VinculoNovoCartaoBloqueadoPortador)
            {
                vincularResponse.Mensagem = "Cartão vinculado com sucesso!";
                return new Retorno<object>(true, vincularResponse.Mensagem, null);
            }

            var requestBloqueio = new BloquearCartaoRequest()
            {
                Cartao = new IdentificadorCartao()
                {
                    Identificador = requestVinculo.Identificador,
                    Produto = requestVinculo.ProdutoId
                },
                Observacao = "Bloqueio por parametrização da empresa!",
                Motivo = _parametrosApp.GetMotivoPadraoBloqueioCartaoEmpresa()
            };

            var resultadoBloqueio = cartoesApp.BloquearCartaoParametrizacaoEmpresa(requestBloqueio);
            if (resultadoBloqueio.Status == BloquearCartaoResponseStatus.Sucesso)
                return new Retorno<object>(true,
                    $"Cartão vinculado com sucesso ao portador documento {requestVinculo.Documento.FormatMaskCpfCnpj()}. " +
                    $"O cartão encontra-se BLOQUEADO. Para realizar o desbloqueio entrar em contato com a EXTRATTA.", null);

            return new Retorno<object>(true,
                "Ocorreu um erro ao bloquear o cartão do portador de acordo com a parametrização da empresa.", null);
        }
    }
}


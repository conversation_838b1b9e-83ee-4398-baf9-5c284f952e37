﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Grid;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Service
{
    public interface IFilialService: IService<Filial>
    {
        //Filial GetWithAllChilds(int idFilial);
        ValidationResult Update(Filial filial);
        Filial Get(int id);
        ValidationResult Add(Filial filial);
        //Filial GetWithLocalizacao(int id);
        IQueryable<Filial> Consultar(string razaoSocial, int? idEmpresa);
        ValidationResult Inativar(int idFilial);
        ValidationResult Reativar(int idFilial);
        IQueryable<Filial> GetListaFilialPorEmpresa(int idEmpresa);
        IEnumerable<Filial> GetFiliaisAtualizadas(int? idEmpresa, DateTime dataAtualizacao);
        int? GetIdPorCnpj(string cnpj);
        string GetCnpj(int idFilial);
        Filial GetFilialPorEmpresa(int idEmpresa, int idFilial);
        int? GetIdPorCodigoFilial(int idEmpresa, string cnpjFilial, string codigoFilial);
        string GetCnpjPorId(int idFilial);
        int? GetIdPorCnpjTodos(string cnpj);
        List<string> GetCnpjList(int[] idFilialList);
        IQueryable<Filial> QueryById(int id);
        IQueryable<Filial> GetQuery(int? idEmpresa);
        FilialCrudResponse GetFilialCadastro(int id);
        Filial Get(string cnpj);
        ValidationResult AlterarStatus(int idFilial);
        object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        List<FilialContatos> GetContatosByFilial(int idFilial);
        byte[] GerarRelatorioGridFilial(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo);
    }
}

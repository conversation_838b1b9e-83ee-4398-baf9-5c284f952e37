﻿// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

using System.Collections.Generic;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class TransferenciaPixGridReleasesResponse
    {
        // ReSharper disable once CollectionNeverUpdated.Global (por causa do automapper)
        public List<TransferenciaPixGridReleasesResponseContent> Content { get; set; }

        public bool? FirstPage { get; set; }

        public bool? LastPage { get; set; }

        public bool? HasContent { get; set; }

        public bool? HasNextPage { get; set; }

        public bool? HasPreviousPage { get; set; }

        public int? Page { get; set; }

        public int? Elements { get; set; }

        public int? NextPage { get; set; }

        public int? PreviousPage { get; set; }

        public int? TotalElements { get; set; }

        public int? TotalPages { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseContent
    {
        public string ReleaseId { get; set; }

        public string ReleaseType { get; set; }

        public string Vision { get; set; }

        public string OperationType { get; set; }

        public string LiteralRelease { get; set; }

        public string OperationChannel { get; set; }

        public TransferenciaPixGridReleasesResponsePaymentDetail PaymentDetail { get; set; }

        public TransferenciaPixGridReleasesResponseReturnDetail ReturnDetail { get; set; }
    }

    public class TransferenciaPixGridReleasesResponsePaymentDetail
    {
        public string PaymentId { get; set; }

        public string TransferId { get; set; }

        public string PlatformPaymentId { get; set; }

        public string ReleaseId { get; set; }

        public string TxId { get; set; }

        public decimal? Value { get; set; }

        public decimal? TaxValue { get; set; }

        public string RateModality { get; set; }

        public decimal? DiscountAmountApplied { get; set; }

        public decimal? FineAmountApplied { get; set; }

        public decimal? ReductionAmountApplied { get; set; }

        public decimal? InterestAmountApplied { get; set; }

        public string Returned { get; set; }

        public bool? LockIndicator { get; set; }

        public bool? ReturnPossibilityIndicator { get; set; }

        public decimal? MaximumReturnValue { get; set; }

        public decimal? ValueReturned { get; set; }

        public string PayerReplyText { get; set; }

        public TransferenciaPixGridReleasesResponseDocument Document { get; set; }

        public TransferenciaPixGridReleasesResponseCredited Credited { get; set; }

        public TransferenciaPixGridReleasesResponseDebited Debited { get; set; }

        public TransferenciaPixGridReleasesResponseValueDetail ValueDetail { get; set; }

        public string AccountingDate { get; set; }

        public string Date { get; set; }

        public List<TransferenciaPixGridReleasesResponseReturn> Returns { get; set; }

        public TransferenciaPixGridReleasesResponseReturnDetail ReturnDetail { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseAccountData
    {
        public string AccountType { get; set; }

        public string EAccountType { get; set; }

        public string Ispb { get; set; }

        public string InstitutionName { get; set; }

        public string Agency { get; set; }

        public string Account { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseAdditionalInfo
    {
        public string Name { get; set; }

        public string Value { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseBatch
    {
        public long? Id { get; set; }

        public string Location { get; set; }

        public string ChargeType { get; set; }

        public string CreationDate { get; set; }

        public string BacenDocumentVersion { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseCalendar
    {
        public string Creation { get; set; }

        public string Presentation { get; set; }

        public string Expiration { get; set; }

        public string CancellationDate { get; set; }

        public int? ValidAfterExpiration { get; set; }

        public string DueDate { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseCredited
    {
        public int? DocumentNumber { get; set; }

        public string Name { get; set; }

        public string IspbCode { get; set; }

        public string NameFinancialInstitution { get; set; }

        public string AccountType { get; set; }

        public string Agency { get; set; }

        public string Account { get; set; }

        public string CheckDigit { get; set; }

        public string AddressKey { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseDebited
    {
        public decimal? DocumentNumber { get; set; }

        public string Name { get; set; }

        public string IspbCode { get; set; }

        public string NameFinancialInstitution { get; set; }

        public string AccountType { get; set; }

        public string Agency { get; set; }

        public string Account { get; set; }

        public string CheckDigit { get; set; }

        public string AddressKey { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseDocument
    {
        public string ChargeType { get; set; }

        public string DocumentId { get; set; }

        public string Version { get; set; }

        public string Key { get; set; }

        public string TxId { get; set; }

        public decimal? Revision { get; set; }

        public string Status { get; set; }

        public TransferenciaPixGridReleasesResponsePayer Payer { get; set; }

        public TransferenciaPixGridReleasesResponseReceiver Receiver { get; set; }

        public TransferenciaPixGridReleasesResponseValue Value { get; set; }

        public TransferenciaPixGridReleasesResponseCalendar Calendar { get; set; }

        public List<TransferenciaPixGridReleasesResponseAdditionalInfo> AdditionalInfos { get; set; }

        public string PayerSolicitaion { get; set; }

        public string Url { get; set; }

        public TransferenciaPixGridReleasesResponseLoc Loc { get; set; }

        public TransferenciaPixGridReleasesResponseBatch Batch { get; set; }

        public string BacenDocumentVersion { get; set; }

        public TransferenciaPixGridReleasesResponseQrCodePix QrCodePix { get; set; }

        public bool? AmountPayments { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseIndirectParticipant
    {
        public string Id { get; set; }

        public string NameInstitutionFinancial { get; set; }

        public string IndirectParticipantAccountTypeName { get; set; }

        public string IspbCodePaymentInstantPayment { get; set; }

        public string AgencyNumberIndirectParticipant { get; set; }

        public string AccountNumberIndirectParticipant { get; set; }

        public string CheckDigitIndirectParticipant { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseLoc
    {
        public string Id { get; set; }

        public string Location { get; set; }

        public string ChargeType { get; set; }

        public string CreationDate { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseNotice
    {
        public string Type { get; set; }

        public string Content { get; set; }
    }

    public class TransferenciaPixGridReleasesResponsePayer
    {
        public string Cpf { get; set; }

        public string Cnpj { get; set; }

        public string Name { get; set; }

        public string Email { get; set; }

        public string Phone { get; set; }

        public string Stret { get; set; }

        public string City { get; set; }

        public string State { get; set; }

        public string ZipCode { get; set; }

        public TransferenciaPixGridReleasesResponseAccountData AccountData { get; set; }
    }

    public class TransferenciaPixGridReleasesResponsePayment
    {
        public string PaymentId { get; set; }

        public string TransferId { get; set; }

        public string PaymentPlatformId { get; set; }

        public string ReleaseId { get; set; }

        public string TxId { get; set; }

        public decimal? Value { get; set; }

        public decimal? TaxValue { get; set; }

        public string TaxModality { get; set; }

        public decimal? DiscountValueApplied { get; set; }

        public decimal? FineValueApplied { get; set; }

        public decimal? ReductionValueApplied { get; set; }

        public decimal? InterestValueApplied { get; set; }

        public string Returned { get; set; }

        public bool? LockIndicator { get; set; }

        public bool? ReturnPossibilityIndicator { get; set; }

        public decimal? MaximumReturnValue { get; set; }

        public decimal? ReturnedValue { get; set; }

        public string PayerResponseText { get; set; }

        public TransferenciaPixGridReleasesResponseDocument Document { get; set; }

        public TransferenciaPixGridReleasesResponseCredited Credited { get; set; }

        public TransferenciaPixGridReleasesResponseDebited Debited { get; set; }

        public TransferenciaPixGridReleasesResponseValueDetail ValueDetail { get; set; }

        public string AccountingDate { get; set; }

        public string Date { get; set; }

        public List<TransferenciaPixGridReleasesResponseReturn> Returns { get; set; }
    }
    public class TransferenciaPixGridReleasesResponseQrCodePix
    {
        public string QrCodePixId { get; set; }

        public bool? Simulation { get; set; }

        public string Type { get; set; }

        public bool? Pattern { get; set; }

        public string Status { get; set; }

        public TransferenciaPixGridReleasesResponseIndirectParticipant IndirectParticipant { get; set; }

        public bool? Reusable { get; set; }

        public string ReusableType { get; set; }

        public TransferenciaPixGridReleasesResponseReceiver Receiver { get; set; }

        public string PixLink { get; set; }

        public string Emv { get; set; }

        public string Base64Image { get; set; }

        public string OperationChannel { get; set; }

        public string OperatorId { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseReceiver
    {
        public string Cpf { get; set; }

        public string Cnpj { get; set; }

        public string Name { get; set; }

        public string Email { get; set; }

        public string Phone { get; set; }

        public string KeyType { get; set; }

        public string AddressKey { get; set; }

        public string Stret { get; set; }

        public string City { get; set; }

        public string State { get; set; }

        public string ZipCode { get; set; }

        public TransferenciaPixGridReleasesResponseAccountData AccountData { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseReturn
    {
        public string DevolutionId { get; set; }

        public string Id { get; set; }

        public string Date { get; set; }

        public decimal? Value { get; set; }

        public string DevolutionReason { get; set; }

        public string DevolutionReasonCode { get; set; }

        public string OtherDevolutionReason { get; set; }

        public TransferenciaPixGridReleasesResponseCredited Credited { get; set; }

        public TransferenciaPixGridReleasesResponseDebited Debited { get; set; }

        public string OperationChannel { get; set; }

        public string Operator { get; set; }

        public TransferenciaPixGridReleasesResponseIndirectParticipant IndirectParticipant { get; set; }

        public TransferenciaPixGridReleasesResponseNotice Notice { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseReturnDetail
    {
        public string DevolutionId { get; set; }

        public string Id { get; set; }

        public string IdReleasePaymentOriginalPix { get; set; }

        public TransferenciaPixGridReleasesResponsePayment Payment { get; set; }

        public string Date { get; set; }

        public decimal? Value { get; set; }

        public string ReturnReason { get; set; }

        public string ReturnReasonCode { get; set; }

        public string Kind { get; set; }

        public string OtherReturnReason { get; set; }

        public TransferenciaPixGridReleasesResponseCredited Credited { get; set; }

        public TransferenciaPixGridReleasesResponseDebited Debited { get; set; }

        public string OperationChannel { get; set; }

        public string Operator { get; set; }

        public TransferenciaPixGridReleasesResponseIndirectParticipant IndirectParticipant { get; set; }

        public TransferenciaPixGridReleasesResponseNotice Notice { get; set; }

        public bool? CreditOperation { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseValue
    {
        public decimal? Original { get; set; }

        public decimal? Fees { get; set; }

        public decimal? Fine { get; set; }

        public decimal? Reduction { get; set; }

        public decimal? Discount { get; set; }

        public decimal? Final { get; set; }
    }

    public class TransferenciaPixGridReleasesResponseValueDetail
    {
        public decimal? Value { get; set; }

        public decimal? Change { get; set; }

        public decimal? Withdraw { get; set; }

        public decimal? Purchase { get; set; }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Pedagio.Recibo;
using ATS.Data.Repository.External.SistemaInfo.Pedagio;
using ATS.Domain.DTO;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using AutoMapper;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.Service
{
    public class PedagioService : IPedagioService
    {
        private readonly PedagioExternalRepository _repository;
        private readonly IViagemRepository _viagemRepository;

        public PedagioService(int? idEmpresa, string tokenEmpresa, string documentoUsuarioAudit, string nomeUsuarioAudit,IViagemRepository viagemRepository)
        {
            _repository = new PedagioExternalRepository(tokenEmpresa, documentoUsuarioAudit, nomeUsuarioAudit);
            _viagemRepository = viagemRepository;
        }
        public CalcularRotaResponseDTO CalcularRota(ConsultaRotaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var response = _repository.CalcularRota(request, documentoUsuarioAudit, nomeUsuarioAudit);
            
            return Mapper.Map<CalcularRotaResponseDTO>(response);
        }

        public ComprovanteValePedagioResponse ConsultarDadosComprovante(FornecedorEnum fornecedor, long idViagem)
        {
            return _repository.ConsultarDadosComprovante((Fornecedor4) (int) fornecedor, idViagem);
        }

        public CompraPedagioDTOResponse ConsultarReciboPedagio(int protocoloRequisicao)
        {
            var resultadoConsultaRecibo = _repository.ConsultarReciboMoedeiro(new ConsultaCompraPedagioMoedeiroReciboRequest {ProtocoloRequisicao = protocoloRequisicao});

            if (resultadoConsultaRecibo.Status == ConsultaCompraPedagioMoedeiroReciboResponseStatus.Falha)
                throw new Exception(resultadoConsultaRecibo.Mensagem);

            if (resultadoConsultaRecibo.CompraPedagioDtoList == null)
                throw new Exception("Compra não localizada na base de dados");

            return resultadoConsultaRecibo.CompraPedagioDtoList;
        }

        public byte[] ConsultarReciboMoedeiro(int protocoloRequisicao)
        {
            var resultadoConsultaRecibo = 
                _repository.ConsultarReciboMoedeiro(new ConsultaCompraPedagioMoedeiroReciboRequest {ProtocoloRequisicao = protocoloRequisicao});
            
            if (resultadoConsultaRecibo.Status == ConsultaCompraPedagioMoedeiroReciboResponseStatus.Falha)
                throw new Exception(resultadoConsultaRecibo.Mensagem);
            
            if (resultadoConsultaRecibo.CompraPedagioDtoList == null)
                throw new Exception("Compra não localizada na base de dados");

            var resultadoConsultaPracas = _repository.ConsultaHistoricoRota(new ConsultaHistoricoRotaRequest
            {
                ConsultaCustoHistoricoId = resultadoConsultaRecibo.CompraPedagioDtoList.ConsultaCustoHistoricoId
            });
            
            var recibo = resultadoConsultaRecibo.CompraPedagioDtoList;
            var pracas = resultadoConsultaPracas.Pracas.Select(praca => new ComprovanteCompraPedagioHistoricoPracasDataType
                {Nome = praca.Nome, Endereco = praca.Endereco, Concessao = praca.Concessao, Valor = $"R$ {praca.Valor:N}"}).ToList();

            var dadosTransportador = _viagemRepository.Where(v => v.IdViagem == protocoloRequisicao)
                .Select(v => new
                {
                    NomeContratante = v.NomeProprietario,
                    CpfCnpjContratante = v.CPFCNPJProprietario,
                    NomeCampoCpfCnpj = v.CPFCNPJProprietario.Trim().Length == 11 ? "CPF: " : "CNPJ: "
                }).FirstOrDefault();

            return new ComprovanteCompraPedagio().GetReport(new ComprovanteCompraPedagioDataType
                {
                    CompraId = recibo.Id.ToString(),
                    EmpresaNome = recibo.NomeEmpresa,
                    NomeFavorecido = recibo.NomeFavorecido,
                    NomeContratante = dadosTransportador?.NomeContratante,
                    CpfCnpjContratante = dadosTransportador?.CpfCnpjContratante.FormatarCpfCnpj(),
                    NomeCampoCpfCnpj = dadosTransportador?.NomeCampoCpfCnpj,
                    Categoria = $"{recibo.QtdEixos ?? 0} eixos",
                    DataCadastro = recibo.DataCadastro.HasValue ? recibo.DataCadastro.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty,
                    ProtocoloValePedagio = recibo.ProtocoloValePedagio,
                    ProtocoloEnvioValePedagio = recibo.ProtocoloEnvioValePedagio.PadLeft(20, '0'),
                    AvisoTransportador = recibo.AvisoTransportador,
                    EmpresaCnpj = recibo.CnpjEmpresa.FormatarCpfCnpj(),
                    DocumentoFavorecido = recibo.DocumentoFavorecido.ToCpfOrCnpj(),
                    Placa = recibo.Placa.FormatarPlaca(),
                    DataConfirmacao = recibo.DataConfirmacao.HasValue ? recibo.DataConfirmacao.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty,
                    CartaoFrete = recibo.CartaoFrete.HasValue ? recibo.CartaoFrete.Value.ToString( ): string.Empty,
                    DataExpiracaoCompraPedagio = recibo.DataExpiracaoCompraPedagio.HasValue ? recibo.DataExpiracaoCompraPedagio.Value.ToString("dd/MM/yyyy HH:mm") : string.Empty,
                    Status = recibo.Status.HasValue ? recibo.Status.DescriptionAttr() : string.Empty
                },
                pracas, $"R$ {recibo.Valor:N}");
        }
        public ObterExtratoSemPararResponseDTO GetExtratoSemParar(DateTime datainicio, DateTime datafim)
        {
            var extrato = _repository.ObterExtratoSemParar(datainicio, datafim);

            var response = new ObterExtratoSemPararResponseDTO
            {
                Sucesso = extrato.Status == ObterExtratoCreditoResponseStatus.Sucesso,
                Mensagem = extrato.Mensagem
            };

            if (response.Sucesso)
            {
                response.Objeto = new ObterExtratoSemPararObjetoResponseDTO();
                response.Objeto.Itens = new List<ObterExtratoSemPararItemResponseDTO>();

                foreach (var item in extrato.Itens)
                {
                    response.Objeto.Itens.Add(new ObterExtratoSemPararItemResponseDTO
                    {
                        Acao = item.Acao,
                        Descricao = item.Descricao,
                        Fatura = item.Fatura,
                        Numero = item.Numero,
                        Placa = item.Placa,
                        Status = item.Status,
                        Tag = item.Tag,
                        DataCompra = item.DataCompra,
                        DataFatura = item.DataFatura,
                        DataOperacao = item.DataOperacao,
                        DataPassagem = item.DataPassagem,
                        NomeConcessionaria = item.NomeConcessionaria,
                        NomePraca = item.NomePraca,
                        NomeRodovia = item.NomeRodovia,
                        NomeRota = item.NomeRota,
                        NomeTransportador = item.NomeTransportador,
                        NumeroViagem = item.NumeroViagem,
                        SaldoDia = item.SaldoDia,
                        TipoVp = item.TipoVp,
                        ValorOperacao = item.ValorOperacao,
                        CnpjCpfTransportador = item.CnpjCpfTransportador,
                        DataFimVigencia = item.DataFimVigencia,
                        DataInicioVigencia = item.DataInicioVigencia
                    });
                }
            }

            return response;
        }
        public ConsultarNumeroCartaoViagemResponse ConsultarNumeroCartaoViagem(FornecedorEnum fornecedor, string protocoloEnvio)
        {
            return _repository.ConsultarNumeroCartaoViagem((Fornecedor5) (int) fornecedor, protocoloEnvio);
        }

        public ConsultarStatusVeiculoSemPararDTO ConsultarStatusVeiculoSemParar(string placa)
        {
            var veiculos = _repository.ConsultarStatusVeiculoSemParar(placa);
            
            var veiculo = veiculos.Itens?.FirstOrDefault();

            if (veiculo?.VeiculoStatusSemParar == null || veiculos.Status == ConsultarStatusVeiculoSemPararResponseStatus.Falha)
            {
                return new ConsultarStatusVeiculoSemPararDTO
                {
                    Sucesso = false,
                    Mensagem = veiculos.Mensagem,
                    VeiculoStatusSemParar = EStatusVeiculoSemParar.ErroServico,
                };
            }

            var status = System.Enum.IsDefined(typeof(EStatusVeiculoSemParar), veiculo.VeiculoStatusSemParar)
                ? (EStatusVeiculoSemParar)veiculo.VeiculoStatusSemParar
                : EStatusVeiculoSemParar.Indisponivel;

            var response = new ConsultarStatusVeiculoSemPararDTO
            {
                Sucesso = veiculos.Status == ConsultarStatusVeiculoSemPararResponseStatus.Sucesso && (veiculo.VeiculoStatusSemParar == 0 || veiculo.VeiculoStatusSemParar == 4),
                Mensagem = string.IsNullOrWhiteSpace(veiculos.Mensagem) ? status.GetDescription() : veiculos.Mensagem,
                VeiculoPossuiTagSemParar = veiculo.VeiculoPossuiTagSemParar == true,
                VeiculoExistenteSemParar = veiculo.VeiculoExistenteSemParar == true,
                VeiculoStatusSemParar = status,
            };

            return response;
        }
        
        public ConsultarPolylineDTO GetPolyline(int codPolyline)
        {
            var resultPolyline = _repository.ConsultarPolyline(codPolyline);

            if (resultPolyline.Status == ConsultarPolylineResponseStatus.Falha)
            {
                return new ConsultarPolylineDTO()
                {
                    Sucesso = false,
                    Mensagem = resultPolyline.Mensagem
                };
            }

            return new ConsultarPolylineDTO
            {
                Sucesso = true,
                DistanciaTotalKm = resultPolyline.DistanciaTotalKm,
                TempoPrevistoSegundos = resultPolyline.TempoPrevistoSegundos,
                Polyline = resultPolyline.Polyline,
                Localizacao = resultPolyline.Localizacao
                    .Select(x => new PolylinelocationDTO
                    {
                        Latitude = x.Latitude,
                        Longitude = x.Longitude
                    })
                    .ToList()
            };
        }

        public ConsultarStatusVeiculoTaggyEdenredDTO ConsultarStatusVeiculoTaggyEdenred(string placa)
        {
            try
            {
                var resp = _repository.ConsultarStatusVeiculoTaggyEdenred(placa);
                return new ConsultarStatusVeiculoTaggyEdenredDTO
                {
                    Sucesso = resp.Status == TaggyEdenredVeiculoResponseStatus.Sucesso,
                    Status = resp.StatusId,
                    Mensagem = resp.Mensagem
                };
            }
            catch (Exception e)
            {
                return new ConsultarStatusVeiculoTaggyEdenredDTO
                {
                    Sucesso = false,
                    Mensagem = e.Message,
                };
            }
        }
        
        public ConsultarSituacaoTagResponseModel ConsultarSituacaoTagPlaca(string placa, Fornecedor2 fornecedor)
        {
            var retornoPedagio = _repository.ConsultarSituacaoTagPlaca(placa, fornecedor);
            
            if (retornoPedagio.Status == ConsultarSituacaoTagPlacaResponseStatus.Falha)
            {
                return new ConsultarSituacaoTagResponseModel
                {
                    Sucesso = false,
                    MensagemRetorno = retornoPedagio.Mensagem
                };
            }

            return new ConsultarSituacaoTagResponseModel
            {
                Sucesso = true,
                Fornecedor = (retornoPedagio.Fornecedor ?? ConsultarSituacaoTagPlacaResponseFornecedor.Desabilitado),
                TagAtiva = retornoPedagio.TagAtiva ?? false,
                CodigoStatusTag = retornoPedagio.CodigoStatusTag,
                MensagemStatusTag = retornoPedagio.MensagemStatusTag,
            };
        }
    }
}
﻿using System;
using ATS.Application.Application.Common;
using System.Collections.Generic;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Application
{
    public class RotasCacheApp : AppBase,IRotasCacheApp
    {
        private readonly IRotasCacheService _rotasCacheService;
        public RotasCacheApp(string documentoUsuarioAudit,string nomeUsuarioAudit)
        {
            _rotasCacheService = new RotasCacheService(documentoUsuarioAudit,nomeUsuarioAudit);
        }
        
        #region Factory

        public static RotasCacheApp CreateByUsuario(IUserIdentity userIdentity)
        {
            if (userIdentity.Perfil == (int)EPerfil.Administrador)
                return new RotasCacheApp(userIdentity.CpfCnpj,userIdentity.Nome);

            throw new Exception("Funcionalidade não dísponível para usuários com perfil diferente de administrador!"
                .FormatEx(userIdentity.IdUsuario));
        }
        
        #endregion

        public DataModel<object> ConsultarGrid(DateTime dataInicio,DateTime dataFim, int take, int page, 
            OrderFiltersPedagio order, List<QueryFiltersPedagio> filters)
        {
            return _rotasCacheService.ConsultarGrid(dataInicio, dataFim, take, page, order, filters);
        }

        public void DeletarCache(Guid guidRota)
        {
            _rotasCacheService.DeletarRotaCache(guidRota);
        }
    }
}

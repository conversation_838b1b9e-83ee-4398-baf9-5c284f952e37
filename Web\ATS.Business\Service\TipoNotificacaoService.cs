﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Domain.Helpers;
using System.Linq.Dynamic;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class TipoNotificacaoService : ServiceBase, ITipoNotificacaoService
    {
        private readonly ITipoNotificacaoRepository _tipoNotificacaoRepository;

        public TipoNotificacaoService(ITipoNotificacaoRepository tipoNotificacaoRepository)
        {
            _tipoNotificacaoRepository = tipoNotificacaoRepository;
        }

        public ValidationResult Add(TipoNotificacao TipoNotificacao)
        {
            try
            {
                var tipoNotificacaoRepository = _tipoNotificacaoRepository;

                TipoNotificacao.Ativo = true;
                TipoNotificacao.DataUltimaAtualizacao = DateTime.Now;
                tipoNotificacaoRepository.Add(TipoNotificacao);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(TipoNotificacao TipoNotificacao)
        {
            try
            {
                var tipoNotificacaoRepository = _tipoNotificacaoRepository;

                TipoNotificacao.Ativo = true;
                TipoNotificacao.DataUltimaAtualizacao = DateTime.Now;
                tipoNotificacaoRepository.Update(TipoNotificacao);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idTipoNotificacao)
        {
            try
            {
                var tipoNotificacaoRepository = _tipoNotificacaoRepository;

                var TipoNotificacao = tipoNotificacaoRepository.Get(idTipoNotificacao);

                if (TipoNotificacao != null)
                {
                    TipoNotificacao.Ativo = true;
                    TipoNotificacao.DataUltimaAtualizacao = DateTime.Now;
                    tipoNotificacaoRepository.Update(TipoNotificacao);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Inativar(int idTipoNotificacao)
        {
            try
            {
                var tipoNotificacaoRepository = _tipoNotificacaoRepository;

                var TipoNotificacao = tipoNotificacaoRepository.Get(idTipoNotificacao);

                if (TipoNotificacao != null)
                {
                    TipoNotificacao.Ativo = false;
                    TipoNotificacao.DataUltimaAtualizacao = DateTime.Now;
                    tipoNotificacaoRepository.Update(TipoNotificacao);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public TipoNotificacao Get(int id)
        {
            return _tipoNotificacaoRepository
                .Find(x => x.IdTipoNotificacao == id && x.Ativo)
                .Include(x => x.Empresa)
                .Include(x => x.Filial)
                .FirstOrDefault();
        }

        public List<TipoNotificacao> GetAll()
        {
            return _tipoNotificacaoRepository
                .GetAll().ToList();
        }

        public object ConsultaGrid(int? idEmpresa,
                                   int? idFilial,
                                   string descricao,
                                   int Take,
                                   int Page,
                                   OrderFilters orderFilters,
                                   List<QueryFilters> Filters)
        {

            var tiposNotificacao = _tipoNotificacaoRepository
                .GetAll().Include(x => x.Empresa).Include(x => x.Filial);

            if (idEmpresa.HasValue) tiposNotificacao = tiposNotificacao.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idFilial.HasValue) tiposNotificacao = tiposNotificacao.Where(x => x.IdFilial == idFilial.Value);

            if (descricao != null) tiposNotificacao = tiposNotificacao.Where(x => x.Descricao.Contains(descricao));

            if (string.IsNullOrWhiteSpace(orderFilters?.Campo))
                tiposNotificacao = tiposNotificacao.OrderBy(x => x.IdTipoNotificacao);
            else
                tiposNotificacao = tiposNotificacao.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            tiposNotificacao = tiposNotificacao.AplicarFiltrosDinamicos<TipoNotificacao>(Filters);

            return new
            {
                totalItems = tiposNotificacao.Count(),
                items = tiposNotificacao.Skip((Page - 1) * Take).Take(Take)
                .ToList().Select(x => new
                {
                    x.Ativo,
                    x.Descricao,
                    x.IdTipoNotificacao,
                    RazaoSocialEmpresa = x.Empresa?.RazaoSocial ?? string.Empty,
                    RazaoSocialFilial = x.Filial?.RazaoSocial ?? string.Empty
                })
            };
        }

        public IQueryable<TipoNotificacao> Consultar(List<QueryFilters> queryFilters, OrderFilters orderFilters)
        {
            return _tipoNotificacaoRepository.Consultar(queryFilters, orderFilters);
        }

        public IQueryable<TipoNotificacao> ConsultarAtivos()
        {
            return _tipoNotificacaoRepository.Where(x => x.Ativo);
        }

        public IQueryable<TipoNotificacao> ConsultarPorEmpresaFilial(int idEmpresa, int? idFilial)
        {

            var entidades = from p in _tipoNotificacaoRepository.All()
                            where p.Ativo && p.IdEmpresa == idEmpresa
                            select p;


            if (idFilial.HasValue)
                entidades = entidades.Where(x => x.IdFilial == idFilial.Value);

            return entidades;
        }

        public IQueryable<TipoNotificacao> GetPorDataBase(DateTime? dataBase, int idEmpresa)
        {
            if (!dataBase.HasValue)
                dataBase = new DateTime(1753, 1, 1);

            return _tipoNotificacaoRepository
                .Find(x => x.DataUltimaAtualizacao >= dataBase && x.Ativo && x.IdEmpresa == idEmpresa);
        }
    }
}

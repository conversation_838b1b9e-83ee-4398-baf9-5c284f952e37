﻿using System;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.Cartoes.ConciliacaoAnalitico;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Domain.Entities;
using ATS.Domain.Models;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using CustomFilter = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.CustomFilter;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Data.Repository.External.Extratta.Models;

namespace ATS.Domain.Interface.Service
{
    public interface ICartoesService : IService<CartaoResponse>
    {
        CartaoVinculadoPessoaListResponse GetCartoesVinculados(string documento, List<int> idProdutos, 
            bool ativarCartaoVirtualCasoNaoPossuir, string documentoUsuarioAudit = null, 
            string nomeUsuarioAudit = null, bool buscarCartoesBloqueados = false);
        CartaoVinculadoPessoaListResponseDto GetCartoesVinculadosGrid(string documento, List<int> idProdutos);
        List<ProdutoResponse> GetCartaoProdutos();
        HistoricoCartaoPessoaListResponse GetHistoricoCartoes(string documento, List<int> idProdutos);
        HistoricoCartaoPessoaListResponseDto GetHistoricoCartoesGrid(string documento, List<int> idProdutos);
        ConsultarSaldoCartaoResponse ConsultarSaldoCartao(string documento);
        OperacaoCartaoResponseDTO TransferirValorContaBancaria(TransferirContaBancariaRequestDTO request, EOrigemTransacaoCartao origem);
        List<ConsultarPontoDistribuicaoResponse> GetPontoDistribuicao(List<string> cnpjList);
        EnvioRemessaResponse EnviarRemessaCartoes(EnvioRemessaRequest request);
        BaixaRemessaResponse ReceberRemessaCartoes(BaixaRemessaRequest request);
        ConsultarRemessaResponse ConsultarCartoesLote(int loteId);
        object RelatorioSituacaoCartao(RelatorioCartaoApiRequest request, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        EnvioRemessaResponse ValidarCartaoRemessa(ValidarCartaoRequest request);
        RelatorioConciliacaoAnaliticoDataType GetRelatorioConciliacaoAnaliticoDataType(RelatorioConciliacaoAnaliticoRequest request, bool somenteDivergencias, string pesquisa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarRemessaResponseDTO CarregarRemessaEmpresa(List<string> cnpjList, bool filtrarEmpresaOrigem, int Take, int Page, OrderFilters Order, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim);
        SolicitarCompraPedagioResponse SolicitarCompraPedagio(SolicitarCompraPedagioRequest request);
        ConsultaCompraPedagioResponse ConsultarCompraPedagio(int pageSize, int pageIndex, IEnumerable<object> orderBy, List<CustomFilter> customFilter, ConsultaCompraPedagioRequest request);
        CancelarCompraPedagioResponse CancelarCompraPedagio(CancelarCompraPedagioRequest request);
        ConsultaHistoricoRotaResponse ConsultaHistoricoRota(ConsultaHistoricoRotaRequest request);
        List<PessoaContaBancariaResponse> GetContaBancaria(string documento);
        ConsultarSaldoCartaoResponseDTO ConsultarSaldoCartao(ConsultarSaldoCartaoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit);
        ConsultarPessoaDetalhadaResponse ConsultarPortadorDetalhado(string documento);
        AlterarSenhaCartaoResponse AlterarSenhaCartao(AlterarSenhaCartaoRequestDTO requestDto);
        FilteredResultOfMotivoBloqueioModel BuscarMotivosBloquearCartao(int Take, int Page, OrderFilters Order, List<SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CustomFilter> Filters);
        ConsultarSaldoCartaoResponse ConsultarSaldoCartaoAtendimento(ConsultarSaldoCartaoRequest request);
        BloquearCartaoResponse BloquearCartao(BloquearCartaoRequest request);
        DesbloquearCartaoResponse DesbloquearCartao(DesbloquearCartaoRequest request);
        ConsultarExtratoResponseDTO ConsultarExtrato(ConsultarExtratoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit);
        IntegracaoResult<GetExtratoBizResponse> ConsultarExtratoV2(ConsultaExtratoV2DTORequest request);
        ConsultarContasBancariasResponseDTO ContasBancarias(string documento, string documentoUsuarioAudit, string nomeUsuarioAudit);
        InativarContaBancariaAtsResponseDTO InativarContaBancaria(InativarContaBancariaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit);
        ConsultarProtocoloResponse ConsultarProcotolo(ConsultarProtocoloRequest request);
        ConsultarPrimeiraTransacaoResponse ConsultarPrimeiraTransacao(int identificador, int produto);
        EmpresaTokenMicroServicoDto GetOrGenerateTokenEmpresa(string cnpjEmpresa, string appName, int grupoContabilizacao);
        PessoasCartoesSaldoResponse ConsultarListaPortadorCartaoComSaldo(CartoesVinculadosListaSaldoRequest request);
        ConsultarSaldoEmpresaResponse ConsultarSaldoEmpresa(string cnpjEmpresa);
        List<PessoasCartoesVinculadosResponse> CartoesVinculadosLista(List<string> listaCpf);
        BloquearCartaoResponse BloquearCartaoParametrizacaoEmpresa(BloquearCartaoRequest request);
        OperacaoCartaoResponseDTO RealizarCargaFrete(Viagem viagem, ViagemEvento viagemEvento, CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario);
        int GetIdProdutoCartaoFretePadrao();
        ConsultarInformacoesPorDadosContaResponse ConsultarInformacoesPorDadosConta(int conta, int produto, int emissor, int sucursal, int grupoAfinidade);
    }
}

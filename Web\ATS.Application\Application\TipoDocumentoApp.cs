﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Linq;
using System.Transactions;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class TipoDocumentoApp : AppBase, ITipoDocumentoApp
    {
        private readonly ITipoDocumentoService _tipoDocumentoService;

        public TipoDocumentoApp(ITipoDocumentoService tipoDocumentoService)
        {
            _tipoDocumentoService = tipoDocumentoService;
        }

        public TipoDocumento Get(int id)
        {
            return _tipoDocumentoService.Get(id);
        }

        public ValidationResult Add(TipoDocumento entity)
        {
            ValidationResult validationResult;

            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    validationResult = _tipoDocumentoService.Add(entity);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return validationResult;
        }

        public ValidationResult Update(TipoDocumento entity)
        {
            ValidationResult validationResult;

            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    validationResult = _tipoDocumentoService.Update(entity);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return validationResult;
        }

        public int? GetCnh()
        {
            return _tipoDocumentoService.GetCNH();
        }

        public ValidationResult Inativar(int idTipoDoc)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoDocumentoService.Inativar(idTipoDoc);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idTpDoc)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    ValidationResult validationResult = _tipoDocumentoService.Reativar(idTpDoc);
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public IQueryable<TipoDocumento> All()
        {
            return _tipoDocumentoService.All();
        }
    }
}
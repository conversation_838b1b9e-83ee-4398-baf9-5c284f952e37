﻿using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class MenuEstruturaModel
    {
        /// <summary>
        /// Código do Menu
        /// </summary>
        public int? IdMenu { get; set; }

        /// <summary>
        /// Descrição do menu
        /// </summary>
        public string Menu { get; set; }

        /// <summary>
        /// Sequência do módulo
        /// </summary>
        public int SeqModulo { get; set; }

        /// <summary>
        /// Código do menu pai
        /// </summary>
        public int? IdMenuPai { get; set; }

        /// <summary>
        /// Link 
        /// </summary>
        public string Link { get; set; }

        /// <summary>
        /// Link 
        /// </summary>
        public string LinkNovo { get; set; }

        /// <summary>
        /// Nome de uma funcao js para ser executada no front-end ao clicar no menu quando o menu nao tem link
        /// </summary>
        public string Funcao { get; set; }

        public bool IsNovoATS { get; set; }

        /// <summary>
        /// Sequência do menu
        /// </summary>
        public int SeqMenu { get; set; }

        /// <summary>
        /// Lista dos menus vinculados
        /// </summary>
        public virtual ICollection<MenuEstruturaModel> Menus { get; set; }
    }
}
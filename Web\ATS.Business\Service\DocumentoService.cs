﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.Reports.Documento;

namespace ATS.Domain.Service
{
    public class DocumentoService : ServiceBase, IDocumentoService
    {
        private IDocumentoRepository _repository;
        public DocumentoService(IDocumentoRepository repository)
        {
            _repository = repository;
        }
        
        public ValidationResult Add(Documento documento)
        {
            try
            {
                _repository.Add(documento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public IEnumerable<Documento> GetPorEmpresa(int? idEmpresa, int? idFilial)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa && (x.IdFilial == idFilial || x.IdFilial == null) && x.Ativo).ToList();
        }
        
        public List<int> GetIdsDocumentosEmpresa(int idEmpresa)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa && x.Ativo).Select(x => x.IdDocumento).ToList();
        }

        public int GetIdEmpresa(int idDocumento)
        {
            return _repository.Where(x => x.IdDocumento == idDocumento).Select(x => x.IdEmpresa).FirstOrDefault();
        }

        public ValidationResult Inativar(int idDocumento)
        {
            try
            {
                var pagamentoDocumentoRepository = _repository;
                var pagamentoDocumento = pagamentoDocumentoRepository.Get(idDocumento);
                if (pagamentoDocumento == null)
                    return new ValidationResult().Add("Documento não encontrado para inativação. ");

                pagamentoDocumento.Ativo = false;

                pagamentoDocumentoRepository.Update(pagamentoDocumento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public ValidationResult Reativar(int idDocumento)
        {
            try
            {
                var pagamentoDocumentoRepository = _repository;
                var pagamentoDocumento = pagamentoDocumentoRepository.Get(idDocumento);
                if (pagamentoDocumento == null)
                    return new ValidationResult().Add("Documento não encontrado para ativação. ");

                pagamentoDocumento.Ativo = true;

                pagamentoDocumentoRepository.Update(pagamentoDocumento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public ValidationResult Update(Documento documento)
        {
            try
            {
                _repository.Update(documento);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public Documento Get(int idDocumento)
        {
            return _repository
                .Include(x => x.Empresa)
                .Include(x => x.Filial)
                .FirstOrDefault(x => x.IdDocumento == idDocumento);
        }

        public IQueryable<Documento> GetAll(int idEmpresa, int? idFilial)
        {
            var documentos = _repository.GetAll()
                .Where(x => x.Ativo && x.IdEmpresa == idEmpresa);

            if (idFilial.HasValue)
                documentos = documentos.Where(x => x.IdFilial == idFilial);

            return documentos;
        }

        public DataModel<DocumentoModel> ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var documento = _repository
                .GetAll()
                .Include(x => x.Empresa)
                .Include(x => x.Filial);

            if (idEmpresa.HasValue)
                documento = documento.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (!string.IsNullOrWhiteSpace(descricao))
                documento = documento.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                documento = documento.OrderBy(x => x.IdDocumento);
            else
                documento = documento.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            documento = documento.AplicarFiltrosDinamicos(filters);

            return new DataModel<DocumentoModel>
            {
                totalItems = documento.Count(),
                items = documento.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new DocumentoModel
                {
                    IdDocumento = x.IdDocumento,
                    Descricao = x.Descricao,
                    Ativo = x.Ativo,
                    Empresa = x.Empresa?.RazaoSocial,
                    Filial = x.Filial?.RazaoSocial,
                    AtivoDescricao = x.Ativo ? "Sim" : "Não"
                })
            };
        }
        
        public IQueryable<Documento> GetAllDocumentoCredenciamento(List<int> idsEmpresa = null, List<int> idsDocumentoIgnorar = null)
        {
            return _repository.GetAllDocumentoCredenciamento(idsEmpresa, idsDocumentoIgnorar);
        }

        public byte[] GerarRelatorioGridDocumento(int? idEmpresa, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var motivos = ConsultaGrid(idEmpresa, descricao, int.MaxValue, 1, orderFilters, filters);

            return new RelatorioDocumento().GetReport(motivos.items, tipoArquivo, logo);
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Application.Interface
{
    public interface IViagemSolicitacaoAbonoApp
    {
        List<ViagemSolicitacaoAbono> GetSolicitacoes(string token, string numero, DateTime? dataInicio, DateTime? dataFim, EStatusAbono? status);
        ViagemSolicitacaoAbono AlterarStatus(int idViagemSolicitacao, EStatusAbono status);
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class BannerUsuarioMap : EntityTypeConfiguration<BannerUsuario>
    {
        public BannerUsuarioMap()
        {
            ToTable("BANNER_USUARIO");

            HasKey(t => t.Id);

            Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasIndex(t => t.IdUsuario);
            HasIndex(t => t.IdBanner);

            HasRequired(c => c.<PERSON>)
                .WithMany(c => c.BannerUsuarios)
                .HasForeignKey(c => c.IdBanner);

            HasRequired(c => c.Usuario)
                .WithMany(c => c.BannersUsuario)
                .HasForeignKey(c => c.IdUsuario);
        }
    }
}
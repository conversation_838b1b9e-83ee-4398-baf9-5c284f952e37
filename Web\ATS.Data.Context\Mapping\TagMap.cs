﻿using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
     public class TagMap : EntityTypeConfiguration<Domain.Entities.Tag>
     {
          public TagMap()
          {
              ToTable("TAG");

              HasKey(x => x.Id);
              
              Property(c => c.SerialNumber).IsRequired();
              Property(c => c.Desblo<PERSON>ado).IsRequired();
              Property(c => c.Status).IsRequired();
              Property(c => c.IdEmpresa).IsOptional();
              Property(c => c.UsuarioCriacao).HasColumnName("usuariocriacao").IsOptional().HasMaxLength(150);
              Property(c => c.UsuarioAtualizacao).HasColumnName("usuarioatualizacao").IsOptional().HasMaxLength(150);
              Property(c => c.DataCriacao).HasColumnName("datacriacao").IsOptional();
              Property(c => c.DataAtualizacao).HasColumnName("dataatualizacao").IsOptional();
              Property(c => c.Placa).IsOptional();
              Property(c => c.ModeloDescricaoMoveMais).IsOptional();
              Property(c => c.ModeloIdMoveMais).IsOptional();
              
              HasOptional(x => x.Empresa)
                  .WithMany()
                  .HasForeignKey(x => x.IdEmpresa);
          }
     }
}

﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using System.Linq;

namespace ATS.Domain.Interface.Service
{
    public interface IAutenticacaoAplicacaoService : IService<AutenticacaoAplicacao>
    {
        bool AcessoConcedido(string cnpjAplicacao, string token);
        IQueryable<AutenticacaoAplicacao> GetAutenticacaoAplicacaoPorCNPJAplicacao(string cnpjAplicacao, string token);

        AutenticacaoAplicacao Get(string cnpjAplicacao);
        IQueryable<AutenticacaoAplicacao> GetPorIdEmpresa(int idEmpresa);
    }
}

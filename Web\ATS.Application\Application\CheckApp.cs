﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Transactions;

namespace ATS.Application.Application
{
    public class CheckApp : AppBase, ICheckApp
    {
        public ViagemCheck GetById(int id)
        {
            return null; /*new CheckService().GetById(id);*/
        }

        public ValidationResult Add(ViagemCheck viagemCheckIn)
        {
            try
            {
                using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
                {
                    /*ValidationResult validationResult = new CheckService().Add(checkIn);
                    if (!validationResult.IsValid)
                        return validationResult;*/

                    transaction.Complete();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(ViagemCheck entity)
        {
            return null; /*new CheckService().Update(entity);*/
        }

        public CheckInApp Get(int id)
        {
            throw new System.NotImplementedException();
        }

        public IEnumerable<CheckInApp> Find()
        {
            throw new System.NotImplementedException();
        }
    }
}
﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.IoC.Validation;
using ATS.CrossCutting.Reports.Credenciamento;
using ATS.Data.Repository.External.SistemaInfo.Infra;
using Autofac;
using Sistema.Framework.Util.Helper;

namespace ATS.Domain.Service
{
    public interface ITeste
    {
        

        /// <summary>
        /// Retorna se existem diferenças entre as propriedades públicas de dois objetos.
        /// </summary>
        /// <typeparam name="T">Tipo de objeto</typeparam>
        /// <param name="self">Primeiro objeto</param>
        /// <param name="to">Segundo objeto</param>
        /// <param name="ignore">Campos a serem ignorados</param>
        /// <returns></returns>
        bool PublicPropertiesEqual<T>(T self, T to, params string[] ignore) where T : class;
    }

    public class CredenciamentoService : ServiceBase, ICredenciamentoService, ITeste
    {
        private readonly ILifetimeScope _scope;
        private readonly ICredenciamentoRepository _repository;
        private readonly ICredenciamentoAnexoRepository _repositoryCredenciamentoAnexo;
        private readonly ICredenciamentoMotivoRepository _repositoryCredenciamentoMotivo;
        private readonly IEstabelecimentoBaseDocumentoRepository _repositoryEstabelecimentoBaseDocumento;
        private readonly ILayoutService _layoutService;
        private readonly IParametrosAdministradoraPlataformaService _parametrosService;
        private readonly IParametrosAdministradoraPlataformaService _parametrosAdministradoraPlataformaService;
        private readonly IEmpresaService _empresaService;
        private readonly IMotivoRepository _motivoRepository;
        private readonly ILayoutRepository _layoutRepository;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IEmailService _emailService;
        private readonly IUsuarioEstabelecimentoRepository _usuarioEstabelecimentoRepository;
        private readonly IEstabelecimentoBaseRepository _estabelecimentoBaseRepository;
        private readonly IProtocoloRepository _protocoloRepository;
        private readonly IWebhookService _webhookService;
        private readonly WebhookActionDependencies _webhookActionDependencies;
        private readonly IDataMediaServerService _dataMediaServerService;
        
        private IEstabelecimentoBaseService _estabelecimentoBaseService => _scope.Resolve<IEstabelecimentoBaseService>();
        private IEstabelecimentoService _estabelecimentoService => _scope.Resolve<IEstabelecimentoService>();

        public CredenciamentoService(ILifetimeScope scope, ICredenciamentoRepository repository, ICredenciamentoAnexoRepository repositoryCredenciamentoAnexo,
            ICredenciamentoMotivoRepository repositoryCredenciamentoMotivo, IEstabelecimentoBaseDocumentoRepository repositoryEstabelecimentoBaseDocumento, ILayoutService layoutService, IParametrosAdministradoraPlataformaService parametrosService,
            IParametrosAdministradoraPlataformaService parametrosAdministradoraPlataformaService, IEmpresaService empresaService,
            IMotivoRepository motivoRepository, ILayoutRepository layoutRepository, IEmpresaRepository empresaRepository, IEmailService emailService,
            IUsuarioEstabelecimentoRepository usuarioEstabelecimentoRepository, IEstabelecimentoBaseRepository estabelecimentoBaseRepository, IProtocoloRepository protocoloRepository,
            IWebhookService webhookService, WebhookActionDependencies webhookActionDependencies, IDataMediaServerService dataMediaServerService)
        {
            _scope = scope;
            _repository = repository;
            _repositoryCredenciamentoAnexo = repositoryCredenciamentoAnexo;
            _repositoryCredenciamentoMotivo = repositoryCredenciamentoMotivo;
            _repositoryEstabelecimentoBaseDocumento = repositoryEstabelecimentoBaseDocumento;
            _layoutService = layoutService;
            _parametrosService = parametrosService;
            _parametrosAdministradoraPlataformaService = parametrosAdministradoraPlataformaService;
            _empresaService = empresaService;
            _motivoRepository = motivoRepository;
            _layoutRepository = layoutRepository;
            _empresaRepository = empresaRepository;
            _emailService = emailService;
            _usuarioEstabelecimentoRepository = usuarioEstabelecimentoRepository;
            _estabelecimentoBaseRepository = estabelecimentoBaseRepository;
            _protocoloRepository = protocoloRepository;
            _webhookService = webhookService;
            _webhookActionDependencies = webhookActionDependencies;
            _dataMediaServerService = dataMediaServerService;
        }

        public Credenciamento Get(int id)
        {
            return _repository.Find(c => c.IdCredenciamento == id)
                .Include(x => x.CredenciamentoAnexo)
                .FirstOrDefault();
        }

        public ValidationResult Add(Credenciamento credenciamento, int administradoraPlataforma)
        {
            try
            {
                var emp = _empresaService.Get(credenciamento.IdEmpresa, null);
                var nomeAplicativo = string.Empty;
                if (emp != null)
                {
                    var layout = _layoutService.GetPorEmpresa(emp.IdEmpresa);
                    if (layout != null && !string.IsNullOrWhiteSpace(layout?.NomeAplicativo))
                        nomeAplicativo = layout.NomeAplicativo;
                }

                var hasCredenciamentoEnv = _repository
                    .Any(x => x.IdEmpresa == credenciamento.IdEmpresa &&
                              x.IdEstabelecimentoBase == credenciamento.IdEstabelecimentoBase &&
                              (x.Status == EStatusCredenciamento.Enviado || x.Status == EStatusCredenciamento.Aprovado));

                if (!hasCredenciamentoEnv && credenciamento.IdEstabelecimento.HasValue)
                    hasCredenciamentoEnv = _repository
                        .Any(x => x.IdEmpresa == credenciamento.IdEmpresa &&
                                  x.IdEstabelecimento == credenciamento.IdEstabelecimento &&
                                  (x.Status == EStatusCredenciamento.Enviado || x.Status == EStatusCredenciamento.Aprovado));

                if (hasCredenciamentoEnv)
                    throw new Exception("Já existe um registro de credenciamento pra o estabelecimento!");

                credenciamento.DataValidadeChave = DateTime.Now.AddHours(emp?.HorasValidadeChaveCadastroUsuario ?? 24);

                _repository.Add(credenciamento);
                if (credenciamento.IdEstabelecimentoBase.HasValue)
                {
                    var estabelecimento = _estabelecimentoBaseService.Get(credenciamento.IdEstabelecimentoBase.Value);
                    if (emp != null && estabelecimento != null)
                    {
                        EnviarEmailSolicitacaoCredenciamento(emp.NomeFantasia, estabelecimento.Descricao, emp.Email, credenciamento.Empresa?.Logo,
                            nomeAplicativo, administradoraPlataforma);
                    }
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.GetBaseException().Message);
            }
        }

        public ValidationResult Update(Credenciamento credenciamento, int administradoraPlataforma)
        {
            try
            {
                var nomeAplicativo = string.Empty;
                var emp = _empresaService.Get(credenciamento.IdEmpresa, null);
                if (emp != null)
                {
                    var layout = _layoutService.GetPorEmpresa(emp.IdEmpresa);
                    if (layout != null && !string.IsNullOrWhiteSpace(layout?.NomeAplicativo))
                        nomeAplicativo = layout.NomeAplicativo;
                }

                _repository.Update(credenciamento);


                if (credenciamento.IdEstabelecimentoBase.HasValue &&
                    credenciamento.Status == EStatusCredenciamento.Enviado)
                {
                    var estabelecimento =
                        _estabelecimentoBaseService.Get(credenciamento.IdEstabelecimentoBase.Value);
                    Task.Run(() =>
                    {
                        if (emp != null && estabelecimento != null)
                        {
                            EnviarEmailSolicitacaoCredenciamento(emp.NomeFantasia, estabelecimento.Descricao,
                                emp.Email,
                                credenciamento.Empresa?.Logo, nomeAplicativo, administradoraPlataforma);
                        }
                    });
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.GetBaseException().Message);
            }
        }

        public object ConsultarImagemPorToken(string token)
        {
            var docto = _dataMediaServerService.GetMedia(token);
            return new
            {
                docto?.Data,
                Tipo = docto?.Type,
                docto?.MimeType,
                docto?.FileName
            };
        }

        public bool ChaveValida(string chaveEmail)
        {
            var credenciamento = _repository.FirstOrDefault(x => x.ChaveCadastroUsuario == chaveEmail);

            return DateTime.Now <= credenciamento?.DataValidadeChave;
        }

        public Credenciamento GetByChaveEmail(string chaveEmail)
        {
            return _repository.FirstOrDefault(x => x.ChaveCadastroUsuario == chaveEmail);
        }

        public void ReenviarEmailCredenciamentoUsuarioCadastro(int id, string linkWebNovo, int administradoraPlataforma)
        {
            var credenciamento = _repository
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoBase.EstabelecimentoBaseProdutos)
                .FirstOrDefault(x => x.IdCredenciamento == id);

            var nomeAplicativo = string.Empty;
            var layout = _layoutService.GetPorEmpresa(credenciamento.IdEmpresa);
            if (layout != null && !string.IsNullOrWhiteSpace(layout.NomeAplicativo))
                nomeAplicativo = layout.NomeAplicativo;

            credenciamento.ChaveCadastroUsuario = Guid.NewGuid().ToString().Replace("-", "");
            // Validade da chace
            credenciamento.DataValidadeChave = DateTime.Now.AddHours(credenciamento.Empresa.HorasValidadeChaveCadastroUsuario);

            _repository.Update(credenciamento);

            EnviarEmailCredenciamentoCadastroUsuario(credenciamento.EstabelecimentoBase.Descricao,
                credenciamento.EstabelecimentoBase.Email, credenciamento.Empresa.NomeFantasia,
                credenciamento.Empresa?.Logo, nomeAplicativo, credenciamento.ChaveCadastroUsuario,
                credenciamento.IdEstabelecimentoBase.Value, credenciamento.DataValidadeChave, linkWebNovo, administradoraPlataforma);
        }

        public DataModel<CredenciamentoModel> ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> Filters)
        {
            var credenciamentos = _repository
                .Find(x => x.IdEstabelecimentoBase > 0)
                .Include(x => x.EstabelecimentoBase);

            if (idEmpresa.HasValue)
                credenciamentos = credenciamentos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                credenciamentos = credenciamentos.OrderBy(x => x.IdCredenciamento);
            else
                credenciamentos = credenciamentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            credenciamentos = credenciamentos.AplicarFiltrosDinamicos<Credenciamento>(Filters);

            return new DataModel<CredenciamentoModel>
            {
                totalItems = credenciamentos.Count(),
                items = credenciamentos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new CredenciamentoModel
                    {
                        Estabelecimento = x.EstabelecimentoBase?.Descricao,
                        CNPJ = x.EstabelecimentoBase?.CNPJEstabelecimento.ToCNPJFormato(),
                        DataSolicitacao = x.DataSolicitacao.ToString("dd/MM/yyyy hh:mm"),
                        Status = x.Status.DescriptionAttr(),
                        IdCredenciamento = x.IdCredenciamento,
                        IdEstabelecimentoBase = x.IdEstabelecimentoBase,
                        IdEstabelecimento = x.IdEstabelecimento,
                        StatusInt = (int) x.Status
                    })
            };
        }

        public List<object> ConsultarCredenciamentosPorEmpresa(int idEmpresa, DateTime? dtInicio, DateTime? dtFim, int administradoraPlataforma,
            bool aberto = false, bool aprovado = false, bool rejeitado = false, bool bloqueado = false, bool regular = true, bool irregular = true,
            bool aguardando = true, int? idEstabelecimento = null)
        {
            var credenciamentos = _repository
                .Where(x => x.IdEmpresa == idEmpresa && x.IdEstabelecimentoBase > 0)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoBase.UsuarioEstabelecimentos);

            if (idEstabelecimento.HasValue)
            {
                credenciamentos = credenciamentos.Where(c => c.IdEstabelecimento == idEstabelecimento);
            }

            if (dtInicio.HasValue && dtFim.HasValue)
            {
                credenciamentos = credenciamentos.Where(x => x.DataSolicitacao >= dtInicio && x.DataSolicitacao <= dtFim);
            }

            var aprovados = new List<Credenciamento>();
            var abertos = new List<Credenciamento>();
            var rejeitados = new List<Credenciamento>();
            var bloqueados = new List<Credenciamento>();

            AtualizarStatusDocumentacao(credenciamentos, administradoraPlataforma);

            var documentStates_ = new List<EStatusDocumentacaoCredenciamento>();

            if (regular)
                documentStates_.Add(EStatusDocumentacaoCredenciamento.Regular);

            if (irregular)
                documentStates_.Add(EStatusDocumentacaoCredenciamento.Irregular);

            if (aguardando)
                documentStates_.Add(EStatusDocumentacaoCredenciamento.Aguardando);

            if (aberto)
                abertos = credenciamentos.Where(x => x.Status == EStatusCredenciamento.Enviado).ToList();

            if (aprovado)
                aprovados = credenciamentos.Where(x => x.Status == EStatusCredenciamento.Aprovado).ToList();

            if (rejeitado)
                rejeitados = credenciamentos.Where(x => x.Status == EStatusCredenciamento.Rejeitado).ToList();

            if (bloqueado)
                bloqueados = credenciamentos.Where(x => x.Status == EStatusCredenciamento.Bloqueado).ToList();

            var temp = new List<Credenciamento>();

            if (abertos.Any())
                temp.AddRange(abertos);

            if (aprovados.Any())
                temp.AddRange(aprovados);

            if (rejeitados.Any())
                temp.AddRange(rejeitados);

            if (bloqueados.Any())
                temp.AddRange(bloqueados);

            if (documentStates_.Any())
                temp = temp.Where(obj => documentStates_.Contains(obj.StatusDocumentacao)).ToList();

            var retorno = new List<object>();
            if (temp.Any())
                temp.ForEach(x =>
                    retorno.Add(new
                    {
                        Estabelecimento = x.EstabelecimentoBase?.Descricao,
                        CNPJ = x.EstabelecimentoBase?.CNPJEstabelecimento.ToCNPJFormato(),
                        DataSolicitacao = x.DataSolicitacao.ToString("dd/MM/yyyy hh:mm"),
                        Status = x.Status.DescriptionAttr(),
                        x.IdCredenciamento,
                        x.IdEstabelecimentoBase,
                        x.IdEstabelecimento,
                        StatusInt = x.Status,
                        HasChave = !string.IsNullOrEmpty(x.ChaveCadastroUsuario) && x.ChaveCadastroUsuario.Length > 0,
                        ChaveExpirou = x.DataValidadeChave.HasValue && DateTime.Now > x.DataValidadeChave,
                        EstabTemUsuario = x.EstabelecimentoBase.UsuarioEstabelecimentos.Any(),
                        StatusDocumentacao = x.StatusDocumentacao.DescriptionAttr(),
                        TemHistoricoRejeicaoDocumentacao = _repositoryCredenciamentoMotivo.Any(y => y.IdCredenciamento == x.IdCredenciamento)
                    }));

            return retorno;
        }

        private void AtualizarStatusDocumentacao(IQueryable<Credenciamento> credenciamentos, int administradoraPlataforma)
        {
            var idCredenciamentoList = credenciamentos
                .Where(x => x.StatusDocumentacao == EStatusDocumentacaoCredenciamento.Regular)
                .Select(x => x.IdCredenciamento)
                .ToList();

            var anexos = _repositoryCredenciamentoAnexo.Find(x => idCredenciamentoList.Contains((int) x.IdCredenciamento))
                .Where(x => x.DataValidade.HasValue)
                .ToList();

            idCredenciamentoList = new List<int>();

            foreach (var anexo in anexos)
                if (!idCredenciamentoList.Contains((int) (anexo.IdCredenciamento)) && anexo.DataValidade?.GetDataOnly() < DateTime.Now.GetDataOnly())
                    idCredenciamentoList.Add((int) anexo.IdCredenciamento);

            foreach (var idCredenciamento in idCredenciamentoList)
                AtualizarStatusDocumentacao(idCredenciamento, EStatusDocumentacaoCredenciamento.Irregular, administradoraPlataforma,
                    "[Rejeição automática] Data de validade de um ou mais documentos de credenciamento está expirada");
        }

        public void DesbloquearCredenciamento(int idCredenciamento)
        {
            var credenciamento = _repository
                .Include(x => x.Estabelecimento)
                .FirstOrDefault(x => x.IdCredenciamento == idCredenciamento);

            if (credenciamento == null)
                throw new Exception($"Não foi possível encontrar um credenciamento para o id {idCredenciamento} !");

            credenciamento.Status = EStatusCredenciamento.Rejeitado;
            credenciamento.DataAtualizacao = DateTime.Now;

            _repository.Update(credenciamento);
        }

        public void RejeitarCredenciamento(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma)
        {
            var credenciamento = _repository
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.Estabelecimento)
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.IdCredenciamento == idCredenciamento);

            if (credenciamento == null)
                throw new Exception($"Não foi possível encontrar um credenciamento para o id {idCredenciamento} !");

            var motivo = _motivoRepository
                .FirstOrDefault(x => x.IdMotivo == idMotivo);

            if (motivo == null)
                throw new Exception("Não foi possível encontrar o motivo informado!");

            credenciamento.IdMotivo = idMotivo;
            credenciamento.Detalhamento = detalhamento;
            credenciamento.Status = EStatusCredenciamento.Rejeitado;
            credenciamento.DataAtualizacao = DateTime.Now;
            credenciamento.ChaveCadastroUsuario = string.Empty;
            credenciamento.EmailEnviado = false;
            // Caso n tenha dados informados vira 24hs por default
            credenciamento.DataValidadeChave = null;
            credenciamento.ChaveCadastroUsuario = string.Empty;

            if (credenciamento.Estabelecimento != null)
                credenciamento.Estabelecimento.Credenciado = false;

            _repository.Update(credenciamento);

            var detalhes = "";
            if (!string.IsNullOrEmpty(detalhamento))
                detalhes = "Detalhamento: " + detalhamento;

            var nomeAplicativo = string.Empty;
            var layout = _layoutService.GetPorEmpresa(credenciamento.IdEmpresa);
            if (layout != null && !string.IsNullOrWhiteSpace(layout.NomeAplicativo))
                nomeAplicativo = layout.NomeAplicativo;

            EnviarEmailReprovacaoCredenciamento(credenciamento.EstabelecimentoBase.Descricao,
                credenciamento.EstabelecimentoBase.Email, credenciamento.Empresa.NomeFantasia, true,
                credenciamento.Empresa?.Logo, administradoraPlataforma, motivo.Descricao, detalhes, nomeAplicativo);
        }

        public void BloquearCredenciamento(int idCredenciamento)
        {
            var credenciamento = _repository
                .Include(x => x.Estabelecimento)
                .FirstOrDefault(x => x.IdCredenciamento == idCredenciamento);

            if (credenciamento == null)
                throw new Exception($"Não foi possível encontrar um credenciamento para o id {idCredenciamento} !");

            credenciamento.Status = EStatusCredenciamento.Bloqueado;
            credenciamento.DataAtualizacao = DateTime.Now;
            if (credenciamento.Estabelecimento != null)
                credenciamento.Estabelecimento.Credenciado = false;

            _repository.Update(credenciamento);
        }

        public void Descredenciar(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma)
        {
            var credenciamento = _repository
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.Estabelecimento)
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.IdCredenciamento == idCredenciamento);

            if (credenciamento == null)
                throw new Exception($"Não foi possível encontrar um credenciamento para o id {idCredenciamento} !");

            var motivo = _motivoRepository
                .FirstOrDefault(x => x.IdMotivo == idMotivo);

            if (motivo == null)
                throw new Exception($"Não foi possível encontrar o motivo informado!");

            credenciamento.IdMotivo = idMotivo;
            credenciamento.Detalhamento = detalhamento;
            credenciamento.Status = EStatusCredenciamento.Descredenciado;
            credenciamento.DataAtualizacao = DateTime.Now;
            if (credenciamento.Estabelecimento != null)
            {
                credenciamento.Estabelecimento.IdEstabelecimentoBase = null;
                credenciamento.Estabelecimento.Credenciado = false;
            }

            //Antes de realizarmos a atualização do credenciamento, salvamos a referencia de descrição e email do base, para que não se perca.
            var descricaoBase = credenciamento.EstabelecimentoBase.Descricao;
            var emailBase = credenciamento.EstabelecimentoBase.Email;

            _repository.Update(credenciamento);

            var detalhes = "";
            if (!string.IsNullOrEmpty(detalhamento))
                detalhes = "Detalhamento: " + detalhamento;
            var nomeAplicativo = string.Empty;
            var layout = _layoutService.GetPorEmpresa(credenciamento.IdEmpresa);
            if (layout != null && !string.IsNullOrWhiteSpace(layout.NomeAplicativo))
                nomeAplicativo = layout.NomeAplicativo;

            EnviarEmailReprovacaoCredenciamento(descricaoBase, emailBase, credenciamento.Empresa.NomeFantasia, false, credenciamento.Empresa?.Logo,
                administradoraPlataforma, motivo.Descricao, detalhes, nomeAplicativo);
        }

        public ValidationResult AprovarCredenciamento(int idCredenciamento, string linkWebNovo, int administradoraPlataforma)
        {
            var validacao = new ValidationResult();

            var credenciamento = _repository
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoBase.Pais)
                .Include(x => x.EstabelecimentoBase.Cidade)
                .Include(x => x.EstabelecimentoBase.Estado)
                .Include(x => x.EstabelecimentoBase.TipoEstabelecimento)
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoBase.EstabelecimentoBaseContasBancarias)
                .Include(x => x.EstabelecimentoBase.EstabelecimentoBaseProdutos)
                .FirstOrDefault(x => x.IdCredenciamento == idCredenciamento);

            if (credenciamento == null)
                return validacao.Add($"Não foi possível encontrar um credenciamento para o id {idCredenciamento} !");

            Estabelecimento estabelecimento = null;
            if (credenciamento.IdEstabelecimento.HasValue)
                estabelecimento = _estabelecimentoService.Get(credenciamento.IdEstabelecimento.Value, false);

            if (estabelecimento == null)
            {
                estabelecimento = new Estabelecimento
                {
                    CNPJEstabelecimento = credenciamento.EstabelecimentoBase.CNPJEstabelecimento,
                    Descricao = credenciamento.EstabelecimentoBase.Descricao,
                    IdEmpresa = credenciamento.IdEmpresa,
                    DataUltimaAtualizacao = DateTime.Now,
                    IdTipoEstabelecimento = credenciamento.EstabelecimentoBase.IdTipoEstabelecimento,
                    IdPais = credenciamento.EstabelecimentoBase.IdPais,
                    IdEstado = credenciamento.EstabelecimentoBase.IdEstado,
                    IdCidade = credenciamento.EstabelecimentoBase.IdCidade,
                    Bairro = credenciamento.EstabelecimentoBase.Bairro,
                    Logradouro = credenciamento.EstabelecimentoBase.Logradouro,
                    Numero = credenciamento.EstabelecimentoBase.Numero,
                    CEP = credenciamento.EstabelecimentoBase.CEP,
                    Latitude = credenciamento.EstabelecimentoBase.Latitude,
                    Longitude = credenciamento.EstabelecimentoBase.Longitude,
                    Email = credenciamento.EstabelecimentoBase.Email,
                    Complemento = credenciamento.EstabelecimentoBase.Complemento,
                    Ativo = true,
                    Credenciado = true,
                    EstabelecimentoProdutos = new List<EstabelecimentoProduto>(),
                    EstabelecimentoAssociacoesAssociacao = new List<EstabelecimentoAssociacao>(),
                    EstabelecimentoAssociacoesEstabelecimento = new List<EstabelecimentoAssociacao>(),
                    EstabelecimentoContasBancarias = new List<EstabelecimentoContaBancaria>(),
                    IdEstabelecimentoBase = credenciamento.IdEstabelecimentoBase,
                    Associacao = credenciamento.EstabelecimentoBase.Associacao,
                    IdFilialProcessoCaixaTms = credenciamento.EstabelecimentoBase.IdFilial,
                    RazaoSocial = credenciamento.EstabelecimentoBase.RazaoSocial
                };

                if (credenciamento.EstabelecimentoBase.AssociacoesBaseEstabelecimento != null)
                    foreach (var item in credenciamento.EstabelecimentoBase.AssociacoesBaseEstabelecimento)
                    {
                        var estabFromBase = _estabelecimentoService.GetByBase(item.IdAssociacao);
                        if (estabFromBase.HasValue)
                            estabelecimento.EstabelecimentoAssociacoesEstabelecimento.Add(new EstabelecimentoAssociacao
                            {
                                IdAssociacao = estabFromBase.Value,
                                IdEstabelecimento = item.IdEstabelecimento
                            });
                    }

                if (credenciamento.EstabelecimentoBase.EstabelecimentoBaseProdutos != null)
                {
                    foreach (var item in credenciamento.EstabelecimentoBase.EstabelecimentoBaseProdutos)
                    {
                        var produto = new EstabelecimentoProduto();
                        produto.Descricao = item.Descricao;
                        produto.UnidadeMedida = item.UnidadeMedida;
                        produto.PrecoUnitario = item.PrecoUnitario;
                        produto.PrecoPromocional = item.PrecoPromocional;
                        produto.IdEstabelecimentoBase = credenciamento.EstabelecimentoBase.IdEstabelecimento;
                        produto.IdProdutoBase = item.IdProduto;
                        estabelecimento.EstabelecimentoProdutos.Add(produto);
                    }
                }

                if (credenciamento.EstabelecimentoBase.EstabelecimentoBaseContasBancarias != null &&
                    credenciamento.EstabelecimentoBase.EstabelecimentoBaseContasBancarias.Any())
                {
                    foreach (var conta in credenciamento.EstabelecimentoBase.EstabelecimentoBaseContasBancarias)
                    {
                        var estabelecimentoConta = new EstabelecimentoContaBancaria
                        {
                            DigitoConta = conta.DigitoConta,
                            CodigoBanco = conta.CodigoBanco,
                            Conta = conta.Conta,
                            NomeBanco = conta.NomeBanco,
                            CnpjTitular = conta.CnpjTitular,
                            Agencia = conta.Agencia,
                            NomeTitular = conta.NomeTitular,
                            TipoConta = conta.TipoConta,
                            NomeConta = conta.NomeConta
                        };

                        Validator.Validate(estabelecimentoConta);
                        estabelecimento.EstabelecimentoContasBancarias.Add(estabelecimentoConta);
                    }
                }

                validacao.Add(_estabelecimentoService.Add(estabelecimento));
                if (!validacao.IsValid)
                    return validacao;
            }
            else
            {
                estabelecimento.CNPJEstabelecimento = credenciamento.EstabelecimentoBase.CNPJEstabelecimento;
                estabelecimento.Descricao = credenciamento.EstabelecimentoBase.Descricao;
                estabelecimento.IdEmpresa = credenciamento.IdEmpresa;
                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                estabelecimento.IdTipoEstabelecimento = credenciamento.EstabelecimentoBase.IdTipoEstabelecimento;
                estabelecimento.IdPais = credenciamento.EstabelecimentoBase.IdPais;
                estabelecimento.IdEstado = credenciamento.EstabelecimentoBase.IdEstado;
                estabelecimento.IdCidade = credenciamento.EstabelecimentoBase.IdCidade;
                estabelecimento.Bairro = credenciamento.EstabelecimentoBase.Bairro;
                estabelecimento.Logradouro = credenciamento.EstabelecimentoBase.Logradouro;
                estabelecimento.Numero = credenciamento.EstabelecimentoBase.Numero;
                estabelecimento.CEP = credenciamento.EstabelecimentoBase.CEP;
                estabelecimento.Latitude = credenciamento.EstabelecimentoBase.Latitude;
                estabelecimento.Longitude = credenciamento.EstabelecimentoBase.Longitude;
                estabelecimento.Email = credenciamento.EstabelecimentoBase.Email;
                estabelecimento.Complemento = credenciamento.EstabelecimentoBase.Complemento;
                estabelecimento.Ativo = true;
                estabelecimento.Credenciado = true;
                estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();
                estabelecimento.IdEstabelecimentoBase = credenciamento.IdEstabelecimentoBase;
                estabelecimento.RazaoSocial = credenciamento.EstabelecimentoBase.RazaoSocial;

                if (credenciamento.EstabelecimentoBase.EstabelecimentoBaseProdutos != null)
                {
                    foreach (var item in credenciamento.EstabelecimentoBase.EstabelecimentoBaseProdutos)
                    {
                        var produto = new EstabelecimentoProduto();
                        produto.Descricao = item.Descricao;
                        produto.UnidadeMedida = item.UnidadeMedida;
                        produto.PrecoUnitario = item.PrecoUnitario;
                        produto.PrecoPromocional = item.PrecoPromocional;
                        produto.IdEstabelecimentoBase = credenciamento.EstabelecimentoBase.IdEstabelecimento;
                        produto.IdProdutoBase = item.IdProduto;
                        estabelecimento.EstabelecimentoProdutos.Add(produto);
                    }
                }

                validacao.Add(_estabelecimentoService.Update(estabelecimento));
                if (!validacao.IsValid)
                    return validacao;
            }

            var nomeAplicativo = string.Empty;
            var nomeApp = _layoutRepository
                .Find(x => x.IdEmpresa == credenciamento.IdEmpresa)
                .Select(x => x.NomeAplicativo)
                .FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(nomeApp))
                nomeAplicativo = nomeApp;

            var validacaoEmail = EnviarEmailAprovacaoCredenciamento(credenciamento.EstabelecimentoBase.Descricao,
                credenciamento.EstabelecimentoBase.Email, credenciamento.Empresa.NomeFantasia, credenciamento.Empresa?.Logo, nomeAplicativo,
                administradoraPlataforma);
            if (!validacaoEmail.IsValid)
                validacao.Add($"Erro ao enviar e-mail de criação de usuário: {validacaoEmail}", EFaultType.Alert);

            //TODO: ADICIONAR LOG
            credenciamento.IdMotivo = null;
            credenciamento.Status = EStatusCredenciamento.Aprovado;
            credenciamento.DataAtualizacao = DateTime.Now;
            credenciamento.IdEstabelecimento = estabelecimento.IdEstabelecimento;

            // Validade da chace
            credenciamento.DataValidadeChave = DateTime.Now.AddHours(credenciamento.Empresa.HorasValidadeChaveCadastroUsuario);
            credenciamento.ChaveCadastroUsuario = Guid.NewGuid().ToString().Replace("-", "");

            // Aqui iremos enviar o email e definir o email como enviado
            var usuarioEstab = _usuarioEstabelecimentoRepository
                .FirstOrDefault(x => x.IdEstabelecimento == estabelecimento.IdEstabelecimentoBase);
            if (usuarioEstab == null)
            {
                if(!credenciamento.EmailEnviado || credenciamento.DataValidadeChave < DateTime.Now)
                    EnviarEmailCredenciamentoCadastroUsuario(credenciamento.EstabelecimentoBase.Descricao,
                        credenciamento.EstabelecimentoBase.Email, credenciamento.Empresa.NomeFantasia,
                        credenciamento.Empresa?.Logo, nomeAplicativo, credenciamento.ChaveCadastroUsuario,
                        credenciamento.IdEstabelecimentoBase.Value, credenciamento.DataValidadeChave, linkWebNovo, administradoraPlataforma);
            }

            // Variável definida como true
            credenciamento.EmailEnviado = true;

            if (credenciamento.Estabelecimento != null)
            {
                credenciamento.Estabelecimento.IdEstabelecimentoBase = credenciamento.IdEstabelecimentoBase;
                credenciamento.Estabelecimento.Credenciado = true;
            }

            EspelhamentoDocumentosBaseParaCredenciamentoAnexo(credenciamento);

            _repository.Update(credenciamento);
            EnviarCredenciamentoWebHook(credenciamento);

            return validacao;
        }
        
        public ValidationResult AtualizaEmailEnviado(List<int> listaIdCredenciamento)
        {
            var validacao = new ValidationResult();
            try
            {
                foreach (var credenciamento in listaIdCredenciamento.Select(id => _repository
                    .FirstOrDefault(x => x.IdCredenciamento == id)).Where(credenciamento => credenciamento != null))
                {
                    credenciamento.EmailEnviado = true;
                    _repository.Update(credenciamento);
                }

                return validacao;
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível atualizar o credencieamento", EFaultType.Error);
            }
        }

        private void EspelhamentoDocumentosBaseParaCredenciamentoAnexo(Credenciamento credenciamento)
        {
            var anexos = _repositoryEstabelecimentoBaseDocumento
                .Find(x => x.IdEstabelecimentoBase == credenciamento.EstabelecimentoBase.IdEstabelecimento)
                .ToList()
                .Select(x => new CredenciamentoAnexo
                {
                    IdDocumento = x.IdDocumento,
                    Token = x.Token,
                    DataValidade = x.DataValidade,
                    Descricao = x.Descricao
                }).ToList();

            if (anexos.Any())
            {
                credenciamento.StatusDocumentacao = EStatusDocumentacaoCredenciamento.Aguardando;

                foreach (var a in anexos)
                    AddCredenciamentoAnexo(credenciamento.EstabelecimentoBase.IdEstabelecimento, a.IdDocumento, a.Token, a.DataValidade, a.Descricao);
            }
        }

        public IEnumerable<Credenciamento> GetPorEmpresa(int? idEmpresa)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa).ToList();
        }

        public IQueryable<Credenciamento> GetQuery()
        {
            return _repository.All();
        }

        public object GetDetalhesRejeicaoCredenciamento(int idCredenciamento)
        {
            var credenciamento = GetQuery().Where(c => c.IdCredenciamento == idCredenciamento).Include(c => c.Motivo).Select(c => new
            {
                c.DataAtualizacao,
                c.Motivo.Descricao,
                c.Detalhamento
            }).FirstOrDefault();

            var retorno = new
            {
                DataRejeicao = credenciamento?.DataAtualizacao.ToString("G"),
                Motivo = credenciamento?.Descricao,
                Detalhes = credenciamento?.Detalhamento
            };

            return retorno;
        }

        public bool HasDocumentacaoVencendo(int idCredenciamento)
        {
            var credenciamentoQuery = _repository.Where(c => c.IdCredenciamento == idCredenciamento)
                .Include(c => c.CredenciamentoAnexo);

            if (!credenciamentoQuery.Any())
                return false;

            var credenciamento = credenciamentoQuery.First();

            var prazoParaInformarDocumentos = _empresaRepository.All().Where(c => c.IdEmpresa == credenciamento.IdEmpresa)
                .Select(c => c.PrazoParaInformarDocumentos).First();

            var menorDataValidade = credenciamento.CredenciamentoAnexo.Where(c => c.DataValidade.HasValue).Select(c => c.DataValidade).OrderBy(c => c)
                .FirstOrDefault();
            var dataMaxima = DateTime.Now.AddDays(prazoParaInformarDocumentos ?? 0);

            if (!menorDataValidade.HasValue) return false;

            return menorDataValidade.Value < dataMaxima;
        }

        public ValidationResult Cancelar(int idCredenciamento)
        {
            try
            {
                var credenciamento = _repository.Get(idCredenciamento);
                if (credenciamento == null)
                    return new ValidationResult().Add("Credencaimento não encontrado!");

                credenciamento.Status = EStatusCredenciamento.Cancelado;
                _repository.Update(credenciamento);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.GetBaseException().Message);
            }
        }

        public object ConsultarGridPendentes(int idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var estabelecimentoBase = _estabelecimentoBaseRepository.FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento);

            if (estabelecimentoBase == null)
                throw new Exception("Estabelecimento base não foi encontrado!");

            var empresas = _empresaRepository
                .Find(x => x.Ativo &&
                           x.PermiteCredenciamento &&
                           ((x.Estabelecimentos.Any(y => !y.Credenciado && y.CNPJEstabelecimento == estabelecimentoBase.CNPJEstabelecimento)) ||
                            !(x.Estabelecimentos.Any(y => y.CNPJEstabelecimento == estabelecimentoBase.CNPJEstabelecimento))))
                .Include(x => x.Estabelecimentos)
                .Include(x => x.Credenciamentos);

            var empresasCredenciadas = _empresaRepository
                .Find(x => x.Ativo &&
                           x.PermiteCredenciamento &&
                           (x.Estabelecimentos.Any(y => y.Credenciado && y.CNPJEstabelecimento == estabelecimentoBase.CNPJEstabelecimento)))
                .Include(x => x.Estabelecimentos);


            empresas = empresas.Union(empresasCredenciadas);

            //empresas = string.IsNullOrWhiteSpace(order?.Campo)
            //        ? empresas.OrderBy(x => x.NomeFantasia)
            //        : empresas.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            empresas = empresas.AplicarFiltrosDinamicos<Empresa>(filters);

            var aux = new List<Empresa>();
            foreach (var empresa in empresas)
            {
                if (empresa.Credenciamentos != null &&
                    empresa.Credenciamentos.Any(x =>
                        x.Status == EStatusCredenciamento.Bloqueado && x.IdEstabelecimentoBase == estabelecimentoBase.IdEstabelecimento)) continue;

                aux.Add(empresa);
            }

            //TODO Refazer o select no DAPPER
            var retQuery = aux.Select(x => new
            {
                IdCredenciamento = GetIdCredenciamento(estabelecimentoBase.IdEstabelecimento, x.IdEmpresa),
                RazaoSocialEmpresa = x.RazaoSocial,
                x.CNPJ,
                x.IdEmpresa,
                x.Telefone,
                x.Email,
                Status = GetStatus(x.IdEmpresa, estabelecimentoBase.IdEstabelecimento),
                StatusDocumentacao = GetStatusDocumentacao(x.IdEmpresa, estabelecimentoBase.IdEstabelecimento),
                EStatusDocumentacao = GetCodeStatusDocumentacao(x.IdEmpresa, estabelecimentoBase.IdEstabelecimento),
                EStatus = GetCodeStatus(x.IdEmpresa, estabelecimentoBase.IdEstabelecimento),
                HasDocumentosVencendo = HasDocumentacaoVencendo(GetIdCredenciamento(estabelecimentoBase.IdEstabelecimento, x.IdEmpresa) ?? 0)
            }).AsQueryable();

            var ret = string.IsNullOrWhiteSpace(order?.Campo)
                ? retQuery.OrderBy(x => x.IdEmpresa).ToList()
                : retQuery.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}").ToList();

            var filter_ = filters?.FirstOrDefault(x => x.Campo == "Status");

            if (filter_ != null)
            {
                ret = ret.Where(x => x.EStatus != null && (((int) x.EStatus) == (Convert.ToInt32(filter_.Valor)))).ToList();
                filters.Remove(filter_);
            }

            filter_ = filters?.FirstOrDefault(x => x.Campo == "StatusDocumentacao");
            if (filter_ != null)
            {
                ret = ret.Where(x => (x.EStatusDocumentacao != null && ((int) x.EStatusDocumentacao == (Convert.ToInt32(filter_.Valor))))).ToList();
                filters.Remove(filter_);
            }

            return new
            {
                totalItems = ret.Count(),
                items = ret.Skip((page - 1) * take).Take(take)
                    .ToList()
            };
        }

        public int? GetIdCredenciamento(int idEstabelecimento, int? idEmpresa = null)
        {
            var query = _repository.Find(x => x.IdEstabelecimentoBase == idEstabelecimento);

            if (idEmpresa != null)
                query = query.Where(x => x.IdEmpresa == idEmpresa);

            return query.Select(x => x.IdCredenciamento).FirstOrDefault();
        }

        public object GetStatus(int idEmpresa, int idEstabelecimento)
        {
            object retorno;

            var credenciamento = _repository.FirstOrDefault(x => x.IdEmpresa == idEmpresa && x.IdEstabelecimentoBase == idEstabelecimento);

            if (credenciamento == null)
            {
                //Retorna um code alto para que o usuario
                retorno = new
                {
                    Status = "Não credenciado",
                    Code = new int?()
                };
                return retorno;
            }

            retorno = new
            {
                Status = EnumHelpers.GetDescription(credenciamento.Status),
                Code = credenciamento.Status
            };

            return retorno;
        }

        public object GetStatusDocumentacao(int idEmpresa, int idEstabelecimento)
        {
            object retorno;

            var credenciamento = _repository.FirstOrDefault(x => x.IdEmpresa == idEmpresa && x.IdEstabelecimentoBase == idEstabelecimento);

            if (credenciamento == null)
            {
                //Retorna um code alto para que o usuario
                retorno = new
                {
                    StatusDocumentacao = "Não credenciado",
                    Code = new int?()
                };
                return retorno;
            }

            retorno = new
            {
                StatusDocumentacao = EnumHelpers.GetDescription(credenciamento.StatusDocumentacao),
                Code = credenciamento.StatusDocumentacao
            };

            return retorno;
        }

        public EStatusDocumentacaoCredenciamento? GetCodeStatusDocumentacao(int idEmpresa, int idEstabelecimento)
        {
            var credenciamento = _repository.FirstOrDefault(x => x.IdEmpresa == idEmpresa && x.IdEstabelecimentoBase == idEstabelecimento);

            return credenciamento?.StatusDocumentacao;
        }

        public EStatusCredenciamento? GetCodeStatus(int idEmpresa, int idEstabelecimento)
        {
            var credenciamento = _repository.FirstOrDefault(x => x.IdEmpresa == idEmpresa && x.IdEstabelecimentoBase == idEstabelecimento);

            return credenciamento?.Status;
        }

        public EStatusCredenciamento? GetCodeStatusBase(int idEmpresa, int idEstabelecimento)
        {
            var credenciamento = _repository.FirstOrDefault(x =>
                x.IdEmpresa == idEmpresa && x.IdEstabelecimento == idEstabelecimento && x.Status != EStatusCredenciamento.Aprovado);

            return credenciamento?.Status;
        }

        public object ConsultarGridAprovados(int idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var credenciamentos = _repository
                .GetAll()
                .Where(x => x.IdEstabelecimento == idEstabelecimento && x.Status == EStatusCredenciamento.Aprovado)
                .Include(x => x.Empresa)
                .Include(c => c.Estabelecimento);


            credenciamentos = string.IsNullOrWhiteSpace(order?.Campo)
                ? credenciamentos.OrderBy(x => x.Empresa.RazaoSocial)
                : credenciamentos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            credenciamentos = credenciamentos.AplicarFiltrosDinamicos<Credenciamento>(filters);

            return new
            {
                totalItems = credenciamentos.Count(),
                items = credenciamentos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdCredenciamento,
                        Empresa = x.Empresa.RazaoSocial,
                        x.Empresa.Telefone,
                        x.Empresa.Email,
                        x.IdEmpresa,
                        CodeStatus = x.Status,
                        Status = EnumHelpers.GetDescription(x.Status)
                    })
            };
        }


        public CredenciamentoMotivo AddMotivoRejeicao(int idCredenciamento_, string message_)
        {
            CredenciamentoMotivo credenciamentoMotivo_ = new CredenciamentoMotivo()
            {
                IdCredenciamento = idCredenciamento_,
                Motivo = message_,
                DataRejeicao = DateTime.Now
            };

            _repositoryCredenciamentoMotivo.Add(credenciamentoMotivo_);

            return credenciamentoMotivo_;
        }

        public List<CredenciamentoMotivo> GetAllMotivos(int idCredencimento_)
        {
            return _repositoryCredenciamentoMotivo.Where(x => x.IdCredenciamento == idCredencimento_).ToList();
        }

        public bool AtualizarStatusDocumentacao(int idCredenciamento_, EStatusDocumentacaoCredenciamento status_, int administradoraPlataforma,
            string motivo_ = null)
        {
            try
            {
                //TODO: fazer um método no repositório apenas para atualizar statusDocumentação dando um Attach pra não precisar mais um select
                var credenciamento_ = _repository.FirstOrDefault(x => x.IdCredenciamento == idCredenciamento_);

                if (credenciamento_ != null)
                {
                    credenciamento_.StatusDocumentacao = status_;
                    credenciamento_.DataAtualizacao = DateTime.Now;
                }

                _repository.Update(credenciamento_);

                if (status_ == EStatusDocumentacaoCredenciamento.Irregular)
                {
                    if (!string.IsNullOrEmpty(motivo_))
                        AddMotivoRejeicao(idCredenciamento_, motivo_);

                    credenciamento_ = _repository
                        .Include(x => x.EstabelecimentoBase)
                        .Include(x => x.Empresa)
                        .First(x => x.IdCredenciamento == idCredenciamento_);

                    var nomeAplicativo = _layoutService.GetPorEmpresa(credenciamento_.IdEmpresa)?.NomeAplicativo ?? ConstantesUtils.GetNomeAdministradoraPlataforma;
                    EnviarEmailDocumentacaoRejeitada(credenciamento_.EstabelecimentoBase.Descricao, credenciamento_.EstabelecimentoBase.Email,
                        credenciamento_.Empresa.NomeFantasia, administradoraPlataforma, credenciamento_.Empresa?.Logo, motivo_, nomeAplicativo);
                }

                return true;
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return false;
            }
        }

        public object ConsultarAnexosCredenciamento(int idEstabelecimento)
        {
            var retorno = new List<object>();
            var credenciamentoAnexo = _repositoryCredenciamentoAnexo.Find(x => x.Credenciamento.IdEstabelecimento == idEstabelecimento)
                .Include(x => x.Credenciamento);

            var dataMediaServerService = _dataMediaServerService;
            foreach (var item in credenciamentoAnexo)
            {
                var media = dataMediaServerService.GetMedia(item.Token);

                retorno.Add(new
                {
                    item.IdCredenciamentoAnexo,
                    item.IdDocumento,
                    item.Descricao,
                    DataValidade = item.DataValidade?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DescricaoDocumento = item.Documento?.Descricao ?? item.Descricao,
                    Data = media != null ? (media.Data.Contains(',') ? media.Data.Split(',')[1] : media.Data) : null,
                    Formato = media?.FileName ?? string.Empty,
                    media?.Type,
                    BlobType = media != null ? EnumHelpers.GetDescription(media.Type) : string.Empty,
                    FileName = media?.FileName ?? string.Empty,
                    MimeType = media?.MimeType ?? string.Empty
                });
            }

            return retorno;
        }

        public List<Credenciamento> GetCredenciamentosAprovadosParaEstabBase(int idEstabelecimentoBase)
        {
            return _repository
                .GetAll()
                .Where(x => x.IdEstabelecimentoBase == idEstabelecimentoBase && x.Status == EStatusCredenciamento.Aprovado &&
                            x.Estabelecimento.EstabelecimentoProdutos.Any(a => !a.Contrato))
                .Include(x => x.Empresa)
                .Include(c => c.Estabelecimento)
                .Include(x => x.Estabelecimento.EstabelecimentoProdutos)
                .ToList();
        }

        public List<Credenciamento> GetCredsAprovadosPorIdProdutoBase(int idProdutoBase)
        {
            return _repository
                .GetAll()
                .Where(x => x.Status == EStatusCredenciamento.Aprovado &&
                            x.Estabelecimento.EstabelecimentoProdutos.Any(a => a.Contrato == false && a.IdProdutoBase == idProdutoBase))
                .Include(x => x.Empresa)
                .Include(c => c.Estabelecimento)
                .Include(x => x.Estabelecimento.EstabelecimentoProdutos)
                .ToList();
        }

        public ValidationResult EnviarEmailAprovacaoCredenciamento(string nomeEstab, string emailDestinatario, string nomeEmpresa, byte[] logoEmpresa,
            string nomeAplicativo, int administradoraPlataforma)
        {
            var validacao = new ValidationResult();
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario))
                    return validacao.Add("endereço de e-mail de destinatário não informado");

                nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Aprovação de Credenciamento - {nomeAplicativo}"};
                var caminhoLogo = _parametrosAdministradoraPlataformaService.GetCaminhoLogo(administradoraPlataforma);

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\triagem-aprovada.html"))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource($@"{caminhoLogo}" /*caminhoAplicacao + @"\Content\Image\logo-ats-login.png"*/)
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource($@"{caminhoLogo}" /*caminhoAplicacao + @"\Content\Image\logo-ats-login.png"*/)
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var fileName = @"\Content\Image\ats-tudo-certo-extratta.png";
                    var bannerAprovacao = new LinkedResource(caminhoAplicacao + fileName)
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };
                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerAprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", nomeEstab);
                    html = html.Replace("{EMPRESA}", nomeEmpresa);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerAprovacao);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logoAts);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = new List<string> {emailDestinatario};
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                validacao.Add(_emailService.EnviarEmail(emailModel));
                return validacao;
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        public ValidationResult EnviarEmailCredenciamentoCadastroUsuario(string nomeEstab, string emailDestinatario, string nomeEmpresa,
            byte[] logoEmpresa, string nomeAplicativo, string chave, int idEstab, DateTime? dataValidadeChave, string linkAtsWebNovo,
            int administradoraPlataforma)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario))
                    return new ValidationResult();

                nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Finalize seu cadastro de estabelecimento - {nomeAplicativo}"};
                var caminhoLogo = _parametrosAdministradoraPlataformaService.GetCaminhoLogo(administradoraPlataforma);

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\credenciamento-cadastro-usuario.html"))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource($@"{caminhoLogo}")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource($@"{caminhoLogo}")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };


                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    if (!File.Exists(caminhoAplicacao + @"\Content\Image\header-falta-pouc-extratta.png"))
                        _logger.Error($@"Está faltando o arquivo \Content\Image\header-falta-pouc-extratta.png!!!");

                    var fileName = @"\Content\Image\header-falta-pouc-extratta.png";
                    var bannerFaltaPouco = new LinkedResource(caminhoAplicacao + fileName)
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerFaltaPouco.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", nomeEstab);
                    html = html.Replace("{EMPRESA}", nomeEmpresa);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);
                    html = html.Replace("{chave}", chave);
                    html = html.Replace("{validoAte}", dataValidadeChave?.ToString("dd/MM/yyyy HH:mm:ss"));
                    html = html.Replace("{linkCadastro}", $"{linkAtsWebNovo}/usuario/novo-estabelecimento/{chave}/{idEstab}");

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);


                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(bannerFaltaPouco);
                    view.LinkedResources.Add(logoAts);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = emailDestinatario?.Split(';').ToList();
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);
                return new ValidationResult();
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        /// <summary>
        /// Envia E-mail para reprovação ou descredenciamento do estabelecimento
        /// </summary>
        /// <param name="nomeEstab"></param>
        /// <param name="emailDestinatario"></param>
        /// <param name="nomeEmpresa"></param>
        /// <param name="reprovacao">Determina se o e-mail será de reprovação ou descredenciamento</param>
        /// <param name="logoEmpresa"></param>
        public ValidationResult EnviarEmailReprovacaoCredenciamento(string nomeEstab, string emailDestinatario, string nomeEmpresa, bool reprovacao,
            byte[] logoEmpresa, int administradoraPlataforma, string motivo = null, string detalhamento = null, string nomeAplicativo = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario))
                    return new ValidationResult();

                nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Retorno Credenciamento - {nomeAplicativo}"};
                var caminhoLogo = _parametrosAdministradoraPlataformaService.GetCaminhoLogo(administradoraPlataforma);

                using (var ms = new StreamReader(caminhoAplicacao +
                                                 (reprovacao
                                                     ? @"\Content\Email\triagem-reprovado.html"
                                                     : @"\Content\Email\triagem-descredenciamento.html")))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource($@"{caminhoLogo}")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource($@"{caminhoLogo}")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var fileName = administradoraPlataforma == 1 ? @"\Content\Image\ats-reprovado.png" : @"\Content\Image\ats-reprovado-extratta.png";
                    var bannerReprovacao = new LinkedResource(caminhoAplicacao + fileName)
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerReprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", nomeEstab);
                    html = html.Replace("{EMPRESA}", nomeEmpresa);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{MOTIVO}", motivo);
                    html = html.Replace("{DETALHAMENTO}", detalhamento);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerReprovacao);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logoAts);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = new List<string> {emailDestinatario};
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);
                return new ValidationResult();
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        public ValidationResult EnviarEmailSolicitacaoCredenciamento(string nomeEmp, string nomeEstab, string emailDestinatario, byte[] logoEmpresa,
            string nomeAplicativo, int administradoraPlataforma)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario))
                    return new ValidationResult();

                nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Alerta de Credenciamento - {nomeAplicativo}"};
                var caminhoLogo = _parametrosService.GetCaminhoLogo(administradoraPlataforma);

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\solicitacao-credenciamento.html"))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource($@"{caminhoLogo}")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logo = new LinkedResource($@"{caminhoLogo}")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var fileName = @"\Content\Image\ats-reprovado-extratta.png";
                    var bannerReprovacao = new LinkedResource(caminhoAplicacao + fileName)
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerReprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", nomeEmp);
                    html = html.Replace("{Estabelecimento}", nomeEstab);
                    html = html.Replace("{logoATS}", logo.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerReprovacao);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logo);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = new List<string> {emailDestinatario};
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);

                return new ValidationResult();
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        public ValidationResult EnviarEmailDocumentacaoRejeitada(string nomeEstab, string emailDestinatario, string nomeEmpresa,
            int administradoraPlataforma, byte[] logoEmpresa, string motivo = null, string nomeAplicativo = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario))
                    return new ValidationResult();

                nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Retorno Credenciamento - {nomeAplicativo}"};
                var caminhoLogo = _parametrosAdministradoraPlataformaService.GetCaminhoLogo(administradoraPlataforma);

                using (var ms = new StreamReader(caminhoAplicacao + (@"\Content\Email\documentacao-credenciamento-rejeitada.html")))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource($@"{caminhoLogo}")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource($@"{caminhoLogo}")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var fileName = administradoraPlataforma == 1
                        ? @"\Content\Image\ats-reprovado.png"
                        : @"\Content\Image\ats-reprovado\ats-reprovado-extratta.png";
                    var bannerReprovacao = new LinkedResource(caminhoAplicacao + fileName)
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerReprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", nomeEstab);
                    html = html.Replace("{EMPRESA}", nomeEmpresa);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{MOTIVO}", motivo);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerReprovacao);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logoAts);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = new List<string> {emailDestinatario};
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);
                return new ValidationResult();
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        public Credenciamento GetCredenciamentoPorProtocolo(int idProtocolo)
        {
            var protocolo = _protocoloRepository.Get(idProtocolo);

            var credenciamento = _repository
                .Find(x => x.IdEmpresa == protocolo.IdEmpresa && x.IdEstabelecimentoBase == protocolo.IdEstabelecimentoBase)
                .Include(x => x.Estabelecimento)
                .Include(x => x.Estabelecimento.EstabelecimentoAssociacoesAssociacao)
                .Include(x => x.Estabelecimento.EstabelecimentoAssociacoesAssociacao.Select(y => y.Associacao))
                .FirstOrDefault();

            return credenciamento;
        }

        public Credenciamento Get(int idEmpresa, int idEstabelecimento)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa && x.IdEstabelecimento == idEstabelecimento).FirstOrDefault();
        }

        public List<Credenciamento> GetByIdEstabelecimentoBase(int? idEstabelecimentoBase)
        {
            var credenciamentos = _repository
                .Find(o => o.IdEstabelecimentoBase == idEstabelecimentoBase)
                .Include(o => o.EstabelecimentoBase)
                .Include(o => o.Empresa);

            return credenciamentos.ToList();
        }

        public IQueryable<Credenciamento> GetQueryByIdEstabelecimentoBase(int idEstabelecimentoBase, int? idEmpresa)
        {
            var credenciamentos = _repository
                .Find(o => o.IdEstabelecimentoBase == idEstabelecimentoBase);

            if (idEmpresa.HasValue)
                credenciamentos = credenciamentos.Where(c => c.IdEmpresa == idEmpresa);

            return credenciamentos;
        }

        public List<int> GetIdsEmpresaSolicitadoCredenciamento(int idEstabelecimentoBase)
        {
            return _repository
                .Find(o => o.IdEstabelecimentoBase == idEstabelecimentoBase)
                .Select(o => o.IdEmpresa)
                .Distinct()
                .ToList();
        }

        public bool VerificarEstabelecimentoBaseAssociado(int idEstabelecimentoBase)
        {
            return _repository.Find(x => x.IdEstabelecimentoBase == idEstabelecimentoBase).Any(x => x.Estabelecimento.Associacao);
        }

        public bool GetAllCredenciamentosIrregulares(List<int> idsEstabelecimentos_)
        {
            return _repository.Any(x => idsEstabelecimentos_.Contains(x.IdEstabelecimentoBase ?? 0)
                                        && x.StatusDocumentacao == EStatusDocumentacaoCredenciamento.Irregular);
        }

        public byte[] GerarRelatorioGridCredenciamento(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,
            string tipoArquivo, string logo)
        {
            var credenciamentos = ConsultaGrid(idEmpresa, int.MaxValue, 1, orderFilters, filters);

            return new RelatorioCredenciamento().GetReport(credenciamentos.items, tipoArquivo, logo);
        }

        public List<Credenciamento> GetCredenciadosByIdEstabelecimentoBase(int idEstabelecimentoBase)
        {
            var credenciamenteos = _repository.Where(o =>
                o.Status == EStatusCredenciamento.Aprovado && o.IdEstabelecimentoBase == idEstabelecimentoBase).ToList();

            return credenciamenteos;
        }

        public void EnviarCredenciamentoWebHook(Credenciamento credenciamento)
        {
            try
            {
                var empresa = _empresaRepository.Get(credenciamento.IdEmpresa);

                if (empresa == null)
                    return;

                var token = WebConfigurationManager.AppSettings["MS_TOKEN_ADMINISTRADORA"];
                
                var webhook = _webhookService.GetByIdRegistro(credenciamento.IdEmpresa, WebhookTipoEvento.IntegracaoEstabelecimento);

                if (webhook != null)
                {
                    if (string.IsNullOrWhiteSpace(webhook.Endpoint))
                        return;

                    var retorno = OrganizarObjetoEstabelecimentoCredenciadoWebHook(credenciamento);

                    webhook.EnviarNotificacao(
                        _webhookActionDependencies,
                        "Aprovação de credenciamento de estabelecimento: " + credenciamento.IdCredenciamento,
                        retorno,
                        token, credenciamento.IdEmpresa);
                }
            }
            catch (Exception e)
            {
                _logger.Error(e, $"Erro ao notificar web hook de aprovação de credenciamento: {credenciamento.IdCredenciamento}");
            }
        }

        public EstabelecimentoCredenciadoWebHook OrganizarObjetoEstabelecimentoCredenciadoWebHook(Credenciamento credenciamento)
        {
            var produtos = new List<ProdutosEstabelecimentoWebHook>();

            if (credenciamento?.EstabelecimentoBase?.EstabelecimentoBaseProdutos != null)
                foreach (var produto in credenciamento.EstabelecimentoBase.EstabelecimentoBaseProdutos)
                    produtos.Add(new ProdutosEstabelecimentoWebHook
                    {
                        Id = produto.IdProduto,
                        Descricao = produto.Descricao,
                        PrecoPromocional = produto.PrecoPromocional,
                        PrecoUnitario = produto.PrecoUnitario,
                        UnidadeMedida = produto.UnidadeMedida
                    });

            var estabekecumentoCredenciadoWebHook = new EstabelecimentoCredenciadoWebHook
            {
                CnpjEstabelecimento = credenciamento?.EstabelecimentoBase?.CNPJEstabelecimento,
                RazaoSocial = credenciamento?.EstabelecimentoBase?.RazaoSocial,
                NomeFantasia = credenciamento?.EstabelecimentoBase?.Descricao,
                Pais = credenciamento?.EstabelecimentoBase?.Pais?.Nome,
                Estado = credenciamento?.EstabelecimentoBase?.Estado?.Sigla,
                Cidade = credenciamento?.EstabelecimentoBase?.Cidade?.Nome,
                CidadeIbge = credenciamento?.EstabelecimentoBase?.Cidade?.IBGE,
                Bairro = credenciamento?.EstabelecimentoBase?.Bairro,
                Logradouro = credenciamento?.EstabelecimentoBase?.Logradouro,
                Numero = credenciamento?.EstabelecimentoBase?.Numero,
                Cep = credenciamento?.EstabelecimentoBase?.CEP,
                Email = credenciamento?.EstabelecimentoBase?.Email,
                Telefone = credenciamento?.EstabelecimentoBase?.Telefone,
                Latitude = credenciamento?.EstabelecimentoBase?.Latitude,
                Longitude = credenciamento?.EstabelecimentoBase?.Longitude,
                Complemento = credenciamento?.EstabelecimentoBase?.Complemento,
                IdTipoEstabelecimento = credenciamento?.EstabelecimentoBase?.TipoEstabelecimento?.IdTipoEstabelecimento,
                TipoEstabelecimento = credenciamento?.EstabelecimentoBase?.TipoEstabelecimento?.Descricao,
                EmailProtocolo = credenciamento?.EstabelecimentoBase?.EmailProtocolo,
                Associacao = credenciamento?.EstabelecimentoBase?.Associacao ?? false,
                Produtos = produtos,
            };

            return estabekecumentoCredenciadoWebHook;
        }

        #region Uso de EstabelecimentoBaseDocumentoService

        public void AddCredenciamentoAnexo(int idEstabelecimento, int? idDocumento, string token, DateTime? dataValidade, string descricao)
        {
            try
            {
                var idCredenciamento = GetIdCredenciamento(idEstabelecimento);

                var credenciamentoAnexo = new CredenciamentoAnexo();

                credenciamentoAnexo.IdCredenciamento = idCredenciamento;
                credenciamentoAnexo.IdDocumento = idDocumento;
                credenciamentoAnexo.Token = token;
                credenciamentoAnexo.DataValidade = dataValidade;
                credenciamentoAnexo.Descricao = descricao;

                _repositoryCredenciamentoAnexo.Add(credenciamentoAnexo);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                throw;
            }
        }

        public void UpdateCredenciamentoAnexo(int idEstabelecimento, int? idDocumento, string token, DateTime? dataValidade, string descricao)
        {
            try
            {
                var idCredenciamento = GetIdCredenciamento(idEstabelecimento);

                var query = _repositoryCredenciamentoAnexo.Find(x => x.IdCredenciamento == idCredenciamento && x.IdDocumento == idDocumento);

                if (query.Any())
                {
                    var credenciamentoAnexoFromDb = query.First();

                    credenciamentoAnexoFromDb.Token = token;
                    credenciamentoAnexoFromDb.DataValidade = dataValidade;
                    credenciamentoAnexoFromDb.Descricao = descricao;

                    _repositoryCredenciamentoAnexo.Update(credenciamentoAnexoFromDb);
                }
                else
                    AddCredenciamentoAnexo(idEstabelecimento, idDocumento, token, dataValidade, descricao);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                throw;
            }
        }

        public ValidationResult AtualizarStatusDocumentacaoPorEstabelecimento(int idEstabelecimento, int idEmpresa,
            EStatusDocumentacaoCredenciamento statusDocumentacao, int administradoraPlataforma)
        {
            try
            {
                ValidationResult result = new ValidationResult();

                var idCredenciamento = GetIdCredenciamento(idEstabelecimento, idEmpresa);

                if (!idCredenciamento.HasValue || idCredenciamento == 0)
                    return result;

                var credenciamento = _repository
                    .Include(x => x.Estabelecimento)
                    .Include(x => x.Empresa)
                    .First(x => x.IdCredenciamento == idCredenciamento.Value);

                if (!AtualizarStatusDocumentacao(idCredenciamento.Value, statusDocumentacao, administradoraPlataforma))
                    return result.Add("Ocorreu um erro ao atualizar o status da documentação");

                //Se só existe o EstabelecimentoBase não deve enviar o e-mail de documento anexado pq a Sotran já recebe o do credenciamento
                if (credenciamento.Estabelecimento != null)
                {
                    var nomeAplicativo = _layoutService.GetPorEmpresa(credenciamento.IdEmpresa)?.NomeAplicativo ?? ConstantesUtils.GetNomeAdministradoraPlataforma;
                    var resultEmail = EnviarEmailDocumentoAnexadoParaAnalise(credenciamento.Empresa.NomeFantasia,
                        credenciamento.Estabelecimento.Descricao, credenciamento.Empresa.EmailCartaFrete, credenciamento.Empresa?.Logo,
                        nomeAplicativo);

                    if (resultEmail.Alerts.Any())
                        result.Add(resultEmail.ToString(), EFaultType.Alert);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
                throw;
            }
        }

        public ValidationResult EnviarEmailDocumentoAnexadoParaAnalise(string nomeEmp, string nomeEstab, string emailDestinatario, byte[] logoEmpresa,
            string nomeAplicativo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario)) return new ValidationResult();

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Novo Documento de Credenciamento Para Análise - {nomeAplicativo}"};

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\documento-anexado-para-analise.html"))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var bannerReprovacao = new LinkedResource(caminhoAplicacao + @"\Content\Image\ats-atualizacao.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };
                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerReprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{Usuario}", nomeEmp);
                    html = html.Replace("{Estabelecimento}", nomeEstab);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerReprovacao);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logoAts);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = new List<string> {emailDestinatario};
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmail(emailModel);

                return new ValidationResult();
            }
            catch (Exception)
            {
                return new ValidationResult().Add($"Não foi possível enviar o e-mail. Verifique as configurações de e-mail", EFaultType.Alert);
            }
        }

        #endregion

        public bool EstabelecimentoCredenciado(int idEstabelecimento, int idEmpresa)
        {
            return _repository.EstabelecimentoCredenciado(idEstabelecimento, idEmpresa);
        }
    }
}
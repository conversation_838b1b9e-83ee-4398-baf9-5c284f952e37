 using ATS.Domain.Entities.Common;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class EstabelecimentoBase : EstabelecimentoBaseClass
    {
        public string RazaoSocial { get; set; }
        public bool Associacao { get; set; } = false;
        public bool PertenceRedeJsl { get; set; }
        
        public int? IdFilial { get; set; }
        
        public virtual TipoEstabelecimento TipoEstabelecimento { get; set; }
        public virtual Pais Pais { get; set; }
        public virtual Estado Estado { get; set; }
        public virtual Cidade Cidade { get; set; }
        
        /// <summary>
        /// Trazer todas assossiações do estabelecimento
        /// </summary>
        public virtual ICollection<EstabelecimentoBaseAssociacao> AssociacoesBaseEstabelecimento { get; set; }
        public virtual ICollection<EstabelecimentoBaseProduto> EstabelecimentoBaseProdutos { get; set; }
        public virtual ICollection<Credenciamento> Credenciamentos { get; set; }
        public virtual ICollection<UsuarioEstabelecimento> UsuarioEstabelecimentos { get; set; }
        public virtual ICollection<ViagemEvento> ViagemEventos { get; set; }
        public virtual ICollection<Protocolo> Protocolos { get; set; }
        public virtual ICollection<Estabelecimento> Estabelecimento { get; set; }
        public virtual ICollection<EstabelecimentoBaseContaBancaria> EstabelecimentoBaseContasBancarias { get; set; }
        public virtual ICollection<EstabelecimentoBaseDocumento> Documentos { get; set; } 
        public virtual Filial Filial { get; set; }
    }
}

﻿using System.ComponentModel;
using System.Runtime.Serialization;
using ATS.Domain.Entities.Base;

namespace ATS.Domain.Entities
{
    public enum ETipoLimiteTransacaoPortador
    {
        [EnumMember, Description("Valor limite unitário para transferência TED")]
        UnitarioTransferenciaTED = 1,

        [EnumMember, Description("Valor limite diário para transferência TED")]
        DiarioTransferenciaTED = 2,

        [EnumMember, Description("Valor limite unitário para transferência entre Cartões")]
        UnitarioTransferenciaCartoes = 3,

        [EnumMember, Description("Valor limite diário para transferência entre Cartões")]
        DiarioTransferenciaCartoes = 4,
    }

    public class LimiteTransacaoPortador : Entidade
    {
        public int IdDespesaUsuario { get; set; }
        public int? IdUsuarioCriacao { get; set; }
        public int? IdUsuarioAtualizacao { get; set; }
        public string Documento { get; set; }
        public short Tipo { get; set; }
        public decimal Valor { get; set; }

        #region MyRegion

        public virtual Usuario UsuarioCadastroDB { get; set; }
        public virtual Usuario UsuarioAtualizacaoDB { get; set; }

        #endregion
    }
}
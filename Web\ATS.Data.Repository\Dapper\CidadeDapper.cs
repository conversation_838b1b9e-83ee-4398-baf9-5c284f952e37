﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using System.Linq;
using NLog;

namespace ATS.Data.Repository.Dapper
{
    public class CidadeDapper : DapperFactory<Cidade>, ICidadeDapper
    {
        public Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude)
        {
            var sql =
                "SELECT TOP 1 CIDADE.* FROM CIDADE WHERE LATITUDE IS NOT NULL AND LONGITUDE IS NOT NULL AND ATIVO = 1 ORDER BY ((latitude - (@lat)) * (latitude - (@lat)) +  (longitude - (@lon)) * (longitude - (@lon)))";

            var pars = new
            {
                lat = latitude,
                lon = longitude
            };

            return this.RunSelect(sql, pars).FirstOrDefault();
        }

        public List<Cidade> GetCidadesPorNome(string nome)
        {
            var nomeParaConsulta = nome.ToUpper();

            var sql = $@"SELECT C.idcidade, 
		                        C.ibge, 
		                        (select UPPER(C.nome) collate SQL_Latin1_General_Cp1251_CS_AS) as nome,
	                            C.idestado FROM CIDADE C WHERE nome Like '{nomeParaConsulta}%'";

            var resultado = this.RunSelect(sql).ToList();

            return resultado;
        }

        public IQueryable<Cidade> GetCidades(string siglaEstado, DateTime dataAtualizacao)
        {
            var clausulaWhere = string.Empty;

            LogManager.GetCurrentClassLogger().Info($"PASSO 1 - Entrou no GetCidades: {dataAtualizacao}");
            
            var data = dataAtualizacao.ToString("yyyy-MM-dd HH:mm:ss");
            
            LogManager.GetCurrentClassLogger().Info($"PASSO 2 - Converteu no GetCidades: {data}");

            if (!string.IsNullOrEmpty(siglaEstado))
                clausulaWhere = $@" AND E.SIGLA = '{siglaEstado}'";
            
            var sql = $@"SELECT C.*
                        FROM CIDADE C
                               INNER JOIN ESTADO E on E.IDESTADO = C.IDESTADO
                        WHERE C.DATAALTERACAO > '{data}' {clausulaWhere}";
            
            LogManager.GetCurrentClassLogger().Info($"PASSO 3 - Montou SQL no GetCidades: {sql}");
            
            return this.RunSelect(sql).AsQueryable();
        }
    }

    //public static class CidadeStatic
    //{
    //    public static List<Cidade> Cidades { get; set; }
    //    public static Cidade GetCidadeMaisProxima(decimal latitude, decimal longitude)
    //    {
    //        if(Cidades == null || !Cidades.Any())
    //            Cidades = this.RunSelect("SELECT CIDADE.* FROM CIDADE").ToList();

    //        if (Cidades == null || !Cidades.Any()) return null;

    //        return Cidades
    //            .OrderBy(x => (x.Latitude - latitude) * (x.Latitude - latitude) + (x.Longitude - longitude) * (x.Longitude - longitude))
    //            .FirstOrDefault();
    //    }
    //}
}

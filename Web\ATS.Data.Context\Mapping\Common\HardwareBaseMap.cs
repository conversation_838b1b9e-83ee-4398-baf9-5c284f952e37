﻿using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities.Common;

namespace ATS.Data.Context.Mapping.Common
{
    public class HardwareBaseMap<TEntity> : EntityTypeConfiguration<TEntity> where TEntity : HardwareBase
    {
        public HardwareBaseMap()
        {
            Property(t => t.IMEI)
                .IsRequired()
                .HasMaxLength(20);

            Property(t => t.Velocidade)
                .HasPrecision(7, 3)
                .IsOptional();

            Property(t => t.Temperatura)
               .HasPrecision(11, 6)
                .IsOptional();

            Property(t => t.TensaoBateria)
                .HasPrecision(7, 3)
                .IsOptional();

            Property(t => t.Latitude)
                .IsOptional();

            Property(t => t.Longitude)
                .IsOptional();

            Property(t => t.Versao)
                .HasMaxLength(15)
                .IsOptional();

            Property(t => t.Alarme)
                .IsOptional();

            Property(t => t.DataHoraIntegracao)
                .IsRequired();

            Property(t => t.DataHoraSatelite)
                .IsRequired();
        }
    }
}

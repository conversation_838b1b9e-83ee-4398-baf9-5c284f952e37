﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.PagamentoFrete.Cheque.ImpressaoCheque
{
    public class RelatorioImpressaoCheque
    {
        public byte[] GetReport(List<RelatorioImpressaoChequeDataType> listaDados)
        {
            var bytes = new Base.Reports().GetReport(listaDados, new Tuple<string, string, bool>[0], true, "DtsCheque",
                "ATS.CrossCutting.Reports.PagamentoFrete.Cheque.ImpressaoCheque.RelatorioImpressaoCheque.rdlc", "pdf");
            
            return bytes;
        }
    }
}

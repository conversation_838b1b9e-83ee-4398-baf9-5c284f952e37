namespace ATS.Domain.Enum
{
    public enum EBloqueioFinanceiroTipo
    {
        parcelaAdiantamento = 1,
        parcelaSaldo = 2,
        parcelaEstadia = 3,
        parcelaRPA = 4,
        parcelaTarifaANTT = 5,
        parcelaAbastecimento = 6,
        pagamentoCargaAvulsa = 7,
        vincularCartao = 8,
        consultarExtratoPortador = 9,
        baixarOuCancelarViagem = 10,
        realizarEstornoCargaAvulsa = 11,
        permiTirCancelamentoParcelaFrete = 12,
        realizarResgatePortadorDespesasViagem = 13,
        desvincularCartao = 14,
        exigeAutenticação2FA = 15,
        permiteVisualizarSaldoFreteTopbar = 16,
        permiteVisualizarSaldoPixTopbar = 17,
        permiteAprovarDesaprovarCargaAvulsa = 18
    }
 
    public enum EBloqueioGestorFinanceiro
    {
        Pendente = 0,
        Liberado = 1,
        Bloqueado = 2
    }
}
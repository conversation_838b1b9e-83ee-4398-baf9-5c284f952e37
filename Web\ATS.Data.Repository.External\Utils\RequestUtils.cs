﻿using ATS.Data.Repository.External.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Data.Repository.External.Utils
{
    public class RequestUtils
    {
        /// <summary>
        /// Realiza uma requisição ao webservice do ATS usando o método GET
        /// </summary>
        /// <typeparam name="T">Tipo de retorno</typeparam>
        /// <param name="linkWs">Url da requisição</param>
        /// <param name="parametros">Parâmetro da requisição</param>
        /// <returns>Instância de T com os dados de retorno da requisição</returns>
        public static T DoGet<T>(string linkWs, object parametros)
        {
            var paramsFormatados = SerializarParametros(parametros);

            var restClient = new RestClient(linkWs, HttpVerb.GET);

            var response = restClient.MakeRequest(paramsFormatados);
            var dadosResposta = string.IsNullOrWhiteSpace(response)
                    ? default(T)
                    : JsonConvert.DeserializeObject<T>(response);

            return dadosResposta;
        }

        /// <summary>
        /// Realiza uma requisição ao webservice do ATS usando o método POST
        /// </summary>
        /// <typeparam name="T">Tipo de retorno</typeparam>
        /// <param name="linkWs">Url da requisição</param>
        /// <param name="parametros">Parâmetro da requisição</param>
        /// <returns>Instância de T com os dados de retorno da requisição</returns>
        public static T DoPost<T>(string linkWs, object parametros)
        {
            var lRestClient = new RestClient(linkWs, HttpVerb.POST, JsonConvert.SerializeObject(parametros), Encoding.UTF8);

            var response = lRestClient.MakeRequest();
            var dadosResposta = string.IsNullOrWhiteSpace(response)
                    ? default(T)
                    : JsonConvert.DeserializeObject<T>(response);

            return dadosResposta;
        }

        public static string SerializarParametros(object parametros)
        {
            var @params = "";

            var paramsSerializados = JsonConvert.SerializeObject(parametros);
            //var paramsUsados = JsonConvert.DeserializeObject<Dictionary<string, string>>(paramsSerializados);

            List<PropertyInfo> properties = parametros
                .GetType()
                .GetProperties()
                .ToList();

            foreach (var property in properties)
            {
//                if (!paramsUsados.ContainsKey(property.Name))
//                    continue;

                var query = string.Empty;
                if (!string.IsNullOrEmpty(@params))
                    @params += "/";

                var isDateTime = property.PropertyType == typeof(DateTime);
                var isDateTimeNullable = property.PropertyType == typeof(DateTime?);
                var isArrayIntNullable = property.PropertyType == typeof(int?[]);

                if (isDateTime)
                    query = property.Name + "=" + ((DateTime)property.GetValue(parametros, null)).ToUniversalTime().ToString("s");
                else if (isDateTimeNullable)
                {
                    var hasValue = ((DateTime?)property.GetValue(parametros, null)).HasValue;
                    if (hasValue)
                        query = property.Name + "=" + ((DateTime?)property.GetValue(parametros, null)).Value.ToUniversalTime().ToString("s");
                }
                else if (isArrayIntNullable)
                {
                    foreach (var parametro in ((int?[])property.GetValue(parametros, null)))
                    {
                        query += $"{property.Name}={parametro}&";
                    }
                }
                else
                    query = property.Name + "=" + property.GetValue(parametros, null);

                @params += query;
            }

            return @params;
        }
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using System.Linq;

namespace ATS.Domain.Service
{
    public class VeiculoCombustivelService : ServiceBase, IVeiculoCombustivelService
    {
        private readonly IVeiculoCombustivelRepository _veiculoCombustivelRepository;

        public VeiculoCombustivelService(IVeiculoCombustivelRepository veiculoCombustivelRepository)
        {
            _veiculoCombustivelRepository = veiculoCombustivelRepository;
        }

        /// <summary>
        /// Retorna a lista de combustivel de um determinado veículo
        /// </summary>
        /// <param name="idVeiculo"></param>
        /// <returns></returns>
        public IQueryable<VeiculoTipoCombustivel> GetByIdVeiculoWithAllChilds(int idVeiculo)
        {
            return _veiculoCombustivelRepository.GetByIdVeiculoWithAllChilds(idVeiculo);
        }
    }
}
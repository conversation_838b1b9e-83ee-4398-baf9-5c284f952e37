﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Triggers;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.Domain.Trigger.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Interface.Service;
using Autofac;

namespace ATS.Domain.Trigger
{
    public class ProtocoloTrigger : Trigger<Protocolo>, IProtocoloTrigger
    {
        public ProtocoloTrigger()
        {
            this.RegisterAfterTrigger(EOperationTrigger.Insert, (provider, @new, @old) => provider.Resolve<IProtocoloService>().AfterUpdate(@new, @old), "EnviarProtocoloWebHook");
            this.RegisterAfterTrigger(EOperationTrigger.Update, (provider, @new, @old) => provider.Resolve<IProtocoloService>().AfterUpdate(@new, @old), "EnviarProtocoloWEbHook");
        }
    }
}
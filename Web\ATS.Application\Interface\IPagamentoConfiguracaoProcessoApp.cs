﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Application.Interface
{
    public interface IPagamentoConfiguracaoProcessoApp 
    {
        ValidationResult Add(PagamentoConfiguracaoProcesso PagamentoConfiuracao);
        ValidationResult Update(PagamentoConfiguracaoProcesso PagamentoConfigurcao);
        IEnumerable<PagamentoConfiguracaoProcesso> GetItemsByProcess(int idConfiguracao, EProcessoPgtoFrete processo);
    }
}

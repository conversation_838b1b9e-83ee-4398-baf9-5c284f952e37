﻿using System.Collections.Generic;

namespace ATS.Domain.Service
{
    public class GoogleGeoCodeResponse
    {
        public string Status { get; set; }
        public List<Result> Results { get; set; }
    }

    public class Result
    {
        public Geometry Geometry { get; set; }
    }

    public class Geometry
    {
        public Location Location { get; set; }

    }

    public class Location
    {
        public string Lat { get; set; }
        public string Lng { get; set; }
    }


    public class GoogleRoutesResponse
    {
        public string status { get; set; }
        public routes[] routes { get; set; }

    }

    public class routes
    {
        public legs[] legs { get; set; }
    }

    public class legs
    {
        public duration duration { get; set; }
    }

    public class duration
    {
        public string text { get; set; }
        public int value { get; set; }
    }

}
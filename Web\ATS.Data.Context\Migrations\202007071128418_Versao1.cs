﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Collections.Generic;
    using System.Data.Entity.Infrastructure.Annotations;
    using System.Data.Entity.Migrations;
    
    public partial class Versao1 : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.ATENDIMENTO_PORTADOR",
                c => new
                    {
                        idatendimentoportador = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(),
                        cnpjcpf = c.String(maxLength: 14, unicode: false),
                        observacao = c.String(maxLength: 200, unicode: false),
                        datainicio = c.DateTime(nullable: false),
                        datafinal = c.DateTime(),
                        idusuario = c.Int(),
                        status = c.Int(nullable: false),
                        protocolo = c.String(nullable: false, maxLength: 100, unicode: false),
                        idmotivofinalizacaoatendimento = c.Int(),
                    })
                .PrimaryKey(t => t.idatendimentoportador)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivofinalizacaoatendimento)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idusuario)
                .Index(t => t.idmotivofinalizacaoatendimento);
            
            CreateTable(
                "dbo.ATENDIMENTO_PORTADOR_TRAMITE",
                c => new
                    {
                        idatendimentoportador = c.Int(nullable: false),
                        sequencial = c.Int(nullable: false),
                        datatramite = c.DateTime(nullable: false),
                        identificadorcartao = c.Int(),
                        produtocartao = c.Int(),
                        operacao = c.String(nullable: false, maxLength: 2000, unicode: false),
                        tipo = c.Int(nullable: false),
                        idmotivo = c.Int(),
                        permanente = c.Boolean(),
                    })
                .PrimaryKey(t => new { t.idatendimentoportador, t.sequencial })
                .ForeignKey("dbo.ATENDIMENTO_PORTADOR", t => t.idatendimentoportador)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .Index(t => t.idatendimentoportador)
                .Index(t => new { t.tipo, t.idmotivo }, name: "IX_ATENDIMENTO_PORTADOR_TRAMITE_Tipo_motivo");
            
            CreateTable(
                "dbo.MOTIVO",
                c => new
                    {
                        idmotivo = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        descricao = c.String(maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                        datacadastro = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.idmotivo)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.CREDENCIAMENTO",
                c => new
                    {
                        idcredenciamento = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idestabelecimento = c.Int(),
                        idestabelecimentobase = c.Int(),
                        datasolicitacao = c.DateTime(nullable: false),
                        dataatualizacao = c.DateTime(nullable: false),
                        idmotivo = c.Int(),
                        detalhamento = c.String(maxLength: 100, unicode: false),
                        status = c.Int(nullable: false),
                        modoregistro = c.Int(nullable: false),
                        statusdocumentacao = c.Int(nullable: false),
                        chavecadastrousuario = c.String(maxLength: 100, unicode: false),
                        emailenviado = c.Boolean(nullable: false),
                        datavalidadechave = c.DateTime(),
                    })
                .PrimaryKey(t => t.idcredenciamento)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimento)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento")
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase")
                .Index(t => t.idmotivo, name: "IX_IdMotivo");
            
            CreateTable(
                "dbo.CREDENCIAMENTO_ANEXO",
                c => new
                    {
                        idcredenciamentoanexo = c.Int(nullable: false, identity: true),
                        idcredenciamento = c.Int(),
                        iddocumento = c.Int(),
                        descricao = c.String(maxLength: 100, unicode: false),
                        datavalidade = c.DateTime(),
                        token = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idcredenciamentoanexo)
                .ForeignKey("dbo.CREDENCIAMENTO", t => t.idcredenciamento)
                .ForeignKey("dbo.DOCUMENTO", t => t.iddocumento)
                .Index(t => t.idcredenciamento, name: "IX_IdCredenciamento")
                .Index(t => t.iddocumento, name: "IX_IdDocumento");
            
            CreateTable(
                "dbo.DOCUMENTO",
                c => new
                    {
                        iddocumento = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                        possuirvalidade = c.Boolean(nullable: false),
                        obrigadocoriginal = c.Boolean(nullable: false),
                        diasvalidade = c.Int(),
                    })
                .PrimaryKey(t => t.iddocumento)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.EMPRESA",
                c => new
                    {
                        idempresa = c.Int(nullable: false, identity: true),
                        cnpj = c.String(nullable: false, maxLength: 14, unicode: false),
                        razaosocial = c.String(nullable: false, maxLength: 150, unicode: false),
                        nomefantasia = c.String(nullable: false, maxLength: 100, unicode: false),
                        logo = c.Binary(),
                        emailgeracaooc = c.Binary(),
                        emailcancelamentooc = c.Binary(),
                        emailrelatoriooc = c.Binary(),
                        emailprotocolorejeitado = c.Binary(),
                        emaileventorejeitado = c.Binary(),
                        ativo = c.Boolean(nullable: false),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        obrigarnumerofrota = c.Boolean(nullable: false),
                        tempoportaaberta = c.Int(nullable: false),
                        obrigarvalorterceiro = c.Boolean(nullable: false),
                        tempoexecucaoservico = c.Int(),
                        tempoparada = c.Int(),
                        ultimaexecucao = c.DateTime(),
                        possuimonitoramento = c.Boolean(nullable: false),
                        agrupaprotocolomesmoevento = c.Boolean(nullable: false),
                        diasparabloquearpagto = c.Int(nullable: false),
                        enderecowsgatilho = c.String(maxLength: 100, unicode: false),
                        usuariogatilho = c.String(maxLength: 100, unicode: false),
                        senhagatilho = c.String(maxLength: 100, unicode: false),
                        controlefilaofertacargas = c.Boolean(nullable: false),
                        permitecredenciamento = c.Boolean(nullable: false),
                        atualizaestabelecimento = c.Boolean(nullable: false),
                        intervaloconsultafretesconcorrente = c.Int(nullable: false),
                        habilitarcontroleimeis = c.Boolean(nullable: false),
                        utilizaaplicativopersonalizado = c.Boolean(nullable: false),
                        emailschecklist = c.String(maxLength: 1000, unicode: false),
                        validacaopagfrete = c.Boolean(nullable: false),
                        precadastrocarga = c.Boolean(nullable: false),
                        listaprecarga = c.Boolean(nullable: false),
                        diasantesvencdoc = c.Int(nullable: false),
                        freqvencdoc = c.Int(nullable: false),
                        diasinatividadelote = c.Int(),
                        toleranciahorasinatividade = c.Int(),
                        tempolimitepermanenciacliente = c.Int(),
                        emailsugestoes = c.String(maxLength: 100, unicode: false),
                        tokenmicroservices = c.String(maxLength: 100, unicode: false),
                        enviaemailtempoexcedido = c.Boolean(nullable: false),
                        enviaemailcadastroocorrencia = c.Boolean(nullable: false),
                        tiponotificacaocandidatura = c.Int(),
                        tiponotificacaolotesinativosmesa = c.Int(),
                        mostrarvalorfreteparafrota = c.Boolean(nullable: false),
                        dataatualizacao = c.DateTime(),
                        obrigarcpfreceber = c.Boolean(nullable: false),
                        autenticarcodigobarranf = c.Boolean(nullable: false),
                        utilizacheckinrastreamento = c.Boolean(nullable: false),
                        pagamentofretetoleranciapesochegadamais = c.Double(),
                        pagamentofretetoleranciapesochegadamenos = c.Double(),
                        validachavebaixaevento = c.Boolean(nullable: false),
                        validachavemhbaixaevento = c.Boolean(nullable: false),
                        tempovalidadechave = c.Int(nullable: false),
                        limitekmfretecurto = c.Int(),
                        horaenvemailmotnaoemb = c.String(maxLength: 2, unicode: false),
                        diasbuscamotnaoemb = c.Int(),
                        keygoogle = c.String(maxLength: 100, unicode: false),
                        raiopontoreferencia = c.Decimal(precision: 18, scale: 2),
                        somentepainelgestaoencerranota = c.Boolean(nullable: false),
                        utilizaagendamento = c.Boolean(nullable: false),
                        aprovachecklistautomaticamente = c.Boolean(nullable: false),
                        horasrespostaagendamento = c.Int(),
                        horasrealizavistoria = c.Int(),
                        horacancelaagendamento = c.Int(),
                        horaalteraagendamento = c.Int(),
                        permiteusuariojuridico = c.Boolean(nullable: false),
                        atualizarstatusloteautomaticamente = c.Boolean(),
                        idprodutocartaofretepadrao = c.Int(),
                        naovalidarprotocolorecebidoempresa = c.Boolean(nullable: false),
                        modeloemailcancelamentooc = c.String(maxLength: 1000, unicode: false),
                        modeloemailgeracaooc = c.String(maxLength: 1000, unicode: false),
                        modeloemailrelatoriooc = c.String(maxLength: 1000, unicode: false),
                        modeloemailprotocolorejeitado = c.String(maxLength: 1000, unicode: false),
                        cntrc = c.Int(),
                        numordemcarregamento = c.Int(nullable: false),
                        idoccancgr = c.Int(),
                        idoccanccte = c.Int(),
                        idoccancviagem = c.Int(),
                        idocremocaofila = c.Int(),
                        distanciaentradaraio = c.Int(),
                        distanciasaidaraio = c.Int(),
                        distanciaraiogr = c.Int(),
                        bateriafraca = c.Int(),
                        bateriaforte = c.Int(),
                        emailcartafrete = c.String(maxLength: 100, unicode: false),
                        cancelaviagemcomprotocolo = c.Boolean(nullable: false),
                        minutosvalidadehash = c.Int(),
                        utilizavalidacaoporperfilnopagamentodefrete = c.Boolean(),
                        telefone0800 = c.String(maxLength: 20, unicode: false),
                        utilizaplacasespeciais = c.Boolean(),
                        enviaemailgrremocaolote = c.Boolean(nullable: false),
                        utilizanovorotograma = c.Boolean(nullable: false),
                        cep = c.String(nullable: false, maxLength: 8, unicode: false),
                        endereco = c.String(nullable: false, maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        bairro = c.String(nullable: false, maxLength: 100, unicode: false),
                        idcidade = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idpais = c.Int(nullable: false),
                        telefone = c.String(nullable: false, maxLength: 20, unicode: false),
                        email = c.String(nullable: false, maxLength: 500, unicode: false),
                        celular = c.String(maxLength: 20, unicode: false),
                        prioridadecooperadocargas = c.Boolean(nullable: false),
                        minutosprioridade = c.Int(),
                        raiocooperado = c.Double(nullable: false),
                        periodoinienvcheckincooperado = c.Time(nullable: false, precision: 7),
                        periodobloqueioeventosaberto = c.String(maxLength: 85, unicode: false),
                        periodofimenvcheckincooperado = c.Time(nullable: false, precision: 7),
                        raioterceiro = c.Double(nullable: false),
                        periodoinienvcheckinterceiro = c.Time(nullable: false, precision: 7),
                        periodofimenvcheckinterceiro = c.Time(nullable: false, precision: 7),
                        roteirizarcarga = c.Boolean(nullable: false),
                        temperaturainicial = c.Decimal(precision: 5, scale: 2),
                        temperaturafinal = c.Decimal(precision: 5, scale: 2),
                        avisosonorofraco = c.Int(),
                        avisosonoromoderado = c.Int(),
                        avisosonoroforte = c.Int(),
                        periodoexpiracaochat = c.Time(nullable: false, precision: 7),
                        ocultarnovocadastros = c.Boolean(nullable: false),
                        emailcontatogr = c.String(maxLength: 1000, unicode: false),
                        diascontratochecklist = c.Int(nullable: false),
                        alertaarea = c.Boolean(nullable: false),
                        acuracia = c.Decimal(nullable: false, precision: 10, scale: 0),
                        validaraio = c.Int(nullable: false),
                        raioentrega = c.Int(),
                        idocorrenciavelocidade = c.Int(),
                        idmotivovelocidade = c.Int(),
                        quantidadeocorrenciavelocidade = c.Int(),
                        emailnome = c.String(maxLength: 50, unicode: false),
                        emailendereco = c.String(maxLength: 100, unicode: false),
                        emailporta = c.Decimal(precision: 4, scale: 0),
                        emailservidor = c.String(maxLength: 100, unicode: false),
                        emailssl = c.Boolean(nullable: false),
                        emailusuario = c.String(maxLength: 100, unicode: false),
                        emailsenha = c.String(maxLength: 100, unicode: false),
                        idlayoutcartao = c.Int(),
                        tipocarregamentofrete = c.Int(),
                        percentualtransferenciamotorista = c.Decimal(nullable: false, precision: 18, scale: 2),
                        valortotaltarifasmeiohomologado = c.Decimal(precision: 18, scale: 2),
                        quantidadetotaltarifasmeiohomologado = c.Int(),
                        tempoexpiracaocreditopedagio = c.Time(precision: 7),
                        diasexpiracaosaldopedagio = c.Int(),
                        informacoestransferenciabancaria = c.String(maxLength: 100, unicode: false),
                        urlrastreamento = c.String(maxLength: 100, unicode: false),
                        loginrastreamento = c.String(maxLength: 100, unicode: false),
                        senharastreamento = c.String(maxLength: 100, unicode: false),
                        idtecnologia = c.Int(),
                        idsistemaexterno = c.String(maxLength: 15, unicode: false),
                        apresentapontoreferencia = c.Boolean(nullable: false),
                        prazoparainformardocumentos = c.Int(),
                        webhookprotocoloendpoint = c.String(maxLength: 100, unicode: false),
                        webhookprotocoloheaders = c.String(maxLength: 100, unicode: false),
                        horasvalidadechavecadastrousuario = c.Int(nullable: false),
                        validadeoc = c.Int(),
                        ativavalidacaoempresagr = c.Boolean(),
                        empresacontabancaria_id = c.Int(),
                    })
                .PrimaryKey(t => t.idempresa)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.EMPRESA_CONTABANCARIA", t => t.empresacontabancaria_id)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.LAYOUT_CARTAO", t => t.idlayoutcartao)
                .ForeignKey("dbo.MOTIVO", t => t.idoccanccte)
                .ForeignKey("dbo.MOTIVO", t => t.idoccancgr)
                .ForeignKey("dbo.MOTIVO", t => t.idoccancviagem)
                .ForeignKey("dbo.MOTIVO", t => t.idocremocaofila)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .Index(t => t.idoccancgr, name: "IX_IdOCcancGR")
                .Index(t => t.idoccanccte, name: "IX_IdOCcancCTe")
                .Index(t => t.idoccancviagem, name: "IX_IdOCcancViagem")
                .Index(t => t.idocremocaofila, name: "IX_IdOCRemocaoFila")
                .Index(t => t.idcidade, name: "IX_IdCidade")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idlayoutcartao, name: "IX_IdLayoutCartao")
                .Index(t => t.empresacontabancaria_id, name: "IX_EmpresaContaBancaria_Id");
            
            CreateTable(
                "dbo.AUTENTICACAOAPLICACAO",
                c => new
                    {
                        idautenticacaoempresa = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        cnpjaplicacao = c.String(nullable: false, maxLength: 100, unicode: false),
                        token = c.String(nullable: false, maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idautenticacaoempresa)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.AUTORIZACAO_EMPRESA",
                c => new
                    {
                        idempresa = c.Int(nullable: false),
                        idmenu = c.Int(nullable: false),
                        haspermissao = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => new { t.idempresa, t.idmenu })
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.MENU", t => t.idmenu)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idmenu, name: "IX_IdMenu");
            
            CreateTable(
                "dbo.MENU",
                c => new
                    {
                        idmenu = c.Int(nullable: false, identity: true),
                        idmenupai = c.Int(),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        link = c.String(maxLength: 100, unicode: false),
                        linknovo = c.String(maxLength: 100, unicode: false),
                        perfis = c.String(nullable: false, maxLength: 20, unicode: false),
                        sequencia = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        identificadorpermissao = c.Int(),
                        ismenupai = c.Boolean(nullable: false),
                        ismenumobile = c.Boolean(nullable: false),
                        modulo_idmodulo = c.Int(),
                    })
                .PrimaryKey(t => t.idmenu)
                .ForeignKey("dbo.MENU", t => t.idmenupai)
                .ForeignKey("dbo.MODULO", t => t.modulo_idmodulo)
                .Index(t => t.idmenupai, name: "IX_IdMenuPai")
                .Index(t => t.modulo_idmodulo, name: "IX_Modulo_IdModulo");
            
            CreateTable(
                "dbo.GRUPO_USUARIO_MENU",
                c => new
                    {
                        idgrupousuario = c.Int(nullable: false),
                        idmenu = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idgrupousuario, t.idmenu })
                .ForeignKey("dbo.GRUPO_USUARIO", t => t.idgrupousuario)
                .ForeignKey("dbo.MENU", t => t.idmenu)
                .Index(t => t.idgrupousuario, name: "IX_IdGrupoUsuario")
                .Index(t => t.idmenu, name: "IX_IdMenu");
            
            CreateTable(
                "dbo.GRUPO_USUARIO",
                c => new
                    {
                        idgrupousuario = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                        idestabelecimentobase = c.Int(),
                    })
                .PrimaryKey(t => t.idgrupousuario)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_BASE",
                c => new
                    {
                        idestabelecimento = c.Int(nullable: false, identity: true),
                        razaosocial = c.String(maxLength: 100, unicode: false),
                        associacao = c.Boolean(nullable: false),
                        pertenceredejsl = c.Boolean(nullable: false),
                        idfilial = c.Int(),
                        descricao = c.String(maxLength: 100, unicode: false),
                        cnpjestabelecimento = c.String(maxLength: 100, unicode: false),
                        dataultimaatualizacao = c.DateTime(),
                        idtipoestabelecimento = c.Int(nullable: false),
                        idpais = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idcidade = c.Int(nullable: false),
                        bairro = c.String(maxLength: 100, unicode: false),
                        logradouro = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        complemento = c.String(maxLength: 100, unicode: false),
                        cep = c.String(maxLength: 20, unicode: false),
                        email = c.String(maxLength: 100, unicode: false),
                        emailprotocolo = c.String(maxLength: 100, unicode: false),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        ativo = c.Boolean(nullable: false),
                        telefone = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idestabelecimento)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .ForeignKey("dbo.TIPO_ESTABELECIMENTO", t => t.idtipoestabelecimento)
                .Index(t => t.idfilial, name: "IX_IdFilial")
                .Index(t => t.idtipoestabelecimento, name: "IX_IdTipoEstabelecimento")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idcidade, name: "IX_IdCidade");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_BASE_ASSOCIACAO",
                c => new
                    {
                        idestabelecimento = c.Int(nullable: false),
                        idassociacao = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idestabelecimento, t.idassociacao })
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idassociacao)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimento)
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento")
                .Index(t => t.idassociacao, name: "IX_IdAssociacao");
            
            CreateTable(
                "dbo.CIDADE",
                c => new
                    {
                        idcidade = c.Int(nullable: false, identity: true),
                        idestado = c.Int(nullable: false),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                        ibge = c.Int(),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        ativo = c.Boolean(nullable: false),
                        dataalteracao = c.DateTime(nullable: false, precision: 7, storeType: "datetime2"),
                    })
                .PrimaryKey(t => t.idcidade)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .Index(t => t.idestado, name: "IX_IdEstado");
            
            CreateTable(
                "dbo.CHECKIN_RESUMO",
                c => new
                    {
                        idcheckinresumo = c.Int(nullable: false, identity: true),
                        idcheckin = c.Int(nullable: false),
                        idusuario = c.Int(),
                        idmotorista = c.Int(),
                        idviagem = c.Int(),
                        idempresa = c.Int(),
                        datahora = c.DateTime(nullable: false),
                        tipoevento = c.Int(nullable: false),
                        latitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        longitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        idmotoristabase = c.Int(),
                        cpfcnpjusuario = c.String(maxLength: 14, unicode: false),
                        idcidadecheckin = c.Int(),
                        regiaobrasil = c.Int(),
                    })
                .PrimaryKey(t => t.idcheckinresumo)
                .ForeignKey("dbo.CHECKIN", t => t.idcheckin)
                .ForeignKey("dbo.CIDADE", t => t.idcidadecheckin)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.MOTORISTA", t => t.idmotorista)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => t.idcheckin, name: "IX_IdCheckin")
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idmotorista, name: "IX_IdMotorista")
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa")
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idcidadecheckin, name: "IX_IdCidadeCheckin");
            
            CreateTable(
                "dbo.CHECKIN",
                c => new
                    {
                        idcheckin = c.Int(nullable: false, identity: true),
                        idusuario = c.Int(),
                        idmotorista = c.Int(),
                        idviagem = c.Int(),
                        idempresa = c.Int(),
                        datahora = c.DateTime(nullable: false),
                        tipoevento = c.Int(nullable: false),
                        latitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        idmotoristabase = c.Int(),
                        cpfcnpjusuario = c.String(maxLength: 100, unicode: false),
                        longitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        idcidadecheckin = c.Int(),
                        regiaobrasil = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idcheckin)
                .ForeignKey("dbo.CIDADE", t => t.idcidadecheckin)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.MOTORISTA", t => t.idmotorista)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idmotorista, name: "IX_IdMotorista")
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa")
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idcidadecheckin, name: "IX_IdCidadeCheckIn");
            
            CreateTable(
                "dbo.MOTORISTA",
                c => new
                    {
                        idmotorista = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(),
                        nome = c.String(nullable: false, maxLength: 150, unicode: false),
                        rg = c.String(maxLength: 100, unicode: false),
                        rgorgaoexpedidor = c.String(nullable: false, maxLength: 10, unicode: false),
                        referencia1 = c.String(maxLength: 100, unicode: false),
                        referencia2 = c.String(maxLength: 100, unicode: false),
                        validadecnh = c.DateTime(),
                        cpf = c.String(nullable: false, maxLength: 11, unicode: false),
                        sexo = c.String(nullable: false, maxLength: 1, unicode: false),
                        cnh = c.String(nullable: false, maxLength: 14, unicode: false),
                        cnhcategoria = c.String(nullable: false, maxLength: 2, unicode: false),
                        celular = c.String(maxLength: 11, unicode: false),
                        tipocontrato = c.Int(nullable: false),
                        email = c.String(maxLength: 100, unicode: false),
                        foto = c.Binary(),
                        ativo = c.Boolean(nullable: false),
                        cep = c.String(maxLength: 100, unicode: false),
                        endereco = c.String(nullable: false, maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.String(maxLength: 5, unicode: false),
                        bairro = c.String(maxLength: 50, unicode: false),
                        idpais = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idcidade = c.Int(nullable: false),
                        idusuariocadastro = c.Int(),
                        datahoraultimaatualizacao = c.DateTime(nullable: false),
                        avisovalidadecnh = c.DateTime(),
                        datacontatado = c.DateTime(),
                        nomemae = c.String(maxLength: 100, unicode: false),
                        nomepai = c.String(maxLength: 100, unicode: false),
                        datanascimento = c.DateTime(),
                        formulariocnh = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idmotorista)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.USUARIO", t => t.idusuariocadastro)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .Index(t => new { t.cpf, t.ativo, t.idempresa }, unique: true, name: "IX_MOTORISTA_CPF_ATIVO_IDEMPRESA")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idcidade, name: "IX_IdCidade")
                .Index(t => t.idusuariocadastro, name: "IX_IdUsuarioCadastro");
            
            CreateTable(
                "dbo.CONJUNTO_EMPRESA",
                c => new
                    {
                        idconjuntoempresa = c.Int(nullable: false, identity: true),
                        idconjunto = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idveiculo = c.Int(nullable: false),
                        idmotorista = c.Int(),
                        idusuario = c.Int(),
                        proprietario_idproprietario = c.Int(),
                        proprietario_idempresa = c.Int(),
                    })
                .PrimaryKey(t => t.idconjuntoempresa)
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.proprietario_idproprietario, t.proprietario_idempresa })
                .ForeignKey("dbo.CONJUNTO", t => t.idconjunto)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.MOTORISTA", t => t.idmotorista)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .ForeignKey("dbo.VEICULO", t => t.idveiculo)
                .Index(t => t.idconjunto, name: "IX_IdConjunto")
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idveiculo, name: "IX_IdVeiculo")
                .Index(t => t.idmotorista, name: "IX_IdMotorista")
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => new { t.proprietario_idproprietario, t.proprietario_idempresa }, name: "IX_Proprietario_IdProprietario_Proprietario_IdEmpresa");
            
            CreateTable(
                "dbo.CONJUNTO",
                c => new
                    {
                        idconjunto = c.Int(nullable: false, identity: true),
                        placacavalo = c.String(maxLength: 100, unicode: false),
                        idtipocavalo = c.Int(nullable: false),
                        idmotoristabase = c.Int(nullable: false),
                        datacadastro = c.DateTime(nullable: false, precision: 7, storeType: "datetime2"),
                        dataatualizacao = c.DateTime(nullable: false, precision: 7, storeType: "datetime2"),
                        dataintegracaogr = c.DateTime(precision: 7, storeType: "datetime2"),
                        idgestorfrota = c.Int(),
                        statusconjunto = c.Int(nullable: false),
                        idproprietariobase = c.Int(),
                    })
                .PrimaryKey(t => t.idconjunto)
                .ForeignKey("dbo.TIPO_CAVALO", t => t.idtipocavalo)
                .Index(t => t.idtipocavalo, name: "IX_IdTipoCavalo");
            
            CreateTable(
                "dbo.CONJUNTO_CARRETA",
                c => new
                    {
                        idconjuntocarreta = c.Int(nullable: false, identity: true),
                        idconjunto = c.Int(nullable: false),
                        placa = c.String(maxLength: 7, unicode: false),
                        idtipocarreta = c.Int(nullable: false),
                        capacidade = c.Decimal(precision: 18, scale: 2),
                        veiculo_idveiculo = c.Int(),
                    })
                .PrimaryKey(t => t.idconjuntocarreta)
                .ForeignKey("dbo.CONJUNTO", t => t.idconjunto)
                .ForeignKey("dbo.VEICULO", t => t.veiculo_idveiculo)
                .ForeignKey("dbo.TIPO_CARRETA", t => t.idtipocarreta)
                .Index(t => t.idconjunto, name: "IX_IdConjunto")
                .Index(t => t.idtipocarreta, name: "IX_IdTipoCarreta")
                .Index(t => t.veiculo_idveiculo, name: "IX_Veiculo_IdVeiculo");
            
            CreateTable(
                "dbo.TIPO_CARRETA",
                c => new
                    {
                        idtipocarreta = c.Int(nullable: false, identity: true),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                        categoria = c.Int(nullable: false),
                        qtdeeixos = c.Int(),
                        metroscubicos = c.Int(),
                        capacidade = c.Int(),
                        eixosespacados = c.Boolean(),
                        ativo = c.Boolean(nullable: false),
                        idempresa = c.Int(),
                        destacar = c.Boolean(nullable: false),
                        datahoraultimaatualizacao = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.idtipocarreta)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.VEICULO",
                c => new
                    {
                        idveiculo = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(),
                        idfilial = c.Int(),
                        idproprietario = c.Int(),
                        idmotorista = c.Int(),
                        idusuario = c.Int(),
                        idpais = c.Int(),
                        idestado = c.Int(),
                        idcidade = c.Int(),
                        numerofrota = c.Long(),
                        tipocontrato = c.Int(nullable: false),
                        avisosonorofraco = c.Int(),
                        avisosonoromoderado = c.Int(),
                        avisosonoroforte = c.Int(),
                        quantidadeeixos = c.Int(nullable: false),
                        dataultimaatualizacao = c.DateTime(),
                        idoperacao = c.Int(),
                        municipio = c.String(maxLength: 100, unicode: false),
                        idtecnologia = c.Int(),
                        habilitarcontratociotagregado = c.Boolean(),
                        corveiculo = c.String(maxLength: 100, unicode: false),
                        placa = c.String(nullable: false, maxLength: 10, unicode: false),
                        tipopadraoplaca = c.Int(nullable: false),
                        placapadraobrasil = c.String(maxLength: 10, unicode: false),
                        chassi = c.String(nullable: false, maxLength: 22, unicode: false),
                        anofabricacao = c.Int(),
                        anomodelo = c.Int(),
                        renavam = c.String(maxLength: 100, unicode: false),
                        marca = c.String(nullable: false, maxLength: 50, unicode: false),
                        modelo = c.String(nullable: false, maxLength: 50, unicode: false),
                        comtracao = c.Boolean(nullable: false),
                        tiporodagem = c.Int(nullable: false),
                        tecnologiarastreamento = c.String(maxLength: 100, unicode: false),
                        idtipocavalo = c.Int(),
                        idtipocarreta = c.Int(),
                        ativo = c.Boolean(nullable: false),
                        statusintegracao = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idveiculo)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.MOTORISTA", t => t.idmotorista)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.idproprietario, t.idempresa })
                .ForeignKey("dbo.TIPO_CARRETA", t => t.idtipocarreta)
                .ForeignKey("dbo.TIPO_CAVALO", t => t.idtipocavalo)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => new { t.idproprietario, t.idempresa }, name: "IX_IdProprietario_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial")
                .Index(t => t.idmotorista, name: "IX_IdMotorista")
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idcidade, name: "IX_IdCidade")
                .Index(t => t.idtipocavalo, name: "IX_IdTipoCavalo")
                .Index(t => t.idtipocarreta, name: "IX_IdTipoCarreta");
            
            CreateTable(
                "dbo.CONTRATO_CIOT_AGREGADO_VEICULO",
                c => new
                    {
                        idcontratociotagregado = c.Int(nullable: false),
                        idveiculo = c.Int(nullable: false),
                        datacadastro = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => new { t.idcontratociotagregado, t.idveiculo })
                .ForeignKey("dbo.CONTRATO_CIOT_AGREGADO", t => t.idcontratociotagregado)
                .ForeignKey("dbo.VEICULO", t => t.idveiculo)
                .Index(t => t.idcontratociotagregado, name: "IX_IdContratoCiotAgregado")
                .Index(t => t.idveiculo, name: "IX_IdVeiculo");
            
            CreateTable(
                "dbo.CONTRATO_CIOT_AGREGADO",
                c => new
                    {
                        idcontratociotagregado = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idproprietario = c.Int(nullable: false),
                        datainicio = c.DateTime(nullable: false),
                        datafinal = c.DateTime(nullable: false),
                        iddeclaracaociot = c.Int(),
                        datacadastro = c.DateTime(nullable: false),
                        idusuario = c.Int(),
                        status = c.Int(nullable: false),
                        resultadodeclaracaociot = c.Int(),
                        mensagemdeclaracaociot = c.String(maxLength: 2000, unicode: false),
                        origemencerramento = c.Int(),
                    })
                .PrimaryKey(t => t.idcontratociotagregado)
                .ForeignKey("dbo.DECLARACAO_CIOT", t => t.iddeclaracaociot)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.idproprietario, t.idempresa })
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => new { t.idproprietario, t.idempresa }, name: "IX_idproprietario_IdEmpresa")
                .Index(t => t.iddeclaracaociot)
                .Index(t => t.idusuario);
            
            CreateTable(
                "dbo.DECLARACAO_CIOT",
                c => new
                    {
                        iddeclaracaociot = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idviagem = c.Int(),
                        ciot = c.String(maxLength: 12, unicode: false),
                        verificador = c.String(maxLength: 4, unicode: false),
                        avisotransportador = c.String(maxLength: 300, unicode: false),
                        emcontigencia = c.Boolean(nullable: false),
                        protocoloerro = c.String(maxLength: 50, unicode: false),
                        datadeclaracao = c.DateTime(nullable: false),
                        senha = c.String(maxLength: 20, unicode: false),
                        datacancelamento = c.DateTime(),
                        motivocancelamento = c.String(maxLength: 100, unicode: false),
                        protocolocancelamento = c.String(maxLength: 100, unicode: false),
                        tipodeclaracao = c.Int(nullable: false),
                        idcontratociotagregado = c.Int(),
                        tarifapaga = c.Boolean(nullable: false),
                        status = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.iddeclaracaociot)
                .ForeignKey("dbo.CONTRATO_CIOT_AGREGADO", t => t.idcontratociotagregado)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa")
                .Index(t => t.ciot, unique: true, name: "Ix_DeclaracaoCIot_Ciot")
                .Index(t => t.idcontratociotagregado, name: "IX_IdContratoCiotAgregado");
            
            CreateTable(
                "dbo.VIAGEM",
                c => new
                    {
                        idviagem = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idviagemcomplementada = c.Int(),
                        idfilial = c.Int(),
                        idproprietario = c.Int(),
                        placa = c.String(nullable: false, maxLength: 7, unicode: false),
                        idclienteorigem = c.Int(nullable: false),
                        idclientedestino = c.Int(nullable: false),
                        idclientetomador = c.Int(),
                        datacoleta = c.DateTime(),
                        coleta = c.String(maxLength: 250, unicode: false),
                        entrega = c.String(maxLength: 250, unicode: false),
                        dataprevisaoentrega = c.DateTime(nullable: false),
                        datafinalizacao = c.DateTime(),
                        pesosaida = c.Decimal(precision: 10, scale: 3),
                        pesochegada = c.Decimal(precision: 10, scale: 3),
                        pesochegadaoriginal = c.Decimal(precision: 18, scale: 2),
                        pesodiferenca = c.Decimal(precision: 10, scale: 3),
                        valormercadoria = c.Decimal(precision: 10, scale: 2),
                        diffretemotorista = c.Decimal(precision: 10, scale: 2),
                        valorquebramercadoria = c.Decimal(precision: 10, scale: 2),
                        valorquebramercadoriacalculado = c.Decimal(precision: 10, scale: 2),
                        datalancamento = c.DateTime(),
                        nomemotorista = c.String(maxLength: 100, unicode: false),
                        cpfmotorista = c.String(nullable: false, maxLength: 11, unicode: false),
                        nomeproprietario = c.String(maxLength: 100, unicode: false),
                        cpfcnpjproprietario = c.String(maxLength: 100, unicode: false),
                        rntrc = c.Int(),
                        cnhmotorista = c.String(maxLength: 14, unicode: false),
                        statusviagem = c.Int(nullable: false),
                        numerodocumento = c.String(maxLength: 100, unicode: false),
                        numeronota = c.String(maxLength: 100, unicode: false),
                        numerodocumentocomplementado = c.String(maxLength: 100, unicode: false),
                        dataemissao = c.DateTime(),
                        cnpjfilial = c.String(maxLength: 100, unicode: false),
                        razaosocialfilial = c.String(maxLength: 100, unicode: false),
                        produto = c.String(maxLength: 100, unicode: false),
                        naturezacarga = c.Int(),
                        unidade = c.Int(nullable: false),
                        origem = c.String(maxLength: 100, unicode: false),
                        quantidade = c.Decimal(nullable: false, precision: 18, scale: 2),
                        statusintegracao = c.Int(nullable: false),
                        documentocliente = c.String(maxLength: 100, unicode: false),
                        numerocartao = c.String(maxLength: 30, unicode: false),
                        irrpf = c.Decimal(nullable: false, precision: 10, scale: 2),
                        inss = c.Decimal(nullable: false, precision: 10, scale: 2),
                        sestsenat = c.Decimal(nullable: false, precision: 10, scale: 2),
                        valorpedagio = c.Decimal(nullable: false, precision: 18, scale: 2),
                        pedagiobaixado = c.Boolean(nullable: false),
                        habilitardeclaracaociot = c.Boolean(nullable: false),
                        resultadodeclaracaociot = c.Int(),
                        mensagemdeclaracaociot = c.String(maxLength: 500, unicode: false),
                        resultadocomprapedagio = c.Int(nullable: false),
                        dataconfirmacaocreditopedagio = c.DateTime(),
                        dataconfirmacaoestornopedagio = c.DateTime(),
                        mensagemcomprapedagio = c.String(maxLength: 500, unicode: false),
                        numeroprotocolopedagio = c.Long(),
                        estornosaldoresidualpedagiosolicitado = c.Boolean(),
                        dataconfirmacaopedagio = c.DateTime(),
                        datacancelamentopedagio = c.DateTime(),
                        iddeclaracaociot = c.Int(),
                        numerocontrole = c.String(maxLength: 300, unicode: false),
                        datadescarga = c.DateTime(),
                        codigotipocarga = c.Int(),
                        distanciaviagem = c.Int(),
                        ceporigem = c.String(maxLength: 8, unicode: false),
                        cepdestino = c.String(maxLength: 8, unicode: false),
                        altodesempenho = c.Boolean(nullable: false),
                        destinacaocomercial = c.Boolean(nullable: false),
                        freteretorno = c.Boolean(nullable: false),
                        cepretorno = c.String(maxLength: 8, unicode: false),
                        distanciaretorno = c.Int(),
                        formapagamento = c.Int(nullable: false),
                        dataatualizacao = c.DateTime(),
                        valepedagiosolicitado = c.Boolean(),
                        protocolovalepedagio = c.String(maxLength: 30, unicode: false),
                    })
                .PrimaryKey(t => new { t.idviagem, t.idempresa })
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.idproprietario, t.idempresa })
                .ForeignKey("dbo.CLIENTE", t => t.idclientedestino)
                .ForeignKey("dbo.CLIENTE", t => t.idclienteorigem)
                .ForeignKey("dbo.CLIENTE", t => t.idclientetomador)
                .ForeignKey("dbo.DECLARACAO_CIOT", t => t.iddeclaracaociot)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagemcomplementada, t.idempresa })
                .Index(t => new { t.idproprietario, t.idempresa })
                .Index(t => new { t.idviagemcomplementada, t.idempresa }, name: "IX_IdViagemComplementada_idempresa")
                .Index(t => t.idfilial, name: "IX_IdFilial")
                .Index(t => t.idclienteorigem)
                .Index(t => t.idclientedestino)
                .Index(t => t.idclientetomador, name: "IX_IdClienteTomador")
                .Index(t => t.iddeclaracaociot, name: "IX_IdDeclaracaoCiot");
            
            CreateTable(
                "dbo.CLIENTE",
                c => new
                    {
                        idcliente = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        razaosocial = c.String(nullable: false, maxLength: 100, unicode: false),
                        nomefantasia = c.String(maxLength: 100, unicode: false),
                        tipopessoa = c.Int(nullable: false),
                        cnpjcpf = c.String(nullable: false, maxLength: 14, unicode: false),
                        rg = c.String(maxLength: 15, unicode: false),
                        orgaoexpedidorrg = c.String(maxLength: 10, unicode: false),
                        ie = c.String(maxLength: 15, unicode: false),
                        logo = c.Binary(),
                        celular = c.String(maxLength: 11, unicode: false),
                        email = c.String(maxLength: 150, unicode: false),
                        cep = c.String(nullable: false, maxLength: 8, unicode: false),
                        endereco = c.String(nullable: false, maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        bairro = c.String(nullable: false, maxLength: 100, unicode: false),
                        idpais = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idcidade = c.Int(nullable: false),
                        tipocliente = c.Int(),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        raiolocalizacao = c.Decimal(precision: 18, scale: 2),
                        ativarparametrosgestao = c.Boolean(nullable: false),
                        obrigarcpfreceber = c.Boolean(nullable: false),
                        autenticarcodigobarranf = c.Boolean(nullable: false),
                        permitiralterardata = c.Boolean(nullable: false),
                        enviarsmsconfirmacao = c.Boolean(nullable: false),
                        enviaremailconfirmacao = c.Boolean(nullable: false),
                        statusintegracao = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        dataatualizacao = c.DateTime(nullable: false),
                        pontoreferencia = c.Boolean(),
                        enviaremailocgerada = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idcliente)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .Index(t => new { t.idempresa, t.cnpjcpf, t.ativo }, unique: true, name: "IX_CLIENTE_CPFCNPJ_ATIVO_IDEMPRESA")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idcidade, name: "IX_IdCidade");
            
            CreateTable(
                "dbo.CLIENTE_ACESSO",
                c => new
                    {
                        idcliente = c.Int(nullable: false),
                        cnpjcpf = c.String(nullable: false, maxLength: 14, unicode: false),
                    })
                .PrimaryKey(t => new { t.idcliente, t.cnpjcpf })
                .ForeignKey("dbo.CLIENTE", t => t.idcliente)
                .Index(t => t.idcliente, name: "IX_IdCliente");
            
            CreateTable(
                "dbo.CLIENTE_ENDERECO",
                c => new
                    {
                        idclienteendereco = c.Int(nullable: false, identity: true),
                        idcliente = c.Int(nullable: false),
                        cep = c.String(maxLength: 100, unicode: false),
                        endereco = c.String(nullable: false, maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        bairro = c.String(maxLength: 100, unicode: false),
                        idpais = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idcidade = c.Int(nullable: false),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                    })
                .PrimaryKey(t => new { t.idclienteendereco, t.idcliente })
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.CLIENTE", t => t.idcliente)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .Index(t => t.idcliente, name: "IX_IdCliente")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idcidade, name: "IX_IdCidade");
            
            CreateTable(
                "dbo.ESTADO",
                c => new
                    {
                        idestado = c.Int(nullable: false, identity: true),
                        idpais = c.Int(nullable: false),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                        sigla = c.String(nullable: false, maxLength: 2, unicode: false),
                        ibge = c.Int(),
                        ativo = c.Boolean(nullable: false),
                        dataalteracao = c.DateTime(nullable: false, precision: 7, storeType: "datetime2"),
                        regiao = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .Index(t => t.idpais, name: "IX_IdPais");
            
            CreateTable(
                "dbo.ESTABELECIMENTO",
                c => new
                    {
                        idestabelecimento = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        credenciado = c.Boolean(nullable: false),
                        taxaantecipacao = c.Double(nullable: false),
                        idestabelecimentobase = c.Int(),
                        associacao = c.Boolean(nullable: false),
                        razaosocial = c.String(maxLength: 100, unicode: false),
                        pagamentoantecipado = c.Boolean(nullable: false),
                        pontoreferencia = c.Boolean(),
                        liberaprotocolos = c.Boolean(nullable: false),
                        permitealterardocumentospesochegada = c.Boolean(nullable: false),
                        obrigadocumentospagamento = c.Boolean(nullable: false),
                        horainicialsemvalidarchave = c.Time(precision: 7),
                        horafinalsemvalidarchave = c.Time(precision: 7),
                        realizapagamentocomcheque = c.Boolean(nullable: false),
                        idfilialprocessocaixatms = c.Int(),
                        descricao = c.String(maxLength: 100, unicode: false),
                        cnpjestabelecimento = c.String(maxLength: 100, unicode: false),
                        dataultimaatualizacao = c.DateTime(),
                        idtipoestabelecimento = c.Int(nullable: false),
                        idpais = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idcidade = c.Int(nullable: false),
                        bairro = c.String(maxLength: 100, unicode: false),
                        logradouro = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        complemento = c.String(maxLength: 100, unicode: false),
                        cep = c.String(maxLength: 20, unicode: false),
                        email = c.String(maxLength: 100, unicode: false),
                        emailprotocolo = c.String(maxLength: 100, unicode: false),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        ativo = c.Boolean(nullable: false),
                        telefone = c.String(maxLength: 16, unicode: false),
                    })
                .PrimaryKey(t => t.idestabelecimento)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .ForeignKey("dbo.TIPO_ESTABELECIMENTO", t => t.idtipoestabelecimento)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase")
                .Index(t => t.idtipoestabelecimento, name: "IX_IdTipoEstabelecimento")
                .Index(t => t.idpais, name: "IX_IdPais")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idcidade, name: "IX_IdCidade");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_ASSOCIACAO",
                c => new
                    {
                        idestabelecimento = c.Int(nullable: false),
                        idassociacao = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idestabelecimento, t.idassociacao })
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idassociacao)
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimento)
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento")
                .Index(t => t.idassociacao, name: "IX_IdAssociacao");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_CONTA_BANCARIA",
                c => new
                    {
                        idestabelecimentocontabancaria = c.Int(nullable: false, identity: true),
                        idestabelecimento = c.Int(nullable: false),
                        nomeconta = c.String(nullable: false, maxLength: 100, unicode: false),
                        codigobanco = c.String(nullable: false, maxLength: 10, unicode: false),
                        nomebanco = c.String(nullable: false, maxLength: 100, unicode: false),
                        agencia = c.String(nullable: false, maxLength: 10, unicode: false),
                        conta = c.String(nullable: false, maxLength: 30, unicode: false),
                        digitoconta = c.String(maxLength: 10, unicode: false),
                        tipoconta = c.Int(nullable: false),
                        cnpjtitular = c.String(nullable: false, maxLength: 14, unicode: false),
                        nometitular = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idestabelecimentocontabancaria)
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimento)
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_PRODUTO",
                c => new
                    {
                        idestabelecimento = c.Int(nullable: false),
                        idproduto = c.Int(nullable: false, identity: true),
                        idprodutobase = c.Int(),
                        idestabelecimentobase = c.Int(),
                        descricao = c.String(maxLength: 100, unicode: false),
                        unidademedida = c.String(maxLength: 10, unicode: false),
                        precounitario = c.Decimal(precision: 18, scale: 2),
                        precopromocional = c.Decimal(precision: 18, scale: 2),
                        contrato = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => new { t.idestabelecimento, t.idproduto })
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimento)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE_PRODUTO", t => new { t.idestabelecimentobase, t.idprodutobase })
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento")
                .Index(t => new { t.idestabelecimentobase, t.idprodutobase }, name: "IX_IdEstabelecimentoBase_IdProdutoBase");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_BASE_PRODUTO",
                c => new
                    {
                        idestabelecimentobase = c.Int(nullable: false),
                        idproduto = c.Int(nullable: false, identity: true),
                        descricao = c.String(maxLength: 100, unicode: false),
                        unidademedida = c.String(maxLength: 10, unicode: false),
                        precounitario = c.Decimal(precision: 18, scale: 2),
                        precopromocional = c.Decimal(precision: 18, scale: 2),
                    })
                .PrimaryKey(t => new { t.idestabelecimentobase, t.idproduto })
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase");
            
            CreateTable(
                "dbo.PAIS",
                c => new
                    {
                        idpais = c.Int(nullable: false, identity: true),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                        sigla = c.String(maxLength: 3, unicode: false),
                        bacen = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        dataalteracao = c.DateTime(nullable: false, precision: 7, storeType: "datetime2"),
                    })
                .PrimaryKey(t => t.idpais);
            
            CreateTable(
                "dbo.FILIAL",
                c => new
                    {
                        idfilial = c.Int(nullable: false, identity: true),
                        cnpj = c.String(nullable: false, maxLength: 14, unicode: false),
                        codigofilial = c.String(maxLength: 100, unicode: false),
                        razaosocial = c.String(nullable: false, maxLength: 100, unicode: false),
                        nomefantasia = c.String(nullable: false, maxLength: 100, unicode: false),
                        sigla = c.String(nullable: false, maxLength: 10, unicode: false),
                        cep = c.String(nullable: false, maxLength: 8, unicode: false),
                        endereco = c.String(nullable: false, maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        bairro = c.String(nullable: false, maxLength: 100, unicode: false),
                        telefone = c.String(nullable: false, maxLength: 11, unicode: false),
                        email = c.String(nullable: false, maxLength: 100, unicode: false),
                        idempresa = c.Int(nullable: false),
                        idcidade = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idpais = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        ie = c.Int(),
                        datahoraultimaatualizacao = c.DateTime(nullable: false),
                        pontoapoio = c.Boolean(nullable: false),
                        idfilialmae = c.Int(),
                        idsistemaexterno = c.String(maxLength: 15, unicode: false),
                        pontoreferencia = c.Boolean(),
                        alertaarea = c.Boolean(nullable: false),
                        acuracia = c.Decimal(nullable: false, precision: 10, scale: 0),
                        emailnome = c.String(maxLength: 50, unicode: false),
                        emailendereco = c.String(maxLength: 100, unicode: false),
                        emailporta = c.Decimal(precision: 4, scale: 0),
                        emailservidor = c.String(maxLength: 100, unicode: false),
                        emailssl = c.Boolean(nullable: false),
                        emailusuario = c.String(maxLength: 100, unicode: false),
                        emailsenha = c.String(maxLength: 100, unicode: false),
                        validadeoc = c.Int(),
                        ativavalidacaofilialgr = c.Boolean(),
                        mostrafilialoc = c.Boolean(),
                        inscricaoestadual = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idfilial)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idcidade, name: "IX_IdCidade")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idpais, name: "IX_IdPais");
            
            CreateTable(
                "dbo.CONTRATO",
                c => new
                    {
                        idcontrato = c.Int(nullable: false, identity: true),
                        numcontratoerp = c.String(nullable: false, maxLength: 10, unicode: false),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        idclientepagador = c.Int(),
                        imprimefreteoc = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idcontrato)
                .ForeignKey("dbo.CLIENTE", t => t.idclientepagador)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => t.idfilial, name: "IX_IdFilial")
                .Index(t => t.idclientepagador, name: "IX_IdClientePagador");
            
            CreateTable(
                "dbo.FILIAL_CONTATOS",
                c => new
                    {
                        idfilialcontato = c.Int(nullable: false, identity: true),
                        idfilial = c.Int(nullable: false),
                        nome = c.String(maxLength: 100, unicode: false),
                        telefone = c.String(maxLength: 100, unicode: false),
                        email = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => new { t.idfilialcontato, t.idfilial })
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.NOTIFICACAO_PUSH",
                c => new
                    {
                        idnotificacaopush = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        descricaomensagem = c.String(nullable: false, maxLength: 200, unicode: false),
                        sql = c.String(nullable: false, maxLength: 5000, unicode: false),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        momentoexecucao = c.Int(nullable: false),
                        idtiponotificacao = c.Int(nullable: false),
                        dataultimaatualizacao = c.DateTime(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        grupousuario_idgrupousuario = c.Int(),
                    })
                .PrimaryKey(t => t.idnotificacaopush)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.TIPO_NOTIFICACAO", t => t.idtiponotificacao)
                .ForeignKey("dbo.GRUPO_USUARIO", t => t.grupousuario_idgrupousuario)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial")
                .Index(t => t.idtiponotificacao, name: "IX_IdTipoNotificacao")
                .Index(t => t.grupousuario_idgrupousuario, name: "IX_GrupoUsuario_IdGrupoUsuario");
            
            CreateTable(
                "dbo.NOTIFICACAO_PUSH_ITEM",
                c => new
                    {
                        idhardware = c.Int(nullable: false),
                        idnotificacaopush = c.Int(nullable: false),
                        apelido = c.String(nullable: false, maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => new { t.idhardware, t.idnotificacaopush })
                .ForeignKey("dbo.NOTIFICACAO_PUSH", t => t.idnotificacaopush)
                .Index(t => t.idnotificacaopush, name: "IX_IdNotificacaoPush");
            
            CreateTable(
                "dbo.NOTIFICACAO_PUSH_GRUPO_USUARIO",
                c => new
                    {
                        idgrupousuario = c.Int(nullable: false),
                        idnotificacaopush = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idgrupousuario, t.idnotificacaopush })
                .ForeignKey("dbo.GRUPO_USUARIO", t => t.idgrupousuario)
                .ForeignKey("dbo.NOTIFICACAO_PUSH", t => t.idnotificacaopush)
                .Index(t => t.idgrupousuario, name: "IX_IdGrupoUsuario")
                .Index(t => t.idnotificacaopush, name: "IX_IdNotificacaoPush");
            
            CreateTable(
                "dbo.TIPO_NOTIFICACAO",
                c => new
                    {
                        idtiponotificacao = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        dataultimaatualizacao = c.DateTime(nullable: false),
                        ativo = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idtiponotificacao)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.NOTIFICACAO",
                c => new
                    {
                        idnotificacao = c.Int(nullable: false, identity: true),
                        idusuario = c.Int(nullable: false),
                        tipo = c.Int(nullable: false,
                            annotations: new Dictionary<string, AnnotationValues>
                            {
                                { 
                                    "defaultValue",
                                    new AnnotationValues(oldValue: null, newValue: "0")
                                },
                            }),
                        conteudo = c.String(nullable: false, unicode: false, storeType: "text"),
                        datahoraenvio = c.DateTime(nullable: false),
                        recebida = c.Boolean(nullable: false),
                        recebidanovo = c.Boolean(nullable: false),
                        lida = c.Boolean(),
                        link = c.String(maxLength: 100, unicode: false),
                        idtiponotificacao = c.Int(nullable: false),
                        funcaoorigem = c.Int(nullable: false),
                        idparent = c.Int(),
                    })
                .PrimaryKey(t => t.idnotificacao)
                .ForeignKey("dbo.TIPO_NOTIFICACAO", t => t.idtiponotificacao)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idtiponotificacao, name: "IX_IdTipoNotificacao");
            
            CreateTable(
                "dbo.USUARIO",
                c => new
                    {
                        idusuario = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                        login = c.String(maxLength: 100, unicode: false),
                        senha = c.String(nullable: false, maxLength: 50, unicode: false),
                        cpfcnpj = c.String(maxLength: 14, unicode: false),
                        idgrupousuario = c.Int(),
                        template = c.String(maxLength: 100, unicode: false),
                        foto = c.Binary(),
                        perfil = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        tipocobranca = c.Int(nullable: false),
                        permiteresponderchat = c.Boolean(nullable: false),
                        idpush = c.String(maxLength: 300, unicode: false),
                        recebernotificacao = c.Boolean(nullable: false),
                        rntrc = c.String(maxLength: 8, unicode: false),
                        carreteiro = c.Boolean(nullable: false),
                        datacadastro = c.DateTime(nullable: false),
                        dataultimoacessoweb = c.DateTime(),
                        dataultimoacessoaplicativo = c.DateTime(),
                        dataultimaaberturaaplicativo = c.DateTime(),
                        idfacebook = c.Long(),
                        idponto = c.Int(),
                        gestor = c.Boolean(nullable: false),
                        tokenfirebase = c.String(maxLength: 100, unicode: false),
                        fotourl = c.String(maxLength: 100, unicode: false),
                        vistoriador = c.Boolean(nullable: false),
                        cnh = c.String(maxLength: 100, unicode: false),
                        cnhcategoria = c.String(maxLength: 100, unicode: false),
                        validadecnh = c.DateTime(),
                        rg = c.String(maxLength: 100, unicode: false),
                        rgorgaoexpedidor = c.String(maxLength: 100, unicode: false),
                        referencia1 = c.String(maxLength: 100, unicode: false),
                        referencia2 = c.String(maxLength: 100, unicode: false),
                        tipocliente = c.Int(),
                        cpfcnpjdesabilitado = c.String(maxLength: 100, unicode: false),
                        logindesabilitado = c.String(maxLength: 100, unicode: false),
                        usoappempresa = c.Int(),
                        idhorario = c.Int(),
                        datacontatado = c.DateTime(),
                        statuscontatado = c.Int(nullable: false),
                        receberrelatoriooc = c.Boolean(nullable: false),
                        matriz = c.Boolean(),
                        nomemae = c.String(maxLength: 100, unicode: false),
                        nomepai = c.String(maxLength: 100, unicode: false),
                        datanascimento = c.DateTime(),
                        mostrarvideostreinamento = c.Boolean(),
                        vistoriadormaster = c.Boolean(nullable: false),
                        sistemaoperacional = c.Int(),
                        visualizatodoschecklists = c.Boolean(nullable: false),
                        usuariomasterestabelecimento = c.Boolean(),
                        keycodetransaction = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idusuario)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.GRUPO_USUARIO", t => t.idgrupousuario)
                .Index(t => new { t.idempresa, t.cpfcnpj, t.ativo }, unique: true, name: "IX_USUARIO_CPFCNPJ_ATIVO_IDEMPRESA")
                .Index(t => t.idgrupousuario, name: "IX_IdGrupoUsuario");
            
            CreateTable(
                "dbo.AUTH_SESSION",
                c => new
                    {
                        idauthsession = c.Int(nullable: false, identity: true),
                        token = c.Guid(nullable: false),
                        datacadastro = c.DateTime(nullable: false),
                        dataultimareq = c.DateTime(nullable: false),
                        idusuario = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idauthsession)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.CARGA_AVULSA",
                c => new
                    {
                        idcargaavulsa = c.Int(nullable: false, identity: true),
                        datacadastro = c.DateTime(nullable: false, precision: 7, storeType: "datetime2"),
                        idusuariocadastro = c.Int(nullable: false),
                        cpfmototista = c.String(maxLength: 100, unicode: false),
                        nomemotorista = c.String(maxLength: 100, unicode: false),
                        placacavalo = c.String(maxLength: 100, unicode: false),
                        placacarreta1 = c.String(maxLength: 100, unicode: false),
                        placacarreta2 = c.String(maxLength: 100, unicode: false),
                        placacarreta3 = c.String(maxLength: 100, unicode: false),
                        observacao = c.String(unicode: false, storeType: "text"),
                        valor = c.Decimal(nullable: false, precision: 18, scale: 2),
                        arquivo = c.String(unicode: false, storeType: "text"),
                        tipocarga = c.Int(nullable: false),
                        nrocontroleintegracao = c.String(maxLength: 100, unicode: false),
                        cpfusuario = c.String(maxLength: 11, unicode: false),
                        nomeusuario = c.String(maxLength: 100, unicode: false),
                        idempresa = c.Int(),
                        idfilial = c.Int(),
                        statuscargaavulsa = c.Int(nullable: false),
                        mensagemprocessamento = c.String(maxLength: 500, unicode: false),
                        codigoplanilhaimportada = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idcargaavulsa)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.USUARIO", t => t.idusuariocadastro)
                .Index(t => t.idusuariocadastro, name: "IX_IdUsuariocadastro")
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.TRANSACAO_CARTAO",
                c => new
                    {
                        idtransacaocartao = c.Int(nullable: false, identity: true),
                        lineid = c.Int(nullable: false),
                        datacriacao = c.DateTime(nullable: false),
                        idviagemevento = c.Int(),
                        statuspagamento = c.Int(nullable: false),
                        mensagemprocessamentows = c.String(maxLength: 300, unicode: false),
                        numeroprotocolows = c.Long(nullable: false),
                        tipoprocessamentocartao = c.Int(nullable: false),
                        valormovimentado = c.Decimal(nullable: false, precision: 18, scale: 2),
                        historico = c.Int(nullable: false),
                        cnpjcpforigem = c.String(maxLength: 14, unicode: false),
                        cnpjcpfdestino = c.String(maxLength: 14, unicode: false),
                        origemtransacaocartao = c.Int(nullable: false),
                        idcargaavulsa = c.Int(),
                        idresgate = c.Int(),
                        dataconfirmacaomeiohomologado = c.DateTime(),
                    })
                .PrimaryKey(t => t.idtransacaocartao)
                .ForeignKey("dbo.CARGA_AVULSA", t => t.idcargaavulsa)
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemevento)
                .Index(t => t.idviagemevento, name: "IX_IdViagemEvento")
                .Index(t => t.idcargaavulsa, name: "IX_IdCargaAvulsa");
            
            CreateTable(
                "dbo.VIAGEM_EVENTO",
                c => new
                    {
                        idviagemevento = c.Int(nullable: false, identity: true),
                        numerocontrole = c.String(maxLength: 300, unicode: false),
                        idviagem = c.Int(nullable: false),
                        habilitarpagamentocartao = c.Boolean(nullable: false),
                        modificouformapagamentocartafreteparacartao = c.Boolean(),
                        modificouformapagamentocartaoparacartafrete = c.Boolean(),
                        idempresa = c.Int(nullable: false),
                        idprotocolo = c.Int(),
                        idestabelecimentobase = c.Int(),
                        tipoeventoviagem = c.Int(nullable: false),
                        valorpagamento = c.Decimal(nullable: false, precision: 10, scale: 2),
                        valortotalpagamento = c.Decimal(precision: 10, scale: 2),
                        valortotalpagamentocalculado = c.Decimal(precision: 10, scale: 2),
                        datahorapagamento = c.DateTime(),
                        datahoracancelamento = c.DateTime(),
                        quebramercadoriaabonada = c.Boolean(),
                        datavalidade = c.DateTime(),
                        numerorecibo = c.String(maxLength: 20, unicode: false),
                        instrucao = c.String(maxLength: 700, unicode: false),
                        status = c.Int(nullable: false),
                        origempagamento = c.Int(nullable: false),
                        token = c.String(maxLength: 100, unicode: false),
                        idmotivo = c.Int(),
                        motivobloqueio = c.String(maxLength: 100, unicode: false),
                        irrpf = c.Decimal(nullable: false, precision: 10, scale: 2),
                        inss = c.Decimal(nullable: false, precision: 10, scale: 2),
                        sestsenat = c.Decimal(nullable: false, precision: 10, scale: 2),
                        valorbruto = c.Decimal(nullable: false, precision: 18, scale: 2),
                        idestablibsemchave = c.Int(),
                        idusuariolibsemchave = c.Int(),
                        idmotivorejeicaoabono = c.Int(),
                        descricaorejeicaoabono = c.String(maxLength: 100, unicode: false),
                        datahorarejeicaoabono = c.DateTime(),
                        idusuariorejeicaoabono = c.Int(),
                        datalibsemchave = c.DateTime(),
                        datavalidadechavetoken = c.DateTime(),
                        liberarpagtosemchave = c.Boolean(nullable: false),
                        obslibsemchave = c.String(maxLength: 100, unicode: false),
                        chavetoken = c.String(maxLength: 100, unicode: false),
                        tokenanexoabono = c.String(maxLength: 100, unicode: false),
                        idviagemeventoorigem = c.Int(),
                        idusuariobaixacheque = c.Int(),
                        databaixacheque = c.DateTime(),
                        statusbaixacheque = c.Int(),
                        idpagamentochequeagrupador = c.Int(),
                        cpfusuario = c.String(maxLength: 14, unicode: false),
                        nomeusuario = c.String(maxLength: 100, unicode: false),
                        idusuariobaixaevento = c.Int(),
                        usuariobaixacheque_idusuario = c.Int(),
                    })
                .PrimaryKey(t => t.idviagemevento)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivorejeicaoabono)
                .ForeignKey("dbo.PROTOCOLO", t => t.idprotocolo)
                .ForeignKey("dbo.USUARIO", t => t.usuariobaixacheque_idusuario)
                .ForeignKey("dbo.USUARIO", t => t.idusuariobaixaevento)
                .ForeignKey("dbo.USUARIO", t => t.idusuariolibsemchave)
                .ForeignKey("dbo.USUARIO", t => t.idusuariorejeicaoabono)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemeventoorigem)
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa")
                .Index(t => t.idprotocolo, name: "IX_IdProtocolo")
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase")
                .Index(t => t.idmotivo, name: "IX_IdMotivo")
                .Index(t => t.idusuariolibsemchave, name: "IX_IdUsuarioLibSemChave")
                .Index(t => t.idmotivorejeicaoabono, name: "IX_IdMotivoRejeicaoAbono")
                .Index(t => t.idusuariorejeicaoabono, name: "IX_IdUsuarioRejeicaoAbono")
                .Index(t => t.idviagemeventoorigem, name: "IX_IdViagemEventoOrigem")
                .Index(t => t.idusuariobaixaevento, name: "IX_IdUsuarioBaixaEvento")
                .Index(t => t.usuariobaixacheque_idusuario, name: "IX_UsuarioBaixaCheque_IdUsuario");
            
            CreateTable(
                "dbo.PROTOCOLO",
                c => new
                    {
                        idprotocolo = c.Int(nullable: false, identity: true),
                        idestabelecimentobase = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        valorprotocolo = c.Decimal(nullable: false, precision: 10, scale: 2),
                        datageracao = c.DateTime(nullable: false),
                        datapagamento = c.DateTime(),
                        dataaprovacao = c.DateTime(),
                        dataprevisaopagamento = c.DateTime(),
                        datarejeicao = c.DateTime(),
                        datatransito = c.DateTime(),
                        codigorastreamento = c.String(maxLength: 100, unicode: false),
                        datarecebidoempresa = c.DateTime(),
                        estabpagamentoantecipado = c.Boolean(nullable: false),
                        ocorrenciapendente = c.Boolean(nullable: false),
                        geradoassociacao = c.Boolean(nullable: false),
                        processado = c.Boolean(nullable: false),
                        tipodestinatario = c.Int(nullable: false),
                        statusprotocolo = c.Int(nullable: false),
                        idestabelecimentodestinatario = c.Int(),
                        idempresadestinatario = c.Int(),
                        idusuarioaprovacao = c.Int(),
                        tiposeventos = c.String(maxLength: 100, unicode: false),
                        tipospagamentos = c.Int(nullable: false),
                        possuieventoreincidente = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idprotocolo)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.EMPRESA", t => t.idempresadestinatario)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimentodestinatario)
                .ForeignKey("dbo.USUARIO", t => t.idusuarioaprovacao)
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase")
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idestabelecimentodestinatario, name: "IX_IdEstabelecimentoDestinatario")
                .Index(t => t.idempresadestinatario, name: "IX_IdEmpresaDestinatario")
                .Index(t => t.idusuarioaprovacao, name: "IX_IdUsuarioAprovacao");
            
            CreateTable(
                "dbo.PROTOCOLO_ANEXO",
                c => new
                    {
                        idprotocoloanexo = c.Int(nullable: false, identity: true),
                        idprotocolo = c.Int(nullable: false),
                        iddocumento = c.Int(nullable: false),
                        token = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idprotocoloanexo)
                .ForeignKey("dbo.DOCUMENTO", t => t.iddocumento)
                .ForeignKey("dbo.PROTOCOLO", t => t.idprotocolo)
                .Index(t => t.idprotocolo, name: "IX_IdProtocolo")
                .Index(t => t.iddocumento, name: "IX_IdDocumento");
            
            CreateTable(
                "dbo.PROTOCOLO_ANTECIPACAO",
                c => new
                    {
                        idprotocoloantecipacao = c.Int(nullable: false, identity: true),
                        idprotocolo = c.Int(nullable: false),
                        status = c.Int(nullable: false),
                        datasolicitacao = c.DateTime(nullable: false),
                        valorpagamentoantecipado = c.Decimal(nullable: false, precision: 10, scale: 2),
                        datapagamentoantecipado = c.DateTime(nullable: false),
                        taxapagamentoantecipado = c.Decimal(nullable: false, precision: 18, scale: 2),
                        idmotivo = c.Int(),
                        detalhamento = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idprotocoloantecipacao)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .ForeignKey("dbo.PROTOCOLO", t => t.idprotocolo)
                .Index(t => t.idprotocolo, name: "IX_IdProtocolo")
                .Index(t => t.idmotivo, name: "IX_IdMotivo");
            
            CreateTable(
                "dbo.PROTOCOLO_EVENTO",
                c => new
                    {
                        idprotocoloevento = c.Int(nullable: false, identity: true),
                        idprotocolo = c.Int(nullable: false),
                        idviagemevento = c.Int(nullable: false),
                        idmotivo = c.Int(),
                        detalhamento = c.String(maxLength: 100, unicode: false),
                        ocorrenciapendente = c.Boolean(nullable: false),
                        dataocorrencia = c.DateTime(),
                        eventoanalisado = c.Boolean(nullable: false),
                        status = c.Int(nullable: false),
                        valortotalpagamento = c.Decimal(precision: 18, scale: 2),
                        valorpagamento = c.Decimal(precision: 18, scale: 2),
                        irrpf = c.Decimal(precision: 18, scale: 2),
                        inss = c.Decimal(precision: 18, scale: 2),
                        sestsenat = c.Decimal(precision: 18, scale: 2),
                        valorbruto = c.Decimal(precision: 18, scale: 2),
                        numerorecibo = c.String(maxLength: 100, unicode: false),
                        pesochegada = c.Decimal(precision: 10, scale: 3),
                        valordiffretemotorista = c.Decimal(precision: 18, scale: 2),
                        valorquebramercadoria = c.Decimal(precision: 18, scale: 2),
                        valordesconto = c.Decimal(precision: 18, scale: 2),
                        idmotivodesconto = c.Int(),
                        idmotivoocorrencia = c.Int(),
                        descricaomotivodesconto = c.String(maxLength: 100, unicode: false),
                        detalhamentoocorrencia = c.String(maxLength: 100, unicode: false),
                        observacaodesconto = c.String(maxLength: 100, unicode: false),
                        idprotocoloevento_vinculado = c.Int(),
                        idprotocoloorigem = c.Int(),
                        tokenanexodesconto = c.String(maxLength: 100, unicode: false),
                        analiseabono = c.Int(),
                        datahoraanaliseabono = c.DateTime(),
                        idusuarioanaliseabono = c.Int(),
                        eventoreincidente = c.Boolean(nullable: false),
                        idusuariocadocorrencia = c.Int(),
                        idusuariobaixaocorrencia = c.Int(),
                        databaixaocorrencia = c.DateTime(),
                    })
                .PrimaryKey(t => t.idprotocoloevento)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivodesconto)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivoocorrencia)
                .ForeignKey("dbo.PROTOCOLO", t => t.idprotocolo)
                .ForeignKey("dbo.PROTOCOLO_EVENTO", t => t.idprotocoloevento_vinculado)
                .ForeignKey("dbo.USUARIO", t => t.idusuarioanaliseabono)
                .ForeignKey("dbo.USUARIO", t => t.idusuariocadocorrencia)
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemevento)
                .Index(t => t.idprotocolo, name: "IX_IdProtocolo")
                .Index(t => t.idviagemevento, name: "IX_IdViagemEvento")
                .Index(t => t.idmotivo, name: "IX_IdMotivo")
                .Index(t => t.idmotivodesconto, name: "IX_IdMotivoDesconto")
                .Index(t => t.idmotivoocorrencia, name: "IX_IdMotivoOcorrencia")
                .Index(t => t.idprotocoloevento_vinculado, name: "IX_IdProtocoloEvento_Vinculado")
                .Index(t => t.idusuarioanaliseabono, name: "IX_IdUsuarioAnaliseAbono")
                .Index(t => t.idusuariocadocorrencia, name: "IX_IdUsuarioCadOcorrencia");
            
            CreateTable(
                "dbo.VIAGEM_DOCUMENTO",
                c => new
                    {
                        idviagemdocumento = c.Int(nullable: false, identity: true),
                        idviagemevento = c.Int(nullable: false),
                        iddocumento = c.Int(),
                        tipoevento = c.Int(nullable: false),
                        descricao = c.String(maxLength: 100, unicode: false),
                        tipodocumento = c.Int(nullable: false),
                        numerodocumento = c.Int(nullable: false),
                        obrigaanexo = c.Boolean(nullable: false),
                        obrigaanexomatriz = c.Boolean(nullable: false),
                        obrigaanexofilial = c.Boolean(nullable: false),
                        tokenanexo = c.String(maxLength: 100, unicode: false),
                        obrigadocoriginal = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idviagemdocumento)
                .ForeignKey("dbo.DOCUMENTO", t => t.iddocumento)
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemevento)
                .Index(t => t.idviagemevento, name: "IX_IdViagemEvento")
                .Index(t => t.iddocumento, name: "IX_IdDocumento");
            
            CreateTable(
                "dbo.VIAGEM_EVENTO_PROTOCOLO_ANEXO",
                c => new
                    {
                        idviagemeventoprotocoloanexo = c.Int(nullable: false, identity: true),
                        idviagemevento = c.Int(nullable: false),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        token = c.String(nullable: false, maxLength: 100, unicode: false),
                        tamanhoarquivo = c.String(maxLength: 50, unicode: false),
                    })
                .PrimaryKey(t => t.idviagemeventoprotocoloanexo)
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemevento)
                .Index(t => t.idviagemevento, name: "IX_IdViagemEvento");
            
            CreateTable(
                "dbo.VIAGEM_SOLICITACAO_ABONO",
                c => new
                    {
                        idviagemsolicitacao = c.Int(nullable: false, identity: true),
                        idmotivo = c.Int(),
                        detalhamento = c.String(maxLength: 100, unicode: false),
                        status = c.Int(nullable: false),
                        idviagemevento = c.Int(nullable: false),
                        idusuario = c.Int(),
                        nomeusuario = c.String(maxLength: 100, unicode: false),
                        descricaomotivo = c.String(maxLength: 100, unicode: false),
                        datasolicitacao = c.DateTime(nullable: false),
                        dataatualizacao = c.DateTime(),
                    })
                .PrimaryKey(t => t.idviagemsolicitacao)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemevento)
                .Index(t => t.idmotivo, name: "IX_IdMotivo")
                .Index(t => t.idviagemevento, name: "IX_IdViagemEvento")
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.VIAGEM_VLADICIONAL",
                c => new
                    {
                        idviagemvaloradicional = c.Int(nullable: false, identity: true),
                        idviagemevento = c.Int(nullable: false),
                        numerodocumento = c.Int(nullable: false),
                        tipo = c.Int(nullable: false),
                        descricao = c.String(maxLength: 100, unicode: false),
                        valor = c.Decimal(nullable: false, precision: 10, scale: 2),
                        codigoerp = c.Long(),
                    })
                .PrimaryKey(t => t.idviagemvaloradicional)
                .ForeignKey("dbo.VIAGEM_EVENTO", t => t.idviagemevento)
                .Index(t => t.idviagemevento, name: "IX_IdViagemEvento");
            
            CreateTable(
                "dbo.USUARIO_CLIENTE",
                c => new
                    {
                        idcliente = c.Int(nullable: false),
                        idusuario = c.Int(nullable: false),
                        ischecked = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => new { t.idcliente, t.idusuario })
                .ForeignKey("dbo.CLIENTE", t => t.idcliente)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idcliente, name: "IX_IdCliente")
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.USUARIO_CONTATO",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idcontato = c.Int(nullable: false, identity: true),
                        email = c.String(maxLength: 200, unicode: false),
                        telefone = c.String(maxLength: 20, unicode: false),
                        celular = c.String(maxLength: 20, unicode: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idcontato })
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.MENSAGEM_DESTINATARIO",
                c => new
                    {
                        idmensagemdestinatario = c.Int(nullable: false, identity: true),
                        idusuariodestinatario = c.Int(nullable: false),
                        idmensagem = c.Int(nullable: false),
                        datahoralido = c.DateTime(),
                    })
                .PrimaryKey(t => t.idmensagemdestinatario)
                .ForeignKey("dbo.MENSAGEM", t => t.idmensagem)
                .ForeignKey("dbo.USUARIO", t => t.idusuariodestinatario)
                .Index(t => t.idusuariodestinatario, name: "IX_IdUsuarioDestinatario")
                .Index(t => t.idmensagem, name: "IX_IdMensagem");
            
            CreateTable(
                "dbo.MENSAGEM",
                c => new
                    {
                        idmensagem = c.Int(nullable: false, identity: true),
                        idusuarioremetente = c.Int(nullable: false),
                        assunto = c.String(nullable: false, maxLength: 100, unicode: false),
                        conteudo = c.String(nullable: false, unicode: false, storeType: "text"),
                        conteudomobile = c.String(unicode: false, storeType: "text"),
                        datahoraenvio = c.DateTime(),
                        dataagendada = c.DateTime(nullable: false),
                        recebida = c.Boolean(nullable: false),
                        ativo = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idmensagem)
                .ForeignKey("dbo.USUARIO", t => t.idusuarioremetente)
                .Index(t => t.idusuarioremetente, name: "IX_IdUsuarioRemetente");
            
            CreateTable(
                "dbo.MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO",
                c => new
                    {
                        idgrupousuario = c.Int(nullable: false, identity: true),
                        idmensagem = c.Int(nullable: false),
                        idmensagemgrupousuario = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idgrupousuario)
                .ForeignKey("dbo.MENSAGEM_GRUPO_USUARIO", t => t.idmensagemgrupousuario)
                .ForeignKey("dbo.MENSAGEM", t => t.idmensagem)
                .Index(t => t.idmensagem, name: "IX_IdMensagem")
                .Index(t => t.idmensagemgrupousuario, name: "IX_IdMensagemGrupoUsuario");
            
            CreateTable(
                "dbo.MENSAGEM_GRUPO_USUARIO",
                c => new
                    {
                        idgrupousuario = c.Int(nullable: false, identity: true),
                        nomegrupousuario = c.String(nullable: false, maxLength: 100, unicode: false),
                        idusuario = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idgrupousuario);
            
            CreateTable(
                "dbo.MENSAGEM_GRUPO_DESTINATARIO",
                c => new
                    {
                        idgrupodestinatario = c.Int(nullable: false, identity: true),
                        idusuariodestinatario = c.Int(nullable: false),
                        idgrupousuario = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idgrupodestinatario)
                .ForeignKey("dbo.MENSAGEM_GRUPO_USUARIO", t => t.idgrupousuario)
                .ForeignKey("dbo.USUARIO", t => t.idusuariodestinatario)
                .Index(t => t.idusuariodestinatario, name: "IX_IdUsuarioDestinatario")
                .Index(t => t.idgrupousuario, name: "IX_IdGrupoUsuario");
            
            CreateTable(
                "dbo.USUARIO_DOCUMENTO",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idtipodocumento = c.Int(nullable: false),
                        idusuariodocumento = c.Int(nullable: false, identity: true),
                        validade = c.DateTime(),
                        avisovalidade = c.DateTime(),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idtipodocumento })
                .ForeignKey("dbo.TIPO_DOCUMENTO", t => t.idtipodocumento)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idtipodocumento, name: "IX_IdTipoDocumento");
            
            CreateTable(
                "dbo.TIPO_DOCUMENTO",
                c => new
                    {
                        idtipodocumento = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 450, unicode: false),
                        ativo = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idtipodocumento);
            
            CreateTable(
                "dbo.USUARIO_ENDERECO",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idendereco = c.Int(nullable: false, identity: true),
                        cep = c.String(maxLength: 8, unicode: false),
                        endereco = c.String(maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        bairro = c.String(maxLength: 100, unicode: false),
                        idcidade = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idpais = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idendereco })
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idcidade, name: "IX_IdCidade")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idpais, name: "IX_IdPais");
            
            CreateTable(
                "dbo.USUARIO_FILIAL",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idfilial = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idfilial })
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.USUARIO_HORARIO_CHECKIN",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idhorario = c.Int(nullable: false, identity: true),
                        horario = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idhorario })
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.LOG_SMS",
                c => new
                    {
                        idlogsms = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        celularenvio = c.String(nullable: false, maxLength: 20, unicode: false),
                        texto = c.String(nullable: false, maxLength: 255, unicode: false),
                        processoenvio = c.Int(nullable: false),
                        datahoraenvio = c.DateTime(nullable: false),
                        idusuarioenvio = c.Int(),
                        idpreusuarioenvio = c.Int(),
                        perfil = c.Int(),
                    })
                .PrimaryKey(t => t.idlogsms)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.USUARIO", t => t.idusuarioenvio)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idusuarioenvio, name: "IX_IdUsuarioEnvio");
            
            CreateTable(
                "dbo.USUARIO_PERMISSOES_CONCEDIDAS_MOBILE",
                c => new
                    {
                        idusuariopermissaoconcediadmobile = c.Int(nullable: false, identity: true),
                        idusuario = c.Int(nullable: false),
                        permitelercalendario = c.Boolean(nullable: false),
                        permiteescrevercalendario = c.Boolean(nullable: false),
                        permiteusarcamera = c.Boolean(nullable: false),
                        permitelerperfil = c.Boolean(nullable: false),
                        permitelercontatos = c.Boolean(nullable: false),
                        permiteescrevercontatos = c.Boolean(nullable: false),
                        permitebuscarcontatos = c.Boolean(nullable: false),
                        permiteacessolocalizacao = c.Boolean(nullable: false),
                        permitegravaraudio = c.Boolean(nullable: false),
                        permitelerestadotelefone = c.Boolean(nullable: false),
                        permiterealizarchamada = c.Boolean(nullable: false),
                        permitelerlogchamada = c.Boolean(nullable: false),
                        permiteescreverlogchamada = c.Boolean(nullable: false),
                        permiteadicionarcorreiovoz = c.Boolean(nullable: false),
                        permiteusarsip = c.Boolean(nullable: false),
                        permiteprocessarchamadasaida = c.Boolean(nullable: false),
                        permiteusarsensorescomporais = c.Boolean(nullable: false),
                        permiteenviarsms = c.Boolean(nullable: false),
                        permiterecebersms = c.Boolean(nullable: false),
                        permitelersms = c.Boolean(nullable: false),
                        permitereceberwappush = c.Boolean(nullable: false),
                        permiterecebermms = c.Boolean(nullable: false),
                        permitelerarmazenamentoexterno = c.Boolean(nullable: false),
                        permiteescreverarmazenamentoexterno = c.Boolean(nullable: false),
                        permitereceberconfirmacaoinicio = c.Boolean(nullable: false),
                        permiteignorarotimizacoesbateria = c.Boolean(nullable: false),
                        permiteimpedirbloqueio = c.Boolean(nullable: false),
                        permitejanelasoverlay = c.Boolean(nullable: false),
                        permiteforeground = c.Boolean(nullable: false),
                        permiteinternet = c.Boolean(nullable: false),
                        permiteacessarinformacoesrede = c.Boolean(nullable: false),
                        dataatualizacao = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.idusuariopermissaoconcediadmobile)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.USUARIO_ESTABELECIMENTO",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idestabelecimento = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idestabelecimento })
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimento)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento");
            
            CreateTable(
                "dbo.PAGAMENTO_CHEQUES_AGRUPADOR",
                c => new
                    {
                        idpagamentochequeagrupador = c.Int(nullable: false, identity: true),
                        idusuario = c.Int(nullable: false),
                        datapagamento = c.DateTime(nullable: false),
                        valortotal = c.Decimal(nullable: false, precision: 18, scale: 2),
                        idproprietario = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idpagamentochequeagrupador)
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.idproprietario, t.idempresa })
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => new { t.idproprietario, t.idempresa }, name: "IX_IdProprietario_IdEmpresa");
            
            CreateTable(
                "dbo.PAGAMENTO_CHEQUES",
                c => new
                    {
                        idpagamentocheque = c.Int(nullable: false, identity: true),
                        idpagamentochequeagrupador = c.Int(nullable: false),
                        numerocheque = c.Int(nullable: false),
                        serie = c.String(nullable: false, maxLength: 50, unicode: false),
                        data = c.DateTime(nullable: false),
                        valor = c.Decimal(nullable: false, precision: 18, scale: 2),
                        banco = c.String(nullable: false, maxLength: 150, unicode: false),
                        nominal = c.Int(nullable: false),
                        chequeimpresso = c.Boolean(nullable: false),
                        quantidadeimpressoes = c.Int(),
                        statusintegracaochequetms = c.Int(),
                        dataintegracaotms = c.DateTime(),
                        mensagemretornotms = c.String(maxLength: 1000, unicode: false),
                        status = c.Int(nullable: false),
                        idmotivo = c.Int(),
                        detalhamento = c.String(maxLength: 150, unicode: false),
                        idusuariocancelamento = c.Int(),
                        datacancelamento = c.DateTime(),
                    })
                .PrimaryKey(t => t.idpagamentocheque)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .ForeignKey("dbo.PAGAMENTO_CHEQUES_AGRUPADOR", t => t.idpagamentochequeagrupador)
                .ForeignKey("dbo.USUARIO", t => t.idusuariocancelamento)
                .Index(t => t.idpagamentochequeagrupador, name: "IX_IdPagamentoChequeAgrupador")
                .Index(t => t.idmotivo, name: "IX_IdMotivo")
                .Index(t => t.idusuariocancelamento, name: "IX_IdUsuarioCancelamento");
            
            CreateTable(
                "dbo.PROPRIETARIO",
                c => new
                    {
                        idproprietario = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        cnpjcpf = c.String(nullable: false, maxLength: 14, unicode: false),
                        razaosocial = c.String(nullable: false, maxLength: 100, unicode: false),
                        nomefantasia = c.String(nullable: false, maxLength: 100, unicode: false),
                        rg = c.String(maxLength: 20, unicode: false),
                        rgorgaoexpedidor = c.String(maxLength: 100, unicode: false),
                        ie = c.String(maxLength: 15, unicode: false),
                        rntrc = c.String(nullable: false, maxLength: 100, unicode: false),
                        statusintegracao = c.Int(nullable: false),
                        tipocontrato = c.Int(nullable: false),
                        datanascimento = c.DateTime(),
                        endereco = c.String(maxLength: 100, unicode: false),
                        inss = c.String(maxLength: 100, unicode: false),
                        referencia1 = c.String(maxLength: 100, unicode: false),
                        referencia2 = c.String(maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                        nomemae = c.String(maxLength: 100, unicode: false),
                        nomepai = c.String(maxLength: 100, unicode: false),
                        tipocarregamentofrete = c.Int(nullable: false),
                        percentualtransferenciamotorista = c.Decimal(precision: 18, scale: 2),
                        equiparadotac = c.Boolean(),
                        habilitarcontratociotagregado = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => new { t.idproprietario, t.idempresa })
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => new { t.cnpjcpf, t.ativo, t.idempresa }, unique: true, name: "IX_PROPRIETARIO_CNPJCPF_IDEMPRESA");
            
            CreateTable(
                "dbo.CONJUNTO_CARRETA_EMPRESA",
                c => new
                    {
                        idconjuntocarretaempresa = c.Int(nullable: false, identity: true),
                        idconjuntoempresa = c.Int(nullable: false),
                        idveiculo = c.Int(nullable: false),
                        proprietario_idproprietario = c.Int(),
                        proprietario_idempresa = c.Int(),
                    })
                .PrimaryKey(t => t.idconjuntocarretaempresa)
                .ForeignKey("dbo.CONJUNTO_EMPRESA", t => t.idconjuntoempresa)
                .ForeignKey("dbo.VEICULO", t => t.idveiculo)
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.proprietario_idproprietario, t.proprietario_idempresa })
                .Index(t => t.idconjuntoempresa, name: "IX_IdConjuntoEmpresa")
                .Index(t => t.idveiculo, name: "IX_IdVeiculo")
                .Index(t => new { t.proprietario_idproprietario, t.proprietario_idempresa }, name: "IX_Proprietario_IdProprietario_Proprietario_IdEmpresa");
            
            CreateTable(
                "dbo.PROPRIETARIO_CONTATO",
                c => new
                    {
                        idproprietario = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idcontato = c.Int(nullable: false, identity: true),
                        telefone = c.String(maxLength: 20, unicode: false),
                        celular = c.String(maxLength: 20, unicode: false),
                        email = c.String(maxLength: 200, unicode: false),
                    })
                .PrimaryKey(t => new { t.idproprietario, t.idempresa, t.idcontato })
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.idproprietario, t.idempresa })
                .Index(t => new { t.idproprietario, t.idempresa }, name: "IX_IdProprietario_IdEmpresa");
            
            CreateTable(
                "dbo.PROPRIETARIO_ENDERECO",
                c => new
                    {
                        idproprietario = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idendereco = c.Int(nullable: false, identity: true),
                        cep = c.String(nullable: false, maxLength: 8, unicode: false),
                        endereco = c.String(nullable: false, maxLength: 100, unicode: false),
                        complemento = c.String(maxLength: 100, unicode: false),
                        numero = c.Int(),
                        bairro = c.String(nullable: false, maxLength: 100, unicode: false),
                        idcidade = c.Int(nullable: false),
                        idestado = c.Int(nullable: false),
                        idpais = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idproprietario, t.idempresa, t.idendereco })
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.ESTADO", t => t.idestado)
                .ForeignKey("dbo.PAIS", t => t.idpais)
                .ForeignKey("dbo.PROPRIETARIO", t => new { t.idproprietario, t.idempresa })
                .Index(t => new { t.idproprietario, t.idempresa }, name: "IX_IdProprietario_IdEmpresa")
                .Index(t => t.idcidade, name: "IX_IdCidade")
                .Index(t => t.idestado, name: "IX_IdEstado")
                .Index(t => t.idpais, name: "IX_IdPais");
            
            CreateTable(
                "dbo.USUARIO_PREFERENCIAS",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        campo = c.String(nullable: false, maxLength: 100, unicode: false),
                        valor = c.String(maxLength: 8000, unicode: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.campo })
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.VIAGEM_CHECK",
                c => new
                    {
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idcheck = c.Int(nullable: false, identity: true),
                        idusuario = c.Int(nullable: false),
                        datahora = c.DateTime(nullable: false),
                        status = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idviagem, t.idempresa, t.idcheck })
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa")
                .Index(t => t.idusuario, name: "IX_IdUsuario");
            
            CreateTable(
                "dbo.PAGAMENTO_CONFIGURACAO",
                c => new
                    {
                        idpagamentoconfiguracao = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        valortaxa = c.Double(nullable: false),
                        datacadastro = c.DateTime(nullable: false),
                        dataultimaatualizaca = c.DateTime(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        bonificarmotorista = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idpagamentoconfiguracao)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idfilial, name: "IX_IdFilial");
            
            CreateTable(
                "dbo.PAGAMENTO_CONFIGURACAO_PROCESSO",
                c => new
                    {
                        idconfiguracao = c.Int(nullable: false),
                        iddocumento = c.Int(nullable: false),
                        processo = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idconfiguracao, t.iddocumento, t.processo })
                .ForeignKey("dbo.PAGAMENTO_CONFIGURACAO", t => t.idconfiguracao)
                .ForeignKey("dbo.DOCUMENTO", t => t.iddocumento)
                .Index(t => t.idconfiguracao, name: "IX_IdConfiguracao")
                .Index(t => t.iddocumento, name: "IX_IdDocumento");
            
            CreateTable(
                "dbo.ROTA_ESTABELECIMENTO",
                c => new
                    {
                        idrota = c.Int(nullable: false),
                        idestabelecimento = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idrota, t.idestabelecimento })
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimento)
                .ForeignKey("dbo.ROTA", t => t.idrota)
                .Index(t => t.idrota, name: "IX_IdRota")
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento");
            
            CreateTable(
                "dbo.ROTA",
                c => new
                    {
                        idrota = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        descricao = c.String(maxLength: 100, unicode: false),
                        idcidadeorigem = c.Int(nullable: false),
                        idcidadedestino = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        database = c.DateTime(nullable: false),
                        from = c.String(maxLength: 200, unicode: false),
                        fromlatitude = c.Decimal(nullable: false, precision: 18, scale: 8),
                        fromlongitude = c.Decimal(nullable: false, precision: 18, scale: 8),
                        to = c.String(maxLength: 200, unicode: false),
                        tolatitude = c.Decimal(nullable: false, precision: 18, scale: 8),
                        tolongitude = c.Decimal(nullable: false, precision: 18, scale: 8),
                        totalsegundos = c.Int(nullable: false),
                        totaltempoviagem = c.String(maxLength: 100, unicode: false),
                        totalkm = c.Double(nullable: false),
                    })
                .PrimaryKey(t => t.idrota)
                .ForeignKey("dbo.CIDADE", t => t.idcidadedestino)
                .ForeignKey("dbo.CIDADE", t => t.idcidadeorigem)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idcidadeorigem, name: "IX_IdCidadeOrigem")
                .Index(t => t.idcidadedestino, name: "IX_IdCidadeDestino");
            
            CreateTable(
                "dbo.ROTA_TRAJETO",
                c => new
                    {
                        idtrajeto = c.Int(nullable: false, identity: true),
                        idrota = c.Int(nullable: false),
                        descricao = c.String(maxLength: 1500, unicode: false),
                        latitude = c.Decimal(precision: 18, scale: 8),
                        longitude = c.Decimal(precision: 18, scale: 8),
                        duracaoviagemsegundos = c.Int(nullable: false),
                        duracaoviagem = c.String(maxLength: 200, unicode: false),
                        distanciaviagemmetros = c.Int(nullable: false),
                        distanciaviagem = c.String(maxLength: 200, unicode: false),
                        googleicon = c.String(maxLength: 200, unicode: false),
                    })
                .PrimaryKey(t => new { t.idtrajeto, t.idrota })
                .ForeignKey("dbo.ROTA", t => t.idrota)
                .Index(t => t.idrota, name: "IX_IdRota");
            
            CreateTable(
                "dbo.TIPO_ESTABELECIMENTO",
                c => new
                    {
                        idtipoestabelecimento = c.Int(nullable: false, identity: true),
                        descricao = c.String(maxLength: 100, unicode: false),
                        idicone = c.Int(),
                        dataultimaatualizacao = c.DateTime(),
                        ativo = c.Boolean(nullable: false),
                        idempresa = c.Int(),
                    })
                .PrimaryKey(t => t.idtipoestabelecimento)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.ICONE", t => t.idicone)
                .Index(t => t.idicone, name: "IX_IdIcone")
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.ICONE",
                c => new
                    {
                        idicone = c.Int(nullable: false, identity: true),
                        tokenicone = c.String(maxLength: 100, unicode: false),
                        descricao = c.String(maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                        iconepara = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idicone);
            
            CreateTable(
                "dbo.VIAGEM_ESTABELECIMENTO",
                c => new
                    {
                        idviagemestabelecimento = c.Int(nullable: false, identity: true),
                        idestabelecimento = c.Int(nullable: false),
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        tipoeventoviagem = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idviagemestabelecimento)
                .ForeignKey("dbo.ESTABELECIMENTO", t => t.idestabelecimento)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => t.idestabelecimento, name: "IX_IdEstabelecimento")
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa");
            
            CreateTable(
                "dbo.CLIENTE_PRODUTO_ESPECIE",
                c => new
                    {
                        idclienteprodutoespecie = c.Int(nullable: false, identity: true),
                        idcliente = c.Int(nullable: false),
                        idproduto = c.Int(nullable: false),
                        idespecie = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idclienteprodutoespecie, t.idcliente })
                .ForeignKey("dbo.CLIENTE", t => t.idcliente)
                .ForeignKey("dbo.ESPECIE", t => t.idespecie)
                .ForeignKey("dbo.PRODUTO", t => t.idproduto)
                .Index(t => t.idcliente, name: "IX_IdCliente")
                .Index(t => t.idproduto, name: "IX_IdProduto")
                .Index(t => t.idespecie, name: "IX_IdEspecie");
            
            CreateTable(
                "dbo.ESPECIE",
                c => new
                    {
                        idespecie = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                        datahoraultimaatualizacao = c.DateTime(nullable: false),
                        codigofretebras = c.Int(),
                    })
                .PrimaryKey(t => t.idespecie);
            
            CreateTable(
                "dbo.PRODUTO",
                c => new
                    {
                        idproduto = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idproduto)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.PRODUTO_DADOSCARGA",
                c => new
                    {
                        iddadoscarga = c.Int(nullable: false, identity: true),
                        idproduto = c.Int(),
                        numeropedido = c.String(maxLength: 100, unicode: false),
                        ordemcompra = c.String(maxLength: 100, unicode: false),
                        protocolo = c.String(maxLength: 100, unicode: false),
                        formula = c.String(maxLength: 100, unicode: false),
                        quantidade = c.String(maxLength: 100, unicode: false),
                        armazem = c.String(maxLength: 150, unicode: false),
                    })
                .PrimaryKey(t => t.iddadoscarga)
                .ForeignKey("dbo.PRODUTO", t => t.idproduto)
                .Index(t => t.idproduto, name: "IX_IdProduto");
            
            CreateTable(
                "dbo.TIPO_CAVALO_CLIENTE",
                c => new
                    {
                        idtipocavalo = c.Int(nullable: false),
                        idcliente = c.Int(nullable: false),
                        nome = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => new { t.idtipocavalo, t.idcliente })
                .ForeignKey("dbo.CLIENTE", t => t.idcliente)
                .ForeignKey("dbo.TIPO_CAVALO", t => t.idtipocavalo)
                .Index(t => t.idtipocavalo, name: "IX_IdTipoCavalo")
                .Index(t => t.idcliente, name: "IX_IdCliente");
            
            CreateTable(
                "dbo.TIPO_CAVALO",
                c => new
                    {
                        idtipocavalo = c.Int(nullable: false, identity: true),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                        numeroeixos = c.Int(),
                        categoria = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        idempresa = c.Int(),
                        capacidade = c.Int(),
                        datahoraultimaatualizacao = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.idtipocavalo)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.VIAGEM_CARGA",
                c => new
                    {
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idcarga = c.Int(nullable: false),
                        quantidadecarregada = c.Decimal(nullable: false, precision: 18, scale: 2),
                    })
                .PrimaryKey(t => new { t.idviagem, t.idempresa, t.idcarga })
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa");
            
            CreateTable(
                "dbo.VIAGEM_CARRETA",
                c => new
                    {
                        idviagemcarreta = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        idviagem = c.Int(nullable: false),
                        placa = c.String(maxLength: 10, unicode: false),
                        rntrc = c.String(maxLength: 9, unicode: false),
                    })
                .PrimaryKey(t => new { t.idviagemcarreta, t.idempresa })
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa");
            
            CreateTable(
                "dbo.VIAGEM_DOCUMENTO_FISCAL",
                c => new
                    {
                        idviagemdocumentofiscal = c.Int(nullable: false, identity: true),
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        numerodocumento = c.Decimal(precision: 10, scale: 0),
                        serie = c.String(nullable: false, maxLength: 4, unicode: false),
                        pesosaida = c.Decimal(nullable: false, precision: 10, scale: 3),
                        valor = c.Decimal(precision: 10, scale: 2),
                        tipodocumento = c.Int(nullable: false),
                        idclienteorigem = c.Int(),
                        idclientedestino = c.Int(),
                    })
                .PrimaryKey(t => t.idviagemdocumentofiscal)
                .ForeignKey("dbo.CLIENTE", t => t.idclientedestino)
                .ForeignKey("dbo.CLIENTE", t => t.idclienteorigem)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa")
                .Index(t => t.idclienteorigem, name: "IX_IdClienteOrigem")
                .Index(t => t.idclientedestino, name: "IX_IdClienteDestino");
            
            CreateTable(
                "dbo.VIAGEM_PAGAMENTO_CONTA",
                c => new
                    {
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        cpfcnpjconta = c.String(maxLength: 14, unicode: false),
                        codigobacenbanco = c.String(maxLength: 5, unicode: false),
                        agencia = c.String(maxLength: 6, unicode: false),
                        conta = c.String(maxLength: 20, unicode: false),
                    })
                .PrimaryKey(t => new { t.idviagem, t.idempresa })
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa");
            
            CreateTable(
                "dbo.VIAGEM_REGRA",
                c => new
                    {
                        idviagemregra = c.Int(nullable: false, identity: true),
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        taxaantecipacao = c.Decimal(precision: 8, scale: 4),
                        toleranciapeso = c.Decimal(precision: 8, scale: 4),
                        tarifatonelada = c.Decimal(precision: 10, scale: 2),
                        totalfrete = c.Decimal(precision: 18, scale: 2),
                        tipoquebramercadoria = c.Int(nullable: false),
                        fretelotacao = c.Boolean(nullable: false),
                        unidademedida = c.String(maxLength: 5, unicode: false),
                    })
                .PrimaryKey(t => t.idviagemregra)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa }, name: "IX_IdViagem_IdEmpresa");
            
            CreateTable(
                "dbo.VEICULO_TIPO_COMBUSTIVEL",
                c => new
                    {
                        idveiculo = c.Int(nullable: false),
                        idtipocombustivel = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idveiculo, t.idtipocombustivel })
                .ForeignKey("dbo.TIPO_COMBUSTIVEL", t => t.idtipocombustivel)
                .ForeignKey("dbo.VEICULO", t => t.idveiculo)
                .Index(t => t.idveiculo, name: "IX_IdVeiculo")
                .Index(t => t.idtipocombustivel, name: "IX_IdTipoCombustivel");
            
            CreateTable(
                "dbo.TIPO_COMBUSTIVEL",
                c => new
                    {
                        idtipocombustivel = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        ativo = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idtipocombustivel);
            
            CreateTable(
                "dbo.MOTORISTA_MOVEL",
                c => new
                    {
                        idmotorista = c.Int(nullable: false),
                        idmovel = c.Int(nullable: false, identity: true),
                        imei = c.String(nullable: false, maxLength: 15, unicode: false),
                    })
                .PrimaryKey(t => new { t.idmotorista, t.idmovel })
                .ForeignKey("dbo.MOTORISTA", t => t.idmotorista)
                .Index(t => t.idmotorista, name: "IX_IdMotorista");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_BASE_DOCUMENTOS",
                c => new
                    {
                        idestabelecimentobasedocumento = c.Int(nullable: false, identity: true),
                        idestabelecimentobase = c.Int(nullable: false),
                        iddocumento = c.Int(),
                        token = c.String(maxLength: 100, unicode: false),
                        datavalidade = c.DateTime(),
                        descricao = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idestabelecimentobasedocumento)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase");
            
            CreateTable(
                "dbo.ESTABELECIMENTO_BASE_CONTA_BANCARIA",
                c => new
                    {
                        idestabelecimentobasecontabancaria = c.Int(nullable: false, identity: true),
                        idestabelecimentobase = c.Int(nullable: false),
                        nomeconta = c.String(nullable: false, maxLength: 100, unicode: false),
                        codigobanco = c.String(nullable: false, maxLength: 10, unicode: false),
                        nomebanco = c.String(nullable: false, maxLength: 100, unicode: false),
                        agencia = c.String(nullable: false, maxLength: 10, unicode: false),
                        conta = c.String(nullable: false, maxLength: 30, unicode: false),
                        digitoconta = c.String(maxLength: 10, unicode: false),
                        tipoconta = c.Int(nullable: false),
                        cnpjtitular = c.String(nullable: false, maxLength: 14, unicode: false),
                        nometitular = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idestabelecimentobasecontabancaria)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase");
            
            CreateTable(
                "dbo.LAYOUT",
                c => new
                    {
                        idlayout = c.Int(nullable: false, identity: true),
                        href = c.String(maxLength: 100, unicode: false),
                        css = c.String(maxLength: 2000, unicode: false),
                        image = c.String(maxLength: 100, unicode: false),
                        background = c.String(maxLength: 100, unicode: false),
                        idempresa = c.Int(),
                        idgrupousuariomotorista = c.Int(),
                        idgrupousuarioproprietario = c.Int(),
                        favicon = c.String(maxLength: 100, unicode: false),
                        htmltitle = c.String(maxLength: 100, unicode: false),
                        logotitle = c.String(maxLength: 100, unicode: false),
                        mailfootercompanyname = c.String(maxLength: 100, unicode: false),
                        nomeaplicativo = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idlayout)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.GRUPO_USUARIO", t => t.idgrupousuariomotorista)
                .ForeignKey("dbo.GRUPO_USUARIO", t => t.idgrupousuarioproprietario)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idgrupousuariomotorista, name: "IX_IdGrupoUsuarioMotorista")
                .Index(t => t.idgrupousuarioproprietario, name: "IX_IdGrupoUsuarioProprietario");
            
            CreateTable(
                "dbo.MODULO_MENU",
                c => new
                    {
                        idmodulo = c.Int(nullable: false),
                        idmenu = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idmodulo, t.idmenu })
                .ForeignKey("dbo.MENU", t => t.idmenu)
                .ForeignKey("dbo.MODULO", t => t.idmodulo)
                .Index(t => t.idmodulo, name: "IX_IdModulo")
                .Index(t => t.idmenu, name: "IX_IdMenu");
            
            CreateTable(
                "dbo.MODULO",
                c => new
                    {
                        idmodulo = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        sequencia = c.Int(nullable: false),
                        ativo = c.Boolean(nullable: false),
                        classicon = c.String(maxLength: 100, unicode: false),
                        codigo = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idmodulo);
            
            CreateTable(
                "dbo.EMPRESA_CONTABANCARIA",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        idempresa = c.Int(nullable: false),
                        nomeconta = c.String(nullable: false, maxLength: 100, unicode: false),
                        codigobacenbanco = c.String(nullable: false, maxLength: 10, unicode: false),
                        agencia = c.String(nullable: false, maxLength: 10, unicode: false),
                        conta = c.String(nullable: false, maxLength: 30, unicode: false),
                        digitoconta = c.Int(),
                        tipoconta = c.Int(),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.EMPRESA_LAYOUT",
                c => new
                    {
                        idempresa = c.Int(nullable: false),
                        corfundo = c.String(maxLength: 100, unicode: false),
                        cortexto = c.String(maxLength: 100, unicode: false),
                        corguia = c.String(maxLength: 100, unicode: false),
                        corguiatexto = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idempresa)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.LAYOUT_CARTAO",
                c => new
                    {
                        idlayout = c.Int(nullable: false),
                        nome = c.String(nullable: false, maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.idlayout);
            
            CreateTable(
                "dbo.LAYOUT_CARTAO_ITEM",
                c => new
                    {
                        idlayout = c.Int(nullable: false),
                        key = c.String(nullable: false, maxLength: 100, unicode: false),
                        itemcartao = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idlayout, t.key })
                .ForeignKey("dbo.LAYOUT_CARTAO", t => t.idlayout)
                .Index(t => t.idlayout, name: "IX_IdLayout");
            
            CreateTable(
                "dbo.EMPRESA_MODULO",
                c => new
                    {
                        idempresa = c.Int(nullable: false),
                        idmodulo = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idempresa, t.idmodulo })
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.MODULO", t => t.idmodulo)
                .Index(t => t.idempresa, name: "IX_IdEmpresa")
                .Index(t => t.idmodulo, name: "IX_IdModulo");
            
            CreateTable(
                "dbo.VEICULOS_HISTORICO_EMPRESA",
                c => new
                    {
                        idempresa = c.Int(nullable: false),
                        placa = c.String(nullable: false, maxLength: 6, unicode: false),
                        cpfmotorista = c.String(nullable: false, maxLength: 11, unicode: false),
                    })
                .PrimaryKey(t => new { t.idempresa, t.placa })
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.CREDENCIAMENTO_MOTIVO",
                c => new
                    {
                        idcredenciamento = c.Int(nullable: false),
                        idcredenciamentomotivo = c.Int(nullable: false, identity: true),
                        motivo = c.String(maxLength: 100, unicode: false),
                        datarejeicao = c.DateTime(precision: 7, storeType: "datetime2"),
                    })
                .PrimaryKey(t => new { t.idcredenciamento, t.idcredenciamentomotivo })
                .ForeignKey("dbo.CREDENCIAMENTO", t => t.idcredenciamento)
                .Index(t => t.idcredenciamento, name: "IX_IdCredenciamento");
            
            CreateTable(
                "dbo.MOTIVO_TIPO",
                c => new
                    {
                        idtipomotivo = c.Int(nullable: false, identity: true),
                        idmotivo = c.Int(nullable: false),
                        tipo = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idtipomotivo)
                .ForeignKey("dbo.MOTIVO", t => t.idmotivo)
                .Index(t => t.idmotivo, name: "IX_IdMotivo");
            
            CreateTable(
                "dbo.AuditLog",
                c => new
                    {
                        auditlogid = c.Long(nullable: false, identity: true),
                        username = c.String(maxLength: 100, unicode: false),
                        eventdateutc = c.DateTime(nullable: false),
                        eventtype = c.Int(nullable: false),
                        typefullname = c.String(nullable: false, maxLength: 512, unicode: false),
                        recordid = c.String(nullable: false, maxLength: 256, unicode: false),
                    })
                .PrimaryKey(t => t.auditlogid);
            
            CreateTable(
                "dbo.AuditLogDetail",
                c => new
                    {
                        id = c.Long(nullable: false, identity: true),
                        propertyname = c.String(nullable: false, maxLength: 256, unicode: false),
                        originalvalue = c.String(maxLength: 100, unicode: false),
                        newvalue = c.String(maxLength: 100, unicode: false),
                        auditlogid = c.Long(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.AuditLog", t => t.auditlogid)
                .Index(t => t.auditlogid, name: "IX_AuditLogId");
            
            CreateTable(
                "dbo.BLOQUEIO_GESTOR_TIPO",
                c => new
                    {
                        idbloqueiogestortipo = c.Int(nullable: false),
                        descricao = c.String(maxLength: 100, unicode: false),
                        habilitarporempresa = c.Boolean(nullable: false),
                        habilitarporfilial = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idbloqueiogestortipo);
            
            CreateTable(
                "dbo.BLOQUEIO_GESTOR_VALOR",
                c => new
                    {
                        idbloqueiogestorvalor = c.Int(nullable: false, identity: true),
                        idbloqueiogestortipo = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idfilial = c.Int(),
                        valor = c.Decimal(nullable: false, precision: 18, scale: 2),
                    })
                .PrimaryKey(t => t.idbloqueiogestorvalor)
                .ForeignKey("dbo.BLOQUEIO_GESTOR_TIPO", t => t.idbloqueiogestortipo)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .Index(t => new { t.idbloqueiogestortipo, t.idempresa, t.idfilial }, unique: true, name: "BLOQUEIO_GESTOR_VALOR_UK");
            
            CreateTable(
                "dbo.CONSUMO_SERVICO_EXTERNO",
                c => new
                    {
                        idconsumoservicoexterno = c.Int(nullable: false, identity: true),
                        uri = c.String(maxLength: 500, unicode: false),
                        origemconsumoservicoexterno = c.Int(nullable: false),
                        descricao = c.String(maxLength: 500, unicode: false),
                        dataconsumo = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.idconsumoservicoexterno);
            
            CreateTable(
                "dbo.PARAMETROS",
                c => new
                    {
                        idparametro = c.Int(nullable: false, identity: true),
                        nometabela = c.String(nullable: false, maxLength: 100, unicode: false),
                        idempresa = c.Int(),
                        idregistro = c.Int(),
                        idregistrostr = c.String(maxLength: 100, unicode: false),
                        chave = c.String(nullable: false, maxLength: 100, unicode: false),
                        valorstring = c.String(maxLength: 1000, unicode: false),
                        valordecimal = c.Decimal(precision: 18, scale: 2),
                        valordatetime = c.DateTime(),
                        valorboolean = c.Boolean(),
                    })
                .PrimaryKey(t => t.idparametro)
                .Index(t => new { t.nometabela, t.idempresa, t.idregistro, t.idregistrostr, t.chave }, unique: true, name: "IX_ParametrosUniqueKey");
            
            CreateTable(
                "dbo.PEDAGIO_ROTA",
                c => new
                    {
                        idpedagiorota = c.Int(nullable: false, identity: true),
                        descricao = c.String(nullable: false, maxLength: 300, unicode: false),
                        idempresa = c.Int(nullable: false),
                        datacadastro = c.DateTime(nullable: false),
                        dataatualizacao = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.idpedagiorota)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .Index(t => t.idempresa, name: "IX_IdEmpresa");
            
            CreateTable(
                "dbo.PEDAGIO_ROTA_PONTO",
                c => new
                    {
                        idpedagiorota = c.Int(nullable: false),
                        sequencia = c.Int(nullable: false),
                        latitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        longitude = c.Decimal(nullable: false, precision: 10, scale: 7),
                        idcidade = c.Int(),
                        datacadastro = c.DateTime(nullable: false),
                        dataatualizacao = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => new { t.idpedagiorota, t.sequencia })
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.PEDAGIO_ROTA", t => t.idpedagiorota)
                .Index(t => t.idpedagiorota, name: "IX_IdPedagioRota")
                .Index(t => t.idcidade, name: "IX_IdCidade");
            
            CreateTable(
                "dbo.PIN",
                c => new
                    {
                        idpin = c.Int(nullable: false, identity: true),
                        codigo = c.String(maxLength: 100, unicode: false),
                        celular = c.String(nullable: false, maxLength: 100, unicode: false),
                        datavalidade = c.DateTime(nullable: false),
                        datacriacao = c.DateTime(nullable: false),
                        enviado = c.Boolean(nullable: false),
                        statusenvio = c.Int(nullable: false),
                        dataenvio = c.DateTime(),
                        validado = c.Boolean(nullable: false),
                        datavalidacao = c.DateTime(precision: 7, storeType: "datetime2"),
                        inativo = c.Boolean(nullable: false),
                        idsms = c.String(maxLength: 100, unicode: false),
                        enviadooperadora = c.Boolean(nullable: false),
                        statusoperadora = c.Int(nullable: false),
                        dataenviooperadora = c.DateTime(),
                    })
                .PrimaryKey(t => t.idpin);
            
            CreateTable(
                "dbo.RESGATE_CARTAO_ATENDIMENTO",
                c => new
                    {
                        idresgate = c.Int(nullable: false, identity: true),
                        valor = c.Decimal(nullable: false, precision: 10, scale: 2),
                        cpfportador = c.String(nullable: false, maxLength: 14, unicode: false),
                        cnpjempresa = c.String(nullable: false, maxLength: 14, unicode: false),
                        numerocartao = c.Int(nullable: false),
                        produto = c.Int(nullable: false),
                        motivo = c.String(maxLength: 250, unicode: false),
                        idusuariocadastro = c.Int(nullable: false),
                        datahoracadastro = c.DateTime(nullable: false),
                        statusresgate = c.Int(nullable: false),
                        idusuarioestorno = c.Int(),
                        datahoraestorno = c.DateTime(),
                        motivoestorno = c.String(maxLength: 250, unicode: false),
                    })
                .PrimaryKey(t => t.idresgate);
            
            CreateTable(
                "dbo.USUARIO_PERMISSAO_GESTOR",
                c => new
                    {
                        idusuario = c.Int(nullable: false),
                        idbloqueiogestortipo = c.Int(nullable: false),
                        desbloquearempresa = c.Boolean(nullable: false),
                        desbloquearfilial = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => new { t.idusuario, t.idbloqueiogestortipo })
                .ForeignKey("dbo.BLOQUEIO_GESTOR_TIPO", t => t.idbloqueiogestortipo)
                .ForeignKey("dbo.USUARIO", t => t.idusuario)
                .Index(t => t.idusuario, name: "IX_IdUsuario")
                .Index(t => t.idbloqueiogestortipo, name: "IX_IdBloqueioGestorTipo");
            
            CreateTable(
                "dbo.VEICULO_DIGITOS_MERCOSUL",
                c => new
                    {
                        numeroorigem = c.Short(nullable: false),
                        letradestino = c.String(nullable: false, maxLength: 1, unicode: false),
                    })
                .PrimaryKey(t => t.numeroorigem);
            
            CreateTable(
                "dbo.VIAGEM_PENDENTE_GESTOR",
                c => new
                    {
                        idviagem = c.Int(nullable: false),
                        idempresa = c.Int(nullable: false),
                        idbloqueiogestortipo = c.Int(nullable: false),
                        idfilial = c.Int(),
                        status = c.Int(nullable: false),
                        idusuariodesbloqueio = c.Int(),
                        datacadastro = c.DateTime(nullable: false),
                        datastatus = c.DateTime(),
                        ocorrencia = c.String(maxLength: 300, unicode: false),
                        motivo = c.String(maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => new { t.idviagem, t.idempresa, t.idbloqueiogestortipo })
                .ForeignKey("dbo.BLOQUEIO_GESTOR_TIPO", t => t.idbloqueiogestortipo)
                .ForeignKey("dbo.EMPRESA", t => t.idempresa)
                .ForeignKey("dbo.FILIAL", t => t.idfilial)
                .ForeignKey("dbo.USUARIO", t => t.idusuariodesbloqueio)
                .ForeignKey("dbo.VIAGEM", t => new { t.idviagem, t.idempresa })
                .Index(t => new { t.idviagem, t.idempresa, t.idfilial, t.idbloqueiogestortipo }, unique: true, name: "IX_ViagemPendenteGestorUniqueKey")
                .Index(t => t.idusuariodesbloqueio, name: "IX_IdUsuarioDesbloqueio");
            
            CreateTable(
                "dbo.VIAGEM_VIRTUAL",
                c => new
                    {
                        idviagemvirtual = c.Int(nullable: false, identity: true),
                    })
                .PrimaryKey(t => t.idviagemvirtual);
            
            CreateTable(
                "dbo.WebHook",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        tipo = c.Int(nullable: false),
                        idregistro = c.Int(nullable: false),
                        verbo = c.String(nullable: false, maxLength: 30, unicode: false),
                        headers = c.String(nullable: false, maxLength: 2000, unicode: false),
                        endpoint = c.String(nullable: false, maxLength: 2000, unicode: false),
                        tempo = c.Int(),
                        aplicacao = c.String(nullable: false, maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.VIAGEM_ROTA",
                c => new
                    {
                        idviagemrota = c.Int(nullable: false, identity: true),
                        idviagem = c.Int(nullable: false),
                        fornecedorpedagio = c.Int(nullable: false),
                        tipoveiculo = c.Int(nullable: false),
                        identificadorhistorico = c.Guid(),
                    })
                .PrimaryKey(t => t.idviagemrota);
            
            CreateTable(
                "dbo.VIAGEM_ROTA_PONTO",
                c => new
                    {
                        idviagemrotaponto = c.Int(nullable: false, identity: true),
                        idviagemrota = c.Int(nullable: false),
                        sequencia = c.Int(nullable: false),
                        latitude = c.Decimal(precision: 10, scale: 7),
                        longitude = c.Decimal(precision: 10, scale: 7),
                        idcidade = c.Int(),
                    })
                .PrimaryKey(t => t.idviagemrotaponto)
                .ForeignKey("dbo.CIDADE", t => t.idcidade)
                .ForeignKey("dbo.VIAGEM_ROTA", t => t.idviagemrota)
                .Index(t => t.idviagemrota, name: "IX_IdViagemRota")
                .Index(t => t.idcidade, name: "IX_IdCidade");
            
            CreateTable(
                "dbo.ADMINISTRADORA_PLATAFORMA",
                c => new
                    {
                        idadministradoraplataforma = c.Int(nullable: false),
                        nome = c.String(maxLength: 100, unicode: false),
                        idadministradorameiohomologado = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.idadministradoraplataforma);
            
            CreateTable(
                "dbo.CTE",
                c => new
                    {
                        idcte = c.Int(nullable: false, identity: true),
                        cpfmotorista = c.String(maxLength: 11, unicode: false),
                        dataalteracao = c.DateTime(),
                        status = c.Int(nullable: false),
                        xmlcte = c.String(storeType: "ntext"),
                    })
                .PrimaryKey(t => t.idcte);
            
            CreateTable(
                "dbo.COMBUSTIVEL_JSL",
                c => new
                    {
                        idcombustiveljsl = c.Int(nullable: false),
                        descricao = c.String(nullable: false, maxLength: 100, unicode: false),
                        sincronizarats = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.idcombustiveljsl);
            
            CreateTable(
                "dbo.COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE",
                c => new
                    {
                        idcombustiveljsl = c.Int(nullable: false),
                        idestabelecimentobase = c.Int(nullable: false),
                        idproduto = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idcombustiveljsl, t.idestabelecimentobase })
                .ForeignKey("dbo.COMBUSTIVEL_JSL", t => t.idcombustiveljsl)
                .ForeignKey("dbo.ESTABELECIMENTO_BASE", t => t.idestabelecimentobase)
                .Index(t => t.idcombustiveljsl, name: "IX_IdCombustivelJSL")
                .Index(t => t.idestabelecimentobase, name: "IX_IdEstabelecimentoBase");
            
            CreateTable(
                "dbo.VEICULO_CONJUNTO",
                c => new
                    {
                        idveiculo = c.Int(nullable: false),
                        idveiculoconjunto = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.idveiculo, t.idveiculoconjunto })
                .ForeignKey("dbo.VEICULO", t => t.idveiculo)
                .ForeignKey("dbo.VEICULO", t => t.idveiculoconjunto)
                .Index(t => t.idveiculo, name: "IX_IdVeiculo")
                .Index(t => t.idveiculoconjunto, name: "IX_IdVeiculoConjunto");
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE", "idcombustiveljsl", "dbo.COMBUSTIVEL_JSL");
            DropForeignKey("dbo.VIAGEM_ROTA_PONTO", "idviagemrota", "dbo.VIAGEM_ROTA");
            DropForeignKey("dbo.VIAGEM_ROTA_PONTO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.VIAGEM_PENDENTE_GESTOR", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_PENDENTE_GESTOR", "idusuariodesbloqueio", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_PENDENTE_GESTOR", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.VIAGEM_PENDENTE_GESTOR", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.VIAGEM_PENDENTE_GESTOR", "idbloqueiogestortipo", "dbo.BLOQUEIO_GESTOR_TIPO");
            DropForeignKey("dbo.USUARIO_PERMISSAO_GESTOR", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_PERMISSAO_GESTOR", "idbloqueiogestortipo", "dbo.BLOQUEIO_GESTOR_TIPO");
            DropForeignKey("dbo.PEDAGIO_ROTA_PONTO", "idpedagiorota", "dbo.PEDAGIO_ROTA");
            DropForeignKey("dbo.PEDAGIO_ROTA_PONTO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.PEDAGIO_ROTA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.BLOQUEIO_GESTOR_VALOR", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.BLOQUEIO_GESTOR_VALOR", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.BLOQUEIO_GESTOR_VALOR", "idbloqueiogestortipo", "dbo.BLOQUEIO_GESTOR_TIPO");
            DropForeignKey("dbo.AuditLogDetail", "auditlogid", "dbo.AuditLog");
            DropForeignKey("dbo.ATENDIMENTO_PORTADOR", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.ATENDIMENTO_PORTADOR", "idmotivofinalizacaoatendimento", "dbo.MOTIVO");
            DropForeignKey("dbo.ATENDIMENTO_PORTADOR", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.ATENDIMENTO_PORTADOR_TRAMITE", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.MOTIVO_TIPO", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.MOTIVO", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.MOTIVO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CREDENCIAMENTO", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.CREDENCIAMENTO", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.CREDENCIAMENTO", "idestabelecimento", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.CREDENCIAMENTO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CREDENCIAMENTO_MOTIVO", "idcredenciamento", "dbo.CREDENCIAMENTO");
            DropForeignKey("dbo.CREDENCIAMENTO_ANEXO", "iddocumento", "dbo.DOCUMENTO");
            DropForeignKey("dbo.DOCUMENTO", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.DOCUMENTO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.VEICULOS_HISTORICO_EMPRESA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.EMPRESA", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.EMPRESA", "idocremocaofila", "dbo.MOTIVO");
            DropForeignKey("dbo.EMPRESA", "idoccancviagem", "dbo.MOTIVO");
            DropForeignKey("dbo.EMPRESA", "idoccancgr", "dbo.MOTIVO");
            DropForeignKey("dbo.EMPRESA", "idoccanccte", "dbo.MOTIVO");
            DropForeignKey("dbo.EMPRESA_MODULO", "idmodulo", "dbo.MODULO");
            DropForeignKey("dbo.EMPRESA_MODULO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.EMPRESA", "idlayoutcartao", "dbo.LAYOUT_CARTAO");
            DropForeignKey("dbo.LAYOUT_CARTAO_ITEM", "idlayout", "dbo.LAYOUT_CARTAO");
            DropForeignKey("dbo.EMPRESA_LAYOUT", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.EMPRESA", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.EMPRESA", "empresacontabancaria_id", "dbo.EMPRESA_CONTABANCARIA");
            DropForeignKey("dbo.EMPRESA", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.MODULO_MENU", "idmodulo", "dbo.MODULO");
            DropForeignKey("dbo.MENU", "modulo_idmodulo", "dbo.MODULO");
            DropForeignKey("dbo.MODULO_MENU", "idmenu", "dbo.MENU");
            DropForeignKey("dbo.MENU", "idmenupai", "dbo.MENU");
            DropForeignKey("dbo.GRUPO_USUARIO_MENU", "idmenu", "dbo.MENU");
            DropForeignKey("dbo.NOTIFICACAO_PUSH", "grupousuario_idgrupousuario", "dbo.GRUPO_USUARIO");
            DropForeignKey("dbo.GRUPO_USUARIO_MENU", "idgrupousuario", "dbo.GRUPO_USUARIO");
            DropForeignKey("dbo.LAYOUT", "idgrupousuarioproprietario", "dbo.GRUPO_USUARIO");
            DropForeignKey("dbo.LAYOUT", "idgrupousuariomotorista", "dbo.GRUPO_USUARIO");
            DropForeignKey("dbo.LAYOUT", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.GRUPO_USUARIO", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE", "idtipoestabelecimento", "dbo.TIPO_ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE_CONTA_BANCARIA", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE_DOCUMENTOS", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.CIDADE", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.CHECKIN_RESUMO", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.CHECKIN_RESUMO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.CHECKIN_RESUMO", "idmotorista", "dbo.MOTORISTA");
            DropForeignKey("dbo.CHECKIN_RESUMO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CHECKIN_RESUMO", "idcidadecheckin", "dbo.CIDADE");
            DropForeignKey("dbo.CHECKIN_RESUMO", "idcheckin", "dbo.CHECKIN");
            DropForeignKey("dbo.CHECKIN", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.CHECKIN", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.CHECKIN", "idmotorista", "dbo.MOTORISTA");
            DropForeignKey("dbo.MOTORISTA", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.MOTORISTA_MOVEL", "idmotorista", "dbo.MOTORISTA");
            DropForeignKey("dbo.MOTORISTA", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.MOTORISTA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CONJUNTO_EMPRESA", "idveiculo", "dbo.VEICULO");
            DropForeignKey("dbo.CONJUNTO_EMPRESA", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.CONJUNTO_EMPRESA", "idmotorista", "dbo.MOTORISTA");
            DropForeignKey("dbo.CONJUNTO_EMPRESA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CONJUNTO_EMPRESA", "idconjunto", "dbo.CONJUNTO");
            DropForeignKey("dbo.CONJUNTO", "idtipocavalo", "dbo.TIPO_CAVALO");
            DropForeignKey("dbo.CONJUNTO_CARRETA", "idtipocarreta", "dbo.TIPO_CARRETA");
            DropForeignKey("dbo.VEICULO_CONJUNTO", "idveiculoconjunto", "dbo.VEICULO");
            DropForeignKey("dbo.VEICULO_CONJUNTO", "idveiculo", "dbo.VEICULO");
            DropForeignKey("dbo.VEICULO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.VEICULO_TIPO_COMBUSTIVEL", "idveiculo", "dbo.VEICULO");
            DropForeignKey("dbo.VEICULO_TIPO_COMBUSTIVEL", "idtipocombustivel", "dbo.TIPO_COMBUSTIVEL");
            DropForeignKey("dbo.VEICULO", "idtipocavalo", "dbo.TIPO_CAVALO");
            DropForeignKey("dbo.VEICULO", "idtipocarreta", "dbo.TIPO_CARRETA");
            DropForeignKey("dbo.VEICULO", new[] { "idproprietario", "idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.VEICULO", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.VEICULO", "idmotorista", "dbo.MOTORISTA");
            DropForeignKey("dbo.VEICULO", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.VEICULO", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.VEICULO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CONTRATO_CIOT_AGREGADO_VEICULO", "idveiculo", "dbo.VEICULO");
            DropForeignKey("dbo.CONTRATO_CIOT_AGREGADO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.CONTRATO_CIOT_AGREGADO", new[] { "idproprietario", "idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.CONTRATO_CIOT_AGREGADO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CONTRATO_CIOT_AGREGADO", "iddeclaracaociot", "dbo.DECLARACAO_CIOT");
            DropForeignKey("dbo.DECLARACAO_CIOT", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_REGRA", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_PAGAMENTO_CONTA", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_DOCUMENTO_FISCAL", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_DOCUMENTO_FISCAL", "idclienteorigem", "dbo.CLIENTE");
            DropForeignKey("dbo.VIAGEM_DOCUMENTO_FISCAL", "idclientedestino", "dbo.CLIENTE");
            DropForeignKey("dbo.VIAGEM", new[] { "idviagemcomplementada", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_CARRETA", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_CARGA", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.VIAGEM", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.VIAGEM", "iddeclaracaociot", "dbo.DECLARACAO_CIOT");
            DropForeignKey("dbo.VIAGEM", "idclientetomador", "dbo.CLIENTE");
            DropForeignKey("dbo.VIAGEM", "idclienteorigem", "dbo.CLIENTE");
            DropForeignKey("dbo.VIAGEM", "idclientedestino", "dbo.CLIENTE");
            DropForeignKey("dbo.TIPO_CAVALO_CLIENTE", "idtipocavalo", "dbo.TIPO_CAVALO");
            DropForeignKey("dbo.TIPO_CAVALO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.TIPO_CAVALO_CLIENTE", "idcliente", "dbo.CLIENTE");
            DropForeignKey("dbo.CLIENTE", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.CLIENTE", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.CLIENTE", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CLIENTE_PRODUTO_ESPECIE", "idproduto", "dbo.PRODUTO");
            DropForeignKey("dbo.PRODUTO_DADOSCARGA", "idproduto", "dbo.PRODUTO");
            DropForeignKey("dbo.PRODUTO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CLIENTE_PRODUTO_ESPECIE", "idespecie", "dbo.ESPECIE");
            DropForeignKey("dbo.CLIENTE_PRODUTO_ESPECIE", "idcliente", "dbo.CLIENTE");
            DropForeignKey("dbo.CLIENTE_ENDERECO", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.CLIENTE_ENDERECO", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.ESTADO", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.VIAGEM_ESTABELECIMENTO", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_ESTABELECIMENTO", "idestabelecimento", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO", "idtipoestabelecimento", "dbo.TIPO_ESTABELECIMENTO");
            DropForeignKey("dbo.TIPO_ESTABELECIMENTO", "idicone", "dbo.ICONE");
            DropForeignKey("dbo.TIPO_ESTABELECIMENTO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.ROTA_ESTABELECIMENTO", "idrota", "dbo.ROTA");
            DropForeignKey("dbo.ROTA_TRAJETO", "idrota", "dbo.ROTA");
            DropForeignKey("dbo.ROTA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.ROTA", "idcidadeorigem", "dbo.CIDADE");
            DropForeignKey("dbo.ROTA", "idcidadedestino", "dbo.CIDADE");
            DropForeignKey("dbo.ROTA_ESTABELECIMENTO", "idestabelecimento", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.FILIAL", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.PAGAMENTO_CONFIGURACAO_PROCESSO", "iddocumento", "dbo.DOCUMENTO");
            DropForeignKey("dbo.PAGAMENTO_CONFIGURACAO_PROCESSO", "idconfiguracao", "dbo.PAGAMENTO_CONFIGURACAO");
            DropForeignKey("dbo.PAGAMENTO_CONFIGURACAO", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.PAGAMENTO_CONFIGURACAO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.NOTIFICACAO_PUSH", "idtiponotificacao", "dbo.TIPO_NOTIFICACAO");
            DropForeignKey("dbo.NOTIFICACAO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_CHECK", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_CHECK", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_PREFERENCIAS", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.PAGAMENTO_CHEQUES_AGRUPADOR", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.PAGAMENTO_CHEQUES_AGRUPADOR", new[] { "idproprietario", "idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.VIAGEM", new[] { "idproprietario", "idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.PROPRIETARIO_ENDERECO", new[] { "idproprietario", "idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.PROPRIETARIO_ENDERECO", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.PROPRIETARIO_ENDERECO", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.PROPRIETARIO_ENDERECO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.PROPRIETARIO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.PROPRIETARIO_CONTATO", new[] { "idproprietario", "idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.CONJUNTO_CARRETA_EMPRESA", new[] { "proprietario_idproprietario", "proprietario_idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.CONJUNTO_CARRETA_EMPRESA", "idveiculo", "dbo.VEICULO");
            DropForeignKey("dbo.CONJUNTO_CARRETA_EMPRESA", "idconjuntoempresa", "dbo.CONJUNTO_EMPRESA");
            DropForeignKey("dbo.CONJUNTO_EMPRESA", new[] { "proprietario_idproprietario", "proprietario_idempresa" }, "dbo.PROPRIETARIO");
            DropForeignKey("dbo.PAGAMENTO_CHEQUES", "idusuariocancelamento", "dbo.USUARIO");
            DropForeignKey("dbo.PAGAMENTO_CHEQUES", "idpagamentochequeagrupador", "dbo.PAGAMENTO_CHEQUES_AGRUPADOR");
            DropForeignKey("dbo.PAGAMENTO_CHEQUES", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.USUARIO_ESTABELECIMENTO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_ESTABELECIMENTO", "idestabelecimento", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.USUARIO_PERMISSOES_CONCEDIDAS_MOBILE", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.MOTORISTA", "idusuariocadastro", "dbo.USUARIO");
            DropForeignKey("dbo.LOG_SMS", "idusuarioenvio", "dbo.USUARIO");
            DropForeignKey("dbo.LOG_SMS", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.USUARIO_HORARIO_CHECKIN", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO", "idgrupousuario", "dbo.GRUPO_USUARIO");
            DropForeignKey("dbo.USUARIO_FILIAL", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_FILIAL", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.USUARIO_ENDERECO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_ENDERECO", "idpais", "dbo.PAIS");
            DropForeignKey("dbo.USUARIO_ENDERECO", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.USUARIO_ENDERECO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.USUARIO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.USUARIO_DOCUMENTO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_DOCUMENTO", "idtipodocumento", "dbo.TIPO_DOCUMENTO");
            DropForeignKey("dbo.MENSAGEM_DESTINATARIO", "idusuariodestinatario", "dbo.USUARIO");
            DropForeignKey("dbo.MENSAGEM_DESTINATARIO", "idmensagem", "dbo.MENSAGEM");
            DropForeignKey("dbo.MENSAGEM", "idusuarioremetente", "dbo.USUARIO");
            DropForeignKey("dbo.MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO", "idmensagem", "dbo.MENSAGEM");
            DropForeignKey("dbo.MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO", "idmensagemgrupousuario", "dbo.MENSAGEM_GRUPO_USUARIO");
            DropForeignKey("dbo.MENSAGEM_GRUPO_DESTINATARIO", "idusuariodestinatario", "dbo.USUARIO");
            DropForeignKey("dbo.MENSAGEM_GRUPO_DESTINATARIO", "idgrupousuario", "dbo.MENSAGEM_GRUPO_USUARIO");
            DropForeignKey("dbo.USUARIO_CONTATO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_CLIENTE", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.USUARIO_CLIENTE", "idcliente", "dbo.CLIENTE");
            DropForeignKey("dbo.CARGA_AVULSA", "idusuariocadastro", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_VLADICIONAL", "idviagemevento", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.VIAGEM_SOLICITACAO_ABONO", "idviagemevento", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.VIAGEM_SOLICITACAO_ABONO", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_SOLICITACAO_ABONO", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.VIAGEM_EVENTO_PROTOCOLO_ANEXO", "idviagemevento", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idviagemeventoorigem", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.VIAGEM_DOCUMENTO", "idviagemevento", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.VIAGEM_DOCUMENTO", "iddocumento", "dbo.DOCUMENTO");
            DropForeignKey("dbo.VIAGEM_EVENTO", new[] { "idviagem", "idempresa" }, "dbo.VIAGEM");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idusuariorejeicaoabono", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idusuariolibsemchave", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idusuariobaixaevento", "dbo.USUARIO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "usuariobaixacheque_idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.TRANSACAO_CARTAO", "idviagemevento", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idprotocolo", "dbo.PROTOCOLO");
            DropForeignKey("dbo.PROTOCOLO", "idusuarioaprovacao", "dbo.USUARIO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idviagemevento", "dbo.VIAGEM_EVENTO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idusuariocadocorrencia", "dbo.USUARIO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idusuarioanaliseabono", "dbo.USUARIO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idprotocoloevento_vinculado", "dbo.PROTOCOLO_EVENTO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idprotocolo", "dbo.PROTOCOLO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idmotivoocorrencia", "dbo.MOTIVO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idmotivodesconto", "dbo.MOTIVO");
            DropForeignKey("dbo.PROTOCOLO_EVENTO", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.PROTOCOLO_ANTECIPACAO", "idprotocolo", "dbo.PROTOCOLO");
            DropForeignKey("dbo.PROTOCOLO_ANTECIPACAO", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.PROTOCOLO_ANEXO", "idprotocolo", "dbo.PROTOCOLO");
            DropForeignKey("dbo.PROTOCOLO_ANEXO", "iddocumento", "dbo.DOCUMENTO");
            DropForeignKey("dbo.PROTOCOLO", "idestabelecimentodestinatario", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.PROTOCOLO", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.PROTOCOLO", "idempresadestinatario", "dbo.EMPRESA");
            DropForeignKey("dbo.PROTOCOLO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idmotivorejeicaoabono", "dbo.MOTIVO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idmotivo", "dbo.MOTIVO");
            DropForeignKey("dbo.VIAGEM_EVENTO", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.TRANSACAO_CARTAO", "idcargaavulsa", "dbo.CARGA_AVULSA");
            DropForeignKey("dbo.CARGA_AVULSA", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.CARGA_AVULSA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.AUTH_SESSION", "idusuario", "dbo.USUARIO");
            DropForeignKey("dbo.NOTIFICACAO", "idtiponotificacao", "dbo.TIPO_NOTIFICACAO");
            DropForeignKey("dbo.TIPO_NOTIFICACAO", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.TIPO_NOTIFICACAO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.NOTIFICACAO_PUSH_GRUPO_USUARIO", "idnotificacaopush", "dbo.NOTIFICACAO_PUSH");
            DropForeignKey("dbo.NOTIFICACAO_PUSH_GRUPO_USUARIO", "idgrupousuario", "dbo.GRUPO_USUARIO");
            DropForeignKey("dbo.NOTIFICACAO_PUSH_ITEM", "idnotificacaopush", "dbo.NOTIFICACAO_PUSH");
            DropForeignKey("dbo.NOTIFICACAO_PUSH", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.NOTIFICACAO_PUSH", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.FILIAL_CONTATOS", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.FILIAL", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.FILIAL", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CONTRATO", "idfilial", "dbo.FILIAL");
            DropForeignKey("dbo.CONTRATO", "idclientepagador", "dbo.CLIENTE");
            DropForeignKey("dbo.FILIAL", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.ESTABELECIMENTO", "idestado", "dbo.ESTADO");
            DropForeignKey("dbo.ESTABELECIMENTO_PRODUTO", new[] { "idestabelecimentobase", "idprodutobase" }, "dbo.ESTABELECIMENTO_BASE_PRODUTO");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE_PRODUTO", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.ESTABELECIMENTO_PRODUTO", "idestabelecimento", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO_CONTA_BANCARIA", "idestabelecimento", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO", "idestabelecimentobase", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.ESTABELECIMENTO_ASSOCIACAO", "idestabelecimento", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO_ASSOCIACAO", "idassociacao", "dbo.ESTABELECIMENTO");
            DropForeignKey("dbo.ESTABELECIMENTO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.ESTABELECIMENTO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.CLIENTE_ENDERECO", "idcliente", "dbo.CLIENTE");
            DropForeignKey("dbo.CLIENTE_ENDERECO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.CLIENTE", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.CLIENTE_ACESSO", "idcliente", "dbo.CLIENTE");
            DropForeignKey("dbo.DECLARACAO_CIOT", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.DECLARACAO_CIOT", "idcontratociotagregado", "dbo.CONTRATO_CIOT_AGREGADO");
            DropForeignKey("dbo.CONTRATO_CIOT_AGREGADO_VEICULO", "idcontratociotagregado", "dbo.CONTRATO_CIOT_AGREGADO");
            DropForeignKey("dbo.CONJUNTO_CARRETA", "veiculo_idveiculo", "dbo.VEICULO");
            DropForeignKey("dbo.VEICULO", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.TIPO_CARRETA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CONJUNTO_CARRETA", "idconjunto", "dbo.CONJUNTO");
            DropForeignKey("dbo.MOTORISTA", "idcidade", "dbo.CIDADE");
            DropForeignKey("dbo.CHECKIN", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CHECKIN", "idcidadecheckin", "dbo.CIDADE");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE_ASSOCIACAO", "idestabelecimento", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.ESTABELECIMENTO_BASE_ASSOCIACAO", "idassociacao", "dbo.ESTABELECIMENTO_BASE");
            DropForeignKey("dbo.GRUPO_USUARIO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.AUTORIZACAO_EMPRESA", "idmenu", "dbo.MENU");
            DropForeignKey("dbo.AUTORIZACAO_EMPRESA", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.AUTENTICACAOAPLICACAO", "idempresa", "dbo.EMPRESA");
            DropForeignKey("dbo.CREDENCIAMENTO_ANEXO", "idcredenciamento", "dbo.CREDENCIAMENTO");
            DropForeignKey("dbo.ATENDIMENTO_PORTADOR_TRAMITE", "idatendimentoportador", "dbo.ATENDIMENTO_PORTADOR");
            DropIndex("dbo.VEICULO_CONJUNTO", "IX_IdVeiculoConjunto");
            DropIndex("dbo.VEICULO_CONJUNTO", "IX_IdVeiculo");
            DropIndex("dbo.COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE", "IX_IdCombustivelJSL");
            DropIndex("dbo.VIAGEM_ROTA_PONTO", "IX_IdCidade");
            DropIndex("dbo.VIAGEM_ROTA_PONTO", "IX_IdViagemRota");
            DropIndex("dbo.VIAGEM_PENDENTE_GESTOR", "IX_IdUsuarioDesbloqueio");
            DropIndex("dbo.VIAGEM_PENDENTE_GESTOR", "IX_ViagemPendenteGestorUniqueKey");
            DropIndex("dbo.USUARIO_PERMISSAO_GESTOR", "IX_IdBloqueioGestorTipo");
            DropIndex("dbo.USUARIO_PERMISSAO_GESTOR", "IX_IdUsuario");
            DropIndex("dbo.PEDAGIO_ROTA_PONTO", "IX_IdCidade");
            DropIndex("dbo.PEDAGIO_ROTA_PONTO", "IX_IdPedagioRota");
            DropIndex("dbo.PEDAGIO_ROTA", "IX_IdEmpresa");
            DropIndex("dbo.PARAMETROS", "IX_ParametrosUniqueKey");
            DropIndex("dbo.BLOQUEIO_GESTOR_VALOR", "BLOQUEIO_GESTOR_VALOR_UK");
            DropIndex("dbo.AuditLogDetail", "IX_AuditLogId");
            DropIndex("dbo.MOTIVO_TIPO", "IX_IdMotivo");
            DropIndex("dbo.CREDENCIAMENTO_MOTIVO", "IX_IdCredenciamento");
            DropIndex("dbo.VEICULOS_HISTORICO_EMPRESA", "IX_IdEmpresa");
            DropIndex("dbo.EMPRESA_MODULO", "IX_IdModulo");
            DropIndex("dbo.EMPRESA_MODULO", "IX_IdEmpresa");
            DropIndex("dbo.LAYOUT_CARTAO_ITEM", "IX_IdLayout");
            DropIndex("dbo.EMPRESA_LAYOUT", "IX_IdEmpresa");
            DropIndex("dbo.MODULO_MENU", "IX_IdMenu");
            DropIndex("dbo.MODULO_MENU", "IX_IdModulo");
            DropIndex("dbo.LAYOUT", "IX_IdGrupoUsuarioProprietario");
            DropIndex("dbo.LAYOUT", "IX_IdGrupoUsuarioMotorista");
            DropIndex("dbo.LAYOUT", "IX_IdEmpresa");
            DropIndex("dbo.ESTABELECIMENTO_BASE_CONTA_BANCARIA", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.ESTABELECIMENTO_BASE_DOCUMENTOS", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.MOTORISTA_MOVEL", "IX_IdMotorista");
            DropIndex("dbo.VEICULO_TIPO_COMBUSTIVEL", "IX_IdTipoCombustivel");
            DropIndex("dbo.VEICULO_TIPO_COMBUSTIVEL", "IX_IdVeiculo");
            DropIndex("dbo.VIAGEM_REGRA", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.VIAGEM_PAGAMENTO_CONTA", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.VIAGEM_DOCUMENTO_FISCAL", "IX_IdClienteDestino");
            DropIndex("dbo.VIAGEM_DOCUMENTO_FISCAL", "IX_IdClienteOrigem");
            DropIndex("dbo.VIAGEM_DOCUMENTO_FISCAL", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.VIAGEM_CARRETA", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.VIAGEM_CARGA", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.TIPO_CAVALO", "IX_IdEmpresa");
            DropIndex("dbo.TIPO_CAVALO_CLIENTE", "IX_IdCliente");
            DropIndex("dbo.TIPO_CAVALO_CLIENTE", "IX_IdTipoCavalo");
            DropIndex("dbo.PRODUTO_DADOSCARGA", "IX_IdProduto");
            DropIndex("dbo.PRODUTO", "IX_IdEmpresa");
            DropIndex("dbo.CLIENTE_PRODUTO_ESPECIE", "IX_IdEspecie");
            DropIndex("dbo.CLIENTE_PRODUTO_ESPECIE", "IX_IdProduto");
            DropIndex("dbo.CLIENTE_PRODUTO_ESPECIE", "IX_IdCliente");
            DropIndex("dbo.VIAGEM_ESTABELECIMENTO", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.VIAGEM_ESTABELECIMENTO", "IX_IdEstabelecimento");
            DropIndex("dbo.TIPO_ESTABELECIMENTO", "IX_IdEmpresa");
            DropIndex("dbo.TIPO_ESTABELECIMENTO", "IX_IdIcone");
            DropIndex("dbo.ROTA_TRAJETO", "IX_IdRota");
            DropIndex("dbo.ROTA", "IX_IdCidadeDestino");
            DropIndex("dbo.ROTA", "IX_IdCidadeOrigem");
            DropIndex("dbo.ROTA", "IX_IdEmpresa");
            DropIndex("dbo.ROTA_ESTABELECIMENTO", "IX_IdEstabelecimento");
            DropIndex("dbo.ROTA_ESTABELECIMENTO", "IX_IdRota");
            DropIndex("dbo.PAGAMENTO_CONFIGURACAO_PROCESSO", "IX_IdDocumento");
            DropIndex("dbo.PAGAMENTO_CONFIGURACAO_PROCESSO", "IX_IdConfiguracao");
            DropIndex("dbo.PAGAMENTO_CONFIGURACAO", "IX_IdFilial");
            DropIndex("dbo.PAGAMENTO_CONFIGURACAO", "IX_IdEmpresa");
            DropIndex("dbo.VIAGEM_CHECK", "IX_IdUsuario");
            DropIndex("dbo.VIAGEM_CHECK", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.USUARIO_PREFERENCIAS", "IX_IdUsuario");
            DropIndex("dbo.PROPRIETARIO_ENDERECO", "IX_IdPais");
            DropIndex("dbo.PROPRIETARIO_ENDERECO", "IX_IdEstado");
            DropIndex("dbo.PROPRIETARIO_ENDERECO", "IX_IdCidade");
            DropIndex("dbo.PROPRIETARIO_ENDERECO", "IX_IdProprietario_IdEmpresa");
            DropIndex("dbo.PROPRIETARIO_CONTATO", "IX_IdProprietario_IdEmpresa");
            DropIndex("dbo.CONJUNTO_CARRETA_EMPRESA", "IX_Proprietario_IdProprietario_Proprietario_IdEmpresa");
            DropIndex("dbo.CONJUNTO_CARRETA_EMPRESA", "IX_IdVeiculo");
            DropIndex("dbo.CONJUNTO_CARRETA_EMPRESA", "IX_IdConjuntoEmpresa");
            DropIndex("dbo.PROPRIETARIO", "IX_PROPRIETARIO_CNPJCPF_IDEMPRESA");
            DropIndex("dbo.PAGAMENTO_CHEQUES", "IX_IdUsuarioCancelamento");
            DropIndex("dbo.PAGAMENTO_CHEQUES", "IX_IdMotivo");
            DropIndex("dbo.PAGAMENTO_CHEQUES", "IX_IdPagamentoChequeAgrupador");
            DropIndex("dbo.PAGAMENTO_CHEQUES_AGRUPADOR", "IX_IdProprietario_IdEmpresa");
            DropIndex("dbo.PAGAMENTO_CHEQUES_AGRUPADOR", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_ESTABELECIMENTO", "IX_IdEstabelecimento");
            DropIndex("dbo.USUARIO_ESTABELECIMENTO", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_PERMISSOES_CONCEDIDAS_MOBILE", "IX_IdUsuario");
            DropIndex("dbo.LOG_SMS", "IX_IdUsuarioEnvio");
            DropIndex("dbo.LOG_SMS", "IX_IdEmpresa");
            DropIndex("dbo.USUARIO_HORARIO_CHECKIN", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_FILIAL", "IX_IdFilial");
            DropIndex("dbo.USUARIO_FILIAL", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_ENDERECO", "IX_IdPais");
            DropIndex("dbo.USUARIO_ENDERECO", "IX_IdEstado");
            DropIndex("dbo.USUARIO_ENDERECO", "IX_IdCidade");
            DropIndex("dbo.USUARIO_ENDERECO", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_DOCUMENTO", "IX_IdTipoDocumento");
            DropIndex("dbo.USUARIO_DOCUMENTO", "IX_IdUsuario");
            DropIndex("dbo.MENSAGEM_GRUPO_DESTINATARIO", "IX_IdGrupoUsuario");
            DropIndex("dbo.MENSAGEM_GRUPO_DESTINATARIO", "IX_IdUsuarioDestinatario");
            DropIndex("dbo.MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO", "IX_IdMensagemGrupoUsuario");
            DropIndex("dbo.MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO", "IX_IdMensagem");
            DropIndex("dbo.MENSAGEM", "IX_IdUsuarioRemetente");
            DropIndex("dbo.MENSAGEM_DESTINATARIO", "IX_IdMensagem");
            DropIndex("dbo.MENSAGEM_DESTINATARIO", "IX_IdUsuarioDestinatario");
            DropIndex("dbo.USUARIO_CONTATO", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_CLIENTE", "IX_IdUsuario");
            DropIndex("dbo.USUARIO_CLIENTE", "IX_IdCliente");
            DropIndex("dbo.VIAGEM_VLADICIONAL", "IX_IdViagemEvento");
            DropIndex("dbo.VIAGEM_SOLICITACAO_ABONO", "IX_IdUsuario");
            DropIndex("dbo.VIAGEM_SOLICITACAO_ABONO", "IX_IdViagemEvento");
            DropIndex("dbo.VIAGEM_SOLICITACAO_ABONO", "IX_IdMotivo");
            DropIndex("dbo.VIAGEM_EVENTO_PROTOCOLO_ANEXO", "IX_IdViagemEvento");
            DropIndex("dbo.VIAGEM_DOCUMENTO", "IX_IdDocumento");
            DropIndex("dbo.VIAGEM_DOCUMENTO", "IX_IdViagemEvento");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdUsuarioCadOcorrencia");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdUsuarioAnaliseAbono");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdProtocoloEvento_Vinculado");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdMotivoOcorrencia");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdMotivoDesconto");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdMotivo");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdViagemEvento");
            DropIndex("dbo.PROTOCOLO_EVENTO", "IX_IdProtocolo");
            DropIndex("dbo.PROTOCOLO_ANTECIPACAO", "IX_IdMotivo");
            DropIndex("dbo.PROTOCOLO_ANTECIPACAO", "IX_IdProtocolo");
            DropIndex("dbo.PROTOCOLO_ANEXO", "IX_IdDocumento");
            DropIndex("dbo.PROTOCOLO_ANEXO", "IX_IdProtocolo");
            DropIndex("dbo.PROTOCOLO", "IX_IdUsuarioAprovacao");
            DropIndex("dbo.PROTOCOLO", "IX_IdEmpresaDestinatario");
            DropIndex("dbo.PROTOCOLO", "IX_IdEstabelecimentoDestinatario");
            DropIndex("dbo.PROTOCOLO", "IX_IdEmpresa");
            DropIndex("dbo.PROTOCOLO", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_UsuarioBaixaCheque_IdUsuario");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdUsuarioBaixaEvento");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdViagemEventoOrigem");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdUsuarioRejeicaoAbono");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdMotivoRejeicaoAbono");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdUsuarioLibSemChave");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdMotivo");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdProtocolo");
            DropIndex("dbo.VIAGEM_EVENTO", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.TRANSACAO_CARTAO", "IX_IdCargaAvulsa");
            DropIndex("dbo.TRANSACAO_CARTAO", "IX_IdViagemEvento");
            DropIndex("dbo.CARGA_AVULSA", "IX_IdFilial");
            DropIndex("dbo.CARGA_AVULSA", "IX_IdEmpresa");
            DropIndex("dbo.CARGA_AVULSA", "IX_IdUsuariocadastro");
            DropIndex("dbo.AUTH_SESSION", "IX_IdUsuario");
            DropIndex("dbo.USUARIO", "IX_IdGrupoUsuario");
            DropIndex("dbo.USUARIO", "IX_USUARIO_CPFCNPJ_ATIVO_IDEMPRESA");
            DropIndex("dbo.NOTIFICACAO", "IX_IdTipoNotificacao");
            DropIndex("dbo.NOTIFICACAO", "IX_IdUsuario");
            DropIndex("dbo.TIPO_NOTIFICACAO", "IX_IdFilial");
            DropIndex("dbo.TIPO_NOTIFICACAO", "IX_IdEmpresa");
            DropIndex("dbo.NOTIFICACAO_PUSH_GRUPO_USUARIO", "IX_IdNotificacaoPush");
            DropIndex("dbo.NOTIFICACAO_PUSH_GRUPO_USUARIO", "IX_IdGrupoUsuario");
            DropIndex("dbo.NOTIFICACAO_PUSH_ITEM", "IX_IdNotificacaoPush");
            DropIndex("dbo.NOTIFICACAO_PUSH", "IX_GrupoUsuario_IdGrupoUsuario");
            DropIndex("dbo.NOTIFICACAO_PUSH", "IX_IdTipoNotificacao");
            DropIndex("dbo.NOTIFICACAO_PUSH", "IX_IdFilial");
            DropIndex("dbo.NOTIFICACAO_PUSH", "IX_IdEmpresa");
            DropIndex("dbo.FILIAL_CONTATOS", "IX_IdFilial");
            DropIndex("dbo.CONTRATO", "IX_IdClientePagador");
            DropIndex("dbo.CONTRATO", "IX_IdFilial");
            DropIndex("dbo.FILIAL", "IX_IdPais");
            DropIndex("dbo.FILIAL", "IX_IdEstado");
            DropIndex("dbo.FILIAL", "IX_IdCidade");
            DropIndex("dbo.FILIAL", "IX_IdEmpresa");
            DropIndex("dbo.ESTABELECIMENTO_BASE_PRODUTO", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.ESTABELECIMENTO_PRODUTO", "IX_IdEstabelecimentoBase_IdProdutoBase");
            DropIndex("dbo.ESTABELECIMENTO_PRODUTO", "IX_IdEstabelecimento");
            DropIndex("dbo.ESTABELECIMENTO_CONTA_BANCARIA", "IX_IdEstabelecimento");
            DropIndex("dbo.ESTABELECIMENTO_ASSOCIACAO", "IX_IdAssociacao");
            DropIndex("dbo.ESTABELECIMENTO_ASSOCIACAO", "IX_IdEstabelecimento");
            DropIndex("dbo.ESTABELECIMENTO", "IX_IdCidade");
            DropIndex("dbo.ESTABELECIMENTO", "IX_IdEstado");
            DropIndex("dbo.ESTABELECIMENTO", "IX_IdPais");
            DropIndex("dbo.ESTABELECIMENTO", "IX_IdTipoEstabelecimento");
            DropIndex("dbo.ESTABELECIMENTO", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.ESTABELECIMENTO", "IX_IdEmpresa");
            DropIndex("dbo.ESTADO", "IX_IdPais");
            DropIndex("dbo.CLIENTE_ENDERECO", "IX_IdCidade");
            DropIndex("dbo.CLIENTE_ENDERECO", "IX_IdEstado");
            DropIndex("dbo.CLIENTE_ENDERECO", "IX_IdPais");
            DropIndex("dbo.CLIENTE_ENDERECO", "IX_IdCliente");
            DropIndex("dbo.CLIENTE_ACESSO", "IX_IdCliente");
            DropIndex("dbo.CLIENTE", "IX_IdCidade");
            DropIndex("dbo.CLIENTE", "IX_IdEstado");
            DropIndex("dbo.CLIENTE", "IX_IdPais");
            DropIndex("dbo.CLIENTE", "IX_CLIENTE_CPFCNPJ_ATIVO_IDEMPRESA");
            DropIndex("dbo.VIAGEM", "IX_IdDeclaracaoCiot");
            DropIndex("dbo.VIAGEM", "IX_IdClienteTomador");
            DropIndex("dbo.VIAGEM", new[] { "idclientedestino" });
            DropIndex("dbo.VIAGEM", new[] { "idclienteorigem" });
            DropIndex("dbo.VIAGEM", "IX_IdFilial");
            DropIndex("dbo.VIAGEM", "IX_IdViagemComplementada_idempresa");
            DropIndex("dbo.VIAGEM", new[] { "idproprietario", "idempresa" });
            DropIndex("dbo.DECLARACAO_CIOT", "IX_IdContratoCiotAgregado");
            DropIndex("dbo.DECLARACAO_CIOT", "Ix_DeclaracaoCIot_Ciot");
            DropIndex("dbo.DECLARACAO_CIOT", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.DECLARACAO_CIOT", "IX_IdEmpresa");
            DropIndex("dbo.CONTRATO_CIOT_AGREGADO", new[] { "idusuario" });
            DropIndex("dbo.CONTRATO_CIOT_AGREGADO", new[] { "iddeclaracaociot" });
            DropIndex("dbo.CONTRATO_CIOT_AGREGADO", "IX_idproprietario_IdEmpresa");
            DropIndex("dbo.CONTRATO_CIOT_AGREGADO", "IX_IdEmpresa");
            DropIndex("dbo.CONTRATO_CIOT_AGREGADO_VEICULO", "IX_IdVeiculo");
            DropIndex("dbo.CONTRATO_CIOT_AGREGADO_VEICULO", "IX_IdContratoCiotAgregado");
            DropIndex("dbo.VEICULO", "IX_IdTipoCarreta");
            DropIndex("dbo.VEICULO", "IX_IdTipoCavalo");
            DropIndex("dbo.VEICULO", "IX_IdCidade");
            DropIndex("dbo.VEICULO", "IX_IdEstado");
            DropIndex("dbo.VEICULO", "IX_IdPais");
            DropIndex("dbo.VEICULO", "IX_IdUsuario");
            DropIndex("dbo.VEICULO", "IX_IdMotorista");
            DropIndex("dbo.VEICULO", "IX_IdFilial");
            DropIndex("dbo.VEICULO", "IX_IdProprietario_IdEmpresa");
            DropIndex("dbo.VEICULO", "IX_IdEmpresa");
            DropIndex("dbo.TIPO_CARRETA", "IX_IdEmpresa");
            DropIndex("dbo.CONJUNTO_CARRETA", "IX_Veiculo_IdVeiculo");
            DropIndex("dbo.CONJUNTO_CARRETA", "IX_IdTipoCarreta");
            DropIndex("dbo.CONJUNTO_CARRETA", "IX_IdConjunto");
            DropIndex("dbo.CONJUNTO", "IX_IdTipoCavalo");
            DropIndex("dbo.CONJUNTO_EMPRESA", "IX_Proprietario_IdProprietario_Proprietario_IdEmpresa");
            DropIndex("dbo.CONJUNTO_EMPRESA", "IX_IdUsuario");
            DropIndex("dbo.CONJUNTO_EMPRESA", "IX_IdMotorista");
            DropIndex("dbo.CONJUNTO_EMPRESA", "IX_IdVeiculo");
            DropIndex("dbo.CONJUNTO_EMPRESA", "IX_IdEmpresa");
            DropIndex("dbo.CONJUNTO_EMPRESA", "IX_IdConjunto");
            DropIndex("dbo.MOTORISTA", "IX_IdUsuarioCadastro");
            DropIndex("dbo.MOTORISTA", "IX_IdCidade");
            DropIndex("dbo.MOTORISTA", "IX_IdEstado");
            DropIndex("dbo.MOTORISTA", "IX_IdPais");
            DropIndex("dbo.MOTORISTA", "IX_MOTORISTA_CPF_ATIVO_IDEMPRESA");
            DropIndex("dbo.CHECKIN", "IX_IdCidadeCheckIn");
            DropIndex("dbo.CHECKIN", "IX_IdEmpresa");
            DropIndex("dbo.CHECKIN", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.CHECKIN", "IX_IdMotorista");
            DropIndex("dbo.CHECKIN", "IX_IdUsuario");
            DropIndex("dbo.CHECKIN_RESUMO", "IX_IdCidadeCheckin");
            DropIndex("dbo.CHECKIN_RESUMO", "IX_IdEmpresa");
            DropIndex("dbo.CHECKIN_RESUMO", "IX_IdViagem_IdEmpresa");
            DropIndex("dbo.CHECKIN_RESUMO", "IX_IdMotorista");
            DropIndex("dbo.CHECKIN_RESUMO", "IX_IdUsuario");
            DropIndex("dbo.CHECKIN_RESUMO", "IX_IdCheckin");
            DropIndex("dbo.CIDADE", "IX_IdEstado");
            DropIndex("dbo.ESTABELECIMENTO_BASE_ASSOCIACAO", "IX_IdAssociacao");
            DropIndex("dbo.ESTABELECIMENTO_BASE_ASSOCIACAO", "IX_IdEstabelecimento");
            DropIndex("dbo.ESTABELECIMENTO_BASE", "IX_IdCidade");
            DropIndex("dbo.ESTABELECIMENTO_BASE", "IX_IdEstado");
            DropIndex("dbo.ESTABELECIMENTO_BASE", "IX_IdPais");
            DropIndex("dbo.ESTABELECIMENTO_BASE", "IX_IdTipoEstabelecimento");
            DropIndex("dbo.ESTABELECIMENTO_BASE", "IX_IdFilial");
            DropIndex("dbo.GRUPO_USUARIO", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.GRUPO_USUARIO", "IX_IdEmpresa");
            DropIndex("dbo.GRUPO_USUARIO_MENU", "IX_IdMenu");
            DropIndex("dbo.GRUPO_USUARIO_MENU", "IX_IdGrupoUsuario");
            DropIndex("dbo.MENU", "IX_Modulo_IdModulo");
            DropIndex("dbo.MENU", "IX_IdMenuPai");
            DropIndex("dbo.AUTORIZACAO_EMPRESA", "IX_IdMenu");
            DropIndex("dbo.AUTORIZACAO_EMPRESA", "IX_IdEmpresa");
            DropIndex("dbo.AUTENTICACAOAPLICACAO", "IX_IdEmpresa");
            DropIndex("dbo.EMPRESA", "IX_EmpresaContaBancaria_Id");
            DropIndex("dbo.EMPRESA", "IX_IdLayoutCartao");
            DropIndex("dbo.EMPRESA", "IX_IdPais");
            DropIndex("dbo.EMPRESA", "IX_IdEstado");
            DropIndex("dbo.EMPRESA", "IX_IdCidade");
            DropIndex("dbo.EMPRESA", "IX_IdOCRemocaoFila");
            DropIndex("dbo.EMPRESA", "IX_IdOCcancViagem");
            DropIndex("dbo.EMPRESA", "IX_IdOCcancCTe");
            DropIndex("dbo.EMPRESA", "IX_IdOCcancGR");
            DropIndex("dbo.DOCUMENTO", "IX_IdFilial");
            DropIndex("dbo.DOCUMENTO", "IX_IdEmpresa");
            DropIndex("dbo.CREDENCIAMENTO_ANEXO", "IX_IdDocumento");
            DropIndex("dbo.CREDENCIAMENTO_ANEXO", "IX_IdCredenciamento");
            DropIndex("dbo.CREDENCIAMENTO", "IX_IdMotivo");
            DropIndex("dbo.CREDENCIAMENTO", "IX_IdEstabelecimentoBase");
            DropIndex("dbo.CREDENCIAMENTO", "IX_IdEstabelecimento");
            DropIndex("dbo.CREDENCIAMENTO", "IX_IdEmpresa");
            DropIndex("dbo.MOTIVO", "IX_IdFilial");
            DropIndex("dbo.MOTIVO", "IX_IdEmpresa");
            DropIndex("dbo.ATENDIMENTO_PORTADOR_TRAMITE", "IX_ATENDIMENTO_PORTADOR_TRAMITE_Tipo_motivo");
            DropIndex("dbo.ATENDIMENTO_PORTADOR_TRAMITE", new[] { "idatendimentoportador" });
            DropIndex("dbo.ATENDIMENTO_PORTADOR", new[] { "idmotivofinalizacaoatendimento" });
            DropIndex("dbo.ATENDIMENTO_PORTADOR", new[] { "idusuario" });
            DropIndex("dbo.ATENDIMENTO_PORTADOR", "IX_IdEmpresa");
            DropTable("dbo.VEICULO_CONJUNTO");
            DropTable("dbo.COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE");
            DropTable("dbo.COMBUSTIVEL_JSL");
            DropTable("dbo.CTE");
            DropTable("dbo.ADMINISTRADORA_PLATAFORMA");
            DropTable("dbo.VIAGEM_ROTA_PONTO");
            DropTable("dbo.VIAGEM_ROTA");
            DropTable("dbo.WebHook");
            DropTable("dbo.VIAGEM_VIRTUAL");
            DropTable("dbo.VIAGEM_PENDENTE_GESTOR");
            DropTable("dbo.VEICULO_DIGITOS_MERCOSUL");
            DropTable("dbo.USUARIO_PERMISSAO_GESTOR");
            DropTable("dbo.RESGATE_CARTAO_ATENDIMENTO");
            DropTable("dbo.PIN");
            DropTable("dbo.PEDAGIO_ROTA_PONTO");
            DropTable("dbo.PEDAGIO_ROTA");
            DropTable("dbo.PARAMETROS");
            DropTable("dbo.CONSUMO_SERVICO_EXTERNO");
            DropTable("dbo.BLOQUEIO_GESTOR_VALOR");
            DropTable("dbo.BLOQUEIO_GESTOR_TIPO");
            DropTable("dbo.AuditLogDetail");
            DropTable("dbo.AuditLog");
            DropTable("dbo.MOTIVO_TIPO");
            DropTable("dbo.CREDENCIAMENTO_MOTIVO");
            DropTable("dbo.VEICULOS_HISTORICO_EMPRESA");
            DropTable("dbo.EMPRESA_MODULO");
            DropTable("dbo.LAYOUT_CARTAO_ITEM");
            DropTable("dbo.LAYOUT_CARTAO");
            DropTable("dbo.EMPRESA_LAYOUT");
            DropTable("dbo.EMPRESA_CONTABANCARIA");
            DropTable("dbo.MODULO");
            DropTable("dbo.MODULO_MENU");
            DropTable("dbo.LAYOUT");
            DropTable("dbo.ESTABELECIMENTO_BASE_CONTA_BANCARIA");
            DropTable("dbo.ESTABELECIMENTO_BASE_DOCUMENTOS");
            DropTable("dbo.MOTORISTA_MOVEL");
            DropTable("dbo.TIPO_COMBUSTIVEL");
            DropTable("dbo.VEICULO_TIPO_COMBUSTIVEL");
            DropTable("dbo.VIAGEM_REGRA");
            DropTable("dbo.VIAGEM_PAGAMENTO_CONTA");
            DropTable("dbo.VIAGEM_DOCUMENTO_FISCAL");
            DropTable("dbo.VIAGEM_CARRETA");
            DropTable("dbo.VIAGEM_CARGA");
            DropTable("dbo.TIPO_CAVALO");
            DropTable("dbo.TIPO_CAVALO_CLIENTE");
            DropTable("dbo.PRODUTO_DADOSCARGA");
            DropTable("dbo.PRODUTO");
            DropTable("dbo.ESPECIE");
            DropTable("dbo.CLIENTE_PRODUTO_ESPECIE");
            DropTable("dbo.VIAGEM_ESTABELECIMENTO");
            DropTable("dbo.ICONE");
            DropTable("dbo.TIPO_ESTABELECIMENTO");
            DropTable("dbo.ROTA_TRAJETO");
            DropTable("dbo.ROTA");
            DropTable("dbo.ROTA_ESTABELECIMENTO");
            DropTable("dbo.PAGAMENTO_CONFIGURACAO_PROCESSO");
            DropTable("dbo.PAGAMENTO_CONFIGURACAO");
            DropTable("dbo.VIAGEM_CHECK");
            DropTable("dbo.USUARIO_PREFERENCIAS");
            DropTable("dbo.PROPRIETARIO_ENDERECO");
            DropTable("dbo.PROPRIETARIO_CONTATO");
            DropTable("dbo.CONJUNTO_CARRETA_EMPRESA");
            DropTable("dbo.PROPRIETARIO");
            DropTable("dbo.PAGAMENTO_CHEQUES");
            DropTable("dbo.PAGAMENTO_CHEQUES_AGRUPADOR");
            DropTable("dbo.USUARIO_ESTABELECIMENTO");
            DropTable("dbo.USUARIO_PERMISSOES_CONCEDIDAS_MOBILE");
            DropTable("dbo.LOG_SMS");
            DropTable("dbo.USUARIO_HORARIO_CHECKIN");
            DropTable("dbo.USUARIO_FILIAL");
            DropTable("dbo.USUARIO_ENDERECO");
            DropTable("dbo.TIPO_DOCUMENTO");
            DropTable("dbo.USUARIO_DOCUMENTO");
            DropTable("dbo.MENSAGEM_GRUPO_DESTINATARIO");
            DropTable("dbo.MENSAGEM_GRUPO_USUARIO");
            DropTable("dbo.MENSAGEM_GRUPOS_USUARIOS_IDENTIFICACAO");
            DropTable("dbo.MENSAGEM");
            DropTable("dbo.MENSAGEM_DESTINATARIO");
            DropTable("dbo.USUARIO_CONTATO");
            DropTable("dbo.USUARIO_CLIENTE");
            DropTable("dbo.VIAGEM_VLADICIONAL");
            DropTable("dbo.VIAGEM_SOLICITACAO_ABONO");
            DropTable("dbo.VIAGEM_EVENTO_PROTOCOLO_ANEXO");
            DropTable("dbo.VIAGEM_DOCUMENTO");
            DropTable("dbo.PROTOCOLO_EVENTO");
            DropTable("dbo.PROTOCOLO_ANTECIPACAO");
            DropTable("dbo.PROTOCOLO_ANEXO");
            DropTable("dbo.PROTOCOLO");
            DropTable("dbo.VIAGEM_EVENTO");
            DropTable("dbo.TRANSACAO_CARTAO");
            DropTable("dbo.CARGA_AVULSA");
            DropTable("dbo.AUTH_SESSION");
            DropTable("dbo.USUARIO");
            DropTable("dbo.NOTIFICACAO",
                removedColumnAnnotations: new Dictionary<string, IDictionary<string, object>>
                {
                    {
                        "tipo",
                        new Dictionary<string, object>
                        {
                            { "defaultValue", "0" },
                        }
                    },
                });
            DropTable("dbo.TIPO_NOTIFICACAO");
            DropTable("dbo.NOTIFICACAO_PUSH_GRUPO_USUARIO");
            DropTable("dbo.NOTIFICACAO_PUSH_ITEM");
            DropTable("dbo.NOTIFICACAO_PUSH");
            DropTable("dbo.FILIAL_CONTATOS");
            DropTable("dbo.CONTRATO");
            DropTable("dbo.FILIAL");
            DropTable("dbo.PAIS");
            DropTable("dbo.ESTABELECIMENTO_BASE_PRODUTO");
            DropTable("dbo.ESTABELECIMENTO_PRODUTO");
            DropTable("dbo.ESTABELECIMENTO_CONTA_BANCARIA");
            DropTable("dbo.ESTABELECIMENTO_ASSOCIACAO");
            DropTable("dbo.ESTABELECIMENTO");
            DropTable("dbo.ESTADO");
            DropTable("dbo.CLIENTE_ENDERECO");
            DropTable("dbo.CLIENTE_ACESSO");
            DropTable("dbo.CLIENTE");
            DropTable("dbo.VIAGEM");
            DropTable("dbo.DECLARACAO_CIOT");
            DropTable("dbo.CONTRATO_CIOT_AGREGADO");
            DropTable("dbo.CONTRATO_CIOT_AGREGADO_VEICULO");
            DropTable("dbo.VEICULO");
            DropTable("dbo.TIPO_CARRETA");
            DropTable("dbo.CONJUNTO_CARRETA");
            DropTable("dbo.CONJUNTO");
            DropTable("dbo.CONJUNTO_EMPRESA");
            DropTable("dbo.MOTORISTA");
            DropTable("dbo.CHECKIN");
            DropTable("dbo.CHECKIN_RESUMO");
            DropTable("dbo.CIDADE");
            DropTable("dbo.ESTABELECIMENTO_BASE_ASSOCIACAO");
            DropTable("dbo.ESTABELECIMENTO_BASE");
            DropTable("dbo.GRUPO_USUARIO");
            DropTable("dbo.GRUPO_USUARIO_MENU");
            DropTable("dbo.MENU");
            DropTable("dbo.AUTORIZACAO_EMPRESA");
            DropTable("dbo.AUTENTICACAOAPLICACAO");
            DropTable("dbo.EMPRESA");
            DropTable("dbo.DOCUMENTO");
            DropTable("dbo.CREDENCIAMENTO_ANEXO");
            DropTable("dbo.CREDENCIAMENTO");
            DropTable("dbo.MOTIVO");
            DropTable("dbo.ATENDIMENTO_PORTADOR_TRAMITE");
            DropTable("dbo.ATENDIMENTO_PORTADOR");
        }
    }
}

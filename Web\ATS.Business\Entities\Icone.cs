﻿using ATS.Domain.Enum;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class Icone
    {
        public int IdIcone { get; set; }
        public string TokenIcone { get; set; }
        public string Descricao { get; set; }
        public bool Ativo { get; set; }
        public EIconePara IconePara { get; set; } = EIconePara.CheckList;

        #region Navegação inversa

        public virtual ICollection<TipoEstabelecimento> TipoEstabelecimentos { get; set; }


        #endregion
    }
}

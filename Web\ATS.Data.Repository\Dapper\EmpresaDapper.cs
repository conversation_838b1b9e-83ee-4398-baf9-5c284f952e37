﻿using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;

namespace ATS.Data.Repository.Dapper
{
    public class EmpresaDapper : DapperFactory<Empresa>, IEmpresaDapper
    {
        public bool AtualizaUltimaExecucao(int idEmpresa)
        {
            var sSql = "UPDATE EMPRESA SET ULTIMAEXECUCAO = '" + DateTime.Now + "'  WHERE IDEMPRESA =" + Convert.ToString(idEmpresa);
               
            using (IDbConnection dbConnection = this.GetConnection())
            {
                return dbConnection.Execute(sSql) > 0;

            }
        }

        public List<Empresa> GetCompanies()
        {
            string sql =
                 string.Format("SELECT * FROM EMPRESA");

            return this.RunSelect(sql).ToList();

        }

        public List<string> GetEmailsFinanceiro(int idempresa)
        {
            var sql = $@"SELECT distinct uc.email FROM USUARIO_CONTATO uc
                            JOIN USUARIO u ON u.idusuario = uc.idusuario
                            JOIN PARAMETROS p on p.idregistro = u.idusuario and p.nometabela = 'USUARIO_ID' and p.chave = 'PermiteEfetuarCargaSolicitacaoAdiantamentoApp'
                            WHERE p.valorboolean = @True
                            and u.ativo = @True
                            and u.idempresa = @IdEmpresa";

            using var connection = GetConnection();
            var parameters = new
            {
                True = 1,
                IdEmpresa = idempresa
            };

            var registros = connection.Query<string>(sql, parameters).ToList();

            return registros;
        }
    }
}
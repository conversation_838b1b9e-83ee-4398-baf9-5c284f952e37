﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IModuloMenuService : IService<ModuloMenu>
    {
        IQueryable<ModuloMenu> GetPorIdMenu(int id, List<int> modulosPermitidos);

        /// <summary>
        /// Adicionar a mensagem
        /// </summary>
        /// <param name="moduloMenu">Informações sobre a mensagem</param>
        /// <returns></returns>
        ValidationResult Add(ModuloMenu moduloMenu);

        ValidationResult DeleteRange(int idMenu);
    }
}

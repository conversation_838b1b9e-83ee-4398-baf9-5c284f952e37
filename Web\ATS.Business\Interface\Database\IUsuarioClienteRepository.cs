﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Database
{
    public interface IUsuarioClienteRepository : IRepository<UsuarioCliente>
    {
        ValidationResult Remove(UsuarioCliente usuarioCliente);
    }
}

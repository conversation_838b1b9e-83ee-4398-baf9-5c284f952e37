﻿using ATS.WS.ControllersATS.Default;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.Models.Common.Request;
using Newtonsoft.Json;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using GridValePedagioHubRequest = ATS.Domain.DTO.TagExtratta.GridValePedagioHubRequest;

namespace ATS.WS.ControllersATS
{
    public class TagAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ITagExtrattaApp _tagExtrattaApp;

        public TagAtsController(IUserIdentity userIdentity, ITagExtrattaApp tagExtrattaApp)
        {
            _userIdentity = userIdentity;
            _tagExtrattaApp = tagExtrattaApp;
        }

        /// <summary>
        /// Cria a solicitacao de remessa e deixa Pendente
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult EnviarRemessa(TagRemessaEnvioRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.EnviarRemessa(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Cria a solicitacao de remessa e deixa Pendente
        /// </summary>
        /// <param name="idRemessa"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ReceberRemessa(int idRemessa)
        {
            try
            {
                var result = _tagExtrattaApp.ReceberRemessa(idRemessa);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Retorna os dados de uma remessa para a tela de crud
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarRemessa(int idRemessa)
        {
            try
            {
                var result = _tagExtrattaApp.GetRemessa(idRemessa);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Lista as remessas (nao as tags) que ainda nao foram enviadas
        /// Ao clicar numa remessa deve mandar pra tela de consulta pelo IdRemessa
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GridRemessaEnvio([FromBody] GridRemessaEnvioRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.GridRemessaEnvio(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Lista as remessas já enviadas
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GridRemessaRecebimento([FromBody] GridRemessaRecebimentoRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.GridRemessaRecebimento(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Busca tags indicando um serial inicial e um final
        /// Retorno deve ser do mesmo tipo do item da grid de serial
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTagSerialLoteEstoque(long serialInicial, long serialFinal)
        {
            try
            {
                var result = _tagExtrattaApp.ConsultarLoteEstoque(serialInicial,serialFinal);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return !result.Value.Any() ? 
                    ResponderErro("Nenhuma TAG encontrada com o lote informado!") : 
                    ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Busca uma tag por serial
        /// Retorno deve ser do mesmo tipo do item da grid de serial
        /// </summary>
        /// <param name="serial"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarTagSerialEstoque(long serial)
        {
            try
            {
                var result = _tagExtrattaApp.GetTagSerialEstoque(serial);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Busca uma veiculo por placa e sua tag atual
        /// </summary>
        /// <param name="placa"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarVeiculoTag(string placa)
        {
            try
            {
                var result = _tagExtrattaApp.GetVeiculo(placa);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Lista as tags cadastradas que nao estejam em nenhuma solicitacao nem vinculadas à uma empresa
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <param name="tagsSelecionadas">Excluir essas tags da consulta pois já estao selecionadas para o envio da remessa</param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GridTagsDisponiveis(int take, int page, OrderFilters order, List<QueryFilters> filters, List<long> tagsSelecionadas)
        {
            try
            {
                var result = _tagExtrattaApp.GridTagsAtivas(take,page,order,filters,tagsSelecionadas);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GridTag(int take, int page, OrderFilters order, List<QueryFilters> filters,int? idEmpresa)
        {
            try
            { 
                var result = _tagExtrattaApp.GridTags(take,page,order,filters,idEmpresa,null,null);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioConsultaSituacaoTags(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<ConsultaSituacaoTagsReportRequestModel>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            filtrosGridModel.IdUsuario = _userIdentity.IdUsuario;
            
            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _tagExtrattaApp.GerarRelatorioConsultaSituacaoTags(filtrosGridModel.Take, 
                filtrosGridModel.Page,filtrosGridModel.Order,filtrosGridModel.Filters,filtrosGridModel.Extensao, 
                filtrosGridModel.IdEmpresa,filtrosGridModel.IdUsuario,filtrosGridModel.DataInicio,filtrosGridModel.DataFim);
        
            return File(report, ConstantesUtils.ExcelMimeType, $"Relatorio_Tags.{filtrosGridModel.Extensao}");
        }

        /// <summary>
        /// Grid tags reduzida
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GridTagsReduzida(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var result = _tagExtrattaApp.GridTagsReduzida(take,page,order,filters);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Grid tags reduzida
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GridModeloMoveMais(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var result = _tagExtrattaApp.GridModeloMoveMais(take,page,order,filters);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Salvar vinculo TAG
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult Salvar(SalvarTagRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.Vincular(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Bloquear TAG
        /// </summary>
        /// <param name="serial"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult Bloquear(long serial)
        {
            try
            {
                var result = _tagExtrattaApp.Bloquear(serial);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Desbloquear TAG
        /// </summary>
        /// <param name="serial"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult Desbloquear(long serial)
        {
            try
            {
                var result = _tagExtrattaApp.Desbloquear(serial);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Desvincular TAG
        /// </summary>
        /// <param name="serial"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult Desvincular(long serial)
        {
            try
            {
                var result = _tagExtrattaApp.Desvincular(serial);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Get bloqueios TAG
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult GetBloqueios()
        {
            try
            {
                var result = _tagExtrattaApp.GetBloqueios(_userIdentity.IdUsuario);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Vale pedagios emetidos via hub pedágio
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GridValePedagioHub([FromBody] GridValePedagioHubRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.GridValePedagioHub(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// GerarRelatorioGridConciliacao
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridValePedagioHub(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<GridValePedagioHubReportRequestModel>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});
            
            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _tagExtrattaApp.GerarRelatorioGridValePedagioHub(
                filtrosGridModel.Order,filtrosGridModel.Filters,filtrosGridModel.Extensao, 
                filtrosGridModel.IdEmpresa,filtrosGridModel.DataInicio,filtrosGridModel.DataFim);
        
            return File(report, ConstantesUtils.ExcelMimeType, $"Relatorio_Grid_Vale_Pedagio_Hub_TAG.{filtrosGridModel.Extensao}");
        }

        /// <summary>
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <param name="dataInicio"></param>
        /// <param name="dataFim"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GridPassagemWebhook(int take, int page, OrderFilters order, List<QueryFilters> filters,
            DateTime? dataInicio, DateTime? dataFim,int? idEmpresa)
        {
            try
            {
                var result = _tagExtrattaApp.GridPassagensWebhook(take, page, order, filters, dataInicio, dataFim,idEmpresa);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Lista Pagamentos TAG
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GridPagamentos([FromBody] GridPagamentosTagRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.GridPagamentos(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// GerarRelatorioGridPagamentos
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridPagamentos(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<ConsultaPagamentosTagsReportRequestModel>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;

            var report = _tagExtrattaApp.GerarRelatorioGridPagamentos(
                filtrosGridModel.Order,filtrosGridModel.Filters,filtrosGridModel.Extensao,
                filtrosGridModel.IdEmpresa,filtrosGridModel.DataInicio,filtrosGridModel.DataFim,filtrosGridModel.Fornecedor);

            return File(report, ConstantesUtils.ExcelMimeType, $"Relatorio_Grid_Pagamentos_TAG.{filtrosGridModel.Extensao}");
        }

        /// <summary>
        /// GetPagamento TAG
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult GetPagamento(long id)
        {
            try
            {
                var result = _tagExtrattaApp.GetPagamento(id);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }


        /// <summary>
        /// Pagamento TAG
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult PagamentoEventoTag(PagamentoTagRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.PagamentoEventoTag(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// Estorno TAG
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult EstornoEventoTag(EstornoTagRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.EstornoManualEventoTag(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult RelatorioGridPassagemWebhook(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<GridPassagemWebhookReportRequestModel>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _tagExtrattaApp.GerarRelatorioGridPassagemWebhook(filtrosGridModel.Order,
                filtrosGridModel.Filters, filtrosGridModel.Extensao, filtrosGridModel.DataInicio, filtrosGridModel.DataFim,filtrosGridModel.IdEmpresa);

            return File(report, ConstantesUtils.ExcelMimeType,
                $"Relatorio_Grid_Passagem_Webhook_TAG.{filtrosGridModel.Extensao}");
        }
        
        /// <summary>
        /// Desvincular TAG
        /// </summary>
        /// <param name="serial"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesvincularEmpresa(long serial)
        {
            try
            {
                var result = _tagExtrattaApp.DesvincularEmpresa(serial);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// </summary>
        /// <param name="take"></param>
        /// <param name="page"></param>
        /// <param name="order"></param>
        /// <param name="filters"></param>
        /// <param name="dataInicio"></param>
        /// <param name="dataFim"></param>
        /// <param name="fornecedor"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GridFaturamento(int take, int page, OrderFilters order, List<QueryFilters> filters,
            DateTime dataInicio, DateTime dataFim, FornecedorEnum fornecedor)
        {
            try
            {
                var result = _tagExtrattaApp.GridFaturamento(take, page, order, filters, dataInicio, dataFim,fornecedor);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult GetTotalizadorFaturamento(FaturamentoTotalizadorRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.GetTotalizadorFaturamento(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// GerarRelatorioGridFaturamento
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioGridFaturamento(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<ConsultaFaturamentoReportRequestModel>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            var report = _tagExtrattaApp.GerarRelatorioGridFaturamento(
                filtrosGridModel.Order,filtrosGridModel.Filters,filtrosGridModel.Extensao,filtrosGridModel.DataInicio,filtrosGridModel.DataFim,filtrosGridModel.Fornecedor);

            var mimeType = string.Empty;

            if (filtrosGridModel.Extensao == "pdf")
                mimeType = ConstantesUtils.PdfMimeType;
            else if(filtrosGridModel.Extensao == "xlsx")
                mimeType = ConstantesUtils.ExcelMimeType;

            return File(report,mimeType, $"Relatorio_Grid_Faturamento_TAG.{filtrosGridModel.Extensao}");
        }
        
        /// <summary>
        /// GerarFatura
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public FileResult GerarFatura(string json)
        {
            var request = JsonConvert.DeserializeObject<FaturaRequest>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});
            var report = _tagExtrattaApp.GerarFatura(request);

            return File(report, ConstantesUtils.PdfMimeType, $"Fatura_TAG_Extratta.pdf");
        }
        
        /// <summary>
        /// </summary>
        /// <param name="compraId"></param>
        /// <param name="fornecedor"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar(EPerfil.Administrador,EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPassagensPedagioCompraHub(int compraId,FornecedorEnum fornecedor)
        {
            try
            {
                var result = _tagExtrattaApp.ConsultarPassagensPedagioCompraHub(compraId,fornecedor);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso("OK", result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        /// <summary>
        /// GerarRelatorioPassagensPedagioCompraHub
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador,EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioPassagensPedagioCompraHub(ReportPassagensPedagioCompraHub request)
        {
            var report = _tagExtrattaApp.GerarRelatorioPassagensPedagioCompraHub(request);

            var mimeType = string.Empty;

            if (request.Extensao == "pdf")
                mimeType = ConstantesUtils.PdfMimeType;
            else if(request.Extensao == "xlsx")
                mimeType = ConstantesUtils.ExcelMimeType;

            return File(report,mimeType, $"Relatorio_Passagens_Pedagio.{request.Extensao}");        
        }
        
        /// <summary>
        /// </summary>
        /// <param name="placa"></param>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [Autorizar(EPerfil.Administrador,EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarSaldoValePedagioVeiculo(string placa)
        {
            try
            {
                var result = _tagExtrattaApp.ConsultarSaldoValePedagioVeiculo(placa);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Consulta a stuacao da TAG do veiculo no fornecedor especificado
        /// </summary>
        /// <returns></returns>
        [System.Web.Http.HttpGet]
        [EnableLogRequest]
        [Autorizar(EPerfil.Administrador,EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarSituacaoTagPlaca(string placa, Fornecedor2 fornecedor)
        {
            try
            {
                var result = _tagExtrattaApp.ConsultarSituacaoTagPlaca(placa, fornecedor);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());

                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        
        /// <summary>
        /// Contestar Passagem
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ContestarPassagem(ContestarPassagemRequest request)
        {
            try
            {
                var result = _tagExtrattaApp.ContestarPassagem(request);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                    
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }   
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using Newtonsoft.Json;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.ControllersATS
{
    public class AtendimentoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IAtendimentoPortadorApp _portadorApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IResgateCartaoAtendimentoApp _resgatarCartaoApp;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IMotoristaApp _motoristaApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public AtendimentoAtsController(IUserIdentity userIdentity, IAtendimentoPortadorApp portadorApp,
            IUsuarioApp usuarioApp, IParametrosApp parametrosApp, IProprietarioApp proprietarioApp,
            IResgateCartaoAtendimentoApp resgatarCartaoApp, ICidadeRepository cidadeRepository,
            IMotoristaApp motoristaApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies)
        {
            _userIdentity = userIdentity;
            _portadorApp = portadorApp;
            _usuarioApp = usuarioApp;
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _resgatarCartaoApp = resgatarCartaoApp;
            _cidadeRepository = cidadeRepository;
            _motoristaApp = motoristaApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult IniciarAtendimento()
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");
                
                var atendimentoPortadorResult = _portadorApp.IniciarAtendimento(_userIdentity.IdUsuario, _userIdentity.IdEmpresa);
                return atendimentoPortadorResult.Sucesso 
                    ? ResponderSucesso(atendimentoPortadorResult.Mensagem, atendimentoPortadorResult.AtendimentoPortador) 
                    : ResponderErro(atendimentoPortadorResult.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult FinalizarAtendimento(FinalizarAtendimentoDTORequest request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var atendimentoPortadorResult = _portadorApp.Finalizar(request, _userIdentity.IdUsuario);
                return Responder(atendimentoPortadorResult.Sucesso, atendimentoPortadorResult.Mensagem, null);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaAtendimentoPendente()
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var atendimentoPortadorResult = _portadorApp.ConsultaAtendimentoPendente(_userIdentity.IdUsuario, _userIdentity.IdEmpresa);
                return Responder(atendimentoPortadorResult.Sucesso, atendimentoPortadorResult.Mensagem, atendimentoPortadorResult.AtendimentoPortador);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult InserirTramite(AtendimentoPortadorTramiteRequest atendimentoTramitePortadorDto)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                if (atendimentoTramitePortadorDto.IdAtendimentoPortador == 0)
                {
                    atendimentoTramitePortadorDto.IdAtendimentoPortador = _portadorApp
                        .Find(x => x.IdUsuario == _userIdentity.IdUsuario && x.Status == EStatusAtendimentoPortador.Pendente)
                        .Select(x => x.IdAtendimentoPortador)
                        .FirstOrDefault();
                }
                
                _portadorApp.InserirTramite(atendimentoTramitePortadorDto);

                return ResponderSucesso($"Trâmite '{atendimentoTramitePortadorDto.Tipo.GetDescription()}' armazenado na base de dados com sucesso");
            }
            catch (Exception e)
            {
                return ResponderErro(
                    $"Ocorreu um erro ao armazenar o tramite na base de dados:{Environment.NewLine}{e}");
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult BuscarInformacoesAtendimentoPortador(string documento)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);

                var portador = cartoesApp.ConsultarPortadorAtendimento(documento);

                if (portador != null)
                {
                    _portadorApp.AtualizaDocumento(documento, _userIdentity.IdUsuario);
                    return ResponderSucesso(portador);
                }

                return ResponderErro("Portador não encontrado");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarHistoricoCartoes(string documento)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);

                var resultado = cartoesApp.GetCartaoHistoricoGrid(documento, new List<int>());

                if (!resultado.Sucesso)
                    return ResponderErro(resultado.Mensagem);

                return ResponderSucesso(new
                {
                    totalItems = resultado.Objeto.Count,
                    items = resultado.Objeto
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        [EnableLogRequest]
        public JsonResult ConsultarSaldoCartaoAtendimento(ConsultaSaldoDTORequest consultaSaldoDtoRequest)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
        
                var request = new ConsultarSaldoCartaoRequest();
                request.Cartao = new IdentificadorCartao
                {
                    Produto = consultaSaldoDtoRequest.Produto,
                    Identificador = consultaSaldoDtoRequest.Identificador
                };
        
                var response = cartoesApp.ConsultarSaldoCartaoAtendimento(request);
                if (response == null || response.Status == ConsultarSaldoCartaoResponseStatus.Falha)
                    ResponderErro("Não foi possível consultar o saldo do cartão.");
        
                InserirTramite(new AtendimentoPortadorTramiteRequest()
                {
                    IdAtendimentoPortador = _portadorApp.Find(x =>
                            x.IdUsuario == _userIdentity.IdUsuario && x.Status == EStatusAtendimentoPortador.Pendente)
                        .Select(x => x.IdAtendimentoPortador)
                        .FirstOrDefault(),
                    Tipo = ETipoTramiteAtendimentoPortador.ConsultouSaldo,
                    Operacao =
                        $"Consultou o saldo do cartão ID: {request.Cartao.Identificador}, produto: {request.Cartao.Produto} com saldo de {response?.ValorSaldoDisponivel?.FormatMoney()}",
                    IdentificadorCartao = request.Cartao.Identificador,
                    ProdutoCartao = request.Cartao.Produto
                });
        
                var result = new
                {
                    ValorSaldoDisponivel = response?.ValorSaldoDisponivel?.FormatMoney(),
                    ValorLimiteCredito = response?.ValorLimiteCredito?.FormatMoney()
                };
        
                return ResponderSucesso(result);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        public JsonResult ConsultarExtratoConsolidadoGrid(ConsultaExtratoDTORequest request, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                var cartoesApp = CartoesApp
                    .CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario));

                return ResponderSucesso(cartoesApp.ConsultarExtrato(request, order, filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        public JsonResult ConsultarExtratoGrid(ConsultaExtratoDTORequest request, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                var cartoesApp = CartoesApp
                    .CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario));
                
                return ResponderSucesso(cartoesApp.ConsultarExtrato(request,order,filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }


        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        public JsonResult ConsultarExtratoGridV2(ConsultaExtratoV2DTORequest request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                var cartoesApp = CartoesApp
                    .CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario));

                return ResponderSucesso(cartoesApp.ConsultarExtratoV2(request));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarHistoricoAtendimentoGrid(string documento)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);

                var resultado = cartoesApp.GetCartaoHistoricoGrid(documento, new List<int>());

                if (!resultado.Sucesso)
                    return ResponderErro(resultado.Mensagem);

                return ResponderSucesso(new
                {
                    totalItems = resultado.Objeto.Count,
                    items = resultado.Objeto
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        #region Metodos bloqueados


        //
        // [HttpPost]
        // [Autorizar]
        // [Publico(EPublico.Portal)]
        // [EnableLogRequest]
        // public JsonResult AlterarSenhaCartao(AlterarSenhaCartaoRequestDTO request)
        // {
        //     try
        //     {
        //         var empresaPossuiPermissao =
        //             _parametrosApp.GetAtendimentoPermiteAlterarSenhaCartao(_userIdentity.IdUsuario);
        //         if (!empresaPossuiPermissao)
        //             return ResponderErro("Usuário não possui permissão para esta funcionalidade");
        //
        //         var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
        //         var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
        //
        //         var result = cartoesApp.AlterarSenhaCartao(request);
        //         if (result.Status == AlterarSenhaCartaoResponseStatus.Falha)
        //             return Responder(false, result.Mensagem, result);
        //
        //         InserirTramite(new AtendimentoPortadorTramiteRequest()
        //         {
        //             IdAtendimentoPortador = _portadorApp.Find(x =>
        //                     x.IdUsuario == _userIdentity.IdUsuario && x.Status == EStatusAtendimentoPortador.Pendente)
        //                 .Select(x => x.IdAtendimentoPortador)
        //                 .FirstOrDefault(),
        //             Tipo = ETipoTramiteAtendimentoPortador.AlterouSenhaCartao,
        //             Operacao =
        //                 $"Alterou a senha do cartão ID: {request.Cartao.Identificador}, produto: {request.Cartao.Produto}",
        //             IdentificadorCartao = request.Cartao.Identificador,
        //             ProdutoCartao = request.Cartao.Produto
        //         });
        //
        //         return ResponderSucesso(result);
        //     }
        //     catch (Exception e)
        //     {
        //         throw new Exception("Error ao alterar senha do cartão! " + e.Message, e);
        //     }
        // }
        //
        // [HttpPost]
        // [Autorizar]
        // [Publico(EPublico.Portal)]
        // [EnableLogRequest]
        // public JsonResult BuscarMotivosBloquearCartao(int take, int page, OrderFilters order,
        //     List<QueryFilters> filters)
        // {
        //     try
        //     {
        //         var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
        //         var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
        //
        //         int? index = null;
        //         for (int i = 0; i < filters.Count; i++)
        //             if (filters[i].Campo.Equals("Ativo"))
        //                 index = i;
        //         if (index.HasValue)
        //             filters.RemoveAt(index.Value);
        //
        //         //TODO: Filtro está no formato correto mas no Cartao.Api está montando a URL de forma que retorna exceção, no último método está passando nulo
        //         var customFilter = Mapper.Map<List<QueryFilters>, List<CustomFilter>>(filters);
        //
        //         var result = cartoesApp.BuscarMotivosBloquearCartao(take, page, order, customFilter);
        //
        //         if (result?.FilteredOptions.TotalRecords > 0)
        //             return ResponderSucesso(new DataModel<MotivoBloqueioModel>
        //             {
        //                 totalItems = result.FilteredOptions.TotalRecords.Value,
        //                 items = result.Data.AsQueryable().AplicarFiltrosDinamicos(filters).ToList()
        //             });
        //
        //
        //         return Responder(false, "A consulta não retornou resultados", null);
        //
        //     }
        //     catch (Exception e)
        //     {
        //         // ReSharper disable once StringLiteralTypo
        //         throw new Exception($"Ocorreu um erro durante o processamento:{Environment.NewLine}{e.Message}");
        //     }
        // }
        //
        // [HttpPost]
        // [Autorizar]
        // [Publico(EPublico.Portal)]
        // [EnableLogRequest]
        // public JsonResult BloquearCartao(BloquearCartaoRequest request, string motivo, bool permanente)
        // {
        //     try
        //     {
        //         var empresaPossuiPermissao =
        //             _parametrosApp.GetAtendimentoPermiteBloquearCartao(_userIdentity.IdUsuario);
        //         if (!empresaPossuiPermissao)
        //             return ResponderErro("Usuário não possui permissão para esta funcionalidade");
        //
        //         var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
        //         var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
        //
        //         var result = cartoesApp.BloquearCartao(request);
        //
        //         if (result.Status == BloquearCartaoResponseStatus.Falha)
        //             return Responder(false, result.Mensagem, result);
        //
        //         string operacao;
        //
        //         if (permanente)
        //             operacao = "Cancelou";
        //         else
        //             operacao = "Bloqueou";
        //
        //         operacao += string.Concat(
        //             $" o cartão ID: {request.Cartao.Identificador}, produto: {request.Cartao.Produto} com o motivo '{motivo}'",
        //             string.IsNullOrEmpty(request.Observacao)
        //                 ? string.Empty
        //                 : $" com a observação '{request.Observacao}'");
        //
        //         InserirTramite(new AtendimentoPortadorTramiteRequest()
        //         {
        //             IdAtendimentoPortador = _portadorApp.Find(x =>
        //                     x.IdUsuario == _userIdentity.IdUsuario && x.Status == EStatusAtendimentoPortador.Pendente)
        //                 .Select(x => x.IdAtendimentoPortador)
        //                 .FirstOrDefault(),
        //             Tipo = ETipoTramiteAtendimentoPortador.BloqueouCartao,
        //             Operacao = operacao,
        //             IdentificadorCartao = request.Cartao.Identificador,
        //             ProdutoCartao = request.Cartao.Produto,
        //             IdMotivo = request.Motivo,
        //             Permanente = permanente
        //         });
        //
        //         return ResponderSucesso(result);
        //     }
        //     catch (Exception e)
        //     {
        //         // ReSharper disable once StringLiteralTypo
        //         throw new Exception($"Ocorreu um erro durante o processamento:{Environment.NewLine}{e.Message}");
        //     }
        // }
        //
        // [HttpPost]
        // [Autorizar]
        // [Publico(EPublico.Portal)]
        // [EnableLogRequest]
        // public JsonResult DesbloquearCartao(DesbloquearCartaoRequest request)
        // {
        //     try
        //     {
        //         var empresaPossuiPermissao =
        //             _parametrosApp.GetAtendimentoPermiteDesbloquearCartao(_userIdentity.IdUsuario);
        //         if (!empresaPossuiPermissao)
        //             return ResponderErro("Usuário não possui permissão para esta funcionalidade");
        //
        //         var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
        //         var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
        //
        //         var result = cartoesApp.DesbloquearCartao(request);
        //
        //         if (result.Status == DesbloquearCartaoResponseStatus.Falha)
        //             return Responder(false, result.Mensagem, result);
        //
        //         InserirTramite(new AtendimentoPortadorTramiteRequest()
        //         {
        //             IdAtendimentoPortador = _portadorApp.Find(x =>
        //                     x.IdUsuario == _userIdentity.IdUsuario && x.Status == EStatusAtendimentoPortador.Pendente)
        //                 .Select(x => x.IdAtendimentoPortador)
        //                 .FirstOrDefault(),
        //             Tipo = ETipoTramiteAtendimentoPortador.DesbloqueouCartao,
        //             Operacao = string.Concat(
        //                 $"Desbloqueou o cartão ID: {request.Cartao.Identificador}, produto: {request.Cartao.Produto}",
        //                 string.IsNullOrEmpty(request.Observacao)
        //                     ? string.Empty
        //                     : $" com a observação '{request.Observacao}'"),
        //             IdentificadorCartao = request.Cartao.Identificador,
        //             ProdutoCartao = request.Cartao.Produto
        //         });
        //
        //         return ResponderSucesso(result);
        //     }
        //     catch (Exception e)
        //     {
        //         throw new Exception($"Ocorreu um erro durante o processamento:{Environment.NewLine}{e.Message}");
        //     }
        // }


        #endregion

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAtendimento(ConsultarAtendimentoRequestDTO request, int take, int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var result = _portadorApp.ConsultarAtendimento(request, take, page, order, filters,
                    _userIdentity.IdEmpresa.Value);

                return ResponderSucesso(new
                {
                    totalItems = result.ConsultaAtendimento.Count(),
                    items = result.ConsultaAtendimento.Skip((page - 1) * take).Take(take).ToList()
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarHistoricoAtendimento(ConsultarHistoricoAtendimentoRequestDTO request, int take,
            int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var result = _portadorApp.ConsultarHistoricoAtendimento(request, take, page, order, filters,
                    _userIdentity.IdEmpresa.Value);

                return ResponderSucesso(new
                {
                    totalItems = result.ConsultaHistoricoAtendimento.Count,
                    items = result.ConsultaHistoricoAtendimento.Skip((page - 1) * take).Take(take).ToList()
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioConsultaAtendimento(string json)
        {
            
            if (!_userIdentity.IdEmpresa.HasValue)
                throw new InvalidOperationException("Usuário sem empresa vinculada.");

            if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                throw new InvalidOperationException("Usuário não autenticado.");

            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioAtendimentoRequestDTO>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            var retorno = _portadorApp.RelatorioConsultaAtendimento(filtrosGridModel, filtrosGridModel.Take,
                filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters, _userIdentity.IdEmpresa ?? 0,
                filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(retorno, mimeType, $"Relatório de consulta de Atendimento.{filtrosGridModel.Extensao}");
        }

        #region MÉTODOS BLOQUEADOS

        /*
       [HttpPost]
       [Autorizar]
       [EnableLogRequest]
       [Publico(EPublico.Portal)]
       public JsonResult TransferirValorCartao(CartaoTransferenciaDTO request)
       {
           try
           {
               var empresaPossuiPermissao = _parametrosApp.GetAtendimentoPermiteRealizarTransferenciaCartoes(_userIdentity.IdUsuario);
               if (!empresaPossuiPermissao)
                   return ResponderErro("Usuário não possui permissão para esta funcionalidade");
               
               var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
               var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
               
               var infoAdministradora = cartoesApp.AdministradoraCartao(request.CartaoOrigem.Identificador.GetValueOrDefault(0),
                   request.CartaoOrigem.Produto.GetValueOrDefault(0));

               var cartoesAppAdministradora =
                   CartoesApp.CreateByAdministradora(_cartoesAppFactoryDependencies, infoAdministradora.AdministradoraId.GetValueOrDefault(),
                       _userIdentity.IdEmpresa, _userIdentity.CpfCnpj, _userIdentity.Nome);
               
               int grupoContabilizacao = _parametrosApp.GetGrupoContabilizacaoCentralAtendimento();
               var empresaMicroServico = cartoesAppAdministradora.GetOrGenerateTokenEmpresa(infoAdministradora.CnpjEmpresa, "Central Atendimento", grupoContabilizacao);
               if(!empresaMicroServico.Sucesso)
                   return ResponderErro(empresaMicroServico.Mensagem);
               
               var cartoesAppEmpresa = new CartoesApp(
                   _cartoesAppFactoryDependencies.ParametrosApp, _cartoesAppFactoryDependencies.ProprietarioApp, _cartoesAppFactoryDependencies.ResgatarCartaoApp, 
                   _cartoesAppFactoryDependencies.CidadeRepository, _cartoesAppFactoryDependencies.MotoristaApp, _cartoesAppFactoryDependencies.UsuarioApp,
                   _cartoesAppFactoryDependencies.CartoesServiceArgs, _cartoesAppFactoryDependencies.CidadeApp, _cartoesAppFactoryDependencies.EmpresaApp,
                   empresaMicroServico.IdEmpresa, empresaMicroServico.Token, _userIdentity.CpfCnpj, _userIdentity.Nome);
               
               var resultado = cartoesAppEmpresa.TransferirValorCartaoAtendimento(request.DocumentoOrigem, request.DocumentoDestino, 
                   request.Valor, request.CnpjEmpresa,EOrigemTransacaoCartao.Atendimento,request.Senha, request.CartaoOrigem, request.CartaoDestino, infoAdministradora.AdministradoraId);

               if(resultado.Status == EStatusPagamentoCartao.Erro)
                   return ResponderErro(resultado.Mensagem);
               
               return ResponderSucesso(resultado);
           }
           catch (Exception e)
           {
               Logger.Error(e);
               return ResponderErro(e.Message);
           }
       }
       */

        /*
        [HttpPost]
        [Autorizar]
        [EnableLogRequest]
        [Publico(EPublico.Portal)]
        public JsonResult TransferirValorContaBancaria(TransferirContaBancariaRequestDTO request)
        {
            try
            {
                var empresaPossuiPermissao = _parametrosApp.GetAtendimentoPermiteRealizarTransferenciaBancaria(_userIdentity.IdUsuario);
                if (!empresaPossuiPermissao)
                    return ResponderErro("Usuário não possui permissão para esta funcionalidade");
                
                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                
                var infoAdministradora = cartoesApp.AdministradoraCartao(request.CartaoOrigem.Identificador,
                    request.CartaoOrigem.Produto);
        
                var cartoesAppAdministradora =
                    CartoesApp.CreateByAdministradora(_cartoesAppFactoryDependencies, infoAdministradora.AdministradoraId.GetValueOrDefault(),
                        _userIdentity.IdEmpresa, _userIdentity.CpfCnpj, _userIdentity.Nome);
                
                int grupoContabilizacao = _parametrosApp.GetGrupoContabilizacaoCentralAtendimento();
                var empresaMicroServico = cartoesAppAdministradora.GetOrGenerateTokenEmpresa(infoAdministradora.CnpjEmpresa, "Central Atendimento", grupoContabilizacao);
                if(!empresaMicroServico.Sucesso)
                    return ResponderErro(empresaMicroServico.Mensagem);
                
                var cartoesAppEmpresa = new CartoesApp(_cartoesAppFactoryDependencies.ParametrosApp, _cartoesAppFactoryDependencies.ProprietarioApp, _cartoesAppFactoryDependencies.ResgatarCartaoApp, 
                    _cartoesAppFactoryDependencies.CidadeRepository, _cartoesAppFactoryDependencies.MotoristaApp, _cartoesAppFactoryDependencies.UsuarioApp,
                    _cartoesAppFactoryDependencies.CartoesServiceArgs, _cartoesAppFactoryDependencies.CidadeApp, _cartoesAppFactoryDependencies.EmpresaApp,
                    empresaMicroServico.IdEmpresa, empresaMicroServico.Token, _userIdentity.CpfCnpj, _userIdentity.Nome);
                
                var resultado = cartoesAppEmpresa.TransferirValorContaBancaria(request, EOrigemTransacaoCartao.Atendimento);
        
                if(resultado.Status == EStatusPagamentoCartao.Erro)
                    return ResponderErro(resultado.Mensagem);
                
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }
        */

        #endregion


        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ResgatarValor(ResgatarValorDTO request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var empresaPossuiPermissao = _parametrosApp.GetAtendimentoPermiteRealizarResgate(_userIdentity.IdUsuario);
                if (!empresaPossuiPermissao)
                    return ResponderErro("Usuário não possui permissão para esta funcionalidade");
                
                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                
                var infoAdministradora = cartoesApp.AdministradoraCartao(request.Cartao,
                                 request.Produto);
                
                var cartoesAppAdministradora = CartoesApp.CreateByAdministradora(_cartoesAppFactoryDependencies, infoAdministradora.AdministradoraId.GetValueOrDefault(), 
                    _userIdentity.IdEmpresa, _userIdentity.CpfCnpj, _userIdentity.Nome);
        
                int grupoContabilizacao = _parametrosApp.GetGrupoContabilizacaoCentralAtendimento();
                var empresaMicroServico = cartoesAppAdministradora.GetOrGenerateTokenEmpresa(request.Empresa, "Central Atendimento", grupoContabilizacao);
                if(!empresaMicroServico.Sucesso)
                    return ResponderErro(empresaMicroServico.Mensagem);
                
                var cartoesAppEmpresa = new CartoesApp(empresaMicroServico.IdEmpresa, empresaMicroServico.Token, 
                    _userIdentity.CpfCnpj, _userIdentity.Nome, _cartoesAppFactoryDependencies);
                
                //Atribuições para conciliação de transações
                request.CpfUsuario = usuario.CPFCNPJ;
                request.Especificacao = "ATD";
                
                var resultado = cartoesAppEmpresa.ResgatarValor(request);
        
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }

        
        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult EstornarResgateValor(EstornarResgateValorDTO request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var empresaPossuiPermissao = _parametrosApp.GetAtendimentoPermiteRealizarEstornoResgate(_userIdentity.IdUsuario);
                if (!empresaPossuiPermissao)
                    return ResponderErro("Usuário não possui permissão para esta funcionalidade");
                
                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                
                var infoAdministradora = cartoesApp.AdministradoraCartao(request.Cartao.GetValueOrDefault(0),
                    request.Produto.GetValueOrDefault(0));
             
                var cartoesAppAdministradora = CartoesApp.CreateByAdministradora(_cartoesAppFactoryDependencies, infoAdministradora.AdministradoraId.GetValueOrDefault(), 
                    _userIdentity.IdEmpresa, _userIdentity.CpfCnpj, _userIdentity.Nome);
        
                int grupoContabilizacao = _parametrosApp.GetGrupoContabilizacaoCentralAtendimento();
                var empresaMicroServico = cartoesAppAdministradora.GetOrGenerateTokenEmpresa(request.Empresa, "Central Atendimento", grupoContabilizacao);
                if(!empresaMicroServico.Sucesso)
                    return ResponderErro(empresaMicroServico.Mensagem);
             
                var cartoesAppEmpresa = new CartoesApp(empresaMicroServico.IdEmpresa, empresaMicroServico.Token, 
                    _userIdentity.CpfCnpj, _userIdentity.Nome, _cartoesAppFactoryDependencies);
                
                var resultado = cartoesAppEmpresa.EstornarResgateValor(request, _userIdentity.IdUsuario);
        
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }

        
        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarResgate(ConsultarResgateValorDTO request, int take, int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(_userIdentity.IdUsuario);
                var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);
                
                var resultado = cartoesApp.GetResgatarValor(request, take, page, order, filters);
                    
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaPermissoesUsuario()
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Usuário sem empresa vinculada.");

                if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                    return ResponderErro("Usuário não autenticado.");

                var permissoesResult = _portadorApp.ConsultaPermissoesUsuario(_userIdentity.IdUsuario,
                    (EPerfil) _userIdentity.Perfil, _userIdentity.IdEmpresa);
                return Responder(permissoesResult.Sucesso, permissoesResult.Mensagem, permissoesResult.Permissoes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioExtratoGrid(string json)
        {
            if (!_userIdentity.IdEmpresa.HasValue)
                throw new InvalidOperationException("Usuário sem empresa vinculada.");

            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioGridExtratoDTO>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});
            var usuario = _usuarioApp.UsuarioMicroServicoInstanciaApp(filtrosGridModel.IdUsuarioLogado);
            var cartoesApp = CartoesApp.CreateByUsuarioAtendimento(_cartoesAppFactoryDependencies, usuario);

            var requestApi = new ConsultarExtratoRequest
            {
                DataFim = filtrosGridModel.DataFim,
                DataInicio = filtrosGridModel.DataInicio,
                Tipo = filtrosGridModel.Tipo,
                Cartao = new IdentificadorCartao
                {
                    Produto = filtrosGridModel.Produto,
                    Identificador = filtrosGridModel.Identificador
                },
                SomenteTransferencia = filtrosGridModel.SomenteTransferencia
            };

            var report =
                cartoesApp.GerarRelatorioExtratoGrid(requestApi, filtrosGridModel);

            return File(report, ConstantesUtils.ExcelOldMimeType, $"Relatório do extrato.{filtrosGridModel.Extensao}");
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult ImprimirRecibo(string json)
        {
            if (!_userIdentity.IdEmpresa.HasValue)
                throw new InvalidOperationException("Usuário sem empresa vinculada.");

            if (!_parametrosApp.GetParametroUsuarioPermitirAcessoAtendimento(_userIdentity.IdUsuario))
                throw new InvalidOperationException("Usuário não autenticado.");

            var dadosRecibo = JsonConvert.DeserializeObject<ReciboTransferenciaDto>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (dadosRecibo == null)
                throw new Exception("Não foram encontrados dados do recibo de transferência.");

            var validacaoPedagio = dadosRecibo.ValidarTransacao();

            if (!validacaoPedagio.IsValid)
                throw new Exception(validacaoPedagio.GetFaults().FirstOrDefault()?.ToString());

            var impressaoReciboTransferencia = _portadorApp.GerarReciboTransacoes(dadosRecibo);

            return File(impressaoReciboTransferencia, ConstantesUtils.PdfMimeType, "Recibo de transferência.pdf");
        }

        [HttpGet]
        [Autorizar(EPerfil.Empresa, EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Contato()
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    throw new InvalidOperationException("Usuário sem empresa vinculada.");

                var retorno = _portadorApp.ConsultarInformacoesContatoWhatsapp();

                return ResponderSucesso("Dados consultados.", retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
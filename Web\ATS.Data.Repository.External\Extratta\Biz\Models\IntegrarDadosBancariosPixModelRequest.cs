﻿using System;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class IntegrarDadosBancariosPixModelRequest
    {
        public string DocumentoProprietario { get; set; }
        public string DocumentoEmpresa { get; set; }
        
        //Dados Pessoa
        public string Nome { get; set; }
        public string NomeFantasia { get; set; }
        public string TipoPessoa => DocumentoProprietario.Length == 11 ? "F" : "J";
        public IntegrarDadosBancariosPixModelRequestInfo Info { get; set; }
        public IntegrarDadosBancariosPixModelRequestEndereco Endereco { get; set; }
        
        //Dados Pix
        public ETipoChavePix? TipoChavePix { get; set; }
        public string ChavePix { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodBanco { get; set; }
    }

    public class IntegrarDadosBancariosPixModelRequestInfo
    {
        public string Telefone { get; set; }
        public string Sexo { get; set; }
        public string Rg { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
        public bool? TacEquiparado { get; set; }
    }

    public class IntegrarDadosBancariosPixModelRequestEndereco
    {
        public int? CidadeIbge { get; set; }
        public string Cep { get; set; }
        public string Bairro { get; set; }
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string Complemento { get; set; }
    }
}
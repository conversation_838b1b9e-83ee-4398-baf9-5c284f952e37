﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Linq;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Database
{
    public interface IFilialRepository : IRepository<Filial>
    {
        //Filial GetWithAllChilds(int idFilial);
        Filial GetWithLocalizacao(int id);
        IQueryable<Filial> Consultar(string razaoSocial, int idEmpresa);
        //IQueryable<Filial> ConsultarPorNomeFantasia(string nomeFantasia, int idEmpresa);
        IQueryable<Filial> GetFiliaisPorEmpresa(int idEmpresa);
        IQueryable<Filial> GetIdsFiliaisAtualizadas(DateTime dataHora, int? idEmpresa);
        Filial GetFilialPorEmpresa(int idEmpresa, int idFilial);
        string GetCnpjPorId(int idFilial);

        int GetFilialIdFromFunc(string filialCliente);
    }
}
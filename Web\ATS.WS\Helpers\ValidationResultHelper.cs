﻿using ATS.Domain.Validation;
using System.Text;

namespace ATS.WS.Helpers
{
    public static class ValidationResultHelper
    {
        /// <summary>
        /// Retorna as mensagens de erro do ValidationResult formatadas em HTML
        /// </summary>
        /// <param name="validationResult">Informações sobre os erros</param>
        /// <param name="breakLine">Informa se a cada mensagem deverá ser efetuada a quebra de linha</param>
        /// <returns></returns>
        public static string ToFormatedMessage(this ValidationResult validationResult, bool breakLine = true)
        {
            StringBuilder returnMessage = new StringBuilder(string.Empty);
            foreach (ValidationError error in validationResult.Errors)
                returnMessage.AppendLine($"{error.Message}{(breakLine ? "<br/>" : string.Empty)}");

            return returnMessage.ToString();
        }


        public static string ToAlertFormatedMessage(this ValidationResult validationResult, bool breakLine = true)
        {
            StringBuilder returnMessage = new StringBuilder(string.Empty);
            foreach (ValidationError alert in validationResult.Alerts)
                returnMessage.AppendLine($"{alert.Message}{(breakLine ? "<br/>" : string.Empty)}");

            return returnMessage.ToString();
        }
    }
}
﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using Newtonsoft.Json;

namespace ATS.WS.ControllersATS
{
    public class TriagemProtocoloAssociacaoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly SrvTriagemProtocolo _srvTriagemProtocolo;

        public TriagemProtocoloAssociacaoAtsController(IUserIdentity userIdentity, IUsuarioApp usuarioApp, IEstabelecimentoApp estabelecimentoApp, IProtocoloApp protocoloApp, SrvTriagemProtocolo srvTriagemProtocolo)
        {
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _estabelecimentoApp = estabelecimentoApp;
            _protocoloApp = protocoloApp;
            _srvTriagemProtocolo = srvTriagemProtocolo;
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idEstabelecimento, int? idAssociacao,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataPagamentoInicial, 
            DateTime? dataPagamentoFinal, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;
                
                if (filters == null)
                    filters = new List<QueryFilters>();

                var idEstabelecimentoBase = new List<int>();
                var associacoes = new List<KeyValuePair<int, int>>();
                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento)
                {

                    idEstabelecimentoBase = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario)?.Select(x => x.IdEstabelecimento)
                        .ToList();
                }

                var idsAssociacoes = new List<int?>();

                //Funcionalidade Associacao
                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento)
                {
                    var listaEstabelecimentos = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario)?.Select(x => x.IdEstabelecimento).FirstOrDefault();
                    
                    var estabAssoc = _estabelecimentoApp.GetEstabelecimentoPorIdBase(listaEstabelecimentos ?? 0)
                        .Where(c => c.Associacao)
                        .Select(c => c.IdEstabelecimento).ToList();
                    
                    if (!estabAssoc.Any())
                        throw new Exception("Método disponível apenas para estabelecimentos que são associações!");

                    foreach (var i in estabAssoc)
                    {
                        idsAssociacoes.Add(i);
                    }
                }

                var protocolos = _protocoloApp.ConsultarTriagemProtocoloAssociacao(idEmpresa, idEstabelecimentoBase,
                    idEstabelecimento, idsAssociacoes, dataGeracaoInicial, dataGeracaoFinal, dataPagamentoInicial, 
                    dataPagamentoFinal, associacoes, take, page, order, filters);

                return ResponderSucesso(protocolos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(EStatusProtocoloEvento? status, int? idProtocolo, int? idMotivo,
            DateTime? dataPrevisaoPagamento, string detalhamento)
        {
            try
            {
                if (!status.HasValue)
                    return ResponderErro("Status não informado. ");
                if (!idProtocolo.HasValue)
                    return ResponderErro("Protocolo não informado. ");

                switch (status)
                {
                    case EStatusProtocoloEvento.Aprovado:
                        var validationResult = _protocoloApp.Aprovar(idProtocolo.Value, dataPrevisaoPagamento, _userIdentity.IdUsuario);
                        return validationResult.IsValid
                            ? ResponderSucesso("Protocolo aprovado com sucesso!")
                            : ResponderErro(validationResult.ToString());

                    case EStatusProtocoloEvento.Rejeitado:
                        if (!idMotivo.HasValue)
                            return ResponderErro("Motivo deve ser informado para rejeição de um protocolo");

                        var validationResultReprovado =
                            _protocoloApp.Rejeitar(idProtocolo.Value, idMotivo.Value, detalhamento, (Usuario)_userIdentity);
                        return validationResultReprovado.IsValid
                            ? ResponderSucesso("Protocolo rejeitado com sucesso!")
                            : ResponderErro(validationResultReprovado.ToString());

                    //case EStatusProtocoloEvento.Liberado:
                    //    var validationResultLiberacao = _protocoloApp.Liberar(idProtocolo.Value);
                    //    return validationResultLiberacao.IsValid
                    //        ? ResponderSucesso("Protocolo liberado com sucesso!")
                    //        : ResponderErro(validationResultLiberacao.ToString());
                    default:
                        return ResponderErro("Ação não informada para   a alteração de status.");
                }
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GetRelatorio(string sessionKey, string idsProtocolo)
        {
            try
            {
                List<int> listaIdsProtocolo = new List<int>();
                if (!string.IsNullOrWhiteSpace(idsProtocolo))
                {
                    listaIdsProtocolo = idsProtocolo.Split(',')
                        .Select(x => Convert.ToInt32(x)).ToList();
                }
                else
                    throw new Exception("Impossível identificar os parâmetros do relatório. ");
                var report = _srvTriagemProtocolo
                    .GerarRelatorio(sessionKey, listaIdsProtocolo);

                Response.AddHeader("Content-Disposition", $"inline; filename=Protocolo.pdf");
                return File(report, "application/pdf");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpNotFoundResult(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public FileResult RelatorioEventosVinculados(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioEventosVinculadosDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            
            var retorno = _srvTriagemProtocolo.GerarRelatorioProtocolosEventosVinculados(filtroGridModel.IdProtocolo, filtroGridModel.Take, filtroGridModel.Page, filtroGridModel.Order, filtroGridModel.Filters, filtroGridModel.Extensao);

            var mimeType = string.Empty;
            var extensaoArquivo = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    extensaoArquivo = "pdf";
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    extensaoArquivo = "xlsx";
                    break;
            }

            return File(retorno, mimeType, $"Relatório de eventos vinculados.{extensaoArquivo}");
        }
    }
}
﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Configuration;

namespace ATS.WS.Security.Configuration
{
    public class SigningConfigurations
    {
        public SecurityKey Key { get; }
        public SigningCredentials SigningCredentials { get; }

        public SigningConfigurations()
        {
            Key = new SymmetricSecurityKey(Convert.FromBase64String(ConfigurationManager.AppSettings["ATS_SecretKey"]));
            SigningCredentials = new SigningCredentials(Key, SecurityAlgorithms.HmacSha512);
        }
    }
}

﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class PrestacaoContasMap : EntityTypeConfiguration<PrestacaoContas>
    {
        public PrestacaoContasMap()
        {
            ToTable("PRESTACAO_CONTAS");

            HasKey(x => x.Id);

            Property(x => x.Observacao).HasMaxLength(200);
            
            Property(x => x.Valor).HasPrecision(10, 2);

            HasRequired(x => x.UsuarioPrestacao)
                .WithMany()
                .HasForeignKey(x => x.IdUsuarioPrestacao); 

            HasRequired(x => x.Empresa)
                .WithMany()
                .HasForeign<PERSON>ey(x => x.IdEmpresa); 
            
            HasOptional(x => x.UsuarioCadastro)
                .WithMany()
                .HasForeignKey(x => x.IdUsuarioCadastro);

            HasOptional(x => x.UsuarioAtualizacao)
                .WithMany()
                .HasForeign<PERSON>ey(x => x.IdUsuarioAtualizacao); 
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Data.Context;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;

namespace ATS.Data.Repository.EntityFramework
{
    public class CheckInRepository : Repository<CheckIn>, ICheckInRepository
    {
        private readonly ICheckinDapper _checkinDapper;
        public CheckInRepository(AtsContext context, ICheckinDapper checkinDapper) : base(context)
        {
            _checkinDapper = checkinDapper;
        }
        
        /// <summary>
        /// Retorna a lista
        /// </summary>
        /// <param name="idMotorista"></param>
        /// <returns></returns>
        public CheckIn GetLastMotorista(int idMotorista)
        {
            return All().OrderByDescending(a=> a.DataHora)
                .FirstOrDefault(a => a.IdMotorista == idMotorista);                            
        }

        public CheckIn GetLastUsuario(int idUsuario)
        {
            return All().OrderByDescending(a => a.DataHora)
                .FirstOrDefault(a => a.IdUsuario == idUsuario);
        }

        public List<CheckinConsultaModel> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf)
        {
            var checkins = _checkinDapper.
                GetByCpf(IdEmpresa, dataInicial, dataFinal, cpf);

            return checkins;
        }

        public List<CheckinConsultaModel> GetByPlaca(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa)
        {
            var checkins = _checkinDapper.
                GetByPlaca(IdEmpresa, dataInicial, dataFinal, placa);

            return checkins;
        }

        public List<CheckinConsultaModel> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, int idMotoristaVeiculo)
        {
            var checkins = _checkinDapper.
                GetByPlacaMotorista(IdEmpresa, dataInicial, dataFinal, idMotoristaVeiculo);

            return checkins;
        }
    }
}
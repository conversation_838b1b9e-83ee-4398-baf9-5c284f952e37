﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;

namespace ATS.WS.ControllersATS
{
    //TODO: Todos os recursor relacionados á triagem de antecipaação foram comentados...
    public class TriagemAntecipacaoProtocoloAtsController : DefaultController
    {

        private readonly IUserIdentity _userIdentity;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly SrvTriagemProtocolo _srvTriagemProtocolo;

        public TriagemAntecipacaoProtocoloAtsController(IUserIdentity userIdentity, IUsuarioApp usuarioApp, IProtocoloApp protocoloApp, SrvTriagemProtocolo srvTriagemProtocolo)
        {
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _protocoloApp = protocoloApp;
            _srvTriagemProtocolo = srvTriagemProtocolo;
        }


        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idEstabelecimento, DateTime? dataGeracaoInicial, 
            DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial, DateTime? dataSolicitacaoFinal,
            EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;
                
                var idEstabelecimentoBase = new List<int>();

                if (idEstabelecimento.HasValue)
                    idEstabelecimentoBase.Add(idEstabelecimento.Value);

                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento)
                    idEstabelecimentoBase = _usuarioApp.GetUsuariosEstabelecimento(_userIdentity.IdUsuario)?.Select(x => x.IdEstabelecimento)
                        .ToList();
                
                return ResponderSucesso(_protocoloApp.ConsultarTriagemAntecipacaoProtocolo(idEmpresa,
                    idEstabelecimentoBase, dataGeracaoInicial, dataGeracaoFinal, dataSolcitacaoInicial,
                    dataSolicitacaoFinal, status, take, page, order, filters, (Usuario)_userIdentity));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarOcorrenciasGrid(int? idProtocolo, int? idEmpresa, int? idEstabelecimento,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataSolcitacaoInicial, DateTime? dataSolicitacaoFinal,
            EStatusProtocoloAntecipacao? status, int take, int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;
                
                var idEstabelecimentoBase = new List<int>();

                if (idEstabelecimento.HasValue)
                    idEstabelecimentoBase.Add(idEstabelecimento.Value);

                if (_userIdentity.Perfil == (int)EPerfil.Estabelecimento)
                    idEstabelecimentoBase = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario)?.Select(x => x.IdEstabelecimento)
                        .ToList();

                return ResponderSucesso(_protocoloApp.ConsultarTriagemAntecipacaoOcorrenciasProtocolo(idProtocolo, idEmpresa,
                    idEstabelecimentoBase, dataGeracaoInicial, dataGeracaoFinal, dataSolcitacaoInicial,
                    dataSolicitacaoFinal, status, take, page, order, filters));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        //[HttpPost]
        //public JsonResult Aprovar(int idProtocoloAntecipacao)
        //{
        //    try
        //    {
        //        var validationResult = _protocoloApp.AprovarAntecipacao(idProtocoloAntecipacao);
        //        return validationResult.IsValid
        //            ? ResponderSucesso("Antecipação de protocolo aprovada com sucesso!")
        //            : ResponderErro(validationResult.ToString());
        //    }
        //    catch (Exception e)
        //    {
        //        return ResponderErro(e);
        //    }
        //}

        //[HttpPost]
        //public JsonResult Rejeitar(int idProtocoloAntecipacao, int idMotivo, string detalhamento)
        //{
        //    try
        //    {
        //        var validationResult = _protocoloApp.RejeitarAntecipacao(idProtocoloAntecipacao, idMotivo, detalhamento);
        //        return validationResult.IsValid
        //            ? ResponderSucesso("Antecipação de protocolo rejeitada com sucesso!")
        //            : ResponderErro(validationResult.ToString());
        //    }
        //    catch (Exception e)
        //    {
        //        return ResponderErro(e);
        //    }
        //}

        //[HttpPost]
        //public JsonResult Reenviar(int idProtocoloAntecipacao, DateTime dataPagamentoAntecipado)
        //{
        //    try
        //    {
        //        var validationResult =
        //            _protocoloApp.ReenviarAntecipacao(idProtocoloAntecipacao, dataPagamentoAntecipado);
        //        return validationResult.IsValid
        //            ? ResponderSucesso("Antecipação de protocolo reenviada com sucesso!")
        //            : ResponderErro(validationResult.ToString());
        //    }
        //    catch (Exception e)
        //    {
        //        return ResponderErro(e);
        //    }
        //}


        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GetRelatorio(string sessionKey, string idsProtocolo)
        {
            try
            {
                List<int> listaIdsProtocolo = new List<int>();
                if (!string.IsNullOrWhiteSpace(idsProtocolo))
                {
                    listaIdsProtocolo = idsProtocolo.Split(',')
                        .Select(x => Convert.ToInt32(x)).ToList();
                }
                else
                    throw new Exception("Impossível identificar os parâmetros do relatório.");
                var report = _srvTriagemProtocolo
                    .GerarRelatorioProtocolosComOcorrencia(sessionKey, listaIdsProtocolo);

                Response.AddHeader("Content-Disposition", $"inline; filename=ProtocoloComOcorrencia.pdf");
                return File(report, "application/pdf");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpNotFoundResult(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult RelatorioEventos( int? IdProtocolo, DateTime? dataInicial, int? idEmpresa, DateTime? dataFinal, int Take, int Page, OrderFilters Order, List<QueryFilters> filters, string Extensao)
        {
            try
            {
                var retorno = _srvTriagemProtocolo.GerarRelatorioProtocolosEventosOcorrencia(IdProtocolo,  Take, Page, Order, filters, Extensao);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class DocumentoMap : EntityTypeConfiguration<Documento>
    {
        public DocumentoMap()
        {
            ToTable("DOCUMENTO");

            HasKey(x => x.IdDocumento);

            Property(x => x.Descricao)
                .IsRequired();

            Property(x => x.PossuirValidade)
                .IsRequired();

            Property(x => x.Ativo)
                .IsRequired();

            HasRequired(x => x.Empresa)
                .WithMany(x => x.Documentos)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Filial)
                .WithMany(x => x.Documentos)
                .HasForeignKey(x => x.IdFilial);

            Property(x => x.DiasValidade)
                .IsOptional();
        }
    }
}

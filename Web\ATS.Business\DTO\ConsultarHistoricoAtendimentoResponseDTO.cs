using System;
using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class ConsultarHistoricoAtendimentoResponseDTO
    {
        public List<ListaHistoricoAtendimentoDTO> ConsultaHistoricoAtendimento { get; set; }
    }
    
    public class ListaHistoricoAtendimentoDTO 
    {
        public int IdAtendimentoPortador { get; set; }
        public int Sequencial { get; set; }
        public string DataTramite { get; set; }
        public DateTime DataTramiteFT { get; set; }
        public int? IdentificadorCartao { get; set; }
        public int? ProdutoCartao { get; set; }
        public string Operacao { get; set; }
        public ETipoTramiteAtendimentoPortador Tipo { get; set; }
        public int? IdMotivo { get; set; }
        public bool? Permanente { get; set; }
    }
}
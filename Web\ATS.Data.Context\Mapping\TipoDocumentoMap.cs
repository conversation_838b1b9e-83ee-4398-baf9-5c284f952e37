using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class TipoDocumentoMap : EntityTypeConfiguration<TipoDocumento>
    {
        public TipoDocumentoMap()
        {
            ToTable("TIPO_DOCUMENTO");

            HasKey(t => t.IdTipoDocumento);

            Property(t => t.IdTipoDocumento)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .IsRequired()
                .HasMaxLength(450);
        }
    }
}
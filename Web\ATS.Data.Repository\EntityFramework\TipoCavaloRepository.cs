﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class TipoCavaloRepository : Repository<TipoCavalo>, ITipoCavaloRepository
    {
        public TipoCavaloRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado para consultar Tipo de Veículo.
        /// </summary>
        /// <param name="nome">Nome de Tipo de Veículo.</param>
        /// <returns>IQueryable de TipoCavaloGrid</returns>
        public IQueryable<TipoCavaloGrid> Consultar(string nome)
        {
            return (from tipoCavalo in All()
                    where tipoCavalo.Nome.Contains(nome)
                    orderby tipoCavalo.IdTipoCavalo descending
                    select new TipoCavaloGrid
                    {
                        IdTipoCavalo = tipoCavalo.IdTipoCavalo,
                        Nome = tipoCavalo.Nome,
                        CategoriaCavalo =
                            tipoCavalo.Categoria == ECategoriaTipoCavalo.Leves
                                ? "Leves"
                                : tipoCavalo.Categoria == ECategoriaTipoCavalo.Medios
                                    ? "Medios"
                                    : tipoCavalo.Categoria == ECategoriaTipoCavalo.Pesados ? "Pesados" : "",
                        Ativo = tipoCavalo.Ativo,
                        IdEmpresa = tipoCavalo.IdEmpresa,
                        RazaoSocial = tipoCavalo.Empresa.RazaoSocial
                    });
        }

        /// <summary>
        /// Método utilizado para listar os Tipos de Cavalo por categoria
        /// </summary>
        /// <param name="categoria">Categoria de Tipo de Veículo</param>
        /// <returns>IQueryable de Tipo de Veículo</returns>
        public IQueryable<TipoCavalo> GetPorCategoria(ECategoriaTipoCavalo categoria)
        {
            return from tipoCavalo in All()
                   where tipoCavalo.Categoria == categoria
                   && tipoCavalo.Ativo
                   select tipoCavalo;
        }

        public IQueryable<TipoCavalo> GetTipoCavaloAtualizado(DateTime dataHora, List<int> idsEmpresa)
        {
            var tiposCavalo = (from tipoCavalo in All()
                               where tipoCavalo.DataHoraUltimaAtualizacao >= dataHora
                               select tipoCavalo).Include(x => x.TipoCavaloCliente).Include(x => x.TipoCavaloCliente.Select(y => y.Cliente));

            tiposCavalo = idsEmpresa != null
                ? tiposCavalo.Where(x => x.IdEmpresa == null || idsEmpresa.Contains(x.IdEmpresa.Value))
                : tiposCavalo.Where(x => x.IdEmpresa == null);

            return tiposCavalo;
        }
    }
}
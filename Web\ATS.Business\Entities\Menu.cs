using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATS.Domain.Entities
{
    public class Menu
    {
        /// <summary>
        /// Código do menu
        /// </summary>
        public int IdMenu { get; set; }

        /// <summary>
        /// Código do menu pai
        /// </summary>
        public int? IdMenuPai { get; set; }

        /// <summary>
        /// Descrição
        /// </summary>
        public string Descricao { get; set; }

        /// <summary>
        /// Link para acesso sistema antigo
        /// </summary>
        public string Link { get; set; }

        /// <summary>
        /// Link para acesso
        /// </summary>
        public string LinkNovo { get; set; }

        /// <summary>
        /// Nome de uma funcao js para ser executada no front-end ao clicar no menu quando o menu nao tem link
        /// </summary>
        public string Funcao { get; set; }

        /// <summary>
        /// Perfis permitidos para acesso.
        /// <para>Exemplo: 1,2,3</para>
        /// </summary>
        public string Perfis { get; set; }

        /// <summary>
        /// Nú<PERSON>o da sequência, utilizado para realizar a ordenação
        /// </summary>
        public int Sequencia { get; set; }

        /// <summary>
        /// Informa se o menu esta ativo
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Código de identificação de permissão do menu
        /// </summary>
        public int? IdentificadorPermissao { get; set; }

        public bool IsMenuPai { get; set; } = false;
        public bool IsMenuMobile { get; set; } = false;

        #region Referências

        [ForeignKey("IdMenuPai")]
        public virtual Menu Menus { get; set; }

        public virtual ICollection<Menu> MenuLista { get; set; }
        public virtual ICollection<GrupoUsuarioMenu> GruposDoMenu { get; set; }
        public virtual ICollection<GrupoUsuario> GrupoUsuarioLista { get; set; }
        public virtual ICollection<AutorizacaoEmpresa> AutorizacaoEmpresaLista { get; set; }
        public virtual ICollection<ModuloMenu> ModuloMenus { get; set; }

        #endregion
    }
}
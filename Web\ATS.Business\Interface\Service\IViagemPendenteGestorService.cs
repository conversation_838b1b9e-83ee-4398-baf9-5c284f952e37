﻿using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Interface.Service
{
    public interface IViagemPendenteGestorService : IService<ViagemPendenteGestor>
    {
        EBloqueioGestorStatus? GetStatusViagem(int idviagem);
        List<ViagemPendenteGestor> GetPendenciasByIdViagem( int idViagem, int idEmpresa);
        IQueryable<ViagemPendenteGestor> GetQuery();
        IQueryable<ViagemPendenteGestor> GetQueryIncluded();
        ViagemPendenteGestor Save(ViagemPendenteGestor pendencia);
        void Update(ViagemPendenteGestor pendencia);
    }
}
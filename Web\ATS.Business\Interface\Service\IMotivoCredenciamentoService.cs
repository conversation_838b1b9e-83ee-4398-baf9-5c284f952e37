﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.CrossCutting.Reports.MotivoCredenciamento;
using ATS.Domain.Grid;

namespace ATS.Domain.Interface.Service
{
    public interface IMotivoCredenciamentoService : IService<Motivo>
    {
        ValidationResult Add(Motivo motivo);
        ValidationResult Update(Motivo motivo, List<ETipoMotivo> tipos);
        ValidationResult Reativar(int idMotivoCredenciamento);
        ValidationResult Inativar(int idMotivoCredenciamento);
        Motivo Get(int idMotivoCredenciamento, bool include = true);
        IQueryable<Motivo> GetAll();
        Motivo ValidarEntidade(Motivo motivo);
        ValidationResult UpdateTipoMotivo(int idMotivo, List<ETipoMotivo> tipos);

        DataModel<MotivoCredenciamentoModel> ConsultarGrid(int? idEmpresa,
            int? idFilial,
            string descricao,
            int take,
            int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters);

        object ConsultarGridTodos(int? idEmpresa,
            int? idFilial,
            string descricao,
            int take,
            int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters);

        byte[] GerarRelatorioGridMotivo(int? idEmpresa,
            int? idFilial,
            string descricao,
            int take,
            int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters, string tipoArquivo, string logo);
    }
}

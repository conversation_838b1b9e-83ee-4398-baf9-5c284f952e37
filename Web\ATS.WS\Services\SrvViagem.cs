﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Pedagio;
using ATS.WS.Services.ViagemServices;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services
{
    public class SrvViagem : SrvBase
    {
        private readonly IEmpresaApp _empresaApp;
        private readonly IntegracaoViagem _integracaoViagem;
        private readonly AlteracaoViagem _alteracaoViagem;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemEventoApp _viagemEventoApp;

        public SrvViagem(IVersaoAnttLazyLoadService versaoAntt, IEmpresaApp empresaApp, IntegracaoViagem integracaoViagem, AlteracaoViagem alteracaoViagem, IViagemEventoApp viagemEventoApp)
        {
            _versaoAntt = versaoAntt;
            _empresaApp = empresaApp;
            _integracaoViagem = integracaoViagem;
            _alteracaoViagem = alteracaoViagem;
            _viagemEventoApp = viagemEventoApp;
        }

        public Retorno<ViagemIntegrarResponseModel> Integrar(ViagemIntegrarRequestModel @params,bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return _integracaoViagem.IntegrarV2(@params,isApi);
                case EVersaoAntt.Versao3:
                    return _integracaoViagem.IntegrarV3(@params,isApi);
                default:
                    return _integracaoViagem.IntegrarV2(@params,isApi);
            }
        }
        
        public Retorno<PedagioAvulsoResponse> IntegrarPedagioAvulso(PedagioAvulsoRequest @params)
        {
            return _integracaoViagem.IntegrarPedagioAvulso(@params);
        }

        public Retorno<ViagemIntegrarResponseModel> Alterar(ViagemIntegrarRequestModel @params,bool isApi = false)
        {
            Retorno<ViagemIntegrarResponseModel> retorno;
            try
            {
                switch (_versaoAntt.Value)
                {
                    case EVersaoAntt.Versao2:
                        retorno = _alteracaoViagem
                            .AlterarV2(@params, isApi);
                        break;
                    case EVersaoAntt.Versao3:
                        retorno = _alteracaoViagem
                            .AlterarV3(@params,isApi);
                        break;
                    default:
                        retorno = _alteracaoViagem
                            .AlterarV2(@params,isApi);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Erro ao atualizar viagem: " + @params.IdViagem);
                
                var e = ex.GetBaseException();
                if (e.ToString().Contains("deadlock"))
                    retorno = new Retorno<ViagemIntegrarResponseModel>(false,
                        "A viagem está em atualização em outro processo e não é permitido alterações simultâneas", null);
                else
                    retorno = new Retorno<ViagemIntegrarResponseModel>(false,
                        e.GetBaseException().Message, null);
            }
            
            return retorno;
        }
        
        public Retorno<ViagemRemoverCarretasResponse> RemoverCarretas(ViagemRemoverCarretasRequest request)
        {
            return _alteracaoViagem.RemoverCarretas(request, _versaoAntt.Value);
        }

        public Retorno<object> CancelarViagens(List<int> idViagens, int empresaId, string documentoUsuarioAudit, string nomeUsuarioAudit, bool isApi = false)
        {
            var empresa = _empresaApp.All().Where(c => c.IdEmpresa == empresaId).Select(c => new
            {
                c.CNPJ,
                Token = c.AutenticacaoAplicacao.Select(a => a.Token).FirstOrDefault()
            }).FirstOrDefault();

            if (empresa == null)
                return new Retorno<object>(false, "Nenhuma empresa vinculada a este usuário.", null);

            var cnpj = empresa.CNPJ.OnlyNumbers();
            var viagemEventoApp = _viagemEventoApp;
            foreach (var viagem in idViagens)
            {
                var queryViagemEvento = viagemEventoApp.Find(x => x.IdEmpresa == empresaId);
                queryViagemEvento = queryViagemEvento.Where(x => x.IdViagem == viagem);

                var viagemEventos = queryViagemEvento
                    .ToList();
                var cancelar = new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = cnpj,
                    CNPJAplicacao = cnpj,
                    Token = empresa.Token,
                    DocumentoUsuarioAudit = documentoUsuarioAudit,
                    NomeUsuarioAudit = nomeUsuarioAudit,
                    IdViagem = viagemEventos.FirstOrDefault()?.IdViagem,
                    NumeroControle = viagemEventos.FirstOrDefault()?.Viagem.NumeroControle,
                    PesoSaida = viagemEventos.FirstOrDefault()?.Viagem.PesoSaida,
                    PedagioBaixado = viagemEventos.FirstOrDefault()?.Viagem.PedagioBaixado,
                    ValorPedagio = viagemEventos.FirstOrDefault()?.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = viagemEventos.FirstOrDefault()?.Viagem.HabilitarDeclaracaoCiot ?? false,
                    NaturezaCarga = viagemEventos.FirstOrDefault()?.Viagem.NaturezaCarga,
                    DataAtualizacao = DateTime.Now,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>()
                };

                foreach (var evento in viagemEventos)
                {
                    cancelar.ViagemEventos.Add(new ViagemEventoIntegrarModel
                    {
                        IdViagemEvento = evento.IdViagemEvento,
                        NumeroControle = evento.NumeroControle,
                        CpfUsuario = documentoUsuarioAudit,
                        NomeUsuario = nomeUsuarioAudit,
                        TipoEvento = evento.TipoEventoViagem,
                        IdViagem = evento.IdViagem,
                        Status = EStatusViagemEvento.Cancelado,
                    });
                }

                var ret = Alterar(cancelar, isApi);
                if (!ret.Sucesso)
                {
                    return new Retorno<object>(false, $"Falha ao cancelar a viagem {cancelar.IdViagem}", null);
                }
            }
            
            return new Retorno<object>(true, "", null);
        }
    }
}
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Models.ViagemModels;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Pedagio;
using ATS.WS.Models.Webservice.Request.Veiculo;
using AutoMapper;
using Quartz.Util;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.Services.ViagemServices
{
    public class IntegracaoViagem : SrvBase
    {
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly PropriedadesPagamentoFreteViagem _propriedadesPagamentoFreteViagem;
        private readonly IntegracaoMeioHomologadoViagem _integracaoMeioHomologadoViagem;
        private readonly BloqueioGestorViagem _bloqueioGestorViagem;
        private readonly IEmpresaApp _empresaApp;
        private readonly IFilialApp _filialApp;
        private readonly IVeiculoApp _veiculoApp;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IViagemEstabelecimentoApp _viagemEstabelecimentoApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IMotoristaService _motoristaService;
        private readonly IRotaModeloApp _rotaModeloApp;
        private readonly IUsuarioService _usuarioService;
        private readonly IViagemPendenteGestorService _viagemPendenteGestorService;
        private readonly ICidadeApp _cidadeApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly ITagExtrattaApp _tagExtrattaApp;
        private readonly IValePedagioApp _valePedagioApp;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly ITransacaoPixRepository _transacaoPixRepository;

        private readonly IList<ETipoProcessamentoCartao> ListCargas = new List<ETipoProcessamentoCartao>
        {
            ETipoProcessamentoCartao.CargaAbastecimento,
            ETipoProcessamentoCartao.CargaAbono,
            ETipoProcessamentoCartao.CargaAdiantamento,
            ETipoProcessamentoCartao.CargaAvulsa,
            ETipoProcessamentoCartao.CargaEstadia,
            ETipoProcessamentoCartao.CargaRpa,
            ETipoProcessamentoCartao.CargaSaldo
        };

        private readonly IList<ETipoProcessamentoCartao> ListTransf = new List<ETipoProcessamentoCartao>
        {
            ETipoProcessamentoCartao.TransferenciaAbastecimento,
            ETipoProcessamentoCartao.TransferenciaAbono,
            ETipoProcessamentoCartao.TransferenciaAdiantamento,
            ETipoProcessamentoCartao.TransferenciaEstadia,
            ETipoProcessamentoCartao.TransferenciaManual,
            ETipoProcessamentoCartao.TransferenciaRpa,
            ETipoProcessamentoCartao.TransferenciaSaldo
        };

        public IntegracaoViagem(IClienteApp clienteApp, IParametrosApp parametrosApp, IViagemApp viagemApp, IProprietarioApp proprietarioApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App,
            IVersaoAnttLazyLoadService versaoAntt, ITransacaoCartaoApp transacaoCartaoApp,
            PropriedadesPagamentoFreteViagem propriedadesPagamentoFreteViagem, IntegracaoMeioHomologadoViagem integracaoMeioHomologadoViagem, BloqueioGestorViagem bloqueioGestorViagem,
           IEmpresaApp empresaApp, IFilialApp filialApp, IVeiculoApp veiculoApp,
            IViagemEventoApp viagemEventoApp, IViagemEstabelecimentoApp viagemEstabelecimentoApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IMotoristaService motoristaService,
            IRotaModeloApp rotaModeloApp, IUsuarioService usuarioService, IViagemPendenteGestorService viagemPendenteGestorService, ICidadeApp cidadeApp, ICadastrosApp cadastrosApp, IValePedagioApp valePedagioApp, IParametrosGenericoService parametrosGenericoService, ITagExtrattaApp tagExtrattaApp, IParametrosEmpresaService parametrosEmpresaService, IProprietarioRepository proprietarioRepository, IParametrosProprietarioService parametrosProprietarioService, ITransacaoPixRepository transacaoPixRepository)
        {
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _proprietarioApp = proprietarioApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _versaoAntt = versaoAntt;
            _transacaoCartaoApp = transacaoCartaoApp;
            _propriedadesPagamentoFreteViagem = propriedadesPagamentoFreteViagem;
            _integracaoMeioHomologadoViagem = integracaoMeioHomologadoViagem;
            _bloqueioGestorViagem = bloqueioGestorViagem;
            _empresaApp = empresaApp;
            _filialApp = filialApp;
            _veiculoApp = veiculoApp;
            _viagemEventoApp = viagemEventoApp;
            _viagemEstabelecimentoApp = viagemEstabelecimentoApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _motoristaService = motoristaService;
            _rotaModeloApp = rotaModeloApp;
            _usuarioService = usuarioService;
            _viagemPendenteGestorService = viagemPendenteGestorService;
            _cidadeApp = cidadeApp;
            _cadastrosApp = cadastrosApp;
            _tagExtrattaApp = tagExtrattaApp;
            _parametrosEmpresaService = parametrosEmpresaService;
            _proprietarioRepository = proprietarioRepository;
            _parametrosProprietarioService = parametrosProprietarioService;
            _transacaoPixRepository = transacaoPixRepository;
            _valePedagioApp = valePedagioApp;
            _parametrosGenericoService = parametrosGenericoService;
        }

        public Retorno<ViagemIntegrarResponseModel> IntegrarV2(ViagemIntegrarRequestModel @params, bool isApi = false)
        {
            #region Inicializacoes

            int? filialId = null;
            var empresa = _empresaApp.Get(@params.CNPJEmpresa);

            if (string.IsNullOrEmpty(@params.CNPJEmpresa))
                @params.CNPJEmpresa = @params.CNPJAplicacao;

            #endregion

            try
            {
                var empresaId = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

                if (empresaId == null)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Empresa inválida.", null);

                var usuarioDocAudit = !string.IsNullOrWhiteSpace(@params.DocumentoUsuarioAudit)
                    ? @params.DocumentoUsuarioAudit
                    : CartoesService.AuditDocIntegracao;
                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, @params.CNPJEmpresa,
                    usuarioDocAudit, @params.NomeUsuarioAudit);

                #region Buscas necessárias para integração

                var empresaValidacaoPagFrete = _empresaApp.EmpresaValidaPagamentoFrete(empresaId.Value);
                var idClienteOri = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteOrigem, empresaId.Value);
                var idClienteDes = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteDestino, empresaId.Value);
                var idClienteTomador = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteTomador, empresaId.Value);

                if (!string.IsNullOrWhiteSpace(@params.CodigoFilial))
                {
                    filialId = _filialApp.GetIdPorCodigoFilial(empresaId.Value, @params.CNPJFilial,
                        @params.CodigoFilial);

                    if (filialId.HasValue && string.IsNullOrWhiteSpace(@params.CNPJFilial))
                    {
                        var filialcnpj = _filialApp.GetCnpjPorId(filialId.Value);
                        @params.CNPJFilial = filialcnpj;
                    }
                }

                if (!filialId.HasValue)
                    if (!string.IsNullOrEmpty(@params.CNPJFilial))
                        filialId = _filialApp.GetIdPorCnpj(@params.CNPJFilial);

                if (!filialId.HasValue) @params.CNPJFilial = null;

                var cartaoVinculadoProprietario = new CartaoVinculadoPessoaListResponse();
                var cartaoVinculadoMotorista = new CartaoVinculadoPessoaListResponse();

                //Viagem Internacional
                if ((@params.CPFCNPJClienteOrigem == "00000000000000" || @params.CPFCNPJClienteDestino == "00000000000000") && empresa.GerarCiotViagemInternacional)
                    @params.HabilitarDeclaracaoCiot = false;

                #endregion

                #region Validacoes
                
                if (!idClienteOri.HasValue)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Cliente de origem inválido.", null);

                if (!idClienteDes.HasValue)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Cliente de destino inválido.", null);

                if (@params.Unidade == EUnidadeMedida.Saca && @params.Quantidade <= 0)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Quantidade de sacas inválida.", null);

                if (!string.IsNullOrEmpty(@params.DocumentoCliente) && @params.DocumentoCliente.Length > 100)
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        @"O campo DocumentoCliente não pode ter mais de 100 caracteres.", null);

                if (empresaValidacaoPagFrete && @params.PesoSaida.HasValue && @params.PesoSaida.Value <= 0)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Peso de saida deve ser maior que 0.",
                        null);

                if (@params.PesoSaida.HasValue &&
                    @params.PesoSaida.Value.ToString("N3", CultureInfo.InvariantCulture).Length > 13)
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        $"Peso de saída: {@params.PesoSaida} inválido", null);

                if (@params.HabilitarDeclaracaoCiot)
                {
                    if (@params.NaturezaCarga == null || @params.NaturezaCarga == 0)
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            @"Natureza da carga é obrigatória ao integrar viagem com recurso de CIOT habilitado.",
                            null);

                    if (@params.ViagemEventos == null)
                    {
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            @"Ao menos um evento é necessário ao integrar viagem com recurso de CIOT habilitado.",
                            null);
                    }

                    if (@params.ViagemRegra == null)
                    {
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            @"Ao menos uma regra é necessária ao integrar viagem com recurso de CIOT habilitado.",
                            null);
                    }
                }

                if (@params.IdCarga.HasValue && @params.IdCarga <= 0)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Código da carga inválido.", null);

                if (empresaValidacaoPagFrete && @params.ViagemEventos == null)
                    return new Retorno<ViagemIntegrarResponseModel>(false, "Eventos não informados.", null);

                if (!string.IsNullOrWhiteSpace(@params.NumeroControle) &&
                                               _viagemApp.ViagemCadastrada(empresaId.Value, @params.NumeroControle))
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        $"Viagem {@params.NumeroControle} já cadastrada.", null);

                var viagemEmAberto = _viagemApp.GetQuery().FirstOrDefault(x =>
                    (x.CPFMotorista == @params.CPFMotorista.Trim()
                     || x.Placa == @params.Placa.Trim())
                    && x.StatusViagem == EStatusViagem.Aberto
                    && x.Empresa.BloquearNovaViagem
                    && x.IdEmpresa == empresaId);

                if (viagemEmAberto != null)
                {
                    var nomeMotorista = _motoristaService.GetAllByIdEmpresa(empresa.IdEmpresa)
                        .Where(m => m.CPF == @params.CPFMotorista)
                        .Select(m => m.Nome)
                        .FirstOrDefault();

                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        $"Já existem viagens em aberto para o motorista {nomeMotorista} e/ou placa {@params.Placa}",
                        null);
                }

                if (@params.ViagemEventos != null && @params.ViagemEventos.Count > 0)
                {
                    var numerosControleRepetidos = @params.ViagemEventos.Where(x => x.NumeroControle != null)
                        .Select(x => x.NumeroControle)
                        .GroupBy(x => x)
                        .Where(g => g.Count() > 1)
                        .Select(y => y.Key)
                        .ToList();

                    if (numerosControleRepetidos.Any())
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            numerosControleRepetidos.Count == 1
                                ? $"O número de controle do evento {numerosControleRepetidos.First()} não pode ser repetido na mesma viagem."
                                : $"Os números de controle dos eventos {string.Join(", ", numerosControleRepetidos)} não podem ser repetidos na mesma viagem.",
                            null);
                }

                if (!string.IsNullOrWhiteSpace(@params.Placa))
                {
                    @params.Placa = @params.Placa.RemoveSpecialCharacters().Trim().ToUpper();
                    if (@params.Placa.Length != 7)
                        return new Retorno<ViagemIntegrarResponseModel>(false, "O campo Placa deve conter 7 caracteres",
                            null);
                }

                if (@params.Carretas?.Any() == true)
                    for (var i = 0; @params.Carretas.Count > i; i++)
                    {
                        @params.Carretas[i] = @params.Carretas[i].RemoveSpecialCharacters().Trim().ToUpper();
                        if (@params.Carretas[i].Length != 7)
                            return new Retorno<ViagemIntegrarResponseModel>(false,
                                "Os itens de Carretas devem conter 7 caracteres", null);
                    }

                var validacaoPlacasRepetidas = ValidarPlacasRepetidas(@params);

                if (!validacaoPlacasRepetidas.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        validacaoPlacasRepetidas.Errors.FirstOrDefault()?.Message, null);

                if (@params.Pedagio != null)
                {
                    var rotaApp = _rotaModeloApp;
                    LocalizacaoDTO localizacao;


                    if (!string.IsNullOrWhiteSpace(@params.Placa) && 
                        @params.Pedagio.Fornecedor == FornecedorEnum.ViaFacil && 
                        (_parametrosGenericoService.GetParametro<bool?>(GLOBAL.HabilitarConsultaStatusVeiculoSemPararIntegracoes, 0) ?? false))
                    {
                        var respostaConsulta = _valePedagioApp.GetStatusVeiculoSemParar(@params.Placa, @params.CNPJEmpresa);
                        if (respostaConsulta.VeiculoStatusSemParar != EStatusVeiculoSemParar.Ok )
                        {
                            return new Retorno<ViagemIntegrarResponseModel>(false, respostaConsulta.Mensagem, null);
                        }
                    }
                    
                    //Caso informado uma rota padrão modelo
                    if (@params.Pedagio.NomeRota != null || @params.Pedagio.IdRotaModelo.HasValue)
                    {
                        var pedagioRotaModelo = rotaApp.GetByIdOrNomeRota(@params.Pedagio.IdRotaModelo ?? 0,
                            @params.Pedagio.NomeRota, empresa.IdEmpresa);

                        if (pedagioRotaModelo != null)
                        {
                            var localizacaoRotaModelo = rotaApp.SetarRetornoDestino(pedagioRotaModelo);

                            //Reatribuição de valores do pedagio
                            @params.Pedagio.ValorPedagio = @params.Pedagio.Fornecedor == FornecedorEnum.Moedeiro || !empresa.PedagioTag && !@params.Pedagio.Istag()  ? pedagioRotaModelo.CustoRota : pedagioRotaModelo.CustoRotaTag;
                            @params.Pedagio.TipoVeiculo = pedagioRotaModelo.TipoVeiculo;
                            @params.Pedagio.Localizacoes = new List<LocalizacaoDTO>();
                            
                            var permiteUtilizarPolyline = _parametrosApp.GetUtilizaRoteirizacaoPorPolyline(empresa.IdEmpresa);

                            if (permiteUtilizarPolyline)
                                @params.Pedagio.CodPolyline = pedagioRotaModelo.CodPolyline;

                            foreach (var item in localizacaoRotaModelo)
                            {
                                localizacao = new LocalizacaoDTO()
                                {
                                    IbgeCidade = item.Ibge.ToInt(),
                                    Latitude = item.Latitude,
                                    Longitude = item.Longitude,
                                };

                                @params.Pedagio.Localizacoes.Add(localizacao);
                            }

                            //Reset para nome rota ter prioridade
                            @params.Pedagio.IdentificadorHistorico = null;

                            //Atribuição do número de eixos
                            if (!string.IsNullOrWhiteSpace(@params.Placa))
                            {
                                var existVeiculo = _veiculoApp.PlacaExistente(@params.Placa, empresa.IdEmpresa);
                                if (!existVeiculo.Existente)
                                    return new Retorno<ViagemIntegrarResponseModel>(false, "Placa não cadastrada.",
                                        null);

                                @params.Pedagio.QtdEixos = _veiculoApp
                                    .GetVeiculoPorPlaca(@params.Placa, empresa.IdEmpresa).QuantidadeEixos;

                                if (@params.Carretas != null)
                                {
                                    foreach (var carreta in @params.Carretas)
                                    {
                                        if (!_veiculoApp.VeiculoValidoIntegracao(carreta, empresa.IdEmpresa))
                                            return new Retorno<ViagemIntegrarResponseModel>(false,
                                                "Carreta não cadastrada.", null);

                                        @params.Pedagio.QtdEixos += _veiculoApp
                                            .GetVeiculoPorPlaca(carreta, empresa.IdEmpresa).QuantidadeEixos;
                                    }
                                }

                                if (@params.CarretasViagemV2 != null)
                                {
                                    foreach (var carretaV2 in @params.CarretasViagemV2)
                                    {
                                        if (!_veiculoApp.VeiculoValidoIntegracao(carretaV2.Placa, empresa.IdEmpresa))
                                            return new Retorno<ViagemIntegrarResponseModel>(false,
                                                "Carreta não cadastrada.", null);

                                        @params.Pedagio.QtdEixos += _veiculoApp
                                            .GetVeiculoPorPlaca(@carretaV2.Placa, empresa.IdEmpresa).QuantidadeEixos;
                                    }
                                }
                            }
                        }
                    }
                    
                    //Caso informado uma polyline
                    if (@params.Pedagio.CodPolyline != null)
                    {
                        var permiteUtilizarPolyline = _parametrosEmpresaService.GetUtilizaRoteirizacaoPorPolyline(empresa.IdEmpresa);

                        if (!permiteUtilizarPolyline)
                            return new Retorno<ViagemIntegrarResponseModel>(false, "Empresa não possui permissão para utilizar polyline no processo de roteirização!", null);
                        
                        var respostaConsulta = _valePedagioApp.GetPolyline(@params.Pedagio.CodPolyline ?? 0, @params.CNPJEmpresa);

                        if (!respostaConsulta.Sucesso)
                            return new Retorno<ViagemIntegrarResponseModel>(false, respostaConsulta.Mensagem, null);

                        @params.Pedagio.Localizacoes = respostaConsulta.Localizacao.Select(x => new LocalizacaoDTO
                        {
                            Latitude = x.Latitude,
                            Longitude = x.Longitude
                        }).ToList();
                    }
                    
                    if (@params.Pedagio.Fornecedor == FornecedorEnum.ExtrattaTag)
                    {
                        var utilizaTag = _empresaApp.GetParametrosPedagioEmpresa(empresa.IdEmpresa, usuarioDocAudit, @params.NomeUsuarioAudit);
                                    
                        if (utilizaTag?.TagExtratta == null)
                            return new Retorno<ViagemIntegrarResponseModel>(false, "Empresa não possui permissão de utilizar o Fornecedor Extratta TAG.", null);
                                    
                        if (utilizaTag?.TagExtratta.PermiteUtilizarFornecedor == null || utilizaTag?.TagExtratta.PermiteUtilizarFornecedor == false)
                            return new Retorno<ViagemIntegrarResponseModel>(false, "Empresa não possui permissão de utilizar o Forncedor Extratta TAG.", null);

                        var tagVeiculo = _tagExtrattaApp.GetVeiculo(@params.Placa);

                        if (!tagVeiculo.Success)
                            return new Retorno<ViagemIntegrarResponseModel>(false, tagVeiculo.Messages.FirstOrDefault(), null);
                        
                        if(!tagVeiculo.Value.Serial.HasValue)
                            return new Retorno<ViagemIntegrarResponseModel>(false, "Placa informada não possui nenhuma TAG ativa!", null);

                        @params.Pedagio.TagSerialNumber = tagVeiculo.Value.Serial;
                    }

                    if (@params.Pedagio.Fornecedor == FornecedorEnum.TaggyEdenred)
                    {
                        var statusVeiculo = _valePedagioApp.GetStatusVeiculoTaggyEdenred(@params.Placa, @params.CNPJAplicacao);
                        if (!statusVeiculo.Sucesso)
                            return new Retorno<ViagemIntegrarResponseModel>(false,
                                "Erro ao consultar veículo na Taggy Edenred; " + statusVeiculo.Mensagem,
                                null);

                        if (statusVeiculo.Status != TaggyEdenredVeiculoResponseStatusId.TagAtiva)
                        {
                            string msg;
                            switch (statusVeiculo.Status)
                            {
                                case TaggyEdenredVeiculoResponseStatusId.TagBloqueada: msg = "Tag Edenred do veículo foi bloqueada temporariamente."; break;
                                case TaggyEdenredVeiculoResponseStatusId.TagCancelada: msg = "Tag Edenred do veículo foi cancelada."; break;
                                case TaggyEdenredVeiculoResponseStatusId.TagBloqueadaFinanceiro: msg = "Tag Edenred do veículo foi cancelada pelo financeiro"; break;
                                case TaggyEdenredVeiculoResponseStatusId.TagCanceladaSubstituida: msg = "Tag Edenred do veículo foi cancelada para ser substituída."; break;
                                case TaggyEdenredVeiculoResponseStatusId.TagNaoVinculada: msg = "Veículo não possui tag Edenred vinculada."; break;
                                case TaggyEdenredVeiculoResponseStatusId.TagBloqueadaFaltaSaldo: msg = "Tag Edenred do veículo foi bloqueada por falta de saldo."; break;
                                default: msg = "Consulta de status do veículo na Taggy Edenred retornou um status inválido"; break;
                            }

                            return new Retorno<ViagemIntegrarResponseModel>(false, msg, null);
                        }
                    }

                    if (@params.Pedagio.Fornecedor != FornecedorEnum.Desabilitado &&
                        @params.Pedagio.ValorPedagio.HasValue && @params.Pedagio.ValorPedagio <= 0)
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            "Solicitação de pedágio incorreta. Para definir o valor de pedágio, deve ser indicado um valor maior que R$ 0,00.",
                            null);

                    if (@params.ValorPedagio > 0 == false && @params.Pedagio.ValorPedagio > 0 == false &&
                        !@params.Pedagio.IdentificadorHistorico.HasValue &&
                        @params.Pedagio.Localizacoes?.Any() == true && @params.Pedagio.QtdEixos < 2)
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            "Quantidade de eixos inválido, deve ser indicado um valor igual ou maior que 2.", null);
                }

                if (empresaValidacaoPagFrete && @params.ViagemEventos == null)
                    if (@params.HabilitarDeclaracaoCiot || @params.ViagemEventos.Any(c => c.HabilitarPagamentoCartao) ||
                        @params.Pedagio != null)
                        if (string.IsNullOrWhiteSpace(@params.CNPJFilial))
                            return new Retorno<ViagemIntegrarResponseModel>(false,
                                "Para integrações com CIOT, Cartão ou Pedágio, deve ser informado o código ou CNPJ da filial da viagem.",
                                null);

                if (@params.HabilitarDeclaracaoCiot)
                {
                    var idProprietario = _proprietarioApp.GetIdPorCpfCnpj(@params.CPFCNPJProprietario, empresaId.Value);

                    if (_parametrosApp.ValidaCnpjCpfProprietarioNaViagem() && !idProprietario.HasValue)
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            $"O proprietário {@params.CPFCNPJProprietario.ToCpfOrCnpj()} não foi encontrado na base de dados",
                            null);
                }

                if (@params.ViagemEventos != null && @params.ViagemEventos.Any())
                {
                    if (@params.ViagemEventos.Any(ve => ve.ValorPagamento < 0))
                        return new Retorno<ViagemIntegrarResponseModel>(false,
                            "Valor do evento informado não pode ser negativo.", null);

                    if (@params.ViagemEventos.Any(ve => ve.ValorPagamento == 0))
                        return new Retorno<ViagemIntegrarResponseModel>(false, "Valor  do evento não informado.", null);
                }

                var validacaoIntegracaoMh = ValidarIntegracaoMeioHomologado(@params);

                if (!validacaoIntegracaoMh.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        validacaoIntegracaoMh.Errors.FirstOrDefault()?.Message, null);

                #endregion

                var viagem = Mapper.Map<ViagemIntegrarRequestModel, Viagem>(@params);

                viagem.IdClienteOrigem = idClienteOri.Value;
                viagem.IdClienteDestino = idClienteDes.Value;
                viagem.IdClienteTomador = idClienteTomador;
                viagem.IdEmpresa = empresaId.Value;
                viagem.PesoSaida = @params.PesoSaida;
                viagem.ValorMercadoria = @params.ValorMercadoria;
                viagem.DataLancamento = DateTime.Now;
                viagem.NumeroDocumento = @params.NumeroDocumento;
                viagem.Produto = @params.Produto.ValueLimited(100);
                viagem.DataEmissao = @params.DataEmissao;
                viagem.Unidade = @params.Unidade;
                viagem.Quantidade = @params.Quantidade;
                viagem.IdFilial = filialId;
                viagem.NumeroNota = @params.NumeroNota;
                viagem.CNPJFilial = @params.CNPJFilial;
                viagem.RazaoSocialFilial = @params.RazaoSocialFilial;
                viagem.NumeroDocumentoComplementado = @params.NumeroDocumentoComplementado;
                viagem.CPFMotorista = @params.CPFMotorista?.Trim();
                viagem.DocumentoCliente = @params.DocumentoCliente;

                viagem.IRRPF = @params.IRRPF;
                viagem.INSS = @params.INSS;
                viagem.SESTSENAT = @params.SESTSENAT;
                viagem.PesoSaida = @params.PesoSaida;
                viagem.NaturezaCarga = @params.NaturezaCarga;

                #region Dados de pagamento

                if (@params.DadosPagamento != null) {
                    viagem.FormaPagamentoSemCartao = @params.DadosPagamento.FormaPagamento;
                    viagem.ContaCorrente = @params.DadosPagamento.Conta;
                    viagem.Agencia = @params.DadosPagamento.Agencia;
                    viagem.IdBanco = @params.DadosPagamento.CodigoBacen.ToIntSafe(1058);
                    if (!@params.DadosPagamento.CodigoBacen.IsNullOrWhiteSpace())
                        viagem.DescricaoBanco = _cadastrosApp.Bancos().Objeto
                            .FirstOrDefault(x => x.Id == @params.DadosPagamento.CodigoBacen)?.Nome;
                }
                else if (@params.DadosBancarioPagamentoSemCartao != null)
                {
                    viagem.FormaPagamentoSemCartao = @params.DadosBancarioPagamentoSemCartao.FormaPagamentoSemCartao;
                    viagem.ContaCorrente = @params.DadosBancarioPagamentoSemCartao.ContaCorrente;
                    viagem.Agencia = @params.DadosBancarioPagamentoSemCartao.Agencia;
                    viagem.IdBanco = @params.DadosBancarioPagamentoSemCartao.IdBanco;
                    viagem.DescricaoBanco = _cadastrosApp.Bancos().Objeto
                        .FirstOrDefault(x => x.Id == @params.DadosBancarioPagamentoSemCartao.IdBanco.ToStringSafe().TrimStart('0'))
                        ?.Nome;
                    viagem.TipoConta = @params.DadosBancarioPagamentoSemCartao.TipoConta ?? ETipoConta.ContaCorrente;
                }

                #endregion

                if (!@params.DataColeta.HasValue)
                    viagem.DataColeta = DateTime.Now.AddHours(1);

                if (!@params.DataPrevisaoEntrega.HasValue)
                    viagem.DataPrevisaoEntrega = DateTime.Now.AddDays(1);

                if (!string.IsNullOrWhiteSpace(@params.CPFMotorista))
                {
                    viagem.NomeMotorista = _motoristaService.GetPorCpfQueryable(@params.CPFMotorista.OnlyNumbers())
                        .Select(m => m.Nome)
                        .FirstOrDefault();

                    if (string.IsNullOrWhiteSpace(viagem.NomeMotorista))
                        viagem.NomeMotorista = _usuarioService.GetByCpfQuery(viagem.CPFMotorista)
                            .Select(u => u.Nome)
                            .FirstOrDefault();
                }
                
                if (@params.ViagemEventos != null && @params.ViagemEventos.Any())
                    _propriedadesPagamentoFreteViagem.SetarPropriedadesPagamentoFrete(@params, viagem, true);

                if (@params.IdCarga.HasValue)
                    viagem.ViagemCargas = new List<ViagemCarga>
                    {
                        new ViagemCarga
                        {
                            IdEmpresa = empresaId.Value,
                            IdCarga = @params.IdCarga.Value
                        }
                    };

                if ((@params.Carretas != null && @params.Carretas.Any()) ||
                    (@params.CarretasViagemV2 != null && @params.CarretasViagemV2.Any()))
                {
                    viagem.ViagemCarretas = new List<ViagemCarreta>();

                    if (@params.CarretasViagemV2 != null)
                    {
                        foreach (var paramsCarreta in @params.CarretasViagemV2)
                        {
                            var veiculoExistente =
                                _veiculoApp.VeiculoValidoIntegracao(paramsCarreta.Placa, empresaId.Value);

                            if (!veiculoExistente)
                                veiculoExistente = _veiculoApp.VeiculoValidoIntegracao(paramsCarreta.Placa);

                            var estaTentandoCadastrarVeiculo = @params.CarretasViagemV2.Any(x =>
                                x.Placa == paramsCarreta.Placa);

                            if (!veiculoExistente && !estaTentandoCadastrarVeiculo)
                                return new Retorno<ViagemIntegrarResponseModel>(false,
                                    $"A placa {paramsCarreta.Placa} não foi encontrada. ", null);

                            viagem.ViagemCarretas.Add(new ViagemCarreta
                            {
                                IdEmpresa = empresaId.Value,
                                Placa = paramsCarreta.Placa,
                                Rntrc = paramsCarreta.Rntrc
                            });
                        }
                    }
                    else
                    {
                        foreach (var paramsCarreta in @params.Carretas)
                        {
                            var veiculoExistente = _veiculoApp.VeiculoValidoIntegracao(paramsCarreta, empresaId.Value);

                            if (!veiculoExistente)
                                veiculoExistente = _veiculoApp.VeiculoValidoIntegracao(paramsCarreta);

                            var estaTentandoCadastrarVeiculo = @params.Carretas.Any(x =>
                                x == paramsCarreta);

                            if (!veiculoExistente && !estaTentandoCadastrarVeiculo)
                                return new Retorno<ViagemIntegrarResponseModel>(false,
                                    $"A placa {paramsCarreta} não foi encontrada. ", null);

                            viagem.ViagemCarretas.Add(new ViagemCarreta
                            {
                                IdEmpresa = empresaId.Value,
                                Placa = paramsCarreta,
                                Rntrc = string.Empty
                            });
                        }
                    }
                }

                if (@params.CarretasV2 != null && @params.CarretasV2.Any())
                {
                    if (@params.CarretasViagemV2 != null)
                    {
                        foreach (var paramsCarreta in @params.CarretasV2)
                        {
                            var veiculoExistente =
                                _veiculoApp.VeiculoValidoIntegracao(paramsCarreta.Placa, empresaId.Value);

                            if (!veiculoExistente)
                            {
                                if (!_veiculoApp.VeiculoValidoIntegracao(paramsCarreta.Placa))
                                {
                                    var veiculoRequest = new VeiculoCreateRequest();
                                    veiculoRequest.IdEmpresa = empresaId;
                                    veiculoRequest.IdProprietario =
                                        _proprietarioApp.GetIdPorCpfCnpj(paramsCarreta.CPFCNPJProprietario, empresaId);
                                    veiculoRequest.Placa = paramsCarreta.Placa;
                                    veiculoRequest.Chassi = paramsCarreta.Chassi;
                                    veiculoRequest.AnoFabricacao = paramsCarreta.AnoFabricacao;
                                    veiculoRequest.AnoModelo = paramsCarreta.AnoModelo;
                                    veiculoRequest.Marca = paramsCarreta.Marca;
                                    veiculoRequest.Modelo = paramsCarreta.Modelo;
                                    veiculoRequest.ComTracao = paramsCarreta.Comtracao;
                                    veiculoRequest.IdTipoCarreta = paramsCarreta.IdTipoCarreta;
                                    veiculoRequest.TipoContrato = paramsCarreta.TipoContrato;
                                    veiculoRequest.QuantidadeEixos = paramsCarreta.QuantidadeEixos;
                                    veiculoRequest.IdTipoCavalo = paramsCarreta.IdTipoCavalo;
                                    veiculoRequest.NumeroFrota = paramsCarreta.NumeroFrota;
                                    veiculoRequest.Municipio = paramsCarreta.Municipio;
                                    veiculoRequest.Renavam = paramsCarreta.Renavam;
                                    veiculoRequest.IdCidade = paramsCarreta.IBGECidade.HasValue
                                        ? _cidadeApp.GetIdCidade(paramsCarreta.IBGECidade.Value)
                                        : null;
                                    veiculoRequest.IdFilial = !string.IsNullOrWhiteSpace(paramsCarreta.CNPJFilial)
                                        ? _filialApp.GetIdPorCnpj(paramsCarreta.CNPJFilial)
                                        : null;
                                    veiculoRequest.TipoRodagem = paramsCarreta.TipoRodagem;
                                    veiculoRequest.TecnologiaRastreamento = paramsCarreta.TecnologiaRastreamento;
                                    veiculoRequest.IdOperacao = paramsCarreta.CodigodaOperacao;
                                    veiculoRequest.CorVeiculo = paramsCarreta.CorVeiculo;

                                    var veiculo = Mapper.Map<VeiculoCreateRequest, Veiculo>(veiculoRequest);

                                    var cadastrarVeiculo = _veiculoApp.Add(veiculo);
                                    if (!cadastrarVeiculo.IsValid)
                                    {
                                        return new Retorno<ViagemIntegrarResponseModel>(false,
                                            $"Erro ao salvar veículo {veiculo.Placa}: {cadastrarVeiculo.Errors.FirstOrDefault().Message}",
                                            null);
                                    }

                                    if (!viagem.ViagemCarretas.Any(x => x.Placa == paramsCarreta.Placa))
                                    {
                                        viagem.ViagemCarretas.Add(new ViagemCarreta
                                        {
                                            IdEmpresa = empresaId.Value,
                                            Placa = paramsCarreta.Placa,
                                            Rntrc = paramsCarreta.Rntrc
                                        });
                                    }
                                }
                            }
                        }
                    }
                }

                _viagemApp.AjustarProprietario(viagem);

                if (viagem.ViagemEventos != null && viagem.ViagemEventos.Any(c => c.HabilitarPagamentoCartao))
                {
                    var listaEventos = viagem.ViagemEventos.Select(c => c.TipoEventoViagem).ToList();
                    var produtoId = cartoesApp.GetIdProdutoCartaoFretePadrao();

                    //var retornoCartoes = ValidarCartaoMeioHomologado(viagem.CPFMotorista, viagem.CPFCNPJProprietario, viagem.IdEmpresa, out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, listaEventos);
                    var retornoCartoes =
                        new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App)
                            .ValidarCartaoMeioHomologado(
                                viagem.CPFMotorista, viagem.CPFCNPJProprietario, viagem.IdEmpresa, cartoesApp,
                                out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, listaEventos, isApi, true);

                    if (!retornoCartoes.Key)
                        return new Retorno<ViagemIntegrarResponseModel>(false, retornoCartoes.Value, null);

                    if (cartaoVinculadoProprietario.Cartoes != null && cartaoVinculadoMotorista.Cartoes != null)
                    {
                        var cartaoOrigem =
                            cartaoVinculadoProprietario.Cartoes.FirstOrDefault(x => x.Produto.Id == produtoId);
                        var cartaoDestino =
                            cartaoVinculadoMotorista.Cartoes.FirstOrDefault(x => x.Produto.Id == produtoId);

                        //Atribuições dos cartões viagem evento
                        foreach (var item in viagem.ViagemEventos)
                        {
                            if (item.IdViagemEvento == 0 && item.HabilitarPagamentoCartao)
                            {
                                item.CartaoDestino = cartaoDestino.Identificador.ToStringSafe();
                                item.CartaoOrigem = cartaoOrigem.Identificador.ToStringSafe();
                            }
                        }
                    }
                }

                if (viagem.ViagemEventos != null)
                    foreach (var evento in viagem.ViagemEventos)
                    {
                        if (evento.Status == EStatusViagemEvento.Baixado && evento.HabilitarPagamentoCartao)
                            evento.Status = EStatusViagemEvento.Aberto;

                        if (evento.Status == EStatusViagemEvento.Baixado && evento.HabilitaPagamentoTicket)
                            evento.Status = EStatusViagemEvento.Aberto;
                    }

                viagem.ViagemDocumentosFiscais = new List<ViagemDocumentoFiscal>();
                if (@params.DocumentosFiscais != null && @params.DocumentosFiscais.Any())
                {
                    foreach (var item in @params.DocumentosFiscais)
                    {
                        decimal numeroDocumento;
                        var validacaoDocumento = ValidarDocumentoFiscal(item, viagem.IdEmpresa, out numeroDocumento);
                        if (!validacaoDocumento.IsValid)
                            return new Retorno<ViagemIntegrarResponseModel>(false, validacaoDocumento.ToString(), null);

                        if (!item.IdClienteOrigem.HasValue || !item.IdClienteDestino.HasValue)
                        {
                            item.IdClienteOrigem = viagem.IdClienteOrigem;
                            item.IdClienteDestino = viagem.IdClienteDestino;
                        }

                        viagem.ViagemDocumentosFiscais.Add(new ViagemDocumentoFiscal
                        {
                            Valor = item.Valor >= 1 ? item.Valor : 1,
                            PesoSaida = item.PesoSaida >= 1 ? item.PesoSaida : 1,
                            NumeroDocumento = numeroDocumento,
                            Serie = string.IsNullOrWhiteSpace(item.Serie) ? "0" : item.Serie,
                            Chave = item.Chave,
                            TipoDocumento = item.TipoDocumento,
                            IdViagemDocumentoFiscal = item.IdViagemDocumentoFiscal ?? 0,
                            IdViagem = viagem.IdViagem,
                            IdClienteOrigem = item.IdClienteOrigem,
                            IdClienteDestino = item.IdClienteDestino
                        });
                    }
                }

                var validationResult = _viagemApp.Add(viagem);

                if (!validationResult.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToFormatedMessage(), null);

                if (@params.Pedagio != null)
                {
                    validationResult.Add(_viagemApp.AdicionarAtualizarViagemRota(viagem.IdViagem, empresaId.Value,
                        @params.Pedagio.Localizacoes, @params.Pedagio.IdentificadorHistorico,
                        @params.Pedagio.Fornecedor ?? FornecedorEnum.Desabilitado,
                        @params.Pedagio.TipoVeiculo ?? ETipoVeiculoPedagioEnum.Caminhao, @params.Pedagio.QtdEixos,
                        @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit));

                    if (!validationResult.IsValid)
                        return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToString(), null);
                }


                var declararCiotResult = new DeclararCiotResult {Resultado = EResultadoDeclaracaoCiot.Erro};

                var compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
                {
                    Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado,
                    Status = @params.Pedagio?.Fornecedor != FornecedorEnum.Desabilitado
                        ? EResultadoCompraPedagio.Erro
                        : EResultadoCompraPedagio.NaoRealizado
                };

                // A PARTIR DESTE PONTO, QUALQUER ERRO DE PROCESSO DEVE MANTER O RESULTADO DO JSON NO PADRÃO DE RETORNO PARA O CONSUMIDOR OBTER NOSSO ID DA VIAGEM

                // Bloqueio para liberação do gestor
                var retornoLiberacaoGestor =
                    _bloqueioGestorViagem.ValidarBloqueioGestor(@params, viagem,isApi ? EBloqueioOrigemTipo.API : EBloqueioOrigemTipo.Portal);

                var mensagemIntegacao =
                    _integracaoMeioHomologadoViagem.RealizarIntegracoesViagem(@params, viagem, cartoesApp,
                        out declararCiotResult, out compraPedagioResponse, cartaoVinculadoMotorista,
                        cartaoVinculadoProprietario, retornoLiberacaoGestor, isApi:isApi);

                #region Retorno

                var eventoGestorStatus = _viagemPendenteGestorService.GetStatusViagem(viagem.IdViagem);

                var estabelecimentoAdd = _viagemEstabelecimentoApp.GetPorViagem(viagem.IdViagem);
                var eventos = new List<ViagemIntegrarEventoResponseModel>();

                var viagemEventos = _viagemEventoApp.GetEventosViagem(viagem.IdViagem);

                //Pega os resultados das integracoes dos eventos pra colocar no retorno
                if (viagemEventos != null)
                {
                    var tasks = new List<Task>();

                    foreach (var evento in viagemEventos)
                    {
                        var transacao = _transacaoCartaoApp.GetAllByIdEvento(evento.IdViagemEvento)
                            .Where(c => ListCargas.Contains(c.TipoProcessamentoCartao))
                            .OrderByDescending(c => c.LineId).FirstOrDefault();

                        var transacaoTransf = _transacaoCartaoApp.GetAllByIdEvento(evento.IdViagemEvento)
                            .Where(c => ListTransf.Contains(c.TipoProcessamentoCartao))
                            .OrderByDescending(c => c.LineId).FirstOrDefault();
                        

                        tasks.Add(Task.Factory.StartNew(() =>
                        {
                            ERetornoOperacaoCartao eRetornoOperacao;

                            var statusOperacao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.NaoHabilitado, string.Empty);

                            if (evento.IdAbastecimentoticket.HasValue)
                            {
                                eRetornoOperacao = ERetornoOperacaoCartao.Sucesso;
                                statusOperacao = new StatusOperacaoCartaoModel(eRetornoOperacao, string.Empty);
                            }
                            else if (transacao != null)
                            {
                                switch (transacao.StatusPagamento)
                                {
                                    case EStatusPagamentoCartao.Aberto:
                                    case EStatusPagamentoCartao.Pendente:
                                        eRetornoOperacao = ERetornoOperacaoCartao.Pendente;
                                        break;

                                    case EStatusPagamentoCartao.Baixado:
                                        eRetornoOperacao = ERetornoOperacaoCartao.Sucesso;
                                        break;

                                    case EStatusPagamentoCartao.Erro:
                                        eRetornoOperacao = ERetornoOperacaoCartao.Erro;
                                        break;

                                    default:
                                        throw new ArgumentException(nameof(transacao.StatusPagamento));
                                }

                                if (transacaoTransf != null)
                                {
                                    if (transacaoTransf.StatusPagamento == EStatusPagamentoCartao.Erro)
                                    {
                                        statusOperacao = new StatusOperacaoCartaoModel(eRetornoOperacao, "Transferência do Proprietário para o Motorista não teve sucesso.");
                                    }
                                }
                                else
                                {
                                    statusOperacao = new StatusOperacaoCartaoModel(eRetornoOperacao, transacao.MensagemProcessamentoWs);
                                }
                            }
                            else if (evento.HabilitarPagamentoPix == true)
                            {
                                if (evento.Status == EStatusViagemEvento.Baixado)
                                {
                                    statusOperacao.Status = ERetornoOperacaoCartao.Sucesso;
                                    statusOperacao.Mensagem = "Pix efetuado com sucesso.";
                                }
                                else if (evento.Status == EStatusViagemEvento.Cancelado)
                                {
                                    statusOperacao.Status = ERetornoOperacaoCartao.Sucesso;
                                    statusOperacao.Mensagem = "Parcela Pix cancelada. Nenhum estorno será efetuado.";
                                }
                                else if (evento.Status == EStatusViagemEvento.Aberto)
                                {
                                    var statusTransacoesPix = _transacaoPixRepository
                                        .AsNoTracking().Where(c => c.IdViagemEvento == evento.IdViagemEvento)
                                        .Select(c => c.IdTransacaoPixStatus).ToList();
                                    var transacaoPixComErro = statusTransacoesPix.Count != 0 && statusTransacoesPix.All(c => c != ETransacaoPixStatus.Confirmado);
                                    statusOperacao.Status = transacaoPixComErro ? ERetornoOperacaoCartao.Erro : ERetornoOperacaoCartao.NaoHabilitado;
                                    statusOperacao.Mensagem = transacaoPixComErro ? "Pix não efetuado. Integre a parcela novamente." : "Parcela Pix cadastrada com status em aberto. Pagamento não efetuado.";
                                }
                                else if (evento.Status == EStatusViagemEvento.Bloqueado)
                                {
                                    statusOperacao.Status = ERetornoOperacaoCartao.Pendente;
                                    statusOperacao.Mensagem = evento.MotivoBloqueio;
                                }
                            }

                            var eventoModel = new ViagemIntegrarEventoResponseModel
                            {
                                OperacaoCartao = statusOperacao,
                                IdViagemEvento = evento.IdViagemEvento,
                                NumeroControle = evento.NumeroControle,
                                Token = evento.Token,
                                IdsViagemDocumento = evento.ViagemDocumentos.Select(x =>
                                    new ViagemIntegrarEventoDocumentosResponse
                                    {
                                        IdViagemDocumento = x.IdViagemDocumento, NumeroDocumento = x.NumeroDocumento
                                    }).ToList(),
                                IdsViagemOutrosDescontos = evento.ViagemValoresAdicionais
                                    .Where(x => x.Tipo == ETipoValorAdicional.Desconto)
                                    .Select(x => x.IdViagemValorAdicional).ToList(),
                                ViagemOutrosDescontos = evento.ViagemValoresAdicionais
                                    .Where(x => x.Tipo == ETipoValorAdicional.Desconto)
                                    .Select(x => new ViagemIntegrarEventoOutrosAcrescimosDescontos
                                    {
                                        IdViagemOutrosDescontos = x.IdViagemValorAdicional,
                                        NumeroDocumento = x.NumeroDocumento,
                                        Descricao = x.Descricao,
                                        Valor = x.Valor,
                                        CodigoERP = x.CodigoERP
                                    }).ToList(),
                                IdsViagemOutrosAcrescimos = evento.ViagemValoresAdicionais
                                    .Where(x => x.Tipo == ETipoValorAdicional.Acrescimo)
                                    .Select(x => x.IdViagemValorAdicional).ToList(),
                                ViagemOutrosAcrescimos = evento.ViagemValoresAdicionais
                                    .Where(x => x.Tipo == ETipoValorAdicional.Acrescimo)
                                    .Select(x => new ViagemIntegrarEventoOutrosAcrescimosDescontos
                                    {
                                        IdViagemOutrosDescontos = x.IdViagemValorAdicional,
                                        NumeroDocumento = x.NumeroDocumento,
                                        Descricao = x.Descricao,
                                        Valor = x.Valor,
                                        CodigoERP = x.CodigoERP
                                    }).ToList(),
                                TipoEventoViagem = evento.TipoEventoViagem,
                                ValorBruto = evento.ValorBruto
                            };

                            eventos.Add(eventoModel);
                        }));
                    }

                    Task.WaitAll(tasks.ToArray());
                }

                var retorno = new ViagemIntegrarResponseModel
                {
                    IdViagem = viagem.IdViagem,
                    BloqueioGestorNew = retornoLiberacaoGestor,
                    NumeroDocumento = viagem.NumeroDocumento,
                    IdsViagemEstabelecimento = estabelecimentoAdd?.Select(x => x.IdViagemEstabelecimento).ToList(),
                    Eventos = eventos,
                    CIOT = declararCiotResult,
                    Pedagio = compraPedagioResponse
                };

                var result = new Retorno<ViagemIntegrarResponseModel>(true, mensagemIntegacao.MensagemViagem, retorno);
                if (!retornoLiberacaoGestor.Sucesso)
                {
                    result.Sucesso = true;
                    result.Mensagem = retornoLiberacaoGestor.Mensagem;
                }

                return result;

                #endregion
            }
            catch (Exception e)
            {
                _logger.Error(e, "Erro ao integrar viagem");
                throw new Exception(
                    $"{nameof(IntegrarV2)}: {e.Message}{(e.InnerException != null ? " - " + e.InnerException.Message : string.Empty)}", e);
            }
        }

        private ValidationResult ValidarDocumentoFiscal(ViagemDocumentoFiscalModel documentoFiscal, int idEmpresa, out decimal numeroDocumento)
         {
             var validationResult = new ValidationResult();

             if (!decimal.TryParse(documentoFiscal.NumeroDocumento.ToString(CultureInfo.InvariantCulture), out numeroDocumento) || documentoFiscal.NumeroDocumento.ToString().Length > 10)
                 return validationResult.Add($"Número do documento fiscal: {documentoFiscal.NumeroDocumento} inválido");

             if (documentoFiscal.PesoSaida.ToString("N3", CultureInfo.InvariantCulture).Length > 13)
                 return validationResult.Add($"Peso de saída: {documentoFiscal.PesoSaida} inválido");

             if (!Enum.IsDefined(typeof(ETipoDocumento), documentoFiscal.TipoDocumento))
                 return validationResult.Add($"Tipo de documento fiscal: {documentoFiscal.TipoDocumento} inválido.");
             
             if (documentoFiscal.Chave?.Length > 150)
                 return validationResult.Add($"Tamanho da chave: {documentoFiscal.PesoSaida} excede o comprimento máximo de 150");

             if(documentoFiscal.IdClienteOrigem.HasValue && !_clienteApp.All().Any(x => x.IdCliente == documentoFiscal.IdClienteOrigem && x.IdEmpresa == idEmpresa))
                 return validationResult.Add($"Não foi encontrado IdClienteOrigem {documentoFiscal.IdClienteOrigem} na base de dados.");

             if(documentoFiscal.IdClienteDestino.HasValue && !_clienteApp.All().Any(x => x.IdCliente == documentoFiscal.IdClienteDestino && x.IdEmpresa == idEmpresa))
                 return validationResult.Add($"Não foi encontrado IdClienteDestino {documentoFiscal.IdClienteDestino} na base de dados.");

             return validationResult;
         }

         public Retorno<ViagemIntegrarResponseModel> IntegrarV3(ViagemIntegrarRequestModel @params, bool isApi)
         {
             #region Inicializacoes

             IEmpresaApp empresaApp;
             IFilialApp filialApp;
             IVeiculoApp veiculoApp;
             IViagemEventoApp viagemEventoApp;
             IViagemEstabelecimentoApp viagemEstabelecimentoApp;
             ITransacaoCartaoApp transacaoCartaoApp;
             ICartoesApp cartoesApp;

             int? filialId = null;

             if (string.IsNullOrEmpty(@params.CNPJEmpresa))
                 @params.CNPJEmpresa = @params.CNPJAplicacao;

             #endregion

             try
             {
                 #region Injecoes de dependencias

                 empresaApp = _empresaApp;
                 var empresa = empresaApp.Get(@params.CNPJEmpresa);
                 var empresaId = empresa?.IdEmpresa;

                 if (empresaId == null)
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Empresa inválida.", null);

                 filialApp = _filialApp;
                 veiculoApp = _veiculoApp;
                 viagemEventoApp = _viagemEventoApp;
                 viagemEstabelecimentoApp = _viagemEstabelecimentoApp;
                 transacaoCartaoApp = _transacaoCartaoApp;

                 cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, @params.CNPJEmpresa, @params.DocumentoUsuarioAudit,
                     @params.NomeUsuarioAudit);

                 #endregion

                 #region Buscas necessárias para integração

                 var empresaValidacaoPagFrete = empresaApp.EmpresaValidaPagamentoFrete(empresaId.Value);
                 var idClienteOri = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteOrigem, empresaId.Value);
                 var idClienteDes = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteDestino, empresaId.Value);
                 var idClienteTomador = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteTomador, empresaId.Value);

                 if (!string.IsNullOrWhiteSpace(@params.CodigoFilial))
                 {
                     filialId = filialApp.GetIdPorCodigoFilial(empresaId.Value, @params.CNPJFilial,
                         @params.CodigoFilial);

                     if (filialId.HasValue && string.IsNullOrWhiteSpace(@params.CNPJFilial))
                     {
                         var filialcnpj = filialApp.GetCnpjPorId(filialId.Value);
                         @params.CNPJFilial = filialcnpj;
                     }
                 }

                 if (!filialId.HasValue)
                     if (!string.IsNullOrEmpty(@params.CNPJFilial))
                         filialId = filialApp.GetIdPorCnpj(@params.CNPJFilial);

                 if (!filialId.HasValue) @params.CNPJFilial = null;

                 var cartaoVinculadoProprietario = new CartaoVinculadoPessoaListResponse();
                 var cartaoVinculadoMotorista = new CartaoVinculadoPessoaListResponse();

                 #endregion

                 #region Validacoes

                 if (ConfigurationManager.AppSettings["TestarErro"] == "true")
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Teste de erro para simulação.", null);

                 if (!idClienteOri.HasValue)
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Cliente de origem inválido.", null);

                 if (!idClienteDes.HasValue)
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Cliente de destino inválido.", null);

                 if (@params.Unidade == EUnidadeMedida.Saca && @params.Quantidade <= 0)
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Quantidade de sacas inválida.", null);

                 if (!string.IsNullOrEmpty(@params.DocumentoCliente) && @params.DocumentoCliente.Length > 100)
                     return new Retorno<ViagemIntegrarResponseModel>(false,
                         @"O campo DocumentoCliente não pode ter mais de 100 caracteres.", null);

                 if (empresaValidacaoPagFrete && @params.PesoSaida.HasValue && @params.PesoSaida.Value <= 0)
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Peso de saida deve ser maior que 0.",
                         null);

                 if (@params.PesoSaida.HasValue &&
                     @params.PesoSaida.Value.ToString("N3", CultureInfo.InvariantCulture).Length > 13)
                     return new Retorno<ViagemIntegrarResponseModel>(false,
                         $"Peso de saída: {@params.PesoSaida} inválido",
                         null);

                 if (@params.HabilitarDeclaracaoCiot)
                 {
                     if (@params.NaturezaCarga == null || @params.NaturezaCarga == 0)
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             @"Natureza da carga é obrigatória ao integrar viagem com recurso de CIOT habilitado.",
                             null);

                     if (@params.ViagemEventos == null)
                     {
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             @"Ao menos um evento é necessário ao integrar viagem com recurso de CIOT habilitado.",
                             null);
                     }

                     if (@params.ViagemRegra == null)
                     {
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             @"Ao menos uma regra é necessária ao integrar viagem com recurso de CIOT habilitado.",
                             null);
                     }
                 }

                 if (@params.IdCarga.HasValue && @params.IdCarga <= 0)
                     return new Retorno<ViagemIntegrarResponseModel>(false, @"Código da carga inválido.", null);

                 if (empresaValidacaoPagFrete && @params.ViagemEventos == null)
                     return new Retorno<ViagemIntegrarResponseModel>(false, "Eventos não informados.", null);

                 if (!string.IsNullOrWhiteSpace(@params.NumeroControle) &&
                     _viagemApp.ViagemCadastrada(empresaId.Value, @params.NumeroControle))
                     return new Retorno<ViagemIntegrarResponseModel>(false,
                         $"Viagem {@params.NumeroControle} já cadastrada.", null);

                 if (@params.ViagemEventos != null && @params.ViagemEventos.Count > 0)
                 {
                     var numerosControleRepetidos = @params.ViagemEventos.Where(x => x.NumeroControle != null)
                         .Select(x => x.NumeroControle)
                         .GroupBy(x => x)
                         .Where(g => g.Count() > 1)
                         .Select(y => y.Key)
                         .ToList();

                     if (numerosControleRepetidos.Any())
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             numerosControleRepetidos.Count == 1
                                 ? $"O número de controle do evento {numerosControleRepetidos.First()} não pode ser repetido na mesma viagem."
                                 : $"Os números de controle dos eventos {string.Join(", ", numerosControleRepetidos)} não podem ser repetidos na mesma viagem.",
                             null);
                 }

                 if (@params.Pedagio != null)
                 {
                     if (@params.Pedagio.Fornecedor != FornecedorEnum.Desabilitado &&
                         @params.Pedagio.ValorPedagio.HasValue && @params.Pedagio.ValorPedagio <= 0)
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             "Solicitação de pedágio incorreta. Para definir o valor de pedágio, deve ser indicado um valor maior que R$ 0,00.",
                             null);

                     if (@params.ValorPedagio > 0 == false && @params.Pedagio.ValorPedagio > 0 == false &&
                         !@params.Pedagio.IdentificadorHistorico.HasValue &&
                         @params.Pedagio.Localizacoes?.Any() == true && @params.Pedagio.QtdEixos < 2)
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             "Quantidade de eixos inválido, deve ser indicado um valor igual ou maior que 2.", null);
                 }

                 if (empresaValidacaoPagFrete && @params.ViagemEventos == null)
                     if (@params.HabilitarDeclaracaoCiot ||
                         @params.DadosPagamento?.FormaPagamento == EViagemFormaPagamento.Cartao ||
                         @params.Pedagio != null)
                         if (string.IsNullOrWhiteSpace(@params.CNPJFilial))
                             return new Retorno<ViagemIntegrarResponseModel>(false,
                                 "Para integrações com CIOT, Cartão ou Pedágio, deve ser informado o código ou CNPJ da filial da viagem.",
                                 null);

                 if (@params.HabilitarDeclaracaoCiot)
                 {
                     var idProprietario =
                         _proprietarioApp.GetIdPorCpfCnpj(@params.CPFCNPJProprietario, empresaId.Value);

                     if (_parametrosApp.ValidaCnpjCpfProprietarioNaViagem() && !idProprietario.HasValue)
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             $"O proprietário {@params.CPFCNPJProprietario.ToCpfOrCnpj()} não foi encontrado na base de dados",
                             null);
                 }

                 /*var retornarAvisoTagDescontinuada = false;
                 if (@params.ViagemEventos.Any())
                     foreach (var viagemEvento in @params.ViagemEventos)
                         if (viagemEvento.HabilitarPagamentoCartao)
                             retornarAvisoTagDescontinuada = true;

                 if (retornarAvisoTagDescontinuada)
                     return new Retorno<ViagemIntegrarResponseModel>(false,
                         "A tag 'HabilitarPagamentoCartao' foi descontinuada, de acordo com as novas regras da ANTT, a forma de pagamento será realizaada conforme o campo Forma de pagamento nos dados do pagamento",
                         null);*/

                 if (!string.IsNullOrWhiteSpace(@params.Placa))
                 {
                     @params.Placa = @params.Placa.RemoveSpecialCharacters().Trim().ToUpper();
                     if (@params.Placa.Length != 7)
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             "O campo Placa deve conter 7 caracteres", null);
                 }

                 if (@params.Carretas?.Any() == true)
                     for (var i = 0; @params.Carretas.Count > i; i++)
                     {
                         @params.Carretas[i] = @params.Carretas[i].RemoveSpecialCharacters().Trim().ToUpper();
                         if (@params.Carretas[i].Length != 7)
                             return new Retorno<ViagemIntegrarResponseModel>(false,
                                 "Os itens de Carretas devem conter 7 caracteres", null);
                     }

                 var validacaoPlacasRepetidas = ValidarPlacasRepetidas(@params);

                 if (!validacaoPlacasRepetidas.IsValid)
                     return new Retorno<ViagemIntegrarResponseModel>(false,
                         validacaoPlacasRepetidas.Errors.FirstOrDefault()?.Message, null);

                 if (@params.ViagemEventos != null && @params.ViagemEventos.Any())
                 {
                     if (@params.ViagemEventos.Any(ve => ve.ValorPagamento < 0))
                         return new Retorno<ViagemIntegrarResponseModel>(false,
                             "Valor do evento informado não pode ser negativo.", null);

                     if (@params.ViagemEventos.Any(ve => ve.ValorPagamento == 0))
                         return new Retorno<ViagemIntegrarResponseModel>(false, "Valor do evento não informado.", null);
                 }

                 var validacaoIntegracaoMh = ValidarIntegracaoMeioHomologado(@params);

                 if (!validacaoIntegracaoMh.IsValid)
                     return new Retorno<ViagemIntegrarResponseModel>(false,
                         validacaoIntegracaoMh.Errors.FirstOrDefault()?.Message, null);

                 #endregion

                 var viagem = Mapper.Map<ViagemIntegrarRequestModel, Viagem>(@params);

                 viagem.IdClienteOrigem = idClienteOri.Value;
                 viagem.IdClienteDestino = idClienteDes.Value;
                 viagem.IdClienteTomador = idClienteTomador;
                 viagem.IdEmpresa = empresaId.Value;
                 viagem.PesoSaida = @params.PesoSaida;
                 viagem.ValorMercadoria = @params.ValorMercadoria;
                 viagem.DataLancamento = DateTime.Now;
                 viagem.NumeroDocumento = @params.NumeroDocumento;
                 viagem.Produto = @params.Produto.ValueLimited(100);
                 viagem.DataEmissao = @params.DataEmissao;
                 viagem.Unidade = @params.Unidade;
                 viagem.Quantidade = @params.Quantidade;
                 viagem.IdFilial = filialId;
                 viagem.NumeroNota = @params.NumeroNota;
                 viagem.CNPJFilial = @params.CNPJFilial;
                 viagem.RazaoSocialFilial = @params.RazaoSocialFilial;
                 viagem.NumeroDocumentoComplementado = @params.NumeroDocumentoComplementado;
                 viagem.CPFMotorista = @params.CPFMotorista?.Trim();

                 viagem.IRRPF = @params.IRRPF;
                 viagem.INSS = @params.INSS;
                 viagem.SESTSENAT = @params.SESTSENAT;
                 viagem.PesoSaida = @params.PesoSaida;
                 viagem.NaturezaCarga = @params.NaturezaCarga;

                 viagem.CodigoTipoCarga = @params.CodigoTipoCarga ?? _parametrosApp.GetTipoCargaAnttDefault();
                 viagem.DistanciaViagem = @params.DistanciaViagem ?? 0;

                 viagem.CepOrigem = string.IsNullOrEmpty(@params.CepOrigem)
                     ? _clienteApp.GetQuery(idClienteOri.Value).Select(o => o.CEP).FirstOrDefault()
                     : @params.CepOrigem;

                 viagem.CepDestino = string.IsNullOrEmpty(@params.CepDestino)
                     ? _clienteApp.GetQuery(idClienteDes.Value).Select(o => o.CEP).FirstOrDefault()
                     : @params.CepDestino;

                 viagem.AltoDesempenho = @params.DadosAntt?.AltoDesempenho ?? false;
                 viagem.DestinacaoComercial = @params.DadosAntt?.DestinacaoComercial ?? false;
                 viagem.FreteRetorno = @params.DadosAntt?.FreteRetorno ?? false;

                 if (viagem.FreteRetorno)
                 {
                     viagem.CepRetorno = @params.DadosAntt?.CepRetorno;
                     viagem.DistanciaRetorno = @params.DadosAntt?.DistanciaRetorno;
                 }

                 if (@params.DadosPagamento == null ||
                     @params.DadosPagamento.FormaPagamento == EViagemFormaPagamento.Indefinido)
                     viagem.FormaPagamento = EViagemFormaPagamento.Outros;
                 else
                     viagem.FormaPagamento = @params.DadosPagamento.FormaPagamento;

                 var pagamentosEmConta = new List<EViagemFormaPagamento>
                 {
                     EViagemFormaPagamento.ContaCorrente, EViagemFormaPagamento.ContaPagamento,
                     EViagemFormaPagamento.ContaPoupanca
                 };

                 if (pagamentosEmConta.Contains(viagem.FormaPagamento))
                     viagem.ViagemPagamentoConta = new ViagemPagamentoConta
                     {
                         CpfCnpjConta = viagem.CPFCNPJProprietario,
                         CodigoBacenBanco = @params.DadosPagamento?.CodigoBacen,
                         Agencia = @params.DadosPagamento?.Agencia,
                         Conta = @params.DadosPagamento?.Conta
                     };
                 else
                     viagem.ViagemPagamentoConta = new ViagemPagamentoConta {CpfCnpjConta = viagem.CPFCNPJProprietario};

                 if (!@params.DataColeta.HasValue)
                     viagem.DataColeta = DateTime.Now.AddHours(1);

                 if (!@params.DataPrevisaoEntrega.HasValue)
                     viagem.DataPrevisaoEntrega = DateTime.Now.AddDays(1);

                 if (!string.IsNullOrWhiteSpace(@params.CPFMotorista))
                 {
                     viagem.NomeMotorista = _motoristaService.GetAllByIdEmpresa(viagem.IdEmpresa)
                         .Where(m => m.CPF == @params.CPFMotorista)
                         .Select(m => m.Nome)
                         .FirstOrDefault();

                     if (string.IsNullOrWhiteSpace(viagem.NomeMotorista))
                         viagem.NomeMotorista = _usuarioService.GetByCpfQuery(viagem.CPFMotorista)
                             .Select(u => u.Nome)
                             .FirstOrDefault();
                 }

                 if (@params.ViagemRegra != null && @params.ViagemEventos != null && @params.ViagemRegra.Any() &&
                     @params.ViagemEventos.Any())
                     _propriedadesPagamentoFreteViagem.SetarPropriedadesPagamentoFrete(
                         @params, viagem, true);

                 // TODO: Verificar se vai funcionar

                 // O habilitar pagamento no cartão só será true se a forma de pagamento for cartão
                 if (viagem.ViagemEventos.Any())
                     foreach (var viagemEvento in viagem.ViagemEventos)
                         viagemEvento.HabilitarPagamentoCartao = viagem.FormaPagamento == EViagemFormaPagamento.Cartao;

                 if (@params.IdCarga.HasValue)
                     viagem.ViagemCargas = new List<ViagemCarga>
                     {
                         new ViagemCarga
                         {
                             IdEmpresa = empresaId.Value,
                             IdCarga = @params.IdCarga.Value
                         }
                     };

                 if ((@params.Carretas != null && @params.Carretas.Any()) ||
                     (@params.CarretasViagemV2 != null && @params.CarretasViagemV2.Any()))
                 {
                     viagem.ViagemCarretas = new List<ViagemCarreta>();

                     if (@params.CarretasViagemV2 != null)
                     {
                         foreach (var paramsCarreta in @params.CarretasViagemV2)
                         {
                             if (paramsCarreta == null)
                                 continue;

                             var veiculoExistente =
                                 veiculoApp.VeiculoValidoIntegracao(paramsCarreta.Placa, empresaId.Value);

                             if (!veiculoExistente)
                                 veiculoExistente = veiculoApp.VeiculoValidoIntegracao(paramsCarreta.Placa);

                             if (!veiculoExistente)
                                 return new Retorno<ViagemIntegrarResponseModel>(false,
                                     $"A placa {paramsCarreta.Placa} não foi encontrada. ", null);

                             viagem.ViagemCarretas.Add(new ViagemCarreta
                             {
                                 IdEmpresa = empresaId.Value,
                                 Placa = paramsCarreta.Placa,
                                 Rntrc = paramsCarreta.Rntrc
                             });
                         }
                     }
                     else
                     {
                         foreach (var paramsCarreta in @params.Carretas)
                         {
                             if (paramsCarreta == null)
                                 continue;

                             var veiculoExistente = veiculoApp.VeiculoValidoIntegracao(paramsCarreta, empresaId.Value);

                             if (!veiculoExistente)
                                 veiculoExistente = veiculoApp.VeiculoValidoIntegracao(paramsCarreta);

                             if (!veiculoExistente)
                                 return new Retorno<ViagemIntegrarResponseModel>(false,
                                     $"A placa {paramsCarreta} não foi encontrada. ", null);

                             viagem.ViagemCarretas.Add(new ViagemCarreta
                             {
                                 IdEmpresa = empresaId.Value,
                                 Placa = paramsCarreta,
                                 Rntrc = string.Empty
                             });
                         }
                     }
                 }

                 _viagemApp.AjustarProprietario(viagem);

                 if (viagem.ViagemEventos != null && viagem.ViagemEventos.Any(c => c.HabilitarPagamentoCartao))
                 {
                     var listaEventos = viagem.ViagemEventos.Select(c => c.TipoEventoViagem).ToList();

                     var retornoCartoes =
                         new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App)
                             .ValidarCartaoMeioHomologado(viagem.CPFMotorista, viagem.CPFCNPJProprietario,
                                 viagem.IdEmpresa, cartoesApp, out cartaoVinculadoMotorista,
                                 out cartaoVinculadoProprietario, listaEventos, buscarCartoesBloqueados:true);

                     if (!retornoCartoes.Key)
                         return new Retorno<ViagemIntegrarResponseModel>(false, retornoCartoes.Value, null);
                 }

                 if (viagem.ViagemEventos != null)
                     foreach (var evento in viagem.ViagemEventos)
                         if (evento.Status == EStatusViagemEvento.Baixado && evento.HabilitarPagamentoCartao)
                             evento.Status = EStatusViagemEvento.Aberto;

                 viagem.ViagemDocumentosFiscais = new List<ViagemDocumentoFiscal>();
                 if (@params.DocumentosFiscais != null && @params.DocumentosFiscais.Any())
                 {
                     foreach (var item in @params.DocumentosFiscais)
                     {
                         decimal numeroDocumento;
                         var validacaoDocumento = ValidarDocumentoFiscal(item, viagem.IdEmpresa, out numeroDocumento);
                         if (!validacaoDocumento.IsValid)
                             return new Retorno<ViagemIntegrarResponseModel>(false, validacaoDocumento.ToString(),
                                 null);

                         if (!item.IdClienteOrigem.HasValue || !item.IdClienteDestino.HasValue)
                         {
                             item.IdClienteOrigem = viagem.IdClienteOrigem;
                             item.IdClienteDestino = viagem.IdClienteDestino;
                         }

                         viagem.ViagemDocumentosFiscais.Add(new ViagemDocumentoFiscal
                         {
                             Valor = item.Valor >= 1 ? item.Valor : 1,
                             PesoSaida = item.PesoSaida >= 1 ? item.PesoSaida : 1,
                             NumeroDocumento = numeroDocumento,
                             Serie = string.IsNullOrWhiteSpace(item.Serie) ? "0" : item.Serie,
                             Chave = item.Chave,
                             TipoDocumento = item.TipoDocumento,
                             IdViagemDocumentoFiscal = item.IdViagemDocumentoFiscal ?? 0,
                             IdViagem = viagem.IdViagem,
                             IdClienteOrigem = item.IdClienteOrigem,
                             IdClienteDestino = item.IdClienteDestino
                         });
                     }
                 }

                 var validationResult = _viagemApp.Add(viagem);

                 if (!validationResult.IsValid)
                     return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToFormatedMessage(), null);

                 if (@params.Pedagio != null)
                 {
                     validationResult.Add(_viagemApp.AdicionarAtualizarViagemRota(viagem.IdViagem, empresaId.Value,
                         @params.Pedagio.Localizacoes, @params.Pedagio.IdentificadorHistorico,
                         @params.Pedagio.Fornecedor ?? FornecedorEnum.Desabilitado,
                         @params.Pedagio.TipoVeiculo ?? ETipoVeiculoPedagioEnum.Caminhao, @params.Pedagio.QtdEixos,
                         @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit));

                     if (!validationResult.IsValid)
                         return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToString(), null);
                 }

                 var declararCiotResult = new DeclararCiotResult {Resultado = EResultadoDeclaracaoCiot.Erro};

                 var compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
                 {
                     Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado,
                     Status = @params.Pedagio?.Fornecedor != FornecedorEnum.Desabilitado
                         ? EResultadoCompraPedagio.Erro
                         : EResultadoCompraPedagio.NaoRealizado
                 };

                 // A PARTIR DESTE PONTO, QUALQUER ERRO DE PROCESSO DEVE MANTER O RESULTADO DO JSON NO PADRÃO DE RETORNO PARA O CONSUMIDOR OBTER NOSSO ID DA VIAGEM

                 // Bloqueio para liberação do gestor
                 var retornoLiberacaoGestor =
                     _bloqueioGestorViagem.ValidarBloqueioGestor(@params, viagem,isApi ? EBloqueioOrigemTipo.API : EBloqueioOrigemTipo.Portal);

                 var mensagemIntegacao =
                     _integracaoMeioHomologadoViagem.RealizarIntegracoesViagem(@params, viagem, cartoesApp,
                         out declararCiotResult, out compraPedagioResponse, cartaoVinculadoMotorista,
                         cartaoVinculadoProprietario, retornoLiberacaoGestor);

                 #region Retorno

                 var eventoGestorStatus = _viagemPendenteGestorService.GetStatusViagem(viagem.IdViagem);

                 var estabelecimentoAdd = viagemEstabelecimentoApp.GetPorViagem(viagem.IdViagem);
                 var eventos = new List<ViagemIntegrarEventoResponseModel>();

                 var viagemEventos = viagemEventoApp.GetEventosViagem(viagem.IdViagem);

                 if (viagemEventos != null)
                 {
                     var tasks = new List<Task>();

                     foreach (var evento in viagemEventos)
                     {
                         var transacao = transacaoCartaoApp.GetAllByIdEvento(evento.IdViagemEvento)
                             .OrderByDescending(c => c.LineId).FirstOrDefault();

                         tasks.Add(Task.Factory.StartNew(() =>
                         {
                             var statusOperacao =
                                 new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.NaoHabilitado, string.Empty);

                             if (transacao != null)
                             {
                                 ERetornoOperacaoCartao eRetornoOperacao;

                                 switch (transacao.StatusPagamento)
                                 {
                                     case EStatusPagamentoCartao.Aberto:
                                     case EStatusPagamentoCartao.Pendente:
                                         eRetornoOperacao = ERetornoOperacaoCartao.Pendente;
                                         break;

                                     case EStatusPagamentoCartao.Baixado:
                                         eRetornoOperacao = ERetornoOperacaoCartao.Sucesso;
                                         break;

                                     case EStatusPagamentoCartao.Erro:
                                         eRetornoOperacao = ERetornoOperacaoCartao.Erro;
                                         break;

                                     default:
                                         throw new ArgumentException(nameof(transacao.StatusPagamento));
                                 }

                                 statusOperacao = new StatusOperacaoCartaoModel(eRetornoOperacao,
                                     transacao.MensagemProcessamentoWs);
                             }

                             var eventoModel = new ViagemIntegrarEventoResponseModel
                             {
                                 OperacaoCartao = statusOperacao,
                                 IdViagemEvento = evento.IdViagemEvento,
                                 NumeroControle = evento.NumeroControle,
                                 Token = evento.Token,
                                 IdsViagemDocumento = evento.ViagemDocumentos.Select(x =>
                                     new ViagemIntegrarEventoDocumentosResponse
                                     {
                                         IdViagemDocumento = x.IdViagemDocumento, NumeroDocumento = x.NumeroDocumento
                                     }).ToList(),
                                 IdsViagemOutrosDescontos = evento.ViagemValoresAdicionais
                                     .Where(x => x.Tipo == ETipoValorAdicional.Desconto)
                                     .Select(x => x.IdViagemValorAdicional).ToList(),
                                 ViagemOutrosDescontos = evento.ViagemValoresAdicionais
                                     .Where(x => x.Tipo == ETipoValorAdicional.Desconto)
                                     .Select(x => new ViagemIntegrarEventoOutrosAcrescimosDescontos
                                     {
                                         IdViagemOutrosDescontos = x.IdViagemValorAdicional,
                                         NumeroDocumento = x.NumeroDocumento,
                                         Descricao = x.Descricao,
                                         Valor = x.Valor,
                                         CodigoERP = x.CodigoERP
                                     }).ToList(),
                                 IdsViagemOutrosAcrescimos = evento.ViagemValoresAdicionais
                                     .Where(x => x.Tipo == ETipoValorAdicional.Acrescimo)
                                     .Select(x => x.IdViagemValorAdicional).ToList(),
                                 ViagemOutrosAcrescimos = evento.ViagemValoresAdicionais
                                     .Where(x => x.Tipo == ETipoValorAdicional.Acrescimo)
                                     .Select(x => new ViagemIntegrarEventoOutrosAcrescimosDescontos
                                     {
                                         IdViagemOutrosDescontos = x.IdViagemValorAdicional,
                                         NumeroDocumento = x.NumeroDocumento,
                                         Descricao = x.Descricao,
                                         Valor = x.Valor,
                                         CodigoERP = x.CodigoERP
                                     }).ToList(),
                                 TipoEventoViagem = evento.TipoEventoViagem,
                                 ValorBruto = evento.ValorBruto
                             };

                             eventos.Add(eventoModel);
                         }));
                     }

                     Task.WaitAll(tasks.ToArray());
                 }

                 var retorno = new ViagemIntegrarResponseModel
                 {
                     IdViagem = viagem.IdViagem,
                     BloqueioGestorNew = retornoLiberacaoGestor,
                     NumeroDocumento = viagem.NumeroDocumento,
                     IdsViagemEstabelecimento = estabelecimentoAdd?.Select(x => x.IdViagemEstabelecimento).ToList(),
                     Eventos = eventos,
                     CIOT = declararCiotResult,
                     Pedagio = compraPedagioResponse
                 };

                 var result = new Retorno<ViagemIntegrarResponseModel>(true, mensagemIntegacao.MensagemViagem, retorno);
                 if (!retornoLiberacaoGestor.Sucesso)
                 {
                     result.Sucesso = true;
                     result.Mensagem = retornoLiberacaoGestor.Mensagem;
                 }

                 return result;

                 #endregion
             }
             catch (Exception e)
             {
                 _logger.Error(e, "Erro ao integrar viagem");
                 throw new Exception(
                     $"{nameof(IntegrarV3)}: {e.Message}{(e.InnerException != null ? " - " + e.InnerException.Message : string.Empty)}");
             }
         }

        private static ValidationResult ValidarPlacasRepetidas(ViagemIntegrarRequestModel viagemRequest)
        {
            var validationResult = new ValidationResult();
            var listaPlacas = new List<string>();

            if (viagemRequest.Carretas != null && viagemRequest.Carretas.Any())
            {
                listaPlacas.Add(viagemRequest.Placa);

                if (viagemRequest.Carretas != null && viagemRequest.Carretas.Any())
                    listaPlacas.AddRange(viagemRequest.Carretas);

                if (listaPlacas.Count != listaPlacas.Distinct().Count())
                    return validationResult.Add("A operação de transporte contém mais de um veículo com a mesma placa");
            }

            if (viagemRequest.CarretasViagemV2 != null && viagemRequest.CarretasViagemV2.Any())
            {
                listaPlacas.Add(viagemRequest.Placa);
                listaPlacas.AddRange(viagemRequest.CarretasViagemV2.Select(o => o.Placa));
            }

            return listaPlacas.Count != listaPlacas.Distinct().Count()
                ? validationResult.Add("A operação de transporte contém mais de um veículo com a mesma placa")
                : validationResult;
        }

        private ValidationResult ValidarIntegracaoMeioHomologado(ViagemIntegrarRequestModel @params)
        {
            var validarToken = false;
            var validation = new ValidationResult();

            if (@params.HabilitarDeclaracaoCiot)
                validarToken = true;

            if (@params.ViagemEventos != null && @params.ViagemEventos.Any(o => o.HabilitarPagamentoCartao))
                validarToken = true;

            if (@params.Pedagio?.Fornecedor != FornecedorEnum.Desabilitado)
                validarToken = true;

            if (!validarToken)
                return validation;

            var empresaId = _empresaApp.GetIdPorCnpj(@params.CNPJAplicacao);

            if (empresaId != null)
            {
                var empresaQuery = _empresaApp.GetQuery(empresaId.Value);

                if (empresaQuery.Any())
                {
                    var token = empresaQuery.Select(o => o.TokenMicroServices).FirstOrDefault();

                    if (string.IsNullOrEmpty(token))
                        validation.Add("Empresa não integrada com Meio Homologado", EFaultType.Error);
                }
                else
                {
                    validation.Add("Empresa não integrada com Meio Homologado", EFaultType.Error);
                }
            }
            else
            {
                validation.Add($"Empresa CNPJ {@params.CNPJAplicacao} não encontrada na base de dados", EFaultType.Error);
            }

            return validation;
        }

        public Retorno<PedagioAvulsoResponse> IntegrarPedagioAvulso(PedagioAvulsoRequest @params)
        {
            #region Validações
            ValidationResult validationResult;
            
            var empresaApp = _empresaApp;

            var empresa = empresaApp.Get(@params.IdEmpresa);

            if (empresa == null)
                return new Retorno<PedagioAvulsoResponse>(false,"Empresa inválida.",null );

            if (empresa.TokenMicroServices == null)
                return new Retorno<PedagioAvulsoResponse>(false,"Empresa não integrada com Meio Homologado.", null);

            if (@params.Veiculo == null)
                return new Retorno<PedagioAvulsoResponse>(false,"Veículo não encontrado.", null);

            if (@params.DataInicioFrete >= @params.DataFimFrete)
                return new Retorno<PedagioAvulsoResponse>(false,"Data início do frete não pode ser menor ou igual que a data final do frete.", null);
            var veiculoTracao = _veiculoApp.QueryById(@params.Veiculo.IdVeiculo).FirstOrDefault();

            if(veiculoTracao == null)
                return new Retorno<PedagioAvulsoResponse>(false,"Veículo não encontrado.", null);
            if(!@params.IdProprietario.HasValue)
                return new Retorno<PedagioAvulsoResponse>(false,"Proprietário não informado.", null);

            var proprietario = _proprietarioApp.Get(@params.IdProprietario.Value);

            if(proprietario == null)
                return new Retorno<PedagioAvulsoResponse>(false,"Proprietário não encontrado.", null);

            if (@params.Pedagio.Fornecedor != FornecedorEnum.Moedeiro)
            {
                if (@params.Pedagio.Localizacoes == null || !@params.Pedagio.Localizacoes.Any())
                    return new Retorno<PedagioAvulsoResponse>(false,"Localizações inválidas.", null);
            }

            if (@params.Pedagio.Localizacoes != null && @params.Pedagio.Localizacoes.Any())
            {
                if (@params.Pedagio.Localizacoes.Count < 2)
                    return new Retorno<PedagioAvulsoResponse>(false,"Localizações inválidas.", null);
            }

            var usuarioDocAudit = !string.IsNullOrWhiteSpace(@params.UsuarioAudit.DocumentoUsuarioAudit)
                ? @params.UsuarioAudit.DocumentoUsuarioAudit
                : CartoesService.AuditDocIntegracao;

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, empresa.CNPJ,
                usuarioDocAudit, @params.UsuarioAudit.NomeUsuarioAudit);

            if (@params.IdMotorista.HasValue)
            {
                var motorista = _motoristaService
                    .GetAllByIdEmpresa(empresa.IdEmpresa)
                    .FirstOrDefault(m => m.IdMotorista == @params.IdMotorista);

                if (motorista == null)
                    return new Retorno<PedagioAvulsoResponse>(false,$"Motorista com ID: {@params.IdMotorista} não encontrado!", null);

                var viagemEmAberto = _viagemApp.GetQuery().FirstOrDefault(x =>
                    (x.CPFMotorista == motorista.CPF.Trim()
                     || x.Placa == veiculoTracao.Placa.Trim())
                    && x.StatusViagem == EStatusViagem.Aberto
                    && x.Empresa.BloquearNovaViagem
                    && x.IdEmpresa == empresa.IdEmpresa);

                if (viagemEmAberto != null)
                    return new Retorno<PedagioAvulsoResponse>(false,$"Já existem viagens em aberto para o motorista {motorista.Nome} e/ou placa {veiculoTracao.Placa}", null);

            }

            var numeroEixos = veiculoTracao.QuantidadeEixos;

            if (@params.Pedagio == null)
                return new Retorno<PedagioAvulsoResponse>(false,"Dados do pedágio não informado!", null);


            if (!string.IsNullOrWhiteSpace(veiculoTracao.Placa) && 
                @params.Pedagio.Fornecedor == FornecedorEnum.ViaFacil &&
                (_parametrosGenericoService.GetParametro<bool?>(GLOBAL.HabilitarConsultaStatusVeiculoSemPararIntegracoes, 0) ?? false))
            {
                var respostaConsulta = _valePedagioApp.GetStatusVeiculoSemParar(veiculoTracao.Placa, empresa.CNPJ);
                if (respostaConsulta.VeiculoStatusSemParar != EStatusVeiculoSemParar.Ok )
                {
                    return new Retorno<PedagioAvulsoResponse>(false, respostaConsulta.Mensagem, null);
                }
            }

            var carretasPlaca = new List<ViagemCarreta>();

            if (@params.Carretas != null && @params.Carretas.Any())
            {
                if (@params.Carretas.Count != @params.Carretas.Distinct().Count())
                    return new Retorno<PedagioAvulsoResponse>(false,"A operação de transporte contém mais de um veículo com a mesma placa", null);

                var lVeiculos = _veiculoApp.GetVeiculosByListIdVeiculos(@params.Carretas.Select(x => x.IdVeiculo).ToList(),@params.IdEmpresa);

                foreach (var veiculo in lVeiculos)
                {
                    carretasPlaca.Add(new ViagemCarreta()
                    {
                        IdEmpresa = @params.IdEmpresa,
                        Placa = veiculo.Placa,
                        Rntrc = @params.Carretas?.FirstOrDefault(x => x.IdVeiculo == veiculo.IdVeiculo)?.RNTRC
                    });

                    numeroEixos += veiculo.QuantidadeEixos;
                }
            }
            
            if (@params.Pedagio.NomeRota != null || @params.Pedagio.IdRotaModelo.HasValue)
            {
                var pedagioRotaModelo = _rotaModeloApp.GetByIdOrNomeRota(@params.Pedagio.IdRotaModelo ?? 0,
                    @params.Pedagio.NomeRota, empresa.IdEmpresa);

                if (pedagioRotaModelo != null)
                {
                    var localizacaoRotaModelo = _rotaModeloApp.SetarRetornoDestino(pedagioRotaModelo);

                    //Reatribuição de valores do pedagio
                    @params.Pedagio.ValorPedagio = @params.Pedagio.Fornecedor == FornecedorEnum.Moedeiro || !empresa.PedagioTag && !@params.Pedagio.Istag() ? pedagioRotaModelo.CustoRota : pedagioRotaModelo.CustoRotaTag;
                    @params.Pedagio.TipoVeiculo = pedagioRotaModelo.TipoVeiculo;
                    @params.Pedagio.Localizacoes = new List<ViagemIntegrarPedagioLocalizacaoRequest>();
                    @params.Pedagio.CodPolyline = pedagioRotaModelo.CodPolyline;
                        
                    foreach (var item in localizacaoRotaModelo)
                    {
                        var localizacao = new ViagemIntegrarPedagioLocalizacaoRequest()
                        {
                            IbgeCidade = item.Ibge.ToInt(),
                            Latitude = item.Latitude,
                            Longitude = item.Longitude,
                        };

                        @params.Pedagio.Localizacoes.Add(localizacao);
                    }
                }
            }
           
            if (FornecedorEnumExtensions.RoteirizacoesObrigatorias.Contains(@params.Pedagio.Fornecedor) ||
                @params.Pedagio.Roteirizar && @params.Pedagio.Fornecedor == FornecedorEnum.Moedeiro)
            {
                var pedagioConsulta = @params.Pedagio.IdentificadorHistorico ??
                                      GetConsultaHistoricoPedagio(numeroEixos, @params.Pedagio,empresa.IdEmpresa,@params.UsuarioAudit.IdUsuario);

                @params.Pedagio.IdentificadorHistorico = pedagioConsulta;
            }

            var pedagioModel = new PedagioModel()
            {
                Fornecedor = @params.Pedagio.Fornecedor,
                TipoVeiculo = @params.Pedagio.TipoVeiculo,
                QtdEixos = numeroEixos,
                ValorPedagio = @params.Pedagio.ValorPedagio,
                IdentificadorHistorico = @params.Pedagio.IdentificadorHistorico,
                CodPolyline = @params.Pedagio.CodPolyline
            };

            var localizacoesDto = new List<LocalizacaoDTO>();

            if (@params.Pedagio.Localizacoes != null)
            {
                foreach (var item in @params.Pedagio.Localizacoes)
                {
                    localizacoesDto.Add(new LocalizacaoDTO
                    {
                        Latitude = item.Latitude,
                        Longitude = item.Longitude
                    });
                }

                pedagioModel.Localizacoes = localizacoesDto;
            }

            if (@params.Pedagio.ValorPedagio.HasValue && @params.Pedagio.ValorPedagio <= 0)
                return new Retorno<PedagioAvulsoResponse>(false,"Solicitação de pedágio incorreta. Para definir o valor de pedágio, deve ser indicado um valor maior que R$ 0,00.", null);

            if (pedagioModel.ValorPedagio > 0 == false && @params.Pedagio.ValorPedagio > 0 == false &&
                !@params.Pedagio.IdentificadorHistorico.HasValue && @params.Pedagio.Localizacoes?.Any() == true && pedagioModel.QtdEixos < 2)
            {
                return new Retorno<PedagioAvulsoResponse>(false,"Quantidade de eixos inválido, deve ser indicado um valor igual ou maior que 2.", null);
            }

            if (@params.Pedagio.Fornecedor == FornecedorEnum.ExtrattaTag)
            {
                var tagVeiculo = _tagExtrattaApp.GetVeiculo(veiculoTracao.Placa);

                if (!tagVeiculo.Success)
                    return new Retorno<PedagioAvulsoResponse>(false, tagVeiculo.Messages.FirstOrDefault(), null);
                        
                if(!tagVeiculo.Value.Serial.HasValue)
                    return new Retorno<PedagioAvulsoResponse>(false, "Placa informada não possui nenhuma TAG ativa!", null);
                
                pedagioModel.TagSerialNumber = tagVeiculo.Value.Serial;
            }
            
            #endregion

            #region Integração

            var viagem = Mapper.Map<PedagioAvulsoRequest, Viagem>(@params);

            if (@params.IdFilial.HasValue)
                viagem.CNPJFilial = _filialApp.GetCnpjPorId(@params.IdFilial.Value);

            if(carretasPlaca.Any())
                viagem.ViagemCarretas = carretasPlaca;

            viagem.DataPrevisaoEntrega = DateTime.Now.AddDays(1);
            viagem.Placa = veiculoTracao.Placa;
            viagem.CPFCNPJProprietario = proprietario.CNPJCPF;

            if (@params.IdMotorista.HasValue)
            {
                var motorista = _motoristaService.GetAllByIdEmpresa(viagem.IdEmpresa)
                    .FirstOrDefault(m => m.IdMotorista == @params.IdMotorista);

                viagem.NomeMotorista = motorista.Nome;
                viagem.CPFMotorista = motorista.CPF;
            }

            _viagemApp.AjustarProprietario(viagem);

            validationResult = _viagemApp.Add(viagem,true);

            if (@params.Pedagio != null)
            {
                validationResult.Add(_viagemApp.AdicionarAtualizarViagemRota(viagem.IdViagem, @params.IdEmpresa,
                    localizacoesDto, @params.Pedagio.IdentificadorHistorico,
                    pedagioModel.Fornecedor ?? FornecedorEnum.Desabilitado,
                    pedagioModel.TipoVeiculo ?? ETipoVeiculoPedagioEnum.Caminhao, pedagioModel.QtdEixos,
                    @params.UsuarioAudit.DocumentoUsuarioAudit, @params.UsuarioAudit.NomeUsuarioAudit));

                if (!validationResult.IsValid)
                    return new Retorno<PedagioAvulsoResponse>(validationResult, null, null);
            }

            if (!validationResult.IsValid)
                return new Retorno<PedagioAvulsoResponse>(validationResult, null, null);

            var viagemRequest = new ViagemIntegrarRequestModel
            {
                Pedagio = pedagioModel,
                CNPJEmpresa = empresa.CNPJ
            };

            var retornoLiberacaoGestor = _bloqueioGestorViagem.ValidarBloqueioGestor(viagemRequest, viagem, EBloqueioOrigemTipo.Portal);
            var compraPedagio = _integracaoMeioHomologadoViagem.ComprarPedagioAvulso(viagemRequest, viagem, cartoesApp,retornoLiberacaoGestor);

            #endregion

            if (compraPedagio.Status == EResultadoCompraPedagio.Erro ||
                compraPedagio.Status == EResultadoCompraPedagio.NaoRealizado)
            {
                return new Retorno<PedagioAvulsoResponse>(true,compraPedagio.Mensagem,new PedagioAvulsoResponse()
                {
                    SucessoCompra = false,
                    IdViagem = null
                });
            }

            return new Retorno<PedagioAvulsoResponse>(true,"Pedágio avulso cadastrado com sucesso!",new PedagioAvulsoResponse()
            {
                SucessoCompra = true,
                IdViagem = viagem.IdViagem
            });
        }

        private Guid GetConsultaHistoricoPedagio(int quantidadeEixos, ViagemIntegrarPedagioRequest pedagioRequest,int idEmpresa,int idUsuario)
        {
            var request = new ConsultaRotaRequest()
            {
                QtdEixos = quantidadeEixos,
                CodPolyline = pedagioRequest.CodPolyline
            };

            switch (pedagioRequest.TipoVeiculo)
            {
                case ETipoVeiculoPedagioEnum.Carro:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Carro;
                    break;
                case ETipoVeiculoPedagioEnum.Motocicleta:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Motocicleta;
                    break;
                case ETipoVeiculoPedagioEnum.Onibus:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Onibus;
                    break;
                case ETipoVeiculoPedagioEnum.Caminhao:
                    request.TipoVeiculo = ConsultaRotaRequestTipoVeiculo.Caminhao;
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(ViagemIntegrarPedagioRequest), pedagioRequest.TipoVeiculo, null);
            }

            request.Localizacoes = new List<LocationDTO>();

            foreach (var viagemIntegrarPedagioLocalizacaoRequest in pedagioRequest.Localizacoes)
                request.Localizacoes.Add(new LocationDTO
                {
                    Latitude = viagemIntegrarPedagioLocalizacaoRequest.Latitude,
                    Longitude = viagemIntegrarPedagioLocalizacaoRequest.Longitude
                });

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies,
                idEmpresa, idUsuario, true);

            var empresa = _empresaApp.Get(idEmpresa);

            request.DesabilitaCacheRotas = empresa?.DesabilitaCacheRotas;

            var response = cartoesApp.ConsultarCustoRota(request);

            if (response.Status == ConsultaRotaResponseDtoStatus.Sucesso)
                return response.IdentificadorHistorico ?? Guid.Empty;

            return Guid.Empty;
        }
    }
}
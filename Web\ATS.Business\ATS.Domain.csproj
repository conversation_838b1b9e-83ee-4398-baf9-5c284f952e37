﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2810ACEC-3610-4AAD-8E20-D01E14466D87}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ATS.Domain</RootNamespace>
    <AssemblyName>ATS.Domain</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkProfile />
    <LangVersion>9</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Consts.cs" />
    <Compile Include="DTO\AbastecimentoTicketLogRequestDTO.cs" />
    <Compile Include="DTO\AbastecimentoTicketLogResponseDTO.cs" />
    <Compile Include="DTO\AlterarSenhaCartaoRequestDTO.cs" />
    <Compile Include="DTO\UsuarioAplicativoGetInformacoesResponse.cs" />
    <Compile Include="DTO\Backoffice\AlterarSenhaUsuarioResponse.cs" />
    <Compile Include="DTO\Banner\BannerAlterarStatusRequest.cs" />
    <Compile Include="DTO\Banner\BannerAlterarStatusResponse.cs" />
    <Compile Include="DTO\Banner\BannerConsultarResponse.cs" />
    <Compile Include="DTO\Banner\BannerGridResponse.cs" />
    <Compile Include="DTO\Banner\BannerVisualizarResponse.cs" />
    <Compile Include="DTO\Banner\BannerIntegrarRequest.cs" />
    <Compile Include="DTO\Banner\BannerVisualizarRequest.cs" />
    <Compile Include="DTO\BloqueioEventosExpiradosResponseDTO.cs" />
    <Compile Include="DTO\BloqueioFinanceiroValorDto.cs" />
    <Compile Include="DTO\BloqueioGestorValorDto.cs" />
    <Compile Include="DTO\BloqueioGestorValorResponseDTO.cs" />
    <Compile Include="DTO\CadastroRotas\RotaModeloDetalhesResponse.cs" />
    <Compile Include="DTO\CadastroRotas\RotaModeloParametrosResponse.cs" />
    <Compile Include="DTO\CadastroRotas\RotaModeloResponse.cs" />
    <Compile Include="DTO\CalcularRotaResponseDTO.cs" />
    <Compile Include="DTO\AuthSessionDTO.cs" />
    <Compile Include="DTO\BaixarEventoSaldoResult.cs" />
    <Compile Include="DTO\Campanha\AlterarStatusCampanhaRequest.cs" />
    <Compile Include="DTO\Campanha\AlterarStatusCampanhaResponse.cs" />
    <Compile Include="DTO\Campanha\CampanhaConsultaResponse.cs" />
    <Compile Include="DTO\Campanha\CampanhaGridResponse.cs" />
    <Compile Include="DTO\Campanha\CampanhaRespostaResponse.cs" />
    <Compile Include="DTO\Campanha\IntegrarCampanhaRequest.cs" />
    <Compile Include="DTO\Campanha\ResponderCampanhaRequest.cs" />
    <Compile Include="DTO\CargaAvulsaResponseDTO.cs" />
    <Compile Include="DTO\CargaAvulsaResponseValorDiarioTransacaoDTO.cs" />
    <Compile Include="DTO\CargaAvulsaResponseItemDTO.cs" />
    <Compile Include="DTO\CartaoBloqueadoPessoaListResponseDTO.cs" />
    <Compile Include="DTO\CartaoIdentificacaoDto.cs" />
    <Compile Include="DTO\CartaoTransferenciaDTO.cs" />
    <Compile Include="DTO\CartaoVinculadoPessoaListResponseDTO.cs" />
    <Compile Include="DTO\CategoriaGetAllResponseDTO.cs" />
    <Compile Include="DTO\Ciot\DadosAtualizacaoCiotDto.cs" />
    <Compile Include="DTO\Ciot\DadosCiotV2Dto.cs" />
    <Compile Include="DTO\Ciot\DadosCiotV3Dto.cs" />
    <Compile Include="DTO\Ciot\DadosSituacaoCiotDto.cs" />
    <Compile Include="DTO\Ciot\RetificarContratoAgregadoResponseDto.cs" />
    <Compile Include="DTO\CombustivelCiotAgregadoDto.cs" />
    <Compile Include="DTO\ConsultaDetalhesEmpresaMeioHomologadoReponseDTO.cs" />
    <Compile Include="DTO\ConsultaExtratoDTORequest.cs" />
    <Compile Include="DTO\ConsultaExtratoConsolidadoDTORequest.cs" />
    <Compile Include="DTO\ConsultarAtendimentoRequestDTO.cs" />
    <Compile Include="DTO\ConsultarAtendimentoResponseDTO.cs" />
    <Compile Include="DTO\ConsultarHistoricoAtendimentoRequestDTO.cs" />
    <Compile Include="DTO\ConsultarHistoricoAtendimentoResponseDTO.cs" />
    <Compile Include="DTO\ConsultarBancoResponseDTO.cs" />
    <Compile Include="DTO\ConsultarContasBancariasResponseDTO.cs" />
    <Compile Include="DTO\ConsultarExtratoResponseDTO.cs" />
    <Compile Include="DTO\ConsultarPolylineDTO.cs" />
    <Compile Include="DTO\ConsultarResgateValorDTO.cs" />
    <Compile Include="DTO\ConsultarSaldoCartaoResponseDTO.cs" />
    <Compile Include="DTO\ConsultarStatusVeiculoTaggyEdenredDTO.cs" />
    <Compile Include="DTO\ConsultaSaldoDTORequest.cs" />
    <Compile Include="DTO\ConsultaDadosVeiculoResponseDTO.cs" />
    <Compile Include="DTO\ConsultaProprietarioPorCnpjCpfResponseDTO.cs" />
    <Compile Include="DTO\ConsultarStatusVeiculoSemPararDTO.cs" />
    <Compile Include="DTO\ConsultaViagemRequestDTO.cs" />
    <Compile Include="DTO\ConsultaPlacaDTO.cs" />
    <Compile Include="DTO\ConsultarParametroCartaoDTO.cs" />
    <Compile Include="DTO\ConsultarRemessaResponseDTO.cs" />
    <Compile Include="DTO\CancelarAtendimentoDTORequest.cs" />
    <Compile Include="DTO\AtendimentoTramitePortadorRequest.cs" />
    <Compile Include="DTO\EmpresaParametrosVeiculoDto.cs" />
    <Compile Include="DTO\EmpresaParametrosViagemDto.cs" />
    <Compile Include="DTO\Empresa\ConsultaTodasEmpresasDto.cs" />
    <Compile Include="DTO\Estabelecimento\AssociacoesEmpresaDto.cs" />
    <Compile Include="DTO\EstornarResgateValorDTO.cs" />
    <Compile Include="DTO\EstornarResgateValorResponseDTO.cs" />
    <Compile Include="DTO\ExtratoConsolidadoDTORequest.cs" />
    <Compile Include="DTO\ExtratoConsolidadoDTOResponse.cs" />
    <Compile Include="DTO\ExtratoConsolidadoPortadorGridDTOResponse.cs" />
    <Compile Include="DTO\FaturamentoGridDTO.cs" />
    <Compile Include="DTO\FaturamentoItemDTO.cs" />
    <Compile Include="DTO\FinalizarAtendimentoDTORequest.cs" />
    <Compile Include="DTO\Firebase\FirebaseNotificationResponse.cs" />
    <Compile Include="DTO\GetUsuarioFotoResponse.cs" />
    <Compile Include="DTO\GridDadosVeiculoResponseDTO.cs" />
    <Compile Include="DTO\HistoricoCartaoPessoaListResponseDto.cs" />
    <Compile Include="DTO\Pix\AlterarLimitesPixRequest.cs" />
    <Compile Include="DTO\Pix\AlterarStatusParcelaPixGestaoAlcadasRequest.cs" />
    <Compile Include="DTO\Pix\CancelarParcelaPixGestaoAlcadasRequest.cs" />
    <Compile Include="DTO\Pix\TransferenciaPixGridGestaoAlcadasResponse.cs" />
    <Compile Include="DTO\Pix\CadastrarChavePixProprietarioRequest.cs" />
    <Compile Include="DTO\Pix\CadastrarChavePixProprietarioResponse.cs" />
    <Compile Include="DTO\Pix\CadastrarPlanilhaChavePixProprietarioRequest.cs" />
    <Compile Include="DTO\Pix\SolicitacaoChavePixAlterarStatusAppRequest.cs" />
    <Compile Include="DTO\Pix\SolicitacaoChavePixGridAppRequest.cs" />
    <Compile Include="DTO\Pix\SolicitacaoChavePixGridResponse.cs" />
    <Compile Include="DTO\Pix\TransferenciaPixGridAppRequest.cs" />
    <Compile Include="DTO\Pix\TransferenciaPixGridRelatorioRequest.cs" />
    <Compile Include="DTO\Pix\TransferenciaPixGridRequest.cs" />
    <Compile Include="DTO\Pix\TransferenciaPixGridResponse.cs" />
    <Compile Include="DTO\Pix\ValidarPlanilhaSolicitacaoChavePixResponse.cs" />
    <Compile Include="DTO\Plano\PlanoEmpresaDto.cs" />
    <Compile Include="DTO\ImportacaoDadosRequest.cs" />
    <Compile Include="DTO\ImportacaoDadosResponse.cs" />
    <Compile Include="DTO\PixExtratoGridResponse.cs" />
    <Compile Include="DTO\PracasDTO.cs" />
    <Compile Include="DTO\PracasPedagioResponseDTO.cs" />
    <Compile Include="DTO\ObterExtratoSemPararResponseDTO.cs" />
    <Compile Include="DTO\PracasPedagioVeiculoResponseDTO.cs" />
    <Compile Include="DTO\PrestacaoContas\PrestacaoContasGridResponse.cs" />
    <Compile Include="DTO\PrestacaoContas\PrestacaoContasNovoRequest.cs" />
    <Compile Include="DTO\ReciboTransferenciaDto.cs" />
    <Compile Include="DTO\InativarContaBancariaAtsResponseDTO.cs" />
    <Compile Include="DTO\IntegrarPessoaRequestDTO.cs" />
    <Compile Include="DTO\MenusUsuarioDto.cs" />
    <Compile Include="DTO\MicroServicoTokenEmpresaDto.cs" />
    <Compile Include="DTO\ConsultaViagemResponseDTO.cs" />
    <Compile Include="DTO\ProcessoIntegracaoViagemDto.cs" />
    <Compile Include="DTO\Proprietario\ProprietarioViagemDto.cs" />
    <Compile Include="DTO\Proprietario\ProprietarioAnttDto.cs" />
    <Compile Include="DTO\Proprietario\ProprietarioCadastroServicoCartaoDto.cs" />
    <Compile Include="DTO\RelatorioAtendimentoRequestDTO.cs" />
    <Compile Include="DTO\RelatorioAtendimentoResponseDTO.cs" />
    <Compile Include="DTO\RelatorioConciliacaoDTO.cs" />
    <Compile Include="DTO\RelatorioGridExtratoDetalhadoDTO.cs" />
    <Compile Include="DTO\RelatorioGridExtratoDTO.cs" />
    <Compile Include="DTO\RelatorioProtocoloGridDTO.cs" />
    <Compile Include="DTO\ReprocessarPagamentoCartaoResponseDTO.cs" />
    <Compile Include="DTO\ResgatarValorDespesasViagemRequest.cs" />
    <Compile Include="DTO\ResgatarValorDTO.cs" />
    <Compile Include="DTO\ResgatarCartaoResponseDTO.cs" />
    <Compile Include="DTO\ResgatarValorResponseDTO.cs" />
    <Compile Include="DTO\EventoCiotAgregadoDto.cs" />
    <Compile Include="DTO\TagExtratta\ConsultarSituacaoTagRequestModel.cs" />
    <Compile Include="DTO\TagExtratta\ConsultarSituacaoTagResponseModel.cs" />
    <Compile Include="DTO\TagExtratta\TagExtrattaDTO.cs" />
    <Compile Include="DTO\UsuarioCadastrarV2Request.cs" />
    <Compile Include="DTO\UsuarioCadastrarV2Response.cs" />
    <Compile Include="DTO\UsuarioTokenDTO.cs" />
    <Compile Include="DTO\Usuario\UsuarioFotoDto.cs" />
    <Compile Include="DTO\Usuario\UsuarioMicroServicoInstanciaAppDto.cs" />
    <Compile Include="DTO\ValidacaoImportacaoDadosResponse.cs" />
    <Compile Include="DTO\ValidarSenhaCartaoResponseDto.cs" />
    <Compile Include="DTO\Veiculo\VeiculoDTO.cs" />
    <Compile Include="DTO\Veiculo\VeiculoGridResponse.cs" />
    <Compile Include="DTO\Veiculo\VeiculoRntrcDTO.cs" />
    <Compile Include="Entities\AdministradoraPlataforma.cs" />
    <Compile Include="DTO\ConsultarRemessaResponseItemDTO.cs" />
    <Compile Include="DTO\LocalizacaoDTO.cs" />
    <Compile Include="DTO\OperacaoCartaoResponseDTO.cs" />
    <Compile Include="DTO\PainelProdutividadeDTO.cs" />
    <Compile Include="DTO\PainelProdutividadeIndicacoesDTO.cs" />
    <Compile Include="DTO\PortadorDto.cs" />
    <Compile Include="DTO\RelatorioConciliacaoAnaliticoTransacoesDTO.cs" />
    <Compile Include="DTO\RelatorioCurvaAbcDTO.cs" />
    <Compile Include="DTO\RelatorioGridTriagemDTO.cs" />
    <Compile Include="DTO\RelatorioProtocoloPagamentooFreteDTO.cs" />
    <Compile Include="DTO\RelatorioProvisaoDTO.cs" />
    <Compile Include="DTO\RelatorioRecebimentoProtocoloDTO.cs" />
    <Compile Include="DTO\RelatorioSituacaoCartoesDTO.cs" />
    <Compile Include="DTO\RelatorioTransferenciasContaBancariaDTO.cs" />
    <Compile Include="DTO\SolicitarCompraPedagioResponseDTO.cs" />
    <Compile Include="DTO\TransferirContaBancariaRequestDTO.cs" />
    <Compile Include="DTO\UsuarioAuthSession.cs" />
    <Compile Include="Entities\AtendimentoPortador.cs" />
    <Compile Include="Entities\AtendimentoPortadorTramite.cs" />
    <Compile Include="Entities\AutenticacaoAplicacao.cs" />
    <Compile Include="Entities\AutorizacaoEmpresa.cs" />
    <Compile Include="Entities\AuthSession.cs" />
    <Compile Include="Entities\AvaliacaoPlanilhaGestorCargaAvulsa.cs" />
    <Compile Include="Entities\Banner.cs" />
    <Compile Include="Entities\BannerUsuario.cs" />
    <Compile Include="Entities\Base\Entidade.cs" />
    <Compile Include="Entities\BlacklistIp.cs" />
    <Compile Include="Entities\BloqueioCartaoTipo.cs" />
    <Compile Include="Entities\BloqueioFinanceiroTipo.cs" />
    <Compile Include="Entities\BloqueioGestorTipo.cs" />
    <Compile Include="Entities\Campanha.cs" />
    <Compile Include="Entities\CampanhaResposta.cs" />
    <Compile Include="Entities\BloqueioOrigemTipo.cs" />
    <Compile Include="Entities\CargaAvulsa.cs" />
    <Compile Include="Entities\BloqueioGestorValor.cs" />
    <Compile Include="Entities\CargaAvulsaPlanilha.cs" />
    <Compile Include="Entities\Categoria.cs" />
    <Compile Include="Entities\ChavePagamento.cs" />
    <Compile Include="Entities\CheckinResumo.cs" />
    <Compile Include="Entities\CombustivelJSL.cs" />
    <Compile Include="Entities\CombustivelJSLEstabelecimentoBase.cs" />
    <Compile Include="Entities\ConsumoServicoExterno.cs" />
    <Compile Include="Entities\ContratoCiotAgregado.cs" />
    <Compile Include="Entities\ContratoCiotAgregadoVeiculo.cs" />
    <Compile Include="Entities\CredenciamentoMotivo.cs" />
    <Compile Include="Entities\ClienteEndereco.cs" />
    <Compile Include="Entities\ClienteProdutoEspecie.cs" />
    <Compile Include="Entities\Contrato.cs" />
    <Compile Include="Entities\Cte.cs" />
    <Compile Include="Entities\DeclaracaoCiot.cs" />
    <Compile Include="Entities\DespesasViagem.cs" />
    <Compile Include="Entities\DespesaUsuario.cs" />
    <Compile Include="Entities\EmpresaContaBancaria.cs" />
    <Compile Include="Entities\EmpresaIndicadores.cs" />
    <Compile Include="Entities\EstabelecimentoBaseContaBancaria.cs" />
    <Compile Include="Entities\EstabelecimentoBaseDocumento.cs" />
    <Compile Include="Entities\EstabelecimentoContaBancaria.cs" />
    <Compile Include="Entities\FornecedorCnpjPedagio.cs" />
    <Compile Include="Entities\GestorUsuario.cs" />
    <Compile Include="Entities\ItensGraficoPorEmpresa.cs" />
    <Compile Include="Entities\LimiteTransacaoPortador.cs" />
    <Compile Include="Entities\LocalizacaoUsuario.cs" />
    <Compile Include="Entities\NumeroExtenso.cs" />
    <Compile Include="Entities\Parametros.cs" />
    <Compile Include="Entities\EstabelecimentoBaseAssociacao.cs" />
    <Compile Include="Entities\ConjuntoCarretaEmpresa.cs" />
    <Compile Include="Entities\ConjuntoEmpresa.cs" />
    <Compile Include="Entities\LayoutCartao.cs" />
    <Compile Include="Entities\LayoutCartaoItem.cs" />
    <Compile Include="Entities\LogSms.cs" />
    <Compile Include="Entities\PedagioRota.cs" />
    <Compile Include="Entities\PedagioRotaPonto.cs" />
    <Compile Include="Entities\PIN.cs" />
    <Compile Include="Entities\Common\EstabelecimentoBaseClass.cs" />
    <Compile Include="Entities\ConjuntoCarreta.cs" />
    <Compile Include="Entities\Conjunto.cs" />
    <Compile Include="Entities\Credenciamento.cs" />
    <Compile Include="Entities\CredenciamentoAnexo.cs" />
    <Compile Include="Entities\EstabelecimentoAssociacao.cs" />
    <Compile Include="Entities\EstabelecimentoBase.cs" />
    <Compile Include="Entities\EstabelecimentoBaseProduto.cs" />
    <Compile Include="Entities\FilialContatos.cs" />
    <Compile Include="Entities\Plano.cs" />
    <Compile Include="Entities\PlanoEmpresa.cs" />
    <Compile Include="Entities\PontosRotaModelo.cs" />
    <Compile Include="Entities\PracasRotaModelo.cs" />
    <Compile Include="Entities\PrestacaoContas.cs" />
    <Compile Include="Entities\PrestacaoContasEvento.cs" />
    <Compile Include="Entities\ProdutoDadosCarga.cs" />
    <Compile Include="Entities\ProjetoFirebase.cs" />
    <Compile Include="Entities\RotaEstabelecimento.cs" />
    <Compile Include="Entities\RotaModelo.cs" />
    <Compile Include="Entities\SerproCache.cs" />
    <Compile Include="Entities\SerproCacheResultado.cs" />
    <Compile Include="Entities\SolicitacaoChavePix.cs" />
    <Compile Include="Entities\SolicitacaoChavePixEvento.cs" />
    <Compile Include="Entities\SolicitacaoChavePixStatus.cs" />
    <Compile Include="Entities\Tag.cs" />
    <Compile Include="Entities\TipoMotivo.cs" />
    <Compile Include="Entities\ResgateCartaoAtendimento.cs" />
    <Compile Include="Entities\TransacaoCartao.cs" />
    <Compile Include="Entities\TipoCavaloCliente.cs" />
    <Compile Include="Entities\TransacaoPix.cs" />
    <Compile Include="Entities\TransacaoPixStatus.cs" />
    <Compile Include="Entities\UsoTipoEstabelecimento.cs" />
    <Compile Include="Entities\LocalizacaoUsuarioPortal.cs" />
    <Compile Include="Entities\UsuarioPermissaoCartao.cs" />
    <Compile Include="Entities\UsuarioPermissaoFinanceiro.cs" />
    <Compile Include="Entities\UsuarioPermissaoGestor.cs" />
    <Compile Include="Entities\UsuarioPermissoesConcedidasMobile.cs" />
    <Compile Include="Entities\ValidationKeyTransaction.cs" />
    <Compile Include="Entities\VeiculoDigitosMercosul.cs" />
    <Compile Include="Entities\ViagemDocumentoFiscal.cs" />
    <Compile Include="Entities\ViagemEventoProtocoloAnexo.cs" />
    <Compile Include="Entities\ViagemPagamentoConta.cs" />
    <Compile Include="Entities\ViagemPendenteGestor.cs" />
    <Compile Include="Entities\ViagemRota.cs" />
    <Compile Include="Entities\ViagemRotaPonto.cs" />
    <Compile Include="Entities\Webhook.cs" />
    <Compile Include="Entities\WhiteListIP.cs" />
    <Compile Include="Enum\EActionViagem.cs" />
    <Compile Include="Enum\EAvaliacaoGestor.cs" />
    <Compile Include="Enum\EBizWebhook.cs" />
    <Compile Include="Enum\EBloqueioCartaoTipo.cs" />
    <Compile Include="Enum\EBloqueioFinanceiroStatus.cs" />
    <Compile Include="Enum\EBloqueioGestorStatus.cs" />
    <Compile Include="Enum\EConsultaPagamentoTipo.cs" />
    <Compile Include="Enum\EBloqueioOrigemTipo.cs" />
    <Compile Include="Enum\ECriterioRanking.cs" />
    <Compile Include="Enum\EDeclaracaoCiotViagem.cs" />
    <Compile Include="Enum\EErrroProvisionamento.cs" />
    <Compile Include="Enum\EEventoSaldoTag.cs" />
    <Compile Include="Enum\EExtensaoArquivoRelatorio.cs" />
    <Compile Include="Enum\EFornecedorPedagio.cs" />
    <Compile Include="Enum\EOrigemEncerramentoContratoAgregado.cs" />
    <Compile Include="Enum\EApi.cs" />
    <Compile Include="Enum\EMenu.cs" />
    <Compile Include="Enum\EOrigemRequisicao.cs" />
    <Compile Include="Enum\ESolicitacaoChavePixStatus.cs" />
    <Compile Include="Enum\EStatusAnaliseAbono.cs" />
    <Compile Include="Enum\EOrigemConsumoServicoExterno.cs" />
    <Compile Include="Enum\EStatusAtendimentoPortador.cs" />
    <Compile Include="Enum\ENominalCheque.cs" />
    <Compile Include="Enum\EStatusCargaAvulsa.cs" />
    <Compile Include="Enum\EStatusFaturamento.cs" />
    <Compile Include="Enum\EStatusIntegracaoChequeTms.cs" />
    <Compile Include="Enum\EStatusDeclaracaoCiot.cs" />
    <Compile Include="Enum\EStatusContratoAgregado.cs" />
    <Compile Include="Enum\EStatusTag.cs" />
    <Compile Include="Enum\EStatusPrestacaoContas.cs" />
    <Compile Include="Enum\EStatusPrestacaoContasEvento.cs" />
    <Compile Include="Enum\EStatusVeiculoFrotaTransportador.cs" />
    <Compile Include="Enum\EStatusVeiculoSemParar.cs" />
    <Compile Include="Enum\ETipoArquivoPagamento.cs" />
    <Compile Include="Enum\ETipoCarga.cs" />
    <Compile Include="Enum\EStatusDocumentacaoCredenciamento.cs" />
    <Compile Include="Enum\EFuncaoOrigem.cs" />
    <Compile Include="Enum\EOperationTrigger.cs" />
    <Compile Include="Enum\EFaultType.cs" />
    <Compile Include="Enum\EOrigemTransacaoCartao.cs" />
    <Compile Include="Enum\EPerfilSotran.cs" />
    <Compile Include="Enum\EResultadoCompraPedagio.cs" />
    <Compile Include="Enum\ERetornoOperacaoCartao.cs" />
    <Compile Include="Enum\EProcessoEnvio.cs" />
    <Compile Include="Enum\ESMSDeliveredStatus.cs" />
    <Compile Include="Enum\ESMSSentStatus.cs" />
    <Compile Include="Enum\ESistemaOperacional.cs" />
    <Compile Include="Enum\EStatusContatado.cs" />
    <Compile Include="Enum\EStatusCTe.cs" />
    <Compile Include="Enum\EStatusConjunto.cs" />
    <Compile Include="Entities\UsuarioDocumento.cs" />
    <Compile Include="Entities\TipoDocumento.cs" />
    <Compile Include="Entities\Layout.cs" />
    <Compile Include="Entities\Motivo.cs" />
    <Compile Include="Entities\Common\ContatoBase.cs" />
    <Compile Include="Entities\Common\VeiculoBase.cs" />
    <Compile Include="Entities\EstabelecimentoProduto.cs" />
    <Compile Include="Entities\GruposUsuarios.cs" />
    <Compile Include="Entities\Icone.cs" />
    <Compile Include="Entities\MensagemDestinatario.cs" />
    <Compile Include="Entities\MensagemGrupoDestinatario.cs" />
    <Compile Include="Entities\MensagemGrupoUsuario.cs" />
    <Compile Include="Entities\ModuloMenu.cs" />
    <Compile Include="Entities\NotificacaoPushGrupoUsuario.cs" />
    <Compile Include="Entities\NotificacaoPushItem.cs" />
    <Compile Include="Entities\NotificacaoPush.cs" />
    <Compile Include="Entities\Estabelecimento.cs" />
    <Compile Include="Entities\Documento.cs" />
    <Compile Include="Entities\PagamentoConfiguracao.cs" />
    <Compile Include="Entities\PagamentoConfiguracaoProcesso.cs" />
    <Compile Include="Entities\Produto.cs" />
    <Compile Include="Entities\Protocolo.cs" />
    <Compile Include="Entities\ProtocoloAnexo.cs" />
    <Compile Include="Entities\ProtocoloAntecipacao.cs" />
    <Compile Include="Entities\ProtocoloEvento.cs" />
    <Compile Include="Entities\Rota.cs" />
    <Compile Include="Entities\RotaTrajeto.cs" />
    <Compile Include="Entities\UsuarioEstabelecimento.cs" />
    <Compile Include="Entities\VeiculosHistoricoEmpresa.cs" />
    <Compile Include="Entities\ViagemDocumento.cs" />
    <Compile Include="Entities\ViagemEstabelecimento.cs" />
    <Compile Include="Entities\ViagemEvento.cs" />
    <Compile Include="Entities\ViagemRegra.cs" />
    <Compile Include="Entities\ViagemSolicitacaoAbono.cs" />
    <Compile Include="Entities\ViagemValorAdicional.cs" />
    <Compile Include="Entities\ViagemVirtual.cs" />
    <Compile Include="Enum\ETipoConta.cs" />
    <Compile Include="Enum\ETipoDeclaracao.cs" />
    <Compile Include="Enum\EStatusPagamentoCartao.cs" />
    <Compile Include="Enum\ETipoCarregamentoFrete.cs" />
    <Compile Include="Enum\ETipoClienteContrato.cs" />
    <Compile Include="Enum\ETipoContaBancaria.cs" />
    <Compile Include="Enum\ETipoDestinatario.cs" />
    <Compile Include="Enum\ETipoCobranca.cs" />
    <Compile Include="Enum\EModoRegistro.cs" />
    <Compile Include="Enum\EOrigemIntegracao.cs" />
    <Compile Include="Enum\EProcessoDocumento.cs" />
    <Compile Include="Enum\EStatusAbono.cs" />
    <Compile Include="Enum\ETipoCTe.cs" />
    <Compile Include="Enum\ETipoOperacaoCartao.cs" />
    <Compile Include="Enum\ETipoPadraoPlaca.cs" />
    <Compile Include="Enum\ETipoPagamentoTag.cs" />
    <Compile Include="Enum\ETipoProcessamentoCartao.cs" />
    <Compile Include="Enum\ETipoRota.cs" />
    <Compile Include="Enum\ETipoSerproCacheResultado.cs" />
    <Compile Include="Enum\ETiposPagamentos.cs" />
    <Compile Include="Enum\ETipoTramiteAtendimentoPortador.cs" />
    <Compile Include="Enum\ETipoTransacao.cs" />
    <Compile Include="Enum\ETipoValorAdicional.cs" />
    <Compile Include="Enum\ETipoVeiculo.cs" />
    <Compile Include="Enum\EUnidadeMedida.cs" />
    <Compile Include="Enum\EProcessoPgtoFrete.cs" />
    <Compile Include="Enum\EStatusCredenciamento.cs" />
    <Compile Include="Enum\EStatusProtocolo.cs" />
    <Compile Include="Enum\EStatusProtocoloAntecipacao.cs" />
    <Compile Include="Enum\EStatusProtocoloEvento.cs" />
    <Compile Include="Enum\EStatusViagemEvento.cs" />
    <Compile Include="Enum\ETipoDocumento.cs" />
    <Compile Include="Enum\ETipoEventoViagem.cs" />
    <Compile Include="Enum\ETipoQuebraMercadoria.cs" />
    <Compile Include="Enum\EValidacaoToken.cs" />
    <Compile Include="Enum\EVeiculoContratoAgregado.cs" />
    <Compile Include="Enum\EVersaoAntt.cs" />
    <Compile Include="Enum\EViagemAlterarStatusResponseTipoFalha.cs" />
    <Compile Include="Enum\EViagemEventoFormaPagamento.cs" />
    <Compile Include="Enum\EViagemFormaPagamento.cs" />
    <Compile Include="Enum\EFormaPagamentoFrete.cs" />
    <Compile Include="Enum\FornecedorEnum.cs" />
    <Compile Include="Enum\ETipoVeiculoPedagioEnum.cs" />
    <Compile Include="Enum\FornecedorEnumPagamento.cs" />
    <Compile Include="Enum\ValidationsType\CargaAvulsa\ECodigoEstornarCargaAvulsaResponse.cs" />
    <Compile Include="Enum\ValidationsType\CargaAvulsa\EValidationCargaAvulsa.cs" />
    <Compile Include="Enum\ValidationsType\Viagem\EValidationViagemEstabelecimento.cs" />
    <Compile Include="Events\ClienteIntegrarFilaEvent.cs" />
    <Compile Include="Events\FilialIntegrarFilaEvent.cs" />
    <Compile Include="Events\MotoristaIntegrarFilaEvent.cs" />
    <Compile Include="Events\ProprietarioIntegrarFilaEvent.cs" />
    <Compile Include="Events\VeiculoIntegrarFilaEvent.cs" />
    <Compile Include="Exceptions\EstabelecimentoBaseContaBancariaInvalidException.cs" />
    <Compile Include="Exceptions\PagamentoChequesInvalidException.cs" />
    <Compile Include="Exceptions\ValidationKeyTransactionInvalidException.cs" />
    <Compile Include="Extensions\DateTimeExtensions.cs" />
    <Compile Include="Extensions\StringExtensions.cs" />
    <Compile Include="Grid\DataModel.cs" />
    <Compile Include="Helpers\ATSWebClient.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Helpers\KeycloakHelper.cs" />
    <Compile Include="Helpers\MimeTypeHelper.cs" />
    <Compile Include="Helpers\OfxHelper.cs" />
    <Compile Include="Helpers\TxtBuilderHelper.cs" />
    <Compile Include="Helpers\CsvBuilderHelper.cs" />
    <Compile Include="Helpers\EmailHelper.cs" />
    <Compile Include="Helpers\QueryableExtensions.cs" />
    <Compile Include="Interface\Dapper\IAtendimentoDapper.cs" />
    <Compile Include="Interface\Dapper\ICargaAvulsaDapper.cs" />
    <Compile Include="Interface\Dapper\ICheckinDapper.cs" />
    <Compile Include="Interface\Dapper\ICheckinResumoDapper.cs" />
    <Compile Include="Interface\Dapper\IProprietarioDomainDapper.cs" />
    <Compile Include="Interface\Dapper\ITipoCavaloDapper.cs" />
    <Compile Include="Interface\Dapper\IUsuarioDapper.cs" />
    <Compile Include="Interface\Dapper\IViagemDapper.cs" />
    <Compile Include="Interface\Dapper\IProprietarioDapper.cs" />
    <Compile Include="Interface\Database\IAdministradoraPlataformaRepository.cs" />
    <Compile Include="Interface\Database\IAtendimentoPortadorRepository.cs" />
    <Compile Include="Interface\Database\IAtendimentoPortadorTramiteRepository.cs" />
    <Compile Include="Interface\Database\IAvaliacaoPlanilhaGestorCargaAvulsaRepository.cs" />
    <Compile Include="Interface\Database\IBannerRepository.cs" />
    <Compile Include="Interface\Database\IBannerUsuarioRepository.cs" />
    <Compile Include="Interface\Database\IBlacklistIpRepository.cs" />
    <Compile Include="Interface\Database\IBloqueioCartaoTipoRepository.cs" />
    <Compile Include="Interface\Database\IBloqueioFinanceiroTipoRepository.cs" />
    <Compile Include="Interface\Database\IBloqueioGestorValorRepository.cs" />
    <Compile Include="Interface\Database\ICampanhaRepository.cs" />
    <Compile Include="Interface\Database\ICampanhaRespostaRepository.cs" />
    <Compile Include="Interface\Database\ICategoriaRepository.cs" />
    <Compile Include="Interface\Database\ICheckinResumoRepository.cs" />
    <Compile Include="Helpers\RandomHelper.cs" />
    <Compile Include="Interface\Dapper\IEstabelecimentoDapper.cs" />
    <Compile Include="Interface\Dapper\IPagamentoDapper.cs" />
    <Compile Include="Interface\Dapper\IPortadorDapper.cs" />
    <Compile Include="Interface\Dapper\IViagemEventoDapper.cs" />
    <Compile Include="Interface\Database\ICargaAvulsaRepository.cs" />
    <Compile Include="Interface\Database\IClienteEnderecoRepository.cs" />
    <Compile Include="Interface\Database\IClienteProdutoEspecieRepository.cs" />
    <Compile Include="Interface\Database\IDespesasViagemRepository.cs" />
    <Compile Include="Interface\Database\IDespesaUsuarioRepository.cs" />
    <Compile Include="Interface\Database\IFornecedorCnpjPedagioRepository.cs" />
    <Compile Include="Interface\Database\IGestorUsuarioRepository.cs" />
    <Compile Include="Interface\Database\ILimiteTransacaoPortadorRepository.cs" />
    <Compile Include="Interface\Database\ILocalizacaoUsuarioRepository.cs" />
    <Compile Include="Interface\Database\IPedagioRotaRepository.cs" />
    <Compile Include="Interface\Database\IPlanoEmpresaRepository.cs" />
    <Compile Include="Interface\Database\IPlanoRepository.cs" />
    <Compile Include="Interface\Database\IPontaRotaModeloRepository.cs" />
    <Compile Include="Interface\Database\IPracaRotaModeloRepository.cs" />
    <Compile Include="Interface\Database\IPrestacaoContasEventoRepository.cs" />
    <Compile Include="Interface\Database\IPrestacaoContasRepository.cs" />
    <Compile Include="Interface\Database\IResgateCartaoAtendimentoRepository.cs" />
    <Compile Include="Interface\Database\IRotaEstabelecimentoRepository.cs" />
    <Compile Include="Interface\Database\IRotaModeloRepository.cs" />
    <Compile Include="Interface\Database\IRotaTrajetoRepository.cs" />
    <Compile Include="Interface\Database\ISerproCacheRepository.cs" />
    <Compile Include="Interface\Database\ISolicitacaoChavePixEventoRepository.cs" />
    <Compile Include="Interface\Database\ISolicitacaoChavePixRepository.cs" />
    <Compile Include="Interface\Database\ISolicitacaoChavePixStatusRepository.cs" />
    <Compile Include="Interface\Database\ITagRepository.cs" />
    <Compile Include="Interface\Database\ITransacaoPixRepository.cs" />
    <Compile Include="Interface\Database\ITransacaoPixStatusRepository.cs" />
    <Compile Include="Interface\Database\IUsoTipoEstabelecimentoRepository.cs" />
    <Compile Include="Interface\Database\ILocalizacaoUsuarioPortalRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioPermissaoCartaoRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioPermissaoFinanceiroRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioPermissoesConcedidasMobileRepository.cs" />
    <Compile Include="Interface\Database\IViagemRotaRepository.cs" />
    <Compile Include="Interface\Database\IWhiteListIPRepository.cs" />
    <Compile Include="Interface\Service\Common\IBaseService.cs" />
    <Compile Include="Interface\Service\IAdministradoraPlataformaService.cs" />
    <Compile Include="Interface\Service\IBancoService.cs" />
    <Compile Include="Interface\Service\IBannerService.cs" />
    <Compile Include="Interface\Service\IBizWebhookService.cs" />
    <Compile Include="Interface\Service\IBloqueioCartaoTipoService.cs" />
    <Compile Include="Interface\Service\IBloqueioFinanceiroTipoService.cs" />
    <Compile Include="Interface\Service\ICadastrosService.cs" />
    <Compile Include="Interface\Service\ICampanhaService.cs" />
    <Compile Include="Interface\Service\ICategoriaService.cs" />
    <Compile Include="Interface\Service\ICheckinResumoService.cs" />
    <Compile Include="Interface\Service\IAtendimentoPortadorService.cs" />
    <Compile Include="Interface\Service\ICiotV2Service.cs" />
    <Compile Include="Interface\Service\IClienteProdutoEspecieService.cs" />
    <Compile Include="Interface\Service\IConsumoServicoExternoService.cs" />
    <Compile Include="Interface\Database\ICombustivelJSLEstabelecimentoBaseRepository.cs" />
    <Compile Include="Interface\Database\ICombustivelJSLRepository.cs" />
    <Compile Include="Interface\Database\IProdutoDadosCargaRepository.cs" />
    <Compile Include="Interface\Database\ITipoMotivoRepository.cs" />
    <Compile Include="Interface\Database\IVeiculoDigitosMercosulRepository.cs" />
    <Compile Include="Interface\Database\IViagemEventoProtocoloAnexoRepository.cs" />
    <Compile Include="Interface\Service\IDataMediaServerApp.cs" />
    <Compile Include="Interface\Service\IDataMediaServerService.cs" />
    <Compile Include="Interface\Service\IDespesasViagemService.cs" />
    <Compile Include="Interface\Service\IDespesaUsuarioService.cs" />
    <Compile Include="Interface\Service\IEmpresaModuloService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoBaseProdutoService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoProdutoService.cs" />
    <Compile Include="Interface\Service\IExtratoConsolidadoService.cs" />
    <Compile Include="Interface\Service\IGrupoUsuarioMenuService.cs" />
    <Compile Include="Interface\Service\IImportacaoDadosService.cs" />
    <Compile Include="Interface\Service\ILimiteTransacaoPortadorService.cs" />
    <Compile Include="Interface\Service\ILocalizacaoUsuarioService.cs" />
    <Compile Include="Interface\Service\ILogSmsService.cs" />
    <Compile Include="Interface\Service\INotificacaoPushService.cs" />
    <Compile Include="Interface\Service\IPagamentoFreteService.cs" />
    <Compile Include="Interface\Service\IPermissaoCartaoService.cs" />
    <Compile Include="Interface\Service\IPlanoService.cs" />
    <Compile Include="Interface\Service\IPortadorService.cs" />
    <Compile Include="Interface\Service\IPrestacaoContasService.cs" />
    <Compile Include="Interface\Service\IProtocoloApp.cs" />
    <Compile Include="Interface\Service\IRotaModeloService.cs" />
    <Compile Include="Interface\Service\IRotaService.cs" />
    <Compile Include="Interface\Service\IRsaCryptoService.cs" />
    <Compile Include="Interface\Service\ISMService.cs" />
    <Compile Include="Interface\Service\ISolicitacaoChavePixService.cs" />
    <Compile Include="Interface\Service\ITagExtrattaService.cs" />
    <Compile Include="Interface\Service\ITipoNotificacaoApp.cs" />
    <Compile Include="Interface\Service\ITipoNotificacaoService.cs" />
    <Compile Include="Interface\Service\IRotasCacheService.cs" />
    <Compile Include="Interface\Service\ITransacaoPixService.cs" />
    <Compile Include="Interface\Service\IUsuarioPermissaoCartaoService.cs" />
    <Compile Include="Interface\Service\IUsuarioPermissaoFinanceiroService.cs" />
    <Compile Include="Interface\Service\IUsuarioPermissoesConcedidasMobileService.cs" />
    <Compile Include="Interface\Service\IVersaoAnttLazyLoadService.cs" />
    <Compile Include="Interface\Service\IViagemEstabelecimentoService.cs" />
    <Compile Include="Interface\Service\IViagemSolicitacaoAbonoService.cs" />
    <Compile Include="Interface\Service\IWhiteListIPService.cs" />
    <Compile Include="Interface\Validators\ILocalizacaoUsuarioAddModelValidator.cs" />
    <Compile Include="MassTransit\IpBloqueadoInsertEvent.cs" />
    <Compile Include="Models\AtendimentoPortador\AtendimentoInformacoesContatoWhatsappModel.cs" />
    <Compile Include="Models\AtendimentoPortador\PermissoesEmpresaAtendimentoPortador.cs" />
    <Compile Include="Models\AtendimentoPortador\PermissoesEmpresaAtendimentoPortadorResultModel.cs" />
    <Compile Include="Models\AtendimentoPortador\PermissoesUsuarioAtendimentoPortador.cs" />
    <Compile Include="Models\BizWebhook\BizWebhookModel.cs" />
    <Compile Include="Models\CargaAvulsaAddResponseModel.cs" />
    <Compile Include="Models\CargaAvulsaProcessadaModel.cs" />
    <Compile Include="Models\CargaAvulsaResultadoValidacaoPlanilha.cs" />
    <Compile Include="Models\CargaAvulsaValidacaoAlcadasLimitesPlanilha.cs" />
    <Compile Include="Models\CargaAvulsa\AprovarCargaAvulsaEvent.cs" />
    <Compile Include="Models\CargaAvulsa\ProcessarCargaAvulsaEvent.cs" />
    <Compile Include="Models\CargaAvulsa\EstornarCargaAvulsaResponseModel.cs" />
    <Compile Include="Models\CargaAvulsa\ReportCargaAvulsaRequestModel.cs" />
    <Compile Include="Models\CargaAvulsa\ReprovarCargaAvulsaEvent.cs" />
    <Compile Include="Models\Categoria\CategoriaAddModel.cs" />
    <Compile Include="Models\Categoria\CategoriaAddModelResponse.cs" />
    <Compile Include="Models\Categoria\CategoriaDisableModel.cs" />
    <Compile Include="Models\Categoria\CategoriaDisableModelResponse.cs" />
    <Compile Include="Models\Categoria\CategoriaEnableModel.cs" />
    <Compile Include="Models\Categoria\CategoriaEnableModelResponse.cs" />
    <Compile Include="Models\Categoria\CategoriaGetGridModelResponse.cs" />
    <Compile Include="Models\Categoria\CategoriaGetModelResponse.cs" />
    <Compile Include="Models\Categoria\CategoriaUpdateModel.cs" />
    <Compile Include="Models\Categoria\CategoriaUpdateModelResponse.cs" />
    <Compile Include="Models\CheckinConsultaModel.cs" />
    <Compile Include="Models\CheckinResumoConsultaModel.cs" />
    <Compile Include="Models\CidadeIbgeRequestModel.cs" />
    <Compile Include="Models\Ciot\CancelarCiotAgregadoViagensResult.cs" />
    <Compile Include="Models\Ciot\CancelarCiotResult.cs" />
    <Compile Include="Models\Ciot\CiotAtsException.cs" />
    <Compile Include="Models\Ciot\DeclararCiotModel.cs" />
    <Compile Include="Models\Ciot\DeclararCiotResult.cs" />
    <Compile Include="Models\Ciot\EncerradoCanceladoCiotResult.cs" />
    <Compile Include="Models\Ciot\NotificarCiotContingencia.cs" />
    <Compile Include="Models\ConsultaEstabelecimentoModelResponse.cs" />
    <Compile Include="Models\ConsultaInformacoesMobileModel.cs" />
    <Compile Include="Models\ConsultaPagamentosModel.cs" />
    <Compile Include="Models\ConsultarEstabelecimentosRotaRequestModel.cs" />
    <Compile Include="Models\ConsultaSituacaoTransportadorResponse.cs" />
    <Compile Include="Models\ConsultaTiposCargasResponse.cs" />
    <Compile Include="Models\DadosBancariosPixModel.cs" />
    <Compile Include="Models\DespesasViagem\ExtratoImagensVinculadasResponse.cs" />
    <Compile Include="Models\DespesasViagem\DespesasViagemGridResponse.cs" />
    <Compile Include="Models\DespesasViagem\ResgatePortadorResponse.cs" />
    <Compile Include="Models\DespesaUsuario\DespesaUsuarioAddModel.cs" />
    <Compile Include="Models\DespesaUsuario\DespesaUsuarioAddModelResponse.cs" />
    <Compile Include="Models\DespesaUsuario\DespesaUsuarioAnexoModelResponse.cs" />
    <Compile Include="Models\DestinoRotaModelo\DestinoRotaModeloModel.cs" />
    <Compile Include="Models\DestinoRotaModelo\DetalhesRotaModeloModel.cs" />
    <Compile Include="Models\FaturamentoGridModelResponse.cs" />
    <Compile Include="Models\FilialCrudResponse.cs" />
    <Compile Include="Models\KeycloakAccessTokenModel.cs" />
    <Compile Include="Models\KeycloakClientModel.cs" />
    <Compile Include="Models\LocalizacaoUsuarios\LocalizacaoUsuarioAddModel.cs" />
    <Compile Include="Models\KeycloakOpenIdTokenModel.cs" />
    <Compile Include="Models\KeycloakScopeModel.cs" />
    <Compile Include="Models\KeycloakSettings.cs" />
    <Compile Include="Models\KeycloakUserCredential.cs" />
    <Compile Include="Models\KeycloakUserCredentialInfo.cs" />
    <Compile Include="Models\KeycloakUserModel.cs" />
    <Compile Include="Models\PagamentosPorViagemEventoModel.cs" />
    <Compile Include="Models\Menu\MenuCadastroRequest.cs" />
    <Compile Include="Models\Menu\MenusPaiCadastroMenu.cs" />
    <Compile Include="Models\Menu\ModulosCadastroMenu.cs" />
    <Compile Include="Models\MotoristaCargaAvulsaModel.cs" />
    <Compile Include="Models\PedagioCadastrarTransportadorViaFacilRequest.cs" />
    <Compile Include="Models\PedagioConsultarTransportadorViaFacilRequest.cs" />
    <Compile Include="Models\PedagioConsultarTransportadorViaFacilResponse.cs" />
    <Compile Include="Models\PedagioDeletarTransportadorViaFacilRequest.cs" />
    <Compile Include="Models\PedagioEditarTransportadorViaFacilRequest.cs" />
    <Compile Include="Models\PedagioGridTransportadorViaFacilRequest.cs" />
    <Compile Include="Models\PedagioGridTransportadorViaFacilResponse.cs" />
    <Compile Include="Models\PedagioReciboResponse.cs" />
    <Compile Include="Models\PermissaoCartao\PermissaoCartaoModelResponse.cs" />
    <Compile Include="Models\PortadorLimitesValor.cs" />
    <Compile Include="Models\Proprietarios\AtualizaBaseEquiparadoTacResponse.cs" />
    <Compile Include="Models\ReciboCargaAvulsaModel.cs" />
    <Compile Include="Models\RecursosUsuarioMobileModel.cs" />
    <Compile Include="Models\RelatorioGrupoUsuarioModel.cs" />
    <Compile Include="Models\RelatorioRoteirizadorModel.cs" />
    <Compile Include="Models\TagExtratta\EventoSaldoCobrancaValePedagioTagEvent.cs" />
    <Compile Include="Models\TagExtratta\EventoSaldoEstornoValePedagioTagEvent.cs" />
    <Compile Include="Models\UsuarioPermissoesConcedidasMobileModel.cs" />
    <Compile Include="Models\Usuario\ConsultarGridUsuariosLideradosResponse.cs" />
    <Compile Include="Models\ViagemModels\ComprovanteCargaResponse.cs" />
    <Compile Include="Models\ViagemModels\PedagioAvulsoResponse.cs" />
    <Compile Include="Models\ViagemModels\ReciboPefDadosResponse.cs" />
    <Compile Include="Models\ViagemModels\ViagemDadosValePedagio.cs" />
    <Compile Include="Models\ViagemModels\ViagemEventoIdentifierModel.cs" />
    <Compile Include="Service\AdministradoraPlataformaService.cs" />
    <Compile Include="Interface\Service\IViagemEventoProtocoloAnexoService.cs" />
    <Compile Include="Models\ViagemEventoProtocoloAnexoModel.cs" />
    <Compile Include="Service\BancoService.cs" />
    <Compile Include="Service\BannerService.cs" />
    <Compile Include="Service\BloqueioCartaoTipoService.cs" />
    <Compile Include="Service\BizWebhookService.cs" />
    <Compile Include="Service\BloqueioFinanceiroTipoService.cs" />
    <Compile Include="Service\CampanhaService.cs" />
    <Compile Include="Service\CategoriaService.cs" />
    <Compile Include="Service\CheckinResumoService.cs" />
    <Compile Include="Service\CiotV2Service.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoBaseDocumentoRepository.cs" />
    <Compile Include="Interface\Service\IPedagioRotaService.cs" />
    <Compile Include="Interface\Service\IPedagioService.cs" />
    <Compile Include="Interface\Service\IResgateCartaoAtendimentoService.cs" />
    <Compile Include="Models\ConfiguracaoVeiculoEmailModel.cs" />
    <Compile Include="Models\ConsultarEventoRequestModel.cs" />
    <Compile Include="Models\ConsultarEventoResponseModel.cs" />
    <Compile Include="Models\CidadeModels\CidadeResponse.cs" />
    <Compile Include="Models\Clientes\ClientesModel.cs" />
    <Compile Include="Models\PedagioRota\PedagioRotaRequests.cs" />
    <Compile Include="Models\PedagioRota\PedagioRotaResponses.cs" />
    <Compile Include="Models\Proprietarios\ProprietarioRequests.cs" />
    <Compile Include="Models\Proprietarios\ProprietarioResponses.cs" />
    <Compile Include="Models\RetificarContratoAgregadoModel.cs" />
    <Compile Include="Service\CadastrosService.cs" />
    <Compile Include="Models\ViagemModels\ViagemRequest.cs" />
    <Compile Include="Models\ViagemModels\ViagemResponse.cs" />
    <Compile Include="Service\Common\BaseService.cs" />
    <Compile Include="Service\DespesasViagemService.cs" />
    <Compile Include="Service\DespesaUsuarioService.cs" />
    <Compile Include="Service\EstabelecimentoBaseDocumentoService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoAssociacaoService.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoBaseContaBancariaRepository.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoContaBancariaRepository.cs" />
    <Compile Include="Interface\Database\IBloqueioGestorTipoRepository.cs" />
    <Compile Include="Interface\Database\ICteRepository.cs" />
    <Compile Include="Interface\Database\IContratoCiotAgregadoRepository.cs" />
    <Compile Include="Interface\Database\IContratoCiotAgregadoVeiculoRepository.cs" />
    <Compile Include="Interface\Database\ICredenciamentoMotivoRepository.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoBaseAssociacaoRepository.cs" />
    <Compile Include="Interface\Database\IEmpresaContaBancariaRepository.cs" />
    <Compile Include="Interface\Database\ILogSmsRepository.cs" />
    <Compile Include="Interface\Database\IDeclaracaoCiotRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioPermissaoGestorRepository.cs" />
    <Compile Include="Interface\Database\IViagemPendenteGestorRepository.cs" />
    <Compile Include="Interface\Database\IParametrosRepository.cs" />
    <Compile Include="Interface\Database\IConjuntoEmpresaRepository.cs" />
    <Compile Include="Interface\Database\IContratoRepository.cs" />
    <Compile Include="Interface\Database\IFilialContatosRepository.cs" />
    <Compile Include="Interface\Database\ILayoutCartaoRepository.cs" />
    <Compile Include="Interface\Database\IMotivoRepository.cs" />
    <Compile Include="Interface\Database\IPINRepository.cs" />
    <Compile Include="Interface\Database\IConjuntoRepository.cs" />
    <Compile Include="Interface\Database\IProdutoRepository.cs" />
    <Compile Include="Interface\Database\ITransacaoCartaoRepository.cs" />
    <Compile Include="Helpers\QRCodeHelper.cs" />
    <Compile Include="Interface\Database\ITipoCavaloClienteRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioContatoRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioDocumentoRepository.cs" />
    <Compile Include="Interface\Database\ITipoDocumentoRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioEnderecoRepository.cs" />
    <Compile Include="Interface\Database\IVeiculosHistoricoEmpresaRepository.cs" />
    <Compile Include="Interface\Database\IViagemDocumentoFiscalRepository.cs" />
    <Compile Include="Interface\Database\IViagemSolicitacaoAbono.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoAssociacaoRepository.cs" />
    <Compile Include="Interface\Database\ICredenciamentoAnexoRepository.cs" />
    <Compile Include="Interface\Database\ICredenciamentoRepository.cs" />
    <Compile Include="Interface\Database\IDocumentoRepository.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoBaseProdutoRepository.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoBaseRepository.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoProdutoRepository.cs" />
    <Compile Include="Interface\Database\ILayoutRepository.cs" />
    <Compile Include="Interface\Database\IPagamentoConfiguracaoProcessoRepository.cs" />
    <Compile Include="Interface\Database\IPagamentoConfiguracaoRepository .cs" />
    <Compile Include="Interface\Database\IRotaRepository.cs" />
    <Compile Include="Interface\Database\IViagemCarretaRepository.cs" />
    <Compile Include="Interface\Database\IViagemEstabelecimentoRepository.cs" />
    <Compile Include="Interface\Database\IViagemRegraRepository.cs" />
    <Compile Include="Interface\Database\IViagemEventoRepository.cs" />
    <Compile Include="Interface\Database\IViagemDocumentoRepository.cs" />
    <Compile Include="Interface\Database\IViagemValorAdicionalRepository.cs" />
    <Compile Include="Interface\Database\IProtocoloRepository.cs" />
    <Compile Include="Interface\Database\IProtocoloEventoRepository.cs" />
    <Compile Include="Interface\Database\IProtocoloAnexoRepository.cs" />
    <Compile Include="Interface\Database\IProtocoloAntecipacaoRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioEstabelecimentoRepository.cs" />
    <Compile Include="Interface\Database\IWebhookRepository.cs" />
    <Compile Include="Interface\Service\Common\IServiceParametroBase.cs" />
    <Compile Include="Interface\Service\IAuthSessionService.cs" />
    <Compile Include="Interface\Service\IBloqueioGestorTipoService.cs" />
    <Compile Include="Interface\Service\IBloqueioGestorValorService.cs" />
    <Compile Include="Interface\Service\ICargaAvulsaService.cs" />
    <Compile Include="Interface\Service\ICartoesService.cs" />
    <Compile Include="Interface\Service\ICiotV3Service.cs" />
    <Compile Include="Interface\Service\ICombustivelJSLService.cs" />
    <Compile Include="Interface\Service\IConjuntoEmpresaService.cs" />
    <Compile Include="Interface\Service\ICteService.cs" />
    <Compile Include="Interface\Service\IContratoCiotAgregadoService.cs" />
    <Compile Include="Interface\Service\IFilialContatoService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoBaseContaBancariaService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoContaBancariaService.cs" />
    <Compile Include="Interface\Service\IMotivoService.cs" />
    <Compile Include="Interface\Service\IDeclaracaoCiotService.cs" />
    <Compile Include="Interface\Service\IEmpresaContaBancariaService.cs" />
    <Compile Include="Interface\Service\ILayoutCartaoService.cs" />
    <Compile Include="Interface\Service\IUsuarioPermissaoGestorService.cs" />
    <Compile Include="Interface\Service\IViagemPendenteGestorService.cs" />
    <Compile Include="Interface\Service\IParametrosService.cs" />
    <Compile Include="Interface\Service\IProdutoDadosCargaService.cs" />
    <Compile Include="Interface\Service\ITransacaoCartaoService.cs" />
    <Compile Include="Interface\Service\ITipoCavaloClienteService.cs" />
    <Compile Include="Interface\Service\IUserBaseService.cs" />
    <Compile Include="Interface\Service\IUsuarioFilialService.cs" />
    <Compile Include="Interface\Service\IPINService.cs" />
    <Compile Include="Interface\Service\IConjuntoService.cs" />
    <Compile Include="Interface\Database\IViagemVirtualRepository.cs" />
    <Compile Include="Interface\Service\IUsuarioDocumentoService.cs" />
    <Compile Include="Interface\Service\ITipoDocumentoService.cs" />
    <Compile Include="Interface\Service\ICredenciamentoService.cs" />
    <Compile Include="Interface\Service\IDocumentoService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoBaseService.cs" />
    <Compile Include="Interface\Service\ILayoutService.cs" />
    <Compile Include="Interface\Service\IMotivoCredenciamentoService.cs" />
    <Compile Include="Interface\Service\IPagamentoConfiguracaoProcessoService.cs" />
    <Compile Include="Interface\Service\IPagamentoConfiguracaoService.cs" />
    <Compile Include="Interface\Service\IProdutoService.cs" />
    <Compile Include="Interface\Service\IProtocoloService.cs" />
    <Compile Include="Interface\Service\IVeiculosHistoricoEmpresaService.cs" />
    <Compile Include="Interface\Service\IViagemDocumentoFiscalService.cs" />
    <Compile Include="Interface\Service\IViagemDocumentoService.cs" />
    <Compile Include="Interface\Service\IViagemEventoService.cs" />
    <Compile Include="Interface\Service\IWebhookService.cs" />
    <Compile Include="Interface\Triggers\Base\ITrigger.cs" />
    <Compile Include="Interface\Triggers\IEmpresaTrigger.cs" />
    <Compile Include="Interface\Triggers\IMotoristaTrigger.cs" />
    <Compile Include="Interface\Triggers\IProtocoloTrigger.cs" />
    <Compile Include="Interface\Triggers\IViagemEventoTrigger.cs" />
    <Compile Include="Interface\Triggers\ICargaAvulsaTrigger.cs" />
    <Compile Include="Interface\Triggers\IViagemTrigger.cs" />
    <Compile Include="Interface\Service\IViagemVirtualService.cs" />
    <Compile Include="Models\AtendimentoPortador\AtendimentoPortadorResultModel.cs" />
    <Compile Include="Models\BancosFebrabanModel.cs" />
    <Compile Include="Models\Base64File.cs" />
    <Compile Include="Models\CancelarContratoAgregadoModel.cs" />
    <Compile Include="Models\CarregarContratoAgregadoModel.cs" />
    <Compile Include="Models\Ciot\AbrirContratoCiotAgregadoResultModel.cs" />
    <Compile Include="Models\Ciot\DeclararOperacaoTransporteModel.cs" />
    <Compile Include="Models\Comum\BaseResultModel.cs" />
    <Compile Include="Models\ContratoAberturaModel.cs" />
    <Compile Include="Models\ClienteProdutoEspecieModel.cs" />
    <Compile Include="Models\ConsultarBloqueioTransporteRequest.cs" />
    <Compile Include="Models\ConsultaContratoAgregadoModel.cs" />
    <Compile Include="Models\EstabelecimentoCredenciadoWebHook.cs" />
    <Compile Include="Models\EventosChequeIniciadoModel.cs" />
    <Compile Include="Models\FrotaUtilizadaModel.cs" />
    <Compile Include="Models\EncerrarContratoAgregadoModel.cs" />
    <Compile Include="Models\ImprimirComprovanteContratoAgregadoModel.cs" />
    <Compile Include="Models\PagamentoChequesModel.cs" />
    <Compile Include="Models\PagamentoFrete\CheckTokenModel.cs" />
    <Compile Include="Models\PagamentoFrete\ConsultarPorTokenModel.cs" />
    <Compile Include="Models\PagamentoFrete\PagamentosPorViagemModel.cs" />
    <Compile Include="Models\PagamentoModel.cs" />
    <Compile Include="Models\ParametrosChequeModel.cs" />
    <Compile Include="Models\Parametro\PercentualTransferenciaFreteViagemParametro.cs" />
    <Compile Include="Models\Parametro\PermissaoUsuarioAlterarLimiteAlcadas.cs" />
    <Compile Include="Models\PontoReferenciaDapperModel.cs" />
    <Compile Include="Models\Protocolo\ProtocoloConsultaModel.cs" />
    <Compile Include="Models\ProvisaoEventoModel.cs" />
    <Compile Include="Models\PushCargaModel.cs" />
    <Compile Include="Models\RecebimentoProtocoloModel.cs" />
    <Compile Include="Models\RelatorioProtocoloCapaModel.cs" />
    <Compile Include="Models\TotalEstabelecimentoModel.cs" />
    <Compile Include="Models\ValoresViagemModel.cs" />
    <Compile Include="Models\VeiculoModelAgregado.cs" />
    <Compile Include="Models\Google\DirectionsModel.cs" />
    <Compile Include="Models\ProdutoEspecieModel.cs" />
    <Compile Include="Models\RequestBaseDomainModel.cs" />
    <Compile Include="Models\SMSResponse.cs" />
    <Compile Include="Models\Grid\Base\FiltrosGridBaseModel.cs" />
    <Compile Include="Models\Grid\FiltroGridUsuarioModel.cs" />
    <Compile Include="Models\PagamentoFreteAnexoModel.cs" />
    <Compile Include="Models\PagamentoFreteModel.cs" />
    <Compile Include="Models\ProprietarioCreateModel.cs" />
    <Compile Include="Models\PushResponse.cs" />
    <Compile Include="Models\TarifasMeioHomologadoModel.cs" />
    <Compile Include="Models\TriagemProtocoloModel.cs" />
    <Compile Include="Models\GridContatosModel.cs" />
    <Compile Include="Models\Usuario\ConsultaVistoriadores.cs" />
    <Compile Include="Models\ViagemModelAgregado.cs" />
    <Compile Include="Service\AdressService.cs" />
    <Compile Include="Service\AtendimentoPortadorService.cs" />
    <Compile Include="Service\BloqueioGestorTipoService.cs" />
    <Compile Include="Service\BloqueioGestorValorService.cs" />
    <Compile Include="Models\ViagemEventoByCiotModel.cs" />
    <Compile Include="Service\CargaAvulsaService.cs" />
    <Compile Include="Service\CombustivelJSLService.cs" />
    <Compile Include="Service\CteService.cs" />
    <Compile Include="Service\FilialContatoService.cs" />
    <Compile Include="Service\ContratoCiotAgregadoService.cs" />
    <Compile Include="Service\EstabelecimentoAssociacaoService.cs" />
    <Compile Include="Service\EstabelecimentoBaseContaBancariaService.cs" />
    <Compile Include="Service\EstabelecimentoContaBancariaService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoBaseDocumentoService.cs" />
    <Compile Include="Service\ExtratoConsolidadoService.cs" />
    <Compile Include="Service\ImportacaoDadosService.cs" />
    <Compile Include="Service\LimiteTransacaoPortadorService.cs" />
    <Compile Include="Service\LocalizacaoUsuarioService.cs" />
    <Compile Include="Service\ParametrosService.cs" />
    <Compile Include="Service\PedagioRotaService.cs" />
    <Compile Include="Service\PedagioService.cs" />
    <Compile Include="Service\PermissaoCartaoService.cs" />
    <Compile Include="Service\PlanoService.cs" />
    <Compile Include="Service\PortadorService.cs" />
    <Compile Include="Service\PrestacaoContasService.cs" />
    <Compile Include="Service\ProdutoDadosCargaService.cs" />
    <Compile Include="Service\ResgateCartaoAtendimentoService.cs" />
    <Compile Include="Service\RotaModeloService.cs" />
    <Compile Include="Service\RotasCacheService.cs" />
    <Compile Include="Service\RoteirizadorService.cs" />
    <Compile Include="Service\RsaCryptoService.cs" />
    <Compile Include="Service\SaldoCartaFreteService.cs" />
    <Compile Include="Service\ClienteProdutoEspecieService.cs" />
    <Compile Include="Models\ViagemConsultaModel.cs" />
    <Compile Include="Service\CiotV3Service.cs" />
    <Compile Include="Service\DeclaracaoCiotService.cs" />
    <Compile Include="Enum\EResultadoDeclaracaoCiot.cs" />
    <Compile Include="Service\EmpresaContaBancariaService.cs" />
    <Compile Include="Service\SolicitacaoChavePixService.cs" />
    <Compile Include="Service\TagExtrattaService.cs" />
    <Compile Include="Service\TransacaoPixService.cs" />
    <Compile Include="Service\UsuarioPermissaoCartaoService.cs" />
    <Compile Include="Service\UsuarioPermissaoFinanceiroService.cs" />
    <Compile Include="Service\UsuarioPermissaoGestorService.cs" />
    <Compile Include="Service\UsuarioPermissoesConcedidasMobileService.cs" />
    <Compile Include="Service\UsuarioService.cs" />
    <Compile Include="Service\VersaoAnttLazyLoadService.cs" />
    <Compile Include="Service\ViagemPendenteGestorService.cs" />
    <Compile Include="Service\CartoesService.cs" />
    <Compile Include="Service\ConjuntoEmpresaService.cs" />
    <Compile Include="Service\LayoutCartaoService.cs" />
    <Compile Include="Service\MotivoService.cs" />
    <Compile Include="Service\PINService.cs" />
    <Compile Include="Service\ConjuntoService.cs" />
    <Compile Include="Service\Coordinate.cs" />
    <Compile Include="Service\ProdutoService.cs" />
    <Compile Include="Service\SMSService.cs" />
    <Compile Include="Service\TransacaoCartaoService.cs" />
    <Compile Include="Service\LogSmsService.cs" />
    <Compile Include="Service\TipoCavaloClienteService.cs" />
    <Compile Include="Service\UsuarioDocumentoService.cs" />
    <Compile Include="Service\TipoDocumentoService.cs" />
    <Compile Include="Service\VeiculosHistoricoEmpresaService.cs" />
    <Compile Include="Service\ViagemDocumentoFiscalService.cs" />
    <Compile Include="Service\ViagemDocumentoService.cs" />
    <Compile Include="Service\ViagemEventoProtocoloAnexoService.cs" />
    <Compile Include="Service\ViagemSolicitacaoAbonoService.cs" />
    <Compile Include="Service\EnumService.cs" />
    <Compile Include="Service\CredenciamentoService.cs" />
    <Compile Include="Service\EstabelecimentoBaseProdutoService.cs" />
    <Compile Include="Service\EstabelecimentoProdutoService.cs" />
    <Compile Include="Service\GoogleGeoCodeResponse.cs" />
    <Compile Include="Service\LayoutService.cs" />
    <Compile Include="Service\PagamentoConfiguracaoProcessoService.cs" />
    <Compile Include="Service\PagamentoConfiguracaoService.cs" />
    <Compile Include="Entities\TipoEstabelecimento.cs" />
    <Compile Include="Entities\TipoNotificacao.cs" />
    <Compile Include="Entities\UsuarioCliente.cs" />
    <Compile Include="Entities\UsuarioPreferencias.cs" />
    <Compile Include="Entities\Notificacao.cs" />
    <Compile Include="Entities\ProprietarioContato.cs" />
    <Compile Include="Entities\ProprietarioEndereco.cs" />
    <Compile Include="Entities\EmpresaLayout.cs" />
    <Compile Include="Entities\UsuarioContato.cs" />
    <Compile Include="Entities\UsuarioHorarioCheckIn.cs" />
    <Compile Include="Entities\ViagemCarga.cs" />
    <Compile Include="Entities\ViagemCarreta.cs" />
    <Compile Include="Entities\ViagemCheck.cs" />
    <Compile Include="Entities\CheckIn.cs" />
    <Compile Include="Entities\Cidade.cs" />
    <Compile Include="Entities\Common\EnderecoBase.cs" />
    <Compile Include="Entities\Especie.cs" />
    <Compile Include="Entities\Mensagem.cs" />
    <Compile Include="Entities\ClienteAcesso.cs" />
    <Compile Include="Entities\Cliente.cs" />
    <Compile Include="Entities\Estado.cs" />
    <Compile Include="Entities\Filial.cs" />
    <Compile Include="Entities\GrupoUsuario.cs" />
    <Compile Include="Entities\GrupoUsuarioMenu.cs" />
    <Compile Include="Entities\Menu.cs" />
    <Compile Include="Entities\Modulo.cs" />
    <Compile Include="Entities\EmpresaModulo.cs" />
    <Compile Include="Entities\Motorista.cs" />
    <Compile Include="Entities\MotoristaMovel.cs" />
    <Compile Include="Entities\Pais.cs" />
    <Compile Include="Entities\Proprietario.cs" />
    <Compile Include="Entities\TipoCarreta.cs" />
    <Compile Include="Entities\TipoCavalo.cs" />
    <Compile Include="Entities\TipoCombustivel.cs" />
    <Compile Include="Entities\Empresa.cs" />
    <Compile Include="Entities\UsuarioEndereco.cs" />
    <Compile Include="Entities\UsuarioFilial.cs" />
    <Compile Include="Entities\Usuario.cs" />
    <Compile Include="Entities\VeiculoTipoCombustivel.cs" />
    <Compile Include="Entities\VeiculoConjunto.cs" />
    <Compile Include="Entities\Veiculo.cs" />
    <Compile Include="Entities\Viagem.cs" />
    <Compile Include="Enum\ECategoriaTipoCarreta.cs" />
    <Compile Include="Enum\ECategoriaTipoCavalo.cs" />
    <Compile Include="Enum\EIconePara.cs" />
    <Compile Include="Enum\ENotificacaoPushExecutarRegra.cs" />
    <Compile Include="Enum\EOperador.cs" />
    <Compile Include="Enum\EOperadorOrder.cs" />
    <Compile Include="Enum\ERegiaoBrasil.cs" />
    <Compile Include="Enum\ETipoArquivo.cs" />
    <Compile Include="Enum\EValidaRaio.cs" />
    <Compile Include="Enum\ETipoAcessoSistema.cs" />
    <Compile Include="Enum\ETipoBuscaViagens.cs" />
    <Compile Include="Enum\EProcesso.cs" />
    <Compile Include="Enum\EStatusIntegracao.cs" />
    <Compile Include="Enum\EPerfil.cs" />
    <Compile Include="Enum\EStatusCheckViagem.cs" />
    <Compile Include="Enum\EStatusViagem.cs" />
    <Compile Include="Enum\ETipoCliente.cs" />
    <Compile Include="Enum\ETipoContrato.cs" />
    <Compile Include="Enum\ETipoEvento.cs" />
    <Compile Include="Enum\ETipoPessoa.cs" />
    <Compile Include="Enum\ETipoRodagem.cs" />
    <Compile Include="Enum\ETipoViagem.cs" />
    <Compile Include="Enum\EStatusUsuario.cs" />
    <Compile Include="Enum\EUnidadeMedidaDistancia.cs" />
    <Compile Include="Extensions\EnumExtensions.cs" />
    <Compile Include="Grid\AutorizacaoEmpresaMenuGrid.cs" />
    <Compile Include="Grid\CidadeGrid.cs" />
    <Compile Include="Grid\GrupoUsuarioMenuGrid.cs" />
    <Compile Include="Grid\MenuGrid.cs" />
    <Compile Include="Grid\OrderFilters.cs" />
    <Compile Include="Grid\QueryFilters.cs" />
    <Compile Include="Grid\TipoCarretaGrid.cs" />
    <Compile Include="Grid\TipoCavaloGrid.cs" />
    <Compile Include="Helpers\MapHelper.cs" />
    <Compile Include="Helpers\DateTimeHelper.cs" />
    <Compile Include="Helpers\EnumHelpers.cs" />
    <Compile Include="Helpers\MD5Hash.cs" />
    <Compile Include="Helpers\RegexUtilities.cs" />
    <Compile Include="Helpers\StringHelper.cs" />
    <Compile Include="Interface\Dapper\ICidadeDapper.cs" />
    <Compile Include="Interface\Dapper\IEmpresaDapper.cs" />
    <Compile Include="Interface\Dapper\INotificacaoPushDapper.cs" />
    <Compile Include="Interface\Dapper\INotificacaoDapper.cs" />
    <Compile Include="Interface\Dapper\IMotoristaDapper.cs" />
    <Compile Include="Interface\Dapper\ITipoCarretaDapper.cs" />
    <Compile Include="Interface\Dapper\IVeiculoDapper.cs" />
    <Compile Include="Interface\Database\IAutenticacaoAplicacaoRepository.cs" />
    <Compile Include="Interface\Database\IAutorizacaoEmpresaRepository.cs" />
    <Compile Include="Interface\Database\IAuthSessionRepository.cs" />
    <Compile Include="Interface\Database\ICheckInRepository.cs" />
    <Compile Include="Interface\Database\IEspecieRepository.cs" />
    <Compile Include="Interface\Database\IGruposUsuariosRepository.cs" />
    <Compile Include="Interface\Database\IIconeRepository.cs" />
    <Compile Include="Interface\Database\INotificacaoPushItemRepository.cs" />
    <Compile Include="Interface\Database\INotificacaoPushRepository.cs" />
    <Compile Include="Interface\Database\IMensagemDestinatarioRepository.cs" />
    <Compile Include="Interface\Database\IMensagemGrupoDestinatarioRepository.cs" />
    <Compile Include="Interface\Database\IMensagemGrupoUsuarioRepository.cs" />
    <Compile Include="Interface\Database\IModuloMenuRepository.cs" />
    <Compile Include="Interface\Database\INotificacaoRepository.cs" />
    <Compile Include="Interface\Database\IEstabelecimentoRepository.cs" />
    <Compile Include="Interface\Database\ITipoEstabelecimentoRepository.cs" />
    <Compile Include="Interface\Database\ITipoNotificacaoRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioClienteRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioPreferenciasRepository.cs" />
    <Compile Include="Interface\DataMediaServer\IDataMediaServerRepository.cs" />
    <Compile Include="Interface\Database\IConsultaRepository.cs" />
    <Compile Include="Interface\Service\IAutenticacaoAplicacaoService.cs" />
    <Compile Include="Interface\Service\IAutorizacaoEmpresaService.cs" />
    <Compile Include="Interface\Service\ICheckInService.cs" />
    <Compile Include="Interface\Service\ICidadeService.cs" />
    <Compile Include="Interface\Service\IEmailService.cs" />
    <Compile Include="Interface\Service\IEspecieServic.cs" />
    <Compile Include="Interface\Service\IEspecieService.cs" />
    <Compile Include="Interface\Service\IEstadoService.cs" />
    <Compile Include="Interface\Service\IGrupoUsuarioService.cs" />
    <Compile Include="Interface\Service\IIConeService.cs" />
    <Compile Include="Interface\Service\IMensagemDestinatarioService.cs" />
    <Compile Include="Interface\Service\IMensagemGrupoUsuarioService.cs" />
    <Compile Include="Interface\Service\IModuloMenuService.cs" />
    <Compile Include="Interface\Service\IPushService.cs" />
    <Compile Include="Interface\Service\INotificacaoService.cs" />
    <Compile Include="Interface\Service\IMensagemService.cs" />
    <Compile Include="Interface\Service\IMenuService.cs" />
    <Compile Include="Interface\Service\IModuloService.cs" />
    <Compile Include="Interface\Service\IMotoristaMovelService.cs" />
    <Compile Include="Interface\Dapper\Common\IQueryDapper.cs" />
    <Compile Include="Interface\Service\IPaisService.cs" />
    <Compile Include="Interface\Service\ITipoCombustivelService.cs" />
    <Compile Include="Interface\Service\IEmpresaService.cs" />
    <Compile Include="Interface\Service\IEstabelecimentoService.cs" />
    <Compile Include="Interface\Service\ITipoEstabelecimentoService.cs" />
    <Compile Include="Interface\Service\IUsuarioClienteService.cs" />
    <Compile Include="Interface\Service\IUsuarioService.cs" />
    <Compile Include="Interface\Service\IVeiculoService.cs" />
    <Compile Include="Interface\Service\IViagemCheckService.cs" />
    <Compile Include="Models\MensagemPushModel.cs" />
    <Compile Include="Models\NotificacaoPushModel.cs" />
    <Compile Include="Models\EmailModel.cs" />
    <Compile Include="Models\ModuloEstruturaModel.cs" />
    <Compile Include="Models\PlacasViagemModel.cs" />
    <Compile Include="Models\PushRequest.cs" />
    <Compile Include="Models\DistanciaModel.cs" />
    <Compile Include="Models\IdentityUserModel.cs" />
    <Compile Include="Models\LocalizacaoModel.cs" />
    <Compile Include="Models\MenuEstruturaModel.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Service\AuthSessionService.cs" />
    <Compile Include="Service\AutenticacaoAplicacaoService.cs" />
    <Compile Include="Service\AutorizacaoEmpresaService.cs" />
    <Compile Include="Service\CheckInService.cs" />
    <Compile Include="Service\CheckService.cs" />
    <Compile Include="Service\DataMediaServerService.cs" />
    <Compile Include="Service\Common\ServiceBase.cs" />
    <Compile Include="Service\EmailService.cs" />
    <Compile Include="Service\ClienteService.cs" />
    <Compile Include="Service\EspecieService.cs" />
    <Compile Include="Service\EstabelecimentoBaseService.cs" />
    <Compile Include="Service\FilialService.cs" />
    <Compile Include="Service\CidadeService.cs" />
    <Compile Include="Helpers\GoogleMapsHelper.cs" />
    <Compile Include="Service\MotivoCredenciamentoService.cs" />
    <Compile Include="Service\DocumentoService.cs" />
    <Compile Include="Service\PagamentoFreteService.cs" />
    <Compile Include="Service\ProtocoloService.cs" />
    <Compile Include="Service\IconeService.cs" />
    <Compile Include="Service\MensagemGrupoUsuarioService.cs" />
    <Compile Include="Service\ModuloMenuService.cs" />
    <Compile Include="Service\PushService.cs" />
    <Compile Include="Service\RotaService.cs" />
    <Compile Include="Service\NotificacaoService.cs" />
    <Compile Include="Service\MensagemService.cs" />
    <Compile Include="Service\GrupoUsuarioMenuService.cs" />
    <Compile Include="Service\TipoCarretaService.cs" />
    <Compile Include="Service\TipoCavaloService.cs" />
    <Compile Include="Service\NotificacaoPushService.cs" />
    <Compile Include="Service\EstabelecimentoService.cs" />
    <Compile Include="Service\TipoEstabelecimentoService.cs" />
    <Compile Include="Service\TipoNotificacaoService.cs" />
    <Compile Include="Service\UsuarioClienteService.cs" />
    <Compile Include="Service\VeiculoCombustivelService.cs" />
    <Compile Include="Interface\Service\Common\IService.cs" />
    <Compile Include="Interface\Service\ICheckService.cs" />
    <Compile Include="Interface\Service\IClienteService.cs" />
    <Compile Include="Interface\Service\IFilialService.cs" />
    <Compile Include="Interface\Service\IMotoristaService.cs" />
    <Compile Include="Interface\Service\IProprietarioService.cs" />
    <Compile Include="Interface\Service\ITipoCarretaService.cs" />
    <Compile Include="Interface\Service\ITipoCavaloService.cs" />
    <Compile Include="Interface\Service\IVeiculoCombustivelService.cs" />
    <Compile Include="Interface\Service\IViagemService.cs" />
    <Compile Include="Interface\Database\Common\IRepository.cs" />
    <Compile Include="Interface\Database\IMotoristaMovelRepository.cs" />
    <Compile Include="Interface\Database\IGrupoUsuarioMenuRepository.cs" />
    <Compile Include="Interface\Database\IEmpresaModuloRepository.cs" />
    <Compile Include="Interface\Database\IMenuRepository.cs" />
    <Compile Include="Interface\Database\ICheckRepository.cs" />
    <Compile Include="Interface\Database\IProprietarioRepository.cs" />
    <Compile Include="Interface\Database\ITipoCarretaRepository.cs" />
    <Compile Include="Interface\Database\ITipoCavaloRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioFilialRepository.cs" />
    <Compile Include="Interface\Database\IVeiculoCombustivelRepository.cs" />
    <Compile Include="Interface\Database\IVeiculoConjuntoRepository.cs" />
    <Compile Include="Service\MenuService.cs" />
    <Compile Include="Service\EmpresaModuloService.cs" />
    <Compile Include="Service\MotoristaMovelService.cs" />
    <Compile Include="Service\ProprietarioService.cs" />
    <Compile Include="Service\UsuarioFilialService.cs" />
    <Compile Include="Service\VeiculoConjuntoService.cs" />
    <Compile Include="Service\ViagemCarretaService.cs" />
    <Compile Include="Service\ViagemCheckService.cs" />
    <Compile Include="Service\ViagemEstabelecimentoService.cs" />
    <Compile Include="Service\ViagemEventoService.cs" />
    <Compile Include="Service\ViagemService.cs" />
    <Compile Include="Service\VeiculoService.cs" />
    <Compile Include="Service\TipoCombustivelService.cs" />
    <Compile Include="Service\PaisService.cs" />
    <Compile Include="Service\MotoristaService.cs" />
    <Compile Include="Service\ModuloService.cs" />
    <Compile Include="Service\GrupoUsuarioService.cs" />
    <Compile Include="Service\EstadoService.cs" />
    <Compile Include="Service\EmpresaService.cs" />
    <Compile Include="Interface\Database\IFilialRepository.cs" />
    <Compile Include="Interface\Database\ICidadeRepository.cs" />
    <Compile Include="Interface\Database\IClienteRepository.cs" />
    <Compile Include="Interface\Database\IMensagemRepository.cs" />
    <Compile Include="Interface\Database\IViagemRepository.cs" />
    <Compile Include="Interface\Database\IVeiculoRepository.cs" />
    <Compile Include="Interface\Database\IUsuarioRepository.cs" />
    <Compile Include="Interface\Database\ITipoCombustivelRepository.cs" />
    <Compile Include="Interface\Database\IPaisRepository.cs" />
    <Compile Include="Interface\Database\IMotoristaRepository.cs" />
    <Compile Include="Interface\Database\IModuloRepository.cs" />
    <Compile Include="Interface\Database\IGrupoUsuarioRepository.cs" />
    <Compile Include="Interface\Database\IEstadoRepository.cs" />
    <Compile Include="Interface\Database\IEmpresaRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Interface\Database\Common\IRepositoryDapper.cs" />
    <Compile Include="Service\ViagemVirtualService.cs" />
    <Compile Include="Service\WebhookService.cs" />
    <Compile Include="Service\WebServiceHelper.cs" />
    <Compile Include="Service\WhiteListIPService.cs" />
    <Compile Include="Trigger\Base\Trigger.cs" />
    <Compile Include="Interface\Triggers\IEstabelecimentoBaseTrigger.cs" />
    <Compile Include="Trigger\EstabelecimentoBaseTrigger.cs" />
    <Compile Include="Trigger\CargaAvulsaTrigger.cs" />
    <Compile Include="Trigger\ProtocoloTrigger.cs" />
    <Compile Include="Trigger\ViagemEventoTrigger.cs" />
    <Compile Include="Validation\AssertionConcern.cs" />
    <Compile Include="Validation\BusinessResult.cs" />
    <Compile Include="Validation\Interface\IBusinessResult.cs" />
    <Compile Include="Validation\ValidationError.cs" />
    <Compile Include="Validation\ValidationResult.cs" />
    <Compile Include="Validators\LocalizacaoUsuarioAddModelValidator.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15A48F30-13BE-47C9-A4A5-CDA5DFCCA13C}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.CrossCutting.Reports\ATS.CrossCutting.Reports.csproj">
      <Project>{3f484548-f9a3-4e22-a74d-8dd198c7abab}</Project>
      <Name>ATS.CrossCutting.Reports</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Data.Repository.External\ATS.Data.Repository.External.csproj">
      <Project>{8a62a98c-9d32-48ee-b100-0259b1fdf08a}</Project>
      <Name>ATS.Data.Repository.External</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.DataMedia.Context\ATS.MongoDB.Context.csproj">
      <Project>{6E8229CB-290B-48E6-A229-26C2E1A8F422}</Project>
      <Name>ATS.DataMedia.Context</Name>
      <Name>ATS.MongoDB.Context</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sistema.Framework\Sistema.Framework.Util\Sistema.Framework.Util.csproj">
      <Project>{2a5da508-d09f-4dc8-b0d6-e21a6e1a7ae6}</Project>
      <Name>Sistema.Framework.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Autofac, Version=8.2.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.8.2.0\lib\netstandard2.0\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=4.2.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.4.2.1\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="BouncyCastle, Version=1.8.5.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.NetFramework.1.8.5.2\lib\net20\BouncyCastle.dll</HintPath>
    </Reference>
    <Reference Include="ClosedXML, Version=0.95.4.0, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\packages\ClosedXML.0.95.4\lib\net46\ClosedXML.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Dapper, Version=1.50.5.0, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\packages\Dapper.1.50.5\lib\net451\Dapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DnsClient, Version=1.6.1.0, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.6.1\lib\net471\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.7.2.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.7.2\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.MappingAPI, Version=6.1.0.9, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.MappingAPI.6.1.0.9\lib\net45\EntityFramework.MappingAPI.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader, Version=3.6.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa">
      <HintPath>..\packages\ExcelDataReader.3.6.0\lib\net45\ExcelDataReader.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ExcelDataReader.DataSet, Version=3.6.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa">
      <HintPath>..\packages\ExcelDataReader.DataSet.3.6.0\lib\net35\ExcelDataReader.DataSet.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=1.0.10.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca">
      <HintPath>..\packages\ExcelNumberFormat.1.0.10\lib\net20\ExcelNumberFormat.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="FluentValidation, Version=6.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.6.2.1.0\lib\Net45\FluentValidation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis, Version=1.68.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.68.0\lib\net462\Google.Apis.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.68.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.68.0\lib\net462\Google.Apis.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.68.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.68.0\lib\net462\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="MassTransit.Abstractions, Version=8.2.0.0, Culture=neutral, PublicKeyToken=b8e0e9f2f1e657fa, processorArchitecture=MSIL">
      <HintPath>..\packages\MassTransit.Abstractions.8.2.0-develop.1655\lib\net472\MassTransit.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.2\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.Memory, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Memory.9.0.0\lib\net462\Microsoft.Bcl.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.TimeProvider.8.0.1\lib\net462\Microsoft.Bcl.TimeProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=6.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=8.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.8.6.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=8.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.8.6.0\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=8.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.8.6.0\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=8.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.8.6.0\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=1*******, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
      <HintPath>..\lib\Microsoft.Office.Interop.Excel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Registry.5.0.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=3.2.0.0, Culture=neutral, PublicKeyToken=94992a530f44e321, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.3.2.0\lib\net472\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=3.2.0.0, Culture=neutral, PublicKeyToken=94992a530f44e321, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.3.2.0\lib\net472\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=2.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.Core.2.7.0\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="QRCoder, Version=1.3.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.3.2\lib\net40\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="SharpCompress, Version=0.30.1.0, Culture=neutral, PublicKeyToken=afb0a02973931d96, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpCompress.0.30.1\lib\net461\SharpCompress.dll</HintPath>
    </Reference>
    <Reference Include="Snappier, Version=1.0.0.0, Culture=neutral, PublicKeyToken=a1b25124e6e13a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Snappier.1.0.0\lib\netstandard2.0\Snappier.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Barcode, Version=1.2.5.21040, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\lib\Spire.Barcode.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Activities" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.CodeDom, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.CodeDom.7.0.0\lib\net462\System.CodeDom.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Device" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=8.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.8.6.0\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.0.1\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\packages\System.IO.Packaging.4.0.0\lib\net46\System.IO.Packaging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.2\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Dynamic, Version=1.0.6132.35681, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Dynamic.1.0.7\lib\net40\System.Linq.Dynamic.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.5.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.2\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.2\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="TrackerEnabledDbContext, Version=3.6.1.0, Culture=neutral, PublicKeyToken=4f92af0b908c4a0a, processorArchitecture=MSIL">
      <HintPath>..\packages\TrackerEnabledDbContext.3.6.1\lib\net45\TrackerEnabledDbContext.dll</HintPath>
    </Reference>
    <Reference Include="TrackerEnabledDbContext.Common, Version=3.6.1.0, Culture=neutral, PublicKeyToken=4f92af0b908c4a0a, processorArchitecture=MSIL">
      <HintPath>..\packages\TrackerEnabledDbContext.Common.3.6.1\lib\net45\TrackerEnabledDbContext.Common.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.3.2\lib\net40\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="ZstdSharp, Version=0.7.3.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.7.3\lib\net461\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
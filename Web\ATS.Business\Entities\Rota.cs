﻿using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class Rota
    {
        public int IdRota { get; set; }
        public int IdEmpresa { get; set; }
        public string Descricao { get; set; }
        public int IdCidadeOrigem { get; set; }
        public int IdCidadeDestino { get; set; }
        public bool Ativo { get; set; }
        public DateTime DataBase { get; set; } = DateTime.Now;
        public string From { get; set; }
        public decimal FromLatitude { get; set; }
        public decimal FromLongitude { get; set; }
        public string To { get; set; }
        public decimal ToLatitude { get; set; }
        public decimal ToLongitude { get; set; }
        public int TotalSegundos { get; set; }
        public string TotalTempoViagem { get; set; }
        public double TotalKm { get; set; }

        public virtual Cidade CidadeOrigem { get; set; }
        public virtual Cidade CidadeDestino { get; set; }
        public virtual Empresa Empresa { get; set; }

        public virtual ICollection<RotaTrajeto> RotaTrajeto { get; set; } = new List<RotaTrajeto>();
        public virtual ICollection<RotaEstabelecimento> RotaEstabelecimento { get; set; }
    }
}

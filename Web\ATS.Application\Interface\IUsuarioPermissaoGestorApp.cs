﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IUsuarioPermissaoGestorApp : IAppBase<UsuarioPermissaoGestor>
    {
        ValidationResult Integrar(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo, bool empresa, bool filial);
        ValidationResult Integrar(int idUsuario, Dictionary<EBloqueioGestorTipo, KeyValuePair<bool, bool>> permissoesBloqueioGestor);
        UsuarioPermissaoGestor GetParametroPermissaoGestor(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo);
    }
}
﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.Pedagio.ReciboPagamento
{
    public class ReciboPagamento
    {
        public byte[] GetReport(ReciboPagamentoDataType reciboPagamento, List<ReciboPagamentoHistoricoParcelasDataType> parcelas, 
            string valorTotalParcelas, bool viagemPossuiPedagio)
        {
            try
            {
                var parametrizes = new Tuple<string, string, bool>[1];
                parametrizes[0] = new Tuple<string, string, bool>("ValorTotalParcelas", valorTotalParcelas, true);

                var dataSources = new List<Tuple<object, string>>
                {
                    new (new List<ReciboPagamentoDataType> {reciboPagamento}, "ReciboPagamentoDts"), 
                    new (parcelas, "ParcelasDts")
                };

                var caminhoRelatorio = viagemPossuiPedagio
                    ? "ATS.CrossCutting.Reports.Pedagio.ReciboPagamento.ReciboPagamentoComprovanteCarga.rdlc"
                    : "ATS.CrossCutting.Reports.Pedagio.ReciboPagamento.ReciboPagamento.rdlc";
                    
                    var bytes = 
                    new Base.Reports().GetReport(dataSources, parametrizes, true, caminhoRelatorio, ConstantesUtils.FormatoPdfMinusculo);

                return bytes;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
﻿using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.DTO.Estabelecimento;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using AutoMapper.QueryableExtensions;

namespace ATS.Data.Repository.EntityFramework
{
    public class EstabelecimentoBaseRepository : Repository<EstabelecimentoBase>, IEstabelecimentoBaseRepository
    {
        public EstabelecimentoBaseRepository(AtsContext context) : base(context)
        {
        }
        
        public List<AssociacoesEmpresaDto> GetAssociacoesEmpresas(List<int> idsEmpresa)
        {
            return Where(b => b.Associacao && b.Estabelecimento.Any(e => idsEmpresa.Contains(e.IdEmpresa)))
                .ProjectTo<AssociacoesEmpresaDto>().ToList();
        }
        
        public int GetIdByCnpj(string cnpj)
        {
            return Where(b => b.CNPJEstabelecimento == cnpj).Select(b => b.IdEstabelecimento).FirstOrDefault();
        }
    }
}

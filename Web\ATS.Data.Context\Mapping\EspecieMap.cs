﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EspecieMap : EntityTypeConfiguration<Especie>
    {
        public EspecieMap()
        {
            ToTable("ESPECIE");

            HasKey(t => t.IdEspecie);

            Property(t => t.IdEspecie)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .IsRequired()
                .HasMaxLength(100);

            Property(x => x.DataHoraUltimaAtualizacao)
                .IsRequired();

            Property(o => o.CodigoFreteBras)
                .IsOptional();
        }
    }
}

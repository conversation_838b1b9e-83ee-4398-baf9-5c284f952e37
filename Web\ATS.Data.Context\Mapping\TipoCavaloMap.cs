﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TipoCavaloMap : EntityTypeConfiguration<TipoCavalo>
    {
        public TipoCavaloMap()
        {
            ToTable("TIPO_CAVALO");

            HasKey(t => t.IdTipoCavalo);

            Property(t => t.IdTipoCavalo)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Categoria)
                .IsRequired();

            Property(t => t.IdEmpresa)
               .IsOptional();

            Property(t => t.Ativo)
                .IsRequired();

            HasOptional(t => t.Empresa)
                .WithMany(t => t.TiposCavalo)
                .HasForeignKey(d => d.IdEmpresa);

            Property(x => x.DataHoraUltimaAtualizacao)
                .IsRequired();
        }
    }
}
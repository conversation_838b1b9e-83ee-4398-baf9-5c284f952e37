﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Common.Request
{
    public class ConsultarViagemRequest : RequestBase
    {
        /// <summary>
        /// CPF do motorista vinculado a viagem
        /// </summary>
        public string CPFMotorista { get; set; }

        public string TokenViagem { get; set; }
        public DateTime? DataLancamentoInicial { get; set; }
        public DateTime? DataLancamentoFinal { get; set; }

        /// <summary>
        /// Status da viagem que se deseja consultar
        /// </summary>
        public List<EStatusViagem> StatusViagem { get; set; }

        /// <summary>
        /// Status do check da viagem que se deseja consultar
        /// </summary>
        public List<EStatusCheckViagem> StatusCheckViagem { get; set; }

        /// <summary>
        /// Ids da viagem que deseja consultar
        /// </summary>
        public List<int> IdsViagem { get; set; }
        
        /// <summary>
        /// Numeros de controle que deseja consultar
        /// </summary>
        public List<string> NumerosControle { get; set; }

    }
}
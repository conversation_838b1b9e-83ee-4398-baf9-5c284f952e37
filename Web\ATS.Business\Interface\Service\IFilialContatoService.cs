﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IFilialContatoService : IService<FilialContatos>
    {
        ValidationResult Update(FilialContatos filialContato);
        ValidationResult Add(FilialContatos filialContato);

        /// <summary>
        /// Remove filial contato
        /// </summary>
        /// <param name="filialContato"></param>
        /// <returns></returns>
        ValidationResult Delete(FilialContatos filialContato);
    }
}

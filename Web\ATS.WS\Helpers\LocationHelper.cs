﻿using System;
using System.Net.Http;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using ATS.WS.Models.Webservice.Response.Localizacao;
using Newtonsoft.Json;

namespace ATS.WS.Helpers
{
    public class LocationHelper
    {
        private readonly ICidadeApp _cidadeApp;
        private readonly WebServiceHelperWs _webServiceHelperWs;
        private readonly IEstadoApp _estadoApp;
        private readonly IPaisApp _paisApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IUserIdentity _userIdentity;
        private readonly HttpClient _httpClient = new HttpClient();

        public LocationHelper(ICidadeApp cidadeApp, WebServiceHelperWs webServiceHelperWs, IEstadoApp estadoApp, 
            IPaisApp paisApp, IUsuarioApp usuarioApp, IUserIdentity userIdentity)
        {
            _cidadeApp = cidadeApp;
            _webServiceHelperWs = webServiceHelperWs;
            _estadoApp = estadoApp;
            _paisApp = paisApp;
            _usuarioApp = usuarioApp;
            _userIdentity = userIdentity;
        }

        public void SetCoordinatesObject(int num, string endereco, string bairro, int codIbgeCidade, int codIbgeEstado,
            int codBacenPais, out decimal lat, out decimal lng)
        {
            var end = string.Empty;
            if (num != 0)
                end += num + ",";
            if (!string.IsNullOrWhiteSpace(endereco))
                end += endereco + ",";
            if (!string.IsNullOrWhiteSpace(bairro))
                end += bairro + ",";
                
            if (codIbgeCidade > 0)
            {
                var cidade = _cidadeApp.GetCidadeByIBGE(codIbgeCidade);
                
                if (cidade == null)
                    throw new Exception($"Código de IBGE {codIbgeCidade} da cidade não localizado na base de dados.");
                
                end += cidade.Nome + ",";
            }
            if (codIbgeEstado > 0)
            {
                var estado = _estadoApp.GetPorIBGE(codIbgeEstado);
                
                if (estado == null)
                    throw new Exception($"Código de IBGE {codIbgeEstado} do estado não localizado na base de dados.");
                
                end += estado.Nome + ",";
            }
            if (codBacenPais > 0)
            {
                var pais = _paisApp.GetPaisPorBACEN(codBacenPais);
                
                if (pais == null)
                    throw new Exception($"Código Bacen {codBacenPais} não localizado na base de dados.");
                
                end += pais.Nome + ",";
            }

            var resultCoordinates = _webServiceHelperWs.GetCoordinateFromAdress(end);

            if ((resultCoordinates.Latitude == 0) && (resultCoordinates.Longitude == 0))
            {
                //Caso esteja na primeira vez e o endereço não foi encontrado, removemos os dados do endereço e realizamos uma nova tentativa
                if (!string.IsNullOrEmpty(endereco))
                    endereco = "";
                //Caso na segunda tentavia o endereço ainda não foi encontrado, então removemos o bairro e tentamos novamente
                else
                    bairro = "";

                //Por fim, se mesmo removendo as duas partes do endereço, ele ainda não pode ser encontrado, paramos e retornamos.
                if (string.IsNullOrWhiteSpace(endereco) && string.IsNullOrWhiteSpace(bairro))
                {
                    resultCoordinates = _webServiceHelperWs.GetCoordinateFromAdress(end);
                    if (resultCoordinates.Latitude != 0 && resultCoordinates.Longitude != 0)
                    {
                        lat = resultCoordinates.Latitude;
                        lng = resultCoordinates.Longitude;
                        return;
                    }
                    else
                    {
                        lat = 0;
                        lng = 0;
                        return;
                    }
                }
                
                SetCoordinatesObject(num, endereco, bairro, codIbgeCidade, codIbgeEstado, codBacenPais, out lat, out lng);
            }
            else
            {
                lat = resultCoordinates.Latitude;
                lng = resultCoordinates.Longitude;
            }
        }

        public ValidationResult SalvarLocalizacao(string ip)
        {
            var localizacao = new LocalizacaoUsuarioPortal()
            {
                Latitude = _userIdentity.Latitude,
                Longitude = _userIdentity.Longitude,
                Cidade = _userIdentity.Cidade,
                Provedor = _userIdentity.Provedor,
                Estado = _userIdentity.Estado,
                IdUsuario = _userIdentity.IdUsuario,
                Ip = ip,
                DataCadastro = DateTime.Now
            };
            return _usuarioApp.SalvarLocalizacao(localizacao);
        }

        public bool ExistePorUsuarioIp(string ip)
        {
            return _usuarioApp.ExisteLocalizacaoPorUsuarioIp(_userIdentity.IdUsuario, ip);
        }

        public LocalizacaoResponse GetLocalizacao(string ip)
        {
            try
            {
                var httpResponse = _httpClient.GetAsync($"http://ip-api.com/json/{ip}").ConfigureAwait(false).GetAwaiter().GetResult();
                var jsonResponse = httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false).GetAwaiter().GetResult();
                
                var deserializeSettings = new JsonSerializerSettings { Error = (_, ev) => ev.ErrorContext.Handled = true };
                var response = JsonConvert.DeserializeObject<LocalizacaoResponse>(jsonResponse, deserializeSettings);
                
                response.StatusCode = httpResponse.StatusCode;
                response.Success = httpResponse.IsSuccessStatusCode;
            
                return response;
            }
            catch (Exception)
            {
                return new LocalizacaoResponse() { Success = false };
            }
        }
    }
}
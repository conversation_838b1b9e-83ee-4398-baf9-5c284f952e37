﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class EmpresaLayoutMap : EntityTypeConfiguration<EmpresaLayout>
    {
        public EmpresaLayoutMap()
        {
            ToTable("EMPRESA_LAYOUT");

            HasKey(t => t.IdEmpresa );

            Property(x => x.IdEmpresa)
                .IsRequired()
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            HasRequired(x => x.Empresa)
                .WithOptional(x => x.Layout);
        }
    }
}
﻿using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Pedagio.GridFaturamentoTAG
{
    public class RelatorioGridFaturamentoTag
    {
        public byte[] GetReport(string tipo, RelatorioGridFaturamentoTagDataType dadosRelatorio,TotalizadorFaturamentoTagDataType totalizador)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DtoConsultaGridFaturamentoTAG"
                });
                
                var parametros = new List<ReportParameter>();
                
                //Parametros
                parametros.Add(new ReportParameter("ValorTotal",totalizador.ValorTotal));
                parametros.Add(new ReportParameter("ValorTotalProvisionado",totalizador.ValorTotalProvisionado));
                parametros.Add(new ReportParameter("ValorTotalNaoProvisionado",totalizador.ValorTotalNaoProvisionado));
                parametros.Add(new ReportParameter("ValorTotalEstornoNaoProvisionado",totalizador.ValorTotalEstornoNaoProvisionado));
                parametros.Add(new ReportParameter("ValorTotalEstornoProvisionado",totalizador.ValorTotalEstornoProvisionado));
                
                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Pedagio.GridFaturamentoTAG.RelatorioGridFaturamentoTAG.rdlc";
                localReport.SetParameters(parametros);
                
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
            }
        }
    }
}

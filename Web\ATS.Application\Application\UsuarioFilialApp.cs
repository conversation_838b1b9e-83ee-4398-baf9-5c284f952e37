﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using System;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class UsuarioFilialApp : AppBase, IUsuarioFilialApp
    {
        private readonly IUsuarioFilialService _usuarioFilialService;
        private readonly IFilialApp _filialApp;

        public UsuarioFilialApp(IUsuarioFilialService usuarioFilialService, IFilialApp filialApp)
        {
            _usuarioFilialService = usuarioFilialService;
            _filialApp = filialApp;
        }

        /// <summary>
        /// Retorna todos as filiais de um determinado usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public IQueryable<UsuarioFilial> GetFiliaisPorIdUsuario(int? idUsuario)
        {
            return _usuarioFilialService.GetFiliaisPorIdUsuario(idUsuario);
        }

        /// <summary>
        /// Retorna a única filial do usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário</param>
        /// <returns></returns>
        public int GetFilialPorIdUsuario(int idUsuario)
        {
            return _usuarioFilialService.GetFilialPorIdUsuario(idUsuario);
        }

        public Tuple<int, string> GetFilialCompletaPorIdUsuario(int idUsuario)
        {
            var idFilial = GetFilialPorIdUsuario(idUsuario);
            var nomeFilial = _filialApp.Get(idFilial).NomeFantasia;

            return new Tuple<int, string>(idFilial, nomeFilial);
        }
    }
}
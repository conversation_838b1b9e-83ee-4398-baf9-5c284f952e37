﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class EmpresaModuloApp : AppBase, IEmpresaModuloApp
    {
        private readonly IEmpresaModuloService _empresaModuloService;

        public EmpresaModuloApp(IEmpresaModuloService empresaModuloService)
        {
            _empresaModuloService = empresaModuloService;
        }

        public ValidationResult Add(EmpresaModulo entity)
        {
            throw new System.NotImplementedException();
        }

        public ValidationResult Update(EmpresaModulo entity)
        {
            throw new System.NotImplementedException();
        }

        public EmpresaModulo Get(int id)
        {
            throw new System.NotImplementedException();
        }

        public IEnumerable<EmpresaModulo> Find()
        {
            throw new System.NotImplementedException();
        }

        /// <summary>
        /// Método utilizado por listar ModuloEmpresa
        /// </summary>
        /// <param name="idEmpresa">id de Empresa</param>
        /// <returns>IQueryable de ModuloEmpresa</returns>
        public IQueryable<EmpresaModulo> ListarModuloPorIdEmpresa(int? idEmpresa)
        {
            return _empresaModuloService.ListarModuloPorIdEmpresa(idEmpresa);
        }
    }
}
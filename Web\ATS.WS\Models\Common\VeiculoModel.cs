﻿using ATS.Domain.Enum;
using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class VeiculoModel
    {
        public string Placa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdEmpresa { get; set; }
        public int? IdVeiculo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdFilial { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdProprietario { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdMotorista { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdUsuario { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Chassi { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? AnoFabricacao { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? AnoModelo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Marca { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Modelo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string RENAVAM { get; set; }

        public bool ComTracao { get; set; }
        public ETipoRodagem TipoRodagem { get; set; }
        public int? QuantidadeEixos { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string TecnologiaRastreamento { get; set; }

        public int StatusIntegracao { get; set; }
        public int? CodigoDaOperacao { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdTipoCavalo { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdTipoCarreta { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IdTecnologia { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Seguradora { get; set; }
        public long? NumeroFrota { get; set; }

        public bool Ativo { get; set; } = true;

        #region Referências

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual TipoCavaloModel TipoCavalo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public virtual TipoCarretaModel TipoCarreta { get; set; }

        #endregion
    }
}
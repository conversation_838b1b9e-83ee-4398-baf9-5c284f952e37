using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Domain.Interface.Service
{
    public interface IBloqueioGestorValorService
    {
        IQueryable<BloqueioGestorValor> GetAll();
        BloqueioGestorValorDto PegarBloqueioGestorValor(int? idEmpresa, int? idFilial);
        void IntegrarValores(BloqueioGestorValorDto valores);
        decimal? ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa,EBloqueioOrigemTipo? origem);
        decimal? ValorLimiteConfiguradoFilial(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa, int? idFilial,EBloqueioOrigemTipo? origem);
    }
}
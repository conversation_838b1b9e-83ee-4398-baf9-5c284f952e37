using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class VeiculoMap : EntityTypeConfiguration<Veiculo>
    {
        public VeiculoMap()
        {
            ToTable("VEICULO");

            HasKey(t => t.IdVeiculo);

            Property(t => t.IdVeiculo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Placa)
                .IsRequired()
                .HasMaxLength(10);

            Property(t => t.TipoPadraoPlaca)
                .IsRequired();
            
            Property(t => t.PlacaPadraoBrasil)
                .IsOptional()
                .HasMaxLength(10);

            Property(t => t.Chassi)
                .IsOptional()
                .HasMaxLength(22);

            Property(t => t.Marca)
               .IsOptional()
               .HasMaxLength(50);

            Property(t => t.Modelo)
               .IsOptional()
               .HasMaxLength(50);

            Property(t => t.TecnologiaRastreamento)
                .IsOptional()
                .HasMaxLength(100);

            Property(x => x.DataUltimaAtualizacao)
                .IsOptional();

            HasMany(t => t.Veiculos)
                .WithMany(t => t.VeiculoConjuntos)
                .Map(m =>
                {
                    m.ToTable("VEICULO_CONJUNTO");
                    m.MapLeftKey("IdVeiculo");
                    m.MapRightKey("IdVeiculoConjunto");
                });

            HasOptional(a => a.Motorista)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => c.IdMotorista);

            HasOptional(a => a.Usuario)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => c.IdUsuario);

            HasOptional(a => a.Proprietario)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => new { c.IdProprietario, IdEmpresa = c.IdEmpresa });

            HasOptional(t => t.Empresa)
                .WithMany(t => t.Veiculos)
                .HasForeignKey(d => d.IdEmpresa);

            HasOptional(a => a.TipoCarreta)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => c.IdTipoCarreta);

            HasOptional(a => a.TipoCavalo)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => c.IdTipoCavalo);

            HasOptional(a => a.Filial)
                .WithMany(b => b.Veiculos)
                .HasForeignKey(c => c.IdFilial);

            HasOptional(x => x.Pais)
              .WithMany(x => x.Veiculo)
              .HasForeignKey(x => x.IdPais);

            HasOptional(x => x.Estado)
               .WithMany(x => x.Veiculo)
               .HasForeignKey(x => x.IdEstado);

            HasOptional(x => x.Cidade)
              .WithMany(x => x.Veiculo)
              .HasForeignKey(x => x.IdCidade);
        }
    }
}
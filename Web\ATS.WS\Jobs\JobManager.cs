﻿using ATS.CrossCutting.IoC.Utils;
using NLog;
using Quartz;
using System.Configuration;

namespace ATS.WS.Jobs
{
    public class JobManager
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IScheduler _scheduler;

        public JobManager(IScheduler scheduler)
        {
            _scheduler = scheduler;
        }

        public void Start()
        {
            _scheduler.Start();
            ScheduleJob<ProcessarPagamentoAgendadoJob>("ProcessarPagamentoAgendado");
        }

        private void ScheduleJob<T>(string jobSettingsKey)
            where T : IJob
        {
            var enabled = ConfigurationManager.AppSettings[$"Jobs:{jobSettingsKey}:Enabled"].ToBoolSafe(false);
            var cronExpression = ConfigurationManager.AppSettings[$"Jobs:{jobSettingsKey}:Cron"];

            _logger.Info($"Scheduling Job {jobSettingsKey} => Enabled: {enabled}, Cron: {cronExpression}");

            if (enabled)
            {
                var job = CreateJobBuilder<T>().Build();
                _scheduler.ScheduleJob(job, CreateCronTrigger(cronExpression)).Wait();
            }
        }

        private static JobBuilder CreateJobBuilder<T>()
            where T : IJob
        {
            return JobBuilder.Create<T>()
                .DisallowConcurrentExecution();
        }

        private static ITrigger CreateCronTrigger(string cronExpression)
        {
            return TriggerBuilder.Create()
                .WithCronSchedule(cronExpression)
                .Build();
        }
    }
}
﻿using System.Collections.Generic;
using System.Linq;
using ATS.Data.Repository.External.SistemaInfo.Ciot;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Domain.Interface.Service
{
    public interface ICiotV3Service : IBaseService<IDeclaracaoCiotRepository>
    {
        CiotExternalRepository CiotRepository { get; }

        void GerarToken(int idEmpresa);

        List<Viagem> GetViagensNaoCanceladas(int idDeclaracaoCiot);
        
        DeclararCiotResult EncerrarCiot(ContratoCiotAgregado contratoCiotAgregado, IContratoCiotAgregadoRepository contratoCiotAgregadoRepository,
            List<Viagem> viagens);

        ContratoCiotAgregado GetContratoAgregado(int contratoAgregadoId);
        void EnviarEmail(DadosSituacaoCiotDto dados);
        void EnviarEmailCancelamento(string ciotsCancelados, int idEmpresa);

        ValoresViagemModel CalcularValoresViagem(List<Viagem> viagens);
        
        CancelarCiotResult CancelarCiot(Viagem viagem);

        ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request);

        ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request);

        ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario);
        
        void GerarToken(string cnpjEmpresa);

//        ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario,
//            string rntrcProprietario);

        bool IsDeclaracaoTacAgregado(string placa, int idEmpresa, bool habilitarContratoCiotAgregado);

        IQueryable<DeclaracaoCiot> GetQueryDeclaracaoCiotByViagem(int idViagem, int idEmpresa);
        
        DeclaracaoCiot InserirRegistroDeclaracaoCiot(DeclararOperacaoTransporteReponse ciot, Viagem viagem,
            ContratoCiotAgregado contratoAgregado);

        DeclararOperacaoTransporteModel DeclararOperacaoTransporte(DeclararOperacaoTransporteRequest request,
            Viagem viagem, ContratoCiotAgregado contratoAgregado);

        void CancelarDeclaracaoCiot(DeclaracaoCiot declaracaoCiot,
            CancelarOperacaoTransporteRequest request, CancelarOperacaoTransporteReponse cancelarReponse);

        CancelarOperacaoTransporteReponse CancelarOperacaoTranspote(CancelarOperacaoTransporteRequest request,
            DeclaracaoCiot declaracaoCiot);

        DeclararCiotResult RetificarCiot(IContratoCiotAgregadoService contratoCiotAgregadoService, ContratoCiotAgregado contratoCiotAgregado,
            IContratoCiotAgregadoRepository contratoCiotAgregadoRepository,
            ICollection<VeiculoModelAgregado> veiculos);

        ConsultarSituacaoCiotReponse ConsultarSituacaoCiot(ConsultarSituacaoCiotRequest ciotRequest);

        ValidationResult NotificarCiotContingencia(NotificarCiotContingencia notificarCiotContingencia);

        DeclararCiotResult EncerrarCiotPadrao(Viagem viagem, DeclaracaoCiot declaracaoCiot);

        ConsultarSituacaoTransportadorReponse ConsultarSituacaoProprietarioAntt(string documento, string rntrc, string cnpjEmpresa);
        ConsultarTiposCargaResponse ConsultarTiposCarga(string cnpjEmpresa);
        ConsultarTiposCargaResponse ConsultarTiposCarga(int idEmpresa);
        ETipoDeclaracao ObterTipoDeclaracao(int viagemId);
        VeiculoRntrcDTO ObterVeiculoPorPlaca(ViagemCarreta carreta, int idEmpresa);
        ValidationResult AtualizarVeiculosContratoAgregado(IContratoCiotAgregadoService contratoCiotAgregadoService, ContratoCiotAgregado contratoCiotAgregado, ICollection<VeiculoModelAgregado> veiculos);
        AtualizarCiotResponse AtualizarCiot(AtualizarCiotRequest request);
        DadosAtualizacaoCiotDto GetDeclaracaoCiotIncludeViagens(int idDeclaracaoCiot);
    }
}
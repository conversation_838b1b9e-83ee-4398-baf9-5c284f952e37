﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;
using System.Web.WebPages;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using AutoMapper;

namespace ATS.WS.ControllersATS
{
    public class ContratoAgregadoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IViagemApp _viagemApp;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly SrvViagem _srvViagem;

        public ContratoAgregadoAtsController(IParametrosApp parametrosApp, IUserIdentity userIdentity, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IViagemApp viagemApp, IClienteApp clienteApp, IProprietarioApp proprietarioApp, ICadastrosApp cadastrosApp, IEmpresaRepository empresaRepository, SrvViagem srvViagem, IVersaoAnttLazyLoadService versaoAntt)
        {
            _parametrosApp = parametrosApp;
            _userIdentity = userIdentity;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _viagemApp = viagemApp;
            _clienteApp = clienteApp;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _empresaRepository = empresaRepository;
            _srvViagem = srvViagem;
            _versaoAntt = versaoAntt;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarContratosAgregado(DateTime dataInicio, DateTime dataFinal, int take, int page, 
            OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var contratos = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.ConsultarContratosAgregado(dataInicio, dataFinal, take, page, order, filters, _userIdentity.IdEmpresa)
                    : _ciotV3App.ConsultarContratosAgregado(take, page, order, filters, _userIdentity.IdEmpresa);
                
                return ResponderSucesso(contratos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CarregarContratoAgregado(int contratoAgregadoId, int? idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Administrador)
                    return ResponderErro("Não é possível consultar informações de contrato com usuário administrador.");

                idEmpresa = _userIdentity.IdEmpresa;
                
                if (!idEmpresa.HasValue)
                    return ResponderErro("Não é possível realizar a consulta com um usuário sem empresa configurada.");
                
                var contrato = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.CarregarContratoAgregado(contratoAgregadoId, idEmpresa.Value)
                    : _ciotV3App.CarregarContratoAgregado(contratoAgregadoId, idEmpresa.Value);
                
                return ResponderSucesso(contrato);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult AlgumContratoAberto(int idProprietario)
        {
            try
            {
                if(!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Funcionalidade disponível apenas para usuários de perfil empresa.");

                var algumContratoVigente = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.AlgumContratoAberto(idProprietario, _userIdentity.IdEmpresa.Value)
                    : _ciotV3App.AlgumContratoAberto(idProprietario, _userIdentity.IdEmpresa.Value);
                
                return ResponderSucesso(algumContratoVigente);
            }
            catch (Exception e)
            {
                return ResponderErro(e.GetBaseException().Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult AbrirContrato(ContratoAberturaModel request)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Administrador)
                    return ResponderErro("Não é possível salvar informações de contrato com usuário administrador.");

                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro(
                        "Não é possível realizar o cadastro com um usuário sem empresa configurada");
                    
                //var ciotApp = _ciotApp;
                var abrirContratoResult = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.AbrirContratoCiotAgregado(request, _userIdentity.IdEmpresa.Value)
                    : _ciotV3App.AbrirContratoCiotAgregado(request, _userIdentity.IdEmpresa.Value);
                
                var result = new
                {
                    abrirContratoResult.CiotResult?.Ciot,
                    Senha = abrirContratoResult.CiotResult?.SenhaAlteracao
                };

                return abrirContratoResult.Sucesso ? ResponderSucesso("Contrato de agregado aberto com sucesso!", result) : ResponderErro(abrirContratoResult.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult RetificarContrato(RetificarContratoAgregadoModel request)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro("Não é possível realizar o cadastro com um usuário sem empresa configurada");

                var retificarCiotAgregadoResponse = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.RetificarCiotAgregadoPorTela(_userIdentity.IdEmpresa.Value, request)
                    : _ciotV3App.RetificarCiotAgregadoPorTela(_userIdentity.IdEmpresa.Value, request);

                return retificarCiotAgregadoResponse.Sucesso 
                    ? Responder(true, null, retificarCiotAgregadoResponse) 
                    : ResponderErro(retificarCiotAgregadoResponse.Mensagem);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e.GetBaseException().Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CancelarContratoeViagens(int idContrato)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro(
                        "Não é possível realizar o cancelamento com um usuário sem empresa configurada");

                var cancelarCiotAgregado = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.CancelarCiotAgregado(_userIdentity.IdEmpresa.Value, idContrato)
                    : _ciotV3App.CancelarCiotAgregado(_userIdentity.IdEmpresa.Value, idContrato);

                if (cancelarCiotAgregado.Sucesso)
                {
                    var retorno = _srvViagem
                        .CancelarViagens(cancelarCiotAgregado.IdViagens, _userIdentity.IdEmpresa ?? 0, _userIdentity.CpfCnpj, _userIdentity.Nome);
                    if (!retorno.Sucesso)
                        ResponderErro(retorno.Mensagem);
                }

                return cancelarCiotAgregado.Sucesso ? ResponderSucesso(cancelarCiotAgregado.Mensagem) : ResponderErro(cancelarCiotAgregado.Mensagem);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        /// <summary>
        /// Encerra um contrato de agregado
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult EncerrarContrato(EncerrarContratoAgregadoRequestModel @params)
        {
            try
            {
                if (!_userIdentity.IdEmpresa.HasValue)
                    return ResponderErro(
                        "Não é possível realizar o cadastro com um usuário sem empresa configurada");

                var encerrarContratoAgregadoModel = Mapper.Map<EncerrarContratoAgregadoModel>(@params);
                var encerrarCiotAgregado = _versaoAntt.Value == EVersaoAntt.Versao2
                    ? _ciotV2App.EncerrarCiotAgregado(_userIdentity.IdEmpresa.Value, encerrarContratoAgregadoModel)
                    : _ciotV3App.EncerrarCiotAgregado(_userIdentity.IdEmpresa.Value, encerrarContratoAgregadoModel);

                return encerrarCiotAgregado.Resultado == EResultadoDeclaracaoCiot.Sucesso ? ResponderSucesso(encerrarCiotAgregado.Mensagem) : ResponderErro(encerrarCiotAgregado.Mensagem);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CancelarContrato(CancelarContratoAgregadoModel @params)
        {
            
            if (!_userIdentity.IdEmpresa.HasValue)
                return ResponderErro(
                    "Não é possível realizar o cadastro com um usuário sem empresa configurada");
            
            if (@params.MotivoCancelamento.IsEmpty())
                return ResponderErro(
                    "Por favor, informe o motivo do cancelamento");

            var cancelarCiotAgregado = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.CancelarCiotAgregado(@params, _userIdentity.IdEmpresa.Value)
                : _ciotV3App.CancelarCiotAgregado(@params, _userIdentity.IdEmpresa.Value);

            return cancelarCiotAgregado.Sucesso ? ResponderSucesso(cancelarCiotAgregado.Mensagem) : ResponderErro(cancelarCiotAgregado.Mensagem);
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult LinkComprovanteContrato(ImprimirComprovanteContratoAgregadoModel @params)
        {
            if (@params.Ciot.IsEmpty())
                return ResponderErro(
                    "Não foi possível localizar o CIOT do comprovante informado");
            if (@params.Senha.IsEmpty())
                return ResponderErro(
                    "Não foi possível localizar a senha do CIOT do comprovante informado");

            var link = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.LinkComprovanteContrato(@params)
                : _ciotV3App.LinkComprovanteContrato(@params);

            return ResponderSucesso(link);
        }
    }
}
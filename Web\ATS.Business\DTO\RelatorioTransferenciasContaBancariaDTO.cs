﻿using System;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioTransferenciasContaBancariaDTO : FiltrosGridBaseModel
    {
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public int? Produto { get; set; }
        public StatusExportacao? Status { get; set; }
        public TipoTransacao? TipoTransacao { get; set; }
        public int IdUsuario { get; set; }
        public string Nome { get; set; }
    }

    public enum StatusExportacao
    {
        Todos,
        NaoExportados,
        Exportado,
        Sucesso,
        Erro
    }

    public enum TipoTransacao
    {
        Todos,
        Transferencias,
        Estornos
    }
}
﻿using System.ComponentModel;
using System.Runtime.Serialization;
using Sistema.Framework.Util.Helper;

namespace ATS.Domain.Enum
{
    public enum EResultadoCompraPedagio
    {
        /// <summary>
        /// Solicitação de compra de pedágio realizada com sucesso
        /// </summary>
        [EnumMember, Description("Compra solicitada")]
        CompraSolicitada = 0,

        /// <summary>
        /// Erro ao criar solicitação de compra na API de pedágio
        /// </summary>
        [EnumMember, Description("Erro")]
        Erro = 1,

        /// <summary>
        /// Recurso de compra de pedágio desabilitada durante a integração da viagem (Parâmetro Pedagio.Fornecedor = 0)
        /// Recurso de compra de pedágio nào foi realizado por erro anterior.
        /// </summary>
        [EnumMember, Description("Não realizada")]
        NaoRealizado = 2,

        /// <summary>
        /// Solicitação de  cancelamento do pedágio
        /// </summary>
        [EnumMember, Description("Cancelamento solicitado")]
        CancelamentoSolicitado = 3,

        /// <summary>
        /// Confirmação da carga da compra
        /// </summary>
        [EnumMember, Description("Compra confirmada")]
        CompraConfirmada = 4,

        /// <summary>
        /// Confirmação do cancelamento da compra
        /// </summary>
        [EnumMember, Description("Cancelamento confirmado")]
        CancelamentoConfirmado = 5
    }
    
    public static class EResultadoCompraPedagioUtils 
    {
        public static bool IsCreditoConfirmadoCartao(this EResultadoCompraPedagio value)
        {
            return value.In(EResultadoCompraPedagio.CompraConfirmada, EResultadoCompraPedagio.CancelamentoSolicitado);
        }
    }
}

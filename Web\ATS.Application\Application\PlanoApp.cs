﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Data.Repository.EntityFramework;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.DTO.Plano;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace ATS.Application.Application
{
    public class PlanoApp : AppBase, IPlanoApp
    {
        private readonly IPlanoService _service;
        private readonly IUserIdentity _userIdentity;

        public PlanoApp(IPlanoService service, IUserIdentity userIdentity)
        {
            _service = service;
            _userIdentity = userIdentity;
        }


        public List<PlanoEmpresaDto> GetPlanosAtivos()
        {
            return _service.GetPlanosAtivos();
        }

        public List<PlanoEmpresaDto> GetPlanosEmpresa(int idEmpresa)
        {
            return _service.GetPlanosEmpresa(idEmpresa);
        }

        public ValidationResult AddPlanoEmpresa(PlanoEmpresa planoEmpresa)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _service.AddPlanoEmpresa(planoEmpresa);

                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public ValidationResult UpdatePlanoEmpresa(PlanoEmpresa planoEmpresa)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _service.UpdatePlanoEmpresa(planoEmpresa);

                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public PlanoEmpresa GetPlanoEmpresa(int idPlano, int idEmpresa)
        {
            return _service.GetPlanoEmpresa(idPlano, idEmpresa);
        }
    }
}

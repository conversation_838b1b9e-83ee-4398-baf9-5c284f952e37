﻿$ProgressPreference = "SilentlyContinue"

$date = Get-Date -Format "yyyy.MM.dd HH.mm.ss"
$destination = "$BACKUP_FOLDER\$date"
$limit = (Get-Date).AddDays(-$DIAS_PARA_MANTER_BACKUPS)

Write-Host "> Remover backups antigos em '$BACKUP_FOLDER' (Data de modificação < $DIAS_PARA_MANTER_BACKUPS dias | $($limit.ToString('dd/MM/yyyy HH:mm:ss')))"
Get-ChildItem -Path $BACKUP_FOLDER -Recurse -Force | Where-Object { !$_.PSIsContainer -and $_.LastWriteTime -lt $limit } | Remove-Item -Force

Write-Host "> Backup '$APP_PATH' para '$destination'"
Write-Host "COMMIT_SHORT_SHA: $COMMIT_SHORT_SHA"

Copy-Item -Path (Get-Item -Path "$APP_PATH\*" -Exclude ("logs", "Services", "doc")).FullName -Destination "$destination" -Recurse -Force
# Compress-Archive -Path "$destination\*" -DestinationPath "$destination-$COMMIT_SHORT_SHA.zip" -Force -CompressionLevel NoCompression
# Remove-Item -Path $destination -Recurse

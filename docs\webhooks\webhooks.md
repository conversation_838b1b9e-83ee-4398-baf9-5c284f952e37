# Webhooks

Webhooks servem para notificar os clientes near real time (O mais rápido possível) quando uma operação é realizada em nosso sistema.

Os principais benefícios são:

- Redução no tempo de sincronização dos sistemas
- Diminuição do consumo de hardware de nosso servidor, pois o cliente não precisa mais realizar pooling (Consuta recorrente) para descobrir se algo foi realizado na plataforma.

Operações que disparam webhooks:

- Baixa automática de um evento de viagem agendado

## Como testar

### 1. Habilitar webhook no cadastro da apenas (Apenas admin possui acesso)

![01-config-empresa.png](01-config-empresa.png)

Nesta tela ative a opção ***"Habilitar agendamento de pagamento de frete"*** para conseguirmos cadastrar uma viagem que entre na condição de enviar webhook.

A principal configuração agora é no campo ***"Endpoint base para notificação por webhook"***.

Nele deve ser informado uma URL base do cliente destino da notificação, exemplo: https://api.transportadoraXXX.com.br/notificacoes (O cliente deve fornecer isso e criar uma API aberta para receber a notificação).

Quando a plataforma baixar o evento agendado, será concatenado na url base a ação realizada. Enviaremos um POST para https://api.transportadoraXXX.com.br/notificacoes/**baixar-evento-viagem**.

O cliente deve retornar o status code 200 para entendermos que a notificação foi recebida, caso retornar códigos na faixa 4XX e 5XX a mensagem ficará em nossa fila reexecutando a integração a cada minuto, durante 4 horas.

**webhook.site**

Para facilitar o teste técnico afim de descobrir se está funcionando, você pode utilizar a ferramenta [webhook.site](https://webhook.site/).

Ao acessar o site será gerado uma URL temporária que irá logar tudo o que for enviado para a mesma.

Clique em *"Copy to clipboard"* e cole no campo *"Endpoint base para notificação por webhook"*.

![02-webhook-site.png](02-webhook-site.png)

### 2. Integrar viagem com agendamento de pagamento

Pegue qualquer JSON que você já utiliza para integrar viagem e altere os campos:

- ViagemEventos[].Status: Informar 5 (Status para agendado)
- ViagemEventos[].DataAgendamentoPagamento: Informe a data que será realizado o pagamento (Obrigatório ser maior que a atual)
- ViagemEventos[].HabilitarPagamentoCartao: Obrigatório true

Confira a viagem na plataforma:

![03-consulta-viagem.png](03-consulta-viagem.png)

Confira o status agendado e a data programada:

![04-consulta-pagamentos.png](04-consulta-pagamentos.png)

### 3. Manipular banco de dados para forçar pagamento agora

Para agilizar o teste, vamos manipular o banco de dados e diminuir a data agendada.

```sql
-- Alterar a data de agendamento para ontem
update VIAGEM_EVENTO set dataagendamentopagamento = dateadd(day, -1, cast(getdate() as date)) where idviagem = 629880 and status = 5;

-- Diminuir 10 minutos na data de cadastro da viagem para que a mesma entre na consulta de pendências
update VIAGEM set datalancamento = dateadd(minute, -10, getdate()) where idviagem = 629880;
```

### 4. Conferir webhook.site

Em homologação, o serviço está executando a cada minuto (sempre no segundo 0).

Novamente ao [webhook.site](https://webhook.site) você deverá ver na lista a esquerda todas as notificações enviadas pela plataforma e ao clicar nela, visualizará o payload enviado.

![05-webhook-site-notificacao.png](05-webhook-site-notificacao.png)

**Payload notificado**

```json
{
  "IdViagem": 629880,
  "IdViagemEvento": 47851,
  "IdProtocolo": null,
  "IdEstabelecimentoBase": null,
  "TipoEvento": 0,
  "ValorPagamento": 1.1,
  "ValorTotalPagamento": 1.1,
  "ValorBruto": 1.15,
  "ValorQuebraAbonada": null,
  "HabilitarPagamentoCartao": true,
  "DataPagamento": "2023-03-23 22:03:02",
  "HoraPagamento": "22:03:02",
  "DataValidade": null,
  "NumeroRecibo": null,
  "NumeroControle": null,
  "Instrucao": "gg",
  "Token": "0016796190768409",
  "Status": 2,
  "PesoChegada": null,
  "PesoDiferenca": null,
  "ValorDifFreteMotorista": null,
  "ValorQuebraMercadoria": null,
  "ViagemDocumentos": null,
  "ViagemOutrosDescontos": null,
  "ViagemOutrosAcrescimos": null,
  "SESTSENAT": 0,
  "IRRPF": 0,
  "INSS": 0,
  "OrigemPagamento": 0,
  "DataDescarga": null
}
```
</details>

### 5. Monitoramento

No [painel de infra estrutura](http://apiho.extratta.com.br:1000/Infra/App/) também é possível acompanhar as webhook enviadas.

![06-painel-infra.png](06-painel-infra.png)

A coluna status indica o retorno do cliente, qualquer código fora da faixa 2XX significa que a mesma não foi entregue (Durante 4 horas é tentado o reenvio automátcio a cada minuto).

Ao clicar na notificação é possível ver os paylods e na aba "Resposta" é possível forçar um reenvio.

![07-painel-infra-reenvio.png](07-painel-infra-reenvio.png)

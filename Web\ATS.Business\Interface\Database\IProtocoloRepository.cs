﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Database
{
    public interface IProtocoloRepository : IRepository<Protocolo>
    {
        IQueryable<Protocolo> Consultar(int idEmpresa, bool includeEstabelecimentoBase = false);
        IQueryable<Protocolo> ConsultarProtocolosPorEstabelecimentos(List<int> idsEstabelecimentoBase, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal);
    }
}
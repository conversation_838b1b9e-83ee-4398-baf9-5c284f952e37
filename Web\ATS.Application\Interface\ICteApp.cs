﻿using System;
using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ICteApp : IAppBase<Cte>
    {
        IQueryable<Cte> Consultar(string cpfMotorista, DateTime dataInicial, DateTime? dataFinal);
        int GetTotalCtes(string cPFCNPJUsuario);
        Cte ConsultaPorId(int idCte);
        ValidationResult Update(Cte cte);
        ValidationResult Add(Cte cte);
    }
}

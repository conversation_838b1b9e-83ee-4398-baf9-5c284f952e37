﻿using System;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Application.Application;
using ATS.Domain.DTO;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.Common.Request;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Interface
{
    public interface IViagemApp : IBaseApp<IViagemService>
    {
        Viagem Get(int idViagem, bool comIncludes = true);
        Viagem GetComViagemEstabelecimentos(int idViagem);
        IEnumerable<Viagem> GetTodasViagens(ETipoBuscaViagens tpBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta);
        IEnumerable<Viagem> GetViagensFinalizadasEntre(ETipoBuscaViagens tipoBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta);
        IEnumerable<Viagem> GetViagensEmViagem(int idEmpresa);
        ValidationResult Add(Viagem viagem,bool IsPedagioAvulso = false);
        ValidationResult IntegrarCheck(int idViagem, int idEmpresa, ViagemCheck check, CheckIn checkIn);
        ViagemCheck GetUltimoViagemCheck(int idViagem);
        Viagem GetViagemAbertaPlaca(string placa);
        Viagem GetUltimaViagemFechadaPlaca(string placa);
        IEnumerable<Viagem> GetViagensEmViagemPorCPF(string nCPFMotorista);
        List<string> GetPlacas(int idEmpresa);
        void AjustarProprietario(Viagem viagem);
        IQueryable<Viagem> Find(Expression<Func<Viagem, bool>> predicate, bool @readonly = false);
        bool ViagemCadastrada(int idEmpresa, string numeroControle);
        ValidationResult BloqueioProcessoViagem(List<EBloqueioGestorTipo> idsTipoBloqueio, Viagem viagem, decimal valorTotalPedagio,EBloqueioOrigemTipo origem,decimal valorTotalFrete);
        ValidationResult AlterarViagemPendente(int idViagem, EBloqueioGestorTipo idBloqueioGestorTipo, int status, int idUsuario, string motivo);
        object ConsultarViagens(ConsultaViagemRequestDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        byte[] RelatorioConsultaPedagioVPO(RelatorioConsultaViagemRequestDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters, string extensao);
        byte[] RelatorioConsultaViagens(RelatorioConsultaViagemRequestDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters, string extensao);
        ConsultaViagemExternalCiotResponseDTO ConsultarDetalhesCiotViagem(int idViagem);
        ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(string cnpjCpf, string rntrc, int idEmpresa);
        object GetEventosViagem(string CPFCNPJProprietario, string CPFMotorista, List<EStatusViagemEvento> statusEvento = null, List<ETipoEventoViagem> tipoEvento = null, DateTime? dataInicio = null, DateTime? dataFim = null);
        bool PossuiResgateDeSaldoResidualSolicitadoNoDia(string documento, DateTime dia);
        ValidationResult RemoverPlacasDasCarretas(int idViagem, List<int> idViagemCarretaRemovidaList = null);
        void RemoverViagemEstabelecimentos(int idViagem, List<int> idViagemEstabelecimentoRemovidoList = null);
        ValidationResult IsValidToCrud(Viagem viagem, EProcesso processo);
        ValidationResult Update(Viagem viagem);
        IQueryable<Viagem> Consultar(string nCpfMotorista, string tokenViagem, DateTime? dataLancamentoInicial, DateTime? dataLancamentoFinal, List<EStatusCheckViagem> statusCheckViagem, List<EStatusViagem> statusViagem, string token, string cnpjAplicacao, List<int> IdsViagem,List<string> NumerosControle);
        SolicitarCompraPedagioResponseDTO GetStatusPedagio(Viagem viagem);
        IList<ViagemEvento> GetEventos(int idViagem);
        string GerarTokenViagemEvento();
        List<Viagem> GetViagensPorPeriodo(int idEmpresa, DateTime dataInicial, DateTime dataFinal);
        object GetHistoricoViagem(string placa, int idEmpresa, int skip);
        List<FrotaUtilizadaModel> GetUltimaViagemFrota(List<string> placa, int idEmpresa);
        IQueryable<ViagemPendenteGestor> ConsultaViagensPendentes(int? idEmpresa);
        ConsultaPagamentosModel GetPagamentosPorViagem(int idEmpresa, DateTime dataInicial, DateTime dataFinal,
            int take, int page, OrderFilters order = null, List<QueryFilters> filters = null);
        ConsultaPagamentosModel GetRelatorioPagamentosPorViagem(int idEmpresa, DateTime dataInicio, DateTime dataFim);
        byte[] GerarRelatorioPagamentos(int idEmpresa, DateTime dataInicial, DateTime dataFinal, ETipoArquivoPagamento? tipoRelatorio);

        object GetCartasPagas(int? idEmpresa, int ano, int mes);
        object GetCartasEmAberto(int? idEmpresa, int ano, int mes);
        object GetTotalCartasMes(int? idEmpresa, int ano);
        object GetTotalGanhosPorMes(int? idEmpresa, int ano);
        object GetTotalPagamentos(int? idEmpresa, int ano, int mes);
        List<Viagem> GetByCte(string numero);
        object ConsultarDocumentos(int idViagem);
        IEnumerable<ViagemMotoristasModel> GetMotoristasComViagensDoProprietario(string cnpjProprietario,
            DateTime? dataCadastroBase = null);

        bool ExisteCartaFreteParaCombinacao(string cpfCnpjProprietario, string cpfMotorista, int idEmpresa, int mesesAnteriores);
        ViagemEvento ConsultarViagemEvento(string token);
        ViagemCalcularValorPedagioResponse CalcularValorPedagio(ViagemCalcularValorPedagioRequest request);
        ViagemConsultarResponse ConsultarViagemV2(int idViagem, int? idEmpresa, int idUsuario, bool filtrarVeiculoTerceiro = false);
        ViagemConsultarResponse ConsultarViagemV3(int idViagem, bool filtrarVeiculoTerceiro = false);
        IQueryable<Viagem> GetQuery();
        IQueryable<ViagemEvento> GetEvento(int idviagem, int idviagemEvento);
        ValidationResult AdicionarAtualizarViagemRota(int idViagem, int idEmpresa, List<LocalizacaoDTO> localizacoes, 
            Guid? identificadorHistorico, FornecedorEnum? fornecedor, ETipoVeiculoPedagioEnum? tipoVeiculo, 
            int? quantidadeEixos, string documentoUsuarioAudit, string nomeUsuarioAudit);
        ValidationResult AdicionarAtualizarViagemRota(ViagemRota viagemRota);
        ViagemDadosCiotResponse GetDadosCiot(int idviagem);
        ComprovanteValePedagioResponse GetDadosValePedagio(int idviagem, out ViagemDadosValePedagio dadosValePedagio, string CnpjEmpresa = null);
        byte[] GetDadosValePedagioReport(ComprovanteValePedagioResponse dadosComprovante, FornecedorEnum fornecedor);
        ConsultarEventoResponseModel ConsultarDetalheEvento(ConsultarEventoRequestModel request);
        Viagem ObterViagemPorEmpresa(int viagemId, int empresaId);
        ConsultaTiposCargasResponse ConsultarTiposCarga(string cnpjEmpresa);
        ConsultarTiposCargaResponse ConsultarTiposCarga(int idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        int? GetIdViagemEstabelecimentoPorCnpj(string cnpj, string cnpjEmpresa);
        bool GetViagemEstabelecimentoAtivo(int idEstabelecimento);
        bool GetViagemEstabelecimentoCredenciado(int idEstabelecimento);
        int QuantidadeViagensAbertasPorNumeroCiotsVinculados(int idDclaracaociot);
        FornecedorEnum? GetFornecedorViagemRota(int idViagem);
        CompraPedagioDTOResponse ConsultarReciboPedagio(int idViagem, string cnpjEmpresa);
        byte[] ConsultarReciboMoedeiro(int idViagem, int idEmpresa);
        ValidationResult DesvincularCiot(int? idViagem, int? idUsuario);
        ConsultarFilialEDadosPagamentoResponse ConsultarFilialEDadosPagamento(string ciot);
        object ConsultarUltimaViagemEmpresa(int empresaId, int take, int page, OrderFilters order, List<QueryFilters> filters);
        bool VincularViagemAoContrato(int idContratoCiotAgregado, int idUltimaViagem);
        ReciboPefDadosResponse GetDadosReciboPef(int idviagem, bool listarParcelasCanceladas, string CnpjEmpresa = null);
        byte[] GetDadosReciboPefReport(ReciboPefDadosResponse dadosRecibo);
        bool PertenceAEmpresa(int? idempresa, int idviagem);
        bool VerificarPermissaoPix(int idempresa, int idProprietario);
    }
}
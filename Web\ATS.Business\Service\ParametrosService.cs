using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.AtendimentoPortador;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Service
{
    #region Separacao dos Services entre as tabelas (Apenas para organizacao de codigo)

    public class ParametrosGenericoService : BaseService<IParametrosRepository>, IParametrosGenericoService
    {
        public ParametrosGenericoService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public ValidationResult SetParametro<T>(GLOBAL parametro, T valor)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(GLOBAL), tipoEnum, null, null);
        }

        public T GetParametro<T>(GLOBAL parametro, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(GLOBAL), tipoEnum, null, null);

            return (T) retorno;
        }

        public Dictionary<GLOBAL, T> GetParametros<T>(IList<GLOBAL> parametros)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var retornos = Repository.GetValues<T>(nameof(GLOBAL), chaves, null, null);

            var resposta = new Dictionary<GLOBAL, T>();

            foreach (var retorno in retornos)
            {
                System.Enum.TryParse(retorno.Key, out GLOBAL myenum);
                resposta.Add(myenum, (T) retorno.Value);
            }

            return resposta;
        }
    }

    public class ParametrosProprietarioService :  BaseService<IParametrosRepository>, IParametrosProprietarioService
    {
        public ParametrosProprietarioService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public ValidationResult SetParametro<T>(PROPRIETARIO_DOCUMENTO parametro, T valor, string idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(PROPRIETARIO_DOCUMENTO), tipoEnum, idregistro, null);
        }

        public ValidationResult SetParametro<T>(PROPRIETARIO_DOCUMENTO parametro, T valor, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(PROPRIETARIO_DOCUMENTO), tipoEnum, idregistro, null);
        }

        public T GetParametro<T>(PROPRIETARIO_DOCUMENTO parametro, string documento)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(PROPRIETARIO_DOCUMENTO), tipoEnum, documento, null);

            return (T) retorno;
        }

        public Dictionary<PROPRIETARIO_DOCUMENTO, T> GetParametros<T>(IList<PROPRIETARIO_DOCUMENTO> parametros, string idregistro)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var retornos = Repository.GetValues<T>(nameof(PROPRIETARIO_DOCUMENTO), chaves, idregistro, null);

            var resposta = new Dictionary<PROPRIETARIO_DOCUMENTO, T>();

            foreach (var retorno in retornos)
                if (retorno.Value != null)
                {
                    System.Enum.TryParse(retorno.Key, out PROPRIETARIO_DOCUMENTO myenum);
                    if (typeof(T).IsEnum)
                    {
                        if (retorno.Value is decimal)
                        {
                            var enumValue = (T) System.Enum.Parse(typeof(T),
                                Convert.ToInt32(retorno.Value).ToString());
                            resposta.Add(myenum, enumValue);
                        }
                        else
                        {
                            resposta.Add(myenum, (T) retorno.Value);
                        }
                    }
                    else
                    {
                        resposta.Add(myenum, (T) retorno.Value);
                    }
                }

            return resposta;
        }

        public bool AnyParametro(IEnumerable<PROPRIETARIO_DOCUMENTO> parametros, string idregistro)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var anyParametro = Repository.AnyParametro(nameof(PROPRIETARIO_DOCUMENTO), chaves.FirstOrDefault(), idregistro, null);

            return anyParametro;
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(string documento)
        {
            return GetParametro<AcaoSaldoResidualNovoCreditoCartaoPedagio>(
                PROPRIETARIO_DOCUMENTO.AcaoSaldoResidualNovoCreditoCartaoPedagio, documento);
        }
        
        public bool GetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa)
        {
            return (bool?) Repository.GetValue<bool?>(nameof(PROPRIETARIO_DOCUMENTO), 
                PROPRIETARIO_DOCUMENTO.PermiteReceberPagamentoPix.ToString("G"), documento, idEmpresa) ?? false;
        }

        public ValidationResult SetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa, bool valor)
        {
            return Repository.SetValue(valor, nameof(PROPRIETARIO_DOCUMENTO), 
                PROPRIETARIO_DOCUMENTO.PermiteReceberPagamentoPix.ToString("G"), documento, idEmpresa);
        }
    }

    public class ParametrosProprietarioMotoristaService : BaseService<IParametrosRepository>, IParametrosProprietarioMotoristaService
    {
        public ParametrosProprietarioMotoristaService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public ValidationResult SetParametro<T>(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, T valor, string idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(PROPRIETARIO_MOTORISTA_DOCUMENTO), tipoEnum, idregistro, null);
        }

        public ValidationResult SetParametro<T>(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, T valor, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(PROPRIETARIO_MOTORISTA_DOCUMENTO), tipoEnum, idregistro, null);
        }

        public T GetParametro<T>(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, string documento)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(PROPRIETARIO_MOTORISTA_DOCUMENTO), tipoEnum, documento, null);

            return (T)retorno;
        }

        public Dictionary<PROPRIETARIO_MOTORISTA_DOCUMENTO, T> GetParametros<T>(IList<PROPRIETARIO_MOTORISTA_DOCUMENTO> parametros, string idregistro)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var retornos = Repository.GetValues<T>(nameof(PROPRIETARIO_MOTORISTA_DOCUMENTO), chaves, idregistro, null);

            var resposta = new Dictionary<PROPRIETARIO_MOTORISTA_DOCUMENTO, T>();

            foreach (var retorno in retornos)
                if (retorno.Value != null)
                {
                    System.Enum.TryParse(retorno.Key, out PROPRIETARIO_MOTORISTA_DOCUMENTO myenum);
                    if (typeof(T).IsEnum)
                    {
                        if (retorno.Value is decimal)
                        {
                            var enumValue = (T)System.Enum.Parse(typeof(T),
                                Convert.ToInt32(retorno.Value).ToString());
                            resposta.Add(myenum, enumValue);
                        }
                        else
                        {
                            resposta.Add(myenum, (T)retorno.Value);
                        }
                    }
                    else
                    {
                        resposta.Add(myenum, (T)retorno.Value);
                    }
                }

            return resposta;
        }

        public bool AnyParametro(IEnumerable<PROPRIETARIO_MOTORISTA_DOCUMENTO> parametros, string idregistro)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var anyParametro = Repository.AnyParametro(nameof(PROPRIETARIO_DOCUMENTO), chaves.FirstOrDefault(), idregistro, null);

            return anyParametro;
        }
    }

    public class ParametrosEmpresaService : BaseService<IParametrosRepository>, IParametrosEmpresaService
    {
        public ParametrosEmpresaService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public ValidationResult SetParametro<T>(EMPRESA_ID parametro, T valor, string idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(EMPRESA_ID), tipoEnum, idregistro, null);
        }

        public ValidationResult SetParametro<T>(EMPRESA_ID parametro, T valor, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(EMPRESA_ID), tipoEnum, idregistro, null);
        }

        public T GetParametro<T>(EMPRESA_ID parametro, int idEmpresa)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(EMPRESA_ID), tipoEnum, idEmpresa, null);

            return (T) retorno;
        }

        public Dictionary<EMPRESA_ID, T> GetParametros<T>(IList<EMPRESA_ID> parametros, int idEmpresa)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var retornos = Repository.GetValues<T>(nameof(EMPRESA_ID), chaves, idEmpresa, null);

            var resposta = new Dictionary<EMPRESA_ID, T>();

            foreach (var retorno in retornos)
            {
                System.Enum.TryParse(retorno.Key, out EMPRESA_ID myenum);
                if (typeof(T).IsEnum)
                {
                    if (retorno.Value is decimal)
                    {
                        var enumValue = (T) System.Enum.Parse(typeof(T), Convert.ToInt32(retorno.Value).ToString());
                        resposta.Add(myenum, enumValue);
                    }
                    else
                    {
                        resposta.Add(myenum, (T) retorno.Value);
                    }
                }
                else
                {
                    resposta.Add(myenum, (T) retorno.Value);
                }
            }

            return resposta;
        }

        public bool AnyParametro(IEnumerable<EMPRESA_ID> parametros, int idEmpresa)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var anyParametro = Repository.AnyParametro(nameof(EMPRESA_ID), chaves.FirstOrDefault(), idEmpresa, null);

            return anyParametro;
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(int idEmpresa)
        {
            return GetParametro<AcaoSaldoResidualNovoCreditoCartaoPedagio>(
                EMPRESA_ID.AcaoSaldoResidualNovoCreditoCartaoPedagio, idEmpresa);
        }

        public decimal? GetNumeroDeRegistrosDeCargaAvulsaParaProcessar(int idEmpresa)
        {
            var resultado = GetParametro<decimal?>(EMPRESA_ID.NumeroDeRegistrosDeCargaAvulsaParaProcessar, idEmpresa);
            return resultado;
        }

        public PermissoesEmpresaAtendimentoPortador GetPermissoesAtendimentoCartao(int idEmpresa)
        {
            var parametrosEnum = new List<EMPRESA_ID>
            {
                EMPRESA_ID.AtendimentoPermiteBloquearCartao,
                EMPRESA_ID.AtendimentoPermiteDesbloquearCartao,
                EMPRESA_ID.AtendimentoPermiteAlterarSenhaCartao,
                EMPRESA_ID.AtendimentoPermiteRealizarTransferenciaBancaria,
                EMPRESA_ID.AtendimentoPermiteRealizarTransferenciaCartoes,
                EMPRESA_ID.AtendimentoPermiteRealizarResgate,
                EMPRESA_ID.AtendimentoPermiteRealizarEstornoResgate,
            };

            var parametros = GetParametros<bool?>(parametrosEnum, idEmpresa);

            if (parametros == null || !parametros.Any())
                return new PermissoesEmpresaAtendimentoPortador();

            return new PermissoesEmpresaAtendimentoPortador
            {
                BloquearCartao = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteBloquearCartao).Select(x => x.Value).FirstOrDefault().ToBoolSafe(),
                DesbloquearCartao = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteDesbloquearCartao).Select(x => x.Value).FirstOrDefault().ToBoolSafe(),
                AlterarSenhaCartao = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteAlterarSenhaCartao).Select(x => x.Value).FirstOrDefault().ToBoolSafe(),
                TransferenciaBancaria = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteRealizarTransferenciaBancaria).Select(x => x.Value).FirstOrDefault().ToBoolSafe(),
                TransferenciaCartoes = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteRealizarTransferenciaCartoes).Select(x => x.Value).FirstOrDefault().ToBoolSafe(),
                Resgate = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteRealizarResgate).Select(x => x.Value).FirstOrDefault().ToBoolSafe(),
                EstornoResgate = parametros.Where(x => x.Key == EMPRESA_ID.AtendimentoPermiteRealizarEstornoResgate).Select(x => x.Value).FirstOrDefault().ToBoolSafe()
            };
        }

        public ValidationResult SetPermissoesAtendimentoCartao(int idEmpresa, PermissoesEmpresaAtendimentoPortador permissoes)
        {
            var validationResult = new ValidationResult();

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteBloquearCartao, permissoes.BloquearCartao, idEmpresa));
            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteDesbloquearCartao, permissoes.DesbloquearCartao, idEmpresa));
            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteAlterarSenhaCartao, permissoes.AlterarSenhaCartao, idEmpresa));
            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteRealizarTransferenciaBancaria, permissoes.TransferenciaBancaria, idEmpresa));
            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteRealizarTransferenciaCartoes, permissoes.TransferenciaCartoes, idEmpresa));
            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteRealizarResgate, permissoes.Resgate, idEmpresa));
            if (!validationResult.IsValid)
                return validationResult;

            validationResult.Add(SetParametro(EMPRESA_ID.AtendimentoPermiteRealizarEstornoResgate, permissoes.EstornoResgate, idEmpresa));
            return validationResult;
        }

        public string GetTokenMicroServicoCentralAtendimento(int idEmpresa)
        {
            return GetParametro<string>(EMPRESA_ID.TokenMicroServicoCentralAtendimento, idEmpresa);
        }

        public ValidationResult SetTokenMicroServicoCentralAtendimento(int idEmpresa, string token)
        {
            return SetParametro(EMPRESA_ID.TokenMicroServicoCentralAtendimento, token, idEmpresa);
        }

        public bool GetOcultarListagemTerceiros(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.OcultarListagemTerceiros, idEmpresa) ?? false;
        }

        public bool CadastraSomentePerfilEmpresa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.CadastraSomentePerfilEmpresa, idEmpresa) ?? false;
        }

        public bool GetRegistrarValePedagio(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.RegistrarValePedagio, idEmpresa) ?? true;
        }

        public ValidationResult SetRegistrarValePedagio(int idEmpresa, bool enviar)
        {
            return SetParametro(EMPRESA_ID.RegistrarValePedagio, enviar, idEmpresa);
        }

        public bool GetRealizaTriagemEstabelecimentoInterno(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.RealizaTriagemEstabelecimentoInterno, idEmpresa) ?? true;
        }

        public ValidationResult SetRealizaTriagemEstabelecimentoInterno(int idEmpresa, bool? realizaTriagemEstabelecimentoInterno)
        {
            return SetParametro(EMPRESA_ID.RealizaTriagemEstabelecimentoInterno, realizaTriagemEstabelecimentoInterno ?? true, idEmpresa);
        }

        public bool? GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.MantemViagemAbertaAposCancelamentoDoUltimoEvento, idEmpresa) ?? false;
        }

        public ValidationResult SetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.PermiteCadastrarMotoristaComCpfFicticio, parametro, idEmpresa);
        }

        public bool? GetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.PermiteCadastrarMotoristaComCpfFicticio, idEmpresa) ?? true;
        }

        public ValidationResult SetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.PermiteCadastrarProprietarioComCpfFicticio, parametro, idEmpresa);
        }

        public bool? GetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.PermiteCadastrarProprietarioComCpfFicticio, idEmpresa) ?? true;
        }

        public ValidationResult SetPermiteVincularCartaoComCpfFicticio(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.PermiteVincularCartaoComCpfFicticio, parametro, idEmpresa);
        }

        public bool? GetPermiteVincularCartaoComCpfFicticio(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.PermiteVincularCartaoComCpfFicticio, idEmpresa) ?? false;
        }

        public ValidationResult SetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.MantemViagemAbertaAposCancelamentoDoUltimoEvento, parametro, idEmpresa);
        }

        public int GetMotivoPadraoBloqueioCartaoEmpresa()
        {
             var parametro = GetParametro<decimal?>(EMPRESA_ID.MotivoPadraoBloqueioCartaoParametroEmpresa, 1);

             return parametro.ToIntNullable() ?? 1;
        }
        
        public decimal GetValorMinimoAlertaSaldoContaFrete(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.ValorMinimoAlertaEmailContaFrete, idEmpresa) ?? 0;
        }

        public decimal GetValorMinimoAlertaSaldoContaPix(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.ValorMinimoAlertaEmailContaPix, idEmpresa) ?? 0;
        }
        public ValidationResult SetValorMinimoAlertaSaldoContaFrete(int idEmpresa, decimal? valor)
        {
            return SetParametro(EMPRESA_ID.ValorMinimoAlertaEmailContaFrete, valor, idEmpresa);
        }
        
        public ValidationResult SetValorMinimoAlertaSaldoContaPix(int idEmpresa, decimal? valor)
        {
            return SetParametro(EMPRESA_ID.ValorMinimoAlertaEmailContaPix, valor, idEmpresa);
        }

        public decimal GetTagExtrattaTaxaVpo(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaTaxaVpo, idEmpresa) ?? 0m;
        }

        public ValidationResult SetTagExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaTaxaVpo, parametro, idEmpresa);
        }

        public decimal GetTagExtrattaValorTag(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaValorTag, idEmpresa) ?? 0m;
        }

        public ValidationResult SetTagExtrattaValorTag(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaValorTag, parametro, idEmpresa);
        }

        public decimal GetTagExtrattaValorMensalidade(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaValorMensalidade, idEmpresa) ?? 0m;
        }

        public ValidationResult SetTagExtrattaValorMensalidade(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaValorMensalidade, parametro, idEmpresa);
        }

        public bool GetTagExtrattaProvisionarValor(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaProvisionarValor, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaProvisionarValor(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaProvisionarValor, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaProvisionarTaxa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaProvisionarTaxa, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaProvisionarTaxa, parametro, idEmpresa);
        }
        
        public bool GetHubMoveMaisProvisionarValor(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubMoveMaisProvisionarValor, idEmpresa) ?? true;
        }

        public ValidationResult SetHubMoveMaisProvisionarValor(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubMoveMaisProvisionarValor, parametro, idEmpresa);
        }
        
        public bool GetHubMoveMaisProvisionarTaxa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubMoveMaisProvisionarTaxa, idEmpresa) ?? true;
        }

        public ValidationResult SetHubMoveMaisProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubMoveMaisProvisionarTaxa, parametro, idEmpresa);
        }
        
        public bool GetHubConectCarProvisionarValor(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubConectCarProvisionarValor, idEmpresa) ?? true;
        }

        public ValidationResult SetHubConectCarProvisionarValor(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubConectCarProvisionarValor, parametro, idEmpresa);
        }
        
        public bool GetHubConectCarProvisionarTaxa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubConectCarProvisionarTaxa, idEmpresa) ?? true;
        }

        public ValidationResult SetHubConectCarProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubConectCarProvisionarTaxa, parametro, idEmpresa);
        }
        
        public bool GetHubViaFacilProvisionarValor(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubViaFacilProvisionarValor, idEmpresa) ?? true;
        }

        public ValidationResult SetHubViaFacilProvisionarValor(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubViaFacilProvisionarValor, parametro, idEmpresa);
        }
        
        public bool GetHubViaFacilProvisionarTaxa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubViaFacilProvisionarTaxa, idEmpresa) ?? true;
        }

        public ValidationResult SetHubViaFacilProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubViaFacilProvisionarTaxa, parametro, idEmpresa);
        }
        
        public bool GetHubVeloeProvisionarValor(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubVeloeProvisionarValor, idEmpresa) ?? true;
        }

        public ValidationResult SetHubVeloeProvisionarValor(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubVeloeProvisionarValor, parametro, idEmpresa);
        }
        
        public bool GetHubVeloeProvisionarTaxa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubVeloeProvisionarTaxa, idEmpresa) ?? true;
        }

        public ValidationResult SetHubVeloeProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubVeloeProvisionarTaxa, parametro, idEmpresa);
        }
        
        public decimal GetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaSaldoMinimoContaFreteWebhook, idEmpresa) ?? 0;
        }

        public ValidationResult SetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaSaldoMinimoContaFreteWebhook, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaProvisionarValorPedagioWebhook, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaProvisionarValorPedagioWebhook, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaEstornarValorPedagioWebhook(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaEstornarPedagio, idEmpresa) ?? true;
        }

        public ValidationResult SetTagExtrattaEstornarValorPedagioWebhook(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaEstornarPedagio, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaProvisionarTaxaPedagioWebhook, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaProvisionarTaxaPedagioWebhook, parametro, idEmpresa);
        }
        
        public decimal GetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaFaixaToleranciaNotificacaoEmail, idEmpresa) ?? 0;
        }

        public ValidationResult SetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaFaixaToleranciaNotificacaoEmail, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaBloquearTagUnitaria, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaBloquearTagUnitaria, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaBloquearTagLoteWebhook(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaBloquearTagLote, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaBloquearTagLoteWebhook(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaBloquearTagLote, parametro, idEmpresa);
        }
        
        public decimal GetTagExtrattaValorSubstituicao(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaValorSubstituicao, idEmpresa) ?? 0;
        }

        public ValidationResult SetTagExtrattaValorSubstituicao(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaValorSubstituicao, parametro, idEmpresa);
        }
        
        public decimal GetTagExtrattaValorRecargaConta(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TagExtrattaValorRecargaConta, idEmpresa) ?? 0;
        }

        public ValidationResult SetTagExtrattaValorRecargaConta(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaValorRecargaConta, parametro, idEmpresa);
        }
        
        public decimal GetMoveMaisExtrattaTaxaVpo(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.MoveMaisExtrattaTaxaVpo, idEmpresa) ?? 0.2m;
        }

        public ValidationResult SetMoveMaisExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.MoveMaisExtrattaTaxaVpo, parametro, idEmpresa);
        }
        
        public bool GetTagExtrattaUtilizaTaxaPedagio(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.TagExtrattaUtilizaTaxaPedagio, idEmpresa) ?? false;
        }

        public ValidationResult SetTagExtrattaUtilizaTaxaPedagio(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.TagExtrattaUtilizaTaxaPedagio, parametro, idEmpresa);
        }
        
        public decimal GetViaFacilExtrattaTaxaVpo(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.ViaFacilExtrattaTaxaVpo, idEmpresa) ?? 0.9m;
        }

        public ValidationResult SetViaFacilExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.ViaFacilExtrattaTaxaVpo, parametro, idEmpresa);
        }

        public decimal GetVeloeExtrattaTaxaVpo(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.VeloeExtrattaTaxaVpo, idEmpresa) ?? 0.75m;
        }

        public ValidationResult SetVeloeExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.VeloeExtrattaTaxaVpo, parametro, idEmpresa);
        }
        
        public bool GetPermiteRealizarPagamentoPix(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.PermiteRealizarPagamentoPix, idEmpresa) ?? false;
        }

        public ValidationResult SetPermiteRealizarPagamentoPix(int idEmpresa, bool parametro)
        {
            return SetParametro(EMPRESA_ID.PermiteRealizarPagamentoPix, parametro, idEmpresa);
        }
        

        public decimal GetConectCarExtrattaTaxaVpo(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.ConectCarExtrattaTaxaVpo, idEmpresa) ?? 0.8m;
        }

        public ValidationResult SetConectCarExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.ConectCarExtrattaTaxaVpo, parametro, idEmpresa);
        }

        public bool GetUtilizaRelatoriosOfx(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.UtilizaRelatoriosOfx, idEmpresa) ?? false;
        }

        public bool GetUtilizaRoteirizacaoPorPolyline(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.UtilizaRoteirizacaoPorPolyline, idEmpresa) ?? false;
        }
        
        public ValidationResult SetUtilizaRelatoriosOfx(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.UtilizaRelatoriosOfx, parametro, idEmpresa);
        }
        
        public ValidationResult SetUtilizaUtilizaRoteirizacaoPorPolyline(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.UtilizaRoteirizacaoPorPolyline, parametro, idEmpresa);
        }
        
        public bool GetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.SolicitaAprovacaoGestorCargaAvulsaIntegracao, idEmpresa) ?? false;
        }

        public ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.SolicitaAprovacaoGestorCargaAvulsaIntegracao, parametro, idEmpresa);
        }

        public bool GetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.SolicitaAprovacaoGestorCargaAvulsaUnitario, idEmpresa) ?? false;
        }

        public ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.SolicitaAprovacaoGestorCargaAvulsaUnitario, parametro, idEmpresa);
        }

        public bool GetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.SolicitaAprovacaoGestorCargaAvulsaLote, idEmpresa) ?? false;
        }

        public ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.SolicitaAprovacaoGestorCargaAvulsaLote, parametro, idEmpresa);
        }

        public bool GetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.MantemViagemAbertaAposBaixaDoUltimoEvento, idEmpresa) ?? false;
        }

        public ValidationResult SetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.MantemViagemAbertaAposBaixaDoUltimoEvento, parametro, idEmpresa);
        }

        public ValidationResult SetDefaultIntegracaoTipoRodagemDupla(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.DefaultIntegracaoTipoRodagemDupla, parametro, idEmpresa);
        }

        public bool GetDefaultIntegracaoTipoRodagemDupla(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.DefaultIntegracaoTipoRodagemDupla, idEmpresa) ?? false;
        }

        public bool GetSolicitarSenhaTransacionalPix(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.SolicitarSenhaTransacionalPix, idEmpresa) ?? false;
        }

        public ValidationResult SetSolicitarSenhaTransacionalPix(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.SolicitarSenhaTransacionalPix, parametro, idEmpresa);
        }

        public bool GetAprovarSolicitacoesChavePixAutomaticamente(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.AprovarSolicitacoesChavePixAutomaticamente, idEmpresa) ?? false;
        }
        
        public bool GetHubTaggyEdenredProvisionarValor(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubTaggyEdenredProvisionarValor, idEmpresa) ?? true;
        }

        public ValidationResult SetHubTaggyEdenredProvisionarValor(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubTaggyEdenredProvisionarValor, parametro, idEmpresa);
        }
        
        public bool GetHubTaggyEdenredProvisionarTaxa(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.HubTaggyEdenredProvisionarTaxa, idEmpresa) ?? true;
        }

        public ValidationResult SetHubTaggyEdenredProvisionarTaxa(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.HubTaggyEdenredProvisionarTaxa, parametro, idEmpresa);
        }
        
        public decimal GetTaggyEdenredExtrattaTaxaVpo(int idEmpresa)
        {
            return GetParametro<decimal?>(EMPRESA_ID.TaggyEdenredExtrattaTaxaVpo, idEmpresa) ?? 0.3m;
        }

        public ValidationResult SetTaggyEdenredExtrattaTaxaVpo(int idEmpresa, decimal? parametro)
        {
            return SetParametro(EMPRESA_ID.TaggyEdenredExtrattaTaxaVpo, parametro, idEmpresa);
        }
        
        public bool GetBloqueiaCargaAvulsaDuplicada(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.BloqueiaCargaAvulsaDuplicada, idEmpresa) ?? false;
        }

        public ValidationResult SetBloqueiaCargaAvulsaDuplicada(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.BloqueiaCargaAvulsaDuplicada, parametro, idEmpresa);
        }
        
        public decimal GetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa)
        {
            var parametro = GetParametro<decimal?>(EMPRESA_ID.HorasBloqueioCargaAvulsaDuplicada, idEmpresa) ?? 24;
            if (parametro == 0) parametro = 24;
            return parametro;
        }

        public ValidationResult SetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa, decimal? parametro)
        {
            if (parametro == null) return new ValidationResult();
            if (parametro == 0) parametro = 24;
            return SetParametro(EMPRESA_ID.HorasBloqueioCargaAvulsaDuplicada, parametro, idEmpresa);
        }

        public ValidationResult SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe, parametro, idEmpresa);
        }

        public bool GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe, idEmpresa) ?? false;
        }

        public bool GetNaoBaixarParcelasDeposito(int idEmpresa)
        {
            return GetParametro<bool?>(EMPRESA_ID.NaoBaixarParcelasDeposito, idEmpresa) ?? false;
        }

        public ValidationResult SetNaoBaixarParcelasDeposito(int idEmpresa, bool? parametro)
        {
            return SetParametro(EMPRESA_ID.NaoBaixarParcelasDeposito, parametro, idEmpresa);
        }
    }

    public class ParametrosFilialService :  BaseService<IParametrosRepository>, IParametrosFilialService
    {
        public ParametrosFilialService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public ValidationResult SetParametro<T>(FILIAL_PARAMS parametro, T valor, int idregistro, int idEmpresa)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(FILIAL_PARAMS), tipoEnum, idregistro, idEmpresa);
        }

        public T GetParametro<T>(FILIAL_PARAMS parametro, int idFilial, int idEmpresa)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(FILIAL_PARAMS), tipoEnum, idFilial, idEmpresa);

            return (T) retorno;
        }
    }

    public class ParametrosAdministradoraMeioHomologadoService :  BaseService<IParametrosRepository>, IParametrosAdministradoraMeioHomologadoService
    {
        public ParametrosAdministradoraMeioHomologadoService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }


        public ValidationResult SetParametro<T>(ADMINISTRADORA_MH_ID parametro, T valor, string idregistro, int idEmpresa)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(ADMINISTRADORA_MH_ID), tipoEnum, idregistro, idEmpresa);
        }

        public T GetParametro<T>(ADMINISTRADORA_MH_ID parametro, int idAdministradora)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(ADMINISTRADORA_MH_ID), tipoEnum, idAdministradora, null);

            return (T) retorno;
        }
    }

    public class ParametrosAdministradoraPlataformaService :  BaseService<IParametrosRepository>, IParametrosAdministradoraPlataformaService
    {
        public ParametrosAdministradoraPlataformaService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public string GetCaminhoEmailRecuperacaoSenhaApp(int idAdministradoraPlataforma)
        {
            var tipoEnum = ADMINISTRADORA_PLATAFORMA_ID.CaminhoEmailRecuperacaoSenhaApp.ToString("G");

            var retorno = Repository.GetValue<string>(nameof(ADMINISTRADORA_PLATAFORMA_ID), tipoEnum, idAdministradoraPlataforma, null);

            return (string) retorno;
        }

        public string GetCaminhoLogo(int idAdministradoraPlataforma)
        {
            var tipoEnum = ADMINISTRADORA_PLATAFORMA_ID.LogoAdministradora.ToString("G");

            var retorno = Repository.GetValue<string>(nameof(ADMINISTRADORA_PLATAFORMA_ID), tipoEnum, idAdministradoraPlataforma, null);

            return (string) retorno;
        }

        public string GetKeyEnvioPush(int idAdministradoraPlataforma)
        {
            var tipoEnum = PROJETO_FIREBASE.KeyEnvioPush.ToString("G");

            var retorno = Repository.GetValue<string>(nameof(PROJETO_FIREBASE), tipoEnum, idAdministradoraPlataforma, null);

            return (string) retorno;
        }

        public ConfiguracaoEnvioEmail GetConfiguracaoEmail(int idAdministradoraPlataforma)
        {
            var enumEmail = ADMINISTRADORA_PLATAFORMA_ID.EmailEndereco.ToString("G");
            var enumSenha = ADMINISTRADORA_PLATAFORMA_ID.EmailSenha.ToString("G");
            var enumSMTP = ADMINISTRADORA_PLATAFORMA_ID.EmailSMTP.ToString("G");
            var emailPorta = ADMINISTRADORA_PLATAFORMA_ID.EmailPorta.ToString("G");
            var enumSSL = ADMINISTRADORA_PLATAFORMA_ID.EmailSSL.ToString("G");

            var email = (string) Repository.GetValue<string>(nameof(ADMINISTRADORA_PLATAFORMA_ID), enumEmail, idAdministradoraPlataforma, null);
            var senha = (string) Repository.GetValue<string>(nameof(ADMINISTRADORA_PLATAFORMA_ID), enumSenha, idAdministradoraPlataforma, null);
            var smtp =(string)  Repository.GetValue<string>(nameof(ADMINISTRADORA_PLATAFORMA_ID), enumSMTP, idAdministradoraPlataforma, null);
            var porta = (decimal?) Repository.GetValue<decimal?>(nameof(ADMINISTRADORA_PLATAFORMA_ID), emailPorta, idAdministradoraPlataforma, null);
            var ssl = (bool?) Repository.GetValue<bool?>(nameof(ADMINISTRADORA_PLATAFORMA_ID), enumSSL, idAdministradoraPlataforma, null);

            return new ConfiguracaoEnvioEmail
            {
                Endereco = email,
                Senha = senha,
                Porta = porta.ToIntNullable() ?? 0,
                SMTP = smtp,
                SSL = ssl ?? false
            };
        }
    }

    public class ParametrosUsuarioService :  BaseService<IParametrosRepository>, IParametrosUsuarioService
    {
        private IParametrosEmpresaService _parametrosEmpresaService;
        public ParametrosUsuarioService(IParametrosRepository repository, IUserIdentity sessionUser, IParametrosEmpresaService parametrosEmpresaService) : base(repository, sessionUser)
        {
            _parametrosEmpresaService = parametrosEmpresaService;
        }

        public ValidationResult SetParametro<T>(USUARIO_ID parametro, T valor, string idregistro)
        {
            throw new NotImplementedException();
        }

        public ValidationResult SetParametro<T>(USUARIO_ID parametro, T valor, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            return Repository.SetValue(valor, nameof(USUARIO_ID), tipoEnum, idregistro, null);
        }

        public T GetParametro<T>(USUARIO_ID parametro, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");

            var retorno = Repository.GetValue<T>(nameof(USUARIO_ID), tipoEnum, idregistro, null);

            return (T) retorno;
        }

        public T GetParametro<T>(USUARIO_ID parametro, string idregistro)
        {
            throw new NotImplementedException();
        }

        public Dictionary<USUARIO_ID, T> GetParametros<T>(IList<USUARIO_ID> parametros, string idregistro)
        {
            throw new NotImplementedException();
        }

        public Dictionary<USUARIO_ID, T> GetParametros<T>(IList<USUARIO_ID> parametros, int idregistro)
        {
            var chaves = parametros.Select(parametrosProprietarioDocumento => parametrosProprietarioDocumento.ToString("G")).ToList();

            var retornos = Repository.GetValues<T>(nameof(USUARIO_ID), chaves, idregistro, null);

            var resposta = new Dictionary<USUARIO_ID, T>();

            foreach (var retorno in retornos)
            {
                System.Enum.TryParse(retorno.Key, out USUARIO_ID myenum);
                if (typeof(T).IsEnum)
                {
                    if (retorno.Value is decimal)
                    {
                        var enumValue = (T) System.Enum.Parse(typeof(T), Convert.ToInt32(retorno.Value).ToString());
                        resposta.Add(myenum, enumValue);
                    }
                    else
                    {
                        resposta.Add(myenum, (T) retorno.Value);
                    }
                }
                else
                {
                    resposta.Add(myenum, (T) retorno.Value);
                }
            }

            return resposta;
        }

        public bool AnyParametro(IEnumerable<USUARIO_ID> parametros, int idregistro)
        {
            var chaves = parametros.Select(parametroUsuarioId => parametroUsuarioId.ToString("G")).ToList();

            var anyParametro = Repository.AnyParametro(nameof(USUARIO_ID), chaves.FirstOrDefault(), idregistro, null);

            return anyParametro;
        }

        public bool AnyParametro(IEnumerable<USUARIO_ID> parametros, string idregistro)
        {
            throw new NotImplementedException();
        }

        public PermissoesUsuarioAtendimentoPortador GetNovoUsuarioPermissoesAtendimentoCartao(int idEmpresa)
        {
            var parametrosempresa = _parametrosEmpresaService.GetPermissoesAtendimentoCartao(idEmpresa);

            //Se a empresa tem permissão, deve retornar falso para o botão vir desmarcado, caso contrário nulo para não apresentar o botão na tela
            return new PermissoesUsuarioAtendimentoPortador
            {
                BloquearCartao = !parametrosempresa.BloquearCartao ? (bool?) null : false,
                DesbloquearCartao = !parametrosempresa.DesbloquearCartao ? (bool?) null : false,
                AlterarSenhaCartao = !parametrosempresa.AlterarSenhaCartao ? (bool?) null : false,
                TransferenciaBancaria = !parametrosempresa.TransferenciaBancaria ? (bool?) null : false,
                TransferenciaCartoes = !parametrosempresa.TransferenciaCartoes ? (bool?) null : false,
                Resgate = !parametrosempresa.Resgate ? (bool?) null : false,
                EstornoResgate = !parametrosempresa.EstornoResgate ? (bool?) null : false
            };
        }

        public PermissoesUsuarioAtendimentoPortador GetPermissoesAtendimentoCartao(int idUsuario, int idEmpresa)
        {
            var parametrosempresa = _parametrosEmpresaService.GetPermissoesAtendimentoCartao(idEmpresa);
            if(!parametrosempresa.AlgumaPermissao())
                return new PermissoesUsuarioAtendimentoPortador();

            var parametrosEnum = new List<USUARIO_ID>
            {
                USUARIO_ID.AtendimentoPermiteBloquearCartao,
                USUARIO_ID.AtendimentoPermiteDesbloquearCartao,
                USUARIO_ID.AtendimentoPermiteAlterarSenhaCartao,
                USUARIO_ID.AtendimentoPermiteRealizarTransferenciaBancaria,
                USUARIO_ID.AtendimentoPermiteRealizarTransferenciaCartoes,
                USUARIO_ID.AtendimentoPermiteRealizarResgate,
                USUARIO_ID.AtendimentoPermiteRealizarEstornoResgate,
            };
            var parametros = GetParametros<bool?>(parametrosEnum, idUsuario);

            //Se a empresa tem permissão, deve retornar do usuário a permissão dele ou falso, caso contrário nulo para não apresentar o botão na tela
            return new PermissoesUsuarioAtendimentoPortador
            {
                BloquearCartao = !parametrosempresa.BloquearCartao ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteBloquearCartao).Select(x => x.Value).FirstOrDefault() ?? false,
                DesbloquearCartao = !parametrosempresa.DesbloquearCartao ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteDesbloquearCartao).Select(x => x.Value).FirstOrDefault() ?? false,
                AlterarSenhaCartao = !parametrosempresa.AlterarSenhaCartao ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteAlterarSenhaCartao).Select(x => x.Value).FirstOrDefault() ?? false,
                TransferenciaBancaria = !parametrosempresa.TransferenciaBancaria ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteRealizarTransferenciaBancaria).Select(x => x.Value).FirstOrDefault() ?? false,
                TransferenciaCartoes = !parametrosempresa.TransferenciaCartoes ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteRealizarTransferenciaCartoes).Select(x => x.Value).FirstOrDefault() ?? false,
                Resgate = !parametrosempresa.Resgate ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteRealizarResgate).Select(x => x.Value).FirstOrDefault() ?? false,
                EstornoResgate = !parametrosempresa.EstornoResgate ? (bool?) null
                    : parametros?.Where(x => x.Key == USUARIO_ID.AtendimentoPermiteRealizarEstornoResgate).Select(x => x.Value).FirstOrDefault() ?? false
            };
        }

        public ValidationResult SetPermissoesAtendimentoCartao(int idUsuario, PermissoesUsuarioAtendimentoPortador permissoes)
        {
            var validationResult = new ValidationResult();

            if (permissoes.BloquearCartao.HasValue)
            {
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteBloquearCartao, permissoes.BloquearCartao, idUsuario));
                if (!validationResult.IsValid)
                    return validationResult;
            }

            if (permissoes.DesbloquearCartao.HasValue)
            {
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteDesbloquearCartao, permissoes.DesbloquearCartao, idUsuario));
                if (!validationResult.IsValid)
                    return validationResult;
            }

            if (permissoes.AlterarSenhaCartao.HasValue)
            {
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteAlterarSenhaCartao, permissoes.AlterarSenhaCartao, idUsuario));
                if (!validationResult.IsValid)
                    return validationResult;
            }

            if (permissoes.TransferenciaBancaria.HasValue)
            {
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteRealizarTransferenciaBancaria, permissoes.TransferenciaBancaria, idUsuario));
                if (!validationResult.IsValid)
                    return validationResult;
            }

            if (permissoes.TransferenciaCartoes.HasValue)
            {
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteRealizarTransferenciaCartoes, permissoes.TransferenciaCartoes, idUsuario));
                if (!validationResult.IsValid)
                    return validationResult;
            }

            if (permissoes.Resgate.HasValue)
            {
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteRealizarResgate, permissoes.Resgate, idUsuario));
                if (!validationResult.IsValid)
                    return validationResult;
            }

            if (permissoes.EstornoResgate.HasValue)
                validationResult.Add(SetParametro(USUARIO_ID.AtendimentoPermiteRealizarEstornoResgate, permissoes.EstornoResgate, idUsuario));

            return validationResult;
        }

        public bool GetAtendimentoPermiteBloquearCartao(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteBloquearCartao, idUsuario).ToBoolSafe();
        }

        public bool GetAtendimentoPermiteDesbloquearCartao(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteDesbloquearCartao, idUsuario).ToBoolSafe();
        }

        public bool GetAtendimentoPermiteAlterarSenhaCartao(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteAlterarSenhaCartao, idUsuario).ToBoolSafe();
        }

        public bool GetAtendimentoPermiteRealizarTransferenciaBancaria(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteRealizarTransferenciaBancaria, idUsuario).ToBoolSafe();
        }

        public bool GetAtendimentoPermiteRealizarTransferenciaCartoes(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteRealizarTransferenciaCartoes, idUsuario).ToBoolSafe();
        }

        public bool GetAtendimentoPermiteRealizarResgate(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteRealizarResgate, idUsuario).ToBoolSafe();
        }

        public bool GetAtendimentoPermiteRealizarEstornoResgate(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AtendimentoPermiteRealizarEstornoResgate, idUsuario).ToBoolSafe();
        }

        public bool GetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AplicativoPermiteRealizarTransferenciaBancaria, idusuario).ToBoolSafe();
        }

        public bool GetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.AplicativoPermiteRealizarTransferenciaCartoes, idusuario).ToBoolSafe();
        }

        public ValidationResult SetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario, bool value)
        {
            return SetParametro(USUARIO_ID.AplicativoPermiteRealizarTransferenciaBancaria, value, idusuario);
        }

        public ValidationResult SetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario, bool value)
        {
            return SetParametro(USUARIO_ID.AplicativoPermiteRealizarTransferenciaCartoes, value, idusuario);
        }

        public ValidationResult SetParametroUsuarioPermitirAcessoAtendimento(int idUsuario, bool value)
        {
            return SetParametro(USUARIO_ID.PermiteAcessoAtendimento, value, idUsuario);
        }

        public ValidationResult SetPermitirAcessoExtratoDetalhado(int idUsuario, bool value)
        {
            return SetParametro(USUARIO_ID.PermitirAcessoExtratoDetalhado, value, idUsuario);
        }

        public bool GetParametroUsuarioPermitirAcessoAtendimento(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermiteAcessoAtendimento, idUsuario).ToBoolSafe();
        }

        public bool GetPermitirEdicaoDadosAdministrativosEmpresa(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirEdicaoDadosAdministrativosEmpresa, idUsuario).ToBoolSafe();
        }

        public bool GetPermitirEdicaoDadosAdministrativosFilial(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirEdicaoDadosAdministrativosFilial, idUsuario).ToBoolSafe();
        }

        public bool GetPermitirEdicaoDadosAdministrativosUsuario(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirEdicaoDadosAdministrativosUsuario, idUsuario).ToBoolSafe();
        }

        public bool GetPermitirEdicaoDadosAdministrativosGrupoUsuario(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirEdicaoDadosAdministrativosGrupoUsuario, idUsuario).ToBoolSafe();
        }

        public bool GetPermitirAcessoExtratoDetalhado(int idUsuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirAcessoExtratoDetalhado, idUsuario).ToBoolSafe();
        }

        public bool GetPermiteAprovarSolicitacaoAdiantamentoApp(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermiteAprovarSolicitacaoAdiantamentoApp, idusuario).ToBoolSafe();
        }

        public ValidationResult SetPermiteAprovarSolicitacaoAdiantamentoApp(int idusuario, bool value)
        {
            return SetParametro(USUARIO_ID.PermiteAprovarSolicitacaoAdiantamentoApp, value, idusuario);
        }

        public bool GetPermiteSolicitarAdiantamentoApp(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermiteSolicitarAdiantamentoApp, idusuario).ToBoolSafe();
        }

        public ValidationResult SetPermiteSolicitarAdiantamentoApp(int idusuario, bool value)
        {
            return SetParametro(USUARIO_ID.PermiteSolicitarAdiantamentoApp, value, idusuario);
        }

        public bool GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermiteEfetuarCargaSolicitacaoAdiantamentoApp, idusuario).ToBoolSafe();
        }

        public ValidationResult SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idusuario, bool value)
        {
            return SetParametro(USUARIO_ID.PermiteEfetuarCargaSolicitacaoAdiantamentoApp, value, idusuario);
        }

        public decimal? GetLimiteDiarioPagamentoPixUsuario(int idUsuario)
        {
            return GetParametro<decimal?>(USUARIO_ID.LimiteDiarioPagamentoPix, idUsuario);
        }

        public ValidationResult SetLimiteDiarioPagamentoPixUsuario(int idUsuario, decimal? parametro)
        {
            return SetParametro(USUARIO_ID.LimiteDiarioPagamentoPix, parametro, idUsuario);
        }
        
        public decimal? GetLimiteUnitarioPagamentoPixUsuario(int idUsuario)
        {
            return GetParametro<decimal?>(USUARIO_ID.LimiteUnitarioPagamentoPix, idUsuario);
        }

        public ValidationResult SetLimiteUnitarioPagamentoPixUsuario(int idUsuario, decimal? parametro)
        {
            return SetParametro(USUARIO_ID.LimiteUnitarioPagamentoPix, parametro, idUsuario);
        }
        
        public ValidationResult SetPermitirEdicaoDadosAdministrativosPix(int idUsuario, bool parametro)
        {
            return SetParametro(USUARIO_ID.PermitirEdicaoDadosAdministrativosPix, parametro, idUsuario);
        }
        
        public bool GetPermitirEdicaoDadosAdministrativosPix(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirEdicaoDadosAdministrativosPix, idusuario).ToBoolSafe();
        }

        public ValidationResult SetPermiteRealizarPagamentoPix(int idUsuario, bool parametro)
        {
            return SetParametro(USUARIO_ID.PermiteRealizarPagamentoPix, parametro, idUsuario);
        }
        
        public bool GetPermiteRealizarPagamentoPix(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermiteRealizarPagamentoPix, idusuario).ToBoolSafe();
        }
        
        public ValidationResult SetPermitirCadastroChavePix(int idUsuario, bool parametro)
        {
            return SetParametro(USUARIO_ID.PermitirCadastroChavePix, parametro, idUsuario);
        }
        
        public bool GetPermitirCadastroChavePix(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermitirCadastroChavePix, idusuario).ToBoolSafe();
        }

        public ValidationResult SetPermiteSolicitarAlteracoesLimitePix(int idUsuario, bool parametro)
        {
            return SetParametro(USUARIO_ID.PermiteSolicitarAlteracoesLimitePix, parametro, idUsuario);
        }
        
        public bool GetPermiteSolicitarAlteracoesLimitePix(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.PermiteSolicitarAlteracoesLimitePix, idusuario).ToBoolSafe();
        }

        public ValidationResult SetGestorAlcadasPix(int idUsuario, bool parametro)
        {
            return SetParametro(USUARIO_ID.GestorAlcadasPix, parametro, idUsuario);
        }

        public bool GetGestorAlcadasPix(int idusuario)
        {
            return GetParametro<bool?>(USUARIO_ID.GestorAlcadasPix, idusuario).ToBoolSafe();
        }
    }

    #endregion

    public class ParametrosEstabelecimentoService : BaseService<IParametrosRepository>, IParametrosEstabelecimentoService
    {
        public ParametrosEstabelecimentoService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        { }

        public ValidationResult SetParametro<T>(ESTABELECIMENTO_ID parametro, T valor, string idregistro)
        {
            var tipoEnum = parametro.ToString("G");
            return Repository.SetValue(valor, nameof(ESTABELECIMENTO_ID), tipoEnum, idregistro, null);
        }

        public ValidationResult SetParametro<T>(ESTABELECIMENTO_ID parametro, T valor, int idregistro)
        {
            var tipoEnum = parametro.ToString("G");
            return Repository.SetValue(valor, nameof(ESTABELECIMENTO_ID), tipoEnum, idregistro, null);
        }

        public T GetParametro<T>(ESTABELECIMENTO_ID parametro, int idEstabelecimento)
        {
            var tipoEnum = parametro.ToString("G");
            var retorno = Repository.GetValue<T>(nameof(ESTABELECIMENTO_ID), tipoEnum, idEstabelecimento, null);
            return (T) retorno;
        }

        public ValidationResult SetValidarChavePagamento(int idEstabelecimento, bool validarChavePagamento)
        {
            return SetParametro(ESTABELECIMENTO_ID.ValidarChavePagamento, validarChavePagamento, idEstabelecimento);
        }

        public bool? GetValidarChavePagamento(int idEstabelecimento)
        {
            var resultado = GetParametro<bool?>(ESTABELECIMENTO_ID.ValidarChavePagamento, idEstabelecimento);
            return resultado;
        }
    }

    public class ParametrosMobileGlobalService : BaseService<IParametrosRepository>, IParametrosMobileGlobalService
    {
        public ParametrosMobileGlobalService(IParametrosRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public T GetParametro<T>(RECURSO_MOBILE parametro)
        {
            var tipoEnum = parametro.ToString("G");
            var retorno = Repository.GetValue<T>(nameof(RECURSO_MOBILE), tipoEnum, null, null);
            return (T)retorno;
        }

        public string GetTelefoneAtendimento()
        {
            var resultado = GetParametro<string>(RECURSO_MOBILE.TelefoneAtendimento);
            return resultado;
        }

        public bool? GetValidarSenhaIgualCPF()
        {
            var resultado = GetParametro<bool?>(RECURSO_MOBILE.ValidarSenhaIgualCPF);
            return resultado;
        }

        public bool? GetUtilizaKeycloack()
        {
            var resultado = GetParametro<bool?>(RECURSO_MOBILE.UtilizaKeycloack);
            return resultado;
        }
    }

    public class ParametrosService : BaseService<IParametrosRepository>, IParametrosService
    {
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly IParametrosProprietarioMotoristaService _parametrosProprietarioMotoristaService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;
        private readonly IParametrosFilialService _parametrosFilialService;
        private readonly IParametrosAdministradoraMeioHomologadoService _parametrosAdministradoraMeioHomologadoService;

        public ParametrosService(IParametrosRepository repository, IUserIdentity sessionUser, IParametrosGenericoService parametrosGenericoService, IParametrosProprietarioService parametrosProprietarioService, IParametrosEmpresaService parametrosEmpresaService, IParametrosUsuarioService parametrosUsuarioService, IParametrosFilialService parametrosFilialService, IParametrosAdministradoraMeioHomologadoService parametrosAdministradoraMeioHomologadoService, IParametrosProprietarioMotoristaService parametrosProprietarioMotoristaService) : base(repository, sessionUser)
        {
            _parametrosGenericoService = parametrosGenericoService;
            _parametrosProprietarioService = parametrosProprietarioService;
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosUsuarioService = parametrosUsuarioService;
            _parametrosFilialService = parametrosFilialService;
            _parametrosAdministradoraMeioHomologadoService = parametrosAdministradoraMeioHomologadoService;
            _parametrosProprietarioMotoristaService = parametrosProprietarioMotoristaService;
        }

        public ValidationResult SetPercentualTransferenciaFreteGenerico(PercentualTransferenciaFreteViagemParametro percentuais)
        {
            var validationResult = new ValidationResult();

            try
            {
                if (!percentuais.Abastecimento.HasValue || percentuais.Abastecimento < 0 || percentuais.Abastecimento > 100)
                    validationResult.Add("O percentual de transferência de abastecimento deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Abono.HasValue || percentuais.Abono < 0 || percentuais.Abono > 100)
                    validationResult.Add("O percentual de transferência de Abono deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Adiantamento.HasValue || percentuais.Adiantamento < 0 || percentuais.Adiantamento > 100)
                    validationResult.Add("O percentual de transferência de Adiantamento deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Estadia.HasValue || percentuais.Estadia < 0 || percentuais.Estadia > 100)
                    validationResult.Add("O percentual de transferência de Estadia deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Saldo.HasValue || percentuais.Saldo < 0 || percentuais.Saldo > 100)
                    validationResult.Add("O percentual de transferência de Saldo deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.TarifaAntt.HasValue || percentuais.TarifaAntt < 0 || percentuais.TarifaAntt > 100)
                    validationResult.Add("O percentual de transferência da Tarifa ANTT deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.RPA.HasValue || percentuais.RPA < 0 || percentuais.RPA > 100)
                    validationResult.Add("O percentual de transferência de RPA deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!validationResult.IsValid) return validationResult;

                validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaAdiantamento, percentuais.Adiantamento);

                if (validationResult.IsValid)
                    validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaSaldo, percentuais.Saldo);

                if (validationResult.IsValid)
                    validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaEstadia, percentuais.Estadia);

                if (validationResult.IsValid)
                    validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaRPA, percentuais.RPA);

                if (validationResult.IsValid)
                    validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaTarifaANTT, percentuais.TarifaAntt);

                if (validationResult.IsValid)
                    validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaAbastecimento, percentuais.Abastecimento);

                if (validationResult.IsValid)
                    validationResult = _parametrosGenericoService.SetParametro(GLOBAL.PercTransferenciaAbono, percentuais.Abono);

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetIdTipoEstabelecimentoPadraoJSL(int idTipoEstabelecimento)
        {
            var validationResult = new ValidationResult();

            try
            {
                var valor = idTipoEstabelecimento.ToDecimalSafe();

                validationResult = _parametrosGenericoService.SetParametro(GLOBAL.IdTipoEstabelecimentoPadraoJSL, valor);
            }
            catch (Exception e)
            {
                validationResult.Add(e.Message);
            }

            return validationResult;
        }

        public ValidationResult SetPercentualTransferenciaFreteProprietario(PercentualTransferenciaFreteViagemParametro percentuais, string documento)
        {
            var validationResult = new ValidationResult();

            try
            {
                if (!percentuais.Abastecimento.HasValue || percentuais.Abastecimento < 0 || percentuais.Abastecimento > 100)
                    validationResult.Add("O percentual de transferência de abastecimento deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Abono.HasValue || percentuais.Abono < 0 || percentuais.Abono > 100)
                    validationResult.Add("O percentual de transferência de Abono deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Adiantamento.HasValue || percentuais.Adiantamento < 0 || percentuais.Adiantamento > 100)
                    validationResult.Add("O percentual de transferência de Adiantamento deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Estadia.HasValue || percentuais.Estadia < 0 || percentuais.Estadia > 100)
                    validationResult.Add("O percentual de transferência de Estadia deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Saldo.HasValue || percentuais.Saldo < 0 || percentuais.Saldo > 100)
                    validationResult.Add("O percentual de transferência de Saldo deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.TarifaAntt.HasValue || percentuais.TarifaAntt < 0 || percentuais.TarifaAntt > 100)
                    validationResult.Add("O percentual de transferência da Tarifa ANTT deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.RPA.HasValue || percentuais.RPA < 0 || percentuais.RPA > 100)
                    validationResult.Add("O percentual de transferência de RPA deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!validationResult.IsValid) return validationResult;

                validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaAdiantamento, percentuais.Adiantamento, documento);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaSaldo, percentuais.Saldo, documento);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaEstadia, percentuais.Estadia, documento);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaRPA, percentuais.RPA, documento);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaTarifaANTT, percentuais.TarifaAntt, documento);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaAbastecimento, percentuais.Abastecimento, documento);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO.PercTransferenciaAbono, percentuais.Abono, documento);

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetPercentualTransferenciaFreteProprietarioMotorista(PercentualTransferenciaFreteViagemParametro percentuais, string documentoProprietario, string documentoMotorista)
        {
            var validationResult = new ValidationResult();

            try
            {
                if (!percentuais.Abastecimento.HasValue || percentuais.Abastecimento < 0 || percentuais.Abastecimento > 100)
                    validationResult.Add("O percentual de transferência de abastecimento deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Abono.HasValue || percentuais.Abono < 0 || percentuais.Abono > 100)
                    validationResult.Add("O percentual de transferência de Abono deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Adiantamento.HasValue || percentuais.Adiantamento < 0 || percentuais.Adiantamento > 100)
                    validationResult.Add("O percentual de transferência de Adiantamento deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Estadia.HasValue || percentuais.Estadia < 0 || percentuais.Estadia > 100)
                    validationResult.Add("O percentual de transferência de Estadia deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.Saldo.HasValue || percentuais.Saldo < 0 || percentuais.Saldo > 100)
                    validationResult.Add("O percentual de transferência de Saldo deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.TarifaAntt.HasValue || percentuais.TarifaAntt < 0 || percentuais.TarifaAntt > 100)
                    validationResult.Add("O percentual de transferência da Tarifa ANTT deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!percentuais.RPA.HasValue || percentuais.RPA < 0 || percentuais.RPA > 100)
                    validationResult.Add("O percentual de transferência de RPA deve ser maior ou igual que 0 e menor ou igual que 100");

                if (!validationResult.IsValid) return validationResult;

                validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAdiantamento, percentuais.Adiantamento, documentoProprietario, documentoMotorista);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaSaldo, percentuais.Saldo, documentoProprietario, documentoMotorista);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaEstadia, percentuais.Estadia, documentoProprietario, documentoMotorista);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaRPA, percentuais.RPA, documentoProprietario, documentoMotorista);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaTarifaANTT, percentuais.TarifaAntt, documentoProprietario, documentoMotorista);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbastecimento, percentuais.Abastecimento, documentoProprietario, documentoMotorista);

                if (validationResult.IsValid)
                    validationResult = SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbono, percentuais.Abono, documentoProprietario, documentoMotorista);

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO parametro, decimal? valor, string documento)
        {
            var validationResult = new ValidationResult();

            var parametrosValidos = new List<PROPRIETARIO_DOCUMENTO>
            {
                PROPRIETARIO_DOCUMENTO.PercTransferenciaAbastecimento,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaAbono,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaAdiantamento,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaEstadia,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaSaldo,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaRPA,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaTarifaANTT
            };

            try
            {
                if (!parametrosValidos.Contains(parametro))
                    validationResult.Add($"Parâmetro de transferência inesperado. Parâmetro informado: {parametro:G}.");

                if (!valor.HasValue || valor < 0 || valor > 100)
                    validationResult.Add($"O percentual de transferência de {parametro.GetDescription()} deve ser maior ou igual que 0 e menor ou igual que 100.");

                if (validationResult.IsValid)
                {
                    validationResult = _parametrosProprietarioService.SetParametro(parametro, valor, documento);
                }

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetParametroProprietarioMotoristaTransferencia(PROPRIETARIO_MOTORISTA_DOCUMENTO parametro, decimal? valor, string documentoProprietario, string documentoMotorista)
        {
            var validationResult = new ValidationResult();

            var parametrosValidos = new List<PROPRIETARIO_MOTORISTA_DOCUMENTO>
            {
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbastecimento,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbono,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAdiantamento,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaEstadia,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaSaldo,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaRPA,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaTarifaANTT
            };

            try
            {
                if (!parametrosValidos.Contains(parametro))
                    validationResult.Add($"Parâmetro de transferência inesperado. Parâmetro informado: {parametro:G}.");

                if (!valor.HasValue || valor < 0 || valor > 100)
                    validationResult.Add($"O percentual de transferência de {parametro.GetDescription()} deve ser maior ou igual que 0 e menor ou igual que 100.");

                if (validationResult.IsValid)
                {
                    validationResult = _parametrosProprietarioMotoristaService.SetParametro(parametro, valor, $"{documentoProprietario}:{documentoMotorista}");
                }

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagio(AcaoSaldoResidualNovoCreditoCartaoPedagio? valor, string documento)
        {
            try
            {
                var docNumb = documento.OnlyNumbers();

                var validationResult = _parametrosProprietarioService.SetParametro(PROPRIETARIO_DOCUMENTO.AcaoSaldoResidualNovoCreditoCartaoPedagio, valor.ToDecimalSafe(), docNumb);

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagio(AcaoSaldoResidualNovoCreditoCartaoPedagio? valor, int idEmpresa)
        {
            var validationResult = new ValidationResult();

            try
            {
                validationResult.Add(_parametrosEmpresaService.SetParametro(EMPRESA_ID.AcaoSaldoResidualNovoCreditoCartaoPedagio, valor.ToDecimalSafe(), idEmpresa));

                return validationResult;
            }
            catch (Exception e)
            {
                return validationResult.Add(e.Message);
            }
        }

        public ValidationResult SetAutorizaEstabelecimentosRedeJSL(bool valor, int idEmpresa)
        {
            var validationResult = new ValidationResult();

            try
            {
                validationResult = _parametrosEmpresaService.SetParametro(EMPRESA_ID.AutorizaEstabelecimentosRedeJSL, valor, idEmpresa);

                return validationResult;
            }
            catch (Exception e)
            {
                return validationResult.Add(e.Message);
            }
        }

        public ValidationResult SetBancoPadraoCheque(string valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado = parametrosEmpresaService.SetParametro(EMPRESA_ID.BancoPadraoCheque, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetCancelarViagemComSaldoMenorQueValorDoExtorno(bool valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.CancelarViagemComSaldoMenorQueValorDoExtorno,
                        valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetQuantidadeLimiteImpressoesCheque(decimal? valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.QuantidadeLimiteImpressoesCheque, valor,
                        idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetMarginTopImpressaoCheque(decimal? valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.MarginTopImpressaoCheque, valor,
                        idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetMarginLeftImpressaoCheque(decimal? valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.MarginLeftImpressaoCheque, valor,
                        idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetEndpointIntegracaoCheque(string valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.EndpointIntegracaoCheque, valor,
                        idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetHeadersIntegracaoCheque(string valor, int idEmpresa)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.HeadersIntegracaoCheque, valor,
                        idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetIdProjetoFireBase(int idUsuario, int? idAdministradora)
        {
            try
            {
                var adiministradoraDecimal = idAdministradora.ToDecimalSafe();
                var resultado = _parametrosUsuarioService.SetParametro(USUARIO_ID.IdProjetoFireBase, adiministradoraDecimal ?? 1, idUsuario);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteGenerico()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.PercTransferenciaAdiantamento,
                GLOBAL.PercTransferenciaSaldo,
                GLOBAL.PercTransferenciaEstadia,
                GLOBAL.PercTransferenciaRPA,
                GLOBAL.PercTransferenciaTarifaANTT,
                GLOBAL.PercTransferenciaAbastecimento,
                GLOBAL.PercTransferenciaAbono
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal?>(listaEnuns);

            var result = new PercentualTransferenciaFreteViagemParametro
            {
                Adiantamento = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaAdiantamento).Select(c => c.Value).FirstOrDefault(),
                Saldo = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaSaldo).Select(c => c.Value).FirstOrDefault(),
                Estadia = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaEstadia).Select(c => c.Value).FirstOrDefault(),
                RPA = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaRPA).Select(c => c.Value).FirstOrDefault(),
                TarifaAntt = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaTarifaANTT).Select(c => c.Value).FirstOrDefault(),
                Abastecimento = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaAbastecimento).Select(c => c.Value).FirstOrDefault(),
                Abono = resultado.Where(c => c.Key == GLOBAL.PercTransferenciaAbono).Select(c => c.Value).FirstOrDefault()
            };

            return result;
        }

        public int GetIdTipoEstabelecimentoPadraoJSL()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.IdTipoEstabelecimentoPadraoJSL
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal>(listaEnuns).FirstOrDefault();

            return resultado.Value.ToIntSafe() ?? 0;
        }

        public int GetIdEmpresaPadraoUsuarioEstabelecimentoJSL()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.IdEmpresaPadraoUsuarioEstabelecimentoJSL
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal>(listaEnuns).FirstOrDefault();

            return resultado.Value.ToIntSafe() ?? 0;
        }

        public PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteProprietario(string documento)
        {
            var listaEnuns = new List<PROPRIETARIO_DOCUMENTO>
            {
                PROPRIETARIO_DOCUMENTO.PercTransferenciaAdiantamento,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaSaldo,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaEstadia,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaRPA,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaTarifaANTT,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaAbastecimento,
                PROPRIETARIO_DOCUMENTO.PercTransferenciaAbono
            };

            var resultado = _parametrosProprietarioService.GetParametros<decimal?>(listaEnuns, documento);

            var result = new PercentualTransferenciaFreteViagemParametro
            {
                Adiantamento = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaAdiantamento).Select(c => c.Value).FirstOrDefault(),
                Saldo = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaSaldo).Select(c => c.Value).FirstOrDefault(),
                Estadia = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaEstadia).Select(c => c.Value).FirstOrDefault(),
                RPA = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaRPA).Select(c => c.Value).FirstOrDefault(),
                TarifaAntt = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaTarifaANTT).Select(c => c.Value).FirstOrDefault(),
                Abastecimento = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaAbastecimento).Select(c => c.Value).FirstOrDefault(),
                Abono = resultado.Where(c => c.Key == PROPRIETARIO_DOCUMENTO.PercTransferenciaAbono).Select(c => c.Value).FirstOrDefault()
            };

            return result;
        }

        public PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteProprietarioMotorista(string documentoProprietario, string documentoMotorista)
        {
            var listaEnuns = new List<PROPRIETARIO_MOTORISTA_DOCUMENTO>
            {
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAdiantamento,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaSaldo,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaEstadia,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaRPA,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaTarifaANTT,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbastecimento,
                PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbono
            };

            var resultado = _parametrosProprietarioMotoristaService.GetParametros<decimal?>(listaEnuns, $"{documentoProprietario}:{documentoMotorista}");

            var result = new PercentualTransferenciaFreteViagemParametro
            {
                Adiantamento = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAdiantamento).Select(c => c.Value).FirstOrDefault(),
                Saldo = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaSaldo).Select(c => c.Value).FirstOrDefault(),
                Estadia = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaEstadia).Select(c => c.Value).FirstOrDefault(),
                RPA = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaRPA).Select(c => c.Value).FirstOrDefault(),
                TarifaAntt = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaTarifaANTT).Select(c => c.Value).FirstOrDefault(),
                Abastecimento = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbastecimento).Select(c => c.Value).FirstOrDefault(),
                Abono = resultado.Where(c => c.Key == PROPRIETARIO_MOTORISTA_DOCUMENTO.PercTransferenciaAbono).Select(c => c.Value).FirstOrDefault()
            };

            return result;
        }

        public bool GetAutorizaEstabelecimentosRedeJSL(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.AutorizaEstabelecimentosRedeJSL
            };

            var resultado = _parametrosEmpresaService.GetParametros<bool>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(string documento)
        {
            var listaEnuns = new List<PROPRIETARIO_DOCUMENTO>
            {
                PROPRIETARIO_DOCUMENTO.AcaoSaldoResidualNovoCreditoCartaoPedagio
            };

            var resultado = _parametrosProprietarioService.GetParametros<decimal>(listaEnuns, documento)
                .FirstOrDefault();

            return (AcaoSaldoResidualNovoCreditoCartaoPedagio) resultado.Value;
        }

        public AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.AcaoSaldoResidualNovoCreditoCartaoPedagio
            };

            var resultado = _parametrosEmpresaService.GetParametros<decimal>(listaEnuns, idEmpresa)
                .FirstOrDefault();

            return (AcaoSaldoResidualNovoCreditoCartaoPedagio) resultado.Value;
        }

        public PermissaoUsuarioAlterarLimiteAlcadas GetPermissaoUsuarioAlterarLimiteAlcadas(int idusuario)
        {
            var listaEnuns = new List<USUARIO_ID>
            {
                USUARIO_ID.PermiteAlterarLimitesAlcadasEmpresa,
                USUARIO_ID.PermiteAlterarLimitesAlcadasFilial
            };

            var resultado = _parametrosUsuarioService.GetParametros<bool>(listaEnuns, idusuario).ToList();

            var result = new PermissaoUsuarioAlterarLimiteAlcadas
            {
                PermiteEmpresa = resultado.Where(c => c.Key == USUARIO_ID.PermiteAlterarLimitesAlcadasEmpresa).Select(c => c.Value).FirstOrDefault(),
                PermiteFilial = resultado.Where(c => c.Key == USUARIO_ID.PermiteAlterarLimitesAlcadasFilial).Select(c => c.Value).FirstOrDefault(),
            };

            return result;
        }

        public ValidationResult SetPermissaoUsuarioAlterarLimiteAlcadas(PermissaoUsuarioAlterarLimiteAlcadas valor, int idusuario)
        {
            var validationResult = new ValidationResult();

            try
            {
                validationResult = _parametrosUsuarioService.SetParametro(USUARIO_ID.PermiteAlterarLimitesAlcadasEmpresa, valor.PermiteEmpresa, idusuario);
                validationResult = _parametrosUsuarioService.SetParametro(USUARIO_ID.PermiteAlterarLimitesAlcadasFilial, valor.PermiteFilial, idusuario);

                return validationResult;
            }
            catch (Exception e)
            {
                return validationResult.Add(e.Message);
            }
        }

        public ValidationResult SetValorLimitePagamentoCheque(decimal? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ValorLimitePagamentoCheque, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetSeriePadraoCheque(string valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.SeriePadraoCheque, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public decimal? GetValorLimitePagamentoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.ValorLimitePagamentoCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<decimal?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public string GetSeriePadraoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.SeriePadraoCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<string>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public string GetBancoPadraoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.BancoPadraoCheque,
            };

            var parametrosEmpresaService = _parametrosEmpresaService;

            var resultado = parametrosEmpresaService.GetParametros<string>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public bool GetCancelarViagemComSaldoMenorQueValorDoExtorno(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.CancelarViagemComSaldoMenorQueValorDoExtorno,
            };

            var parametrosEmpresaService = _parametrosEmpresaService;

            var resultado = parametrosEmpresaService.GetParametros<bool>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public decimal? GetQuantidadeLimiteImpressoesCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.QuantidadeLimiteImpressoesCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<decimal?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public decimal? GetMarginTopImpressaoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.MarginTopImpressaoCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<decimal?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public decimal? GetMarginLeftImpressaoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.MarginLeftImpressaoCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<decimal?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public string GetEndpointIntegracaoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.EndpointIntegracaoCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<string>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public string GetHeadersIntegracaoCheque(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.HeadersIntegracaoCheque
            };

            var resultado = _parametrosEmpresaService.GetParametros<string>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public ValidationResult SetObrigatoriedadeArmazemEmpresa(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ObrigatoriedadeArmazemOC, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetSeparadorArquivoCsv(string valor, int idEmpresa)
        {
            try
            {
                var resultado =
                    _parametrosEmpresaService.SetParametro(EMPRESA_ID.SeparadorArquivoCsv, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetMostrarHeaderArquivoCsv(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado =
                    _parametrosEmpresaService.SetParametro(EMPRESA_ID.MostrarHeaderArquivoCsv, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeArmazemEmpresa(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigatoriedadeArmazemOC, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeOrdemCompraEmpresa(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.OrdemCompraOC, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeOrdemCompraEmpresa(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.OrdemCompraOC, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeFormulaEmpresa(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ObrigatoriedadeFormulaOC, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeFormulaEmpresa(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigatoriedadeFormulaOC, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadePedidoEmpresa(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ObrigatoriedadePedidoOC, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadePedidoEmpresa(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigatoriedadePedidoOC, idEmpresa);

            return resultado;
        }
        
        public ValidationResult SetCodigoOfx(string valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.CodOfx, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public string GetCodigoOfx(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<string>(EMPRESA_ID.CodOfx, idEmpresa);

            return resultado;
        }


        public ValidationResult SetObrigatoriedadeProtocolo(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ObrigatoriedadeProtocoloOC, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeProtocolo(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigatoriedadeProtocoloOC, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeQuantidadeEmpresa(bool? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ObrigatoriedadeQuantidadeOC, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeQuantidadeEmpresa(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigatoriedadeQuantidadeOC, idEmpresa);

            return resultado;
        }
        public ValidationResult SetInformacoesTransferenciaBancaria(string valor, int idEmpresa)
        {
            var validationResult = new ValidationResult();

            try
            {
                validationResult.Add(_parametrosEmpresaService.SetParametro(EMPRESA_ID.InformacoesTransferenciaBancaria, valor, idEmpresa));

                return validationResult;
            }
            catch (Exception e)
            {
                return validationResult.Add(e.Message);
            }
        }
        public string GetInformacoesTransferenciaBancaria(int idEmpresa)
        {
            var resultado = _parametrosEmpresaService.GetParametro<string>(EMPRESA_ID.InformacoesTransferenciaBancaria, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeArmazemFilial(bool? valor, int idEmpresa, int idFilial)
        {
            try
            {
                var resultado = _parametrosFilialService.SetParametro(FILIAL_PARAMS.ObrigatoriedadeArmazemOC, valor, idFilial, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeArmazemFilial(int idEmpresa, int idFilial)
        {
            var resultado = _parametrosFilialService.GetParametro<bool?>(FILIAL_PARAMS.ObrigatoriedadeArmazemOC, idFilial, idEmpresa);

            return resultado;
        }
        public ValidationResult SetObrigatoriedadeOrdemCompraFilial(bool? valor, int idEmpresa, int idFilial)
        {
            try
            {
                var resultado = _parametrosFilialService.SetParametro(FILIAL_PARAMS.OrdemCompraOC, valor, idFilial, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeOrdemCompraFilial(int idEmpresa, int idFilial)
        {
            var resultado = _parametrosFilialService.GetParametro<bool?>(FILIAL_PARAMS.OrdemCompraOC, idFilial, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeFormulaFilial(bool? valor, int idEmpresa, int idFilial)
        {
            try
            {
                var resultado = _parametrosFilialService.SetParametro(FILIAL_PARAMS.ObrigatoriedadeFormulaOC, valor, idFilial, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeFormulaFilial(int idEmpresa, int idFilial)
        {
            var resultado = _parametrosFilialService.GetParametro<bool?>(FILIAL_PARAMS.ObrigatoriedadeFormulaOC, idFilial, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadePedidoFilial(bool? valor, int idEmpresa, int idFilial)
        {
            try
            {
                var resultado = _parametrosFilialService.SetParametro(FILIAL_PARAMS.ObrigatoriedadePedidoOC, valor, idFilial, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadePedidoFilial(int idEmpresa, int idFilial)
        {
            var resultado = _parametrosFilialService.GetParametro<bool?>(FILIAL_PARAMS.ObrigatoriedadePedidoOC, idFilial, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeProtocoloFilial(bool? valor, int idEmpresa, int idFilial)
        {
            try
            {
                var resultado = _parametrosFilialService.SetParametro(FILIAL_PARAMS.ObrigatoriedadeProtocoloOC, valor, idFilial, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeProtocoloFilial(int idEmpresa, int idFilial)
        {
            var resultado = _parametrosFilialService.GetParametro<bool?>(FILIAL_PARAMS.ObrigatoriedadeProtocoloOC, idFilial, idEmpresa);

            return resultado;
        }

        public ValidationResult SetObrigatoriedadeQuantidadeFilial(bool? valor, int idEmpresa, int idFilial)
        {
            try
            {
                var resultado = _parametrosFilialService.SetParametro(FILIAL_PARAMS.ObrigatoriedadeQuantidadeOC, valor, idFilial, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetObrigatoriedadeQuantidadeFilial(int idEmpresa, int idFilial)
        {
            var resultado = _parametrosFilialService.GetParametro<bool?>(FILIAL_PARAMS.ObrigatoriedadeQuantidadeOC, idFilial, idEmpresa);

            return resultado;
        }

        public bool GetObrigaRoteirizacaoPedagioViagemEmpresa(int idEmpresa = 0)
        {
            bool? resultado = null;

            if (idEmpresa != 0)
                resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigaRoteirizacaoPedagioViagem, idEmpresa);
            else if (SessionUser.IdEmpresa.HasValue)
            {
                // if (!SessionUser.IdEmpresa.HasValue)
                //     throw new Exception("Consulta não pode ser realizada por usuário sem empresa vinculada.");

                resultado = _parametrosEmpresaService.GetParametro<bool?>(EMPRESA_ID.ObrigaRoteirizacaoPedagioViagem, SessionUser.IdEmpresa.Value);
            }

            return resultado ?? true;
        }

        public ValidationResult SetObrigaRoteirizacaoPedagioViagemEmpresa(bool valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.ObrigaRoteirizacaoPedagioViagem, valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public string GetSeparadorArquivoCsv(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.SeparadorArquivoCsv
            };

            var resultado = _parametrosEmpresaService.GetParametros<string>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public bool? GetMostrarHeaderArquivoCsv(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.MostrarHeaderArquivoCsv
            };

            var resultado = _parametrosEmpresaService.GetParametros<bool?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public int? GetIdUsuarioGenericoWS()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.IdUsuarioGenericoWS,
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal>(listaEnuns).FirstOrDefault();

            return resultado.Value.ToIntSafe();
        }

        public int? GetHorasExpiracaoCreditoPedagio(int idEmpresa)
        {
            var horasDecimal =  Convert.ToInt32(_parametrosEmpresaService.GetParametro<decimal?>(EMPRESA_ID.HorasExpiracaoCreditoPedagio, idEmpresa));

            return horasDecimal.ToIntSafe();
        }

        public ValidationResult SetHorasExpiracaoCreditoPedagio(int horas, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.HorasExpiracaoCreditoPedagio, Convert.ToDecimal(horas), idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetEmailsAlertaCiotAgregado(string emails, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.EmailAvisoCiotAgregado, emails, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetDiasCancelamentoViagem(int? valor, int idEmpresa)
        {
            try
            {
                var resultado = _parametrosEmpresaService.SetParametro(EMPRESA_ID.DiasCancelamentoViagem, (valor ?? 0).ToDecimal(), idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetTokenAdministradora(string valor, string idregistro ,int idEmpresa)
        {
            try
            {
               var validationResult = _parametrosAdministradoraMeioHomologadoService.SetParametro(ADMINISTRADORA_MH_ID.TokenAdministradora, valor, idregistro ,idEmpresa);

               return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
        public string GetTokenAdministradora(int idAdministradora)
        {
            var resultado = _parametrosAdministradoraMeioHomologadoService.GetParametro<string>(ADMINISTRADORA_MH_ID.TokenAdministradora, idAdministradora);

            return resultado;
        }

        public ValidationResult SetProdutoIdPadrao(string valor, string idregistro ,int idEmpresa)
        {
            try
            {
                var validationResult = _parametrosAdministradoraMeioHomologadoService.SetParametro(ADMINISTRADORA_MH_ID.ProdutoIdPadrao, valor, idregistro ,idEmpresa);

                return validationResult;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public string GetProdutoIdPadrao(int idAdministradora)
        {
            var resultado = _parametrosAdministradoraMeioHomologadoService.GetParametro<string>(ADMINISTRADORA_MH_ID.ProdutoIdPadrao, idAdministradora);

            return resultado;
        }

        public bool ValidaCnpjCpfProprietarioNaViagem()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.ValidaCnpjCpfProprietarioNaViagem,
            };

            var resultado = _parametrosGenericoService.GetParametros<bool?>(listaEnuns).FirstOrDefault();

            return resultado.Value ?? false;
        }

        public int GetIdLayoutCartao()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.IdLayoutCartao,
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal>(listaEnuns).FirstOrDefault();

            return resultado.Value.ToIntSafe() ?? 1;
        }

        public int GetIdProdutoCartaoFrete()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.IdProdutoCartaoFrete,
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal>(listaEnuns).FirstOrDefault();

            return resultado.Value.ToIntSafe() ?? 1;
        }

        public bool GetMetodoRefatoradoRelatorioConciliacaoAnalitico()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.MetodoRefatoradoRelatorioConciliacaoAnalitico,
            };

            var resultado = _parametrosGenericoService.GetParametros<bool?>(listaEnuns).FirstOrDefault();

            return resultado.Value ?? false;
        }

        public int GetTipoCargaAnttDefault()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.TipoCargaAnttDefault,
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal>(listaEnuns).FirstOrDefault();

            return resultado.Value.ToIntSafe() ?? 5;
        }

        public int? GetVersaoAntt()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.VersaoAntt
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal?>(listaEnuns).FirstOrDefault();
            return resultado.Value.ToIntSafe();
        }

        public bool? GetValidarApenasQuantidadeDeCaracteresCnh()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.ValidarApenasQuantidadeDeCaracteresCnh
            };

            var resultado = _parametrosGenericoService.GetParametros<bool?>(listaEnuns).FirstOrDefault();
            return resultado.Value;
        }

        public int GetGrupoContabilizacaoCentralAtendimento()
        {
            var listaEnuns = new List<GLOBAL>
            {
                GLOBAL.GrupoContabilizacaoCentralAtendimento
            };

            var resultado = _parametrosGenericoService.GetParametros<decimal?>(listaEnuns).FirstOrDefault();
            return resultado.Value.ToIntSafe() ?? 0;
        }

        public int GetIdProjetoFireBase(int idusuario)
        {
            var tipoEnum = USUARIO_ID.IdProjetoFireBase.ToString("G");

            var retorno = Repository.GetValue<decimal?>(nameof(USUARIO_ID), tipoEnum, idusuario, null);

            return ((decimal?) retorno).ToIntNullable() ?? 1; // Se não existe, é o ATS
        }

        public int GetIdAdministradoraPlataforma(int idEmpresa)
        {
            var tipoEnum = EMPRESA_ID.IdAdministradoraPlataforma.ToString("G");

            var retorno = Repository.GetValue<decimal?>(nameof(ADMINISTRADORA_PLATAFORMA_ID), tipoEnum, idEmpresa, null);

            return ((decimal?) retorno).ToIntNullable() ?? 1; // Se não existe, é o ATS
        }

        public bool? HasTransferenciaFrete(IList<ETipoEventoViagem> tiposEvento, string proprietarioDocumento, int idempresa, string motoristaDocumento)
        {
            var transferenciaFreteProprietarioMotorista = GetPercentualTransferenciaFreteProprietarioMotorista(proprietarioDocumento, motoristaDocumento);
            var transferenciasProprietario = GetPercentualTransferenciaFreteProprietario(proprietarioDocumento);
            var transferenciasGenerico = GetPercentualTransferenciaFreteGenerico();

            foreach (var eTipoEventoViagem in tiposEvento)
            {
                switch (eTipoEventoViagem)
                {
                    case ETipoEventoViagem.Adiantamento:

                        if (transferenciaFreteProprietarioMotorista?.Adiantamento.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.Adiantamento > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.Adiantamento.HasValue ?? false)
                        {
                            if (transferenciasProprietario.Adiantamento > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.Adiantamento > 0)
                            return true;

                        break;

                    case ETipoEventoViagem.Saldo:

                        if (transferenciaFreteProprietarioMotorista?.Saldo.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.Saldo > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.Saldo.HasValue ?? false)
                        {
                            if (transferenciasProprietario.Saldo > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.Saldo > 0)
                            return true;

                        break;

                    case ETipoEventoViagem.Estadia:

                        if (transferenciaFreteProprietarioMotorista?.Estadia.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.Estadia > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.Estadia.HasValue ?? false)
                        {
                            if (transferenciasProprietario.Estadia > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.Estadia > 0)
                            return true;

                        break;

                    case ETipoEventoViagem.RPA:

                        if (transferenciaFreteProprietarioMotorista?.RPA.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.RPA > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.RPA.HasValue ?? false)
                        {
                            if (transferenciasProprietario.RPA > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.RPA > 0)
                            return true;

                        break;

                    case ETipoEventoViagem.TarifaAntt:

                        if (transferenciaFreteProprietarioMotorista?.TarifaAntt.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.TarifaAntt > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.TarifaAntt.HasValue ?? false)
                        {
                            if (transferenciasProprietario.TarifaAntt > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.TarifaAntt > 0)
                            return true;

                        break;

                    case ETipoEventoViagem.Abastecimento:

                        if (transferenciaFreteProprietarioMotorista?.Abastecimento.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.Abastecimento > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.Abastecimento.HasValue ?? false)
                        {
                            if (transferenciasProprietario.Abastecimento > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.Abastecimento > 0)
                            return true;

                        break;

                    case ETipoEventoViagem.Abono:

                        if (transferenciaFreteProprietarioMotorista?.Abono.HasValue ?? false)
                        {
                            if (transferenciaFreteProprietarioMotorista.Abono > 0)
                                return true;
                        }
                        else if (transferenciasProprietario?.Abono.HasValue ?? false)
                        {
                            if (transferenciasProprietario.Abono > 0)
                                return true;
                        }
                        else if (transferenciasGenerico.Abono > 0)
                            return true;

                        break;

                    default:
                        throw new ArgumentOutOfRangeException(nameof(eTipoEventoViagem));
                }
            }

            return false;
        }

        public bool? GetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.ValidarDocumentosViagemComDocumentosDasIntegracoes
            };

            var resultado = _parametrosEmpresaService.GetParametros<bool?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public ValidationResult SetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa, bool valor)
        {
            try
            {
                var parametrosEmpresaService = _parametrosEmpresaService;

                var resultado =
                    parametrosEmpresaService.SetParametro(EMPRESA_ID.ValidarDocumentosViagemComDocumentosDasIntegracoes,
                        valor, idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult SetReemiteCiotPadraoAlteracaoViagem(bool valor, int idEmpresa)
        {
            try
            {
                var resultado =
                    _parametrosEmpresaService.SetParametro(EMPRESA_ID.ReemiteCiotPadraoAlteracaoViagem, valor,
                        idEmpresa);

                return resultado;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public bool? GetReemiteCiotPadraoAlteracaoViagem(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.ReemiteCiotPadraoAlteracaoViagem
            };

            var resultado = _parametrosEmpresaService.GetParametros<bool?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public string GetEmailsAlertaCiotAgregado(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.EmailAvisoCiotAgregado
            };

            var resultado = _parametrosEmpresaService.GetParametros<string>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public int GetDiasCancelamentoViagem(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.DiasCancelamentoViagem
            };

            var resultado = _parametrosEmpresaService.GetParametros<decimal?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value.ToIntSafe(0) ?? 0;
        }

        public bool? GetPermiteConsultarTodasViagensEmpresa(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.PermiteConsultarTodasViagensEmpresa
            };

            var resultado = _parametrosEmpresaService.GetParametros<bool?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value;
        }

        public ValidationResult SetParametroEmpresaGridListarCnpjDespesasViagem(bool listar,int idEmmpresa)
        {
            return _parametrosEmpresaService.SetParametro(EMPRESA_ID.GridDespesasViagemListarCnpj, listar, idEmmpresa);
        }

        public bool GetParametroEmpresaGridListarCnpjDespesasViagem(int idEmpresa)
        {
            var listaEnuns = new List<EMPRESA_ID>
            {
                EMPRESA_ID.GridDespesasViagemListarCnpj
            };

            var resultado = _parametrosEmpresaService.GetParametros<bool?>(listaEnuns, idEmpresa).FirstOrDefault();

            return resultado.Value ?? false;
        }
    }
}
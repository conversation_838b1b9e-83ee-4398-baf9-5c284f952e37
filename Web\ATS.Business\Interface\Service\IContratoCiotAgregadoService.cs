﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IContratoCiotAgregadoService : IService<ContratoCiotAgregado>
    {
        IContratoCiotAgregadoRepository Repository { get; }
        ContratoCiotAgregado Get(int id);
        ContratoCiotAgregado GetWithDeclaracaoCiot(int idProprietario);
        ContratoCiotAgregado GetWithAllIncludes(int idContratoAgregado);
        ValidationResult AtualizarVeiculosContratoAgregado(ContratoCiotAgregado contratoCiotAgregado, ICollection<VeiculoModelAgregado> veiculos);
        ContratoCiotAgregado GetContratoVigente(int idEmpresa, string rntc);
        void CancelarContrato(ContratoCiotAgregado contratoCiotAgregado, string motivoCancelamento);
        AbrirContratoCiotAgregadoResultModel AbrirContratoCiotAgregado(ContratoAberturaModel contratoAberturaModel);
        List<ContratoCiotAgregado> GetContratosSemViagensParaCancelar();
        List<ContratoCiotAgregado> GetContratosExpiradosParaEncerrar();
        List<ContratoCiotAgregado> GetContratosSemViagensParaCancelamentoFuturo();
        List<ContratoCiotAgregado> GetContratosExpiradosParaEncerramentoFuturo();
        bool AnyContratoAberto(int idProprietario, int idEmpresa);
        List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa);
    }
}
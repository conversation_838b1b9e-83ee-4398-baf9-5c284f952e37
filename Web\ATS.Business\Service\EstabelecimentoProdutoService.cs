﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class EstabelecimentoProdutoService : ServiceBase, IEstabelecimentoProdutoService
    {
        private readonly IEstabelecimentoProdutoRepository _estabelecimentoProdutoRepository;

        public EstabelecimentoProdutoService(IEstabelecimentoProdutoRepository estabelecimentoProdutoRepository)
        {
            _estabelecimentoProdutoRepository = estabelecimentoProdutoRepository;
        }

        public ValidationResult Delete(List<EstabelecimentoProduto> entity)
        {
            try
            {
                foreach (var item in entity)
                {
                    var estabelecimento = _estabelecimentoProdutoRepository
                        .Find(x => x.IdProduto == item.IdProduto)
                        .FirstOrDefault();
                    _estabelecimentoProdutoRepository.Delete(estabelecimento);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

        }

        public void Update(EstabelecimentoProduto estab)
        {
            _estabelecimentoProdutoRepository.Update(estab);
        }
    }
}

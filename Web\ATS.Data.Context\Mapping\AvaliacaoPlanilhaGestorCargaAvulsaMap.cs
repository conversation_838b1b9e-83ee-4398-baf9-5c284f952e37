﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class AvaliacaoPlanilhaGestorCargaAvulsaMap  : EntityTypeConfiguration<AvaliacaoPlanilhaGestorCargaAvulsa>
    {
        public AvaliacaoPlanilhaGestorCargaAvulsaMap()
        {
            ToTable("AVALIACAO_PLANILHA_GESTOR_CARGA_AVULSA");
       
            HasKey(t => t.Id);
            Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.DataCadastro).IsRequired().HasColumnType("datetime2");
            Property(o => o.CodigoPlanilhaImportada).IsRequired().HasMaxLength(100);
            Property(o => o.Resultado).IsRequired();
        }
    }
}

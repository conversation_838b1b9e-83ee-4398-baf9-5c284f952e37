﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DespesaUsuario;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using AutoMapper;
using NLog;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Domain.Enum;

namespace ATS.Domain.Service
{
    public class DespesaUsuarioService : BaseService<IDespesaUsuarioRepository>, IDespesaUsuarioService
    {
        private readonly ILogger _logger;
        private readonly IPrestacaoContasService _prestacaoContasService;

        public DespesaUsuarioService(IDespesaUsuarioRepository repository, IUserIdentity sessionUser, IPrestacaoContasService prestacaoContasService) : base(repository, sessionUser)
        {
            _prestacaoContasService = prestacaoContasService;
            _logger = LogManager.GetCurrentClassLogger();
        }

        public BusinessResult<DespesaUsuarioAddModelResponse> Add(DespesaUsuarioAddModel model)
        {
            try
            {
                var despesa = Mapper.Map<DespesaUsuario>(model);
                despesa.DataCadastro = DateTime.Now;
                despesa.DataAtualizacao = DateTime.Now;

                var prestacaoContasAberta = _prestacaoContasService.VerificarPrestacaoContasAberta(model.IdUsuario);

                if (prestacaoContasAberta != null)
                {
                    despesa.IdPrestacaoContas = prestacaoContasAberta.Value;
                    _prestacaoContasService.GerarEventoPrestacaoConta(prestacaoContasAberta.Value, EStatusPrestacaoContasEvento.VinculoAnexoPortador);
                }

                var entity = Repository.Add(despesa);

                return BusinessResult<DespesaUsuarioAddModelResponse>.Valid(new DespesaUsuarioAddModelResponse
                {
                    IdDespesaUsuario = entity.IdDespesaUsuario
                });
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult<DespesaUsuarioAddModelResponse>.Error($"Ocorreu um erro interno: {message}");
            }
        }
        
        public IQueryable<DespesaUsuario> GetDespesaUsuario(int? idUsuario)
        {
            var retorno =  Repository.GetAll().Include(x => x.Categoria);
            
            if (idUsuario.HasValue)
                retorno = retorno.Where(x => x.IdUsuario == idUsuario);

            return retorno;
        }

        public IQueryable<DespesaUsuario> GetAll()
        {
            return Repository.GetAll();
        }
        
        public IQueryable<DespesaUsuario> GetAllWithInclude()
        {
            return Repository.GetAll().Include(x => x.Categoria);
        }
    }
}

using System;
using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioConsultaViagemRequestDTO : FiltrosGridBaseModel
    {
        public IList<Filiais> Filiais { get; set; }
        public DateTime DataInicial { get; set; }
        public DateTime DataFinal { get; set; }
        public string Pesquisa { get; set; }
        public bool IsPedagioAvulso { get; set; } = false;
    }

    public class ConsultaViagemRequestDTO
    {
        public int? IdEmpresa { get; set; }
        public IList<Filiais> Filiais { get; set; }
        public DateTime DataInicial { get; set; }
        public DateTime DataFinal { get; set; }
        public string Pesquisa { get; set; }
        public bool IsPedagioAvulso { get; set; } = false;
    }

    public class Filiais
    {
        public int Codigo { get; set; }
    }
}
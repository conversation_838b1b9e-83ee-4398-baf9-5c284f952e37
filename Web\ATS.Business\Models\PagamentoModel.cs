﻿using System.ComponentModel.DataAnnotations;
using ATS.Domain.Enum;

namespace ATS.Domain.Models
{
    [TrackChanges]
    public class PagamentoModel
    {
        public int IdViagemEvento { get; set; }

        public string Token { get; set; }

        public string DataHoraPagamento { get; set; }

        public string Evento { get; set; }

        public bool HabilitarPagamentoCartao { get; set; }

        public string PagoCartao { get; set; }

        public ETipoEventoViagem EventoInt { get; set; }

        public decimal ValorPagamento { get; set; }

        public decimal Valor { get; set; }

        public string Status { get; set; }

        public string MotoristaNome { get; set; }

        public string MotoristaCNH { get; set; }

        public string Placa { get; set; }

        public object Motivos { get; set; }

        public bool IsRejected { get; set; }

        public bool PodeMudarAnexo { get; set; }

        public bool HasAnexo { get; set; }

        public string NumeroCTE { get; set; }

        public string TipoEvento { get; set; }
    }
}

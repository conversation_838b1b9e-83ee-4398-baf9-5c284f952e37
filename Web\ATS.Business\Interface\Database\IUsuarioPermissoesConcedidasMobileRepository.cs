using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Database
{
    public interface IUsuarioPermissoesConcedidasMobileRepository: IRepository<UsuarioPermissoesConcedidasMobile>
    {
        ValidationResult Salvar(UsuarioPermissoesConcedidasMobile usuarioPermissoesConcedidasMobile);

        ValidationResult Editar(UsuarioPermissoesConcedidasMobile usuarioPermissoesConcedidasMobile);

        UsuarioPermissoesConcedidasMobile ConsultarPorUsuario(int usuarioId);
    }
}
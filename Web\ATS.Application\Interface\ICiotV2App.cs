using System;
using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Data.Repository.External.SistemaInfo.Ciot.DTOs;
using ATS.Domain.DTO.Ciot;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Validation;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Application.Interface
{
    public interface ICiotV2App : IBaseApp<ICiotV2Service>
    {
        DeclararCiotResult DeclararCiotFromIntegracaoViagem(Viagem viagem, string cnpjEmpresa,
            bool habilitarDeclaracaoCiot, bool retificarCiot, bool forcarCiotNaoEquiparado, bool? gerarCiotTacAgregado);

        AbrirContratoCiotAgregadoResultModel AbrirContratoCiotAgregado(ContratoAberturaModel requestModel,
            int idEmpresa);

        DeclararCiotResult EncerrarCiotAgregado(int idEmpresa, EncerrarContratoAgregadoModel requestModel);

        EncerradoCanceladoCiotResult EncerrarCancelarCiotAgregadoExpirados();

        RetificarContratoAgregadoResponseDto RetificarCiotAgregadoPorTela(int idEmpresa, RetificarContratoAgregadoModel requestModel);

        CancelarCiotAgregadoViagensResult CancelarCiotAgregado(int idEmpresa, int idContrato);

        object ConsultarContratosAgregado(DateTime dataInicio, DateTime dataFinal, int take, int page, 
            OrderFilters order, List<QueryFilters> filters, int? idEmpresa);

        CarregarContratoAgregadoModel CarregarContratoAgregado(int idContratoAgregado, int idEmpresa);

        CancelarCiotResult CancelarCiot(Viagem viagem);

        CancelarCiotResult CancelarCiotAgregado(CancelarContratoAgregadoModel requestModel, int idEmpresa);

        /// <summary>
        ///     Resolve o link para imprimir comprovante de contrato.
        /// </summary>
        string LinkComprovanteContrato(ImprimirComprovanteContratoAgregadoModel requestModel);

        DeclararCiotResult GetCiotResult(int idViagem, int idEmpresa);

        ConsultarFrotaTransportadorReponse ConsultarFrotaTransportador(ConsultarFrotaTransportadorRequest request);

        ConsultarSituacaoTransportadorReponse ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorRequest request);

        ConsultarSituacaoTransportadorInternalResponse EquiparadoTac(string cpfCnpjProprietario, string rntrcProprietario, string cnpjEmpresa);
        VeiculoRntrcDTO ObterVeiculoPorPlaca(ViagemCarreta carreta, int idEmpresa);
        ValidationResult Encerrar(string requestCnpjEmpresa, string requestCiot); 
        bool AlgumContratoAberto(int idProprietario, int idEmpresa);
        List<string> GetPlacasContratoAberto(int idProprietario, int idEmpresa);
        void Avisar();
        bool IsDeclaracaoTacAgregado(string placa, int idEmpresa, bool habilitarContratoCiotAgregado);
        AtualizarCiotResponse AtualizarCiot(int idDeclaracaoCiot);
        ValidationResult DesvincularCiot(Viagem viagem,Usuario usuario);

    }
}
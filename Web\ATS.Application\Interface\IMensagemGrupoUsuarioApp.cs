﻿using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IMensagemGrupoUsuarioApp : IAppBase<MensagemGrupoUsuario>
    {
        IEnumerable<MensagemGrupoUsuario> GetGruposDoUsuario(int idUsuario);
        ValidationResult DeletarGrupo(int idGrupo);
        ValidationResult AddGrupo(MensagemGrupoUsuario grupoUsuarios);
        ValidationResult AddUsuarioParaGrupo(int idGrupo, int idUsuario);
        ValidationResult RemoverUsuarioDoGrupo(int idGrupo, int idUsuario);
    }
}

﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class ViagemVirtualApp : AppBase, IViagemVirtualApp
    {
        private readonly ViagemVirtualService _service;

        public ViagemVirtualApp(ViagemVirtualService service)
        {
            _service = service;
        }

        public ValidationResult Add(ViagemVirtual viagemVirtual)
        {
            return _service.Add(viagemVirtual);
        }

        public int AddReturningId(ViagemVirtual viagemVirtual)
        {
            return _service.AddReturningId(viagemVirtual);
        }

        public ViagemVirtual Get(int id)
        {
            return _service.Get(id);
        }
    }
}

namespace ATS.Domain.Models
{
    public class PagamentosPorViagemEventoModel
    {
        public string AdiantamentoAberto { get; set; }
        public string AdiantamentoBloqueado { get; set; }
        public string AdiantamentoBaixado { get; set; }
        public string AdiantamentoCancelado { get; set; }
        public string TarifaAnttAberto { get; set; }
        public string TarifaAnttBloqueado { get; set; }
        public string TarifaAnttBaixado { get; set; }
        public string TarifaAnttCancelado { get; set; }
        public string SaldoAberto { get; set; }
        public string SaldoBloqueado { get; set; }
        public string SaldoCancelado { get; set; }
        public string SaldoBaixado { get; set; }
        public string RpaAberto { get; set; }
        public string RpaBloqueado { get; set; }
        public string RpaBaixado { get; set; }
        public string RpaCancelado { get; set; }
        public string EstadiaAberto { get; set; }
        public string EstadiaBloqueado { get; set; }
        public string EstadiaBaixado { get; set; }
        public string EstadiaCancelado { get; set; }
        public string PedagioAberto { get; set; }
        public string PedagioBloqueado { get; set; }
        public string PedagioBaixado { get; set; }
        public string PedagioCancelado { get; set; }
        public string AbastecimentoAberto { get; set; }
        public string AbastecimentoBloqueado { get; set; }
        public string AbastecimentoCancelado { get; set; }
        public string AbastecimentoBaixado { get; set; }
        public string CargaAvulsaBaixado { get; set; }
        public string CargaAvulsaCancelado { get; set; }
    }
}
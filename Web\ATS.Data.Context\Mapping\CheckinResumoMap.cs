using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class CheckinResumoMap : EntityTypeConfiguration<CheckinResumo>
    {
        public CheckinResumoMap()
        {
            ToTable("CHECKIN_RESUMO");

            HasKey(o => o.IdCheckinResumo);

            HasRequired(o => o.Checkin)
                .WithMany(o => o.Resumos)
                .HasForeignKey(o => o.IdCheckin);

            HasOptional(o => o.Usuario)
                .WithMany(o => o.CheckinResumos)
                .HasForeignKey(o => o.IdUsuario);

            HasOptional(o => o.Motorista)
                .WithMany(o => o.CheckinResumos)
                .HasForeignKey(o => o.IdMotorista);

            Property(o => o.IdViagem)
                .IsOptional();

            HasOptional(o => o.Empresa)
                .WithMany(o => o.CheckinResumos)
                .HasForeignKey(o => o.IdEmpresa);

            Property(o => o.DataHora)
                .IsRequired();

            Property(o => o.TipoEvento)
                .IsRequired();

            Property(o => o.Latitude)
                .IsRequired();

            Property(o => o.Longitude)
                .IsRequired();

            Property(o => o.CpfCnpjUsuario)
                .IsOptional()
                .HasMaxLength(14);

            HasOptional(o => o.Cidade)
                .WithMany(o => o.CheckinResumos)
                .HasForeignKey(o => o.IdCidadeCheckin);
            
            HasOptional(o => o.Viagem)
                .WithMany(o => o.CheckinResumo)
                .HasForeignKey(t => new { t.IdViagem, IdEmpresa = t.IdEmpresa });

            Property(o => o.RegiaoBrasil)
                .IsOptional();
        }
    }
}
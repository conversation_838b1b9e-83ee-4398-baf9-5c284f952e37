﻿using ATS.Domain.DTO.Campanha;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface ICampanhaService : IBaseService<ICampanhaRepository>
    {
        CampanhaConsultaResponse ConsultarAtual();
        CampanhaGridResponse ConsultarCampanhas();
        CampanhaRespostaResponse Responder(ResponderCampanhaRequest request);
        ValidationResult Integrar(IntegrarCampanhaRequest request);
        AlterarStatusCampanhaResponse AlterarStatus(AlterarStatusCampanhaRequest request);
    }
}

using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Service;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Services.ViagemServices
{
    public class ConsultasViagem : SrvBase
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IResgateCartaoAtendimentoApp _resgatarCartaoApp;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IPagamentoFreteService _pagamentoFreteService;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;

        public ConsultasViagem(IVersaoAnttLazyLoadService versaoAntt, IViagemApp viagemApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IParametrosApp parametrosApp, 
            IProprietarioApp proprietarioApp, IResgateCartaoAtendimentoApp resgatarCartaoApp, ICidadeRepository cidadeRepository, IMotoristaApp motoristaApp, IUsuarioApp usuarioApp, IPagamentoFreteService pagamentoFreteService, CartoesAppFactoryDependencies cartoesAppFactoryDependencies)
        {
            _versaoAntt = versaoAntt;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _resgatarCartaoApp = resgatarCartaoApp;
            _cidadeRepository = cidadeRepository;
            _motoristaApp = motoristaApp;
            _usuarioApp = usuarioApp;
            _pagamentoFreteService = pagamentoFreteService;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
        }

        public Retorno<IEnumerable<ViagemConsultaModel>> Consultar(ConsultarViagemRequest @params)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return ConsultarV2(@params);
                case EVersaoAntt.Versao3:
                    return ConsultarV3(@params);
                default:
                    return ConsultarV2(@params);
            }
        }
        
        private Retorno<IEnumerable<ViagemConsultaModel>> ConsultarV2(ConsultarViagemRequest @params)
        {
            try
            {
                var pagamentoService = _pagamentoFreteService;
                var viagens = _viagemApp.Consultar(@params.CPFMotorista, @params.TokenViagem, @params.DataLancamentoInicial,
                    @params.DataLancamentoFinal, @params.StatusCheckViagem, @params.StatusViagem, @params.Token, @params.CNPJAplicacao, @params.IdsViagem,@params.NumerosControle);

                var retViagens = new List<ViagemConsultaModel>();
                foreach (var viagem in viagens)
                {
                    var retItem = new ViagemConsultaModel
                    {
                        IdViagem = viagem.IdViagem,
                        IdEmpresa = viagem.IdEmpresa,
                        RazaoSocialEmpresa = viagem.Empresa?.RazaoSocial,
                        QuantidadeCargas = viagem.ViagemCargas?.Count ?? 0,
                        StatusViagem = viagem.StatusViagem,
                        PaisClienteOrigem = viagem.ClienteOrigem?.Pais?.Nome,
                        UFClienteOrigem = viagem.ClienteOrigem?.Estado?.Sigla,
                        CidadeClienteOrigem = viagem.ClienteOrigem?.Cidade?.Nome,
                        PaisClienteDestino = viagem.ClienteDestino?.Pais?.Nome,
                        UFClienteDestino = viagem.ClienteDestino?.Estado?.Sigla,
                        CidadeClienteDestino = viagem.ClienteDestino?.Cidade?.Nome,
                        PaisClienteTomador = viagem.ClienteTomador?.Pais?.Nome,
                        UFClienteTomador = viagem.ClienteTomador?.Estado?.Sigla,
                        CidadeClienteTomador = viagem.ClienteTomador?.Cidade?.Nome,
                        CPFCNPJClienteTomador = viagem.ClienteTomador?.CNPJCPF,
                        PesoChegada = viagem.PesoChegada,
                        PesoSaida = viagem.PesoSaida,
                        PesoDiferenca = viagem.PesoDiferenca,
                        ValorMercadoria = viagem.ValorMercadoria,
                        ValorDifFreteMotorista = viagem.DifFreteMotorista,
                        ValorQuebraMercadoria = viagem.ValorQuebraMercadoria,
                        DataLancamento = viagem.DataLancamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                        INSS = viagem.INSS,
                        IRRPF = viagem.IRRPF,
                        SESTSENAT = viagem.SESTSENAT,
                        NumeroDocumento = viagem.NumeroDocumento,
                        DataEmissao = viagem.DataEmissao?.ToString("yyyy-MM-dd HH:mm:ss"),
                        Produto = viagem.Produto,
                        Unidade = viagem.Unidade,
                        Quantidade = viagem.Quantidade,
                        Pedagio = _viagemApp.GetStatusPedagio(viagem),
                        ValorPedagio = viagem.ValorPedagio,
                        PedagioBaixado = viagem.PedagioBaixado,
                        NaturezaCarga = viagem.NaturezaCarga,
                        DataPrevisaoEntrega = viagem.DataPrevisaoEntrega.ToString("yyyy-MM-dd HH:mm:ss"),
                        NumeroControle = viagem.NumeroControle,
                        Placa = viagem.Placa,
                        CnpjFilial = viagem.Filial?.CNPJ
                    };

                    if (viagem.ViagemCarretas != null && viagem.ViagemCarretas.Any())
                    {
                        retItem.Carretas = viagem.ViagemCarretas.Select(c => new ViagemConsultaCarretaModel
                        {
                            Placa = c.Placa
                        }).ToList();
                    }

                    if (viagem.IdProprietario.HasValue)
                    {
                        retItem.DocumentoProprietario = viagem.Proprietario.CNPJCPF;
                        retItem.NomeProprietario = viagem.Proprietario.NomeFantasia;
                        retItem.RNTRCProprietario = viagem.Proprietario.RNTRC;
                        
                        var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, CartoesService.AuditDocIntegracao, null);

                        var produtos = new List<int>();
                        produtos.Add(cartoesApp.GetIdProdutoCartaoFretePadrao());

                        var cartaoVinculado = cartoesApp.GetCartoesVinculados(retItem.DocumentoProprietario, produtos, false);

                        if (cartaoVinculado.Status == CartaoVinculadoPessoaListResponseStatus.Sucesso && cartaoVinculado.Cartoes.Any())
                        {
                            retItem.CartaoProprietario = new CartaoIdentificacaoDto
                            {
                                Identificador = cartaoVinculado.Cartoes.FirstOrDefault()?.Identificador ?? 0,
                                Produto = cartaoVinculado.Cartoes.FirstOrDefault()?.Produto?.Id ?? 0,
                            };   
                        }
                    }

                    if (viagem.ViagemDocumentosFiscais != null && viagem.ViagemDocumentosFiscais.Any())
                    {
                        retItem.ViagemDocumentosFiscais = viagem.ViagemDocumentosFiscais.Select(c => new ViagemConsultaDocumentoFiscalModel
                        {
                            IdViagemDocumentoFiscal = c.IdViagemDocumentoFiscal,
                            NumeroDocumento = c.NumeroDocumento,
                            Serie = c.Serie,
                            
                            PesoSaida = c.PesoSaida,
                            Valor = c.Valor,
                            IdClienteOrigem = c.IdClienteOrigem,
                            IdClienteDestino = c.IdClienteDestino,
                            TipoDocumento = c.TipoDocumento,
                            Chave = c.Chave
                        }).ToList();
                    }

                    //Propriedades das regras da viagem
                    if (viagem.ViagemRegras != null && viagem.ViagemRegras.Any())
                    {
                        retItem.ViagemRegras = new List<ViagemRegrasConsultaModel>();
                        foreach (var viagemRegra in viagem.ViagemRegras)
                            retItem.ViagemRegras.Add(new ViagemRegrasConsultaModel
                            {
                                TarifaTonelada = viagemRegra.TarifaTonelada,
                                TaxaAntecipacao = viagemRegra.TaxaAntecipacao,
                                TipoQuebraMercadoria = viagemRegra.TipoQuebraMercadoria,
                                ToleranciaPeso = viagemRegra.ToleranciaPeso
                            });
                    }

                    var viagemEstabelecimento = viagem.ViagemEstabelecimentos?.ToList();
                    if (viagemEstabelecimento?.Any() == true)
                    {
                        retItem.ViagemEstabelecimentos = new List<ViagemEstabelecimentoConsultaModel>();
                        foreach (var estabelecimento in viagemEstabelecimento)
                            retItem.ViagemEstabelecimentos.Add(new ViagemEstabelecimentoConsultaModel
                            {
                                IdViagemEstabelecimento = estabelecimento.IdViagemEstabelecimento,
                                IdEstabelecimento = estabelecimento.IdEstabelecimento,
                                IdViagem = estabelecimento.IdViagem,
                                TipoEventoViagem = estabelecimento.TipoEventoViagem
                            });
                    }

                    var viagemEventos = viagem.ViagemEventos?.ToList();
                    if (viagemEventos?.Any() == true)
                    {
                        retItem.ViagemEventos = new List<ViagemEventoConsultaModel>();
                        foreach (var viagemEvento in viagemEventos)
                        {
                            var somarPedagio = pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagemEvento.Viagem);

                            var evento = new ViagemEventoConsultaModel
                            {
                                IdViagem = viagemEvento.IdViagem,
                                IdViagemEvento = viagemEvento.IdViagemEvento,
                                IdEstabelecimentoBase = viagemEvento.IdEstabelecimentoBase,
                                Token = viagemEvento.Token,
                                Status = viagemEvento.Status,
                                TipoEvento = viagemEvento.TipoEventoViagem,
                                DataPagamento = viagemEvento.DataHoraPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                                HoraPagamento = viagemEvento.DataHoraPagamento?.TimeOfDay.ToString(@"hh\:mm\:ss"),
                                IdProtocolo = viagemEvento.IdProtocolo,
                                NumeroRecibo = viagemEvento.NumeroRecibo,
                                NumeroControle = viagemEvento.NumeroControle,
                                ValorPagamento = viagemEvento.ValorPagamento,
                                ValorBruto = viagemEvento.ValorBruto,
                                ValorTotalPagamento = (viagemEvento.ValorTotalPagamento ?? 0) + (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                                ValorQuebraAbonada = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo
                                    ? viagemEvento.Viagem.ValorQuebraMercadoria.GetValueOrDefault(0) + viagemEvento.Viagem.DifFreteMotorista.GetValueOrDefault(0)
                                    : (decimal?) null,
                                DataValidade = viagemEvento.DataValidade?.ToString("yyyy-MM-dd HH:mm:ss"),
                                INSS = viagemEvento.INSS,
                                IRRPF = viagemEvento.IRRPF,
                                SESTSENAT = viagemEvento.SESTSENAT,
                                Instrucao = viagemEvento.Instrucao,
                                HabilitarPagamentoCartao = viagemEvento.HabilitarPagamentoCartao,
                                PesoChegada = viagemEvento.Viagem.PesoChegada,
                                PesoDiferenca = viagemEvento.Viagem.PesoDiferenca,
                                ValorDifFreteMotorista = viagemEvento.Viagem.DifFreteMotorista,
                                ValorQuebraMercadoria = viagemEvento.Viagem.ValorQuebraMercadoria
                            };

                            var viagemDocumentos = viagemEvento.ViagemDocumentos.ToList();
                            if (viagemDocumentos.Any())
                            {
                                evento.ViagemDocumentos = new List<ViagemDocumentoConsultaModel>();
                                foreach (var viagemDocumento in viagemDocumentos)
                                    evento.ViagemDocumentos.Add(new ViagemDocumentoConsultaModel
                                    {
                                        Descricao = viagemDocumento.Descricao,
                                        TipoEvento = viagemDocumento.TipoEvento,
                                        IdEvento = viagemDocumento.IdViagemEvento,
                                        IdDocumento = viagemDocumento.IdViagemDocumento,
                                        NumeroDocumento = viagemDocumento.NumeroDocumento,
                                        ObrigaAnexo = viagemDocumento.ObrigaAnexo,
                                        TipoDocumento = viagemDocumento.TipoDocumento,
                                        ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal
                                    });
                            }

                            var viagemValorAdd = viagemEvento.ViagemValoresAdicionais.ToList();
                            if (viagemValorAdd.Any())
                            {
                                evento.ViagemOutrosDescontos = new List<ViagemValorAdicionalConsultaModel>();
                                foreach (var viagemValorAdicional in viagemValorAdd.Where(x => x.Tipo == ETipoValorAdicional.Desconto))
                                    evento.ViagemOutrosDescontos.Add(new ViagemValorAdicionalConsultaModel
                                    {
                                        Descricao = viagemValorAdicional.Descricao,
                                        Valor = viagemValorAdicional.Valor,
                                        NumeroDocumento = viagemValorAdicional.NumeroDocumento,
                                        IdViagemOutrosDescontos = viagemValorAdicional.IdViagemValorAdicional
                                    });

                                evento.ViagemOutrosAcrescimos = new List<ViagemValorAdicionalConsultaModel>();
                                foreach (var item in viagemValorAdd.Where(x => x.Tipo == ETipoValorAdicional.Acrescimo))
                                    evento.ViagemOutrosAcrescimos.Add(new ViagemValorAdicionalConsultaModel
                                    {
                                        Descricao = item.Descricao,
                                        Valor = item.Valor,
                                        NumeroDocumento = item.NumeroDocumento,
                                        IdViagemOutrosDescontos = item.IdViagemValorAdicional
                                    });
                            }

                            retItem.ViagemEventos.Add(evento);
                        }
                    }

                    if (viagem.ViagemChecks != null && viagem.ViagemChecks.Any())
                        retItem.StatusUltimoCheckViagem = viagem.ViagemChecks.Last().Status;

                    if (viagem.HabilitarDeclaracaoCiot)
                    {
                        retItem.CIOT = _ciotV2App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);
                    }

                    retViagens.Add(retItem);
                }

                if(!retViagens.Any())
                    return new Retorno<IEnumerable<ViagemConsultaModel>>(false, "Viagem não encontrada",retViagens);

                return new Retorno<IEnumerable<ViagemConsultaModel>>(true, retViagens);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<IEnumerable<ViagemConsultaModel>>(false, e.Message, null);
            }
        }
        
        private Retorno<IEnumerable<ViagemConsultaModel>> ConsultarV3(ConsultarViagemRequest @params)
        {
            try
            {
                var pagamentoService = _pagamentoFreteService;
                var viagens = _viagemApp.Consultar(@params.CPFMotorista, @params.TokenViagem,
                    @params.DataLancamentoInicial,
                    @params.DataLancamentoFinal, @params.StatusCheckViagem, @params.StatusViagem, @params.Token,
                    @params.CNPJAplicacao, @params.IdsViagem,@params.NumerosControle);

                var retViagens = new List<ViagemConsultaModel>();
                foreach (var viagem in viagens)
                {
                    var retItem = new ViagemConsultaModel
                    {
                        IdViagem = viagem.IdViagem,
                        IdEmpresa = viagem.IdEmpresa,
                        RazaoSocialEmpresa = viagem.Empresa.RazaoSocial,
                        QuantidadeCargas = viagem.ViagemCargas.Count,
                        StatusViagem = viagem.StatusViagem,
                        PaisClienteOrigem = viagem.ClienteOrigem.Pais.Nome,
                        UFClienteOrigem = viagem.ClienteOrigem.Estado.Sigla,
                        CidadeClienteOrigem = viagem.ClienteOrigem.Cidade.Nome,
                        PaisClienteDestino = viagem.ClienteDestino.Pais.Nome,
                        UFClienteDestino = viagem.ClienteDestino.Estado.Sigla,
                        CidadeClienteDestino = viagem.ClienteDestino.Cidade.Nome,
                        PaisClienteTomador = viagem.ClienteTomador?.Pais?.Nome,
                        UFClienteTomador = viagem.ClienteTomador?.Estado?.Sigla,
                        CidadeClienteTomador = viagem.ClienteTomador?.Cidade?.Nome,
                        CPFCNPJClienteTomador = viagem.ClienteTomador?.CNPJCPF,
                        PesoChegada = viagem.PesoChegada,
                        PesoSaida = viagem.PesoSaida,
                        PesoDiferenca = viagem.PesoDiferenca,
                        ValorMercadoria = viagem.ValorMercadoria,
                        ValorDifFreteMotorista = viagem.DifFreteMotorista,
                        ValorQuebraMercadoria = viagem.ValorQuebraMercadoria,
                        DataLancamento = viagem.DataLancamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                        INSS = viagem.INSS,
                        IRRPF = viagem.IRRPF,
                        SESTSENAT = viagem.SESTSENAT,
                        NumeroDocumento = viagem.NumeroDocumento,
                        DataEmissao = viagem.DataEmissao?.ToString("yyyy-MM-dd HH:mm:ss"),
                        Produto = viagem.Produto,
                        Unidade = viagem.Unidade,
                        Quantidade = viagem.Quantidade,
                        Pedagio = _viagemApp.GetStatusPedagio(viagem),
                        ValorPedagio = viagem.ValorPedagio,
                        PedagioBaixado = viagem.PedagioBaixado,
                        NaturezaCarga = viagem.NaturezaCarga,
                        DataPrevisaoEntrega = viagem.DataPrevisaoEntrega.ToString("yyyy-MM-dd HH:mm:ss"),
                        NumeroControle = viagem.NumeroControle,
                        Placa = viagem.Placa,
                        CepOrigem = viagem.CepOrigem,
                        CepDestino = viagem.CepDestino,
                        CodigoTipoCarga = viagem.CodigoTipoCarga,
                        DistanciaViagem = viagem.DistanciaViagem,
                        DadosPagamento = new ViagemDadosPagamentoConsultaModel
                        {
                            FormaPagamento = viagem.FormaPagamento,
                            CodigoBacen = viagem.ViagemPagamentoConta?.CodigoBacenBanco,
                            Agencia = viagem.ViagemPagamentoConta?.Agencia,
                            Conta = viagem.ViagemPagamentoConta?.Conta
                        }
                    };

                    if (viagem.ViagemCarretas != null && viagem.ViagemCarretas.Any())
                    {
                        retItem.Carretas = viagem.ViagemCarretas.Select(c => new ViagemConsultaCarretaModel
                        {
                            Placa = c.Placa
                        }).ToList();
                    }

                    if (viagem.IdProprietario.HasValue)
                    {
                        retItem.DocumentoProprietario = viagem.Proprietario.CNPJCPF;
                        retItem.NomeProprietario = viagem.Proprietario.NomeFantasia;
                        retItem.RNTRCProprietario = viagem.Proprietario.RNTRC;

                        var cartoesApp =
                            CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, CartoesService.AuditDocIntegracao, null);

                        var produtos = new List<int>();
                        produtos.Add(cartoesApp.GetIdProdutoCartaoFretePadrao());

                        var cartaoVinculado =
                            cartoesApp.GetCartoesVinculados(retItem.DocumentoProprietario, produtos, false);

                        if (cartaoVinculado.Status == CartaoVinculadoPessoaListResponseStatus.Sucesso &&
                            cartaoVinculado.Cartoes.Any())
                        {
                            retItem.CartaoProprietario = new CartaoIdentificacaoDto
                            {
                                Identificador = cartaoVinculado.Cartoes.FirstOrDefault()?.Identificador ?? 0,
                                Produto = cartaoVinculado.Cartoes.FirstOrDefault()?.Produto?.Id ?? 0,
                            };
                        }
                    }

                    if (viagem.ViagemDocumentosFiscais != null && viagem.ViagemDocumentosFiscais.Any())
                    {
                        retItem.ViagemDocumentosFiscais = viagem.ViagemDocumentosFiscais.Select(c =>
                            new ViagemConsultaDocumentoFiscalModel
                            {
                                IdViagemDocumentoFiscal = c.IdViagemDocumentoFiscal,
                                NumeroDocumento = c.NumeroDocumento,
                                Serie = c.Serie,
                                PesoSaida = c.PesoSaida,
                                Valor = c.Valor,
                                Chave = c.Chave,
                                IdClienteOrigem = c.IdClienteOrigem,
                                IdClienteDestino = c.IdClienteDestino,
                                TipoDocumento = c.TipoDocumento
                            }).ToList();
                    }

                    //Propriedades das regras da viagem
                    if (viagem.ViagemRegras != null && viagem.ViagemRegras.Any())
                    {
                        retItem.ViagemRegras = new List<ViagemRegrasConsultaModel>();
                        foreach (var viagemRegra in viagem.ViagemRegras)
                            retItem.ViagemRegras.Add(new ViagemRegrasConsultaModel
                            {
                                TarifaTonelada = viagemRegra.TarifaTonelada,
                                TaxaAntecipacao = viagemRegra.TaxaAntecipacao,
                                TipoQuebraMercadoria = viagemRegra.TipoQuebraMercadoria,
                                ToleranciaPeso = viagemRegra.ToleranciaPeso
                            });
                    }

                    var viagemEstabelecimento = viagem.ViagemEstabelecimentos?.ToList();
                    if (viagemEstabelecimento?.Any() == true)
                    {
                        retItem.ViagemEstabelecimentos = new List<ViagemEstabelecimentoConsultaModel>();
                        foreach (var estabelecimento in viagemEstabelecimento)
                            retItem.ViagemEstabelecimentos.Add(new ViagemEstabelecimentoConsultaModel
                            {
                                IdViagemEstabelecimento = estabelecimento.IdViagemEstabelecimento,
                                IdEstabelecimento = estabelecimento.IdEstabelecimento,
                                IdViagem = estabelecimento.IdViagem,
                                TipoEventoViagem = estabelecimento.TipoEventoViagem
                            });
                    }

                    var viagemEventos = viagem.ViagemEventos?.ToList();
                    if (viagemEventos?.Any() == true)
                    {
                        retItem.ViagemEventos = new List<ViagemEventoConsultaModel>();
                        foreach (var viagemEvento in viagemEventos)
                        {
                            var somarPedagio =
                                pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento,
                                    viagemEvento.Viagem);

                            var evento = new ViagemEventoConsultaModel
                            {
                                IdViagem = viagemEvento.IdViagem,
                                IdViagemEvento = viagemEvento.IdViagemEvento,
                                IdEstabelecimentoBase = viagemEvento.IdEstabelecimentoBase,
                                Token = viagemEvento.Token,
                                Status = viagemEvento.Status,
                                TipoEvento = viagemEvento.TipoEventoViagem,
                                DataPagamento = viagemEvento.DataHoraPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                                HoraPagamento = viagemEvento.DataHoraPagamento?.TimeOfDay.ToString(@"hh\:mm\:ss"),
                                IdProtocolo = viagemEvento.IdProtocolo,
                                NumeroRecibo = viagemEvento.NumeroRecibo,
                                NumeroControle = viagemEvento.NumeroControle,
                                ValorPagamento = viagemEvento.ValorPagamento,
                                ValorBruto = viagemEvento.ValorBruto,
                                ValorTotalPagamento = (viagemEvento.ValorTotalPagamento ?? 0) +
                                                      (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                                ValorQuebraAbonada = viagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo
                                    ? viagemEvento.Viagem.ValorQuebraMercadoria.GetValueOrDefault(0) +
                                      viagemEvento.Viagem.DifFreteMotorista.GetValueOrDefault(0)
                                    : (decimal?) null,
                                DataValidade = viagemEvento.DataValidade?.ToString("yyyy-MM-dd HH:mm:ss"),
                                INSS = viagemEvento.INSS,
                                IRRPF = viagemEvento.IRRPF,
                                SESTSENAT = viagemEvento.SESTSENAT,
                                Instrucao = viagemEvento.Instrucao,
                                HabilitarPagamentoCartao = viagemEvento.HabilitarPagamentoCartao,
                                PesoChegada = viagemEvento.Viagem.PesoChegada,
                                PesoDiferenca = viagemEvento.Viagem.PesoDiferenca,
                                ValorDifFreteMotorista = viagemEvento.Viagem.DifFreteMotorista,
                                ValorQuebraMercadoria = viagemEvento.Viagem.ValorQuebraMercadoria
                            };

                            var viagemDocumentos = viagemEvento.ViagemDocumentos.ToList();
                            if (viagemDocumentos.Any())
                            {
                                evento.ViagemDocumentos = new List<ViagemDocumentoConsultaModel>();
                                foreach (var viagemDocumento in viagemDocumentos)
                                    evento.ViagemDocumentos.Add(new ViagemDocumentoConsultaModel
                                    {
                                        Descricao = viagemDocumento.Descricao,
                                        TipoEvento = viagemDocumento.TipoEvento,
                                        IdEvento = viagemDocumento.IdViagemEvento,
                                        IdDocumento = viagemDocumento.IdViagemDocumento,
                                        NumeroDocumento = viagemDocumento.NumeroDocumento,
                                        ObrigaAnexo = viagemDocumento.ObrigaAnexo,
                                        TipoDocumento = viagemDocumento.TipoDocumento,
                                        ObrigaDocOriginal = viagemDocumento.ObrigaDocOriginal
                                    });
                            }

                            var viagemValorAdd = viagemEvento.ViagemValoresAdicionais.ToList();
                            if (viagemValorAdd.Any())
                            {
                                evento.ViagemOutrosDescontos = new List<ViagemValorAdicionalConsultaModel>();
                                foreach (var viagemValorAdicional in viagemValorAdd.Where(x =>
                                    x.Tipo == ETipoValorAdicional.Desconto))
                                    evento.ViagemOutrosDescontos.Add(new ViagemValorAdicionalConsultaModel
                                    {
                                        Descricao = viagemValorAdicional.Descricao,
                                        Valor = viagemValorAdicional.Valor,
                                        NumeroDocumento = viagemValorAdicional.NumeroDocumento,
                                        IdViagemOutrosDescontos = viagemValorAdicional.IdViagemValorAdicional
                                    });

                                evento.ViagemOutrosAcrescimos = new List<ViagemValorAdicionalConsultaModel>();
                                foreach (var item in viagemValorAdd.Where(x => x.Tipo == ETipoValorAdicional.Acrescimo))
                                    evento.ViagemOutrosAcrescimos.Add(new ViagemValorAdicionalConsultaModel
                                    {
                                        Descricao = item.Descricao,
                                        Valor = item.Valor,
                                        NumeroDocumento = item.NumeroDocumento,
                                        IdViagemOutrosDescontos = item.IdViagemValorAdicional
                                    });
                            }

                            retItem.ViagemEventos.Add(evento);
                        }
                    }

                    if (viagem.ViagemChecks != null && viagem.ViagemChecks.Any())
                        retItem.StatusUltimoCheckViagem = viagem.ViagemChecks.Last().Status;

                    if (viagem.HabilitarDeclaracaoCiot)
                    {
                        retItem.CIOT = _ciotV3App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);

                        if (retItem.CIOT == null)
                            retItem.CIOT = new DeclararCiotResult
                            {
                                Declarado = false, Mensagem = viagem.MensagemDeclaracaoCiot,
                                Resultado = EResultadoDeclaracaoCiot.Erro
                            };

                        retItem.CIOT.DadosAntt = new DeclararCiotResult.DadosAnttDto
                        {
                            AltoDesempenho = viagem.AltoDesempenho,
                            DestinacaoComercial = viagem.DestinacaoComercial,
                            FreteRetorno = viagem.FreteRetorno,
                            CepRetorno = viagem.CepRetorno,
                            DistanciaRetorno = viagem.DistanciaRetorno
                        };
                    }

                    retViagens.Add(retItem);
                }

                return new Retorno<IEnumerable<ViagemConsultaModel>>(true, retViagens);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<IEnumerable<ViagemConsultaModel>>(false, e.Message, null);
            }
        }
    }
}
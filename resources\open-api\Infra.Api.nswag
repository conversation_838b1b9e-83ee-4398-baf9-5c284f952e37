﻿{
  "runtime": "WinX64",
  "defaultVariables": null,
  "swaggerGenerator": {
    "fromSwagger": {
      "json": "{\r\n  \"swagger\": \"2.0\",\r\n  \"info\": {\r\n    \"version\": \"v1\",\r\n    \"title\": \"/Infra/Api\"\r\n  },\r\n  \"basePath\": \"/Infra/Api\",\r\n  \"paths\": {\r\n    \"/LogIntegracoes\": {\r\n      \"put\": {\r\n        \"tags\": [\r\n          \"LogIntegracaoApi\"\r\n        ],\r\n        \"summary\": \"Atualizar log com os dados de resposta\",\r\n        \"operationId\": \"LogIntegracoesPut\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"log\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/LogIntegracaoUpdateApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      },\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"LogIntegracaoApi\"\r\n        ],\r\n        \"summary\": \"Inserir log de integrações\",\r\n        \"operationId\": \"LogIntegracoesPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"log\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/LogIntegracaoInsertApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Notificacao/Webhook\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"NotificacaoApi\"\r\n        ],\r\n        \"summary\": \"Inserir Notificacao de WebHook\",\r\n        \"operationId\": \"NotificacaoWebhookPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"notificacao\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/NotificacaoWebhookApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Notificacao/Email\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"NotificacaoApi\"\r\n        ],\r\n        \"summary\": \"Inserir Notificacao de Email\",\r\n        \"operationId\": \"NotificacaoEmailPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"notificacao\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/NotificacaoEmailApiRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Notificacao/Sms\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"NotificacaoApi\"\r\n        ],\r\n        \"summary\": \"Inserir Notificacao de Sms\",\r\n        \"operationId\": \"NotificacaoSmsPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"notificacao\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/NotificacaoSmsBusRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"/Notificacao/Push\": {\r\n      \"post\": {\r\n        \"tags\": [\r\n          \"NotificacaoApi\"\r\n        ],\r\n        \"summary\": \"Inserir Notificacao de Push\",\r\n        \"operationId\": \"NotificacaoPushPost\",\r\n        \"consumes\": [\r\n          \"application/json-patch+json\",\r\n          \"application/json\",\r\n          \"text/json\",\r\n          \"application/*+json\",\r\n          \"application/xml\",\r\n          \"text/xml\",\r\n          \"application/*+xml\"\r\n        ],\r\n        \"produces\": [],\r\n        \"parameters\": [\r\n          {\r\n            \"name\": \"notificacao\",\r\n            \"in\": \"body\",\r\n            \"required\": false,\r\n            \"schema\": {\r\n              \"$ref\": \"#/definitions/NotificacaoPushBusRequest\"\r\n            }\r\n          },\r\n          {\r\n            \"name\": \"x-auth-token\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Token de identificação da aplicação + empresa + administradora\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-doc\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Documento (CPF/CNPJ) do usuário da aplicação consumidora. Informação para auditoria, não representa um login da aplicação.\",\r\n            \"required\": true,\r\n            \"type\": \"string\"\r\n          },\r\n          {\r\n            \"name\": \"x-audit-user-name\",\r\n            \"in\": \"header\",\r\n            \"description\": \"Nome do usuário da aplicação consumidora (Sem caracteres especiais [Suportado: a-Z, 0-9, -]). Informação para auditoria, não representa um login da aplicação\",\r\n            \"required\": false,\r\n            \"type\": \"string\"\r\n          }\r\n        ],\r\n        \"responses\": {\r\n          \"200\": {\r\n            \"description\": \"Success\"\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  \"definitions\": {\r\n    \"LogIntegracaoInsertApiRequest\": {\r\n      \"description\": \"Dados para persistência do log com conteúdo e informações da requisição efetuada\",\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"uuid\",\r\n          \"description\": \"Identificador único da operação. Pré-inicializado com o valor Guid.NewGuid()\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"parentId\": {\r\n          \"format\": \"uuid\",\r\n          \"description\": \"Em execuções em cadeia de microserviços, neste campo será armazenado o Id do log que originou o log atual\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"rootId\": {\r\n          \"format\": \"uuid\",\r\n          \"description\": \"Em execuções em cadeia de microserviços, neste campo será armazenado o Id do log raiz que originou a execução em cadeia até inserir o log atual\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"nivel\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Nível do micros erviço executando o processo, indica por quantas aplicações diferentes a requisição já passou.\\r\\nExemplo:\\r\\n0 = Camanda web processando\\r\\n1 = Camada de serviços da plataforma processando\\r\\n2 = Microserviço específico da processadora de cartões processando\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"microServico\": {\r\n          \"description\": \"Identificador do recurso sendo processado (Exemplo: Cartao, Ciot, ATS).\\r\\nEsta informação é o micro serviço que está esta recebendo/enviando o comando de integração.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"description\": \"Nome da aplicação que capturou a requisição, ou que está enviando o comando de saída (Exemplo: SistemaInfo.Ciot.Api.exe, SistemaInfo.Ciot.Service.exe, etc).\\r\\nEsta informação é o nome do exe do micro serviço que está esta recebendo/enviando o comando de integração.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"basePath\": {\r\n          \"description\": \"URL base para fornecer recurso web (Exemplo: /Cartoes/Api, /Ciot/Api)\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"metodo\": {\r\n          \"description\": \"Rota da aplicação executada para executar ação, ou nome da fila do RabbitMQ (Exemplo: /Operacoes/ConsultarExtrato, /Operacoes/VincularPortador, Cartao.AtivarVirtual.Request, Biz.ConsultaExtrato.Request)\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"verbo\": {\r\n          \"description\": \"get, post, put, etc\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"contentyType\": {\r\n          \"description\": \"Formato de dados da integração: application/json, application/xml\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"direcao\": {\r\n          \"description\": \"Input / Output\",\r\n          \"enum\": [\r\n            \"Input\",\r\n            \"Output\"\r\n          ],\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"description\": \"Data e hora da requisição recepcionada pelo serviço específico\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"requisicao\": {\r\n          \"description\": \"Cópia do comando de requisição\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"headerRequisicao\": {\r\n          \"description\": \"Headers da requisição\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"ipRequisicao\": {\r\n          \"description\": \"Para Input: IP do client que está comunicando com o server\\r\\nPara Output: IP ou Link destino da comunicação\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"ipServidor\": {\r\n          \"description\": \"IP do servidor que recepcionou a integração\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"hostNameServidor\": {\r\n          \"description\": \"HostName do servidor que recepcionou a integração\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"description\": \"Informação adicional para vincular no log\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"handlerClassName\": {\r\n          \"description\": \"Nome da classe que processou a operação\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"handlerMethodName\": {\r\n          \"description\": \"Nome do método que processou a operação\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"LogIntegracaoUpdateApiRequest\": {\r\n      \"description\": \"Dados para persistência do log com conteúdo e informações da resposta processada\",\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"uuid\",\r\n          \"description\": \"Íd do log gerado para armazenar a requisição\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataResposta\": {\r\n          \"format\": \"date-time\",\r\n          \"description\": \"Data e hora da resposta\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"resposta\": {\r\n          \"description\": \"Cópia do comando de resposta\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"headerResposta\": {\r\n          \"description\": \"Header da resposta\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"statusCodeResposta\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Código da resposta (Http 200, 404, 500, entre outros)\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"handlerClassName\": {\r\n          \"description\": \"Nome da classe que processou a operação\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"handlerMethodName\": {\r\n          \"description\": \"Nome do método que processou a operação\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoWebhookApiRequest\": {\r\n      \"description\": \"Configuração da notificação de webhook\",\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"endpoint\": {\r\n          \"description\": \"Link destino para executar a integração\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"verbo\": {\r\n          \"description\": \"Verbo HTTP para executar a integração (POST, GET, PUT, outros)\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"requisicao\": {\r\n          \"description\": \"Conteúdo para transmitir no body da notificação\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"headers\": {\r\n          \"description\": \"Conteúdo para transmitir no header da notificação\",\r\n          \"type\": \"object\",\r\n          \"additionalProperties\": {\r\n            \"type\": \"string\"\r\n          }\r\n        },\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"description\": \"Data e hora que foi solicitado o envio da notificação. Deve ser indicado a hora do servidor do client.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"description\": \"Informações adicionais para listar no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"description\": \"Identificação da aplicação que solicitou o envio da notificação.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacaoDestino\": {\r\n          \"description\": \"Identificação da aplicação destino da notificação.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tempo\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Tempo de vida em horas para reenvio da notificação em caso de falhas.\\r\\nApós este periodo o serviço na tentará mais reenviar automaticamente.\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaApiRequest\",\r\n          \"description\": \"Configurações de como se comportar durante as falhas de notificações\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoPoliticaFalhaApiRequest\": {\r\n      \"description\": \"Configuração em cenários de falhas no envio da notificação\",\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"minutosParaNovaTentativa\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Minutos para aguardar entre novas tentativas de reenvio da notificação.\\r\\nPadrão: 1 minuto.\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"minutosParaMonitorar\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Tempo de monitoramento das falhas pelo serviço de notificação.\\r\\nO serviço tentará reenviar uma notificação com falha durante X minutos, após este tempo sem sucesso, a mesma terá seu status alterado para \\\"Expirada\\\".\\r\\nPadrão: 4 horas (240 minutos).\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"quantidadeMaximaTentativasReenvio\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Quantidade máxima de tentativas de reenvio da notificação com falha.\\r\\nSe o serviço falhas X vezes na tentativa de notificação, a mesma terá seu status alterado para \\\"Expirada\\\".\\r\\nPadrão: null - Recurso desabilitado.\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"alertas\": {\r\n          \"description\": \"Configurações de envio de alertas a responsáveis em caso de falhas\",\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaAlertasApiRequest\"\r\n          },\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoPoliticaFalhaAlertasApiRequest\": {\r\n      \"description\": \"Configurações de envio de alertas a responsáveis em caso de falhas\",\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"falhasParaEnviarAlerta\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Enviar alerta ao atingir esta quantidade de tentativas.\\r\\nAo ocorrer a falha de número X, envia o alerta.\\r\\nCaso a mensagem expirar antes de chegar a este número de falhas, o alerta é executado também.\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"configuracao\": {\r\n          \"$ref\": \"#/definitions/NotificacaoBaseApiRequest\",\r\n          \"description\": \"Configuração do alerta a enviar\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoBaseApiRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"description\": \"Data e hora que foi solicitado o envio da notificação. Deve ser indicado a hora do servidor do client.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"description\": \"Informações adicionais para listar no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"description\": \"Identificação da aplicação que solicitou o envio da notificação.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacaoDestino\": {\r\n          \"description\": \"Identificação da aplicação destino da notificação.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tempo\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Tempo de vida em horas para reenvio da notificação em caso de falhas.\\r\\nApós este periodo o serviço na tentará mais reenviar automaticamente.\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaApiRequest\",\r\n          \"description\": \"Configurações de como se comportar durante as falhas de notificações\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoEmailApiRequest\": {\r\n      \"description\": \"Configuração da notificação de e-mail\",\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"smtp\": {\r\n          \"$ref\": \"#/definitions/NotificacaoEmailSmtpApiRequest\"\r\n        },\r\n        \"message\": {\r\n          \"$ref\": \"#/definitions/NotificacaoEmailMessageApiRequest\"\r\n        },\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"description\": \"Data e hora que foi solicitado o envio da notificação. Deve ser indicado a hora do servidor do client.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"description\": \"Informações adicionais para listar no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"description\": \"Identificação da aplicação que solicitou o envio da notificação.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacaoDestino\": {\r\n          \"description\": \"Identificação da aplicação destino da notificação.\\r\\nUtilizado para apresentação no painel de gerenciamento de notificações.\\r\\nUtilizado apenas para carater informativo para facilitar auditorias.\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"tempo\": {\r\n          \"format\": \"int32\",\r\n          \"description\": \"Tempo de vida em horas para reenvio da notificação em caso de falhas.\\r\\nApós este periodo o serviço na tentará mais reenviar automaticamente.\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaApiRequest\",\r\n          \"description\": \"Configurações de como se comportar durante as falhas de notificações\",\r\n          \"readOnly\": true\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoEmailSmtpApiRequest\": {\r\n      \"required\": [\r\n        \"enableSsl\",\r\n        \"useDefaultCredentials\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"host\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"port\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"userName\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"password\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"enableSsl\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"useDefaultCredentials\": {\r\n          \"type\": \"boolean\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoEmailMessageApiRequest\": {\r\n      \"required\": [\r\n        \"isBodyHtml\",\r\n        \"priority\"\r\n      ],\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"from\": {\r\n          \"$ref\": \"#/definitions/NotificacaoEmailAddressApiRequest\"\r\n        },\r\n        \"to\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/NotificacaoEmailAddressApiRequest\"\r\n          }\r\n        },\r\n        \"copy\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/NotificacaoEmailAddressApiRequest\"\r\n          }\r\n        },\r\n        \"hiddenCopy\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/NotificacaoEmailAddressApiRequest\"\r\n          }\r\n        },\r\n        \"attachments\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/NotificacaoEmailAttachmentApiRequest\"\r\n          }\r\n        },\r\n        \"subject\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"subjectEncoding\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"body\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"bodyEncoding\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"isBodyHtml\": {\r\n          \"type\": \"boolean\"\r\n        },\r\n        \"priority\": {\r\n          \"enum\": [\r\n            \"Normal\",\r\n            \"Low\",\r\n            \"High\"\r\n          ],\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoEmailAddressApiRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"address\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"displayName\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoEmailAttachmentApiRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"contentBase64\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"name\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"mediaType\": {\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoSmsBusRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"tipo\": {\r\n          \"enum\": [\r\n            \"Webhook\",\r\n            \"Email\",\r\n            \"Sms\",\r\n            \"Push\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"id\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacaoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaBusRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoPoliticaFalhaBusRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"minutosParaNovaTentativa\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"minutosParaMonitorar\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"quantidadeMaximaTentativasReenvio\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"alertas\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaAlertaBusRequest\"\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoPoliticaFalhaAlertaBusRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"falhasParaEnviarAlerta\": {\r\n          \"format\": \"int32\",\r\n          \"type\": \"integer\"\r\n        },\r\n        \"configuracao\": {\r\n          \"$ref\": \"#/definitions/INotificacaoBaseBusRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"INotificacaoBaseBusRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"tipo\": {\r\n          \"enum\": [\r\n            \"Webhook\",\r\n            \"Email\",\r\n            \"Sms\",\r\n            \"Push\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacaoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaBusRequest\"\r\n        }\r\n      }\r\n    },\r\n    \"NotificacaoPushBusRequest\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"tipo\": {\r\n          \"enum\": [\r\n            \"Webhook\",\r\n            \"Email\",\r\n            \"Sms\",\r\n            \"Push\"\r\n          ],\r\n          \"type\": \"string\",\r\n          \"readOnly\": true\r\n        },\r\n        \"id\": {\r\n          \"format\": \"uuid\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"dataRequisicao\": {\r\n          \"format\": \"date-time\",\r\n          \"type\": \"string\"\r\n        },\r\n        \"infoAdicional\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacao\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"aplicacaoDestino\": {\r\n          \"type\": \"string\"\r\n        },\r\n        \"politicaFalha\": {\r\n          \"$ref\": \"#/definitions/NotificacaoPoliticaFalhaBusRequest\"\r\n        }\r\n      }\r\n    }\r\n  }\r\n}",
      "url": "http://*************/Infra/Api/Swagger/v1/swagger.json",
      "output": null
    }
  },
  "codeGenerators": {
    "swaggerToCSharpClient": {
      "clientBaseClass": "SistemaInfoMicroServiceBaseClient",
      "configurationClass": "SistemaInfoMicroServiceClientParams",
      "generateClientClasses": true,
      "generateClientInterfaces": false,
      "generateDtoTypes": true,
      "injectHttpClient": false,
      "disposeHttpClient": true,
      "protectedMethods": [],
      "generateExceptionClasses": true,
      "exceptionClass": "SwaggerException",
      "wrapDtoExceptions": true,
      "useHttpClientCreationMethod": true,
      "httpClientType": "System.Net.Http.HttpClient",
      "useHttpRequestMessageCreationMethod": true,
      "useBaseUrl": true,
      "generateBaseUrlProperty": true,
      "generateSyncMethods": true,
      "exposeJsonSerializerSettings": false,
      "clientClassAccessModifier": "public",
      "typeAccessModifier": "public",
      "generateContractsOutput": false,
      "contractsNamespace": null,
      "contractsOutputFilePath": null,
      "parameterDateTimeFormat": "s",
      "generateUpdateJsonSerializerSettingsMethod": true,
      "serializeTypeInformation": false,
      "queryNullValue": "",
      "className": "{controller}Client",
      "operationGenerationMode": "MultipleClientsFromPathSegments",
      "additionalNamespaceUsages": [
        "System.Web",
        "ATS.Data.Repository.External.SistemaInfo"
      ],
      "additionalContractNamespaceUsages": [],
      "generateOptionalParameters": false,
      "generateJsonMethods": true,
      "enforceFlagEnums": false,
      "parameterArrayType": "System.Collections.Generic.IEnumerable",
      "parameterDictionaryType": "System.Collections.Generic.IDictionary",
      "responseArrayType": "System.Collections.Generic.List",
      "responseDictionaryType": "System.Collections.Generic.Dictionary",
      "wrapResponses": false,
      "wrapResponseMethods": [],
      "generateResponseClasses": true,
      "responseClass": "SwaggerResponse",
      "namespace": "SistemaInfo.MicroServices.Rest.Infra.ApiClient",
      "requiredPropertiesMustBeDefined": true,
      "dateType": "System.DateTime",
      "jsonConverters": null,
      "dateTimeType": "System.DateTime",
      "timeType": "System.TimeSpan",
      "timeSpanType": "System.TimeSpan",
      "arrayType": "System.Collections.ObjectModel.ObservableCollection",
      "arrayInstanceType": null,
      "dictionaryType": "System.Collections.Generic.Dictionary",
      "dictionaryInstanceType": null,
      "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection",
      "dictionaryBaseType": "System.Collections.Generic.Dictionary",
      "classStyle": "Inpc",
      "generateDefaultValues": true,
      "generateDataAnnotations": true,
      "excludedTypeNames": [],
      "handleReferences": false,
      "generateImmutableArrayProperties": false,
      "generateImmutableDictionaryProperties": false,
      "jsonSerializerSettingsTransformationMethod": null,
      "templateDirectory": null,
      "typeNameGeneratorType": null,
      "propertyNameGeneratorType": null,
      "enumNameGeneratorType": null,
      "serviceHost": null,
      "serviceSchemes": null,
      "output": "Sources/Infra.ApiClient.cs"
    }
  }
}
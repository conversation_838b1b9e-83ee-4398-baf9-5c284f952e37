﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class MensagemGrupoUsuarioMap : EntityTypeConfiguration<MensagemGrupoUsuario>
    {
        public MensagemGrupoUsuarioMap()
        {
            ToTable("MENSAGEM_GRUPO_USUARIO");
            HasKey(x => x.IdGrupoUsuario);
            Property(x => x.IdGrupoUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.NomeGrupoUsuario)
                .IsRequired();
            Property(x => x.IdUsuario)
                .IsRequired();
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models.AtendimentoPortador;

namespace ATS.Domain.Interface.Service
{
    public interface IAtendimentoPortadorService : IService<AtendimentoPortador>
    {
        AtendimentoPortadorResultModel IniciarAtendimento(int idUsuario, int? idEmpresa);
        void AtualizaDocumento(string documento, int idUsuario);
        AtendimentoPortadorResultModel Finalizar(FinalizarAtendimentoDTORequest request, int idUsuario);
        AtendimentoPortadorResultModel ConsultaAtendimentoPendente(int idUsuario, int? idEmpresa);
        void InserirTramite(AtendimentoPortadorTramiteRequest atendimentoTramitePortadorDto);
        List<ItensGraficoPorEmpresa> ConsultaQuantidadeAtendimentoPortador(DateTime dataInicial, DateTime dataFinal, DateTime? dataSelecinada);
        IQueryable<AtendimentoPortador> Find(Expression<Func<AtendimentoPortador, bool>> predicate, bool @readonly = false);
        ConsultarAtendimentoResponseDTO ConsultarAtendimento(ConsultarAtendimentoRequestDTO request, int Take, int Page,
            OrderFilters Order, List<QueryFilters> filters, int idEmpresa);
        ConsultarHistoricoAtendimentoResponseDTO ConsultarHistoricoAtendimento(ConsultarHistoricoAtendimentoRequestDTO request,
            int Take, int Page, OrderFilters Order, List<QueryFilters> filters, int idEmpresa);
        IList<ConsultaAtendimentoInternalResponseDTO> RelatorioConsultaAtendimentos(ConsultaRelatorioAtendimentoDTO request, 
            int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        PermissoesEmpresaAtendimentoPortadorResultModel ConsultaPermissoesUsuario(int idUsuario, EPerfil perfil, int? idEmpresa);
    }
}
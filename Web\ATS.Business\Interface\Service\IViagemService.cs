﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.DTO;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Models.ViagemModels;
using ATS.WS.Models.Common.Request;
using SistemaInfo.MicroServices.Rest.Ciot.ApiClient;

namespace ATS.Domain.Interface.Service
{
    public interface IViagemService : IBaseService<IViagemRepository>
    {
        Viagem GetComViagemEstabelecimentos(int id);
        IEnumerable<Viagem> GetViagens(ETipoBuscaViagens tipoBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta);
        IEnumerable<Viagem> GetViagensFinalizadasEntre(ETipoBuscaViagens tipoBusca, int idEmpresa, int? idFilial, int? idTipoCavalo, int? idTipoCarreta);
        IEnumerable<Viagem> GetViagensEmViagem(int idEmpresa);
        Viagem GetChilds(int id, int idEmpresa);
        ValidationResult IsValidToCrudViagemPedagioAvulso(Viagem viagem);
        ValidationResult IntegrarCheck(int idViagem, int idEmpresa, ViagemCheck check);
        ValidationResult Add(Viagem viagem, bool isPedagioAvulso = false);
        ViagemCheck GetUltimoViagemCheck(int idViagem);
        Viagem GetViagemAbertaPlaca(string placa);
        Viagem GetUltimaViagemFechadaPlaca(string placa);
        IQueryable<Viagem> Consultar(string nCPFMotorista, string tokenViagem, DateTime? dataLancamentoInicial, DateTime? dataLancamentoFinal, List<EStatusCheckViagem> statusCheckViagem, List<EStatusViagem> statusViagem, string token, string cnpjAplicacao, List<int> IdsViagem,List<string> NumerosControle);
        IEnumerable<Viagem> GetViagensEmViagemPorCPF(string nCPFMotorista);
        bool ViagemCadastrada(int idEmpresa, string numeroControle);
        ValidationResult BloqueioProcessoViagem(List<EBloqueioGestorTipo> idsTipoBloqueio, Viagem viagem, decimal valorPedagioViagem,EBloqueioOrigemTipo origem,decimal valorTotalFrete);
        ValidationResult AlterarViagemPendente(int idViagem, EBloqueioGestorTipo idBloqueioGestorTipo, int status, int idUsuario, string motivo);
        ConsultaViagemExternalResponseDTO ConsultarViagens(ConsultaViagemRequestDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        ConsultaViagemExternalCiotResponseDTO ConsultarDetalhesCiotViagem(int idViagem);
        object GetEventosViagem(string CPFCNPJProprietario, string CPFMotorista, List<EStatusViagemEvento> statusEvento = null, List<ETipoEventoViagem> tipoEvento = null, DateTime? dataInicio = null, DateTime? dataFim = null);
        object GetHistoricoViagem(string placa, int idEmpresa, int skip);
        object GetCartasPagas(int? idEmpresa, int ano, int mes);
        object GetCartasEmAberto(int? idEmpresa, int ano, int mes);
        object GetTotalDeCartas(int? idEmpresa, int ano, int mes);
        object GetTotalCartasMes(int? idEmpresa, int ano);
        object GetTotalGanhosPorMes(int? idEmpresa, int ano);
        object GetTotalPagamentos(int? idEmpresa, int ano, int mes);
        ConsultaPagamentosModel GetPagamentosPorViagem(int idEmpresa, DateTime dataInicial, DateTime dataFinal, int take, int page, OrderFilters order = null, List<QueryFilters> filters = null);
        byte[] GerarRelatorioPagamentos(int idEmpresa, DateTime dataInicial, DateTime dataFinal, ETipoArquivoPagamento? tipoRelatorio);
        ValidationResult Update(Viagem viagem);
        List<Viagem> GetViagemAbertaPlacas(List<string> placas);
        List<Viagem> GetViagensAbertasPorPlaca(List<string> placas);
        List<Viagem> GetUltimaViagemFechadaPlacas(List<string> placas);
        Viagem GetUltimaViagemPorPlaca(string placa, int idEmpresa);
        List<FrotaUtilizadaModel> GetUltimaViagemFrota(List<string> placa, int idEmpresa);
        List<string> GetPlacas(int idEmpresa);
        void AjustarProprietario(Viagem viagem);
        List<PlacasViagemModel> GetPlacasComCarretas(int idEmpresa);
        List<Viagem> GetViagensPorPeriodo(int idEmpresa, DateTime dataInicial, DateTime dataFinal);
        object ConsultarDocumentos(int idViagemEvento);
        ValidationResult RemoverPlacasCarretas(int idViagem, List<int> idViagemCarretaRemovidaList = null);
        void RemoverViagemEstabelecimentos(int idViagem, List<int> idViagemEstabelecimentoRemovidoList = null);
        List<Viagem> GetByCte(string numero);
        SolicitarCompraPedagioResponseDTO GetStatusPedagio(Viagem viagem);
        ValidationResult IsValidToCrudViagem(Viagem viagem, EProcesso processo);
        IList<ConsultaViagemInternalResponseDTO> RelatorioConsultaViagens(ConsultaViagemRequestDTO request, int Take, int Page, OrderFilters Order, List<QueryFilters> filters);
        bool PossuiResgateDeSaldoResidualSolicitadoNoDia(string documento, DateTime dia);
        string GerarTokenViagemEvento();
        Viagem ConsultarViagemPorToken(string token);
        IEnumerable<ViagemMotoristasModel> GetMotoristasComViagensDoProprietario(string cnpjProprietario,
            DateTime? dataCadastroBase = null);
        bool ExisteCartaFreteParaCombinacao(string cpfCnpjProprietario, string cpfMotorista, int idEmpresa, int mesesAnteriores);
        ViagemCalcularValorPedagioResponse CalcularValorPedagio(ViagemCalcularValorPedagioRequest request);
        ViagemEvento ConsultarViagemEvento(string token);
        ViagemConsultarResponse ConsultarViagemV2(int idViagem, int? idEmpresa, bool filtrarVeiculoTerceiro = false);        
        ViagemConsultarResponse ConsultarViagemV3(int idViagem, bool filtrarVeiculoTerceiro = false);        
        IQueryable<ViagemEvento> GetEvento(int idviagem, int idviagemEvento);
        IQueryable<Viagem> GetViagem(int idviagem);
        ValidationResult AddViagemRota(ViagemRota viagemRota);
        ValidationResult UpdateViagemRota(ViagemRota viagemRota);
        ViagemRota GetViagemRota(int idViagem);
        ValidationResult DeleteViagemRota(ViagemRota viagemRota);
        Guid? GetHistoricoViagemRota(int idViagem);
        ViagemDadosCiotResponse GetDadosCiot(int idviagem);
        ViagemDadosValePedagio GetDadosValePedagio(int idviagem);
        ConsultarEventoResponseModel ConsultarDetalheEvento(ConsultarEventoRequestModel request);
        Viagem ObterViagemPorEmpresa(int viagemId, int empresaId);
        ConsultaTiposCargasResponse ConsultarTiposCarga(string cnpjEmpresa);
        ConsultarTiposCargaResponse ConsultarTiposCarga(int idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        int QuantidadeViagensAbertasPorNumeroCiotsVinculados(int idDclaracaociot);
        FornecedorEnum? GetFornecedorViagemRota(int idViagem);
        ConsultaPagamentosModel GetRelatorioPagamentosPorViagem(int idEmpresa, DateTime dataInicio, DateTime dataFim);
        DadosFilialEPagamento GetCnpjFilialEDadosPagamento(string ciot);
        object ConsultarUltimaViagemEmpresa(int empresaId, int take, int page, OrderFilters order, List<QueryFilters> filters);
        bool VincularViagemAoContrato(int idContratoCiotAgregado, int idUltimaViagem);
        ReciboPefDadosResponse GetDadosReciboPef(int idviagem);
        ComprovanteCargaResponse GetDadosComprovanteCarga(int idviagem);
    }
}
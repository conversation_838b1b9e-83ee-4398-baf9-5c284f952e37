﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="4.2.1" targetFramework="net48" />
  <package id="Dapper" version="1.50.5" targetFramework="net48" />
  <package id="DnsClient" version="1.6.1" targetFramework="net48" />
  <package id="EnterpriseLibrary.Common" version="6.0.1304.0" targetFramework="net48" />
  <package id="EnterpriseLibrary.Data" version="6.0.1304.0" targetFramework="net48" />
  <package id="EntityFramework" version="6.2.0" targetFramework="net48" />
  <package id="EntityFramework.MappingAPI" version="6.1.0.9" targetFramework="net48" />
  <package id="MongoDB.Driver" version="3.2.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.Win32.Registry" version="5.0.0" targetFramework="net48" />
  <package id="MongoDB.Bson" version="3.2.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.0.0" targetFramework="net48" />
  <package id="MongoDB.Driver.Core" version="2.7.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="NLog" version="4.7.2" targetFramework="net48" />
  <package id="SharpCompress" version="0.30.1" targetFramework="net48" />
  <package id="Snappier" version="1.0.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="5.0.0" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net48" />
  <package id="System.Text.Encoding.CodePages" version="5.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="TrackerEnabledDbContext" version="3.6.1" targetFramework="net48" />
  <package id="TrackerEnabledDbContext.Common" version="3.6.1" targetFramework="net48" />
  <package id="ZstdSharp.Port" version="0.7.3" targetFramework="net48" />
</packages>
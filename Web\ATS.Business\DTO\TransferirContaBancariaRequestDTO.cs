﻿using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class TransferirContaBancariaRequestDTO
    {
        /// <summary>
        /// CNPJ da Empresa
        /// </summary>
        public string CnpjEmpresa { get; set; }

        /// <summary>
        /// Cartão origem da transferência.
        /// Para o ATS Global, onde não há vinculo com a empresa, o app necessita obrigatoriamente informar esta informação.
        /// Em aplicativos específicos para empresa, como a SOTRAN, não é obrigado esta informação porque é possivel identificar o cartão automaticamente pelo CPF x Produto padrão de frete da empresa.
        /// </summary>
        public CartaoIdentificacaoDto CartaoOrigem { get; set; }

        /// <summary>
        /// Documento do favorecido da transferencia
        /// </summary>
        public string DocumentoFavorecido { get; set; }

        /// <summary>
        /// Identificador da conta bancária
        /// </summary>
        public IdentificadorContaBancaria ContaBancaria { get; set; }

        /// <summary>
        /// Valor para efetuar carga
        /// </summary>
        public decimal Valor { get; set; }

        /// <summary>
        /// Senha
        /// </summary>
        public string Senha { get; set; }
    }
}
﻿using ATS.Application.Application.Common;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Transactions;
using ATS.Application.Interface;

namespace ATS.Application.Application
{
    public class PinApp : AppBase, IPinApp
    {

        private readonly IPINService _service;

        public PinApp(IPINService service)
        {
            _service = service;
        }

        public ValidationResult SetEnviado(string idSms, bool status, ESMSDeliveredStatus statusCode, DateTime? dataEnvio)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
               new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.SetEnviado(idSms, status, statusCode, dataEnvio);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

        public ValidationResult SetEnviadoOperadora(string idSms, bool status, ESMSSendStatus statusCode, DateTime? dataEnvio)
        {
            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
              new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                ValidationResult validationResult = _service.SetEnviadoOperadora(idSms, status, statusCode, dataEnvio);
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }

    }
}
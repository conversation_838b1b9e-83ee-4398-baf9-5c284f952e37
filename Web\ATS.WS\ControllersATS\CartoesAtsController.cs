﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Faturamento;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.Cartoes;
using Newtonsoft.Json;
using NLog;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;
using IdentificadorCartao = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IdentificadorCartao;

namespace ATS.WS.ControllersATS
{
    public class CartoesAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IEmpresaApp _empresaApp;
        private readonly IFilialApp _filialApp;
        private readonly IUsuarioPermissaoFinanceiroApp _usuarioPermissaoFinanceiroApp;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ISerproApp _serproApp;
        private readonly IPermissaoCartaoApp _permissaoCartaoApp;
        private readonly IRsaCryptoService _rsaCryptoService;

        public CartoesAtsController(IUserIdentity userIdentity, IUsuarioApp usuarioApp, IParametrosApp parametrosApp, CartoesAppFactoryDependencies cartoesAppFactoryDependencies,
            IEmpresaApp empresaApp, IFilialApp filialApp, IUsuarioPermissaoFinanceiroApp usuarioPermissaoFinanceiroApp, IParametrosEmpresaService parametrosEmpresa, ISerproApp serproApp, IPermissaoCartaoApp permissaoCartaoApp, IRsaCryptoService rsaCryptoService)
        {
            _userIdentity = userIdentity;
            _usuarioApp = usuarioApp;
            _parametrosApp = parametrosApp;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _empresaApp = empresaApp;
            _filialApp = filialApp;
            _usuarioPermissaoFinanceiroApp = usuarioPermissaoFinanceiroApp;
            _parametrosEmpresa = parametrosEmpresa;
            _serproApp = serproApp;
            _permissaoCartaoApp = permissaoCartaoApp;
            _rsaCryptoService = rsaCryptoService;
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CarregarProdutos(string documento)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                var resultado = ResponderSucesso(cartaoProdutosList);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult FiltrarProdutosMestre(string documento)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.FiltrarProdutosMestre();
                var resultado = ResponderSucesso(cartaoProdutosList);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [EnableLogAudit]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarVinculadosGrid(string documento)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                var resultado = cartoesApp.GetCartoesVinculadosGrid(documento, cartaoIdArray);

                if (!resultado.Sucesso)
                    return ResponderErro(resultado.Mensagem);

                return ResponderSucesso(new
                {
                    totalItems = resultado.Objeto.Count,
                    items = resultado.Objeto
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCartoesPortador(string documento)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                var resultado = cartoesApp.GetCartoesVinculadosGrid(documento, cartaoIdArray);

                if (!resultado.Sucesso)
                    return ResponderErro(resultado.Mensagem);

                if (!resultado.Objeto.Any())
                {
                    var resultadCartoesBloqueado = cartoesApp.GetCartoesBloqueadoGrid(documento, cartaoIdArray);

                    return ResponderSucesso(new
                    {
                        totalItems = resultadCartoesBloqueado.Objeto.Count,
                        items = resultadCartoesBloqueado.Objeto
                    });
                }

                return ResponderSucesso(new
                {
                    totalItems = resultado.Objeto.Count,
                    items = resultado.Objeto
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarHistoricoGrid(string documento)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                var resultado = cartoesApp.GetCartaoHistoricoGrid(documento, cartaoIdArray);

                if (!resultado.Sucesso)
                    return ResponderErro(resultado.Mensagem);

                return ResponderSucesso(new
                {
                    totalItems = resultado.Objeto.Count,
                    items = resultado.Objeto
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult BuscarInformacoesPortador(string documento)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                
                
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var portador = cartoesApp.CarregarInformacoesPortador(documento);

                if (portador != null)
                {
                    portador.DesabilitarBotoesVincularCartao = _userIdentity.Perfil != (int)EPerfil.Administrador
                                                 && !_usuarioPermissaoFinanceiroApp.PossuiPermissao(
                                                     _userIdentity.IdUsuario,
                                                     EBloqueioFinanceiroTipo.vincularCartao);
                    
                    portador.DesabilitarBotoesDesvincularCartao = _userIdentity.Perfil != (int)EPerfil.Administrador
                                                               && !_usuarioPermissaoFinanceiroApp.PossuiPermissao(
                                                                   _userIdentity.IdUsuario,
                                                                   EBloqueioFinanceiroTipo.desvincularCartao);

                    return ResponderSucesso(portador);
                }


                return ResponderErro("Portador não encontrado");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult VincularCartaoPortador(CartaoVincularPortadorAtsRequest cartaoVincularPortadorAtsRequest)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador &&
                    !_usuarioPermissaoFinanceiroApp.PossuiPermissao(_userIdentity.IdUsuario,
                        EBloqueioFinanceiroTipo.vincularCartao))
                    throw new InvalidOperationException("Usuário não possui permissão para vincular cartões.");
                
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                    cartaoVincularPortadorAtsRequest.IdEmpresa = _userIdentity.IdEmpresa ?? 0;

                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var empresa = _empresaApp.Get(cartaoVincularPortadorAtsRequest.IdEmpresa);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                var portador = cartoesApp.CarregarInformacoesPortador(cartaoVincularPortadorAtsRequest.Documento);

                var resultado = cartoesApp.GetCartaoHistoricoGrid(cartaoVincularPortadorAtsRequest.Documento.OnlyNumbers(), cartaoIdArray);
                
                if(!resultado.Sucesso)
                    return ResponderErro($"Não foi possível consultar o historíco de cartões do usuário!");
                
                if (resultado.Objeto.Any())
                {
                    var mesmoCartaoJaVinculado = resultado.Objeto
                        .FirstOrDefault(x =>
                            x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado &&
                            x.Identificador == cartaoVincularPortadorAtsRequest.Identificador);
                    if (mesmoCartaoJaVinculado != null)
                        return ResponderSucesso("Cartão vinculado com sucesso!");
                
                    var mesmoCartaoJaBloqueado = resultado.Objeto
                        .FirstOrDefault(x =>
                            x.Status == HistoricoCartaoPessoaResponseStatus.Bloqueado &&
                            x.Identificador == cartaoVincularPortadorAtsRequest.Identificador);
                    if (mesmoCartaoJaBloqueado != null)
                        return ResponderSucesso($"Cartão vinculado com sucesso ao portador documento {cartaoVincularPortadorAtsRequest.Documento.FormatMaskCpfCnpj()}. " +
                                                $"O cartão encontra-se BLOQUEADO. Para realizar o desbloqueio entrar em contato com a EXTRATTA.");
                
                    var mesmoCartaoCancelado = resultado.Objeto
                        .FirstOrDefault(x =>
                            x.Status == HistoricoCartaoPessoaResponseStatus.Cancelado &&
                            x.Identificador == cartaoVincularPortadorAtsRequest.Identificador);
                    if (mesmoCartaoCancelado != null)
                        return ResponderErro("Cartao encontra-se cancelado.");
                    
                    if (empresa != null)
                    {
                        var cartaoVinculado = resultado.Objeto
                            .FirstOrDefault(x => 
                                x.Status == HistoricoCartaoPessoaResponseStatus.Vinculado && 
                                x.Identificador != cartaoVincularPortadorAtsRequest.Identificador);
                        if (cartaoVinculado != null && !empresa.VinculoNovoCartaoPortador)
                            return ResponderErro($"O portador de documento {cartaoVincularPortadorAtsRequest.Documento.FormatMaskCpfCnpj()} já possui um cartão vinculado. Para realizar o vínculo de um novo cartão é necessário desvincular o atual. Em caso de dúvidas entre em contato com a Extratta!");
                    }

                    var cartaoBloqueado = resultado.Objeto
                        .FirstOrDefault(x => 
                            x.Status == HistoricoCartaoPessoaResponseStatus.Bloqueado && 
                            x.Identificador != cartaoVincularPortadorAtsRequest.Identificador);
                    
                    if (cartaoBloqueado != null)
                        return ResponderErro($"Não foi possível vincular o cartão, pois o portador já possui um cartão com status bloqueado. Efetue o desbloqueio do cartão para o vínculo de um novo cartão!");
                }

                var vincularResponse = cartoesApp.VincularCartaoPortador(cartaoVincularPortadorAtsRequest.Identificador,
                    cartaoVincularPortadorAtsRequest.ProdutoId, portador, _userIdentity.IdEmpresa ?? 0);

                if (vincularResponse.Status == VincularResponseStatus.Sucesso)
                {
                    if (empresa != null)
                    {
                        if (empresa.VinculoNovoCartaoBloqueadoPortador)
                        {
                            var parametrosApp = _parametrosApp;

                            var requestBloqueio = new BloquearCartaoRequest()
                            {
                                Cartao = new IdentificadorCartao()
                                {
                                    Identificador = cartaoVincularPortadorAtsRequest.Identificador,
                                    Produto = cartaoVincularPortadorAtsRequest.ProdutoId
                                },
                                Observacao = "Bloqueio por parametrização da empresa!",
                                Motivo = parametrosApp.GetMotivoPadraoBloqueioCartaoEmpresa()
                            };

                            var cartoesAppEmpresa = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, empresa.IdEmpresa, _userIdentity.CpfCnpj, _userIdentity.Nome);
                            var resultadoBloqueio = cartoesAppEmpresa.BloquearCartaoParametrizacaoEmpresa(requestBloqueio);

                            if (resultadoBloqueio.Status == BloquearCartaoResponseStatus.Sucesso)
                                return ResponderPadrao(true, $"Cartão vinculado com sucesso ao portador documento {cartaoVincularPortadorAtsRequest.Documento.FormatMaskCpfCnpj()}. O cartão encontra-se BLOQUEADO. Para realizar o desbloqueio entrar em contato com a EXTRATTA.", vincularResponse);


                            return ResponderPadrao(true, $"Ocorreu um erro ao bloquear o cartão do portador, de acordo com a parametrização da empresa!", vincularResponse);
                        }
                    }

                    vincularResponse.Mensagem = "Cartão " + cartaoVincularPortadorAtsRequest.Identificador +
                                                " vinculado com sucesso!";

                }

                return ResponderPadrao(vincularResponse.Status == VincularResponseStatus.Sucesso, vincularResponse.Mensagem, vincularResponse);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult DesvincularCartaoPortador(
            CartaoDesvincularPortadorAtsRequest cartaoDesvincularPortadorAtsRequest)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador &&
                    !_usuarioPermissaoFinanceiroApp.PossuiPermissao(_userIdentity.IdUsuario,
                        EBloqueioFinanceiroTipo.desvincularCartao))
                    throw new InvalidOperationException("Usuário não possui permissão para desvincular cartões.");
                
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var portador = cartoesApp.CarregarInformacoesPortador(cartaoDesvincularPortadorAtsRequest.Documento);

                var desvincularResponse = cartoesApp.DesvincularCartaoPortador(
                    cartaoDesvincularPortadorAtsRequest.Identificador, cartaoDesvincularPortadorAtsRequest.ProdutoId,
                    cartaoDesvincularPortadorAtsRequest.MotivoDesvinculo,
                    portador, cartaoDesvincularPortadorAtsRequest.CartaoMestreId);

                var mensagem = string.Join("\n", desvincularResponse.Mensagem);
                var resultado = ResponderPadrao(desvincularResponse.Status == DesvincularResponseStatus.Sucesso,
                    mensagem, desvincularResponse);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCartao(int identificador, int produto)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var cartaoProdutosList = cartoesApp.GetCartaoProcessadora(identificador, produto);
                var resultado = ResponderSucesso(cartaoProdutosList);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CarregarPontosDistribuicao(bool validaFilialUsuario)
        {
            List<string> cnpjList;
            try
            {
                var usuarioLogado = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                if (usuarioLogado.Filiais != null && usuarioLogado.Filiais.Count > 0)
                {
                    var idFiliais = usuarioLogado.Filiais.Select(f => f.IdFilial);
                    var filialApp = _filialApp;
                    cnpjList = filialApp.GetCnpjList(idFiliais.ToArray());
                }
                else
                {
                    var empresaApp = _empresaApp;
                    cnpjList = empresaApp.GetTodas().Where(e => e.Ativo && e.TokenMicroServices != null).Select(e => e.CNPJ).ToList();
                }

                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuarioLogado);
                var pontoDistribuicaoList =
                    cartoesApp.GetPontosDistribuicao(cnpjList);
                if (EPerfil.Administrador != usuarioLogado.Perfil && usuarioLogado.Filiais != null && usuarioLogado.Filiais.Count > 0 && validaFilialUsuario)
                {
                    pontoDistribuicaoList = pontoDistribuicaoList.Where(p => cnpjList.Contains(p.CpfCnpj)).ToList();
                }

                var resultado = ResponderSucesso(pontoDistribuicaoList);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult EnviarRemessaCartoes(List<EnvioRemessaRequestDTO> envioRemessaRequestDto, string documentoOrigem, string documentoDestino)
        {
            try
            {
                var list = new List<IdentificadorCartao>();
                foreach (var item in envioRemessaRequestDto)
                {
                    var identificadorCartao = new IdentificadorCartao
                    {
                        Identificador = item.Identificador,
                        Produto = item.ProdutoId
                    };
                    list.Add(identificadorCartao);
                }

                var envioRemessaRequest = new EnvioRemessaRequest
                {
                    Cartoes = list,
                    DocumentoOrigem = documentoOrigem,
                    DocumentoDestino = documentoDestino
                };
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var enviarRemessaCartoes = cartoesApp.EnviarRemessaCartoes(envioRemessaRequest);
                return enviarRemessaCartoes.Status == EnvioRemessaResponseStatus.Sucesso
                    ? ResponderSucesso(enviarRemessaCartoes.Mensagem, enviarRemessaCartoes)
                    : ResponderErro(enviarRemessaCartoes.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarCartaoRemessa(EnvioRemessaRequestDTO envioRemessaRequestDto, string documentoOrigem, string documentoDestino)
        {
            try
            {
                var identificadorCartao = new IdentificadorCartao
                {
                    Identificador = envioRemessaRequestDto.Identificador,
                    Produto = envioRemessaRequestDto.ProdutoId
                };

                var envioRemessaRequest = new ValidarCartaoRequest
                {
                    Cartao = identificadorCartao,
                    DocumentoOrigem = documentoOrigem,
                    DocumentoDestino = documentoDestino
                };
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var enviarRemessaCartoes = cartoesApp.ValidarCartaoRemessa(envioRemessaRequest);
                return enviarRemessaCartoes.Status == EnvioRemessaResponseStatus.Sucesso
                    ? ResponderSucesso(enviarRemessaCartoes.Mensagem, enviarRemessaCartoes)
                    : ResponderErro(enviarRemessaCartoes.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult ValidarLoteCartaoRemessa(EnvioLoteRemessaRequestDTO envioRemessaRequestDto, string documentoOrigem, string documentoDestino)
        {
            try
            {
                var envioRemessaRequest = new ValidarCartoesLoteRequest
                {
                    Produto = envioRemessaRequestDto.ProdutoId,
                    IdentificadorInicial = envioRemessaRequestDto.IdentificadorInicial,
                    IdentificadorFinal = envioRemessaRequestDto.IdentificadorFinal,
                    DocumentoOrigem = documentoOrigem,
                    DocumentoDestino = documentoDestino
                };

                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var enviarRemessaCartoes = cartoesApp.ValidarLoteCartaoRemessa(envioRemessaRequest);
                return enviarRemessaCartoes.Status == EnvioRemessaResponseStatus.Sucesso
                    ? ResponderSucesso(enviarRemessaCartoes.Mensagem, enviarRemessaCartoes)
                    : ResponderErro(enviarRemessaCartoes.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Validate2FA]
        [Expor(EApi.Portal)]
        public JsonResult ReceberRemessaCartoes(BaixaRemessaRequest baixaRemessaRequest)
        {
            try
            {
                if ((int)EPerfil.Administrador == _userIdentity.Perfil)
                    return ResponderErro("Não é possível receber remessa de cartões com o perfil administrador");

                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var receberRemessaCartoes = cartoesApp.ReceberRemessaCartoes(baixaRemessaRequest);

                return receberRemessaCartoes.Status == BaixaRemessaResponseStatus.Sucesso
                    ? ResponderSucesso(receberRemessaCartoes.Mensagem, receberRemessaCartoes)
                    : ResponderErro(receberRemessaCartoes.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CarregarCartoesLote(int loteId)
        {
            try
            {
                var cnpjList = new List<string>();

                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                if (usuario.Filiais != null && usuario.Filiais.Count > 0)
                {
                    var idFiliais = usuario.Filiais.Select(f => f.IdFilial);
                    var filialApp = _filialApp;
                    cnpjList = filialApp.GetCnpjList(idFiliais.ToArray());
                }
                else
                    cnpjList = _filialApp
                        .GetQuery(usuario.IdEmpresa)
                        .Select(f => f.CNPJ)
                        .ToList();

                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var response = cartoesApp.ConsultarCartoesLote(loteId);
                if (response.Status == ConsultarRemessaResponseStatus.Erro)
                    return ResponderErro("Não foi possível consultar o lote informado.");
                if (EPerfil.Administrador == usuario.Perfil)
                    return ResponderErro("Usuário administrador não pode receber lote de cartões.");

                if (!cnpjList.Contains(response.DocumentoDestino))
                    return ResponderErro("O usuário logado não possui permissão para visualizar este lote.");

                var resultado = response.Status == ConsultarRemessaResponseStatus.Erro
                    ? ResponderErro(response.Mensagem)
                    : ResponderSucesso(response.Mensagem, response);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CarregarLoteConsultaRemessa(int loteId)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var response = cartoesApp.ConsultarCartoesLote(loteId);
                if (response.Status == ConsultarRemessaResponseStatus.Erro)
                    return ResponderErro("Não foi possível consultar o lote informado.");

                var resultado = response.Status == ConsultarRemessaResponseStatus.Erro
                    ? ResponderErro(response.Mensagem)
                    : ResponderSucesso(response.Mensagem, response);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult RelatorioSituacaoCartoes(RelatorioSituacaoCartoesDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                {
                    request.IdEmpresa = _userIdentity.IdEmpresa;
                    request.IdUsuario = _userIdentity.IdUsuario;
                }

                if (!request.DataInicio.HasValue || !request.DataFim.HasValue)
                    return ResponderErro("Data de início e fim não informadas");

                if ((request.DataFim.Value - request.DataInicio.Value).Days > 30)
                    return ResponderErro("O intervalo do período consultado não pode ser maior do que 30 dias");

                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, request.IdUsuario, true, request.IdEmpresa);
                var result = cartoesApp.RelatorioSituacaoCartao(request, take, page, order, filters);
                var resultado = ResponderSucesso(result);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult AlterarSenhaCartao(AlterarSenhaCartaoRequestDTO request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Senha) && string.IsNullOrWhiteSpace(request.SenhaCrypt))
                    return ResponderErro("Senha não informada.");
                
                var senhaParaValidacao = request.Senha;
            
                if (!string.IsNullOrWhiteSpace(request.SenhaCrypt))
                {
                    string senhaDecrypt;
                    try
                    {
                        senhaDecrypt = _rsaCryptoService.Decrypt(request.SenhaCrypt);
                        if (string.IsNullOrEmpty(senhaDecrypt)) throw new Exception();
                    }
                    catch (Exception)
                    {
                        throw new InvalidOperationException("Senha criptografada incorretamente.");
                    }
                    var senhaDecryptBytes = Encoding.UTF8.GetBytes(senhaDecrypt);
                    senhaParaValidacao = Convert.ToBase64String(senhaDecryptBytes);
                }

                request.Senha = senhaParaValidacao;
                
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                if (EPerfil.Administrador != usuario.Perfil)
                    return ResponderErro("Apenas Usuários Administradores podem alterar a senha do cartão.");

                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var result = cartoesApp.AlterarSenhaCartao(request);
                if (result.Status == AlterarSenhaCartaoResponseStatus.Falha)
                    return Responder(false, result.Mensagem, result);

                return ResponderSucesso(result);
            }
            catch (Exception e)
            {
                throw new Exception("Error ao alterar senha do cartão! " + e.Message, e);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult RelatorioConciliacaoAnalitico(RelatorioConciliacaoAnaliticoTransacoesDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                {
                    request.IdEmpresa = _userIdentity.IdEmpresa;
                    request.IdUsuario = _userIdentity.IdUsuario;
                }
                
                // Biz limita a consulta em 100 dias a partir da data inicial sem retornar uma mensagem informando este limite. Antes era 30, mas falamos com eles para aumentar para 100.
                if ((request.DataFinal - request.DataInicial).Days > 100)
                    return ResponderErro("O intervalo do período consultado não pode ser maior do que 100 dias");

                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, request.IdUsuario, true, request.IdEmpresa);

                request.Produto = cartoesApp.GetIdProdutoCartaoFretePadrao();
                var resultado = cartoesApp.RelatorioConciliacaoAnalitico(request, take, page, order, filters);

                if (!resultado.Sucesso)
                    return ResponderErro(resultado.Mensagem);

                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e, "Erro ao gerar conciliação no ATS");
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult RelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaDTO request, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil == (int) EPerfil.Empresa)
                {
                    request.IdEmpresa = _userIdentity.IdEmpresa;
                    request.IdUsuario = _userIdentity.IdUsuario;
                }

                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var result = cartoesApp.RelatorioTransferenciasContaBancaria(request, take, page, order, filters);
                var resultado = ResponderSucesso(result);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CarregarRemessaEmpresa(bool filtrarEmpresaOrigem, int take, int page, OrderFilters order, List<QueryFilters> filters, DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                var cnpjList = new List<string>();
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);

                if (usuario.Perfil != EPerfil.Administrador)
                {
                    if (usuario.Filiais != null && usuario.Filiais.Count > 0)
                    {
                        var idFiliais = usuario.Filiais.Select(f => f.IdFilial);
                        var filialApp = _filialApp;
                        cnpjList = filialApp.GetCnpjList(idFiliais.ToArray());
                    }
                    else
                        cnpjList = _filialApp
                            .GetQuery(usuario.IdEmpresa)
                            .Select(f => f.CNPJ)
                            .ToList();
                }

                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                var response = cartoesApp.CarregarRemessaEmpresa(cnpjList, filtrarEmpresaOrigem, take, page, order, filters, dataInicio.StartOfDay(), dataFim.EndOfDay());

                foreach (var item in response.items)
                {
                    if (item.Status == ConsultarRemessaResponseStatus.Erro)
                        return ResponderErro("Não foi possível consultar o lote informado.");
                }

                var resultado = response.Status == ConsultarRemessaEmpresaResponseStatus.Erro
                    ? ResponderErro(response.Mensagem)
                    : ResponderSucesso(response.Mensagem, response);
                return resultado;
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioTransferenciasContaBancaria(string json)
        {
            var filtroGridModel = JsonConvert.DeserializeObject<RelatorioTransferenciasContaBancariaDTO>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
            {
                filtroGridModel.IdEmpresa = _userIdentity.IdEmpresa;
                filtroGridModel.IdUsuario = _userIdentity.IdUsuario;
            }

            var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, filtroGridModel.IdUsuario, true, filtroGridModel.IdEmpresa);

            var result = cartoesApp.GerarRelatorioTransferenciasContaBancaria(filtroGridModel, filtroGridModel.Take,
                filtroGridModel.Page, filtroGridModel.Order, filtroGridModel.Filters, filtroGridModel.Extensao,
                filtroGridModel.IdEmpresa ?? 0);

            var mimeType = string.Empty;

            switch (filtroGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(result, mimeType, $"Relaório de transferências para contas bancárias.{filtroGridModel.Extensao}");
        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioConciliacaoAnalitico(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioConciliacaoAnaliticoTransacoesDTO>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
            {
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;
                filtrosGridModel.IdUsuario = _userIdentity.IdUsuario;
            }

            var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, filtrosGridModel.IdUsuario, true, filtrosGridModel.IdEmpresa);
            string nomeRelatorio;
            byte[] retorno;


            if (filtrosGridModel.Extensao == EExtensaoArquivoRelatorio.Txt)
            {
                nomeRelatorio = $"Relatório CNAB240.txt";
                 retorno = cartoesApp.GerarRelatorioCnab(
                    filtrosGridModel, filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao, filtrosGridModel.IdEmpresa ?? 0);
            }
            else
            {
                retorno = cartoesApp.GerarRelatorioConciliacaoAnalitico(
                   filtrosGridModel, filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao, filtrosGridModel.IdEmpresa ?? 0);
                nomeRelatorio = $"Relatório de conciliação.{filtrosGridModel.Extensao.ToString().ToLower()}";
            }

            string mimeType;

            switch (filtrosGridModel.Extensao)
            {
                case EExtensaoArquivoRelatorio.Pdf:
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case EExtensaoArquivoRelatorio.Xlsx:
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
                case EExtensaoArquivoRelatorio.Csv:
                    mimeType = ConstantesUtils.CsvMimeType;
                    break;
                case EExtensaoArquivoRelatorio.Txt:
                    mimeType = ConstantesUtils.TxtMimeType;
                    break;
                case EExtensaoArquivoRelatorio.Ofx:
                    mimeType = ConstantesUtils.OfxMimeType;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            return File(retorno, mimeType, nomeRelatorio);

        }

        [HttpPost]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public FileResult GerarRelatorioSituacaoCartoes(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<RelatorioSituacaoCartoesDTO>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            if (_userIdentity.Perfil == (int) EPerfil.Empresa)
            {
                filtrosGridModel.IdEmpresa = _userIdentity.IdEmpresa;
                filtrosGridModel.IdUsuario = _userIdentity.IdUsuario;
            }

            var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, filtrosGridModel.IdUsuario, true, filtrosGridModel.IdEmpresa);
            var retorno = cartoesApp.GerarRelatorioSituacaoCartoes(filtrosGridModel, filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order,
                filtrosGridModel.Filters, filtrosGridModel.Extensao, _userIdentity.IdEmpresa ?? 0);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(retorno, mimeType, $"Relatório de situação de cartões.{filtrosGridModel.Extensao}");
        }



        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult CancelarCartaoRemessa(CancelarCartaoRemessaRequest cancelarCartaoRemessaRequest)
        {
            try
            {
                var usuario = _usuarioApp.GetPorCNPJCPF(_userIdentity.CpfCnpj);
                var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);

                var cancelarRemessa = cartoesApp.CancelarCartaoRemessa(cancelarCartaoRemessaRequest);

                if (cancelarRemessa.Sucesso == true)
                {
                    return ResponderSucesso(cancelarRemessa.Mensagem);
                }

                return ResponderErro(cancelarRemessa.Mensagem);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesbloquearCartao(DesbloquearCartaoRequest request, string documento)
        {
            try
            {
                return ResoinderErro("Método indisponível! Entre em contato com a Extratta.", false);
                
                // var usuario = _usuarioApp.Get(_userIdentity.IdUsuario);
                // var cartoesApp = CartoesApp.CreateByUsuario(_cartoesAppFactoryDependencies, usuario);
                //
                // var cartaoProdutosList = cartoesApp.GetCartaoProdutos();
                // var cartaoIdArray = cartaoProdutosList.Select(p => p.Id).ToList();
                // var resultado = cartoesApp.GetCartoesVinculadosGrid(documento.OnlyNumbers(), cartaoIdArray);
                //
                // if (!resultado.Sucesso)
                // {
                //     return ResoinderErro(resultado.Mensagem, null);
                // }
                // if (!resultado.Objeto.Any())
                // {
                //     var result = cartoesApp.DesbloquearCartao(request);
                //
                //     if (result.Status == DesbloquearCartaoResponseStatus.Falha)
                //         return Responder(false, result.Mensagem, result);
                //
                //     return ResponderSucesso(result);
                // }
                //
                // return ResoinderErro("Não foi possível desbloquear este cartão,pois o portador já possui um cartão vinculado!", false);

            }
            catch (Exception e)
            {
                throw new Exception($"Ocorreu um erro durante o processamento:{Environment.NewLine}{e.Message}");
            }
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPermissao(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.ConsultarPermissoes(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso(result.Value);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult HabilitarPermissaoCompraOnline(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.CompraOnlineHabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesabilitarPermissaoCompraOnline(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.CompraOnlineDesabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult HabilitarPermissaoCompraFisica(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.CompraFisicaHabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesabilitarPermissaoCompraFisica(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.CompraFisicaDesabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesabilitarPermissaoSaque(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.SaqueDesabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult HabilitarPermissaoSaque(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.SaqueHabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult DesabilitarPermissaoCompraInternacional(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.CompraInternacionalDesabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [EnableLogAudit]
        [EnableLogRequest]
        [Expor(EApi.Portal)]
        public JsonResult HabilitarPermissaoCompraInternacional(int identificador,int produto)
        {
            try
            {
                var result = _permissaoCartaoApp.CompraInternacionalHabilitar(identificador,produto);

                if (!result.Success)
                    return ResponderErro(result.Messages.FirstOrDefault());
                
                return ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e.Message);

            }
        }
    }
}

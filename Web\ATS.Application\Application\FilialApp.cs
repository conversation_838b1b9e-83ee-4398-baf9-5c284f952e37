﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class FilialApp : AppBase, IFilialApp
    {
        private readonly IFilialService _filialService;
        private readonly IFilialContatoService _filialContatoService;
        private readonly IBloqueioGestorValorApp _bloqueioGestorValorApp;

        public FilialApp(IFilialService filialService, IFilialContatoService filialContatoService, IBloqueioGestorValorApp bloqueioGestorValorApp)
        {
            _filialService = filialService;
            _filialContatoService = filialContatoService;
            _bloqueioGestorValorApp = bloqueioGestorValorApp;
        }

        public ValidationResult Add(Filial filial)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _filialService.Add(filial);
                
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public Filial Get(int id)
        {
            return _filialService.Get(id);
        }
        
        public Filial Get(string cnpj)
        {
            return _filialService.Get(cnpj);
        }

        public object GetFilialCadastro(int id)
        {
            var resposta = _filialService.GetFilialCadastro(id);
            resposta.AlcadasBloqueioGestorValor = _bloqueioGestorValorApp.GetBloqueioGestorValor(resposta.IdEmpresa, resposta.IdFilial);
            return resposta;
        }

        public IQueryable<Filial> GetListaFilialPorEmpresa(int idEmpresa)
        {
            return _filialService.GetListaFilialPorEmpresa(idEmpresa);
        }

        public Filial GetFilialPorEmpresa(int idEmpresa, int idFilial)
        {
            return _filialService.GetFilialPorEmpresa(idEmpresa, idFilial);
        }

        public ValidationResult Update(Filial filial)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _filialService.Update(filial);

                    if (!validationResult.IsValid)
                        return validationResult;

                    var listFilialToRemove = new List<int>();
                
                    foreach (var idFilialContato in filial.FilialContatos.Select(c => c.IdFilialContato))
                        listFilialToRemove.Add(idFilialContato);

                    var contatosFilial = _filialService.GetContatosByFilial(filial.IdFilial);
                
                    foreach (var contatoFilial in contatosFilial)
                        if (listFilialToRemove.All(x => x != contatoFilial.IdFilialContato))
                        {
                            var validationFilialContatoRemovida = _filialContatoService.Delete(contatoFilial);
                            if (!validationFilialContatoRemovida.IsValid)
                                return validationFilialContatoRemovida;
                        }

                    foreach (var filialContato in filial.FilialContatos)
                        if (filialContato.IdFilialContato > 0)
                        {
                            var validationFilialContato = _filialContatoService.Update(filialContato);
                            if (!validationFilialContato.IsValid)
                                return validationFilialContato;
                        }
                        else
                        {
                            var validationFilialContato = _filialContatoService.Add(filialContato);
                            if (!validationFilialContato.IsValid)
                                return validationFilialContato;
                        }

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public IQueryable<Filial> Consultar(string razaoSocial, int? idEmpresa)
        {
            return _filialService.Consultar(razaoSocial, idEmpresa);
        }

        public ValidationResult Inativar(int idFilial)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _filialService.Inativar(idFilial);
                
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Reativar(int idFilial)
        {
            try
            {
                using (var transaction = new TransactionScope(TransactionScopeOption.Required,
                    new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
                {
                    var validationResult = _filialService.Reativar(idFilial);
                
                    if (!validationResult.IsValid)
                        return validationResult;

                    transaction.Complete();
                    return new ValidationResult();   
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult AlterarStatus(int idFilial)
        {
            return _filialService.AlterarStatus(idFilial);
        }

        public IEnumerable<Filial> GetFiliaisAtualizadas(int? idEmpresa, DateTime dataAtualizacao)
        {
            return _filialService.GetFiliaisAtualizadas(idEmpresa, dataAtualizacao).ToList();
        }

        public int? GetIdPorCnpj(string cnpj)
        {
            return _filialService.GetIdPorCnpj(cnpj);
        }

        public List<string> GetCnpjList(int[] idFilialList)
        {
            return _filialService.GetCnpjList(idFilialList);
        }

        public int? GetIdPorCnpjTodos(string cnpj)
        {
            return _filialService.GetIdPorCnpjTodos(cnpj);
        }

        public int? GetIdPorCodigoFilial(int idEmpresa, string cnpjFilial, string codigoFilial)
        {
            return _filialService.GetIdPorCodigoFilial(idEmpresa, cnpjFilial, codigoFilial);
        }

        public string GetCnpjPorId(int idFilial)
        {
            return _filialService.GetCnpjPorId(idFilial);
        }

        public IQueryable<Filial> QueryById(int id)
        {
            return _filialService.QueryById(id);
        }

        public object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _filialService.ConsultarGrid(idEmpresa, take, page, order, filters);
        }

        public List<FilialContatos> GetContatosByFilial(int idFilial)
        {
            return _filialService.GetContatosByFilial(idFilial);
        }

        public byte[] GerarRelatorioGridFiliais(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            return _filialService.GerarRelatorioGridFilial(idEmpresa, orderFilters, filters, tipoArquivo, logo);
        }

        public IQueryable<Filial> GetQuery(int? idEmpresa)
        {
            return _filialService.GetQuery(idEmpresa);
        }

        public bool PertenceAEmpresa(int idEmpresa, int idFilial)
        {
            return _filialService.GetQuery(idEmpresa).Any(c => c.IdFilial == idFilial);
        }
    }
}
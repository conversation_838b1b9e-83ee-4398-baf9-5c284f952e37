﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Domain.Interface.Dapper
{
    public interface INotificacaoDapper : IRepositoryDapper<Notificacao>
    {
       bool Inserir(string idusuario, string tipo, string conteudo, DateTime datahoraenvio, int idTipoNotificacao);

    }
}
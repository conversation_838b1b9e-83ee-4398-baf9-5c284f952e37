﻿using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class ViagemDocumentoFiscal
    {
        /// <summary>
        /// Código do documento fiscal
        /// </summary>
        public int IdViagemDocumentoFiscal { get; set; }

        /// <summary>
        /// Código da viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Número do documento
        /// </summary>
        public decimal NumeroDocumento { get; set; }

        /// <summary>
        /// Série
        /// </summary>
        public string Serie { get; set; }
        public string Chave { get; set; }

        /// <summary>
        /// Peso de saída
        /// </summary>
        public decimal PesoSaida { get; set; }

        /// <summary>
        /// Valor
        /// </summary>
        public decimal? Valor { get; set; }

        /// <summary>
        /// Tipo de documento
        /// </summary>
        public ETipoDocumento TipoDocumento { get; set; }
        
        public int? IdClienteOrigem { get; set; }
        
        public int? IdClienteDestino { get; set; }

        #region Relacionamentos

        public virtual Viagem Viagem { get; set; }
        public virtual Cliente ClienteOrigem { get; set; }
        public virtual Cliente ClienteDestino { get; set; }

        #endregion
    }
}

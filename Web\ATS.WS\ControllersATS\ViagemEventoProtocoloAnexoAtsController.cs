using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersATS
{
    public class ViagemEventoProtocoloAnexoAtsController : DefaultController
    {
        private readonly IViagemEventoProtocoloAnexoApp _viagemEventoProtocoloAnexoApp;

        public ViagemEventoProtocoloAnexoAtsController(IViagemEventoProtocoloAnexoApp viagemEventoProtocoloAnexoApp)
        {
            _viagemEventoProtocoloAnexoApp = viagemEventoProtocoloAnexoApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Add(ViagemEventoProtocoloAnexoModel viagemEventoProtocoloAnexoModel)
        {
            try
            {
                var response = _viagemEventoProtocoloAnexoApp.Add(viagemEventoProtocoloAnexoModel);

                return response.IsValid
                    ? ResponderSucesso(string.Empty)
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AddListOfAnexos(List<ViagemEventoProtocoloAnexoModel> viagemEventoProtocoloAnexoModels, List<int> idsAnexosRemover)
        {
            try
            {
                var response = _viagemEventoProtocoloAnexoApp.AddListOfAnexos(viagemEventoProtocoloAnexoModels, idsAnexosRemover);

                return response.IsValid
                    ? ResponderSucesso(string.Empty)
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult RemoverAnexos(List<int> idsAnexosRemover)
        {
            try
            {
                var response = _viagemEventoProtocoloAnexoApp.RemoverAnexos(idsAnexosRemover);

                return response.IsValid
                    ? ResponderSucesso(string.Empty)
                    : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetAnexosCadastrados(int idViagemEvento)
        {
            try
            {
                var response = _viagemEventoProtocoloAnexoApp.GetAnexosCadastrados(idViagemEvento);
                return ResponderSucesso(response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
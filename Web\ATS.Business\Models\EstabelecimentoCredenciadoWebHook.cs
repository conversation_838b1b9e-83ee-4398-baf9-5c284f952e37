﻿using System.Collections.Generic;

namespace ATS.Domain.Models
{
    public class EstabelecimentoCredenciadoWebHook
    {
        public string CnpjEstabelecimento { get; set; }

        public string RazaoSocial { get; set; }

        public string NomeFantasia { get; set; }

        public string Pais { get; set; }

        public string Estado { get; set; }

        public string Cidade { get; set; }

        public int? CidadeIbge { get; set; }

        public string Bairro { get; set; }

        public string Logradouro { get; set; }

        public int? Numero { get; set; }

        public string Cep { get; set; }

        public string Email { get; set; }

        public string Telefone { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        public string Complemento { get; set; }

        public int? IdTipoEstabelecimento { get; set; }

        public string TipoEstabelecimento { get; set; }

        public string EmailProtocolo { get; set; }

        public bool Associacao { get; set; }

        public List<ProdutosEstabelecimentoWebHook> Produtos { get; set; }

        //public List<DadosBancariosEstabelecimentoWebHook> DadosBancarios { get; set; }
    }

    public class ProdutosEstabelecimentoWebHook
    {
        public int Id { get; set; }

        public string Descricao { get; set; }

        public string UnidadeMedida { get; set; }

        public decimal? PrecoUnitario { get; set; }

        public decimal? PrecoPromocional { get; set; }
    }

    public class DadosBancariosEstabelecimentoWebHook
    {
        public int Id { get; set; }

        public string NomeConta { get; set; }

        public string CodigoBanco { get; set; }

        public string NomeBanco { get; set; }

        public string Agencia { get; set; }

        public string Conta { get; set; }

        public string DigitoConta { get; set; }

        public string TipoConta { get; set; }

        public string CnpjTitular { get; set; }

        public string NomeTitular { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using NLog;

namespace ATS.CrossCutting.Reports.Faturamento
{
    public class RelatorioFaturamento
    {
        public byte[] GetReport(RelatorioFaturamentoDataType listaDados, string tipoArquivo, string logo)
        {
            try
            {
                var path = ReportUtils.CreateLogo(logo);

                var parametros = new Tuple<string, string, bool>[1];
                parametros[0] = new Tuple<string, string, bool>("Logo", "file:///" + path, true);

                var dataSources = new Tuple<object, string>(listaDados.DataGrid, "DtsFaturamento");

                var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                    "ATS.CrossCutting.Reports.Faturamento.RelatorioFaturamento.rdlc", tipoArquivo);

                return bytes;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e);
                throw;
            }
        }
    }
}

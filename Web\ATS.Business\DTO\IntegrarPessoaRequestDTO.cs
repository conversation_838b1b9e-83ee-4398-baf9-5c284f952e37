using System;

namespace ATS.Domain.DTO
{
    public class IntegrarPessoaRequestDTO
    {
        public int IdProprietario { get; set; }
        public string Nome { get; set; }
        public string NomeFantasia { get; set; }
        public string CpfCnpj { get; set; }
        public IntegrarPessoaEnderecoRequestDTO Endereco { get; set; }
        public IntegrarPessoaInfoRequestDTO Info { get; set; }
        public IntegrarPessoaTipoFlagsRequestDTO Flags { get; set; }
    }
    
    public class IntegrarPessoaEnderecoRequestDTO
    {
        public int? Cidade { get; set; }
        public string Cep { get; set; }
        public string Bairro { get; set; }
        public string Logradouro { get; set; }
        public int? Numero { get; set; }
        public string Complemento { get; set; }
    }

    public class IntegrarPessoaInfoRequestDTO
    {
        public string Sexo { get; set; }
        public string Rg { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string NomePai { get; set; }
        public string NomeMae { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public bool? TacEquiparado { get; set; }
    }

    public class IntegrarPessoaTipoFlagsRequestDTO
    {
        public bool? PontoDistribuicaoCartao;
        public bool? PontoCargaMoedeiro;
    }
}
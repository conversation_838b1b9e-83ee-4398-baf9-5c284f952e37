﻿using System;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;

namespace ATS.Application.Interface
{
    public interface IValePedagioApp : IAppBase<Viagem>
    {
        ObterExtratoSemPararResponseDTO GetExtratoSemParar(DateTime datainicio, DateTime datafim, string cnpjAplicacao);
        ConsultarStatusVeiculoSemPararDTO GetStatusVeiculoSemParar(string placa, string cnpjAplicacao);
        ConsultarPolylineDTO GetPolyline(int codPolyline, string cnpjAplicacao);
        ConsultarStatusVeiculoTaggyEdenredDTO GetStatusVeiculoTaggyEdenred(string placa, string cnpjAplicacao);
    }
}
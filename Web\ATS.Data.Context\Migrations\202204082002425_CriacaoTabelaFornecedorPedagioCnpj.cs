﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class CriacaoTabelaFornecedorPedagioCnpj : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.FORNECEDOR_PEDAGIO_CNPJ",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        idfornecedor = c.Int(nullable: false),
                        cnpj = c.String(nullable: false, maxLength: 100, unicode: false),
                    })
                .PrimaryKey(t => t.id);
            
        }
        
        public override void Down()
        {
            DropTable("dbo.FORNECEDOR_PEDAGIO_CNPJ");
        }
    }
}

using System;
using System.Web.Mvc;
using ATS.WS.Controllers.Base;

namespace ATS.WS.Controllers
{
    public class TesteWsController : BaseController
    {
        [HttpGet]
        public JsonResult Testar()
        {
            try
            {
                return Responde(new {Sucesso = true, Mensagem = "Web Service respondendo corretamente."});
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
                return Mensagem(e.Message);
            }
        }

        public TesteWsController(BaseControllerArgs baseArgs) : base(baseArgs)
        {
        }
    }
}
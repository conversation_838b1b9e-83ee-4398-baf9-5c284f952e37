﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.Grid;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Service
{
    public interface IEmpresaService : IService<Empresa>
    {
        Empresa Get(int id, int? idUsuarioLogOn);
        Empresa Get(int id);
        Empresa GetAsNoTracking(int id);
        ValidationResult Add(Empresa empresa);
        ValidationResult AddEmpresaIndicadores(EmpresaIndicadores empresaIndicadores);
        ValidationResult Update(Empresa empresa);
        ValidationResult UpdateEmpresaIndicadores(EmpresaIndicadores empresaIndicadores);
        IQueryable<Empresa> Consultar(string razaoSocial, int? idUsuarioLogOn);
        IQueryable<Empresa> All();
        Empresa GetWithChilds(int id);
        int? GetIdProdutoCartaoFretePadrao(int idEmpresa);
        List<ConsultaTodasEmpresasDto> ConsultarTodas(bool? ativo = null);
        bool AnyById(int id);
        bool EmpresaAgrupaProtocoloMesmoEvento(int id);
        int? GetDiasExpiracaoCompraPedagio(int idEmpresa);
        string GetLogoPorId(int idEmpresa);
        bool ValidaChaveBaixaEvento(int idEmpresa);
        bool ValidaChaveMhBaixaEvento(int idEmpresa);
        string GetLogoPorCnpj(string cnpjEmpresa);
        object GetParametrosPorCnpj(string cnpjEmpresa);
        object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, List<int> idsEstabelecimento, int? empresaId = null);
        ValidationResult AlterarStatus(int idEmpresa);
        object ConsultarGridEmpresa(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        byte[] GerarRelatorioGridEmpresas(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao);
        bool AnyRntrc(int idEmpresa);
        bool AtualizaUltimaExecucao(int idEmpresa);
        void CreateAuthenticationByCompany(Empresa empresa);
        Empresa GetByCnpj(string cnpjEmpresa);
        TarifasMeioHomologadoModel GetTarifasMeioHomologado(string cnpj);
        int? GetIdByCnpj(string cnpjEmpresa);
        string GetCnpjById(int empresaId);
        bool BloqueiaCancelamentoAbastecimento(int empresaId);
        object ConsultarGridEmpresasHubPedagio(int take, int page, OrderFilters order, List<QueryFilters> filters);
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class PagamentoConfiguracaoProcessoMap : EntityTypeConfiguration<PagamentoConfiguracaoProcesso>
    {
        public PagamentoConfiguracaoProcessoMap()
        {
            ToTable("PAGAMENTO_CONFIGURACAO_PROCESSO");

            HasKey(x => new { x.IdConfiguracao, x.IdDocumento, x.Processo});

            HasRequired(x => x.Documento)
                .WithMany(x => x.PagamentoConfiguracoesProcesso)
                .HasForeignKey(x => x.IdDocumento);

            HasRequired(x => x.Configuracao)
                .WithMany(x => x.PagamentoConfiguracoesProcesso)
                .HasForeignKey(x => x.IdConfiguracao); 
        }
    }
}

﻿using System;
using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Transactions;
using ATS.Application.Interface;
using ATS.Domain.DTO.CadastroRotas;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.DestinoRotaModelo;
using ATS.Domain.Models.DestinoRotaModelo.ATS.Domain.Models.DestinoRotaModelo;

namespace ATS.Application.Application
{
    public class RotaModeloApp : AppBase, IRotaModeloApp
    {
        private readonly IRotaModeloService _rotaModeloService;
        private readonly PedagioAppFactoryDependencies _pedagioAppFactoryDependencies;
        private readonly IParametrosApp _parametrosApp;

        public RotaModeloApp(IRotaModeloService rotaModeloService, PedagioAppFactoryDependencies pedagioAppFactoryDependencies, IParametrosApp parametrosApp)
        {
            _rotaModeloService = rotaModeloService;
            _pedagioAppFactoryDependencies = pedagioAppFactoryDependencies;
            _parametrosApp = parametrosApp;
        }

        public ValidationResult Add(RotaModelo rotaModelo)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _rotaModeloService.Add(rotaModelo);
                
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }
        
        public ValidationResult Editar(RotaModelo rotaModelo)
        {
            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                var validationResult = _rotaModeloService.Editar(rotaModelo);
                
                if (!validationResult.IsValid)
                    return validationResult;

                transaction.Complete();
            }

            return new ValidationResult();
        }
        
        public object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _rotaModeloService.ConsultarGrid(idEmpresa, take, page, order, filters);
        }
        
        public BusinessResult<RotaModeloResponse> GetById(int idRota)
        {
            var rotaModelo = _rotaModeloService.GetWithChilds(idRota);
            
            if(rotaModelo == null)
                return BusinessResult<RotaModeloResponse>.Error("Rota não localizada!");
            
            var retorno = new RotaModeloResponse()
            {
                IdRotaModelo = rotaModelo.IdRotaModelo,
                QtdeEixos = rotaModelo.QtdeEixos,
                TipoVeiculo = rotaModelo.TipoVeiculo,
                TipoRodagem = rotaModelo.TipoRodagem,
                CodigosIbge = new List<decimal>(),
                Destinos = new List<DestinoRotaModeloModel>(),
                NomeRota = rotaModelo.NomeRota,
                DataCadastro = rotaModelo.DataCadastro,
                IdaEVolta = rotaModelo.IdaEVolta,
                IdEmpresa = rotaModelo.IdEmpresa 
            };

            retorno.Destinos = SetarRetornoDestino(rotaModelo);
                
            //Retorno codigos Ibge
            foreach (var item in retorno.Destinos)
            {
                retorno.CodigosIbge.Add(item.Ibge);
            }

            if (rotaModelo.CodPolyline.HasValue && rotaModelo.IdEmpresa.HasValue)
            {
                var permiteUtilizarPolyline = _parametrosApp.GetUtilizaRoteirizacaoPorPolyline(rotaModelo.IdEmpresa ?? 0);

                if(!permiteUtilizarPolyline)
                    return BusinessResult<RotaModeloResponse>.Valid(retorno);
                
                var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies,rotaModelo.IdEmpresa ?? 0,"0000000000",null);

                var resultpolyline = pedagioApp.GetPolyline(rotaModelo.CodPolyline ?? 0);

                if (resultpolyline.Sucesso)
                    retorno.Polyline = resultpolyline.Polyline;
            }
            
            return BusinessResult<RotaModeloResponse>.Valid(retorno);
        }

        public RotaModelo GetWithChilds(int idRota)
        {
           return _rotaModeloService.GetWithChilds(idRota);
        }

        public RotaModelo GetByIdOrNomeRota(int? idRota, string nomeRota, int? idEmpresa)
        {
            return _rotaModeloService.GetByIdOrNomeRota(idRota,nomeRota,idEmpresa);
        }

        public BusinessResult<RotaModeloParametrosResponse> ConsultarParametros(int idEmpresa)
        {
            try
            {
                var utilizaPolylineRoteirizacao = _parametrosApp.GetUtilizaRoteirizacaoPorPolyline(idEmpresa);
                    
                return BusinessResult<RotaModeloParametrosResponse>.Valid(new RotaModeloParametrosResponse()
                {
                    UtilizaPolylineRoteirizacao = idEmpresa == 0 || utilizaPolylineRoteirizacao
                });
            }
            catch (Exception e)
            {
                return BusinessResult<RotaModeloParametrosResponse>.Error($"Falha na consulta: {e.Message}");
            }
        }

        public List<DetalhesRotaModeloModel> ConsultarDetalhes(RotaModelo rotaModelo)
        {
            return _rotaModeloService.ConsultarDetalhes(rotaModelo);
        }
        
        public List<DestinoRotaModeloModel> SetarRetornoDestino(RotaModelo rotaModelo)
        {
            return _rotaModeloService.SetarRetornoDestino(rotaModelo);
        }
    }
}

﻿
using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Protocolo
    {
        /// <summary>
        /// Código do protocolo
        /// </summary>
        public int IdProtocolo { get; set; }

        /// <summary>
        /// Código do estabelecimento base
        /// </summary>
        public int IdEstabelecimentoBase { get; set; }

        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }
        
        /// <summary>
        /// Valor do protocolo
        /// </summary>
        public decimal ValorProtocolo { get; set; }

        /// <summary>
        /// Data de geração do protocolo
        /// </summary>
        public DateTime DataGeracao { get; set; }

        /// <summary>
        /// Data de pagamento do protocolo
        /// </summary>
        public DateTime? DataPagamento { get; set; }

       
        /// <summary>
        /// Data em que a empresa realizou a aprovação do protocolo
        /// </summary>
        public DateTime? DataAprovacao { get; set; }

        /// <summary>
        /// Data em que a empresa preve a realização do pagamento do protocolo
        /// </summary>
        public DateTime? DataPrevisaoPagamento { get; set; }

        /// <summary>
        /// Data/Hora em que o protocolo foi rejeitado
        /// </summary>
        public DateTime? DataRejeicao { get; set; }

        /// <summary>
        /// Data de entrada em trânsito
        /// </summary>
        public DateTime? DataTransito { get; set; }

        public string CodigoRastreamento { get; set; }

        public DateTime? DataRecebidoEmpresa { get; set; }
        
        public bool EstabPagamentoAntecipado { get; set; }

        public bool OcorrenciaPendente { get; set; }

        public bool GeradoAssociacao { get; set; } = false;

        /// <summary>
        /// Indica se o protocolo já foi processado por algum WS
        /// </summary>
        public bool Processado { get; set; }

        public ETipoDestinatario TipoDestinatario { get; set; } = ETipoDestinatario.Empresa;

        /// <summary>
        /// Indica se o protocolo já foi pago ou não
        /// </summary>
        public EStatusProtocolo StatusProtocolo { get; set; }

        public int? IdEstabelecimentoDestinatario { get; set; }

        public int? IdEmpresaDestinatario { get; set; }

        public int? IdUsuarioAprovacao { get; set; }
        
        public string TiposEventos { get; set; }
        
        public ETiposPagamentos TiposPagamentos { get; set; }
        
        public bool PossuiEventoReincidente { get; set; }
        
        //public EResponsavelProtocolo ResponsavelProtocolo { get; set/ }

        #region Virtual Fields
        public virtual EstabelecimentoBase EstabelecimentoBase { get; set; }
        public virtual Empresa Empresa { get; set; }

        public virtual Usuario UsuarioAprovacao { get; set; }

        public virtual Estabelecimento EstabelecimentoDestinatario { get; set; }
        public virtual Empresa EmpresaDestinatario { get; set; }
        public virtual ICollection<ViagemEvento> ViagemEventos { get; set; }
        public virtual ICollection<ProtocoloEvento> ProtocoloEventos { get; set; }
        public virtual ICollection<ProtocoloAnexo> ProtocoloAnexos { get; set; }
        public virtual ICollection<ProtocoloAntecipacao> ProtocoloAntecipacoes { get; set; }

        #endregion
    }
}
﻿namespace ATS.Domain.Entities
{
    public class EstabelecimentoProduto
    {
        public int IdEstabelecimento { get; set; }
        public int IdProduto { get; set; }
        public int? IdProdutoBase { get; set; }
        public int? IdEstabelecimentoBase { get; set; }
        public string Descricao { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? PrecoUnitario { get; set; }
        public decimal? PrecoPromocional { get; set; }
        public bool Contrato { get; set; } = false;

        #region Relacionalmentos

        public virtual Estabelecimento Estabelecimento { get; set; }
        public virtual EstabelecimentoBaseProduto ProdutoBase { get; set; }

        #endregion

    }
}

<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="tabs-container">
                <ul class="nav nav-tabs" id="ulParametros">
                    <li id="Tab7" ng-class="{active : vm.activeTabParametros === 'Tab7'}" ng-show="vm.exibirModulo(1)">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab7'">
                            <i class="fa fa-gear"></i> Configuração
                        </a>
                    </li>
                    <li id="Tab8" ng-class="{active : vm.activeTabParametros === 'Tab8'}" ng-show="vm.exibirModulo(7)">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab8'">
                            <i class="fa fa-asterisk"></i> Pag. de Frete</a>
                    </li>
                    <li id="Tab9" ng-class="{active : vm.activeTabParametros === 'Tab9'}" ng-show="vm.exibirModulo(7)">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab9'">
                            <i class="fa fa-money"></i> Meio Homologado
                        </a>
                    </li>
                    <li ng-class="{active : vm.activeTabParametros === 'Tab10'}">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab10'"
                            ng-show="vm.usuarioPermiteModuloAlcadas">
                            <i class="fa fa-money"></i> Alçadas e Limites</a>
                    </li>
                    <li ng-class="{active : vm.activeTabParametros === 'Tab11'}" ng-show="vm.exibirModulo(6)">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab11'">
                            <i class="fa fa-handshake-o"></i> Credenciamento</a>
                    </li>
                    <li ng-class="{active : vm.activeTabParametros === 'Tab12'}" ng-show="vm.isAdministradorLogado">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab12'">
                            <i class="fa fa-headphones"></i> Plataforma de Atendimento</a>
                    </li>
                    <li ng-class="{active : vm.activeTabParametros === 'Tab13'}">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab13'">
                            <i class="fa fa-asterisk"></i> CNAB</a>
                    </li>
                    <li ng-class="{active : vm.activeTabParametros === 'TabPay'}" ng-show="vm.isAdministradorLogado">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'TabPay'">
                            <i class="fa fa-money"></i> Extratta Pay</a>
                    </li>
                    <li ng-class="{active : vm.activeTabParametros === 'Tab14'}" ng-show="vm.isAdministradorLogado">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab14'">
                            <i class="fa fa-road"></i> Tag Extratta</a>
                    </li>
                    <li id="Tab15" ng-class="{active : vm.activeTabParametros === 'Tab15'}" ng-show="vm.usuarioPermiteEdicaoDadosAdministrativosEmpresa">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab15'">
                            <i class="fa fa-road"></i> Movemais
                        </a>
                    </li>
                    <li id="Tab16" ng-class="{active : vm.activeTabParametros === 'Tab16'}" ng-show="vm.usuarioPermiteEdicaoDadosAdministrativosEmpresa">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab16'">
                            <i class="fa fa-road"></i> Sem Parar
                        </a>
                    </li>
                    <li id="Tab17" ng-class="{active : vm.activeTabParametros === 'Tab17'}" ng-show="vm.usuarioPermiteEdicaoDadosAdministrativosEmpresa">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab17'">
                            <i class="fa fa-road"></i> Veloe
                        </a>
                    </li>
                    <li id="Tab18" ng-class="{active : vm.activeTabParametros === 'Tab18'}" ng-show="vm.usuarioPermiteEdicaoDadosAdministrativosEmpresa">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'Tab18'">
                            <i class="fa fa-road"></i> ConectCar
                        </a>
                    </li>
                    <li id="TabTaggy" ng-class="{active : vm.activeTabParametros === 'TabTaggy'}" ng-show="vm.usuarioPermiteEdicaoDadosAdministrativosEmpresa">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'TabTaggy'">
                            <i class="fa fa-road"></i> Taggy Edenred
                        </a>
                    </li>
                    <li id="TabPix" ng-class="{active : vm.activeTabParametros === 'TabPix'}" ng-show="vm.usuarioPermiteEdicaoDadosAdministrativosEmpresa">
                        <a data-toggle="tab" ng-click="vm.activeTabParametros = 'TabPix'">
                            <i class="fa fa-road"></i> Pix
                        </a>
                    </li>
                </ul>
                <div class="tab-content">
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab7'}">
                        <div class="panel-body">
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-5">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.ValidacaoPagFrete" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;" tooltip-placement="top"
                                        uib-tooltip="Se estiver marcado 'Sim', irá trazer os dados da viagem no retorno do Web Service de integração de viagem.">
                                        Validação de pagamento de frete
                                        <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                    </label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-5">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.OcultarNovoCadastros" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Ocultar botão novo para cadastros</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-5">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.HabilitarAgendamentoPagamentoFrete" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;" tooltip-placement="top"
                                        uib-tooltip="Ativando o recurso será permitido agendar pagamento de frente automaticamente para datas futuras na integração de viagem e no cadastro pelo portal. Com o recurso desativado o cliente receberá erro durante a integração da viagem caso enviar o status '5-Agendado'.">
                                        Habilitar agendamento de pagamento de frete
                                        <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                    </label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-12">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Código
                                            da empresa no ERP do cliente:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-1">
                                            <input type="text" ng-model="vm.empresa.IdSistemaExterno"
                                                name="IdSistemaExterno" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-12">
                                    <div class="form-group">
                                        <label
                                            uib-tooltip="Key utilizada para consultas de ponto de referência e gestão de entregas."
                                            style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            key Google:
                                            <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                        </label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" maxlength="200" ng-model="vm.empresa.KeyGoogle"
                                                ng-init="vm.empresa.TempoParada"
                                                name="KeyGoogle" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-12">
                                    <div class="form-group">
                                        <label
                                            uib-tooltip="Endpoint base para envio de notificações near real time ao cliente. Será concatenado a ação realizada ao endpoint base informado (Exemplo: Ao informar http://api.meucliente.com.br, quando houver uma baixa de viagem agendada, o cliente receberá um POST na rota http://api.meucliente.com.br/baixar-evento-viagem)"
                                            style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Endpoint base para notificação por webhook
                                            <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                        </label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" maxlength="255" ng-model="vm.empresa.WebHookEndpoint"
                                                name="WebHookEndpoint" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-12">
                                    <div class="form-group">
                                        <label
                                            uib-tooltip="Código OFX padrão caso veha vazio da BIZ"
                                            style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Código Ofx:                                        
                                            <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                        </label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" maxlength="255" ng-model="vm.empresa.CodOfx"
                                                name="CodOfx" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab8'}">
                        <div class="panel-body">
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.AgrupaProtocoloMesmoEvento" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Agrupar protocolo com mesmo tipo de
                                        evento</label>
                                </div>
                            </div>
                            <!-- <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.ValidaChaveBaixaEvento" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Validar chave na baixa de evento carta frete</label>
                                </div>
                            </div> -->
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.ValidaChaveMHBaixaEvento" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Validar chave na baixa de evento</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.NaoValidarProtocoloRecebidoEmpresa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Não validar protocolo recebido na empresa</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.CancelaViagemComProtocolo" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Permite cancelar viagem com protocolo
                                        gerado</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.UtilizaValidacaoPorPerfilNoPagamentoDeFrete"
                                        class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Utiliza validação de documentos por
                                        perfil</label>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Periodicidade para bloqueio de eventos em aberto:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.PeriodoBloqueioEventosAberto" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Tolerância
                                            de peso de chegada (+):</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" atsPrice ui-number-mask="2"
                                                ng-model="vm.empresa.PagamentoFreteToleranciaPesoChegadaMaisStr"
                                                name="% Tolerância de peso de chegada (+)" class="form-control"
                                                validate-on="blur" />
                                            <span class="input-group-addon">
                                                %
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Tolerância
                                            de peso de chegada (-):</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" atsPrice ui-number-mask="2"
                                                ng-model="vm.empresa.PagamentoFreteToleranciaPesoChegadaMenosStr"
                                                name="% Tolerância de peso de chegada (-)" class="form-control"
                                                validate-on="blur" />
                                            <span class="input-group-addon">
                                                %
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Tempo
                                            de validade da chave (minutos):</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.TempoValidadeChaveStr"
                                                ui-number-mask="0" name="Tempo de validade da chave (minutos)"
                                                class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Dias
                                            para bloqueio de evento em aberto:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.DiasParaBloquearPagtoStr"
                                                ui-number-mask="0" name="Dias para bloqueio de evento em aberto"
                                                class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Tempo
                                            de validade do hash para transação nos estabelecimentos (minutos):</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text"
                                                ng-model="vm.empresa.TempoValidadeChaveEstabelecimentoStr"
                                                ui-number-mask="0"
                                                name="Tempo de validade do hash para transação nos estabelecimentos (minutos)"
                                                class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Dias
                                            para regularizar documentação:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.PrazoParaInformarDocumentosStr"
                                                ui-number-mask="0" name="Dias para regularizar documentação"
                                                class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Endpoint
                                            integração protocolo:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.WebHookProtocoloEndPoint"
                                                name="Endpoint integração protocolo" class="form-control"
                                                validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor saldo minímo da conta frete para notificação aos gestores da empresa:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.ValorSaldoContaFreteMinimoNotificacaoEmail" ui-number-mask="2"
                                                name="Endpoint integração protocolo" class="form-control"
                                                validate-on="blur" />
                                            <small>* As notificações serão enviadas com valores a partir de 0</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Headers
                                            integração protocolo:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <textarea type="text" rows="6" cols="20" name="Headers integração protocolo"
                                                ng-model="vm.empresa.WebHookProtocoloHeaders" class="form-control" />
                                            </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor saldo minímo da conta pix para notificação aos gestores da empresa:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.ValorSaldoContaPixMinimoNotificacaoEmail" ui-number-mask="2"
                                                name="Endpoint integração protocolo" class="form-control"
                                                validate-on="blur" />
                                            <small>* As notificações serão enviadas com valores a partir de 0</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">E-mail de contato do setor de carta frete:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.EmailCartaFrete" name="E-mail de contato do setor de carta frete"
                                                class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab9'}">
                        <div class="panel-body">
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-4">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.HabilitarMeioHomologado" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Habilitar integração meio homologado</label>
                                </div>
                                <div class="col-md-8" ng-if="vm.showToken">
                                    <label class="col-xs-12 col-sm-3 col-md-3 control-label">
                                        TOKEN:</label>
                                    <div class="col-xs-12 col-sm-7 col-md-7 input-group">
                                        <input type="text" uib-tooltip="Copiar"
                                                ng-model="vm.empresa.Token"
                                                name="Token" class="form-control"
                                                id="input-token"
                                                disabled/>
                                        <span class="input-group-btn">
                                                <button type="button" ng-class="'btn btn-info'" id="btn-copy" ng-click="vm.copiarAreaTransf()">
                                                    <i class="fa fa-copy"></i>
                                                </button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-show="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" is-disabled="true" ng-model="vm.empresa.VinculoNovoCartaoPortador" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;">Permitir vinculo de novo cartão quando portador ( CPF / CNPJ ) possuir cartão ativo</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-show="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" is-disabled="true" ng-model="vm.empresa.VinculoNovoCartaoBloqueadoPortador" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;">Novo cartão vinculado com status BLOQUEADO</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.AutorizaEstabelecimentosRedeJSL" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Autoriza automaticamente os estabelecimentos da rede JSL</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.ObrigaRoteirizacaoPedagioViagem" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Obriga informar valor de pedágio no cadastro de viagem</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.ReemiteCiotPadraoAlteracaoViagem" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Reemite CIOT Padrão para alterações na Viagem</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.RegistrarValePedagio" class="switch-mini" ng-change="vm.registrarValePedagioChange()"
                                       tooltip-placement="top" uib-tooltip="Não é necessário desmarcar esta opção caso o fornecedor de pedágio já faça o registro do vale pedágio"></toggle-switch>
                                    <label style="margin-left: 10px;"> Registrar vale pedágio</label>
                                </div>
                                <span class="col-md-12" ng-show="!vm.empresa.RegistrarValePedagio" style="float: right;color: red; margin-bottom: 10px; margin-top: 0px; text-align: left">Atenção! É obrigatório o registro do vale pedágio, ao desmarcar esta opção tenha garantia de que este processo é feito de outra forma.</span>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.BaixarParcelaBloqueado" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Permitir baixar/efetivar parcelas com status bloqueado</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.PedagioTag" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;"> Aplicar cálculo de desconto quando for TAG</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.IntegrarComoUsuario" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Criar usuários através de cadastro de motorista e/ou proprietário</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.PermiteResgate" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Permitir resgate valor (despesas de viagem)</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%"  ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.AcessarExtrato" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Acessar e exportar extrato do portador (despesas de viagem)</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.ListarCnpjGridDespesasViagem" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Listar CNPJ na grid (despesas de viagem)</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.SenhaResgate" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;"> Solicitar senha cartão para resgate</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.ParcelaViagemAberta" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Permitir inclusão de parcelas em viagem aberta</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.BloquearNovaViagem" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Bloquear nova viagem quando tiver viagem aberta para mesmo motorista e/ou placa do cavalo</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.UtilizaCacheRotas" on-change="vm.toggleCacheRotas()" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Utilizar cache de Rota nos processos de Roteirização</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.UtilizaRoteirizacaoPorPolyline" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Utiliza Polyline para roteirização e permite a importação de polyline na tela "Rota"</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado" >
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.GerarCiotViagemInternacional" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Gerar CIOT para viagens internacionais</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado" >
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.MantemViagemAbertaAposCancelamentoDoUltimoEvento" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Mantém viagem aberta após cancelamento do último evento</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado" >
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.MantemViagemAbertaAposBaixaDoUltimoEvento" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Mantém viagem aberta após baixa do último evento</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado" >
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.PermiteVincularCartaoComCpfFicticio" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Permite vincular cartão para CPF fictício</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-show="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.UtilizaRelatoriosOfx" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;">Permite usuários da empresa gerarem relatórios na extensão OFX</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-show="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.DefaultIntegracaoTipoRodagemDupla" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;">Rodagem dupla como default na integração (caso valor venha nulo)</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="vm.isAdministradorLogado">
                                <div class="col-md-8">
                                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.empresa.RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe" class="switch-mini" ></toggle-switch>
                                    <label style="margin-left: 10px;">Usa tipo de rodagem Dupla quando tem 3 ou mais eixos na compra de pedágio para MoveMais e Veloe</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 2%" ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-sm-3 col-md-4 col-lg-5 control-label" tooltip-placement="top" uib-tooltip="Códigos de históricos paraintegração com micro serviço de cartões
                                        Essa combo, pode aparecer somente para o perfil administrador.">Configuração de históricos para transações:
                                            <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                        </label>
                                        <div class="input-group col-sm-9 col-md-8 col-lg-5">
                                            <ui-select ng-model="vm.empresa.IdLayoutCartao" name="Configuração de históricos para transações" ats-ui-select-validator
                                                name="Configuração de históricos para transações"
                                                ng-disabled="vm.consultando > 0">
                                                <ui-select-match placeholder="{{vm.placeHolderHistTransacoes}}">
                                                    <span>{{$select.selected.Nome}}</span>
                                                </ui-select-match>
                                                <ui-select-choices repeat="ez.IdLayout as ez in vm.confHistorico | propsFilter: {Nome: $select.search}">
                                                    <div ng-bind-html="ez.Nome | highlight: $select.search"></div>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 2%" ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-sm-3 col-md-4 col-lg-5 control-label" tooltip-placement="top" uib-tooltip="Tipo de cartão utilizado para operações de frete durante o pagamento de eventos vinculados a viagem e para cargas avulsas.">Cartão padrão para operações de frete:
                                            <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                        </label>
                                        <div class="input-group col-sm-9 col-md-8 col-lg-5">
                                            <ui-select ng-model="vm.empresa.IdProdutoCartaoFretePadrao" name="Tipo de cartão utilizado para operações de frete durante o pagamento de eventos vinculados a viagem e para cargas avulsas."
                                                ats-ui-select-validator name="Tipo de cartão utilizado para operações de frete durante o pagamento de eventos vinculados a viagem e para cargas avulsas."
                                                ng-disabled="vm.consultando > 0">
                                                <ui-select-match placeholder="{{vm.placeHolderCartao}}">
                                                    <span>{{$select.selected.Nome}}</span>
                                                </ui-select-match>
                                                <ui-select-choices repeat="ey.Id as ey in vm.produtos | propsFilter: {Nome: $select.search}">
                                                    <div ng-bind-html="ey.Nome | highlight: $select.search"></div>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-sm-3 col-md-4 col-lg-5 control-label" tooltip-placement="top" uib-tooltip="Os e-mails devem ser separados por ponto e vírgula(;).">
                                            E-mails para alertas de CIOT Agregado:
                                            <img src="assets/images/info-16.png" class="img-tooltip" alt="Informações">
                                        </label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.EmailsAlertaCiotAgregado"
                                                   name="EmailCiotAgregado" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Dias para cancelamento de Viagem:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.DiasCancelamentoViagem"
                                                   ui-number-mask="0" maxlength="3" name="DiasCancelamentoViagem" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Horas limite para carga do crédito de pedágio:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.HorasExpiracaoCreditoPedagio"
                                                   ui-number-mask="0" maxlength="3" name="Horas limite para carga do crédito de pedágio" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Tempo de expiração de saldo do pedágio(dias):</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="number" name="DiasExpiracaoSaldoPedagio" class="form-control" validate-on="blur"
                                                   ng-model="vm.empresa.DiasExpiracaoSaldoPedagio" ng-if="!vm.empresa.RegistrarValePedagio" ng-change="vm.diasExpiracaoSaldoPedagioChange(vm.empresa.RegistrarValePedagio)" />
                                            <input type="number" name="DiasExpiracaoSaldoPedagio" class="form-control" validate-on="blur"
                                                   ng-model="vm.empresa.DiasExpiracaoSaldoPedagio" ng-if="vm.empresa.RegistrarValePedagio" ng-change="vm.diasExpiracaoSaldoPedagioChange(vm.empresa.RegistrarValePedagio)" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" ng-hide="!vm.exibirSomenteAdm()">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-sm-3 col-md-4 col-lg-5 control-label" tooltip-placement="top">
                                            Ação a realizar com o saldo residual do cartão pedágio ao efetuar nova carga:
                                        </label>
                                        <div class="input-group col-sm-9 col-md-8 col-lg-5">
                                            <ui-select ng-model="vm.empresa.AcaoSaldoResidualNovoCreditoCartaoPedagio" name="Ação de saldo residual do cartão" ats-ui-select-validator 
                                            name="Tipo de Carregamento Frete">
                                                <ui-select-match>
                                                    <span>{{$select.selected.Descricao}}</span>
                                                </ui-select-match>
                                                <ui-select-choices repeat="ex.Codigo as ex in vm.TipoAcaoSaldoResidual | propsFilter: {Descricao: $select.search}">
                                                    <div ng-bind-html="ex.Descricao | highlight: $select.search"></div>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab10'}">
                        <div class="panel-body">
                            <label style="color: red;width: 100%;text-align: unset;margin-bottom: 15pt;">* Limite padrão para todas as filiais da empresa.</label>
                            <ul class="nav nav-tabs row" id="ulAlcadas">
                                <li id="Tab1" ng-class="{active : vm.activeTabAlcadas === 'Tab1'}" class="col-xs-12 col-sm-6 col-md-6">
                                    <a data-toggle="tab" ng-click="vm.activeTabAlcadas = 'Tab1'">
                                        <i class="fa fa-cloud"></i> API (Integração)
                                    </a>
                                </li>
                                <li id="Tab2" ng-class="{active : vm.activeTabAlcadas === 'Tab2'}" class="col-xs-12 col-sm-6 col-md-6">
                                    <a data-toggle="tab" ng-click="vm.activeTabAlcadas = 'Tab2'">
                                        <i class="fa fa-globe"></i> Portal
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane" ng-class="{active : vm.activeTabAlcadas === 'Tab1'}">
                                    <div class="row mt-10">
                                        <table class="table table-hover" >
                                            <thead>
                                                <tr>
                                                    <th scope="col">Descrição</th>
                                                    <th scope="col">Valor</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="item in vm.empresa.AlcadasBloqueioGestorValor.Api">
                                                    <td>
                                                        <label style="text-align: left" class="control-label" tooltip-placement="top">
                                                            {{item.DescricaoBloqueioGestorTipo}}
                                                        </label>
                                                    </td>
                                                    <td>
                                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                                            <span class=" input-group-addon">R$</span>
                                                            <input type="text" ui-number-mask="2" ng-model="item.Valor" class="form-control" validate-on="blur" />
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="tab-pane" ng-class="{active : vm.activeTabAlcadas === 'Tab2'}">
                                    <div class="row mt-10">
                                        <table class="table table-hover" >
                                            <thead>
                                                <tr>
                                                    <th scope="col">Descrição</th>
                                                    <th scope="col">Valor</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="item in vm.empresa.AlcadasBloqueioGestorValor.Portal">
                                                    <td>
                                                        <label style="text-align: left" class="control-label" tooltip-placement="top">
                                                            {{item.DescricaoBloqueioGestorTipo}}
                                                        </label>
                                                    </td>
                                                    <td>
                                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                                            <span class=" input-group-addon">R$</span>
                                                            <input type="text" ui-number-mask="2" ng-model="item.Valor" class="form-control" validate-on="blur" />
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab11'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.Atualizaestabelecimento" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Atualiza Estabelecimento</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-md-6">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermiteCredenciamento" class="switch-mini"></toggle-switch>
                                    <label style="margin-left: 10px;"> Permite solicitação de credenciamento de
                                        estabelecimentos</label>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-12">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Horas
                                            validade e-mail de cadastro de usuário tipo estabelecimento:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-1">
                                            <input type="number" min="0" max="999" maxlength="3"
                                                   ng-model="vm.empresa.HorasValidadeChaveCadastroUsuario"
                                                   ng-disabled="!vm.empresa.PermiteCredenciamento"
                                                   name="HorasValidadeChaveCadastroUsuario" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.RealizaTriagemEstabelecimentoInterno" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Realiza triagem de documentos de
                                        credenciamentos feitos internamente</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab12'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.BloquearCartao" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Bloquear cartão </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.DesbloquearCartao" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Desbloquear cartão </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.AlterarSenhaCartao" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Alterar senha cartão </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.TransferenciaBancaria" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Transferência bancária </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.TransferenciaCartoes" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Transferência entre cartões </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.Resgate" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Resgate de saldo </label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-5" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.PermissoesAtendimentoCartao.EstornoResgate" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Estorno de resgate de saldo </label>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Token micro serviço para central de atendimento:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.TokenMicroServicoCentralAtendimento" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab13'}">
                        <div class="panel-body">
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Nome da empresa:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.NomeFantasia" class="form-control" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Tipo de inscrição da empresa:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.TipoInscricaoEmpresa" class="form-control" maxlength="1" ats-numeric/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Código no banco da compensação:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.CodigoBancoCompensacao" class="form-control" maxlength="3" ats-numeric/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Número da inscrição da empresa:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.NumeroInscricaoEmpresa" class="form-control" maxlength="14" ats-numeric/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Código do convênio do banco:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.CodigoConvenioBanco" class="form-control" maxlength="20"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Agência mantenedora da conta:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.AgenciaMantenedora" class="form-control" maxlength="5" ats-numeric />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">
                                            Digito verificador da conta:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.DigitoVerificaConta" class="form-control" maxlength="1"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Digito verificador da Ag/Conta:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <input type="text" ng-model="vm.empresa.DigitoVerificaContaAgConta" class="form-control"  maxlength="1"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab14'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaPermiteUtilizarFornecedor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Permite utilização do fornecedor Tag Extratta</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 10pt;">
                                <hr-label dark="true" title="'Vale Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaProvisionarValor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor rota no ato da emissão)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaProvisionarTaxa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa da passagem)</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 10pt;">
                                <hr-label dark="true" title="'Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaProvisionarValorPedagioWebhook" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor passagem confirmação da concessionária)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaProvisionarTaxaPedagioWebhook" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa de passagem)</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                    ng-model="vm.empresa.TagExtrattaEstornarValorPedagioWebhook" class="switch-mini">
                                    </toggle-switch>
                                <label style="margin-left: 10px;">Estornar passagens automaticamente</label>
                                </div>
                                <div class="col-md-6">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaUtilizaTaxaPedagio" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Cobrar taxa na passagem</label>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Valores'"></hr-label>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Taxa para emissão de VP:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">%</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaTaxaVpo" class="form-control" validate-on="blur" />
                                            <small>* A taxa precisa ser maior que 0,20%</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor da Tag:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaValorTag" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor substituição Tag:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaValorSubstituicao" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor da mensalidade da Tag Extratta:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaValorMensalidade" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10" ng-show="false">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor da taxa de inatividade (por Tag):</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="" class="form-control" validate-on="blur" />
                                            <small>* Serão consideradas Tags inativas as que possuem status aguardando vínculo</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Dias para cobrança da taxa de inatividade:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <input type="text" ui-number-mask="2" ng-model="" class="form-control" validate-on="blur" />
                                            <small>* Data recebimento da remessa</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Valor da taxa de recarga da conta:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaValorRecargaConta" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Bloqueios'"></hr-label>
                            <div class="row mt-20">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-12 col-sm-2 col-md-4 col-lg-6 control-label">Tolerância: Disparar e-mail quando saldo menor igual a:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaFaixaToleranciaNotificacaoEmail" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-6 control-label">Bloqueio: Disparar bloqueio quando saldo menor a:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">R$</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TagExtrattaSaldoMinimoContaFreteWebhook" class="form-control" validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-10">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                        ng-model="vm.empresa.TagExtrattaBloquearTagUnitariaWebhook" class="switch-mini" ng-change="vm.changeBlqueioTag(2)">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Bloquear todas as Tag da empresa caso saldo abaixo do estipulado</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.TagExtrattaBloquearTagLoteWebhook" class="switch-mini" ng-change="vm.changeBlqueioTag(1)">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;"> Bloquear apenas Tag da passagem caso saldo abaixo do estipulado</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab15'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.UtilizaCredenciaisExtrattaCompraMoveMais" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Utiliza credenciais da Extratta para compra do VPO</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 10pt;">
                                <hr-label dark="true" title="'Vale Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubMoveMaisProvisionarValor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor rota no ato da emissão)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubMoveMaisProvisionarTaxa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa da passagem)</label>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Valores'"></hr-label>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Taxa para emissão de VP:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">%</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.MoveMaisExtrattaTaxaVpo" class="form-control" validate-on="blur" />
                                            <small>* A taxa precisa ser maior que 0,20%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Configurações'"></hr-label>
                            <div class="row mt-30" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Login acesso Move Mais:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.LoginAcessoMoveMais" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Login Acesso Move Mais" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Senha acesso Move Mais:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.SenhaAcessoMoveMais" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Senha Acesso Move Mais" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Informações Transferências Bancárias:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-6">
                                            <textarea type="text" rows="6" cols="20" name="Informações Transferências Bancárias"
                                                      ng-model="vm.empresa.InformacoesTransferenciaBancaria" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab16'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.UtilizaCredenciaisExtrattaCompraViaFacil" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Utiliza credenciais da Extratta para compra do VPO</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 10pt;">
                                <hr-label dark="true" title="'Vale Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubViaFacilProvisionarValor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor rota no ato da emissão)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubViaFacilProvisionarTaxa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa da passagem)</label>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Valores'"></hr-label>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Taxa para emissão de VP:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">%</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.ViaFacilExtrattaTaxaVpo" class="form-control" validate-on="blur" />
                                            <small>* A taxa precisa ser maior que 0,90%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Configurações'"></hr-label>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Código acesso Sem Parar:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.CodigoAcessoViaFacil" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Código Acesso Via Facil" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Login acesso Sem Parar:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.LoginAcessoViaFacil" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Login Acesso Via Facil" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Senha acesso Sem Parar:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.SenhaAcessoViaFacil" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Senha Acesso Via Facil" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                            class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Cadastrar como embarcador:
                                        </label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" 
                                                    ng-model="vm.msgCadastroEmbarcador"
                                                    name="msgCadastroEmbarcador" class="form-control"
                                                    id="input-msgCadastroEmbarcador"
                                                    readonly/>
                                            <span class="input-group-btn">
                                                    <button type="button" ng-class="'btn btn-success'" ng-click="vm.cadastrarEmbarcadorViaFacilSTP()" uib-tooltip="Cadastrar" ng-disabled="vm.loadingCadastroEmbarcador">
                                                        <i class="fa fa-check-circle"></i>
                                                    </button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Dias para data de expiração do pedágio:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-1">
                                            <input  type="text" ui-number-mask="0" maxlength="2" ng-model="vm.empresa.DiasDataExpiracaoPedagioViaFacil" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Dias para data de expiração do pedágio" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab17'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.UtilizaCredenciaisExtrattaCompraVeloe" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Utiliza credenciais da Extratta para compra do VPO</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 10pt;">
                                <hr-label dark="true" title="'Vale Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubVeloeProvisionarValor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor rota no ato da emissão)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubVeloeProvisionarTaxa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa da passagem)</label>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Valores'"></hr-label>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Taxa para emissão de VP:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">%</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.VeloeExtrattaTaxaVpo" class="form-control" validate-on="blur" />
                                            <small>* A taxa precisa ser maior que 0,75%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Configurações'"></hr-label>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">CNPJ Embarcador:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.CnpjEmbarcadorVeloe" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Login Acesso Move Mais" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Tenant Id:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.TenantIdVeloe" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Login Acesso Move Mais" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Username:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.UserNameVeloe" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Login Acesso Move Mais" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Password:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.PasswordVeloe" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Senha Acesso Move Mais" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                                class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Basic Auth:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.BasicAuthVeloe" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                    name="Login Acesso Move Mais" class="form-control"
                                                    validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'Tab18'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.UtilizaCredenciaisExtrattaCompraConectCar" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Utiliza credenciais da Extratta para compra do VPO</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 1%;">
                                <hr-label dark="true" title="'Vale Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubConectCarProvisionarValor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor rota no ato da emissão)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubConectCarProvisionarTaxa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa da passagem)</label>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Valores'"></hr-label>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Taxa para emissão de VP:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">%</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.ConectCarExtrattaTaxaVpo" class="form-control" validate-on="blur" />
                                            <small>* A taxa precisa ser maior que 0,80%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Configurações'" ng-if="false"></hr-label>
                            <div class="row" style="margin-top: 1%" ng-if="false">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Login acesso ConectCar:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.LoginAcessoConectCar" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Login acesso ConectCar" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="false">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Senha acesso ConectCar:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.SenhaAcessoConectCar" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Senha acesso ConectCar" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div> 
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'TabPay'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.AprovacaoGestorCargaAvulsaLote" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Carga Avulsa em lote solicita aprovação do gestor</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.AprovacaoGestorCargaAvulsaUnitario" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Carga Avulsa unitário solicita aprovação do gestor</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.AprovacaoGestorCargaAvulsaIntegracao" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Carga Avulsa via integração solicita aprovação do gestor</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.BloqueiaCargaAvulsaDuplicada" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Bloqueia cargas avulsas duplicadas para mesmo valor, documento e portador</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'TabTaggy'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.UtilizaCredenciaisExtrattaCompraTaggyEdenred" class="switch-mini" ng-disabled="vm.empresa.FalhaComunicacaoPedagio">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Utiliza credenciais da Extratta para compra do VPO</label>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 1%;">
                                <hr-label dark="true" title="'Vale Pedágio'"></hr-label>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubTaggyEdenredProvisionarValor" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Cobrar valor rota no ato da emissão)</label>
                                </div>
                                <div class="col-md-6" style="margin-top: 2%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.HubTaggyEdenredProvisionarTaxa" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Pré-Pago: (Descontar valor da taxa da passagem)</label>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Valores'"></hr-label>
                            <div class="row mt-10">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label style="text-align: left" class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Taxa para emissão de VP:</label>
                                        <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                            <span class=" input-group-addon">%</span>
                                            <input type="text" ui-number-mask="2" ng-model="vm.empresa.TaggyEdenredExtrattaTaxaVpo" class="form-control" validate-on="blur" />
                                            <small>* A taxa precisa ser maior que 0,30%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr-label dark="true" title="'Configurações'" ng-if="false"></hr-label>
                            <div class="row" style="margin-top: 1%" ng-if="false">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Login acesso Taggy Edenred:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="text" ng-model="vm.empresa.LoginAcessoTaggyEdenred" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Login acesso Taggy Edenred" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="false">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Senha acesso Taggy Edenred:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.SenhaAcessoTaggyEdenred" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Senha acesso Taggy Edenred" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 1%" ng-if="false">
                                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-9">
                                    <div class="form-group">
                                        <label style="text-align: left"
                                               class="col-xs-3 col-sm-2 col-md-4 col-lg-5 control-label">Codigo parceiro Taggy Edenred:</label>
                                        <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-5">
                                            <input type="password" ng-model="vm.empresa.CodigoParceiroTaggyEdenred" ng-disabled="vm.empresa.FalhaComunicacaoPedagio"
                                                   name="Codigo  parceiro Taggy Edenred" class="form-control"
                                                   validate-on="blur" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" ng-class="{active : vm.activeTabParametros === 'TabPix'}">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6" style="margin-top: 1%">
                                    <toggle-switch on-label="Sim" off-label="Não"
                                                   ng-model="vm.empresa.NaoBaixarParcelasDeposito" class="switch-mini">
                                    </toggle-switch>
                                    <label style="margin-left: 10px;">Não baixar parcelas tipo depósito</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

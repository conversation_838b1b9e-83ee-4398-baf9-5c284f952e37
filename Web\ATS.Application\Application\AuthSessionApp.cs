﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Database;
using ATS.Domain.Service;
using System.Data.Entity;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class AuthSessionApp : AppBase, IAuthSessionApp
    {
        private readonly IAuthSessionRepository _authSessionRepository;
        private readonly IAuthSessionService _authSessionService;

        public AuthSessionApp(IAuthSessionRepository authSessionRepository, IAuthSessionService authSessionService)
        {
            _authSessionRepository = authSessionRepository;
            _authSessionService = authSessionService;
        }

        public UsuarioAuthSession GetNomeUsuarioFromToken(string token)
        {
            return _authSessionRepository
                .Where(p => p.Token.ToString() == token)
                .Include(x => x.Usuario).Select(x => new
                    UsuarioAuthSession
                    {
                        Nome = x.Usuario.Nome,
                        IdEmpresa = x.Usuario.IdEmpresa ?? 0
                    })
                .FirstOrDefault();
        }

        public int GetIdUsuario(string token)
        {
            return _authSessionService.GetIdUsuario(token);
        }
    }
}
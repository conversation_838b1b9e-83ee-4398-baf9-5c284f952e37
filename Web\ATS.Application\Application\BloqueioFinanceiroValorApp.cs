﻿using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class BloqueioFinaceiroValorApp : AppBase, IBloqueioFinanceiroValorApp
    {
        public IQueryable<BloqueioFinanceiroValor> GetAll()
        {
            return new BloqueioFinanceiroValorService().GetAll();
        }

        public BloqueioFianceiroValorDto[] GetBloqueioFinaceiroValor(int? idEmpresa, int? idFilial)
        {
            return new BloqueioFinanceiroValorService().PegarBloqueioFinanceiroValor(idEmpresa, idFilial);
        }

        public void IntegrarValores(BloqueioFinanceiroValorDto[] valores)
        {
            new BloqueioFinanceiroValorService().IntegrarValores(valores);
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class AutorizacaoEmpresaMap : EntityTypeConfiguration<AutorizacaoEmpresa>
    {
        public AutorizacaoEmpresaMap()
        {
            ToTable("AUTORIZACAO_EMPRESA");

            HasKey(t => new { t.IdEmpresa, t.IdMenu });

            Property(t => t.HasPermissao)
                .IsRequired();

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdMenu)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
        }
    }
}

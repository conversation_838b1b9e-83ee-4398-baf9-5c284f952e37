using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.PedagioRota;

namespace ATS.Application.Interface
{
    public interface IPedagioRotaApp : IBaseApp<IPedagioRotaService>
    {
        PedagioRotaSalvarResponse Salvar(PedagioRotaSalvarRequest request);
        IList<PedagioRotaGridResponse> ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters);
        PedagioRotaDetalheResponse ConsultarDetalhes(int idRota);
    }
}
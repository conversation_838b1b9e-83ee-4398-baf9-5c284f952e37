﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class LimiteTransacaoPortadorMap: EntityTypeConfiguration<LimiteTransacaoPortador>
    {
        public LimiteTransacaoPortadorMap()
        {
            ToTable("LIMITE_TRANSACAO_PORTADOR");
            HasKey(x => x.IdDespesaUsuario);

            Property(t => t.IdDespesaUsuario).HasColumnName("id").HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.DataCadastro).HasColumnName("datacriacao").IsOptional();
            Property(t => t.DataAtualizacao).IsOptional();
            Property(t => t.IdUsuarioCriacao).HasColumnName("usuariocadastro").IsOptional();
            Property(t => t.IdUsuarioAtualizacao).HasColumnName("usuarioatualizacao").IsOptional();
            Property(t => t.Documento).HasMaxLength(14).IsRequired();
            Property(t => t.Tipo).IsRequired();
            Property(t => t.Valor).IsRequired();

            Ignore(t => t.UsuarioCadastro);
            Ignore(t => t.UsuarioAtualizacao);

            HasOptional(c => c.UsuarioCadastroDB)
                .WithMany()
                .HasForeignKey(c => c.IdUsuarioCriacao);

            HasOptional(c => c.UsuarioAtualizacaoDB)
                .WithMany()
                .HasForeignKey(c => c.IdUsuarioAtualizacao);
        }
    }
}
﻿using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersATS
{
    public class EstabelecimentoBaseContaBancariaAtsController : DefaultController
    {
        private readonly IEstabelecimentoBaseContaBancariaApp _estabelecimentoBaseContaBancariaApp;

        public EstabelecimentoBaseContaBancariaAtsController(IEstabelecimentoBaseContaBancariaApp estabelecimentoBaseContaBancariaApp)
        {
            _estabelecimentoBaseContaBancariaApp = estabelecimentoBaseContaBancariaApp;
        }

        [HttpGet]
        //[IgnoreAuthSessionValidation]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetBancosFebraban()
        {
            try
            {
                var response = _estabelecimentoBaseContaBancariaApp.GetBancosFebraban();
                return ResponderSucesso(string.Empty, response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Service
{
    public interface IPagamentoDocumentoService : IService<PagamentoDocumento>
    {
        ValidationResult Add(PagamentoDocumento pagamentoDocumento);
        ValidationResult Update(PagamentoDocumento pagamentoDocumento);
        IEnumerable<PagamentoDocumento> GetPorEmpresa(int? idEmpresa);
        ValidationResult Inativar(int idPagamentoDocumento);
        ValidationResult Reativar(int idPagamentoDocumento);
    }
}

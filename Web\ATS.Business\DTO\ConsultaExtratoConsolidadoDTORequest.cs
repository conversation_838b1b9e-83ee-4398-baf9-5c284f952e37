﻿using ATS.Domain.Models.Grid.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.DTO
{
    public class ConsultaExtratoConDTORequest : FiltrosGridBaseModel
    {
        public bool SomenteTransferencia { get; set; }
        public DateTime? DataInicio { get; set; }
        public DateTime? DataFim { get; set; }
    }
}

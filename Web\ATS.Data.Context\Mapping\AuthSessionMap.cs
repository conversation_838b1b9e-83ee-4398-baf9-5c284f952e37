﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class AuthSessionMap : EntityTypeConfiguration<AuthSession>
    {
        public AuthSessionMap()
        {
            ToTable("AUTH_SESSION");
       
            HasKey(t => t.IdAuthSession);

            Property(t => t.IdUsuario)
                .IsRequired();

            Property(t => t.Token)
                .IsRequired();

            HasRequired(t => t.Usuario)
               .WithMany(t => t.AuthsSession)
               .HasForeignKey(d => d.IdUsuario);
        }
    }
}
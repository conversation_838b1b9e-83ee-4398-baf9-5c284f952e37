﻿//Created by <PERSON><PERSON><PERSON><PERSON> on 02-04-201

using ATS.Domain.Enum;
using ATS.Domain.Interface.Triggers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Data.Context.Trigger
{
    public static class TriggerManager
    {
        private static readonly object TriggersLock = new object();
        private static readonly object StackLock = new object();

        //Mantain all regiters in memory
        private static readonly List<KeyValuePair<Type, Type>> Triggers
            = new List<KeyValuePair<Type, Type>>();

        private static readonly List<KeyValuePair<object, KeyValuePair<Type, EOperationTrigger>>> Stack =
            new List<KeyValuePair<object, KeyValuePair<Type, EOperationTrigger>>>();

        public static bool AlreadyExecuted(object entity, Type type, EOperationTrigger operation)
        {
            if (Stack.Any())
                lock (StackLock)
                {
                    return Stack.Any(x => x.Key == entity && x.Value.Key == type && x.Value.Value == operation);
                }

            return false;
        }

        public static void AddToStack(object entity, Type entityType, EOperationTrigger operation)
        {
            lock (StackLock)
            {
                if (Stack.Any(x => x.Key == entity && x.Value.Key == entityType && x.Value.Value == operation))
                    Stack.Add(new KeyValuePair<object, KeyValuePair<Type, EOperationTrigger>>(
                        entity, new KeyValuePair<Type, EOperationTrigger>(entityType, operation)));
            }
        }

        public static void DestroySession(object entity)
        {
            if (Stack.Any())
                lock (StackLock)
                {
                    var processes = Stack.Where(x => x.Key == entity).ToList();
                    foreach (var process in processes)
                    {
                        Stack.Remove(process);
                    }
                }
        }

        public static void Register(Type type, Type typeManager)
        {
            if (Triggers.Any(x => x.Key == type))
                return;

            Triggers.Add(new KeyValuePair<Type, Type>(type, typeManager));
        }

        public static TType Get<TType>(Type entityType, object entity, EOperationTrigger operation) where TType : class
        {
            if (entity == null)
                throw new Exception("No session id specified to this trigger proccess, this property must be set and unique to avoid circular references.");

            //Created to avoid circular reference and overflow
            if (AlreadyExecuted(entity, entityType, operation))
                return null;

            if (Triggers.All(x => x.Key != entityType))
                return null;

            var typeManager = Triggers.FirstOrDefault(x => x.Key == entityType).Value;
            AddToStack(entity, entityType, operation);
            return (TType)Activator.CreateInstance(typeManager);
        }

        public static Domain.Enum.EOperationTrigger GetCurrentOperationByState(EntityState state)
        {
            switch (state)
            {
                case EntityState.Added:
                    return Domain.Enum.EOperationTrigger.Insert;
                case EntityState.Modified:
                    return Domain.Enum.EOperationTrigger.Update;
                case EntityState.Deleted:
                    return Domain.Enum.EOperationTrigger.Delete;
                default:
                    return Domain.Enum.EOperationTrigger.None;
            }
        }
    }
}
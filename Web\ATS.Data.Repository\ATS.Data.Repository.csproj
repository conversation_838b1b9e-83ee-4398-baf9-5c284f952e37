﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{07EC0440-CCCD-4ED5-8564-8F6EEBBC12C6}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ATS.Data.Repository</RootNamespace>
    <AssemblyName>ATS.Data.Repository</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkProfile />
    <LangVersion>9</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=4.2.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.4.2.1\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Dapper, Version=1.50.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.50.5\lib\net451\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="DnsClient, Version=1.6.1.0, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.6.1\lib\net471\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.MappingAPI, Version=6.1.0.9, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.MappingAPI.6.1.0.9\lib\net45\EntityFramework.MappingAPI.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.2\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=6.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common">
      <HintPath>..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data">
      <HintPath>..\packages\EnterpriseLibrary.Data.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Registry.5.0.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=3.2.0.0, Culture=neutral, PublicKeyToken=94992a530f44e321, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.3.2.0\lib\net472\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=3.2.0.0, Culture=neutral, PublicKeyToken=94992a530f44e321, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.3.2.0\lib\net472\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=2.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.Core.2.7.0\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.2\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="SharpCompress, Version=0.30.1.0, Culture=neutral, PublicKeyToken=afb0a02973931d96, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpCompress.0.30.1\lib\net461\SharpCompress.dll</HintPath>
    </Reference>
    <Reference Include="Snappier, Version=1.0.0.0, Culture=neutral, PublicKeyToken=a1b25124e6e13a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Snappier.1.0.0\lib\netstandard2.0\Snappier.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Device" />
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Linq.Dynamic, Version=1.0.6132.35681, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\packages\System.Linq.Dynamic.1.0.7\lib\net40\System.Linq.Dynamic.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Dynamic, Version=1.0.6132.35681, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Dynamic.1.0.7\lib\net40\System.Linq.Dynamic.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.5.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="TrackerEnabledDbContext, Version=3.6.1.0, Culture=neutral, PublicKeyToken=4f92af0b908c4a0a, processorArchitecture=MSIL">
      <HintPath>..\packages\TrackerEnabledDbContext.3.6.1\lib\net45\TrackerEnabledDbContext.dll</HintPath>
    </Reference>
    <Reference Include="TrackerEnabledDbContext.Common, Version=3.6.1.0, Culture=neutral, PublicKeyToken=4f92af0b908c4a0a, processorArchitecture=MSIL">
      <HintPath>..\packages\TrackerEnabledDbContext.Common.3.6.1\lib\net45\TrackerEnabledDbContext.Common.dll</HintPath>
    </Reference>
    <Reference Include="ZstdSharp, Version=0.7.3.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.7.3\lib\net461\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Dapper\AtendimentoDapper.cs" />
    <Compile Include="Dapper\CargaAvulsaDapper.cs" />
    <Compile Include="Dapper\ConsultaCheckinDapper.cs" />
    <Compile Include="Dapper\CheckinResumoDapper.cs" />
    <Compile Include="Dapper\CidadeDapper.cs" />
    <Compile Include="Dapper\Common\DapperContext.cs" />
    <Compile Include="Dapper\Common\DapperFactory.cs" />
    <Compile Include="Dapper\EstabelecimentoDapper.cs" />
    <Compile Include="Dapper\PagamentoDapper.cs" />
    <Compile Include="Dapper\EmpresaDapper.cs" />
    <Compile Include="Dapper\NotificacaoPushDapper.cs" />
    <Compile Include="Dapper\NotificacaoDapper.cs" />
    <Compile Include="Dapper\MotoristaDapper.cs" />
    <Compile Include="Dapper\ProprietarioDapper.cs" />
    <Compile Include="Dapper\PortadorDapper.cs" />
    <Compile Include="Dapper\ProprietarioDomainDapper.cs" />
    <Compile Include="Dapper\UsuarioDapper.cs" />
    <Compile Include="EntityFramework\AdministradoraPlataformaRepository.cs" />
    <Compile Include="EntityFramework\AvaliacaoPlanilhaGestorCargaAvulsaRepository.cs" />
    <Compile Include="EntityFramework\BannerRepository.cs" />
    <Compile Include="EntityFramework\BannerUsuarioRepository.cs" />
    <Compile Include="EntityFramework\BlacklistIpRepository.cs" />
    <Compile Include="EntityFramework\BloqueioCartaoTipoRepository.cs" />
    <Compile Include="EntityFramework\BloqueioFinanceiroTipoRepository.cs" />
    <Compile Include="EntityFramework\CampanhaRepository.cs" />
    <Compile Include="EntityFramework\CampanhaRespostaRepository.cs" />
    <Compile Include="EntityFramework\CategoriaRepository.cs" />
    <Compile Include="EntityFramework\CheckinResumoRepository.cs" />
    <Compile Include="EntityFramework\DespesaUsuarioRepository.cs" />
    <Compile Include="EntityFramework\EmpresaRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoBaseDocumentoRepository.cs" />
    <Compile Include="Dapper\TipoCarretaDapper.cs" />
    <Compile Include="Dapper\TipoCavaloDapper.cs" />
    <Compile Include="Dapper\ViagemDapper.cs" />
    <Compile Include="Dapper\ViagemEventoDapper.cs" />
    <Compile Include="Dapper\VeiculoDapper.cs" />
    <Compile Include="EntityFramework\AtendimentoPortadorRepository.cs" />
    <Compile Include="EntityFramework\AtendimentoPortadorTramiteRepository.cs" />
    <Compile Include="EntityFramework\BloqueioGestorValorRepository.cs" />
    <Compile Include="EntityFramework\CargaAvulsaRepository.cs" />
    <Compile Include="EntityFramework\BloqueioGestorTipoRepository.cs" />
    <Compile Include="EntityFramework\ClienteProdutoEspecieRepository.cs" />
    <Compile Include="EntityFramework\ClienteEnderecoRepository.cs" />
    <Compile Include="EntityFramework\CombustivelJSLEstabelecimentoBaseRepository.cs" />
    <Compile Include="EntityFramework\CombustivelJSLRepository.cs" />
    <Compile Include="EntityFramework\CteRepository.cs" />
    <Compile Include="EntityFramework\ContratoCiotAgregadoRepository.cs" />
    <Compile Include="EntityFramework\ContratoCiotAgregadoVeiculoRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoBaseContaBancariaRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoContaBancariaRepository.cs" />
    <Compile Include="EntityFramework\CredenciamentoMotivoRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoBaseAssociacaoRepository.cs" />
    <Compile Include="EntityFramework\DeclaracaoCiotRepository.cs" />
    <Compile Include="EntityFramework\EmpresaContaBancariaRepository.cs" />
    <Compile Include="EntityFramework\FornecedorCnpjPedagioRepository.cs" />
    <Compile Include="EntityFramework\GestorUsuarioRepository.cs" />
    <Compile Include="EntityFramework\LimiteTransacaoPortadorRepository.cs" />
    <Compile Include="EntityFramework\LocalizacaoUsuarioRepository.cs" />
    <Compile Include="EntityFramework\PedagioRotaRepository.cs" />
    <Compile Include="EntityFramework\PlanoEmpresaRepository.cs" />
    <Compile Include="EntityFramework\PlanoRepository.cs" />
    <Compile Include="EntityFramework\PontoRotaModeloRepository.cs" />
    <Compile Include="EntityFramework\PracaRotaModeloRepository.cs" />
    <Compile Include="EntityFramework\PrestacaoContasEventoRepository.cs" />
    <Compile Include="EntityFramework\PrestacaoContasRepository.cs" />
    <Compile Include="EntityFramework\ResgateCartaoAtendimentoRepository.cs" />
    <Compile Include="EntityFramework\RotaEstabelecimentoRepository.cs" />
    <Compile Include="EntityFramework\RotaModeloRepository.cs" />
    <Compile Include="EntityFramework\RotaTrajetoRepository.cs" />
    <Compile Include="EntityFramework\SerproCacheRepository.cs" />
    <Compile Include="EntityFramework\SolicitacaoChavePixEventoRepository.cs" />
    <Compile Include="EntityFramework\SolicitacaoChavePixRepository.cs" />
    <Compile Include="EntityFramework\SolicitacaoChavePixStatusRepository.cs" />
    <Compile Include="EntityFramework\TagRepository.cs" />
    <Compile Include="EntityFramework\TransacaoPixRepository.cs" />
    <Compile Include="EntityFramework\TransacaoPixStatusRepository.cs" />
    <Compile Include="EntityFramework\UsoTipoEstabelecimentoRepository.cs" />
    <Compile Include="EntityFramework\UsuarioLocalizacaoRepository.cs" />
    <Compile Include="EntityFramework\UsuarioPermissaoCartaoRepository.cs" />
    <Compile Include="EntityFramework\UsuarioPermissaoFinanceiroRepository.cs" />
    <Compile Include="EntityFramework\UsuarioPermissaoGestorRepository.cs" />
    <Compile Include="EntityFramework\UsuarioPermissoesConcedidasMobileRepository.cs" />
    <Compile Include="EntityFramework\ViagemPendenteGestorRepository.cs" />
    <Compile Include="EntityFramework\ViagemRotaRepository.cs" />
    <Compile Include="EntityFramework\ParametrosRepository.cs" />
    <Compile Include="EntityFramework\ConjuntoEmpresaRepository.cs" />
    <Compile Include="EntityFramework\FilialContatosRepository.cs" />
    <Compile Include="EntityFramework\LayoutCartaoRepository.cs" />
    <Compile Include="EntityFramework\MotivoRepository.cs" />
    <Compile Include="EntityFramework\PINRepository.cs" />
    <Compile Include="EntityFramework\ConjuntoRepository.cs" />
    <Compile Include="EntityFramework\ProdutoRepository.cs" />
    <Compile Include="EntityFramework\TipoMotivoRepository.cs" />
    <Compile Include="EntityFramework\TransacaoCartaoRepository.cs" />
    <Compile Include="EntityFramework\TipoCavaloClienteRepository.cs" />
    <Compile Include="EntityFramework\UsuarioContatoRepository.cs" />
    <Compile Include="EntityFramework\UsuarioDocumentoRepository.cs" />
    <Compile Include="EntityFramework\TipoDocumentoRepository.cs" />
    <Compile Include="EntityFramework\UsuarioEnderecoRepository.cs" />
    <Compile Include="EntityFramework\VeiculoDigitosMercosulRepository.cs" />
    <Compile Include="EntityFramework\VeiculosHistoricoEmpresaRepository.cs" />
    <Compile Include="EntityFramework\ViagemDocumentoFiscalRepository.cs" />
    <Compile Include="EntityFramework\ViagemEventoProtocoloAnexoRepository.cs" />
    <Compile Include="EntityFramework\ViagemSolicitacaoAbonoRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoAssociacaoRepository.cs" />
    <Compile Include="EntityFramework\CredenciamentoAnexoRepository.cs" />
    <Compile Include="EntityFramework\CredenciamentoRepository.cs" />
    <Compile Include="EntityFramework\DocumentoRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoBaseProdutoRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoBaseRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoProdutoRepository.cs" />
    <Compile Include="EntityFramework\LayoutRepository.cs" />
    <Compile Include="EntityFramework\PagamentoConfiguracaoProcessoRepository.cs" />
    <Compile Include="EntityFramework\PagamentoConfiguracaoRepository .cs" />
    <Compile Include="EntityFramework\ProtocoloRepository.cs" />
    <Compile Include="EntityFramework\ProtocoloEventoRepository.cs" />
    <Compile Include="EntityFramework\ProtocoloAnexoRepository.cs" />
    <Compile Include="EntityFramework\ProtocoloAntecipacaoRepository.cs" />
    <Compile Include="EntityFramework\AutenticacaoAplicacaoRepository.cs" />
    <Compile Include="EntityFramework\AutorizacaoEmpresaRepository.cs" />
    <Compile Include="EntityFramework\AuthSessionRepository.cs" />
    <Compile Include="EntityFramework\DataMediaServerRepository.cs" />
    <Compile Include="EntityFramework\CheckInRepository.cs" />
    <Compile Include="EntityFramework\CheckRepository.cs" />
    <Compile Include="EntityFramework\CidadeRepository.cs" />
    <Compile Include="EntityFramework\IconeRepository.cs" />
    <Compile Include="EntityFramework\NotificacaoPushItemRepository.cs" />
    <Compile Include="EntityFramework\NotificacaoPushRepository.cs" />
    <Compile Include="EntityFramework\EstabelecimentoRepository.cs" />
    <Compile Include="EntityFramework\RotaRepository.cs" />
    <Compile Include="EntityFramework\TipoEstabelecimentoRepository.cs" />
    <Compile Include="EntityFramework\TipoNotificacaoRepository.cs" />
    <Compile Include="EntityFramework\Common\Repository.cs" />
    <Compile Include="EntityFramework\EspecieRepository.cs" />
    <Compile Include="EntityFramework\GruposUsuariosRepository.cs" />
    <Compile Include="EntityFramework\MensagemDestinatarioRepository.cs" />
    <Compile Include="EntityFramework\MensagemGrupoUsuarioDestinatarioRepository.cs" />
    <Compile Include="EntityFramework\MensagemGrupoUsuarioRepository.cs" />
    <Compile Include="EntityFramework\ModuloMenuRepository.cs" />
    <Compile Include="EntityFramework\NotificacaoRepository.cs" />
    <Compile Include="EntityFramework\MensagemRepository.cs" />
    <Compile Include="EntityFramework\ClienteRepository.cs" />
    <Compile Include="EntityFramework\EstadoRepository.cs" />
    <Compile Include="EntityFramework\FilialRepository.cs" />
    <Compile Include="EntityFramework\GrupoUsuarioMenuRepository.cs" />
    <Compile Include="EntityFramework\GrupoUsuarioRepository.cs" />
    <Compile Include="EntityFramework\MenuRepository.cs" />
    <Compile Include="EntityFramework\ModuloRepository.cs" />
    <Compile Include="EntityFramework\EmpresaModuloRepository.cs" />
    <Compile Include="EntityFramework\MotoristaMovelRepository.cs" />
    <Compile Include="EntityFramework\MotoristaRepository.cs" />
    <Compile Include="EntityFramework\PaisRepository.cs" />
    <Compile Include="EntityFramework\ProprietarioRepository.cs" />
    <Compile Include="EntityFramework\TipoCarretaRepository.cs" />
    <Compile Include="EntityFramework\TipoCavaloRepository.cs" />
    <Compile Include="EntityFramework\TipoCombustivelRepository.cs" />
    <Compile Include="EntityFramework\UsuarioClienteRepository.cs" />
    <Compile Include="EntityFramework\UsuarioEstabelecimentoRepository.cs" />
    <Compile Include="EntityFramework\UsuarioFilialRepository.cs" />
    <Compile Include="EntityFramework\UsuarioPreferenciasRepository.cs" />
    <Compile Include="EntityFramework\UsuarioRepository.cs" />
    <Compile Include="EntityFramework\VeiculoCombustivelRepository.cs" />
    <Compile Include="EntityFramework\VeiculoConjuntoRepository.cs" />
    <Compile Include="EntityFramework\VeiculoRepository.cs" />
    <Compile Include="EntityFramework\ViagemCarretaRepository.cs" />
    <Compile Include="EntityFramework\ViagemEstabelecimentoRepository.cs" />
    <Compile Include="EntityFramework\ViagemRegraRepository.cs" />
    <Compile Include="EntityFramework\ViagemEventoRepository.cs" />
    <Compile Include="EntityFramework\ViagemDocumentoRepository.cs" />
    <Compile Include="EntityFramework\ViagemValorAdicionalRepository.cs" />
    <Compile Include="EntityFramework\ViagemRepository.cs" />
    <Compile Include="EntityFramework\ViagemVirtualRepository.cs" />
    <Compile Include="EntityFramework\LogSmsRepository.cs" />
    <Compile Include="EntityFramework\WebhookRepository.cs" />
    <Compile Include="EntityFramework\WhiteListIPRepository.cs" />
    <Compile Include="Objects\Auth\AuthSessionLoginUpdateModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ATS.Business\ATS.Domain.csproj">
      <Project>{2810acec-3610-4aad-8e20-d01e14466d87}</Project>
      <Name>ATS.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Configuration\ATS.CrossCutting.IoC.csproj">
      <Project>{15a48f30-13be-47c9-a4a5-cda5dfcca13c}</Project>
      <Name>ATS.CrossCutting.IoC</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.CrossCutting.Reports\ATS.CrossCutting.Reports.csproj">
      <Project>{3f484548-f9a3-4e22-a74d-8dd198c7abab}</Project>
      <Name>ATS.CrossCutting.Reports</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.Data.Context\ATS.Data.Context.csproj">
      <Project>{9a3d3cf8-48dd-4623-b276-41c70b9c24bf}</Project>
      <Name>ATS.Data.Context</Name>
    </ProjectReference>
    <ProjectReference Include="..\ATS.DataMedia.Context\ATS.MongoDB.Context.csproj">
      <Project>{6e8229cb-290b-48e6-a229-26c2e1a8f422}</Project>
      <Name>ATS.MongoDB.Context</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sistema.Framework\Sistema.Framework.Util\Sistema.Framework.Util.csproj">
      <Project>{2A5DA508-D09F-4DC8-B0D6-E21A6E1A7AE6}</Project>
      <Name>Sistema.Framework.Util</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
﻿using System;
using System.Globalization;
using System.Web.Configuration;
using System.Web.Mvc;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using Extratta.LogPortalSeguranca.WorkerService.Events;
using MassTransit;
using Newtonsoft.Json;
using NLog;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class EnableLogAudit : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            try
            {
                var disabledLogPortal = WebConfigurationManager.AppSettings["disabledLogPortal"].ToBoolSafe();
                
                if(disabledLogPortal)
                    return;
                
                var userIdentity = DependencyResolver.Current.GetService(typeof(IUserIdentity)) as IUserIdentity;
                    
                var logInsert = new LogInsertEvent()
                {
                    DataRequisicao = DateTime.Now,
                    Metodo = filterContext.HttpContext.Request.FilePath,
                    Ip = GetRealIp(filterContext),
                    Requisicao = JsonConvert.SerializeObject(filterContext?.ActionParameters),
                    Latitude = userIdentity?.Latitude.ToString(CultureInfo.CurrentCulture),
                    Longitude = userIdentity?.Longitude.ToString(CultureInfo.CurrentCulture),
                    Cidade = userIdentity?.Cidade,
                    Organizacao = userIdentity?.Provedor,
                    Estado = userIdentity?.Estado
                };
                
                filterContext.HttpContext.Items.Add("LogAudit", logInsert);    
            }
            catch (Exception e)
            {
              LogManager.GetCurrentClassLogger().Error($"Erro ao enviar log ao worker service (entrada): {e.Message}");
            }
        }
        
        public override void OnActionExecuted(ActionExecutedContext filterContext)
        {
            try
            {
                var disabledLogPortal = WebConfigurationManager.AppSettings["disabledLogPortal"].ToBoolSafe();
                
                if(disabledLogPortal)
                    return;
                
                var publish = DependencyResolver.Current.GetService(typeof(IPublishEndpoint)) as IPublishEndpoint;
                var userIdentity = DependencyResolver.Current.GetService(typeof(IUserIdentity)) as IUserIdentity;
                var logInsert = (LogInsertEvent) filterContext.HttpContext.Items["LogAudit"];

                if (logInsert == null)
                {
                    LogManager.GetCurrentClassLogger().Error("Erro ao enviar log ao worker service (saída): objeto de insert vazio!");
                    return;
                }
                
                if (userIdentity != null)
                {
                    logInsert.IdUsuario = userIdentity.IdUsuario;
                    logInsert.NomeUsuario = userIdentity.Nome;
                }
                
                var result = (JsonResult) filterContext.Result;
                logInsert.Resposta = JsonConvert.SerializeObject(result.Data);

                publish?.Publish(logInsert);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error($"Erro ao enviar log ao worker service (saída): {e.Message}");
            }
        }
        
        private string GetRealIp(ActionExecutingContext filterContext)
        {
            var ip = filterContext.HttpContext.Request.Headers["x-forwarded-for"] ?? filterContext.HttpContext.Request.Headers["x-real-ip"] ?? filterContext.HttpContext.Request.Headers["x_forwarded_for"];
        
            if (string.IsNullOrEmpty(ip))
                ip = filterContext.HttpContext.Request.UserHostAddress;
            
            ip = ip.Split(',')[0] ;
        
            return ip;
        }
    }
}
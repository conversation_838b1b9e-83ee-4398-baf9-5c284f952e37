﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Database
{
    public interface IEmpresaRepository : IRepository<Empresa>
    {
        IQueryable<Empresa> All(Expression<Func<Empresa, bool>> @where, bool @readonly = false);
        Empresa GetWithAllChilds(int id);
        IQueryable<Empresa> GetEmpresasComMonitoramento();
        Empresa GetAsNoTracking(int id);
        void UpdateNumOrdemCarregamento(int idempresa, int numordemcarregamento);
        int? GetLayoutPadraoCartao(int id);
        EmpresaIndicadores GetEmpresaIndicadores(int idEmpresa);
        void AddEmpresaIndicadores(EmpresaIndicadores empresaIndicadores);
        void UpdateEmpresaIndicadores(EmpresaIndicadores empresaIndicadores);
        string GetTokenMicroServices(int id);
        string GetTokenMicroServices(string cnpj);
        int? GetIdProdutoCartaoFretePadrao(int idEmpresa);
        TarifasMeioHomologadoModel GetTarifasMeioHomologado(string cnpj);
        TimeSpan? GetTempoExpiracaoPedagio(int idEmpresa);
        List<ConsultaTodasEmpresasDto> ConsultarTodas(bool? ativo = null);
        bool AnyRntrc(int idEmpresa);
        bool HabilitarAgendamentoPagamentoFrete(int idEmpresa);
        int? GetIdPorCnpj(string cnpj);
        string GetCnpj(int id);
        IQueryable<Empresa> GetQuery(int idEmpresa);
        IQueryable<Empresa> Query(int id);
        int? GetLayoutCartaoPadrao(int id);
        List<Empresa> GetTodas();
        int ObterMinutosValidadeChavePagamento(int empresaId);
        bool EmpresaValidaPagamentoFrete(int id);
        bool GetPermissaoUsuarioJuridicoCnpjEmpresa(string cnpj);
        bool GetPermissaoUsuarioJuridicoEmpresa(int idEmpresa);
        Empresa Get(string cnpj);
        (string Endpoint, string Headers, string BaixarEventoViagemEndpoint) GetWebhookParameters(int idEmpresa);
        Empresa GetWithIncludeAutenticacao(string cnpj);
        Empresa GetWithIncludeAutenticacao(int? id);
    }

    public interface IEmpresaFilialRepository : IRepository<Filial>
    {

    }
}

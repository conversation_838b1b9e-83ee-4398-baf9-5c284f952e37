﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Helpers;
using ATS.Application.Interface;

namespace ATS.WS.ControllersATS
{
    public class TipoCarretaAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ITipoCarretaApp _tipoCarretaApp;
        private readonly SrvTipoCarreta _srvTipoCarreta;

        public TipoCarretaAtsController(IUserIdentity userIdentity, ITipoCarretaApp tipoCarretaApp, SrvTipoCarreta srvTipoCarreta)
        {
            _userIdentity = userIdentity;
            _tipoCarretaApp = tipoCarretaApp;
            _srvTipoCarreta = srvTipoCarreta;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult Cadastrar(TipoCarreta tipoCarreta)
        {
            try
            {
                var validacoes = ValidarPropriedades(tipoCarreta);

                if (!validacoes.IsValid)
                    return ResponderErro(validacoes.ToFormatedMessage());

                if (tipoCarreta.IdTipoCarreta > 0)
                {
                    var resultadoUpdate = _tipoCarretaApp.Update(tipoCarreta);

                    return !resultadoUpdate.IsValid
                        ? ResponderErro(resultadoUpdate.ToFormatedMessage())
                        : ResponderSucesso("Edição realizada com sucesso!");
                }

                var resultado = _tipoCarretaApp.Add(tipoCarreta);

                return !resultado.IsValid
                    ? ResponderErro(resultado.ToFormatedMessage())
                    : ResponderSucesso("Cadastro realizado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar()
        {
            try
            {
                var tiposCarreta = _srvTipoCarreta
                    .Consultar()
                    ?.Objeto?.Select(x => new
                    {
                        x.Nome,
                        x.IdTipoCarreta
                    });
                return ResponderSucesso(tiposCarreta);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idTipoCarreta)
        {
            try
            {
                var tipoCarreta = _tipoCarretaApp.Get(idTipoCarreta);

                if (tipoCarreta == null)
                    throw new Exception("Não foi possível encontrar o registro desejado");

                return ResponderSucesso(new
                {
                    tipoCarreta.IdTipoCarreta,
                    tipoCarreta.Nome,
                    tipoCarreta.Categoria,
                    tipoCarreta.QtdeEixos,
                    tipoCarreta.MetrosCubicos,
                    tipoCarreta.EixosEspacados,
                    tipoCarreta.Capacidade,
                    tipoCarreta.Empresa?.IdEmpresa,
                    tipoCarreta.Destacar,
                    RazaoSocialEmpresa = tipoCarreta.Empresa?.RazaoSocial
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idCarroceria, string descricao, int Take, int Page, OrderFilters Order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var tipoCarreta = _tipoCarretaApp
                    .ConsultaGrid(idEmpresa, idCarroceria, descricao, Take, Page, Order, filters);

                return ResponderSucesso(tipoCarreta);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult Inativar(int idTipoCarreta)
        {
            try
            {
                var validationResult = _tipoCarretaApp.Inativar(idTipoCarreta);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Tipo de carreta inativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        [EnableLogAudit]
        public JsonResult Reativar(int idTipoCarreta)
        {
            try
            {
                var validationResult = _tipoCarretaApp.Reativar(idTipoCarreta);

                if (!validationResult.IsValid)
                    return ResponderErro(validationResult.ToString());

                return ResponderSucesso("Tipo de carreta reativada com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        private static ValidationResult ValidarPropriedades(TipoCarreta tipoCarreta)
        {
            var retorno = new ValidationResult();

            if (!tipoCarreta.IdEmpresa.HasValue)
                retorno.Add("Empresa é obrigatória.");

            if (string.IsNullOrWhiteSpace(tipoCarreta.Nome))
                retorno.Add("Nome é obrigatório.");

            if (tipoCarreta.Categoria <= 0)
                retorno.Add("Categoria obrigatória.");

            return retorno;
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorEmpresa(int? idEmpresa)
        {
            try
            {
                var tiposCarreta =
                    _tipoCarretaApp.Consultar(string.Empty, idEmpresa).Where(o => o.Ativo)
                                                                           .OrderByDescending(x => x.Destacar)
                                                                           .ThenBy(x => x.Nome);
                return ResponderSucesso(tiposCarreta);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarSemEmpresa()
        {
            try
            {
                return ResponderSucesso(_srvTipoCarreta.ConsultarSemEmpresa());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
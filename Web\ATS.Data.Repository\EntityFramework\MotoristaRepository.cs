﻿using System;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Domain.Enum;

namespace ATS.Data.Repository.EntityFramework
{
    public class MotoristaRepository : Repository<Motorista>, IMotoristaRepository
    {
        private readonly IUsuarioRepository _usuarioRepository;
        public MotoristaRepository(AtsContext context, IUsuarioRepository usuarioRepository) : base(context)
        {
            _usuarioRepository = usuarioRepository;
        }
        
        public int? GetIdMotoristaPorCpf(string cpf)
        {
            return Find(x => x.CPF == cpf && x.Ativo)
                .FirstOrDefault()?.IdMotorista;
        }

        [Obsolete(
            "Não utilize este método, pois retorna toda a entidade. Utilize métodos queryable para selecionar apenas o que você precisa, evitando problemas com o banco de dados.")]
        public override Motorista Get(int id)
        {
            return (from motorista in All()
                    .Include(m => m.Moveis)
                where motorista.IdMotorista == id
                select motorista).FirstOrDefault();
        }

        public Motorista GetFromAllTables(string cpfCnpj)
        {
            var motorista = Get(cpfCnpj);

            var terceiro = _usuarioRepository.Find(x => x.CPFCNPJ == cpfCnpj && x.Ativo)
                .Include(x => x.Contatos)
                .Include(x => x.Enderecos)
                .FirstOrDefault();

            return (terceiro != null) ? GetMotoristaFromUsuario(terceiro) : motorista;
        }

        public Motorista GetMotoristaFromUsuario(Usuario usuario)
        {
            return new Motorista()
            {
                IdMotorista = usuario.IdUsuario,
                TipoContrato = ETipoContrato.Terceiro,
                Nome = usuario.Nome,
                CPF = usuario.CPFCNPJ,
                Endereco = usuario.Enderecos.FirstOrDefault()?.Endereco,
                Bairro = usuario.Enderecos.FirstOrDefault()?.Bairro,
                CEP = usuario.Enderecos.FirstOrDefault()?.CEP,
                RG = "",
                RGOrgaoExpedidor = "",
                CNH = usuario.CNH,
                ValidadeCNH = usuario.ValidadeCNH,
                CNHCategoria = usuario.CNHCategoria,
                DataHoraUltimaAtualizacao = usuario.DataCadastro,
                Ativo = true
            };
        }

        /// <summary>
        /// Retorna o objeto completo do Motorista a partir do CPF
        /// </summary>
        /// <param name="cpf"></param>
        /// <returns></returns>
        public Motorista Get(string cpf)
        {
            return (from motorista in All()
                    .Include(m => m.Moveis)
                    .Include(m => m.Veiculos)
                where motorista.CPF == cpf
                select motorista).FirstOrDefault();
        }
        
        public IQueryable<Motorista> GetIdsMotoristasAtualizados(DateTime dataAtualizacao)
        {
            return from motorista in All()
                where motorista.DataHoraUltimaAtualizacao >= dataAtualizacao
                select motorista;
        }
    }
}
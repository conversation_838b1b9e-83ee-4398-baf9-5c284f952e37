﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class TransacaoCartaoApp : AppBase, ITransacaoCartaoApp
    {
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IPushService _pushService;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ITransacaoCartaoService _transacaoCartaoService;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IViagemApp _viagem;

        public TransacaoCartaoApp(IViagemEventoRepository viagemEventoRepository, IPushService pushService, IEmpresaRepository empresaRepository, ITransacaoCartaoService transacaoCartaoService,
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IViagemEventoApp viagemEventoApp,IViagemApp viagem)
        {
            _viagemEventoRepository = viagemEventoRepository;
            _pushService = pushService;
            _empresaRepository = empresaRepository;
            _transacaoCartaoService = transacaoCartaoService;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _viagemEventoApp = viagemEventoApp;
            _viagem = viagem;
        }
        
        public TransacaoCartao Add(TransacaoCartao transacaoCartao)
        {
            return _transacaoCartaoService.Add(transacaoCartao);
        }

        public void AdicionarByViagemEvento(ViagemEvento viagemEvento)
        {
            var transacaoService = _transacaoCartaoService;

            var transacao = new TransacaoCartao();

            transacao.IdViagemEvento = viagemEvento.IdViagemEvento;
            transacao.DataCriacao = DateTime.Now;
            transacao.LineId = transacaoService.GetCountByIdEvento(transacao.IdViagemEvento ?? 0) + 1;
            transacao.StatusPagamento = EStatusPagamentoCartao.Aberto;
            transacao.MensagemProcessamentoWs = string.Empty;
            transacao.NumeroProtocoloWs = 0;

            transacao = Add(transacao);

            viagemEvento.TransacaoCartao.Add(transacao);
        }

        public IList<TransacaoCartao> GetAllByIdEvento(int idevento)
        {
            return _transacaoCartaoService.GetByIdEvento(idevento);
        }

        public IList<TransacaoCartao> GetAllByIdViagem(int idViagem)
        {
            var listaTransacao = new List<TransacaoCartao>();
            var transacaoService = _transacaoCartaoService;

            var eventos = _viagemEventoRepository.GetViagensEventos(idViagem);

            foreach (var viagemEvento in eventos)
            {
                var transacao = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento);

                listaTransacao.AddRange(transacao);
            }

            return listaTransacao;
        }
        
        public ValidationResult AtualizarStatusTransacaoPendenteReprocessada(int protocoloRequisicao,
            long protocoloProcessamento, DateTime dataTransacao, string mensagemStatus)
        {
            var result = new ValidationResult();
            
            // Atualizar status da transação
            var transacaoService = _transacaoCartaoService;
            var transacao = transacaoService.GetById(protocoloRequisicao);
            if (transacao == null)
            {
                result.Add("Transação não localizada: " + protocoloRequisicao);
                return result;
            }

            transacao.StatusPagamento = EStatusPagamentoCartao.Baixado;
            transacao.MensagemProcessamentoWs = mensagemStatus.ValueLimited(300);
            transacao.DataConfirmacaoMeioHomologado = dataTransacao;
            
            if (protocoloProcessamento != 0)
                transacao.NumeroProtocoloWs = protocoloProcessamento;
            transacaoService.Update(transacao);

            if (transacao.IdViagemEvento.HasValue &&
                (ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem.Contains(transacao.TipoProcessamentoCartao) ||
                 ETipoProcessamentoCartaoUtils.TransferenciaProprietarioParaMotoristaViagem.Contains(transacao
                     .TipoProcessamentoCartao)))
            {
                var viagemApp = _viagemEventoApp;
                var evento = viagemApp.GetQueryable(transacao.IdViagemEvento.Value)
                    .Include(v => v.Viagem)
                    .FirstOrDefault();
                if (evento == null)
                {
                    result.Add("Viagem evento não encontrada: " + transacao.IdViagemEvento.Value);
                    return result;
                }

                var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies,
                    transacao.ViagemEvento.IdEmpresa, CartoesService.AuditDocIntegracao, null);

                if (ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem
                    .Contains(transacao.TipoProcessamentoCartao))
                {
                    // Caso for pendencia de crédito, executa regra de transferência ao motorista da viagem
                    cartoesApp.RealizarCargaFrete(evento.Viagem, evento);
                }
                else if (ETipoProcessamentoCartaoUtils.TransferenciaProprietarioParaMotoristaViagem
                    .Contains(transacao.TipoProcessamentoCartao))
                {
                    // Caso for pendencia de transferência prop x mot, executa envio do push
                    var nomeEmpresa = _empresaRepository.GetQuery(evento.Viagem.IdEmpresa).Select(x => x.NomeFantasia).FirstOrDefault();
                    
                    _pushService.EnviarPorDocumento(transacao.CnpjCpfDestino, "Crédito de " +  transacao.ValorMovimentado.FormatMoney(),
                        $"Crédito transferido para sua conta: { transacao.ValorMovimentado.FormatMoney()}." +
                        $"\nOrigem: {evento.Viagem.NomeProprietario} - {evento.Viagem.CPFCNPJProprietario.FormatarCpfCnpj(false)}"  +
                        $"\nTransportadora: {nomeEmpresa}");
                }
            }

            //
            return result;
        }

        public ValidationResult AtualizarStatusTransacaoFalhaReprocessada(int protocoloRequisicao, long protocoloProcessamento, DateTime dataTransacao, string mensagemStatus)
        {
            var result = new ValidationResult();
            
            var transacao = _transacaoCartaoService.GetById(protocoloRequisicao);

            if (transacao == null)
                result.Add("Transação não localizada: " + protocoloRequisicao);

            if (transacao == null) 
                return result;

            #region Transacao

            transacao.StatusPagamento = EStatusPagamentoCartao.Erro;
            transacao.MensagemProcessamentoWs = mensagemStatus.ValueLimited(300);

            if (protocoloProcessamento != 0)
                transacao.NumeroProtocoloWs = protocoloProcessamento;

            _transacaoCartaoService.Update(transacao);
            #endregion

            #region Viagem evento

            if (transacao.IdViagemEvento == null) 
                return result;
                
            var viagemEvento = _viagemEventoApp.Get(transacao.IdViagemEvento ?? 0);

            if (viagemEvento == null)
                result.Add(
                    $"Viagem evento cód {transacao.IdViagemEvento} da transação cód {transacao.IdTransacaoCartao} não encontrada!");

            if (viagemEvento == null) 
                return result;
            
            viagemEvento.Status = EStatusViagemEvento.Aberto;

            _viagemEventoApp.Update(viagemEvento);
                
            #region Viagem

            var viagem = _viagem.Get(viagemEvento.IdViagem);

            if (viagem.StatusViagem != EStatusViagem.Aberto)
            {
                viagem.StatusViagem = EStatusViagem.Aberto;
                _viagem.Update(viagem);
            }

            #endregion

            #endregion

            return result;
        }

        public IQueryable<TransacaoCartao> Find(Expression<Func<TransacaoCartao, bool>> predicate, bool @readonly = false)
        {
            return _transacaoCartaoService.Find(predicate, @readonly);
        }

        public List<TransacaoCartao> BuscarEventosBaixados(int idViagem, List<int?> idsEventos)
        {
            var listaTransacao = new List<TransacaoCartao>();
            var transacaoService = _transacaoCartaoService;

            var eventos = _viagemEventoRepository
                .GetViagensEventos(idViagem).ToList()
                .Where(ve => ve.Status == EStatusViagemEvento.Baixado  && idsEventos.Contains(ve.IdViagemEvento));

            foreach (var viagemEvento in eventos)
            {
                var transacao = transacaoService.GetByIdEvento(viagemEvento.IdViagemEvento);
                listaTransacao.AddRange(transacao);
            }

            return listaTransacao;
        }

        public decimal GetValorTotalTransferenciasTEDUltimas24horas(string documentoPortador)
        {
            var dataOntem = DateTime.Now.AddHours(-24);

            return _transacaoCartaoService.GetQuery()
                .Where(c => c.CnpjCpfOrigem == documentoPortador && c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaContaBancaria
                                                                 && c.StatusPagamento == EStatusPagamentoCartao.Baixado
                                                                 && c.OrigemTransacaoCartao == EOrigemTransacaoCartao.ManualAplicativo
                                                                 && c.DataCriacao >= dataOntem)
                .Sum(c => (decimal?)c.ValorMovimentado) ?? 0;
        }

        public decimal GetValorTotalTransferenciasCartaoUltimas24horas(string documentoPortador, bool applicativo = true)
        {
            var dataOntem = DateTime.Now.AddHours(-24);

            var query = _transacaoCartaoService.GetQuery()
                .Where(c => c.CnpjCpfOrigem == documentoPortador && c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaSaldo
                                                                 && c.StatusPagamento == EStatusPagamentoCartao.Baixado
                                                                 && c.DataCriacao >= dataOntem);

            if (applicativo)
                query = query.Where(c => c.OrigemTransacaoCartao == EOrigemTransacaoCartao.ManualAplicativo);

            return query.Sum(c => (decimal?)c.ValorMovimentado) ?? 0;
        }
    }
}

﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.WS.Models;

namespace ATS.WS.ControllersATS
{
    public class ClienteAtsController : BaseAtsController<IClienteApp>
    {
        private readonly SrvCliente _srvCliente;

        public ClienteAtsController(IClienteApp app, IUserIdentity userIdentity, SrvCliente srvCliente)
            : base(app, userIdentity)
        {
            _srvCliente = srvCliente;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(ClienteAtsConsultaGridFilters @params)
        {
            try
            {
                if (SessionUser.Perfil != (int) EPerfil.Administrador)
                {
                    if (@params.Filters == null) @params.Filters = new List<QueryFilters>();
                    @params.Filters.Add(new QueryFilters
                    {
                        Campo = "IdEmpresa",
                        CampoTipo = EFieldTipo.Number,
                        Operador = EOperador.Exact,
                        Valor = SessionUser.IdEmpresa?.ToString()
                    });
                }

                var clientes = App.ConsultarGrid(@params.Take, @params.Page, @params.Order, @params.Filters);

                return ResponderSucesso(clientes);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idCliente)
        {
            try
            {
                var validationResult = App.Inativar(idCliente);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Cliente inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idCliente)
        {
            try
            {
                var validationResult = App.Reativar(idCliente);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Cliente reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult CadastrarAtualizar(ClienteCadastrarAtualizarCls @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro("A requisição não está em um formato válido!");

                if (SessionUser.Perfil != (int) EPerfil.Administrador)
                    @params.idEmpresa = SessionUser.IdEmpresa;

                if (!@params.idCliente.HasValue)
                    _srvCliente.Cadastrar(@params);
                else
                    _srvCliente.Atualizar(@params);

                return ResponderSucesso(!@params.idCliente.HasValue ? "Cliente cadastrado com sucesso!" : "Cliente atualizado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetImageJson(int idCliente)
        {
            try
            {
                var clienteQuery = App.GetAll();
                
                if (SessionUser.Perfil != (int)EPerfil.Administrador && SessionUser.IdEmpresa != null)
                    clienteQuery = clienteQuery.Where(c => c.IdEmpresa == SessionUser.IdEmpresa.Value);

                var byteArray = clienteQuery.Select(c => c.Logo).FirstOrDefault();
                
                if (byteArray == null)
                    throw new Exception($"Nenhuma imagem encontrada para o cliente de ID {idCliente}!");

                return ResponderSucesso($"data:image/png;base64,{Convert.ToBase64String(new FileContentResult(byteArray, "image/png").FileContents)}");
            }
            catch (Exception err)
            {
                return ResponderErro(err);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idCliente)
        {
            try
            {
                var cli = App.GetAllChilds(idCliente);

                if (cli == null)
                    throw new Exception($"Nenhum cliente encontrado para o id {idCliente}!");

                if (!cli.Ativo)
                    throw new Exception("Não é possível editar um registro inativo.");

                if(SessionUser.Perfil != (int)EPerfil.Administrador && cli.IdEmpresa != SessionUser.IdEmpresa)
                    throw new InvalidOperationException("Usuário não autenticado.");

                object retorno = new
                {
                    cli.IdCliente,
                    cli.RazaoSocial,
                    cli.CNPJCPF,
                    cli.TipoPessoa,
                    cli.NomeFantasia,
                    cli.RG,
                    cli.OrgaoExpedidorRG,
                    cli.IE,
                    cli.Celular,
                    cli.TipoCliente,
                    cli.IdEmpresa,
                    cli.Email,
                    cli.PontoReferencia,
                    cli.AtivarParametrosGestao,
                    RazaoSocialEmpresa = cli.Empresa.RazaoSocial,
                    Parametros = new
                    {
                        cli.ObrigarCPFReceber,
                        cli.AutenticarCodigoBarraNF
                    },
                    Endereco = new
                    {
                        cli.CEP,
                        cli.Endereco,
                        cli.Numero,
                        cli.Complemento,
                        cli.Bairro,
                        cli.IdPais,
                        cli.IdEstado,
                        cli.IdCidade,
                        cli.Latitude,
                        cli.Longitude
                    }
                };

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridClientes(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});

            if (SessionUser.Perfil == (int) EPerfil.Empresa)
                filtrosGridModel.IdEmpresa = SessionUser.IdEmpresa;

            var report = _srvCliente.GerarRelatorioGridClientes(filtrosGridModel.IdEmpresa, filtrosGridModel.Order,
                filtrosGridModel.Filters, filtrosGridModel.Extensao);

            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de Clientes.{filtrosGridModel.Extensao}");
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarDetalhes(int idCliente)
        {
            try
            {
                var cliente = App.ConsultarDetalhes(idCliente);

                return ResponderSucesso(cliente);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
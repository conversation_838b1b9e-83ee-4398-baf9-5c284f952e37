﻿using ATS.Domain.Enum;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Response.Proprietario
{
    public class ProprietarioConsultarPamcardModel
    {
        public int IdProprietario { get; set; }
        public int IdEmpresa { get; set; }
        public string CNPJCPF { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string RG { get; set; }
        public string RGOrgaoExpedidor { get; set; }
        public string IE { get; set; }
        public string RNTRC { get; set; }
        public string StatusRntrc { get; set; }
        public EStatusIntegracao StatusIntegracao { get; set; }
        public ETipoContrato TipoContrato { get; set; }
        public List<Cartao> Cartoes { get; set; }
        public List<Conta> Contas { get; set; }

        public List<ProprietarioContatoConsultarModel> Contatos { get; set; }
        public List<ProprietarioEnderecoConsultarModel> Enderecos { get; set; }
    }

    public class ProprietarioContatoConsultarPamcardModel
    {
        public int IdContato { get; set; }
        public string Telefone { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
    }

    public class ProprietarioEnderecoConsultarPamcardModel
    {
        public int IdEndereco { get; set; }
        public string CEP { get; set; }
        public string Endereco { get; set; }
        public string Complemento { get; set; }
        public int? Numero { get; set; }
        public string Bairro { get; set; }
        public int? CodigoIBGECidade { get; set; }
        public int? CodigoIBGEEstado { get; set; }
        public int? CodigoBACENPais { get; set; }
    }

    public class Cartao
    {
        private const string Liberado = "LIBERADO";
        public int Quantidade { get; set; } = 1;
        public int Numero { get; set; }
        public string Tipo { get; set; }
        public string Status { get; set; } = Liberado;
    }
    
    public class Conta
    {
        private const string Pendente = "PENDENTE";
        public string Agencia { get; set; }
        public int AgenciaDigito { get; set; }
        public string Banco { get; set; }
        public string BancoNome { get; set; }
        public string Numero { get; set; }
        public string Status { get; set; } = Pendente;
        public string Tipo { get; set; }
    }
}
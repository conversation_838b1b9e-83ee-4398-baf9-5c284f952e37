﻿using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class SMService : ISMService
    {
        private readonly ILogSmsService _logSmsService;
        private readonly IPushService _pushService;

        public SMService(ILogSmsService logSmsService, IPushService pushService)
        {
            _logSmsService = logSmsService;
            _pushService = pushService;
        }

        public void Send(List<string> idsPush, string title, ETipoMensagemPush messageType, string message, object data)
        {
            try
            {
                _pushService.Enviar(idsPush, title, message, data, messageType);
            }
            catch (Exception e)
            {
                throw new Exception($"Não foi possível enviar a mensagem via Push.  {e.Message} --------------- {e.InnerException?.Message}", e);
            }
        }

        public SMSResponse EnviarSMS(string celular, string message, EProcessoEnvio? processoenvio = EProcessoEnvio.LocalNaoMapeado, int? idUsuarioEnvio = null, int? idPreUsuarioEnvio = null, int? idempresa = null)
        {
            try
            {
                var webAddr = "https://api-messaging.movile.com/v1/send-sms";

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(webAddr);
                httpWebRequest.ContentType = "application/json";
                httpWebRequest.Headers.Add("authenticationtoken:g2nPGSyVSgxpnqFxPKKc-3Rjxj1VKGExnAKGZyRS");
                httpWebRequest.Headers.Add("username:tmov-sms");
                httpWebRequest.Method = "POST";

                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    var lSMSBody = new
                    {
                        destination = $"55{celular}",
                        messageText = message
                    };
                    var json = JsonConvert.SerializeObject(lSMSBody);
                    streamWriter.Write(json);
                    streamWriter.Flush();
                }

                var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                var logSms = new LogSms()
                {
                    CelularEnvio = celular,
                    DataHoraEnvio = DateTime.Now,
                    ProcessoEnvio = processoenvio.Value,
                    Texto = message,
                    IdUsuarioEnvio = idUsuarioEnvio,
                    IdPreUsuarioEnvio = idPreUsuarioEnvio,
                };

                _logSmsService.Add(logSms, idempresa);

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    string content = streamReader.ReadToEnd();
                    var response = JsonConvert.DeserializeObject<SMSResponse>(content);

                    return response;
                }
            }
            catch (Exception e)
            {
                throw new Exception($"Erro ao enviar código de segurança solicite o pin novamente! [{e.Message}]");
            }
        }
    }
}

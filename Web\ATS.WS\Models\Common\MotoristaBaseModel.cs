﻿using System;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Common
{
    public class MotoristaBaseModel
    {
        public int IdMotoristaBase { get; set; }
        public int? IdEmpresa { get; set; }
        public string Nome { get; set; }
        public string CPF { get; set; }
        public string Foto { get; set; }
        public string Placa { get; set; }
        public string NomeCidadeAtual { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Telefone { get; set; }
        public EStatusMotorista StatusMotorista { get; set; }
        public ETipoPessoa TipoMotorista { get; set; }
        public EStatusGR StatusGR { get; set; }
        public EStatusConjunto StatusConjunto { get; set; }
        public bool PossuiTmov { get; set; }
        public bool Ativo { get; set; }
        public DateTime? DataContatado { get; set; }
        public DateTime? DataCadastro { get; set; }
        public DateTime? DataAtualizado { get; set; }
    }
}

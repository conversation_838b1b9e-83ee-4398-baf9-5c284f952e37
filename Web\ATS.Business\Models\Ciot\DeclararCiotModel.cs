using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Models.Ciot
{
    public class DeclararCiotModel
    {
        public ICiotV3Service CiotV3Service { get; set; }

        public IProprietarioService ProprietarioService { get; set; }

        public IViagemRepository ViagemRepository { get; set; }

        public IDeclaracaoCiotRepository DeclaracaoCiotRepository { get; set; }
        public string CnpjEmpresa { get; set; }
        public IParametrosService ParametrosService { get; set; }
        
        public bool Retificar { get; set; }

        public bool HabilitarDeclaracaoCiot { get; set; }
        
        public bool? GerarCiotTacAgregado { get; set; }
    }
}
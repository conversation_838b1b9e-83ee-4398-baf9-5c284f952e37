﻿using System;
using System.Linq;
using System.Web.Configuration;

namespace ATS.CrossCutting.IoC.Utils
{
    public static class AppSettingsUtils
    {
        /// <summary>
        /// Indica que a instância do código fonte em execução está sendo processada pelo servidor da SOTRAN
        /// </summary>
        public static bool IsInstanciaSotran => WebConfigurationManager.AppSettings["CNPJ_EMPRESA"] == "03286888000169";

        public static string TituloSistema => WebConfigurationManager.AppSettings["TITULO_SISTEMA"];

        /// <summary>
        /// Nome do serviço para enviar ao serviço de logs no campo "Micro Serviço"
        /// </summary>
        public static string NomeServicoLogs => WebConfigurationManager.AppSettings["MS.Logs.NomeServico"]
            .DefaultIfNullOrWhiteSpace(TituloSistema);

        /// <summary>
        /// Tempo padrão em minutos para o serviço de infra estrutura reintegrar as falhas de webhook por conta de recusas do endpoint destino
        /// </summary>
        public static int? TempoPadraoParaReintegrarWebhooksComFalha => WebConfigurationManager
            .AppSettings["MS.Infra.TempoPadraoParaReintegrarWebhooksComFalha"].ToIntSafe(TimeSpan.FromDays(1).TotalMinutes.ToInt());
    }
}

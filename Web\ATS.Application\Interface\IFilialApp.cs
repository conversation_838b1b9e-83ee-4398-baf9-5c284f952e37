﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IFilialApp : IAppBase<Filial>
    {
        //Filial GetWithAllChilds(int idFilial);
        ValidationResult Add(Filial filial);
        Filial Get(int id);
        IQueryable<Filial> GetListaFilialPorEmpresa(int idEmpresa);
        ValidationResult Update(Filial filial);
        IQueryable<Filial> Consultar(string razaoSocial, int? idEmpresa);
        ValidationResult Inativar(int idFilial);
        ValidationResult Reativar(int idFilial);
        IEnumerable<Filial> GetFiliaisAtualizadas(int? idEmpresa, DateTime dataAtualizacao);
        //int? GetIdPorCnpj(string cnpj);
        //Filial GetWithLocalizacao(int id);
        //int? GetIdPorCNPJTodos(string nCNPJ);
        //List<string> GetCNPJList(int[] idFilialList);
        int? GetIdPorCodigoFilial(int idEmpresa, string cnpjFilial, string codigoFilial);
        string GetCnpjPorId(int idFilial);
        IQueryable<Filial> QueryById(int id);
        Filial Get(string cnpj);
        object GetFilialCadastro(int id);
        Filial GetFilialPorEmpresa(int idEmpresa, int idFilial);
        ValidationResult AlterarStatus(int idFilial);
        int? GetIdPorCnpj(string cnpj);
        List<string> GetCnpjList(int[] idFilialList);
        int? GetIdPorCnpjTodos(string cnpj);
        object ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters);
        List<FilialContatos> GetContatosByFilial(int idFilial);
        byte[] GerarRelatorioGridFiliais(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo);
        IQueryable<Filial> GetQuery(int? idEmpresa);
        bool PertenceAEmpresa(int idEmpresa, int idFilial);
    }
}

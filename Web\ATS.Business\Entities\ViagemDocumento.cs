﻿using ATS.Domain.Enum;
using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class ViagemDocumento
    {
        /// <summary>
        /// Código do documento da viagem
        /// </summary>
        public int IdViagemDocumento { get; set; }

        /// <summary>
        /// Código do evento da viagem
        /// </summary>
        public int IdViagemEvento { get; set; }

        /// <summary>
        /// Código do documento da viagem documento
        /// </summary>
        public int? IdDocumento { get; set; }

        /// <summary>
        /// Tipo de evento da viagem
        /// </summary>
        public ETipoEventoViagem TipoEvento { get; set; }

        /// <summary>
        /// Descrição do evento da viagem
        /// </summary>
        public string Descricao { get; set; }

        /// <summary>
        /// Tipo de documento da viagem
        /// </summary>
        public ETipoDocumento TipoDocumento { get; set; } = 0;

        /// <summary>
        /// Número do documento da viagem
        /// </summary>
        public int NumeroDocumento { get; set; }
       
        /// <summary>
        /// É obrigatório anexar o documento da viagem?
        /// </summary>
        public bool ObrigaAnexo { get; set; } = false;

        public bool ObrigaAnexoMatriz { get; set; } = false;

        public bool ObrigaAnexoFilial { get; set; } = false;

        /// <summary>
        /// FK com a tabela de mídia do MongoDb
        /// </summary>
        public string TokenAnexo { get; set; }

        public bool ObrigaDocOriginal { get; set; }

        #region Virtual Fields
        
        public virtual ViagemEvento ViagemEvento { get; set; }
        public virtual Documento Documento { get; set; }
        
        #endregion
    }
}
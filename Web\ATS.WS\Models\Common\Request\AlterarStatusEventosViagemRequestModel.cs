using System.Collections.Generic;
using System.Text;
using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Models.Common.Request
{
    public class AlterarStatusEventosViagemRequestModel : RequestBase
    {
        public int? IdViagem { get; set; }        
        public string NumeroControle { get; set; }
        public IList<ViagemEventoStatusModel> ViagemEventos { get; set; }
        
        public string Valida()
        {
            var erros = new StringBuilder();
            
            if (string.IsNullOrWhiteSpace(NumeroControle))
                erros.AppendLine("O campo NumeroControle é obrigatório e não foi preenchido");

            if (!IdViagem.HasValue)
                erros.AppendLine("O campo IdViagem é obrigatório e não foi preenchido");

            if (erros.Length > 0)
                return erros.ToString();
            
            if (CNPJEmpresa.OnlyNumbers().Length != 14)
                erros.AppendLine("O campo CNPJEmpresa deve conter 14 caracteres");
            
            if (CNPJAplicacao.OnlyNumbers().Length != 14)
                erros.AppendLine("O campo CNPJAplicacao deve conter 14 caracteres");
            
            if (!string.IsNullOrWhiteSpace(NumeroControle) && NumeroControle.Length > 300)
                erros.AppendLine("O campo NumeroControle não pode conter mais de 300 caracteres");
            
            if (erros.Length > 0)
                return erros.ToString();
            
            return string.Empty;
        }
    }

    public class ViagemEventoStatusModel
    {
        public int? IdViagemEvento { get; set; }
        public EStatusViagemEvento? Status { get; set; } = EStatusViagemEvento.Aberto;
        public string NumeroControle { get; set; }
    }
}
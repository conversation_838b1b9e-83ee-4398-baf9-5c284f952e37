﻿using ATS.Data.Context;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Data.Repository.EntityFramework.Common;

namespace ATS.Data.Repository.EntityFramework
{
    public class MensagemGrupoUsuarioDestinatarioRepository : Repository<MensagemGrupoDestinatario>, IMensagemGrupoDestinatarioRepository
    {
        public MensagemGrupoUsuarioDestinatarioRepository(AtsContext context) : base(context)
        {
        }
        
        public void DeletarUsuarios(int idUsuario)
        {
#pragma warning disable 618
            var mensagemGrupoUsuario = FirstOrDefault(x => x.IdUsuarioDestinatario == idUsuario);
#pragma warning restore 618
            if (mensagemGrupoUsuario != null)
                base.Delete(mensagemGrupoUsuario);
        }
    }
}

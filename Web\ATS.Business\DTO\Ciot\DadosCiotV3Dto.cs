namespace ATS.Domain.DTO.Ciot
{
    public class DadosCiotV3Dto
    {
        public ContratanteCiotV3Dto Contratante { get; set; }
        public RemetenteCiotV3Dto Remetente { get; set; }
        public DestinatarioCiotV3Dto Destinatario { get; set; }
    }
    
    public class ContratanteCiotV3Dto : DadosBaseCiotV3Dto
    {
        public string CNPJ { get; set; }
        public string Telefone { get; set; }
    }

    public class RemetenteCiotV3Dto : DadosBaseCiotV3Dto
    {
        public string CNPJCPF { get; set; }
    }
    
    public class DestinatarioCiotV3Dto : DadosBaseCiotV3Dto
    {
        public string CNPJCPF { get; set; }
    }
    
    public class DadosBaseCiotV3Dto
    {
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public int? IBGE { get; set; }
        public string CEP { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public string Email { get; set; }
        public string Endereco { get; set; }
        public int? Numero { get; set; }
        public string Celular { get; set; }
    }
}
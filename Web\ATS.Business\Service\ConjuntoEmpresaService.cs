﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace ATS.Domain.Service
{
    public class ConjuntoEmpresaService : ServiceBase, IConjuntoEmpresaService
    {
        private readonly IConjuntoEmpresaRepository _conjuntoEmpresaRepository;

        public ConjuntoEmpresaService(IConjuntoEmpresaRepository conjuntoEmpresaRepository)
        {
            _conjuntoEmpresaRepository = conjuntoEmpresaRepository;
        }

        public ConjuntoEmpresa Get(int id)
        {
            return _conjuntoEmpresaRepository.Get(id);
        }

        public ConjuntoEmpresa GetByConjunto(int idConjunto)
        {
            return _conjuntoEmpresaRepository
                .Find(o => o.IdConjunto == idConjunto)
                .Include(o => o.Conjunto)
                .Include(o => o.Veiculo)
                .Include(o => o.Veiculo)
                .FirstOrDefault();
        }

        public ValidationResult Add(ConjuntoEmpresa entity)
        {
            try
            {
                _conjuntoEmpresaRepository.Add(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }

        public ValidationResult Update(ConjuntoEmpresa entity)
        {
            try
            { 
                _conjuntoEmpresaRepository.Update(entity);
            }
            catch (Exception Ex)
            {
                return new ValidationResult().Add(Ex.ToString());
            }

            return new ValidationResult();
        }
    }
}

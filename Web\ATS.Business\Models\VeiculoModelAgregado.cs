﻿namespace ATS.Domain.Models
{
    public class VeiculoModelAgregado
    {
        public int IdVeiculo { get; set; }
        public string Placa { get; set; }
        public int? IdEmpresa { get; set; }
        public string Chassi { get; set; }
        public int? AnoFabricacao { get; set; }
        public int? AnoModelo { get; set; }
        public string RNTRC { get; set; }
        public string Marca { get; set; }
        public string Modelo { get; set; }
        public bool ComTracao { get; set; }
        public int TipoRodagem { get; set; }
        public string TecnologiaRastreamento { get; set; }
        public int? IdProprietario { get; set; }
        public int? IdMotorista { get; set; }
        public int IdTipoCavalo { get; set; }
        public int IdTipoCarreta { get; set; }
        public int QtdEixos { get; set; }
        public bool Ativo { get; set; } = true;
        public bool InclusoCiot { get; set; } = true;
    }
}
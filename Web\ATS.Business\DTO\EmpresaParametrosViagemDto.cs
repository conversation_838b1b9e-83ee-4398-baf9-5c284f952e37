﻿using System.Collections.Generic;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;

namespace ATS.Domain.DTO
{
    public class EmpresaParametrosViagemDto
    {
        public List<FornecedorItem> FornecedoresPedagio { get; set; }
        public ParametrosPedagio Parametros { get; set; }
    }

    public class FornecedorItem
    {
        public FornecedorItem(FornecedorEnum fornecedor)
        {
            Value = fornecedor.GetHashCode();
            Descricao = fornecedor.GetDescription();
        }
        public int Value { get; set; }
        public string Descricao { get; set; }
    }
    
    public class ParametrosPedagio
    {
        public bool PedagioTag { get; set; }
        public bool ParcelaViagemAberta { get; set; }
    }
}

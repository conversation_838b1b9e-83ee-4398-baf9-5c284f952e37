﻿using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Validators;
using ATS.Domain.Models.LocalizacaoUsuarios;
using FluentValidation;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Validators
{
    public class LocalizacaoUsuarioAddModelValidator : AbstractValidator<LocalizacaoUsuarioAddModel>, ILocalizacaoUsuarioAddModelValidator
    {
        public LocalizacaoUsuarioAddModelValidator(IUsuarioRepository usuarioRepository)
        {
            RuleFor(o => o.IdUsuario)
                .Must(c => usuarioRepository.Any(a => a.IdUsuario == c))
                .WithMessage("Usuário informada é inválido!");

            RuleFor(o => o.Latitude)
                .Must(a => a.IsValidLatitude()).WithMessage("A Latitude enviada deve ser válida!");

            RuleFor(o => o.Longitude)
                .Must(a => a.IsValidLongitude()).WithMessage("A Longitude enviada deve ser válida!");
        }
    }
}
﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ATS.Domain.Interface.Service
{
    public interface IConjuntoService
    {
        Conjunto Get(int id);
        ValidationResult AddGestorFrota(int IdConjunto, int IdGestorFrota);
        ValidationResult RemoveGestorFrota(List<int> IdsConjunto, int IdGestorFrota);
        ValidationResult AtulizarStatusConjunto(int IdConjunto, string CPF);
        ValidationResult Add(Conjunto entity);
        ValidationResult Update(Conjunto entity);
        ValidationResult AlterarTipoVeiculoConjunto(int idConjunto, string placa, bool ehCavalo, int? tipoVeiculo);
        List<Conjunto> GetTodos( int IdMotoristaBase);
        List<Conjunto> GetMotoristasBaseByPlaca(string placa);
        List<Conjunto> GetByIdGestor(int IdGestorFrota);
        Conjunto GetConjuntoByPlacaCPF(string CPF, string placa);
        string GetByCPFPlacaConjuntoMontado(string CPF);
        ValidationResult AtualizarTipos(int IdConjunto, Conjunto conjunto);
        ValidationResult MontarConjunto(string CPF, int IdConjunto);
        ValidationResult IsValidToCrud(Conjunto conjunto, EProcesso processo);
        List<Conjunto> GetConjuntoMontadoByPlaca(string placa);
        ConjuntoEmpresa GetConjuntoEmpresa(int IdConjunto);
        IQueryable<Conjunto> GetQuery(int idConjunto);
        List<Conjunto> GetByCPFCNPJGestor(List<string> CPFCNPJs);
    }
}

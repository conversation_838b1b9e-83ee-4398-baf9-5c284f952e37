using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemMap : EntityTypeConfiguration<Viagem>
    {
        public ViagemMap()
        {
            ToTable("VIAGEM");

            HasKey(t => new {  t.IdViagem, t.IdEmpresa });

            Property(x => x.IdViagem)
                .HasColumnName("idviagem")
                .IsRequired()
                .HasColumnType("int")
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.IdEmpresa)
                .HasColumnName("idempresa")
                .IsRequired()
                .HasColumnType("int")
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(x => x.Placa)
                .HasColumnName("placa")
                .IsRequired()
                .IsUnicode(false)
                .HasColumnType("varchar")
                .HasMaxLength(7);


            Property(x => x.<PERSON>)
                .HasColumnName("coleta")
                .IsUnicode(false)
                .HasColumnType("varchar")
                .HasMaxLength(250);

            Property(x => x.Entrega)
                .HasColumnName("entrega")
                .IsUnicode(false)
                .HasColumnType("varchar")
                .HasMaxLength(250);

            Property(x => x.IdClienteOrigem)
                .HasColumnName("idclienteorigem")
                .IsOptional()
                .HasColumnType("int");

            Property(x => x.IdClienteDestino)
                .HasColumnName("idclientedestino")
                .IsOptional()
                .HasColumnType("int");

            Property(x => x.DataColeta)
                .HasColumnName("datacoleta")
                .IsOptional()
                .HasColumnType("datetime");

            Property(x => x.DataPrevisaoEntrega)
                .HasColumnName("dataprevisaoentrega")
                .IsOptional()
                .HasColumnType("datetime");

            Property(x => x.DataFinalizacao)
                .HasColumnName("datafinalizacao")
                .IsOptional()
                .HasColumnType("datetime");

            Property(x => x.CPFMotorista)
                .HasColumnName("cpfmotorista")
                .IsRequired()
                .IsUnicode(false)
                .HasColumnType("varchar")
                .HasMaxLength(11);

            Property(x => x.CNHMotorista)
                .HasMaxLength(14);

            Property(x => x.StatusViagem)
                .HasColumnName("statusviagem")
                .IsRequired()
                .HasColumnType("int");

            Property(x => x.StatusIntegracao)
                .HasColumnName("statusintegracao")
                .IsRequired()
                .HasColumnType("int");

            Property(x => x.IdProprietario)
                .HasColumnName("idproprietario")
                .IsOptional();

            Property(x => x.ResultadoDeclaracaoCiot).IsOptional();

            Property(x => x.MensagemDeclaracaoCiot).IsOptional().HasMaxLength(2000);
            
            Property(x => x.MensagemCompraPedagio).IsOptional().HasMaxLength(500);

            Property(x => x.PesoSaida)
                .HasPrecision(10, 3);

            Property(x => x.PesoChegada)
                .HasPrecision(10, 3);

            Property(x => x.PesoDiferenca)
                .HasPrecision(10, 3);

            Property(x => x.ValorMercadoria)
                .HasPrecision(10, 2);

            Property(x => x.DifFreteMotorista)
                .HasPrecision(10, 2);

            Property(x => x.ValorQuebraMercadoria)
                .HasPrecision(10, 2);

            Property(x => x.ValorQuebraMercadoriaCalculado)
                .HasPrecision(10, 2);

            Property(x => x.NumeroCartao)
                .HasMaxLength(30);

            Property(x => x.MensagemDeclaracaoCiot)
                .HasMaxLength(500);

            // Impostos
            Property(x => x.IRRPF)
                .HasPrecision(10, 2);
            Property(x => x.INSS)
                .HasPrecision(10, 2);
            Property(x => x.SESTSENAT)
                .HasPrecision(10, 2);

            // Referências
            HasOptional(a => a.ClienteDestino)
                .WithMany(b => b.ViagemsClienteDestino)
                .HasForeignKey(c => c.IdClienteDestino);

            HasOptional(a => a.ClienteOrigem)
                .WithMany(b => b.ViagemsClienteOrigem)
                .HasForeignKey(c => c.IdClienteOrigem);

            HasOptional(x => x.ClienteTomador)
                .WithMany(x => x.ViagensClienteTomador)
                .HasForeignKey(x => x.IdClienteTomador);

            HasOptional(t => t.Filial)
                .WithMany(t => t.Viagens)
                .HasForeignKey(t => t.IdFilial);

            HasRequired(t => t.Empresa)
                .WithMany(t => t.Viagens)
                .HasForeignKey(t => t.IdEmpresa);

            HasOptional(t => t.ViagemComplementada)
                .WithMany(t => t.ViagensComplementar)
                .HasForeignKey(t => new {t.IdViagemComplementada, t.IdEmpresa})
                .WillCascadeOnDelete(false);

            Property(o => o.NumeroControle)
                .IsOptional()
                .HasMaxLength(300);

            Property(o => o.DataDescarga)
                .IsOptional();
            
            HasOptional(t => t.DeclaracaoCiot)
                .WithMany(d => d.ViagensVinculadas)
                .HasForeignKey(t => t.IdDeclaracaoCiot);

            Property(o => o.CodigoTipoCarga)
                .IsOptional();

            Property(o => o.DistanciaViagem)
                .IsOptional();

            Property(o => o.IsPedagioAvulso)
                .IsRequired();

            Property(o => o.CepOrigem)
                .HasMaxLength(8)
                .IsOptional();
            
            Property(o => o.CepDestino)
                .HasMaxLength(8)
                .IsOptional();

            Property(o => o.AltoDesempenho)
                .IsRequired();

            Property(o => o.DestinacaoComercial)
                .IsRequired();

            Property(o => o.FreteRetorno)
                .IsRequired();

            Property(o => o.CepRetorno)
                .HasMaxLength(8)
                .IsOptional();

            Property(o => o.DistanciaRetorno)
                .IsOptional();

            Property(o => o.FormaPagamento)
                .IsRequired();

            HasOptional(o => o.ViagemPagamentoConta)
                .WithRequired(o => o.Viagem);
            
            Property(x => x.DataAtualizacao)
                .HasColumnName("dataatualizacao")
                .IsOptional()
                .HasColumnType("datetime");
            
            Property(o => o.ValePedagioSolicitado)
                .IsOptional();
            
            Property(o => o.ProtocoloValePedagio)
                .IsOptional()
                .HasMaxLength(30);
            
            Property(o => o.ProtocoloEnvioValePedagio)
                .IsOptional()
                .HasMaxLength(30);
            
            Property(o => o.ContaCorrente)
                .IsOptional();
            
            Property(o => o.Agencia)
                .IsOptional();
            
            Property(o => o.FormaPagamentoSemCartao)
                .IsOptional();
            
            Property(o => o.IdBanco)
                .IsOptional();
            
            Property(o => o.TipoConta)
                .IsOptional();
            
            Property(o => o.DescricaoBanco)
                .IsOptional();
        }
    }
}

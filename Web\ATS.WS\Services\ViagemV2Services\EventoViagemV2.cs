using System;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Request;
using ATS.WS.Services.ViagemServices;
using AutoMapper;

namespace ATS.WS.Services.ViagemV2Services
{
    public class EventoViagemV2
    {
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IViagemApp _viagemApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly BaixaEventoViagem _baixaEventoViagem;
        private readonly CancelamentoEventoViagem _cancelamentoEventoViagem;
        private readonly BloqueioEventoViagem _bloqueioEventoViagem;
        private readonly DesbloqueioEventoViagem _desbloqueioEventoViagem;
        private readonly IntegracaoEventoViagem _integracaoEventoViagem;
        private readonly StatusEventoViagem _statusEventoViagem;

        public EventoViagemV2(IClienteApp clienteApp, IParametrosApp parametrosApp, IProprietarioApp proprietarioApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IViagemApp viagemApp,
            IVersaoAnttLazyLoadService versaoAntt, ICadastrosApp cadastrosApp, IEmpresaApp empresaApp, IEmpresaRepository empresaRepository, BaixaEventoViagem baixaEventoViagem,
            CancelamentoEventoViagem cancelamentoEventoViagem, BloqueioEventoViagem bloqueioEventoViagem, DesbloqueioEventoViagem desbloqueioEventoViagem, IntegracaoEventoViagem integracaoEventoViagem, StatusEventoViagem statusEventoViagem)
        {
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _proprietarioApp = proprietarioApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _viagemApp = viagemApp;
            _versaoAntt = versaoAntt;
            _cadastrosApp = cadastrosApp;
            _empresaApp = empresaApp;
            _empresaRepository = empresaRepository;
            _baixaEventoViagem = baixaEventoViagem;
            _cancelamentoEventoViagem = cancelamentoEventoViagem;
            _bloqueioEventoViagem = bloqueioEventoViagem;
            _desbloqueioEventoViagem = desbloqueioEventoViagem;
            _integracaoEventoViagem = integracaoEventoViagem;
            _statusEventoViagem = statusEventoViagem;
        }

        public Retorno<object> Baixar(ViagemV2AlterarStatusRequestModel request)
        {
            var validacaoEntrada = request.ValidarEntrada();

            if (!validacaoEntrada.IsValid)
                return new Retorno<object>(validacaoEntrada, null, "Falha ao baixar viagem.");

            var resultadoBaixa =
                _statusEventoViagem.AlterarStatus(request.ViagemId ?? 0, EStatusViagem.Baixada, request.CNPJAplicacao,
                    request.Token, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit, DateTime.Now, request.ComportamentoPedagioConfirmado);

            return !resultadoBaixa.Sucesso
                ? new Retorno<object>(new ValidationResult().Add(resultadoBaixa.Mensagem, EFaultType.Error), resultadoBaixa, "Falha ao baixar viagem.")
                : new Retorno<object>(true, "Viagem baixada com sucesso.", null);
        }

        public Retorno<BaixarEventoResponseModel> BaixarEvento(ViagemV2BaixarEventoRequestModel request, bool isApi = false)
        {
            var validacaoEntrada = request.ValidarEntrada();

            if (!validacaoEntrada.IsValid)
                return new Retorno<BaixarEventoResponseModel>(validacaoEntrada, null, "Falha ao baixar evento.");

            var resultadoBaixaEvento =
                _baixaEventoViagem.BaixarEvento(Mapper.Map<BaixarEventoRequestModel>(request), isApi);

            return !resultadoBaixaEvento.Sucesso
                ? new Retorno<BaixarEventoResponseModel>(new ValidationResult().Add(resultadoBaixaEvento.Mensagem, EFaultType.Error), null, "Falha ao baixar evento.")
                : new Retorno<BaixarEventoResponseModel>(true, "Baixa realizada com sucesso.", null);
        }

        public Retorno<CancelarEventoResponseModel> CancelarEvento(ViagemV2CancelarEventoRequestModel request, bool isApi = false)
        {
            var validacaoEntrada = request.ValidarEntrada();

            if (!validacaoEntrada.IsValid)
                return new Retorno<CancelarEventoResponseModel>(validacaoEntrada, null, "Falha ao cancelar evento.");

            var resultadoCancelamentoEvento =
                _cancelamentoEventoViagem.CancelarEvento(Mapper.Map<CancelarEventoRequestModel>(request), isApi);

            return !resultadoCancelamentoEvento.Sucesso
                ? new Retorno<CancelarEventoResponseModel>(new ValidationResult().Add(resultadoCancelamentoEvento.Mensagem, EFaultType.Error), null, "Falha ao cancelar evento.")
                : new Retorno<CancelarEventoResponseModel>(true, "Cancelamento realizado com sucesso.", null);
        }

        public Retorno<BloquearEventoResponseModel> BloquearEvento(ViagemV2BloquearEventoRequestModel request)
        {
            var validacaoEntrada = request.ValidarEntrada();

            if (!validacaoEntrada.IsValid)
                return new Retorno<BloquearEventoResponseModel>(validacaoEntrada, null, "Falha ao bloquear evento.");

            var resultadoBloqueioEvento =
                _bloqueioEventoViagem.BloquearEvento(Mapper.Map<BloquearEventoRequestModel>(request));

            return !resultadoBloqueioEvento.Sucesso
                ? new Retorno<BloquearEventoResponseModel>(new ValidationResult().Add(resultadoBloqueioEvento.Mensagem, EFaultType.Error), null, "Falha ao bloquear evento.")
                : new Retorno<BloquearEventoResponseModel>(true, "Bloqueio realizado com sucesso.", null);
        }

        public Retorno<DesbloquearEventoResponseModel> DesbloquearEvento(ViagemV2DesbloquearEventoRequestModel request, bool isApi = false)
        {
            var validacaoEntrada = request.ValidarEntrada();

            if (!validacaoEntrada.IsValid)
                return new Retorno<DesbloquearEventoResponseModel>(validacaoEntrada, null, "Falha ao desbloquear evento.");

            var resultadoDesbloqueioEvento =
                _desbloqueioEventoViagem.DesbloquearEvento(Mapper.Map<DesbloquearEventoRequestModel>(request), isApi);

            return !resultadoDesbloqueioEvento.Sucesso
                ? new Retorno<DesbloquearEventoResponseModel>(new ValidationResult().Add(resultadoDesbloqueioEvento.Mensagem, EFaultType.Error), null, "Falha ao desbloquear evento")
                : new Retorno<DesbloquearEventoResponseModel>(true, "Desbloqueio realizado com sucesso.", null);
        }

        public Retorno<object> AdicionarEventos(ViagemV2AdicionarEventosRequestModel request, bool isApi = false)
        {
            var validacaoEntrada = request.ValidarEntrada();

            if (!validacaoEntrada.IsValid)
                return new Retorno<object>(validacaoEntrada, null, "Falha ao adicionar eventos.");

            var resultadoAdicionarEvento =
                _integracaoEventoViagem.AdicionarEventosViagem(Mapper.Map<AdicionarEventosViagemRequestModel>(request), isApi);

            return !resultadoAdicionarEvento.Sucesso
                ? new Retorno<object>(new ValidationResult().Add(resultadoAdicionarEvento.Mensagem, EFaultType.Error), null, "Falha ao adicionar eventos.")
                : new Retorno<object>(true, "Eventos adicionados com sucesso.", null);
        }
    }
}
﻿using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class UsuarioPermissaoFinanceiroRepository : Repository<UsuarioPermissaoFinanceiro>, IUsuarioPermissaoFinanceiroRepository
    {
        public UsuarioPermissaoFinanceiroRepository(AtsContext context) : base(context)
        {
        }
    }
}
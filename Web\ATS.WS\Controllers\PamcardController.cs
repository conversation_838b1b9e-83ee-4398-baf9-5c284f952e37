﻿using System;
using System.Web.Http;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services;

namespace ATS.WS.Controllers
{
    public class PamcardController : BaseController
    {
        private readonly IProprietarioApp _proprietarioApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly SrvProprietario _srvProprietario;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;

        public PamcardController(BaseControllerArgs baseArgs, IProprietarioApp proprietarioApp, IParametrosApp parametrosApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, SrvProprietario srvProprietario, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp) : base(baseArgs)
        {
            _proprietarioApp = proprietarioApp;
            _parametrosApp = parametrosApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _srvProprietario = srvProprietario;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarMotoristaPorCpf(MotoristaPorCpfRequestModel @params)
        {
            try
            {
                if (!ValidarToken(@params.Token, @params.DocumentoUsuarioAudit) &&
                    !_autenticacaoAplicacaoApp.AcessoConcedido(@params.CNPJAplicacao, @params.Token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde("");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }

        [HttpGet]
        [Expor(EApi.Integracao)]
        [AutorizarIntegracao]
        public string ConsultarProprietario(string token, string cnpjAplicacao, string cnpjcpfProprietario)
        {
            try
            {
                if (!ValidarToken(token) && !_autenticacaoAplicacaoApp.AcessoConcedido(cnpjAplicacao, token))
                    return new JsonResult().TokenInvalido();

                return new JsonResult().Responde(_srvProprietario.ConsultarPamcard(token, cnpjAplicacao,
                    cnpjcpfProprietario));
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new JsonResult().Mensagem(e.Message);
            }
        }
    }
}
﻿using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Common.Request
{
    public class ConsultaEventoAbonoRequestModel : RequestBase
    {
        public string CNPJCPFProprietario { get; set; }
        public string CPFMotorista  { get; set; }
        public List<EStatusViagemEvento> StatusEvento { get; set; }
        public List<ETipoEventoViagem> TipoEvento { get; set; }
        public DateTime? DataInicialViagem { get; set; }
        public DateTime? DataFinalViagem { get; set; }
    }
}

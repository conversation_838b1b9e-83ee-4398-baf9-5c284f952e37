﻿using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Collections.Generic;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IPagamentoConfiguracaoApp 
    {
        ValidationResult Add(PagamentoConfiguracao PagamentoConfiuracao);
        ValidationResult Update(PagamentoConfiguracao PagamentoConfigurcao);

        ValidationResult Inativar(int idPagamentoConfiguracao);
        ValidationResult Reativar(int idPagamentoConfiguracao);
        IEnumerable<PagamentoConfiguracao> GetPorEmpresa(int idEmpresa, int? idFilial);
        object ConsultarGrid(int? idEmpresa, int? idFilial, int take, int page, OrderFilters order, List<QueryFilters> filters);
        PagamentoConfiguracao ConsultarPorId(int idPagamentoConfiguracao);
    }
}

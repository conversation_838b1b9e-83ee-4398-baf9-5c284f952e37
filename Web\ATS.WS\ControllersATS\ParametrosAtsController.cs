using System;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Models;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersATS
{
    public class ParametrosAtsController  : BaseAtsController<IParametrosApp>
    {
        private readonly IProprietarioApp _proprietarioApp;
        
        public ParametrosAtsController(IParametrosApp app, IUserIdentity userIdentity, IProprietarioApp proprietarioApp) : base(app, userIdentity)
        {
            _proprietarioApp = proprietarioApp;
        }
        
        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ListarParametrosTransferenciaProprietario(int? idproprietario)
        {
            try
            {
                var documento = _proprietarioApp.All().Where(c => c.IdProprietario == idproprietario).Select(c => c.CNPJCPF).FirstOrDefault();

                var response = App.GetPercentualTransferenciaCartaoProprietario(documento).PercentuaisTransferenciaMotorista.Select(p => new ProprietarioParametrosTransferencia
                {
                    Chave = p.Chave,
                    Texto = p.Texto,
                    Valor = p.Valor
                }).ToList();
                
                return Responder(true, string.Empty, response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ObrigaRoteirizacaoPedagioViagemEmpresa(int? idEmpresa)
        {
            try
            {
                var response = App.GetObrigaRoteirizacaoPedagioViagemEmpresa(idEmpresa ?? default(int));
                
                return Responder(true, string.Empty, response);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult VerificarParametroSeparadorCsvConfigurado(int idEmpresa)
        {
            try
            {
                var response = App.GetSeparadorArquivoCsv(idEmpresa);

                return string.IsNullOrEmpty(response)
                    ? ResponderErro(
                        $"Nenhum separador de arquivo CSV configurado para a empresa {idEmpresa}. Acesse as configurações da empresa para adicionar um valor.")
                    : ResponderSucesso(string.Empty);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
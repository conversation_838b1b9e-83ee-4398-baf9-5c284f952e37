﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class WhiteListIPMap : EntityTypeConfiguration<WhiteListIP>
    {
        public WhiteListIPMap()
        {
            ToTable("WHITE_LIST_IP");

            HasKey(t => t.Id);
            Property(t => t.IPV4).HasMaxLength(20).IsRequired();
            Property(c => c.IdEmpresa).IsOptional();

            Property(t => t.Id)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            
            HasRequired(c => c.Empresa)
                .WithMany()
                .HasForeign<PERSON>ey(c => c.IdEmpresa);
        }
    }
}
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;
using Sistema.Framework.Util.Migrator;

namespace ATS.Data.Context.Mapping
{
    public class ClienteMap : EntityTypeConfiguration<Cliente>
    {
        public ClienteMap()
        {
            ToTable("CLIENTE");

            HasKey(t => t.IdCliente);

            Property(t => t.IdCliente)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdEmpresa)
                .HasIndex("IX_CLIENTE_CPFCNPJ_ATIVO_IDEMPRESA", true, 0)
                .IsRequired();

            Property(t => t.RazaoSocial)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.NomeFantasia)
                .IsOptional()
                .HasMaxLength(100);

            Property(t => t.TipoPessoa)
                .IsRequired();

            Property(t => t.CNPJCPF)
                .HasIndex("IX_CLIENTE_CPFCNPJ_ATIVO_IDEMPRESA", true, 1)
                .IsRequired()
                .HasMaxLength(14);

            Property(t => t.RG)
                .IsOptional()
                .HasMaxLength(15);

            Property(t => t.OrgaoExpedidorRG)
                .IsOptional()
                .HasMaxLength(10);

            Property(t => t.IE)
                .IsOptional()
                .HasMaxLength(15);

            Property(t => t.Celular)
                .IsOptional()
                .HasMaxLength(11);

            Property(t => t.Email)
                .IsOptional()
                .HasMaxLength(150);

            Property(t => t.CEP)
                .IsRequired()
                .HasMaxLength(8);

            Property(t => t.Endereco)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Numero)
                .IsOptional();

            Property(t => t.Bairro)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.IdPais)
                .IsRequired();

            Property(t => t.IdEstado)
                .IsRequired();

            Property(t => t.IdCidade)
                .IsRequired();

            Property(t => t.Latitude)
                .IsOptional();

            Property(t => t.Longitude)
                .IsOptional();

            Property(t => t.ObrigarCPFReceber)
                .IsRequired();

            Property(t => t.EnviarEmailConfirmacao)
                .IsRequired();

            Property(t => t.AutenticarCodigoBarraNF)
                .IsRequired();

            Property(t => t.StatusIntegracao)
                .IsRequired();

            Property(t => t.Ativo)
                .HasIndex("IX_CLIENTE_CPFCNPJ_ATIVO_IDEMPRESA", true, 2)
                .IsRequired();

            Property(o => o.EnviarEmailOcGerada)
                .IsRequired();
        }
    }
}
using System.Collections.Generic;
using ATS.Domain.Entities;

namespace ATS.Domain.Models
{
    public class FilialCrudResponse
    {
        public int IdFilial {get; set;}
        public string CNPJEmpresa {get; set;}
        public string RazaoSocialEmpresa {get; set;}
        public string CNPJ {get; set;}
        public string RazaoSocial {get; set;}
        public string NomeFantasia {get; set;}
        public string Sigla {get; set;}
        public string CEP {get; set;}
        public string CodigoFilial {get; set;}
        public string Endereco {get; set;}
        public string Complemento {get; set;}
        public int? Numero {get; set;}
        public string Bairro {get; set;}
        public string Telefone {get; set;}
        public string Email {get; set;}
        public int IdEmpresa {get; set;}
        public bool Ativo {get; set;}
        public decimal? Latitude {get; set;}
        public decimal? Longitude {get; set;}
        public int? IdFilialMae {get; set;}
        public bool PontoApoio {get; set;}
        public int IdCidade {get; set;}
        public int IdEstado {get; set;}
        public int IdPais {get; set;}
        public bool EmailSsl {get; set;}
        public string EmailUsuario {get; set;}
        public string EmailSenha {get; set;}
        public string EmailServidor {get; set;}
        public object AlcadasBloqueioGestorValor {get; set;}
        public decimal? EmailPorta {get; set;}
        public string EmailEndereco {get; set;}
        public string EmailNome {get; set;}
        public ICollection<FilialContatos> FilialContatos {get; set;}
        public string IdSistemaExterno {get; set;}
        public bool? PontoReferencia { get; set; }        
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class PagamentoConfiguracaoMap : EntityTypeConfiguration<PagamentoConfiguracao>
    {
        public PagamentoConfiguracaoMap()
        {
            ToTable("PAGAMENTO_CONFIGURACAO");

            HasKey(x => x.IdPagamentoConfiguracao);

            HasRequired(x => x.Empresa).
                WithMany(x => x.PagamentoConfiguracao)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Filial)
                .WithMany(x => x.PagamentoConfiguracoes)
                .HasForeignKey(x => x.IdFilial);


        }
    }
}

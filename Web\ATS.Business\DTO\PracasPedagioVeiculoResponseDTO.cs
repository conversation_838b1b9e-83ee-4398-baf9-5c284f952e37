﻿using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class PracasPedagioVeiculoResponseDTO
    {
        public string SaldoVP { get; set; }
        public string QuantidadePeriodo{ get; set; }
        public string ValorTotalPeriodo { get; set; }
        public List<PracasPedagioVeiculoItemResponseDTO> Itens { get; set; }
    }
    
    public class PracasPedagioVeiculoItemResponseDTO
    {
        public long Id { get; set; }
        public string Concessionaria { get; set; }
        public string Descricao { get; set; }
        public string Praca { get; set; }
        public string DataEmissao { get; set; }
        public long? TagSerialNumber { get; set; }
        public string CidadePraca { get; set; }
        public string EstadoPraca { get; set; }
        public decimal? LatitudePraca { get; set; }
        public decimal? LongitudePraca { get; set; }
        public long? IdAnttPraca { get; set; }
        public string Valor  { get; set; }
        public string Tipo { get; set; }
    }
}
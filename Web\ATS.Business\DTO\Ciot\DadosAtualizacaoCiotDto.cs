using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO.Ciot
{
    public class DadosAtualizacaoCiotDto
    {
        public int IdEmpresa { get; set; }
        public ETipoDeclaracao TipoDeclaracao { get; set; }
        public int? IdContratoCiotAgregado { get; set; }
        public string Ciot { get; set; }
        public string Senha { get; set; }
        public List<DadosAtualizacaoCiotViagemDto> ViagensVinculadas { get; set; }
    }
    
    public class DadosAtualizacaoCiotViagemDto
    {
        public int? IdAdministradoraPlataforma { get; set; }
        public EStatusViagem StatusViagem { get; set; }
        public decimal IRRPF { get; set; }
        public decimal INSS { get; set; }
        public decimal SESTSENAT { get; set; }
        public string DocumentoCliente { get; set; }
        public List<DadosAtualizacaoCiotViagemEventoDto> ViagemEventos { get; set; }
    }
    
    public class DadosAtualizacaoCiotViagemEventoDto
    {
        public ETipoEventoViagem TipoEventoViagem { get; set; }
        public EStatusViagemEvento Status { get; set; }
        public decimal ValorPagamento { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
    }
}
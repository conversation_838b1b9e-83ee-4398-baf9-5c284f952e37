using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Abastecimento.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Abastecimento.Client.Models;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using Extratta.Tag.Application.Events.Events;
using MassTransit;
using NLog;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ValidationResult = ATS.Domain.Validation.ValidationResult;

namespace ATS.WS.Services.ViagemServices
{
    public class IntegracaoMeioHomologadoViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly PedagioViagem _pedagioViagem;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly IViagemRepository _viagemRepository;
        private readonly WebHookViagem _webHookViagem;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly PedagioAppFactoryDependencies _pedagioAppFactoryDependencies;
        private readonly IEmpresaService _empresaService;
        private readonly ICargaAvulsaApp _cargaAvulsaApp;
        private readonly IAbastecimentoApiClient _abastecimentoApiClient;
        private readonly ITagExtrattaApp _tagExtrattaApp;
        private readonly IPublishEndpoint _publisher;
        private readonly ITransacaoPixService _transacaoPixService;
        private readonly ITransacaoPixRepository _transacaoPixRepository;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly IViagemEventoRepository _viagemEventoRepository;

        public IntegracaoMeioHomologadoViagem(IVersaoAnttLazyLoadService versaoAntt, IClienteApp clienteApp, IParametrosApp parametrosApp, IViagemApp viagemApp, ICiotV2App ciotV2App,
            ICiotV3App ciotV3App, IProprietarioApp proprietarioApp, PedagioViagem pedagioViagem, ITransacaoCartaoApp transacaoCartaoApp, IViagemRepository viagemRepository,
            WebHookViagem webHookViagem, IViagemEventoApp viagemEventoApp, IEmpresaApp empresaApp, ICidadeApp cidadeApp, PedagioAppFactoryDependencies pedagioAppFactoryDependencies, IEmpresaService empresaService, IAbastecimentoApiClient abastecimentoApiClient, ICargaAvulsaApp cargaAvulsaApp, ITagExtrattaApp tagExtrattaApp, IPublishEndpoint publisher, ITransacaoPixService transacaoPixService, ITransacaoPixRepository transacaoPixRepository, IParametrosEmpresaService parametrosEmpresaService, IParametrosProprietarioService parametrosProprietarioService, IViagemEventoRepository viagemEventoRepository)
        {
            _versaoAntt = versaoAntt;
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _pedagioViagem = pedagioViagem;
            _transacaoCartaoApp = transacaoCartaoApp;
            _viagemRepository = viagemRepository;
            _webHookViagem = webHookViagem;
            _viagemEventoApp = viagemEventoApp;
            _empresaApp = empresaApp;
            _cidadeApp = cidadeApp;
            _pedagioAppFactoryDependencies = pedagioAppFactoryDependencies;
            _empresaService = empresaService;
            _cargaAvulsaApp = cargaAvulsaApp;
            _tagExtrattaApp = tagExtrattaApp;
            _publisher = publisher;
            _transacaoPixService = transacaoPixService;
            _transacaoPixRepository = transacaoPixRepository;
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosProprietarioService = parametrosProprietarioService;
            _viagemEventoRepository = viagemEventoRepository;
            _abastecimentoApiClient = abastecimentoApiClient;
        }

        public ProcessoIntegracaoViagemDto RealizarIntegracoesViagem(ViagemIntegrarRequestModel @params, Viagem viagem, 
            ICartoesApp cartoesApp, out DeclararCiotResult declararCiotResult, out SolicitarCompraPedagioResponseDTO compraPedagioResponse, 
            CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario, 
            Retorno<BloqueioGestorValorResponseDTO> liberacaoGestor, bool retificarCiot = false, bool atualizarCiot = false, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return RealizarIntegracoesViagemV2(@params, viagem, cartoesApp, out declararCiotResult,
                        out compraPedagioResponse, cartaoVinculadoMotorista, cartaoVinculadoProprietario, liberacaoGestor, retificarCiot, atualizarCiot, isApi);
                case EVersaoAntt.Versao3:
                    return RealizarIntegracoesViagemV3(@params, viagem, cartoesApp,  out declararCiotResult,
                        out compraPedagioResponse, cartaoVinculadoMotorista, cartaoVinculadoProprietario, liberacaoGestor, retificarCiot, atualizarCiot);
                default:
                    return RealizarIntegracoesViagemV2(@params, viagem, cartoesApp,  out declararCiotResult,
                        out compraPedagioResponse, cartaoVinculadoMotorista, cartaoVinculadoProprietario, liberacaoGestor, retificarCiot, atualizarCiot, isApi);
            }
        }
        
        private ProcessoIntegracaoViagemDto RealizarIntegracoesViagemV2(ViagemIntegrarRequestModel @params, Viagem viagem, 
            ICartoesApp cartoesApp, out DeclararCiotResult declararCiotResult, out SolicitarCompraPedagioResponseDTO compraPedagioResponse, 
            CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario, 
            Retorno<BloqueioGestorValorResponseDTO> liberacaoGestor, bool retificarCiot = false, bool atualizarCiot = false, bool isApi = false)
        {
            _webHookViagem.SetarWebhooks(@params, viagem);

            var mensagensProcessos = new ProcessoIntegracaoViagemDto();
            
            declararCiotResult = new DeclararCiotResult {Resultado = EResultadoDeclaracaoCiot.Erro}; // Valor será sobreescrito para baixo
            compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
            {
                Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado,
                Status = EResultadoCompraPedagio.NaoRealizado
            };

            try
            {
                ValidationResult validationResult;
                var viagemEventoApp = _viagemEventoApp;

                #region Preencher valor pedágio
                
                var mensagemRetorno = PreencheValorPedagioParaDeclaracaoCiot(@params, viagem, cartoesApp);
                if (!string.IsNullOrWhiteSpace(mensagemRetorno))
                {
                    mensagensProcessos.MensagemPedagio = "Falha ao obter valor de pedágio*: " + mensagemRetorno;
                    return mensagensProcessos;
                }
                
                #endregion
                
                #region CIOT

                var cnpj = !string.IsNullOrWhiteSpace(@params.CNPJEmpresa) ? @params.CNPJEmpresa : @params.CNPJAplicacao;
                var naoTinhaCiotAinda = !viagem.IdDeclaracaoCiot.HasValue;
                
                declararCiotResult = _ciotV2App.DeclararCiotFromIntegracaoViagem(viagem, cnpj, viagem.HabilitarDeclaracaoCiot, retificarCiot, @params.ForcarCiotNaoEquiparado, @params.GerarCiotTacAgregado); // AQUI

                if (naoTinhaCiotAinda && viagem.IdDeclaracaoCiot.HasValue && @params.ViagemEventos?.Any(ve => ve.HabilitarPagamentoCartao) == true)
                    atualizarCiot = true;
                
                if (viagem.ResultadoDeclaracaoCiot != EResultadoDeclaracaoCiot.Sucesso)
                {
                    viagem.ResultadoDeclaracaoCiot = declararCiotResult.Resultado;
                    viagem.MensagemDeclaracaoCiot = declararCiotResult.Mensagem;   
                }

                if (@params.HabilitarDeclaracaoCiot)
                    viagem.HabilitarDeclaracaoCiot = true;
                
                validationResult = _viagemApp.Update(viagem);
                
                if (declararCiotResult.Resultado == EResultadoDeclaracaoCiot.Erro)
                    mensagensProcessos.MensagemCiot = "Falha ao declarar CIOT: " + declararCiotResult.Mensagem;
                else if (!validationResult.IsValid)
                    mensagensProcessos.MensagemCiot = "Falha ao declarar CIOT*: " + validationResult.ToFormatedMessage();

                #endregion
                
                if (liberacaoGestor.Sucesso)
                {
                    #region Pedágio

                    // Pedágio
                    if (declararCiotResult.Resultado != EResultadoDeclaracaoCiot.Erro && string.IsNullOrWhiteSpace(mensagemRetorno))
                    {
                        if (@params.Pedagio != null && @params.Pedagio.Fornecedor != FornecedorEnum.Desabilitado &&
                            (viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.NaoRealizado ||
                             viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.Erro))
                        {
                            validationResult = _pedagioViagem.ValidacoesCompraPedagio(@params);

                            if (validationResult.IsValid)
                            {
                                compraPedagioResponse = _pedagioViagem.SolicitarCompraPedagio(@params, viagem, declararCiotResult);
                                viagem.ResultadoCompraPedagio = compraPedagioResponse.Status;
                                viagem.MensagemCompraPedagio = compraPedagioResponse.Mensagem.ValueLimited(500);
                                viagem.NumeroProtocoloPedagio = compraPedagioResponse.ProtocoloProcessamento;
                                viagem.EstornoSaldoResidualPedagioSolicitado = compraPedagioResponse.EstornoSaldoResidualSolicitado;
                                viagem.ProtocoloValePedagio = compraPedagioResponse.ProtocoloValePedagio;
                                viagem.ProtocoloEnvioValePedagio = compraPedagioResponse.ProtocoloEnvioValePedagio;
                                if (compraPedagioResponse.Valor.HasValue)
                                    viagem.ValorPedagio =  compraPedagioResponse.Valor.ToDecimal();
                                
                                validationResult = _viagemApp.Update(viagem);

                                if (compraPedagioResponse.Status == EResultadoCompraPedagio.Erro)
                                    mensagensProcessos.MensagemPedagio = "Falha ao comprar pedágio: " + compraPedagioResponse.Mensagem;
                                else if (!validationResult.IsValid)
                                    mensagensProcessos.MensagemPedagio = "Falha ao comprar pedágio*: " + validationResult.ToFormatedMessage();
                            }
                            else
                            {
                                mensagensProcessos.MensagemPedagio = "Falha ao comprar pedágio*: " + validationResult.ToFormatedMessage();
                            }
                        }
                    }

                    if (viagem.ResultadoCompraPedagio != EResultadoCompraPedagio.NaoRealizado)
                    {
                        compraPedagioResponse = _viagemApp.GetStatusPedagio(viagem);
                        compraPedagioResponse.Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado;
                    }

                    #endregion

                    #region Cartão / Abastecimento

                    // Cartão
                    var retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.NaoHabilitado, string.Empty);

                    if (viagem.ViagemEventos != null && declararCiotResult.Resultado != EResultadoDeclaracaoCiot.Erro)
                    {
                        foreach (var viagemEventoModel in @params.ViagemEventos)
                        {
                            if (viagemEventoModel.Status != EStatusViagemEvento.Baixado && viagemEventoModel.Status != EStatusViagemEvento.Cancelado)
                                continue;

                            ViagemEvento evento;

                            if(viagemEventoModel.IdViagemEvento.HasValue)
                                evento = viagem.ViagemEventos.FirstOrDefault(ve => ve.IdViagemEvento == viagemEventoModel.IdViagemEvento);

                            else if (!string.IsNullOrWhiteSpace(viagemEventoModel.NumeroControle))
                                evento = viagem.ViagemEventos.FirstOrDefault(ve => ve.NumeroControle == viagemEventoModel.NumeroControle);

                            else if (!string.IsNullOrWhiteSpace(viagemEventoModel.Token))
                                evento = viagem.ViagemEventos.FirstOrDefault(ve => ve.Token == viagemEventoModel.Token);

                            else
                            {
                                // Se haver somente um evento deste tipo, pega o first.
                                if (viagem.ViagemEventos.Count(ve => ve.TipoEventoViagem == viagemEventoModel.TipoEvento) == 1)
                                    evento = viagem.ViagemEventos.First(ve => ve.TipoEventoViagem == viagemEventoModel.TipoEvento);
                                else
                                    evento = viagem.ViagemEventos.FirstOrDefault(
                                        ve => ve.TipoEventoViagem == viagemEventoModel.TipoEvento &&
                                              ve.ValorPagamento == viagemEventoModel.ValorPagamento);
                            }

                            if (viagemEventoModel.DadosAbastecimento != null)
                            {
                                switch (viagemEventoModel.Status)
                                {
                                    case EStatusViagemEvento.Baixado:

                                        switch (viagemEventoModel.DadosAbastecimento.Fornecedor)
                                        {
                                            case EFornecedorPedagio.TicketLog:

                                                var retornoabastecimento = AbastecimentoTicketLog(viagemEventoModel, evento.IdViagemEvento, evento.IdEmpresa, @params.Placa, @params.NomeUsuarioAudit);

                                                if (!retornoabastecimento.Sucesso)
                                                {
                                                    mensagensProcessos.MensagemCartao = $"Falha ao carregar abastecimento ({evento.TipoEventoViagem}): " + retornoabastecimento.Mensagem;
                                                }
                                                else
                                                {
                                                    retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Sucesso, "Carga de abastecimento feita com sucesso.");

                                                    evento.Status = EStatusViagemEvento.Baixado;
                                                    evento.DataHoraPagamento= DateTime.Now;
                                                    evento.IdAbastecimentoticket = retornoabastecimento.IdAbastecimento;
                                                }

                                                break;
                                            default:
                                                throw new ArgumentOutOfRangeException("Fornecedor de abastecimento não mapeado");
                                        }

                                        break;

                                    case EStatusViagemEvento.Cancelado:

                                        if (evento.IdAbastecimentoticket.HasValue)
                                        {
                                            var retornoCancelamento = CancelarAbastecimentoTicketLog(evento.IdAbastecimentoticket.Value, evento.IdEmpresa, @params.NomeUsuarioAudit);

                                            if (!retornoCancelamento.Sucesso)
                                            {
                                                retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Erro, retornoCancelamento.Mensagem);

                                                mensagensProcessos.MensagemCartao = $"Falha ao cancelar abastecimento ({evento.TipoEventoViagem}): " + retornoCancelamento.Mensagem;
                                            }
                                            else
                                            {
                                                retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Sucesso, "Carga de abastecimento feita com sucesso.");

                                                evento.Status = EStatusViagemEvento.Baixado;
                                                evento.DataHoraPagamento= DateTime.Now;
                                            }
                                        }

                                        break;
                                }

                                continue;
                            }

                            // Verifica se originalmente o evento deveria integrar no meio homologado
                            if (evento == null || (!evento.HabilitarPagamentoCartao && evento.HabilitarPagamentoPix != true))
                                continue;

                            //evento pix bloqueado caiu p avaliacao do gestor e so deve ser baixado/cancelado por ele
                            if (evento.HabilitarPagamentoPix == true && evento.Status == EStatusViagemEvento.Bloqueado) 
                                continue;
                            
                            OperacaoCartaoResponseDTO cargaResultado;

                            switch (viagemEventoModel.Status)
                            {
                                case EStatusViagemEvento.Baixado:

                                    //pix
                                    if (_parametrosEmpresaService.GetPermiteRealizarPagamentoPix(viagem.IdEmpresa) && viagemEventoModel.HabilitarPagamentoPix == true)
                                    {
                                        if (!_parametrosProprietarioService.GetProprietarioPermiteReceberPagamentoPix(viagem.CPFCNPJProprietario, viagem.IdEmpresa))
                                        {
                                            mensagensProcessos.MensagemPix += $"Proprietário sem permissão para recebimento Pix pela empresa. ";
                                            break;
                                        }

                                        if (viagemEventoModel.Status != EStatusViagemEvento.Baixado && viagemEventoModel.Status != EStatusViagemEvento.Aberto)
                                        {
                                            mensagensProcessos.MensagemPix += $"Integração de parcelas Pix permitida apenas com status 'Baixado' ou 'Aberto'. ";
                                            break;
                                        }

                                        var valor = 0m;

                                        if (evento.ValorPagamento != 0) valor = evento.ValorPagamento;
                                        else if (evento.ValorTotalPagamento != null && evento.ValorTotalPagamento != 0) valor = evento.ValorTotalPagamento.Value;

                                        if (valor == 0)
                                        {
                                            mensagensProcessos.MensagemPix += $"Não foi possível obter o valor da parcela Pix de {evento.TipoEventoViagem.GetDescription()}.";
                                            break;
                                        }

                                        var mensagem = $"Tipo: {evento.TipoEventoViagem.GetDescription()}, " +
                                                       $"IdViagem: {viagem.IdViagem}, " +
                                                       $"IdParcela: {evento.IdViagemEvento}, " +
                                                       $"CIOT: {viagem.DeclaracaoCiot?.Ciot ?? "--"}, " +
                                                       $"Documento: {viagem.DocumentoCliente}";
                                        var resultadoPix = _transacaoPixService.EfetuarTransferenciaPix(viagem.IdEmpresa, 
                                            viagem.CPFCNPJProprietario, valor, evento, mensagem, @params.DocumentoUsuarioAudit, isApi);

                                        if (!resultadoPix.Success)
                                        {
                                            viagem.StatusViagem = EStatusViagem.Aberto;
                                            //se o evento foi bloqueado durante a transferencia quer dizer q caiu p aprovacao do gestor
                                            if (evento.Status != EStatusViagemEvento.Bloqueado) evento.Status = EStatusViagemEvento.Aberto;
                                            evento.DataHoraPagamento = null;
                                            mensagensProcessos.MensagemPix += $"Pix de {evento.TipoEventoViagem.GetDescription()} não efetuado. {resultadoPix?.Messages.FirstOrDefault()}";
                                            break;
                                        }

                                        evento.Status = EStatusViagemEvento.Baixado;
                                        evento.DataHoraPagamento = DateTime.Now;
                                    }
                                    //cartao
                                    else
                                    {
                                        cargaResultado = cartoesApp.RealizarCargaFrete(viagem, evento,
                                            cartaoVinculadoMotorista, cartaoVinculadoProprietario);

                                        switch (cargaResultado.Status)
                                        {
                                            case EStatusPagamentoCartao.Baixado:

                                                if (retornoOperacaoCartao.Status ==
                                                    ERetornoOperacaoCartao.NaoHabilitado)
                                                {
                                                    retornoOperacaoCartao =
                                                        new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Sucesso,
                                                            cargaResultado.Mensagem);
                                                }

                                                if (viagemEventoModel.Status == EStatusViagemEvento.Baixado &&
                                                    evento.Status != EStatusViagemEvento.Baixado)
                                                {
                                                    evento.Status = EStatusViagemEvento.Baixado;
                                                    evento.DataHoraPagamento = DateTime.Now;
                                                }

                                                break;

                                            case EStatusPagamentoCartao.Pendente:

                                                if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro &&
                                                    retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Sucesso)
                                                {
                                                    retornoOperacaoCartao =
                                                        new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Pendente,
                                                            cargaResultado.Mensagem);
                                                }

                                                if (viagemEventoModel.Status == EStatusViagemEvento.Baixado &&
                                                    evento.Status != EStatusViagemEvento.Baixado)
                                                {
                                                    evento.Status = EStatusViagemEvento.Baixado;
                                                    evento.DataHoraPagamento = DateTime.Now;
                                                }

                                                break;

                                            case EStatusPagamentoCartao.Erro:

                                                if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro)
                                                {
                                                    retornoOperacaoCartao =
                                                        new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Erro,
                                                            cargaResultado.Mensagem);
                                                }

                                                evento.Status = EStatusViagemEvento.Aberto;

                                                if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                                                    mensagensProcessos.MensagemCartao =
                                                        $"Falha ao carregar cartão ({evento.TipoEventoViagem}): " +
                                                        cargaResultado.Mensagem;

                                                break;
                                        }
                                    }
                                    break;

                                case EStatusViagemEvento.Cancelado:
                                    //pix
                                    if (evento.HabilitarPagamentoPix == true)
                                    {
                                        mensagensProcessos.MensagemPix += $"Evento Pix será cancelado, porém nenhum valor será estornado. ";
                                        evento.Status = EStatusViagemEvento.Cancelado;
                                        evento.DataHoraCancelamento = DateTime.Now;
                                        break;
                                    }
                                    
                                    var viagemEvento = Mapper.Map<ViagemEvento>(evento);

                                    cargaResultado = cartoesApp.RealizarEstornoCargaFrete(viagemEvento,
                                        viagem.IdEmpresa, viagem.CPFMotorista, viagem.CPFCNPJProprietario,
                                        EOrigemTransacaoCartao.Automatico);

                                    switch (cargaResultado.Status)
                                    {
                                        case EStatusPagamentoCartao.Baixado:

                                            if (retornoOperacaoCartao.Status == ERetornoOperacaoCartao.NaoHabilitado)
                                            {
                                                retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Sucesso, cargaResultado.Mensagem);
                                            }
                                            
                                            if (viagemEventoModel.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Cancelado)
                                            {
                                                evento.Status = EStatusViagemEvento.Cancelado;
                                                evento.DataHoraCancelamento = DateTime.Now;
                                            }

                                            break;

                                        case EStatusPagamentoCartao.Pendente:

                                            if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro && retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Sucesso)
                                            {
                                                retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Pendente, cargaResultado.Mensagem);
                                            }
                                            
                                            if (viagemEventoModel.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Cancelado)
                                            {
                                                evento.Status = EStatusViagemEvento.Cancelado;
                                                evento.DataHoraCancelamento = DateTime.Now;
                                            }

                                            break;

                                        case EStatusPagamentoCartao.Erro:

                                            if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro)
                                            {
                                                retornoOperacaoCartao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Erro, cargaResultado.Mensagem);
                                            }

                                            if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                                                mensagensProcessos.MensagemCartao =
                                                    $"Falha ao estornar cartão ({evento.TipoEventoViagem}): " +
                                                    cargaResultado.Mensagem;

                                            break;
                                    }

                                    break;

                                default:
                                    throw new InvalidOperationException("Ocorreu uma tentativa de integração de um evento com status inesperado.");
                            }

                            validationResult = viagemEventoApp.Update(evento);
                            if (!validationResult.IsValid && string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                                mensagensProcessos.MensagemCartao = $"Falha ao processar cartão ({evento.TipoEventoViagem})*: " +
                                                                    validationResult.ToFormatedMessage();
                        }

                        validationResult = _viagemApp.Update(viagem);
                        if (!validationResult.IsValid && string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                            mensagensProcessos.MensagemCartao = $"Falha ao processar cartão**: " + validationResult.ToFormatedMessage();
                    }

                    #endregion
                }
                
                #region Atualizar CIOT
                // Atualiza os valores pagos na impressão do CIOT, pois o CIOT é feito antes do cartão, e sem isso o pagamento fica desatualizado
                if (atualizarCiot && viagem.IdDeclaracaoCiot.HasValue)
                    _ciotV2App.AtualizarCiot(viagem.IdDeclaracaoCiot.Value);
                #endregion
            }
            catch (Exception e)
            {
                var message = $"Erro ao processar integração de viagem: {viagem.IdViagem}. {e.Message}";
                
                LogManager.GetCurrentClassLogger().Error(e, message);

                if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemViagem))
                    mensagensProcessos.MensagemViagem = message;
            }

            if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemViagem)) mensagensProcessos.Sucesso = true;
            return mensagensProcessos;
        }

        private AbastecimentoTicketLogResponseDTO AbastecimentoTicketLog(ViagemEventoIntegrarModel request, int viagemEventoId, int idEmpresa, string placa, string usuarioAuditoria)
        {
            var cnpjEmpresa = _empresaService.GetCnpjById(idEmpresa);

            var requestTicket = new InserirCreditoTicketLogRequest
            {
                Valor = request.ValorPagamento ?? 0,
                CnpjEmpresa = cnpjEmpresa,
                CodigoCredito = request.DadosAbastecimento?.CodigoCredito ?? viagemEventoId,
                CodigoProduto = request.DadosAbastecimento?.CodigoProduto ?? 0,
                NumeroCartao = request.DadosAbastecimento?.NumeroCartao,
                DataLiberacao = request.DadosAbastecimento?.DataLiberacao,
                DataValidade = request.DadosAbastecimento?.DataValidade,
                UsuarioAuditoria = usuarioAuditoria
            };

            if (string.IsNullOrWhiteSpace(request.DadosAbastecimento?.NumeroCartao))
                requestTicket.PlacaVeiculo = placa;

            var response = _abastecimentoApiClient.InserirTicket(requestTicket);

            if (!response.Success)
                return new AbastecimentoTicketLogResponseDTO
                {
                    Sucesso = false,
                    Mensagem = response.ToString()
                };

            return new AbastecimentoTicketLogResponseDTO
            {
                Sucesso = true,
                IdAbastecimento = response.Value.Id
            };
        }

        private CancelamentoAbastecimentoTicketLogResponseDTO CancelarAbastecimentoTicketLog(int abastecimentoId, int idEmpresa, string usuarioAuditoria)
        {
            var cnpjEmpresa = _empresaService.GetCnpjById(idEmpresa);

            var requestTicket = new CancelarCreditoTicketLogRequest
            {
                CnpjEmpresa = cnpjEmpresa,
                Motivo = "Cancelamento do evento da Viagem",
                UsuarioAuditoria = usuarioAuditoria
            };

            var response = _abastecimentoApiClient.CancelarTicket(abastecimentoId, requestTicket);

            if (!response.Success)
                return new CancelamentoAbastecimentoTicketLogResponseDTO
                {
                    Sucesso = false,
                    Mensagem = response.ToString()
                };

            return new CancelamentoAbastecimentoTicketLogResponseDTO
            {
                Sucesso = true
            };
        }
        
        public SolicitarCompraPedagioResponseDTO ComprarPedagioAvulso(ViagemIntegrarRequestModel @params, Viagem viagem, ICartoesApp cartoesApp, Retorno<BloqueioGestorValorResponseDTO> liberacaoGestor)
        {
            var compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
            {
                Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado,
                Status = EResultadoCompraPedagio.NaoRealizado
            };

            try
            {
                ValidationResult validationResult;
                
                var mensagemRetorno = PreencheValorPedagioParaDeclaracaoCiot(@params, viagem, cartoesApp);
                
                if (!string.IsNullOrWhiteSpace(mensagemRetorno))
                {
                    compraPedagioResponse.Mensagem = "Falha ao obter valor de pedágio: " + mensagemRetorno;
                    return compraPedagioResponse;
                }

                if (!liberacaoGestor.Sucesso)
                {
                    compraPedagioResponse.Mensagem = liberacaoGestor.Mensagem;
                    return compraPedagioResponse;
                }
                
                if (@params.Pedagio != null)
                {
                    validationResult = _pedagioViagem.ValidacoesCompraPedagio(@params);

                    if (validationResult.IsValid)
                    {
                        compraPedagioResponse = _pedagioViagem.SolicitarCompraPedagio(@params, viagem, null);
                        
                        viagem.ResultadoCompraPedagio = compraPedagioResponse.Status;
                        viagem.MensagemCompraPedagio = compraPedagioResponse.Mensagem.ValueLimited(500);
                        viagem.NumeroProtocoloPedagio = compraPedagioResponse.ProtocoloProcessamento;
                        viagem.EstornoSaldoResidualPedagioSolicitado = compraPedagioResponse.EstornoSaldoResidualSolicitado;
                        viagem.ProtocoloValePedagio = compraPedagioResponse.ProtocoloValePedagio;
                        viagem.ProtocoloEnvioValePedagio = compraPedagioResponse.ProtocoloEnvioValePedagio;
                        
                        if (compraPedagioResponse.Valor.HasValue)
                            viagem.ValorPedagio = compraPedagioResponse.Valor.ToDecimal();

                        validationResult = _viagemApp.Update(viagem);

                        if (compraPedagioResponse.Status == EResultadoCompraPedagio.Erro)
                            compraPedagioResponse.Mensagem = "Falha ao comprar pedágio, " + compraPedagioResponse.Mensagem;
                        else if (!validationResult.IsValid)
                            compraPedagioResponse.Mensagem = "Falha ao comprar pedágio, " + validationResult.ToFormatedMessage();
                    }
                    else
                        compraPedagioResponse.Mensagem = "Falha ao comprar pedágio, " + validationResult.ToFormatedMessage();
                }
                
                if (viagem.ResultadoCompraPedagio != EResultadoCompraPedagio.NaoRealizado)
                {
                    compraPedagioResponse = _viagemApp.GetStatusPedagio(viagem);
                    compraPedagioResponse.Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado;
                }
                else
                {
                    compraPedagioResponse.Mensagem = liberacaoGestor.Mensagem;
                    return compraPedagioResponse;
                }
                
            }
            catch (Exception e)
            {
                var message = $"Erro ao processar integração de viagem: {viagem.IdViagem}. {e.Message}";
                
                LogManager.GetCurrentClassLogger().Error(e, message);

                if (string.IsNullOrWhiteSpace(compraPedagioResponse.Mensagem))
                    compraPedagioResponse.Mensagem = message;
            }

            return compraPedagioResponse;
        }
        
        private ProcessoIntegracaoViagemDto RealizarIntegracoesViagemV3(ViagemIntegrarRequestModel @params, Viagem viagem, ICartoesApp cartoesApp, out DeclararCiotResult declararCiotResult, out SolicitarCompraPedagioResponseDTO compraPedagioResponse, CartaoVinculadoPessoaListResponse cartaoVinculadoMotorista, CartaoVinculadoPessoaListResponse cartaoVinculadoProprietario, Retorno<BloqueioGestorValorResponseDTO> liberacaoGestor, bool retificarCiot = false, bool atualizarCiot = false)
        {
            _webHookViagem.SetarWebhooks(@params, viagem);
            
            var mensagensProcessos = new ProcessoIntegracaoViagemDto();
            declararCiotResult = new DeclararCiotResult
                {Resultado = EResultadoDeclaracaoCiot.Erro}; // Valor será sobreescrito para baixo
            compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
            {
                Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado,
                Status = EResultadoCompraPedagio.NaoRealizado
            };

            try
            {
                ValidationResult validationResult;
                var viagemEventoApp = _viagemEventoApp;

                #region Preencher valor pedágio
                
                var mensagemRetorno = PreencheValorPedagioParaDeclaracaoCiot(@params, viagem, cartoesApp);
                if (!string.IsNullOrWhiteSpace(mensagemRetorno))
                {
                    mensagensProcessos.MensagemPedagio = "Falha ao obter valor de pedágio*: " + mensagemRetorno;
                    return mensagensProcessos;
                }
                
                #endregion
                
                #region CIOT

                var cnpj = !string.IsNullOrWhiteSpace(@params.CNPJEmpresa) ? @params.CNPJEmpresa : @params.CNPJAplicacao;
                var naoTinhaCiotAinda = !viagem.IdDeclaracaoCiot.HasValue;

                declararCiotResult =
                    _ciotV3App.DeclararCiotFromIntegracaoViagem(viagem, cnpj, @params.HabilitarDeclaracaoCiot,
                        retificarCiot, @params.GerarCiotTacAgregado);

                if (naoTinhaCiotAinda && viagem.IdDeclaracaoCiot.HasValue && viagem.FormaPagamento == EViagemFormaPagamento.Cartao)
                    atualizarCiot = true;
                
                if (viagem.ResultadoDeclaracaoCiot != EResultadoDeclaracaoCiot.Sucesso)
                {
                    viagem.ResultadoDeclaracaoCiot = declararCiotResult.Resultado;
                    viagem.MensagemDeclaracaoCiot = declararCiotResult.Mensagem;   
                }

                if (@params.HabilitarDeclaracaoCiot)
                {
                    viagem.HabilitarDeclaracaoCiot = true;
                }

                validationResult = _viagemApp.Update(viagem);

                if (declararCiotResult.Resultado == EResultadoDeclaracaoCiot.Erro)
                    mensagensProcessos.MensagemCiot = "Falha ao atualizar CIOT: " + declararCiotResult.Mensagem;
                else if (!validationResult.IsValid)
                    mensagensProcessos.MensagemCiot = "Falha ao declarar CIOT*: " + validationResult.ToFormatedMessage();

                #endregion
                
                if (liberacaoGestor.Sucesso)
                {
                    #region Pedágio

                    // Pedágio
                    if (declararCiotResult.Resultado != EResultadoDeclaracaoCiot.Erro &&
                        string.IsNullOrWhiteSpace(mensagemRetorno))
                    {
                        if (@params.Pedagio != null && @params.Pedagio.Fornecedor != FornecedorEnum.Desabilitado &&
                            (viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.NaoRealizado ||
                             viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.Erro))
                        {
                            validationResult = _pedagioViagem.ValidacoesCompraPedagio(@params);

                            if (validationResult.IsValid)
                            {
                                compraPedagioResponse = _pedagioViagem.SolicitarCompraPedagio(@params, viagem, declararCiotResult);
                                viagem.ResultadoCompraPedagio = compraPedagioResponse.Status;
                                viagem.MensagemCompraPedagio = compraPedagioResponse.Mensagem.ValueLimited(500);
                                viagem.NumeroProtocoloPedagio = compraPedagioResponse.ProtocoloProcessamento;
                                viagem.EstornoSaldoResidualPedagioSolicitado = compraPedagioResponse.EstornoSaldoResidualSolicitado;
                                viagem.ProtocoloValePedagio = compraPedagioResponse.ProtocoloValePedagio;
                                viagem.ProtocoloEnvioValePedagio = compraPedagioResponse.ProtocoloEnvioValePedagio;
                                if (compraPedagioResponse.Valor.HasValue)
                                    viagem.ValorPedagio =  compraPedagioResponse.Valor.ToDecimal();
                                
                                validationResult = _viagemApp.Update(viagem);

                                if (compraPedagioResponse.Status == EResultadoCompraPedagio.Erro)
                                    mensagensProcessos.MensagemPedagio = "Falha ao comprar pedágio: " + compraPedagioResponse.Mensagem;
                                else if (!validationResult.IsValid)
                                    mensagensProcessos.MensagemPedagio = "Falha ao comprar pedágio*: " + validationResult.ToFormatedMessage();
                            }
                            else
                            {
                                mensagensProcessos.MensagemPedagio = "Falha ao comprar pedágio*: " + validationResult.ToFormatedMessage();
                            }
                        }
                    }

                    if (viagem.ResultadoCompraPedagio != EResultadoCompraPedagio.NaoRealizado)
                    {
                        compraPedagioResponse = _viagemApp.GetStatusPedagio(viagem);
                        compraPedagioResponse.Fornecedor = @params.Pedagio?.Fornecedor ?? FornecedorEnum.Desabilitado;
                    }

                    #endregion

                    #region Cartão

                    // Cartão
                    var retornoOperacaoCartao =
                        new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.NaoHabilitado, string.Empty);

                    if (viagem.ViagemEventos != null && declararCiotResult.Resultado != EResultadoDeclaracaoCiot.Erro)
                    {
                        foreach (var viagemEventoModel in @params.ViagemEventos)
                        {
                            if (viagemEventoModel.Status != EStatusViagemEvento.Baixado &&
                                viagemEventoModel.Status != EStatusViagemEvento.Cancelado)
                                continue;

                            ViagemEvento evento;

                            if(viagemEventoModel.IdViagemEvento.HasValue)
                                evento = viagem.ViagemEventos.FirstOrDefault(ve => ve.IdViagemEvento == viagemEventoModel.IdViagemEvento);

                            else if (!string.IsNullOrWhiteSpace(viagemEventoModel.NumeroControle))
                                evento = viagem.ViagemEventos.FirstOrDefault(ve => ve.NumeroControle == viagemEventoModel.NumeroControle);

                            else if (!string.IsNullOrWhiteSpace(viagemEventoModel.Token))
                                evento = viagem.ViagemEventos.FirstOrDefault(ve => ve.Token == viagemEventoModel.Token);

                            else
                            {
                                // Se haver somente um evento deste tipo, pega o first.
                                if (viagem.ViagemEventos.Count(ve => ve.TipoEventoViagem == viagemEventoModel.TipoEvento) ==
                                    1)
                                    evento = viagem.ViagemEventos.First(
                                        ve => ve.TipoEventoViagem == viagemEventoModel.TipoEvento);
                                else
                                    evento = viagem.ViagemEventos.FirstOrDefault(
                                        ve => ve.TipoEventoViagem == viagemEventoModel.TipoEvento &&
                                              ve.ValorPagamento == viagemEventoModel.ValorPagamento);
                            }

                            // Verifica se originalmente o evento deveria integrar no meio homologado
                            if (evento == null || !evento.HabilitarPagamentoCartao)
                                continue;

                            OperacaoCartaoResponseDTO cargaResultado;

                            switch (viagemEventoModel.Status)
                            {
                                case EStatusViagemEvento.Baixado:

                                    cargaResultado = cartoesApp.RealizarCargaFrete(viagem, evento, cartaoVinculadoMotorista,
                                        cartaoVinculadoProprietario);

                                    switch (cargaResultado.Status)
                                    {
                                        case EStatusPagamentoCartao.Baixado:

                                            if (retornoOperacaoCartao.Status == ERetornoOperacaoCartao.NaoHabilitado)
                                            {
                                                retornoOperacaoCartao =
                                                    new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Sucesso,
                                                        cargaResultado.Mensagem);
                                            }

                                            if (viagemEventoModel.Status == EStatusViagemEvento.Baixado && evento.Status != EStatusViagemEvento.Baixado)
                                            {
                                                evento.Status = EStatusViagemEvento.Baixado;
                                                evento.DataHoraPagamento = DateTime.Now;
                                            }

                                            break;

                                        case EStatusPagamentoCartao.Pendente:

                                            if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro &&
                                                retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Sucesso)
                                            {
                                                retornoOperacaoCartao =
                                                    new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Pendente,
                                                        cargaResultado.Mensagem);
                                            }

                                            if (viagemEventoModel.Status == EStatusViagemEvento.Baixado && evento.Status != EStatusViagemEvento.Baixado)
                                            {
                                                evento.Status = EStatusViagemEvento.Baixado;
                                                evento.DataHoraPagamento = DateTime.Now;
                                            }

                                            break;

                                        case EStatusPagamentoCartao.Erro:

                                            if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro)
                                            {
                                                retornoOperacaoCartao =
                                                    new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Erro,
                                                        cargaResultado.Mensagem);
                                            }

                                            evento.Status = EStatusViagemEvento.Aberto;
                                            evento.DataHoraPagamento = null;

                                            if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                                                mensagensProcessos.MensagemCartao =
                                                    $"Falha ao carregar cartão ({evento.TipoEventoViagem}): " +
                                                    cargaResultado.Mensagem;

                                            break;
                                    }

                                    break;

                                case EStatusViagemEvento.Cancelado:

                                    var viagemEvento = Mapper.Map<ViagemEvento>(evento);

                                    cargaResultado = cartoesApp.RealizarEstornoCargaFrete(viagemEvento, viagem.IdEmpresa,
                                        viagem.CPFMotorista, viagem.CPFCNPJProprietario, EOrigemTransacaoCartao.Automatico);

                                    switch (cargaResultado.Status)
                                    {
                                        case EStatusPagamentoCartao.Baixado:

                                            if (retornoOperacaoCartao.Status == ERetornoOperacaoCartao.NaoHabilitado)
                                            {
                                                retornoOperacaoCartao =
                                                    new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Sucesso,
                                                        cargaResultado.Mensagem);
                                            }
                                            
                                            if (viagemEventoModel.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Cancelado)
                                            {
                                                evento.Status = EStatusViagemEvento.Cancelado;
                                                evento.DataHoraCancelamento = DateTime.Now;
                                            }

                                            break;

                                        case EStatusPagamentoCartao.Pendente:

                                            if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro &&
                                                retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Sucesso)
                                            {
                                                retornoOperacaoCartao =
                                                    new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Pendente,
                                                        cargaResultado.Mensagem);
                                            }
                                            
                                            if (viagemEventoModel.Status == EStatusViagemEvento.Cancelado && evento.Status != EStatusViagemEvento.Cancelado)
                                            {
                                                evento.Status = EStatusViagemEvento.Cancelado;
                                                evento.DataHoraCancelamento = DateTime.Now;
                                            }

                                            break;

                                        case EStatusPagamentoCartao.Erro:

                                            if (retornoOperacaoCartao.Status != ERetornoOperacaoCartao.Erro)
                                            {
                                                retornoOperacaoCartao =
                                                    new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.Erro,
                                                        cargaResultado.Mensagem);
                                            }

                                            if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                                                mensagensProcessos.MensagemCartao =
                                                    $"Falha ao estornar cartão ({evento.TipoEventoViagem}): " +
                                                    cargaResultado.Mensagem;

                                            break;
                                    }

                                    break;

                                default:
                                    throw new ArgumentException(nameof(evento.Status));
                            }

                            validationResult = viagemEventoApp.Update(evento);
                            if (!validationResult.IsValid && string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                                mensagensProcessos.MensagemCartao = $"Falha ao processar cartão ({evento.TipoEventoViagem})*: " +
                                                                    validationResult.ToFormatedMessage();
                        }

                        validationResult = _viagemApp.Update(viagem);
                        if (!validationResult.IsValid && string.IsNullOrWhiteSpace(mensagensProcessos.MensagemCartao))
                            mensagensProcessos.MensagemCartao = $"Falha ao processar cartão**: " + validationResult.ToFormatedMessage();
                    }

                    #endregion
                }
                
                #region Atualizar CIOT
                // Atualiza os valores pagos na impressão do CIOT, pois o CIOT é feito antes do cartão, e sem isso o pagamento fica desatualizado
                if (atualizarCiot && viagem.IdDeclaracaoCiot.HasValue)
                    _ciotV3App.AtualizarCiot(viagem.IdDeclaracaoCiot.Value);
                #endregion
            }
            catch (Exception e)
            {
                var message = $"Erro ao processar integração de viagem: {viagem.IdViagem}. {e.Message}";

                LogManager.GetCurrentClassLogger().Error(e, message);

                if (string.IsNullOrWhiteSpace(mensagensProcessos.MensagemViagem))
                    mensagensProcessos.MensagemViagem = message;
            }

            return mensagensProcessos;
        }
        
        private string PreencheValorPedagioParaDeclaracaoCiot(ViagemIntegrarRequestModel @params, Viagem viagem, ICartoesApp cartoesApp)
        {
            var empresa = _empresaApp.Get(@params.CNPJEmpresa);
            if (@params.Pedagio?.Fornecedor == null || @params.Pedagio.Fornecedor == FornecedorEnum.Desabilitado
            || !viagem.ResultadoCompraPedagio.In(EResultadoCompraPedagio.NaoRealizado, EResultadoCompraPedagio.Erro))
                return null;

            var registraValePedagio = _parametrosApp.GetRegistrarValePedagio(viagem.IdEmpresa);
            if (!registraValePedagio && (@params.ValorPedagio > 0 || @params.Pedagio?.ValorPedagio > 0))
            {
                viagem.ValorPedagio = (@params.ValorPedagio ?? @params.Pedagio?.ValorPedagio).Value;
                return null;
            }

            if (@params.Pedagio.IdentificadorHistorico.HasValue && @params.Pedagio.IdentificadorHistorico != default(Guid))
            {
                var historicoRota = cartoesApp.ConsultaHistoricoRota(
                    new ConsultaHistoricoRotaRequest
                    {
                        ConsultaCustoHistoricoId = @params.Pedagio.IdentificadorHistorico,
                        RecalcularValorParaEixos = @params.Pedagio.QtdEixos > 0 ? @params.Pedagio.QtdEixos : (int?) null,
                        IncluirFragmentos = false
                    });

                if (historicoRota.Status == ConsultaHistoricoRotaResponseStatus.Falha)
                    return historicoRota.Mensagem;

                if (empresa.PedagioTag && @params.Pedagio.Istag())
                {
                    @params.Pedagio.ValorPedagio = historicoRota.CustoTotalTag;
                    viagem.ValorPedagio = historicoRota.CustoTotalTag.GetValueOrDefault();      
                }
                else
                {
                    @params.Pedagio.ValorPedagio = historicoRota.CustoTotal;
                    viagem.ValorPedagio = historicoRota.CustoTotal.GetValueOrDefault();   
                }

                if(@params.Pedagio.QtdEixos == 0 && historicoRota.QtdEixos.HasValue)
                    @params.Pedagio.QtdEixos = historicoRota.QtdEixos.Value;
            }
            else if (@params.Pedagio.Localizacoes != null && @params.Pedagio.Localizacoes.Count >= 2)
            {
                var tipoVeiculo = ConsultaRotaRequestTipoVeiculo.Caminhao;

                switch (@params.Pedagio.TipoVeiculo)
                {
                    case ETipoVeiculoPedagioEnum.Carro:
                        tipoVeiculo = ConsultaRotaRequestTipoVeiculo.Carro;
                        break;
                    case ETipoVeiculoPedagioEnum.Motocicleta:
                        tipoVeiculo = ConsultaRotaRequestTipoVeiculo.Motocicleta;
                        break;
                    case ETipoVeiculoPedagioEnum.Onibus:
                        tipoVeiculo = ConsultaRotaRequestTipoVeiculo.Onibus;
                        break;
                }

                var cidadeApp = _cidadeApp;
                var locationList = new List<LocationDTO>();
                foreach (var localizacao in @params.Pedagio.Localizacoes)
                {
                    var cidade = cidadeApp.GetCidadeByIBGE(localizacao.IbgeCidade);
                    if (cidade == null && (!localizacao.Latitude.HasValue || !localizacao.Longitude.HasValue))
                        return $"Cidade com IBGE {localizacao.IbgeCidade} não localizada. Informe uma cidade com IBGE válido ou a latitide / longitude.";

                    var locationDto = new LocationDTO
                    {
                        Latitude = localizacao.Latitude ?? cidade.Latitude,
                        Longitude = localizacao.Longitude ?? cidade.Longitude,
                        Ibge = localizacao.IbgeCidade
                    };
                    locationList.Add(locationDto);
                }
                
                var calcularRotaRequest = new ConsultaRotaRequest()
                {
                    Localizacoes = locationList,
                    TipoVeiculo = tipoVeiculo,
                    QtdEixos = @params.Pedagio.QtdEixos,
                    ExibirDetalhes = false,
                    DesabilitaCacheRotas = empresa.DesabilitaCacheRotas,
                    CodPolyline = @params.Pedagio.CodPolyline
                };
                
                var pedagioApp = PedagioApp.CreateByEmpresa(_pedagioAppFactoryDependencies, @params.CNPJEmpresa, @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit);
        
                var rota = pedagioApp.CalcularRota(calcularRotaRequest, @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit);

                if (!rota.Sucesso)
                    return rota.Mensagem;
                
                if (empresa.PedagioTag && @params.Pedagio.Istag())
                {
                    @params.Pedagio.IdentificadorHistorico = rota.Objeto.IdentificadorHistorico;
                    @params.Pedagio.ValorPedagio = rota.Objeto.CustoTotalTag;
                    viagem.ValorPedagio = rota.Objeto.CustoTotalTag.GetValueOrDefault();
                }
                else
                {
                    @params.Pedagio.IdentificadorHistorico = rota.Objeto.IdentificadorHistorico;
                    @params.Pedagio.ValorPedagio = rota.Objeto.CustoTotal;
                    viagem.ValorPedagio = rota.Objeto.CustoTotal.GetValueOrDefault();
                }
            }

            return null;
        }

        public ProcessoIntegracaoViagemDto CancelarViagem(Viagem viagem,
            CartoesApp cartoesApp, 
            ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado comportamentoEstornoPedagioMoedeiro,
            out DeclararCiotResult declararCiotResult, out SolicitarCompraPedagioResponseDTO compraPedagioResponse,
            string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            try
            {
                    //  Regras do cancelamento da viagem: Se estiver cancelando
                    // 1 - Se pagou cartão frete, validar se tem saldo, e abortar caso nao houver
                    // 2 - Se gerou ciot, validar se ainda pode ser cancelado o CIOT e abortar caso não poder
                    // 0 - Se pagou abastecimento, tenta cancelar
                    // 3 - Efetivar o cancelamento do CIOT
                    // 4 - Efetivar estorno do crédito do cartão frete. Estornar o que o sistema fez automaticamente, transferenecia ao motorista, e estorno do proprietário.
                    // 5 - Validar se há pedágio moedeiro com carga de valor confirmada, existindo é necessário estornar o valor fisicamente.
                    //        - Avaliar parâmetro da empresa para o comportamento desde cenário
                    //    5.1 - Gerar registro pendente de cancelamento no serviço de pedágio e retornar sucesso, executando o estorno do ciot/cartão frete.
                    //    5.2 - Gerar registro pendente de cancelamento no serviço de pedágio e retornar falha, executando o estorno do ciot/cartão frete.
                    //    5.3 - Gerar registro pendente de cancelamento no serviço de pedágio e retornar falha, não executando estorno do ciot/cartão frete.
                    //    5.4 - Bloquear cancelamento e não realizar nenhum ação, não gerando registro de pendência de cancelamento no serviço de pedágio e nem estornar ciot/cartão frete.

                    // var cartoesApp = CartoesApp.CreateByEmpresa(cnpjAplicacao, usuarioDocAudit, nomeUsuarioAudit);
                    var transacaoService = _transacaoCartaoApp;
                    declararCiotResult = new DeclararCiotResult {Resultado = EResultadoDeclaracaoCiot.Erro}; // Valor será sobreescrito mais abaixo
                    
                    var fornecedorPedagio = _viagemApp.GetFornecedorViagemRota(viagem.IdViagem);
                    compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
                    {
                        Status = EResultadoCompraPedagio.NaoRealizado,
                        Fornecedor = fornecedorPedagio ?? FornecedorEnum.Desabilitado
                    };

                    // Configuar ações habilitadas para o cancelamento
                    var executarCancelamentoCiot = viagem.HabilitarDeclaracaoCiot;
                    var executarEstornoCartao = true;
                    var executarEstornoPedagio = true;
                    var possuiPedagioMoedeiroConfirmado = false;
                    var executarEstornoAbastecimento = true;

                    if (fornecedorPedagio == FornecedorEnum.Moedeiro)
                    {
                        // Se o status for compra confirmada, ou pendente de cancelamento, siginifica que a viagem ainda está com o pedágio confirmado.
                        possuiPedagioMoedeiroConfirmado = viagem.ResultadoCompraPedagio.In(
                            EResultadoCompraPedagio.CompraConfirmada,
                            EResultadoCompraPedagio.CancelamentoSolicitado);

                        executarEstornoPedagio = possuiPedagioMoedeiroConfirmado || viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraSolicitada;

                        switch (comportamentoEstornoPedagioMoedeiro)
                        {
                            case ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_NaoEstornandoFreteCiot:
                                executarCancelamentoCiot = false;
                                executarEstornoCartao = false;
                                executarEstornoAbastecimento = false;
                                break;
                            case ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal:
                                if (possuiPedagioMoedeiroConfirmado)
                                    return new ProcessoIntegracaoViagemDto
                                    {
                                        MensagemPedagio = "Viagem possui pedágio carregado em cartão, favor descarregar para seguir o cancelamento.",
                                        StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.CartaoPedagioPendenteDescarregamento
                                    };
                                break;
                        }
                    }

                    // Processar operações
                    if (executarEstornoCartao)
                    {
                        #region 1 - Validar se há saldo no cartao frete para estonar o processo. O sistema deve estornar o que foi realizado automaticamente.

                        // Primeiro apenas valida a existência saldo em conta para o procedimento.
                        var transacoes = transacaoService.GetAllByIdViagem(viagem.IdViagem);

                        var valorTransferencia = transacoes.Where(c =>
                                c.ViagemEvento.Status == EStatusViagemEvento.Baixado &&
                                c.StatusPagamento == EStatusPagamentoCartao.Baixado &&
                                c.TipoProcessamentoCartao.In(ETipoProcessamentoCartaoUtils.TransferenciaProprietarioParaMotoristaViagem))
                            .Sum(c => c.ValorMovimentado);

                        if (valorTransferencia > 0)
                        {
                            var retornoSaldo =
                                new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App)
                                    .ValidarSaldoCartao(StringExtension.OnlyNumbers(viagem.CPFMotorista),
                                        viagem.IdEmpresa, valorTransferencia, cartoesApp);

                            if (!retornoSaldo.Key)
                                return new ProcessoIntegracaoViagemDto{
                                    MensagemCartao = retornoSaldo.Value,
                                    StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.SaldoInsuficienteCartaoFrete
                                };
                        }

                        var valoresCarga = transacoes.Where(c =>
                                c.ViagemEvento.Status == EStatusViagemEvento.Baixado &&
                                c.StatusPagamento == EStatusPagamentoCartao.Baixado &&
                                c.TipoProcessamentoCartao.In(ETipoProcessamentoCartaoUtils.CreditoProprietarioViagem))
                            .Sum(c => c.ValorMovimentado);

                        if (valoresCarga > 0)
                        {
                            var retornoSaldo = new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App).ValidarSaldoCartao(StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario),
                                viagem.IdEmpresa, valoresCarga - valorTransferencia, cartoesApp);

                            if (!retornoSaldo.Key)
                                return new ProcessoIntegracaoViagemDto{
                                    MensagemCartao = retornoSaldo.Value,
                                    StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.SaldoInsuficienteCartaoFrete
                                };
                        }

                        #endregion
                    }

                    if (executarEstornoAbastecimento)
                    {
                        var viagemEventos = _viagemApp.GetEventos(viagem.IdViagem);
                        var bloqueiaCancelamento = _empresaService.BloqueiaCancelamentoAbastecimento(viagem.IdEmpresa);

                        foreach (var viagemEvento in viagemEventos)
                        {
                            if (!viagemEvento.IdAbastecimentoticket.HasValue)
                                continue;

                            var retornoCancelamento = CancelarAbastecimentoTicketLog(viagemEvento.IdAbastecimentoticket ?? 0, viagem.IdEmpresa, nomeUsuarioAudit);

                            if (!retornoCancelamento.Sucesso)
                            {
                                return new ProcessoIntegracaoViagemDto{
                                    MensagemCartao = "Falha ao cancelar Abastecimento: " + retornoCancelamento.Mensagem,
                                    StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaIntegrarAbastecimento
                                };
                            }

                            viagemEvento.Status = EStatusViagemEvento.Cancelado;
                            viagemEvento.DataHoraCancelamento = DateTime.Now;

                            var eventoUpdateResult = _viagemEventoApp.Update(viagemEvento);
                            if (!eventoUpdateResult.IsValid)
                                return new ProcessoIntegracaoViagemDto{
                                    MensagemCartao = "Falha ao salvar estorno de carga de frete: " + eventoUpdateResult.Errors?.FirstOrDefault()?.Message,
                                    StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaEstornoCartaoFrete
                                };
                        }
                    }

                    if (executarCancelamentoCiot)
                    {
                        #region 2 e 3 - Validando e cancelando CIOT caso necessário

                        var ciotResult = _ciotV2App.CancelarCiot(viagem);
                        if (!ciotResult.Sucesso)
                            return new ProcessoIntegracaoViagemDto{
                                MensagemCartao = "Falha ao cancelar CIOT: " + ciotResult.Mensagem,
                                StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaIntegracaoAntt
                            };

                        #endregion
                    }

                    if (executarEstornoCartao)
                    {
                        #region 4 - Efetivar estorno do crédito do cartão frete. Estornar o que o sistema fez automaticamente, transferenecia ao motorista, e estorno do proprietário.

                        var viagensEventos = _viagemApp.GetEventos(viagem.IdViagem)
                            .Where(c => c.HabilitarPagamentoPix != true).ToList();

                        foreach (var viagemEvento in viagensEventos)
                        {
                            var transacao = transacaoService.GetAllByIdEvento(viagemEvento.IdViagemEvento)
                                .Where(c=>c.StatusPagamento == EStatusPagamentoCartao.Baixado || c.StatusPagamento == EStatusPagamentoCartao.Pendente)
                                .Select(c => new
                                {
                                    c.IdTransacaoCartao,
                                    c.NumeroProtocoloWs,
                                    c.StatusPagamento
                                }).ToList();

                            var save = false;
                            if (viagemEvento.Status == EStatusViagemEvento.Baixado || transacao.Any())
                            {
                                var cargaResultado = cartoesApp.RealizarEstornoCargaFrete(viagemEvento,
                                    viagem.IdEmpresa, viagem.CPFMotorista, viagem.CPFCNPJProprietario,
                                    EOrigemTransacaoCartao.Automatico);

                                if (cargaResultado.Status == EStatusPagamentoCartao.Erro)
                                    return new ProcessoIntegracaoViagemDto{
                                        MensagemCartao = "Falha ao estornar carga de frete: " + cargaResultado.Mensagem,
                                        StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaEstornoCartaoFrete
                                    };

                                viagemEvento.Status = EStatusViagemEvento.Cancelado;
                                viagemEvento.DataHoraCancelamento = DateTime.Now;
                                save = true;
                            }
                            else if (viagemEvento.Status != EStatusViagemEvento.Cancelado)
                            {
                                viagemEvento.Status = EStatusViagemEvento.Bloqueado;
                                viagemEvento.DataAgendamentoPagamento = null;
                                save = true;
                            }

                            if (save)
                            {
                                var eventoUpdateResult = _viagemEventoApp.Update(viagemEvento);
                                if (!eventoUpdateResult.IsValid)
                                    return new ProcessoIntegracaoViagemDto{
                                        MensagemCartao = "Falha ao salvar estorno de carga de frete: " + eventoUpdateResult.Errors?.FirstOrDefault()?.Message,
                                        StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaEstornoCartaoFrete
                                    };
                            }

                            if (transacao.Any(c => c.StatusPagamento == EStatusPagamentoCartao.Pendente))
                            {
                                // Aqui atualiza as transações que estavam pendentes e foram canceladas no MH
                                // Elas devem ser marcadas como processadas
                                foreach (var tra in transacao)
                                {
                                    if (tra.StatusPagamento == EStatusPagamentoCartao.Pendente)
                                        transacaoService.AtualizarStatusTransacaoPendenteReprocessada(tra.IdTransacaoCartao, tra.NumeroProtocoloWs, DateTime.Now, "Transação alterada de pendente para não processada.");
                                }
                            }
                        }

                        #endregion
                    }

                    if (executarEstornoPedagio)
                    {
                        #region 5 - Cancelando compra de pedágio caso necessário (Gerar registro pendente de cancelamento no serviço de pedágio)

                        if (viagem.NumeroProtocoloPedagio.HasValue &&
                            viagem.ResultadoCompraPedagio.In(
                                EResultadoCompraPedagio.CompraSolicitada, EResultadoCompraPedagio.CompraConfirmada))
                        {
                            
                            var cancelarCompraPedagioRequest = new CancelarCompraPedagioRequest
                            {
                                Id = (int?) viagem.NumeroProtocoloPedagio
                            };
                            
                            var cancelarCompraPedagioResponse = cartoesApp.CancelarCompraPedagio(cancelarCompraPedagioRequest);

                            if (cancelarCompraPedagioResponse.Status == CancelarCompraPedagioResponseStatus.Falha)
                            {
                                return new ProcessoIntegracaoViagemDto
                                {
                                    MensagemCartao = "Falha ao cancelar compra de pedágio: " + cancelarCompraPedagioResponse.Mensagem,
                                    StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaEstornoPedagio
                                };
                            }
                            
                            viagem.MensagemCompraPedagio = cancelarCompraPedagioResponse.Mensagem;
                            viagem.ResultadoCompraPedagio =
                                fornecedorPedagio == FornecedorEnum.Moedeiro && viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada
                                    ? EResultadoCompraPedagio.CancelamentoSolicitado
                                    : EResultadoCompraPedagio.CancelamentoConfirmado;
                            
                            _viagemApp.Update(viagem);
                            
                            if (fornecedorPedagio == FornecedorEnum.ExtrattaTag 
                                || fornecedorPedagio == FornecedorEnum.ConectCar 
                                || fornecedorPedagio == FornecedorEnum.MoveMais 
                                || fornecedorPedagio == FornecedorEnum.ViaFacil
                                || fornecedorPedagio == FornecedorEnum.Veloe
                                || fornecedorPedagio == FornecedorEnum.TaggyEdenred)
                            {
                                // Estorno na API da TAG
                                _publisher.Publish(new EventoSaldoEstornoValePedagioTagEvent
                                {
                                    IdEmpresa = viagem.IdEmpresa,
                                    Perfil = EPerfil.Empresa,
                                    ViagemId = viagem.IdViagem
                                });
                                
                                //Caso erro durante este processo na tela de pagamentos TAG ficará pendente
                                _cargaAvulsaApp.EstornarProvisionamentoValePedagio(viagem.IdViagem,viagem.IdEmpresa);
                            }
                            

                            if (viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada)
                                CartoesApp.EnviarPushCompraPedagioMoedeiro(
                                    _empresaService,
                                    viagem.IdEmpresa,
                                    viagem.CPFMotorista,
                                    viagem.ValorPedagio,
                                    SolicitarCompraPedagioRequestFornecedor.Moedeiro,
                                    false,
                                    true);
                            
                        }

                        #endregion
                    }

                    if (possuiPedagioMoedeiroConfirmado)
                    {
                        #region 6 - Se haver pedágio moedeiro confirmado - Retornar falha

                        if (comportamentoEstornoPedagioMoedeiro !=
                            ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Sucesso_EstornandoFreteCiot)
                            return new ProcessoIntegracaoViagemDto()
                            {
                                MensagemPedagio =
                                    "Viagem possui pedágio carregado em cartão, favor descarregar para seguir o cancelamento.",
                                StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.CartaoPedagioPendenteDescarregamento
                            };

                        #endregion
                    }
                    
                    #region Cancelamento de eventos Pix
                    
                    var viagensEventosPix = _viagemApp.GetEventos(viagem.IdViagem)
                        .Where(c => c.HabilitarPagamentoPix == true && c.Status == EStatusViagemEvento.Aberto).ToList();

                    if (viagensEventosPix.Count != 0)
                    {
                        foreach (var evento in viagensEventosPix)
                        {
                            evento.Status = EStatusViagemEvento.Cancelado;
                        }
                    
                        _viagemEventoRepository.SaveChanges();
                    }

                    #endregion

                    var statusCancelamento =
                        fornecedorPedagio == FornecedorEnum.Moedeiro && viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada
                            ? EResultadoCompraPedagio.CancelamentoSolicitado
                            : EResultadoCompraPedagio.CancelamentoConfirmado;
                        
                    compraPedagioResponse.Status = statusCancelamento;
                    viagem.ResultadoCompraPedagio = statusCancelamento;
                            
                    _viagemApp.Update(viagem);
                   
                    return new ProcessoIntegracaoViagemDto
                    {
                        Sucesso = true
                    };
            }
            catch (Exception e)
            {
                var message = "Não foi possível realizar o cancelamento da viagem.";
                
                LogManager.GetCurrentClassLogger().Error(e, message);
                throw;
            }
        }
        
        
        public ProcessoIntegracaoViagemDto CancelarVpo(Viagem viagem, ICartoesApp cartoesApp, 
        ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado comportamentoEstornoPedagioMoedeiro,
        out SolicitarCompraPedagioResponseDTO compraPedagioResponse)
        {
            try
            {
                var fornecedor = _viagemApp.GetFornecedorViagemRota(viagem.IdViagem);
                compraPedagioResponse = new SolicitarCompraPedagioResponseDTO
                {
                    Status = EResultadoCompraPedagio.NaoRealizado,
                    Fornecedor = fornecedor ?? FornecedorEnum.Desabilitado
                };

                var executarEstornoPedagio = true;
                var possuiPedagioMoedeiroConfirmado = false;
                
                if (fornecedor == FornecedorEnum.Moedeiro)
                {
                    possuiPedagioMoedeiroConfirmado = viagem.ResultadoCompraPedagio.In(EResultadoCompraPedagio.CompraConfirmada,
                        EResultadoCompraPedagio.CancelamentoSolicitado);

                    executarEstornoPedagio = possuiPedagioMoedeiroConfirmado || viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraSolicitada;

                    if(comportamentoEstornoPedagioMoedeiro == ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal)
                    {
                        if (possuiPedagioMoedeiroConfirmado)
                            return new ProcessoIntegracaoViagemDto
                            {
                                MensagemPedagio = "Viagem possui pedágio carregado em cartão, favor descarregar para seguir o cancelamento.",
                                StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.CartaoPedagioPendenteDescarregamento
                            };
                    }
                }

                if (executarEstornoPedagio)
                {
                    #region 5 - Cancelando compra de pedágio caso necessário (Gerar registro pendente de cancelamento no serviço de pedágio)

                    if (viagem.NumeroProtocoloPedagio.HasValue &&
                        viagem.ResultadoCompraPedagio.In(
                            EResultadoCompraPedagio.CompraSolicitada, EResultadoCompraPedagio.CompraConfirmada))
                    {
                        var cancelarCompraPedagioRequest = new CancelarCompraPedagioRequest
                        {
                            Id = (int?) viagem.NumeroProtocoloPedagio
                        };
                        
                        var cancelarCompraPedagioResponse = cartoesApp.CancelarCompraPedagio(cancelarCompraPedagioRequest);

                        if (cancelarCompraPedagioResponse.Status == CancelarCompraPedagioResponseStatus.Falha)
                        {
                            return new ProcessoIntegracaoViagemDto
                            {
                                MensagemCartao = "Falha ao cancelar compra de pedágio: " + cancelarCompraPedagioResponse.Mensagem,
                                StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.FalhaEstornoPedagio
                            };
                        }
                        
                        viagem.MensagemCompraPedagio = cancelarCompraPedagioResponse.Mensagem;
                        viagem.ResultadoCompraPedagio = fornecedor == FornecedorEnum.Moedeiro 
                                                        && viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada
                                ? EResultadoCompraPedagio.CancelamentoSolicitado
                                : EResultadoCompraPedagio.CancelamentoConfirmado;
                        
                        _viagemApp.Update(viagem);
                        
                        if (fornecedor == FornecedorEnum.ExtrattaTag 
                            || fornecedor == FornecedorEnum.ConectCar 
                            || fornecedor == FornecedorEnum.MoveMais 
                            || fornecedor == FornecedorEnum.ViaFacil 
                            || fornecedor == FornecedorEnum.Veloe
                            || fornecedor == FornecedorEnum.TaggyEdenred)
                        {
                            // Estorno na API da TAG
                            _publisher.Publish(new EventoSaldoEstornoValePedagioTagEvent
                            {
                                IdEmpresa = viagem.IdEmpresa,
                                Perfil = EPerfil.Empresa,
                                ViagemId = viagem.IdViagem
                            });
                            
                            //Caso erro durante este processo na tela de pagamentos TAG ficará pendente
                            _cargaAvulsaApp.EstornarProvisionamentoValePedagio(viagem.IdViagem,viagem.IdEmpresa);
                        }

                        if (viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada)
                            CartoesApp.EnviarPushCompraPedagioMoedeiro(_empresaService, viagem.IdEmpresa, viagem.CPFMotorista, viagem.ValorPedagio,
                                SolicitarCompraPedagioRequestFornecedor.Moedeiro, false, true);
                    }

                    #endregion
                }
                
                if (possuiPedagioMoedeiroConfirmado)
                {
                    #region 6 - Se haver pedágio moedeiro confirmado - Retornar falha

                    if (comportamentoEstornoPedagioMoedeiro != ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Sucesso_EstornandoFreteCiot)
                        return new ProcessoIntegracaoViagemDto()
                        {
                            MensagemPedagio = "Viagem possui pedágio carregado em cartão, favor descarregar para seguir o cancelamento.",
                            StatusResponseTipoFalha = EViagemAlterarStatusResponseTipoFalha.CartaoPedagioPendenteDescarregamento
                        };
                    
                    #endregion
                }
                
                var statusCancelamento = fornecedor == FornecedorEnum.Moedeiro 
                                         && viagem.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada
                        ? EResultadoCompraPedagio.CancelamentoSolicitado
                        : EResultadoCompraPedagio.CancelamentoConfirmado;
                    
                compraPedagioResponse.Status = statusCancelamento;
                viagem.ResultadoCompraPedagio = statusCancelamento;
                        
                _viagemApp.Update(viagem);
               
                return new ProcessoIntegracaoViagemDto
                {
                    Sucesso = true
                };
            }
            catch (Exception e)
            {
                var message = "Não foi possível realizar o cancelamento da viagem.";
                LogManager.GetCurrentClassLogger().Error(e, message);
                throw;
            }
        }
    }
}
﻿using System;
using System.Collections.Generic;
using TagExtrattaClient;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class ExtratoConsolidadoModelRequest
    {
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public bool Relatorio { get; set; }
        public List<string> DocumentosPortadores { get; set; }
        public string OrderCampo { get; set; }
        public EOperadorOrder? OrderOperador { get; set; }
        public int Take { get; set; }
        public int Page { get; set; }
        public string DescricaoPlano { get; set; }
        public string DebitoCredito { get; set; }
        public string Estabelecimento { get; set; }
        public int? Conta { get; set; }
        public int? Identificador { get; set; }
    }
}
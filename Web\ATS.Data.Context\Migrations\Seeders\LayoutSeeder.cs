﻿using ATS.Domain.Entities;
using System;
using System.Data.Entity.Migrations;
using System.Linq;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class LayoutSeeder
    {
        public void Execute(AtsContext context)
        {
            if (context.Layout.Any())
                return;

            context.Layout.AddOrUpdate(new[]
            {
                new Layout {
                    Href = "http://sotran.sistemainfo.com.br:91/",
                    CSS = @".login .login-header { display: block; position: absolute; top: 0; left: 0; right: 0; width: 403px; padding: 0; margin: -20px auto; font-weight: 300; } .logoSotranAtsNovoHotFix { width: 325px; margin-top: 80px } #logoCustomDomainFormHeader { max-height: 60px; height: 60px; margin-top: 15px } .login-cover-bg { background: radial-gradient(ellipse at center, #666666 0%, rgba(145, 145, 145, 0.72) 100%) !important }",
                    Image = "sotranLogo.png",
                    Background = "bgSotran.jpg",
                    IdEmpresa = 2073
                },
            });

            context.SaveChanges();
        }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using Dapper;
using NLog;

namespace ATS.Data.Repository.Dapper
{
    public class CargaAvulsaDapper : DapperFactory<CargaAvulsa>, ICargaAvulsaDapper
    {
        public List<int> GetIdsCargaAvulsaParaProcessar()
        {
            try
            {
                var query = $@"SELECT CA.idcargaavulsa FROM CARGA_AVULSA CA WHERE CA.statuscargaavulsa IN (4, 5)";

                using (var connection = new DapperContext().GetConnection)
                {
                    var ids = connection.Query<int>(query).ToList();
                    return ids;
                }
            }
            catch (Exception)
            {
                return new List<int>();
            }
        }

        public List<CargaAvulsa> GetCargaAvulsasParaProcessar(int numeroRegistros)
        {
            try
            {
                var query = $@"SELECT TOP(@NumeroRegistros)
                                      CA.idcargaavulsa,
                                      CA.idusuariocadastro,
                                      CA.cpfmototista,
                                      CA.cpfusuario,
                                      CA.valor,
                                      CA.statuscargaavulsa,
                                      CA.idempresa
                               FROM CARGA_AVULSA CA
                               WHERE CA.statuscargaavulsa IN (4, 9)";

                using (var connection = new DapperContext().GetConnection)
                {
                    var parameters = new {NumeroRegistros = numeroRegistros};
                    var cargasAvulsas = connection.Query<CargaAvulsa>(query, parameters).ToList();
                    return cargasAvulsas;
                }
            }
            catch (Exception)
            {
                return new List<CargaAvulsa>();
            }
        }

        public ValidationResult SetarStatusProcessamentoEmAndamento()
        {
            try
            {
                var query =
                    $@"UPDATE CARGA_AVULSA SET statuscargaavulsa = 4 WHERE statuscargaavulsa IN (1) and tipocarga = 2";

                using (var connection = new DapperContext().GetConnection)
                {
                    connection.Query<CargaAvulsa>(query);
                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public List<CargaAvulsaProcessadaModel> GetCargasAvulsasProcessadas()
        {
            try
            {
                var query = $@"SELECT CA.idcargaavulsa           AS IdCargaAvulsa,
                                      TC.statuspagamento         AS StatusPagamento,
                                      TC.mensagemprocessamentows AS MensagemWebService
                               FROM CARGA_AVULSA CA
                                      LEFT JOIN TRANSACAO_CARTAO TC on CA.idcargaavulsa = TC.idcargaavulsa
                               WHERE CA.statuscargaavulsa = 4
                                 AND TC.idtransacaocartao IS NOT NULL";

                using (var connection = new DapperContext().GetConnection)
                {
                    var cargasAvulsas = connection.Query<CargaAvulsaProcessadaModel>(query).ToList();
                    return cargasAvulsas;
                }
            }
            catch (Exception)
            {
                return new List<CargaAvulsaProcessadaModel>();
            }
        }

        public List<CargaAvulsaProcessadaModel> GetCargasAvulsasProcessadasPorId(List<int> cargaAvulsaIds)
        {
            try
            {
                var query = $@"SELECT CA.idcargaavulsa           AS IdCargaAvulsa,
                                      CA.idempresa               AS IdEmpresa,
                                      CA.cpfmototista            AS CPFMototista,
                                      CA.valor                   AS Valor,
                                      CA.valor                   AS Valor,
                                      TC.statuspagamento         AS StatusPagamento,
                                      CA.statuscargaavulsa       AS Statuscargaavulsa,
                                      TC.mensagemprocessamentows AS MensagemWebService,
                                      E.razaosocial              AS RazaoSocialEmpresa
                               FROM CARGA_AVULSA CA
                                      LEFT JOIN TRANSACAO_CARTAO TC on CA.idcargaavulsa = TC.idcargaavulsa
                                      INNER JOIN EMPRESA E on CA.idempresa = E.idempresa
                               WHERE CA.statuscargaavulsa in (4, 9)
                                 AND CA.idcargaavulsa in @Ids
                                 AND TC.idtransacaocartao IS NOT NULL";

                using (var connection = new DapperContext().GetConnection)
                {
                    var parameters = new {Ids = cargaAvulsaIds};
                    var cargasAvulsas = connection.Query<CargaAvulsaProcessadaModel>(query, parameters).ToList();
                    return cargasAvulsas;
                }
            }
            catch (Exception)
            {
                return new List<CargaAvulsaProcessadaModel>();
            }
        }

        public CargaAvulsaProcessadaModel GetCargaAvulsaProcessadaPorId(int cargaAvulsaId)
        {
            try
            {
                var query = $@"SELECT CA.idcargaavulsa           AS IdCargaAvulsa,
                                      CA.idempresa               AS IdEmpresa,
                                      CA.cpfmototista            AS CPFMototista,
                                      CA.valor                   AS Valor,
                                      CA.valor                   AS Valor,
                                      TC.statuspagamento         AS StatusPagamento,
                                      CA.statuscargaavulsa       AS Statuscargaavulsa,
                                      TC.mensagemprocessamentows AS MensagemWebService,
                                      E.razaosocial              AS RazaoSocialEmpresa
                               FROM CARGA_AVULSA CA
                                      LEFT JOIN TRANSACAO_CARTAO TC on CA.idcargaavulsa = TC.idcargaavulsa
                                      INNER JOIN EMPRESA E on CA.idempresa = E.idempresa
                               WHERE 
                                   CA.idcargaavulsa = @Id
                                 AND TC.idtransacaocartao IS NOT NULL";

                using (var connection = new DapperContext().GetConnection)
                {
                    var parameters = new {Id = cargaAvulsaId};
                    var cargasAvulsas = connection.Query<CargaAvulsaProcessadaModel>(query, parameters).ToList();
                    return cargasAvulsas.LastOrDefault();
                }
            }
            catch (Exception)
            {
                return new CargaAvulsaProcessadaModel();
            }
        }

        public void AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa statusCargaAvulsa, string mensagem, string mensagemantifraude)
        {
            try
            {
                var query = $@"UPDATE CARGA_AVULSA SET statuscargaavulsa = @Status WHERE idcargaavulsa = @Id";
                var query2 = $@"UPDATE CARGA_AVULSA SET mensagemprocessamento = @Mensagem WHERE idcargaavulsa = @Id";
                var query3 = $@"UPDATE CARGA_AVULSA SET mensagemantifraude = @MensagemAntiFraude WHERE idcargaavulsa = @Id";

                using var connection = new DapperContext().GetConnection;

                var parameters = new {Status = statusCargaAvulsa, Id = cargaAvulsaId};
                connection.Query<CargaAvulsa>(query, parameters);

                if (!string.IsNullOrEmpty(mensagem))
                {
                    var parameters2 = new {Mensagem = mensagem, Id = cargaAvulsaId};
                    connection.Query<CargaAvulsa>(query2, parameters2);
                }

                if (string.IsNullOrEmpty(mensagemantifraude)) return;
                {
                    var parameters3 = new {MensagemAntiFraude = mensagemantifraude, Id = cargaAvulsaId};
                    connection.Query<CargaAvulsa>(query3, parameters3);
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
            }
        }

        public ReciboCargaAvulsaModel ConsultarDadosReciboCargaAvulsa(int cargaAvulsaId)
        {
            try
            {
                var query = $@"select ca.nomemotorista as NomeMotorista,
                                             ca.cpfmototista as CpfMotorista,
                                             ca.placacavalo as PlacaCavalo,
                                             ca.placacarreta1 as PlacaCarreta1,
                                             ca.placacarreta2 as PlacaCarreta2,
                                             ca.placacarreta3 as PlacaCarreta3,
                                             ca.idcargaavulsa as IdCargaAvulsa,
                                             ca.nrocontroleintegracao as NumeroControleIntegracao,
                                             ca.valor as Valor,
                                             ca.datacadastro as DataCadastro,
                                             ca.statuscargaavulsa as StatusCargaAvulsa,
                                             ca.observacao as Observacao,
                                             tc.dataconfirmacaomeiohomologado as DataConfirmacaoMeioHomologado,
                                             tc.idtransacaocartao as IdTransacaoCartao,
                                             emp.IdEmpresa as EmpresaId,
                                             emp.logo as LogoEmpresa,
                                             emp.razaosocial as RazaoSocialEmpresa,
                                             emp.cnpj as CnpjEmpresa,
                                             emp.endereco as EnderecoEmpresa,
                                             emp.numero as NumeroEmpresa,
                                             emp.bairro as BairroEmpresa,
                                             emp.cep as CepEmpresa,
                                             emp.telefone as TelefoneEmpresa,
                                             c.nome as NomeCidadeEmpresa,
                                             e.sigla as SiglaEstadoEmpresa,
                                             m.celular as CelularMotorista
                                      from CARGA_AVULSA ca
                                             left join TRANSACAO_CARTAO tc on tc.idcargaavulsa = ca.idcargaavulsa
                                             inner join EMPRESA emp on emp.idempresa = ca.idempresa
                                             inner join CIDADE c on c.idcidade = emp.idcidade
                                             inner join ESTADO e on e.idestado = c.idestado
                                             left join MOTORISTA m on m.cpf = ca.cpfmototista
                                      where ca.idcargaavulsa = @CargaAvulsaId";

                using (var connection = new DapperContext().GetConnection)
                {
                    var parameters = new {CargaAvulsaId = cargaAvulsaId};
                    var cargaAvulsa = connection.Query<ReciboCargaAvulsaModel>(query, parameters).FirstOrDefault();
                    return cargaAvulsa;
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                throw new Exception(e.Message);
            }
        }

        public bool HasCargaAvulsaMesmoDocumento(int idEmpresa, string cpfMotorista, int intervalo)
        {
            try
            {
                var query = $@"SELECT Count(1) FROM CARGA_AVULSA ca 
                where ca.statuscargaavulsa in @Status
                and ca.cpfmototista = @CPFMotorista
                and ca.datacadastro >= @DataCadastro
                and ca.idempresa = @IdEmpresa";

                using var connection = new DapperContext().GetConnection;

                var parameters = new
                {
                    Status = new List<int> {2, 4},
                    CPFMotorista = cpfMotorista,
                    DataCadastro = DateTime.Now.AddHours(intervalo * -1),
                    IdEmpresa = idEmpresa
                };
                var cargaAvulsa = connection.Query<int>(query, parameters).FirstOrDefault();
                return cargaAvulsa > 0;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                throw new Exception(e.Message);
            }
        }

        public bool HasCargaAvulsaMesmoValor(int idEmpresa, decimal valor, string cpfMotorista, int idCargaAvulsa)
        {
            try
            {
                var query = $@"SELECT Count(1) FROM CARGA_AVULSA ca 
                where ca.statuscargaavulsa in @Status
                and ca.idempresa = @IdEmpresa
                and ca.cpfmototista = @CPFMotorista
                and ca.datacadastro >= @DataCadastro
                and ca.valor = @Valor
                and ca.idcargaavulsa != @IdCargaAvulsa";

                using var connection = new DapperContext().GetConnection;

                var parameters = new
                {
                    Status = new List<int> {1, 2, 4, 5, 6, 8, 9},
                    IdEmpresa = idEmpresa,
                    CPFMotorista = cpfMotorista,
                    DataCadastro = DateTime.Now.AddHours(-24),
                    Valor = valor,
                    IdCargaAvulsa = idCargaAvulsa,
                };
                
                var cargaAvulsa = connection.Query<int>(query, parameters).FirstOrDefault();
                
                return cargaAvulsa > 0;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                throw new Exception(e.Message);
            }
        }
        
        public bool HasCargaAvulsaMesmoNroControle(int idEmpresa, string nroControle)
        {
            try
            {
                var query = $@"SELECT Count(1) FROM CARGA_AVULSA ca 
                where ca.idempresa = @IdEmpresa
                and ca.nrocontroleintegracao = @NroControleIntegracao";

                using var connection = new DapperContext().GetConnection;

                var parameters = new
                {
                    IdEmpresa = idEmpresa,
                    NroControleIntegracao = nroControle
                };
                
                var cargaAvulsa = connection.Query<int>(query, parameters).FirstOrDefault();
                
                return cargaAvulsa > 0;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                throw new Exception(e.Message);
            }
        }
    }
}
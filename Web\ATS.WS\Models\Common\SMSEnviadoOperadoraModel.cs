﻿using ATS.Domain.Enum;
using System;

namespace ATS.WS.Models.Common
{
    public class SMSEnviadoOperadoraModel
    {
        public string id { get; set; }

        public string correlationId { get; set; }

        public string carrierId { get; set; }

        public string carrierName { get; set; }

        public string destination { get; set; }

        public ESMSSendStatus sentStatusCode { get; set; }

        public string sentStatus { get; set; }

        public string sentAt { get; set; }

        public DateTime? sentDate { get; set; }

        public int campaignId { get; set; }

        public object extraInfo { get; set; }
    }
}
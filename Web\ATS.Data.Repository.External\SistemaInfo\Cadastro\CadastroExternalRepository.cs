using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Data.Repository.External.SistemaInfo.Cartao.DTO;
using NLog;
using SistemaInfo.MicroServices.Rest.Cadastros.ApiClient;
using Client = SistemaInfo.MicroServices.Rest.Cadastros.ApiClient.Client;

namespace ATS.Data.Repository.External.SistemaInfo.Cadastro
{
    public class CadastroExternalRepository : IDisposable
    {
        private readonly Client _cadastrosApi;
        
        private const string ServicoIndisponivelResultMessage = "Serviço de cadastros indisponível.";

        public CadastroExternalRepository()
        {
            _cadastrosApi = new Client(HttpContext.Current);
            _cadastrosApi.BaseUrl = SistemaInfoConsts.CadastroApiUrl;
        }
        
        public ConsultarListaBancosResponse Bancos()
        {
            try
            {
                var bancos = _cadastrosApi.Bancos();
                var status = ConsultarListaBancosResponseStatus.Sucesso;
                var mensagem = string.Empty;

                if (bancos == null || !bancos.Any())
                {
                    status = ConsultarListaBancosResponseStatus.Falha;
                    mensagem = "Não foi possível retornar os bancos cadastrados";
                }

                return new ConsultarListaBancosResponse
                {
                    Bancos = bancos, 
                    Status = status,
                    Mensagem = mensagem
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new ConsultarListaBancosResponse
                {
                    Status = ConsultarListaBancosResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage,
                    Bancos = new List<ConsultarBancoResponse>()
                };
            }
        }

        public List<ConsultarNaturezaCargaResponse> GetNaturezasCarga()
        {
            var naturezasCarga = _cadastrosApi.NaturezasCarga();
            return naturezasCarga;
        }
        
        public void Dispose()
        {
        }
    }
}
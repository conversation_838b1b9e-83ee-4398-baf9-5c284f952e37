namespace ATS.Domain.DTO.Veiculo
{
    public class VeiculoRntrcDTO
    {
        public int IdVeiculo { get; set; }
        public string Placa { get; set; }
        public string DocumentoProprietario { get; set; }
        public string RNTRC
        {
            get { return !string.IsNullOrEmpty(_rntrc) ? _rntrc.PadLeft(9, '0') : null; }
            set { _rntrc = value; }
        }
        private string _rntrc { get; set; }
    }
}
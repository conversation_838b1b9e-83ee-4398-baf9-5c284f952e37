﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class LayoutCartaoMap : EntityTypeConfiguration<LayoutCartao>
    {
        public LayoutCartaoMap()
        {
            ToTable("LAYOUT_CARTAO");
            HasKey(x => x.IdLayout);

            Property(x => x.IdLayout)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(x => x.Nome)
                .IsRequired();

            HasMany(c => c.Itens)
                .WithRequired(c => c.LayoutCartao)
                .HasForeignKey(c => c.IdLayout);
        }
    }
}
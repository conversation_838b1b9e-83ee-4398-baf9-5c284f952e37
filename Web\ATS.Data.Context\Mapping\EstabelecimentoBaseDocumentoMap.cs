﻿using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoBaseDocumentoMap : EntityTypeConfiguration<EstabelecimentoBaseDocumento>
    {
        public EstabelecimentoBaseDocumentoMap()
        {
            ToTable("ESTABELECIMENTO_BASE_DOCUMENTOS");

            HasKey(t => t.IdEstabelecimentoBaseDocumento);

            HasRequired(o => o.EstabelecimentoBase)
                .WithMany(o => o.Documentos)
                .HasForeignKey(o => o.IdEstabelecimentoBase);
            
            
            Property(o => o.IdDocumento)
                .IsOptional();
           
            Property(o => o.Descricao)
                .IsOptional()
                .HasMaxLength(100);
            
            Property(o => o.DataValidade)
                .IsOptional();

            Property(o => o.Token)
                .IsOptional()
                .HasMaxLength(100);

            Ignore(x => x.PermiteEditarData)
            .Ignore(x => x.DiasValidade);
        }
    }
}

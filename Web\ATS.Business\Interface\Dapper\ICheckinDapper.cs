using System;
using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Models;

namespace ATS.Domain.Interface.Dapper
{
    public interface ICheckinDapper: IRepositoryDapper<CheckIn>
    {
        List<CheckinConsultaModel> GetByCpf(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string cpf);

        List<CheckinConsultaModel> GetByPlaca(int IdEmpresa, DateTime dataInicial, DateTime dataFinal, string placa);

        List<CheckinConsultaModel> GetByPlacaMotorista(int IdEmpresa, DateTime dataInicial, DateTime dataFinal,
            int  idMotoristaVeiculo);
    }
}
﻿// Created by <PERSON><PERSON><PERSON><PERSON> on 02-04-2018
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Enum
{
    public enum EOperationTrigger
    {

        [EnumMember, Description("Insert")]
        Insert = 0,

        [EnumMember, Description("Update")]
        Update = 1,

        [EnumMember, Description("Delete")]
        Delete = 2,

        [EnumMember, Description("None")]
        None = 3

    }
}

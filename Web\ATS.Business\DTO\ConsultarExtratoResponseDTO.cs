using System;
using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class ConsultarExtratoResponseDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public ObjetoExtratoResponseDTO Objeto { get; set; }
    }
    
    public class ObjetoGridExtratoResponseDTO
    {
        public int totalItems { get; set; }
        public List<ConsultarExtratoGridResponseDTO> items { get; set; }
    }
    
    public class ConsultarExtratoGridResponseDTO
    {
        public string ValorFormatado { get; set; }
        public string Descricao { get; set; }
        public Dictionary<string,string> Metadados { get; set; }
        public int? ProtocoloProcessamento { get; set; }
        public long? ProtocoloRequisicao { get; set; }
        public string DataTransacao { get; set; }
        public string Informacoes { get; set; }
        public Dictionary<string,string> InformacoesAdicionais { get; set; }
        public string SaldoFinal { get; set; }
        public string Tipo { get; set; }
        public string Valor { get; set; }
        public string NomeDestino { get; set; }
        public string CpfCnpjDestino { get; set; }
        public string CartaoDestino { get; set; }
        public string Instituicao { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string Dv { get; set; }
        public string HashId { get; set; }
        public List<string> Url { get; set; }
    }

    public class ObjetoExtratoResponseDTO
    {
        public decimal SaldoInicialPeriodo { get; set; }
        public decimal SaldoFinalPeriodo { get; set; }
        public List<ConsultarExtratoDetalheResponseDTO> Detalhes { get; set; }
    }

    public class ConsultarExtratoDetalheResponseDTO
    {
        public DateTime? Data { get; set; }
        public string DescricaoProcessadora { get; set; }
        public string Tipo { get; set; }
        public decimal? Valor { get; set; }
        public decimal? SaldoFinal { get; set; }
        public int? HistoricoId { get; set; }
        public string Historico { get; set; }
        public long? ProtocoloRequisicao { get; set; }
        public int? ProtocoloProcessamento { get; set; }
        public string NomeDestino { get; set; }
        public string CpfCnpjDestino { get; set; }
        public string CartaoDestino { get; set; }
        public string Instituicao { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string Dv { get; set; }
        public string HashId { get; set; }
        public Dictionary<string, string> InformacoesAdicionais { get; set; }
        public Dictionary<string, string> Metadados { get; set; }
        public bool ArquivosAnexados { get; set; }
    }
}
﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Grid;
using ATS.Domain.Models.Grid.Base;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common;
using ATS.WS.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using System.Web.Configuration;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.Application.Interface;
using ATS.WS.Attributes;

namespace ATS.WS.ControllersATS
{
    public class CredenciamentoEmpresaAtsController : DefaultController
    {

        public readonly ICredenciamentoApp AppLayer;
        private readonly IUserIdentity _userIdentity;
        private readonly SrvCredenciamento _srvCredenciamento;
        
        public CredenciamentoEmpresaAtsController(IUserIdentity userIdentity, ICredenciamentoApp appLayer, SrvCredenciamento srvCredenciamento)
        {
            _userIdentity = userIdentity;
            AppLayer = appLayer;
            _srvCredenciamento = srvCredenciamento;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public object ConsultarGridPendentes(int idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var ret = AppLayer.ConsultarGridPendentes(idEstabelecimento, take, page, order, filters);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }

        }
        
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public object ConsultarGridAprovados(int idEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var ret = AppLayer.ConsultarGridAprovados(idEstabelecimento, take, page, order, filters);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public object ConsultarImagemPorToken(string token)
        {
            try
            {
                var ret = AppLayer.ConsultarImagemPorToken(token);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public object CancelarCredenciamento(int idCredenciamento)
        {
            try
            {
                var ret = AppLayer.Cancelar(idCredenciamento);

                if (!ret.IsValid)
                    throw new Exception(ret.ToString());

                return ResponderSucesso("Solicitação de credenciamento cancelada com sucesso!");
            }
            catch (Exception e)
            {
                return
                    (e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridAnexos(int idEstabelecimento)
        {
            try
            {
                var estabelecimentos = AppLayer
                    .ConsultarAnexosCredenciamento(idEstabelecimento);

                return ResponderSucesso(estabelecimentos);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AprovarDocumentacao(int idCredenciamento, int administradoraPlataforma)
        {
            try
            {
                var ret = _srvCredenciamento
                    .AprovarDocumentacao(idCredenciamento, administradoraPlataforma);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ReprovarDocumentacao(int idCredenciamento, string motivo, int administradoraPlataforma)
        {
            try
            {
                var ret = _srvCredenciamento
                    .ReprovarDocumentacao(idCredenciamento, motivo, administradoraPlataforma);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarMotivosRejeicao(int idCredenciamento)
        {
            var rejeicoes = AppLayer.GetAllMotivos(idCredenciamento);

            return ResponderSucesso(rejeicoes.Select(x => new {
                DataRejeicao = x.DataRejeicao?.ToString("G"), x.Motivo
            }));
        }
        
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarMotivosRejeicaoCredenciamento(int idCredenciamento)
        {
            var rejeicoes = AppLayer.GetDetalhesRejeicaoCredenciamento(idCredenciamento);

            return ResponderSucesso(rejeicoes);
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult SalvarSolicitacao(CredenciamentoModel credenciamento)
        {
            try
            {

                var retorno = (credenciamento.IdCredenciamento.HasValue && credenciamento.IdCredenciamento != 0) 
                    ? _srvCredenciamento.Update(credenciamento) 
                    : _srvCredenciamento.Add(credenciamento);

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultaGrid(int? idEmpresa, int? idFilial,
            string descricao, int take, int page, OrderFilters order,
            List<QueryFilters> filters, string dataInicial, string dataFinal)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Administrador)
                    throw new Exception("Funcionalidade não habilitada para o perfil administrador!");

                if (!idEmpresa.HasValue && _userIdentity.IdEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa.Value;

                var dadosGrid = AppLayer
                    .ConsultaGrid(idEmpresa, take, page, order, filters);

                return ResponderSucesso(dadosGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GerarRelatorioGridCredenciamento(string json)
        {
            var filtrosGridModel = JsonConvert.DeserializeObject<FiltrosGridBaseModel>(json,
                new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            var report = _srvCredenciamento.GerarRelatorioGridCredenciamento(filtrosGridModel.IdEmpresa, filtrosGridModel.Take, filtrosGridModel.Page, filtrosGridModel.Order, filtrosGridModel.Filters, filtrosGridModel.Extensao);
            var mimeType = string.Empty;

            switch (filtrosGridModel.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de credenciamentos.{filtrosGridModel.Extensao}");
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult GetCredenciamentosParaEmpresaUsuarioLogado(DateTime? dataInicial, DateTime? dataFinal, int administradoraPlaraforma, bool aberto = false, bool aprovado = false, bool rejeitado = false, bool bloqueado = false, bool regular = true, bool irregular = true, bool aguardando = true, int idEmpresa = 0, int? idEstabelecimento = null)
        {
            try
            {
                dataInicial = dataInicial?.StartOfDay();
                
                if (dataFinal.HasValue)
                {
                    dataFinal = dataFinal.Value.AddHours(23);
                    dataFinal = dataFinal.Value.AddMinutes(59);
                    dataFinal = dataFinal.Value.AddSeconds(59);   
                }

                if (idEmpresa == 0)
                    idEmpresa = _userIdentity.IdEmpresa ?? 0;

                var dadosGrid = AppLayer
                    .ConsultarCredenciamentosPorEmpresa(idEmpresa, dataInicial, dataFinal, administradoraPlaraforma, aberto, aprovado, rejeitado, bloqueado, regular, irregular, aguardando, idEstabelecimento);

                return ResponderSucesso(dadosGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        /// <summary>
        /// tipoAcao(0) = Rejeitar  = tipoAcao(1) Aprovar
        /// </summary>
        /// <param name="idCredenciamento"></param>
        /// <param name="administradoraPlataforma"></param>
        /// <param name="idMotivo"></param>
        /// <param name="tipoAcao"></param>
        /// <param name="detalhamento"></param>
        /// <returns></returns>
        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult RejeitarAprovarCredenciamento(int idCredenciamento, int administradoraPlataforma, int idMotivo = 0, int tipoAcao = 0, string detalhamento = null)
        {
            try
            {
                if (tipoAcao == 0)
                {
                    AppLayer.RejeitarCredenciamento(idCredenciamento, idMotivo, detalhamento, administradoraPlataforma);
                    return ResponderSucesso("Credenciamento rejeitado com sucesso!");
                }
                else
                {
                    var validacao = AppLayer.AprovarCredenciamento(idCredenciamento, WebConfigurationManager.AppSettings.Get("LINK_WEB_NOVO"), administradoraPlataforma);
                    if (validacao.IsValid)
                    {
                        return validacao.Alerts.Any() 
                            ? ResponderAtencao(true, validacao.ToString()) 
                            : ResponderSucesso("Credenciamento aprovado com sucesso");
                    }

                    return ResponderErro(validacao.ToString());
                }
            }
            catch (Exception e)
            {
                return ResponderErro(e.GetBaseException().Message);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult BloquearCredenciamento(int idCredenciamento)
        {
            try
            {
                AppLayer.BloquearCredenciamento(idCredenciamento);

                return ResponderSucesso("Credenciamento bloqueado!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult DesbloquearCredenciamento(int idCredenciamento)
        {
            try
            {
                AppLayer.DesbloquearCredenciamento(idCredenciamento);

                return ResponderSucesso("Credenciamento desbloqueado!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Descredenciar(int? idCredenciamento, int? idMotivo, string detalhamento, int administradoraPlataforma)
        {
            try
            {
                if (!idCredenciamento.HasValue)
                    return ResponderErro("Não foi possível identificar o credenciamento. ");
                if (!idMotivo.HasValue)
                    return ResponderErro("Deve ser informado um motivo válido para realizar o descredenciamento. ");

                AppLayer.Descredenciar(idCredenciamento.Value, idMotivo.Value, detalhamento, administradoraPlataforma);
                return ResponderSucesso("Descredenciamento realizado com sucesso");
            }
            catch (Exception ex)
            {
                return ResponderErro(ex);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ReenviarEmailCredenciamentoUsuarioCadastro(int id, int administradoraPlataforma)
        {
            try
            {

                AppLayer.ReenviarEmailCredenciamentoUsuarioCadastro(id, WebConfigurationManager.AppSettings.Get("LINK_WEB_NOVO"), administradoraPlataforma);

                return ResponderSucesso("E-mail reenviado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult HasDocumentacaoVencendo(int idCredenciamento)
        {
            try
            {
                var retorno = AppLayer.HasDocumentacaoVencendo(idCredenciamento);
                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpGet]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public JsonResult ValidarNovoUsuarioEstabelecimento(string chave, int idEstabelecimento)
        {
            var valido = new ValidationResult();
            
            try
            {
                valido.Add(AppLayer.ValidarNovoUsuarioEstabelecimento(chave, idEstabelecimento));
                
                if(valido.IsValid)
                    return ResponderSucesso("Usuário valido");
            }
            catch (Exception e)
            {
                valido.Add(e.GetBaseException().Message);
            }
            
            return ResponderErro(valido.ToString());
        }
    }
}
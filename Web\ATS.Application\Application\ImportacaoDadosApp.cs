﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using NLog;

namespace ATS.Application.Application
{
    public class ImportacaoDadosApp : AppBase, IImportacaoDadosApp
    {
        private readonly IImportacaoDadosService _service;
        public ImportacaoDadosApp(IImportacaoDadosService service)
        {
            _service = service;
        }

        public BusinessResult<ValidacaoImportacaoDadosResponse> ValidarPlanilha(HttpPostedFileBase file)
        {
            try
            {
                if (!ValidarTipoArquivo(file.FileName))
                    return BusinessResult<ValidacaoImportacaoDadosResponse>.Error("Arquivo inválido");
                
                if (file.ContentLength == 0)
                    return BusinessResult<ValidacaoImportacaoDadosResponse>.Error("Planilha vazia");

                var resultValidacao = _service.ValidarPlanilha(file);

                if (!resultValidacao.Success)
                    return BusinessResult<ValidacaoImportacaoDadosResponse>.Error(resultValidacao.Messages);
                
                var retorno = new ValidacaoImportacaoDadosResponse()
                {
                    Validacao = new List<ValidacaoImportacaoDadosItem>()
                };
                
                if(resultValidacao.Value.Any())
                    retorno.Validacao.AddRange(resultValidacao.Value);
                
                return BusinessResult<ValidacaoImportacaoDadosResponse>.Valid(retorno);
            }
            catch (Exception e)
            {
                return BusinessResult<ValidacaoImportacaoDadosResponse>.Error(e.Message);
            }
        }

        public BusinessResult Processar(HttpPostedFileBase file, int idEmpresa)
        {
            try
            {
                var result = _service.ImportarDados(file,idEmpresa);
                
                if(!result.Success)
                    return BusinessResult.Error($"Falha ao processar planilha: {result.Messages.FirstOrDefault()}");

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                return BusinessResult.Error($"Falha ao processar planilha: {e.Message}");
            }
        }

        private bool ValidarTipoArquivo(string arquivo)
        {
            return
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.ExcelBinaryExtension ||
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.ExcelXmlExtention ||
                Path.GetExtension(arquivo.ToLower()) == ConstantesUtils.CsvExtention;
        }
    }
}

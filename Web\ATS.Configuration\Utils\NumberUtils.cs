﻿namespace ATS.CrossCutting.IoC.Utils
{
    public static class NumberUtils
    {
        public static decimal? NullIf(this decimal value, decimal check)
        {
            if (value == check)
                return null;
            return value;
        }

        public static decimal? NullIf(this decimal? value, decimal check)
        {
            if (value.HasValue && value == check)
                return null;
            return value;
        }
    }
}

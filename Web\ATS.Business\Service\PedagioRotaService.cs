using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models.PedagioRota;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class PedagioRotaService : BaseService<IPedagioRotaRepository>, IPedagioRotaService
    {
        private readonly ICidadeRepository _cidadeRepository;
        
        public PedagioRotaService(IPedagioRotaRepository repository, IUserIdentity sessionUser, ICidadeRepository cidadeRepository) : base(repository, sessionUser)
        {
            _cidadeRepository = cidadeRepository;
        }

        public PedagioRotaSalvarResponse Salvar(PedagioRotaSalvarRequest request)
        {
            if (!request.Id.HasValue) return Adicionar(request);

            var existente = Repository.Get(c => c.IdPedagioRota == request.Id).Any();

            if (existente)
                return Atualizar(request);

            return new PedagioRotaSalvarResponse
            {
                Resultado = new ValidationResult().Add("Nenhuma rota encontrada com o Id informado")
            };
        }

        public IList<PedagioRotaGridResponse> ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            if (!SessionUser.IdEmpresa.HasValue || SessionUser.IdEmpresa <= 0)
                throw new Exception("Não é permitida consulta a usuário sem vínculo a empresa.");
            
            var query = Repository.Get(c => c.IdEmpresa == SessionUser.IdEmpresa);

            var registro = query.Select(c => new
            {
                Id = c.IdPedagioRota,
                c.Descricao,
                CidadeOrigem = c.Pontos.OrderBy(a => a.Sequencia).Select(a => a.Cidade.Nome).FirstOrDefault(),
                EstadoOrigem = c.Pontos.OrderBy(a => a.Sequencia).Select(a => a.Cidade.Estado.Sigla).FirstOrDefault(),
                PaisOrigem = c.Pontos.OrderBy(a => a.Sequencia).Select(a => a.Cidade.Estado.Pais.Nome).FirstOrDefault(),
                CidadeDestino = c.Pontos.OrderByDescending(a => a.Sequencia).Select(a => a.Cidade.Nome).FirstOrDefault(),
                EstadoDestino = c.Pontos.OrderByDescending(a => a.Sequencia).Select(a => a.Cidade.Estado.Sigla).FirstOrDefault(),
                PaisDestino = c.Pontos.OrderByDescending(a => a.Sequencia).Select(a => a.Cidade.Estado.Pais.Nome).FirstOrDefault(),
            }).ToList();

            var retorno = registro.Select(c => new PedagioRotaGridResponse
            {
                Id = c.Id,
                Descricao = c.Descricao,
                Origem = $"{c.CidadeOrigem}-{c.EstadoOrigem} ({c.PaisOrigem})",
                Destino = $"{c.CidadeDestino}-{c.EstadoDestino} ({c.PaisDestino})"
            }).AsQueryable();
            
            retorno = retorno.AplicarFiltrosDinamicos(filters);
            
            retorno = string.IsNullOrWhiteSpace(order?.Campo)
                ? retorno.OrderBy(o => o.Id)
                : retorno.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            return retorno
                .Skip((page - 1)*take)
                .Take(take).ToList();
        }

        public PedagioRotaDetalheResponse ConsultarDetalhes(int idRota)
        {
            var resultado = Repository.Get(c => c.IdPedagioRota == idRota && c.IdEmpresa == SessionUser.IdEmpresa).Select(c => new PedagioRotaDetalheResponse
            {
                Id = idRota,
                Descricao = c.Descricao,
                Pontos = c.Pontos.Select(a => new PedagioRotaPontoDetalheResponse
                {
                    Sequencia = a.Sequencia,
                    Latitude = a.Latitude,
                    Longitude = a.Longitude,
                    Cidade = a.Cidade.Nome,
                    Estado = a.Cidade.Estado.Sigla,
                    Pais = a.Cidade.Estado.Pais.Nome
                }).ToList()
            }).FirstOrDefault();

            return resultado;
        }

        #region Métodos privados

        private PedagioRotaSalvarResponse Adicionar(PedagioRotaSalvarRequest request)
        {
            if (!SessionUser.IdEmpresa.HasValue)
                throw new Exception("Erro ao salvar a rota. Usuário deve ter empresa vinculada.");
            
            var pedagioRota = new PedagioRota();

            pedagioRota.Descricao = request.Descricao;
            pedagioRota.IdEmpresa = SessionUser.IdEmpresa ?? 0;

            pedagioRota.Pontos = new List<PedagioRotaPonto>();

            var i = 1;
            
            foreach (var pedagioRotaPontoRequest in request.Pontos)
            {
                var idCidade = _cidadeRepository.Where(c => c.Latitude == pedagioRotaPontoRequest.Latitude && c.Longitude == pedagioRotaPontoRequest.Longitude)
                    .Select(c => c.IdCidade).FirstOrDefault();

                pedagioRota.Pontos.Add(new PedagioRotaPonto
                {
                    Sequencia = i,
                    IdCidade = idCidade > 0 ? idCidade : (int?) null,
                    Latitude = pedagioRotaPontoRequest.Latitude,
                    Longitude = pedagioRotaPontoRequest.Longitude
                });
                
                i++;
            }

            Repository.Add(pedagioRota);

            Repository.SaveChanges();

            return new PedagioRotaSalvarResponse
            {
                Id = pedagioRota.IdPedagioRota,
                Descricao = pedagioRota.Descricao
            };
        }

        private PedagioRotaSalvarResponse Atualizar(PedagioRotaSalvarRequest request)
        {
            var pedagioRota = Repository.Get(c => c.IdPedagioRota == request.Id)
                .Include(c => c.Pontos).First();

            if (SessionUser.Perfil == (int)EPerfil.Empresa && pedagioRota.IdEmpresa != SessionUser.IdEmpresa)
                throw new InvalidOperationException("Usuário não autenticado.");

            pedagioRota.Descricao = request.Descricao;
            pedagioRota.IdEmpresa = SessionUser.IdEmpresa ?? 0;

            pedagioRota.Pontos = new List<PedagioRotaPonto>();

            var i = 1;
            
            foreach (var pedagioRotaPontoRequest in request.Pontos)
            {
                var idCidade = _cidadeRepository.Where(c => c.Latitude == pedagioRotaPontoRequest.Latitude && c.Longitude == pedagioRotaPontoRequest.Longitude)
                    .Select(c => c.IdCidade).FirstOrDefault();

                pedagioRota.Pontos.Add(new PedagioRotaPonto
                {
                    Sequencia = i,
                    IdCidade = idCidade > 0 ? idCidade : (int?) null,
                    Latitude = pedagioRotaPontoRequest.Latitude,
                    Longitude = pedagioRotaPontoRequest.Longitude
                });

                i++;
            }

            Repository.Update(pedagioRota);

            Repository.SaveChanges();

            return new PedagioRotaSalvarResponse
            {
                Id = pedagioRota.IdPedagioRota,
                Descricao = pedagioRota.Descricao
            };
        }

        #endregion
    }
}
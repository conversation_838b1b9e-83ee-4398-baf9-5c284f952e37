﻿using System;
using ATS.Domain.Enum;

namespace ATS.Domain.Entities
{
    public class LogSms
    {
        public int IdLogSms { get; set; }
        public int IdEmpresa { get; set; }
        public string CelularEnvio { get; set; }
        public string Texto { get; set; }
        public EProcessoEnvio ProcessoEnvio { get; set; }
        public DateTime DataHoraEnvio { get; set; }
        public int? IdUsuarioEnvio { get; set; }
        public int? IdPreUsuarioEnvio { get; set; }
        public EPerfil? Perfil { get; set; }

        #region Relacionamentos
        public virtual Empresa Empresa { get; set; }
        public virtual Usuario Usuario { get; set; }
        #endregion
    }
}

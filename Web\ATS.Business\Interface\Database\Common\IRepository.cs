﻿using ATS.Domain.Grid;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using TrackerEnabledDbContext.Common.Interfaces;

namespace ATS.Domain.Interface.Database.Common
{
    public interface IRepository : IDisposable
    {

    }

    public interface IRepository<TEntity> : IRepository where TEntity : class
    {
        #region CRUD

        TEntity Add(TEntity entity, bool autoSaveChanges = true);
        void Update(TEntity entity, bool autoSaveChanges = true);
        void Delete(TEntity entity, bool autoSaveChanges = true);
        void DeleteRange(List<TEntity> entities, bool autoSaveChanges = true);
        void SaveChanges(TEntity entity);
        void SaveChanges();
        Task SaveChangesAsync();

        #endregion

        #region Selectors

        TEntity Get(int id);
        IQueryable<TEntity> All(bool @readonly = false);
        IQueryable<TEntity> Find(Expression<Func<TEntity, bool>> predicate, bool @readonly = false);
        IQueryable<TEntity> GetAll();
        IQueryable<TEntity> AsNoTracking();
        IQueryable<TEntity> Include<TProperty>(Expression<Func<TEntity, TProperty>> aPath);
        TEntity First(Expression<Func<TEntity, bool>> predicate = null);
        TResult First<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector);
        TEntity FirstOrDefault(Expression<Func<TEntity, bool>> predicate = null);
        TResult FirstOrDefault<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector);
        TEntity Single(Expression<Func<TEntity, bool>> predicate = null);
        TResult Single<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector);
        TEntity SingleOrDefault(Expression<Func<TEntity, bool>> predicate = null);
        TResult SingleOrDefault<TResult>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TResult>> selector);
        IQueryable<TEntity> Where(Expression<Func<TEntity, bool>> predicate);
        IQueryable<TEntity> Query();
        IQueryable<TResult> Select<TResult>(Expression<Func<TEntity, TResult>> selector);
        bool Any(Expression<Func<TEntity, bool>> predicate);

        IQueryable<TEntity> Consultar(List<QueryFilters> Filters, OrderFilters Order);

        #endregion

        #region Others

        void Detach(TEntity entity);
        void Detach(List<TEntity> entities);
        DbPropertyValues OriginalValues(TEntity entity);

        #endregion
    }
}
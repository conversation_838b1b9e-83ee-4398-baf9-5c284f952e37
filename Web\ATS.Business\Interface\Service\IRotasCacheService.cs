﻿using System;
using System.Collections.Generic;
using ATS.Domain.Grid;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.Interface.Service
{
    public interface IRotasCacheService
    {
        DataModel<object> ConsultarGrid(DateTime dataInicio, DateTime dataFim, int take, int page,
            OrderFiltersPedagio order, List<QueryFiltersPedagio> filters);
        void DeletarRotaCache(Guid guidRota);
    }
}

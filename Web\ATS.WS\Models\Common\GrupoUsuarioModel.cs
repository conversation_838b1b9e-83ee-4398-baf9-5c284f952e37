﻿using System.Collections.Generic;

namespace ATS.WS.Models.Common
{
    public class GrupoUsuarioModel
    {
        public int IdGrupoUsuario { get; set; }

        public int? IdEmpresa { get; set; }

        public string Descricao { get; set; }

        public bool Ativo { get; set; } = true;

        public virtual EmpresaModel Empresa { get; set; }

        public virtual ICollection<GrupoUsuarioMenuModel> Menus { get; set; }
    }
}
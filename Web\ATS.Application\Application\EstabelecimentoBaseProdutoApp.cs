﻿using ATS.Domain.Entities;
using ATS.Domain.Service;
using System.Collections.Generic;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class EstabelecimentoBaseProdutoApp : IEstabelecimentoBaseProdutoApp
    {
        private readonly IEstabelecimentoBaseProdutoService _estabelecimentoBaseProdutoService;

        public EstabelecimentoBaseProdutoApp(IEstabelecimentoBaseProdutoService estabelecimentoBaseProdutoService)
        {
            _estabelecimentoBaseProdutoService = estabelecimentoBaseProdutoService;
        }

        public List<EstabelecimentoBaseProduto> GetProdutos(List<int> ids)
        {
            return _estabelecimentoBaseProdutoService.GetProdutos(ids);
        }

        public int GetUltimoIdProduto()
        {
            return _estabelecimentoBaseProdutoService.GetUltimoIdProduto();
        }
    }
}

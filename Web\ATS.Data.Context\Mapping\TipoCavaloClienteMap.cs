﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class TipoCavaloClienteMap : EntityTypeConfiguration<TipoCavaloCliente>
    {
        public TipoCavaloClienteMap()
        {
            ToTable("TIPO_CAVALO_CLIENTE");

            HasKey(x => new { x.IdTipoCavalo, x.IdCliente });

            Property(t => t.Nome)
               .IsOptional()
               .HasMaxLength(100);

            HasRequired(t => t.TipoCavalo)
                .WithMany(t => t.TipoCavaloCliente)
                .HasForeignKey(t => t.IdTipoCavalo);

            HasRequired(t => t.Cliente)
               .WithMany(t => t.TipoCavaloCliente)
               .HasForeignKey(t => t.IdCliente);

        }

    }
}

using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Request.Base;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Response;
using ATS.WS.Models.Webservice.Request.Cartoes;
using ATS.WS.Models.Webservice.Request.Veiculo;
using AutoMapper;

namespace ATS.WS.Services.ViagemV2Services
{
    public class IntegracoesPreViagemV2
    {
        private readonly SrvCartoes _srvCartoes;
        private readonly SrvCliente _srvCliente;
        private readonly SrvProprietario _srvProprietario;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IParametrosEmpresaService _parametrosEmpresa;
        private readonly ISerproApp _serproApp;

        public IntegracoesPreViagemV2(SrvCartoes srvCartoes, SrvCliente srvCliente, SrvProprietario srvProprietario, 
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IParametrosEmpresaService parametrosEmpresa, 
            ISerproApp serproApp)
        {
            _srvCartoes = srvCartoes;
            _srvCliente = srvCliente;
            _srvProprietario = srvProprietario;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _parametrosEmpresa = parametrosEmpresa;
            _serproApp = serproApp;
        }

        public ViagemV2ResultadoIntegracoesPreViagem RealizarIntegracaoClienteViagem(ClienteRequestModel cliente, RequestBase request)
        {
            var resposta = new ViagemV2ResultadoIntegracoesPreViagem();
            cliente.CNPJEmpresa = request.CNPJEmpresa;
            cliente.CNPJAplicacao = request.CNPJAplicacao;
            
            var resultadoIntegracao = _srvCliente.Integrar(cliente);
            return resultadoIntegracao.Sucesso ? resposta.RetornarSucesso() : resposta.RetornarFalha(resposta.Mensagem);
        }

        public ViagemV2ResultadoIntegracoesPreViagem RealizarIntegracaoProprietarioViagem(ProprietarioIntegrarRequestModel proprietario, RequestBase request, int empresaId)
        {
            var resposta = new ViagemV2ResultadoIntegracoesPreViagem();
            proprietario.CNPJEmpresa = request.CNPJEmpresa;
            proprietario.CNPJAplicacao = request.CNPJAplicacao;
            
            var resultadoIntegracao = _srvProprietario.Integrar(proprietario);

            if (resultadoIntegracao.Sucesso)
            {
                if (proprietario.Cartao != null)
                {
                    var resultadoVinculoCartao = RealizarVinculoCartao(_cartoesAppFactoryDependencies, _srvCartoes, proprietario.Cartao.NumeroCartao, empresaId,proprietario.CnpjCpf, proprietario.Cartao.RealizarTrocaCartao, 
                        request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                    if (!resultadoIntegracao.Sucesso)
                        return resposta.RetornarFalha($"Proprietário integrado com sucesso, porém ao vincular o cartão ocorreu uma falha: {resultadoVinculoCartao.Mensagem}");
                    
                    return resposta.RetornarSucesso(resultadoVinculoCartao.Mensagem);
                }
            }
            else
                return resposta.RetornarFalha(resultadoIntegracao.Mensagem);

            return resposta.RetornarSucesso();
        }

        public ViagemV2ResultadoIntegracoesPreViagem RealizarIntegracaoMotoristaViagem(CartoesAppFactoryDependencies cartoesAppFactoryDependencies, SrvCartoes srvCartoesInstance, SrvMotorista srvMotorista, MotoristaIntegrarRequestModel motorista, RequestBase request, int empresaId)
        {
            var resposta = new ViagemV2ResultadoIntegracoesPreViagem();
            motorista.CNPJEmpresa = request.CNPJEmpresa;
            motorista.CNPJAplicacao = request.CNPJAplicacao;
            
            var resultadoIntegracao = srvMotorista.Integrar(motorista);

            if (resultadoIntegracao.Sucesso)
            {
                if (motorista.Cartao != null)
                {
                    var resultadoVinculoCartao =
                        RealizarVinculoCartao(cartoesAppFactoryDependencies, srvCartoesInstance, motorista.Cartao.NumeroCartao, empresaId, motorista.Cpf, motorista.Cartao.RealizarTrocaCartao, 
                            request.CNPJEmpresa, request.DocumentoUsuarioAudit, request.NomeUsuarioAudit);

                    if (!resultadoVinculoCartao.Sucesso)
                        return resposta.RetornarFalha($"Motorista integrado com sucesso, porém ao vincular o cartão ocorreu uma falha: {resultadoVinculoCartao.Mensagem}");
                    
                    return resposta.RetornarSucesso(resultadoVinculoCartao.Mensagem);
                }
            }
            else
                return resposta.RetornarFalha(resultadoIntegracao.Mensagem);

            return resposta.RetornarSucesso();
        }

        public ViagemV2ResultadoIntegracoesPreViagem RealizarIntegracaoVeiculoViagem(SrvVeiculo srvVeiculo, VeiculoIntegrarRequestModel veiculo, RequestBase request)
        {
            var retorno = new ViagemV2ResultadoIntegracoesPreViagem();
            veiculo.CNPJEmpresa = request.CNPJEmpresa;
            veiculo.CNPJAplicacao = request.CNPJAplicacao;
            
            var respostaIntegracao = srvVeiculo.Integrar(veiculo);
            return respostaIntegracao.Sucesso ? retorno.RetornarSucesso() : retorno.RetornarFalha(respostaIntegracao.Mensagem);
        }

        public ViagemV2ResultadoIntegracoesPreViagem RealizarIntegracaoCarretaViagem(List<ViagemCarretasV2IntegrarModel> carretas, 
            int empresaId, IVeiculoApp veiculoApp, IProprietarioApp proprietarioApp, ICidadeApp cidadeApp, IFilialApp filialApp)
        {
            var retorno = new ViagemV2ResultadoIntegracoesPreViagem();
            var msgsErro = "Erro ao salvar carretas: ";
            var erro = false;
            foreach (var carreta in carretas)
            {
                var veiculoExistente = veiculoApp.VeiculoValidoIntegracao(carreta.Placa, empresaId);
                if (veiculoExistente) continue;
                var veiculoRequest = new VeiculoCreateRequest()
                {
                    IdEmpresa = empresaId,
                    IdProprietario = proprietarioApp.GetIdPorCpfCnpj(carreta.CPFCNPJProprietario, empresaId),
                    Placa = carreta.Placa,
                    Chassi = carreta.Chassi,
                    AnoFabricacao = carreta.AnoFabricacao,
                    AnoModelo = carreta.AnoModelo,
                    Marca = carreta.Marca,
                    Modelo = carreta.Modelo,
                    ComTracao = carreta.Comtracao,
                    IdTipoCarreta = carreta.IdTipoCarreta,
                    TipoContrato = carreta.TipoContrato,
                    QuantidadeEixos = carreta.QuantidadeEixos,
                    IdTipoCavalo = carreta.IdTipoCavalo,
                    NumeroFrota = carreta.NumeroFrota,
                    Municipio = carreta.Municipio,
                    Renavam = carreta.Renavam,
                    TipoRodagem = carreta.TipoRodagem,
                    TecnologiaRastreamento = carreta.TecnologiaRastreamento,
                    IdOperacao = carreta.CodigodaOperacao,
                    CorVeiculo = carreta.CorVeiculo,
                };

                if (carreta.IBGECidade.HasValue) veiculoRequest.IdCidade = cidadeApp.GetIdCidade(carreta.IBGECidade.Value);
                if (!string.IsNullOrWhiteSpace(carreta.CNPJFilial)) veiculoRequest.IdFilial = filialApp.GetIdPorCnpj(carreta.CNPJFilial);

                var veiculo = Mapper.Map<VeiculoCreateRequest, Veiculo>(veiculoRequest);
                var cadastrarVeiculo = veiculoApp.Add(veiculo);
                if (!cadastrarVeiculo.IsValid)
                {
                    erro = true;
                    msgsErro += $"{veiculo.Placa} - {cadastrarVeiculo.Errors.FirstOrDefault()?.Message}; ";
                }
            }

            return erro ? retorno.RetornarFalha(msgsErro) : retorno.RetornarSucesso();
        }

        public Retorno<object> RealizarVinculoCartao(CartoesAppFactoryDependencies cartoesAppFactoryDependencies, SrvCartoes srvCartoesInstance,
            int numeroCartao, int empresaId, string documento, bool realizarTroca, string cnpjEmpresa, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var usuarioDocAudit = !string.IsNullOrWhiteSpace(documentoUsuarioAudit)
                ? documentoUsuarioAudit
                : CartoesService.AuditDocIntegracao;
            
            var cartoesApp = CartoesApp.CreateByEmpresa(cartoesAppFactoryDependencies, empresaId, usuarioDocAudit, nomeUsuarioAudit);
            var produtos = new List<int> {cartoesApp.GetIdProdutoCartaoFretePadrao()};
            var cartoesVinculados = cartoesApp.GetCartoesVinculados(documento, produtos,buscarCartoesBloqueados:true);

            if (cartoesVinculados.Cartoes.Any())
            {
                if (cartoesVinculados.Cartoes.Any(o => o.Identificador == numeroCartao))
                    return new Retorno<object>(true);
                
                if (!realizarTroca)
                    return new Retorno<object>(false, "Portador já possui um cartão vinculado, para realizar a troca do cartão, habilite a flag 'RealizarTrocaCartao' na integração.", null);
            }
            
            var resultadoVinculoCartao = srvCartoesInstance.VincularCartaoPortador(new CartaoVincularPortadorExternalRequest
            {
                NumeroCartao = numeroCartao, CNPJEmpresa = cnpjEmpresa, CNPJEstabelecimento = string.Empty, Documento = documento
            });

            return resultadoVinculoCartao;
        }
    }
}
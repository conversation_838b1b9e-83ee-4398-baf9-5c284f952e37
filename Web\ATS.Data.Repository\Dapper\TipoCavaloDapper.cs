﻿using ATS.CrossCutting.IoC;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database.Common;
using System.Linq;

namespace ATS.Data.Repository.Dapper
{
    public class TipoCavaloDapper : DapperFactory<TipoCavalo>, ITipoCavaloDapper
    {
        public TipoCavalo GetPorDescricao(string nome, int idEmpresa)
        {
            string sSql = $"SELECT * FROM TIPO_CAVALO WHERE UPPER(nome) collate Latin1_General_CI_AI LIKE UPPER('{nome}') " +
                          $" AND (idempresa IS NULL OR idempresa = {idEmpresa})";

            return this.RunSelect(sSql).FirstOrDefault();
        }
    }
}

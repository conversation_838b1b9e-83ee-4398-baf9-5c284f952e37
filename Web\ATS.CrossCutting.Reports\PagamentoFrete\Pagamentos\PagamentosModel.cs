﻿namespace ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos
{
    public class PagamentosHeaderModel
    {
        public string DataGeracao { get; set; }
        public string Usuario { get; set; }
    }

    public class PagamentosModel
    {
        public string Token { get; set; }
        public string DataLancamento { get; set; }
        public string CteSerie { get; set; }
        public string DataPagamento { get; set; }
        public string DataLiberacao { get; set; }
        public string Evento { get; set; }
        public string Valor { get; set; }
        public string Status { get; set; }
        public string DataAgendamentoPagamento { get; set; }
        public string Placa { get; set; }
        public string Estabelecimento { get; set; }
        public string CpfMotorista { get; set; }
        public string Motorista { get; set; }
        public string PossuiProtocolo { get; set; }
        public int? IdProtocolo { get; set; }
        public string CIOT { get; set; }
        public string NumeroNota { get; set; }
        public string ValorQuebra { get; set; }
        public string ValorPedagio { get; set; }
        public string DocProprietario { get; set; }
        public string Proprietario { get; set; }
        public string PagoCartao { get; set; }
        public int? IdViagemEvento { get; set; }
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ClienteAcessoMap : EntityTypeConfiguration<ClienteAcesso>
    {
        public ClienteAcessoMap()
        {
            ToTable("CLIENTE_ACESSO");

            HasKey(t => new { IdCliente = t.IdCliente, t.CNPJCPF });

            Property(t => t.IdCliente)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.CNPJCPF)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.CNPJCPF)
                .IsRequired()
                .HasMaxLength(14);
        }
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Models.Common;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Especie;
using AutoMapper;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;

namespace ATS.WS.Services
{
    public class SrvEspecie : SrvBase
    {
        private readonly IEspecieApp _especieApp;

        public SrvEspecie(IEspecieApp especieApp)
        {
            _especieApp = especieApp;
        }

        public Retorno<EspecieModel> IntegrarAlterarEspecie(IntegrarAlterarEspecieRequestModel @params)
        {
            try
            {
                var parametros = @params;

                if (string.IsNullOrEmpty(parametros.Descricao))
                    return new Retorno<EspecieModel>(false, "Descrição da espécie não informada", null);

                if (parametros.IdEspecie.HasValue)
                {
                    var especie = _especieApp.Get(parametros.IdEspecie.Value);

                    if (especie == null)
                        return new Retorno<EspecieModel>(false,
                            $"Espécie não encontrada para o IdEspecie {parametros.IdEspecie.Value}", null);

                    especie.Descricao = parametros.Descricao;
                    especie.Ativo = parametros.Ativo;
                    especie.DataHoraUltimaAtualizacao = DateTime.Now;

                    var resultadoUpdate = _especieApp.UpdateReturningObject(especie);
                    return new Retorno<EspecieModel>(true, "Espécie atualizada com sucesso.",
                        new EspecieModel
                        {
                            IdEspecie = resultadoUpdate.IdEspecie,
                            Descricao = resultadoUpdate.Descricao,
                            Ativo = resultadoUpdate.Ativo
                        });
                }

                var resultadoAdd = _especieApp.AddReturningObject(new Especie
                {
                    Descricao = parametros.Descricao,
                    DataHoraUltimaAtualizacao = DateTime.Now,
                    Ativo = parametros.Ativo
                });

                return new Retorno<EspecieModel>(true, "Espécie cadastrada com sucesso.", new EspecieModel
                {
                    IdEspecie = resultadoAdd.IdEspecie,
                    Descricao = resultadoAdd.Descricao,
                    Ativo = resultadoAdd.Ativo
                });
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<EspecieModel>($"{nameof(GetEspeciesAtualizadas)} >> {e.Message}");
            }
        }

        public Retorno<EspecieModel> ConsultarEspecie(int? idEspecie)
        {
            try
            {
                if (!idEspecie.HasValue)
                    return new Retorno<EspecieModel>(false, "IdEspecia não informado.", null);

                var especie = _especieApp.Get(idEspecie.Value);

                if (especie == null)
                    return new Retorno<EspecieModel>(false,
                        $"Nenhum cadastro de espécie encontrado para o IdEspecie {idEspecie.Value}.", null);

                return new Retorno<EspecieModel>(true, "Consulta realizada com sucesso.",
                    new EspecieModel
                    {
                        IdEspecie = especie.IdEspecie,
                        Descricao = especie.Descricao,
                        Ativo = especie.Ativo
                    });
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<EspecieModel>($"{nameof(GetEspeciesAtualizadas)} >> {e.Message}");
            }
        }

        /// <summary>
        /// Retorna a lista das Especieis de Carga atualizadas/inseridas a partir da data de base
        /// </summary>
        /// <param name="dataBase">Data base para pesquisa</param>
        /// <returns></returns>
        public Retorno<List<EspecieModel>> GetEspeciesAtualizadas(DateTime dataBase)
        {
            try
            {
                return new Retorno<List<EspecieModel>>(true, string.Empty,
                    Mapper.Map<List<Especie>, List<EspecieModel>>
                        (_especieApp.GetEspeciesAtualizadas(dataBase).ToList()));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<EspecieModel>>($"{nameof(GetEspeciesAtualizadas)} >> {e.Message}");
            }
        }

        public ValidationResult Cadastrar(CadastroEspecieRequestModel @params, Usuario usuarioLogado)
        {

            try
            {
                var validationResult = new ValidationResult();

                if (usuarioLogado.Perfil != EPerfil.Administrador)
                    validationResult.Add("Apenas usuários administradores podem editar uma especie.");

                if (@params.Descricao == null || @params.Descricao.Length < 1)
                    validationResult.Add("Descrição é obrigatória");

                if (!validationResult.IsValid)
                    return validationResult;

                var especie = new Especie
                {
                    Ativo = true,
                    DataHoraUltimaAtualizacao = DateTime.Now,
                    Descricao = @params.Descricao
                };

                validationResult = _especieApp.Add(especie);

                return !validationResult.IsValid ? validationResult : new ValidationResult();
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Editar(CadastroEspecieRequestModel @params, Usuario usuarioLogado)
        {
            try
            {
                var validationResult = new ValidationResult();

                if (usuarioLogado.Perfil != EPerfil.Administrador)
                    validationResult.Add("Usuário não possui permissão para editar este registro.");

                var especie = _especieApp.Get(@params.IdEspecie);
                if (especie == null)
                    validationResult.Add("Não foi possível localizar a especie.");

                if (!validationResult.IsValid)
                    return validationResult;

                ReflectionHelper.CopyProperties(@params, especie);
                validationResult = _especieApp.Update(especie);

                return validationResult;
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}
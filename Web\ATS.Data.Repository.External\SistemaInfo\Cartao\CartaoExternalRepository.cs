﻿using ATS.Data.Repository.External.SistemaInfo.Auth;
using NLog;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.SistemaInfo.Cartao.DTO;
using Newtonsoft.Json;
using RestSharp.Extensions;
using CartoesClient = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.CartoesClient;
using Client = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.Client;
using ConsultarExtratoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarExtratoRequest;
using ConsultarSaldoCartaoRequest = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoRequest;
using ConsultarSaldoCartaoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoResponse;
using ConsultarSaldoCartaoResponseStatus = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoCartaoResponseStatus;
using ConsultarSaldoEmpresaResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoEmpresaResponse;
using ConsultarSaldoEmpresaResponseStatus = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ConsultarSaldoEmpresaResponseStatus;
using EmpresasClient = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.EmpresasClient;
using ProdutoResponse = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.ProdutoResponse;
using SwaggerException = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.SwaggerException;
using Sistema.Framework.Util.Extension;

namespace ATS.Data.Repository.External.SistemaInfo.Cartao
{
    public class CartaoExternalRepository : IDisposable
    {
        private const string ServicoIndisponivelResultMessage = "Serviço de cartão indisponível.";
        private const string PrefixoMensagemExcecao = "[Serviço de cartão] ";

        private AuthExternalRepository authRepository;

        private readonly PessoasClient _pessoaApi;
        private readonly Client _produtoApi;
        private readonly OperacoesClient _operacaoApi;
        private readonly BuscarClient _buscarApi;
        private readonly RemessaClient _remessaApi;
        private readonly CartoesClient _cartoesApi;
        private readonly TransacoesClient _transacoesClient;
        private readonly EmpresasClient _empresaApi;
        private readonly RelatoriosClient _relatoriosClient;

        private readonly global::SistemaInfo.MicroServices.Rest.Cartao.WebClient.Client _cartoesWeb;
        private readonly TransferenciasClient _transferenciasClient;

        private string Token { get; }
        private string DocumentoUsuarioAudit { get; }
        public string NomeUsuarioAudit { get; }

        public CartaoExternalRepository(string token, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var authWeb = new AuthClient(HttpContext.Current);
            authWeb.BaseUrl = SistemaInfoConsts.CartaoWebUrl;

            _pessoaApi = new PessoasClient(HttpContext.Current);
            _produtoApi = new Client(HttpContext.Current);
            _operacaoApi = new OperacoesClient(HttpContext.Current);
            _buscarApi = new BuscarClient(HttpContext.Current);
            _remessaApi = new RemessaClient(HttpContext.Current);
            _cartoesApi = new CartoesClient(HttpContext.Current);
            _transacoesClient = new TransacoesClient(HttpContext.Current);
            _transferenciasClient = new TransferenciasClient(HttpContext.Current);
            _cartoesWeb = new global::SistemaInfo.MicroServices.Rest.Cartao.WebClient.Client(HttpContext.Current);
            _empresaApi = new EmpresasClient(HttpContext.Current);
            _relatoriosClient = new RelatoriosClient(HttpContext.Current);

            _pessoaApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _produtoApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _operacaoApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _buscarApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _remessaApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _cartoesApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _transacoesClient.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _transferenciasClient.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _cartoesWeb.BaseUrl = SistemaInfoConsts.CartaoWebUrl;
            _empresaApi.BaseUrl = SistemaInfoConsts.CartaoApiUrl;
            _relatoriosClient.BaseUrl = SistemaInfoConsts.CartaoApiUrl;

            Token = token;
            DocumentoUsuarioAudit = documentoUsuarioAudit;

            NomeUsuarioAudit = StringUtil.RemoveAccents(nomeUsuarioAudit).RemoveSpecialCaracter(true);
        }

        public void Dispose()
        {
        }

        private string GetWebAuthToken()
        {
            if (authRepository == null)
                authRepository = new AuthExternalRepository();

            return authRepository.LoginDinamicoCacheable(DocumentoUsuarioAudit, NomeUsuarioAudit, Token);
        }

        public CartaoVinculadoPessoaListResponse GetCartoesVinculados(string documento, List<int> idProdutos, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                return _pessoaApi.CartoesVinculados(documento, idProdutos, Token,
                    documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new CartaoVinculadoPessoaListResponse
                {
                    Status = CartaoVinculadoPessoaListResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cartoes = new List<CartaoVinculadoPessoaResponse>()
                };
            }
        }

        public CartaoBloqueadoPessoaListResponse GetCartoesBloqueado(string documento, List<int> idProdutos, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                return _pessoaApi.CartoesBloqueado(documento, idProdutos, Token,
                    documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new CartaoBloqueadoPessoaListResponse
                {
                    Status = CartaoBloqueadoPessoaListResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cartoes = new List<CartaoBloqueadoPessoaResponse>()
                };
            }
        }

        public PessoaCartaoResponse GetCartaoFretePorVinculoDaEmpresa(string documento, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                return _pessoaApi.CartoesFretePorEmpresa(documento, Token,
                    documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new PessoaCartaoResponse
                {
                    Status = PessoaCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public List<ProdutoResponse> GetProdutos()
        {
            try
            {
                return _produtoApi.ProdutosGet(Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new List<ProdutoResponse>();
            }
        }

        public ProdutoResponse GetProduto(int id)
        {
            try
            {
                return _produtoApi.ProdutosGet(id, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new ProdutoResponse();
            }
        }

        public HistoricoCartaoPessoaListResponse GetHistoricoCartoes(string documento, List<int> idProdutos)
        {
            try
            {
                return _pessoaApi.HistoricoCartoes(documento, idProdutos, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<HistoricoCartaoPessoaListResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }
                return new HistoricoCartaoPessoaListResponse
                {
                    Status = HistoricoCartaoPessoaListResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cartoes = new List<HistoricoCartaoPessoaResponse>()
                };
            }
        }

        public VincularResponse VincularCartaoPortador(VincularRequest vincularRequest)
        {
            try
            {
                return _operacaoApi.VincularPortador(vincularRequest, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<VincularResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new VincularResponse
                {
                    Status = VincularResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public DesvincularResponse DesvincularCartaoPortador(DesvincularRequest desvincularRequest)
        {
            try
            {
                return _operacaoApi.DesvincularPortador(desvincularRequest, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<DesvincularResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new DesvincularResponse
                {
                    Status = DesvincularResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }
        public CarregarValorResponse CarregarValorCartao(CarregarValorRequest carregarValorRequest)
        {
            try
            {
                return _operacaoApi.Carregar(carregarValorRequest, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CarregarValorResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new CarregarValorResponse
                {
                    Status = CarregarValorResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public TransferirValorCartaoResponse TransferirValorCartao(TransferirValorCartaoRequest transferirValorRequest)
        {
            try
            {
                return _operacaoApi.TransferirParaCartao(transferirValorRequest, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<TransferirValorCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new TransferirValorCartaoResponse
                {
                    Status = TransferirValorCartaoResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public EstornarCargaCartaoResponse EstornarCargaCartao(EstornarCargaCartaoRequest estornarCargaRequest)
        {
            try
            {
                return _operacaoApi.EstornarCarga(estornarCargaRequest, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EstornarCargaCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new EstornarCargaCartaoResponse
                {
                    Status = EstornarCargaCartaoResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public EstornarTransferenciaCartaoResponse EstornarTransferencia(EstornarTransferenciaCartaoRequest estornarTransferenciaRequest)
        {
            try
            {
                return _operacaoApi.EstornarTransferenciaCartao(estornarTransferenciaRequest, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EstornarTransferenciaCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new EstornarTransferenciaCartaoResponse
                {
                    Status = EstornarTransferenciaCartaoResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarSaldoCartaoResponse ConsultarSaldoCartao(ConsultarSaldoCartaoRequest consultarSaldoCartaoRequest, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            try
            {
                return _operacaoApi.ConsultarSaldoCartao(consultarSaldoCartaoRequest, Token,
                    documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarSaldoCartaoResponse
                {
                    Status = ConsultarSaldoCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }
        public ConsultarPrimeiraTransacaoResponse ConsultarPrimeiraTransacao(int identificador, int produto)
        {
            try
            {
                return _operacaoApi.ConsultarPrimeiraTransacao(identificador, produto, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarPrimeiraTransacaoResponse
                {
                    Status = ConsultarPrimeiraTransacaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public VincularCartaoVirtualResponse VincularCartaoVirtual(VincularCartaoVirtualRequest vincularCartaoVirtualRequest)
        {
            try
            {
                return _operacaoApi.VincularCartaoVirtual(vincularCartaoVirtualRequest, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<VincularCartaoVirtualResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new VincularCartaoVirtualResponse
                {
                    Status = VincularCartaoVirtualResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public TransferirValorContaBancariaResponse TransferirValorContaBancaria(TransferirValorContaBancariaRequest transferirValorContaBancariaRequest)
        {
            try
            {
                return _operacaoApi.TransferirParaContaBancaria(transferirValorContaBancariaRequest, Token,
                    DocumentoUsuarioAudit ?? "00000000000", NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<TransferirValorContaBancariaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new TransferirValorContaBancariaResponse
                {
                    Status = TransferirValorContaBancariaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultaCartaoResponse GetCartaoProcessadora(int identificador, int produto)
        {
            try
            {
                return _cartoesWeb.CartoesGet(identificador, produto, GetWebAuthToken());
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return null;
            }
        }

        public IntegrarPessoaResponse IntegrarPessoa(IntegrarPessoaRequest request)
        {
            try
            {
                return _pessoaApi.Integrar(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<IntegrarPessoaResponse>(swaggerException.Response);
                    if (response?.Mensagens.Any() == true)
                    {
                        response.Status = IntegrarPessoaResponseStatus.Falha;
                        foreach (var apiResponse in response.Mensagens)
                            apiResponse.Message = PrefixoMensagemExcecao + apiResponse.Message;
                    }

                    return response;
                }

                return new IntegrarPessoaResponse
                {
                    Status = IntegrarPessoaResponseStatus.Falha,
                    Mensagens = new List<ApiResponseValidation>
                    {
                        new ApiResponseValidation
                        {
                            Message = ServicoIndisponivelResultMessage,
                            Type = ApiResponseValidationType.Error
                        }
                    }
                };
            }
        }

        public List<ConsultarPontoDistribuicaoResponse> GetPontoDistribuicao(List<string> cnpjList)
        {
            try
            {
                return _pessoaApi.PontosDistribuicaoPost(new PontosDistribuicaoGetRequest
                {
                    Cnpjs = cnpjList
                }, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new List<ConsultarPontoDistribuicaoResponse>();
            }
        }

        public PessoasCartoesSaldoResponse ConsultarListaPortadorCartaoComSaldo(CartoesVinculadosListaSaldoRequest request)
        {
            try
            {
                return _operacaoApi.CartoesVinculadosListaComSaldo(request, Token,DocumentoUsuarioAudit ?? "00000000000" , NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw new Exception("Falha ao consultar saldo dos portadores no MS Cartão");
            }
        }

        public RelatorioPortadorCartaoResponse RelatorioPortadorCartaoPorListaDocumento(RelatorioPortadorCartaoRequest request)
        {
            try
            {
                return _relatoriosClient.RelatorioPortadorCartaoPorListaDocumento(request, Token,DocumentoUsuarioAudit ?? "00000000000" , NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                throw new Exception("Falha ao consultar os portadores no MS Cartão");
            }
        }

        public List<PessoasCartoesVinculadosResponse> CartoesVinculadosLista(List<string> cpfList)
        {
            try
            {
                return new List<PessoasCartoesVinculadosResponse> {_pessoaApi.CartoesVinculadosLista(cpfList, Token,DocumentoUsuarioAudit ?? "00000000000" , NomeUsuarioAudit)};
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new List<PessoasCartoesVinculadosResponse>();
            }
        }

        public EnvioRemessaResponse EnviarRemessaCartoes(EnvioRemessaRequest request)
        {
            try
            {
                return _remessaApi.Enviar(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EnvioRemessaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new EnvioRemessaResponse
                {
                    Status = EnvioRemessaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public EnvioRemessaResponse ValidarCartaoRemessa(ValidarCartaoRequest request)
        {
            try
            {
                return _remessaApi.ValidarCartao(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EnvioRemessaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new EnvioRemessaResponse
                {
                    Status = EnvioRemessaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public EnvioRemessaResponse ValidarLoteCartaoRemessa(ValidarCartoesLoteRequest request)
        {
            try
            {
                return _remessaApi.ValidarLoteCartoes(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EnvioRemessaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new EnvioRemessaResponse
                {
                    Status = EnvioRemessaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public BaixaRemessaResponse ReceberRemessaCartoes(BaixaRemessaRequest request)
        {
            try
            {
                return _remessaApi.Baixar(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<BaixaRemessaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new BaixaRemessaResponse
                {
                    Status = BaixaRemessaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarProtocoloResponse ConsultarProcotolo(ConsultarProtocoloRequest request)
        {
            try
            {
                return _operacaoApi.ConsultarProtocolo(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarProtocoloResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarProtocoloResponse
                {
                    Status = ConsultarProtocoloResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarRemessaResponse ConsultarCartoesLote(int loteId)
        {
            try
            {
                return _remessaApi.Consultar(loteId, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarRemessaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarRemessaResponse
                {
                    Status = ConsultarRemessaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cartoes = new List<IdentificadorCartaoRemessa>()
                };
            }
        }

        public ConsultarRemessaEmpresaResponse CarregarRemessaEmpresa(List<string> cnpjList, bool filtrarEmpresaOrigem, DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                var request = new ConsultarRemessasEmpresaRequest
                {
                    CnpjList = cnpjList,
                    FiltrarEmpresaOrigem = filtrarEmpresaOrigem,
                    DataInicio = dataInicio,
                    DataFim = dataFim
                };

                return _remessaApi.ConsultarRemessasEmpresaPost(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarRemessaEmpresaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarRemessaEmpresaResponse
                {
                    Status = ConsultarRemessaEmpresaResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    ConsultarRemessaResponseList = new List<ConsultarRemessaResponse>()
                };
            }
        }

        public RelatorioCartaoApiResponse RelatorioSituacaoCartao(RelatorioCartaoApiRequest request)
        {
            try
            {
                return _cartoesApi.Situacao(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<RelatorioCartaoApiResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new RelatorioCartaoApiResponse
                {
                    Sucesso = false,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Dados = new List<Item>()
                };
            }
        }

        public RelatorioConciliacaoAnaliticoResponse RelatorioConciliacaoAnalitico(RelatorioConciliacaoAnaliticoRequest request)
        {
            try
            {
                return _transacoesClient.ConciliacaoAnalitico(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<RelatorioConciliacaoAnaliticoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new RelatorioConciliacaoAnaliticoResponse
                {
                    Status = RelatorioConciliacaoAnaliticoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Itens = new List<ConciliacaoItem>()
                };
            }
        }

        public RelatorioTransferenciasContaBancariaResponse RelatorioTransferenciasContaBancaria(RelatorioTransferenciasContaBancariaRequest request)
        {
            try
            {
                return _transferenciasClient.ContaBancaria(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<RelatorioTransferenciasContaBancariaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new RelatorioTransferenciasContaBancariaResponse
                {
                    Status = RelatorioTransferenciasContaBancariaResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Itens = new List<ItemTransacao>()
                };
            }
        }

        public ValidarSenhaCartaoResponse ValidarSenhaCartao(ValidarSenhaCartaoRequest request)
        {
            try
            {
                return _operacaoApi.ValidarSenhaCartao(request, Token, DocumentoUsuarioAudit ?? "00000000000", NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ValidarSenhaCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ValidarSenhaCartaoResponse
                {
                    Status = ValidarSenhaCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public AlterarSenhaCartaoResponse AlterarSenhaCartao(AlterarSenhaCartaoRequest request)
        {
            try
            {
                return _operacaoApi.AlterarSenhaCartao(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<AlterarSenhaCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new AlterarSenhaCartaoResponse
                {
                    Status = AlterarSenhaCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public FilteredResultOfMotivoBloqueioModel BuscarMotivosBloquearCartao(int Take, int Page, object Order, List<CustomFilter> Filters)
        {
            try
            {
                return _buscarApi.MotivosBloqueio(Take, Page, null, null, Token,
                    DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new FilteredResultOfMotivoBloqueioModel
                {
                    Data = null,
                    FilteredOptions = new FilteredOptions()
                    {
                        TotalRecords = 0,
                        PageCount = 0
                    }
                };
            }
        }

        public BloquearCartaoResponse BloquearCartao(BloquearCartaoRequest request)
        {
            try
            {
                return _operacaoApi.Bloquear(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<BloquearCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new BloquearCartaoResponse
                {
                    Status = BloquearCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public BloquearCartaoResponse BloquearCartaoParametrizacaoEmpresa(BloquearCartaoRequest request)
        {
            try
            {
                return _operacaoApi.BloquearCartaoParametrizacaoEmpresa(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<BloquearCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new BloquearCartaoResponse
                {
                    Status = BloquearCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }
        public DesbloquearCartaoResponse DesbloquearCartao(DesbloquearCartaoRequest request)
        {
            try
            {
                return _operacaoApi.Desbloquear(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<DesbloquearCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new DesbloquearCartaoResponse
                {
                    Status = DesbloquearCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public IntegrarEmpresaResponse EmpresaIntegrar(IntegrarEmpresaRequest request)
        {
            try
            {
                return _empresaApi.Integrar(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<IntegrarEmpresaResponse>(swaggerException.Response);
                    if (response?.Mensagens.Any() == true)
                    {
                        response.Status = IntegrarEmpresaResponseStatus.Falha;
                        foreach (var apiResponse in response.Mensagens)
                            apiResponse.Message = PrefixoMensagemExcecao + apiResponse.Message;
                        return response;
                    }
                }

                return new IntegrarEmpresaResponse
                {
                    Status = IntegrarEmpresaResponseStatus.Falha,
                    Mensagens = new List<ApiResponseValidation>
                    {
                        new ApiResponseValidation
                        {
                            Message = ServicoIndisponivelResultMessage,
                            Type = ApiResponseValidationType.Error
                        }
                    }
                };
            }
        }

        public List<PessoaContaBancariaResponse> GetContaBancaria(string documento)
        {
            try
            {
                return _pessoaApi.ContasBancariasGet(documento, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new List<PessoaContaBancariaResponse>();
            }
        }

        public ConsultarExtratoResponse ConsultarExtrato(ConsultarExtratoRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            try
            {
                return _operacaoApi.ConsultarExtrato(request, Token, documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarExtratoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarExtratoResponse
                {
                    Status = ConsultarExtratoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Detalhes = new List<ConsultarExtratoDetalheResponse>()
                };
            }
        }

        public ConsultarPessoaDetalhadaResponse ConsultarPortadorDetalhado(string documento)
        {
            try
            {
                return _pessoaApi.GetDetalhesByDocumento(documento, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new ConsultarPessoaDetalhadaResponse();
            }
        }

        public ConsultarSaldoCartaoResponse ConsultarSaldoCartaoAtendimento(ConsultarSaldoCartaoRequest consultarSaldoCartaoRequest)
        {
            try
            {
                return _operacaoApi.ConsultarSaldoCartao(consultarSaldoCartaoRequest, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarSaldoCartaoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarSaldoCartaoResponse
                {
                    Status = ConsultarSaldoCartaoResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public HistoricoCartaoPessoaListResponse GetHistoricoCartoesAtendimento(string documento, List<int> idProdutos)
        {
            try
            {
                return _pessoaApi.HistoricoCartoes(documento, idProdutos, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<HistoricoCartaoPessoaListResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new HistoricoCartaoPessoaListResponse
                {
                    Status = HistoricoCartaoPessoaListResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cartoes = new List<HistoricoCartaoPessoaResponse>()
                };
            }
        }

        public CartaoVinculadoPessoaListResponse GetCartoesVinculadosAtendimento(string documento, List<int> idProdutos)
        {
            try
            {
                return _pessoaApi.CartoesVinculados(documento, idProdutos, Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CartaoVinculadoPessoaListResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new CartaoVinculadoPessoaListResponse
                {
                    Status = CartaoVinculadoPessoaListResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cartoes = new List<CartaoVinculadoPessoaResponse>()
                };
            }
        }

        public ConsultarContasBancariasResponse ContasBancarias(string documento, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                return new ConsultarContasBancariasResponse
                {
                    ContasBancarias = _pessoaApi.ContasBancariasGet(documento, Token, documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit),
                    Status = ConsultarContasBancariasResponseStatus.Sucesso,
                    Mensagem = string.Empty
                };
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarContasBancariasResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarContasBancariasResponse
                {
                    Status = ConsultarContasBancariasResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    ContasBancarias = new List<PessoaContaBancariaResponse>()
                };
            }
        }

        public InativarContaBancariaResponse InativarContaBancaria(InativarContaBancariaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            try
            {
                return _pessoaApi.InativarContaBancaria(request, Token, documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<InativarContaBancariaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new InativarContaBancariaResponse
                {
                    Status = InativarContaBancariaResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarEmpresaResponse ConsultarEmpresa(string cnpj)
        {
            try
            {
                return _empresaApi.Consultar(cnpj ,Token, DocumentoUsuarioAudit ?? "00000000000" , NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarEmpresaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarEmpresaResponse
                {
                    Status = ConsultarEmpresaResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Cnpj = null,
                    RazaoSocial = null
                };
            }
        }

        public ConsultaCartaoAdministradoraResponse AdministradoraCartao(int identificador, int idproduto)
        {
            try
            {
                return _buscarApi.AdministradoraCartao(identificador, idproduto, Token, DocumentoUsuarioAudit ?? "00000000000" , NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new ConsultaCartaoAdministradoraResponse
                {
                    AdministradoraId = null,
                    EmpresaId =  null
                };
            }
        }

        public ResgatarValorResponse ResgatarValor(ResgatarValorRequest request)
        {
            try
            {
                return _operacaoApi.ResgatarValor(request, Token, DocumentoUsuarioAudit ?? "00000000000", NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ResgatarValorResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ResgatarValorResponse
                {
                    Status = ResgatarValorResponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public EstornarResgateValorReponse EstornarResgateValor(EstornarResgateValorRequest request)
        {
            try
            {
                return _operacaoApi.EstornarResgateValor(request, Token, DocumentoUsuarioAudit ?? "00000000000", NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EstornarResgateValorReponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new EstornarResgateValorReponse
                {
                    Status = EstornarResgateValorReponseStatus.Erro,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public GetOrGenerateCartaoAppTokenApiResponse GetOrGenerateTokenEmpresa(string cnpjEmpresa, string appName, int grupoContabililzacaoTransacao)
        {
            try
            {
                return _empresaApi.GetOrGenerateCartaoAppToken(cnpjEmpresa, appName, grupoContabililzacaoTransacao, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<GetOrGenerateCartaoAppTokenApiResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new GetOrGenerateCartaoAppTokenApiResponse
                {
                    Status = GetOrGenerateCartaoAppTokenApiResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarSaldoEmpresaResponse ConsultarSaldoEmpresa(string cnpjEmpresa)
        {
            try
            {
                return _empresaApi.ConsultarSaldoEmpresa(cnpjEmpresa, Token, DocumentoUsuarioAudit ?? "00000000000", NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<GetOrGenerateCartaoAppTokenApiResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarSaldoEmpresaResponse
                {
                    Status = ConsultarSaldoEmpresaResponseStatus.Falha,
                    Mensagem =  mensagem ?? ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarInformacoesPorDadosContaResponse ConsultarInformacoesPorDadosConta(int conta, int produto, int emissor, int sucursal, int grupoAfinidade)
        {
            try
            {
                return _buscarApi.ConsultarInformacoesPorDadosConta(conta, produto, emissor, sucursal, grupoAfinidade, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarInformacoesPorDadosContaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }

                return new ConsultarInformacoesPorDadosContaResponse
                {
                    Sucesso = false,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    Documento = null
                };
            }
        }

        public CancelarCartaoRemessaResponse CancelarCartaoRemessa(CancelarCartaoRemessaRequest cancelarCartaoRemessaRequest)
        {
            return _operacaoApi.CancelarCartaoRemessa(cancelarCartaoRemessaRequest, Token, DocumentoUsuarioAudit ?? "00000000000", NomeUsuarioAudit);
        }
    }
}
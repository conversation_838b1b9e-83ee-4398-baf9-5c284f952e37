using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.WS.Models.ViagemV2.Integracao
{
    public class ViagemV2DadosAnttModel
    {
        public bool? AltoDesempenho { get; set; }

        public bool? DestinacaoComercial { get; set; }

        public int? CodigoTipoCarga { get; set; }

        public int? DistanciaViagem { get; set; }

        public bool? FreteRetorno { get; set; }

        public string CepRetorno { get; set; }

        public int? DistanciaRetorno { get; set; }

        public ValidationResult ValidarEntrada()
        {
            if (CodigoTipoCarga.HasValue)
                if (CodigoTipoCarga.Value < 0)
                    return new ValidationResult().Add("Código do tipo de carga não pode conter um valor negativo.", EFaultType.Error);
            
            if (DistanciaViagem.HasValue)
                if (DistanciaViagem.Value < 0)
                    return new ValidationResult().Add("Distância da viagem não pode conter um valor negativo.", EFaultType.Error);
            
            if (!string.IsNullOrEmpty(CepRetorno))
                if (CepRetorno.Length > 8)
                    return new ValidationResult().Add("CEP de retorno não pode conter mais que 8 caracteres.", EFaultType.Error);
            
            if (DistanciaRetorno.HasValue)
                if (DistanciaRetorno.Value < 0)
                    return new ValidationResult().Add("Distância de retorno da viagem não pode conter um valor negativo.", EFaultType.Error);
            
            return new ValidationResult();
        }
    }
}
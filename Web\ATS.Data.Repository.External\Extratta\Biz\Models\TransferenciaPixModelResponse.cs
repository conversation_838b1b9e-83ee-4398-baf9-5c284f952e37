﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class TransferenciaPixModelResponse
    {
        public TransferenciaPixComprovanteResponse Comprovante { get; set; }
        public string EndToEndIdentification { get; set; }
        public ETransacaoPixStatus? StatusTransacaoPix { get; set; }
        public decimal? Valor { get; set; }
        public string MessageIdentification { get; set; }
        public string Information { get; set; }
        public string CreationDatetime { get; set; }
        public string ScheduleDateTime { get; set; }
        public int? AntiFraudRecommendationCode { get; set; }
        public DateTime? DataTransacao { get; set; }
        public int? TransacaoId { get; set; }
        public string Message { get; set; }
        public string Detail { get; set; }
        public int? Status { get; set; }
        public string Erro { get; set; }
    }
    
    /// <summary>
    /// Usadas tambem nas validacoes do evento e transacao para impedir pagamentos duplicados de pix ja baixados
    /// </summary>
    public enum ETransacaoPixStatus
    {
        
        /// <summary>
        /// Status transitório do TRANSACAO_PIX, quando acabou de ser criada e tá esperando ou a confirmacao do pagamento ou erro
        /// </summary>
        [EnumMember, Description("Aberto")]
        Aberto = 1,
        /// <summary>
        /// Apenas uma TRANSACAO_PIX deve ter o status de confirmada, e isso indica que o pagamento foi efetuado com sucesso e que o evento deve estar baixado
        /// </summary>
        [EnumMember, Description("Confirmado")]
        Confirmado = 2,
        /// <summary>
        /// Qualquer outro erro
        /// </summary>
        [EnumMember, Description("Erro")]
        Erro = 3,
        /// <summary>
        /// Pagamentos que caíram em validacoes que colocam em pendente de aprovacao do gestor.
        /// A existencia de uma transacao pendente deve impedir a criacao de outras transacoes ate ser aprovada ou ter o evento cancelado pelo gestor
        /// </summary>
        [EnumMember, Description("Pendente")]
        Pendente = 4,
        /// <summary>
        /// Pagamentos não processados pelo banco central (status "Cancelada On-Line" na consulta do timeline da conta pix biz)
        /// por alguma divergência das informações enviadas na requisição pra biz na hora do pagamento (casos encontrados: tipo de conta, chave do destinatário pertencente a uma conta invalida)
        /// </summary>
        [EnumMember, Description("Não processado")]
        NaoProcessado = 5,
    }
}
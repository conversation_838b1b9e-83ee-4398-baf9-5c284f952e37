using System.ComponentModel.DataAnnotations;

namespace ATS.Domain.Entities
{
    public class ItensGraficoPorEmpresa
    {
        [Key]
        public int IdEmpresa { get; set; }
        
        public int QuantidadeItensPorEmpresa { get; set; }
        
        public string NomeEmpresa { get; set; }
        
        public string DescricaoTipoAtendimento { get; set; }

        public int QuantidadeTotalItens { get; set; }
        
        public int QuantidadeItensPorDia { get; set; }
        
        public int QuantidadeItensPorTipo { get; set; }
    }
}
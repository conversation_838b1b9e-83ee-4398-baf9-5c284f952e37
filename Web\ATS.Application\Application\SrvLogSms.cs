﻿using ATS.Application.Application.Common;
using ATS.Domain.Grid;
using System;
using System.Collections.Generic;
using ATS.Domain.Models;
using ATS.Domain.Grid;
using ATS.Domain.Entities;
using ATS.Application.Application.Common;

namespace ATS.Application.Application
{
    public class SrvLogSms : AppBase
    {
        public LogSmsGridModel ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, bool isReport = false, int? idEmpresa = null)
        {
            try
            {
                return new LogSmsApp().ConsultarGrid(take, page, order, filters, isReport, idEmpresa);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}

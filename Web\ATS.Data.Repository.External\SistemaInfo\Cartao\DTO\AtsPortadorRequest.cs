﻿using System;

namespace ATS.Data.Repository.External.SistemaInfo.Cartao.DTO
{
    public class AtsPortadorRequest
    {

        public string Nome { get; set; }
        public string NomeFantasia { get; set; }
        public string Documento { get; set; }
        
        public int? CodigoIbge { get; set; }
        public string Endereco { get; set; }        
        public string Bairro { get; set; }
        public string Cep { get; set; }
        public string Complemento { get; set; }
        public string Logradouro { get; set; }
        public string Cidade { get; set; }
        public string Estado { get; set; }
        public int? Numero { get; set; }
            
        public string Sexo { get; set; }
        public string SexoFormatado { get; set; }
        public string Contato { get; set; }
        public string Email { get; set; }
        public string TipoPessoa { get; set; }
        public string TipoPessoaFormatado { get; set; }
        public string Telefone { get; set; }
        
        public string RG { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string DataNascimentoFormatado { get; set; }
        public bool? EquiparadoTac { get; set; }
        public bool DesabilitarBotoesDesvincularCartao { get; set; }
        public bool DesabilitarBotoesVincularCartao { get; set; }
    }
}

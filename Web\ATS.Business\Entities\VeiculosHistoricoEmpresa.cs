﻿namespace ATS.Domain.Entities
{
    public class VeiculosHistoricoEmpresa
    {
        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Placa do veículo
        /// </summary>
        public string Placa { get; set; }

        /// <summary>
        /// CPF do motorista da viagem
        /// </summary>
        public string CpfMotorista { get; set; }

        #region Referências

        public virtual Empresa Empresa { get; set; }

        #endregion
    }
}

﻿using System.Net;
using System;
using Newtonsoft.Json;

namespace ATS.Domain.Models
{
    public class KeycloakOpenIdTokenModel
    {

        private int _expiresIn;
        private Nullable<DateTime> _expiresAt = null;

        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("expires_in")]
        public int ExpiresIn
        {
            get { return _expiresIn; }
            set
            {
                _expiresIn = value;
                _expiresAt = DateTime.Now.AddSeconds(_expiresIn);
            }
        }
        public Nullable<DateTime> ExpiresAt { get { return _expiresAt; } }

        [JsonProperty("refresh_expires_in")]
        public int RefreshExpiresIn { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("session_state")]
        public string SessionState { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }

        [JsonProperty("status_code")]
        public HttpStatusCode StatusCode { get; set; }

    }
}
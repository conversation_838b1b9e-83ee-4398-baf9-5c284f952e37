﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AgendamentoPagamentoFrete : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.EMPRESA", "habilitaragendamentopagamentofrete", c => c<PERSON>(nullable: false));
            AddColumn("dbo.VIAGEM_EVENTO", "dataagendamentopagamento", c => c.DateTime());
        }
        
        public override void Down()
        {
            DropColumn("dbo.VIAGEM_EVENTO", "dataagendamentopagamento");
            DropColumn("dbo.EMPRESA", "habilitaragendamentopagamentofrete");
        }
    }
}

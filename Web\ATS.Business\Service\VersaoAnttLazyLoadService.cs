﻿using System;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class VersaoAnttLazyLoadService : IVersaoAnttLazyLoadService
    {
        private readonly IParametrosService _parametrosService;
        private readonly Lazy<EVersaoAntt> _lazy;

        public VersaoAnttLazyLoadService(IParametrosService parametrosService)
        {
            _parametrosService = parametrosService;
            _lazy = new Lazy<EVersaoAntt>(() =>
            {
                var versaoAntt = _parametrosService.GetVersaoAntt();
                return versaoAntt.HasValue ? (EVersaoAntt)versaoAntt : EVersaoAntt.Versao2;
            });
        }

        public EVersaoAntt Value => _lazy.Value;
    }
}
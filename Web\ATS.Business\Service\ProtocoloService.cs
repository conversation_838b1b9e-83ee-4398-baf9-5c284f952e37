﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Mail;
using ATS.CrossCutting.IoC.Utils;
using IProtocoloRepository = ATS.Domain.Interface.Database.IProtocoloRepository;
using Microsoft.Win32;
using System.Web;
using ATS.CrossCutting.Reports.PagamentoFrete.ListagemPagamentos;
using ATS.CrossCutting.Reports.Protocolo;
using ATS.CrossCutting.Reports.Protocolo.Capa;
using ATS.CrossCutting.Reports.Protocolo.Etiquetas;
using ATS.CrossCutting.Reports.Protocolo.RecebimentoProtocolo;
using ATS.CrossCutting.Reports.Protocolo.TriagemProtocolo;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models.Protocolo;
using Autofac;
using NLog;
using Protocolo = ATS.Domain.Entities.Protocolo;

namespace ATS.Domain.Service
{
    public class ProtocoloService : ServiceBase, IProtocoloService
    {
        private readonly ILifetimeScope _scope;
        private readonly IProtocoloRepository _repository;
        private readonly IProtocoloAnexoRepository _repositoryAnexo;
        private readonly IProtocoloAntecipacaoRepository _repositoryAntecipacao;
        private readonly IProtocoloEventoRepository _repositoryEvento;
        private readonly ICredenciamentoRepository _credenciamentoRepository;
        private readonly IPagamentoConfiguracaoProcessoRepository _pagamentoConfiguracaoProcessoRepository;
        private readonly IPagamentoConfiguracaoRepository _pagamentoConfiguracaoRepository;
        private readonly IProtocoloRepository _protocoloRepository;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IViagemRepository _viagemRepository;
        private readonly IProprietarioService _proprietarioService;
        private readonly ILayoutService _layoutService;
        private readonly IProtocoloEventoRepository _protocoloEventoRepository;
        private readonly IViagemEventoService _viagemEventoService;
        private readonly IViagemEventoDapper _viagemEventoDapper;
        private readonly ILayoutRepository _layoutRepository;
        private readonly IDocumentoRepository _documentoRepository;
        private readonly IEstabelecimentoBaseRepository _estabelecimentoBaseRepository;
        private readonly IMotoristaService _motoristaService;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IEstabelecimentoAssociacaoRepository _estabelecimentoAssociacaoRepository;
        private readonly IEstabelecimentoRepository _estabelecimentoRepository;
        private readonly IMotivoRepository _motivoRepository;
        private readonly IProtocoloAntecipacaoRepository _protocoloAntecipacaoRepository;
        private readonly IEmailService _emailService;
        private readonly IEmpresaService _empresaService;
        private readonly IDataMediaServerService _dataMediaServerService;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IWebhookService _webhookService;
        private readonly WebhookActionDependencies _webhookActionDependencies;
        private readonly IEstabelecimentoService _estabelecimentoService;
        private readonly IPushService _pushService;
        
        private IPagamentoFreteService _pagamentoFreteService => _scope.Resolve<IPagamentoFreteService>();

        public ProtocoloService(ILifetimeScope scope, IProtocoloRepository repository, IProtocoloAnexoRepository repositoryAnexo, IProtocoloAntecipacaoRepository repositoryAntecipacao,
            IProtocoloEventoRepository repositoryEvento, ICredenciamentoRepository credenciamentoRepository, IPagamentoConfiguracaoProcessoRepository pagamentoConfiguracaoProcessoRepository,
            IPagamentoConfiguracaoRepository pagamentoConfiguracaoRepository, IProtocoloRepository protocoloRepository, IViagemEventoRepository viagemEventoRepository,
            IViagemRepository viagemRepository, IProprietarioService proprietarioService, ILayoutService layoutService,
            IProtocoloEventoRepository protocoloEventoRepository, IViagemEventoService viagemEventoService, IViagemEventoDapper viagemEventoDapper,
            ILayoutRepository layoutRepository, IDocumentoRepository documentoRepository, IEstabelecimentoBaseRepository estabelecimentoBaseRepository, IMotoristaService motoristaService,
            IEmpresaRepository empresaRepository, IEstabelecimentoAssociacaoRepository estabelecimentoAssociacaoRepository, IEstabelecimentoRepository estabelecimentoRepository,
            IMotivoRepository motivoRepository, IProtocoloAntecipacaoRepository protocoloAntecipacaoRepository, IEmailService emailService, IEmpresaService empresaService,
            IDataMediaServerService dataMediaServerService, IUsuarioRepository usuarioRepository, IWebhookService webhookService, WebhookActionDependencies webhookActionDependencies,
            IEstabelecimentoService estabelecimentoService, IPushService pushService)
        {
            _scope = scope;
            _repository = repository;
            _repositoryAnexo = repositoryAnexo;
            _repositoryAntecipacao = repositoryAntecipacao;
            _repositoryEvento = repositoryEvento;
            _credenciamentoRepository = credenciamentoRepository;
            _pagamentoConfiguracaoProcessoRepository = pagamentoConfiguracaoProcessoRepository;
            _pagamentoConfiguracaoRepository = pagamentoConfiguracaoRepository;
            _protocoloRepository = protocoloRepository;
            _viagemEventoRepository = viagemEventoRepository;
            _viagemRepository = viagemRepository;
            _proprietarioService = proprietarioService;
            _layoutService = layoutService;
            _protocoloEventoRepository = protocoloEventoRepository;
            _viagemEventoService = viagemEventoService;
            _viagemEventoDapper = viagemEventoDapper;
            _layoutRepository = layoutRepository;
            _documentoRepository = documentoRepository;
            _estabelecimentoBaseRepository = estabelecimentoBaseRepository;
            _motoristaService = motoristaService;
            _empresaRepository = empresaRepository;
            _estabelecimentoAssociacaoRepository = estabelecimentoAssociacaoRepository;
            _estabelecimentoRepository = estabelecimentoRepository;
            _motivoRepository = motivoRepository;
            _protocoloAntecipacaoRepository = protocoloAntecipacaoRepository;
            _emailService = emailService;
            _empresaService = empresaService;
            _dataMediaServerService = dataMediaServerService;
            _usuarioRepository = usuarioRepository;
            _webhookService = webhookService;
            _webhookActionDependencies = webhookActionDependencies;
            _estabelecimentoService = estabelecimentoService;
            _pushService = pushService;
        }

        public ValidationResult Receber(List<int> ids)
        {
            var protocolos = _repository.Where(x => ids.Contains(x.IdProtocolo)).Include(o => o.EstabelecimentoBase)
                .Include(o => o.EstabelecimentoBase.Estabelecimento).ToList();

            foreach (var protocolo in protocolos)
            {
                if (protocolo.EstabelecimentoBase.Estabelecimento != null && protocolo.EstabelecimentoBase
                        .Estabelecimento.FirstOrDefault().PagamentoAntecipado)
                    if (protocolo.StatusProtocolo != EStatusProtocolo.Aprovado)
                        return new ValidationResult().Add(
                            $"O protocolo {protocolo.IdProtocolo} não se encontra aprovado.");

                if (protocolo.DataRecebidoEmpresa != null)
                    continue;

                if (!protocolo.EstabPagamentoAntecipado)
                    protocolo.StatusProtocolo = EStatusProtocolo.Recebido;

                protocolo.DataRecebidoEmpresa = DateTime.Now;

                _repository.Update(protocolo);
            }

            return new ValidationResult();
        }

        public ValidationResult AddOcorrencia(int idProtocoloEvento, int idMotivo, string descricao, Usuario UsuarioLogado)
        {
            var protocoloEvento = _protocoloEventoRepository
                .FirstOrDefault(x => x.IdProtocoloEvento == idProtocoloEvento);

            if (protocoloEvento != null)
            {
                protocoloEvento.IdMotivoOcorrencia = idMotivo;
                protocoloEvento.DetalhamentoOcorrencia = descricao;
                protocoloEvento.OcorrenciaPendente = true;
                protocoloEvento.DataOcorrencia = DateTime.Now;
                protocoloEvento.IdUsuarioCadOcorrencia = UsuarioLogado.IdUsuario; 
                _protocoloEventoRepository.Update(protocoloEvento);

                var protocolo = _protocoloRepository
                    .Get(protocoloEvento.IdProtocolo);

                protocolo.OcorrenciaPendente = true;

                _protocoloRepository.Update(protocolo);
            }

            return new ValidationResult();
        }

        public object ConsultarEventosOriginal(int idProtocolo, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var eventos = _protocoloEventoRepository
                .Include(x => x.ProtocoloEvento_Vinculado)
                .Include(x => x.ProtocoloEvento_Vinculado.Protocolo)
                .Include(x => x.ProtocoloEvento_Vinculado.Protocolo.EstabelecimentoBase)
                .Include(x => x.Protocolo)
                .Include(x => x.ViagemEvento)
                .Where(x => x.IdProtocolo == idProtocolo);

            eventos = string.IsNullOrWhiteSpace(order?.Campo)
                ? eventos.OrderBy(x => x.ProtocoloEvento_Vinculado.Protocolo.IdProtocolo)
                : eventos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            return new
            {
                totalItems = eventos.Count(),
                items = eventos.ToList().Skip((page - 1) * take).Take(take).Select(x => x).Select(x => new
                {
                    TipoEventoViagem = x.ViagemEvento.TipoEventoViagem.DescriptionAttr(),
                    x.ViagemEvento.ValorTotalPagamento,
                    Estabelecimento = x.ProtocoloEvento_Vinculado.Protocolo.EstabelecimentoBase.Descricao,
                    x.ProtocoloEvento_Vinculado.Protocolo.IdProtocolo
                })
            };
        }

        public List<RelatorioEventosVinculados> ConsultarEventosOriginalReport(int idProtocolo, int take, int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            var eventos = _protocoloEventoRepository
                .Include(x => x.ProtocoloEvento_Vinculado)
                .Include(x => x.ProtocoloEvento_Vinculado.Protocolo)
                .Include(x => x.ProtocoloEvento_Vinculado.Protocolo.EstabelecimentoBase)
                .Include(x => x.Protocolo)
                .Include(x => x.ViagemEvento)
                .Where(x => x.IdProtocolo == idProtocolo);

            eventos = string.IsNullOrWhiteSpace(order?.Campo)
                ? eventos.OrderBy(x => x.ProtocoloEvento_Vinculado.Protocolo.IdProtocolo)
                : eventos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            return eventos.ToList().Select(x => new RelatorioEventosVinculados()
            {
                Evento = x.ViagemEvento.TipoEventoViagem.DescriptionAttr(),
                Valor = x.ViagemEvento.ValorTotalPagamento,
                Estabelecimento = x.ProtocoloEvento_Vinculado.Protocolo.EstabelecimentoBase.Descricao,
                IdProtocolo = x.ProtocoloEvento_Vinculado.Protocolo.IdProtocolo
            }).ToList();
        }


        public PagamentoFreteModel ConsultarPorToken(string token, Usuario usuarioLogado, List<int> idsEstabelecimentosUsuario, int? idProtocolo)
        {
            var ret = _pagamentoFreteService.ConsultarPorToken(token, usuarioLogado.CPFCNPJ, usuarioLogado.Nome,
                idsEstabelecimentosUsuario);

            if (ret == null)
                return null;

            ProtocoloEvento evento = null;
            var eventoQyBase = _repositoryEvento                
                .Include(x => x.ViagemEvento)
                .Include(x => x.ViagemEvento.EstabelecimentoBase)
                .Include(x => x.ViagemEvento.Viagem)
                .Include(x => x.ViagemEvento.Viagem.ViagemRegras)
                .Include(x => x.Protocolo)
                .Include(x => x.MotivoOcorrencia);

            if (idProtocolo != null) // recebeu codigo            
                evento = eventoQyBase.Where(pe => pe.IdProtocolo == idProtocolo &&
                                                  pe.IdViagemEvento == ret.Evento.IdViagemEvento)
                    .FirstOrDefault();                                        

            if (evento == null)            
                evento = eventoQyBase.Where(x =>
                        x.IdViagemEvento == ret.Evento.IdViagemEvento &&
                        x.Protocolo.StatusProtocolo != EStatusProtocolo.Rejeitado)
                    .OrderByDescending(x => x.IdProtocoloEvento)
                    .FirstOrDefault();            

            if (evento != null)
            {
                var eventoAbono = _viagemEventoRepository
                    .FirstOrDefault(x =>
                        x.IdViagemEventoOrigem == evento.IdViagemEvento &&
                        (x.Status == EStatusViagemEvento.Aberto || x.IdMotivoRejeicaoAbono != null));

                ret.Evento.ValorIRRF = evento.IRRPF;
                ret.Evento.ValorINSS = evento.INSS;
                ret.Evento.ValorIRRF = evento.IRRPF;
                ret.ValorPedagio = evento.ViagemEvento.Viagem.ValorPedagio;
                ret.Evento.ValorPagamento = evento.ValorPagamento ?? 0;
                ret.Evento.ValorTotalPagamentoCalculado = evento.ViagemEvento.ValorTotalPagamentoCalculado ?? 0;
                ret.EventoAnalisado = evento.EventoAnalisado;
                ret.Rejeitado = eventoAbono?.IdMotivoRejeicaoAbono != null;
                //ret.Evento.ValorTotalPagamento = evento.ValorTotalPagamento ?? 0;
                ret.Evento.ValorBruto = evento.ValorBruto;
                ret.Evento.QuebraMercadoria = evento.ValorQuebraMercadoria ?? 0;
                ret.Evento.QuebraMercadoriaCalculada = evento.ViagemEvento.Viagem.ValorQuebraMercadoriaCalculado ?? 0;
                ret.Evento.DifFreteMotorista = evento.ValorDifFreteMotorista ?? 0;
                //ret.Evento.PesoChegada = evento.PesoChegada ?? 0;
                ret.Evento.TipoEvento = (int?) evento.ViagemEvento?.TipoEventoViagem;
                ret.Evento.IdProtocoloEvento = evento.IdProtocoloEvento;
                ret.IdMotivoOcorrencia = evento.IdMotivoOcorrencia;
                ret.DetalhamentoOcorrencia = evento.DetalhamentoOcorrencia;
                ret.HasDesconto = evento.ValorDesconto != null && evento.ValorDesconto != 0;
                ret.HasProtocolo = evento.Status == EStatusProtocoloEvento.Aprovado;
                ret.ValorEvento = evento.ValorTotalPagamento ?? 0;
                ret.CreditarAbono = eventoAbono != null;
                ret.IdViagemEVentoAbono = eventoAbono?.IdViagemEvento ?? 0;
                ret.IdProtocolo = evento.IdProtocolo;
                ret.StatusProtocoloEvento = evento.Status;
                ret.StatusViagemEvento = evento.ViagemEvento?.Status;
                ret.PagamentoAntecipado = evento.Protocolo?.EstabPagamentoAntecipado;
                ret.DescricaoOcorrencia = evento.MotivoOcorrencia?.Descricao;
                ret.EstabelecimentoDePagamento = evento.ViagemEvento?.EstabelecimentoBase?.Descricao;
                ret.FreteLotacao = evento.ViagemEvento.Viagem.ViagemRegras.FirstOrDefault()?.FreteLotacao ?? false;
                ret.AbonoAnalisado = evento.Protocolo?.ProtocoloEventos?.LastOrDefault()?.AnaliseAbono !=
                                     EStatusAnaliseAbono.NaoAnalisado &&
                                     evento.Protocolo?.ProtocoloEventos?.LastOrDefault()?.AnaliseAbono != null;
                ret.DataDescargaStr = evento.ViagemEvento?.Viagem?.DataDescarga?.ToShortDateString();
                ret.Evento.ModificouFormaPagamentoCartaFreteParaCartao =
                    evento.ViagemEvento.ModificouFormaPagamentoCartaFreteParaCartao;
                ret.Evento.ModificouFormaPagamentoCartaoParaCartaFrete =
                    evento.ViagemEvento.ModificouFormaPagamentoCartaoParaCartaFrete;

                if (ret.TipoEvento.Valor == ETipoEventoViagem.Saldo)
                {
                    ret.Saldo = evento.ValorPagamento;
                }
            }

            return ret;
        }

        public KeyValuePair<ValidationResult, int?> Add(Protocolo protocolo, int? IdUsuario)
        {
            var validations = IsValidToCrud(protocolo, EProcesso.Create);

            if (!validations.IsValid)
                return new KeyValuePair<ValidationResult, int?>(validations, null);
            
            var viagemRepository = _viagemEventoRepository;
            
            //IdViagemEvento as quais serão vinculados o protocolo recem gerado.
            var viagensEventosVincularProtocolo = new List<int>();
            
            if (protocolo.ProtocoloEventos != null && protocolo.ProtocoloEventos.Any())
            {
                var isCartaFrete = false;
                var isMeioHomologado = false;
                var possuiEventoReincidente = false;
                
                foreach (var protocoloEvento in protocolo.ProtocoloEventos)
                {
                    var eventoReincidente = _viagemEventoService.IsReicidente(protocoloEvento.IdViagemEvento);

                    if (eventoReincidente)
                        possuiEventoReincidente = true;                   
                    
                    var habilitaPagamentoCartao = _viagemEventoService.GetTipoPagamento(protocoloEvento.IdViagemEvento);
                    
                    if (habilitaPagamentoCartao)
                        isMeioHomologado = true;
                    else
                        isCartaFrete = true;
                    
                    viagensEventosVincularProtocolo.Add(protocoloEvento.IdViagemEvento);
                }

                protocolo.PossuiEventoReincidente = possuiEventoReincidente;
                
                if (isCartaFrete && isMeioHomologado)
                    protocolo.TiposPagamentos = ETiposPagamentos.CFMH;
                else if (isCartaFrete)
                    protocolo.TiposPagamentos = ETiposPagamentos.CartaFrete;
                else
                    protocolo.TiposPagamentos = ETiposPagamentos.MeioHomologado;
            }

            _repository.Add(protocolo);                                                    
            
            foreach (var @event in protocolo.ProtocoloEventos)
            {
                if (IdUsuario != null)
                {                                        
                    if (protocolo.OcorrenciaPendente)
                    {
                        @event.OcorrenciaPendente = false;
                        @event.DataBaixaOcorrencia = DateTime.Now;
                        @event.IdUsuarioBaixaOcorrencia = IdUsuario;                        
                    }
                }    
                
                var eventoRepository = _protocoloEventoRepository;                                                
                var evento = eventoRepository.Where(x =>
                        x.IdViagemEvento == @event.IdViagemEvento && x.OcorrenciaPendente && x.Status == EStatusProtocoloEvento.Rejeitado)
                    .OrderByDescending(c => c.IdProtocolo).FirstOrDefault();
                                
                if (evento != null)            
                    if (evento.OcorrenciaPendente)
                    {
                        evento.Detalhamento = "";
                        evento.DetalhamentoOcorrencia = "";
                        evento.IdMotivoOcorrencia = null;
                        evento.OcorrenciaPendente = false;
                        evento.DataOcorrencia = null;
                        eventoRepository.Update(evento);

                        var protocoloRepository = _protocoloRepository;
                        var prot = protocoloRepository
                            .FirstOrDefault(x => x.IdProtocolo == evento.IdProtocolo);
                        prot.OcorrenciaPendente = false;
                        protocoloRepository.Update(prot);
                    }
            }

            _viagemEventoDapper
                .VincularProtocoloAosEventos(viagensEventosVincularProtocolo, protocolo.IdProtocolo);

            string nomeAplicativo = string.Empty;
            var nomeApp = _layoutRepository
                .Find(x => x.IdEmpresa == protocolo.IdEmpresa)
                .Select(x => x.NomeAplicativo)
                .FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(nomeApp))
                nomeAplicativo = nomeApp;

            var validation = EnviarNotificacaoProtocolo(protocolo,
                protocolo.EstabelecimentoBase?.EmailProtocolo, protocolo.Empresa?.NomeFantasia, protocolo.Empresa?.Logo, nomeAplicativo);

            var tiposEventos = _viagemEventoRepository
                .Find(x => viagensEventosVincularProtocolo.Contains(x.IdViagemEvento)).Select(x => x.TipoEventoViagem)
                .ToList()
                .Select(x => x.DescriptionAttr())
                .Distinct();
            
            protocolo.TiposEventos = tiposEventos.Aggregate((i, j) => i + " / " + j);
            
            _repository.Update(protocolo);
            
            return new KeyValuePair<ValidationResult, int?>(validation, protocolo.IdProtocolo);
        }

        public ValidationResult SetAnalisado(int? IdProtocoloEvento, bool analisado)
        {
            if (IdProtocoloEvento == null)
                throw new Exception("Informe um protocolo para continuar!");

            var protocoloRepository_ = _protocoloEventoRepository;

            var protocolo = protocoloRepository_.Get(IdProtocoloEvento.Value);

            if (protocolo == null)
                throw new Exception("Informe um protocolo para continuar!");

            protocolo.EventoAnalisado = analisado;

            protocoloRepository_.Update(protocolo);

            return new ValidationResult();
        }

        public Protocolo Get(int idProtocolo)
        {
            return _repository.Get(idProtocolo);
        }

        public List<Protocolo> Get(List<int> idsProtocolo)
        {
            return _repository.Find(x => idsProtocolo.Contains(x.IdProtocolo))
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.Empresa)
                .ToList();
        }

        public IQueryable<Protocolo> GetQuery(List<int> idsProtocolo)
        {
            var protocolo = _protocoloRepository
                .Where(x => idsProtocolo.Contains(x.IdProtocolo)
                );

            return protocolo;
        }

        public void EmAnalise(int idProtocolo)
        {
            var protocolo = _repository.FirstOrDefault(x => x.IdProtocolo == idProtocolo);
            if (protocolo == null)
                throw new Exception($"Nenhum protocolo encontrado para o id {idProtocolo}!");

            protocolo.StatusProtocolo = EStatusProtocolo.EmAnalise;

            _repository.Update(protocolo);
        }

        public Protocolo GetComInclude(int idProtocolo)
        {
            var found = _repository.Find(x => x.IdProtocolo == idProtocolo)
                .Include(x => x.Empresa)
                .Include(x => x.Empresa.PagamentoConfiguracao)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloAnexos)
                .Include(x => x.ProtocoloAnexos.Select(o => o.Documento))
                .Include(x => x.ProtocoloAntecipacoes)
                .FirstOrDefault();

            if (found == null)
                throw new Exception($"Protocolo {idProtocolo} não encontrado!");

            return found;
        }

        public List<Protocolo> GetProtocolos(List<int> idsProtocolo)
        {
            return _repository
                .Include(x => x.EmpresaDestinatario)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoDestinatario)
                .Where(x => idsProtocolo.Contains(x.IdProtocolo))
                .ToList();
        }

        public object ConsultarDescontosPorId(int idProtocoloEvento)
        {
            var protocoloEvento = _repositoryEvento
                .Include(x => x.MotivoDesconto)
                .FirstOrDefault(x => x.IdProtocoloEvento == idProtocoloEvento);

            if (protocoloEvento == null || (protocoloEvento != null && !protocoloEvento.ValorDesconto.HasValue))
                return new { };

            return new
            {
                protocoloEvento.ValorDesconto,
                DescricaoMotivoDesconto = protocoloEvento?.MotivoDesconto?.Descricao,
                protocoloEvento.IdMotivoDesconto,
                protocoloEvento.ObservacaoDesconto,
                protocoloEvento.TokenAnexoDesconto,
                protocoloEvento.PesoChegada
            };
        }

        public IEnumerable<Protocolo> GetAll()
        {
            return _repository.GetAll();
        }

        public ValidationResult Update(Protocolo protocolo)
        {
            var validations = IsValidToCrud(protocolo, EProcesso.Update);

            // Updates all events related to this protocol.
            if (validations.IsValid)
            {
                _repository.Update(protocolo);

                var viagemRepository = _viagemEventoRepository;
                var protocoloEvents = viagemRepository.Find(x => x.IdProtocolo == protocolo.IdProtocolo);

                // Set up all relationships with events
                foreach (var @event in protocolo.ProtocoloEventos)
                {
                    var document =
                        viagemRepository.FirstOrDefault(x => x.IdViagemEvento == @event.IdViagemEvento);

                    if (document != null)
                    {
                        document.IdProtocolo = @event.IdProtocolo;
                        viagemRepository.Update(document);
                    }
                }
            }

            return validations;
        }

        public List<string> ConsultarPagamentosPorProtocolo(int IdProtocolo)
        {
            var protocoloEventoRepository = _protocoloEventoRepository;

            var pagamentos = protocoloEventoRepository
                .Find(x => x.IdProtocolo == IdProtocolo)
                .Include(x => x.ViagemEvento)
                .Select(x => x.ViagemEvento.Token)
                .ToList();

            return pagamentos;
        }

        public List<object> ConsultarDocumentosProtocolo(int idEmpresa, int? idProtocolo)
        {
            var confgiruacaoRepository = _pagamentoConfiguracaoRepository;
            var configuracaoProcessoRepository = _pagamentoConfiguracaoProcessoRepository;
            var documentRepository = _documentoRepository;
            var retorno = new List<object>();

            //Seguindo o mesmo padrão de validação conforme já realizado mais abaixo.
            var configuracao =
                confgiruacaoRepository.FirstOrDefault(x => x.IdEmpresa == idEmpresa && x.Ativo && x.IdFilial == null);

            var configuracaoProcesso = configuracao != null
                ? configuracaoProcessoRepository.Find(x => x.IdConfiguracao == configuracao.IdPagamentoConfiguracao
                                                           && x.Processo == EProcessoPgtoFrete.Protocolo)
                    .Include(y => y.Documento)
                    .ToList()
                : null;

            if (configuracao == null || configuracaoProcesso == null)
                return retorno;

            var protocolo = idProtocolo.HasValue
                ? _repository
                    .Find(x => x.IdProtocolo == idProtocolo)
                    .Include(x => x.ProtocoloAnexos)
                    .ToList()
                : null;

            var anexos = protocolo != null ? protocolo.FirstOrDefault().ProtocoloAnexos : null;

            foreach (var item in configuracaoProcesso)
            {
                var doc = documentRepository.Get(item.IdDocumento);
                if (doc == null)
                    continue;

                var anexo = idProtocolo.HasValue
                    ? anexos.FirstOrDefault(x => x.IdDocumento == doc.IdDocumento && x.IdProtocolo == idProtocolo.Value)
                    : null;

                retorno.Add(new
                {
                    doc?.IdDocumento,
                    doc?.Descricao,
                    PossuiValidade = doc?.PossuirValidade,
                    anexo?.IdProtocolo,
                    StatusOk = anexo != null,
                    TokenAnexo = anexo?.Token,
                    idProtocoloAnexo = anexo?.IdProtocoloAnexo
                });
            }

            return retorno;
        }

        public Object GetMotivosProtocoloEvento(int IdViagemEvento)
        {
            var motivoEvento = _protocoloEventoRepository
                .Include(x => x.Motivo)
                .Where(x => x.IdViagemEvento == IdViagemEvento && x.Status == EStatusProtocoloEvento.Rejeitado)
                .Select(x => new {x.Motivo.IdMotivo, x.Motivo.Descricao, x.Detalhamento});

            return motivoEvento.ToList();
        }

        public bool IsRejected(int IdViagemEvento)
        {
            var _repositoryViagemEvento = _protocoloEventoRepository;


            return !_repositoryViagemEvento.Any(x =>
                       x.IdViagemEvento == IdViagemEvento && x.Status == EStatusProtocoloEvento.Aprovado)
                   && _repositoryViagemEvento.Any(x =>
                       x.IdViagemEvento == IdViagemEvento && x.Status == EStatusProtocoloEvento.Rejeitado);
        }

        public bool ViagemEventoNotAprovadoOuRejeitado(int IdViagemEvento)
        {
            var _repositoryProtocoloEvento = _protocoloEventoRepository;

            var hasAprovado = _repositoryProtocoloEvento.Any(x =>
                x.IdViagemEvento == IdViagemEvento && x.Status == EStatusProtocoloEvento.Aprovado);
            if (hasAprovado)
                return false;
            else
            {
                var hasRejeitado = _repositoryProtocoloEvento.Any(x =>
                    x.IdViagemEvento == IdViagemEvento && x.Status == EStatusProtocoloEvento.Rejeitado);

                return hasRejeitado == true;
            }
        }

        public object ConsultarGrid(int? idEstabelecimento, int? idEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var pagamentoService = _pagamentoFreteService;

            var estabelecimentoBase = _estabelecimentoBaseRepository
                .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento);

            if (estabelecimentoBase == null && idEstabelecimento > 0)
                throw new Exception("Estabelecimento base não foi encontrado!");

            var pagamentos = _viagemEventoRepository
                .Include(x => x.Viagem)
                .Include(x => x.ViagemDocumentos)
                .Include(x => x.Protocolo)
                .Where(x => x.Status == EStatusViagemEvento.Baixado
                            && x.OrigemPagamento != EOrigemIntegracao.TMS
                            && x.IdProtocolo == null
                            && x.IdEstabelecimentoBase == idEstabelecimento
                            && ((x.ValorPagamento > 0) ||
                                (x.TipoEventoViagem == ETipoEventoViagem.Adiantamento && !x.HabilitarPagamentoCartao &&
                                 !x.Viagem.PedagioBaixado)));

            if (idEmpresa.HasValue)
                pagamentos = pagamentos.Where(x => x.IdEmpresa == idEmpresa);

            pagamentos = pagamentos.AplicarFiltrosDinamicos(filters);

            pagamentos = string.IsNullOrWhiteSpace(order?.Campo)
                ? pagamentos.OrderByDescending(x => x.DataHoraPagamento)
                : pagamentos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            int totalItens = pagamentos.Count();

            var pagamentosAgrupados = pagamentos.GroupBy(o => o.TipoEventoViagem);

            pagamentos = pagamentos.Skip((page - 1) * take).Take(take).Select(x => x);

            var items = new List<PagamentoModel>();
            foreach (var pagamento in pagamentos)
            {
                var motorista = _motoristaService.GetPorCpf(pagamento.Viagem?.CPFMotorista);
                var somarPedagio =
                    pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(pagamento, pagamento.Viagem);

                var item = new PagamentoModel
                {
                    IdViagemEvento = pagamento.IdViagemEvento,
                    Token = pagamento.Token,
                    DataHoraPagamento = pagamento.DataHoraPagamento.ToString(),
                    Evento = EnumHelpers.GetDescription(pagamento.TipoEventoViagem),
                    HabilitarPagamentoCartao = pagamento.HabilitarPagamentoCartao,
                    PagoCartao = pagamento.HabilitarPagamentoCartao ? "Sim" : "Não",
                    EventoInt = pagamento.TipoEventoViagem,
                    ValorPagamento = (pagamento.ValorTotalPagamento ?? 0) +
                                     (somarPedagio ? pagamento.Viagem.ValorPedagio : 0),
                    Valor = (pagamento.ValorTotalPagamento ?? 0) + (somarPedagio ? pagamento.Viagem.ValorPedagio : 0),
                    Status = !IsRejected(pagamento.IdViagemEvento)
                        ? EnumHelpers.GetDescription(pagamento.Status)
                        : "Rejeitado",
                    MotoristaNome = !string.IsNullOrWhiteSpace(motorista?.Nome)
                        ? motorista?.Nome
                        : (pagamento.Viagem.NomeMotorista != null ? pagamento.Viagem.NomeMotorista : "Não informado."),
                    MotoristaCNH = !string.IsNullOrWhiteSpace(motorista?.CNH)
                        ? motorista?.CNH
                        : (pagamento.Viagem.CNHMotorista != null ? pagamento.Viagem.CNHMotorista : "Não informado."),
                    Placa = pagamento.Viagem.Placa != null ? pagamento.Viagem.Placa.ToPlacaFormato() : "Não informado.",
                    Motivos = GetMotivosProtocoloEvento(pagamento.IdViagemEvento),
                    IsRejected = IsRejected(pagamento.IdViagemEvento),
                    PodeMudarAnexo = pagamento.IdProtocolo.HasValue
                        ? (pagamento.Protocolo.StatusProtocolo == EStatusProtocolo.Rejeitado)
                        : true,
                    HasAnexo = pagamento.ViagemDocumentos != null && pagamento.ViagemDocumentos.Any(),
                    NumeroCTE = pagamento.Viagem?.DocumentoCliente
                };

                items.Add(item);
            }

            return new
            {
                totalItems = totalItens,
                items,
                totalizadores = GetTotalizadoresGridGeracaoProtocolo(pagamentosAgrupados)
            };
        }

        public byte[] GerarRelatorioGrid(int? idEstabelecimento, int? idEmpresa, OrderFilters order,
            List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var listaDados = new List<RelatorioListagemPagamentosDataType>();
            var pagamentos = GetDataToGridAndReport(idEstabelecimento, idEmpresa, order, filters);

            foreach (var pagamento in pagamentos)
            {
                listaDados.Add(new RelatorioListagemPagamentosDataType
                {
                    CteSerie = pagamento.NumeroCTE,
                    DataPagamento = pagamento.DataHoraPagamento,
                    Evento = pagamento.Evento,
                    PagaNoCartao = pagamento.PagoCartao,
                    Status = pagamento.Status,
                    Token = pagamento.Token,
                    Valor = $"R$ {pagamento.ValorPagamento:N}"
                });
            }

            return new RelatorioListagemPagamentos().GetReport(listaDados, tipoArquivo, logo);
        }


        public List<PagamentoModel> GetDataToGridAndReport(int? idEstabelecimento, int? idEmpresa, OrderFilters order,
            List<QueryFilters> filters, int? page = null, int? take = null)
        {
            var pagamentoService = _pagamentoFreteService;

            var estabelecimentoBase = _estabelecimentoBaseRepository
                .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento);

            if (estabelecimentoBase == null && idEstabelecimento > 0)
                throw new Exception("Estabelecimento base não foi encontrado!");

            var pagamentos = _viagemEventoRepository
                .Find(x => x.Status == EStatusViagemEvento.Baixado
                           && x.OrigemPagamento != EOrigemIntegracao.TMS
                           && x.IdProtocolo == null
                           && x.IdEstabelecimentoBase == idEstabelecimento
                           && ((x.ValorPagamento > 0) ||
                               (x.TipoEventoViagem == ETipoEventoViagem.Adiantamento && !x.HabilitarPagamentoCartao &&
                                !x.Viagem.PedagioBaixado)))
                .Include(x => x.Viagem)
                .Include(x => x.ViagemDocumentos)
                .Include(x => x.Protocolo);

            if (idEmpresa.HasValue)
                pagamentos = pagamentos.Where(x => x.IdEmpresa == idEmpresa);

            pagamentos = string.IsNullOrWhiteSpace(order?.Campo)
                ? pagamentos.OrderBy(x => x.DataHoraPagamento)
                : pagamentos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            pagamentos = pagamentos.AplicarFiltrosDinamicos(filters);

            if (take != null && page != null)
                pagamentos = pagamentos.Skip((page.Value - 1) * take.Value).Take(take.Value).Select(x => x);

            var items = new List<PagamentoModel>();
            foreach (var pagamento in pagamentos)
            {
                var motorista = _motoristaService.GetPorCpf(pagamento.Viagem?.CPFMotorista);
                var somarPedagio =
                    pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(pagamento, pagamento.Viagem);

                var item = new PagamentoModel
                {
                    IdViagemEvento = pagamento.IdViagemEvento,
                    Token = pagamento.Token,
                    DataHoraPagamento = pagamento.DataHoraPagamento.ToString(),
                    Evento = EnumHelpers.GetDescription(pagamento.TipoEventoViagem),
                    HabilitarPagamentoCartao = pagamento.HabilitarPagamentoCartao,
                    PagoCartao = pagamento.HabilitarPagamentoCartao ? "Sim" : "Não",
                    EventoInt = pagamento.TipoEventoViagem,
                    ValorPagamento = (pagamento.ValorTotalPagamento ?? 0) +
                                     (somarPedagio ? pagamento.Viagem.ValorPedagio : 0),
                    Valor = (pagamento.ValorTotalPagamento ?? 0) + (somarPedagio ? pagamento.Viagem.ValorPedagio : 0),
                    Status = !IsRejected(pagamento.IdViagemEvento)
                        ? EnumHelpers.GetDescription(pagamento.Status)
                        : "Rejeitado",
                    MotoristaNome = !string.IsNullOrWhiteSpace(motorista?.Nome)
                        ? motorista?.Nome
                        : (pagamento.Viagem.NomeMotorista != null ? pagamento.Viagem.NomeMotorista : "Não informado."),
                    MotoristaCNH = !string.IsNullOrWhiteSpace(motorista?.CNH)
                        ? motorista?.CNH
                        : (pagamento.Viagem.CNHMotorista != null ? pagamento.Viagem.CNHMotorista : "Não informado."),
                    Placa = pagamento.Viagem.Placa != null ? pagamento.Viagem.Placa.ToPlacaFormato() : "Não informado.",
                    Motivos = GetMotivosProtocoloEvento(pagamento.IdViagemEvento),
                    IsRejected = IsRejected(pagamento.IdViagemEvento),
                    PodeMudarAnexo = pagamento.IdProtocolo.HasValue
                        ? (pagamento.Protocolo.StatusProtocolo == EStatusProtocolo.Rejeitado)
                        : true,
                    HasAnexo = pagamento.ViagemDocumentos != null && pagamento.ViagemDocumentos.Any(),
                    NumeroCTE = pagamento.Viagem?.NumeroDocumento
                };

                items.Add(item);
            }

            return items;
        }

        private void ValidateDocuments(List<ProtocoloAnexo> anexos, int idEmpresa, ref ValidationResult validations)
        {
            if (anexos == null) return;
            // Implementamdo apenas por empresa nesse primeiro momento.
            //Deve ser analisado o comportamento por filial.
            var configuracao = _pagamentoConfiguracaoRepository
                .FirstOrDefault(x => x.IdEmpresa == idEmpresa && x.IdFilial == null && x.Ativo);

            if (configuracao != null)
            {
                var configuracaoDocumento = _pagamentoConfiguracaoProcessoRepository
                    .Include(x => x.Documento)
                    .Where(x => x.IdConfiguracao == configuracao.IdPagamentoConfiguracao &&
                                x.Processo == EProcessoPgtoFrete.Protocolo && configuracao.Ativo);

                if (configuracaoDocumento != null)
                    foreach (var documento in configuracaoDocumento)
                        if (anexos.All(x => x.IdDocumento != documento.IdDocumento))
                            validations.Add(
                                $"Não indentificamos o anexo do documento: {documento.Documento.Descricao} !");
            }
        }

        public ValidationResult IsValidToCrud(Protocolo protocolo, EProcesso operacao)
        {
            var validations = new ValidationResult();

            var empresa = _empresaRepository.Where(x => x.IdEmpresa == protocolo.IdEmpresa).Any();

            if (empresa == false)
                validations.Add("Informe uma empresa válida para continuar!");

            var estabelecimento = _estabelecimentoBaseRepository.Find(x => x.IdEstabelecimento == protocolo.IdEstabelecimentoBase).Any();

            if (estabelecimento == false)
                validations.Add("Informe um estabelecimento base para continuar!");

            #region Document's validations

            ValidateDocuments(protocolo.ProtocoloAnexos?.ToList(), protocolo.IdEmpresa, ref validations);

            if (protocolo.ProtocoloEventos != null && protocolo.ProtocoloEventos.Any())
            {
                var idsViagensEventos = protocolo.ProtocoloEventos.Select(x => x.IdViagemEvento).ToList();
                var viagemEventos = _viagemEventoRepository
                    .Find(x => idsViagensEventos.Any(y => y == x.IdViagemEvento))
                    .Include(x => x.ViagemDocumentos);

                foreach (var item in viagemEventos)
                {
                    if (item.ViagemDocumentos == null || !item.ViagemDocumentos.Any())
                        continue;

                    foreach (var viagemDocumentos in item.ViagemDocumentos)
                    {
                        if (viagemDocumentos.ObrigaAnexo && string.IsNullOrWhiteSpace(viagemDocumentos.TokenAnexo))
                            validations.Add($"{viagemDocumentos.Descricao} não foi anexado para o evento {item.Token}");
                    }
                }
            }

            #endregion

            if ((protocolo.ProtocoloEventos == null || protocolo.ProtocoloEventos.Count() == 0) &&
                operacao == EProcesso.Create)
                validations.Add("Nenhum pagamento foi informado!");

            if (protocolo.IdProtocolo == 0)
            {
                List<ProtocoloErroDTO> tokensDuplicados = new List<ProtocoloErroDTO>();
                foreach (var evento in protocolo.ProtocoloEventos)
                {
                    var protocoloEvento = GetByIdViagemEvento(evento.IdViagemEvento)
                        .Where(x => (x.Status == EStatusProtocoloEvento.Gerado || x.Status == EStatusProtocoloEvento.Aprovado)
                                    && x.Protocolo.IdEstabelecimentoBase == protocolo.IdEstabelecimentoBase)
                        .Select(x => new ProtocoloErroDTO
                        {
                            Status = x.Status,
                            IdProtocolo = x.IdProtocolo,
                            Token = x.ViagemEvento.Token
                        })
                        .FirstOrDefault();

                    if (protocoloEvento != null)
                        tokensDuplicados.Add(protocoloEvento);
                }

                if (tokensDuplicados.Any())
                    validations.Add(tokensDuplicados.Count == 1
                        ? $"O token {tokensDuplicados.Select(x => x.Token).First()} já foi {tokensDuplicados.Select(x => x.Status).First().GetDescription().ToLower()} no protocolo {tokensDuplicados.Select(x => x.IdProtocolo).First()}, desmarque o token repetido e tente novamente"
                        : $"Os tokens {string.Join(", ", tokensDuplicados.Select(x => x.Token).ToList())} já foram processados no(s) protocolo(s) {string.Join(", ", tokensDuplicados.Select(x => x.IdProtocolo).ToList().Distinct())}, desmarque os tokens repetidos e tente novamente");
            }

            return validations;
        }

        public object ConsultarAnexos(int idProtocolo)
        {
            return _repositoryAnexo.Find(x => x.IdProtocolo == idProtocolo).Include(x => x.Documento).Select(x => new
            {
                x.Token,
                Documento = x.Documento.Descricao
            });
        }

        public List<ProtocoloAnexo> GetAnexos(int idProtocolo)
        {
            return _repositoryAnexo.Find(x => x.IdProtocolo == idProtocolo).Include(x => x.Documento).ToList().Select(
                x => new ProtocoloAnexo
                {
                    Token = x.Token,
                    IdDocumento = x.Documento.IdDocumento
                }).ToList();
        }

        public object ConsultarTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var protocolos = GetDataToGridAndReportTriagemProtocolos(idEmpresa, idsEstabelecimentosBase,
                idEstabelecimento, idAssociacao, dataGeracaoInicial, dataGeracaoFinal, dataPagamentoInicial,
                dataPagamentoFinal, associacoesPorEmpresa, order, filters);

            //Armazena a lista de ids de protocolo antes de skipar os registros, para impressão do relatório
            var idsParaRelatorio = protocolos.Select(x => x.IdProtocolo);
            var protocolosRetorno = new List<Protocolo>();

            protocolosRetorno = protocolos.Skip((page - 1) * take).Take(take).ToList();
            var retorno = ArrangeProtocolosTrigem(protocolosRetorno);

            return new
            {
                totalItems = protocolos.Count(),
                items = retorno,
                idsProtocoloFiltrados = idsParaRelatorio
            };
        }

        public byte[] GerarRelatorioTriagemProtocolo(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo, string logo)
        {
            var listaDados = new List<RelatorioTriagemProtocoloDataType>();
            var protocolos = GetDataToGridAndReportTriagemProtocolos(idEmpresa, idsEstabelecimentosBase,
                idEstabelecimento, idAssociacao, dataGeracaoInicial, dataGeracaoFinal, dataPagamentoInicial,
                dataPagamentoFinal, associacoesPorEmpresa, order, filters);
            var protocolosToReturn = ArrangeProtocolosTrigem(protocolos.ToList());

            foreach (var protocolo in protocolosToReturn)
            {
                listaDados.Add(new RelatorioTriagemProtocoloDataType
                {
                    Codigo = protocolo.IdProtocolo.ToString(),
                    DataAprovacao = protocolo.DataAprovacao,
                    DataGeracao = protocolo.DataGeracao,
                    DataPagamento = protocolo.DataPagamento,
                    DataPrevisaoPagamento = protocolo.DataPrevisaoPagamento,
                    Estabelecimento = protocolo.Descricao,
                    NumeroEventos = protocolo.NumeroEventos?.ToString(),
                    PagamentoAntecipado = protocolo.EstabPagamentoAntecipado,
                    Pago = protocolo.Pago,
                    Status = protocolo.Status,
                    ValorProtocolo = $"R$ {protocolo.ValorProtocolo:N}",
                    CnpjEstabelecimento = protocolo.CNPJEstabelecimento
                });
            }

            return new RelatorioTriagemProtocolo().GetReport(listaDados, tipoArquivo, logo);
        }

        private List<RecebimentoProtocoloModel> ArrangeProtocolosTrigem(List<Protocolo> protocolosRetorno)
        {
            var retorno = new List<RecebimentoProtocoloModel>();
            foreach (var item in protocolosRetorno)
            {
                var isItemAntecipado =
                    item.ProtocoloAntecipacoes?.Any(y => y.Status == EStatusProtocoloAntecipacao.Aprovada) ?? false;
                //Funcionalidade Associacao
                bool requerLiberacao = false;
                if (item.StatusProtocolo == EStatusProtocolo.Gerado)
                {
                    var credenciamento = _credenciamentoRepository.Find(x => x.IdEstabelecimentoBase == item.IdEstabelecimentoBase &&
                                                                             x.IdEmpresa == item.IdEmpresa)
                        .Include(x => x.Estabelecimento)
                        ?.FirstOrDefault();
                    if (credenciamento != null)
                    {
                        var associacoes = _estabelecimentoAssociacaoRepository
                            .Find(x => x.IdEstabelecimento == credenciamento.IdEstabelecimento)
                            .Include(x => x.Associacao)
                            .Select(x => x.Associacao.LiberaProtocolos);
                        if (associacoes != null && associacoes.Any(x => x == true))
                        {
                            requerLiberacao = true;
                        }
                    }
                }

                var protocoloRetorno = new RecebimentoProtocoloModel
                {
                    IdProtocolo = item.IdProtocolo,
                    Associacao = item.EstabelecimentoDestinatario?.Descricao,
                    NomeFantasia = item.Empresa?.NomeFantasia,
                    IdEmpresa = item.Empresa?.IdEmpresa,
                    CNPJEstabelecimento = item.EstabelecimentoBase?.CNPJEstabelecimento.ToCNPJFormato(),
                    Descricao = item.EstabelecimentoBase?.Descricao,
                    IdEstabelecimentoBase = item.IdEstabelecimentoBase,
                    CNPJ = item.Empresa?.CNPJ.ToCNPJFormato(),
                    DataGeracao = item.DataGeracao.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataPagamento = item.DataPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                    Taxa = getTaxaEstabelecimento(item.IdEstabelecimentoBase, item.IdEmpresa),
                    ValorProtocolo = item.ValorProtocolo,
                    Status = EnumHelpers.GetDescription(item.StatusProtocolo),
                    StatusProtocolo = item.StatusProtocolo,
                    Antecipado =
                        item.ProtocoloAntecipacoes?.Any(y => y.Status == EStatusProtocoloAntecipacao.Aprovada) ?? false,
                    AntecipadoStr = isItemAntecipado ? "Sim" : "Não",
                    Pago = item.StatusProtocolo == EStatusProtocolo.Pago ? "Sim" : "Não",
                    TaxaPagamentoAntecipado = item.ProtocoloAntecipacoes
                        ?.FirstOrDefault(y => y.Status == EStatusProtocoloAntecipacao.Aprovada)
                        ?.TaxaPagamentoAntecipado,
                    ValorPagamentoAntecipado = item.ProtocoloAntecipacoes
                        ?.FirstOrDefault(y => y.Status == EStatusProtocoloAntecipacao.Aprovada)
                        ?.ValorPagamentoAntecipado,
                    DataPagamentoAntecipado = item.ProtocoloAntecipacoes
                        ?.FirstOrDefault(y => y.Status == EStatusProtocoloAntecipacao.Aprovada)?.DataPagamentoAntecipado
                        .ToString("dd/MM/yyyy HH:mm:ss"),
                    PermiteAntecipacao = (item.ProtocoloAntecipacoes == null || !item.ProtocoloAntecipacoes.Any()) &&
                                         (item.ProtocoloEventos != null &&
                                          item.ProtocoloEventos.Any(x => x.Status == EStatusProtocoloEvento.Aprovado)),
                    NumeroEventos = item.ProtocoloEventos?.Where(x => x.Status != EStatusProtocoloEvento.Rejeitado)
                        ?.Count(),
                    DataPrevisaoPagamento = item.DataPrevisaoPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataAprovacao = item.DataAprovacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    RequerLiberacao = requerLiberacao,
                    TipoDestinatario = item.TipoDestinatario,
                    IdEstabelecimentoDestinatario = item.IdEstabelecimentoDestinatario,                                        
                    EstabPagamentoAntecipado = item.EstabPagamentoAntecipado ? "Sim" : "Não",
                    TipoEvento = item.TiposEventos,
                    TipoPagamento = EnumHelpers.GetDescription(item.TiposPagamentos),
                    PossuiEventoReincidente = item.PossuiEventoReincidente ? "Sim" : "Não"
                };

                retorno.Add(protocoloRetorno);
            }

            return retorno;
        }
        
        private IQueryable<Protocolo> GetDataToGridAndReportTriagemProtocolos(int? idEmpresa, List<int> idsEstabelecimentosBase, int? idEstabelecimento, int? idAssociacao, DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, List<KeyValuePair<int, int>> associacoesPorEmpresa, OrderFilters order, List<QueryFilters> filters)
        {
            var protocolos = _protocoloRepository.All()
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoDestinatario)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloAntecipacoes);

            if (idEmpresa.HasValue)
                protocolos = protocolos.Where(x => x.IdEmpresa == idEmpresa.Value);


            if (idEstabelecimento.HasValue)
            {
                var idsEstabelecimentosBaseCredenciados = _estabelecimentoRepository
                    .Include(x => x.Credenciamentos)
                    .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento && x.Credenciamentos.Any(y =>
                                             y.Status == EStatusCredenciamento.Aprovado &&
                                             y.IdEstabelecimentoBase.HasValue))
                    ?.Credenciamentos?.Select(x => x.IdEstabelecimentoBase.Value).ToList();
                if (idsEstabelecimentosBaseCredenciados != null && idsEstabelecimentosBaseCredenciados.Any())
                {
                    if (idsEstabelecimentosBase == null) idsEstabelecimentosBase = new List<int>();
                    idsEstabelecimentosBase.AddRange(idsEstabelecimentosBaseCredenciados);
                }
            }

            if (idAssociacao.HasValue)
            {
                var idsEstabelecimentosBaseCredenciados = _estabelecimentoRepository
                    .Include(x => x.Credenciamentos)
                    .FirstOrDefault(x => x.IdEstabelecimento == idAssociacao && x.Credenciamentos.Any(y =>
                                             y.Status == EStatusCredenciamento.Aprovado &&
                                             y.IdEstabelecimentoBase.HasValue))
                    ?.Credenciamentos?.Select(x => x.IdEstabelecimentoBase.Value).ToList();
                if (idsEstabelecimentosBaseCredenciados != null && idsEstabelecimentosBaseCredenciados.Any())
                {
                    if (idsEstabelecimentosBase == null) idsEstabelecimentosBase = new List<int>();
                    idsEstabelecimentosBase.AddRange(idsEstabelecimentosBaseCredenciados);
                }
            }

            if (dataGeracaoInicial.HasValue && dataGeracaoFinal.HasValue)
            {
                dataGeracaoInicial = new DateTimeHelper().StartOfDay(dataGeracaoInicial.Value);
                dataGeracaoFinal = new DateTimeHelper().EndOfDay(dataGeracaoFinal.Value);
                protocolos = protocolos.Where(x => x.DataGeracao > dataGeracaoInicial.Value &&
                                                   x.DataGeracao < dataGeracaoFinal.Value);
            }

            if (dataPagamentoInicial.HasValue && dataPagamentoFinal.HasValue)
            {
                dataPagamentoInicial = new DateTimeHelper().StartOfDay(dataPagamentoInicial.Value);
                dataPagamentoFinal = new DateTimeHelper().EndOfDay(dataPagamentoFinal.Value);
                protocolos = protocolos.Where(x => x.DataPagamento > dataPagamentoInicial.Value &&
                                                   x.DataPagamento < dataPagamentoFinal.Value);
            }

            if (idsEstabelecimentosBase != null && idsEstabelecimentosBase.Any() && associacoesPorEmpresa != null &&
                associacoesPorEmpresa.Any())
            {
                var protocolosAux = new List<Protocolo>();
                foreach (var item in protocolos)
                {
                    if (idsEstabelecimentosBase.Contains(item.IdEstabelecimentoBase))
                        protocolosAux.Add(item);
                }

                protocolos = protocolosAux.AsQueryable();
            }
            else if (idsEstabelecimentosBase != null && idsEstabelecimentosBase.Any())
            {
                protocolos = protocolos
                    .Where(x => idsEstabelecimentosBase.Contains(x.IdEstabelecimentoBase));
            }

            protocolos = string.IsNullOrWhiteSpace(order?.Campo)
                ? protocolos.OrderBy(x => x.IdProtocolo)
                : protocolos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            if (!(idsEstabelecimentosBase.Count() > 0 || idEstabelecimento != null || idAssociacao != null))
            {
                protocolos = protocolos.Where(x =>
                    (((x.StatusProtocolo != EStatusProtocolo.Gerado &&
                       (x.StatusProtocolo != EStatusProtocolo.EmTransito)) ||
                      (x.StatusProtocolo == EStatusProtocolo.EmTransito &&
                       x.TipoDestinatario == ETipoDestinatario.Associacao)) && x.DataRecebidoEmpresa.HasValue) ||
                    (!x.DataRecebidoEmpresa.HasValue && x.EstabPagamentoAntecipado));
            }

            protocolos = protocolos.AplicarFiltrosDinamicos<Protocolo>(filters);

            return protocolos;
        }

        public object ConsultarTriagemProtocoloAssociacao(int? idEmpresa, List<int> idsEstabelecimentosBase,
            int? idEstabelecimento, IList<int?> idAssociacao,
            DateTime? dataGeracaoInicial,
            DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal,
            List<KeyValuePair<int, int>> associacoesPorEmpresa, int take,
            int page,
            OrderFilters order, List<QueryFilters> filters)
        {
            var protocolos = _protocoloRepository.All()
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoDestinatario)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(y => y.ViagemEvento))
                .Include(x => x.EmpresaDestinatario)
                .Include(x => x.ProtocoloAntecipacoes)
                .Where(x => (x.TipoDestinatario == ETipoDestinatario.Associacao ||
                             idAssociacao.Contains(x.IdEstabelecimentoDestinatario)) && x.GeradoAssociacao);

            if (idEmpresa.HasValue)
                protocolos = protocolos.Where(x => x.IdEmpresa == idEmpresa.Value);


            if (idEstabelecimento.HasValue)
            {
                var idsEstabelecimentosBaseCredenciados = _estabelecimentoRepository
                    .Include(x => x.Credenciamentos)
                    .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento && x.Credenciamentos.Any(y =>
                                             y.Status == EStatusCredenciamento.Aprovado &&
                                             y.IdEstabelecimentoBase.HasValue))
                    ?.Credenciamentos?.Select(x => x.IdEstabelecimentoBase.Value).ToList();
                if (idsEstabelecimentosBaseCredenciados != null && idsEstabelecimentosBaseCredenciados.Any())
                {
                    if (idsEstabelecimentosBase == null) idsEstabelecimentosBase = new List<int>();
                    idsEstabelecimentosBase.AddRange(idsEstabelecimentosBaseCredenciados);
                }
            }

            if (idAssociacao.Any())
            {
                var idsEstabelecimentosBaseCredenciados = _estabelecimentoAssociacaoRepository
                    .Include(x => x.Estabelecimento)
                    .Where(x => idAssociacao.Contains(x.IdAssociacao)  && x.Estabelecimento.Credenciamentos.Any(y =>
                                    y.Status == EStatusCredenciamento.Aprovado && y.IdEstabelecimento.HasValue))
                    ?.Select(y => y.Estabelecimento.IdEstabelecimentoBase ?? 0).ToList();
                if (idsEstabelecimentosBaseCredenciados != null && idsEstabelecimentosBaseCredenciados.Any())
                {
                    if (idsEstabelecimentosBase == null) idsEstabelecimentosBase = new List<int>();
                    idsEstabelecimentosBase.AddRange(idsEstabelecimentosBaseCredenciados);
                }
            }

            if (dataGeracaoInicial.HasValue && dataGeracaoFinal.HasValue)
            {
                dataGeracaoInicial = new DateTimeHelper().StartOfDay(dataGeracaoInicial.Value);
                dataGeracaoFinal = new DateTimeHelper().EndOfDay(dataGeracaoFinal.Value);
                protocolos = protocolos.Where(x => x.DataGeracao > dataGeracaoInicial.Value &&
                                                   x.DataGeracao < dataGeracaoFinal.Value);
            }

            if (dataPagamentoInicial.HasValue && dataPagamentoFinal.HasValue)
            {
                dataPagamentoInicial = new DateTimeHelper().StartOfDay(dataPagamentoInicial.Value);
                dataPagamentoFinal = new DateTimeHelper().EndOfDay(dataPagamentoFinal.Value);
                protocolos = protocolos.Where(x => x.DataPagamento > dataPagamentoInicial.Value &&
                                                   x.DataPagamento < dataPagamentoFinal.Value);
            }

            if (idsEstabelecimentosBase != null && idsEstabelecimentosBase.Any() && associacoesPorEmpresa != null &&
                associacoesPorEmpresa.Any())
            {
                var protocolosAux = new List<Protocolo>();
                foreach (var item in protocolos)
                {
                    if (idsEstabelecimentosBase.Contains(item.IdEstabelecimentoBase))
                        protocolosAux.Add(item);
                    if (associacoesPorEmpresa.Any(x =>
                        x.Key == item.IdEstabelecimentoBase && x.Value == item.IdEmpresa))
                        protocolosAux.Add(item);
                }

                protocolos = protocolosAux.AsQueryable();
            }
            else if (idsEstabelecimentosBase != null && idsEstabelecimentosBase.Any())
            {
                protocolos = protocolos
                    .Where(x => idsEstabelecimentosBase.Contains(x.IdEstabelecimentoBase));
            }


            protocolos = string.IsNullOrWhiteSpace(order?.Campo)
                ? protocolos.OrderByDescending(x => x.IdProtocolo)
                : protocolos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            // Mostrará apenas os protocolos que tem um idempresdest e nao tem um idestabdest ou que nao tenha ambos;
            protocolos = protocolos.Where(x =>
                x.IdEstabelecimentoDestinatario.HasValue && idAssociacao.Contains(x.IdEstabelecimentoDestinatario));

            protocolos = protocolos.AplicarFiltrosDinamicos<Protocolo>(filters);

            //Armazena a lista de ids de protocolo antes de skipar os registros, para impressão do relatório
            var idsParaRelatorio = protocolos.Select(x => x.IdProtocolo);
            var protocolosRetorno = protocolos.Skip((page - 1) * take).Take(take)
                .ToList();

            var retorno = new List<object>();
            foreach (var item in protocolosRetorno)
            {
                var isItemAntecipado =
                    item.ProtocoloAntecipacoes?.Any(y => y.Status == EStatusProtocoloAntecipacao.Aprovada) ?? false;
                //Funcionalidade Associacao
                bool requerLiberacao = false;
                if (item.StatusProtocolo == EStatusProtocolo.Gerado)
                {
                    var credenciamento = _credenciamentoRepository.Find(x => x.IdEstabelecimentoBase == item.IdEstabelecimentoBase &&
                                                                             x.IdEmpresa == item.IdEmpresa)
                        .Include(x => x.Estabelecimento)
                        ?.FirstOrDefault();
                    if (credenciamento != null)
                    {
                        var associacoes = _estabelecimentoAssociacaoRepository
                            .Find(x => x.IdEstabelecimento == credenciamento.IdEstabelecimento)
                            .Include(x => x.Associacao)
                            .Select(x => x.Associacao.LiberaProtocolos);
                        if (associacoes != null && associacoes.Any(x => x == true))
                        {
                            requerLiberacao = true;
                        }
                    }
                }

                var protocoloRetorno = new
                {
                    item.IdProtocolo,
                    Associacao = item.EstabelecimentoDestinatario?.Descricao,
                    item.Empresa?.NomeFantasia,
                    item.Empresa?.IdEmpresa,
                    CNPJEstabelecimento = item.EstabelecimentoBase?.CNPJEstabelecimento,
                    item.EstabelecimentoBase?.Descricao,
                    item.IdEstabelecimentoBase,
                    CNPJ = item.Empresa?.CNPJ.ToCNPJFormato(),
                    DataGeracao = item.DataGeracao.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataPagamento = item.DataPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                    Taxa = getTaxaEstabelecimento(item.IdEstabelecimentoBase, item.IdEmpresa),
                    ValorProtocolo = item.ProtocoloEventos
                        ?.Where(o =>
                            o.IdProtocolo == item.IdProtocolo && o.Status != EStatusProtocoloEvento.Rejeitado &&
                            !o.ViagemEvento.HabilitarPagamentoCartao)?.Sum(o => o.ValorTotalPagamento),
                    Status = EnumHelpers.GetDescription(item.StatusProtocolo),
                    item.StatusProtocolo,
                    Antecipado =
                        item.ProtocoloAntecipacoes?.Any(y => y.Status == EStatusProtocoloAntecipacao.Aprovada) ?? false,
                    AntecipadoStr = isItemAntecipado ? "Sim" : "Não",
                    Pago = item.StatusProtocolo == EStatusProtocolo.Pago ? "Sim" : "Não",
                    item.ProtocoloAntecipacoes?.FirstOrDefault(y => y.Status == EStatusProtocoloAntecipacao.Aprovada)
                        ?.TaxaPagamentoAntecipado,
                    item.ProtocoloAntecipacoes?.FirstOrDefault(y => y.Status == EStatusProtocoloAntecipacao.Aprovada)
                        ?.ValorPagamentoAntecipado,
                    DataPagamentoAntecipado = item.ProtocoloAntecipacoes
                        ?.FirstOrDefault(y => y.Status == EStatusProtocoloAntecipacao.Aprovada)?.DataPagamentoAntecipado
                        .ToString("dd/MM/yyyy HH:mm"),
                    PermiteAntecipacao = (item.ProtocoloAntecipacoes == null || !item.ProtocoloAntecipacoes.Any()) &&
                                         (item.ProtocoloEventos != null &&
                                          item.ProtocoloEventos.Any(x => x.Status == EStatusProtocoloEvento.Aprovado)),
                    NumeroEventos = item.ProtocoloEventos?.Where(x => x.Status != EStatusProtocoloEvento.Rejeitado)
                        ?.Count(),
                    DataPrevisaoPagamento = item.DataPrevisaoPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataAprovacao = item.DataAprovacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    RequerLiberacao = requerLiberacao,
                    item.TipoDestinatario,
                    item.IdEstabelecimentoDestinatario
                };

                retorno.Add(protocoloRetorno);
            }

            return new
            {
                totalItems = protocolos.Count(),
                items = retorno,
                idsProtocoloFiltrados = idsParaRelatorio
            };
        }

        public object ConsultarPagamentos(int idProtocolo, int? take, int? page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var eventosQy = _protocoloEventoRepository
                .Find(x => x.IdProtocolo == idProtocolo && x.Status != EStatusProtocoloEvento.Rejeitado)
                .Include(x => x.ViagemEvento)
                .Include(x => x.ViagemEvento.Viagem)
                .Include(x => x.ViagemEvento.Viagem.Filial)
                .Include(x => x.Protocolo);

            var filtroPagoCartao = filters?.FirstOrDefault(x => x.Campo == "ViagemEvento.HabilitarPagamentoCartao");
            if (filtroPagoCartao != null)
            {
                if (filtroPagoCartao.Valor == "1")
                    eventosQy = eventosQy.Where(x => x.ViagemEvento != null && x.ViagemEvento.HabilitarPagamentoCartao);

                if (filtroPagoCartao.Valor == "0")
                    eventosQy = eventosQy.Where(x =>
                        x.ViagemEvento != null && !x.ViagemEvento.HabilitarPagamentoCartao);

                filters.Remove(filtroPagoCartao);
            }

            eventosQy = eventosQy.AplicarFiltrosDinamicos<ProtocoloEvento>(filters);

            if (!string.IsNullOrWhiteSpace(order?.Campo))
                eventosQy = eventosQy.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            var retorno = new List<TriagemProtocoloModel>();

            var eventos = eventosQy.ToArray();
            foreach (var evento in eventos)
            {
                var isReincidente = _viagemEventoService.IsReicidente(evento.IdViagemEvento);
                
                var motorista = _motoristaService.GetPorCpf(evento.ViagemEvento?.Viagem?.CPFMotorista);

                var razaoSocial = evento.ViagemEvento?.Viagem?.Filial?.RazaoSocial;

                var item = new TriagemProtocoloModel
                {
                    IdProtocolo = evento.IdProtocolo,
                    Placa = evento.ViagemEvento?.Viagem?.Placa.ToPlacaFormato(),
                    Motorista = !string.IsNullOrWhiteSpace(motorista?.Nome)
                        ? motorista?.Nome
                        : (evento.ViagemEvento?.Viagem?.NomeMotorista != null
                            ? evento.ViagemEvento?.Viagem?.NomeMotorista
                            : "Não informado."),
                    TipoEventoViagem = EnumHelpers.GetDescription(evento.ViagemEvento?.TipoEventoViagem),
                    ValorPagamento = evento.ValorPagamento,
                    ValorTotalPagamento = evento.ValorTotalPagamento,
                    IdProtocoloEvento = evento.IdProtocoloEvento,
                    IdViagemEvento = evento.IdViagemEvento,
                    TipoEventoViagemInt = evento.ViagemEvento?.TipoEventoViagem,
                    Aprovado = evento.Status == EStatusProtocoloEvento.Aprovado,
                    StatusInt = evento.Protocolo.StatusProtocolo,
                    ViagemToken = evento.ViagemEvento?.Token,
                    PagoCartao = "Não",
                    HabilitarPagamentoCartao = evento?.ViagemEvento?.HabilitarPagamentoCartao,
                    NumeroDocumento = evento.ViagemEvento?.Viagem?.DocumentoCliente ??
                                      evento.ViagemEvento?.Viagem?.NumeroDocumento,
                    RazaoSocialFilial = razaoSocial,
                    IdEmpresa = evento.Protocolo.IdEmpresa,
                    EventoAnalisado = evento.EventoAnalisado,
                    EventoReincidente = isReincidente ? "Sim" : "Não"
                };

                if (evento.ViagemEvento != null)
                    if (evento.ViagemEvento.HabilitarPagamentoCartao)
                        item.PagoCartao = "Sim";

                retorno.Add(item);
            }

            if (string.IsNullOrWhiteSpace(order?.Campo))
                retorno = retorno.OrderBy(x => x.TipoEventoViagemInt != ETipoEventoViagem.Saldo)
                    .ThenBy(x => x.NumeroDocumento).ToList();

            return new
            {
                totalItems = eventosQy.Count(),
                items = retorno
            };
        }

        public ValidationResult Aprovar(int idProtocolo, DateTime? dataPrevisaoPagamento, int? IdUsuarioAprovacao)
        {
            var protocolo = _repository
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(o => o.ViagemEvento))
                .Include(x => x.ProtocoloEventos.Select(o => o.ViagemEvento.Viagem))
                .FirstOrDefault(x => x.IdProtocolo == idProtocolo);

            if (protocolo == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo");

            foreach (var protocoloEvento in protocolo.ProtocoloEventos)
            {
                if (protocoloEvento.Status == EStatusProtocoloEvento.Rejeitado)
                    continue;

                if (!protocoloEvento.EventoAnalisado)
                    return new ValidationResult().Add(
                        $"Protocolo não aprovado. de CT-e {protocoloEvento.ViagemEvento?.Viagem?.NumeroDocumento} não foi analisado");

                if (protocoloEvento.ViagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo)
                {
                    if (!string.IsNullOrEmpty(protocoloEvento.ViagemEvento.TokenAnexoAbono))
                    {
                        var eventoAbonoRejeitado =
                            _viagemEventoRepository.GetViagensEventos(protocoloEvento.ViagemEvento.IdViagem)
                                .FirstOrDefault(o => o.TipoEventoViagem == ETipoEventoViagem.Abono)
                                ?.IdMotivoRejeicaoAbono.HasValue ?? false;

                        if ((!protocoloEvento.AnaliseAbono.HasValue ||
                             protocoloEvento.AnaliseAbono == EStatusAnaliseAbono.NaoAnalisado) && !eventoAbonoRejeitado)
                            return new ValidationResult().Add(
                                $"Protocolo não aprovado. Abono do CT-e {protocoloEvento.ViagemEvento?.Viagem?.NumeroDocumento} não foi analisado");
                    }
                }
            }

            protocolo.DataPrevisaoPagamento = dataPrevisaoPagamento;
            protocolo.DataAprovacao = DateTime.Now;
            protocolo.StatusProtocolo = EStatusProtocolo.Aprovado;
            protocolo.IdUsuarioAprovacao = IdUsuarioAprovacao;
            foreach (var item in protocolo.ProtocoloEventos.Where(x => x.Status != EStatusProtocoloEvento.Rejeitado))
                item.Status = EStatusProtocoloEvento.Aprovado;

            _repository.Update(protocolo);

            return new ValidationResult();
        }

        public ValidationResult Rejeitar(int idProtocolo, int idMotivo, string detalhamento, Usuario UsuarioLogado)
        {
            var protocolo = _repository
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(z => z.Motivo))
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoDestinatario)
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.IdProtocolo == idProtocolo);

            if (protocolo == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo");

            foreach (var item in protocolo.ProtocoloEventos)
            {
                if (item.Status != EStatusProtocoloEvento.Rejeitado)
                {
                    item.Status = EStatusProtocoloEvento.Rejeitado;
                    item.IdMotivo = idMotivo;
                    item.Detalhamento = detalhamento;
                    item.DataOcorrencia = DateTime.Now;
                    item.IdMotivoOcorrencia = idMotivo;
                    item.DetalhamentoOcorrencia = detalhamento;
                    item.OcorrenciaPendente = true;
                    item.IdUsuarioCadOcorrencia = UsuarioLogado.IdUsuario;

                    if (item.IdProtocoloEvento_Vinculado == null)
                        UpdateViagemEvento(item.IdViagemEvento, null);
                }
            }

            //Ao rejeitar se estiver com empresa (protocolo.TipoDestinatario == ETipoDestinatario.Empresa) e tiver id de associacao (protocolo.IdEstabelecimentoDestinatario.HasValue)
            //mando o protocolo pra associacao
            if (protocolo.TipoDestinatario == ETipoDestinatario.Empresa &&
                protocolo.IdEstabelecimentoDestinatario.HasValue)
                protocolo.TipoDestinatario = ETipoDestinatario.Associacao;
            //caso esteja com empresa e nao tenha caido no if anterior irá enviar para establecimento, pois nao tem associacao no protocolo.
            else if (protocolo.TipoDestinatario == ETipoDestinatario.Empresa)
                protocolo.TipoDestinatario = ETipoDestinatario.Estabelecimento;
            //caso esteja com associacao ela só pode enviar para a estabelecimento
            else if (protocolo.TipoDestinatario == ETipoDestinatario.Associacao)
                protocolo.TipoDestinatario = ETipoDestinatario.Estabelecimento;

            protocolo.DataRejeicao = DateTime.Now;
            protocolo.StatusProtocolo = EStatusProtocolo.Rejeitado;
            protocolo.OcorrenciaPendente = true;

            _repository.Update(protocolo);

            var motivoById = _motivoRepository.FirstOrDefault(x => x.IdMotivo == idMotivo);
            return new ValidationResult();
        }


        public ValidationResult ReverterRejeicao(int idProtocolo)
        {
            var protocolo = _repository
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(z => z.Motivo))
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.IdProtocolo == idProtocolo);

            if (protocolo == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo");

            foreach (var item in protocolo.ProtocoloEventos)
            {
                item.Status = EStatusProtocoloEvento.Gerado;
                item.IdMotivo = null;
                item.Detalhamento = string.Empty;
                UpdateViagemEvento(item.IdViagemEvento, null);
            }

            protocolo.DataRejeicao = null;

            //Ao reverter rejecao se estiver com Estabelecimento protocolo.TipoDestinatario == ETipoDestinatario.Estabeleciment)  e tenha id (protocolo.IdEstabelecimentoDestinatario.HasValue)
            //mando o protocolo pra Associacao
            if (protocolo.TipoDestinatario == ETipoDestinatario.Estabelecimento &&
                protocolo.IdEstabelecimentoDestinatario.HasValue)
            {
                protocolo.TipoDestinatario = ETipoDestinatario.Associacao;
                protocolo.StatusProtocolo = EStatusProtocolo.EmTransito;
            }
            //caso esteja com estabelecimento e nao tenha IdDest ou esteja com associacao irá enviar para Empresa.
            else
            {
                protocolo.TipoDestinatario = ETipoDestinatario.Empresa;
                protocolo.StatusProtocolo = EStatusProtocolo.Recebido;
            }

            _repository.Update(protocolo);

            return new ValidationResult();
        }


        //public ValidationResult Liberar(int idProtocolo)
        //{
        //    var protocolo = _repository
        //        .Include(x => x.ProtocoloEventos)
        //        .FirstOrDefault(x => x.IdProtocolo == idProtocolo);
        //    if (protocolo == null)
        //        return new ValidationResult().Add("Não foi possível identificar o protocolo");

        //    foreach (var item in protocolo.ProtocoloEventos.Where(x => x.Status != EStatusProtocoloEvento.Rejeitado && x.Status != EStatusProtocoloEvento.Aprovado))
        //        item.Status = EStatusProtocoloEvento.Liberado;

        //    protocolo.StatusProtocolo = EStatusProtocolo.Liberado;
        //    _repository.Update(protocolo);

        //    return new ValidationResult();
        //}

        public void UpdateViagemEvento(int IdViagemEvento, int? IdProtocolo)
        {
            var viagemEventRepository = _viagemEventoRepository;
            var viagemEvento = viagemEventRepository.Get(IdViagemEvento);
            viagemEvento.IdProtocolo = IdProtocolo;            

            viagemEventRepository.Update(viagemEvento);
        }

        public ValidationResult RejeitarPagamento(int idProtocoloEvento, int? idMotivo, string detalhamento, Usuario UsuarioLogado)
        {
            var protocoloEvento = _repositoryEvento
                .Include(x => x.ViagemEvento)
                .Include(x => x.Protocolo)
                .Include(x => x.Protocolo.Empresa)
                .Include(x => x.Protocolo.EstabelecimentoBase)
                .Include(x => x.Protocolo.EstabelecimentoDestinatario)
                .Include(x => x.ProtocoloEvento_Vinculado)
                .FirstOrDefault(x => x.IdProtocoloEvento == idProtocoloEvento);

            if (protocoloEvento == null)
                return new ValidationResult().Add("Pagamento não encontrado. ");
            if (!idMotivo.HasValue)
                return new ValidationResult().Add("Motivo não informado. ");

            var motivo = _motivoRepository.Get(idMotivo.Value);
            if (motivo == null)
                return new ValidationResult().Add("Motivo não identificado. ");

            protocoloEvento.Status = EStatusProtocoloEvento.Rejeitado;
            protocoloEvento.IdMotivo = idMotivo;
            protocoloEvento.IdMotivoOcorrencia = idMotivo;
            protocoloEvento.Detalhamento = detalhamento;
            protocoloEvento.DetalhamentoOcorrencia = detalhamento;
            protocoloEvento.OcorrenciaPendente = true;
            protocoloEvento.DataOcorrencia = DateTime.Now;
            protocoloEvento.IdUsuarioCadOcorrencia = UsuarioLogado.IdUsuario;

            var motivoRejeicao = _motivoRepository.FirstOrDefault(x => idMotivo.HasValue && x.IdMotivo == idMotivo);
            protocoloEvento.Protocolo.OcorrenciaPendente = true;
            _repositoryEvento.Update(protocoloEvento);

            if (protocoloEvento.IdProtocoloEvento_Vinculado == null)
                UpdateViagemEvento(protocoloEvento.IdViagemEvento, null);

            UpdateProtocolValue(protocoloEvento.IdProtocolo);

            var protocoloEventos = _protocoloEventoRepository
                .Where(x => x.IdProtocolo == protocoloEvento.IdProtocolo);
            var todosRejeitados = protocoloEventos.Count(x => x.Status == EStatusProtocoloEvento.Rejeitado) ==
                                  protocoloEventos.Count();
            if (todosRejeitados)
            {
                protocoloEvento.Protocolo.StatusProtocolo = EStatusProtocolo.Rejeitado;
                _repositoryEvento.Update(protocoloEvento);
            }

            return new ValidationResult();
        }

        public void UpdateProtocolValue(int IdProtocolo)
        {
            var repositoryProtocolo = _protocoloRepository;

            var protocolo = repositoryProtocolo.Find(x => x.IdProtocolo == IdProtocolo)
                .Include(x => x.ProtocoloEventos)
                .FirstOrDefault();

            if (protocolo == null)
                return;

            var viagemEvento = _viagemEventoRepository
                .Find(x => x.IdProtocolo == protocolo.IdProtocolo);

            if (viagemEvento.Count() > 0)
                protocolo.ValorProtocolo = viagemEvento.Where(x => !x.HabilitarPagamentoCartao)
                    .Sum(c => c.ValorTotalPagamento).GetValueOrDefault(0);
            else
                protocolo.ValorProtocolo = 0;


            repositoryProtocolo.Update(protocolo);
        }

        public decimal CalcularTaxaAntecipacao(int idProtocolo, DateTime dataAntecipacao)
        {
            double taxaAntecipacao = 0;
            var protocolo = _repository
                .Include(x => x.ProtocoloAntecipacoes)
                .FirstOrDefault(x => x.IdProtocolo == idProtocolo);

            if (protocolo == null)
                throw new ApplicationException("Protocolo não identificado.");

            if (protocolo.ProtocoloAntecipacoes != null && protocolo.ProtocoloAntecipacoes.Any())
                throw new ApplicationException("Não é possível realizar mais de uma antecipação por protocolo.");
            if (!protocolo.DataPagamento.HasValue)
                throw new ApplicationException("Data de pagamento não foi informada para o protocolo.");
            if (new DateTimeHelper().StartOfDay(protocolo.DataPagamento.Value) <=
                new DateTimeHelper().StartOfDay(dataAntecipacao))
                throw new ApplicationException("Data da antecipação deve ser menor que a data de pagamento.");
            if (new DateTimeHelper().StartOfDay(dataAntecipacao) < new DateTimeHelper().StartOfDay(DateTime.Now))
                throw new ApplicationException("Data da antecipação deve ser maior que a data atual.");

            var estabelecimentoEmpresa = _estabelecimentoRepository
                .Find(x => x.Credenciamentos.Any(y => y.IdEstabelecimentoBase == protocolo.IdEstabelecimentoBase &&
                                                      y.IdEmpresa == protocolo.IdEmpresa))
                .Include(x => x.Credenciamentos).OrderByDescending(x => x.IdEstabelecimento).FirstOrDefault();

            if (estabelecimentoEmpresa != null && estabelecimentoEmpresa.TaxaAntecipacao > 0)
            {
                taxaAntecipacao = estabelecimentoEmpresa.TaxaAntecipacao;
            }
            else
            {
                var configuracaoEmpresa = _pagamentoConfiguracaoRepository
                    .FirstOrDefault(x => x.IdEmpresa == protocolo.IdEmpresa && x.Ativo);

                if (configuracaoEmpresa != null && configuracaoEmpresa.ValorTaxa > 0)
                    taxaAntecipacao = configuracaoEmpresa.ValorTaxa;
            }


            var taxaAntec = Convert.ToDecimal(taxaAntecipacao);
            if (protocolo.DataPagamento == null || !(taxaAntecipacao > 0)) return protocolo.ValorProtocolo;

            var totalDias = Convert.ToDecimal((new DateTimeHelper().StartOfDay(protocolo.DataPagamento.Value) -
                                               new DateTimeHelper().StartOfDay(dataAntecipacao)).TotalDays);

            var valor = protocolo.ValorProtocolo - taxaAntec * protocolo.ValorProtocolo * totalDias;

            return Math.Round(valor, 2);
        }

        public ValidationResult EnviarSolicitacaoAntecipacao(int idProtocolo, decimal valorPagantecipado,
            DateTime dataPagantecipado)
        {
            try
            {
                var protocolo = _protocoloRepository.Get(idProtocolo);
                if (protocolo != null && protocolo.StatusProtocolo == EStatusProtocolo.Pago)
                    return new ValidationResult().Add(
                        "Não é possível realizar uma solicitação de antecipação para protocolos pagos.");

                var antecipacao = new ProtocoloAntecipacao();

                antecipacao.Status = EStatusProtocoloAntecipacao.Solicitada;
                antecipacao.DataPagamentoAntecipado = dataPagantecipado;
                antecipacao.ValorPagamentoAntecipado = valorPagantecipado;
                antecipacao.DataSolicitacao = DateTime.Now;
                antecipacao.IdProtocolo = idProtocolo;

                _protocoloAntecipacaoRepository.Add(antecipacao);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} {e.InnerException?.Message}");
            }
        }

        public ValidationResult EnviarNotificacaoProtocolo(Protocolo protocolo, string emailDestinatario,
            string nomeEmpresa, byte[] logoEmpresa, string nomeAplicativo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario) || string.IsNullOrEmpty(emailDestinatario))
                    return new ValidationResult();

                if (string.IsNullOrWhiteSpace(nomeAplicativo))
                    nomeAplicativo = ConstantesUtils.GetNomeAdministradoraPlataforma;
                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Novo protocolo gerado - {nomeAplicativo}"};

                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\protocolo-criacao.html"))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var bannerAprovacao =
                        new LinkedResource(caminhoAplicacao + @"\Content\Image\ats-novo-protocolo.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };
                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerAprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{NUMERO_PROT}", protocolo.IdProtocolo.ToString());
                    html = html.Replace("{VALOR}", protocolo.ValorProtocolo.ToString());
                    html = html.Replace("{DATA_EMISSAO}", protocolo.DataGeracao.ToString("G"));
                    html = html.Replace("{EMPRESA}", nomeEmpresa);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);

                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                    view.LinkedResources.Add(logoHeader);
                    view.LinkedResources.Add(bannerAprovacao);
                    view.LinkedResources.Add(facebook);
                    view.LinkedResources.Add(logoAts);
                    emailModel.AlternateView = view;
                }

                emailModel.Destinatarios = emailDestinatario?.Split(';').ToList();
                emailModel.NomeVisualizacao = nomeAplicativo;
                emailModel.Prioridade = MailPriority.High;

                _emailService.EnviarEmailAsync(emailModel);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} --- {e.InnerException?.Message}");
            }
        }

        public ValidationResult EnviarEmailDesconto(Protocolo protocolo, string emailDestinatario, int? idEmpresa,
            string nomeEmpresa, byte[] logoEmpresa, decimal valorDesconto, string nomeAplicativo, byte[] anexo,
            string nomeAnexo, decimal pesoChegadaDe, decimal? pesoChegadaPara, decimal? ValorOriginal,
            decimal? ValorComDesconto, bool descontoAbonoRejeitado)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailDestinatario) || string.IsNullOrEmpty(emailDestinatario))
                    return new ValidationResult();

                var emailEmpresa = _empresaService.All().Where(c => c.IdEmpresa == idEmpresa)
                    .Select(c => c.EmailCartaFrete).FirstOrDefault();

                if (string.IsNullOrWhiteSpace(nomeAplicativo))
                    nomeAplicativo = nomeEmpresa;

                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
                var emailModel = new EmailModel {Assunto = $"Atualização em protocolo - {nomeAplicativo}"};

                var pathAttch = $"";
                using (var ms = new StreamReader(caminhoAplicacao + @"\Content\Email\protocolo-desconto.html"))
                {
                    var logoHeader = logoEmpresa != null
                        ? new LinkedResource(new MemoryStream(logoEmpresa))
                        {
                            ContentId = Guid.NewGuid().ToString()
                        }
                        : new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };

                    var logoAts = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-ats-login.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var bannerAprovacao =
                        new LinkedResource(caminhoAplicacao + @"\Content\Image\ats-desconto-protocolo.png")
                        {
                            ContentId = Guid.NewGuid().ToString()
                        };
                    var facebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png")
                    {
                        ContentId = Guid.NewGuid().ToString()
                    };

                    var html = ms.ReadToEnd();
                    LogManager.GetCurrentClassLogger().Info(html);

                    html = html.Replace("{0}", logoHeader.ContentId);
                    html = html.Replace("{1}", bannerAprovacao.ContentId);
                    html = html.Replace("{2}", facebook.ContentId);
                    html = html.Replace("{NUMERO_PROT}", protocolo.IdProtocolo.ToString());
                    html = html.Replace("{VALOR}", valorDesconto.ToString().Replace("-", ""));
                    html = html.Replace("{DATA_EMISSAO}", protocolo.DataGeracao.ToString("G"));
                    html = html.Replace("{EMPRESA}", nomeEmpresa);
                    html = html.Replace("{logoATS}", logoAts.ContentId);
                    html = html.Replace("{NomeAplicativo}", nomeAplicativo);
                    html = html.Replace("{VALOR_ORIGINAL}", ValorOriginal.GetValueOrDefault(0).ToString("N"));
                    html = html.Replace("{VALOR_COM_DESCONTO}", ValorComDesconto.GetValueOrDefault(0).ToString("N"));


                    var strReplaceTitulo = "{0}{1}{2}{3}";

                    try
                    {
                        if (anexo != null && anexo.Length > 0)
                        {
                            pathAttch = $"{Path.GetTempPath()}{nomeAnexo}";
                            File.WriteAllBytes(pathAttch, anexo);
                            emailModel.Anexo = new Attachment(new MemoryStream(anexo, 0, anexo.Length),
                                $"Anexo desconto.{nomeAnexo.Split('.')[1]}",
                                System.Web.MimeMapping.GetMimeMapping(nomeAnexo));
                        }
                    }
                    //TODO: Verificar porquê este catch mudo está aqui, acredito ser interessante criar um log da mensagem de exceção para não esconder o erro
                    catch (Exception e)
                    {
                        _logger.Error(e);
                    }

                    if (descontoAbonoRejeitado)
                    {
                        strReplaceTitulo = strReplaceTitulo.Replace("{0}",
                            $" o abono foi rejeitado, gerando um desconto de R${valorDesconto:N}");
                        strReplaceTitulo = strReplaceTitulo.Replace("{1}", "");
                        strReplaceTitulo = strReplaceTitulo.Replace("{2}", "");
                        strReplaceTitulo = strReplaceTitulo.Replace("{3}", ".");
                    }
                    else
                    {
                        if (pesoChegadaPara > 0)
                            strReplaceTitulo = strReplaceTitulo.Replace("{0}",
                                $" o peso passou de chegada {pesoChegadaDe:N}(KG) para {pesoChegadaPara.GetValueOrDefault(0).ToString("N")}(KG), devido a esta alteração o valor final será modificado");
                        else
                            strReplaceTitulo = strReplaceTitulo.Replace("{0}", "");

                        if (pesoChegadaPara > 0 && valorDesconto > 0)
                            strReplaceTitulo = strReplaceTitulo.Replace("{1}", $" e também");
                        else
                            strReplaceTitulo = strReplaceTitulo.Replace("{1}", "");

                        if (valorDesconto > 0)
                            strReplaceTitulo =
                                strReplaceTitulo.Replace("{2}", $", recebeu um desconto de R${valorDesconto:N}");
                        else
                            strReplaceTitulo = strReplaceTitulo.Replace("{2}", "");

                        if (anexo != null && anexo.Length > 0)
                            strReplaceTitulo = strReplaceTitulo.Replace("{3}", ", conforme anexo.");
                        else
                            strReplaceTitulo = strReplaceTitulo.Replace("{3}", ".");

                        if ("{0}{1}" == strReplaceTitulo)
                            strReplaceTitulo = ".";

                        html = html.Replace("#hash-anexo-peso-chegada#", strReplaceTitulo);

                        var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                        view.LinkedResources.Add(logoHeader);
                        view.LinkedResources.Add(bannerAprovacao);
                        view.LinkedResources.Add(facebook);
                        view.LinkedResources.Add(logoAts);
                        emailModel.AlternateView = view;
                    }

                    emailModel.Destinatarios = emailDestinatario?.Split(';').ToList();

                    emailModel.NomeVisualizacao = nomeAplicativo;
                    emailModel.Prioridade = MailPriority.High;

                    if (!string.IsNullOrWhiteSpace(emailEmpresa))
                        emailModel.Destinatarios.Add(emailEmpresa);

                    _emailService.EnviarEmailAsync(emailModel);

                    if (!string.IsNullOrWhiteSpace(pathAttch) && File.Exists(pathAttch))
                        File.Delete(pathAttch);

                    return new ValidationResult();
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"{e.Message} --- {e.InnerException?.Message}");
            }
        }

        public double getTaxaEstabelecimento(int? IdEstabelecimentoBase, int? IdEmpresa)
        {
            double taxaAntecipacao = 0;

            var estabelecimentoEmpresa = _estabelecimentoRepository
                .Find(x => x.Credenciamentos.Any(y => y.IdEstabelecimentoBase == IdEstabelecimentoBase &&
                                                      y.IdEmpresa == IdEmpresa))
                .Include(x => x.Credenciamentos)
                .Include(x => x.Empresa)
                .Include(x => x.Empresa.PagamentoConfiguracao)
                .OrderByDescending(x => x.IdEstabelecimento).FirstOrDefault();

            if (estabelecimentoEmpresa != null)
            {
                if (estabelecimentoEmpresa.TaxaAntecipacao > 0)
                    taxaAntecipacao = estabelecimentoEmpresa.TaxaAntecipacao;
                else
                {
                    var configuracaoEmpresa =
                        estabelecimentoEmpresa.Empresa?.PagamentoConfiguracao?.FirstOrDefault(x => x.Ativo);
                    if (configuracaoEmpresa != null && configuracaoEmpresa.ValorTaxa > 0)
                        taxaAntecipacao = configuracaoEmpresa.ValorTaxa;
                }
            }

            return taxaAntecipacao > 0 ? taxaAntecipacao * 100 : 0;
        }


        private bool HasEventosPendentes(int IdProtocolo)
        {
            var repository_ = _protocoloEventoRepository;

            return repository_.Any(x => x.IdProtocolo == IdProtocolo && x.OcorrenciaPendente);
        }

        public ValidationResult Resolver(int IdProtocoloEvento)
        {   
            var isCartaFrete = false;
            var isMeioHomologado = false;
            var repository_ = _protocoloEventoRepository;            

            var protocolevento = repository_
                .FirstOrDefault(x => x.IdProtocoloEvento == IdProtocoloEvento);
            
            var protocolorepository_ = _protocoloRepository;
            var protOrigem = protocolorepository_
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(p=>p.ViagemEvento))
                .Include(x => x.ProtocoloAnexos)
                .Include(x=> x.ViagemEventos)
                .Where(x => x.IdProtocolo == protocolevento.IdProtocolo)
                .AsNoTracking()
                .FirstOrDefault();


            if (protocolevento.Status == EStatusProtocoloEvento.Rejeitado && protocolevento.OcorrenciaPendente)
            {
                
                var habilitaPagamentoCartao = _viagemEventoService.GetTipoPagamento(protocolevento.IdViagemEvento);

                if (habilitaPagamentoCartao)
                    isMeioHomologado = true;
                else
                    isCartaFrete = true;


                var tiposEventos = protOrigem.ProtocoloEventos.Where(o =>
                        o.ViagemEvento != null && o.Status == EStatusProtocoloEvento.Rejeitado && o.OcorrenciaPendente)
                    .Select(o => o.ViagemEvento.TipoEventoViagem.DescriptionAttr()).Distinct()
                    .Aggregate((i, j) => $"{i} / {j}");


//            var prot = Mapper.Map<Protocolo>(protOrigem);
                var prot = new Protocolo
                {
                    IdEmpresa = protOrigem.IdEmpresa,
                    IdEstabelecimentoBase = protOrigem.IdEstabelecimentoBase,
                    DataGeracao = DateTime.Now,
                    TipoDestinatario = protOrigem.TipoDestinatario,
                    IdEstabelecimentoDestinatario = protOrigem.IdEstabelecimentoDestinatario,
                    IdEmpresaDestinatario = protOrigem.IdEmpresaDestinatario,
                    EstabPagamentoAntecipado = protOrigem.EstabPagamentoAntecipado,
                    DataRecebidoEmpresa = protOrigem.DataRecebidoEmpresa,
                    //TiposPagamentos = protOrigem.TiposPagamentos,
                    PossuiEventoReincidente = true,
                    TiposEventos = tiposEventos,
                    ProtocoloEventos = new List<ProtocoloEvento>(),
                    ProtocoloAnexos = new List<ProtocoloAnexo>()
                };
                prot.OcorrenciaPendente = false;
                prot.StatusProtocolo = EStatusProtocolo.Recebido;

                if (isCartaFrete && isMeioHomologado)
                    prot.TiposPagamentos = ETiposPagamentos.CFMH;
                else if (isCartaFrete)
                    prot.TiposPagamentos = ETiposPagamentos.CartaFrete;
                else
                    prot.TiposPagamentos = ETiposPagamentos.MeioHomologado;

                foreach (var anexo in protOrigem.ProtocoloAnexos)
                {
                    prot.ProtocoloAnexos.Add(new ProtocoloAnexo
                    {
                        Token = anexo.Token,
                        IdDocumento = anexo.IdDocumento
                    });
                }

                foreach (var evento in protOrigem.ProtocoloEventos)
                {
                    if (evento.Status == EStatusProtocoloEvento.Rejeitado &&
                        evento.OcorrenciaPendente &&
                        evento.IdProtocoloEvento == IdProtocoloEvento)
                        prot.ProtocoloEventos.Add(new ProtocoloEvento()
                        {
                            IdViagemEvento = evento.IdViagemEvento,
                            Status = EStatusProtocoloEvento.Gerado,
                            PesoChegada = evento.PesoChegada,
                            ValorTotalPagamento = evento.ValorTotalPagamento,
                            ValorPagamento = evento.ValorPagamento,
                            ValorBruto = evento.ValorBruto,
                            ValorQuebraMercadoria = evento.ValorQuebraMercadoria,
                            IdUsuarioCadOcorrencia = evento.IdUsuarioCadOcorrencia,
                            ValorDifFreteMotorista = evento.ValorDifFreteMotorista,
                            AnaliseAbono = EStatusAnaliseAbono.NaoAnalisado,
                            EventoReincidente = true,
                            OcorrenciaPendente = false

                        });
                }

//            prot.ProtocoloEventos = prot.ProtocoloEventos
//                .Where(pe => pe.Status == EStatusProtocoloEvento.Rejeitado 
//                             && pe.OcorrenciaPendente 
//                             && pe.IdProtocoloEvento == IdProtocoloEvento)
//                .ToList();           

//            foreach (var pe in prot.ProtocoloEventos)
//            {
//                pe.Detalhamento = "";
//                pe.DetalhamentoOcorrencia = "";
//                pe.IdMotivoOcorrencia = null;
//                pe.OcorrenciaPendente = false;
//                pe.DataOcorrencia = null;
//                pe.Status = 0;
//                pe.IdMotivo = null;                
//                pe.EventoReincidente = true;                
//            }

                if (prot.ProtocoloEventos.Count() > 0)
                    protocolorepository_.Add(prot);

            }

            protocolevento.OcorrenciaPendente = false;

            repository_.Update(protocolevento);            

            if (!HasEventosPendentes(protocolevento.IdProtocolo))
            {
                var protocolo = this.Get(protocolevento.IdProtocolo);

                protocolo.OcorrenciaPendente = false;

                _protocoloRepository
                    .Update(protocolo);
            }

            return new ValidationResult();
        }

        public ValidationResult ResolverProtocolo(int IdProtocolo, Usuario UsuarioLogado)
        {         
            var isCartaFrete = false;
            var isMeioHomologado = false;
            
            var repository_ = _protocoloRepository;  
            
            var protOrigem = repository_
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(p=>p.ViagemEvento))
                .Include(x => x.ProtocoloAnexos)
                .Include(x=> x.ViagemEventos)
                .Where(x => x.IdProtocolo == IdProtocolo)
                .AsNoTracking()
                .FirstOrDefault();                        
                                    
            var tiposEventos = protOrigem.ProtocoloEventos.Where(o => o.Status == EStatusProtocoloEvento.Rejeitado && o.OcorrenciaPendente)
                .Select(o => o.ViagemEvento.TipoEventoViagem.DescriptionAttr()).ToList();

            if (tiposEventos.Any())
            {
                var tipo = tiposEventos.Distinct().Aggregate((i, j) => $"{i} / {j}");

                var prot = new Protocolo
                {
                    IdEmpresa = protOrigem.IdEmpresa,
                    IdEstabelecimentoBase = protOrigem.IdEstabelecimentoBase,
                    DataGeracao = DateTime.Now,
                    TipoDestinatario = protOrigem.TipoDestinatario,
                    IdEstabelecimentoDestinatario = protOrigem.IdEstabelecimentoDestinatario,
                    IdEmpresaDestinatario = protOrigem.IdEmpresaDestinatario,
                    EstabPagamentoAntecipado = protOrigem.EstabPagamentoAntecipado,
                    DataRecebidoEmpresa = protOrigem.DataRecebidoEmpresa,
                    PossuiEventoReincidente = true,
                    TiposEventos = tipo,
                    TiposPagamentos = protOrigem.TiposPagamentos,
                    ProtocoloEventos = new List<ProtocoloEvento>(),
                    ProtocoloAnexos = new List<ProtocoloAnexo>()
                };
                prot.OcorrenciaPendente = false;
                prot.StatusProtocolo = EStatusProtocolo.Recebido;

                foreach (var anexo in protOrigem.ProtocoloAnexos)
                {
                    prot.ProtocoloAnexos.Add(new ProtocoloAnexo
                    {
                        Token = anexo.Token,
                        IdDocumento = anexo.IdDocumento
                    });
                }

                foreach (var even in protOrigem.ProtocoloEventos)
                {
                    if (even.Status == EStatusProtocoloEvento.Rejeitado &&
                        even.OcorrenciaPendente)
                    {
                        prot.ProtocoloEventos.Add(new ProtocoloEvento()
                        {
                            IdViagemEvento = even.IdViagemEvento,
                            Status = EStatusProtocoloEvento.Gerado,
                            PesoChegada = even.PesoChegada,
                            ValorTotalPagamento = even.ValorTotalPagamento,
                            ValorPagamento = even.ValorPagamento,
                            ValorBruto = even.ValorBruto,
                            ValorQuebraMercadoria = even.ValorQuebraMercadoria,
                            ValorDifFreteMotorista = even.ValorDifFreteMotorista,
                            IdUsuarioCadOcorrencia = even.IdUsuarioCadOcorrencia,
                            AnaliseAbono = EStatusAnaliseAbono.NaoAnalisado,
                            EventoReincidente = true,
                            OcorrenciaPendente = false
                        });
                        var habilitaPagamentoCartao = _viagemEventoService.GetTipoPagamento(even.IdViagemEvento);

                        if (habilitaPagamentoCartao)
                            isMeioHomologado = true;
                        else
                            isCartaFrete = true;
                    }
                }

                if (isCartaFrete && isMeioHomologado)
                    prot.TiposPagamentos = ETiposPagamentos.CFMH;
                else if (isCartaFrete)
                    prot.TiposPagamentos = ETiposPagamentos.CartaFrete;
                else
                    prot.TiposPagamentos = ETiposPagamentos.MeioHomologado;

                if (prot.ProtocoloEventos.Any())
                    repository_.Add(prot);
            }
            
            var updateprotocolo = repository_.Get(IdProtocolo);
            
            updateprotocolo.OcorrenciaPendente = false;                                                                                    
            
            repository_.Update(updateprotocolo);
            
            var eventorepository = _protocoloRepository;            
            var evento = eventorepository
                .Include(x => x.ProtocoloEventos)
                .Where(x => x.IdProtocolo == IdProtocolo).FirstOrDefault();                        
            
            foreach (var ev in evento.ProtocoloEventos)            
                ev.OcorrenciaPendente = false;
                        
            eventorepository.Update(evento);
            
            return new ValidationResult();
        }

        public object ConsultarTriagemAntecipacaoProtocolo(int? idEmpresa, List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page, OrderFilters order,
            List<QueryFilters> filters, Usuario UsuarioLogado)
        {
            var protocolos = _protocoloRepository.All()
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.ProtocoloEventos)                
                .Include(x => x.ProtocoloEventos.Select(y => y.UsuarioOcorrencia))
                .Where(x => x.OcorrenciaPendente);

            if (idEmpresa.HasValue)
                protocolos = protocolos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idEstabelecimentoBase != null && idEstabelecimentoBase.Any())
                protocolos = protocolos.Where(x => idEstabelecimentoBase.Contains(x.IdEstabelecimentoBase));

            var registrosDb = protocolos.Select(x => new
            {
                x.IdProtocolo,
                x.Empresa.NomeFantasia,
                Estabelecimento = x.EstabelecimentoBase.Descricao,
                DataGeracao = x.DataGeracao,
                x.ValorProtocolo,
                IdUsuarioCadOcorrencia = x.ProtocoloEventos.Where(y => y.UsuarioOcorrencia != null)
                    .Select(y => y.IdUsuarioCadOcorrencia).FirstOrDefault(),
                nomeusuariocadocorrencia = x.ProtocoloEventos.Where(y => y.UsuarioOcorrencia != null)
                    .Select(y => y.UsuarioOcorrencia.Nome).FirstOrDefault()
            }).ToList();
            
            var registros = registrosDb.Select(x => new
            {
                IdProtocolo = x.IdProtocolo.ToString(),
                x.NomeFantasia,
                Estabelecimento = x.Estabelecimento,
                DataGeracao = x.DataGeracao.ParaFormatoBrasileiroStr().ToString(),
                ValorProtocolo = x.ValorProtocolo,
                IdUsuarioCadOcorrencia = x.IdUsuarioCadOcorrencia.ToString(),
                nomeusuariocadocorrencia = x.nomeusuariocadocorrencia
            }).AsQueryable();
            
            if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                registros = registros.OrderBy(x => x.IdProtocolo);
            else
                registros = registros.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            registros = registros.AplicarFiltrosDinamicos(filters);

            return new
            {
                idsProtocoloFiltrados = registros.Select(x => x.IdProtocolo),
                totalItems = registros.Count(),
                items = registros.Skip((page - 1) * take).Take(take)
                    .ToList()
            };
        }

        public object ConsultarTriagemAntecipacaoOcorrenciaProtocolo(int? idProtocolo, int? idEmpresa,
            List<int> idEstabelecimentoBase,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal, DateTime? dataSolcitacaoInicial,
            DateTime? dataSolicitacaoFinal, EStatusProtocoloAntecipacao? status, int take, int page,
            OrderFilters order,
            List<QueryFilters> filters)
        {
            var protocolos = _protocoloEventoRepository.All()
                .Include(x => x.Protocolo.Empresa)
                .Include(x => x.Protocolo.EstabelecimentoBase)
                .Include(x => x.MotivoOcorrencia)
                .Include(x => x.ViagemEvento)
                .Where(x => x.OcorrenciaPendente && x.IdProtocolo == idProtocolo);


            if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                protocolos = protocolos.OrderBy(x => x.IdProtocolo);
            else
                protocolos = protocolos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            protocolos = protocolos.AplicarFiltrosDinamicos<ProtocoloEvento>(filters);

            return new
            {
                totalItems = protocolos.Count(),
                items = protocolos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdProtocoloEvento,
                        x.ViagemEvento.Token,
                        x.ValorTotalPagamento,
                        DataGeracao = x.DataOcorrencia != null ? x.DataOcorrencia?.ParaFormatoBrasileiroStr() : "",
                        TipoEventoViagem = x.ViagemEvento.TipoEventoViagem.DescriptionAttr(),
                        x.DetalhamentoOcorrencia,
                        MotivoOcorrencia = x.MotivoOcorrencia?.Descricao
                    })
            };
        }

        public List<RelatorioProtocoloOcorrencia> ConsultarTriagemAntecipacaoOcorrenciaProtocolo(int? idProtocolo,
            int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var protocolos = _protocoloEventoRepository.All()
                .Include(x => x.Protocolo.Empresa)
                .Include(x => x.Protocolo.EstabelecimentoBase)
                .Include(x => x.MotivoOcorrencia)
                .Include(x => x.ViagemEvento)
                .Where(x => x.OcorrenciaPendente && x.IdProtocolo == idProtocolo);


            if (order == null || string.IsNullOrWhiteSpace(order.Campo))
                protocolos = protocolos.OrderBy(x => x.IdProtocolo);
            else
                protocolos = protocolos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            protocolos = protocolos.AplicarFiltrosDinamicos<ProtocoloEvento>(filters);

            return protocolos.ToList().Select(x => new RelatorioProtocoloOcorrencia
            {
                Token = x.ViagemEvento.Token,
                ValorTotalPagamento = x.ValorTotalPagamento,
                DataOcorrencia = x.DataOcorrencia != null ? x.DataOcorrencia?.ParaFormatoBrasileiroStr() : "",
                TipoEventoViagem = x.ViagemEvento.TipoEventoViagem.DescriptionAttr(),
                DetalhamentoOcorrencia = x.DetalhamentoOcorrencia,
                MotivoOcorrencia = x.MotivoOcorrencia?.Descricao
            }).ToList();
        }


        public ValidationResult AprovarAntecipacao(int idProtocoloAntecipacao)
        {
            var protocoloAntecipacao =
                _repositoryAntecipacao.FirstOrDefault(x => x.IdProtocoloAntecipacao == idProtocoloAntecipacao);

            if (protocoloAntecipacao == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo!");

            protocoloAntecipacao.Status = EStatusProtocoloAntecipacao.Aprovada;
            _repositoryAntecipacao.Update(protocoloAntecipacao);

            return new ValidationResult();
        }

        public ValidationResult RejeitarAntecipacao(int idProtocoloAntecipacao, int idMotivo, string detalhamento)
        {
            var protocoloAntecipacao =
                _repositoryAntecipacao.FirstOrDefault(x => x.IdProtocoloAntecipacao == idProtocoloAntecipacao);

            if (protocoloAntecipacao == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo!");

            protocoloAntecipacao.Status = EStatusProtocoloAntecipacao.Rejeitada;
            protocoloAntecipacao.IdMotivo = idMotivo;
            protocoloAntecipacao.Detalhamento = detalhamento;
            _repositoryAntecipacao.Update(protocoloAntecipacao);

            return new ValidationResult();
        }

        public ValidationResult ReenviarAntecipacao(int idProtocoloAntecipacao, DateTime dataPagamentoAntecipado)
        {
            var protocoloAntecipacao =
                _repositoryAntecipacao.Include(x => x.Protocolo)
                    .FirstOrDefault(x => x.IdProtocoloAntecipacao == idProtocoloAntecipacao);

            if (protocoloAntecipacao.Protocolo != null &&
                protocoloAntecipacao.Protocolo.StatusProtocolo == EStatusProtocolo.Pago)
                return new ValidationResult().Add("Não é possível realizar a antecipação de um protocolo já pago.");

            if (protocoloAntecipacao == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo!");

            protocoloAntecipacao.Status = EStatusProtocoloAntecipacao.Solicitada;
            protocoloAntecipacao.DataPagamentoAntecipado = dataPagamentoAntecipado;
            _repositoryAntecipacao.Update(protocoloAntecipacao);

            return new ValidationResult();
        }

        public List<Protocolo> ConsultarProtocolos(int? idEmpresa, List<int> idEstabelecimento,
            DateTime? dataGeracaoInicial, DateTime? dataGeracaoFinal,
            DateTime? dataPagamentoInicial, DateTime? dataPagamentoFinal, EStatusProtocoloEvento? status)

        {
            var protocolos = _protocoloRepository.All()
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloAntecipacoes)
                .Where(x => x.ProtocoloEventos.Any(y => y.Status != EStatusProtocoloEvento.Rejeitado));

            if (idEmpresa.HasValue)
                protocolos = protocolos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idEstabelecimento != null && idEstabelecimento.Any())
                protocolos = protocolos.Where(x => idEstabelecimento.Contains(x.IdEstabelecimentoBase));

            if (dataGeracaoInicial.HasValue && dataGeracaoFinal.HasValue)
            {
                dataGeracaoInicial = new DateTimeHelper().StartOfDay(dataGeracaoInicial.Value);
                dataGeracaoFinal = new DateTimeHelper().EndOfDay(dataGeracaoFinal.Value);
                protocolos = protocolos.Where(x => x.DataGeracao > dataGeracaoInicial.Value &&
                                                   x.DataGeracao < dataGeracaoFinal.Value);
            }


            if (dataPagamentoInicial.HasValue && dataPagamentoFinal.HasValue)
            {
                dataPagamentoInicial = new DateTimeHelper().StartOfDay(dataPagamentoInicial.Value);
                dataPagamentoFinal = new DateTimeHelper().EndOfDay(dataPagamentoFinal.Value);
                protocolos = protocolos.Where(x => x.DataPagamento > dataPagamentoInicial.Value &&
                                                   x.DataPagamento < dataPagamentoFinal.Value);
            }

            if (status.HasValue)
                protocolos = protocolos.Where(x => x.ProtocoloEventos.Any(y => y.Status == status));

            return protocolos.ToList();
        }

        public object GetAnteciopacoes(int? idEmpresa, int ano, int mes)
        {
            var _antecipacaoRepository = _protocoloAntecipacaoRepository;
            var protocoloRepository = _protocoloRepository;

            var date = DateTime.Parse(ano.ToString() + "-" + mes.ToString() + "-1 00:00:00");
            var dateBegin = new DateTime(date.Year, date.Month, 1);
            var dateEnd = dateBegin.AddMonths(1).AddDays(-1).AddHours(23).AddMinutes(59).AddSeconds(59);

            var antecipacoes = _antecipacaoRepository.Find(x => x.Protocolo.DataPagamento >= dateBegin
               && x.Protocolo.DataGeracao <= dateEnd
               && x.Status == EStatusProtocoloAntecipacao.Aprovada
               )
               .Include(x => x.Protocolo);

            if (idEmpresa.HasValue)
            {
                antecipacoes = antecipacoes.Where(x => x.Protocolo.IdEmpresa == idEmpresa);
            }


            return new
            {
                reais = antecipacoes.Count() > 0 ? antecipacoes.Sum(x => x.Protocolo.ValorProtocolo - x.ValorPagamentoAntecipado) : 0,
            };
        }

        public ValidationResult RealizarPagamento(int idProtocolo)
        {
            var protocolo = _repository.Get(idProtocolo);
            if (protocolo == null)
                return new ValidationResult().Add("Não foi possível identificar o protocolo");
            if (protocolo.StatusProtocolo == EStatusProtocolo.Rejeitado)
                return new ValidationResult().Add(
                    "Não é possível realizar o pagamento de um protocolo rejeitado. ");
            if (protocolo.StatusProtocolo != EStatusProtocolo.Aprovado)
                return new ValidationResult().Add("Só é possível realizar o pagamento de protocolos Aprovados. ");

            protocolo.StatusProtocolo = EStatusProtocolo.Pago;
            protocolo.DataPagamento = DateTime.Now;
            _repository.Update(protocolo);

            return new ValidationResult();
        }

        public static string GetDefaultExtension(string mimeType)
        {
            string result;
            RegistryKey key;
            object value;

            key = Registry.ClassesRoot.OpenSubKey(@"MIME\Database\Content Type\" + mimeType, false);
            value = key != null ? key.GetValue("Extension", null) : null;
            result = value != null ? value.ToString() : string.Empty;

            return result;
        }

        public ValidationResult RealizarDesconto(int idProtocoloEvento, decimal? valorDesconto,
            decimal? pesoChegada, int? idMotivoDesconto, string observacaoDesconto, string tokenAnexoDesconto,
            bool abonoDescontado)
        {
            try
            {
                var protocoloEventoRepository = _protocoloEventoRepository;
                var protocoloEvento = protocoloEventoRepository
                    .Include(x => x.Protocolo)
                    .Include(x => x.Protocolo.Empresa)
                    .Include(x => x.Protocolo.EstabelecimentoBase)
                    .FirstOrDefault(x => x.IdProtocoloEvento == idProtocoloEvento);

                decimal pesoChegadaOld = 0;
                decimal? valorOriginal = 0;

                if (protocoloEvento == null)
                    throw new Exception("Não foi possível realizar o desconto do Evento. Protocolo não encontrado");

                if (idMotivoDesconto.HasValue)
                {
                    var motivo = _motivoRepository.Get(idMotivoDesconto.Value);
                    if (motivo != null)
                    {
                        protocoloEvento.IdMotivoDesconto = motivo.IdMotivo;
                        protocoloEvento.DescricaoMotivoDesconto = motivo.Descricao;
                    }

                    if (valorDesconto > protocoloEvento.ValorPagamento)
                        throw new Exception("O valor do desconto não pode ultrapassar o valor do evento!");

                    if (pesoChegada < 1)
                        throw new Exception("O peso de chegada deve ser maior que zero!");

                    valorOriginal = protocoloEvento.ValorTotalPagamento;

                    protocoloEvento.ObservacaoDesconto = observacaoDesconto;
                    protocoloEvento.ValorTotalPagamento -= valorDesconto;
                    protocoloEvento.ValorDesconto = valorDesconto;

                    pesoChegadaOld = protocoloEvento.PesoChegada.Value;

                    if (pesoChegada.HasValue && pesoChegada > 0)
                        protocoloEvento.PesoChegada = pesoChegada.Value;

                    if (!string.IsNullOrEmpty(tokenAnexoDesconto))
                        protocoloEvento.TokenAnexoDesconto = tokenAnexoDesconto;

                    protocoloEventoRepository.Update(protocoloEvento);

                    var protocolo = _protocoloRepository.Get(protocoloEvento.IdProtocolo);
                    if (protocolo != null)
                    {
                        protocolo.ValorProtocolo -= valorDesconto.Value;
                        _protocoloRepository.Update(protocolo);
                    }
                }

                var nomeAplicativo = string.Empty;
                var layout = _layoutService.GetPorEmpresa(protocoloEvento.Protocolo.IdEmpresa);
                if (layout != null && !string.IsNullOrWhiteSpace(layout.NomeAplicativo))
                    nomeAplicativo = layout.NomeAplicativo;

                byte[] anexo = new byte[] { };
                string nomeAnexo = "";

                if (!string.IsNullOrWhiteSpace(protocoloEvento.TokenAnexoDesconto))
                {
                    var mediaFile = _dataMediaServerService.GetMedia(protocoloEvento.TokenAnexoDesconto);
                    if (mediaFile != null)
                    {
                        if (mediaFile.Data.Contains(","))
                            mediaFile.Data = mediaFile.Data.Split(',')[1];

                        anexo = Convert.FromBase64String(mediaFile.Data);
                        nomeAnexo = mediaFile.FileName;
                    }
                }

                // Aqui iremos validar se deve ser enviado pro estabelecimento ou pra associacao o email, e enviar o anexo juntamente
                if (protocoloEvento.Protocolo?.IdEstabelecimentoDestinatario > 0)
                {
                    //LogManager.GetCurrentClassLogger().Info("Enviando o e-mail de desconto");

                    EnviarEmailDesconto(protocoloEvento.Protocolo,
                        protocoloEvento.Protocolo?.EstabelecimentoDestinatario?.Email,
                        protocoloEvento.Protocolo?.Empresa?.IdEmpresa,
                        protocoloEvento.Protocolo?.Empresa?.NomeFantasia, protocoloEvento.Protocolo?.Empresa?.Logo,
                        valorDesconto.Value, nomeAplicativo, anexo, nomeAnexo,
                        pesoChegadaOld, pesoChegada, valorOriginal, protocoloEvento.ValorTotalPagamento,
                        abonoDescontado);
                }
                else
                {
                    //LogManager.GetCurrentClassLogger().Info("Enviando o e-mail de desconto");

                    EnviarEmailDesconto(protocoloEvento.Protocolo,
                        protocoloEvento.Protocolo?.EstabelecimentoBase?.Email,
                        protocoloEvento.Protocolo?.Empresa?.IdEmpresa,
                        protocoloEvento.Protocolo?.Empresa?.NomeFantasia, protocoloEvento.Protocolo?.Empresa?.Logo,
                        valorDesconto.Value, nomeAplicativo, anexo, nomeAnexo,
                        pesoChegadaOld, pesoChegada, valorOriginal, protocoloEvento.ValorTotalPagamento,
                        abonoDescontado);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message, "Caiu no erro do envio de email protocolo");
                return new ValidationResult();
            }
        }

        public ValidationResult RemoverDesconto(int idProtocoloEvento)
        {
            var protocoloEventoRepository = _protocoloEventoRepository;
            var protocolopository = _protocoloRepository;

            var protocoloEvento = protocoloEventoRepository
                .Include(o => o.Protocolo)
                .Include(o => o.Protocolo.Empresa)
                .Include(o => o.ViagemEvento.Viagem)
                .Include(o => o.Protocolo.EstabelecimentoBase)
                .FirstOrDefault(o => o.IdProtocoloEvento == idProtocoloEvento);

            if (protocoloEvento == null)
                return new ValidationResult().Add(
                    "Não foi possível remover o desconto. O protocolo não foi encontrado");

            protocoloEvento.Protocolo.ValorProtocolo = protocoloEvento.Protocolo.ValorProtocolo +
                                                       protocoloEvento.ValorDesconto.GetValueOrDefault(0);

            protocoloEvento.IdMotivoDesconto = null;
            protocoloEvento.DescricaoMotivoDesconto = string.Empty;
            protocoloEvento.ObservacaoDesconto = string.Empty;
            protocoloEvento.ValorTotalPagamento += protocoloEvento.ValorDesconto;
            protocoloEvento.PesoChegada = protocoloEvento.ViagemEvento.Viagem.PesoChegada;
            protocoloEvento.ValorDesconto = null;

            protocoloEventoRepository.Update(protocoloEvento);
            protocolopository.Update(protocoloEvento.Protocolo);

            return new ValidationResult();
        }

        public ValidationResult AlterarPesoChegada(int idProtocoloEvento, decimal? pesoChegada, int? numeroSacas,
            bool hasAbono = false)
        {
            var protocoloEventoRepository = _protocoloEventoRepository;
            var viagemRepository = _viagemRepository;
            var protocoloEvento = protocoloEventoRepository
                .Include(x => x.ViagemEvento)
                .Include(x => x.ViagemEvento.ViagemValoresAdicionais)
                .Include(x => x.Protocolo)
                .FirstOrDefault(x => x.IdProtocoloEvento == idProtocoloEvento);
            if (protocoloEvento == null) throw new Exception("Não foi possível identificar o evento do protocolo.");
            var viagem = viagemRepository
                .Include(x => x.ViagemRegras)
                .FirstOrDefault(x => x.ViagemEventos.Any(y => y.IdViagemEvento == protocoloEvento.IdViagemEvento));

            var viagemRegra = viagem?.ViagemRegras.FirstOrDefault();


            if (protocoloEvento.Status == EStatusProtocoloEvento.Aprovado)
                throw new Exception("Não é possível alterar o peso de chegada, protocolo aprovado!");

            decimal pesoChegadaReal;
            if (pesoChegada.HasValue && pesoChegada > 0 && viagem?.PesoSaida != null)
                pesoChegadaReal = pesoChegada.Value;
            else
            {
                if (viagem?.Quantidade < 1)
                    throw new Exception(
                        "Não é possível calcular os valores sem que a quantidade de sacas tenha sido informada na viagem.");
                if (!numeroSacas.HasValue)
                    throw new Exception(
                        "Não é possível calcular os valores sem que o número de sacas entregues seja informada.");

                var difSacas = viagem?.Quantidade - numeroSacas.Value;
                var pesoPorSaca = viagem?.PesoSaida / viagem?.Quantidade;
                pesoChegadaReal = viagem?.PesoSaida - (pesoPorSaca * difSacas) ?? 0;
            }

            var pesoDiferenca = viagem?.PesoSaida - pesoChegadaReal;
            var pesoDiferencaTolerada = viagem?.PesoSaida * viagemRegra?.ToleranciaPeso / 100;
            var valorPorKg = viagem?.ValorMercadoria / viagem?.PesoSaida;

            decimal? valorQuebraMercadoria = 0;
            if (pesoDiferencaTolerada < pesoDiferenca)
            {
                switch (viagemRegra.TipoQuebraMercadoria)
                {
                    case ETipoQuebraMercadoria.Diferenca:
                        valorQuebraMercadoria =
                            Math.Round((pesoDiferencaTolerada - pesoDiferenca) * valorPorKg ?? 0, 2);
                        break;
                    case ETipoQuebraMercadoria.Integral:
                        valorQuebraMercadoria =
                            Math.Round((pesoChegadaReal - viagem.PesoSaida) * valorPorKg ?? 0, 2);
                        break;
                }
            }

            var pagamentoFreteConfiguracao = _pagamentoConfiguracaoRepository
                .FirstOrDefault(x => x.IdEmpresa == viagem.IdEmpresa && x.Ativo);
            var quebraTarifa = (viagemRegra?.TarifaTonelada / 1000) * (pesoChegadaReal - viagem?.PesoSaida) ?? 0;
            if (pagamentoFreteConfiguracao != null && !pagamentoFreteConfiguracao.BonificarMotorista &&
                pesoChegadaReal > viagem?.PesoSaida)
                quebraTarifa = 0;

            var pesoChegadaOriginal = protocoloEvento.PesoChegada;
            protocoloEvento.PesoChegada = pesoChegadaReal;
            protocoloEvento.ValorDifFreteMotorista =
                pesoChegadaReal > 0 ? decimal.Round(Math.Abs(quebraTarifa), 2) : 0;
            protocoloEvento.ValorQuebraMercadoria =
                pesoChegadaReal > 0 ? decimal.Round(Math.Abs(valorQuebraMercadoria ?? 0), 2) : 0;
            var totalDescontar = hasAbono ? quebraTarifa : ((valorQuebraMercadoria ?? 0) + quebraTarifa);
            if (protocoloEvento.ViagemEvento.ViagemValoresAdicionais != null &&
                protocoloEvento.ViagemEvento.ViagemValoresAdicionais.Any())
            {
                foreach (var item in protocoloEvento.ViagemEvento.ViagemValoresAdicionais)
                {
                    switch (item.Tipo)
                    {
                        case ETipoValorAdicional.Acrescimo:
                            totalDescontar += item.Valor;
                            break;
                        case ETipoValorAdicional.Desconto:
                            totalDescontar -= item.Valor;
                            break;
                        default:
                            totalDescontar -= item.Valor;
                            break;
                    }
                }
            }

            var valorTotalPagamento = protocoloEvento.ValorTotalPagamento;
            protocoloEvento.ValorTotalPagamento = pesoChegadaReal > 0
                ? protocoloEvento.ValorPagamento + totalDescontar
                : protocoloEvento.ValorPagamento;

            var totalDescontarProtocolo = (valorTotalPagamento - (protocoloEvento.ValorTotalPagamento) ?? 0) * -1;
            protocoloEvento.Protocolo.ValorProtocolo =
                protocoloEvento.Protocolo.ValorProtocolo + totalDescontarProtocolo;
            if (viagem != null)
            {
                viagem.PesoChegadaOriginal = pesoChegadaOriginal;
                viagem.PesoChegada = protocoloEvento.PesoChegada;
                viagem.DifFreteMotorista = protocoloEvento.ValorDifFreteMotorista;
                viagem.ValorQuebraMercadoria = protocoloEvento.ValorQuebraMercadoria;
                if (protocoloEvento.ViagemEvento != null)
                {
                    protocoloEvento.ViagemEvento.ValorTotalPagamento = protocoloEvento.ValorTotalPagamento;
                    protocoloEvento.ViagemEvento.QuebraMercadoriaAbonada = hasAbono;
                }
            }

            viagem.DataAtualizacao = DateTime.Now;
            viagemRepository.Update(viagem);
            protocoloEventoRepository.Update(protocoloEvento);

            return new ValidationResult();
        }

        /// <summary>
        /// Altera o vinculo de um pagamento para outro protocolo
        /// </summary>
        /// <param name="idProtocolo">Código do protocolo ao qual o pagamento será vinculado</param>
        /// <param name="token">Código do pagamento</param>
        /// <param name="idUsuario">Código do usuário que está realizando o processo</param>
        /// <param name="idEmpresa">Código da empresa que está realizando o processo</param>
        /// <returns></returns>
        public ValidationResult VincularPagamentoProtocolo(int idProtocolo, string token, int idUsuario)
        {
            var usuario = _usuarioRepository.GetWithRelationships(idUsuario);
            var protocolo = _repository.Get(idProtocolo);
            if (protocolo == null || protocolo.IdEmpresa != usuario.IdEmpresa)
                return new ValidationResult().Add(
                    "Não foi possível identificar o protocolo pelo código informado.");
            var viagemEvento = _viagemEventoRepository
                .Find(x => x.Token == token.Trim())
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.Empresa)
                .FirstOrDefault(); ///validar

            if (viagemEvento == null)
                return new ValidationResult().Add("Evento não encontrado pelo token informado");

            if (viagemEvento.Status != EStatusViagemEvento.Baixado)
                return new ValidationResult().Add(
                    "Evento não baixado, realize a baixa do token antes de vincular ou criar novo protocolo!");

            if (protocolo.StatusProtocolo == EStatusProtocolo.Aprovado || protocolo.StatusProtocolo == EStatusProtocolo.AguardandoPagamento || protocolo.StatusProtocolo == EStatusProtocolo.Pago)
                return new ValidationResult().Add("Protocolo já aprovado, impossível vinclular evento!");

            if (viagemEvento.Viagem.Empresa.AgrupaProtocoloMesmoEvento)
                if (_viagemEventoRepository.FirstOrDefault(x => x.IdProtocolo == protocolo.IdProtocolo)
                        ?.TipoEventoViagem != viagemEvento.TipoEventoViagem)
                    return new ValidationResult().Add("Tipo de evento diferente do tipo de evento do protocolo!");

            var protocoloEventoExistente =
                _repositoryEvento.FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento);
            if (protocoloEventoExistente != null &&
                protocoloEventoExistente.ViagemEvento.ProtocoloEventos.FirstOrDefault().Status !=
                EStatusProtocoloEvento.Rejeitado)
                return new ValidationResult().Add("Evento já vinculado ao protocolo " +
                                                  protocoloEventoExistente.ViagemEvento.Protocolo.IdProtocolo +
                                                  "!");
            viagemEvento.IdProtocolo = protocolo.IdProtocolo;
            _viagemEventoRepository.Update(viagemEvento);

            ProtocoloEvento protocoloEvento;
            if (protocolo.ProtocoloEventos.Any(evento => evento.IdViagemEvento == viagemEvento.IdViagemEvento))
            {
                protocoloEvento = protocolo.ProtocoloEventos.First(evento => evento.IdViagemEvento == viagemEvento.IdViagemEvento);
                protocoloEvento.Status = EStatusProtocoloEvento.Gerado;

                if (protocoloEvento.ValorTotalPagamento.HasValue && !viagemEvento.HabilitarPagamentoCartao)
                    protocolo.ValorProtocolo += protocoloEvento.ValorTotalPagamento.Value;
                
                _repository.Update(protocolo);
            }
            else
            {
                var pagamentoService = _pagamentoFreteService;
                var somarPedagio =
                pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagemEvento.Viagem);

                protocoloEvento = new ProtocoloEvento
                {
                    IdViagemEvento = viagemEvento.IdViagemEvento,
                    Status = EStatusProtocoloEvento.Gerado,
                    PesoChegada = viagemEvento.Viagem?.PesoChegada,
                    ValorTotalPagamento = (viagemEvento.ValorTotalPagamento ?? 0) +
                                          (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                    ValorPagamento =
                        viagemEvento.ValorPagamento + (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                    ValorBruto = viagemEvento.ValorBruto,
                    ValorQuebraMercadoria = viagemEvento.Viagem?.ValorQuebraMercadoria,
                    ValorDifFreteMotorista = viagemEvento.Viagem?.DifFreteMotorista,
                    IdProtocolo = protocolo.IdProtocolo,
                    INSS = viagemEvento.INSS,
                    IRRPF = viagemEvento.IRRPF,
                    NumeroRecibo = viagemEvento.NumeroRecibo,
                    SESTSENAT = viagemEvento.SESTSENAT
                };

                _repositoryEvento.Add(protocoloEvento);
                viagemEvento.Status = EStatusViagemEvento.Baixado;
                viagemEvento.IdProtocolo = protocolo.IdProtocolo;
                _viagemEventoRepository.Update(viagemEvento);
                if (!viagemEvento.HabilitarPagamentoCartao)
                {
                    protocolo.ValorProtocolo += protocoloEvento.ValorTotalPagamento.Value;
                    _repository.Update(protocolo);
                }
            }

            return new ValidationResult();
        }

        public int GerarProtocoloPorViagemEvento(string token, int idEstabelecimento)
        {
            var viagemEvento = _viagemEventoRepository
                .Find(x => x.Token == token.Trim())
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemEventos)
                .Include(x => x.Viagem.ViagemEventos.Select(o => o.EstabelecimentoBase))
                .Include(x => x.Viagem.ViagemEstabelecimentos)
                .Include(x => x.Viagem.ViagemEstabelecimentos.Select(e => e.Estabelecimento))
                .FirstOrDefault();

            if (viagemEvento == null) throw new Exception("Evento não encontrado para o token informado.");
            if (viagemEvento.IdProtocolo.HasValue)
                throw new Exception("Este evento já está vinculado a outro protocolo");

            var idEstabelecimentoBase = _credenciamentoRepository.FirstOrDefault(x =>
                    x.IdEstabelecimento == idEstabelecimento && x.IdEmpresa == viagemEvento.IdEmpresa)?
                .IdEstabelecimentoBase;

            if (!idEstabelecimentoBase.HasValue)
                throw new Exception("Estabelecimento selecionado não está credenciado á empresa");

            if (viagemEvento.Status != EStatusViagemEvento.Baixado)
                throw new Exception(
                    "Evento não baixado, realize a baixa do token antes de vincular ou criar novo protocolo!");

            var protocoloEventoExistente =
                _repositoryEvento.FirstOrDefault(x => x.IdViagemEvento == viagemEvento.IdViagemEvento);
            if (protocoloEventoExistente != null &&
                protocoloEventoExistente.ViagemEvento.ProtocoloEventos.FirstOrDefault().Status !=
                EStatusProtocoloEvento.Rejeitado)
                throw new Exception("Evento já vinculado ao protocolo " +
                                    protocoloEventoExistente.ViagemEvento.Protocolo.IdProtocolo + "!");


            var pagamentoService = _pagamentoFreteService;
            var somarPedagio =
                pagamentoService.DeveIncluirPedagioJuntoComPagamentoDoEvento(viagemEvento, viagemEvento.Viagem);

            var protocolo = new Protocolo()
            {
                DataGeracao = DateTime.Now,
                IdEmpresa = viagemEvento.IdEmpresa,
                Processado = false,
                StatusProtocolo = EStatusProtocolo.Recebido,
                TipoDestinatario = ETipoDestinatario.Empresa,
                DataRecebidoEmpresa = DateTime.Now,
                DataTransito = DateTime.Now,
                ValorProtocolo = (!viagemEvento.HabilitarPagamentoCartao ? viagemEvento.ValorPagamento : 0),
                IdEstabelecimentoBase = idEstabelecimentoBase.Value,
                ProtocoloEventos = new List<ProtocoloEvento>
                {
                    new ProtocoloEvento
                    {
                        IdViagemEvento = viagemEvento.IdViagemEvento,
                        Status = EStatusProtocoloEvento.Gerado,
                        PesoChegada = viagemEvento.Viagem?.PesoChegada,
                        ValorTotalPagamento = viagemEvento.ValorTotalPagamento +
                                              (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                        ValorPagamento = viagemEvento.ValorPagamento +
                                         (somarPedagio ? viagemEvento.Viagem.ValorPedagio : 0),
                        ValorBruto = viagemEvento.ValorBruto,
                        ValorQuebraMercadoria = viagemEvento.Viagem?.ValorQuebraMercadoria,
                        ValorDifFreteMotorista = viagemEvento.Viagem?.DifFreteMotorista,
                        INSS = viagemEvento.INSS,
                        IRRPF = viagemEvento.IRRPF,
                        NumeroRecibo = viagemEvento.NumeroRecibo,
                        SESTSENAT = viagemEvento.SESTSENAT
                    }
                },
            };

            _repository.Add(protocolo);

            viagemEvento.IdProtocolo = protocolo.IdProtocolo;
            viagemEvento.Status = EStatusViagemEvento.Baixado;
            _viagemEventoRepository.Update(viagemEvento);

            return protocolo.IdProtocolo;
        }

        public object ConsultarRecebimentoProtocolos(int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var recebimentos = GetDataToGridAndReportRecebimentoProtocolos(order, filters);

            return new
            {
                totalItems = recebimentos.Count(),
                items = recebimentos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(item => new
                    {
                        item.IdProtocolo,
                        EstabBase = item.EstabelecimentoBase?.Descricao,
                        ValorProtocolo = item.ValorProtocolo.ToString("G"),
                        Status = EnumHelpers.GetDescription(item.StatusProtocolo),
                        item.CodigoRastreamento,
                        DataGeracao = item.DataGeracao.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataTransito = item.DataTransito?.ToString("dd/MM/yyyy HH:mm:ss"),
                        Associacao = item.EstabelecimentoDestinatario?.Descricao,
                        EstabPagamentoAntecipado = item.EstabPagamentoAntecipado ? "Sim" : "Não"
                    })
            };
        }

        public byte[] GerarRelatorioGridRecebimentoProtocolo(OrderFilters order, List<QueryFilters> filters,
            string tipoArquivo, string logo)
        {
            var listaDados = new List<RelatorioRecebimentoProtocoloDataType>();
            var protocolos = GetDataToGridAndReportRecebimentoProtocolos(order, filters);

            foreach (var protocolo in protocolos)
            {
                listaDados.Add(new RelatorioRecebimentoProtocoloDataType
                {
                    Status = protocolo.StatusProtocolo.DescriptionAttr(),
                    Valor = $"R$ {protocolo.ValorProtocolo:N}",
                    Codigo = protocolo.IdProtocolo.ToString(),
                    CodigoRastreamento = protocolo.CodigoRastreamento,
                    DataEnvio = protocolo.DataTransito?.ToString("dd/MM/yyyy"),
                    DataGeracao = protocolo.DataGeracao.ToShortDateString(),
                    Estabelecimento = protocolo.EstabelecimentoBase?.Descricao,
                    PagamentoAntecipado = protocolo.EstabPagamentoAntecipado ? "Sim" : "Não"
                });
            }

            return new RelatorioRecebimentoProtocolo().GetReport(listaDados, tipoArquivo, logo);
        }

        public IQueryable<Protocolo> GetDataToGridAndReportRecebimentoProtocolos(OrderFilters order,
            List<QueryFilters> filters)
        {
            var recebimentos = _repository
                .Include(x => x.EmpresaDestinatario)
                .Include(x => x.EstabelecimentoDestinatario)
                .Include(x => x.EstabelecimentoBase)
                .Where(x => (x.StatusProtocolo == EStatusProtocolo.EmTransito ||
                             ((x.StatusProtocolo == EStatusProtocolo.Aprovado
                               || x.StatusProtocolo == EStatusProtocolo.Rejeitado
                               || x.StatusProtocolo == EStatusProtocolo.Pago
                               || x.StatusProtocolo == EStatusProtocolo.AguardandoPagamento)
                              && x.DataRecebidoEmpresa == null && x.EstabPagamentoAntecipado)) &&
                            x.TipoDestinatario == ETipoDestinatario.Empresa);

            if (string.IsNullOrWhiteSpace(order?.Campo))
                recebimentos = recebimentos.OrderBy(x => x.IdProtocolo);

            recebimentos = string.IsNullOrWhiteSpace(order?.Campo)
                ? recebimentos.OrderBy(x => x.IdProtocolo)
                : recebimentos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            recebimentos = recebimentos.AplicarFiltrosDinamicos<Protocolo>(filters);

            return recebimentos;
        }

        public List<Protocolo> ConsultarRelatorioProtocolos(int? idEmpresa, int take, int page, OrderFilters order,
            List<QueryFilters> filters)
        {
            var prots = _repository
                .Include(x => x.EmpresaDestinatario)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoDestinatario);

            if (idEmpresa.HasValue)
                prots = prots.Where(x => x.IdEmpresa == idEmpresa);

            prots = string.IsNullOrWhiteSpace(order?.Campo)
                ? prots.OrderBy(x => x.IdProtocolo)
                : prots.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            prots = prots.AplicarFiltrosDinamicos(filters);

            return prots.Skip((page - 1) * take).Take(take).ToList();
        }

        public object ConsultarGridProtocolos(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var prots = _repository
                .Include(x => x.EmpresaDestinatario)
                .Include(x => x.EstabelecimentoBase)
                .Include(x => x.EstabelecimentoDestinatario);

            prots = string.IsNullOrWhiteSpace(order?.Campo)
                ? prots.OrderBy(x => x.IdProtocolo)
                : prots.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            prots = prots.AplicarFiltrosDinamicos<Protocolo>(filters);

            return new
            {
                totalItems = prots.Count(),
                items = prots.Skip((page - 1) * take).Take(take)
                    .ToList().Select(item => new
                    {
                        item.IdProtocolo,
                        EstabBase = item.EstabelecimentoBase.RazaoSocial,
                        Associacao = item.EstabelecimentoDestinatario?.Descricao,
                        ValorProtocolo = $"R$ {item.ValorProtocolo.ToString("G")}",
                        DataGeracao = item.DataGeracao.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataTransito = item.DataTransito?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataRecebimento = item.DataRecebidoEmpresa?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataAprovacao = item.DataAprovacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataPrevisaoPagamento = item.DataPrevisaoPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataPagamento = item.DataPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                        DataRejeicao = item.DataRejeicao?.ToString("dd/MM/yyyy HH:mm:ss"),
                        Status = EnumHelpers.GetDescription(item.StatusProtocolo)
                    })
            };
        }

        public Protocolo AfterUpdate(Protocolo newProtocolo_, Protocolo oldProtocolo_,
            bool retornarEventosPagosNoCartao = false)
        {
            if (newProtocolo_.StatusProtocolo != EStatusProtocolo.Aprovado)
                return newProtocolo_;

            //var token = WebConfigurationManager.AppSettings["MS_TOKEN_ADMINISTRADORA"];            
            try
            {
                var webhook = _webhookService.GetByIdRegistro(newProtocolo_.IdEmpresa,
                    WebhookTipoEvento.ProtocoloAprovado);

                if (webhook != null &&
                    newProtocolo_.StatusProtocolo == EStatusProtocolo.Aprovado &&
                    oldProtocolo_.StatusProtocolo != newProtocolo_.StatusProtocolo)
                {
                    var empresa_ = _empresaRepository.Get(newProtocolo_.IdEmpresa);

                    if (empresa_ == null || string.IsNullOrWhiteSpace(webhook.Endpoint))
                        return newProtocolo_;

                    // Requisição
                    var retorno = this.ConsultarProtocolos(newProtocolo_.IdEmpresa,
                        newProtocolo_.IdProtocolo, true, true, retornarEventosPagosNoCartao);
                    if (retorno.FirstOrDefault()?.Eventos != null && retorno.First().Eventos.Any())
                    {
                        var infoAdicional = "Aceite do protocolo: " + newProtocolo_.IdProtocolo;
                        var requisicao = new
                        {
                            Sucesso = true,
                            Objeto = retorno
                        };

                        webhook.EnviarNotificacao(_webhookActionDependencies, infoAdicional, requisicao, null, newProtocolo_.IdEmpresa);
                    }
                    else
                        LogManager.GetCurrentClassLogger()
                            .Info(
                                "Aceite do protocolo: {0}. Notificação não enviada por não haver eventos para reembolso do estabelcimento.",
                                newProtocolo_.IdProtocolo);
                }
            }
            catch (Exception e)
            {
                _logger.Error(e, "Erro ao notificar web hook de baixa de evento: " + newProtocolo_.IdProtocolo);
            }

            return newProtocolo_;
        }

        public List<ProtocoloConsultaModel> ConsultarProtocolos(int idEmpresa, int idProtocolo,
            bool includeEstabelecimentoBase, bool setCpfUsuarioLogadoComoAprovador,
            bool retornarEventosPagosNoCartao)
        {
            // setCpfUsuarioLogadoComoAprovador => Gambiarra enquanto fazem o campo para armazenar no DB
            var cpfUsuarioLogado = HttpContext.Current.Items["CpfUsuarioLogado"];
            var protocolos =
                _pagamentoFreteService.ConsultarProtocolosProcessados(idEmpresa, idProtocolo,
                    includeEstabelecimentoBase);

            var retorno = new List<ProtocoloConsultaModel>();
            foreach (var protocolo in protocolos)
            {
                var eventos = retornarEventosPagosNoCartao
                    ? protocolo.ProtocoloEventos?.Where(z => z.Status != EStatusProtocoloEvento.Rejeitado)
                    : protocolo.ProtocoloEventos?.Where(z =>
                        z.Status != EStatusProtocoloEvento.Rejeitado && !z.ViagemEvento.HabilitarPagamentoCartao);
                retorno.Add(new ProtocoloConsultaModel
                {
                    IdProtocolo = protocolo.IdProtocolo,
                    IdEstabelecimentoBase = protocolo.IdEstabelecimentoBase,
                    CNPJEstabelecimento = protocolo.EstabelecimentoBase?.CNPJEstabelecimento,
                    CpfUsuarioAprovacao = cpfUsuarioLogado.ToString(),
                    DataPagamento = protocolo.DataPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                    DataGeracao = protocolo.DataGeracao.ToString("yyyy-MM-dd HH:mm:ss"),
                    DataPrevisaoPagamento = protocolo.DataPrevisaoPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                    ValorProtocolo = protocolo.ValorProtocolo,
                    Processado = protocolo.Processado,
                    StatusProtocolo = (int) protocolo.StatusProtocolo,
                    DescricaoStatusProtocolo = EnumHelpers.GetDescription(protocolo.StatusProtocolo),
                    Anexos = protocolo.ProtocoloAnexos?.Select(y => new ProtocoloAnexoConsultaModel
                    {
                        Token = y.Token,
                        IdProtocolo = y.IdProtocolo,
                        IdDocumento = y.IdDocumento,
                        IdProtocoloAnexo = y.IdProtocoloAnexo
                    }).ToList(),
                    Antecipacoes = protocolo.ProtocoloAntecipacoes?.Select(y =>
                        new ProtocoloAntecipacaoConsultaModel
                        {
                            IdProtocoloAntecipacao = y.IdProtocoloAntecipacao,
                            Status = y.Status,
                            IdProtocolo = y.IdProtocolo,
                            IdMotivo = y.IdMotivo,
                            DataSolicitacao = y.DataSolicitacao.ToString("yyyy-MM-dd HH:mm:ss"),
                            ValorPagamentoAntecipado = y.ValorPagamentoAntecipado
                        }).ToList(),

                    Eventos = eventos?
                        .Select(y => new ProtocoloEventoConsultaModel
                        {
                            Status = y.Status,
                            IdViagemEvento = y.IdViagemEvento,
                            IdProtocolo = y.IdProtocolo,
                            IdMotivo = y.IdMotivo,
                            IdProtocoloEvento = y.IdProtocoloEvento,
                            TipoEventoViagem = y.ViagemEvento.TipoEventoViagem,
                            ValorTotalPagamento = y.ViagemEvento.ValorTotalPagamento,
                            DataHoraPagamento = y.ViagemEvento.DataHoraPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                            NumeroRecibo = y.ViagemEvento.NumeroRecibo,
                            PesoChegada = y.PesoChegada ?? y.ViagemEvento.Viagem.PesoChegada,
                            PesoDiferenca = y.ViagemEvento.Viagem.PesoSaida.GetValueOrDefault() -
                                            (y.PesoChegada ??
                                             y.ViagemEvento.Viagem.PesoChegada.GetValueOrDefault()),
                            PesoChegadaPosto = y.ViagemEvento.Viagem.PesoChegada,
                            PesoDiferencaPosto = y.ViagemEvento.Viagem.PesoDiferenca,
                            ValorDifFreteMotorista = y.ViagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo
                                ? y.ViagemEvento?.Viagem.DifFreteMotorista
                                : null,
                            ValorQuebraMercadoria = y.ViagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo
                                ? y.ViagemEvento.Viagem.ValorQuebraMercadoriaCalculado
                                : null,
                            ValorQuebraAbonada = y.ViagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo &&
                                                 y.ViagemEvento.QuebraMercadoriaAbonada.GetValueOrDefault(false)
                                ? y.ViagemEvento.Viagem.ValorQuebraMercadoriaCalculado
                                : (decimal?) null,
                            IdViagem = y.ViagemEvento.Viagem.IdViagem,
                            ValorDesconto = y.ValorDesconto,
                            DescricaoMotivoDesconto = y.DescricaoMotivoDesconto,
                            IdMotivoDesconto = y.IdMotivoDesconto,
                            ObservacaoDesconto = y.ObservacaoDesconto,
                            HabilitarPagamentoCartao = y.ViagemEvento.HabilitarPagamentoCartao,
                            DataDescarga = y.ViagemEvento.Viagem.DataDescarga?.ToString("yyyy-MM-dd")
                        }).ToList()
                });
            }

            return retorno;
        }

        public Protocolo GetComEvento(int idProtocolo)
        {
            var found = _repository.Include(x => x.ProtocoloEventos)
                .FirstOrDefault(x => x.IdProtocolo == idProtocolo);

            if (found == null)
                throw new Exception($"Protocolo {idProtocolo} não encontrado!");

            return found;
        }

        public object ConsultarGridAssociacao(int? idEstabelecimento, int take, int page, OrderFilters order,
            List<QueryFilters> filters, int? idEmpresa, int? idAssociacao = 0)
        {
            var estabelecimentoBase = _estabelecimentoBaseRepository
                .Include(c => c.Estabelecimento)
                .FirstOrDefault(x => x.IdEstabelecimento == idAssociacao);

            var idsEstabelecimentosAssociacao =
                estabelecimentoBase.Estabelecimento.Select(c => c.IdEstabelecimento).ToList();

            var estabelecimentoAssociados = _estabelecimentoService.GetAssociados(idAssociacao ?? 0)?
                .Select(x => x.IdEstabelecimento)
                .ToList();

            var protocolos = _protocoloRepository
                .Include(x => x.ProtocoloEventos)
                .Include(x => x.ProtocoloEventos.Select(y => y.ViagemEvento))
                .Include(x => x.ProtocoloEventos.Select(y => y.ProtocolosEventos_Vinculados))
                .Where(x => (x.ProtocoloEventos.Any(y =>
                                 !y.ProtocolosEventos_Vinculados.Any() &&
                                 y.Status == EStatusProtocoloEvento.Gerado) &&
                             estabelecimentoAssociados.Contains(x.EstabelecimentoBase.IdEstabelecimento) &&
                             !x.GeradoAssociacao
                             && x.TipoDestinatario == ETipoDestinatario.Associacao)
                            || (x.ProtocoloEventos.Any(z =>
                                    !z.ProtocolosEventos_Vinculados.Any() &&
                                    z.Status == EStatusProtocoloEvento.Rejeitado) && x.GeradoAssociacao &&
                                x.IdEstabelecimentoBase == idAssociacao)
                );

            if (idAssociacao.HasValue)
                protocolos = protocolos.Where(x =>
                    idsEstabelecimentosAssociacao.Contains(x.IdEstabelecimentoDestinatario.Value));

            if (idEstabelecimento.HasValue && idEstabelecimento != 0)
                protocolos = protocolos.Where(x => x.IdEstabelecimentoBase == idEstabelecimento.Value);

            if (idEmpresa.HasValue && idEmpresa != 0)
                protocolos = protocolos.Where(x => x.IdEmpresa == idEmpresa.Value);

            protocolos = string.IsNullOrWhiteSpace(order?.Campo)
                ? protocolos.OrderByDescending(x => x.DataGeracao)
                : protocolos.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            protocolos = protocolos.AplicarFiltrosDinamicos<Protocolo>(filters);

            return new
            {
                totalItems = protocolos.Count(),
                items = protocolos.Skip((page - 1) * take).Take(take).Select(x => x)
                    .ToList()
                    .Select(y => new
                    {
                        DataGeracao = y.DataGeracao.ToString("G"),
                        CNPJEstabelecimento = y.EstabelecimentoBase?.CNPJEstabelecimento.ToCNPJFormato(),
                        Nome = y.EstabelecimentoBase?.Descricao,
                        Valor = y.ValorProtocolo,
                        y.IdProtocolo,
                        NumeroEventos = y.ProtocoloEventos?.Count(),
                        ValorTotal = y.ProtocoloEventos?.Where(z => !z.ViagemEvento.HabilitarPagamentoCartao)
                            .Sum(z => z.ValorTotalPagamento ?? 0),
                        y.GeradoAssociacao,

                        subGridOptions = new
                        {
                            data = ConsultarProtocoloEvento(y.IdProtocolo)
                        }
                    })
                    .ToList()
            };
        }

        public List<int> ConsultarIdProtocoloEvento(int IdProtocolo)
        {
            var eventos = _protocoloEventoRepository
                .Where(x => x.IdProtocolo == IdProtocolo && x.Status != EStatusProtocoloEvento.Rejeitado)
                .Include(x => x.ViagemEvento);

            return eventos
                .Select(x => x.IdProtocoloEvento)
                .ToList();
        }


        public ProtocoloEvento GetProtocoloEvento(int IdProtocoloEvento)
        {
            return _protocoloEventoRepository
                .Include(x => x.ViagemEvento)
                .FirstOrDefault(x => x.IdProtocoloEvento == IdProtocoloEvento);
        }

        public List<ProtocoloEvento> GetProtocolosEventos(List<int> ids)
        {
            return _protocoloEventoRepository
                .Where(x => ids.Contains(x.IdProtocoloEvento))
                .Include(x => x.Protocolo)
                .Include(x => x.ViagemEvento)
                .ToList();
        }

        public object ConsultarProtocoloEvento(int IdProtocolo)
        {
            var eventos = _protocoloEventoRepository
                .Where(x => x.IdProtocolo == IdProtocolo
                            && ((x.Status == EStatusProtocoloEvento.Gerado && !x.Protocolo.GeradoAssociacao)
                                || (x.Status == EStatusProtocoloEvento.Rejeitado && x.Protocolo.GeradoAssociacao)
                            ) && !x.ProtocolosEventos_Vinculados.Any())
                .Include(x => x.Protocolo)
                .Include(x => x.ProtocolosEventos_Vinculados)
                .Include(x => x.ViagemEvento);

            return eventos
                .ToList()
                .Select(x => new
                {
                    sel = false,
                    Valor = x.ValorTotalPagamento,
                    TipoEvento = x.ViagemEvento.TipoEventoViagem.DescriptionAttr(),
                    IdProtocoloEvento = x.IdProtocoloEvento,
                    IdProtocolo = x.IdProtocolo,
                    IsRejected = x.Status == EStatusProtocoloEvento.Rejeitado,
                    Motivos = GetMotivosProtocoloEvento(x.IdViagemEvento),
                    PagoNoCartao = x.ViagemEvento.HabilitarPagamentoCartao ? "Sim" : "Não"
                });
        }

        public void SetProtocoloAsAprovado(int idProtocolo)
        {
            bool hasProtocoloEvento = _protocoloRepository
                .Include(x => x.ProtocoloEventos)
                .Any(x => !x.ProtocoloEventos.Any(y => y.IdProtocoloEvento_Vinculado == null) &&
                          !x.GeradoAssociacao && x.IdProtocolo == idProtocolo);

            if (hasProtocoloEvento)
            {
                var protocolo = _protocoloRepository.Get(idProtocolo);

                protocolo.StatusProtocolo = EStatusProtocolo.Aprovado;
                protocolo.DataAprovacao = DateTime.Now;

                _protocoloRepository.Update(protocolo);
            }
        }

        public byte[] GerarRelatorioEtiquetas(List<int> idsProtocolos, string logo)
        {
            var listaDados = new List<RelatorioProtocoloEtiquetasDataType>();

            var protocolos = _protocoloRepository
                .Include(o => o.EstabelecimentoBase)
                .Include(o => o.EstabelecimentoBase.Cidade)
                .Include(o => o.EstabelecimentoBase.Estado)
                .Include(o => o.EstabelecimentoDestinatario)
                .Include(o => o.EstabelecimentoDestinatario.Cidade)
                .Include(o => o.EstabelecimentoDestinatario.Estado)
                .Include(o => o.EmpresaDestinatario)
                .Include(o => o.EmpresaDestinatario.Cidade)
                .Include(o => o.EmpresaDestinatario.Estado)
                .Where(o => idsProtocolos.Contains(o.IdProtocolo));

            foreach (var protocolo in protocolos)
            {
                var relatorioProtocoloDataType = new RelatorioProtocoloEtiquetasDataType();

                if (protocolo.IdEstabelecimentoDestinatario.HasValue &&
                    protocolo.EstabelecimentoDestinatario.Associacao && !protocolo.IdEmpresaDestinatario.HasValue)
                {
                    relatorioProtocoloDataType.Destinatario = protocolo.EstabelecimentoDestinatario?.RazaoSocial;
                    relatorioProtocoloDataType.EnderecoDestinatario = ArrangeEndereco(
                        protocolo.EstabelecimentoDestinatario?.Logradouro,
                        protocolo.EstabelecimentoDestinatario?.Complemento,
                        protocolo.EstabelecimentoDestinatario?.Numero?.ToString(),
                        protocolo.EstabelecimentoDestinatario?.Bairro,
                        protocolo.EstabelecimentoDestinatario?.Cidade?.Nome,
                        protocolo.EstabelecimentoDestinatario?.Estado?.Sigla,
                        protocolo.EstabelecimentoDestinatario?.CEP);
                }
                else if (protocolo.IdEmpresaDestinatario.HasValue)
                {
                    relatorioProtocoloDataType.Destinatario = protocolo.EmpresaDestinatario?.RazaoSocial;
                    relatorioProtocoloDataType.EnderecoDestinatario = ArrangeEndereco(
                        protocolo.EmpresaDestinatario?.Endereco, protocolo.EmpresaDestinatario?.Complemento,
                        protocolo.EmpresaDestinatario?.Numero?.ToString(), protocolo.EmpresaDestinatario?.Bairro,
                        protocolo.EmpresaDestinatario?.Cidade?.Nome, protocolo.EmpresaDestinatario?.Estado?.Sigla,
                        protocolo.EmpresaDestinatario?.CEP);
                }

                relatorioProtocoloDataType.Remetente =
                    $"{protocolo.EstabelecimentoBase?.RazaoSocial} - {protocolo.EstabelecimentoBase?.CNPJEstabelecimento?.ToCNPJFormato()}";
                relatorioProtocoloDataType.EnderecoRemetente = ArrangeEndereco(
                    protocolo.EstabelecimentoBase?.Logradouro, protocolo.EstabelecimentoBase?.Complemento,
                    protocolo.EstabelecimentoBase?.Numero?.ToString(), protocolo.EstabelecimentoBase?.Bairro,
                    protocolo.EstabelecimentoBase?.Cidade?.Nome, protocolo.EstabelecimentoBase?.Estado?.Sigla,
                    protocolo.EstabelecimentoBase?.CEP);

                relatorioProtocoloDataType.IdProtocolo = protocolo.IdProtocolo.ToString();
                relatorioProtocoloDataType.DataGeracao = protocolo.DataGeracao.ToString("dd/MM/yyyy HH:mm");

                var barcode = new Spire.Barcode.WebUI.BarCodeControl {Data = protocolo.IdProtocolo.ToString()};
                var barCodeBytes = ImageToByteArray(barcode.GenerateImage());
                var barCodeStr = Convert.ToBase64String(barCodeBytes);

                relatorioProtocoloDataType.CodigoBarras = barCodeStr;

                listaDados.Add(relatorioProtocoloDataType);
            }

            return new RelatorioProtocoloEtiquetas().GetReport(listaDados, logo);
        }

        public byte[] GerarRelatorioCapa(List<int> idsProtocolos, string logo)
        {
            var listaDados = new List<RelatorioProtocoloCapaDataType>();

            var protocolos = _protocoloRepository
                .Include(o => o.EstabelecimentoBase)
                .Include(o => o.ProtocoloEventos)
                .Include(o => o.ProtocoloEventos.Select(ve => ve.ViagemEvento))
                .Include(o => o.ProtocoloEventos.Select(ve => ve.ViagemEvento.Viagem))
                .Where(o => idsProtocolos.Contains(o.IdProtocolo));

            foreach (var protocolo in protocolos)
            {
                protocolo.ProtocoloEventos = protocolo.ProtocoloEventos.Where(evento => evento.Status != EStatusProtocoloEvento.Rejeitado).ToList();

                var contador = 0;

                if (protocolo.ProtocoloEventos == null || !protocolo.ProtocoloEventos.Any())
                    continue;

                var quantidadeEventos = protocolo.ProtocoloEventos.Count;
                var valorTotal =
                    protocolo
                        .ValorProtocolo; //protocolo.ProtocoloEventos.Where(o => !o.ViagemEvento.HabilitarPagamentoCartao).Sum(o => o.ValorTotalPagamento);
                var tiposEventos = protocolo.ProtocoloEventos
                    .Select(o => o.ViagemEvento.TipoEventoViagem.DescriptionAttr()).Distinct()
                    .Aggregate((i, j) => $"{i}, {j}");

                foreach (var evento in protocolo.ProtocoloEventos)
                {
                    contador++;

                    var dadosRelatorio = new RelatorioProtocoloCapaDataType
                    {
                        IdProtocolo = protocolo.IdProtocolo,
                        DataGeracao = protocolo.DataGeracao.ToString(ConstantesUtils.FormatoDataBrasil),
                        Estabelecimento =
                            $"{protocolo.EstabelecimentoBase?.RazaoSocial} - {protocolo.EstabelecimentoBase?.CNPJEstabelecimento?.ToCNPJFormato()}",
                        ValorTotal = $"{valorTotal:N}",
                        TiposEvento = tiposEventos,
                        Tipo = evento.ViagemEvento?.TipoEventoViagem.DescriptionAttr(),
                        NumeroEventos = quantidadeEventos.ToString(),
                        CteSerie = evento.ViagemEvento?.Viagem.DocumentoCliente,
                        Token = evento.ViagemEvento?.Token,
                        Valor = $"{evento.ViagemEvento?.ValorTotalPagamento:N}",
                        PagoCartao =
                            evento.ViagemEvento?.HabilitarPagamentoCartao != null &&
                            (bool) evento.ViagemEvento?.HabilitarPagamentoCartao
                                ? ConstantesUtils.Sim
                                : ConstantesUtils.Nao,
                        DataEmissao = DateTime.Now.ToString(ConstantesUtils.FormatoDataBrasil),
                        CodigoLinha = contador
                    };

                    listaDados.Add(dadosRelatorio);
                }
            }

            return new RelatorioProtocoloCapa().GetReport(listaDados, logo);
        }

        public bool VerificarPagamentosSemProtocolo(int idEstabelecimentoBase)
        {
            var dataOntem = DateTime.Now.AddDays(-1);
            var dataBusca = new DateTime(dataOntem.Year, dataOntem.Month, dataOntem.Day, 23, 59, 59);

            var viagemEventos = _viagemEventoRepository.Where(o =>
                o.IdEstabelecimentoBase == idEstabelecimentoBase && !o.IdProtocolo.HasValue &&
                o.DataHoraPagamento < dataBusca);

            return viagemEventos.Any();
        }

        public ValidationResult RealizarAnaliseAbono(int idViagemEvento, int idProtocolo, int idUsuario,
            bool? isCartaFrete, int? idMotivoRejeicao, string descricaoMotivo,
            EStatusAnaliseAbono statusAnaliseAbono)
        {
            try
            {
                var protocoloEvento = _repositoryEvento.Include(o => o.ViagemEvento)
                    .FirstOrDefault(o => o.IdViagemEvento == idViagemEvento && o.IdProtocolo == idProtocolo);

                if (protocoloEvento == null)
                    return new ValidationResult().Add("Não foi possível realizar a análise do abono.");

                protocoloEvento.AnaliseAbono = statusAnaliseAbono;
                protocoloEvento.DataHoraAnaliseAbono = DateTime.Now;
                protocoloEvento.IdUsuarioAnaliseAbono = idUsuario;

                if (statusAnaliseAbono == EStatusAnaliseAbono.Recusado && isCartaFrete.HasValue &&
                    isCartaFrete.Value)
                {
                    var viagemEvento = protocoloEvento.ViagemEvento;

                    viagemEvento.IdMotivoRejeicaoAbono = idMotivoRejeicao;
                    viagemEvento.DescricaoRejeicaoAbono = descricaoMotivo;

                    _viagemEventoRepository.Update(viagemEvento);
                }

                _repositoryEvento.Update(protocoloEvento);

                //
                if (statusAnaliseAbono == EStatusAnaliseAbono.Recusado &&
                    !protocoloEvento.ViagemEvento.HabilitarPagamentoCartao)
                {
                    RealizarDesconto(
                        protocoloEvento.IdProtocoloEvento,
                        protocoloEvento.ValorQuebraMercadoria,
                        null,
                        idMotivoRejeicao,
                        "Rejeição abono: " + descricaoMotivo,
                        null, true);

                    var viagem = _viagemRepository.Get(protocoloEvento.ViagemEvento.IdViagem);
                    if (viagem.ValorQuebraMercadoria.GetValueOrDefault() > 0)
                        EnviarPushRejeicaoAbono(viagem.IdEmpresa, viagem.CPFCNPJProprietario, viagem.CPFMotorista,
                            descricaoMotivo, viagem.ValorQuebraMercadoria.GetValueOrDefault());
                }

                //

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        private void EnviarPushRejeicaoAbono(int? empresaId, string cpfCnpjProprietario, string cpfMotorista,
            string motivo, decimal valor)
        {
            try
            {
                //var preUsuarioService = new PreUsuarioService();
                var empresaService = _empresaService;
                var proprietarioService = _proprietarioService;
                //var preUsuarioCnpjService = new PreUsuarioCnpjService();

                var listaIdsPush = new List<string>();

                // Proprietário
                /*var idPush = preUsuarioService
                    .GetAll()
                    .Where(p => p.CPF == cpfCnpjProprietario || p.CNPJ == cpfCnpjProprietario)
                    .Select(p => p.IdPush)
                    .FirstOrDefault();*/

                //var preUsuarioCpnj = preUsuarioCnpjService.GetByCnpj(cpfCnpjProprietario).FirstOrDefault();

                /*if (preUsuarioCpnj != null)
                {
                    var idPushPreUsuarioCnpj = preUsuarioService.GetAll()
                        .Where(o => o.IdUsuario == preUsuarioCpnj.IdUsuario).Select(o => o.IdPush).FirstOrDefault();

                    if (idPushPreUsuarioCnpj.HasValue())
                        listaIdsPush.Add(idPushPreUsuarioCnpj);
                }

                if (idPush.IsNullOrWhiteSpace())
                {
                    var usuarioService = _usuarioService;
                    var usuario = usuarioService.GetByCpf(cpfCnpjProprietario);
                    if (usuario != null && usuario.IdPush.HasValue())
                        listaIdsPush.Add(usuario.IdPush);
                }
                else
                    listaIdsPush.Add(idPush);*/

                // Id push motorista                               
                /*idPush = preUsuarioService
                    .GetAll()
                    .Where(p => p.CPF == cpfMotorista || p.CNPJ == cpfMotorista)
                    .Select(p => p.IdPush)
                    .FirstOrDefault();

                if (idPush.IsNullOrWhiteSpace())
                {
                    var usuarioService = _usuarioService;
                    var usuario = usuarioService.GetByCpf(cpfMotorista);
                    if (usuario != null)
                        idPush = usuario.IdPush;
                }

                if (idPush.HasValue())
                    listaIdsPush.Add(idPush);*/

                // Enviar
                /*if (listaIdsPush.Any())
                {
                    var nomeEmpresa = empresaId.HasValue
                        ? empresaService.All()
                            .Where(e => e.IdEmpresa == empresaId)
                            .Select(e => e.NomeFantasia ?? e.RazaoSocial)
                            .FirstOrDefault()
                        : null;

                    _pushService.Send(
                        listaIdsPush,
                        "Abono rejeitado " + valor.FormatMoney(),
                        ETipoMensagemPush.Warning,
                        $"Solicitação de abono no valor de {valor.FormatMoney()} foi rejeitada." +
                        $"\nMotivo: {motivo}." +
                        $"\nTransportadora: {nomeEmpresa}",
                        null);

                    LogManager.GetCurrentClassLogger().Info(
                        $"Push de rejeição de abono enviado. Id Push: {idPush} - proprietário: {cpfCnpjProprietario} - Motorista: {cpfMotorista} - Valor: {valor.FormatMoney()} - Motivo: {motivo}");
                }
                else
                {
                    LogManager.GetCurrentClassLogger().Info(
                        $"Push de rejeição de abono não enviado por não localizar id push para o usuário com CPF/CNPJ {cpfCnpjProprietario}. Motorista: {cpfMotorista} - Valor: {valor.FormatMoney()} - Motivo: {motivo}");
                }*/
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Fatal(e,
                        $"Erro ao enviar push de rejeição de abono. Proprietário: {cpfCnpjProprietario} - Motorista: {cpfMotorista} - Valor: {valor.FormatMoney()} - Motivo: {motivo}");
            }
        }

        public object GetDadosAnaliseAbono(int idProtocolo, int idViagemEvento)
        {
            decimal? valorPago;

            var eventoSaldo =
                _repositoryEvento.Include(o => o.ViagemEvento).Include(o => o.ViagemEvento.MotivoRejeicaoAbono).Include(o => o.UsuarioAnaliseAbono)
                    .FirstOrDefault(o =>
                        o.ViagemEvento.TipoEventoViagem == ETipoEventoViagem.Saldo &&
                        o.IdViagemEvento == idViagemEvento && o.IdProtocolo == idProtocolo);

            var eventoAbono = _viagemEventoRepository.Include(o => o.MotivoRejeicaoAbono).FirstOrDefault(o =>
                o.IdViagem == eventoSaldo.ViagemEvento.IdViagem && o.TipoEventoViagem == ETipoEventoViagem.Abono);

            var abonoRecusado = eventoSaldo != null && eventoSaldo.AnaliseAbono == EStatusAnaliseAbono.Recusado;

            if (eventoSaldo != null && eventoSaldo.ViagemEvento.HabilitarPagamentoCartao)
                valorPago = abonoRecusado ? eventoAbono?.ValorPagamento : eventoAbono?.ValorTotalPagamento;
            else
                valorPago = eventoSaldo?.ValorQuebraMercadoria;

            return new
            {
                StatusAnalise = eventoSaldo?.AnaliseAbono?.DescriptionAttr(),
                DataAnalise = eventoSaldo?.DataHoraAnaliseAbono?.ToString("dd/MM/yyyy HH:mm"),
                UsuarioAnalise = eventoSaldo?.UsuarioAnaliseAbono?.Nome,
                AbonoRecusado = abonoRecusado,
                Descricao = eventoSaldo != null && eventoSaldo.ViagemEvento.HabilitarPagamentoCartao
                    ? eventoAbono?.MotivoRejeicaoAbono?.Descricao
                    : eventoSaldo?.ViagemEvento?.MotivoRejeicaoAbono?.Descricao,
                DescricaoRejeicaoAbono = eventoSaldo != null && eventoSaldo.ViagemEvento.HabilitarPagamentoCartao
                    ? eventoAbono?.DescricaoRejeicaoAbono
                    : eventoSaldo?.ViagemEvento?.DescricaoRejeicaoAbono,
                ValorTotalPagamento = valorPago
            };
        }
        
        public IQueryable<ProtocoloEvento> GetByIdViagemEvento(int idViagemEvento)
        {
            return _repositoryEvento.Find(x => x.IdViagemEvento == idViagemEvento);
        }

        private static byte[] ImageToByteArray(Image imageIn)
        {
            using (var ms = new MemoryStream())
            {
                imageIn.Save(ms, System.Drawing.Imaging.ImageFormat.Gif);
                return ms.ToArray();
            }
        }

        private string ArrangeEndereco(string endereco, string complemento, string numero, string bairro,
            string cidade, string estado, string cep)
        {
            var enderecoBuilder = string.Empty;

            if (!string.IsNullOrEmpty(endereco))
                enderecoBuilder += endereco;

            if (!string.IsNullOrEmpty(complemento))
                enderecoBuilder += $" ({complemento})";

            if (!string.IsNullOrEmpty(numero))
                enderecoBuilder += $", {numero}";

            if (!string.IsNullOrEmpty(bairro))
                enderecoBuilder += $" - {bairro}";


            if (!string.IsNullOrEmpty(cidade))
                enderecoBuilder += $", {cidade}";

            if (!string.IsNullOrEmpty(estado))
                enderecoBuilder += $" - {estado}";

            if (!string.IsNullOrEmpty(cep))
                enderecoBuilder += $", {cep}";

            return enderecoBuilder;
        }

        private static List<object> GetTotalizadoresGridGeracaoProtocolo(
            IQueryable<IGrouping<ETipoEventoViagem, ViagemEvento>> pagamentosAgrupados)
        {
            var totalizadores = new List<object>();

            decimal? totalGeral = 0;

            var pagamentosArray = pagamentosAgrupados.Select(ve => new
                {
                    Key = ve.Key,
                    Items = ve.Select(evento => new
                    {
                        evento.Viagem,
                        evento.ValorTotalPagamento,
                        evento.ValorPagamento,
                        evento.TipoEventoViagem,
                        evento.HabilitarPagamentoCartao
                    })
                })
                .ToArray();

            foreach (var pagamentoAgrupado in pagamentosArray)
            {
                var total = pagamentoAgrupado.Items.Sum(o => (o.ValorTotalPagamento ?? o.ValorPagamento) +
                                                             (o.TipoEventoViagem ==
                                                              ETipoEventoViagem.Adiantamento &&
                                                              !o.HabilitarPagamentoCartao &&
                                                              !o.Viagem.PedagioBaixado
                                                                 ? o.Viagem.ValorPedagio
                                                                 : 0));

                totalGeral += total;
                totalizadores.Add(new
                    {Label = $"{pagamentoAgrupado.Key.DescriptionAttr()}: ", Text = $"R$ {total:N}"});
            }

            if (pagamentosAgrupados.Any())
                totalizadores.Add(new {Label = $"Total: ", Text = $"{totalGeral:N}"});
                totalizadores.Add(new { Label = $"Total: ", Text = $"R$ {totalGeral:N}" });

            return totalizadores;
        }
    }
    
    /// <summary>
    /// DTO utilizado apenas para apresentar mensagem de erro
    /// </summary>
    class ProtocoloErroDTO
    {
        public int IdProtocolo { get; set; }
        public EStatusProtocoloEvento Status { get; set; }
        public string Token { get; set; }
    }
}
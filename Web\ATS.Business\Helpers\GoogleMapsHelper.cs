﻿using ATS.Domain.Enum;
using ATS.Domain.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Globalization;
using System.Web.Configuration;
using ATS.Domain.Entities;
using ATS.Domain.Models.Google;
using Newtonsoft.Json;
using NLog;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Helpers
{
    public class GoogleMapsHelper
    {
        /// <summary>
        /// Realiza o processamento da resposta da consulta HTTP
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public string GetHttpFileContent(string fileName, string exceptionResult = "unable to connect to server ")
        {
            string sContents;
            string me = string.Empty;

            try
            {
                if (fileName.ToLower().IndexOf("http:", StringComparison.Ordinal) > -1 ||
                    fileName.ToLower().IndexOf("https:", StringComparison.Ordinal) > -1)
                {
                    System.Net.WebClient wc = new System.Net.WebClient();
                    byte[] response = wc.DownloadData(fileName);
                    sContents = System.Text.Encoding.ASCII.GetString(response);
                }
                else
                {
                    System.IO.StreamReader sr = new System.IO.StreamReader(fileName);
                    sContents = sr.ReadToEnd();
                    sr.Close();
                }
            }
            catch
            {
                sContents = exceptionResult;
            }

            return sContents;
        }

        /// <summary>
        /// Realiza o processamento da resposta da consulta HTTP
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public T GetHttpFileContent<T>(string fileName)
        {
            string httpResult = null;
            try
            {
                httpResult = GetHttpFileContent(fileName, null);
                return JsonConvert.DeserializeObject<T>(httpResult);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao deserealizar retorno do google. Http request: " + fileName + "\nRespose: " + httpResult);
                return default(T);
            }
        }

        /// <summary>
        /// Retorna a distância através do Google, dado a origem e o destino
        /// </summary>
        /// <param name="origem">String contendo a descrição do local de origem</param>
        /// <param name="destino">String contendo a descrição do local de destino</param>
        /// <param name="UnMedida">Tipo de medida a ser usado</param>
        /// <returns></returns>
        public int GetDistancia(string origem, string destino, EUnidadeMedidaDistancia UnMedida = EUnidadeMedidaDistancia.KM)
        {
            JObject jsonResult = JObject.Parse(GetHttpFileContent
                ($"http://maps.googleapis.com/maps/api/directions/json?origin={origem}&destination={destino}&sensor=false"));

            try
            {
                int distance = (int) jsonResult.SelectToken("results[0].geometry.location.lat.value");

                if (UnMedida == EUnidadeMedidaDistancia.KM)
                    return (distance/1000);

                return distance;
            }
            catch
            {
                return 0;
            }
        }

        public string GetKeyGoogleConfig()
        {
            try
            {
                return WebConfigurationManager.AppSettings.Get("GOOGLE_KEY");
            }
            catch (Exception)
            {
                return "";
            }
            
        }
        
        /// <summary>
        /// Retorna a latitude e longitude do local de busca
        /// </summary>
        /// <returns></returns>
        public LocalizacaoModel GetLocalizacao(string local, EOrigemConsumoServicoExterno origemConsumoExterno)
        {
            JObject jsonResult = JObject.Parse(GetHttpFileContent
                ($"http://maps.googleapis.com/maps/api/geocode/json?address={local},+BR&sensor=false&key="+ GetKeyGoogleConfig()));

            decimal lat = Convert.ToDecimal(jsonResult.SelectToken("results[0].geometry.location.lat"), new CultureInfo("en-US"));
            decimal lng = Convert.ToDecimal(jsonResult.SelectToken("results[0].geometry.location.lng"), new CultureInfo("en-US"));

            return new LocalizacaoModel
            {
                Latitude  = lat,
                Longitude = lng
            };
        }

        /// <summary>
        /// Retorna a menor distância entre dois pontos
        /// </summary>
        /// <param name="lat1">Latitude início</param>
        /// <param name="lon1">Longitude início</param>
        /// <param name="lat2">Latitude término</param>
        /// <param name="lon2">Longitude término</param>
        /// <param name="unit">Unidade de medida</param>
        /// <returns></returns>
        public double DistanciaGeodesica(double lat1, double lon1, double lat2, double lon2, char unit)
        {
            Func<double, double> ConvertDeg2rad = deg => (deg * Math.PI / 180.0);
            Func<double, double> ConvertRad2deg = rad => (rad / Math.PI * 180.0);

            double theta = lon1 - lon2;

            double dist = Math.Sin(ConvertDeg2rad(lat1)) * Math.Sin(ConvertDeg2rad(lat2))
                + Math.Cos(ConvertDeg2rad(lat1)) * Math.Cos(ConvertDeg2rad(lat2)) * Math.Cos(ConvertDeg2rad(theta));

            dist = Math.Acos(dist);
            dist = ConvertRad2deg(dist);
            dist = dist * 60 * 1.1515;

            if (unit == 'K')
                dist = dist * 1.609344;
            else if (unit == 'N')
                dist = dist * 0.8684;

            return (dist);
        }

        
        
    }
}
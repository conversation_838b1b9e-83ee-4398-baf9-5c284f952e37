﻿using ATS.Application.Application;
using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Mensagem;
using System;
using System.Linq;
using ATS.Application.Interface;

namespace ATS.WS.Services
{
    public class SrvMensagem : SrvBase
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly IMensagemApp _mensagemApp;

        public SrvMensagem(IUsuarioApp usuarioApp, IMensagemApp mensagemApp)
        {
            _usuarioApp = usuarioApp;
            _mensagemApp = mensagemApp;
        }

        public bool  SendBroadcast()
        {
            ValidationResult validation = _mensagemApp.SendBroadcast();

            return validation.IsValid; 
        }

        public object GetMensagensPorCpfCnpjUsuario(string cPFCNPJUsuario, DateTime dataInicio)
        {
            var usu = _usuarioApp.GetPorCNPJCPF(cPFCNPJUsuario);
            if (usu == null)
                throw new Exception($"Nenhum usuário encontrado para o CPF/CNPJ {cPFCNPJUsuario}!");

            var mensagensUsuario = _mensagemApp
                        .ConsultarRecebidos(usu.IdUsuario, dataInicio, new DateTime(2100, 1, 1), string.Empty)
                        .ToList();
            return new Retorno<object>
            {
                Sucesso = true,
                Objeto = mensagensUsuario.Select(c=> new
                {
                    c.IdMensagem,
                    c.Remetente.Nome,
                    c.Assunto,
                    c.ConteudoMobile,
                    c.DataHoraEnvio,
                    c.DataAgendada,
                    c.Recebida,
                    c.MensagemDestinatario.FirstOrDefault(x => x.IdUsuarioDestinatario == usu.IdUsuario)?.DataHoraLido
                })
            };
        }

        internal object AtualizarMensagens(AtualizarMensagemRequest requestData)
        {
            if (requestData?.Mensagens == null || !requestData.Mensagens.Any())
                throw new Exception("É necessário informar no mínimo uma mensagem para atualizar!");

            var usu = _usuarioApp.GetPorCNPJCPF(requestData.CPFCNPJUsuario);
            if (usu == null)
                throw new Exception($"Nenhum usuário encontrado para o CPF/CNPJ {requestData.CPFCNPJUsuario}!");
            
            requestData.Mensagens.ForEach(msg => {
                _mensagemApp.AtualizarMensagem(msg.IdMensagem, usu.IdUsuario, msg.DataHoraLeitura);
            });

            return new Retorno<object>
            {
                Sucesso = true
            };
        }
    }
}
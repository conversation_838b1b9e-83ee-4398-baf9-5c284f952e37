﻿using ATS.Domain.Entities;
using ATS.Domain.Interface.Database.Common;
using System.Collections.Generic;
using ATS.Domain.DTO.Veiculo;

namespace ATS.Domain.Interface.Dapper
{
    public interface IVeiculoDapper : IRepositoryDapper<Veiculo>
    {
        IEnumerable<Veiculo> GetVeiculosVinculadosConjunto(int idVeiculoConjunto);
        VeiculoRntrcDTO GetVeiculoRntrc(string placa, int idEmpresa);
        VeiculoRntrcDTO GetVeiculoRntrc(int idVeiculo);
    }
}

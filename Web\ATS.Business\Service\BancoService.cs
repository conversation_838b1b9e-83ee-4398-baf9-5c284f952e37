using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using ATS.Domain.DTO;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;

namespace ATS.Domain.Service
{
    public class BancoService : ServiceBase, IBancoService
    {
        public object ConsultaGrid(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,ConsultarBancoResponseDTO bancos)
        {
            var grid = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? bancos.Objeto.AsQueryable().OrderByDescending(x => x.Id)
                : bancos.Objeto.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}").AsQueryable();

            grid = grid.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = grid.Count(),
                items = grid.Skip((page - 1) * take).Take(take)
                    .ToList().Select(o => new
                    {
                        o.Id,
                        o.Nome
                    })
            };
        }
    }
}
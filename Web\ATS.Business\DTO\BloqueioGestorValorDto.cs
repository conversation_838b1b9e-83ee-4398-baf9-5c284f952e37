﻿using System.Collections.Generic;
using ATS.Domain.Enum;

namespace ATS.Domain.DTO
{
    public class BloqueioGestorValorDto
    {
        public List<BloqueioGestorValorItem> Portal { get; set; }
        public List<BloqueioGestorValorItem> Api { get; set; }
    }

    public class BloqueioGestorValorItem
    {
        public int IdBloqueioGestorValor { get; set; }
        public string DescricaoBloqueioGestorTipo { get; set; }
        public int IdBloqueioGestorTipo { get; set; }
        public int IdEmpresa { get; set; }
        public int? IdFilial { get; set; }
        public decimal Valor { get; set; }
        public EBloqueioOrigemTipo Origem { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ModuloMenuMap : EntityTypeConfiguration<ModuloMenu>
    {
        public ModuloMenuMap()
        {
            ToTable("MODULO_MENU");

            HasKey(t => new { t.IdModulo, t.IdMenu });

            Property(t => t.IdModulo)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdMenu)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);
        }
    }
}

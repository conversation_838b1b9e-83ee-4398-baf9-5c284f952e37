using System;
using ATS.Domain.Enum;
using FluentValidation;
using FluentValidation.Results;

namespace ATS.WS.Models.Webservice.Request.TipoCavalo
{
    public class TipoCavaloCadastrarRequest
    {
        public int IdTipoCavalo { get; set; }
        public string Nome { get; set; }
        public int?  NumeroEixos { get; set; }
        public ECategoriaTipoCavalo Categoria { get; set; }
        public bool Ativo { get; set; } = true;
        public int? IdEmpresa { get; set; }
        public int? Capacidade { get; set; }
        
        public ValidationResult ValidarEntrada()
        {
            return new TipoCavaloCadastrarRequestValidacao().Validate(this);
        }
    }

    public class TipoCavaloCadastrarRequestValidacao : AbstractValidator<TipoCavaloCadastrarRequest>
    {
        public TipoCavaloCadastrarRequestValidacao()
        {
            RuleFor(o => o.Nome)
                .NotNull().WithMessage(Resx.TipoCavaloCadastrarRequest.NomeVazio)
                .Length(1, 100).WithMessage(Resx.TipoCavaloCadastrarRequest.NomeTamanhoMax);

            RuleFor(o => o.NumeroEixos)
                .Must(ValidarInteiro).WithMessage(Resx.TipoCavaloCadastrarRequest.NumeroEixos);

            RuleFor(o => o.Categoria)
                .Must(ValidarEnum).WithMessage(Resx.TipoCavaloCadastrarRequest.CategoriaInvalida);

            RuleFor(o => o.Capacidade)
                .Must(ValidarInteiro).WithMessage(Resx.TipoCavaloCadastrarRequest.Capacidade);
        }
        
        private static bool ValidarInteiro(int? value)
        {
            if (!value.HasValue) 
                return true;
            
            return value.Value >= 1;
        }

        private static bool ValidarEnum(ECategoriaTipoCavalo categoria)
        {
            return Enum.IsDefined(typeof(ECategoriaTipoCavalo), categoria);
        }
    }
}
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class CombustivelJSLEstabelecimentoBaseMap: EntityTypeConfiguration<CombustivelJSLEstabelecimentoBase>
    {
        public CombustivelJSLEstabelecimentoBaseMap()
        {
            ToTable("COMBUSTIVEL_JSL_ESTABELECIMENTO_BASE");

            HasKey(x => new {x.IdCombustivelJSL, x.IdEstabelecimentoBase});

            HasRequired(c => c.CombustivelJsl)
                .WithMany()
                .HasForeignKey(c => c.IdCombustivelJSL);
            
            HasRequired(c => c.EstabelecimentoBase)
                .WithMany()
                .HasForeign<PERSON>ey(c => c.IdEstabelecimentoBase);
        }
    }
}
﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;

namespace ATS.WS.ControllersATS
{
    public class TipoNotificacaoAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly IFilialApp _filialApp;
        private readonly ITipoNotificacaoApp _tipoNotificacaoApp;
        private readonly SrvTipoNotificacao _srvTipoNotificacao;

        public TipoNotificacaoAtsController(IUserIdentity userIdentity, ITipoNotificacaoApp tipoNotificacaoApp, SrvTipoNotificacao srvTipoNotificacao, IFilialApp filialApp)
        {
            _userIdentity = userIdentity;
            _tipoNotificacaoApp = tipoNotificacaoApp;
            _srvTipoNotificacao = srvTipoNotificacao;
            _filialApp = filialApp;
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int? idFilial, string descricao, int Take, int Page,
            OrderFilters Order, List<QueryFilters> Filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador && _userIdentity.IdEmpresa.HasValue)
                    idEmpresa = _userIdentity.IdEmpresa.Value;

                var tiposNotificacao = _tipoNotificacaoApp.ConsultaGrid(idEmpresa, idFilial, descricao, Take,
                    Page, Order, Filters);

                return ResponderSucesso(tiposNotificacao);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Inativar(int idTipoNotificacao)
        {
            try
            {
                var validationResult = _tipoNotificacaoApp.Inativar(idTipoNotificacao);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Tipo de notificação inativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Reativar(int idTipoNotificacao)
        {
            try
            {
                var validationResult = _tipoNotificacaoApp.Reativar(idTipoNotificacao);

                return !validationResult.IsValid
                    ? ResponderErro(validationResult.ToString())
                    : ResponderSucesso("Tipo de notificação reativado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(TipoNotificacaoRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro("Dados da requisição em formato incorreto.");

                if (@params.IdFilial.HasValue && _userIdentity.IdEmpresa.HasValue &&
                    !_filialApp.PertenceAEmpresa(_userIdentity.IdEmpresa.Value, @params.IdFilial.Value))
                    return ResponderErro("Usuário não autenticado.");

                var cadastrar = _srvTipoNotificacao.Cadastrar(@params, (EPerfil)_userIdentity.Perfil, _userIdentity.IdEmpresa);

                return cadastrar.IsValid
                    ? ResponderSucesso("Dados incluídos com sucesso.")
                    : ResponderErro(cadastrar.ToFormatedMessage());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Editar(TipoNotificacaoRequestModel @params)
        {
            try
            {
                if (@params == null)
                    return ResponderErro("Dados da requisição em formato incorreto.");

                var cadastrar = _srvTipoNotificacao.Editar(@params, (EPerfil)_userIdentity.Perfil, _userIdentity.IdEmpresa);

                return cadastrar.IsValid
                    ? ResponderSucesso("Dados atualizados com sucesso.")
                    : ResponderErro(cadastrar.ToFormatedMessage());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorId(int idTipoNotificacao)
        {
            try
            {
                var tipoChecklist = _tipoNotificacaoApp.Get(idTipoNotificacao);

                tipoChecklist.Empresa = null;
                tipoChecklist.Filial = null;

                return ResponderSucesso(tipoChecklist);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar(int idEmpresa, int? idFilial = null)
        {
            try
            {
                var retorno = _tipoNotificacaoApp.ConsultarPorEmpresaFilial(idEmpresa, idFilial)
                    .Select(x => new
                    {
                        x.IdTipoNotificacao,
                        x.Descricao
                    });

                return ResponderSucesso(retorno);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
    }
}
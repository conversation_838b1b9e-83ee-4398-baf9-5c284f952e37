using System;
using ATS.Domain.Enum;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioProvisaoDTO : FiltrosGridBaseModel
    {
        public DateTime DataInicial { get; set; }
        public DateTime DataFinal { get; set; }
        public EStatusViagemEvento? Status { get; set; }
        public bool HabilitarPagamentoCartao { get; set; } 
        public int? UF {get;set;}
        public int? IdEstabelecimento { get; set; }
        public ETipoEventoViagem? Tipo { get; set; }
    }
}
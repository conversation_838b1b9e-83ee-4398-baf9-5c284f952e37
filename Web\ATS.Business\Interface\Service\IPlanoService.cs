﻿using ATS.Domain.DTO.Plano;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;
using System.Collections.Generic;

namespace ATS.Domain.Interface.Service
{
    public interface IPlanoService : IService<Plano>
    {
        List<PlanoEmpresaDto> GetPlanosAtivos();
        List<PlanoEmpresaDto> GetPlanosEmpresa(int idEmpresa);
        public ValidationResult AddPlanoEmpresa(PlanoEmpresa planoEmpresa);
        public ValidationResult UpdatePlanoEmpresa(PlanoEmpresa planoEmpresa);
        PlanoEmpresa GetPlanoEmpresa(int idPlano, int idEmpresa);
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Models;
using ATS.Data.Context.Interface;
using ATS.Data.Context.Trigger;
using ATS.Domain.Interface.Triggers;
using System;
using System.Data.Entity;
using System.Data.Entity.Core;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Validation;
using System.Text;
using System.Threading.Tasks;
using Autofac;
using TrackerEnabledDbContext;

namespace ATS.Data.Context.Config
{
    public class BaseDbContext : TrackerContext, IDbContext, IDisposable
    {
        public int? CurrentUserId { get; set; }
        public string TokenEmpresa { get; set; } = null;
        public Guid? SessionIdentifier { get; set; } = null;

        //public Guid? TriggerSessionId { get; set; }

        public bool IsDead { get; set; } = false;
        public ILifetimeScope ServiceProvider { get; }


        public BaseDbContext(string connectionStringName, ILifetimeScope serviceProvider, object currentUserId = null)
            : base(connectionStringName)
        {
            ServiceProvider = serviceProvider;
            CurrentUserId = (currentUserId != null ? Convert.ToInt32(currentUserId) : new int?());

            Configuration.LazyLoadingEnabled = false;
            Configuration.AutoDetectChangesEnabled = true;

            //TriggerSessionId = Guid.NewGuid();

        }

        public int SaveChanges<TEntity>(TEntity entity) where TEntity : class
        {
            try
            {
                //TODO create new feature to identify changes in attached objects to run trigger.
                var state = Entry(entity).State;

                var currentOperation_ = TriggerManager.GetCurrentOperationByState(state);

                var triggerManager = TriggerManager.Get<ITrigger<TEntity>>(typeof(TEntity), entity, currentOperation_);
                try
                {
                    if (triggerManager == null)
                        return SaveChanges();

                    var old_ = (state == EntityState.Modified)
                        ? (TEntity)Entry(entity).OriginalValues.ToObject()
                        : null;

                    #region Execute before statement

                    triggerManager.ExecuteBeforeTrigger(currentOperation_, ServiceProvider, entity, old_);

                    #endregion

                    var returnStatement = SaveChanges();

                    triggerManager.ExecuteAfterTrigger(currentOperation_, ServiceProvider, entity, old_);

                    return returnStatement;
                }
                finally
                {
                    TriggerManager.DestroySession(entity);
                }
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        public new IDbSet<TEntity> Set<TEntity>() where TEntity : class
        {
            return base.Set<TEntity>();
        }

        public override Task<int> SaveChangesAsync(int userId)
        {
             try
            {
                string user = string.Empty;
                try
                {
                    var userIdentity = ServiceProvider.Resolve<IUserIdentity>();
                    if (userIdentity != null)
                    {
                        user = "Plataforma:";

                        switch (userIdentity.Origem)
                        {
                            case EUserOrigin.AtsWeb:
                                user += "ATSWEB|";
                                break;
                            case EUserOrigin.AtsWs:
                                user += "WS|";
                                break;
                            default:
                                user += "UNKNOWN|";
                                break;
                        }

                        user += $"IdUsuario:{userIdentity.IdUsuario}|IdEmpresa:{userIdentity.IdEmpresa}|Perfil:{userIdentity.Perfil}";
                    }
                    else
                    {
                        //TODO: ESTE CÓDIGO FOI MANTIDO AQUI, COMO SEGURANÇA PARA QUE CASO OCORRA ALGUM ERRO COM O NOVO MODELO DE AUTENTICAÇÃO, TENHAMOS A BASE AINDA SENDO APLICADA
                        user = (TokenEmpresa != null ? "WS:" + TokenEmpresa : "ATSWEB:" + CurrentUserId);
                    }
                }
                catch (Exception)
                {
                    //ignoramos quando houver qualquer erro ao salvar o usuário
                }

                return base.SaveChangesAsync(user);
            }
            catch (UpdateException e)
            {
                throw e;
            }
            catch (DbEntityValidationException e)
            {
                StringBuilder mensagem = new StringBuilder(string.Empty);
                foreach (var eve in e.EntityValidationErrors)
                {
                    mensagem.AppendLine($"Entidade: {eve.Entry.Entity.GetType().Name} em {eve.Entry.State} identificou os seguintes erros:");
                    foreach (var ve in eve.ValidationErrors)
                        mensagem.AppendLine($"Propriedade {ve.PropertyName}: {ve.ErrorMessage}");
                }

                throw new Exception(mensagem.ToString());
            }
            catch (DbUpdateException e)
            {
                throw e;
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        public override int SaveChanges()
        {
            try
            {
                #region Verificar todas as modificações

                /*
                var contextChanges = ChangeTracker.Entries()
                     .Where(t => t.State == EntityState.Modified)
                     .Select(t =>
                         new
                         {
                             Original = t.OriginalValues.PropertyNames.ToDictionary(pn => pn, pn => t.OriginalValues[pn]),
                             Current  = t.CurrentValues .PropertyNames.ToDictionary(pn => pn, pn => t.CurrentValues[pn]),
                         });
                */

                #endregion

                string user = string.Empty;
                try
                {
                    var userIdentity = ServiceProvider.Resolve<IUserIdentity>();
                    if (userIdentity != null)
                    {
                        user = "Plataforma:";

                        switch (userIdentity.Origem)
                        {
                            case EUserOrigin.AtsWeb:
                                user += "ATSWEB|";
                                break;
                            case EUserOrigin.AtsWs:
                                user += "WS|";
                                break;
                            default:
                                user += "UNKNOWN|";
                                break;
                        }

                        user += $"IdUsuario:{userIdentity.IdUsuario}|IdEmpresa:{userIdentity.IdEmpresa}|Perfil:{userIdentity.Perfil}";
                    }
                    else
                    {
                        //TODO: ESTE CÓDIGO FOI MANTIDO AQUI, COMO SEGURANÇA PARA QUE CASO OCORRA ALGUM ERRO COM O NOVO MODELO DE AUTENTICAÇÃO, TENHAMOS A BASE AINDA SENDO APLICADA
                        user = (TokenEmpresa != null ? "WS:" + TokenEmpresa : "ATSWEB:" + CurrentUserId);
                    }
                }
                catch (Exception)
                {

                    //ignoramos quando houver qualquer erro ao salvar o usuário
                }

                return base.SaveChanges(user);
            }
            catch (UpdateException e)
            {
                throw e;
            }
            catch (DbEntityValidationException e)
            {
                StringBuilder mensagem = new StringBuilder(string.Empty);
                foreach (var eve in e.EntityValidationErrors)
                {
                    mensagem.AppendLine($"Entidade: {eve.Entry.Entity.GetType().Name} em {eve.Entry.State} identificou os seguintes erros:");
                    foreach (var ve in eve.ValidationErrors)
                        mensagem.AppendLine($"Propriedade {ve.PropertyName}: {ve.ErrorMessage}");
                }

                throw new Exception(mensagem.ToString());
            }
            catch (DbUpdateException e)
            {
                throw e;
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        protected override void Dispose(bool disposing)
        {
            IsDead = true;
            //TriggerManager.DestroySession(TriggerSessionId.Value);
            base.Dispose(disposing);
        }
    }
}
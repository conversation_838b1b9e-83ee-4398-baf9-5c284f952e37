﻿using System;
using System.Data.Entity;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class WebhookService : ServiceBase, IWebhookService
    {
        private readonly IWebhookRepository _webhookRepository;

        public WebhookService(IWebhookRepository webhookRepository)
        {
            _webhookRepository = webhookRepository;
        }

        public IQueryable<Webhook> GetAll()
        {
            return _webhookRepository.GetAll();
        }

        public Webhook GetByIdRegistro(int id, WebhookTipoEvento tipo)
        {
            return _webhookRepository.GetAll().FirstOrDefault(webhook => webhook.IdRegistro == id && webhook.Tipo == tipo);
        }

        public void Add(Webhook webhook)
        {
            _webhookRepository.Add(webhook);
        }

        public void Update(Webhook webhook)
        {
            _webhookRepository.Update(webhook);
        }
    }
}
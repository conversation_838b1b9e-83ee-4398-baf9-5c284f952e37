﻿using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Pedagio.GridFaturamentoTAG
{
    public class RelatorioGridFaturamentoTagDataType
    {
        public List<RelatorioGridFaturamentoTagItemDataType> items { get; set; }
    }

    public class RelatorioGridFaturamentoTagItemDataType
    { 
        public string RazaoSocial { get; set; }
        public string Cnpj { get; set; }
        public string ValorTotalPagoValePedagio { get; set; }
        public string ValorTotalNaoPagoValePedagio { get; set; }
        public string ValorEstornoTotalPagoValePedagio { get; set; }
        public string ValorEstornoTotalNaoPagoValePedagio { get; set; }
        public string ValorTotalPagoPedagio { get; set; }
        public string ValorTotalNaoPagoPedagio { get; set; }
        public string ValorEstornoTotalPagoPedagio { get; set; }
        public string ValorEstornoTotalNaoPagoPedagio { get; set; }
        public string TaxaTotalPagoValePedagio { get; set; }
        public string TaxaTotalNaoPagoValePedagio { get; set; }
        public string TaxaEstornoTotalPagoValePedagio { get; set; }
        public string TaxaEstornoTotalNaoPagoValePedagio { get; set; }
        public string TaxaTotalPagoPedagio { get; set; }
        public string TaxaTotalNaoPagoPedagio { get; set; }
        public string TaxaEstornoTotalPagoPedagio { get; set; }
        public string TaxaEstornoTotalNaoPagoPedagio { get; set; }
        public string ValorTotal { get; set; }
    }
}
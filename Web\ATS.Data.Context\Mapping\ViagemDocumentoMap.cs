﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemDocumentoMap : EntityTypeConfiguration<ViagemDocumento>
    {
        public ViagemDocumentoMap()
        {
            ToTable("VIAGEM_DOCUMENTO");

            HasKey(t => t.IdViagemDocumento);

            HasOptional(t => t.Documento)
                .WithMany(t => t.ViagemDocumentos)
                .HasForeignKey(t => t.IdDocumento);

            HasRequired(t => t.ViagemEvento)
                .WithMany(t => t.ViagemDocumentos)
                .HasForeignKey(t => t.IdViagemEvento);
        }
    }
}
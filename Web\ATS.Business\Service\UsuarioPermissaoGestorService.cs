using System;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class UsuarioPermissaoGestorService : ServiceBase, IUsuarioPermissaoGestorService
    {
        private readonly IUsuarioPermissaoGestorRepository _usuarioPermissaoGestorRepository;

        public UsuarioPermissaoGestorService(IUsuarioPermissaoGestorRepository usuarioPermissaoGestorRepository)
        {
            _usuarioPermissaoGestorRepository = usuarioPermissaoGestorRepository;
        }

        public ValidationResult Integrar(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo, bool empresa, bool filial)
        {
            try
            {
                var repository = _usuarioPermissaoGestorRepository;
                var permissionUpdate = repository.GetAll().FirstOrDefault(g => g.IdUsuario == idUsuario && g.IdBloqueioGestorTipo == (int) idBloqueioGestorTipo);
                if (permissionUpdate != null)
                {
                    permissionUpdate.DesbloquearEmpresa = empresa;
                    permissionUpdate.DesbloquearFilial = filial;
                    repository.Update(permissionUpdate);
                
                    return new ValidationResult();
                }

                var newPermission = new UsuarioPermissaoGestor
                {
                    IdUsuario = idUsuario,
                    IdBloqueioGestorTipo = (int) idBloqueioGestorTipo,
                    DesbloquearEmpresa = empresa,
                    DesbloquearFilial = filial
                };
            
                repository.Add(newPermission);
            
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public UsuarioPermissaoGestor GetParametroPermissaoGestor(int idUsuario, EBloqueioGestorTipo idBloqueioGestorTipo)
        {
            var repository = _usuarioPermissaoGestorRepository;
            var permissionUpdate = repository.GetAll().FirstOrDefault(g => g.IdUsuario == idUsuario && g.IdBloqueioGestorTipo == (int) idBloqueioGestorTipo);
            return permissionUpdate;
        }
    }
}
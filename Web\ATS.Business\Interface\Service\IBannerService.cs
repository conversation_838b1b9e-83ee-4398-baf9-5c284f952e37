﻿using ATS.Domain.DTO.Banner;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Interface.Service
{
    public interface IBannerService : IBaseService<IBannerRepository>
    {
        BannerConsultarResponse ConsultarAtual();
        BannerConsultarResponse ConsultarPorId(int idBanner);
        BannerGridResponse ConsultarBanners();
        ValidationResult Visualizar(BannerVisualizarRequest request);
        ValidationResult Integrar(BannerIntegrarRequest request);
        BannerAlterarStatusResponse AlterarStatus(BannerAlterarStatusRequest request);
    }
}

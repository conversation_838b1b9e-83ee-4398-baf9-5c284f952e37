﻿using System;
using System.Net;
using System.Net.Sockets;
using System.Web;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using Extratta.Bloqueio.IP.Task.Events;
using Extratta.CargaAvulsaWorkerService.Events;
using MassTransit;
using SistemaInfo.MicroServices.Rest.Cartao.WebClient;

namespace ATS.WS.Services
{
    /// <summary>
    ///
    /// </summary>
    public class SrvIP : SrvBase
    {
        private readonly IWhiteListIPService _whiteListIPService;
        private readonly IEmpresaService _empresaService;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IPublishEndpoint _publisher;


        public SrvIP(IWhiteListIPService whiteListIPService, IEmpresaService empresaService, IParametrosGenericoService parametrosGenericoService, IPublishEndpoint publisher)
        {
            _whiteListIPService = whiteListIPService;
            _empresaService = empresaService;
            _parametrosGenericoService = parametrosGenericoService;
            _publisher = publisher;
        }

        public bool AnalisarIP(string empresaCnpj, string ip, EOrigemRequisicao origem = EOrigemRequisicao.Todos)
        {
            try
            {
                var verificaIPAtivo = _parametrosGenericoService.GetParametro<bool?>(GLOBAL.AtivarVerificacaoIP, 0) ?? false;

                if (!verificaIPAtivo)
                    return true;

                if (origem == EOrigemRequisicao.ApenasInterno)
                    return _whiteListIPService.IPLiberado(ip, EOrigemRequisicao.ApenasInterno);
                if(origem == EOrigemRequisicao.Parceiro)
                    return _whiteListIPService.IPLiberado(ip, EOrigemRequisicao.Parceiro);

                var empresaId = _empresaService.GetIdByCnpj(empresaCnpj);

                if (empresaId == null)
                    return false;

                var ret = _whiteListIPService.IPLiberado(ip, empresaId.Value, origem);

                if (!ret)
                {
                    var eventQueue = new IpBloqueadoInsertEvent()
                    {
                        IP = ip,
                        IdEmpresa = empresaId.Value,
                        Metodo = HttpContext.Current.Request.FilePath
                    };

                    _publisher.Publish(eventQueue);
                }

                return ret;
            }
            catch (Exception e)
            {
                _logger.Error(e.Message);
                return false;
            }
        }
    }
}
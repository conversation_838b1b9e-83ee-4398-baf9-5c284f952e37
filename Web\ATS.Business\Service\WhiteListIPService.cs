using System.Linq;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class WhiteListIPService : BaseService<IWhiteListIPRepository>, IWhiteListIPService
    {
        public WhiteListIPService(IWhiteListIPRepository repository, IUserIdentity sessionUser) : base(repository, sessionUser)
        {
        }

        public bool IPLiberado(string ip, EOrigemRequisicao origem = EOrigemRequisicao.Todos)
        {
            var query = Repository.Where(c => c.IPV4 == ip);

            query = origem switch
            {
                EOrigemRequisicao.ApenasInterno => query.Where(c => c.Is<PERSON>nterno),
                EOrigemRequisicao.ApenasExterno => query.Where(c => !c.IsInterno),
                EOrigemRequisicao.Parceiro => query.Where(c => c.<PERSON>),
                _ => query
            };

            return query.Any();
        }

        public bool IPLiberado(string ip, int idempresa, EOrigemRequisicao origem = EOrigemRequisicao.Todos)
        {
            var query = Repository.Where(c => c.IPV4 == ip && c.IdEmpresa == idempresa);

            query = origem switch
            {
                EOrigemRequisicao.ApenasInterno => query.Where(c => c.IsInterno),
                EOrigemRequisicao.ApenasExterno => query.Where(c => !c.IsInterno),
                _ => query
            };

            return query.Any();
        }
    }
}
﻿// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class TransferenciaPixGridTimelineResponse
    {
        public int TotalItems { get; set; } = 0;
        public decimal TotalPagamento { get; set; } = 0;
        public decimal TotalRecebimento { get; set; } = 0;
        public decimal TotalMovimentado => TotalRecebimento + TotalPagamento;
        public List<ConsultarTimelinePixAppResponseItem> Items { get; set; } = new ();
    }

    public class ConsultarTimelinePixAppResponseItem
    {
        public string Codigo { get; set; }
        public ETipoTransferenciaPix TipoEnum { get; set; }
        public string Tipo { get; set; }
        public string DocumentoOrigem { get; set; }
        public string DocumentoDestino { get; set; }
        public string NomeOrigem { get; set; }
        public string NomeDestino { get; set; }
        public string DataTransferencia { get; set; }
        public string Valor { get; set; }
        public int? IdTransacaoPixPortal { get; set; }
    }

    public enum ETipoTransferenciaPix
    {
        [EnumMember, Description("Indefinido")]
        Indefinido = 0,
        [EnumMember, Description("Pagamento")]
        Pagamento = 1,
        [EnumMember, Description("Recebimento")]
        Recebimento = 2,
    }
}
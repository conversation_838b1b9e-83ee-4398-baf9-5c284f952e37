﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.DespesasViagem.RelatorioListaDespesasViagem
{
    public class RelatorioDespesasViagem
    {
        public byte[] GetReport(List<RelatorioDespesasViagemDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var dataSources = new Tuple<object, string>(listaDados, "DtsDespesasViagem");

            var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                "ATS.CrossCutting.Reports.DespesasViagem.RelatorioListaDespesasViagem.RelatorioDespesasViagem.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

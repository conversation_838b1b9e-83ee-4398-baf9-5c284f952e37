﻿using System.ComponentModel.DataAnnotations.Schema;
using ATS.Data.Context.Mapping.Common;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class UsuarioEnderecoMap : EnderecoBaseMap<UsuarioEndereco>
    {
        public UsuarioEnderecoMap()
        {
            ToTable("USUARIO_ENDERECO");

            HasKey(t => new { t.IdUsuario, t.IdEndereco });

            Property(t => t.IdUsuario)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdEndereco)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.CEP)
                .IsOptional()
                .HasMaxLength(8);

            Property(t => t.Endereco)
                .IsOptional()
                .HasMaxLength(100);

            Property(t => t.Complemento)
                .HasMaxLength(100);

            Property(t => t.Bairro)
                .IsOptional()
                .HasMaxLength(100);

            HasRequired(t => t.Usuario)
                .WithMany(t => t.Endere<PERSON>)
                .HasForeignKey(d => d.IdUsuario);
        }
    }
}
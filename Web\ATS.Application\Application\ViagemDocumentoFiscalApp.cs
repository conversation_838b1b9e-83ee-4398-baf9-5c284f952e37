﻿using System.Collections.Generic;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class ViagemDocumentoFiscalApp : AppBase, IViagemDocumentoFiscalApp
    {
        private readonly IViagemDocumentoFiscalService _service;

        public ViagemDocumentoFiscalApp(IViagemDocumentoFiscalService service)
        {
            _service = service;
        }

        public List<object> GetByViagem(int idViagem)
        {
            return _service.GetByViagem(idViagem);
        }
    }
}

using ATS.Domain.Enum;

namespace ATS.WS.Models.Common.Request
{
    public class ConsultarEventoResponseModel
    {
        public string DataEmissao { get; set; }
        public int IdEvento { get; set; }
        public ETipoEventoViagem TipoEvento { get; set; }
        public EStatusViagemEvento StatusEvento { get; set; }
        public decimal ValorPagamento { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public string DataPagamento { get; set; }
        public string HoraPagamento { get; set; }
        public string NumeroRecibo { get; set; }
        public string CIOT { get; set; }
        public string DataRejeicaoAbono { get; set; }
        public string DetalhamentoAbono { get; set; }
    }
}
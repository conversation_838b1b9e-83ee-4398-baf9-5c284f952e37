﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;

namespace ATS.Domain.Entities
{
    public class NotificacaoPush
    {
        public int IdNotificacaoPush { get; set; }
        public string Descricao { get; set; }
        public string DescricaoMensagem { get; set; }
        public string Sql { get; set; }

        public int IdEmpresa { get; set; }
        public virtual Empresa Empresa { get; set; }

        public int? IdFilial { get; set; }
        public virtual Filial Filial { get; set; }

        public ENotificacaoPushExecutarRegra MomentoExecucao { get; set; }
        
        public int IdTipoNotificacao { get; set; }
        public virtual TipoNotificacao TipoNotificacao { get; set; }

        public DateTime DataUltimaAtualizacao { get; set; }
        public bool Ativo { get; set; } = true;
        
        public virtual ICollection<NotificacaoPushItem> Items { get; set; }

        public virtual ICollection<NotificacaoPushGrupoUsuario> NotificacaoPushGrupoUsuario { get; set; }
    }
}
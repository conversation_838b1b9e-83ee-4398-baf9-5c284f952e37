﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;

namespace ATS.CrossCutting.Reports.CargaAvulsa
{
    public class RelatorioCargaAvulsa
    {
        public byte[] GetReport(List<RelatorioCargaAvulsaDataType> listaDados, string tipoArquivo, string logo)
        {
//            var path = "file:///" + ReportUtils.CreateLogo(logo);
            
            var parametrizes = new Tuple<string, string, bool>[1];
            parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var bytes = new Base.Reports().GetReport(listaDados, parametrizes, true, "DtsCargaAvulsa", "ATS.CrossCutting.Reports.CargaAvulsa.RelatorioCargaAvulsa.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

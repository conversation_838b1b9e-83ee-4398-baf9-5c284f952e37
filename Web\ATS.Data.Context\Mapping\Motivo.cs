﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class MotivoMap : EntityTypeConfiguration<Motivo>
    {
        public MotivoMap()
        {
            ToTable("MOTIVO");

            HasKey(x => x.IdMotivo);

            HasRequired(x => x.Empresa)
                .WithMany(x => x.MotivosCredenciamento)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Filial)
                .WithMany(x => x.MotivosCredenciamento)
                .HasForeignKey(x => x.IdFilial);
            
        }
    }
}

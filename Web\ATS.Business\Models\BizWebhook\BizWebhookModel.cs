namespace ATS.WS.Models.Webservice.Request.BizWebhook
{
    public class BizWebhookCreatedAccountInfoMessage
    {
        public string ReturnCode { get; set; }
        public string ReturnDescription { get; set; }
    }

    public class BizWebhookCreatedAccountInfo
    {
        public string Id { get; set; }
        public int? IssuerId { get; set; }
        public int? BranchOfficeId { get; set; }
        public int? ProductId { get; set; }
        public int? AffinityGroupId { get; set; }
        public int? AccountNumber { get; set; }
        public string AccountId { get; set; }
        public string RequestId { get; set; }
        public string Document { get; set; }
        public int? DocumentTypeId { get; set; }
        public BizWebhookCreatedAccountInfoMessage MessageResponse { get; set; }
        public string BankId { get; set; }
    }

    public class BizWebhookCreatedAccountRequest
    {
        public int? IssuerId { get; set; }
        public int? BranchOfficeId { get; set; }
        public int? ProductId { get; set; }
        public int? AffinityGroupId { get; set; }
        public BizWebhookCreatedAccountInfo RequestContent { get; set; }
    }
    
    public class BizWebhookTransactionOcurredRequest
    {
        public int? AccountId { get; set; }
        public int? IssuerId { get; set; }
        public int? ProductId { get; set; }
        public int? AffinityGroupId { get; set; }
        public int? BranchOfficeId { get; set; }
        public string Amount { get; set; }
        public string Amountconverted { get; set; }
        public string CardNumber { get; set; }
        public string CardTypeDescription { get; set; }
        public int? CardTypeId { get; set; }
        public int? Currency { get; set; }
        public string Currencyvalue { get; set; }
        public int? EventId { get; set; }
        public int? Installments { get; set; }
        public string Message { get; set; }
        public string OnlineShopping { get; set; }
        public string Period { get; set; }
        public int? PlasticmodelId { get; set; }
        public string Registered { get; set; }
        public int? RuleId { get; set; }
        public string Stablishment { get; set; }
        public string StablishmentCode { get; set; }
        public string TransactionDate { get; set; }
        /// <summary>
        /// 00 - TRANSACAO AUTORIZADA,
        /// 05 - TRANSACAO NEGADA,
        /// 06 - PROBLEMAS OCORRIDOS EM TRANSACOES ELETRONICAS,
        /// 12 - TRANSACAO INVALIDA,
        /// 14 - CARTAO INVALIDO,
        /// 51 - SALDO INSUFICIENTE,
        /// 55 - CODIGO SECRETO INCORRETO,
        /// 57 - TRANSACAO NAO PERMITIDA A ESSE CLIENTE,
        /// 59 - SUSPEITA DE FRAUDE,
        /// 62 - CARTAO BLOQUEADO,
        /// 75 - EXCEDIDO NUMERO DE TENTATIVAS DE SENHAS,
        /// 85 - NOT DECLINED,
        /// 91 - INSTITUICAO TEMPORARIAMENTE FORA DE OPERACAO,
        /// 96 - MAU FUNCIONAMENTO DO SISTEMA
        /// </summary>
        public string TransactionStatusCode { get; set; }
        public string TransactionStatusDescription { get; set; }
        /// <summary>
        /// 1 - Lançamento
        /// 2 - Cancelamento
        /// </summary>
        public int? TransactionTypeCode { get; set; }
        public string TransactionTypeDescription { get; set; }
        public int? TransOrigintypeCode { get; set; }
        public string TransOrigintypeDescription { get; set; }
        public string ZipCode { get; set; }
        public string SendProcessId { get; set; }
        public string TransactionEventId { get; set; }
        /// <summary>
        /// 0100 - Autorização de compra
        /// 0200 - Autorização de Pagamento
        /// 0400 - Desfazimento de autorização
        /// 0420 - Cancelamento de autorização
        /// 0120 - Aviso de compra. 
        /// </summary>
        public int? MTI { get; set; }
        public string MD5OfBody { get; set; }
    }
}
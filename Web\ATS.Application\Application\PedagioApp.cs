using System;
using ATS.Application.Application.Common;
using ATS.Data.Repository.EntityFramework;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Data.Repository.External.SistemaInfo.Cartao;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using Sistema.Framework.Util.Extension;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Application
{
    public class PedagioAppFactoryDependencies
    {
        public PedagioAppFactoryDependencies(IViagemRepository viagemRepository, IEmpresaRepository empresaRepository)
        {
            ViagemRepository = viagemRepository;
            EmpresaRepository = empresaRepository;
        }

        public IViagemRepository ViagemRepository { get; }
        public IEmpresaRepository EmpresaRepository { get; }
    }
    
    public class PedagioApp : AppBase
    {
        private readonly IViagemRepository _viagemRepository;
        private readonly PedagioService _service;

        public PedagioApp(IViagemRepository viagemRepository, int? idEmpresa, string token, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            _viagemRepository = viagemRepository;
            _service = new PedagioService(idEmpresa, token, documentoUsuarioAudit, nomeUsuarioAudit, viagemRepository);
        }
        
        public static PedagioApp CreateByEmpresa(PedagioAppFactoryDependencies dependencies, string cnpj, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var serv = dependencies.EmpresaRepository;
            var token = string.IsNullOrEmpty(cnpj) ? SistemaInfoConsts.TokenAdministradora : serv.GetTokenMicroServices(cnpj);
            var idEmp = serv.GetIdPorCnpj(cnpj);
            return new PedagioApp(dependencies.ViagemRepository, idEmp, token, documentoUsuarioAudit, nomeUsuarioAudit);
        }
        
        public static PedagioApp CreateByEmpresa(PedagioAppFactoryDependencies dependencies, int idEmpresa, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            var serv = dependencies.EmpresaRepository;
            var token = idEmpresa == 0 ? SistemaInfoConsts.TokenAdministradora : serv.GetTokenMicroServices(idEmpresa);
            return new PedagioApp(dependencies.ViagemRepository, idEmpresa, token, documentoUsuarioAudit, nomeUsuarioAudit);
        }
        
        public static PedagioApp CreateByUsuario(PedagioAppFactoryDependencies dependencies, Usuario usuarioLogado)
        {
            if (usuarioLogado.Empresa != null)
                return new PedagioApp(dependencies.ViagemRepository, usuarioLogado.IdEmpresa, usuarioLogado.Empresa.TokenMicroServices, usuarioLogado.CPFCNPJ, usuarioLogado.Nome);

            if (usuarioLogado.Perfil == EPerfil.Administrador)
                return new PedagioApp(dependencies.ViagemRepository, null, SistemaInfoConsts.TokenAdministradora, usuarioLogado.CPFCNPJ, usuarioLogado.Nome);

            throw new CartaoAtsException("Não foi possívei instância PedagioApp (Usuario: {0})".FormatEx(usuarioLogado.IdUsuario));
        }
        
        public CalcularRotaResponseDTO CalcularRota(ConsultaRotaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            return _service.CalcularRota(request, documentoUsuarioAudit, nomeUsuarioAudit);
        }

        public ObterExtratoSemPararResponseDTO GetExtratoSemParar(DateTime datainicio, DateTime datafim)
        {
            return _service.GetExtratoSemParar(datainicio, datafim);
        }
        
        public ComprovanteValePedagioResponse ConsultarDadosComprovante(FornecedorEnum fornecedor, long idViagem)
        {
            return _service.ConsultarDadosComprovante(fornecedor, idViagem);
        }

        public CompraPedagioDTOResponse ConsultarReciboPedagio(int protocoloRequisicao)
        {
            return _service.ConsultarReciboPedagio(protocoloRequisicao);
        }

        public byte[] ConsultarReciboMoedeiro(int protocoloRequisicao)
        {
            return _service.ConsultarReciboMoedeiro(protocoloRequisicao);
        }
        
        public ConsultarNumeroCartaoViagemResponse ConsultarNumeroCartaoViagem(FornecedorEnum fornecedor, string protocoloEnvio)
        {
            return _service.ConsultarNumeroCartaoViagem(fornecedor, protocoloEnvio);
        }
        
        public ConsultarStatusVeiculoSemPararDTO ConsultarStatusVeiculoSemParar(string placa)
        {
            return _service.ConsultarStatusVeiculoSemParar(placa);
        }
        
        public ConsultarPolylineDTO GetPolyline(int codPolyline)
        {
            return _service.GetPolyline(codPolyline);
        }
        
        public ConsultarStatusVeiculoTaggyEdenredDTO ConsultarStatusVeiculoTaggyEdenred(string placa)
        {
            return _service.ConsultarStatusVeiculoTaggyEdenred(placa);
        }
    }
}
﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class VeiculosHistoricoEmpresaMap : EntityTypeConfiguration<VeiculosHistoricoEmpresa>
    {
        public VeiculosHistoricoEmpresaMap()
        {
            ToTable("VEICULOS_HISTORICO_EMPRESA");

            HasKey(o => new { o.IdEmpresa, o.Placa});

            Property(o => o.Placa)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            HasRequired(o => o.Empresa)
                .WithMany(o => o.VeiculosHistoricosEmpresas)
                .HasForeignKey(o => o.IdEmpresa);

            Property(o => o.Placa)
                .IsRequired()
                .HasMaxLength(6);

            Property(o => o.CpfMotorista)
                .IsRequired()
                .HasMaxLength(11);
        }
    }
}

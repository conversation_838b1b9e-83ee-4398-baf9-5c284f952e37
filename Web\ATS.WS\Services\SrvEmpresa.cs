﻿using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.SistemaInfo.Auth;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Webservice.Request.Empresa;
using AutoMapper;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;
using SistemaInfo.Cartoes.Repository.External.SistemaInfo.Auth.Api.Client;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;
using ConsultarEmpresaResponseStatus = SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient.ConsultarEmpresaResponseStatus;
using IntegrarEmpresaResponseStatus = SistemaInfo.MicroServices.Rest.Cartao.ApiClient.IntegrarEmpresaResponseStatus;

namespace ATS.WS.Services
{
    public class SrvEmpresa : SrvBase
    {
        private readonly IParametrosApp _parametrosApp;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly IEmpresaApp _empresaApp;
        private readonly IPlanoApp _planoApp;
        private readonly IBloqueioGestorValorApp _bloqueioGestorValorApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly CartoesServiceArgs _cartoesServiceArgs;
        private readonly IFilialApp _filialApp;
        private readonly IUserIdentity _userIdentity;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;

        public SrvEmpresa(IParametrosApp parametrosApp, IEstabelecimentoBaseService estabelecimentoBaseService, IEmpresaApp empresaApp, IPlanoApp planoApp, 
            IBloqueioGestorValorApp bloqueioGestorValorApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, CartoesServiceArgs cartoesServiceArgs, IFilialApp filialApp, IUserIdentity userIdentity, IParametrosEmpresaService parametrosEmpresaService)
        {
            _parametrosApp = parametrosApp;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _empresaApp = empresaApp;
            _planoApp = planoApp;
            _bloqueioGestorValorApp = bloqueioGestorValorApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _cartoesServiceArgs = cartoesServiceArgs;
            _filialApp = filialApp;
            _userIdentity = userIdentity;
            _parametrosEmpresaService = parametrosEmpresaService;
        }

        public ValidationResult Cadastrar(EmpresaCreateRequest model, string cpfCnpjUsuarioLogado, string nomeUsuarioLogado, int administradoraPlataforma)
        {
            try
            {
                var empresaApp = _empresaApp;

                var empresa = Mapper.Map<EmpresaCreateRequest, Empresa>(model);

                var retorno = empresa.IsValid();

                if (!retorno.IsValid)
                    return retorno;

                retorno = empresaApp.Add(empresa);

                if (!retorno.IsValid) return retorno;

                var empresaRetorno = empresaApp.Get(empresa.CNPJ);

                var empresaIndicadores = new EmpresaIndicadores
                {
                    IdEmpresa = empresaRetorno.IdEmpresa,
                    DataInicioOperacao = model.DataInicioOperacao,
                    TipoCliente = model.TipoCliente,
                    PrevistoMovimentacaoFinanceiraFrete = model.PrevistoMovimentacaoFinanceiraFrete ?? 0m,
                    PrevistoMovimentacaoFinanceiraVPO = model.PrevistoMovimentacaoFinanceiraVPO ?? 0m,
                    PrevistoQuantidadeViagens = model.PrevistoQuantidadeViagens ?? 0,
                    ComercialConta = model.ComercialConta,
                    TMS = model.TMS
                };

                retorno = empresaApp.AddEmpresaIndicadores(empresaIndicadores);

                if (!retorno.IsValid) return retorno;

                foreach (var planoEmpresa in model.PlanosEmpresa)
                {
                    var planoEmpresaNovo = new PlanoEmpresa
                    {
                        IdPlano = planoEmpresa.IdPlano,
                        IdEmpresa = empresaRetorno.IdEmpresa,
                        Ativo = planoEmpresa.Ativo,
                        IdUsuarioCadastro = _userIdentity.IdUsuario,
                        IdUsuarioAtualizacao = _userIdentity.IdUsuario,
                        DataCadastro = DateTime.Now,
                        DataAtualizacao = DateTime.Now
                    };

                    retorno = _planoApp.AddPlanoEmpresa(planoEmpresaNovo);
                    if (!retorno.IsValid) return retorno;
                }

                IntegrarPlataformaMicroServicos(model, empresaRetorno, cpfCnpjUsuarioLogado, nomeUsuarioLogado);
                
                var permiteAlteracaoAlcadasLimites = _parametrosApp.GetPermissaoUsuarioAlterarLimiteAlcadas(_userIdentity.IdUsuario);
                    
                var permiteAlteracaoDadosAdm = _parametrosApp.GetPermitirEdicaoDadosAdministrativosEmpresa(_userIdentity.IdUsuario);
                
                if (model.AlcadasBloqueioGestorValor != null && permiteAlteracaoAlcadasLimites.PermiteEmpresa && permiteAlteracaoDadosAdm)
                {
                    model.AlcadasBloqueioGestorValor.Portal.ForEach(x => x.IdEmpresa = empresa.IdEmpresa);
                    model.AlcadasBloqueioGestorValor.Api.ForEach(x => x.IdEmpresa = empresa.IdEmpresa);

                    _bloqueioGestorValorApp.IntegrarValores(model.AlcadasBloqueioGestorValor);
                }

                if (!retorno.IsValid)
                    return retorno;

                retorno.Add(_parametrosApp.SetAutorizaEstabelecimentosRedeJSL(model.AutorizaEstabelecimentosRedeJSL, empresaRetorno.IdEmpresa));

                retorno.Add(_parametrosApp.SetObrigaRoteirizacaoPedagioViagemEmpresa(model.ObrigaRoteirizacaoPedagioViagem, empresaRetorno.IdEmpresa));

                retorno.Add(_parametrosApp.SetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(model.AcaoSaldoResidualNovoCreditoCartaoPedagio, empresa.IdEmpresa));

                if (_parametrosApp.GetAutorizaEstabelecimentosRedeJSL(empresa.IdEmpresa))
                    retorno.Add(AutorizarEstabelecimentosJsl(empresa.IdEmpresa, administradoraPlataforma));

                if (model.HorasExpiracaoCreditoPedagio.HasValue)
                    retorno.Add(_parametrosApp.SetHorasExpiracaoCreditoPedagio(model.HorasExpiracaoCreditoPedagio.Value, empresa.IdEmpresa));

                retorno.Add(_parametrosApp.SetReemiteCiotPadraoAlteracaoViagem(model.ReemiteCiotPadraoAlteracaoViagem ?? false, empresa.IdEmpresa));

                retorno.Add(_parametrosApp.SetTokenMicroServicoCentralAtendimento(empresa.IdEmpresa, model.TokenMicroServicoCentralAtendimento));

                if (model.PermissoesAtendimentoCartao != null)
                    retorno.Add(_parametrosApp.SetPermissoesEmpresaAtendimentoCartao(empresa.IdEmpresa, model.PermissoesAtendimentoCartao));
                
                retorno.Add(_parametrosApp.SetRegistrarValePedagio(empresa.IdEmpresa, model.RegistrarValePedagio));
                retorno.Add(_parametrosApp.SetValorMinimoAlertaSaldoContaFrete(empresaRetorno.IdEmpresa,model.ValorSaldoContaFreteMinimoNotificacaoEmail));
                retorno.Add(_parametrosApp.SetValorMinimoAlertaSaldoContaPix(empresaRetorno.IdEmpresa,model.ValorSaldoContaPixMinimoNotificacaoEmail));
                retorno.Add(_parametrosApp.SetUtilizaRelatoriosOfx(empresaRetorno.IdEmpresa,model.UtilizaRelatoriosOfx));
                retorno.Add(_parametrosApp.SetUtilizaUtilizaRoteirizacaoPorPolyline(empresaRetorno.IdEmpresa,model.UtilizaRoteirizacaoPorPolyline));
                retorno.Add(_parametrosApp.SetCodigoOfx(model.CodOfx, empresa.IdEmpresa));

                retorno.Add(_parametrosApp.SetDefaultIntegracaoTipoRodagemDupla(empresa.IdEmpresa, model.DefaultIntegracaoTipoRodagemDupla));
                retorno.Add(_parametrosApp.SetMantemViagemAbertaAposBaixaDoUltimoEvento(empresaRetorno.IdEmpresa,model.MantemViagemAbertaAposBaixaDoUltimoEvento));
                retorno.Add(_parametrosApp.SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(empresaRetorno.IdEmpresa,model.RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe));
                retorno.Add(_parametrosApp.SetNaoBaixarParcelasDeposito(empresaRetorno.IdEmpresa,model.NaoBaixarParcelasDeposito));

                #region Parametros Tag Extratta
                
                //Esse parametro fica no MS Pedagio, não no portal
                retorno.Add(_parametrosApp.SetTagExtrattaTaxaVpo(empresa.IdEmpresa, model.TagExtrattaTaxaVpo));
                retorno.Add(_parametrosApp.SetTagExtrattaValorTag(empresa.IdEmpresa, model.TagExtrattaValorTag));
                retorno.Add(_parametrosApp.SetTagExtrattaValorMensalidade(empresa.IdEmpresa, model.TagExtrattaValorMensalidade));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarValor(empresa.IdEmpresa, model.TagExtrattaProvisionarValor));
                retorno.Add(_parametrosApp.SetTagExtrattaUtilizaTaxaPedagio(empresa.IdEmpresa, model.TagExtrattaUtilizaTaxaPedagio));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarTaxa(empresa.IdEmpresa, model.TagExtrattaProvisionarTaxa));
                retorno.Add(_parametrosApp.SetTagExtrattaFaixaToleranciaNotificacaoEmail(empresa.IdEmpresa, model.TagExtrattaFaixaToleranciaNotificacaoEmail));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarTaxaPedagioWebhook(empresa.IdEmpresa, model.TagExtrattaProvisionarTaxaPedagioWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarValorPedagioWebhook(empresa.IdEmpresa, model.TagExtrattaProvisionarValorPedagioWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaSaldoMinimoContaFreteWebhook(empresa.IdEmpresa, model.TagExtrattaSaldoMinimoContaFreteWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaBloquearTagLoteWebhook(empresa.IdEmpresa, model.TagExtrattaBloquearTagLoteWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaBloquearTagUnitariaWebhook(empresa.IdEmpresa, model.TagExtrattaBloquearTagUnitariaWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaValorSubstituicao(empresa.IdEmpresa, model.TagExtrattaValorSubstituicao));
                retorno.Add(_parametrosApp.SetTagExtrattaValorRecargaConta(empresa.IdEmpresa, model.TagExtrattaValorRecargaConta));
                retorno.Add(_parametrosApp.SetTagExtrattaEstornarPedagio(empresa.IdEmpresa, model.TagExtrattaEstornarValorPedagioWebhook));
                retorno.Add(_parametrosApp.SetSolicitaAprovacaoGestorCargaAvulsaLote(empresa.IdEmpresa, model.AprovacaoGestorCargaAvulsaLote));
                retorno.Add(_parametrosApp.SetSolicitaAprovacaoGestorCargaAvulsaUnitario(empresa.IdEmpresa, model.AprovacaoGestorCargaAvulsaUnitario));
                retorno.Add(_parametrosApp.SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(empresa.IdEmpresa, model.AprovacaoGestorCargaAvulsaIntegracao));
                
                #endregion
                
                #region Parametros Credenciais Extratta Pedágio
                
                retorno.Add(_parametrosApp.SetMoveMaisExtrattaTaxaVpo(empresa.IdEmpresa, model.MoveMaisExtrattaTaxaVpo));
                retorno.Add(_parametrosApp.SetViaFacilExtrattaTaxaVpo(empresa.IdEmpresa, model.ViaFacilExtrattaTaxaVpo));
                retorno.Add(_parametrosApp.SetVeloeExtrattaTaxaVpo(empresa.IdEmpresa, model.VeloeExtrattaTaxaVpo));

                retorno.Add(_parametrosApp.SetHubVeloeProvisionarValor(empresa.IdEmpresa, model.HubVeloeProvisionarValor));
                retorno.Add(_parametrosApp.SetHubVeloeProvisionarTaxa(empresa.IdEmpresa, model.HubVeloeProvisionarTaxa));
                
                retorno.Add(_parametrosApp.SetHubViaFacilProvisionarTaxa(empresa.IdEmpresa, model.HubViaFacilProvisionarTaxa));
                retorno.Add(_parametrosApp.SetHubViaFacilProvisionarValor(empresa.IdEmpresa, model.HubViaFacilProvisionarValor));
                
                retorno.Add(_parametrosApp.SetHubMoveMaisProvisionarValor(empresa.IdEmpresa, model.HubMoveMaisProvisionarValor));
                retorno.Add(_parametrosApp.SetHubMoveMaisProvisionarTaxa(empresa.IdEmpresa, model.HubMoveMaisProvisionarTaxa));
                
                retorno.Add(_parametrosApp.SetHubConectCarMaisProvisionarValor(empresa.IdEmpresa, model.HubConectCarProvisionarValor));
                retorno.Add(_parametrosApp.SetHubConectCarProvisionarTaxa(empresa.IdEmpresa, model.HubConectCarProvisionarTaxa));

                retorno.Add(_parametrosApp.SetHubTaggyEdenredProvisionarValor(empresa.IdEmpresa, model.HubTaggyEdenredProvisionarValor));
                retorno.Add(_parametrosApp.SetHubTaggyEdenredProvisionarTaxa(empresa.IdEmpresa, model.HubTaggyEdenredProvisionarTaxa));
                retorno.Add(_parametrosApp.SetTaggyEdenredExtrattaTaxaVpo(empresa.IdEmpresa, model.TaggyEdenredExtrattaTaxaVpo));
                
                #endregion
                
                #region Parametros ConectCar Extratta
                
                retorno.Add(_parametrosApp.SetConectCarExtrattaTaxaVpo(empresa.IdEmpresa, model.ConectCarExtrattaTaxaVpo));
                
                #endregion
                
                #region Parametros Extratta Pay
                
                retorno.Add(_parametrosApp.SetBloqueiaCargaAvulsaDuplicada(empresa.IdEmpresa, model.BloqueiaCargaAvulsaDuplicada));
                
                #endregion

                return retorno;
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        private ValidationResult AutorizarEstabelecimentosJsl(int idempresa, int administradoraPlataforma)
        {
            var validationResult = new ValidationResult();
            var estabelecimentoBaseApp = new EstabelecimentoBaseApp(_estabelecimentoBaseService);

            var estabelecimentoBases = estabelecimentoBaseApp.GetEstabelecimentosJSL().Select(c => c.IdEstabelecimento).ToList();

            foreach (var estabelecimentoBase in estabelecimentoBases)
                validationResult.Add(estabelecimentoBaseApp.AutorizarEstabelecimentoJSLParaEmpresa(estabelecimentoBase, idempresa, administradoraPlataforma));

            return validationResult;
        }
        
        public ValidationResult Editar(EmpresaCreateRequest model, string cpfCnpjUsuarioLogado, string nomeUsuarioLogado, int administradoraPlataforma)
        {
            try
            {
                if (model.DiasDataExpiracaoPedagioViaFacil < 1 || model.DiasDataExpiracaoPedagioViaFacil > 30)
                    return new ValidationResult().Add("Valor de dias para data de expiração do pedágio não está entre 1 a 30.");

                if (!model.IdEmpresa.HasValue)
                    return new ValidationResult().Add("Id de empresa não informado.");

                var empresa = _empresaApp.GetWithChilds(model.IdEmpresa.Value);
                ReflectionHelper.CopyProperties(model, empresa);

                //Reflection não copia este parâmetro
                empresa.PeriodoExpiracaoChat = model.PeriodoExpiracaoChat;
                empresa.PercentualTransferenciaMotorista = !string.IsNullOrEmpty(model.PercentualTransferenciaMotoristaStr)
                    ? decimal.Parse(model.PercentualTransferenciaMotoristaStr.Replace(".", "").Replace(",", "."))
                    : new decimal();
                empresa.PagamentoFreteToleranciaPesoChegadaMais = !string.IsNullOrEmpty(model.PagamentoFreteToleranciaPesoChegadaMaisStr)
                    ? double.Parse((model.PagamentoFreteToleranciaPesoChegadaMaisStr ?? "").Replace("%", ""))
                    : new double?();
                empresa.PagamentoFreteToleranciaPesoChegadaMenos = !string.IsNullOrEmpty(model.PagamentoFreteToleranciaPesoChegadaMenosStr)
                    ? double.Parse((model.PagamentoFreteToleranciaPesoChegadaMenosStr ?? "").Replace("%", ""))
                    : new double?();
                empresa.AgrupaProtocoloMesmoEvento = model.AgrupaProtocoloMesmoEvento;
                empresa.ValidaChaveBaixaEvento = model.ValidaChaveBaixaEvento;
                var tempValidadeChave = 0;
                var tempoValidadeChaveBool = !string.IsNullOrEmpty(model.TempoValidadeChaveStr)
                                             && int.TryParse(model.TempoValidadeChaveStr.Replace(".", ""), out tempValidadeChave);
                empresa.TempoValidadeChave = tempoValidadeChaveBool ? tempValidadeChave : 0;
                var tempValidaChaveEstabelecimento = 0;
                var tempoValidaChaveEstabelecimentoBool = !string.IsNullOrEmpty(model.TempoValidadeChaveEstabelecimentoStr)
                                                          && int.TryParse(model.TempoValidadeChaveEstabelecimentoStr.Replace(".", ""), out tempValidaChaveEstabelecimento);
                empresa.MinutosValidadeHash = tempoValidaChaveEstabelecimentoBool ? tempValidaChaveEstabelecimento : 0;
                var diasParaBloquear = 0;
                var diasParaBloquearPgtBool = !string.IsNullOrEmpty(model.DiasParaBloquearPagtoStr) && int.TryParse(model.DiasParaBloquearPagtoStr.Replace(".", ""), out diasParaBloquear);
                empresa.DiasParaBloquearPagto = diasParaBloquearPgtBool ? diasParaBloquear : 0;
                var prazoParaDocs = 0;
                var prazoParaInformarDocsBool = !string.IsNullOrEmpty(model.PrazoParaInformarDocumentosStr) &&
                                                int.TryParse(model.PrazoParaInformarDocumentosStr.Replace(".", ""), out prazoParaDocs);
                empresa.PrazoParaInformarDocumentos = prazoParaInformarDocsBool ? prazoParaDocs : new int?();
                empresa.WebHookProtocoloEndPoint = model.WebHookProtocoloEndPoint;
                empresa.WebHookProtocoloHeaders = model.WebHookProtocoloHeaders;
                empresa.DiasExpiracaoSaldoPedagio = model.DiasExpiracaoSaldoPedagio;
                empresa.InformacoesTransferenciaBancaria = model.InformacoesTransferenciaBancaria;
                empresa.PeriodoBloqueioEventosAberto = model.PeriodoBloqueioEventosAberto;
                empresa.ValidaChaveMHBaixaEvento = model.ValidaChaveMHBaixaEvento;
                empresa.TipoInscricaoEmpresa = model.TipoInscricaoEmpresa;
                empresa.CodigoBancoCompensacao = model.CodigoBancoCompensacao;
                empresa.NumeroInscricaoEmpresa = model.NumeroInscricaoEmpresa;
                empresa.AgenciaMantenedora = model.AgenciaMantenedora;
                empresa.DigitoVerificaConta = model.DigitoVerificaConta;
                empresa.DigitoVerificaContaAgConta = model.DigitoVerificaContaAgConta;
                empresa.Modulos = new List<EmpresaModulo>();

                if (model.ApresentaPontoReferencia != null)
                {
                    empresa.ApresentaPontoReferencia = model.ApresentaPontoReferencia.Value;
                }

                if (model.TempoParada == null)
                {
                    empresa.TempoParada = 15;
                }

                if (model.Modulos != null)
                    foreach (var modulo in model.Modulos)
                        empresa.Modulos.Add(new EmpresaModulo
                        {
                            IdModulo = modulo.IdModulo
                        });

                /*if (model.EmpresaIMEIs != null)
                    foreach (var empresaImei in model.EmpresaIMEIs)
                        empresa.EmpresaIMEIs.Add(new EmpresaIMEI
                        {
                            IdEmpresaIMEI = empresaImei.IdEmpresaImei,
                            IMEI = empresaImei.Imei
                        });*/

                var retorno = empresa.IsValid();
                if (!retorno.IsValid)
                    return retorno;

                var empresaApp = _empresaApp;
                retorno = empresaApp.Update(empresa);

                if (model.AlcadasBloqueioGestorValor != null)
                    _bloqueioGestorValorApp.IntegrarValores(model.AlcadasBloqueioGestorValor);

                if (!retorno.IsValid) return retorno;

                var empresaRetorno = empresaApp.Get(empresa.CNPJ);

                var empresaIndicadores = empresaApp.GetEmpresaIndicadores(empresaRetorno.IdEmpresa);

                if (empresaIndicadores == null)
                {
                    empresaIndicadores = new EmpresaIndicadores
                    {
                        IdEmpresa = empresaRetorno.IdEmpresa,
                        DataInicioOperacao = model.DataInicioOperacao,
                        TipoCliente = model.TipoCliente,
                        PrevistoMovimentacaoFinanceiraFrete = model.PrevistoMovimentacaoFinanceiraFrete ?? 0m,
                        PrevistoMovimentacaoFinanceiraVPO = model.PrevistoMovimentacaoFinanceiraVPO ?? 0m,
                        PrevistoQuantidadeViagens = model.PrevistoQuantidadeViagens ?? 0,
                        ComercialConta = model.ComercialConta,
                        TMS = model.TMS
                    };

                    retorno = empresaApp.AddEmpresaIndicadores(empresaIndicadores);
                }
                else
                {
                    empresaIndicadores.DataInicioOperacao = model.DataInicioOperacao;
                    empresaIndicadores.TipoCliente = model.TipoCliente;
                    empresaIndicadores.PrevistoMovimentacaoFinanceiraFrete = model.PrevistoMovimentacaoFinanceiraFrete ?? 0m;
                    empresaIndicadores.PrevistoMovimentacaoFinanceiraVPO = model.PrevistoMovimentacaoFinanceiraVPO ?? 0m;
                    empresaIndicadores.PrevistoQuantidadeViagens = model.PrevistoQuantidadeViagens ?? 0;
                    empresaIndicadores.ComercialConta = model.ComercialConta;
                    empresaIndicadores.TMS = model.TMS;

                    retorno = empresaApp.UpdateEmpresaIndicadores(empresaIndicadores);
                }

                if (!retorno.IsValid) return retorno;

                foreach (var planoEmpresa in model.PlanosEmpresa)
                {
                    var planoEmpresaAtual = _planoApp.GetPlanoEmpresa(planoEmpresa.IdPlano, empresaRetorno.IdEmpresa);

                    if (planoEmpresaAtual == null)
                    {
                        var planoEmpresaNovo = new PlanoEmpresa
                        {
                            IdPlano = planoEmpresa.IdPlano,
                            IdEmpresa = empresaRetorno.IdEmpresa,
                            Ativo = planoEmpresa.Ativo,
                            IdUsuarioCadastro = _userIdentity.IdUsuario,
                            IdUsuarioAtualizacao = _userIdentity.IdUsuario,
                            DataCadastro = DateTime.Now,
                            DataAtualizacao = DateTime.Now
                        };

                        _planoApp.AddPlanoEmpresa(planoEmpresaNovo);
                    }
                    else if (planoEmpresaAtual.Ativo != planoEmpresa.Ativo)
                    {
                        planoEmpresaAtual.Ativo = planoEmpresa.Ativo;
                        planoEmpresaAtual.IdUsuarioAtualizacao = _userIdentity.IdUsuario;
                        planoEmpresaAtual.DataAtualizacao = DateTime.Now;

                        _planoApp.UpdatePlanoEmpresa(planoEmpresaAtual);
                    }
                }

                IntegrarPlataformaMicroServicos(model, empresaRetorno, cpfCnpjUsuarioLogado, nomeUsuarioLogado);

                if (!retorno.IsValid)
                    return retorno;

                retorno.Add(_parametrosApp.SetAutorizaEstabelecimentosRedeJSL(model.AutorizaEstabelecimentosRedeJSL, empresaRetorno.IdEmpresa));

                retorno.Add(_parametrosApp.SetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(model.AcaoSaldoResidualNovoCreditoCartaoPedagio, empresa.IdEmpresa));

                retorno.Add(_parametrosApp.SetObrigaRoteirizacaoPedagioViagemEmpresa(model.ObrigaRoteirizacaoPedagioViagem, empresa.IdEmpresa));
                
                retorno.Add(_parametrosApp.SetParametroEmpresaGridListarCnpjDespesasViagem(model.ListarCnpjGridDespesasViagem, empresa.IdEmpresa));
                
                if (_parametrosApp.GetAutorizaEstabelecimentosRedeJSL(empresa.IdEmpresa))
                    retorno.Add(AutorizarEstabelecimentosJsl(empresa.IdEmpresa, administradoraPlataforma));

                if (model.HorasExpiracaoCreditoPedagio.HasValue)
                    retorno.Add(_parametrosApp.SetHorasExpiracaoCreditoPedagio(model.HorasExpiracaoCreditoPedagio.Value, empresa.IdEmpresa));

                if (!string.IsNullOrWhiteSpace(model.EmailsAlertaCiotAgregado))
                {
                    var emails = model.EmailsAlertaCiotAgregado.Split(';');
                    foreach (var email in emails)
                        if (!EmailHelper.IsValidEmail(email))
                            retorno.Add($"E-mail {email} inválido para alertas de CIOT agregado. Favor verificar.");

                    if (!retorno.IsValid)
                        return retorno;
                    
                }

                retorno.Add(_parametrosApp.SetEmailsAlertaCiotAgregado(model.EmailsAlertaCiotAgregado, empresaRetorno.IdEmpresa));
                retorno.Add(_parametrosApp.SetDiasCancelamentoViagem(model.DiasCancelamentoViagem, empresaRetorno.IdEmpresa));
                retorno.Add(_parametrosApp.SetReemiteCiotPadraoAlteracaoViagem(model.ReemiteCiotPadraoAlteracaoViagem ?? false, empresaRetorno.IdEmpresa));
                retorno.Add(_parametrosApp.SetMostrarHeaderArquivoCsv(model.MostrarHeaderArquivoCsv ?? false, empresa.IdEmpresa));
                retorno.Add(_parametrosApp.SetSeparadorArquivoCsv(model.SeparadorArquivoCsv, empresa.IdEmpresa));
                retorno.Add(_parametrosApp.SetReemiteCiotPadraoAlteracaoViagem(model.ReemiteCiotPadraoAlteracaoViagem ?? false, empresa.IdEmpresa));
                retorno.Add(_parametrosApp.SetTokenMicroServicoCentralAtendimento(empresa.IdEmpresa, model.TokenMicroServicoCentralAtendimento));
                retorno.Add(_parametrosApp.SetRegistrarValePedagio(empresa.IdEmpresa, model.RegistrarValePedagio));
                retorno.Add(_parametrosApp.SetRealizaTriagemEstabelecimentoInterno(empresa.IdEmpresa, model.RealizaTriagemEstabelecimentoInterno));
                retorno.Add(_parametrosApp.SetValorMinimoAlertaSaldoContaFrete(empresaRetorno.IdEmpresa,model.ValorSaldoContaFreteMinimoNotificacaoEmail));
                retorno.Add(_parametrosApp.SetValorMinimoAlertaSaldoContaPix(empresaRetorno.IdEmpresa,model.ValorSaldoContaPixMinimoNotificacaoEmail));
                retorno.Add(_parametrosApp.SetMantemViagemAbertaAposCancelamentoDoUltimoEvento(empresa.IdEmpresa, model.MantemViagemAbertaAposCancelamentoDoUltimoEvento));
                retorno.Add(_parametrosApp.SetPermiteVincularCartaoComCpfFicticio(empresa.IdEmpresa, model.PermiteVincularCartaoComCpfFicticio));
                retorno.Add(_parametrosApp.SetUtilizaRelatoriosOfx(empresa.IdEmpresa, model.UtilizaRelatoriosOfx));
                retorno.Add(_parametrosApp.SetSolicitaAprovacaoGestorCargaAvulsaLote(empresa.IdEmpresa, model.AprovacaoGestorCargaAvulsaLote));
                retorno.Add(_parametrosApp.SetSolicitaAprovacaoGestorCargaAvulsaUnitario(empresa.IdEmpresa, model.AprovacaoGestorCargaAvulsaUnitario));
                retorno.Add(_parametrosApp.SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(empresa.IdEmpresa, model.AprovacaoGestorCargaAvulsaIntegracao));
                retorno.Add(_parametrosApp.SetMantemViagemAbertaAposBaixaDoUltimoEvento(empresa.IdEmpresa, model.MantemViagemAbertaAposBaixaDoUltimoEvento));
                retorno.Add(_parametrosApp.SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(empresa.IdEmpresa, model.RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe));
                retorno.Add(_parametrosApp.SetNaoBaixarParcelasDeposito(empresa.IdEmpresa, model.NaoBaixarParcelasDeposito));
                retorno.Add(_parametrosApp.SetUtilizaUtilizaRoteirizacaoPorPolyline(empresaRetorno.IdEmpresa,model.UtilizaRoteirizacaoPorPolyline));
                retorno.Add(_parametrosApp.SetCodigoOfx(model.CodOfx, empresa.IdEmpresa));

                #region Parametros Tag Extratta
                
                //Esse parametro fica no MS Pedagio, não no portal
                retorno.Add(_parametrosApp.SetTagExtrattaTaxaVpo(empresa.IdEmpresa, model.TagExtrattaTaxaVpo));
                retorno.Add(_parametrosApp.SetTagExtrattaValorTag(empresa.IdEmpresa, model.TagExtrattaValorTag));
                retorno.Add(_parametrosApp.SetTagExtrattaValorMensalidade(empresa.IdEmpresa, model.TagExtrattaValorMensalidade));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarValor(empresa.IdEmpresa, model.TagExtrattaProvisionarValor));
                retorno.Add(_parametrosApp.SetTagExtrattaUtilizaTaxaPedagio(empresa.IdEmpresa, model.TagExtrattaUtilizaTaxaPedagio));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarTaxa(empresa.IdEmpresa, model.TagExtrattaProvisionarTaxa));
                retorno.Add(_parametrosApp.SetTagExtrattaFaixaToleranciaNotificacaoEmail(empresa.IdEmpresa, model.TagExtrattaFaixaToleranciaNotificacaoEmail));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarTaxaPedagioWebhook(empresa.IdEmpresa, model.TagExtrattaProvisionarTaxaPedagioWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaProvisionarValorPedagioWebhook(empresa.IdEmpresa, model.TagExtrattaProvisionarValorPedagioWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaSaldoMinimoContaFreteWebhook(empresa.IdEmpresa, model.TagExtrattaSaldoMinimoContaFreteWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaBloquearTagLoteWebhook(empresa.IdEmpresa, model.TagExtrattaBloquearTagLoteWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaBloquearTagUnitariaWebhook(empresa.IdEmpresa, model.TagExtrattaBloquearTagUnitariaWebhook));
                retorno.Add(_parametrosApp.SetTagExtrattaValorSubstituicao(empresa.IdEmpresa, model.TagExtrattaValorSubstituicao));
                retorno.Add(_parametrosApp.SetTagExtrattaValorRecargaConta(empresa.IdEmpresa, model.TagExtrattaValorRecargaConta));
                retorno.Add(_parametrosApp.SetTagExtrattaEstornarPedagio(empresa.IdEmpresa, model.TagExtrattaEstornarValorPedagioWebhook));
                
                #endregion
                
                if (model.PermissoesAtendimentoCartao != null)
                    retorno.Add(_parametrosApp.SetPermissoesEmpresaAtendimentoCartao(empresa.IdEmpresa, model.PermissoesAtendimentoCartao));
                
                retorno.Add(_parametrosApp.SetDefaultIntegracaoTipoRodagemDupla(empresa.IdEmpresa, model.DefaultIntegracaoTipoRodagemDupla));

                #region Parametros Credenciais Extratta Pedágio
                
                retorno.Add(_parametrosApp.SetMoveMaisExtrattaTaxaVpo(empresa.IdEmpresa, model.MoveMaisExtrattaTaxaVpo));
                retorno.Add(_parametrosApp.SetViaFacilExtrattaTaxaVpo(empresa.IdEmpresa, model.ViaFacilExtrattaTaxaVpo));
                retorno.Add(_parametrosApp.SetVeloeExtrattaTaxaVpo(empresa.IdEmpresa, model.VeloeExtrattaTaxaVpo));
                
                retorno.Add(_parametrosApp.SetHubVeloeProvisionarValor(empresa.IdEmpresa, model.HubVeloeProvisionarValor));
                retorno.Add(_parametrosApp.SetHubVeloeProvisionarTaxa(empresa.IdEmpresa, model.HubVeloeProvisionarTaxa));
                
                retorno.Add(_parametrosApp.SetHubViaFacilProvisionarTaxa(empresa.IdEmpresa, model.HubViaFacilProvisionarTaxa));
                retorno.Add(_parametrosApp.SetHubViaFacilProvisionarValor(empresa.IdEmpresa, model.HubViaFacilProvisionarValor));
                
                retorno.Add(_parametrosApp.SetHubMoveMaisProvisionarValor(empresa.IdEmpresa, model.HubMoveMaisProvisionarValor));
                retorno.Add(_parametrosApp.SetHubMoveMaisProvisionarTaxa(empresa.IdEmpresa, model.HubMoveMaisProvisionarTaxa));
                
                retorno.Add(_parametrosApp.SetHubConectCarMaisProvisionarValor(empresa.IdEmpresa, model.HubConectCarProvisionarValor));
                retorno.Add(_parametrosApp.SetHubConectCarProvisionarTaxa(empresa.IdEmpresa, model.HubConectCarProvisionarTaxa));

                retorno.Add(_parametrosApp.SetHubTaggyEdenredProvisionarValor(empresa.IdEmpresa, model.HubTaggyEdenredProvisionarValor));
                retorno.Add(_parametrosApp.SetHubTaggyEdenredProvisionarTaxa(empresa.IdEmpresa, model.HubTaggyEdenredProvisionarTaxa));
                retorno.Add(_parametrosApp.SetTaggyEdenredExtrattaTaxaVpo(empresa.IdEmpresa, model.TaggyEdenredExtrattaTaxaVpo));
                
                #endregion
                
                #region Parametros ConectCar Extratta
                
                retorno.Add(_parametrosApp.SetConectCarExtrattaTaxaVpo(empresa.IdEmpresa, model.ConectCarExtrattaTaxaVpo));
                
                #endregion

                #region Parametros Extratta Pay
                
                retorno.Add(_parametrosApp.SetBloqueiaCargaAvulsaDuplicada(empresa.IdEmpresa, model.BloqueiaCargaAvulsaDuplicada));
                
                #endregion
                
                return retorno;

            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public EmpresaCreateRequest ConsultarParametroMeioHomologado(int idEmpresa)
        {
            try
            {
                var empresa = _empresaApp.Get(idEmpresa);

                if (empresa == null)
                    return null;

                var empresaCreate = new EmpresaCreateRequest
                {
                    HabilitarMeioHomologado = !string.IsNullOrWhiteSpace(empresa.TokenMicroServices),
                };

                return empresaCreate;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public EmpresaEditRequest ConsultarPorId(int idEmpresa, IUserIdentity userIdentity)
        {
            try
            {
                var empresa = _empresaApp.Get(idEmpresa);
                if (empresa == null) return null;
                var empresaIndicadores = _empresaApp.GetEmpresaIndicadores(idEmpresa);
                var planosEmpresa = _planoApp.GetPlanosEmpresa(idEmpresa);
                //var token = _autenticacaoAplicacaoApp.Get(empresa.CNPJ).Token;
                var listaModulos = new List<EmpresaCreateModuloRequest>();

                if (empresa.Modulos != null)
                    foreach (var modulo in empresa.Modulos)
                        listaModulos.Add(new EmpresaCreateModuloRequest
                        {
                            IdEmpresa = modulo.IdEmpresa,
                            IdModulo = modulo.IdModulo
                        });

                #region Montagem do objeto

                var parametrosApp = _parametrosApp;
                
                var service = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices, userIdentity.CpfCnpj, userIdentity.Nome);

                var parametros = service.ConsultarEmpresaPedagio().ConfigureAwait(false).GetAwaiter().GetResult();
                var parametrosPedagio = service.ConsultarParametrosPedagio().ConfigureAwait(false).GetAwaiter().GetResult();
				var permiteEdicaoDadosAdministrativosEmpresa = parametrosApp.GetPermitirEdicaoDadosAdministrativosEmpresa(userIdentity.IdUsuario);

                var empresaCreate = new EmpresaEditRequest
                {
                    IdEmpresa = empresa.IdEmpresa,
                    CNPJ = empresa.CNPJ,
                    Telefone = empresa.Telefone,
                    RazaoSocial = empresa.RazaoSocial,
                    AlertaArea = empresa.AlertaArea,
                    Atualizaestabelecimento = empresa.Atualizaestabelecimento,
                    Ativo = empresa.Ativo,
                    DataInicioOperacao = empresaIndicadores != null ? empresaIndicadores.DataInicioOperacao : null,
                    TipoCliente = empresaIndicadores != null ? empresaIndicadores.TipoCliente : "",
                    PrevistoMovimentacaoFinanceiraFrete = empresaIndicadores != null ? empresaIndicadores.PrevistoMovimentacaoFinanceiraFrete : 0m,
                    PrevistoMovimentacaoFinanceiraVPO = empresaIndicadores != null ? empresaIndicadores.PrevistoMovimentacaoFinanceiraVPO : 0m,
                    PrevistoQuantidadeViagens = empresaIndicadores != null ? empresaIndicadores.PrevistoQuantidadeViagens : 0,
                    ComercialConta = empresaIndicadores != null ? empresaIndicadores.ComercialConta : "",
                    TMS = empresaIndicadores != null ? empresaIndicadores.TMS : "",
                    PlanosEmpresa = planosEmpresa,
                    ApresentaPontoReferencia = empresa.ApresentaPontoReferencia,
                    Bairro = empresa.Bairro,
                    CEP = empresa.CEP,
                    CNTRC = empresa.CNTRC,
                    Celular = empresa.Celular,
                    Complemento = empresa.Complemento,
                    DataAtualizacao = empresa.DataAtualizacao,
                    Email = empresa.Email,
                    EmailSugestoes = empresa.EmailSugestoes,
                    Endereco = empresa.Endereco,
                    FreqVencDoc = empresa.FreqVencDoc,
                    IdCidade = empresa.IdCidade,
                    IdEstado = empresa.IdEstado,
                    IdMotivoVelocidade = empresa.IdMotivoVelocidade,
                    IdOCcancViagem = empresa.IdOCcancViagem,
                    IdOcorrenciaVelocidade = empresa.IdOcorrenciaVelocidade,
                    IdPais = empresa.IdPais,
                    IdTecnologia = empresa.IdTecnologia,
                    Latitude = empresa.Latitude,
                    Logo = empresa.Logo,
                    LogoBase64 = empresa.Logo != null ? Convert.ToBase64String(empresa.Logo) : string.Empty,
                    Longitude = empresa.Longitude,
                    NomeFantasia = empresa.NomeFantasia,
                    Numero = empresa.Numero,
                    OcultarNovoCadastros = empresa.OcultarNovoCadastros,
                    HabilitarAgendamentoPagamentoFrete = empresa.HabilitarAgendamentoPagamentoFrete,
                    PermiteCredenciamento = empresa.PermiteCredenciamento,
                    PossuiMonitoramento = empresa.PossuiMonitoramento,
                    RoteirizarCarga = empresa.RoteirizarCarga,
                    UltimaExecucao = empresa.UltimaExecucao,
                    UrlRastreamento = empresa.UrlRastreamento,
                    UsuarioGatilho = empresa.UsuarioGatilho,
                    UtilizaAplicativoPersonalizado = empresa.UtilizaAplicativoPersonalizado,
                    ValidacaoPagFrete = empresa.ValidacaoPagFrete,
                    Modulos = listaModulos,
                    AlcadasBloqueioGestorValor = _bloqueioGestorValorApp.GetBloqueioGestorValor(idEmpresa, null),
                    IdLayoutCartao = empresa.IdLayoutCartao,
                    IdProdutoCartaoFretePadrao = empresa.IdProdutoCartaoFretePadrao,
                    TipoCarregamentoFrete = empresa.TipoCarregamentoFrete,
                    PercentualTransferenciaMotoristaStr = empresa.PercentualTransferenciaMotorista.ToIntNullable().ToStringSafe(),
                    AgrupaProtocoloMesmoEvento = empresa.AgrupaProtocoloMesmoEvento,
                    ValidaChaveBaixaEvento = empresa.ValidaChaveBaixaEvento,
                    TempoValidadeChaveStr = empresa.TempoValidadeChave.ToString(),
                    TempoValidadeChaveEstabelecimentoStr = empresa.MinutosValidadeHash.ToString(),
                    DiasParaBloquearPagtoStr = empresa.DiasParaBloquearPagto.ToString(),
                    PrazoParaInformarDocumentosStr = empresa.PrazoParaInformarDocumentos.ToString(),
                    WebHookProtocoloHeaders = empresa.WebHookProtocoloHeaders,
                    WebHookProtocoloEndPoint = empresa.WebHookProtocoloEndPoint,
                    HabilitarMeioHomologado = !string.IsNullOrWhiteSpace(empresa.TokenMicroServices),
                    PagamentoFreteToleranciaPesoChegadaMaisStr =
                        empresa.PagamentoFreteToleranciaPesoChegadaMais != null ? empresa.PagamentoFreteToleranciaPesoChegadaMais.ToString() : string.Empty,
                    PagamentoFreteToleranciaPesoChegadaMenosStr =
                        empresa.PagamentoFreteToleranciaPesoChegadaMenos != null ? empresa.PagamentoFreteToleranciaPesoChegadaMenos.ToString() : string.Empty,
                    Acuracia = empresa.Acuracia,
                    HorasValidadeChaveCadastroUsuario = empresa.HorasValidadeChaveCadastroUsuario,
                    IdSistemaExterno = empresa.IdSistemaExterno,
                    EmailCartaFrete = empresa.EmailCartaFrete,
                    NaoValidarProtocoloRecebidoEmpresa = empresa.NaoValidarProtocoloRecebidoEmpresa,
                    CancelaViagemComProtocolo = empresa.CancelaViagemComProtocolo,
                    AcaoSaldoResidualNovoCreditoCartaoPedagio = parametrosApp.GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(empresa.IdEmpresa),
                    AutorizaEstabelecimentosRedeJSL = parametrosApp.GetAutorizaEstabelecimentosRedeJSL(empresa.IdEmpresa),
                    ValorSaldoContaFreteMinimoNotificacaoEmail = parametrosApp.GetValorMinimoAlertaSaldoContaFrete(empresa.IdEmpresa),
                    ValorSaldoContaPixMinimoNotificacaoEmail = parametrosApp.GetValorMinimoAlertaSaldoContaPix(empresa.IdEmpresa),
                    DiasExpiracaoSaldoPedagio = empresa.DiasExpiracaoSaldoPedagio,
                    InformacoesTransferenciaBancaria = permiteEdicaoDadosAdministrativosEmpresa ? empresa.InformacoesTransferenciaBancaria : null,
                    UtilizaValidacaoPorPerfilNoPagamentoDeFrete = empresa.UtilizaValidacaoPorPerfilNoPagamentoDeFrete,
                    KeyGoogle = empresa.KeyGoogle,
                    RaioPontoReferencia = empresa.RaioPontoReferencia,
                    HorasExpiracaoCreditoPedagio = parametrosApp.GetHorasExpiracaoCreditoPedagio(empresa.IdEmpresa),
                    CodigoAcessoViaFacil = permiteEdicaoDadosAdministrativosEmpresa ? parametros?.CodigoAcessoViaFacil : null,
                    LoginAcessoViaFacil = permiteEdicaoDadosAdministrativosEmpresa ? parametros?.LoginAcessoViaFacil : null,
                    DiasDataExpiracaoPedagioViaFacil = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.ViaFacil?.DiasDataExpiracaoPedagio : null,
                    FalhaComunicacaoPedagio = parametros?.Status == ConsultarEmpresaResponseStatus.Falha || parametrosPedagio.Status == ConsultarParametroResponseStatus.Falha,
                    MostrarHeaderArquivoCsv = parametrosApp.GetMostrarHeaderArquivoCsv(empresa.IdEmpresa),
                    SeparadorArquivoCsv = parametrosApp.GetSeparadorArquivoCsv(empresa.IdEmpresa),
                    LoginAcessoMoveMais = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.LoginAcessoMoveMais : null,
                    MoveMaisExtrattaTaxaVpo = _parametrosApp.GetMoveMaisExtrattaTaxaVpo(empresa.IdEmpresa),
                    HubViaFacilProvisionarTaxa = _parametrosApp.GetHubViaFacilProvisionarTaxa(empresa.IdEmpresa),
                    HubViaFacilProvisionarValor = _parametrosApp.GetHubViaFacilProvisionarValor(empresa.IdEmpresa),
                    HubMoveMaisProvisionarValor = _parametrosApp.GetHubMoveMaisProvisionarValor(empresa.IdEmpresa),
                    HubMoveMaisProvisionarTaxa = _parametrosApp.GetHubMoveMaisProvisionarTaxa(empresa.IdEmpresa),
                    HubConectCarProvisionarTaxa = _parametrosApp.GetHubConectCarProvisionarTaxa(empresa.IdEmpresa),
                    HubConectCarProvisionarValor = _parametrosApp.GetHubConectCarProvisionarValor(empresa.IdEmpresa),
                    HubVeloeProvisionarTaxa = _parametrosApp.GetHubVeloeProvisionarTaxa(empresa.IdEmpresa),
                    HubVeloeProvisionarValor = _parametrosApp.GetHubVeloeProvisionarValor(empresa.IdEmpresa),
                    HubTaggyEdenredProvisionarTaxa = _parametrosApp.GetHubTaggyEdenredProvisionarTaxa(empresa.IdEmpresa),
                    HubTaggyEdenredProvisionarValor = _parametrosApp.GetHubTaggyEdenredProvisionarValor(empresa.IdEmpresa),
                    UtilizaCredenciaisExtrattaCompraTaggyEdenred = parametrosPedagio?.TaggyEdenred?.UtilizaCredenciaisExtrattaCompraTaggyEdenred ?? false,
                    LoginAcessoTaggyEdenred = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio?.TaggyEdenred?.LoginAcesso : null,
                    SenhaAcessoTaggyEdenred = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio?.TaggyEdenred?.SenhaAcesso : null,
                    CodigoParceiroTaggyEdenred = permiteEdicaoDadosAdministrativosEmpresa ?  parametrosPedagio?.TaggyEdenred?.CodigoParceiro : null,
                    TaggyEdenredExtrattaTaxaVpo = _parametrosApp.GetTaggyEdenredExtrattaTaxaVpo(empresa.IdEmpresa),
                    ViaFacilExtrattaTaxaVpo = _parametrosApp.GetViaFacilExtrattaTaxaVpo(empresa.IdEmpresa),
                    UtilizaCredenciaisExtrattaCompraMoveMais = parametrosPedagio?.UtilizaCredenciaisExtrattaMoveMais ?? false,
                    SenhaAcessoViaFacil = permiteEdicaoDadosAdministrativosEmpresa ? parametros.SenhaAcessoViaFacil : null,
                    SenhaAcessoMoveMais = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.SenhaAcessoMoveMais : null,
                    LoginAcessoConectCar = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.LoginAcessoConectCar : null,
                    ConectCarExtrattaTaxaVpo = _parametrosApp.GetConectCarExtrattaTaxaVpo(empresa.IdEmpresa),
                    UtilizaCredenciaisExtrattaCompraConectCar = parametrosPedagio?.UtilizaCredenciaisExtrattaCompraConectCar ?? false,
                    UtilizaCredenciaisExtrattaCompraViaFacil = parametros?.UtilizaCredenciaisExtrattaCompraViaFacil ?? false,
                    PeriodoBloqueioEventosAberto = empresa.PeriodoBloqueioEventosAberto,
                    ValidaChaveMHBaixaEvento = empresa.ValidaChaveMHBaixaEvento,
                    EmailsAlertaCiotAgregado = _parametrosApp.GetEmailsAlertaCiotAgregado(empresa.IdEmpresa),
                    DiasCancelamentoViagem = _parametrosApp.GetDiasCancelamentoViagem(empresa.IdEmpresa),
                    ReemiteCiotPadraoAlteracaoViagem = parametrosApp.GetReemiteCiotPadraoAlteracaoViagem(empresa.IdEmpresa) ?? false,
                    TokenMicroServicoCentralAtendimento = parametrosApp.GetTokenMicroServicoCentralAtendimento(empresa.IdEmpresa),
                    PermissoesAtendimentoCartao = parametrosApp.GetPermissoesEmpresaAtendimentoCartao(empresa.IdEmpresa),
                    RegistrarValePedagio = parametrosApp.GetRegistrarValePedagio(empresa.IdEmpresa),
                    RealizaTriagemEstabelecimentoInterno = parametrosApp.GetRealizaTriagemEstabelecimentoInterno(empresa.IdEmpresa),
                    MantemViagemAbertaAposCancelamentoDoUltimoEvento = parametrosApp.GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(empresa.IdEmpresa),
                    PermiteVincularCartaoComCpfFicticio = parametrosApp.GetPermiteVincularCartaoComCpfFicticio(empresa.IdEmpresa),
                    TipoInscricaoEmpresa = empresa.TipoInscricaoEmpresa,
                    CodigoBancoCompensacao = empresa.CodigoBancoCompensacao,
                    NumeroInscricaoEmpresa = empresa.NumeroInscricaoEmpresa,
                    CodigoConvenioBanco = empresa.CodigoConvenioBanco,
                    AgenciaMantenedora = empresa.AgenciaMantenedora,
                    DigitoVerificaConta = empresa.DigitoVerificaConta,
                    TagExtrattaPermiteUtilizarFornecedor = parametrosPedagio.TagExtratta?.PermiteUtilizarFornecedor ?? false,
                    TagExtrattaTaxaVpo = parametrosApp.GetTagExtrattaTaxaVpo(empresa.IdEmpresa),
                    TagExtrattaValorTag = parametrosApp.GetTagExtrattaValorTag(empresa.IdEmpresa),
                    TagExtrattaValorMensalidade = parametrosApp.GetTagExtrattaValorMensalidade(empresa.IdEmpresa),
                    TagExtrattaUtilizaTaxaPedagio = parametrosApp.GetTagExtrattaUtilizaTaxaPedagio(empresa.IdEmpresa),
                    TagExtrattaProvisionarValor = parametrosApp.GetTagExtrattaProvisionarValor(empresa.IdEmpresa),
                    TagExtrattaProvisionarTaxa = parametrosApp.GetTagExtrattaProvisionarTaxa(empresa.IdEmpresa),
                    TagExtrattaFaixaToleranciaNotificacaoEmail = parametrosApp.GetTagExtrattaFaixaToleranciaNotificacaoEmail(empresa.IdEmpresa),
                    TagExtrattaProvisionarTaxaPedagioWebhook = parametrosApp.GetTagExtrattaProvisionarTaxaPedagioWebhook(empresa.IdEmpresa),
                    TagExtrattaProvisionarValorPedagioWebhook = parametrosApp.GetTagExtrattaProvisionarValorPedagioWebhook(empresa.IdEmpresa),
                    TagExtrattaSaldoMinimoContaFreteWebhook = parametrosApp.GetTagExtrattaSaldoMinimoContaFreteWebhook(empresa.IdEmpresa),
                    TagExtrattaBloquearTagUnitariaWebhook = parametrosApp.GetTagExtrattaBloquearTagUnitariaWebhook(empresa.IdEmpresa),
                    TagExtrattaBloquearTagLoteWebhook = parametrosApp.GetTagExtrattaBloquearTagLoteWebhook(empresa.IdEmpresa),
                    TagExtrattaValorSubstituicao = parametrosApp.GetTagExtrattaValorSubstituicao(empresa.IdEmpresa),
                    TagExtrattaValorRecargaConta = parametrosApp.GetTagExtrattaValorRecargaConta(empresa.IdEmpresa),
                    TagExtrattaEstornarValorPedagioWebhook = _parametrosApp.GetTagExtrattaEstornarPedagio(empresa.IdEmpresa),
                    DigitoVerificaContaAgConta = empresa.DigitoVerificaContaAgConta,
                    PedagioTag = empresa.PedagioTag,
                    PermiteResgate = empresa.PermiteResgate,
                    SenhaResgate = empresa.SenhaResgate,
                    AcessarExtrato = empresa.AcessarExtrato,
                    ListarCnpjGridDespesasViagem = parametrosApp.GetParametroEmpresaGridListarCnpjDespesasViagem(idEmpresa),
                    ParcelaViagemAberta = empresa.ParcelaViagemAberta,
                    Token = string.Empty,
                    IntegrarComoUsuario = empresa.IntegrarComoUsuario,
                    BaixarParcelaBloqueado = empresa.BaixarParcelaBloqueado,
                    BloquearNovaViagem = empresa.BloquearNovaViagem,
                    CnpjEmbarcadorVeloe = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.Veloe?.CnpjEmbarcador : null,
                    TenantIdVeloe = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.Veloe?.TenantId : null,
                    UserNameVeloe = permiteEdicaoDadosAdministrativosEmpresa ? parametrosPedagio.Veloe?.UserName : null, 
                    VeloeExtrattaTaxaVpo = _parametrosApp.GetVeloeExtrattaTaxaVpo(empresa.IdEmpresa),
                    UtilizaCredenciaisExtrattaCompraVeloe = parametrosPedagio.Veloe?.UtilizaCredenciaisExtrattaVeloe ?? false,
                    VinculoNovoCartaoPortador = empresa.VinculoNovoCartaoPortador,
                    VinculoNovoCartaoBloqueadoPortador = empresa.VinculoNovoCartaoBloqueadoPortador,
                    DesabilitaCacheRotas = empresa.DesabilitaCacheRotas,
                    GerarCiotViagemInternacional = empresa.GerarCiotViagemInternacional,
                    WebHookEndpoint = empresa.WebHookEndpoint,
                    WebHookHeaders = empresa.WebHookHeaders,
                    AprovacaoGestorCargaAvulsaIntegracao = _parametrosApp.GetSolicitaAprovacaoGestorCargaAvulsaIntegracao(empresa.IdEmpresa),
                    AprovacaoGestorCargaAvulsaUnitario = _parametrosApp.GetSolicitaAprovacaoGestorCargaAvulsaUnitario(empresa.IdEmpresa),
                    AprovacaoGestorCargaAvulsaLote = _parametrosApp.GetSolicitaAprovacaoGestorCargaAvulsaLote(empresa.IdEmpresa),
                    UtilizaRelatoriosOfx = _parametrosApp.GetUtilizaRelatoriosOfx(empresa.IdEmpresa),
                    UtilizaRoteirizacaoPorPolyline = _parametrosApp.GetUtilizaRoteirizacaoPorPolyline(empresa.IdEmpresa),
                    MantemViagemAbertaAposBaixaDoUltimoEvento = _parametrosApp.GetMantemViagemAbertaAposBaixaDoUltimoEvento(empresa.IdEmpresa),
                    DefaultIntegracaoTipoRodagemDupla = _parametrosApp.GetDefaultIntegracaoTipoRodagemDupla(empresa.IdEmpresa),
                    BloqueiaCargaAvulsaDuplicada = _parametrosApp.GetBloqueiaCargaAvulsaDuplicada(empresa.IdEmpresa),
                    RodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe= _parametrosApp.GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(empresa.IdEmpresa),
                    NaoBaixarParcelasDeposito = _parametrosApp.GetNaoBaixarParcelasDeposito(empresa.IdEmpresa),
                    CodOfx = parametrosApp.GetCodigoOfx(empresa.IdEmpresa),
                };

                #endregion

                return empresaCreate;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public byte[] GerarRelatorioGridEmpresas(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string extensao)
        {
            return _empresaApp.GerarRelatorioGridEmpresas(idEmpresa, orderFilters, filters, GetLogo(idEmpresa),
                extensao);
        }

        private void IntegrarPlataformaMicroServicos(EmpresaCreateRequest transpViewModel, Empresa empresa, string sessaoCpfCnpj, string sessaoNome)
        {
            if (empresa.Ativo && transpViewModel.HabilitarMeioHomologado)
            {
                if (string.IsNullOrWhiteSpace(empresa.TokenMicroServices))
                {
                    // 1 - Integração serviço de autenticação: Registar empresa nos micro serviços da Sistema Info
                    // 2 - Integração serviço de autenticação: Gerar token para integração com ecosistema de micro serviços da Sistema Info (CIOT, Cartão, Pedágio, etc)
                    // 3 - Integração serviço de cartões: Registar empresa para ser replicada na processadora BIZ (Emissor OMNI)
                    // 4 - Integração serviço de cartões: Registar todas as filiais como pontos de distribuição de cartões e carga de moedeiro
                    // 5 - Integração serviço de cartões: Caso empresa não possua registro na tabela de filial, integra também como ponto de distribuição da cartões e carga de moedeiro
                    using (var authRep = new AuthExternalRepository())
                    {
                        empresa.RazaoSocial = empresa.RazaoSocial.ValueLimited(60);
                        
                        // 1 - Integração serviço de autenticação: Registar empresa nos micro serviços da Sistema Info
                        var retEmp = authRep.IntegrarEmpresa(empresa.CNPJ, empresa.RazaoSocial);
                        if (retEmp.Status == IntegrarEmpresaApiResponseStatus.Falha)
                            throw new Exception("Erro ao integrar empresa na plataforma de micro serviços: " +
                                                retEmp.Mensagens.FirstOrDefault()?.Message);
                        
                        // 2 - Integração serviço de autenticação: Gerar token para integração com ecosistema de micro serviços da Sistema Info (CIOT, Cartão, Pedágio, etc)
                        var retToken = authRep.GetOrGenerateAppToken(empresa.CNPJ);
                        if (retToken.Status == GetOrGenerateAppTokenApiResponseStatus.Falha)
                            throw new Exception( "Erro ao gerar token de integração com a empresa na plataforma de micro serviços: " +
                                                 retToken.Mensagem);
                        
                        empresa.TokenMicroServices = retToken.Token;
                        var saveEmpResult = _empresaApp.Update(empresa);
                        if (!saveEmpResult.IsValid)
                        {
                            throw new Exception(
                                    "Erro ao salvar token de integração com micro serviços na empresa do ATS: " +
                                    saveEmpResult.Errors.FirstOrDefault()?.Message);
                        }
                        else
                        {
                            var cartoesService = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices, sessaoCpfCnpj, sessaoNome);
                            
                            // 3 - Integração serviço de cartões: Registar empresa para ser replicada na processadora BIZ (Emissor OMNI)
                            var integrarEmpresaResponse = cartoesService.IntegrarEmpresa(empresa);
                            if (integrarEmpresaResponse.Status == IntegrarEmpresaResponseStatus.Falha)
                            {
                                var mensagem = "Erro ao registrar empresa na processadora de cartões";
                                var erros = integrarEmpresaResponse.Mensagens?.Select(x => x.Message).ToList();
                                if (erros != null && erros.Any())
                                    mensagem += ": " + string.Join(", ", erros);
                                throw new Exception(mensagem);
                            }

                            // 4 - Integração serviço de cartões: Registar todas as filiais como pontos de distribuição de cartões e carga de moedeiro
                            var filialApp = _filialApp;
                            var filiais = filialApp.GetListaFilialPorEmpresa(empresa.IdEmpresa).ToArray();
                            foreach (var filial in filiais)
                            {
                                if (filial.Ativo)
                                    cartoesService.IntegrarPessoaMicroServico(filial);
                            }

                            // 5 - Integração serviço de cartões: Caso empresa não possua registro na tabela de filial, integra também como ponto de distribuição da cartões e carga de moedeiro
                            var filialCnpjEmp = filialApp.Get(empresa.CNPJ);
                            if (filialCnpjEmp == null || !filialCnpjEmp.Ativo)
                                cartoesService.IntegrarPessoaMicroServico(empresa);
                        }
                    }
                }
                //Integração serviço de pedagio
                var cartoesServicePedagio = new CartoesService(_cartoesServiceArgs, empresa.IdEmpresa, empresa.TokenMicroServices, 
                                                               sessaoCpfCnpj, sessaoNome);

                if (!transpViewModel.FalhaComunicacaoPedagio)
                {
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                    cartoesServicePedagio.IntegrarEmpresaPedagio(transpViewModel.CodigoAcessoViaFacil,
                        transpViewModel.LoginAcessoViaFacil, transpViewModel.SenhaAcessoViaFacil,
                        transpViewModel.UtilizaCredenciaisExtrattaCompraViaFacil);
                    cartoesServicePedagio.IntegrarParametrosPedagio(transpViewModel.LoginAcessoMoveMais, 
                        transpViewModel.SenhaAcessoMoveMais,
                        transpViewModel.UtilizaCredenciaisExtrattaCompraMoveMais,
                        new ParametrosConectCarDto
                        {
                            LoginAcesso = transpViewModel.LoginAcessoConectCar,
                            SenhaAcesso = transpViewModel.SenhaAcessoConectCar,
                            UtilizaCredenciaisExtrattaCompraConectCar = transpViewModel.UtilizaCredenciaisExtrattaCompraConectCar
                        },
                        new ParametrosVeloeDto
                        {
                            UtilizaCredenciaisExtrattaVeloe = transpViewModel.UtilizaCredenciaisExtrattaCompraVeloe,
                            CnpjEmbarcador = transpViewModel.CnpjEmbarcadorVeloe,
                            TenantId = transpViewModel.TenantIdVeloe,
                            UserName = transpViewModel.UserNameVeloe,
                            Password = transpViewModel.PasswordVeloe,
                            BasicAuth = transpViewModel.BasicAuthVeloe
                        },
                        new ParametrosTagExtrattaDto
                        {
                            PermiteUtilizarFornecedor = transpViewModel.TagExtrattaPermiteUtilizarFornecedor ?? false
                        },
                        new ParametrosTaggyEdenredDto
                        {
                            LoginAcesso = transpViewModel.LoginAcessoTaggyEdenred,
                            SenhaAcesso = transpViewModel.SenhaAcessoTaggyEdenred,
                            CodigoParceiro = transpViewModel.CodigoParceiroTaggyEdenred,
                            UtilizaCredenciaisExtrattaCompraTaggyEdenred = transpViewModel.UtilizaCredenciaisExtrattaCompraTaggyEdenred,
                        },
                        transpViewModel.DiasDataExpiracaoPedagioViaFacil);
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                }
            }
            else if (!string.IsNullOrWhiteSpace(empresa.TokenMicroServices))
            {
                empresa.TokenMicroServices = null;
                var saveEmpResult = _empresaApp.Update(empresa);

                if (!saveEmpResult.IsValid)
                    throw new Exception($"Erro ao salvar empresa: {saveEmpResult}");
            }
        }
    }
}
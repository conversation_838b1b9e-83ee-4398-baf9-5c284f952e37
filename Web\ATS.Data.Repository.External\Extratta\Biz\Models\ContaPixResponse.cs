﻿namespace ATS.Data.Repository.External.Extratta.Biz.Models
{
    public class ContaPixResponse
    {
        public string AccountId { get; set; }

        public int? AccountNumber { get; set; }

        public decimal? AdjustmentAmount { get; set; }

        public string AffinityGroup { get; set; }

        public int? AffinityGroupId { get; set; }

        public decimal? Balance { get; set; }

        public decimal? BalanceDue { get; set; }

        public string BranchOffice { get; set; }

        public int? BranchOfficeId { get; set; }

        public decimal? CreditLimit { get; set; }

        public string CreditLimitCurrency { get; set; }

        public int? CreditLimitCurrencyId { get; set; }

        public string CreditLimitCurrencySymbol { get; set; }

        public string CreditLimitDate { get; set; }

        public int? DaysinArrears { get; set; }

        public string Document { get; set; }

        public string DocumentType { get; set; }

        public int? DocumentTypeId { get; set; }

        public int? DueDay { get; set; }

        public string Id { get; set; }

        public string IncludeDate { get; set; }

        public decimal? InterestAmount { get; set; }

        public decimal? InvoiceAmount { get; set; }

        public string Issuer { get; set; }

        public int? IssuerId { get; set; }

        public string LanguageInvoice { get; set; }

        public decimal? MinimumPaymentAmount { get; set; }

        public decimal? MoraAmount { get; set; }

        public decimal? OthersmovtsAmount { get; set; }

        public decimal? PaymentAmount { get; set; }

        public string Product { get; set; }

        public int? ProductId { get; set; }

        public string ShippingInvoiceType { get; set; }

        public string Status { get; set; }

        public string StatusDate { get; set; }

        public int? StatusId { get; set; }

        public int? StateClass { get; set; }
    }
}
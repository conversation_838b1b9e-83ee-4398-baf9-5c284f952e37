﻿using System.Linq;
using System.Net.Http;
using ATS.CrossCutting.IoC.Utils;

// ReSharper disable once CheckNamespace
namespace SistemaInfo.Cartoes.Repository.External.SistemaInfo.Auth.Api.Client
{
    public partial class Client
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class EmpresasClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class UsuariosClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class ValidarClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }
}

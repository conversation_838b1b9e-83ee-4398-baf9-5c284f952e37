using ATS.Domain.Entities.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Dynamic;
using System.Text.RegularExpressions;
using ATS.CrossCutting.IoC;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Veiculo : VeiculoBase
    {
        public static Regex RegexMercosul => new Regex(@"^[a-zA-Z]{3}\d[a-zA-Z]\d{2}$");
        public static Regex RegexBrasil => new Regex(@"^[a-zA-Z]{3}\d{4}$"); 

        /// <summary>
        /// Código do Veículo
        /// </summary>
        public int IdVeiculo { get; set; }

        /// <summary>
        /// Código do Transportado onde o veículo esta registrado
        /// </summary>
        public int? IdEmpresa { get; set; }

        /// <summary>
        /// Código da filial
        /// </summary>
        public int? IdFilial { get; set; }

        /// <summary>
        /// Código do Proprietário
        /// </summary>
        public int? IdProprietario { get; set; }

        /// <summary>
        /// Código do Motorista
        /// </summary>
        public int? IdMotorista { get; set; }

        /// <summary>
        /// Código do Usuário
        /// </summary>
        public int? IdUsuario { get; set; }

        /// <summary>
        /// Código do País
        /// </summary
        public int? IdPais { get; set; }

        /// <summary>
        /// Código do Estado
        /// </summary
        public int? IdEstado { get; set; }

        /// <summary>
        /// Código da Cidade
        /// </summary
        public int? IdCidade { get; set; }

        /// <summary>
        /// Número da frota
        /// </summary>
        public long? NumeroFrota { get; set; }

        /// <summary>
        /// Tipo de contrato
        /// </summary>
        public ETipoContrato TipoContrato { get; set; }

        /// <summary>
        /// Aviso sonoro fraco
        /// </summary>
        public int? AvisoSonoroFraco { get; set; }

        /// <summary>
        /// Aviso sonoro moderado
        /// </summary>
        public int? AvisoSonoroModerado { get; set; }

        /// <summary>
        /// Aviso sonoro forte
        /// </summary>
        public int? AvisoSonoroForte { get; set; }

        /// <summary>
        /// Número de eixos
        /// </summary>
        public int QuantidadeEixos { get; set; }
        public DateTime? DataUltimaAtualizacao { get; set; }

        /// <summary>
        /// Indica a operação em que o veículo está
        /// </summary>
        public int? IdOperacao { get; set; }

        public string Municipio { get; set; }
        public int? IdTecnologia { get; set; }

        /// <summary>
        /// Indica se viagens para o veículo devem ter o contrato de agregado
        /// </summary>
        public bool? HabilitarContratoCiotAgregado { get; set; }
        
        #region Atributo CorVeiculo criado na classe Veiculo.cs no dia 09-11-2018

        public string CorVeiculo { get; set; }

        #endregion

        #region Navegação inversa

        /// <summary>
        /// Proprietário
        /// </summary>
        public virtual Proprietario Proprietario { get; set; }

        /// <summary>
        /// Motorista
        /// </summary>
        public virtual Motorista Motorista { get; set; }

        /// <summary>
        /// Usuário
        /// </summary>
        public virtual Usuario Usuario { get; set; }

        /// <summary>
        /// Empresa
        /// </summary>
        public virtual Empresa Empresa { get; set; }

        /// <summary>
        /// Filial a qual o veículo esta vinculado
        /// </summary>
        public virtual Filial Filial { get; set; }

        /// <summary>
        /// Tipo de veículo
        /// </summary>
        public virtual TipoCavalo TipoCavalo { get; set; }

        /// <summary>
        /// País
        /// </summary>
        public virtual Pais Pais { get; set; }

        /// <summary>
        /// Estado
        /// </summary>
        public virtual Estado Estado { get; set; }

        /// <summary>
        /// Cidade
        /// </summary>
        public virtual Cidade Cidade { get; set; }

        /// <summary>
        /// Tipo de carreta
        /// </summary>
        public virtual TipoCarreta TipoCarreta { get; set; }
        public virtual ICollection<ConjuntoEmpresa> ConjuntosEmpresa { get; set; }
        public virtual ICollection<ConjuntoCarreta> ConjuntosCarreta { get; set; }
        public virtual ICollection<ContratoCiotAgregadoVeiculo> ContratoCiotAgregadoVeiculos { get; set; }

        /// <summary>
        /// Indica a operação em que o veículo está
        /// </summary>

        #endregion

        #region Tabelas Filhas

        /// <summary>
        /// Tipos de combustiveis associados ao veículo
        /// </summary>
        public virtual ICollection<VeiculoTipoCombustivel> TipoCombustiveis { get; set; } = new List<VeiculoTipoCombustivel>();

        /// <summary>
        /// Veículos
        /// </summary>
        public virtual ICollection<Veiculo> Veiculos { get; set; } = new List<Veiculo>();

        /// <summary>
        /// Veiculos que fazem parte do conjunto
        /// </summary>
        public virtual ICollection<Veiculo> VeiculoConjuntos { get; set; } = new List<Veiculo>();

        #endregion

        [NotMapped]
        public string ItemListaVeiculoConjunto
        {
            get { return Placa; }
            set { Placa = value; }
        }

        #region Métodos estáticos de validação genéricas

        public static bool PlacaValida(string placa)
        {
            var value = placa.RemoveSpecialCharacters();
            
            return RegexBrasil.IsMatch(value) || RegexMercosul.IsMatch(value);
        }

        public static ETipoPadraoPlaca GetTipoPadraoPlaca(IEmpresaRepository empresaRepository, string placa, int? idEmpresa)
        {
            var empresaPossuiPlacaEspeciais = empresaRepository
                .Where(x => x.IdEmpresa == idEmpresa && x.UtilizaPlacasEspeciais == true).Any();
            
            var value = placa.RemoveSpecialCharacters();
            
            if (empresaPossuiPlacaEspeciais) return ETipoPadraoPlaca.Brasil;
            
            if (RegexBrasil.IsMatch(value))
                return ETipoPadraoPlaca.Brasil;

            if (RegexMercosul.IsMatch(value))
                return ETipoPadraoPlaca.Mercosul;
            
            throw new ArgumentException($"Placa {placa} não utiliza um padrão válido.");

        }

        #endregion
    }
}

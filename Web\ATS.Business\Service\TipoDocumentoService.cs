﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;

namespace ATS.Domain.Service
{
    public class TipoDocumentoService : ServiceBase, ITipoDocumentoService
    {
        private readonly ITipoDocumentoRepository _tipoDocumentoRepository;

        public TipoDocumentoService(ITipoDocumentoRepository tipoDocumentoRepository)
        {
            _tipoDocumentoRepository = tipoDocumentoRepository;
        }

        private ValidationResult IsValid(TipoDocumento estado, EProcesso processo)
        {
            ValidationResult validationResult = new ValidationResult();

            return validationResult;
        }

        public IQueryable<TipoDocumento> Consultar(string descricao)
        {
            if (string.IsNullOrWhiteSpace(descricao))
                descricao = string.Empty;

            return _tipoDocumentoRepository.Consultar(descricao);
        }

        public TipoDocumento Get(int id)
        {
            return _tipoDocumentoRepository.Get(id);
        }

        public ValidationResult Update(TipoDocumento entity)
        {
            try
            {
                ValidationResult validationResult = IsValid(entity, EProcesso.Update);
                if (!validationResult.IsValid)
                    return validationResult;

                _tipoDocumentoRepository.Update(entity);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        public ValidationResult Add(TipoDocumento entity)
        {
            try
            {
                ValidationResult validationResult = IsValid(entity, EProcesso.Create);
                if (!validationResult.IsValid)
                    return validationResult;

                _tipoDocumentoRepository.Add(entity);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.ToString());
            }

            return new ValidationResult();
        }

        public IQueryable<TipoDocumento> All()
        {
            return _tipoDocumentoRepository.All().Where(x => x.Ativo);
        }

        public int? GetCNH()
        {
            return _tipoDocumentoRepository
                .Where(x => x.Ativo && x.Descricao.ToLower() == "cnh")
                .FirstOrDefault()?.IdTipoDocumento;
        }

        public ValidationResult Inativar(int idTipoDocumento)
        {
            try
            {
                ITipoDocumentoRepository repository = _tipoDocumentoRepository;

                TipoDocumento tpDoc = repository.Get(idTipoDocumento);
                if (!tpDoc.Ativo)
                    return new ValidationResult().Add($"Tipo de documento já desativado na base de dados.");

                tpDoc.Ativo = false;

                repository.Update(tpDoc);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idTpDoc)
        {
            try
            {
                ITipoDocumentoRepository repository = _tipoDocumentoRepository;

                TipoDocumento tpDoc = repository.Get(idTpDoc);
                if (tpDoc.Ativo)
                    return new ValidationResult().Add($"Tipo de documento já ativo na base de dados.");

                tpDoc.Ativo = true;

                repository.Update(tpDoc);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }
    }
}
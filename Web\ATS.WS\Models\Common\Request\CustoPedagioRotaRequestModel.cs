﻿using System;
using System.Collections.Generic;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.WS.Models.Common.Request.Base;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.WS.Models.Common.Request
{
    public class CustoPedagioRotaRequestModel : RequestBase
    {
        public IList<LocalizacaoDTO> Localizacoes { get; set; }
        public ETipoVeiculoPedagioEnum TipoVeiculo { get; set; }
        public int QtdEixos { get; set; }
        public bool ExibirDetalhes { get; set; }
        public ETipoRota TipoRota { get; set; }
        public ETipoRodagem TipoRodagem { get; set; }
        public string Billing { get; set; }
        public string Polyline { get; set; }
    }
}

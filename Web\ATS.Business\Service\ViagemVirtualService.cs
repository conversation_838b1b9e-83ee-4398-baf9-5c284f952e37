﻿using System;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class ViagemVirtualService : ServiceBase, IViagemVirtualService
    {
        private readonly IViagemVirtualRepository _viagemVirtualRepository;

        public ViagemVirtualService(IViagemVirtualRepository viagemVirtualRepository)
        {
            _viagemVirtualRepository = viagemVirtualRepository;
        }

        public ValidationResult Add(ViagemVirtual viagemVirtual)
        {
            try
            {
                _viagemVirtualRepository.Add(viagemVirtual);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public int AddReturningId(ViagemVirtual viagemVirtual)
        {
            try
            {
                _viagemVirtualRepository.Add(viagemVirtual);
                return viagemVirtual.IdViagemVirtual;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public ViagemVirtual Get(int id)
        {
            var viagemVirtual = _viagemVirtualRepository.Get(id);
            return viagemVirtual;
        }
    }
}

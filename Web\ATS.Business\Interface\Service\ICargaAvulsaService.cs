﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service.Common;
using ATS.Domain.Models;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using ATS.Domain.Interface.Database;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Models.CargaAvulsa;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.Interface.Service
{
    public interface ICargaAvulsaService : IBaseService<ICargaAvulsaRepository>
    {
        CargaAvulsaAddResponseModel Add(CargaAvulsa cargaAvulsa, string cnpjEmpresa = null, string ipv4 = null, EBloqueioOrigemTipo? origem = null, bool? ignorarValidacaoDuplicada = null);
        decimal GetTransacoesDiaria(int? idEmpresa, int? idFilial);
        object ConsultaGrid(int? idEmpresa, int idUsuario, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor); 
        object ConsultaGridPlanilha(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters,bool apenasLiberacaoGestor);        
        CargaAvulsa Get(int idCargaAvulsa);
        KeyValuePair<CargaAvulsa, ValidationResult<EValidationCargaAvulsa>> CreateTransaction(CargaAvulsa newEntity, string cnpjEmpresa = null, string ipv4 = null, CartaoVinculadoPessoaResponse cartao = null);
        IQueryable<CargaAvulsa> Find(Expression<Func<CargaAvulsa, bool>> predicate);
        EstornarCargaAvulsaResponseModel EstornarCargaAvulsa(int? idCargaAvulsa, string numeroControle, string cnpjEmpresa);
        byte[] GerarRelatorioGridCargaAvulsa(OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo,
            string logo, int? idEmpresa,bool apenasLiberacaoGestor);
        CargaAvulsaResultadoValidacaoPlanilha ValidarLinhasPlanilha(HttpPostedFileBase file,int idEmpresa);
        ValidationResult ReprocessarCargaAvulsa(int cargaAvulsaId);
        ValidationResult CadastrarCargaAvulsaPlanilha(CargaAvulsaValidacaoPlanilha cargaAvulsa);
        ValidationResult AlterarStatusCargaAvulsa(int cargaAvulsaId, EStatusCargaAvulsa status);
        void RealizarCargasAvulsaPendentes();
        byte[] GerarReciboCargaAvulsa(int cargaAvulsaId, int? empresaId, string usuarioNome, string usuarioDocumento);
        IQueryable<CargaAvulsa> GetByEmpresa(int? idEmpresa);
        ValidationResult Aprovar(int cargaAvulsaId, string cnpjEmpresa);
        ValidationResult Processar(int cargaAvulsaId, string cnpjEmpresa);
        IQueryable<CargaAvulsa> GetAll();
        BusinessResult GestorReprovarPlanilha(string codImportacao);
        BusinessResult GestorAprovarPlanilha(string codImportacao);
        BusinessResult Reprovar(int cargaAvulsaId, string motivoRejeicao, bool origemPortal);
    }
}

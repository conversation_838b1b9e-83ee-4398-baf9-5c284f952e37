﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.Domain.Models
{
    public class KeycloakRealmAccessModel
    {
        public string[] roles { get; set; } = new string[0];
    }
    public class KeycloakResourceAccessAccountModel
    {
        public string[] roles { get; set; } = new string[0];
    }
    public class KeycloakResourceAccessModel
    {
        public KeycloakResourceAccessAccountModel account { get; set; }
    }
    public class KeycloakPushedClaimsModel : Dictionary<string, string[]>
    {

    }
    public class KeycloakPermissionModel
    {
        public KeycloakPushedClaimsModel claims { get; set; }
        public string rsid { get; set; }
        public string rsname { get; set; }
    }
    public class KeycloakAuthorizationModel
    {
        public KeycloakPermissionModel[] permissions { get; set; }
    }
    public class KeycloakAccessTokenModel
    {
        public long exp { get; set; }
        public long iat { get; set; }
        public string jti { get; set; }
        public string iss { get; set; }
        public string aud { get; set; }
        public string sub { get; set; }
        public string typ { get; set; }
        public string azp { get; set; }
        public string session_state { get; set; }
        public string acr { get; set; }
        public KeycloakRealmAccessModel realm_access { get; set; }
        public KeycloakResourceAccessModel resource_access { get; set; }
        public KeycloakAuthorizationModel authorization { get; set; }
        public string scope { get; set; }
        public string sid { get; set; }
        public bool email_verified { get; set; }
        public string name { get; set; }
        public string preferred_username { get; set; }
        public string given_name { get; set; }
        public string family_name { get; set; }
        public string email { get; set; }
    }
}

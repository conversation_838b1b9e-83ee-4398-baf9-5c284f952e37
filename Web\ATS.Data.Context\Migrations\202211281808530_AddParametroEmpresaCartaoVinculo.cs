﻿namespace ATS.Data.Context.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddParametroEmpresaCartaoVinculo : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.EMPRESA", "vinculonovocartaoportador", c => c<PERSON>(nullable: false));
            AddColumn("dbo.EMPRESA", "vinculonovocartaobloqueadoportador", c => c<PERSON>(nullable: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.EMPRESA", "vinculonovocartaobloqueadoportador");
            DropColumn("dbo.EMPRESA", "vinculonovocartaoportador");
        }
    }
}

using System;
using ATS.Domain.DTO;
using ATS.Domain.DTO.TagExtratta;
using ATS.Domain.Enum;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Domain.Interface.Service
{
    public interface IPedagioService
    {
        CalcularRotaResponseDTO CalcularRota(ConsultaRotaRequest request, string documentoUsuarioAudit, string nomeUsuarioAudit);
        ComprovanteValePedagioResponse ConsultarDadosComprovante(FornecedorEnum fornecedor, long idViagem);
        CompraPedagioDTOResponse ConsultarReciboPedagio(int protocoloRequisicao);
        byte[] ConsultarReciboMoedeiro(int protocoloRequisicao);
        ObterExtratoSemPararResponseDTO GetExtratoSemParar(DateTime datainicio, DateTime datafim);
        ConsultarSituacaoTagResponseModel ConsultarSituacaoTagPlaca(string placa, Fornecedor2 fornecedor);
    }
}
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class ContratoCiotAgregadoVeiculoMap : EntityTypeConfiguration<ContratoCiotAgregadoVeiculo>
    {
        public ContratoCiotAgregadoVeiculoMap()
        {
            ToTable("CONTRATO_CIOT_AGREGADO_VEICULO");

            Has<PERSON><PERSON>(t => new {t.IdContratoCiotAgregado, t.IdVeiculo});

            Property(x => x.DataCadastro)
                .HasColumnName("datacadastro")
                .IsRequired()
                .HasColumnType("datetime");

            Property(x => x.InclusoCiot)
                .HasColumnName("inclusociot")
                .IsRequired();

            HasRequired(t => t.Veiculo)
                .WithMany(t => t.ContratoCiotAgregadoVeiculos)
                .HasForeignKey(t => t.IdVeiculo);

            HasRequired(t => t.ContratoCiotAgregado)
                .WithMany(t => t.ContratoCiotAgregadoVeiculos)
                .HasForeignKey(t => t.IdContratoCiotAgregado);
        }
    }
}

﻿using System;
using System.Collections.Generic;

namespace ATS.WS.Models.Webservice.Response.Estabelecimento
{
    public class ConsultarCadastroModel
    {
        public int IdEmpresa { get; set; }
        public string RazaoSocial { get; set; }
        public int IdTipoEstabelecimento { get; set; }
        public string CNPJEstabelecimento { get; set; }
        public string Descricao { get; set; }
        public double TaxaAntecipacao { get; set; } = 0;
        public string DescricaoTipo { get; set; }
        public string Email { get; set; }
        public int IdEstabelecimento { get; set; }
        public int IdPais { get; set; }
        public int IdEstado { get; set; }
        public bool PagamentoAntecipado { get; set; }
        public int IdCidade { get; set; }
        public string CEP { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string Logradouro { get; set; }
        public string Bairro { get; set; }
        public int? Numero { get; set; }
        public string Complemento { get; set; }
        public List<Produtos> Produtos { get; set; }
        public List<ProdutoBase> ProdutosBase { get; set; }
        public bool Associacao { get; set; }
        public bool LiberaProtocolos { get; set; }
        public bool PermiteAlterarDocumentosPesoChegada { get; set; }
        public bool ObrigaDocumentosPagamento { get; set; }
        public List<Associacoes> Associados { get; set; }
        public string RazaoSocialEstab { get; set; }
		public string Telefone { get; set; }
        public bool? PontoReferencia { get; set; }
        public TimeSpan? HoraInicialSemValidarChave { get; set; }

        public TimeSpan? HoraFinalSemValidarChave { get; set; }

        public bool RealizaPagamentoComCheque { get; set; }
        public int? IdFilialProcessoCaixaTms { get; set; }
        public List<Documento> Documentos { get; set; }
        public bool EstabelecimentoNaoCredenciado { get; set; }
        public bool ValidarChavePagamento { get; set; }
    }

    public class Produtos
    {
        public int IdProduto { get; set; }
        public int? IdProdutoBase { get; set; }
        public int? IdEstabelecimentoBase { get; set; }
        public string Descricao { get; set; }
        public string UnidadeMedida { get; set; }
        public string PrecoUnidade { get; set; }
        public string PrecoPromocional { get; set; }
        public bool Contrato { get; set; }
    }

    public class ProdutoBase
    {
        public int IdProduto { get; set; }
        public string Descricao { get; set; }
        public string UnidadeMedida { get; set; }
        public string PrecoUnidade { get; set; }
        public string PrecoUnitario { get; set; }
        public string PrecoPromocional { get; set; }
    }

    public class Associacoes
    {
        public int IdEstabelecimento { get; set; }
        public string Descricao { get; set; }
    }

    public class Documento
    {
        public string Token { get; set; }
        public DateTime? DataValidade { get; set; }
        public int IdDocumento { get; set; }
        public string Descricao { get; set; }
    }
}
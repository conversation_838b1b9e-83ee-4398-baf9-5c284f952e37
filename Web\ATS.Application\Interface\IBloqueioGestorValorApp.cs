﻿using System.Linq;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.Application.Interface
{
    public interface IBloqueioGestorValorApp : IAppBase<BloqueioGestorValor>
    {
        IQueryable<BloqueioGestorValor> GetAll();
        BloqueioGestorValorDto GetBloqueioGestorValor(int? idEmpresa, int? idFilial);
        void IntegrarValores(BloqueioGestorValorDto valores);
        decimal? ValorLimiteConfiguradoEmpresa(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa,EBloqueioOrigemTipo? origem);
        decimal? ValorLimiteConfiguradoFilial(EBloqueioGestorTipo bloqueioGestorTipo, int idEmpresa, int? idFilial,EBloqueioOrigemTipo? origem);
    }
}
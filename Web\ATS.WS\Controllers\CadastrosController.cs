using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.Controllers.Base;

namespace ATS.WS.Controllers
{
    public class CadastrosController : BaseController
    {
        private readonly ICadastrosApp _cadastrosApp;

        public CadastrosController(BaseControllerArgs baseArgs, ICadastrosApp cadastrosApp) : base(baseArgs)
        {
            _cadastrosApp = cadastrosApp;
        }

        [HttpGet]
        [AutorizarMobile]
        [Expor(EApi.Mobile, EApi.Portal)]
        public JsonResult Bancos()
        {
            try
            {
                var bancos = _cadastrosApp.Bancos();

                return Responde(bancos);
            }
            catch (Exception e)
            {
                return Responde(new ConsultarBancoResponseDTO
                {
                    Sucesso = false,
                    Mensagem = e.ToString()
                });
            }
        }
    }
}
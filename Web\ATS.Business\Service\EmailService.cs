﻿using ATS.Domain.Models;
using System;
using System.Linq;
using System.Net.Mail;
using ATS.Domain.Interface.Service;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Threading.Tasks;
using NLog;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Mime;
using ATS.CrossCutting.IoC;
using ATS.Domain.DTO;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using Microsoft.Ajax.Utilities;

namespace ATS.Domain.Service
{
    public class EmailService : IEmailService
    {
        private readonly IFilialService _filialService;
        private readonly IEmpresaRepository _empresaRepository;
        public static Logger _logger = LogManager.GetCurrentClassLogger();

        public EmailService(IFilialService filialService, IEmpresaRepository empresaRepository)
        {
            _filialService = filialService;
            _empresaRepository = empresaRepository;
        }

        public ValidationResult TestarEmail(string emailNome, string emailEndereco, int porta, bool usaSSL, string servidor, string logon, string senha)
        {
            ValidationResult validationResult = new ValidationResult();
            try
            {
                //Dados do SMTP
                var destinatario = emailEndereco;
                //Envio do email
                var mailMessage = new MailMessage();

                mailMessage.From = new MailAddress(emailEndereco, emailNome);
                mailMessage.Priority = MailPriority.Normal;
                mailMessage.IsBodyHtml = true;
                mailMessage.Subject = "Teste envio de email";
                mailMessage.Body = "Teste de configuração de servidor de email ATS.<br/>";

                mailMessage.To.Add(emailEndereco);

                var smtpClient = new SmtpClient(servidor);
                smtpClient.Port = porta;
                smtpClient.EnableSsl = usaSSL;
                //smtpClient.UseDefaultCredentials = false;
                //smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network;
                smtpClient.Timeout = 10000;
                smtpClient.Credentials = new System.Net.NetworkCredential(logon, senha);
                smtpClient.Send(mailMessage);

                mailMessage.Dispose();

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e.Message);
                validationResult.Add(e.Message);
            }


            return validationResult;
        }

        private EmailConfiguration GetEmailConfigFromFilial(int idFilial)
        {
            var filial = _filialService
                .GetQuery(null)
                .Where(f => f.IdFilial == idFilial)
                .Select(f => new
                {
                    f.EmailNome,
                    f.EmailEndereco,
                    f.EmailPorta,
                    f.EmailSsl,
                    f.EmailServidor,
                    f.EmailUsuario,
                    f.EmailSenha
                })
                .FirstOrDefault();

            if (filial?.EmailPorta != null
                && !string.IsNullOrEmpty(filial.EmailServidor)
                && !string.IsNullOrEmpty(filial.EmailUsuario)
                && !string.IsNullOrEmpty(filial.EmailSenha))
                return new EmailConfiguration
                {
                    EmailNome = filial.EmailNome,
                    EmailEndereco = filial.EmailEndereco,
                    EmailPorta = (int) filial.EmailPorta,
                    EmailSsl = filial.EmailSsl,
                    EmailServidor = filial.EmailServidor,
                    EmailUsuario = filial.EmailUsuario,
                    EmailSenha = filial.EmailSenha
                };

            return null;
        }

        

        
        private string GetHtmlTituloListaVeiculos(string titulo, bool filial)
        {
            if (filial)
            {
                return $"<tr>" +
                       $"<th colspan='7' style='background-color: #8c8c8c;'>{titulo}</th>" +
                       $"</tr>"; 
            }
            
            return $"<tr>" +
                   $"<th colspan='7' style='background-color: #cccccc;'>{titulo}</th>" +
                   $"</tr>";
       
            
        }
        
        
        

        private EmailConfiguration GetEmailconfigFromTranspotador(int idEmpresa)
        {
            var empresa = _empresaRepository
                .Where(e => e.IdEmpresa == idEmpresa)
                .Select(e => new
                {
                    e.EmailNome,
                    e.EmailEndereco,
                    e.EmailPorta,
                    e.EmailSsl,
                    e.EmailServidor,
                    e.EmailUsuario,
                    e.EmailSenha
                })
                .FirstOrDefault();

            if (empresa?.EmailPorta != null
                && !string.IsNullOrEmpty(empresa.EmailServidor)
                && !string.IsNullOrEmpty(empresa.EmailUsuario)
                && !string.IsNullOrEmpty(empresa.EmailSenha))
                return new EmailConfiguration
                {
                    EmailNome = empresa.EmailNome,
                    EmailEndereco = empresa.EmailEndereco,
                    EmailPorta = (int) empresa.EmailPorta.GetValueOrDefault(0),
                    EmailSsl = empresa.EmailSsl,
                    EmailServidor = empresa.EmailServidor,
                    EmailUsuario = empresa.EmailUsuario,
                    EmailSenha = empresa.EmailSenha
                };

            return null;
        }

        private EmailConfiguration GetEmailconfigFromTranspotador(Empresa empresa)
        {

            if ((empresa != null) && ((empresa.EmailServidor != null) && empresa.EmailServidor != "") &&
                   (empresa.EmailPorta != null) &&
                   ((empresa.EmailUsuario != null) && empresa.EmailUsuario != "") &&
                   ((empresa.EmailSenha != null) && empresa.EmailSenha != "")
                   )
            {

                EmailConfiguration retorno = new EmailConfiguration
                {
                    EmailNome = empresa.EmailNome,
                    EmailEndereco = empresa.EmailEndereco,
                    EmailPorta = (int)empresa.EmailPorta,
                    EmailSsl = empresa.EmailSsl,
                    EmailServidor = empresa.EmailServidor,
                    EmailUsuario = empresa.EmailUsuario,
                    EmailSenha = empresa.EmailSenha
                };
                return retorno;
            }
            return null;
        }

        public EmailConfiguration GetEmailConfiguration(int? idFilial, int idEmpresa)
        {
            EmailConfiguration retorno = null;
            if (idFilial.HasValue)
                retorno = GetEmailConfigFromFilial(idFilial.Value);

            if (retorno == null)
                retorno = GetEmailconfigFromTranspotador(idEmpresa);
            return retorno;
        }

        public ValidationResult EnviarEmail(EmailModel emailModel, bool copiaOculta = false, int? administradora = null, ConfiguracaoEnvioEmail config = null)
        {
            var email = config?.Endereco;
            var password = config?.Senha;
            var porta = config?.Porta;
            var smtp = config?.SMTP;

            var validacao = new ValidationResult();

            if (string.IsNullOrWhiteSpace(email))
            {
                email = Properties.Resources.ATSEmail;
                password = Properties.Resources.Senha;
                porta = Convert.ToInt32(Properties.Resources.Porta);
                smtp = Properties.Resources.SMTPGoogle;
            }
            
            _logger.Info("Email: {email}", email);
            _logger.Info("senha: {password}", password);
            _logger.Info("Porta: {porta}", porta);
            _logger.Info("SMTP: {smtp}", smtp);
            
            try
            {
                //Envio do email
                using (var mailMessage = new MailMessage())
                {
                    mailMessage.From = new MailAddress(email, emailModel.NomeVisualizacao);
                    mailMessage.Priority = MailPriority.Normal;
                    mailMessage.IsBodyHtml = true;
                    mailMessage.Subject = emailModel.Assunto;

                    if (emailModel.Anexo != null)
                        mailMessage.Attachments.Add(emailModel.Anexo);

                    if (!string.IsNullOrEmpty(emailModel.CorpoEmail))
                        mailMessage.Body = emailModel.CorpoEmail;

                    if (emailModel.AlternateView != null)
                        mailMessage.AlternateViews.Add(emailModel.AlternateView);

                    if (copiaOculta)
                    {
                        foreach (var destinatario in emailModel.Destinatarios)
                            mailMessage.Bcc.Add(destinatario);
                    }
                    else
                    {
                        foreach (var destinatario in emailModel.Destinatarios)
                            mailMessage.To.Add(destinatario);
                    }
                    
                    if (emailModel.DestinatariosCopia != null && emailModel.DestinatariosCopia.Any())
                        foreach (var destinatarioCopia in emailModel.DestinatariosCopia)
                            mailMessage.CC.Add(destinatarioCopia);
                    
                    if (emailModel.DestinatariosCopiaOculta != null && emailModel.DestinatariosCopiaOculta.Any())
                        foreach (var destinatarioOculto in emailModel.DestinatariosCopiaOculta)
                            mailMessage.Bcc.Add(destinatarioOculto);

                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                    
                    using (var smtpClient = new SmtpClient(smtp))
                    {
                        smtpClient.UseDefaultCredentials = false;
                        smtpClient.Port = porta ?? 0;
                        smtpClient.EnableSsl = true;
                        smtpClient.Timeout = Convert.ToInt32(Properties.Resources.Timeout);
                        smtpClient.Credentials = new System.Net.NetworkCredential(email, password);

                        smtpClient.Send(mailMessage);
                    }
                }

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                validacao.Add(e.GetBaseException().Message);
            }

            return validacao;
        }

        public void EnviarEmailComAnexo(EmailModel model)
        {
            try
            {
                var email = Properties.Resources.ATSEmail;
                var password = Properties.Resources.Senha;
                var porta = Convert.ToInt32(Properties.Resources.Porta);
                var smtp = Properties.Resources.SMTPGoogle;

                //Envio do email
                var mailMessage = new MailMessage
                {
                    From = new MailAddress(email, model.NomeVisualizacao),
                    Priority = MailPriority.Normal,
                    IsBodyHtml = true,
                    Subject = model.Assunto
                };

                // Adiciona o anexo no e-mail
                mailMessage.Attachments.Add(model.Anexo);

                if (!string.IsNullOrEmpty(model.CorpoEmail))
                    mailMessage.Body = model.CorpoEmail;

                if (model.AlternateView != null)
                    mailMessage.AlternateViews.Add(model.AlternateView);

                foreach (var destinatario in model.Destinatarios)
                    mailMessage.To.Add(destinatario);

                var smtpClient = new SmtpClient(smtp)
                {
                    Port = porta,
                    EnableSsl = true,
                    Timeout = Convert.ToInt32(Properties.Resources.Timeout),
                    Credentials = new System.Net.NetworkCredential(email, password)
                };
                LogManager.GetCurrentClassLogger().Error($"EnviaEmailRelatorioCheckList" + model.CorpoEmail);
                smtpClient.Send(mailMessage);

                mailMessage.Dispose();
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error($"{e.Message}-{e.InnerException?.Message}");
            }
        }
        
        public void EnviarEmailAsync(EmailModel emailModel, ConfiguracaoEnvioEmail config = null)
        {
            /*if (emails != null)
            {
                email = emails.Email;
                password = emails.Password;
                porta = emails.Porta;
                smtp = emails.Smtp;
                timeout = emails.Timeout;    
            }
            else
            {
                email = new ParametrosService().GetEmailConfiguracoesRemetente(idEmpresa); //Properties.Resources.ATSEmail;
                password = new ParametrosService().GetEmailConfiguracoesSenha(idEmpresa); //Properties.Resources.Senha;
                porta = Convert.ToInt32(new ParametrosService().GetEmailConfiguracoesPorta(idEmpresa));
                smtp = new ParametrosService().GetEmailConfiguracoesSmtp(idEmpresa); //Properties.Resources.SMTPGoogle;
                timeout = new ParametrosService().GetEmailConfiguracoesTimeOut(idEmpresa);    
            }*/

            Task.Run(() => EnviarEmail(emailModel, false, null, config));
        }
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Dynamic;
using ATS.CrossCutting.Reports.Estabelecimento;
using ATS.Domain.Interface.Dapper;

namespace ATS.Domain.Service
{
    public class EstabelecimentoService : ServiceBase, IEstabelecimentoService
    {
        private readonly IEstabelecimentoRepository _repository;
        private readonly IParametrosService _parametrosService;
        private readonly ICredenciamentoRepository _credenciamentoRepository;
        private readonly IEstabelecimentoProdutoRepository _estabelecimentoProdutoRepository;
        private readonly IEmpresaService _empresaService;
        private readonly IEstabelecimentoAssociacaoRepository _estabelecimentoAssociacaoRepository;
        private readonly IEstabelecimentoDapper _estabelecimentoDapper;
        private readonly IViagemEventoRepository _viagemEventoRepository;
        private readonly IEstabelecimentoBaseRepository _estabelecimentoBaseRepository;
        private readonly IUsuarioEstabelecimentoRepository _usuarioEstabelecimentoRepository;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly IEstabelecimentoContaBancariaService _estabelecimentoContaBancariaService;
        private readonly IEstabelecimentoProdutoService _estabelecimentoProdutoService;
        private readonly IEstabelecimentoAssociacaoService _estabelecimentoAssociacaoService;

        public EstabelecimentoService(IEstabelecimentoRepository repository, IParametrosService parametrosService,
            ICredenciamentoRepository credenciamentoRepository, IEstabelecimentoProdutoRepository estabelecimentoProdutoRepository, IEmpresaService empresaService,
            IEstabelecimentoAssociacaoRepository estabelecimentoAssociacaoRepository, IEstabelecimentoDapper estabelecimentoDapper, IViagemEventoRepository viagemEventoRepository,
            IEstabelecimentoBaseRepository estabelecimentoBaseRepository, IUsuarioEstabelecimentoRepository usuarioEstabelecimentoRepository,
            IEstabelecimentoBaseService estabelecimentoBaseService, IEstabelecimentoContaBancariaService estabelecimentoContaBancariaService,
            IEstabelecimentoProdutoService estabelecimentoProdutoService, IEstabelecimentoAssociacaoService estabelecimentoAssociacaoService)
        {
            _repository = repository;
            _parametrosService = parametrosService;
            _credenciamentoRepository = credenciamentoRepository;
            _estabelecimentoProdutoRepository = estabelecimentoProdutoRepository;
            _empresaService = empresaService;
            _estabelecimentoAssociacaoRepository = estabelecimentoAssociacaoRepository;
            _estabelecimentoDapper = estabelecimentoDapper;
            _viagemEventoRepository = viagemEventoRepository;
            _estabelecimentoBaseRepository = estabelecimentoBaseRepository;
            _usuarioEstabelecimentoRepository = usuarioEstabelecimentoRepository;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _estabelecimentoContaBancariaService = estabelecimentoContaBancariaService;
            _estabelecimentoProdutoService = estabelecimentoProdutoService;
            _estabelecimentoAssociacaoService = estabelecimentoAssociacaoService;
        }
        
        public ValidationResult Add(Estabelecimento estabelecimento)
        {
            var validationResult = new ValidationResult();
            try
            {
                validationResult.Add(AssertionConcern.AssertArgumentIsValidCNPJ(estabelecimento.CNPJEstabelecimento, "CNPJ informado é inválido."));
                if (!validationResult.IsValid)
                    return validationResult;
                
                _repository.Add(estabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return validationResult.Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public bool IsRegular(int idEstabelecimento)
        {
            var credenciamento = _credenciamentoRepository
                .FirstOrDefault(x => x.IdEstabelecimento == idEstabelecimento);

            if (credenciamento == null)
                return false;

            return credenciamento.StatusDocumentacao != EStatusDocumentacaoCredenciamento.Irregular;
        }
        
        public bool EstaCredenciado(int idEstabelecimento)
        {
            var statusCredenciamento = _credenciamentoRepository
                .Where(x => x.IdEstabelecimento == idEstabelecimento)
                .Select(x => x.Status)
                .FirstOrDefault();

            return statusCredenciamento == EStatusCredenciamento.Aprovado;
        }

        public IQueryable<Estabelecimento> GetQueryByEmpresa(int idestabelecimentoBase, int idEmpresa)
        {
            return _repository.All().Where(c => c.IdEstabelecimentoBase == idestabelecimentoBase && c.IdEmpresa == idEmpresa);
        }

        public IQueryable<EstabelecimentoProduto> GetQueryProduto()
        {
            return _estabelecimentoProdutoRepository.All();
        }

        public ValidationResult RemoverProdutos(IList<int> idprodutobase)
        {
            try
            {
                var repository = _estabelecimentoProdutoRepository;
            
                foreach (var id in idprodutobase)
                {
                    var estabelecimentosProdutos = GetQueryProduto().Where(c => c.IdProdutoBase == id).ToList();
                    
                    repository.DeleteRange(estabelecimentosProdutos);
                }
            
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult ReplicarProdutoBase(EstabelecimentoBaseProduto estabelecimentoBaseProduto)
        {
            var repository = _repository;
            var empresaService = _empresaService;
            var parametroService = _parametrosService;
            
            try
            {
                var empresasParaAutorizar = empresaService.All().Select(c => c.IdEmpresa).ToList();

                foreach (var empresa in empresasParaAutorizar)
                {
                    var empresaAutorizaJsl = parametroService.GetAutorizaEstabelecimentosRedeJSL(empresa);
                
                    if (!empresaAutorizaJsl)
                        continue;
                    
                    var estabelecimento = repository.Where(c => c.IdEstabelecimentoBase == estabelecimentoBaseProduto.IdEstabelecimentoBase && c.IdEmpresa == empresa).Include(c => c.EstabelecimentoProdutos).FirstOrDefault();

                    if (estabelecimento == null)
                        continue;
                
                    if (estabelecimento.EstabelecimentoProdutos.Any(c => c.IdProdutoBase == estabelecimentoBaseProduto.IdProduto))
                    {
                        var estabelecimentosProduto = estabelecimento.EstabelecimentoProdutos.First(c => c.IdProdutoBase == estabelecimentoBaseProduto.IdProduto);
                        estabelecimentosProduto.Descricao = estabelecimentoBaseProduto.Descricao;
                        estabelecimentosProduto.PrecoPromocional = estabelecimentoBaseProduto.PrecoPromocional;
                        estabelecimentosProduto.PrecoUnitario = estabelecimentoBaseProduto.PrecoUnitario;
                    }
                    else
                    {
                        var estabelecimentosProduto = new EstabelecimentoProduto
                        {
                            IdProdutoBase = estabelecimentoBaseProduto.IdProduto,
                            IdEstabelecimentoBase = estabelecimentoBaseProduto.IdEstabelecimentoBase,
                            Descricao = estabelecimentoBaseProduto.Descricao,
                            PrecoPromocional = estabelecimentoBaseProduto.PrecoPromocional,
                            PrecoUnitario = estabelecimentoBaseProduto.PrecoUnitario,
                            UnidadeMedida = estabelecimentoBaseProduto.UnidadeMedida
                        };
                
                    estabelecimento.EstabelecimentoProdutos.Add(estabelecimentosProduto);
                    }

                    repository.Update(estabelecimento); 
                }
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Inativar(int idEstabelecimento)
        {
            try
            {
                IEstabelecimentoRepository estabelecimentoRepository = _repository;

                Estabelecimento estabelecimento = estabelecimentoRepository
                    .Get(idEstabelecimento);
                estabelecimento.Ativo = false;
                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                estabelecimentoRepository.Update(estabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public ValidationResult Reativar(int idEstabelecimento)
        {
            try
            {
                IEstabelecimentoRepository estabelecimentoRepository = _repository;

                Estabelecimento estabelecimento = estabelecimentoRepository
                    .Get(idEstabelecimento);
                estabelecimento.Ativo = true;
                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                estabelecimentoRepository.Update(estabelecimento);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public ValidationResult Update(Estabelecimento estabelecimento)
        {
            try
            {
                IEstabelecimentoRepository estabelecimentoRepository = _repository;

                estabelecimento.DataUltimaAtualizacao = DateTime.Now;
                estabelecimentoRepository.Update(estabelecimento);

                var estabelecimentoAssociacaoRepository = _estabelecimentoAssociacaoRepository;

                if (estabelecimento.Associacao) return new ValidationResult();

                var estabelecimentoAssociacao = estabelecimentoAssociacaoRepository
                    .Find(x => x.IdAssociacao == estabelecimento.IdEstabelecimento).ToList();

                foreach (var item in estabelecimentoAssociacao)
                    estabelecimentoAssociacaoRepository.Delete(item);

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add($"Exception: {e.Message} \n InnerException: {e.InnerException?.Message}");
            }
        }

        public DataModel<EstabelecimentoModel> ConsultaGrid(int? idEmpresa, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool filtrarApenasCredenciadosEmpresa = false)
        {
            var estabelecimentos = _repository
                .GetAll().Include(x => x.Empresa).Include(x => x.EstabelecimentoBase).Include(x => x.Cidade).Include(x => x.Estado).Include(x => x.Pais);

            if (idEmpresa.HasValue)
                estabelecimentos = estabelecimentos.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (filtrarApenasCredenciadosEmpresa)
                estabelecimentos = estabelecimentos.Where(e => e.Credenciamentos.Any(o => o.IdEmpresa == idEmpresa && o.Status == EStatusCredenciamento.Aprovado));

            if (idTipoEstabelecimento.HasValue)
                estabelecimentos = estabelecimentos.Where(x => x.IdTipoEstabelecimento == idTipoEstabelecimento.Value);

            if (!string.IsNullOrWhiteSpace(descricao))
                estabelecimentos = estabelecimentos.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                estabelecimentos = estabelecimentos.OrderBy(x => x.IdEstabelecimento);
            else
                estabelecimentos = estabelecimentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            estabelecimentos = estabelecimentos.AplicarFiltrosDinamicos(filters);

            return new DataModel<EstabelecimentoModel>
            {
                totalItems = estabelecimentos.Count(),
                items = estabelecimentos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new EstabelecimentoModel
                {
                    IdEstabelecimento = x.IdEstabelecimento,
                    Descricao = x.Descricao,
                    RazaoSocial = x.RazaoSocial,
                    Ativo = x.Ativo,
                    IdEstabelecimentoBase = x.IdEstabelecimentoBase,
                    Empresa = x.Empresa?.RazaoSocial,
                    Credenciado = x.Credenciado ? "Sim" : "Não",
                    CNPJ = x.CNPJEstabelecimento.ToCNPJFormato(),
                    Associacao = x.Associacao ? "Sim" : "Não",
                    AtivoDescricao = x.Ativo ? "Sim" : "Não",
                    Endereco = $"{x.Logradouro} {x.Numero}, {x.Complemento} {x.Bairro}, {x.Cidade?.Nome}, {x.Estado?.Nome} - {x.CEP.ToCEPFormato()}"

                })
            };
        }

        public int? GetByBase(int idBase)
        {
            return _repository.FirstOrDefault(x => x.IdEstabelecimentoBase == idBase)?.IdEstabelecimento;
        }

        public int? GetIdBase(int idEstabelecimento)
        {
            return _repository.Where(x => x.IdEstabelecimento == idEstabelecimento).Select(x => x.IdEstabelecimentoBase).FirstOrDefault();
        }

        public IEnumerable<ConsultaEstabelecimentoModelResponse> ConsultarPorEmpresa(int idEmpresa, string cnpjEstabelecimento)
        {
            /*var estabelecimentos = _repository
                .Find(x => x.IdEmpresa == idEmpresa)
                .Include(x => x.EstabelecimentoProdutos)
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.TipoEstabelecimento.Icone)
                .Include(x => x.Pais)
                .Include(x => x.Estado)
                .Include(x => x.Cidade);

            if (!string.IsNullOrWhiteSpace(cnpjEstabelecimento))
                estabelecimentos = estabelecimentos.Where(x => x.CNPJEstabelecimento == cnpjEstabelecimento);

            return estabelecimentos;*/
            var estabelecimentos = _estabelecimentoDapper.ConsultarEstabelecimentos(idEmpresa, cnpjEstabelecimento);
            return estabelecimentos;
        }

        public IQueryable<Estabelecimento> ConsultarEstabelecimentosRota(
            int idEmpresa,
            decimal latitudeOrigem, decimal longitudeOrigem,
            decimal? latitudeDestino = null, decimal? longitudeDestino = null,
            decimal? raio = 50, int[] idsTipoEstabelecimento = null, bool buscaCredenciados = false)
        {
            IEnumerable<int> ids;
            if (latitudeDestino.GetValueOrDefault(0) == 0 && longitudeDestino.GetValueOrDefault(0) == 0)
                ids = _repository
                    .GetIdsEstabelecimentoNoRaio(latitudeOrigem, longitudeOrigem, raio ?? 50);
            else
                ids = _repository
                    .GetIdsEstabelecimentoNoPercurso(latitudeOrigem, longitudeOrigem,
                        latitudeDestino ?? 0, longitudeDestino ?? 0, raio ?? 50);

            var estabelecimentosQy = _repository
                .Find(x => x.IdEmpresa == idEmpresa)
                .AsNoTracking()
                .Include(x => x.EstabelecimentoProdutos)
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.TipoEstabelecimento.Icone)
                .Include(x => x.Pais)
                .Include(x => x.Estado)
                .Include(x => x.Cidade)
                .Where(e => e.Ativo);

            if (idsTipoEstabelecimento != null && idsTipoEstabelecimento.Any())
                estabelecimentosQy = estabelecimentosQy.Where(e => idsTipoEstabelecimento.Contains(e.IdEstabelecimento));

            //TipoEstabelecimento

            if (ids != null)
                estabelecimentosQy = estabelecimentosQy.Where(e => ids.Contains(e.IdEstabelecimento));
            
            if(buscaCredenciados)
                estabelecimentosQy = estabelecimentosQy
                    .Where(e => ((e.Latitude != null && e.Latitude != 0) || (e.Longitude != null && e.Longitude != 0)));
            else
            {
                estabelecimentosQy = estabelecimentosQy
                    .Where(e => ((e.Latitude != null && e.Latitude != 0) || (e.Longitude != null && e.Longitude != 0)) &&
                                e.Credenciado);
            }
            return estabelecimentosQy;
        }

        public Estabelecimento ConsultarPorId(int idEstabelecimento)
        {
            var estabelecimentos = _repository
                .Find(x => x.IdEstabelecimento == idEstabelecimento)
                .Include(x => x.Empresa)
                .Include(x => x.EstabelecimentoProdutos)
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.TipoEstabelecimento.Icone)
                .Include(x => x.Pais)
                .Include(x => x.Estado)
                .Include(x => x.Cidade);

            return estabelecimentos.FirstOrDefault();
        }

        public Estabelecimento Get(int idEstabelecimento, bool withIncludes = true)
        {
            var query = _repository.Where(x => x.IdEstabelecimento == idEstabelecimento);

            if (withIncludes)
                query = query.Include(x => x.Empresa)
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.EstabelecimentoProdutos)
                .Include(x => x.Cidade)
                .Include(x => x.Estado)
                .Include(x => x.Pais)
                .Include(x => x.EstabelecimentoAssociacoesAssociacao)
                .Include(x => x.EstabelecimentoAssociacoesAssociacao.Select(p => p.Associacao))
                .Include(x => x.EstabelecimentoAssociacoesAssociacao.Select(p => p.Estabelecimento))


                .Include(x => x.EstabelecimentoAssociacoesEstabelecimento)
                .Include(x => x.EstabelecimentoAssociacoesEstabelecimento.Select(p => p.Associacao))
                .Include(x => x.EstabelecimentoAssociacoesEstabelecimento.Select(p => p.Estabelecimento))


                    .Include(x => x.EstabelecimentoBase.Credenciamentos);
                
            return query.FirstOrDefault();
        }

        /// <summary>
        /// Retorna o estabelecimento pela empresa e pelo estabelecimento base
        /// </summary>
        /// <param name="idEstabelecimentoBase"></param>
        /// <param name="idEmpresa"></param>
        /// <returns>
        /// <para>Caso não encontre o registro, retorna 0 para a chave</para>
        /// </returns>
        public KeyValuePair<int, bool> GetEstabelecimentoGeracaoProtocolo(int idEstabelecimentoBase, int idEmpresa)
        {
            var estabelecimento = _repository
                .Find(o => o.IdEstabelecimentoBase == idEstabelecimentoBase && o.IdEmpresa == idEmpresa)
                .Select(x => new
                {
                    x.IdEstabelecimento,
                    x.Credenciado
                }).FirstOrDefault();

            var retorno = new KeyValuePair<int, bool>(estabelecimento?.IdEstabelecimento ?? 0, estabelecimento?.Credenciado ?? false);

            return retorno;

        }

        public Estabelecimento GetByIdEstabelecimentoBase(int idEstabelecimentoBase, int idEmpresa)
        {
            var credenciamento = _credenciamentoRepository
                .Find(x => x.IdEstabelecimentoBase == idEstabelecimentoBase && x.Status == EStatusCredenciamento.Aprovado && x.IdEmpresa == idEmpresa)
                .FirstOrDefault();

            return credenciamento?.IdEstabelecimento != null
                ? _repository.Get(credenciamento.IdEstabelecimento.Value)
                : null;
        }

        public IQueryable<Estabelecimento> GetEstabelecimentoPorIdBase(int idEstabelecimentoBase)
        {
            return _repository.Where(x => x.IdEstabelecimentoBase == idEstabelecimentoBase);
        }

        public List<KeyValuePair<int, int>> GetIdEstabelecimentosAssociadosLiberacaoProtocolo(int idEstabelecimentoBase)
        {
            var credenciamento = _credenciamentoRepository
                .Find(x => x.IdEstabelecimentoBase == idEstabelecimentoBase && x.Status == EStatusCredenciamento.Aprovado && x.Estabelecimento.LiberaProtocolos);

            var estabelecimentosAssociados = _estabelecimentoAssociacaoRepository.Find(x => x.IdEstabelecimento == credenciamento.FirstOrDefault().IdEstabelecimento);

            var retorno = _credenciamentoRepository
                .Find(x => estabelecimentosAssociados.Any(y => y.IdEstabelecimento == x.IdEstabelecimento) && x.IdEstabelecimentoBase.HasValue)
                .AsEnumerable()
                .Select(x => new KeyValuePair<int, int>(x.IdEstabelecimentoBase ?? 0, x.IdEmpresa))
                .ToList();
            return retorno;
        }

        public void ExcluirAssociados(int idEstabelecimento)
        {
            var delete = _estabelecimentoAssociacaoRepository.Find(x => x.IdEstabelecimento == idEstabelecimento).ToList();
            if (delete.Any())
                foreach (var item in delete)
                    _estabelecimentoAssociacaoRepository.Delete(item);
        }

        public Estabelecimento GetByIdEstabelecimentoEmpresa(int idEstabelecimento, int idEmpresa)
        {
            var credenciamento = _credenciamentoRepository
                .Find(x => x.IdEstabelecimento == idEstabelecimento && x.Status == EStatusCredenciamento.Aprovado && x.IdEmpresa == idEmpresa)
                .FirstOrDefault();

            return credenciamento != null && credenciamento.IdEstabelecimento.HasValue ? _repository.Get(credenciamento.IdEstabelecimento.Value) : null;
        }

        public Estabelecimento GetByIdEstabelecimentoBaseEmpresa(int idEstabelecimentoBase, int idEmpresa)
        {
            return _repository.FirstOrDefault(x => x.IdEstabelecimentoBase == idEstabelecimentoBase && x.IdEmpresa == idEmpresa);
        }

        public Estabelecimento Get(string cnpj, int idEmpresa)
        {
            return _repository
                .Include(x => x.Empresa)
                .Include(x => x.TipoEstabelecimento)
                .Include(x => x.EstabelecimentoProdutos)
                .Include(x => x.Cidade)
                .Include(x => x.Estado)
                .Include(x => x.Pais)
                .Include(x => x.Credenciamentos)
                .FirstOrDefault(x => x.CNPJEstabelecimento == cnpj && x.IdEmpresa == idEmpresa);
        }

        public object ConsultarAssociacoes(int? idTipoEstabelecimento, string descricao, int take, int page,
            OrderFilters orderFilters, List<QueryFilters> filters)
        {
            var estabelecimentos = _repository
                .Where(x => x.Associacao);

            if (idTipoEstabelecimento.HasValue)
                estabelecimentos = estabelecimentos.Where(x => x.IdTipoEstabelecimento == idTipoEstabelecimento.Value);

            if (!string.IsNullOrWhiteSpace(descricao))
                estabelecimentos = estabelecimentos.Where(x => x.Descricao.Contains(descricao));

            if (orderFilters == null || string.IsNullOrWhiteSpace(orderFilters.Campo))
                estabelecimentos = estabelecimentos.OrderBy(x => x.IdEstabelecimento);
            else
                estabelecimentos =
                    estabelecimentos.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            estabelecimentos = estabelecimentos.AplicarFiltrosDinamicos(filters);

            return new
            {
                totalItems = estabelecimentos.Count(),
                items = estabelecimentos.Skip((page - 1) * take).Take(take)
                    .ToList().Select(x => new
                    {
                        x.IdEstabelecimento,
                        x.Descricao,
                        x.Ativo,
                        CNPJ = x.CNPJEstabelecimento
                    })
            };
        }

        public object GetEstabelecimentoPorViagem(string tokenViagemEvento, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var viagemEvento = _viagemEventoRepository
                .Include(x => x.Viagem)
                .Include(x => x.Viagem.ViagemEstabelecimentos)
                .Include(x => x.Viagem.ViagemEstabelecimentos.Select(y => y.Estabelecimento))
                .FirstOrDefault(x => x.Token == tokenViagemEvento);

            if (viagemEvento == null) return null;

            #region Objeto de retorno

            var retorno = new Func<List<Estabelecimento>, object>(estabelecimentos =>
            {
                return new
                {
                    totalItems = estabelecimentos.Count,
                    items = estabelecimentos.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdEstabelecimento,
                    x.Descricao,
                    x.Ativo,
                    x.IdEstabelecimentoBase,
                    Empresa = x.Empresa?.RazaoSocial,
                    Credenciado = x.Credenciado ? "Sim" : "Não"
                })
                };
            });

            #endregion

            if (viagemEvento.Viagem?.ViagemEstabelecimentos != null && viagemEvento.Viagem.ViagemEstabelecimentos.Any())
            {
                var estabelecimentos = viagemEvento.Viagem.ViagemEstabelecimentos
                    .Where(x => x.TipoEventoViagem == viagemEvento.TipoEventoViagem)
                    .Select(x => x.Estabelecimento)
                    .ToList();

                if (!estabelecimentos.Any())
                {
                    var estabelecimentosPorEmpresa = _repository
                        .Find(x => x.Credenciado && x.Credenciamentos.Any(y => y.IdEmpresa == viagemEvento.IdEmpresa))
                        .ToList();

                    return retorno(estabelecimentosPorEmpresa);
                }

                return retorno(estabelecimentos);
            }
            else
            {
                var estabelecimentos = _repository
                .Find(x => x.Credenciado && x.Credenciamentos.Any(y => y.IdEmpresa == viagemEvento.IdEmpresa))
                .ToList();

                return retorno(estabelecimentos);
            }
        }

        public List<Estabelecimento> GetAssociacoesEmpresa(int idEmpresa, int? ignoreId)
        {
            var assocs = _repository.Find(x => x.IdEmpresa == idEmpresa && x.Associacao);
            if (assocs.Any() && ignoreId.HasValue)
                assocs = assocs.Where(x => x.IdEstabelecimento != ignoreId.Value);

            return assocs.ToList();
        }

        public List<EstabelecimentoBase> GetAssociados(int idEstabelecimento)
        {
            return _estabelecimentoBaseRepository
                .Where(x => x.AssociacoesBaseEstabelecimento.Any(y => y.IdAssociacao == idEstabelecimento))
                .ToList();
        }
        
        public List<Estabelecimento> GetAssociacoes(int idEmpresa, int? ignoreId, int idUsuario, bool onlyEmpresa)
        {
            var assocs = _repository
                .Include(x => x.Credenciamentos)
                .Where(x => x.IdEmpresa == idEmpresa && x.Associacao && x.Credenciamentos.FirstOrDefault(o => o.IdEmpresa == idEmpresa && o.Status == EStatusCredenciamento.Aprovado) != null);
            if (onlyEmpresa)
                return assocs.ToList();

            if (ignoreId > 0)
            {
                var estab = this.GetByBase(ignoreId.Value);
                if (estab > 0)
                    assocs = assocs.Where(x => estab.Value != x.IdEstabelecimento);
            }

            var idEstabelecimentoBaseUsuario = _usuarioEstabelecimentoRepository.Find(x => x.IdUsuario == idUsuario).Select(x => x.IdEstabelecimento).FirstOrDefault();
            var idEstabelecimentoUsuario = _credenciamentoRepository.Find(x => x.IdEstabelecimentoBase == idEstabelecimentoBaseUsuario && x.Status == EStatusCredenciamento.Aprovado).Select(x => x.IdEstabelecimento).FirstOrDefault();

            var idsAssociacoesEstabelecimento = _estabelecimentoAssociacaoRepository.Find(x => x.IdEstabelecimento == idEstabelecimentoUsuario).Select(x => x.IdAssociacao);

            if (idsAssociacoesEstabelecimento.Any())
                assocs = assocs.Where(x => idsAssociacoesEstabelecimento.Contains(x.IdEstabelecimento));
            else
                return new List<Estabelecimento>();

            return assocs.ToList();
        }

        public byte[] GerarRelatorioGridEstabelecimento(int? idEmpresa, int? idTipoEstabelecimento, string descricao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, string logo)
        {
            var estabelecimentos = ConsultaGrid(idEmpresa, idTipoEstabelecimento, descricao, int.MaxValue, 1, orderFilters, filters);

            return new RelatorioEstabelecimento().GetReport(estabelecimentos.items, tipoArquivo, logo);
        }

        public ValidationResult UpdateByEstabelecimentoBase(int idEstabelecimentoBase)
        {
            try
            {
                var estabelecimentoRepository = _repository;

                var estabelecimentoBase = _estabelecimentoBaseService.Get(idEstabelecimentoBase);
                var estabelecimentos = estabelecimentoRepository
                    .Include(o => o.Empresa)
                    .Include(o => o.EstabelecimentoProdutos)
                    .Include(o => o.EstabelecimentoContasBancarias)
                    .Include(o => o.EstabelecimentoAssociacoesAssociacao)
                    .Include(o => o.EstabelecimentoAssociacoesEstabelecimento)
                    .Where(o => o.IdEstabelecimentoBase == idEstabelecimentoBase);

                if (!estabelecimentos.Any())
                    return new ValidationResult();

                foreach (var estabelecimento in estabelecimentos)
                {
                    if (estabelecimento.Empresa == null || !estabelecimento.Empresa.Atualizaestabelecimento)
                        continue;

                    estabelecimento.TipoEstabelecimento = estabelecimentoBase.TipoEstabelecimento;
                    estabelecimento.Telefone = estabelecimentoBase.Telefone;
                    estabelecimento.RazaoSocial = estabelecimentoBase.RazaoSocial;
                    estabelecimento.Descricao = estabelecimentoBase.Descricao;
                    estabelecimento.Email = estabelecimentoBase.Email;
                    estabelecimento.Associacao = estabelecimentoBase.Associacao;

                    if (estabelecimento.EstabelecimentoContasBancarias != null && estabelecimento.EstabelecimentoContasBancarias.Any())
                        _estabelecimentoContaBancariaService.DeleteListContasBancarias(estabelecimento
                            .EstabelecimentoContasBancarias.Select(o => o.IdEstabelecimentoContaBancaria).ToList());

                    if (estabelecimento.EstabelecimentoProdutos != null && estabelecimento.EstabelecimentoProdutos.Any())
                    {
                        var result = _estabelecimentoProdutoService.Delete(estabelecimento.EstabelecimentoProdutos.ToList());

                        if (!result.IsValid)
                            return new ValidationResult().Add(result.Errors.FirstOrDefault().Message);
                    }

                    if (estabelecimento.EstabelecimentoAssociacoesEstabelecimento != null &&estabelecimento.EstabelecimentoAssociacoesEstabelecimento.Any())
                        _estabelecimentoAssociacaoService.DeleteById(estabelecimento.EstabelecimentoAssociacoesEstabelecimento.Select(o => o.IdAssociacao).ToList(), estabelecimento.IdEstabelecimento);

                    estabelecimento.EstabelecimentoContasBancarias = new List<EstabelecimentoContaBancaria>();
                    if (estabelecimentoBase.EstabelecimentoBaseContasBancarias != null && estabelecimentoBase.EstabelecimentoBaseContasBancarias.Any())
                        foreach (var estabelecimentoBaseContaBancaria in estabelecimentoBase.EstabelecimentoBaseContasBancarias)
                        {
                            var estabelecimentoContaBancaria = new EstabelecimentoContaBancaria
                            {
                                DigitoConta = estabelecimentoBaseContaBancaria.DigitoConta,
                                CnpjTitular = estabelecimentoBaseContaBancaria.CnpjTitular,
                                CodigoBanco = estabelecimentoBaseContaBancaria.CodigoBanco,
                                Conta = estabelecimentoBaseContaBancaria.Conta,
                                NomeBanco = estabelecimentoBaseContaBancaria.NomeBanco,
                                Agencia = estabelecimentoBaseContaBancaria.Agencia,
                                NomeTitular = estabelecimentoBaseContaBancaria.NomeTitular,
                                TipoConta = estabelecimentoBaseContaBancaria.TipoConta,
                                NomeConta = estabelecimentoBaseContaBancaria.NomeConta,
                                IdEstabelecimentoContaBancaria = 0,
                                IdEstabelecimento = estabelecimento.IdEstabelecimento
                            };

                            estabelecimento.EstabelecimentoContasBancarias.Add(estabelecimentoContaBancaria);
                        }

                    estabelecimento.EstabelecimentoProdutos = new List<EstabelecimentoProduto>();
                    if (estabelecimentoBase.EstabelecimentoBaseProdutos != null && estabelecimentoBase.EstabelecimentoBaseProdutos.Any())
                        foreach (var estabelecimentoBaseProduto in estabelecimentoBase.EstabelecimentoBaseProdutos)
                        {
                            var estabelecimentoProduto = new EstabelecimentoProduto
                            {
                                IdEstabelecimentoBase = estabelecimentoBaseProduto.IdEstabelecimentoBase,
                                IdEstabelecimento = estabelecimento.IdEstabelecimento,
                                Descricao = estabelecimentoBaseProduto.Descricao,
                                IdProdutoBase = estabelecimentoBaseProduto.IdProduto,
                                PrecoPromocional = estabelecimentoBaseProduto.PrecoPromocional,
                                PrecoUnitario = estabelecimentoBaseProduto.PrecoUnitario,
                                UnidadeMedida = estabelecimentoBaseProduto.UnidadeMedida
                            };

                            estabelecimento.EstabelecimentoProdutos.Add(estabelecimentoProduto);
                        }

                    estabelecimento.EstabelecimentoAssociacoesEstabelecimento = new List<EstabelecimentoAssociacao>();
                    if (estabelecimentoBase.AssociacoesBaseEstabelecimento != null && estabelecimentoBase.AssociacoesBaseEstabelecimento.Any())
                        foreach (var estabelecimentoBaseAssociacao in estabelecimentoBase.AssociacoesBaseEstabelecimento)
                        {
                            var idsAssociacao =  _repository.FirstOrDefault(o => o.IdEstabelecimentoBase == estabelecimentoBaseAssociacao.IdAssociacao)?.IdEstabelecimento;

                            if (idsAssociacao.HasValue)
                            {
                                var estabelecimentoAssociacao = new EstabelecimentoAssociacao
                                {
                                    IdAssociacao = idsAssociacao.Value,
                                    IdEstabelecimento = estabelecimento.IdEstabelecimento
                                };

                                estabelecimento.EstabelecimentoAssociacoesEstabelecimento.Add(estabelecimentoAssociacao);
                            }
                        }

                    estabelecimentoRepository.Update(estabelecimento);
                }
                
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public List<Estabelecimento> EstabelecimentosDaRota(int idRota)
        {
            return _repository
                .GetAll()
                .Include(e => e.TipoEstabelecimento.Icone)
                .Where(e => e.RotaEstabelecimento.Any(re => re.IdRota == idRota))
                .ToList();
        }

        public bool ValidarChaveTokenPorHorarario(int idEstabelecimentoBase, int idEmpresa)
        {
            var estabelecimento = _repository
                .FirstOrDefault(o => o.IdEstabelecimentoBase == idEstabelecimentoBase && o.IdEmpresa == idEmpresa);

            if (estabelecimento?.HoraInicialSemValidarChave == null || !estabelecimento.HoraFinalSemValidarChave.HasValue)
                return false;

            var horaAtual = DateTime.Now.TimeOfDay;

            // Caso a hora inicial seja maior que a hora final significa que a validação deve ser feita de um dia para o outro
            if (estabelecimento.HoraInicialSemValidarChave.Value > estabelecimento.HoraFinalSemValidarChave.Value)
            {
                // Monta uma data com o dia de hoje e a hora de inicio permitida
                var podeApartirDas = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, estabelecimento.HoraInicialSemValidarChave.Value.Hours, estabelecimento.HoraInicialSemValidarChave.Value.Minutes, 0);
                // Monta uma data com o dia de hoje e a hora de fim permitida
                var podeAte = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, estabelecimento.HoraFinalSemValidarChave.Value.Hours, estabelecimento.HoraFinalSemValidarChave.Value.Minutes, 0);

                var dataHoraAtual = DateTime.Now;

                if (dataHoraAtual > podeAte && dataHoraAtual < podeApartirDas)
                    return false;

                return true;
            }

            if (horaAtual < estabelecimento.HoraInicialSemValidarChave)
                return false;

            if (horaAtual > estabelecimento.HoraFinalSemValidarChave)
                return false;

            return true;
    }
        
        public IQueryable<Estabelecimento> GetQueryByCnpj(string cnpj)
        {
            var repository = _repository;

            return repository.All().Where(c => c.CNPJEstabelecimento == cnpj);
}
        
        public IQueryable<Estabelecimento> GetQueryById(int idEstabelecimento)
        {
            var repository = _repository;

            return repository.All().Where(c => c.IdEstabelecimento == idEstabelecimento);
        }

        public int? GetIdPorCnpj(string cnpj, int idEmpresa)
        {
            return _repository.GetIdPorCnpj(cnpj, idEmpresa);
        }

        public bool GetEstabelecimentoAtivo(int idEstabelecimento)
        {
            return _repository.Find(x => x.IdEstabelecimento == idEstabelecimento)
                .Select(x => x.Ativo)
                .FirstOrDefault();
        }
}
}

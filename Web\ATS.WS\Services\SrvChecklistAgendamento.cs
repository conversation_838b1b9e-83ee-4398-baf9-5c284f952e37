﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Helpers;
using ATS.Domain.Validation;
using ATS.WS.checkListAgendamentos.Webservice.Request.CheckList;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request.Agendamento;
using ATS.WS.Models.Webservice.Request.CheckList;
using ATS.WS.Models.Webservice.Response.Agendamento;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Xml.Linq;
using ATS.Domain.Service;

namespace ATS.WS.Services
{
    public class SrvChecklistAgendamento : SrvBase
    {
        
        public Retorno<object> Cadastrar(ChecklistAgendamentoCreateRequest checkListAgendamento)
        {
            var validationResult = new ValidationResult();
            try
            {
                var requestMapped = Mapper.Map<ChecklistAgendamentoCreateRequest, CheckListAgendamento>(checkListAgendamento);
                requestMapped.IdCidade = new CidadeApp().GetIdPorIBGE(checkListAgendamento.IbgeCidade);
                requestMapped.IdEstado = new EstadoApp().GetIdPorIBGE(checkListAgendamento.IbgeEstado);
                requestMapped.IdPais = new PaisApp().GetIdPaisPorBACEN(checkListAgendamento.BacenPais);
                requestMapped.IdEmpresa = new EmpresaApp().GetIdPorCNPJ(checkListAgendamento.CNPJEmpresa) ?? 0;

                if (requestMapped.IdVeiculo == null || requestMapped.IdVeiculo == 0)
                    requestMapped.IdVeiculo = new VeiculoService().GetIdPorPlaca(checkListAgendamento.Placa, requestMapped.IdEmpresa);

                if (checkListAgendamento.IdUsuarioVistoriador.HasValue)
                {
                    var usuarioVistoriador = new UsuarioApp().Get(checkListAgendamento.IdUsuarioVistoriador.Value);
                    requestMapped.IdUsuarioVistoriador = usuarioVistoriador.IdUsuario;
                }
                else
                {
                    if (string.IsNullOrEmpty(checkListAgendamento.CpfUsuarioVistoriador))
                        return new Retorno<object>(false, "CPF do usuário vistoriador não foi informado. ", null);

                    var usuarioVistoriador = new UsuarioApp().GetPorCNPJCPF(checkListAgendamento.CpfUsuarioVistoriador);
                    requestMapped.IdUsuarioVistoriador = usuarioVistoriador.IdUsuario;
                }

                validationResult = new CheckListAgendamentoApp().Cadastrar(requestMapped);

                if(validationResult.IsValid)
                    return new Retorno<object>(true, "", requestMapped.IdCheckListAgendamento);

                return new Retorno<object>(false, validationResult.ToString(), null);
            }
            catch (Exception e)
            {
                return new Retorno<object>(false, e.Message, null);
            }
        }

        public ValidationResult Update(CheckListAgendamento checkListAgendamento, int idUsuarioVistoriadorMaster)
        {
            try
            {
                
                var usuarioApp = new UsuarioApp();
                var checklistAgendamentoApp = new CheckListAgendamentoApp();
                var entity = checklistAgendamentoApp.Get(checkListAgendamento.IdCheckListAgendamento);

                var usuarioVistoriador = usuarioApp.Get(checkListAgendamento.IdUsuarioVistoriador);
                var usuario = usuarioApp.Get(idUsuarioVistoriadorMaster);
                
                var agendamentoEdit = checkListAgendamento;
                entity.Status = agendamentoEdit.Status;
                entity.Bairro = agendamentoEdit.Bairro;
                entity.Cep = agendamentoEdit.Cep.Replace("-", "");
                entity.Complemento = agendamentoEdit.Complemento;
                entity.DataAceite = agendamentoEdit.DataAceite ?? entity.DataAceite;
                entity.DataCancelamento = agendamentoEdit.DataCancelamento ?? entity.DataCancelamento;
                entity.DataRejeicao = agendamentoEdit.DataRejeicao ?? entity.DataRejeicao;
                entity.DataVistoria = agendamentoEdit.DataVistoria;
                entity.Endereco = agendamentoEdit.Endereco ?? entity.Endereco;
                entity.IdCheckList = agendamentoEdit.IdCheckList ?? entity.IdCheckList;
                entity.IdCidade = agendamentoEdit.IdCidade;
                entity.IdEmpresa = agendamentoEdit.IdEmpresa;
                entity.IdEstado = agendamentoEdit.IdEstado;
                entity.IdPais = agendamentoEdit.IdPais;
                entity.IdTipoCheckList = agendamentoEdit.IdTipoCheckList;
                entity.TipoCheckList = agendamentoEdit.TipoCheckList ?? entity.TipoCheckList;
                entity.IdUsuarioAceito = agendamentoEdit.IdUsuarioAceito ?? entity.IdUsuarioAceito;
                entity.IdUsuarioCancelamento = agendamentoEdit.IdUsuarioCancelamento ?? entity.IdUsuarioCancelamento;
                entity.IdUsuarioRejeicao = agendamentoEdit.IdUsuarioRejeicao ?? entity.IdUsuarioRejeicao;
                entity.Latitude = agendamentoEdit.Latitude ?? entity.Latitude;
                entity.Longitude = agendamentoEdit.Longitude ?? entity.Longitude;
                entity.IdVeiculo = agendamentoEdit.IdVeiculo ?? entity.IdVeiculo;
                entity.Numero = agendamentoEdit.Numero ?? entity.Numero;
                entity.PrazoAgendamento = agendamentoEdit.PrazoAgendamento;

                var vistoriadorAnteriorEdicao = entity.IdUsuarioVistoriador;

                
                entity.IdUsuarioVistoriador = agendamentoEdit.IdUsuarioVistoriador;

                var responseEdit = new CheckListAgendamentoApp().Update(entity);

                if (responseEdit.IsValid)
                {
                    //Caso o novo vistoriador for diferente do anterior a edição, devemos enviar um push de novo agendamento e de cancelamento ao outro 
                    
                }


                return responseEdit;
            }

            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
        
        public ChecklistAgendamentoWebUpdateRequest ConsultarPorId(int idChecklistAgendamento)
        {
            var checklistAgendamento = new CheckListAgendamentoApp().GetWithAllChilds(idChecklistAgendamento);

            if (checklistAgendamento == null)
                return null;

            var checklistAgendamentoEdit = new ChecklistAgendamentoWebUpdateRequest
            {
                IdCheckListAgendamento = checklistAgendamento.IdCheckListAgendamento,
                DataAceite = checklistAgendamento.DataAceite,
                DataCancelamento = checklistAgendamento.DataCancelamento,
                DataCadastro = checklistAgendamento.DataCadastro,
                Status = checklistAgendamento.Status,
                DataRejeicao = checklistAgendamento.DataRejeicao,
                Bairro = checklistAgendamento.Bairro,
                Complemento = checklistAgendamento.Complemento,
                DataVistoria = checklistAgendamento.DataVistoria,
                DataVistoriaStr = checklistAgendamento.DataVistoria.ToString("dd/MM/yyyy HH:mm"),
                Cep = checklistAgendamento.Cep,
                Endereco = checklistAgendamento.Endereco,
                IdCheckList = checklistAgendamento.IdCheckList,
                IdCidade = checklistAgendamento.IdCidade,
                IdEmpresa = checklistAgendamento.IdEmpresa,
                RazaoSocialEmpresa = checklistAgendamento.Empresa?.RazaoSocial,
                IdEstado = checklistAgendamento.IdEstado,
                IdTipoCheckList = checklistAgendamento.IdTipoCheckList,
                DescricaoTipoCheckList = checklistAgendamento.TipoCheckList?.Descricao,
                IdPais = checklistAgendamento.IdPais,
                IdUsuarioAceito = checklistAgendamento.IdUsuarioAceito,
                IdUsuarioCancelamento = checklistAgendamento.IdUsuarioCancelamento,
                IdUsuarioRejeicao = checklistAgendamento.IdUsuarioRejeicao,
                IdUsuarioVistoriador = checklistAgendamento.IdUsuarioVistoriador,
                NomeUsuarioVistoriador = checklistAgendamento.UsuarioVistoriador?.Nome,
                IdVeiculo = checklistAgendamento.IdVeiculo,
                Latitude = checklistAgendamento.Latitude,
                Longitude = checklistAgendamento.Longitude,
                Numero = checklistAgendamento.Numero,
                Placa = checklistAgendamento.Placa,
                PrazoAgendamento = checklistAgendamento.PrazoAgendamento,
                PrazoAgendamentoStr = checklistAgendamento.PrazoAgendamento?.ToString("dd/MM/yyyy HH:mm"),
                DataCadastroStr = checklistAgendamento.DataCadastro.ToString("dd/MM/yyyy HH:mm"),
                NomeCliente = checklistAgendamento.NomeCliente
            };

            return checklistAgendamentoEdit;
        }

        public Retorno<object> ConsultarDetalhes(int idAgendamento, string cpfCnpjUsuario)
        {
            var agendamento =
                new CheckListAgendamentoApp().Get(
                    idAgendamento);

            var resposta = Mapper.Map<CheckListAgendamento, ChecklistAgendamentoCreateRequest>(agendamento);
            var cidade = new CidadeApp().Get(agendamento.IdCidade);
            var estado = new EstadoApp().Get(agendamento.IdEstado);
            var pais = new PaisApp().Get(agendamento.IdPais);

            if (cidade.Ativo && cidade.IBGE.HasValue)
            {
                resposta.IbgeCidade = cidade.IBGE.Value;
            }

            if (estado.Ativo && estado.IBGE.HasValue)
            {
                resposta.IbgeEstado = estado.IBGE.Value;
            }

            if (pais.Ativo && pais.BACEN.HasValue)
            {
                resposta.BacenPais = pais.BACEN.Value;
            }

            return new Retorno<object>(true, null, resposta);
        }

        public Retorno<AgendamentoResponse> Consultar(ConsultarAgendamentoRequest request)
        {
            var validoParaConsulta = ValidarConsulta(request);

            if (!validoParaConsulta.IsValid)
                return new Retorno<AgendamentoResponse>(false, validoParaConsulta.ToString(), null);

            if ((!request.DataInicial.HasValue || !request.DataFinal.HasValue) && (!request.IdCheckListAgendamento.HasValue))
                return new Retorno<AgendamentoResponse>(false, "Data inicial e data final devem ser informadas", null);

            if (request.DataInicial.HasValue || request.DataFinal.HasValue)
                if (request.DataFinal < request.DataInicial)
                    return new Retorno<AgendamentoResponse>(false, "Data final deve ser maior que a data inicial", null);

            var autorizacaoEmpresa = new AutenticacaoAplicacaoApp()
                .GetAutenticacaoAplicacaoPorCNPJAplicacao(request.CNPJAplicacao, request.Token);

            var idEmpresa = new EmpresaApp().GetIdPorCNPJ(request.CNPJEmpresa);

            if (!idEmpresa.HasValue)
                return new Retorno<AgendamentoResponse>(false, "Não foi possível identificar a empresa. ", null);

            if (!autorizacaoEmpresa.Any(x => x.IdEmpresa == idEmpresa))
                return new Retorno<AgendamentoResponse>(false, "Empresa não autorizada para realizar esta consulta. ", null);

            var usuario = new UsuarioApp().GetPorCNPJCPF(request.CpfCnpjUsuario);

            if (usuario == null)
                return new Retorno<AgendamentoResponse>(false, "Não foi possível identificar o usuário", null);

            if (!usuario.Vistoriador)
                return new Retorno<AgendamentoResponse>(false, "Usuario não autorizado para realizar esta consulta.", null);

            var agendamentos = new CheckListAgendamentoApp().Consultar(request.DataInicial,
                request.DataFinal, idEmpresa.Value, request.IdCheckListAgendamento, request.DataBase);

            if(agendamentos == null)
                return new Retorno<AgendamentoResponse>()
                {
                    Sucesso = true,
                    Objeto = new AgendamentoResponse()
                    {
                        Agendamentos = new List<ConsultarAgendamentoResponse>(),
                        UsuarioAgendamento = new List<UsuarioVistoriadorResponse>()
                    },
                };

            agendamentos = !usuario.VistoriadorMaster
                ? agendamentos.Where(o => o.IdUsuarioVistoriador == usuario.IdUsuario)
                : agendamentos.Where(o => o.UsuarioVistoriador.IdGrupoUsuario == usuario.IdGrupoUsuario);

            if (request.Status.HasValue)
                agendamentos = agendamentos.Where(x => x.Status == request.Status);

            if (request.IdVistoriador.HasValue)
                agendamentos = agendamentos.Where(x => x.IdUsuarioVistoriador == request.IdVistoriador.Value);

            if (!string.IsNullOrEmpty(request.Placa))
                agendamentos = agendamentos.Where(x => x.Placa == request.Placa);
            
            agendamentos = agendamentos.Include(a => a.Checklist);


            var lRetorno = new AgendamentoResponse();
            lRetorno.Agendamentos = new List<ConsultarAgendamentoResponse>();
            lRetorno.UsuarioAgendamento = new List<UsuarioVistoriadorResponse>();

            foreach (var item in agendamentos.OrderBy(x => x.DataVistoria))
            {
                var lItem = new ConsultarAgendamentoResponse()
                {
                    IdCheckListAgendamento = item.IdCheckListAgendamento,
                    IdCheckList = item.IdCheckList,
                    DataVistoria = item.DataVistoria,
                    Placa = item.Placa,
                    Latitude = item.Latitude,
                    Longitude = item.Longitude,
                    PrazoAgendamento = item.PrazoAgendamento,
                    PrazoVistoria = item.Checklist?.PrazoVistoria,
                    DataRejeicao = item.DataRejeicao,
                    DataCancelamento = item.DataCancelamento,
                    DataConcluido = item.Checklist?.DataHoraFim,
                    DataCadastro = item.DataCadastro,
                    IdTipoCheckList = item.IdTipoCheckList,
                    Endereco = RetornaEnderecoAgendamento(item),
                    StatusAgendamento = item.Status,
                    StatusChecklist = item.Checklist?.Status,
                    DataHoraFrustradoUsuario = item.Checklist?.DataHoraFrustradoUsuario,
                    IdVistoriador = item.Checklist == null ? item.UsuarioVistoriador.IdUsuario : item.Checklist.IdVistoriador
                };

                if (lItem.StatusAgendamento == EStatusAgendamentoCheckList.Pendente)
                {
                    lItem.StatusAgendamentoChecklist = (DateTime.Now > lItem.PrazoAgendamento) ? EStatusChecklistMobile.PrazoAceiteExpirado : EStatusChecklistMobile.Pendente;
                }
                else if (lItem.StatusAgendamento == EStatusAgendamentoCheckList.Aceito)
                {
                    switch (lItem.StatusChecklist)
                    {
                        case EStatusChecklist.Pendente:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.AguardandoCaptura;
                            break;
                        case EStatusChecklist.FrustradoPeloUsuario:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.FrustradoPeloUsuario;
                            break;
                        case EStatusChecklist.FrustradoPorTempo:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.FrustradoPorTempo;
                            break;
                        case EStatusChecklist.Cancelado:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Cancelado;
                            break;
                        case EStatusChecklist.Concluido:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Concluido;
                            break;
                        case null:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                else
                {
                    switch (lItem.StatusAgendamento)
                    {
                        case EStatusAgendamentoCheckList.Rejeitado:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Rejeitado;
                            break;
                        case EStatusAgendamentoCheckList.Cancelado:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Cancelado;
                            break;
                        case EStatusAgendamentoCheckList.Pendente:
                            break;
                        case EStatusAgendamentoCheckList.Aceito:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }

                lRetorno.Agendamentos.Add(lItem);

                if(item.Checklist != null)
                {
                    var usuarioChecklistAgendamento = new UsuarioService().GetAllChilds(item.Checklist.IdVistoriador);
                    if(usuarioChecklistAgendamento!= null)
                    {
                        lRetorno.UsuarioAgendamento.Add(new UsuarioVistoriadorResponse()
                        {
                            IdUsuario = usuarioChecklistAgendamento.IdUsuario,
                            Nome = usuarioChecklistAgendamento.Nome,
                            Foto = usuarioChecklistAgendamento.VistoriadorMaster && usuarioChecklistAgendamento.Foto != null ? Convert.ToBase64String(usuarioChecklistAgendamento.Foto) : string.Empty
                        });
                    }
                }

                if (!lRetorno.UsuarioAgendamento.Any(c => c.IdUsuario == item.UsuarioVistoriador.IdUsuario))
                {
                    lRetorno.UsuarioAgendamento.Add(new UsuarioVistoriadorResponse()
                    {
                        IdUsuario = item.UsuarioVistoriador.IdUsuario,
                        Nome = item.UsuarioVistoriador.Nome,
                        Foto = usuario.VistoriadorMaster && item.UsuarioVistoriador.Foto != null ? Convert.ToBase64String(item.UsuarioVistoriador.Foto) : string.Empty
                    });
                }
            }

            return new Retorno<AgendamentoResponse>(true, null, lRetorno);
        }

        private string RetornaEnderecoAgendamento(CheckListAgendamento item)
        {
            var endereco = "";

            if (item.Endereco != "")
                endereco = item.Endereco;

            if (!string.IsNullOrEmpty(item.Numero))
                endereco += (endereco == "" ? item.Numero : "," + item.Numero);

            if (!string.IsNullOrEmpty(item.Bairro))
                endereco += (endereco == "" ? item.Bairro : " - " + item.Bairro);

            if (item.Cidade != null)
                endereco += (endereco == "" ? item.Cidade.Nome : " - " + item.Cidade.Nome);

            if (item.Estado != null)
                endereco += (endereco == "" ? item.Estado.Sigla : " - " + item.Estado.Sigla);



            return endereco;
        }

        public Retorno<List<ConsultarAgendamentoResponse>> ConsultarSemData(ConsultarAgendamentoRequest request)
        {
            var autorizacaoEmpresa = new AutenticacaoAplicacaoApp()
                .GetAutenticacaoAplicacaoPorCNPJAplicacao(request.CNPJAplicacao, request.Token);

            var idEmpresa = new EmpresaApp().GetIdPorCNPJ(request.CNPJEmpresa);

            if (!idEmpresa.HasValue)
                return new Retorno<List<ConsultarAgendamentoResponse>>(false, "Não foi possível identificar a empresa. ", null);

            if (!autorizacaoEmpresa.Any(x => x.IdEmpresa == idEmpresa))
                return new Retorno<List<ConsultarAgendamentoResponse>>(false, "Empresa não autorizada para realizar esta consulta. ", null);

            var usuario = new UsuarioApp().GetPorCNPJCPF(request.CpfCnpjUsuario);

            if (usuario == null)
                return new Retorno<List<ConsultarAgendamentoResponse>>(false, "Não foi possível identificar o usuário", null);

            if (!usuario.Vistoriador)
                return new Retorno<List<ConsultarAgendamentoResponse>>(false, "Usuario não autorizado para realizar esta consulta.", null);

            var agendamentos = new CheckListAgendamentoApp().ConsultarSemData(idEmpresa.Value);

            if (usuario.VistoriadorMaster)
            {
                agendamentos = agendamentos.Where(o => o.UsuarioVistoriador.IdGrupoUsuario == usuario.IdGrupoUsuario);
            }
            else
            {
                agendamentos = agendamentos.Where(o => o.IdUsuarioVistoriador == usuario.IdUsuario);
            }

            if (request.Status.HasValue)
                agendamentos = agendamentos.Where(x => x.Status == request.Status);

            if (!string.IsNullOrEmpty(request.Placa))
                agendamentos = agendamentos.Where(x => x.Placa == request.Placa);

            agendamentos = agendamentos.Include(a => a.Checklist);

            var retorno = new List<ConsultarAgendamentoResponse>();

            foreach (var item in agendamentos.OrderBy(x => x.DataVistoria))
            {
                var lItem = new ConsultarAgendamentoResponse()
                {
                    IdCheckListAgendamento = item.IdCheckListAgendamento,
                    IdCheckList = item.IdCheckList,
                    DataVistoria = item.DataVistoria,
                    Placa = item.Placa,
                    Latitude = item.Latitude,
                    Longitude = item.Longitude,
                    PrazoAgendamento = item.PrazoAgendamento,
                    PrazoVistoria = item.Checklist?.PrazoVistoria,
                    DataRejeicao = item.DataRejeicao,
                    DataCancelamento = item.DataCancelamento,
                    DataConcluido = item.Checklist?.DataHoraFim,
                    DataCadastro = item.DataCadastro,
                    IdTipoCheckList = item.IdTipoCheckList,
                    Endereco = item.Endereco,
                    StatusAgendamento = item.Status,
                    StatusChecklist = item.Checklist?.Status,
                    DataHoraFrustradoUsuario = item.Checklist?.DataHoraFrustradoUsuario
                };

                if (lItem.StatusAgendamento == EStatusAgendamentoCheckList.Aceito || lItem.StatusAgendamento == EStatusAgendamentoCheckList.Pendente)
                {
                    switch (lItem.StatusChecklist)
                    {
                        case EStatusChecklist.Pendente:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.AguardandoCaptura;
                            break;
                        case EStatusChecklist.FrustradoPeloUsuario:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.FrustradoPeloUsuario;
                            break;
                        case EStatusChecklist.FrustradoPorTempo:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.FrustradoPorTempo;
                            break;
                        case EStatusChecklist.Cancelado:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Cancelado;
                            break;
                        case EStatusChecklist.Concluido:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Concluido;
                            break;
                        case null:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                else
                {
                    switch (lItem.StatusAgendamento)
                    {
                        case EStatusAgendamentoCheckList.Rejeitado:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Rejeitado;
                            break;
                        case EStatusAgendamentoCheckList.Cancelado:
                            lItem.StatusAgendamentoChecklist = EStatusChecklistMobile.Cancelado;
                            break;
                        case EStatusAgendamentoCheckList.Pendente:
                            break;
                        case EStatusAgendamentoCheckList.Aceito:
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }

                retorno.Add(lItem);
            }

            return new Retorno<List<ConsultarAgendamentoResponse>>(true, null, retorno);
        }

        public Retorno<object> AlterarStatus(AceitarAgendamentoRequest request)
        {
            var autorizacaoEmpresa = new AutenticacaoAplicacaoApp()
                .GetAutenticacaoAplicacaoPorCNPJAplicacao(request.CNPJAplicacao, request.Token);

            var idEmpresa = new EmpresaApp().GetIdPorCNPJ(request.CNPJEmpresa);
            if (!idEmpresa.HasValue)
            {
                return new Retorno<object>(false, "Não foi possível identificar a empresa. ", null);
            }

            if (!autorizacaoEmpresa.Any(x => x.IdEmpresa == idEmpresa))
            {
                return new Retorno<object>(false, "Empresa não autorizada para realizar esta solicitação. ", null);
            }

            var usuario = new UsuarioApp().GetPorCNPJCPF(request.CpfCnpjUsuario);
            if (usuario == null)
            {
                return new Retorno<object>(false, "Não foi possível identificar o usuário", null);
            }

            if (!usuario.Vistoriador)
            {
                return new Retorno<object>(false, "Usuario não autorizado para realizar esta solicitação.", null);
            }

            var validationResult = new CheckListAgendamentoApp().AlterarStatus(
                request.IdAgendamento, usuario, request.Status);
            if (!validationResult.Key.IsValid)
            {
                return new Retorno<object>(false, validationResult.Key.ToString(), null);
            }

            return new Retorno<object>(true, validationResult.Value);
        }

        public Retorno<object> Rejeitar(int idCheckListAgendamento, string cpfCnpjUsuario)
        {
            try
            {
                var usuario = new UsuarioApp().GetPorCNPJCPF(cpfCnpjUsuario);

                if (usuario == null)
                    return new Retorno<object>(false, $"Usuário de CPF/CNPJ {cpfCnpjUsuario} não encontrado.");

                var response = new CheckListAgendamentoApp().Rejeitar(idCheckListAgendamento, usuario.IdUsuario);

                return response.IsValid
                    ? new Retorno<object>(true, "Agendamento rejeitado com sucesso", null)
                    : new Retorno<object>(false, response.Errors.FirstOrDefault()?.Message, null);
            }
            catch (Exception e)
            {
                return new Retorno<object>(false, e.Message, null);
            }
        }

        private static ValidationResult ValidarConsulta(ConsultarAgendamentoRequest request)
        {
            var validationResult = new ValidationResult();

            if (!request.DataInicial.HasValue && !request.IdCheckListAgendamento.HasValue)
            {
                validationResult.Add("É necessário informar uma data inicial para a consulta. ");
            }

            if (!request.DataFinal.HasValue && !request.IdCheckListAgendamento.HasValue)
            {
                validationResult.Add("É necessário informar uma data final para a consulta. ");
            }

            if (string.IsNullOrWhiteSpace(request.CNPJEmpresa))
            {
                validationResult.Add("CNPJ da empresa é obrigatório. ");
            }

            return validationResult;
        }

        private static Tuple<decimal?, decimal?, string> GetDadosEndereco(string endereco)
        {
            string cep = null;

            var requestUri =
                $"http://maps.googleapis.com/maps/api/geocode/xml?address={Uri.EscapeDataString(endereco)}&sensor=false";

            var request = WebRequest.Create(requestUri);
            var response = request.GetResponse();
            var xDocument = XDocument.Load(response.GetResponseStream());

            var result = xDocument.Element("GeocodeResponse")?.Element("result");
            var locationElement = result?.Element("geometry")?.Element("location");

            var latitude = string.IsNullOrEmpty(locationElement?.Element("lat")?.Value)
                ? new decimal?()
                : Convert.ToDecimal(locationElement?.Element("lat")?.Value, new CultureInfo("en-US"));

            var longitude = string.IsNullOrEmpty(locationElement?.Element("lng")?.Value)
                ? new decimal?()
                : Convert.ToDecimal(locationElement?.Element("lng")?.Value, new CultureInfo("en-US"));

            var query = from c in xDocument.Descendants("address_component") select c;

            foreach (var item in query)
            {
                var query2 = from c in item.Descendants("type") select c;

                foreach (var item2 in query2)
                {
                    if (item2.Value != "postal_code")
                        continue;

                    var query3 = from c in item.Descendants("long_name") select c;

                    foreach (var item3 in query3)
                        cep = item3.Value?.OnlyNumbers();
                }
            }

            return new Tuple<decimal?, decimal?, string>(latitude, longitude, cep);
        }
    }
}
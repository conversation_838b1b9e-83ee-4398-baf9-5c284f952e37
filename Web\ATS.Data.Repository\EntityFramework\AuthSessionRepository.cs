﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.Objects.Auth;

namespace ATS.Data.Repository.EntityFramework
{
    public class AuthSessionRepository : Repository<AuthSession>, IAuthSessionRepository
    {
        public AuthSessionRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Intervalo de tempo para executar update na tabela AuthSession para incrementar o tempo de atividade da sessão (Sistema derruba login após 60 minutos sem uso)
        /// </summary>
        private const int UpdateDataUltimaRequisicaoWaitMinutes = 5;
        
        /// <summary>
        /// Controle para evitar updates desnecessários na tabela de AuthSession para reduzir consumo de banco de dados.
        /// </summary>
        private static readonly ConcurrentBag<AuthSessionLoginUpdateModel> UpdateDataUltimaRequisicaoCache = new ConcurrentBag<AuthSessionLoginUpdateModel>();
        
        public AuthSession GetByToken(string Token)
        {
            IQueryable<AuthSession> auth = from p in All()
                   .Include(inc => inc.Usuario)
                   .Include(inc => inc.Usuario.UsuarioEstabelecimentos)
                                           where p.Token.ToString() == Token
                                           select p;

            return auth.FirstOrDefault();
        }

        public AuthSession Gerar(int IdUsuario)
        {
            return Add(new AuthSession()
            {
                IdUsuario = IdUsuario,
                DataCadastro = DateTime.Now,
                DataUltimaReq = DateTime.Now,
                Token = Guid.NewGuid()
            });
        }

        public int GetIdUsuario(string Token)
        {
            return Where(c => c.Token.ToString() == Token).Select(c => c.IdUsuario).FirstOrDefault();
        }

        public void AtualizaDataUltimaRequisicao(string Token)
        {
            var lastUpdate = UpdateDataUltimaRequisicaoCache.FirstOrDefault(i => i.AuthSessionToken == Token);
            
            // Se não possir nenhuma informação no cache, ou o mesmo já tiver expirado
            if (lastUpdate == null ||
                (DateTime.Now - lastUpdate.LastDatabaseUpdate).TotalMinutes >= UpdateDataUltimaRequisicaoWaitMinutes)
            {
                lock (UpdateDataUltimaRequisicaoCache)
                {
                    // Lock pattern necessita double check para ser eficaz e não gerar overhead de CPU travando a rotina inteira caso o lock fosse no inicio do bloco
                    lastUpdate = UpdateDataUltimaRequisicaoCache.FirstOrDefault(i => i.AuthSessionToken == Token);

                    if (lastUpdate == null ||
                        (DateTime.Now - lastUpdate.LastDatabaseUpdate).TotalMinutes >= UpdateDataUltimaRequisicaoWaitMinutes)
                    {
                        var authSs = GetByToken(Token);
                        authSs.DataUltimaReq = DateTime.Now;
                        Update(authSs);

                        if (lastUpdate == null)
                        {
                            lastUpdate = new AuthSessionLoginUpdateModel
                            {
                                AuthSessionToken = Token,
                                LastDatabaseUpdate = authSs.DataUltimaReq
                            };
                            UpdateDataUltimaRequisicaoCache.Add(lastUpdate);
                        }
                        else
                            lastUpdate.LastDatabaseUpdate = authSs.DataUltimaReq;
                    }
                }
            }
        }

        public void InvalidarSessao(string sessionKey)
        {
#pragma warning disable 618
            var authSs = FirstOrDefault(x => x.Token.ToString() == sessionKey);
#pragma warning restore 618
            if (authSs != null)
            {
                authSs.DataUltimaReq = authSs.DataUltimaReq.AddMinutes(-30);
                Update(authSs);
            }
        }

        /// <summary>
        /// Este método invalida sessoes ativas para o mesmo usuário, então
        /// isto bloqueia a possibilidade de várias pessoas logarem com mesmo usuário
        /// </summary>
        /// <param name="idUsuario"></param>
        public void InvalidarSessoesAtivasParaUsuario(int idUsuario)
        {
            var authsValidos = Find(x => x.IdUsuario == idUsuario &&
                 DateTime.Now < DbFunctions.AddMinutes(x.DataUltimaReq, 30));
            if (authsValidos.Count() > 2)
                authsValidos.ToList().ForEach(ath =>
                {
                    ath.DataUltimaReq = ath.DataUltimaReq.AddMinutes(-30);
                    Update(ath);
                });
        }
    }
}
﻿using ATS.Domain.Models;
using System;
using System.Net.Mail;
using ATS.Domain.Interface.Service;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Collections.Generic;
using ATS.Domain.Interface.Database;
using ATS.Domain.Enum;
using ATS.CrossCutting.IoC;
using System.Linq;

namespace ATS.Domain.Service
{
    public class ViagemSolicitacaoAbonoService : IViagemSolicitacaoAbonoService
    {

        private readonly IViagemSolicitacaoAbonoRepository _viagemSolicitacaoRepository;
        private readonly IViagemEventoRepository _viagemEventoRepository;


        public ViagemSolicitacaoAbonoService(IViagemSolicitacaoAbonoRepository viagemSolicitacaoRepository, IViagemEventoRepository viagemEventoRepository)
        {
            _viagemSolicitacaoRepository = viagemSolicitacaoRepository;
            _viagemEventoRepository = viagemEventoRepository;
        }
        
        public List<ViagemSolicitacaoAbono> GetSolicitacoes(string token, string numero, DateTime? dataInicio, DateTime? dataFim, EStatusAbono? status)
        {

            var viagemEvento = _viagemSolicitacaoRepository
                .Include(x => x.ViagemEvento)
                .Where(x => x.DataSolicitacao >= dataInicio && x.DataSolicitacao <= dataFim);


            if (!string.IsNullOrEmpty(numero))
                viagemEvento = viagemEvento.Where(x => x.ViagemEvento.NumeroRecibo == numero);

            if (!string.IsNullOrEmpty(token))
                viagemEvento = viagemEvento.Where(x => x.ViagemEvento.Token == token);

            if (status.HasValue)
                viagemEvento = viagemEvento.Where(x => x.Status  == status);

            return viagemEvento.ToList();
        }

        public ViagemSolicitacaoAbono AlterarStatus(int IdViagemSolicitacao, EStatusAbono status)
        {

            ViagemSolicitacaoAbono viagemSolicitacao = _viagemSolicitacaoRepository
                .Include(x => x.ViagemEvento)
                .FirstOrDefault(x => x.IdViagemSolicitacao == IdViagemSolicitacao);

            if (viagemSolicitacao == null)
                throw new Exception("Solicitação de abono não encontrada!");

            var viagemEvento = viagemSolicitacao.ViagemEvento;

            viagemSolicitacao.Status = status;
            viagemSolicitacao.DataAtualizacao = DateTime.Now;


            viagemEvento.QuebraMercadoriaAbonada = viagemSolicitacao.Status == EStatusAbono.Solicitado;  

            _viagemSolicitacaoRepository.Update(viagemSolicitacao);
            _viagemEventoRepository.Update(viagemEvento);

            return viagemSolicitacao;
        }
    }
}

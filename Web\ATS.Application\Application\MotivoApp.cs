﻿using System.Linq;
using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    public class MotivoApp : AppBase, IMotivoApp
    {
        private readonly IMotivoService _motivoService;

        public MotivoApp(IMotivoService motivoService)
        {
            _motivoService = motivoService;
        }
        
        public IQueryable<Motivo> GetAtivos(int idEmpresa, ETipoMotivo? tipo = null)
        {
            return _motivoService.GetAtivos(idEmpresa, tipo);
        }

        public Motivo Get(int idMotivo)
        {
            return _motivoService.Get(idMotivo);
        }
    }
}

﻿using System;
using System.Linq;
using System.Threading.Tasks;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Interface.Validators;
using ATS.Domain.Models.Categoria;
using ATS.Domain.Models.LocalizacaoUsuarios;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using NLog;

namespace ATS.Domain.Service
{
    public class LocalizacaoUsuarioService : BaseService<ILocalizacaoUsuarioRepository>, ILocalizacaoUsuarioService
    {
        private readonly ILogger _logger;
        private readonly ILocalizacaoUsuarioAddModelValidator Validator;
        public LocalizacaoUsuarioService(ILocalizacaoUsuarioRepository repository, IUserIdentity sessionUser, ILocalizacaoUsuarioAddModelValidator validator) : base(repository, sessionUser)
        {
            Validator = validator;
            _logger = LogManager.GetCurrentClassLogger();
        }

        public BusinessResult Add(LocalizacaoUsuarioAddModel model)
        {
            try
            {
                var validation = Validator.Validate(model);

                if (!validation.IsValid)
                    return BusinessResult.Error(validation.Errors.Select(c => c.ErrorMessage).ToList());

                var entity = new LocalizacaoUsuario
                {
                    Id = Guid.NewGuid(),
                    Latitude = model.Latitude,
                    Longitude = model.Longitude,
                    DataCadastro = DateTime.Now,
                    DataAtualizacao = DateTime.Now,
                    IdUsuario = model.IdUsuario,
                    UsuarioCadastro = "Não Informado",
                    IPV4 = model.IPV4 ?? string.Empty
                };

                Repository.Add(entity);

                return BusinessResult.Valid();
            }
            catch (Exception e)
            {
                var message = e.GetBaseException().ToString();

                _logger.Error("Ocorreu um erro interno: {message}", message);

                return BusinessResult.Error(message);
            }
        }
    }
}
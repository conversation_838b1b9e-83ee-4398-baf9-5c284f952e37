﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;

namespace ATS.Application.Application
{
    /// <summary>
    /// Isso refere-se ao serviço de callback
    /// </summary>
    public class WebhookApp : AppBase, IWebhookApp
    {
        private readonly IWebhookService _webhookService;

        public WebhookApp(IWebhookService webhookService)
        {
            _webhookService = webhookService;
        }

        public void Add(Webhook webhook)
        {
            _webhookService.Add(webhook);
        }

        public void Update(Webhook webhook)
        {
            _webhookService.Update(webhook);
        }

        public Webhook GetByIdRegistro(int id, WebhookTipoEvento tipoEvento)
        {
            return _webhookService.GetByIdRegistro(id, tipoEvento);
        }
    }
}
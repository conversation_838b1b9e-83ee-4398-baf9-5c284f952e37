using System;
using System.Collections.Generic;
using System.Linq;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using ATS.Domain.Validation;
using ATS.WS.Models.Common.Request.Base;

namespace ATS.WS.Models.Webservice.Request
{
    public class CargaAvulsaRequest : RequestBase
    {
        public string NroControleIntegracao { get; set; }

        public string CPFUsuario { get; set; }
        
        public string NomeUsuario { get; set; }
        
        public string CNPJFilial { get; set; }

        public List<CargaAvulsa> CargasAvulsas { get; set; }

        public ValidationResult<EValidationCargaAvulsa> Valida()
        {
            var validation = new ValidationResult<EValidationCargaAvulsa>();
            
            if (string.IsNullOrEmpty(NroControleIntegracao))
                validation.Add(EValidationCargaAvulsa.NumeroControleNaoInformado, EFaultType.Error);
            else if (NroControleIntegracao.Length > 100)
                validation.Add(EValidationCargaAvulsa.NumeroControleIntegracaoExcedendoQuantidadeCaracteres, EFaultType.Error);

            if (string.IsNullOrEmpty(CPFUsuario))
                validation.Add(EValidationCargaAvulsa.CpfUsuarioNaoInformado, EFaultType.Error);
            else if (CPFUsuario.Length > 11)
                validation.Add(EValidationCargaAvulsa.CpfUsuarioNaoInformado, EFaultType.Error);

            if (CargasAvulsas == null || !CargasAvulsas.Any())
                validation.Add(EValidationCargaAvulsa.NenhumaCargaAvulsaInformada, EFaultType.Error);
            else
            {
                var cargaAvulsa = CargasAvulsas.FirstOrDefault();

                if (cargaAvulsa != null)
                {
                    if (string.IsNullOrEmpty(cargaAvulsa.Documento) && string.IsNullOrEmpty(cargaAvulsa.CPF))
                        validation.Add(EValidationCargaAvulsa.DocumentoCpfNaoInformado, EFaultType.Error);
                    else if(!string.IsNullOrEmpty(cargaAvulsa.Documento) && !cargaAvulsa.Documento.ValidateDocument())
                        validation.Add(EValidationCargaAvulsa.CpfCnpjInválido, EFaultType.Error);

                    if (!string.IsNullOrEmpty(cargaAvulsa.CPF) && !cargaAvulsa.CPF.ValidateDocument())
                        validation.Add(EValidationCargaAvulsa.CpfCnpjInválido, EFaultType.Error);
                    
                    if (string.IsNullOrEmpty(cargaAvulsa.Nome))
                        validation.Add(EValidationCargaAvulsa.NomeMotoristaNaoInformado, EFaultType.Error);
                    else if (cargaAvulsa.Nome.Length > 100)
                        validation.Add(EValidationCargaAvulsa.NomeMotoristaExcedendoQuantidadeCaracteres, EFaultType.Error);

                    if (cargaAvulsa.Valor <= 0)
                        validation.Add(EValidationCargaAvulsa.ValorNegativoOuZerado, EFaultType.Error);

                    if (!string.IsNullOrEmpty(cargaAvulsa.Observacao))
                        if (cargaAvulsa.Observacao.Length > 255)
                            validation.Add(EValidationCargaAvulsa.ObservacaoExcedendoQuantidadeCaracteres, EFaultType.Error);
                }
                else
                    validation.Add(EValidationCargaAvulsa.NenhumaCargaAvulsaInformada, EFaultType.Error);
            }
            
            return validation;
        }
    }

    public class CargaAvulsa
    {
        public int? IdCargaAvulsa { get; set; }
        
        public string Documento { get; set; }
        public string CPF { get; set; }
        public string Nome { get; set; }
        public string Placa { get; set; }
        public decimal Valor { get; set; }
        public string Observacao { get; set; }
        
        public string PlacaCavalo { get; set; }
    }
}
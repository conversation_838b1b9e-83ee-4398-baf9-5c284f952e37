using System;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;

namespace ATS.WS.ControllersATS
{
    public class LoginAtsController : DefaultController
    {
        private readonly IUsuarioApp _usuarioApp;

        public LoginAtsController(IUsuarioApp usuarioApp)
        {
            _usuarioApp = usuarioApp;
        }
        /*
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult RecuperarSenha(string cpf, string email, string dominio)
        {
            try
            {
                if (string.IsNullOrEmpty(cpf)|| string.IsNullOrEmpty(email))
                    throw new Exception("CPF e e-mail são obrigatórios!");

                _usuarioApp.ResetarSenhaPorCpfEmail(cpf, email, dominio);

                return ResponderSucesso($"Sua nova senha foi enviada para o e-mail {email}!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.GetBaseException().Message);
            }
        }*/
    }
}
﻿using ATS.CrossCutting.IoC.Validation;
using ATS.Domain.Enum;
using ATS.Domain.Exceptions;

namespace ATS.Domain.Entities
{
    public class EstabelecimentoContaBancaria : IValidatedEntity
    {
        /// <summary>
        /// Código PK
        /// </summary>
        public int IdEstabelecimentoContaBancaria { get; set; }

        /// <summary>
        /// Código do estabelecimento FK
        /// </summary>
        public int IdEstabelecimento { get; set; }

        /// <summary>
        /// Descrição da conta
        /// </summary>
        public string NomeConta { get; set; }

        /// <summary>
        /// Código do banco de acordo com o banco central
        /// </summary>
        public string CodigoBanco { get; set; }

        /// <summary>
        /// Nome do banco
        /// </summary>
        public string NomeBanco { get; set; }

        /// <summary>
        /// Número da agência
        /// </summary>
        public string Agencia { get; set; }

        /// <summary>
        /// Número da conta
        /// </summary>
        public string Conta { get; set; }

        /// <summary>
        /// DÃígito verficador do número da conta
        /// </summary>
        public string DigitoConta { get; set; }

        /// <summary>
        /// Enumeração do tipo de conta bancária
        /// </summary>
        public ETipoContaBancaria TipoConta { get; set; }

        /// <summary>
        /// CNPJ do titular da conta
        /// </summary>
        public string CnpjTitular { get; set; }

        /// <summary>
        /// Nome do titular da conta
        /// </summary>
        public string NomeTitular { get; set; }

        #region Referências

        public virtual Estabelecimento Estabelecimento { get; set; }

        #endregion

        public void Validate()
        {
            if (string.IsNullOrEmpty(NomeConta))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O Nome da conta é obrigatório.");

            if (NomeConta.Length > 100)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O Nome da conta deve conter no máximo 100 caracteres.");

            if (string.IsNullOrEmpty(CodigoBanco))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O código do banco é obrigatório.");

            if (CodigoBanco.Length > 10)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O código do banco deve conter no máximo 10 caracteres.");

            if (string.IsNullOrEmpty(NomeBanco))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O nome do banco é obrigatório");

            if (NomeBanco.Length > 100)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O nome do banco deve conter no máximo 100 caracteres.");

            if (string.IsNullOrEmpty(Agencia))
                throw new EstabelecimentoBaseContaBancariaInvalidException("A agência é obrigatória");

            if (!int.TryParse(Agencia, out _))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O número da agência deve ser numérico.");

            if (Agencia.Length > 10)
                throw new EstabelecimentoBaseContaBancariaInvalidException("A agência deve conter no máximo 10 caracteres.");

            if (string.IsNullOrEmpty(Conta))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O número da conta é obrigatório.");

            if (!int.TryParse(Conta, out _))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O número da conta deve ser numérico.");

            if (Conta.Length > 30)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O número da conta deve conter no máximo 30 caracteres.");

            if (string.IsNullOrEmpty(DigitoConta))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O dígito verificador da conta é obrigatório.");

            if (DigitoConta.Length > 10)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O dí­gito verificador da conta deve conter no máximo 10 caracteres.");

            if (!System.Enum.IsDefined(typeof(ETipoContaBancaria), TipoConta))
                throw new EstabelecimentoBaseContaBancariaInvalidException("Tipo de conta inválido.");

            if (string.IsNullOrEmpty(CnpjTitular))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O CNPJ do titular é obrigatório.");

            if (CnpjTitular.Length > 14)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O CNPJ do titular deve conter no máximo 14 caracteres.");

            if (string.IsNullOrEmpty(NomeTitular))
                throw new EstabelecimentoBaseContaBancariaInvalidException("O nome do titular é obrigatório.");

            if (NomeTitular.Length > 100)
                throw new EstabelecimentoBaseContaBancariaInvalidException("O nome do titular deve conter no máximo 100 caracteres.");
        }
    }
}

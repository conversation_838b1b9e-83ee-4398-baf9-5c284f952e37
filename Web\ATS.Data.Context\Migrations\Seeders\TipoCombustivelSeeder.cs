﻿using System.Data.Entity.Migrations;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class TipoCombustivelSeeder
    {
        public void Execute(AtsContext context)
        {
            int idTipoCombustivel = 1;

            context.TipoCombustivel.AddOrUpdate(new[]
            {
                new TipoCombustivel {IdTipoCombustivel = idTipoCombustivel++, Descricao = "Gasolina"},
                new TipoCombustivel {IdTipoCombustivel = idTipoCombustivel++, Descricao = "Diesel"},
                new TipoCombustivel {IdTipoCombustivel = idTipoCombustivel++, Descricao = "Etanol"},
                new TipoCombustivel {IdTipoCombustivel = idTipoCombustivel++, Descricao = "Diesel S10"},
                new TipoCombustivel {IdTipoCombustivel = idTipoCombustivel++, Descricao = "Arla"},
                new TipoCombustivel {IdTipoCombustivel = idTipoCombustivel++, Descricao = "GNV"}
            });

            context.SaveChanges();
        }
    }
}
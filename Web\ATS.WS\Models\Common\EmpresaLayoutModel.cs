﻿using Newtonsoft.Json;

namespace ATS.WS.Models.Common
{
    public class EmpresaLayoutModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Logo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int IdEmpresa { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorFundo { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorTexto { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorGuia { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string CorGuiaTexto { get; set; }
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ProtocoloEventoMap : EntityTypeConfiguration<ProtocoloEvento>
    {
        public ProtocoloEventoMap()
        {
            ToTable("PROTOCOLO_EVENTO");

            HasKey(t => t.IdProtocoloEvento);

            Property(x => x.NumeroRecibo).IsOptional();

            HasRequired(t => t.Protocolo)
                .WithMany(t => t.ProtocoloEventos)
                .HasForeignKey(t => t.IdProtocolo);

            HasRequired(t => t.ViagemEvento)
                .WithMany(t => t.ProtocoloEventos)
                .HasForeignKey(t => t.IdViagemEvento);

            HasOptional(t => t.Motivo)
                .WithMany(t => t.ProtocoloEventos)
                .HasForeignKey(t => t.IdMotivo);

            HasOptional(t => t.MotivoOcorrencia)
                .WithMany(t => t.ProtocoloEventosOcorrencias)
                .HasForeignKey(t => t.IdMotivoOcorrencia);

            Property(x => x.PesoChegada)
                .HasPrecision(10, 3);

            Property(x => x.EventoReincidente)
                .IsRequired();

         /*   Property(x => x.IdUsuarioCadOcorrencia)
                .IsOptional();*/

            Property(x => x.IdUsuarioBaixaOcorrencia)
                .IsOptional();

            Property(x => x.DataBaixaOcorrencia)
                .IsOptional();

            HasOptional(x => x.MotivoDesconto)
                .WithMany(x => x.ProtocoloEventosDescontados)
                .HasForeignKey(x => x.IdMotivoDesconto);

            HasOptional(x => x.ProtocoloEvento_Vinculado)
                .WithMany(x => x.ProtocolosEventos_Vinculados)
                .HasForeignKey(x => x.IdProtocoloEvento_Vinculado);

            Property(o => o.AnaliseAbono)
                .IsOptional();

            Property(o => o.DataHoraAnaliseAbono)
                .IsOptional();

            HasOptional(o => o.UsuarioAnaliseAbono)
                .WithMany(o => o.UsuariosAnaliseAbono)
                .HasForeignKey(o => o.IdUsuarioAnaliseAbono);
            
            HasOptional(o => o.UsuarioOcorrencia)
                .WithMany(o => o.UsuariosOcorrencia)
                .HasForeignKey(o => o.IdUsuarioCadOcorrencia);
        }
    }
}
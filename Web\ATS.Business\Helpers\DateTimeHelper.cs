﻿using ATS.Domain.Enum;
using System;
using System.Globalization;

namespace ATS.Domain.Helpers
{
    public class DateTimeHelper
    {
        #region Converter

        /// <summary>
        /// Converte uma string para um DateTime válido. Aceita os seguintes máscaras:
        /// hmmss | hhmmss | hh:mm:ss
        /// </summary>
        /// <param name="aHora">Hora configurada em uma das máscaras válidas.</param>
        /// <param name="aHoraResult">Hora convertida a partir da string.</param>
        /// <param name="aFormato12H">Especifica se a conversão deve utilizar o formato 12H (AM/PM).</param>
        /// <returns>Indica se foi realizada a conversão de forma correta.</returns>
        public bool ConverterHoraToDateTime(string aHora, out DateTime? aHoraResult, bool aFormato12H = false)
        {
            var lsHH = "00";
            var lsMM = "00";
            var lsSS = "00";

            aHoraResult = null;

            var lbPM = aHora.ToLower().Trim().IndexOf("pm", StringComparison.Ordinal) > 0;
            var lbAM = aHora.ToLower().Trim().IndexOf("am", StringComparison.Ordinal) > 0;

            if (lbPM) aHora = aHora.ToLower().Replace("pm", "").Trim();
            if (lbAM) aHora = aHora.ToLower().Replace("am", "").Trim();

            string[] lsPartes;
            if (aHora.IndexOf(':') > 0)
            {
                // Formato mínimo: h:mm:ss
                lsPartes = aHora.Split(new[] { ':' }, StringSplitOptions.RemoveEmptyEntries);
                lsHH = lsPartes[0].Trim();
                lsMM = lsPartes[1].Trim();
                lsSS = lsPartes.Length > 2 ? lsPartes[2].Trim() : "0";
            }
            else if (aHora.Length == 1 || aHora.Length == 2)
            {
                // Formato mínimo: m | Ex: 00:05 -> valor no banco 5
                lsHH = "0";
                lsMM = aHora;
            }
            else if (aHora.Length == 3 || aHora.Length == 4)
            {
                // Formato mínimo: hmm
                lsHH = aHora.Substring(0, aHora.Length - 2);
                lsMM = aHora.Substring(aHora.Length - 2);
            }
            else if (aHora.Length == 5 || aHora.Length == 6)
            {
                // Formato mínimo: hmmss
                lsHH = aHora.Substring(0, aHora.Length - 4);
                lsMM = aHora.Substring(aHora.Length - 4, 2);
                lsSS = aHora.Substring(aHora.Length - 2);
            }
            else
            {
                // Formato inválido.
                return false;
            }

            if (aFormato12H)
            {
                if (Convert.ToInt32(lsHH) > 11)
                    lbPM = true;
                else
                    lbAM = true;
            }

            string lHora = string.Empty;
            if (lbPM || lbAM)
                lHora = $"{lsHH.Trim()}:{lsMM.Trim()}:{lsSS.Trim()} {(lbPM ? "PM" : "AM")}";
            else
                lHora = $"{lsHH.Trim()}:{lsMM.Trim()}:{lsSS.Trim()}";

            try
            {
                aHoraResult = Convert.ToDateTime(lHora, new CultureInfo("en-US"));
            }
            catch
            {
                aHoraResult = null;
            }

            return (aHoraResult != null);
        }

        /// <summary>
        /// Converte uma string para um DateTime válido. Aceita os seguintes máscaras:
        /// hmmss | hhmmss | hh:mm:ss
        /// </summary>
        /// <param name="aHora">Hora configurada em uma das máscaras válidas.</param>
        /// <param name="aFormato12H">Especifica se a conversão deve utilizar o formato 12H (AM/PM).</param>
        /// <returns>Retorna a Hora convertida para o tipo DateTime.</returns>
        public DateTime? ConverterHoraToDateTime(string aHora, bool aFormato12H = false)
        {
            DateTime? ldHoraResult = null;
            if (!ConverterHoraToDateTime(aHora, out ldHoraResult, aFormato12H))
                ldHoraResult = DateTime.Now;

            return ldHoraResult;
        }

        /// <summary>
        /// Converter a data para o formato short (Utilizado pelo SBO para armazenar na base)
        /// </summary>
        /// <param name="aHora">Hora que se deseja converter.</param>
        /// <param name="aHoraResult">Hora no formato short.</param>
        /// <returns>Indica se a conversão foi realizada com sucesso.</returns>
        public bool ConverterHoraToShort(DateTime aHora, out short? aHoraResult)
        {
            DateTime hora = Convert.ToDateTime(aHora, new CultureInfo("en-US"));

            string sHora = Convert.ToString(hora.Hour);
            sHora = sHora.Length < 2 ? "0" + sHora : sHora;

            string sMin = Convert.ToString(hora.Minute);
            sMin = sMin.Length < 2 ? "0" + sMin : sMin;

            try
            {
                aHoraResult = Convert.ToInt16(sHora + sMin);
            }
            catch
            {
                aHoraResult = null;
            }

            return aHoraResult != null;
        }

        /// <summary>
        /// Converter a data para o formato short (Utilizado pelo B1 para armazenar em base)
        /// </summary>
        /// <param name="aHora">Hora que se deseja converter.</param>
        /// <returns>Retorna a hora no tipo de dado short.</returns>
        public short? ConverterHoraToShort(DateTime aHora)
        {
            short? aHoraResult;
            ConverterHoraToShort(aHora, out aHoraResult);

            return aHoraResult;
        }

        /// <summary>
        /// Converter a hora do formato short para string
        /// </summary>
        /// <param name="aHora">Hora no formato short (B1)</param>
        /// <param name="aHoraResult">Hora no formato string</param>
        /// <param name="aFormato12H">Especifica se a conversão deve utilizar o formato 12H (AM/PM).</param>
        /// <param name="aSomenteNumeros">Especifica se haverá ou não formatação.</param>
        /// <returns>Indica se foi realizada a conversão de forma correta.</returns>
        public bool ConverterHoraShortToString(short aHora, out string aHoraResult, bool aFormato12H = false, bool aSomenteNumeros = false)
        {
            var lsHora = Convert.ToString(aHora);
            var lFormato12H = aSomenteNumeros ? "hhmmtt" : "hh:mmtt";
            var lFormato24H = aSomenteNumeros ? "HHmm" : "HH:mm";

            // Se a hora for menor que 10:00, adiciona o "0" na frante para ser possivel a conversão
            if (aHora < 1000)
                lsHora = Convert.ToString(aHora).PadLeft(4, '0');

            var ldHora = TimeSpan.ParseExact(lsHora, "hhmm", new CultureInfo("en-US"));
            var ldData = new DateTime(1900, 1, 1, ldHora.Hours, ldHora.Minutes, ldHora.Seconds);
            aHoraResult = ldData.ToString(aFormato12H ? lFormato12H : lFormato24H, new CultureInfo("en-US"));

            return true;
        }

        /// <summary>
        /// Converter a hora do formato short para DateTime
        /// </summary>
        /// <param name="aHora">Hora no formato short (B1)</param>
        /// <param name="aHoraResult">Hora no formato string</param>
        /// <returns>Indica se foi realizada a conversão de forma correta.</returns>
        public bool ConverterHoraShortToDateTime(short aHora, out DateTime aHoraResult)
        {
            var lsHora = Convert.ToString(aHora);

            // Se a hora for menor que 10:00, adiciona o "0" na frante para ser possivel a conversão
            if (aHora < 1000)
                lsHora = Convert.ToString(aHora).PadLeft(4, '0');

            var ldHora = TimeSpan.ParseExact(lsHora, "hhmm", new CultureInfo("en-US"));
            aHoraResult = new DateTime(1900, 1, 1, ldHora.Hours, ldHora.Minutes, ldHora.Seconds);

            return true;
        }

        /// <summary>
        /// Converter a hora do formato short para string
        /// </summary>
        /// <param name="aHora">Hora no formato short (B1)</param>
        /// <param name="aFormato12H">Especifica se a conversão deve utilizar o formato 12H (AM/PM).</param>
        /// <returns>Retorna o valor da hora em short no formato de string devidamente formatada.</returns>
        public string ConverterHoraShortToString(short aHora, bool aFormato12H = false)
        {
            string lsHoraResult;

            ConverterHoraShortToString(aHora, out lsHoraResult, aFormato12H);

            return lsHoraResult;
        }

        /// <summary>
        /// Converter hora em formato decimal para formato TimeSpan
        /// </summary>
        /// <param name="aHora">Hora no formato decimal.</param>
        /// <returns>TimeSpan representando o tempo</returns>
        public TimeSpan ConverterHoraDecToTimeSpan(double aHora)
        {
            int lDd = 0;
            int lHr = 0;
            int lMn = 0;

            var lPartes = aHora.ToString("F", CultureInfo.InvariantCulture).Split('.');
            if (lPartes.Length > 1)
            {
                lHr = Convert.ToInt32(lPartes[0].PadLeft(2, '0'));
                lMn = Convert.ToInt32(lPartes[1].PadRight(2, '0'));
            }
            else if (lPartes.Length == 1)
            {
                lMn = Convert.ToInt32(lPartes[0].PadRight(2, '0'));
            }

            while (lHr > 24)
            {
                lDd++;
                lHr -= 24;
            }

            return new TimeSpan(lDd, lHr, lMn, 0, 0);
        }

        #endregion

        #region Formatar

        /// <summary>
        /// Formatar a data no Formato 24H.
        /// </summary>
        /// <param name="aHora">Hora.</param>
        /// <param name="aSegundos">Exibir os segundos.</param>
        /// <returns>Retorna uma string com a hora convertida, caso ocorra algum erro, retorna vazio.</returns>
        public string Formatar24H(string aHora, bool aSegundos = false)
        {
            string lHora = string.Empty;

            if (!Formatar24H(aHora, out lHora, aSegundos))
                return string.Empty;

            return lHora;
        }

        /// <summary>
        /// Formatar a data no Formato 24H.
        /// </summary>
        /// <param name="aHora">Hora.</param>
        /// <param name="aHoraFormata">Resultado da função, exibindo a data devidamente formatada.</param>
        /// <param name="aSegundos">Exibir os segundos.</param>
        /// <returns>Indica se a função foi realizada com sucesso.</returns>
        public bool Formatar24H(string aHora, out string aHoraFormata, bool aSegundos = false)
        {
            aHoraFormata = null;

            DateTime? lHora;
            if (!ConverterHoraToDateTime(aHora, out lHora))
                return false;

            var lsMascara = aSegundos ? "HH:mm:ss" : "HH:mm";
            aHoraFormata = lHora.Value.ToString(lsMascara, new CultureInfo("en-US"));

            return true;
        }

        /// <summary>
        /// Formatar a hora no Formato 12H.
        /// </summary>
        /// <param name="aHora">Hora.</param>
        /// <param name="aSegundos">Exibir os segundos.</param>
        /// <returns>Retorna uma string com a hora convertida, caso ocorra algum erro, retorna vazio.</returns>
        public string Formatar12H(string aHora, bool aSegundos = false)
        {
            string lHora = string.Empty;

            if (!Formatar12H(aHora, out lHora, aSegundos))
                return string.Empty;

            return lHora;
        }

        /// <summary>
        /// Formatar a data no Formato 12H (AM/PM)
        /// </summary>
        /// <param name="aHora">Hora.</param>
        /// <param name="aHoraFormata">Resultado da função, exibindo a data devidamente formatada.</param>
        /// <param name="aSegundos">Exibir os segundos.</param>
        /// <returns>Indica se a função foi realizada com sucesso.</returns>
        public bool Formatar12H(string aHora, out string aHoraFormata, bool aSegundos = false)
        {
            aHoraFormata = null;

            DateTime? lHora = null;
            if (!ConverterHoraToDateTime(aHora, out lHora))
                return false;

            var lsMascara = aSegundos ? "h:mm:sstt" : "h:mmtt";
            aHoraFormata = lHora.Value.ToString(lsMascara, new CultureInfo("en-US"));

            return true;
        }

        /// <summary>
        /// Converter a hora em formato decimal (hora, minutos) para formato normal.
        /// </summary>
        /// <param name="aHora">Hora em formato decimal, onde a parte inteira representa a hora cheia e a parte decimal os minutos</param>
        /// <returns>Hora formatada</returns>
        public string FormatarHoraDecimal(double aHora)
        {
            var lHora = aHora.ToString("0.00", CultureInfo.InvariantCulture);
            var lPart = lHora.Split('.');

            return $"{int.Parse(lPart[0]):00}:{int.Parse(lPart[1]):00}";
        }

        #endregion

        public DateTime FirstDayOfWeek(DateTime date)
        {
            if (date == null || date == default(DateTime))
                throw new Exception("DateTime informed isn't valid!");
            

            return date.AddDays(-(((int)date.DayOfWeek - 0) - (int)Math.Floor((double)((int)date.DayOfWeek - 0) / 7) * 7));
        }
        
        public DateTime LastDayOfWeek(DateTime date)
        {
            var diff = 6 - (int)date.DayOfWeek;

            diff = diff == 6 ? 0 : diff;

            var eow = date.AddDays(diff).Date;

            return new DateTime(eow.Year, eow.Month, eow.Day, 23, 59, 59, 999);
        }
        
        
        public DateTime FirstDayOfMonth(DateTime date)
        {

            if (date == null || date == default(DateTime))
                throw new Exception("DateTime informed isn't valid!");

            return new DateTime(date.Year, date.Month, 1);
        }

        public DateTime LastDayOfMonth(DateTime date)
        {
            if (date == null || date == default(DateTime))
                throw new Exception("DateTime informed isn't valid!");

            return FirstDayOfMonth(date).AddMonths(1).AddDays(-1);
        }

        /// <summary>
        /// Retorna DateTime com data formatada para o fim do dia
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public DateTime EndOfDay(DateTime aValue)
        {
            return new DateTime(aValue.Year, aValue.Month, aValue.Day, 23, 59, 59, 999);
        }

        /// <summary>
        /// Retorna DateTime com data formatada para o início do dia
        /// </summary>
        /// <param name="aValue"></param>
        /// <returns></returns>
        public DateTime StartOfDay(DateTime aValue)
        {
            return new DateTime(aValue.Year, aValue.Month, aValue.Day, 00, 00, 00, 001);
        }

        /// <summary>
        /// Convert o valor DateTime para o formato de filtros do SQL Server.
        /// </summary>
        /// <param name="value">Data e Hora</param>
        /// <param name="onlyDate">Somente data ou data com a hora</param>
        /// <returns></returns>
        public string ConvertToSQL(DateTime value, bool onlyDate = false)
        {
            return onlyDate
                ? value.ToString("dd-MM-yyyy")
                : value.ToString("dd-MM-yyyy HH:mm:ss");
        }

        /// <summary>
        /// Convert o valor DateTime para o formato de filtros do SQL Server.
        /// </summary>
        /// <param name="value">Data e Hora</param>
        /// <returns></returns>
        public string ToSqlServer(DateTime value)
        {
            return value.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// Converte a data e hora especificada para o formato Unix
        /// </summary>
        /// <param name="value">Data e Hora</param>
        /// <returns></returns>
        public double ConvertToUnix(DateTime value)
        {
            return value.ToUniversalTime().Subtract(new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds;
        }

    }
}
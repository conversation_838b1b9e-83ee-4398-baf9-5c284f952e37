﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ProtocoloAnexoMap : EntityTypeConfiguration<ProtocoloAnexo>
    {
        public ProtocoloAnexoMap()
        {
            ToTable("PROTOCOLO_ANEXO");

            Has<PERSON>ey(t => t.IdProtocoloAnexo);

            HasRequired(t => t.Protocolo)
                .WithMany(t => t.ProtocoloAnexos)
                .HasForeignKey(t => t.IdProtocolo);

            HasRequired(t => t.Documento)
                .WithMany(t => t.ProtocoloAnexos)
                .HasForeignKey(t => t.IdDocumento);
        }
    }
}
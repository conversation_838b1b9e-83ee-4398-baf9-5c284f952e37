﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Mobile;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Application.Interface;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service;
using NLog;

namespace ATS.WS.Services
{
    public class SrvCheckIn : SrvBase
    {
        private readonly IUsuarioApp _usuarioApp;
        private readonly ICheckInApp _checkInApp;
        private readonly ICheckinResumoApp _checkinResumoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IVeiculoService _veiculoService;
        private readonly WebServiceHelperWs _webServiceHelperWs;

        public SrvCheckIn(IUsuarioApp usuarioApp, ICheckInApp checkInApp, ICheckinResumoApp checkinResumoApp, IEmpresaApp empresaApp, IMotoristaApp motoristaApp,
            IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IVeiculoService veiculoService, WebServiceHelperWs webServiceHelperWs)
        {
            _usuarioApp = usuarioApp;
            _checkInApp = checkInApp;
            _checkinResumoApp = checkinResumoApp;
            _empresaApp = empresaApp;
            _motoristaApp = motoristaApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _veiculoService = veiculoService;
            _webServiceHelperWs = webServiceHelperWs;
        }

        /// <summary>
        /// Integrar o CheckIn
        /// </summary>
        /// <param name="params">CheckIn</param>
        /// <returns></returns>
        public Retorno<int?> Integrar(IntegrarCheckInRequestModel @params)
        {
            try
            {
                var idUsuario = _usuarioApp.GetIdPorCNPJCPF(@params.CPFCNPJUsuario);
                var checkIn = Mapper.Map<IntegrarCheckInRequestModel, CheckIn>(@params);

                if (idUsuario.HasValue)
                    checkIn.IdUsuario = idUsuario.Value;

                var idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
                
                if (idEmpresa == null)
                    idEmpresa = _empresaApp.GetIdPorCnpj(@params.CNPJAplicacao);

                if (idEmpresa != null)
                    checkIn.IdEmpresa = idEmpresa;

                var motoristaUsu = _motoristaApp.GetIdPorCpf(@params.CPFCNPJUsuario);

                if (motoristaUsu.HasValue)
                    checkIn.IdMotorista = motoristaUsu.Value;

                var validationResult = _checkInApp.Add(checkIn);
                if (!validationResult.IsValid)
                    throw new Exception(validationResult.ToFormatedMessage());

                _checkinResumoApp.SalvarOuEditarResumo(checkIn);

                return new Retorno<int?>(true, checkIn.IdCheckIn);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Integrar)} >> {e.Message}");
            }
        }

        public List<CheckInModel> Consultar(string Token, string CNPJAplicacao, string CNPJEmpresa, DateTime? DataInicial, DateTime? DataFinal, string Placa, string Cpf)
        {
            if (DataInicial == null)
                throw new Exception("Data inicial é um filtro obrigatório para esta consulta!");
            if (DataFinal == null)
                throw new Exception("Data final é um filtro obrigatório para esta consulta!");

            if (DataInicial > DataFinal)
                throw new Exception("Data inicial não pode checser maior que a data final!");

            var timeSpam = DataFinal.Value - DataInicial.Value;
            if (timeSpam.TotalDays > 90)
                throw new Exception("Intervalo informadonão deve ser maior que 90 dias");

            var autorizacaoEmpresa = _autenticacaoAplicacaoApp
                 .GetAutenticacaoAplicacaoPorCnpjAplicacao(CNPJAplicacao, Token);

            var idEmpresa = _empresaApp.GetIdPorCnpj(CNPJEmpresa);
            if (!idEmpresa.HasValue)
                throw new Exception("Não foi possível identificar a empresa. ");

            if (!autorizacaoEmpresa.Any(x => x.IdEmpresa == idEmpresa))
                throw new Exception("Empresa não autorizada para realizar esta consulta. ");

            List<CheckinConsultaModel> checkins;
            if (!string.IsNullOrEmpty(Placa))
            {
                checkins = _checkInApp.GetByPlaca(idEmpresa.Value, DataInicial.Value, DataFinal.Value, Placa).ToList();
                if (!checkins.Any())
                {
                    var motorista = _veiculoService.GetVeiculosPorPlaca(new List<string>() {Placa}).Select(x => x.IdMotorista).FirstOrDefault();
                    checkins = motorista == null
                        ? new List<CheckinConsultaModel>()
                        : _checkInApp.GetByPlacaMotorista(idEmpresa.Value, DataInicial.Value, DataFinal.Value,
                            motorista??0).ToList();
                }
            }else checkins = _checkInApp.GetByCpf(idEmpresa.Value, DataInicial.Value, DataFinal.Value, Cpf).ToList();

            var checkinsModel = checkins.Select(x => new CheckInModel()
            {
                CNPJEmpresa = x.CNPJEmpresa,
                RazaoSocial = x.RazaoSocial,
                CPFMotorista = x.CPFMotorista,
                NomeMotorista = x.NomeMotorista,
                IBGECidade = x.IBGECidade,
                DescricaoCidade = x.DescricaoCidade,
                SiglaUf = x.SiglaUf,
                CPFUsuario = x.CPFUsuario,
                NomeUsuario = x.NomeUsuario,
                DataHora = x.DataHora,
                IdCheckIn = x.IdCheckIn,
                TipoEvento = x.TipoEvento,
                IdMotorista = x.IdMotorista,
                IdViagem = x.IdViagem,
                Latitude = x.Latitude,
                Longitude = x.Longitude,
                Placa = x.Placa
            });


            return checkinsModel.ToList();
        }

        public List<CheckInMobileModel> ConsultarPorEmpresa(string Token, string CNPJAplicacao, string CNPJEmpresa)
        {

            var autorizacaoEmpresa = _autenticacaoAplicacaoApp
                 .GetAutenticacaoAplicacaoPorCnpjAplicacao(CNPJAplicacao, Token);

            var idEmpresa = _empresaApp.GetIdPorCnpj(CNPJEmpresa);
            if (!idEmpresa.HasValue)
                throw new Exception("Não foi possível identificar a empresa. ");

            var keyGoogle = "";// _empresaApp.GetKeyGoogle(idEmpresa);
            if (!autorizacaoEmpresa.Any(x => x.IdEmpresa == idEmpresa))
                throw new Exception("Empresa não autorizada para realizar esta consulta. ");

            var lMotoristas = new List<Motorista>(); //_motoristaApp.GetAllByIdEmpresaComCheckIn(idEmpresa.Value);
            var lCheckIns = new List<CheckInMobileModel>();

            foreach (var item in lMotoristas.ToList())
            {
                var lFoto = _usuarioApp.GetFoto(_usuarioApp.GetIdPorCNPJCPF(item.CPF) ?? 0);
                var lFotoBase64 = string.Empty;
                if (lFoto != null)
                    lFotoBase64 = Convert.ToBase64String(lFoto);

                var lCheckInAtual = item.CheckinResumos.OrderByDescending(y => y.DataHora).FirstOrDefault();
                
                if(lCheckInAtual == null)
                    continue;
                
                var lLatitude = lCheckInAtual.Latitude;
                var lLongitude = lCheckInAtual.Longitude;
                var lEndereco = _webServiceHelperWs.GetAdressFromCoordinate(lLatitude.ToString(), lLongitude.ToString(), keyGoogle);
                lCheckIns.Add(new CheckInMobileModel()
                {
                    CPFMotorista = item.CPF,
                    NomeMotorista = item.Nome,
                    IdMotorista = item.IdMotorista,
                    Latitude = lLatitude,
                    Longitude = lLongitude,
                    DataHora = lCheckInAtual.DataHora,
                    Foto = lFotoBase64,
                    Endereco = lEndereco
                });
            }

            return lCheckIns;
        }

    }
}
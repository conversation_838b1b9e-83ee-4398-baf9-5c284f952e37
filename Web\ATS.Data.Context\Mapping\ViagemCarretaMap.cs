﻿using System.ComponentModel.DataAnnotations.Schema;
using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemCarretaMap : EntityTypeConfiguration<ViagemCarreta>
    {
        public ViagemCarretaMap()
        {
            ToTable("VIAGEM_CARRETA");

            HasKey(x => new { x.IdViagemCarreta, IdEmpresa = x.IdEmpresa });

            Property(t => t.IdViagemCarreta)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(x => x.IdViagem).IsRequired();

            HasRequired(x => x.Viagem)
                .WithMany(x => x.ViagemCarretas)
                .HasForeignKey(x => new { x.IdViagem, IdEmpresa = x.IdEmpresa });

            Property(x => x.IdEmpresa).IsOptional();

            Property(x => x.Placa)
                .HasMaxLength(10);

            Property(o => o.Rntrc)
                .HasMaxLength(9)
                .IsOptional();
        }
    }
}

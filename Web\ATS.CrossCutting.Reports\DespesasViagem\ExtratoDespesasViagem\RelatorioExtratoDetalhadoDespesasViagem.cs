﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem
{
    public class RelatorioExtratoDetalhadoDespesasViagem
    {
        public byte[] GetReport(List<RelatorioExtratoDetalhadoDespesasViagemDataType> listaDados, string tipoArquivo, string logo)
        {
            var parametros = new Tuple<string, string, bool>[1];
            parametros[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var dataSources = new Tuple<object, string>(listaDados, "DtsExtratoDetalhadoDespesasViagem");

            var bytes = new Base.Reports().GetReport(new List<Tuple<object, string>> { dataSources }, parametros, true,
                "ATS.CrossCutting.Reports.DespesasViagem.ExtratoDespesasViagem.RelatorioExtratoDetalhadoDespesasViagem.rdlc", tipoArquivo);

            return bytes;
        }
    }
}

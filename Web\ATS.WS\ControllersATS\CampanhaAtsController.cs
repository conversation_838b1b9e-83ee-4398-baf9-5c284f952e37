﻿using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using System;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.DTO.Campanha;

namespace ATS.WS.ControllersATS
{
    public class CampanhaAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ICampanhaApp _campanhaApp;

        public CampanhaAtsController(ICampanhaApp campanhaApp, IUserIdentity userIdentity)
        {
            _userIdentity = userIdentity;
            _campanhaApp = campanhaApp;
        }

        /// <summary>
        /// Consulta a campanha atual não respondida para o usuário
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Autorizar(EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarAtual()
        {
            try
            {
                var resultado = _campanhaApp.ConsultarAtual();
                return ResponderSucesso("Campanha em andamento encontrada.", resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar(EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCampanhas()
        {
            try
            {
                var resultado = _campanhaApp.ConsultarCampanhas();
                return ResponderSucesso("Campanhas encontradas.", resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Responder(ResponderCampanhaRequest request)
        {
            try
            {
                _campanhaApp.Responder(request);
                return ResponderSucesso("Agradecemos sua resposta!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }


        [HttpPost]
        [Autorizar( EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult Integrar(IntegrarCampanhaRequest request)
        {
            try
            {
                var resultado = _campanhaApp.Integrar(request);
                return ResponderSucesso(resultado);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
        
        [HttpPost]
        [Autorizar( EPerfil.Administrador)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(AlterarStatusCampanhaRequest request)
        {
            try
            {
                var resultado = _campanhaApp.AlterarStatus(request);
                return ResponderSucesso(resultado.Mensagem);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
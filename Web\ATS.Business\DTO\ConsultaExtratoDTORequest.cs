﻿using System;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class ConsultaExtratoDTORequest : FiltrosGridBaseModel
    {
        public string Tipo { get; set; }
        public int Produto { get; set; }
        public int Identificador { get; set; }
        public bool SomenteTransferencia { get; set; }
        public DateTime? DataInicio {get;set;}
        public DateTime? DataFim {get;set;}
    }

    public class ConsultaExtratoV2DTORequest : FiltrosGridBaseModel
    {
        public int Produto { get; set; }
        public int Identificador { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
    }
}
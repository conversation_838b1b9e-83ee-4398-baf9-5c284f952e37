﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Common
{
    public class ViagemEventoModel
    {
        /// <summary>
        /// Código do evento da viagem
        /// </summary>
        public int IdViagemEvento { get; set; }

        /// <summary>
        /// Código da viagem
        /// </summary>
        public int IdViagem { get; set; }

        /// <summary>
        /// Código da empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código do protocolo
        /// </summary>
        public int? IdProtocolo { get; set; }

        /// <summary>
        /// Código do estabelecimento base
        /// </summary>
        public int? IdEstabelecimentoBase { get; set; }

        /// <summary>
        /// Tipo de evento da viagem
        /// </summary>
        public ETipoEventoViagem TipoEventoViagem { get; set; }

        /// <summary>
        /// Valor do pagamento relacionado ao evento da viagem
        /// </summary>
        public decimal ValorPagamento { get; set; }

        /// <summary>
        /// Valor total do pagamento relacionado ao evento da viagem
        /// </summary>
        public decimal? ValorTotalPagamento { get; set; }

        /// <summary>
        /// Data e hora de pagamento relacionado ao evento da viagem
        /// </summary>
        public DateTime? DataHoraPagamento { get; set; }

        /// <summary>
        /// Indica se a quebra de mercadoria foi abonada.
        /// </summary>
        public bool? QuebraMercadoriaAbonada { get; set; } = false;

        /// <summary>
        /// Data de validade do evento da viagem
        /// </summary>
        public DateTime? DataValidade { get; set; }

        /// <summary>
        /// Número do recibo relacionado ao evento da viagem
        /// </summary>
        public string NumeroRecibo { get; set; }

        [SkipTracking]
        public string Instrucao { get; set; }

        /// <summary>
        /// Status do evento da viagem
        /// </summary>
        public EStatusViagemEvento Status { get; set; }

        public EOrigemIntegracao OrigemPagamento { get; set; }

        /// <summary>
        /// Token da Viagem
        /// </summary>
        public string Token { get; set; }
        public int? IdMotivo { get; set; }
    }
}
﻿using ATS.Data.Repository.External.SistemaInfo.Pedagio.DTO;
using NLog;
using Sistema.Framework.Util.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json;
using RestSharp.Extensions;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Data.Repository.External.SistemaInfo.Pedagio
{
    public class PedagioExternalRepository : IDisposable
    {
        private const string ServicoIndisponivelResultMessage = "Serviço de pedágio indisponível.";
        private const string PrefixoMensagemExcecao = "[Serviço de pedágio] ";

        private readonly Client _pedagioClient;
        private readonly EmpresaClient _empresaPedagioClient;
        private readonly ValePedagioClient _valePedagioClient;

        private string Token { get; set; }
        private string DocumentoUsuarioAudit { get; set; }
        public string NomeUsuarioAudit { get; set; }

        public PedagioExternalRepository(string token, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            _pedagioClient = new Client(HttpContext.Current);
            _pedagioClient.BaseUrl = SistemaInfoConsts.PedagioApiUrl;

            _empresaPedagioClient = new EmpresaClient(HttpContext.Current);
            _empresaPedagioClient.BaseUrl = SistemaInfoConsts.PedagioApiUrl;
            
            _valePedagioClient = new ValePedagioClient(HttpContext.Current);
            _valePedagioClient.BaseUrl = SistemaInfoConsts.PedagioApiUrl;

            Token = token;
            DocumentoUsuarioAudit = documentoUsuarioAudit;
            NomeUsuarioAudit = nomeUsuarioAudit.RemoveAccents().RemoveSpecialCaracter(true);
        }

        public void Dispose()
        {
        }

        public ConsultaCompraPedagioResponse ConsultarCompraPedagio(int pageSize, int pageIndex, IEnumerable<object> orderBy, List<CustomFilter> customFilter, ConsultaCompraPedagioRequest request)
        {
            try
            {
                return _pedagioClient.ConsultarCompras(pageSize, pageIndex, customFilter, orderBy, request, Token,
                    DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar compra de pedágio => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultaCompraPedagioResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultaCompraPedagioResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultaCompraPedagioResponse
                {
                    Status = ConsultaCompraPedagioResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }
        
        public async Task<IntegrarParametroResponse> IntegrarParametros(IntegrarParametroRequest request)
        {
            try
            {
                return await _empresaPedagioClient.IntegrarParametrosAsync(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao Integrar parametros da Empresa => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<IntegrarParametroResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = IntegrarParametroResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new IntegrarParametroResponse
                {
                    Mensagem = ServicoIndisponivelResultMessage,
                    Status = IntegrarParametroResponseStatus.Falha
                };
            }
        }

        public async Task<IntegrarEmpresaResponse> IntegrarEmpresa(IntegrarEmpresaRequest request)
        {
            try
            {
                return await _pedagioClient.EmpresaAsync(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao Integrar Empresa");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<IntegrarEmpresaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = IntegrarEmpresaResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new IntegrarEmpresaResponse()
                {
                    Status = IntegrarEmpresaResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

#pragma warning disable 1998
        public async Task<ConsultarParametroResponse> ConsultarParametrosPedagio()
#pragma warning restore 1998
        {
            try
            {
                return _empresaPedagioClient.ConsultarParametro(Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao Buscar Parâmetros da Empresa");

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarParametroResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }
                
                return new ConsultarParametroResponse
                {
                    Status = ConsultarParametroResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    LoginAcessoMoveMais = ServicoIndisponivelResultMessage,
                    SenhaAcessoMoveMais = string.Empty
                };
            }
        }

#pragma warning disable 1998
        public async Task<ConsultarEmpresaResponse> ConsultarEmpresaPedagio()
#pragma warning restore 1998
        {
            try
            {
                return _empresaPedagioClient.ConsultarEmpresa(Token, DocumentoUsuarioAudit,
                    NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao Buscar Informações da Empresa");

                string mensagem = null;
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarEmpresaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                        mensagem = PrefixoMensagemExcecao + response.Mensagem;
                }
                
                return new ConsultarEmpresaResponse
                {
                    Status = ConsultarEmpresaResponseStatus.Falha,
                    Mensagem = mensagem ?? ServicoIndisponivelResultMessage,
                    CodigoAcessoViaFacil = ServicoIndisponivelResultMessage,
                    LoginAcessoViaFacil = ServicoIndisponivelResultMessage,
                    SenhaAcessoViaFacil = string.Empty
                };
            }
            
        }

        public ConsultaRotaResponseDto CalcularRota(ConsultaRotaRequest request, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                return _pedagioClient.CalcularRota(request, Token, documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao calcular rota => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultaRotaResponseDto>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultaRotaResponseDtoStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultaRotaResponseDto()
                {
                    Status = ConsultaRotaResponseDtoStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

        public SolicitarCompraPedagioResponse SolicitarCompraPedagio(SolicitarCompraPedagioRequest request)
        {
            try
            {
                return _pedagioClient.SolicitarCompra(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao solicitar compra de pedágio => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<SolicitarCompraPedagioResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = SolicitarCompraPedagioResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new SolicitarCompraPedagioResponse
                {
                    Status = SolicitarCompraPedagioResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };

            }
        }

        public CancelarCompraPedagioResponse CancelarCompraPedagio(CancelarCompraPedagioRequest request)
        {
            try
            {
                return _pedagioClient.CancelarCompra(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao cancelar compra de pedágio => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CancelarCompraPedagioResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = CancelarCompraPedagioResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new CancelarCompraPedagioResponse
                {
                    Status = CancelarCompraPedagioResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultaHistoricoRotaResponse ConsultaHistoricoRota(ConsultaHistoricoRotaRequest request)
        {
            try
            {
                return _pedagioClient.ConsultarHistoricoRoterizacao(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao cancelar compra de pedágio => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultaHistoricoRotaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultaHistoricoRotaResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultaHistoricoRotaResponse
                {
                    Status = ConsultaHistoricoRotaResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

        //Esse enum tem o nome indefinido quando exportado pelo nswagStudio
        public GetStatusPedagioResponse ValoresCompraStatusTipoDictionary()
            =>
                new GetStatusPedagioResponse()
                {
                    Values =
                        Enum.GetValues(typeof(Status)).Cast<Status>().ToDictionary(
                            compraStatusTipo => (int)compraStatusTipo,
                            compraStatusTipo => compraStatusTipo.ToString())
                };

        public ObterExtratoCreditoResponse ObterExtratoSemParar(DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                return _pedagioClient.ExtratoSemParar(dataInicio, dataFim, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar extrato no sem parar");

                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ObterExtratoCreditoResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ObterExtratoCreditoResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }

                return new ObterExtratoCreditoResponse
                {
                    Status = ObterExtratoCreditoResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }

        public ConsultarStatusVeiculoSemPararResponse ConsultarStatusVeiculoSemParar(string placa)
        {
            try
            {
                return _pedagioClient.ConsultarStatusVeiculoSemParar(placa, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar o status do veículo no sem parar");

                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarStatusVeiculoSemPararResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultarStatusVeiculoSemPararResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }

                return new ConsultarStatusVeiculoSemPararResponse
                {
                    Status = ConsultarStatusVeiculoSemPararResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }
        
        public TaggyEdenredVeiculoResponse ConsultarStatusVeiculoTaggyEdenred(string placa)
        {
            try
            {
                return _pedagioClient.ConsultarStatusVeiculoTaggyEdenred(placa, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar o status do veículo na Taggy Edenred");

                if (e is SwaggerException swaggerException && !string.IsNullOrWhiteSpace(swaggerException.Response) && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<TaggyEdenredVeiculoResponse>(swaggerException.Response);
                    if (!string.IsNullOrWhiteSpace(response?.Mensagem))
                    {
                        response.Status = TaggyEdenredVeiculoResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new TaggyEdenredVeiculoResponse
                {
                    Status = TaggyEdenredVeiculoResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }
        
        public ConsultaCompraPedagioMoedeiroReciboResponse ConsultarReciboMoedeiro(ConsultaCompraPedagioMoedeiroReciboRequest request)
        {
            try
            {
                return _pedagioClient.ConsultarReciboMoedeiro(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar recibo do moedeiro => " + request.ProtocoloRequisicao);
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultaCompraPedagioMoedeiroReciboResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultaCompraPedagioMoedeiroReciboResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultaCompraPedagioMoedeiroReciboResponse
                {
                    Status = ConsultaCompraPedagioMoedeiroReciboResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }        

        public ComprovanteValePedagioResponse ConsultarDadosComprovante(Fornecedor4 fornecedor, long idViagem)
        {
            try
            {
                return _valePedagioClient.ConsultarDadosComprovante(fornecedor, idViagem, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar dados de impressão do vale pedágio => " + idViagem);
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ComprovanteValePedagioResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ComprovanteValePedagioResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ComprovanteValePedagioResponse
                {
                    Status = ComprovanteValePedagioResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }
        
        public ConsultarNumeroCartaoViagemResponse ConsultarNumeroCartaoViagem(Fornecedor5 fornecedor, string protocoloEnvio)
        {
            try
            {
                return _valePedagioClient.ConsultarNumeroCartaoViagem(fornecedor, protocoloEnvio, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar dados de impressão do vale pedágio => " + protocoloEnvio);
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarNumeroCartaoViagemResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultarNumeroCartaoViagemResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultarNumeroCartaoViagemResponse
                {
                    Status = ConsultarNumeroCartaoViagemResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }
        
        public BaseGridResponse ConsultarGridCache(GridCustoCacheRequest request, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                return _pedagioClient.ConsultarGridCache(request, Token, documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consulta grid custo cache => " + request.ToJson());
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    throw new Exception("Não foi possível consultar os dados de cache!");
                }

                return new BaseGridResponse();
            }
        }
        
        public void DeletarRotaCache(Guid rotaGuid, string documentoUsuarioAudit = null, string nomeUsuarioAudit = null)
        {
            try
            {
                _pedagioClient.DeletarRotaCache(rotaGuid, Token, documentoUsuarioAudit ?? DocumentoUsuarioAudit, nomeUsuarioAudit ?? NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao deletar cache => " + e.Message);
                
                throw new Exception($"Não foi possível deletar o cache informado! Erro: {e.Message}");
            }
        }
        
        public CadastrarEmbarcadorViaFacilSTPResponse CadastrarEmbarcadorViaFacilSTP(EmbarcadorEmpresa request)
        {
            try
            {
                return _pedagioClient.CadastrarEmbarcadorViaFacilSTP(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao cadastrar embarcador na Via Facil STP");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CadastrarEmbarcadorViaFacilSTPResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = CadastrarEmbarcadorViaFacilSTPResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new CadastrarEmbarcadorViaFacilSTPResponse
                {
                    Status = CadastrarEmbarcadorViaFacilSTPResponseStatus.Falha,
                    Mensagem = e.Message
                };
            }
        }
        
        public CadastrarTransportadorViaFacilResponse CadastrarTransportadorRntrcViaFacil(CadastrarTransportadorViaFacilRequest request)
        {
            try
            {
                return _pedagioClient.CadastrarTransportadorRntrcViaFacil(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao cadastrar transportador via facil");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<CadastrarTransportadorViaFacilResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = CadastrarTransportadorViaFacilResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new CadastrarTransportadorViaFacilResponse
                {
                    Status = CadastrarTransportadorViaFacilResponseStatus.Falha,
                    Mensagem = e.Message
                };
            }
        }
        
        public EditarTransportadorViaFacilResponse EditarTransportadorRntrcViaFacil(EditarTransportadorViaFacilRequest request)
        {
            try
            {
                return _pedagioClient.EditarTransportadorRntrcViaFacil(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao editar transportador via facil");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<EditarTransportadorViaFacilResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = EditarTransportadorViaFacilResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new EditarTransportadorViaFacilResponse
                {
                    Status = EditarTransportadorViaFacilResponseStatus.Falha,
                    Mensagem = e.Message
                };
            }
        }
        
        public ConsultaTransportadorViaFacilResponse ConsultarTransportadorRntrcViaFacil(string cnpjEmbarcador)
        {
            try
            {
                return _pedagioClient.ConsultarTransportadorRntrcViaFacil(new ConsultarTransportadorViaFacilRequest()
                {
                    CnpjEmbarcador = cnpjEmbarcador
                }, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao consultar transportador via facil");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultaTransportadorViaFacilResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultaTransportadorViaFacilResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultaTransportadorViaFacilResponse
                {
                    Status = ConsultaTransportadorViaFacilResponseStatus.Falha,
                    Mensagem = e.Message
                };
            }
        }
        
        public DeletarTransportadorViaFacilResponse DeletarTransportadorRntrcViaFacil(DeletarTransportadorViaFacilRequest request)
        {
            try
            {
                return _pedagioClient.DeletarTransportadorRntrcViaFacil(request, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "Erro ao deletar transportador via facil");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<DeletarTransportadorViaFacilResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = DeletarTransportadorViaFacilResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new DeletarTransportadorViaFacilResponse
                {
                    Status = DeletarTransportadorViaFacilResponseStatus.Falha,
                    Mensagem = e.Message
                };
            }
        }
        
        public ConsultarSituacaoTagPlacaResponse ConsultarSituacaoTagPlaca(string placa, Fornecedor2 fornecedor)
        {
            try
            {
                return _pedagioClient.ConsultarSituacaoTagPlaca(placa, fornecedor, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "ConsultarSituacaoTagPlaca");
                
                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarSituacaoTagPlacaResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultarSituacaoTagPlacaResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }
                
                return new ConsultarSituacaoTagPlacaResponse
                {
                    Status = ConsultarSituacaoTagPlacaResponseStatus.Falha,
                    Mensagem = e.Message
                };
            }
        }
        
        public ConsultarPolylineResponse ConsultarPolyline(int codPolyline)
        {
            try
            {
                return _valePedagioClient.ConsultarPolyline(codPolyline, Token, DocumentoUsuarioAudit, NomeUsuarioAudit);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger()
                    .Error(e, "Erro ao consultar polyline");

                if (e is SwaggerException swaggerException && swaggerException.Response != null && swaggerException.Response.Trim().StartsWith("{"))
                {
                    var response = JsonConvert.DeserializeObject<ConsultarPolylineResponse>(swaggerException.Response);
                    if (response?.Mensagem != null)
                    {
                        response.Status = ConsultarPolylineResponseStatus.Falha;
                        response.Mensagem = PrefixoMensagemExcecao + response.Mensagem;
                        return response;
                    }
                }

                return new ConsultarPolylineResponse
                {
                    Status = ConsultarPolylineResponseStatus.Falha,
                    Mensagem = ServicoIndisponivelResultMessage
                };
            }
        }
    }
}

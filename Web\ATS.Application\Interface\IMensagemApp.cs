﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ATS.Application.Interface
{
    public interface IMensagemApp : IAppBase<Mensagem>
    {
        //ValidationResult Add(Mensagem mensagem, List<int> idDestinatarios);
        //ValidationResult SetMensagemLida(int idMensagem, DateTime? dataLeitura, DateTime? dataRecebimento);
        //IQueryable<Mensagem> GetPorDataEnvio(DateTime dataBase);
        //IQueryable<Mensagem> Consultar(int? idRemetente, int? idDestinatario, int? idCarga, DateTime dataInicial, DateTime dataFinal, string assunto);
        //IQueryable<Mensagem> ConsultarPorEmpresa(DateTime dataInicial, DateTime dataFinal, string assunto,int? idEmpresa);
        //IQueryable<Mensagem> ConsultarMensagensEnviadas(int idUsuario, DateTime dataInicial, DateTime dataFinal,string assunto);
        //Mensagem Get(int id);
        //Mensagem GetWithAllChilds(int id);
        //ICollection<Mensagem> GetMensagensNovas(int idUsuarioDestinatario);
        //ValidationResult SetAllMensagensRecebidas(int idUsuarioDestinatario);
        void AtualizarMensagem(int idMensagem, int idUsuarioDestinatario, DateTime? dataHoraLeitura);

        /// <summary>
        /// Retorna todas as mensagens cadastradas a partir da data informada por usuario e filtros
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <param name="dataInicial">Data de ínício de filtro</param>
        /// <param name="dataFinal">Data de término do filtro</param>
        /// <param name="assunto">Assunto</param>
        /// <returns></returns>
        IQueryable<Mensagem> ConsultarRecebidos(int idUsuario, DateTime dataInicial, DateTime dataFinal, string assunto);

        ICollection<Mensagem> GetMensagensPeloUsuario(int idUsuarioDestinatario, DateTime? dataFiltro);
        ValidationResult SendBroadcast();
    }
}
﻿using ATS.Application.Interface.Common;
using ATS.Domain.DTO.Banner;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IBannerApp : IAppBase
    {
        BannerConsultarResponse ConsultarAtual();
        BannerConsultarResponse ConsultarPorId(int idBanner);
        BannerGridResponse ConsultarBanners();
        ValidationResult Visualizar(BannerVisualizarRequest request);
        ValidationResult Integrar(BannerIntegrarRequest request);
        BannerAlterarStatusResponse AlterarStatus(BannerAlterarStatusRequest request);
    }
}
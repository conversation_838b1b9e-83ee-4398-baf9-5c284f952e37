﻿using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ATS.Data.Repository.External.Extratta.Abastecimento.Client.Interfaces;
using ATS.Data.Repository.External.Extratta.Abastecimento.Client.Models;
using ATS.Data.Repository.External.Extratta.Models;
using Newtonsoft.Json;

namespace ATS.Data.Repository.External.Extratta.Abastecimento.Client
{
    public class AbastecimentoApiClient : IAbastecimentoApiClient
    {
        private string BaseUrl { get; }

        private readonly HttpClient Client;

        public AbastecimentoApiClient()
        {
            Client = new HttpClient();
            BaseUrl = ConfigurationManager.AppSettings["ABASTECIMENTO_URL"];
        }

        public IntegracaoResult<InserirCreditoTicketLogResponse> InserirTicket(InserirCreditoTicketLogRequest request)
        {
            return Task.Run(async () => await InserirAsync(request, CancellationToken.None)).GetAwaiter().GetResult();
        }

        public IntegracaoResult CancelarTicket(int abastecimentoId, CancelarCreditoTicketLogRequest request)
        {
            return Task.Run(async () => await CancelarAsync(abastecimentoId, request, CancellationToken.None)).GetAwaiter().GetResult();
        }

        #region private

        private async Task<IntegracaoResult<InserirCreditoTicketLogResponse>> InserirAsync(InserirCreditoTicketLogRequest request, CancellationToken cancellationToken)
        {
            var body = JsonConvert.SerializeObject(request);

            var url = BaseUrl + $"/ticketlog/credito";
            var result = await PostAsync(url, string.Empty, string.Empty, body, cancellationToken);

            if (!result.Success)
            {
                return IntegracaoResult<InserirCreditoTicketLogResponse>.Error(result.Messages);
            }

            if (string.IsNullOrWhiteSpace(result.Value))
                return IntegracaoResult<InserirCreditoTicketLogResponse>.Error("O objeto retornou vazio!");

            var content = JsonConvert.DeserializeObject<InserirCreditoTicketLogResponse>(result.Value);

            if (content == null)
                return IntegracaoResult<InserirCreditoTicketLogResponse>.Error("O objeto retornou vazio!");

            return IntegracaoResult<InserirCreditoTicketLogResponse>.Valid(content);
        }

        private async Task<IntegracaoResult> CancelarAsync(int abastecimentoId, CancelarCreditoTicketLogRequest request, CancellationToken cancellationToken)
        {
            var url = BaseUrl + $"/ticketlog/credito/{abastecimentoId}";
            var body = JsonConvert.SerializeObject(request);

            var result = await DeleteAsync(url, string.Empty, string.Empty, body, cancellationToken);

            return !result.Success ? IntegracaoResult.Error(result.Messages) : IntegracaoResult.Valid();
        }

        #endregion

        #region Auxiliar

        private async Task<IntegracaoResult<string>> GetAsync(string url, string tokenType, string token, CancellationToken cancellationToken = default)
        {
            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            var httpResponse = await Client.GetAsync(url, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private async Task<IntegracaoResult<string>> DeleteAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            var content = new StringContent(body, Encoding.UTF8, "application/json");

            var request = new HttpRequestMessage(HttpMethod.Delete, url) {Content = content};

            var httpResponse = await Client.SendAsync(request, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private async Task<IntegracaoResult<string>> PostAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            var httpResponse = await Client.PostAsync(url, content, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        private async Task<IntegracaoResult<string>> PutAsync(string url, string tokenType, string token, string body, CancellationToken cancellationToken = default)
        {
            var content = new StringContent(body, Encoding.UTF8, "application/json");

            if (!string.IsNullOrEmpty(tokenType) && !string.IsNullOrEmpty(token))
                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, token);

            var httpResponse = await Client.PutAsync(url, content, cancellationToken);

            var result = await httpResponse.Content.ReadAsStringAsync();

            if (!httpResponse.IsSuccessStatusCode)
            {
                return IntegracaoResult<string>.Error(result);
            }

            return IntegracaoResult<string>.Valid(result);
        }

        #endregion
    }
}
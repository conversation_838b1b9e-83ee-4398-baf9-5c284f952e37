﻿using ATS.CrossCutting.Reports.PagamentoFrete.Pagamentos;
using ATS.Domain.Enum;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Response.pagamento_frete;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Utils;
using ATS.CrossCutting.Reports.Protocolo;
using ATS.WS.Models.Webservice.Request.PagamentoFreteSemChave;
using ATS.Domain.Validation;
using ATS.Domain.Helpers;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Service;
using ClosedXML.Excel;
using Sistema.Framework.Util.Extension;

namespace ATS.WS.Services
{
    public class SrvPagamentoFrete : SrvBase
    {
        private readonly IViagemApp _viagemApp;
        private readonly IAuthSessionApp _authSessionApp;
        private readonly IPagamentoFreteApp _pagamentoFreteApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IAutenticacaoAplicacaoApp _autenticacaoAplicacaoApp;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly ICidadeApp _cidadeApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly IEstadoApp _estadoApp;

        public SrvPagamentoFrete(IViagemApp viagemApp, IAuthSessionApp authSessionApp, IPagamentoFreteApp pagamentoFreteApp, IUsuarioApp usuarioApp, IAutenticacaoAplicacaoApp autenticacaoAplicacaoApp, IViagemEventoApp viagemEventoApp, IEmpresaApp empresaApp, ICidadeApp cidadeApp, IProtocoloApp protocoloApp, IEstadoApp estadoApp)
        {
            _viagemApp = viagemApp;
            _authSessionApp = authSessionApp;
            _pagamentoFreteApp = pagamentoFreteApp;
            _usuarioApp = usuarioApp;
            _autenticacaoAplicacaoApp = autenticacaoAplicacaoApp;
            _viagemEventoApp = viagemEventoApp;
            _empresaApp = empresaApp;
            _cidadeApp = cidadeApp;
            _protocoloApp = protocoloApp;
            _estadoApp = estadoApp;
        }

        public Retorno<object> Consultar(string token, string cnpjAplicacao, string tokenPagamento)
        {
            var autenticacoesEmpresa = _autenticacaoAplicacaoApp.GetAutenticacaoAplicacaoPorCnpjAplicacao(cnpjAplicacao, token);
            if (autenticacoesEmpresa == null || !autenticacoesEmpresa.Any())
                throw new Exception("Empresa não autorizada para realizar esta consulta.");

            var viagemEvento = _viagemApp.ConsultarViagemEvento(tokenPagamento);

            if (viagemEvento == null)
                return new Retorno<object>(true, null);
            if (!autenticacoesEmpresa.Any(x => x.IdEmpresa == viagemEvento.IdEmpresa))
                throw new Exception("Empresa não autorizada para realizar esta consulta.");

            return new Retorno<object>(true, new PagamentoFreteResumoResponse
            {
                DataHoraPagamento = viagemEvento.DataHoraPagamento?.ToString("yyyy-MM-dd HH:mm:ss"),
                DataValidade = viagemEvento.DataValidade?.ToString("yyyy-MM-dd HH:mm:ss"),
                IdEmpresa = viagemEvento.IdEmpresa,
                IdViagem = viagemEvento.IdViagem,
                IdViagemEvento = viagemEvento.IdViagemEvento,
                INSS = viagemEvento.INSS,
                Instrucao = viagemEvento.Instrucao,
                IRRPF = viagemEvento.IRRPF,
                NumeroRecibo = viagemEvento.NumeroRecibo,
                SESTSENAT = viagemEvento.SESTSENAT,
                Status = viagemEvento.Status,
                Token = viagemEvento.Token,
                ValorBruto = viagemEvento.ValorBruto,
                ValorPagamento = viagemEvento.ValorPagamento,
                ValorTotalPagamento = viagemEvento.ValorTotalPagamento,
                TipoEventoViagem = viagemEvento.TipoEventoViagem,
                OrigemPagamento = viagemEvento.OrigemPagamento
            });
        }

        public void AtualizarPagamentoSemChave(PagamentoFreteSemChaveCrud pagFreteSemChave, int idUsuarioLibSemChave)
        {
            _pagamentoFreteApp.AtualizarPagamentoSemChave(pagFreteSemChave.LiberarPagamento,
                                                               pagFreteSemChave.Token,
                                                               pagFreteSemChave.EstabId,
                                                               pagFreteSemChave.Observacao,
                                                               idUsuarioLibSemChave);
        }

        public ValidationResult ValidarPagamentoSemChave(PagamentoFreteSemChaveCrud pagFreteSemChave)
        {
            throw new NotImplementedException();
        }

        public byte[] GerarRelatorioPagamentos(string sessionKey, List<int> idsViagemEvento, int idEmpresa, ETipoArquivoPagamento? tipoRelatorio = ETipoArquivoPagamento.PDF)
        {
            var usuario = _authSessionApp.GetNomeUsuarioFromToken(sessionKey);
            var viagemEvento = _viagemEventoApp.GetEventosViagem(idsViagemEvento);
            var empresa = _empresaApp.Get(idEmpresa);
            var logo = empresa.Logo == null ? ConstantesUtils.SemImagem : Convert.ToBase64String(empresa.Logo);
            var cidade = _cidadeApp.GetTodos();
            var estadoApp = _estadoApp.GetTodos();
            byte[] report;

            if (tipoRelatorio == ETipoArquivoPagamento.PDF)
            {
                var pagamentosListModel = new List<PagamentosModel>();
                var pagamentosHeaderModel = new PagamentosHeaderModel
                {
                    DataGeracao = DateTime.Now.ToString("dd/MM/yyyy"),
                    Usuario = usuario?.Nome ?? string.Empty
                };

                foreach (var item in viagemEvento)
                {
                    var ciot = _viagemEventoApp.GetCiotViagem(item.Viagem?.IdViagem ?? 0).FirstOrDefault()?.Ciot ?? "";
                    var model = new PagamentosModel();
                        // token: item.Token,
                        // dataLancamento: item.Viagem?.DataLancamento?.ToString("dd/MM/yyyy") ?? string.Empty,
                        // cteSerie: item.Viagem?.NumeroDocumento,
                        // dataPagamento: item.DataHoraPagamento?.ToString("dd/MM/yyyy") ?? string.Empty,
                        // dataLiberacao: null,
                        // evento: EnumHelper.GetDescriptionToString(item.TipoEventoViagem),
                        // valor: item.ValorPagamento.FormatMoney(),
                        // status: Enum.IsDefined(typeof(EStatusViagemEvento), item.Status) ? EnumHelper.GetDescriptionToString(item.Status) : string.Empty,
                        // dataAgendamentoPagamento: item.DataAgendamentoPagamento.FormatDateBr(),
                        // placa: item.Viagem?.Placa.FormatarPlaca(),
                        // estabelecimento: item.EstabelecimentoBase?.RazaoSocial,
                        // cpfMotorista: item.Viagem?.CPFMotorista.FormatarCpfCnpjSafe(),
                        // motorista: item.Viagem?.NomeMotorista,
                        // possuiProtocolo: item.IdProtocolo != null ? "Sim" : "Não",
                        // idProtocolo: item.IdProtocolo,
                        // ciot: ciot,
                        // numeroNota: item.Viagem?.NumeroNota,
                        // valorQuebra: string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", item.Viagem?.ValorQuebraMercadoria ?? 0),
                        // valorPedagio: string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", item.Viagem?.ValorPedagio),
                        // docProprietario: item.Viagem?.CPFCNPJProprietario.FormatarCpfCnpjSafe(),
                        // proprietario: item.Viagem?.NomeProprietario,
                        // pagoCartao: item.HabilitarPagamentoCartao ? "Sim" : "Não",
                        // idViagemEvento: item.IdViagemEvento);

                    pagamentosListModel.Add(model);
                }
                report = new PagamentosReport().RelatorioPagamentoPdf(pagamentosListModel, pagamentosHeaderModel);
            }
            else if (tipoRelatorio == ETipoArquivoPagamento.Excel)
            {
                using (var package = new XLWorkbook())
                {
                    var worksheet = package.Worksheets.Add("Pagamentos");

                    worksheet.Cell("A1").Value = "Contratante";
                    worksheet.Cell("B1").Value = "CNPJ";
                    worksheet.Cell("C1").Value = "Filial";
                    worksheet.Cell("D1").Value = "Sigla Filial";
                    worksheet.Cell("E1").Value = "Documento Cliente";
                    worksheet.Cell("F1").Value = "ID Viagem";
                    worksheet.Cell("G1").Value = "Numero Cartão";
                    worksheet.Cell("H1").Value = "Data Lançamento";
                    worksheet.Cell("I1").Value = "Data Pagamento";
                    worksheet.Cell("J1").Value = "Tipo Evento";
                    worksheet.Cell("K1").Value = "Valor";
                    worksheet.Cell("L1").Value = "Situação";
                    worksheet.Cell("M1").Value = "Agendado Para";
                    worksheet.Cell("N1").Value = "Usuário efetivação";
                    worksheet.Cell("O1").Value = "Pago Cartão";
                    worksheet.Cell("P1").Value = "CPF Motorista";
                    worksheet.Cell("Q1").Value = "Nome Motorista";
                    worksheet.Cell("R1").Value = "Doc. Proprietário";
                    worksheet.Cell("S1").Value = "Nome Proprietário";
                    worksheet.Cell("T1").Value = "Placa";
                    worksheet.Cell("U1").Value = "IdParcela";

                    var line = 2;
                    foreach (var item in viagemEvento)
                    {
                        worksheet.Cell($"A{line}").Value = item.Viagem?.Empresa?.NomeFantasia;
                        worksheet.Cell($"B{line}").Value = item.Viagem?.Empresa?.CNPJ.FormatarCpfCnpjSafe();
                        worksheet.Cell($"C{line}").Value = item.Viagem?.Filial?.NomeFantasia;
                        worksheet.Cell($"D{line}").Value = item.Viagem?.Filial?.Sigla;
                        worksheet.Cell($"E{line}").Value = item.Viagem?.DocumentoCliente;
                        worksheet.Cell($"F{line}").Value = item.IdViagem.ToStringSafe();
                        worksheet.Cell($"G{line}").Value = item.CartaoDestino;
                        worksheet.Cell($"H{line}").Value = item.Viagem?.DataLancamento.ToStringSafe();
                        worksheet.Cell($"I{line}").Value = item.DataHoraPagamento.FormatDateBr();
                        worksheet.Cell($"J{line}").Value = item.TipoEventoViagem.GetDescription();
                        worksheet.Cell($"K{line}").Value = item.ValorPagamento.FormatMoney();
                        worksheet.Cell($"L{line}").Value = item.Status.GetDescription();
                        worksheet.Cell($"M{line}").Value = item.DataAgendamentoPagamento.FormatDateBr();
                        worksheet.Cell($"N{line}").Value = item.NomeUsuario;
                        worksheet.Cell($"O{line}").Value = item.HabilitarPagamentoCartao ? "Sim" : "Não";
                        worksheet.Cell($"P{line}").Value = item.Viagem?.CPFMotorista.FormatarCpfCnpjSafe();
                        worksheet.Cell($"Q{line}").Value = item.Viagem?.NomeMotorista;
                        worksheet.Cell($"R{line}").Value = item.Viagem?.CPFCNPJProprietario.FormatarCpfCnpjSafe();
                        worksheet.Cell($"S{line}").Value = item.Viagem?.NomeProprietario;
                        worksheet.Cell($"T{line}").Value = item.Viagem?.Placa.FormatarPlaca();
                        worksheet.Cell($"U{line}").Value = item.IdViagemEvento;

                        line++;
                    }

                    worksheet.Column("A").AdjustToContents();
                    worksheet.Column("B").AdjustToContents();
                    worksheet.Column("C").AdjustToContents();
                    worksheet.Column("D").AdjustToContents();
                    worksheet.Column("E").AdjustToContents();
                    worksheet.Column("F").AdjustToContents();
                    worksheet.Column("G").AdjustToContents();
                    worksheet.Column("H").AdjustToContents();
                    worksheet.Column("I").AdjustToContents();
                    worksheet.Column("J").AdjustToContents();
                    worksheet.Column("K").AdjustToContents();
                    worksheet.Column("L").AdjustToContents();
                    worksheet.Column("M").AdjustToContents();
                    worksheet.Column("N").AdjustToContents();
                    worksheet.Column("O").AdjustToContents();
                    worksheet.Column("P").AdjustToContents();
                    worksheet.Column("R").AdjustToContents();
                    worksheet.Column("S").AdjustToContents();
                    worksheet.Column("T").AdjustToContents();
                    worksheet.Column("U").AdjustToContents();
                    
                    using (var stream = new MemoryStream())
                    {
                        package.SaveAs(stream);
                        stream.Position = 0;
                        return stream.ToArray();
                    }
                }
            }
            else
            {
                var pagamentosListModelExcel = new List<PagamentosExcel>();
                foreach (var item in viagemEvento)
                {
                    var modelExcel = new PagamentosExcel()
                    {
                        Cnpj = item.Viagem?.Empresa?.CNPJ.ToCnpjFormato(),
                        Documento = item.Viagem?.DocumentoCliente,
                        IdViagem = item.IdViagem.ToStringSafe(),
                        Lancamento = item.Viagem?.DataLancamento.FormatDateBr(),
                        Cartao = item.CartaoDestino,
                        Pagamento = item.DataHoraPagamento.FormatDateBr(),
                        Tipo = item.TipoEventoViagem.GetDescription(),
                        Valor = item.ValorPagamento,
                        Status = item.Status.GetDescription(),
                        Login = item.NomeUsuario,
                        NomeFilial = item.Viagem?.Filial?.RazaoSocial != null
                            ? item.Viagem?.Filial?.CNPJ + " - "
                            + item.Viagem?.Filial?.RazaoSocial + " - "
                            + cidade.FirstOrDefault(x => x.IdCidade == item.Viagem?.Filial?.IdCidade)?.Nome
                            + "/" + estadoApp.FirstOrDefault(x => x.IdEstado == item.Viagem?.Filial?.IdEstado)?.Sigla
                        : string.Empty
                    };

                    pagamentosListModelExcel.Add(modelExcel);
                }

                var listaDataSources = new List<Tuple<string, string>>
                {
                    new Tuple<string, string>("NomeEmpresa",empresa.NomeFantasia != null ? empresa.NomeFantasia : string.Empty),
                    new Tuple<string, string>("ValorTotal",viagemEvento.Sum(x => x.ValorPagamento).FormatMoney()),
                    new Tuple<string, string>("Logo",logo),
                    new Tuple<string, string>("CnpjEmpresa",empresa.CNPJ.FormatarCpfCnpj()),
                    new Tuple<string, string>("DataGeracao",DateTime.Now.FormatDateTimeBr()),
                    new Tuple<string, string>("ValorFilial",pagamentosListModelExcel.Where(x => !string.IsNullOrWhiteSpace(x.NomeFilial))
                        .Sum(x => x.Valor).FormatMoney())
                };

                report = new PagamentosReport().RelatorioPagamentosExcel(pagamentosListModelExcel, listaDataSources);
            }

            return report;
        }

        public byte[] GerarRelatorioPagamentosSemChave(int idUsuarioLogado, ETipoArquivo? tipoRelatorio)
        {
            var usuario = _usuarioApp.Consultar(idUsuarioLogado).Select(c => new
            {
                c.IdEmpresa,
                c.Nome
            }).FirstOrDefault();

            var pagamentosHeaderModel = new PagamentosHeaderModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy"), Usuario = usuario?.Nome ?? string.Empty };
            var pagamentosListModel = new List<PagamentosModel>();
            var viagemEvento = _viagemEventoApp.GetEventosViagemSemChave(usuario?.IdEmpresa ?? 0);

            #region Itens do protocolo

            foreach (var item in viagemEvento)
            {
                var model = new PagamentosModel();
                    // token: item.Token,
                    // dataLancamento: item.Viagem?.DataLancamento?.ToString("dd/MM/yyyy") ?? string.Empty,
                    // cteSerie: item.Viagem?.NumeroDocumento,
                    // dataPagamento: null,
                    // dataLiberacao: item.DataLibSemChave?.ToString("dd/MM/yyyy") ?? string.Empty,
                    // evento: EnumHelper.GetDescriptionToString(item.TipoEventoViagem),
                    // valor: string.Format(CultureInfo.GetCultureInfo("pt-BR"), "{0:C}", item.ValorPagamento),
                    // status: EnumHelper.GetDescriptionToString(item.Status),
                    // dataAgendamentoPagamento: item.DataAgendamentoPagamento.FormatDateBr(),
                    // placa: item.Viagem?.Placa,
                    // estabelecimento: item.EstabelecimentoBase?.RazaoSocial,
                    // cpfMotorista: item.Viagem?.CPFMotorista.FormatarCpfCnpjSafe(),
                    // motorista: item.Viagem?.NomeMotorista,
                    // possuiProtocolo: null,
                    // idProtocolo: null,
                    // ciot: null,
                    // numeroNota: null,
                    // valorQuebra: null,
                    // valorPedagio: null,
                    // docProprietario: item.Viagem?.CPFCNPJProprietario.FormatarCpfCnpjSafe(),
                    // proprietario: item.Viagem?.NomeProprietario,
                    // pagoCartao: item.HabilitarPagamentoCartao ? "Sim" : "Não",
                    // idViagemEvento: item.IdViagemEvento);
                pagamentosListModel.Add(model);
            }

            #endregion

            if (!tipoRelatorio.HasValue)
                tipoRelatorio = ETipoArquivo.PDF;

            var report = new PagamentosSemChaveReport().Relatorio(pagamentosListModel, pagamentosHeaderModel, (int)tipoRelatorio);
            return report;
        }

        public byte[] GerarRelatorioProtocolos(int? idEmpresa, int idUsuario, ETipoArquivo? tipoRelatorio, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var reportData = _protocoloApp.ConsultarRelatorioProtocolos(idEmpresa, take, page, order, filters);
            var usuarioNome = _usuarioApp.Find(x => x.IdUsuario == idUsuario).Select(x => x.Nome).FirstOrDefault();
            var pagamentosHeaderModel = new ProtocoloHeaderRelatorioModel { DataGeracao = DateTime.Now.ToString("dd/MM/yyyy"), Usuario = usuarioNome ?? string.Empty };
            var pagamentosListModel = new List<ProtocoloRelatorioModel>();

            foreach (var item in reportData)
                pagamentosListModel.Add(new ProtocoloRelatorioModel
                {
                    IdProtocolo = item.IdProtocolo,
                    Estabelecimento = item.EstabelecimentoBase.RazaoSocial,
                    Associacao = item.EstabelecimentoDestinatario?.Descricao,
                    ValorProtocolo = $"R$ {item.ValorProtocolo.ToString("G")}",
                    DataGeracao = item.DataGeracao.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataTransito = item.DataTransito?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataRecebimento = item.DataRecebidoEmpresa?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataAprovacao = item.DataAprovacao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataPrevisaoPagamento = item.DataPrevisaoPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataPagamento = item.DataPagamento?.ToString("dd/MM/yyyy HH:mm:ss"),
                    DataRejeicao = item.DataRejeicao?.ToString("dd/MM/yyyy HH:mm:ss"),
                    Status = EnumHelpers.GetDescription(item.StatusProtocolo)
                });

            var report = new ProtocoloReport().GerarRelatorioProtocolos(pagamentosHeaderModel, pagamentosListModel, (int)tipoRelatorio);
            return report;
        }

        public byte[] GerarRelatorioGrid(DateTime dataInicial, DateTime dataFinal, string uf, double? a, double? b, double? c, int idEmpresa, string tipoArquivo)
        {
            return _pagamentoFreteApp.GerarRelatorioGrid(dataInicial, dataFinal, uf, a, b, c, tipoArquivo, GetLogo(idEmpresa), idEmpresa);
        }

        public byte[] GerarRelatorioConsultaPagamentosEstabelecimentoReport(DateTime? dataInicio, DateTime? dataFim, int idEstabelecimentoBase, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, int? idEmpresa)
        {
            return _pagamentoFreteApp.ConsultarPagamentosEstabelecimentoReport(dataInicio, dataFim,
                idEstabelecimentoBase, orderFilters, filters, tipoArquivo, GetLogo(idEmpresa ?? 0));
        }

        public byte[] GerarRelatorioDetalhesProvisaoAberto(DateTime dataInicio, DateTime dataFim, bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, int? idEmpresa)
        {
            return _pagamentoFreteApp.GerarRelatorioDetalhesProvisaoAberto(dataInicio, dataFim,
                habilitarPagamentoCartao, uf, idEstabelecimentoBase, tipoEventoViagem, orderFilters, filters,
                tipoArquivo, GetLogo(idEmpresa ?? 0), idEmpresa);
        }

        public byte[] GerarRelatorioDetalhesProvisaoBaixado(DateTime dataInicio, DateTime dataFim, bool habilitarPagamentoCartao, int? uf, int? idEstabelecimentoBase, ETipoEventoViagem? tipoEventoViagem, OrderFilters orderFilters, List<QueryFilters> filters, string tipoArquivo, int? idEmpresa)
        {
            return _pagamentoFreteApp.GerarRelatorioDetalhesProvisaoBaixado(dataInicio, dataFim,
                habilitarPagamentoCartao, uf, idEstabelecimentoBase, tipoEventoViagem, orderFilters, filters,
                tipoArquivo, GetLogo(idEmpresa ?? 0), idEmpresa);
        }
    }
}

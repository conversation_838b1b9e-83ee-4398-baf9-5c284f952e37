using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.Viagem;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.Ciot;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Common.Response;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Services.ViagemServices.Validacoes;
using Sistema.Framework.Util.Extension;
using Sistema.Framework.Util.Helper;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.WS.Services.ViagemServices
{
    public class AlteracaoViagem : SrvBase
    {
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly IViagemApp _viagemApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IResgateCartaoAtendimentoApp _resgatarCartaoApp;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly PropriedadesPagamentoFreteViagem _propriedadesPagamentoFreteViagem;
        private readonly BloqueioGestorViagem _bloqueioGestorViagem;
        private readonly IntegracaoMeioHomologadoViagem _integracaoMeioHomologadoViagem;
        private readonly ICidadeRepository _cidadeRepository;
        private readonly IMotoristaApp _motoristaApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IVeiculoApp _veiculoApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IFilialApp _filialApp;
        private readonly IMotoristaService _motoristaService;
        private readonly IUsuarioService _usuarioService;
        private readonly WebHookViagem _webHookViagem;
        private readonly ValoresViagem _valoresViagem;
        private readonly CartoesAppFactoryDependencies _cartoesAppFactoryDependencies;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IViagemPendenteGestorService _viagemPendenteGestorService;
        private readonly IViagemEstabelecimentoApp _viagemEstabelecimentoApp;
        private readonly IRotaModeloApp _rotaModeloApp;
        private readonly IUserIdentity _userIdentity;
        private readonly IValePedagioApp _valePedagioApp;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly ITransacaoPixRepository _transacaoPixRepository;


        public AlteracaoViagem(IClienteApp clienteApp, IParametrosApp parametrosApp, IViagemApp viagemApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, IProprietarioApp proprietarioApp,
            ICadastrosApp cadastrosApp, IVersaoAnttLazyLoadService versaoAntt, IEmpresaRepository empresaRepository, IResgateCartaoAtendimentoApp resgatarCartaoApp,
            ITransacaoCartaoApp transacaoCartaoApp, PropriedadesPagamentoFreteViagem propriedadesPagamentoFreteViagem, BloqueioGestorViagem bloqueioGestorViagem,
            IntegracaoMeioHomologadoViagem integracaoMeioHomologadoViagem, ICidadeRepository cidadeRepository, IMotoristaApp motoristaApp, IUsuarioApp usuarioApp, IVeiculoApp veiculoApp, 
            IEmpresaApp empresaApp, IFilialApp filialApp, IMotoristaService motoristaService, IUsuarioService usuarioService, WebHookViagem webHookViagem, ValoresViagem valoresViagem, 
            CartoesAppFactoryDependencies cartoesAppFactoryDependencies, IViagemEventoApp viagemEventoApp, IViagemPendenteGestorService viagemPendenteGestorService, 
            IViagemEstabelecimentoApp viagemEstabelecimentoApp, IRotaModeloApp rotaModeloApp, IUserIdentity userIdentity, IValePedagioApp valePedagioApp, IParametrosGenericoService parametrosGenericoService, ITransacaoPixRepository transacaoPixRepository)
        {
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _viagemApp = viagemApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _versaoAntt = versaoAntt;
            _empresaRepository = empresaRepository;
            _resgatarCartaoApp = resgatarCartaoApp;
            _transacaoCartaoApp = transacaoCartaoApp;
            _propriedadesPagamentoFreteViagem = propriedadesPagamentoFreteViagem;
            _bloqueioGestorViagem = bloqueioGestorViagem;
            _integracaoMeioHomologadoViagem = integracaoMeioHomologadoViagem;
            _cidadeRepository = cidadeRepository;
            _motoristaApp = motoristaApp;
            _usuarioApp = usuarioApp;
            _veiculoApp = veiculoApp;
            _empresaApp = empresaApp;
            _filialApp = filialApp;
            _motoristaService = motoristaService;
            _usuarioService = usuarioService;
            _webHookViagem = webHookViagem;
            _valoresViagem = valoresViagem;
            _cartoesAppFactoryDependencies = cartoesAppFactoryDependencies;
            _viagemEventoApp = viagemEventoApp;
            _viagemPendenteGestorService = viagemPendenteGestorService;
            _viagemEstabelecimentoApp = viagemEstabelecimentoApp;
            _rotaModeloApp = rotaModeloApp;
            _userIdentity = userIdentity;
            _valePedagioApp = valePedagioApp;
            _parametrosGenericoService = parametrosGenericoService;
            _transacaoPixRepository = transacaoPixRepository;
        }
        
        public Retorno<ViagemRemoverCarretasResponse> RemoverCarretas(ViagemRemoverCarretasRequest request, EVersaoAntt versaoAntt)
        {
            var viagem = _viagemApp.Get(request.IdViagem);
            
            if(viagem == null)
                return new Retorno<ViagemRemoverCarretasResponse>($"Viagem não encontrata pelo Id {request.IdViagem}");
            else if (viagem.StatusViagem == EStatusViagem.Cancelada)
                return new Retorno<ViagemRemoverCarretasResponse>("Viagem cancelada, não é permitido realizar alterações em viagens com este status.");
            else if (viagem.StatusViagem == EStatusViagem.Baixada)
                return new Retorno<ViagemRemoverCarretasResponse>("Viagem baixada, não é permitido realizar alterações em viagens com este status.");

            var idViagemCarretaRemovidaList = new List<int>();
            string mensagemRetorno;
            
            foreach (var carretaViagem in viagem.ViagemCarretas)
                foreach (var carretaRemovida in request.Carretas)
                    if (carretaRemovida.Equals(carretaViagem.Placa))
                        idViagemCarretaRemovidaList.Add(carretaViagem.IdViagemCarreta);

            var ciotResult = new DeclararCiotResult
            {
                Resultado = EResultadoDeclaracaoCiot.NaoHabilitado,
                Declarado = false,
                Mensagem = "CIOT não processado. Tag 'HabilitarDeclaracaoCiot' desabilitada pelo consumidor."
            };
            
            if (idViagemCarretaRemovidaList.Any())
            {
                var validation = _viagemApp.RemoverPlacasDasCarretas(viagem.IdViagem, idViagemCarretaRemovidaList);

                if (!validation.IsValid)
                    return new Retorno<ViagemRemoverCarretasResponse>(validation.ToString());

                mensagemRetorno = "Carreta(s) removidas com sucesso";

                if (viagem.HabilitarDeclaracaoCiot)
                    switch (versaoAntt)
                    {
                        case EVersaoAntt.Versao2:
                            ciotResult = _ciotV2App.DeclararCiotFromIntegracaoViagem(viagem, request.CNPJEmpresa, viagem.HabilitarDeclaracaoCiot, true, false, null);
                            break;
                        case EVersaoAntt.Versao3:
                            ciotResult = _ciotV3App.DeclararCiotFromIntegracaoViagem(viagem, request.CNPJEmpresa, viagem.HabilitarDeclaracaoCiot, true, null);
                            break;
                        default:
                            ciotResult = _ciotV2App.DeclararCiotFromIntegracaoViagem(viagem, request.CNPJEmpresa, viagem.HabilitarDeclaracaoCiot, true, false, null);
                            break;
                    }
            }
            else
            {
                if(request.Carretas.Count == 1)
                    mensagemRetorno = $"Carreta {request.Carretas[0].ToPlacaFormato()} não encontrada na viagem {request.IdViagem}";
                else
                {
                    var carretasPlacaFormato = new List<string>();
                    request.Carretas.ForEach(carreta => carretasPlacaFormato.Add(carreta.ToPlacaFormato()));
                    mensagemRetorno = $"Carretas {string.Join(", ", carretasPlacaFormato)} não encontradas na viagem {request.IdViagem}";
                }
            }
            
            return new Retorno<ViagemRemoverCarretasResponse>(idViagemCarretaRemovidaList.Any(), mensagemRetorno, 
                new ViagemRemoverCarretasResponse
                {
                    CarretasRestantes = viagem.ViagemCarretas.Select(carreta => carreta.Placa).ToList(),
                    CIOT = idViagemCarretaRemovidaList.Any() ? ciotResult : null
                });
        }

        public Retorno<ViagemIntegrarResponseModel> AlterarV2(ViagemIntegrarRequestModel @params,bool isApi)
        {
            // @params já vem com os dados da viagem editados de acordo com o que o usuário quis fazer (cancelar, baixar, etc..)
            if (string.IsNullOrEmpty(@params.CNPJEmpresa))
                @params.CNPJEmpresa = @params.CNPJAplicacao;
            
            if(@params.IdViagem == null)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Id Viagem não informado.", null);
            
            var retificarCiot = false;
            var atualizarCiot = false;

            #region Valida NumeroControle

            var viagemQuery = _viagemApp.Find(x => x.IdViagem == @params.IdViagem.Value);

            if (!viagemQuery.Any())
                return new Retorno<ViagemIntegrarResponseModel>(false, $"Não foi encontrada uma viagem com o IdViagem {@params.IdViagem.Value} na base de dados", null);

            var numerosControleRepetidos = @params.ViagemEventos
                .Where(x => x.NumeroControle != null && (x.IdViagemEvento == null || x.IdViagemEvento <= 0))
                .Select(x => x.NumeroControle)
                .GroupBy(x => x)
                .Where(g => g.Count() > 1)
                .Select(y => y.Key)
                .ToList();

            if (!numerosControleRepetidos.Any())
            {
                var numerosControleBanco = viagemQuery
                    .Include(x => x.ViagemEventos)
                    .Select(x => x.ViagemEventos)
                    .Select(y => y.Select(z => z.NumeroControle).ToList())
                    .FirstOrDefault();

                if (numerosControleBanco.Any())
                {
                    var numerosControleNovos = @params.ViagemEventos
                        .Where(x => (x.IdViagemEvento == null || x.IdViagemEvento <= 0) && x.NumeroControle != null)
                        .Select(x => x.NumeroControle).ToList();

                    numerosControleRepetidos = numerosControleNovos.Where(numeroControleNovo => numerosControleBanco.Contains(numeroControleNovo)).ToList();
                }
            }

            if (numerosControleRepetidos.Any())
                return new Retorno<ViagemIntegrarResponseModel>(false,
                    numerosControleRepetidos.Count == 1
                        ? $"Viagem com número de controle do evento {numerosControleRepetidos.First()} já cadastrado."
                        : $"Viagem com número de controle do eventos {string.Join(", ", numerosControleRepetidos)} já cadastrados."
                    , null);

            #endregion
            
            var viagem = _viagemApp.Get(@params.IdViagem.Value);
            
            if (viagem.IdEmpresa != _userIdentity.IdEmpresa)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Viagem não pertence à empresa do usuário.", null);
            
            var viagemStatusBanco = viagem.StatusViagem;

            //if (@params.DataAtualizacao.HasValue && @params.DataAtualizacao < viagem.DataAtualizacao)
            //    return new Retorno<ViagemIntegrarResponseModel>(false, "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente", null);

            int? idClienteOrigem = null;
            int? idClienteDestino = null;
            var empresa = _empresaApp.Get(viagem.IdEmpresa, null);

            if (@params.DadosBancarioPagamentoSemCartao != null)
            {
                viagem.Agencia = @params.DadosBancarioPagamentoSemCartao.Agencia;
                viagem.ContaCorrente = @params.DadosBancarioPagamentoSemCartao.ContaCorrente;
                viagem.IdBanco = @params.DadosBancarioPagamentoSemCartao.IdBanco;
                viagem.DescricaoBanco = @params.DadosBancarioPagamentoSemCartao.DescricaoBanco;
                viagem.TipoConta = @params.DadosBancarioPagamentoSemCartao.TipoConta;
                viagem.FormaPagamentoSemCartao = @params.DadosBancarioPagamentoSemCartao.FormaPagamentoSemCartao;
            }
            
            if (!string.IsNullOrWhiteSpace(@params.CPFCNPJClienteOrigem))
                idClienteOrigem = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteOrigem, viagem.IdEmpresa);

            if (!string.IsNullOrWhiteSpace(@params.CPFCNPJClienteDestino))
                idClienteDestino = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteDestino, viagem.IdEmpresa);

            //Viagem Internacional 
            if ((@params.CPFCNPJClienteOrigem == "00000000000000" || @params.CPFCNPJClienteDestino == "00000000000000") && empresa.GerarCiotViagemInternacional) 
                @params.HabilitarDeclaracaoCiot = false;
            
            var permiteAlterarViagem = PermitirAlterarViagemV2(@params, viagem, idClienteOrigem, idClienteDestino, _ciotV2App);
            
            if (!permiteAlterarViagem.IsValid)
                return new Retorno<ViagemIntegrarResponseModel>(false, permiteAlterarViagem.ToString(), null);

            if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                if(@params.StatusViagem != EStatusViagem.Cancelada)
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        "Viagem cancelada, não é permitido realizar alterações em viagens com este status.", null);
                else
                {
                    var ciot = _ciotV2App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);
                    var pedagio = _viagemApp.GetStatusPedagio(viagem);
                    
                    return new Retorno<ViagemIntegrarResponseModel>(true, "Viagem já estava cancelada", 
                        RetornoViagem(viagem, ciot, pedagio, null,null));
                }
            }

            if (@params.Pedagio != null && @params.ValorPedagio > 0 == false && @params.Pedagio.ValorPedagio > 0 == false && 
                !@params.Pedagio.IdentificadorHistorico.HasValue && @params.Pedagio.Localizacoes?.Any() == true && @params.Pedagio.QtdEixos < 2)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Quantidade de eixos inválido, deve ser indicado um valor igual ou maior que 2.", null);

            if (!string.IsNullOrEmpty(@params.DocumentoCliente) && @params.DocumentoCliente.Length > 100)
                return new Retorno<ViagemIntegrarResponseModel>(false, @"O campo DocumentoCliente não pode ter mais de 100 caracteres.", null);
            
            var ciotIntegrado = _ciotV2App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);

            if (ciotIntegrado != null)
            {
                var validacoesParaRetificacao = new CiotRetificacao(_ciotV2App, _cadastrosApp).ValidarDadosRetificacao(viagem, @params);
                
                if (!validacoesParaRetificacao.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false, validacoesParaRetificacao.Errors.FirstOrDefault()?.Message, null);
            }
            
            var validaPagamentoFrete = empresa.ValidacaoPagFrete;

            if (validaPagamentoFrete && (!@params.PesoSaida.HasValue || @params.PesoSaida.Value <= 0))
                if (validaPagamentoFrete && @params.PesoSaida.HasValue && @params.PesoSaida.Value <= 0)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Peso de saida deve ser maior que 0.", null);
            
            if(@params.HabilitarDeclaracaoCiot && _parametrosApp.ValidaCnpjCpfProprietarioNaViagem() && !_proprietarioApp.All().Any(x => x.IdEmpresa == viagem.IdEmpresa && x.CNPJCPF == viagem.CPFCNPJProprietario))
                return new Retorno<ViagemIntegrarResponseModel>(false, $"O proprietário {@params.CPFCNPJProprietario.ToCpfOrCnpj()} não foi encontrado na base de dados", null);

            if(@params.Carretas?.Any() == true)
                for (var i = 0; @params.Carretas.Count > i; i++)
                {
                    @params.Carretas[i] = @params.Carretas[i].RemoveSpecialCharacters().Trim().ToUpper();
                    if (@params.Carretas[i].Length != 7)
                        return new Retorno<ViagemIntegrarResponseModel>(false, "Os itens de Carretas devem conter 7 caracteres", null);
                }
            
            if (idClienteOrigem.HasValue && viagem.IdClienteOrigem != idClienteOrigem.Value)
            {
                viagem.IdClienteOrigem = idClienteOrigem.Value;
                retificarCiot = true;
            }
            if (idClienteDestino.HasValue && viagem.IdClienteDestino != idClienteDestino.Value)
            {
                viagem.IdClienteDestino = idClienteDestino.Value;
                retificarCiot = true;
            }

            Filial filial = null;

            if (!string.IsNullOrEmpty(@params.CNPJFilial)) filial = _filialApp.Get(@params.CNPJFilial);

            if (!string.IsNullOrWhiteSpace(@params.NumeroDocumento))
                viagem.NumeroDocumento = @params.NumeroDocumento;

            if (!string.IsNullOrWhiteSpace(@params.NumeroNota))
                viagem.NumeroNota = @params.NumeroNota;

            if (!string.IsNullOrWhiteSpace(@params.Produto))
                viagem.Produto = @params.Produto.ValueLimited(100);

            if (@params.DataEmissao.HasValue)
                viagem.DataEmissao = @params.DataEmissao;

            if (@params.DataColeta.HasValue && viagem.DataColeta != @params.DataColeta)
            {
                viagem.DataColeta = @params.DataColeta;
                retificarCiot = true;
            }
            
            if (@params.DataPrevisaoEntrega.HasValue && viagem.DataPrevisaoEntrega != @params.DataPrevisaoEntrega.Value)
            {
                viagem.DataPrevisaoEntrega = @params.DataPrevisaoEntrega.Value;
                retificarCiot = true;
            }

            // Retirado para não sobrepor
            // viagem.Unidade = @params.Unidade;

            if (@params.Quantidade > 0)
                viagem.Quantidade = @params.Quantidade;

            if (filial != null)
                viagem.IdFilial = filial.IdFilial;

            if (!string.IsNullOrWhiteSpace(@params.RazaoSocialFilial))
                viagem.RazaoSocialFilial = @params.RazaoSocialFilial;

            //Campos adicinados durante a homologação...
            //Não foi criado a integração de filial sendo assim para não atrapalhar o processo de homologação adicionei esses campos
            if (!string.IsNullOrWhiteSpace(@params.CNPJFilial))
                viagem.CNPJFilial = @params.CNPJFilial;

            if (!string.IsNullOrWhiteSpace(@params.CPFMotorista) && viagem.CPFMotorista != @params.CPFMotorista.Trim())
            {
                viagem.CPFMotorista = @params.CPFMotorista.Trim();

                if (string.IsNullOrWhiteSpace(viagem.NomeMotorista))
                {
                    viagem.NomeMotorista = _motoristaService.GetAllByIdEmpresa(viagem.IdEmpresa)
                        .Where(m => m.CPF == @params.CPFMotorista)
                        .Select(m => m.Nome)
                        .FirstOrDefault();

                    if (string.IsNullOrWhiteSpace(viagem.NomeMotorista))
                        viagem.NomeMotorista = _usuarioService.GetByCpfQuery(viagem.CPFMotorista)
                            .Select(u => u.Nome)
                            .FirstOrDefault();
                }
            }

            if (@params.IdCarga.HasValue)
            {
                if (@params.IdCarga <= 0)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Código da carga inválido.", null);
                viagem.ViagemCargas = new List<ViagemCarga>
                {
                    new ViagemCarga
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        IdCarga = @params.IdCarga.Value
                    }
                };
            }

            if (@params.HabilitarDeclaracaoCiot && viagem.NaturezaCarga == null && (@params.NaturezaCarga == null || @params.NaturezaCarga == 0))
                return new Retorno<ViagemIntegrarResponseModel>(false, @"Natureza da carga é obrigatória ao integrar viagem com recurso de CIOT habilitado.", null);

            if (!string.IsNullOrWhiteSpace(@params.Placa) && viagem.Placa != @params.Placa.Trim())
            {
                viagem.Placa = @params.Placa.RemoveSpecialCharacters().Trim().ToUpper();
                if (viagem.Placa.Length != 7)
                    return new Retorno<ViagemIntegrarResponseModel>(false, "O campo Placa deve conter 7 caracteres", null);
                
                retificarCiot = true;
            }

            if (@params.Carretas?.Any() == true || @params.CarretasViagemV2?.Any() == true)
                retificarCiot = ProcessarCarretas(@params, viagem, retificarCiot);
            
            _viagemApp.AjustarProprietario(viagem);

            if (!string.IsNullOrWhiteSpace(@params.DocumentoCliente))
            {
                viagem.DocumentoCliente = @params.DocumentoCliente;
                atualizarCiot = true;
            }

            if (!string.IsNullOrWhiteSpace(@params.NumeroDocumento))
                viagem.NumeroDocumento = @params.NumeroDocumento;
            
            if (@params.ViagemEventos.Any())
            {
                if (@params.ViagemEventos.Any(ve => ve.ValorPagamento < 0))
                    return new Retorno<ViagemIntegrarResponseModel>(false,"Valor informado não pode ser negativo.", null);
                
                if (@params.ViagemEventos.Any(ve => ve.ValorPagamento == 0))
                    return new Retorno<ViagemIntegrarResponseModel>(false,"Valor pagamento não informado.", null);
                
                if (@params.ViagemEventos.Any(ve => ve.Status == EStatusViagemEvento.Cancelado && !(ve.IdViagemEvento > 0)))
                    return new Retorno<ViagemIntegrarResponseModel>(false,$"Não é permitido integrar um novo evento com o status {EStatusViagemEvento.Cancelado.GetDescription()}.", null);

                // previne edicao de eventos baixados, a menos que seja cancelamento, dai deixa pras outras regras
                foreach (var evento in @params.ViagemEventos.ToList())
                {
                    if (evento.Status == EStatusViagemEvento.Cancelado) continue;
                    var eventoPersistido = viagem.ViagemEventos
                        .FirstOrDefault(c => evento.IdViagemEvento != null && c.IdViagemEvento == evento.IdViagemEvento|| evento.Token != null && c.Token == evento.Token || evento.NumeroControle != null && c.NumeroControle == evento.NumeroControle);
                    if (eventoPersistido != null && eventoPersistido.Status == EStatusViagemEvento.Baixado)
                        @params.ViagemEventos.Remove(evento);
                }
                
                if (@params.ViagemEventos.Any(o => !o.IdViagemEvento.HasValue))
                {
                    atualizarCiot = true;
                    retificarCiot = true;
                }
                else
                {
                    var eventosPersistidos = viagem.ViagemEventos;
                    var eventosEntrada = @params.ViagemEventos;

                    foreach (var eventoEntrada in eventosEntrada)
                    {
                        var eventoPersistido = eventosPersistidos.FirstOrDefault(o => o.IdViagemEvento == eventoEntrada.IdViagemEvento);

                        if (eventoPersistido != null)
                        {
                            if (eventoPersistido.ValorPagamento != eventoEntrada.ValorPagamento && 
                                eventoPersistido.Status.In(EStatusViagemEvento.Aberto, EStatusViagemEvento.Bloqueado))
                            {
                                atualizarCiot = true;
                                retificarCiot = true;
                            }

                            if (eventoPersistido.Status == EStatusViagemEvento.Bloqueado && eventoEntrada.Status == EStatusViagemEvento.Baixado 
                            && isApi && !empresa.BaixarParcelaBloqueado)
                            {
                                return new Retorno<ViagemIntegrarResponseModel>(false,$"Parcela id {eventoEntrada.IdViagemEvento} está bloqueada e não pode ser efetivada.", null);
                            }

                            if (eventoPersistido.Status != eventoEntrada.Status && eventoEntrada.Status == EStatusViagemEvento.Cancelado)
                            {
                                atualizarCiot = true;
                                retificarCiot = true;
                            }

                            if (eventoPersistido.Status.In(EStatusViagemEvento.Aberto, EStatusViagemEvento.Bloqueado) && eventoEntrada.Status == EStatusViagemEvento.Baixado)
                                atualizarCiot = true;
                        }
                    }
                }
            }
            
            if (@params.DocumentosFiscais != null && @params.DocumentosFiscais.Any())
                foreach (var documentoFiscal in @params.DocumentosFiscais)
                {
                    decimal numeroDocumento;
                    var validacaoDocumento = ValidarDocumentoFiscal(documentoFiscal, viagem.IdEmpresa, out numeroDocumento);
                    if(!validacaoDocumento.IsValid)
                        return new Retorno<ViagemIntegrarResponseModel>(false, validacaoDocumento.ToString(), null);
                    
                    if (documentoFiscal.IdViagemDocumentoFiscal.HasValue)
                    {
                        var documentoFiscalPersistido = viagem.ViagemDocumentosFiscais.FirstOrDefault(x => x.IdViagemDocumentoFiscal == documentoFiscal.IdViagemDocumentoFiscal);
                        if (documentoFiscalPersistido == null)
                            return new Retorno<ViagemIntegrarResponseModel>(false, $"Não foi encontrado documento fiscal de id {documentoFiscal.IdViagemDocumentoFiscal}", null);

                        documentoFiscalPersistido.IdClienteOrigem = documentoFiscal.IdClienteOrigem ?? documentoFiscalPersistido.IdClienteOrigem;
                        documentoFiscalPersistido.IdClienteDestino = documentoFiscal.IdClienteDestino ?? documentoFiscalPersistido.IdClienteDestino;
                        documentoFiscalPersistido.NumeroDocumento = numeroDocumento;
                        documentoFiscalPersistido.Serie = documentoFiscal.Serie;
                        documentoFiscalPersistido.Chave = documentoFiscal.Chave;
                        documentoFiscalPersistido.PesoSaida = documentoFiscal.PesoSaida;
                        documentoFiscalPersistido.Valor = documentoFiscal.Valor;
                        documentoFiscalPersistido.TipoDocumento = documentoFiscal.TipoDocumento;
                    }
                    else
                    {
                        if (!documentoFiscal.IdClienteOrigem.HasValue || !documentoFiscal.IdClienteDestino.HasValue)
                        {
                            documentoFiscal.IdClienteOrigem = viagem.IdClienteOrigem;
                            documentoFiscal.IdClienteDestino = viagem.IdClienteDestino;
                        }
                        
                        viagem.ViagemDocumentosFiscais.Add(new ViagemDocumentoFiscal
                        {
                            Valor = documentoFiscal.Valor,
                            PesoSaida = documentoFiscal.PesoSaida,
                            NumeroDocumento = numeroDocumento,
                            Serie = documentoFiscal.Serie,
                            Chave = documentoFiscal.Chave,
                            TipoDocumento = documentoFiscal.TipoDocumento,
                            IdViagem = viagem.IdViagem,
                            IdClienteOrigem = documentoFiscal.IdClienteOrigem,
                            IdClienteDestino = documentoFiscal.IdClienteDestino
                        });
                    }
                }

            var valorPedagio = @params.ValorPedagio ?? @params.Pedagio?.ValorPedagio;
            if (valorPedagio > 0 && viagem.ValorPedagio != valorPedagio)
            {
                viagem.ValorPedagio = valorPedagio.Value;
                retificarCiot = true;
            }

            if (@params.PesoSaida.HasValue && viagem.PesoSaida != @params.PesoSaida)
            {
                viagem.PesoSaida = @params.PesoSaida;
                retificarCiot = true;
            }

            if (@params.NaturezaCarga.HasValue && viagem.NaturezaCarga != @params.NaturezaCarga)
            {
                viagem.NaturezaCarga = @params.NaturezaCarga;
                retificarCiot = true;
            }

            if (@params.IRRPF > 0 && @params.IRRPF != viagem.IRRPF)
            {
                viagem.IRRPF = @params.IRRPF;
                atualizarCiot = true;
            }
            
            if (@params.INSS > 0 && @params.INSS != viagem.INSS)
            {
                viagem.INSS = @params.INSS;
                atualizarCiot = true;
            }
            
            if (@params.SESTSENAT > 0 && @params.SESTSENAT != viagem.SESTSENAT)
            {
                viagem.SESTSENAT = @params.SESTSENAT;
                atualizarCiot = true;
            }
            
            var transacaoService = _transacaoCartaoApp;
            var idsEventos = @params.ViagemEventos.Select(x => x.IdViagemEvento).ToList();
            
            // Aqui abaixo são resgatas as transações cartão do(s) evento(s) editado(s) e com status baixado (se for em tela será só um evento)
            var transacoes = transacaoService.BuscarEventosBaixados(viagem.IdViagem, idsEventos);
            
            // Seta os eventos e a viagem relacionados ao pagamento de frete (a variável "viagem" fica setada de acordo com o que o usuário quis fazer (cancelar, baixar, etc..)
            _propriedadesPagamentoFreteViagem.SetarPropriedadesPagamentoFrete(@params, viagem, false);
            
            var isValidToCrud = _viagemApp.IsValidToCrud(viagem, EProcesso.Update);
            if (!isValidToCrud.IsValid)
                return new Retorno<ViagemIntegrarResponseModel>(false, isValidToCrud.ToFormatedMessage(), null);

            // TODO: Verificar se vai funcionar
            _webHookViagem.SetarWebhooks(@params, viagem);

            var cartaoVinculadoProprietario = new CartaoVinculadoPessoaListResponse();
            var cartaoVinculadoMotorista = new CartaoVinculadoPessoaListResponse();

            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit);
            
            if (@params.ViagemEventos.Any(c => c.HabilitarPagamentoCartao))
            {
                _viagemApp.AjustarProprietario(viagem);

                var listaEventos = viagem.ViagemEventos.Select(c => c.TipoEventoViagem).ToList();
                var produtoId = cartoesApp.GetIdProdutoCartaoFretePadrao();
              
                var retornoCartoes =
                    new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App)
                        .ValidarCartaoMeioHomologado(StringExtension.OnlyNumbers(viagem.CPFMotorista),
                            StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario), viagem.IdEmpresa, cartoesApp,
                            out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, listaEventos, false, true);

                if (!retornoCartoes.Key)
                    throw new Exception(retornoCartoes.Value);

                if (cartaoVinculadoProprietario.Cartoes != null && cartaoVinculadoMotorista.Cartoes != null)
                {
                    var cartaoOrigem = cartaoVinculadoProprietario.Cartoes.FirstOrDefault(x => x.Produto.Id == produtoId);
                    var cartaoDestino = cartaoVinculadoMotorista.Cartoes.FirstOrDefault(x => x.Produto.Id == produtoId);
                
                    //Atribuições dos cartões viagem evento 
                    foreach (var item in viagem.ViagemEventos)
                    {
                        if (item.IdViagemEvento == 0 && item.HabilitarPagamentoCartao)
                        {
                            item.CartaoDestino = cartaoDestino.Identificador.ToStringSafe();
                            item.CartaoOrigem = cartaoOrigem.Identificador.ToStringSafe();
                        }
                        else if (item.IdViagemEvento != 0 && !item.HabilitarPagamentoCartao && 
                                 (!string.IsNullOrWhiteSpace(item.CartaoDestino) || !string.IsNullOrWhiteSpace(item.CartaoOrigem)))
                        {
                            item.CartaoDestino = null;
                            item.CartaoOrigem = null;
                        }
                    }
                }

                var viagemCancelada = viagem.ViagemEventos.Where(c => c.Status == EStatusViagemEvento.Cancelado).ToList();
                var paramsviagemCancelada = @params.ViagemEventos.Where(c => c.Status == EStatusViagemEvento.Cancelado).Select(o => o.IdViagemEvento.ToInt()).ToList();

                if (viagemCancelada.Any())
                {
                    var transacoesViagem = transacaoService.GetAllByIdViagem(viagem.IdViagem);
                    
                    var transacoesCanceladas = transacoesViagem.Where(o => paramsviagemCancelada.Contains(o.IdViagemEvento.ToInt())).ToList();

                    var valorTransferencia = transacoesCanceladas.Where(c =>
                            c.StatusPagamento == EStatusPagamentoCartao.Baixado &&
                            (c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaTarifaAntt ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaAdiantamento ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaSaldo ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaEstadia ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaAbono ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaAbastecimento ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaRpa))
                        .Sum(c => c.ValorMovimentado);
                    
                    if (valorTransferencia > 0)
                    {
                        var retornoSaldo =
                            new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App)
                                .ValidarSaldoCartao(StringExtension.OnlyNumbers(viagem.CPFMotorista),
                                    viagem.IdEmpresa, valorTransferencia, cartoesApp);

                        if (!retornoSaldo.Key)
                            throw new Exception(retornoSaldo.Value);
                    }

                    var valoresCarga = transacoesCanceladas.Where(c =>
                            c.StatusPagamento == EStatusPagamentoCartao.Baixado &&
                            (c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaTarifaAntt ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAdiantamento ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaSaldo ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaEstadia ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAbono ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAbastecimento ||
                             c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaRpa))
                        .Sum(c => c.ValorMovimentado);
                    
                    if (valoresCarga > 0)
                    {
                        var retornoSaldo =
                            new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App)
                                .ValidarSaldoCartao(StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario),
                                    viagem.IdEmpresa, valoresCarga - valorTransferencia, cartoesApp);

                        if (!retornoSaldo.Key)
                            throw new Exception(retornoSaldo.Value);
                    }
                }
            }

            // TODO: Verificar se vai funcionar
            _valoresViagem.AjustarValoresViagem(viagem);
            
            var validationResult = _viagemApp.Update(viagem);
            if (!validationResult.IsValid)
                return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToString(), null);

            if (@params.Pedagio != null)
            {
                var rotaApp = _rotaModeloApp;
                LocalizacaoDTO localizacao;
                
                if ((@params.StatusViagem == EStatusViagem.Baixada || @params.StatusViagem == EStatusViagem.Programada) && 
                    !string.IsNullOrWhiteSpace(@params.Placa) && 
                    @params.Pedagio.Fornecedor == FornecedorEnum.ViaFacil && 
                    (_parametrosGenericoService.GetParametro<bool?>(GLOBAL.HabilitarConsultaStatusVeiculoSemPararIntegracoes, 0) ?? false))
                {
                    var respostaConsulta = _valePedagioApp.GetStatusVeiculoSemParar(@params.Placa, @params.CNPJEmpresa);
                    if (respostaConsulta.VeiculoStatusSemParar != EStatusVeiculoSemParar.Ok )
                    {
                        return new Retorno<ViagemIntegrarResponseModel>(false, respostaConsulta.Mensagem, null);
                    }
                }
                
                //Caso informado uma rota padrão modelo
                if (@params.Pedagio.NomeRota != null || @params.Pedagio.IdRotaModelo.HasValue)
                {
                    var pedagioRotaModelo = rotaApp.GetByIdOrNomeRota(@params.Pedagio.IdRotaModelo ?? 0,@params.Pedagio.NomeRota,empresa.IdEmpresa);
                        
                    if(pedagioRotaModelo != null)
                    {
                        var localizacaoRotaModelo = rotaApp.SetarRetornoDestino(pedagioRotaModelo);
                        
                        //Reatribuição de valores do pedagio
                        @params.Pedagio.ValorPedagio = @params.Pedagio.Fornecedor == FornecedorEnum.Moedeiro || !empresa.PedagioTag ? pedagioRotaModelo.CustoRota : pedagioRotaModelo.CustoRotaTag;
                        @params.Pedagio.TipoVeiculo = pedagioRotaModelo.TipoVeiculo;
                        @params.Pedagio.Localizacoes = new List<LocalizacaoDTO>();

                        foreach (var item in localizacaoRotaModelo)
                        {
                            localizacao = new LocalizacaoDTO()
                            {
                                IbgeCidade = item.Ibge.ToInt(),
                                Latitude = item.Latitude,
                                Longitude = item.Longitude,
                            };
                                 
                            @params.Pedagio.Localizacoes.Add(localizacao);
                        }
                        //Reset para nome rota ter prioridade
                        @params.Pedagio.IdentificadorHistorico = null;
                        
                        //Atribuição do número de eixos
                        if (!string.IsNullOrWhiteSpace(@params.Placa))
                        {
                            var existVeiculo = _veiculoApp.PlacaExistente(@params.Placa,empresa.IdEmpresa);
                            if (!existVeiculo.Existente)
                                return new Retorno<ViagemIntegrarResponseModel>(false, "Placa não cadastrada.", null);

                            @params.Pedagio.QtdEixos = _veiculoApp.GetVeiculoPorPlaca(@params.Placa,empresa.IdEmpresa).QuantidadeEixos;

                            if (@params.Carretas != null)
                            {
                                foreach (var carreta in @params.Carretas)
                                {
                                    if (!_veiculoApp.VeiculoValidoIntegracao(carreta,empresa.IdEmpresa))
                                        return new Retorno<ViagemIntegrarResponseModel>(false, "Carreta não cadastrada.", null);

                                    @params.Pedagio.QtdEixos += _veiculoApp.GetVeiculoPorPlaca(carreta,empresa.IdEmpresa).QuantidadeEixos;
                                }
                            }

                            if (@params.CarretasViagemV2 != null)
                            {
                                foreach (var carretaV2 in @params.CarretasViagemV2)
                                {
                                    if(!_veiculoApp.VeiculoValidoIntegracao(carretaV2.Placa,empresa.IdEmpresa))
                                        return new Retorno<ViagemIntegrarResponseModel>(false, "Carreta não cadastrada.", null);

                                    @params.Pedagio.QtdEixos += _veiculoApp.GetVeiculoPorPlaca(@carretaV2.Placa,empresa.IdEmpresa).QuantidadeEixos;
                                }
                            }
                        }
                    }
                }

                validationResult.Add(_viagemApp.AdicionarAtualizarViagemRota(viagem.IdViagem, viagem.IdEmpresa, 
                    @params.Pedagio.Localizacoes, @params.Pedagio.IdentificadorHistorico, @params.Pedagio.Fornecedor ?? FornecedorEnum.Desabilitado, 
                    @params.Pedagio.TipoVeiculo ?? ETipoVeiculoPedagioEnum.Caminhao, @params.Pedagio.QtdEixos,
                    @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit));

                if(!validationResult.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToString(), null);
            }

            // Bloqueio para liberação do gestor
            var retornoLiberacaoGestor = _bloqueioGestorViagem.ValidarBloqueioGestor(@params, viagem,isApi ? EBloqueioOrigemTipo.API : EBloqueioOrigemTipo.Portal);

            var msgErroProcesso = string.Empty;

            if (!retornoLiberacaoGestor.Sucesso) msgErroProcesso = retornoLiberacaoGestor.Mensagem;

            var ciotResult = new DeclararCiotResult {Resultado = EResultadoDeclaracaoCiot.NaoHabilitado};
            var compraPedagio = new SolicitarCompraPedagioResponseDTO
            {
                Fornecedor = @params.Pedagio?.Fornecedor ?? _viagemApp.GetFornecedorViagemRota(viagem.IdViagem),
                Status = EResultadoCompraPedagio.NaoRealizado
            };
            
            ProcessoIntegracaoViagemDto mensagensProcesso;
            if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                mensagensProcesso = _integracaoMeioHomologadoViagem.CancelarViagem(viagem, cartoesApp,
                    ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, out ciotResult,
                    out compraPedagio, @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit);

                if (!mensagensProcesso.Sucesso)
                {
                    viagem.StatusViagem = viagemStatusBanco;
                    _viagemApp.Update(viagem);
                    return new Retorno<ViagemIntegrarResponseModel>(false, mensagensProcesso.MensagemViagem, null);
                }
            }
            else
            {
                mensagensProcesso = _integracaoMeioHomologadoViagem.RealizarIntegracoesViagem(@params, viagem,
                    cartoesApp, out ciotResult, out compraPedagio, cartaoVinculadoMotorista, cartaoVinculadoProprietario, 
                    retornoLiberacaoGestor, retificarCiot, atualizarCiot, isApi:isApi);

                if (!mensagensProcesso.Sucesso) msgErroProcesso += mensagensProcesso.MensagemViagem;
            }

            var retorno = RetornoViagem(viagem, ciotResult, compraPedagio, null,retornoLiberacaoGestor);
            if (string.IsNullOrWhiteSpace(retorno.CIOT.Mensagem)) retorno.CIOT.Mensagem = mensagensProcesso.MensagemCiot;
            if (string.IsNullOrWhiteSpace(retorno.Pedagio.Mensagem)) retorno.Pedagio.Mensagem = mensagensProcesso.MensagemPedagio;
            if (retorno.Eventos?.Any() == true)
            {
                var evento = retorno.Eventos.FirstOrDefault();
                if (evento.OperacaoCartao != null && string.IsNullOrWhiteSpace(evento.OperacaoCartao.Mensagem)) 
                    evento.OperacaoCartao.Mensagem = mensagensProcesso.MensagemCartao + " " + mensagensProcesso.MensagemPix;
            }

            return new Retorno<ViagemIntegrarResponseModel>(true, !string.IsNullOrWhiteSpace(msgErroProcesso) ? msgErroProcesso : mensagensProcesso.MensagemViagem, retorno);
        }

        private ValidationResult ValidarDocumentoFiscal(ViagemDocumentoFiscalModel documentoFiscal, int idEmpresa, out decimal numeroDocumento)
        {
            var validationResult = new ValidationResult();
            
            if (!decimal.TryParse(documentoFiscal.NumeroDocumento.ToString(CultureInfo.InvariantCulture), out numeroDocumento) || documentoFiscal.NumeroDocumento.ToString().Length > 10)
                return validationResult.Add($"Número do documento fiscal: {documentoFiscal.NumeroDocumento} inválido");

            if (documentoFiscal.PesoSaida.ToString("N3", CultureInfo.InvariantCulture).Length > 13)
                return validationResult.Add($"Peso de saída: {documentoFiscal.PesoSaida} inválido");
            
            if (documentoFiscal.Chave?.Length > 150)
                return validationResult.Add($"Tamanho da chave: {documentoFiscal.PesoSaida} excede o comprimento máximo de 150");

            if (!Enum.IsDefined(typeof(ETipoDocumento), documentoFiscal.TipoDocumento))
                return validationResult.Add($"Tipo de documento fiscal: {documentoFiscal.TipoDocumento} inválido.");

            if(documentoFiscal.IdClienteOrigem.HasValue && !_clienteApp.All().Any(x => x.IdCliente == documentoFiscal.IdClienteOrigem && x.IdEmpresa == idEmpresa)) 
                return validationResult.Add($"Não foi encontrado IdClienteOrigem {documentoFiscal.IdClienteOrigem} na base de dados.");
                    
            if(documentoFiscal.IdClienteDestino.HasValue && !_clienteApp.All().Any(x => x.IdCliente == documentoFiscal.IdClienteDestino && x.IdEmpresa == idEmpresa))
                return validationResult.Add($"Não foi encontrado IdClienteDestino {documentoFiscal.IdClienteDestino} na base de dados.");

            return validationResult;
        }

        public Retorno<ViagemIntegrarResponseModel> AlterarV3(ViagemIntegrarRequestModel @params, bool isApi)
        {
            if (string.IsNullOrEmpty(@params.CNPJEmpresa))
                @params.CNPJEmpresa = @params.CNPJAplicacao;
            
            if(@params.IdViagem == null)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Id Viagem não informado.", null);
            
            var retificarCiot = false;
            var atualizarCiot = false;

            #region Valida NumeroControle

            var viagemQuery = _viagemApp.Find(x => x.IdViagem == @params.IdViagem.Value);

            if (!viagemQuery.Any())
                return new Retorno<ViagemIntegrarResponseModel>(false,
                    $"Não foi encontrada uma viagem com o IdViagem {@params.IdViagem.Value} na base de dados",
                    null);

            var numerosControleRepetidos = @params.ViagemEventos
                .Where(x => x.NumeroControle != null && (x.IdViagemEvento == null || x.IdViagemEvento <= 0))
                .Select(x => x.NumeroControle)
                .GroupBy(x => x)
                .Where(g => g.Count() > 1)
                .Select(y => y.Key)
                .ToList();

            if (!numerosControleRepetidos.Any())
            {
                var numerosControleBanco = viagemQuery
                    .Include(x => x.ViagemEventos)
                    .Select(x => x.ViagemEventos)
                    .Select(y => y.Select(z => z.NumeroControle).ToList())
                    .FirstOrDefault();

                if (numerosControleBanco != null && numerosControleBanco.Any())
                {
                    var numerosControleNovos = @params.ViagemEventos
                        .Where(x => (x.IdViagemEvento == null || x.IdViagemEvento <= 0) && x.NumeroControle != null)
                        .Select(x => x.NumeroControle).ToList();

                    numerosControleRepetidos = numerosControleNovos
                        .Where(numeroControleNovo => numerosControleBanco.Contains(numeroControleNovo)).ToList();
                }
            }

            if (numerosControleRepetidos.Any())
                return new Retorno<ViagemIntegrarResponseModel>(false,
                    numerosControleRepetidos.Count == 1
                        ? $"Viagem com número de controle do evento {numerosControleRepetidos.First()} já cadastrado."
                        : $"Viagem com número de controle do eventos {string.Join(", ", numerosControleRepetidos)} já cadastrados."
                    , null);

            #endregion

            var viagem = _viagemApp.Get(@params.IdViagem.Value);
            
            if (viagem.IdEmpresa != _userIdentity.IdEmpresa)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Viagem não pertence à empresa do usuário.", null);

            var viagemStatusBanco = viagem.StatusViagem;
            
            if (viagem.DataAtualizacao.HasValue && @params.DataAtualizacao.HasValue && 
                @params.DataAtualizacao < viagem.DataAtualizacao)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente", null);
            
            int? idClienteOrigem = null;
            int? idClienteDestino = null;
            
            if (!string.IsNullOrWhiteSpace(@params.CPFCNPJClienteOrigem))
                idClienteOrigem = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteOrigem, viagem.IdEmpresa);
            if (!string.IsNullOrWhiteSpace(@params.CPFCNPJClienteDestino))
                idClienteDestino = _clienteApp.GetIdPorCpfCnpj(@params.CPFCNPJClienteDestino, viagem.IdEmpresa);
            
            var permiteAlterarViagem = PermitirAlterarViagemV3(@params, viagem, idClienteOrigem, idClienteDestino, _ciotV3App);
            
            if (!permiteAlterarViagem.IsValid)
                return new Retorno<ViagemIntegrarResponseModel>(false, permiteAlterarViagem.ToString(), null);

            if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                if(@params.StatusViagem != EStatusViagem.Cancelada)
                    return new Retorno<ViagemIntegrarResponseModel>(false,
                        "Viagem cancelada, não é permitido realizar alterações em viagens com este status.", null);
                else
                {
                    var ciot = _ciotV3App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);
                    var pedagio = _viagemApp.GetStatusPedagio(viagem);
                    
                    return new Retorno<ViagemIntegrarResponseModel>(true, "Viagem já estava cancelada", 
                        RetornoViagem(viagem, ciot, pedagio, null,null));
                }
            }
            
            if (@params.Pedagio != null && @params.ValorPedagio > 0 == false && @params.Pedagio.ValorPedagio > 0 == false && 
                !@params.Pedagio.IdentificadorHistorico.HasValue && @params.Pedagio.Localizacoes?.Any() == true && @params.Pedagio.QtdEixos < 2)
                return new Retorno<ViagemIntegrarResponseModel>(false, "Quantidade de eixos inválido, deve ser indicado um valor igual ou maior que 2.", null);
            
            if (!string.IsNullOrEmpty(@params.DocumentoCliente) && @params.DocumentoCliente.Length > 100)
                return new Retorno<ViagemIntegrarResponseModel>(false, @"O campo DocumentoCliente não pode ter mais de 100 caracteres.", null);
            
            var ciotIntegrado = _ciotV2App.GetCiotResult(viagem.IdViagem, viagem.IdEmpresa);

            if (ciotIntegrado != null)
            {
                var validacoesParaRetificacao = new CiotRetificacao(_ciotV2App, _cadastrosApp).ValidarDadosRetificacao(viagem, @params);
                
                if (!validacoesParaRetificacao.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false, validacoesParaRetificacao.Errors.FirstOrDefault()?.Message, null);
            }

            //TODO: Melhorar o fluxo de alteração de status
            //ocorreu em um cenário de entrar evento com status baixado pra carregar no cartão e statusviagem cancelado, ocorreu uma carga no cartão
            if (@params.StatusViagem == EStatusViagem.Cancelada && @params.ViagemEventos != null)
                foreach (var evento in @params.ViagemEventos)
                    evento.Status = EStatusViagemEvento.Cancelado;

            var empresa = _empresaApp.Get(viagem.IdEmpresa, null);
            var validaPagamentoFrete = empresa.ValidacaoPagFrete;

            //Integração de viagem pelo postman e depois salvo pela tela
            if (validaPagamentoFrete && @params.PesoSaida.HasValue && @params.PesoSaida <= 0)
                return new Retorno<ViagemIntegrarResponseModel>(false, @"Peso de saida deve ser maior que 0.",
                    null);

            if (@params.HabilitarDeclaracaoCiot && _parametrosApp.ValidaCnpjCpfProprietarioNaViagem() &&
                !_proprietarioApp.All().Any(x =>
                    x.IdEmpresa == viagem.IdEmpresa && x.CNPJCPF == viagem.CPFCNPJProprietario))
                return new Retorno<ViagemIntegrarResponseModel>(false,
                    $"O proprietário {@params.CPFCNPJProprietario.ToCpfOrCnpj()} não foi encontrado na base de dados",
                    null);
            
            if(@params.Carretas?.Any() == true)
                for (var i = 0; @params.Carretas.Count > i; i++)
                {
                    @params.Carretas[i] = @params.Carretas[i].RemoveSpecialCharacters().Trim().ToUpper();
                    if (@params.Carretas[i].Length != 7)
                        return new Retorno<ViagemIntegrarResponseModel>(false, "Os itens de Carretas devem conter 7 caracteres", null);
                }
            
            if (idClienteOrigem.HasValue && viagem.IdClienteOrigem != idClienteOrigem.Value)
            {
                viagem.IdClienteOrigem = idClienteOrigem.Value;
                retificarCiot = true;
            }
            
            if (idClienteDestino.HasValue && viagem.IdClienteDestino != idClienteDestino.Value)
            {
                viagem.IdClienteDestino = idClienteDestino.Value;
                retificarCiot = true;
            }

            Filial filial = null;

            if (!string.IsNullOrEmpty(@params.CNPJFilial)) filial = _filialApp.Get(@params.CNPJFilial);

            if (!string.IsNullOrWhiteSpace(@params.NumeroDocumento))
                viagem.NumeroDocumento = @params.NumeroDocumento;

            if (!string.IsNullOrWhiteSpace(@params.NumeroNota))
                viagem.NumeroNota = @params.NumeroNota;

            if (!string.IsNullOrWhiteSpace(@params.Produto))
                viagem.Produto = @params.Produto.ValueLimited(100);

            if (@params.DataEmissao.HasValue)
                viagem.DataEmissao = @params.DataEmissao;

            if (@params.DataColeta.HasValue && viagem.DataColeta != @params.DataColeta)
            {
                viagem.DataColeta = @params.DataColeta;
                retificarCiot = true;
            }

            if (@params.DataPrevisaoEntrega.HasValue && @params.DataPrevisaoEntrega.Value != viagem.DataPrevisaoEntrega)
            {
                viagem.DataPrevisaoEntrega = @params.DataPrevisaoEntrega.Value;
                retificarCiot = true;
            }

            if (@params.Quantidade > 0)
                viagem.Quantidade = @params.Quantidade;

            if (filial != null)
                viagem.IdFilial = filial.IdFilial;

            if (!string.IsNullOrWhiteSpace(@params.RazaoSocialFilial))
                viagem.RazaoSocialFilial = @params.RazaoSocialFilial;

            //Campos adicinados durante a homologação...
            //Não foi criado a integração de filial sendo assim para não atrapalhar o processo de homologação adicionei esses campos
            if (!string.IsNullOrWhiteSpace(@params.CNPJFilial))
                viagem.CNPJFilial = @params.CNPJFilial;

            if (!string.IsNullOrWhiteSpace(@params.CPFMotorista) && @params.CPFMotorista.Trim() != viagem.CPFMotorista)
            {
                viagem.CPFMotorista = @params.CPFMotorista.Trim();

                if (string.IsNullOrWhiteSpace(viagem.NomeMotorista))
                {
                    viagem.NomeMotorista = _motoristaService.GetAllByIdEmpresa(viagem.IdEmpresa)
                        .Where(m => m.CPF == @params.CPFMotorista)
                        .Select(m => m.Nome)
                        .FirstOrDefault();

                    if (string.IsNullOrWhiteSpace(viagem.NomeMotorista))
                        viagem.NomeMotorista = _usuarioService.GetByCpfQuery(viagem.CPFMotorista)
                            .Select(u => u.Nome)
                            .FirstOrDefault();
                }
            }

            if (@params.IdCarga.HasValue)
            {
                if (@params.IdCarga <= 0)
                    return new Retorno<ViagemIntegrarResponseModel>(false, @"Código da carga inválido.", null);
                viagem.ViagemCargas = new List<ViagemCarga>
                {
                    new ViagemCarga
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        IdCarga = @params.IdCarga.Value
                    }
                };
            }

            if (@params.HabilitarDeclaracaoCiot && viagem.NaturezaCarga == null &&
                (@params.NaturezaCarga == null || @params.NaturezaCarga == 0))
                return new Retorno<ViagemIntegrarResponseModel>(false,
                    @"Natureza da carga é obrigatória ao integrar viagem com recurso de CIOT habilitado.", null);

            if (!string.IsNullOrWhiteSpace(@params.Placa) && @params.Placa.Trim() != viagem.Placa)
            {
                viagem.Placa = @params.Placa.RemoveSpecialCharacters().Trim().ToUpper();
                if (viagem.Placa.Length != 7)
                    return new Retorno<ViagemIntegrarResponseModel>(false, "O campo Placa deve conter 7 caracteres", null);
                retificarCiot = true;
            }

            if (@params.Carretas?.Any() == true || @params.CarretasViagemV2?.Any() == true)
                retificarCiot = ProcessarCarretas(@params, viagem, retificarCiot);
            
            _viagemApp.AjustarProprietario(viagem);

            if (!string.IsNullOrWhiteSpace(@params.DocumentoCliente))
            {
                viagem.DocumentoCliente = @params.DocumentoCliente;
                atualizarCiot = true;
            }

            if (!string.IsNullOrWhiteSpace(@params.NumeroDocumento))
                viagem.NumeroDocumento = @params.NumeroDocumento;

            if (@params.ViagemEventos.Any())
            {
                if (@params.ViagemEventos.Any(ve => ve.ValorPagamento < 0))
                    return new Retorno<ViagemIntegrarResponseModel>(false,"Valor informado não pode ser negativo.", null);
                
                if (@params.ViagemEventos.Any(ve => ve.ValorPagamento == 0))
                    return new Retorno<ViagemIntegrarResponseModel>(false,"Valor pagamento não informado.", null);
                
                if(@params.ViagemEventos.Any(ve => ve.Status == EStatusViagemEvento.Cancelado && !(ve.IdViagemEvento > 0)))
                    return new Retorno<ViagemIntegrarResponseModel>(false,$"Não é permitido integrar um novo evento com o status {EStatusViagemEvento.Cancelado.GetDescription()}.", null);

                if (@params.ViagemEventos.Any(o => !o.IdViagemEvento.HasValue))
                {
                    atualizarCiot = true;
                    retificarCiot = true;
                }
                else
                {
                    var eventosPersistidos = viagem.ViagemEventos;
                    var eventosEntrada = @params.ViagemEventos;

                    foreach (var eventoEntrada in eventosEntrada)
                    {
                        var eventoPersistido = eventosPersistidos.FirstOrDefault(o => o.IdViagemEvento == eventoEntrada.IdViagemEvento);

                        if (eventoPersistido != null)
                        {
                            if (eventoPersistido.ValorPagamento != eventoEntrada.ValorPagamento && 
                                eventoPersistido.Status.In(EStatusViagemEvento.Aberto, EStatusViagemEvento.Bloqueado))
                            {
                                atualizarCiot = true;
                                retificarCiot = true;
                            }

                            if (eventoPersistido.Status != eventoEntrada.Status && eventoEntrada.Status == EStatusViagemEvento.Cancelado)
                            {
                                atualizarCiot = true;
                                retificarCiot = true;
                            }

                            if (eventoPersistido.Status.In(EStatusViagemEvento.Aberto, EStatusViagemEvento.Bloqueado) && eventoEntrada.Status == EStatusViagemEvento.Baixado)
                                atualizarCiot = true;
                        }
                    }
                }
            }
            
            if (@params.DocumentosFiscais != null && @params.DocumentosFiscais.Any())
                foreach (var documentoFiscal in @params.DocumentosFiscais)
                {
                    decimal numeroDocumento;
                    var validacaoDocumento = ValidarDocumentoFiscal(documentoFiscal, viagem.IdEmpresa, out numeroDocumento);
                    if(!validacaoDocumento.IsValid)
                        return new Retorno<ViagemIntegrarResponseModel>(false, validacaoDocumento.ToString(), null);
                    
                    if (documentoFiscal.IdViagemDocumentoFiscal.HasValue)
                    {
                        var documentoFiscalPersistido = viagem.ViagemDocumentosFiscais.FirstOrDefault(x => x.IdViagemDocumentoFiscal == documentoFiscal.IdViagemDocumentoFiscal);
                        if (documentoFiscalPersistido == null)
                            return new Retorno<ViagemIntegrarResponseModel>(false, $"Não foi encontrado documento fiscal de id {documentoFiscal.IdViagemDocumentoFiscal}", null);

                        documentoFiscalPersistido.IdClienteOrigem = documentoFiscal.IdClienteOrigem ?? documentoFiscalPersistido.IdClienteOrigem;
                        documentoFiscalPersistido.IdClienteDestino = documentoFiscal.IdClienteDestino ?? documentoFiscalPersistido.IdClienteDestino;
                        documentoFiscalPersistido.NumeroDocumento = numeroDocumento;
                        documentoFiscalPersistido.Serie = documentoFiscal.Serie;
                        documentoFiscalPersistido.Chave = documentoFiscal.Chave;
                        documentoFiscalPersistido.PesoSaida = documentoFiscal.PesoSaida;
                        documentoFiscalPersistido.Valor = documentoFiscal.Valor;
                        documentoFiscalPersistido.TipoDocumento = documentoFiscal.TipoDocumento;
                    }
                    else
                    {
                        
                        if (!documentoFiscal.IdClienteOrigem.HasValue || !documentoFiscal.IdClienteDestino.HasValue)
                        {
                            documentoFiscal.IdClienteOrigem = viagem.IdClienteOrigem;
                            documentoFiscal.IdClienteDestino = viagem.IdClienteDestino;
                        }
                        
                        viagem.ViagemDocumentosFiscais.Add(new ViagemDocumentoFiscal
                        {
                            Valor = documentoFiscal.Valor,
                            PesoSaida = documentoFiscal.PesoSaida,
                            NumeroDocumento = numeroDocumento,
                            Serie = documentoFiscal.Serie,
                            Chave = documentoFiscal.Chave,
                            TipoDocumento = documentoFiscal.TipoDocumento,
                            IdViagem = viagem.IdViagem,
                            IdClienteOrigem = documentoFiscal.IdClienteOrigem,
                            IdClienteDestino = documentoFiscal.IdClienteDestino
                        });
                    }
                }
            
            var valorPedagio = @params.ValorPedagio ?? @params.Pedagio?.ValorPedagio;
            if (valorPedagio > 0 && viagem.ValorPedagio != valorPedagio)
            {
                viagem.ValorPedagio = valorPedagio.Value;
                retificarCiot = true;
            }
            
            if (@params.PesoSaida.HasValue && viagem.PesoSaida != @params.PesoSaida)
            {
                viagem.PesoSaida = @params.PesoSaida;
                retificarCiot = true;
            }

            if (@params.NaturezaCarga.HasValue && viagem.NaturezaCarga != @params.NaturezaCarga)
            {
                viagem.NaturezaCarga = @params.NaturezaCarga;
                retificarCiot = true;
            }

            if (@params.IRRPF > 0 && @params.IRRPF != viagem.IRRPF)
            {
                viagem.IRRPF = @params.IRRPF;
                atualizarCiot = true;
            }

            if (@params.INSS > 0 && @params.INSS != viagem.INSS)
            {
                viagem.INSS = @params.INSS;
                atualizarCiot = true;
            }

            if (@params.SESTSENAT > 0 && @params.SESTSENAT != viagem.SESTSENAT)
            {
                viagem.SESTSENAT = @params.SESTSENAT;
                atualizarCiot = true;
            }

            if (@params.CodigoTipoCarga.HasValue && viagem.CodigoTipoCarga != @params.CodigoTipoCarga)
                viagem.CodigoTipoCarga = @params.CodigoTipoCarga;

            if (!string.IsNullOrEmpty(@params.CepOrigem) && viagem.CepOrigem != @params.CepOrigem)
                viagem.CepOrigem = @params.CepOrigem;

            if (!string.IsNullOrEmpty(@params.CepDestino) && viagem.CepDestino != @params.CepDestino)
                viagem.CepDestino = @params.CepDestino;

            if (@params.DistanciaViagem.HasValue && viagem.DistanciaViagem != @params.DistanciaViagem)
                viagem.DistanciaViagem = @params.DistanciaViagem;
            
            if (@params.DadosAntt != null)
            {
                if (@params.DadosAntt.AltoDesempenho.HasValue && viagem.AltoDesempenho != @params.DadosAntt.AltoDesempenho.Value)
                    viagem.AltoDesempenho = @params.DadosAntt.AltoDesempenho.Value;

                if (@params.DadosAntt.DestinacaoComercial.HasValue && viagem.DestinacaoComercial != @params.DadosAntt.DestinacaoComercial.Value)
                    viagem.DestinacaoComercial = @params.DadosAntt.DestinacaoComercial.Value;

                if (@params.DadosAntt.FreteRetorno.HasValue && viagem.FreteRetorno != @params.DadosAntt.FreteRetorno.Value)
                    viagem.FreteRetorno = @params.DadosAntt.FreteRetorno.Value;
                    

                if (viagem.FreteRetorno == false)
                {
                    viagem.CepRetorno = null;
                    viagem.DistanciaRetorno = null;
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(@params.DadosAntt.CepRetorno) && viagem.CepRetorno != @params.DadosAntt.CepRetorno)
                        viagem.CepRetorno = @params.DadosAntt.CepRetorno;

                    if (@params.DadosAntt.DistanciaRetorno.HasValue && viagem.DistanciaRetorno != @params.DadosAntt.DistanciaRetorno.Value)
                        viagem.DistanciaRetorno = @params.DadosAntt.DistanciaRetorno;
                }
                
            }

            if (viagem.StatusViagem != EStatusViagem.Cancelada && viagem.StatusViagem != EStatusViagem.Baixada)
            {
                if (!viagem.HabilitarDeclaracaoCiot && @params.HabilitarDeclaracaoCiot)
                    viagem.HabilitarDeclaracaoCiot = true;

                var ciotRejeitado = viagem.HabilitarDeclaracaoCiot &&
                                    viagem.ResultadoDeclaracaoCiot != EResultadoDeclaracaoCiot.Sucesso &&
                                    viagem.DeclaracaoCiot == null;

                if ((!viagem.HabilitarDeclaracaoCiot || ciotRejeitado) && @params.DadosPagamento != null)
                {
                    if (viagem.ViagemPagamentoConta == null)
                        viagem.ViagemPagamentoConta = new ViagemPagamentoConta();

                    if (viagem.FormaPagamento == EViagemFormaPagamento.Indefinido)
                        viagem.FormaPagamento = EViagemFormaPagamento.Outros;

                    if (@params.DadosPagamento.FormaPagamento != EViagemFormaPagamento.Indefinido &&
                        !viagem.ViagemEventos.Any(x => x.Status == EStatusViagemEvento.Baixado))
                        viagem.FormaPagamento = @params.DadosPagamento.FormaPagamento;

                    if (string.IsNullOrWhiteSpace(viagem.ViagemPagamentoConta.CpfCnpjConta))
                        viagem.ViagemPagamentoConta.CpfCnpjConta = viagem.CPFCNPJProprietario;
                    else
                        viagem.ViagemPagamentoConta.CpfCnpjConta = @params.CPFCNPJProprietario; 

                    var pagamentosEmConta = new List<EViagemFormaPagamento>
                    {
                        EViagemFormaPagamento.ContaCorrente, EViagemFormaPagamento.ContaPagamento,
                        EViagemFormaPagamento.ContaPoupanca
                    };

                    if (pagamentosEmConta.Contains(viagem.FormaPagamento))
                    {
                        if (!string.IsNullOrWhiteSpace(@params.DadosPagamento.CodigoBacen))
                            viagem.ViagemPagamentoConta.CodigoBacenBanco = @params.DadosPagamento.CodigoBacen;

                        if (!string.IsNullOrWhiteSpace(@params.DadosPagamento.Agencia))
                            viagem.ViagemPagamentoConta.Agencia = @params.DadosPagamento.Agencia;

                        if (!string.IsNullOrWhiteSpace(@params.DadosPagamento.Conta))
                            viagem.ViagemPagamentoConta.Conta = @params.DadosPagamento.Conta;
                    }
                    else
                    {
                        viagem.ViagemPagamentoConta.CodigoBacenBanco = null;
                        viagem.ViagemPagamentoConta.Agencia = null;
                        viagem.ViagemPagamentoConta.Conta = null;
                    }
                }
            }

            //Seta todos os eventos relacionados ao pagamento de frete
            
            // TODO: Verificar se vai funcionar
            _propriedadesPagamentoFreteViagem.SetarPropriedadesPagamentoFrete(@params, viagem, false);
            
            var isValidToCrud = _viagemApp.IsValidToCrud(viagem, EProcesso.Update);
            if (!isValidToCrud.IsValid)
                return new Retorno<ViagemIntegrarResponseModel>(false, isValidToCrud.ToFormatedMessage(), null);

            // TODO: Verificar se vai funcionar
            _webHookViagem.SetarWebhooks(@params, viagem);

            var cartaoVinculadoProprietario = new CartaoVinculadoPessoaListResponse();
            var cartaoVinculadoMotorista = new CartaoVinculadoPessoaListResponse();
            
            var cartoesApp = CartoesApp.CreateByEmpresa(_cartoesAppFactoryDependencies, viagem.IdEmpresa, @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit);

            if (viagem.ViagemEventos.Any(c => c.HabilitarPagamentoCartao))
            {
                var listaEventos = viagem.ViagemEventos.Select(c => c.TipoEventoViagem).ToList();

                var retornoCartoes = new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App).ValidarCartaoMeioHomologado(StringExtension.OnlyNumbers(viagem.CPFMotorista),
                    StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario), viagem.IdEmpresa, cartoesApp,
                    out cartaoVinculadoMotorista, out cartaoVinculadoProprietario, listaEventos, buscarCartoesBloqueados:true);

                if (!retornoCartoes.Key)
                    throw new Exception(retornoCartoes.Value);

                var eventosCancelados = viagem.ViagemEventos.Where(c => c.Status == EStatusViagemEvento.Cancelado)
                    .Select(x => x.IdViagemEvento).ToList();

                if (eventosCancelados.Any())
                {
                    var transacaoService = _transacaoCartaoApp;

                    var transacoes = transacaoService.Find(x => eventosCancelados.Contains(x.IdViagemEvento.Value)
                                                                && x.StatusPagamento ==
                                                                EStatusPagamentoCartao.Baixado)
                        .Select(x => new {x.TipoProcessamentoCartao, x.ValorMovimentado})
                        .ToList();

                    var valorTransferencia = transacoes.Where(c =>
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaTarifaAntt ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaAdiantamento ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaSaldo ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.TransferenciaAbono)
                        .Sum(c => c.ValorMovimentado);

                    var valoresEstornadosTransferencia = transacoes.Where(c =>
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoTransferenciaTarifaAntt ||
                            c.TipoProcessamentoCartao ==
                            ETipoProcessamentoCartao.EstornoTransferenciaAdiantamento ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoTransferenciaSaldo ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoTransferenciaAbono)
                        .Sum(c => c.ValorMovimentado);

                    valorTransferencia -= valoresEstornadosTransferencia;

                    if (valorTransferencia > 0)
                    {
                        var retornoSaldo = new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App).ValidarSaldoCartao(StringExtension.OnlyNumbers(viagem.CPFMotorista),
                            viagem.IdEmpresa, valorTransferencia, cartoesApp);

                        if (!retornoSaldo.Key)
                            throw new Exception(retornoSaldo.Value);
                    }

                    var valoresCarga = transacoes.Where(c =>
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaTarifaAntt ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAdiantamento ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaSaldo ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.CargaAbono)
                        .Sum(c => c.ValorMovimentado);

                    var valoresEstornadosCarga = transacoes.Where(c =>
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoTarifaAntt ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoAdiantamento ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoSaldo ||
                            c.TipoProcessamentoCartao == ETipoProcessamentoCartao.EstornoAbono)
                        .Sum(c => c.ValorMovimentado);

                    valoresCarga -= valoresEstornadosCarga;

                    if (valoresCarga > 0)
                    {
                        var retornoSaldo = new CartaoViagem(_versaoAntt, _parametrosApp, _viagemApp, _ciotV2App, _ciotV3App).ValidarSaldoCartao(
                            StringExtension.OnlyNumbers(viagem.CPFCNPJProprietario), viagem.IdEmpresa,
                            valoresCarga - valorTransferencia, cartoesApp);

                        if (!retornoSaldo.Key)
                            throw new Exception(retornoSaldo.Value);
                    }
                }
            }

            var msgErroProcesso = string.Empty;
            var mensagemAviso = string.Empty;
            var statusViagemAntesAlteracao = viagem.StatusViagem;

            // TODO: Verificar se vai funcionar
            _valoresViagem.AjustarValoresViagem(viagem);
            
            
            var validationResult = _viagemApp.Update(viagem);
            if (!validationResult.IsValid)
                return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToString(), null);

            if (@params.Pedagio != null)
            {
                validationResult.Add(_viagemApp.AdicionarAtualizarViagemRota(viagem.IdViagem, viagem.IdEmpresa, 
                    @params.Pedagio.Localizacoes, @params.Pedagio.IdentificadorHistorico, @params.Pedagio.Fornecedor ?? FornecedorEnum.Desabilitado, 
                    @params.Pedagio.TipoVeiculo ?? ETipoVeiculoPedagioEnum.Caminhao, @params.Pedagio.QtdEixos,
                    @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit));

                if(!validationResult.IsValid)
                    return new Retorno<ViagemIntegrarResponseModel>(false, validationResult.ToString(), null);
            }

            if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                if (viagem.ViagemEventos.All(o => o.Status == EStatusViagemEvento.Cancelado) &&
                    viagem.StatusViagem != statusViagemAntesAlteracao)
                    mensagemAviso = "Viagem cancelada devido a todas as parcelas estarem canceladas.";

                var declaracaoCiotPersistida = _ciotV3App.ObterDeclaracaoCiot(viagem.IdViagem);

                if (declaracaoCiotPersistida?.TipoDeclaracao == ETipoDeclaracao.Padrao)
                {
                    var resultadoCancelamento = _ciotV3App.CancelarCiot(viagem);

                    if (!resultadoCancelamento.Sucesso)
                        _ciotV3App.EncerrarCiotPadrao(viagem, declaracaoCiotPersistida);
                }
            }

            // Bloqueio para liberação do gestor
            var retornoLiberacaoGestor = _bloqueioGestorViagem.ValidarBloqueioGestor(@params, viagem, isApi ? EBloqueioOrigemTipo.API : EBloqueioOrigemTipo.Portal);


            if (!retornoLiberacaoGestor.Sucesso) msgErroProcesso = retornoLiberacaoGestor.Mensagem;

            var ciotResult = new DeclararCiotResult {Resultado = EResultadoDeclaracaoCiot.NaoHabilitado};
            var compraPedagio = new SolicitarCompraPedagioResponseDTO
            {
                Fornecedor = @params.Pedagio?.Fornecedor ?? _viagemApp.GetFornecedorViagemRota(viagem.IdViagem),
                Status = EResultadoCompraPedagio.NaoRealizado
            };

            ProcessoIntegracaoViagemDto mensagensProcesso;
            if (viagem.StatusViagem == EStatusViagem.Cancelada)
            {
                mensagensProcesso =
                    _integracaoMeioHomologadoViagem.CancelarViagem(viagem, cartoesApp, ComportamentoCancelamentoViagemComPedagioMoedeiroConfirmado.Falha_BloqueioTotal, 
                        out ciotResult, out compraPedagio, @params.DocumentoUsuarioAudit, @params.NomeUsuarioAudit);
                
                if (!mensagensProcesso.Sucesso)
                {
                    viagem.StatusViagem = viagemStatusBanco;
                    _viagemApp.Update(viagem);
                    return new Retorno<ViagemIntegrarResponseModel>(false, mensagensProcesso.MensagemViagem, null);
                }
            }
            else
            {
                mensagensProcesso =
                    _integracaoMeioHomologadoViagem.RealizarIntegracoesViagem(@params, viagem, cartoesApp,
                        out ciotResult, out compraPedagio, cartaoVinculadoMotorista, cartaoVinculadoProprietario, retornoLiberacaoGestor, retificarCiot, atualizarCiot);
            }
            
            var retorno = RetornoViagem(viagem, ciotResult, compraPedagio, mensagemAviso,retornoLiberacaoGestor);

            retorno.CIOT.Mensagem = string.IsNullOrWhiteSpace(retorno.CIOT.Mensagem) ? mensagensProcesso.MensagemCiot : retorno.CIOT.Mensagem;
            retorno.Pedagio.Mensagem = string.IsNullOrWhiteSpace(retorno.Pedagio.Mensagem) ? mensagensProcesso.MensagemPedagio : retorno.Pedagio.Mensagem;
            if(retorno.Eventos?.Any() == true)
                retorno.Eventos.FirstOrDefault().OperacaoCartao.Mensagem = string.IsNullOrWhiteSpace(retorno.Eventos.FirstOrDefault().OperacaoCartao.Mensagem) 
                    ? mensagensProcesso.MensagemCartao 
                    : retorno.Eventos.FirstOrDefault().OperacaoCartao.Mensagem; 
            
            return new Retorno<ViagemIntegrarResponseModel>(true, !string.IsNullOrWhiteSpace(msgErroProcesso) ? msgErroProcesso : mensagensProcesso.MensagemViagem , retorno);
        }

        private bool ProcessarCarretas(ViagemIntegrarRequestModel @params, Viagem viagem, bool retificarCiot)
        {
            var carretasPersistidas = viagem.ViagemCarretas.ToList();
            var placasCarretasPersistidas = carretasPersistidas.Select(o => o.Placa).ToList();

            List<string> carratasParaRemover;
            List<string> carretasParaAdicionar;

            if (viagem.ViagemCarretas != null && viagem.ViagemCarretas.Any())
            {
                var validation = _viagemApp.RemoverPlacasDasCarretas(viagem.IdViagem);
                if (!validation.IsValid)
                    throw new Exception(validation.ToString());
            }

            viagem.ViagemCarretas = new List<ViagemCarreta>();

            if (@params.CarretasViagemV2 != null)
            {
                var placasCarretasRequest = @params.CarretasViagemV2.Select(o => o.Placa).ToList();

                carratasParaRemover = placasCarretasPersistidas.Except(placasCarretasRequest).ToList();
                carretasParaAdicionar = placasCarretasRequest.Except(placasCarretasPersistidas).ToList();

                if (carratasParaRemover.Any() || carretasParaAdicionar.Any())
                    retificarCiot = true;

                foreach (var paramsCarreta in @params.CarretasViagemV2)
                    viagem.ViagemCarretas.Add(new ViagemCarreta
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        Placa = paramsCarreta.Placa,
                        Rntrc = paramsCarreta.Rntrc,
                        IdViagem = viagem.IdViagem
                    });
            }
            else
            {
                var placasCarretasRequest = @params.Carretas;

                carratasParaRemover = placasCarretasPersistidas.Except(placasCarretasRequest).ToList();
                carretasParaAdicionar = placasCarretasRequest.Except(placasCarretasPersistidas).ToList();

                if (carratasParaRemover.Any() || carretasParaAdicionar.Any())
                    retificarCiot = true;

                foreach (var paramsCarreta in @params.Carretas)
                    viagem.ViagemCarretas.Add(new ViagemCarreta
                    {
                        IdEmpresa = viagem.IdEmpresa,
                        Placa = paramsCarreta,
                        Rntrc = string.Empty,
                        IdViagem = viagem.IdViagem
                    });
            }

            return retificarCiot;
        }

        private ValidationResult PermitirAlterarViagemV3(ViagemIntegrarRequestModel request, Viagem viagemPersistida, int? idClienteOrigem, int? idClienteDestino, ICiotV3App ciotV3App)
        {
            var validationResult = new ValidationResult();
            
            if (viagemPersistida.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraSolicitada ||
                viagemPersistida.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada)
            {
                if (!string.IsNullOrEmpty(request.CPFCNPJProprietario) && request.CPFCNPJProprietario != viagemPersistida.CPFCNPJProprietario)
                    return validationResult.Add($"Não é possível alterar o proprietário da viagem que possui Compra de pedágio {viagemPersistida.ResultadoCompraPedagio.GetDescription()}.", EFaultType.Error);
                    
                if (!string.IsNullOrEmpty(request.CPFMotorista) && request.CPFMotorista != viagemPersistida.CPFMotorista)
                    return validationResult.Add($"Não é possível alterar o CPF do motorista de uma viagem que possui Compra de pedágio {viagemPersistida.ResultadoCompraPedagio.GetDescription()}.", EFaultType.Error);
            }
            
            if (viagemPersistida.DeclaracaoCiot != null)
            {
                if (viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Declarado ||
                    viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.DeclaradoContigencia || 
                    viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                {
                    if (!string.IsNullOrEmpty(request.CPFCNPJProprietario) && request.CPFCNPJProprietario != viagemPersistida.CPFCNPJProprietario)
                        return validationResult.Add("Não é possível alterar o proprietário da viagem que possui CIOT integrado.", EFaultType.Error);
                    
                    if (request.RNTRC.HasValue && request.RNTRC != viagemPersistida.RNTRC)
                        return validationResult.Add("Não é possível alterar o RNTRC da viagem que possui CIOT integrado.", EFaultType.Error);

                    if (viagemPersistida.DeclaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Padrao)
                    {
                        if (idClienteOrigem.HasValue && idClienteOrigem != viagemPersistida.IdClienteOrigem)
                            return validationResult.Add("Não é possível alterar o CPF/CNPJ do cliente de origem da viagem que possui CIOT integrado.", EFaultType.Error);

                        if (idClienteDestino.HasValue && idClienteDestino != viagemPersistida.IdClienteDestino)
                            return validationResult.Add("Não é possível alterar o CPF/CNPJ do cliente de destino da viagem que possui CIOT integrado.", EFaultType.Error);
                    }

                    if (viagemPersistida.DeclaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Agregado && 
                        viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                    {
                        if(viagemPersistida.StatusViagem != EStatusViagem.Cancelada && request.StatusViagem == EStatusViagem.Cancelada)
                            return validationResult.Add("Não é possível cancelar a viagem com o CIOT encerrado", EFaultType.Error);
                    
                        if(request.ViagemEventos.All(x => x.IdViagemEvento > 0) == false)
                            return validationResult.Add("Não é possível adicionar eventos na viagem com o CIOT encerrado", EFaultType.Error);

                        var idEventosNaoCanceladosPersistidos = viagemPersistida.ViagemEventos
                            .Where(x => x.Status != EStatusViagemEvento.Cancelado)
                            .Select(x => x.IdViagemEvento).ToList();
                        if(request.ViagemEventos
                            .Any(x => x.IdViagemEvento.HasValue &&
                                      idEventosNaoCanceladosPersistidos.Contains(x.IdViagemEvento.Value) &&
                                      x.Status == EStatusViagemEvento.Cancelado))
                            return validationResult.Add("Não é possível cancelar eventos da viagem com o CIOT encerrado", EFaultType.Error);

                        foreach (var eventoRequest in request.ViagemEventos)
                        {
                            var eventoPersistido = viagemPersistida.ViagemEventos.FirstOrDefault(x => x.IdViagemEvento == eventoRequest.IdViagemEvento);

                            if (eventoPersistido == null)
                                continue;
                        
                            if(eventoRequest.ValorPagamento != (eventoPersistido.ValorTotalPagamento ?? eventoPersistido.ValorPagamento))
                                return validationResult.Add("Não é possível alterar valor de eventos da viagem com o CIOT encerrado", EFaultType.Error);
                        }
                    }
                    
                    if (request.DadosPagamento != null)
                    {
                        if (request.DadosPagamento.FormaPagamento != viagemPersistida.FormaPagamento)
                            return validationResult.Add("Não é possivel alterar a forma de pagamento da viagem que possui CIOT integrado.", EFaultType.Error);

                        if (viagemPersistida.ViagemPagamentoConta != null && viagemPersistida.FormaPagamento.In(
                            EViagemFormaPagamento.ContaCorrente, EViagemFormaPagamento.ContaPagamento, EViagemFormaPagamento.ContaPoupanca))
                        {
                            var viagemPagamentoContaPersistida = viagemPersistida.ViagemPagamentoConta;
                            var viagemPagamentoContaRequest = request.DadosPagamento;

                            if (viagemPagamentoContaRequest.CodigoBacen != viagemPagamentoContaPersistida.CodigoBacenBanco)
                                return validationResult.Add("Não é possível alterar o Código Bacen da viagem que possui CIOT integrado.", EFaultType.Error);

                            if (viagemPagamentoContaRequest.Agencia != viagemPagamentoContaPersistida.Agencia)
                                return validationResult.Add("Não é possível alterar a Agência de pagamento da viagem que possui CIOT integrado.", EFaultType.Error);

                            if (viagemPagamentoContaRequest.Conta != viagemPagamentoContaPersistida.Conta)
                                return validationResult.Add("Não é possível alterar a Conta de pagamento da viagem que possui CIOT integrado.", EFaultType.Error);

                            if (request.CPFCNPJProprietario != viagemPagamentoContaPersistida.CpfCnpjConta)
                                return validationResult.Add("Não é possível alterar o CPF/CNPJ da Conta de pagamento da viagem que possui CIOT integrado.", EFaultType.Error);
                        }
                    }
                }
            }

            if (viagemPersistida.ViagemEventos.Any(x => x.Status.In(EStatusViagemEvento.Baixado, EStatusViagemEvento.Cancelado)))
            {
                if (!string.IsNullOrEmpty(request.CPFCNPJProprietario) && request.CPFCNPJProprietario != viagemPersistida.CPFCNPJProprietario)
                    return validationResult.Add("Não é possível alterar o CPF/CNPJ do proprietário de uma viagem que possui um pagamento já efetuado.", EFaultType.Error);
                
                if (!string.IsNullOrEmpty(request.CPFMotorista) && request.CPFMotorista != viagemPersistida.CPFMotorista)
                    return validationResult.Add("Não é possível alterar o CPF do motorista de uma viagem que possui um pagamento já efetuado.", EFaultType.Error);
                
                if (request.DadosPagamento?.FormaPagamento != null && request.DadosPagamento.FormaPagamento != viagemPersistida.FormaPagamento)
                    return validationResult.Add("Nào é possível alterar a forma de pagamento de uma viagem que já possui um pagamento efetuado.", EFaultType.Error);
            }
            
            if (request.StatusViagem == EStatusViagem.Cancelada && 
                viagemPersistida.StatusViagem != EStatusViagem.Cancelada &&
                viagemPersistida.DeclaracaoCiot?.TipoDeclaracao == ETipoDeclaracao.Agregado &&
                viagemPersistida.DeclaracaoCiot.Status.In(EStatusDeclaracaoCiot.Declarado, EStatusDeclaracaoCiot.DeclaradoContigencia) &&
                viagemPersistida.DeclaracaoCiot.DataDeclaracao.AddDays(5) < DateTime.Today)
            {
                var quantidadeViagens = _viagemApp.QuantidadeViagensAbertasPorNumeroCiotsVinculados(viagemPersistida.DeclaracaoCiot.IdDeclaracaoCiot);
                if(quantidadeViagens == 1)
                    return validationResult.Add("Não é possível cancelar o CIOT, pois este tem apenas uma viagem vinculada a ele. Favor vincular uma nova viagem no CIOT antes de realizar o cancelamento desta.", EFaultType.Error);
            }

            validationResult.Add(ValidaRetificacaoCiotNovasPlacasV3(viagemPersistida, request, ciotV3App));
            
            return validationResult;
        }
        
        private ValidationResult PermitirAlterarViagemV2(ViagemIntegrarRequestModel request, Viagem viagemPersistida, int? idClienteOrigem, int? idClienteDestino, ICiotV2App ciotV2App)
        {
            var validationResult = new ValidationResult();
            
            if (viagemPersistida.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraSolicitada ||
                viagemPersistida.ResultadoCompraPedagio == EResultadoCompraPedagio.CompraConfirmada)
            {
                if (!string.IsNullOrEmpty(request.CPFCNPJProprietario) && request.CPFCNPJProprietario != viagemPersistida.CPFCNPJProprietario)
                    return validationResult.Add($"Não é possível alterar o proprietário da viagem que possui Compra de pedágio {viagemPersistida.ResultadoCompraPedagio.GetDescription()}.", EFaultType.Error);
                    
                if (!string.IsNullOrEmpty(request.CPFMotorista) && request.CPFMotorista != viagemPersistida.CPFMotorista)
                    return validationResult.Add($"Não é possível alterar o CPF do motorista de uma viagem que possui Compra de pedágio {viagemPersistida.ResultadoCompraPedagio.GetDescription()}.", EFaultType.Error);
            }
            
            if (viagemPersistida.DeclaracaoCiot != null)
            {
                if (viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Declarado ||
                    viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.DeclaradoContigencia || 
                    viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                {
                    if (!string.IsNullOrEmpty(request.CPFCNPJProprietario) && request.CPFCNPJProprietario != viagemPersistida.CPFCNPJProprietario)
                        return validationResult.Add("Não é possível alterar o proprietário da viagem que possui CIOT integrado.", EFaultType.Error);
                    
                    if (request.RNTRC.HasValue && request.RNTRC != viagemPersistida.RNTRC)
                        return validationResult.Add("Não é possível alterar o RNTRC da viagem que possui CIOT integrado.", EFaultType.Error);

                    if (viagemPersistida.DeclaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Padrao)
                    {
                        if (idClienteOrigem.HasValue && idClienteOrigem != viagemPersistida.IdClienteOrigem)
                            return validationResult.Add("Não é possível alterar o CPF/CNPJ do cliente de origem da viagem que possui CIOT integrado.", EFaultType.Error);

                        if (idClienteDestino.HasValue && idClienteDestino != viagemPersistida.IdClienteDestino)
                            return validationResult.Add("Não é possível alterar o CPF/CNPJ do cliente de destino da viagem que possui CIOT integrado.", EFaultType.Error);
                    }
                }

                if (viagemPersistida.DeclaracaoCiot.TipoDeclaracao == ETipoDeclaracao.Agregado && 
                    viagemPersistida.DeclaracaoCiot.Status == EStatusDeclaracaoCiot.Encerrado)
                {
                    if(viagemPersistida.StatusViagem != EStatusViagem.Cancelada && request.StatusViagem == EStatusViagem.Cancelada)
                        return validationResult.Add("Não é possível cancelar a viagem com o CIOT encerrado", EFaultType.Error);
                    
                    if(request.ViagemEventos.All(x => x.IdViagemEvento > 0) == false)
                        return validationResult.Add("Não é possível adicionar eventos na viagem com o CIOT encerrado", EFaultType.Error);

                    var idEventosNaoCanceladosPersistidos = viagemPersistida.ViagemEventos
                        .Where(x => x.Status != EStatusViagemEvento.Cancelado)
                        .Select(x => x.IdViagemEvento).ToList();
                    if(request.ViagemEventos
                        .Any(x => x.IdViagemEvento.HasValue &&
                                  idEventosNaoCanceladosPersistidos.Contains(x.IdViagemEvento.Value) &&
                                  x.Status == EStatusViagemEvento.Cancelado))
                        return validationResult.Add("Não é possível cancelar eventos da viagem com o CIOT encerrado", EFaultType.Error);

                    foreach (var eventoRequest in request.ViagemEventos)
                    {
                        var eventoPersistido = viagemPersistida.ViagemEventos.FirstOrDefault(x => x.IdViagemEvento == eventoRequest.IdViagemEvento);

                        if (eventoPersistido == null)
                            continue;
                        
                        if(eventoRequest.ValorPagamento != (eventoPersistido.ValorTotalPagamento ?? eventoPersistido.ValorPagamento))
                            return validationResult.Add("Não é possível alterar valor de eventos da viagem com o CIOT encerrado", EFaultType.Error);
                    }
                }
            }

            if (request.StatusViagem == EStatusViagem.Cancelada && 
                viagemPersistida.StatusViagem != EStatusViagem.Cancelada &&
                viagemPersistida.DeclaracaoCiot?.TipoDeclaracao == ETipoDeclaracao.Agregado &&
                viagemPersistida.DeclaracaoCiot.Status.In(EStatusDeclaracaoCiot.Declarado, EStatusDeclaracaoCiot.DeclaradoContigencia) &&
                viagemPersistida.DeclaracaoCiot.DataDeclaracao.AddDays(5) < DateTime.Today)
            {
                var quantidadeViagens = _viagemApp.QuantidadeViagensAbertasPorNumeroCiotsVinculados(viagemPersistida.DeclaracaoCiot.IdDeclaracaoCiot);
                if(quantidadeViagens == 1)
                    return validationResult.Add("Não é possível cancelar o CIOT, pois este tem apenas uma viagem vinculada a ele. Favor vincular uma nova viagem no CIOT antes de realizar o cancelamento desta.", EFaultType.Error);
            }

            if (viagemPersistida.ViagemEventos.Any(x => x.Status.In(EStatusViagemEvento.Baixado, EStatusViagemEvento.Cancelado)))
            {
                if (!string.IsNullOrEmpty(request.CPFCNPJProprietario) && request.CPFCNPJProprietario != viagemPersistida.CPFCNPJProprietario)
                    return validationResult.Add("Não é possível alterar o CPF/CNPJ do proprietário de uma viagem que possui um pagamento já efetuado.", EFaultType.Error);
                
                if (!string.IsNullOrEmpty(request.CPFMotorista) && request.CPFMotorista != viagemPersistida.CPFMotorista)
                    return validationResult.Add("Não é possível alterar o CPF do motorista de uma viagem que possui um pagamento já efetuado.", EFaultType.Error);
            }
            
            validationResult.Add(ValidaRetificacaoCiotNovasPlacasV2(viagemPersistida, request, ciotV2App));

            return validationResult;
        }
        
        private ValidationResult ValidaRetificacaoCiotNovasPlacasV3(Viagem viagemPersistida, ViagemIntegrarRequestModel request, ICiotV3App ciotV3App)
        {
            var validationResult = new ValidationResult();
            
            //Versão ANTT 3 (CIOT para todos) só valida retificação de placas para contrato agregado, visto que o CIOT normal encerra e faz um novo
            if (viagemPersistida.DeclaracaoCiot?.TipoDeclaracao == ETipoDeclaracao.Padrao)
                return validationResult;
            
            #region idProprietario e habilitarContratoCiotAgregado
            var idProprietario = 0;
            if (viagemPersistida.IdProprietario.HasValue)
                idProprietario = viagemPersistida.IdProprietario.Value;
            else if (!string.IsNullOrWhiteSpace(request.CPFCNPJProprietario))
                idProprietario = _proprietarioApp.GetIdPorCpfCnpj(request.CPFCNPJProprietario, viagemPersistida.IdEmpresa) ?? 0;

            if(idProprietario == 0)
                return validationResult;
            
            var habilitarContratoCiotAgregado = _proprietarioApp.Find(p => p.IdProprietario == idProprietario).Select(p => p.HabilitarContratoCiotAgregado).FirstOrDefault();
            #endregion
            
            if (viagemPersistida.DeclaracaoCiot?.TipoDeclaracao == ETipoDeclaracao.Agregado
                || 
                request.HabilitarDeclaracaoCiot &&
                (
                    request.GerarCiotTacAgregado == true 
                    ||
                    ciotV3App.IsDeclaracaoTacAgregado(viagemPersistida.Placa, viagemPersistida.IdEmpresa, habilitarContratoCiotAgregado)
                )
                && ciotV3App.AlgumContratoAberto(idProprietario, viagemPersistida.IdEmpresa)
            )
            {
                #region verifica se há novas placas
                var placasPersistidas = ciotV3App.GetPlacasContratoAberto(idProprietario, viagemPersistida.IdEmpresa);
                
                if(!placasPersistidas.Any())
                    return validationResult;

                var placasRequest = new List<string>();
                if (request.Carretas?.Any() == true)
                {
                    request.Carretas.ForEach(placa => StringExtension.RemoveSpecialCaracter(placa));
                    placasRequest.AddRange(request.Carretas.Distinct().ToList());
                }

                request.Placa = StringExtension.RemoveSpecialCaracter(request.Placa);
                if(!placasRequest.Contains(request.Placa))
                    placasRequest.Add(request.Placa);
                
                var placasNovas = placasRequest.Except(placasPersistidas).ToList();

                if (placasNovas.Count <= 0) 
                    return validationResult;
                #endregion
                
                if (viagemPersistida.DeclaracaoCiot?.Status == EStatusDeclaracaoCiot.Encerrado)
                    return validationResult.Add("Não é possível alterar as placas da viagem que possui CIOT encerrado.", EFaultType.Error);
                    
                if (viagemPersistida.DeclaracaoCiot?.Status == EStatusDeclaracaoCiot.Cancelado)
                    return validationResult.Add("Não é possível alterar as placas da viagem que possui CIOT cancelado.", EFaultType.Error);
            }
            
            return validationResult;
        }

        private ValidationResult ValidaRetificacaoCiotNovasPlacasV2(Viagem viagemPersistida, ViagemIntegrarRequestModel request, ICiotV2App ciotV2App)
        {
            var validationResult = new ValidationResult();
            
            #region idProprietario e habilitarContratoCiotAgregado
            var idProprietario = 0;
            if (viagemPersistida.IdProprietario.HasValue)
                idProprietario = viagemPersistida.IdProprietario.Value;
            else if (!string.IsNullOrWhiteSpace(request.CPFCNPJProprietario))
                idProprietario = _proprietarioApp.GetIdPorCpfCnpj(request.CPFCNPJProprietario, viagemPersistida.IdEmpresa) ?? 0;

            if(idProprietario == 0)
                return validationResult;

            var habilitarContratoCiotAgregado = _proprietarioApp.Find(p => p.IdProprietario == idProprietario).Select(p => p.HabilitarContratoCiotAgregado).FirstOrDefault();
            #endregion

            //Versão ANTT 2 valida retificações de placas para CIOT normal e agregado
            if (viagemPersistida.DeclaracaoCiot != null || request.HabilitarDeclaracaoCiot)
            {
                #region verifica se há novas placas
                
                var placasPersistidas = new List<string>();
                if(viagemPersistida.DeclaracaoCiot?.TipoDeclaracao == ETipoDeclaracao.Agregado)
                    placasPersistidas.AddRange(ciotV2App.GetPlacasContratoAberto(idProprietario, viagemPersistida.IdEmpresa));
                else if(viagemPersistida.DeclaracaoCiot?.TipoDeclaracao == ETipoDeclaracao.Padrao)
                {
                    placasPersistidas.Add(viagemPersistida.Placa);
                    if (viagemPersistida.ViagemCarretas?.Any() == true)
                        placasPersistidas.AddRange(viagemPersistida.ViagemCarretas.Select(x => x.Placa).Distinct().ToList());
                }
                else if ((request.GerarCiotTacAgregado == true || 
                          ciotV2App.IsDeclaracaoTacAgregado(viagemPersistida.Placa, viagemPersistida.IdEmpresa, habilitarContratoCiotAgregado)) 
                         && ciotV2App.AlgumContratoAberto(idProprietario, viagemPersistida.IdEmpresa))
                    placasPersistidas.AddRange(ciotV2App.GetPlacasContratoAberto(idProprietario, viagemPersistida.IdEmpresa));
                else
                    return validationResult;

                var placasRequest = new List<string>();
                if (request.Carretas?.Any() == true)
                {
                    request.Carretas.ForEach(placa => StringExtension.RemoveSpecialCaracter(placa));
                    placasRequest.AddRange(request.Carretas.Distinct().ToList());
                }

                request.Placa = StringExtension.RemoveSpecialCaracter(request.Placa);
                if (!placasRequest.Contains(request.Placa))
                    placasRequest.Add(request.Placa);

                var placasNovas = placasRequest.Except(placasPersistidas).ToList();
                
                if (placasNovas.Count <= 0) 
                    return validationResult;
                #endregion
                
                if (viagemPersistida.DeclaracaoCiot?.Status == EStatusDeclaracaoCiot.Encerrado)
                    return validationResult.Add("Não é possível alterar as placas da viagem que possui CIOT encerrado.", EFaultType.Error);
                    
                if (viagemPersistida.DeclaracaoCiot?.Status == EStatusDeclaracaoCiot.Cancelado)
                    return validationResult.Add("Não é possível alterar as placas da viagem que possui CIOT cancelado.", EFaultType.Error);
            }
            
            return validationResult;
        }
        
        private ViagemIntegrarResponseModel RetornoViagem(Viagem viagem, DeclararCiotResult ciotResult, SolicitarCompraPedagioResponseDTO compraPedagio, string mensagemAviso,Retorno<BloqueioGestorValorResponseDTO> bloqueio)
        {
            var viagemEventosAdd = _viagemEventoApp.GetEventosViagem(viagem.IdViagem);
            var estabelecimentoAdd = _viagemEstabelecimentoApp.GetPorViagem(viagem.IdViagem);
            var eventos = new List<ViagemIntegrarEventoResponseModel>();

            var eventoGestorStatus = _viagemPendenteGestorService.GetStatusViagem(viagem.IdViagem);

            if (viagemEventosAdd != null)
                foreach (var evento in viagemEventosAdd)
                {
                    var statusOperacao = new StatusOperacaoCartaoModel(ERetornoOperacaoCartao.NaoHabilitado, string.Empty);

                    var transacao = _transacaoCartaoApp.GetAllByIdEvento(evento.IdViagemEvento).OrderByDescending(c => c.LineId).FirstOrDefault();

                    if (transacao != null)
                    {
                        ERetornoOperacaoCartao eRetornoOperacao;

                        switch (transacao.StatusPagamento)
                        {
                            case EStatusPagamentoCartao.Aberto:
                            case EStatusPagamentoCartao.Pendente:
                                eRetornoOperacao = ERetornoOperacaoCartao.Pendente;
                                break;

                            case EStatusPagamentoCartao.Baixado:
                                eRetornoOperacao = ERetornoOperacaoCartao.Sucesso;
                                break;

                            case EStatusPagamentoCartao.Erro:
                                eRetornoOperacao = ERetornoOperacaoCartao.Erro;
                                break;

                            default:
                                throw new ArgumentException(nameof(transacao.StatusPagamento));
                        }

                        statusOperacao = new StatusOperacaoCartaoModel(eRetornoOperacao,
                            transacao.MensagemProcessamentoWs);
                    }

                    if (evento.HabilitarPagamentoPix == true)
                    {
                        if (evento.Status == EStatusViagemEvento.Baixado)
                        {
                            statusOperacao.Status = ERetornoOperacaoCartao.Sucesso;
                            statusOperacao.Mensagem = "Pix efetuado com sucesso.";
                        }
                        else if (evento.Status == EStatusViagemEvento.Cancelado)
                        {
                            statusOperacao.Status = ERetornoOperacaoCartao.Sucesso;
                            statusOperacao.Mensagem = "Parcela Pix cancelada. Nenhum estorno será efetuado.";
                        }
                        else if (evento.Status == EStatusViagemEvento.Aberto)
                        {
                            var statusTransacoesPix = _transacaoPixRepository
                                .AsNoTracking().Where(c => c.IdViagemEvento == evento.IdViagemEvento)
                                .Select(c => c.IdTransacaoPixStatus).ToList();
                            var transacaoPixComErro = statusTransacoesPix.Count != 0 && statusTransacoesPix.All(c => c != ETransacaoPixStatus.Confirmado);
                            statusOperacao.Status = transacaoPixComErro ? ERetornoOperacaoCartao.Erro : ERetornoOperacaoCartao.NaoHabilitado;
                            statusOperacao.Mensagem = transacaoPixComErro ? "Pix não efetuado. Integre a parcela novamente." : "Parcela Pix cadastrada. Pagamento não efetuado devido ao pagamento estar em aberto.";
                        }
                        else if (evento.Status == EStatusViagemEvento.Bloqueado)
                        {
                            statusOperacao.Status = ERetornoOperacaoCartao.Pendente;
                            statusOperacao.Mensagem = evento.MotivoBloqueio;
                        }
                    }

                    eventos.Add(new ViagemIntegrarEventoResponseModel
                    {
                        OperacaoCartao = statusOperacao,
                        IdViagemEvento = evento.IdViagemEvento,
                        NumeroControle = evento.NumeroControle,
                        Token = evento.Token,
                        IdsViagemDocumento = evento.ViagemDocumentos.Select(x => new ViagemIntegrarEventoDocumentosResponse
                        {
                            IdViagemDocumento = x.IdViagemDocumento, NumeroDocumento = x.NumeroDocumento
                        }).ToList(),
                        IdsViagemVlAdicional = evento.ViagemValoresAdicionais.Select(x => x.IdViagemValorAdicional).ToList(),
                        TipoEventoViagem = evento.TipoEventoViagem,
                        ViagemOutrosDescontos = evento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Desconto)
                            .Select(x => new ViagemIntegrarEventoOutrosAcrescimosDescontos
                            {
                                IdViagemOutrosDescontos = x.IdViagemValorAdicional,
                                NumeroDocumento = x.NumeroDocumento,
                                Descricao = x.Descricao,
                                Valor = x.Valor,
                                CodigoERP = x.CodigoERP
                            }).ToList(),
                        ViagemOutrosAcrescimos = evento.ViagemValoresAdicionais.Where(x => x.Tipo == ETipoValorAdicional.Acrescimo)
                            .Select(x => new ViagemIntegrarEventoOutrosAcrescimosDescontos
                            {
                                IdViagemOutrosDescontos = x.IdViagemValorAdicional,
                                NumeroDocumento = x.NumeroDocumento,
                                Descricao = x.Descricao,
                                Valor = x.Valor,
                                CodigoERP = x.CodigoERP
                            }).ToList()
                    });
                }

            return new ViagemIntegrarResponseModel
            {
                IdViagem = viagem.IdViagem,
                BloqueioGestorNew = bloqueio ?? new Retorno<BloqueioGestorValorResponseDTO>()
                {
                    Objeto = new BloqueioGestorValorResponseDTO()
                    {
                        Status = eventoGestorStatus
                    }
                },
                IdsViagemEstabelecimento = estabelecimentoAdd?.Select(x => x.IdViagemEstabelecimento).ToList(),
                NumeroDocumento = viagem.NumeroDocumento,
                Eventos = eventos,
                IRRPF = viagem.IRRPF,
                INSS = viagem.INSS,
                SESTSENAT = viagem.SESTSENAT,
                CIOT = ciotResult,
                Pedagio = compraPedagio,
                Avisos = mensagemAviso
            };
        }
        
        public Retorno<ViagemV2AutorizarEstabelecimentosResponse> AutorizarEstabelecimentos(ViagemV2AutorizarEstabelecimentosRequest request)
        {
            var validacao = new ValidationResult<EValidationViagemEstabelecimento>();
            
            var viagem = _viagemApp.GetComViagemEstabelecimentos(request.ViagemId);
            if (viagem == null)
            {
                validacao.Add(EValidationViagemEstabelecimento.ViagemNaoEncontrada, EFaultType.Error, new object[]{request.ViagemId});
                return new Retorno<ViagemV2AutorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };
            }
            
            var idEmpresa = _empresaApp.GetIdPorCnpj(request.CNPJEmpresa);

            if (!idEmpresa.HasValue || viagem.IdEmpresa != idEmpresa.Value)
            {
                validacao.Add(EValidationViagemEstabelecimento.ViagemNaoPertenceAEmpresa, EFaultType.Error, new object[]{request.ViagemId});
                return new Retorno<ViagemV2AutorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };
            } 
            
            if (viagem.StatusViagem.In(EStatusViagem.Cancelada, EStatusViagem.Baixada))
                validacao.Add(EValidationViagemEstabelecimento.ViagemNaoPermiteEdicao, EFaultType.Error, new object[]{viagem.StatusViagem.GetDescription()});

            if (!validacao.IsValid)
                return new Retorno<ViagemV2AutorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };
            
            if(viagem.ViagemEstabelecimentos == null)
                viagem.ViagemEstabelecimentos = new List<ViagemEstabelecimento>();

            foreach (var estabelecimento in request.AutorizacaoEstabelecimentos)
                if (estabelecimento.IdEstabelecimento.HasValue && estabelecimento.TipoEvento.HasValue)
                {
                    if (viagem.ViagemEstabelecimentos.Any(x => x.IdEstabelecimento == estabelecimento.IdEstabelecimento.Value && x.TipoEventoViagem == estabelecimento.TipoEvento.Value))
                        validacao.Add(EValidationViagemEstabelecimento.EstabelecimentoJaAutorizado, EFaultType.Error, 
                            new object[]{estabelecimento.Cnpj, estabelecimento.TipoEvento.GetDescription()});
                    else
                        viagem.ViagemEstabelecimentos.Add(new ViagemEstabelecimento
                        {
                            IdEstabelecimento = estabelecimento.IdEstabelecimento.Value,
                            TipoEventoViagem = estabelecimento.TipoEvento.Value,
                            IdEmpresa = idEmpresa.Value
                        });
                }

            if (!validacao.IsValid)
                return new Retorno<ViagemV2AutorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };

            var validacaoUpdate = _viagemApp.Update(viagem);
            
            return validacaoUpdate.IsValid 
                ? new Retorno<ViagemV2AutorizarEstabelecimentosResponse> {Sucesso = true, Mensagem = string.Empty} 
                : new Retorno<ViagemV2AutorizarEstabelecimentosResponse> {Sucesso = false, Mensagem = validacaoUpdate.ToString()};
        }
        
        public Retorno<ViagemV2DesautorizarEstabelecimentosResponse> DesautorizarEstabelecimentos(ViagemV2DesautorizarEstabelecimentosRequest request)
        {
            var validacao = new ValidationResult<EValidationViagemEstabelecimento>();
            
            var viagem = _viagemApp.GetComViagemEstabelecimentos(request.ViagemId);
            if (viagem?.ViagemEstabelecimentos == null)
            {
                if(viagem == null)
                    validacao.Add(EValidationViagemEstabelecimento.ViagemNaoEncontrada, EFaultType.Error, new object[]{request.ViagemId});
                else if (!viagem.ViagemEstabelecimentos?.Any() == true)
                    foreach (var estabelecimento in request.DesautorizacaoEstabelecimentos)
                        validacao.Add(EValidationViagemEstabelecimento.EstabelecimentoNaoAutorizado, EFaultType.Error, 
                            new object[]{estabelecimento.Cnpj, estabelecimento.TipoEvento.GetDescription()});

                return new Retorno<ViagemV2DesautorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };
            }
            
            var idEmpresa = _empresaApp.GetIdPorCnpj(request.CNPJEmpresa);

            if (!idEmpresa.HasValue || viagem.IdEmpresa != idEmpresa.Value)
                validacao.Add(EValidationViagemEstabelecimento.ViagemNaoPertenceAEmpresa, EFaultType.Error, new object[]{request.ViagemId});
            else if (viagem.StatusViagem.In(EStatusViagem.Cancelada, EStatusViagem.Baixada))
                validacao.Add(EValidationViagemEstabelecimento.ViagemNaoPermiteEdicao, EFaultType.Error, new object[]{viagem.StatusViagem.GetDescription()});

            if (!validacao.IsValid)
                return new Retorno<ViagemV2DesautorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };
        
            var idViagemEstabelecimentoRemovidoList = new List<int>();
            foreach (var estabelecimento in request.DesautorizacaoEstabelecimentos)
                if (estabelecimento.IdEstabelecimento.HasValue && estabelecimento.TipoEvento.HasValue)
                {
                    var viagemEstabelecimento = viagem.ViagemEstabelecimentos
                        .FirstOrDefault(x => x.TipoEventoViagem == estabelecimento.TipoEvento &&
                                             x.IdEstabelecimento == estabelecimento.IdEstabelecimento);

                    if(viagemEstabelecimento == null)
                        validacao.Add(EValidationViagemEstabelecimento.EstabelecimentoNaoAutorizado, EFaultType.Error, 
                            new object[]{estabelecimento.Cnpj, estabelecimento.TipoEvento.GetDescription()});
                    else
                        idViagemEstabelecimentoRemovidoList.Add(viagemEstabelecimento.IdViagemEstabelecimento);
                }
            
            if (!validacao.IsValid)
                return new Retorno<ViagemV2DesautorizarEstabelecimentosResponse>
                {
                    Sucesso = false, Mensagem = validacao.ToString(),
                    Objeto = null, Faults = validacao.GetFaults()
                };

            _viagemApp.RemoverViagemEstabelecimentos(viagem.IdViagem, idViagemEstabelecimentoRemovidoList);

            return new Retorno<ViagemV2DesautorizarEstabelecimentosResponse> {Sucesso = true, Mensagem = string.Empty};
        }
    }
}
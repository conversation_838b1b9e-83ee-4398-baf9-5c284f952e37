﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class PrestacaoContasEventoMap : EntityTypeConfiguration<PrestacaoContasEvento>
    {
        public PrestacaoContasEventoMap()
        {
            ToTable("PRESTACAO_CONTAS_EVENTO");

            HasKey(x => x.Id);

            HasRequired(x => x.PrestacaoContas)
                .WithMany()
                .HasForeign<PERSON>ey(x => x.IdPrestacaoContas); 

            HasOptional(x => x.UsuarioCadastro)
                .WithMany()
                .<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(x => x.IdUsuarioCadastro); 
        }
    }
}

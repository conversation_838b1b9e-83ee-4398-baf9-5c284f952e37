﻿using ATS.Application.Application.Common;
using ATS.Domain.Interface.Service;
using ATS.MongoDB.Context.Entities;
using ATS.Domain.Service;
using MongoDB.Bson;

namespace ATS.Application.Application
{
    public class DataMediaServerApp : AppBase, IDataMediaServerApp
    {
        private readonly IDataMediaServerService _dataMediaServerService;

        public DataMediaServerApp(IDataMediaServerService dataMediaServerService)
        {
            _dataMediaServerService = dataMediaServerService;
        }

        public ObjectId Add(int type, string base64Data, string fileName, string mimeType = "")
        {
            return _dataMediaServerService.Add(type, base64Data, fileName, mimeType);
        }

        public Media GetMedia(string _id)
        {
            return _dataMediaServerService.GetMedia(_id);
        }

        public void DeleteByToken(string token)
        {
            _dataMediaServerService.DeleteByToken(token);
        }

        public object VisualizarMedia(string token)
        {
            return _dataMediaServerService.VisualizarMedia(token);
        }

        public string GetIconeTipoEstabelecimentoCacheable(string mediaId)
        {
            return _dataMediaServerService.GetIconeTipoEstabelecimentoCacheable(mediaId);
        }
    }
}

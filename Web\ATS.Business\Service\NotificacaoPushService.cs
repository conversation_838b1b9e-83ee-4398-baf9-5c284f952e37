﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Interface.Database;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Net;
using System.Web.Configuration;
using ATS.Domain.Interface.Service;

namespace ATS.Domain.Service
{
    public class NotificacaoPushService : ServiceBase, INotificacaoPushService
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly INotificacaoPushItemRepository _notificacaoPushItemRepository;
        private readonly INotificacaoPushDapper _notificacaoPushDapper;
        private readonly INotificacaoPushRepository _notificacaoPushRepository;
        private readonly IEmpresaService _empresaService;
        private readonly INotificacaoService _notificacaoService;
        private readonly IPushService _pushService;

        public NotificacaoPushService(IUsuarioRepository usuarioRepository, INotificacaoPushItemRepository notificacaoPushItemRepository, INotificacaoPushDapper notificacaoPushDapper,
            INotificacaoPushRepository notificacaoPushRepository, IEmpresaService empresaService, INotificacaoService notificacaoService, IPushService pushService)
        {
            _usuarioRepository = usuarioRepository;
            _notificacaoPushItemRepository = notificacaoPushItemRepository;
            _notificacaoPushDapper = notificacaoPushDapper;
            _notificacaoPushRepository = notificacaoPushRepository;
            _empresaService = empresaService;
            _notificacaoService = notificacaoService;
            _pushService = pushService;
        }

        public void EnviarRequest(IEnumerable<string> idsPush, string title, ETipoMensagemPush messageType, string message)
        {
            try
            {
                _pushService.Enviar(idsPush.ToList(), title, message);
            }
            catch (Exception e)
            {
                throw new Exception("Não foi possível enviar a mensagem via Push.", e);
            }
        }

        public MesagemPushModel EnviarPush(string query, List<string> imeis, string imei, int idEmpresa, string idFilial, int idGrupoUsuario, string modeloMenssagem, string apelido, int idTipoNotificacao, bool isTeste)
        {
            try
            {
                //Tratamento para quando imei for retornado nulo ser setado como o primeiro la lista de imeis. 
                if (string.IsNullOrEmpty(imei))
                    imei = (imeis != null && imeis.Any()) ? imeis[0] : null;

                query = PreparaConsulta(query, imeis, imei, idEmpresa.ToString(), idFilial);

                var mensagem = ValidarRegra(query, modeloMenssagem, idEmpresa, apelido);

                if (!mensagem.Sucesso && !isTeste) return mensagem;

                var usuarios = _usuarioRepository.GetPorGrupoUsuario(idGrupoUsuario);
                var grupos = usuarios.Where(x => x.IdPush != null).Select(x => x.IdPush);
                EnviarRequest(grupos.ToList(), "Nova notificação", ETipoMensagemPush.Warning, mensagem.Mensagem);

                foreach (var usuario in usuarios)
                    _notificacaoService.InserirDapper(usuario.IdUsuario.ToString(), "4", mensagem.Mensagem, DateTime.Now, idTipoNotificacao);

                return mensagem;
            }
            catch (Exception)
            {
                var mensagem = new MesagemPushModel
                {
                    Sucesso = false,
                    Mensagem = "Erro ao enviar push!"
                };

                return mensagem;
            }
        }
        
        
        public MesagemPushModel EnviarPushGrupoUsuarios(string query, List<string> imeis, string imei, int idEmpresa, string idFilial, List<int> idGrupoUsuarios, string modeloMenssagem, string apelido, int idTipoNotificacao, bool isTeste)
        {
            try
            {
                //Tratamento para quando imei for retornado nulo ser setado como o primeiro la lista de imeis. 
                if (string.IsNullOrEmpty(imei))
                    imei = (imeis != null && imeis.Any()) ? imeis[0] : null;

                query = PreparaConsulta(query, imeis, imei, idEmpresa.ToString(), idFilial);

                var mensagem = ValidarRegra(query, modeloMenssagem, idEmpresa, apelido);

                if (!mensagem.Sucesso && !isTeste) return mensagem;

                foreach (var idGrupoUsuario in idGrupoUsuarios)
                {
                    var usuarios = _usuarioRepository.GetPorGrupoUsuario(idGrupoUsuario);
                    var grupos = usuarios.Where(x => x.IdPush != null).Select(x => x.IdPush);
                    EnviarRequest(grupos.ToList(), "Nova notificação", ETipoMensagemPush.Warning, mensagem.Mensagem);

                    foreach (var usuario in usuarios)
                        _notificacaoService.InserirDapper(usuario.IdUsuario.ToString(), "4", mensagem.Mensagem, DateTime.Now, idTipoNotificacao);
                }
                
                return mensagem;
            }
            catch (Exception)
            {
                var mensagem = new MesagemPushModel
                {
                    Sucesso = false,
                    Mensagem = "Erro ao enviar push!"
                };

                return mensagem;
            }
        }

        public void DeletarFilhos(int idNotificacaoPush)
        {
            var listaItemsOld = _notificacaoPushItemRepository
                .Find(x => x.IdNotificacaoPush == idNotificacaoPush);

            // Apaga todos os filhos
            foreach (var item in listaItemsOld)
                _notificacaoPushItemRepository
                    .Delete(item);
        }

        public string PreparaConsulta(string query, List<string> imeis, string imei, string idEmpresa, string idFilial)
        {
            if (query.Contains("@IMEI@") && imei != null)
            {
                query = query.Replace("@IMEI@", imei);
            }

            if (query.Contains("@IMEIS@") && imeis != null)
            {
                query = query.Replace("@IMEIS@", string.Join(",", imeis.Select(x => string.Format("'{0}'", x))));
            }

            if (query.Contains("@FILIAL@") && !string.IsNullOrEmpty(idFilial))
            {
                query = query.Replace("@FILIAL@", idFilial);
            }

            if (query.Contains("@EMPRESA@") && !string.IsNullOrEmpty(idEmpresa))
            {
                query = query.Replace("@EMPRESA@", idEmpresa);
            }

            return query;
        }

        public MesagemPushModel ValidarRegra(string query, string modeloMenssagem, int? idEmpresa, string apelido)
        {
            try
            {
                var retornoConsulta = ExecutarRegra(query);
                var mensagem = modeloMenssagem;
                var retorno = new MesagemPushModel();


                if (retornoConsulta?.Retornos == null)
                {
                    retorno.Sucesso = false;
                    retorno.Mensagem = "Erro durante a execução da consulta SQL.";
                    return retorno;
                }


                var count = 0;
                foreach (var value in retornoConsulta.Retornos)
                {
                    mensagem = mensagem.Replace("{" + count + "}", value?.ToString());
                    count++;
                }

                mensagem = mensagem.Replace("{apelido}", apelido);

                var empresa = idEmpresa != null ? _empresaService.Get((int)idEmpresa, null) : null;

                if (empresa != null) mensagem = mensagem.Replace("{empresa}", empresa.RazaoSocial);

                retorno.Sucesso = retornoConsulta.Sucesso;
                retorno.Mensagem = mensagem;

                return retorno;

            }
            catch (Exception ex)
            {
                return new MesagemPushModel
                {
                    Sucesso = false,
                    Mensagem = ex.Message
                };
            }
        }

        public NotificacaoPushModel ExecutarRegra(string query)
        {
            return _notificacaoPushDapper.ExecutarRegra(query);
        }

        public IQueryable<NotificacaoPush> GetNotificacoesAtivas()
        {
            return _notificacaoPushRepository
                .Find(x => x.Ativo)
                .AsNoTracking()
                .Include(x => x.Items)
                .Include(x => x.NotificacaoPushGrupoUsuario);
        }

        public ValidationResult Add(NotificacaoPush NotificacaoPush)
        {
            try
            {
                NotificacaoPush.Ativo = true;
                NotificacaoPush.DataUltimaAtualizacao = DateTime.Now;

                _notificacaoPushRepository.Add(NotificacaoPush);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Update(NotificacaoPush NotificacaoPush)
        {
            try
            {
                NotificacaoPush.DataUltimaAtualizacao = DateTime.Now;

                _notificacaoPushRepository.Update(NotificacaoPush);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult Reativar(int idNotificacaoPush)
        {
            try
            {
                var notificacaoPushRepo = _notificacaoPushRepository;

                var notificacaoPush = notificacaoPushRepo.Get(idNotificacaoPush);

                if (notificacaoPush != null)
                {
                    notificacaoPush.Ativo = true;
                    notificacaoPush.DataUltimaAtualizacao = DateTime.Now;
                    notificacaoPushRepo.Update(notificacaoPush);
                }
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public ValidationResult Inativar(int idNotificacaoPush)
        {
            try
            {
                var notificacaoPushRepo = _notificacaoPushRepository;

                var notificacaoPush = notificacaoPushRepo.Get(idNotificacaoPush);

                if (notificacaoPush != null)
                {
                    notificacaoPush.Ativo = false;
                    notificacaoPush.DataUltimaAtualizacao = DateTime.Now;
                    notificacaoPushRepo.Update(notificacaoPush);
                }

                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public NotificacaoPush Get(int id)
        {
            return _notificacaoPushRepository
                .Find(x => x.IdNotificacaoPush == id && x.Ativo)
                .Include(x => x.Empresa)
                .Include(x => x.Filial)
                .Include(x => x.Items)
                .Include(x => x.NotificacaoPushGrupoUsuario)
                .Include(x => x.NotificacaoPushGrupoUsuario.Select(y => y.GrupoUsuario))
                .FirstOrDefault();
        }

        public object ConsultaGrid(int? idEmpresa,
                                   int? idFilial,
                                   string descricao,
                                   int Take,
                                   int Page,
                                   OrderFilters Order,
                                   List<QueryFilters> Filters)
        {

            var notificacoesPush = _notificacaoPushRepository
                .GetAll().Include(x => x.Empresa).Include(x => x.Filial);

            if (idEmpresa.HasValue) notificacoesPush = notificacoesPush.Where(x => x.IdEmpresa == idEmpresa.Value);

            if (idFilial.HasValue) notificacoesPush = notificacoesPush.Where(x => x.IdFilial == idFilial.Value);

            if (descricao != null) notificacoesPush = notificacoesPush.Where(x => x.Descricao.Contains(descricao));

            notificacoesPush = string.IsNullOrWhiteSpace(Order?.Campo)
                ? notificacoesPush.OrderBy(x => x.IdTipoNotificacao)
                : notificacoesPush.OrderBy($"{Order.Campo} {Order.Operador.DescriptionAttr()}");

            notificacoesPush = notificacoesPush.AplicarFiltrosDinamicos<NotificacaoPush>(Filters);

            return new
            {
                totalItems = notificacoesPush.Count(),
                items = notificacoesPush.Skip((Page - 1) * Take).Take(Take)
                .ToList().Select(x => new
                {
                    x.Ativo,
                    x.Descricao,
                    x.IdNotificacaoPush,
                    RazaoSocialEmpresa = x.Empresa?.RazaoSocial ?? string.Empty,
                    RazaoSocialFilial = x.Filial?.RazaoSocial ?? string.Empty
                })
            };
        }

        public IQueryable<NotificacaoPush> Consultar(List<QueryFilters> queryFilters, OrderFilters orderFilters)
        {
            return _notificacaoPushRepository.Consultar(queryFilters, orderFilters);
        }
    }
}

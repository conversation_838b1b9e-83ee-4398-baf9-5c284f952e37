﻿using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Interface.Triggers;
using ATS.Domain.Service;
using ATS.Domain.Trigger.Base;
using Autofac;

namespace ATS.Domain.Trigger
{
    public class ViagemTrigger : Trigger<Viagem>, IViagemTrigger
    {
        public ViagemTrigger()
        {
            //this.RegisterAfterTrigger(EOperationTrigger.Insert, (d,e) => new ViagemService().AfterUpdate(d,e), "UpdateViagem");
            //this.RegisterAfterTrigger(EOperationTrigger.Update, (d,e) => new ViagemService().AfterUpdate(d,e), "UpdateViagem");
        }
    }

    public class ViagemEventoTrigger : Trigger<ViagemEvento>, IViagemEventoTrigger
    {
        public ViagemEventoTrigger()
        {                                                          //d = new  - e = old
            /*this.RegisterAfterTrigger(EOperationTrigger.Insert,
                (newEvento, oldEvento) => _viagemEventoService.AfterUpdate(newEvento, oldEvento),
                "UpdateViagemEventoInsert");*/

            this.RegisterAfterTrigger(EOperationTrigger.Update,
                (provider, @new, @old) => provider.Resolve<IViagemEventoService>().AfterUpdate(@new, @old),
                "UpdateViagemEventoUpdate");
        }
    }
}
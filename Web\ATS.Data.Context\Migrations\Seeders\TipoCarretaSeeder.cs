﻿using System;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using System.Data.Entity.Migrations;

namespace ATS.Data.Context.Migrations.Seeders
{
    public class TipoCarretaSeeder
    {
        public void Execute(AtsContext context)
        {
            int idTipoCarreta = 1;

            context.TipoCarreta.AddOrUpdate(new[]
            {
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Baú", Categoria = ECategoriaTipoCarreta.Fechada, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Aberto", Categoria = ECategoriaTipoCarreta.Aberta, DataHoraUltimaAtualizacao = DateTime.Now  },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Graneleiro", Categoria = ECategoriaTipoCarreta.Aberta, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Vanderléia", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Frigorifico", Categoria = ECategoriaTipoCarreta.Aberta, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Sider", Categoria = ECategoriaTipoCarreta.Fechada, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Basculante", Categoria = ECategoriaTipoCarreta.Aberta, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Munck", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Prancha", Categoria = ECategoriaTipoCarreta.Fechada, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Plataforma", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Container", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Porta Container", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Cegonha", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Tanque", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now },
                new TipoCarreta { IdTipoCarreta = idTipoCarreta++, Nome = "Outros", Categoria = ECategoriaTipoCarreta.Especial, DataHoraUltimaAtualizacao = DateTime.Now }
            });
        }
    }
}
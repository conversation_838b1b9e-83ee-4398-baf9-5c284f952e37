﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Validation;
using ATS.WS.Helpers;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.Webservice.Request;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Sistema.Framework.Util.Helper;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Application.Interface;

namespace ATS.WS.Services
{
    public class SrvCidade : SrvBase
    {
        private readonly ICidadeApp _cidadeApp;

        public SrvCidade(ICidadeApp cidadeApp)
        {
            _cidadeApp = cidadeApp;
        }

        /// <summary>
        /// Retorna a lista de cidades atualizadas a partir da data
        /// </summary>
        /// <param name="params"></param>
        /// <returns></returns>
        public Retorno<List<CidadeModel>> GetCidadesAtualizadas(ConsultarCidadeRequestModel @params)
        {
            try
            {
                var listaCidades = new List<CidadeModel>();
                
                var cidades = _cidadeApp.GetCidadesAtualizadas(@params.UF, @params.DataBase)
                    //.ProjectTo<CidadeModel>()
                    .AsNoTracking()
                    .ToList();

                foreach (var cidade in cidades)
                {
                    listaCidades.Add(new CidadeModel
                    {
                        Latitude = cidade.Latitude ?? 0, Longitude = cidade.Longitude ?? 0, Nome = cidade.Nome,
                        IdCidade = cidade.IdCidade, IdEstado = cidade.IdEstado, IBGE = cidade.IBGE
                    });
                }

                cidades.ForEach(c => c.Estado = null);

                return new Retorno<List<CidadeModel>>(true, string.Empty, listaCidades);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<CidadeModel>>($"{nameof(GetCidadesAtualizadas)} >> {e.Message}");
            }
        }

        public Retorno<List<CidadeModel>> ConsultarCidades(ConsultarCidadeRequestModel @params)
        {
            try
            {
                return new Retorno<List<CidadeModel>>(true, string.Empty,
                    Mapper.Map<List<Cidade>, List<CidadeModel>>(
                        _cidadeApp.ConsultarPaginadas(@params.CodigoIBGEEstado, @params.IBGE, @params.DataBase, @params.Take, @params.Skip).ToList()));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<List<CidadeModel>>($"{nameof(ConsultarCidades)} >> {e.Message}");
            }
        }
        public ValidationResult Cadastrar(CadastroCidadeRequestModel @params)
        {

            try
            {
                var validationResult = new ValidationResult();

                if (@params.Nome == null || @params.Nome.Length < 1)
                    validationResult.Add("Descrição é obrigatória");
                if (@params.IdPais < 1)
                    validationResult.Add("País é obrigatório");


                if (!validationResult.IsValid)
                    return validationResult;

                var cidade = new Cidade
                {
                    IdCidade = @params.IdCidade,
                    IdEstado = @params.IdEstado,
                    Nome = @params.Nome,
                    Ativo = true,
                    DataAlteracao = DateTime.Now,
                    IBGE = @params.IBGE,
                    Latitude = @params.Latitude,
                    Longitude = @params.Longitude
                };

                validationResult = _cidadeApp.Add(cidade);

                return !validationResult.IsValid ? validationResult : new ValidationResult();
            }



            catch (Exception e)
            {
                _logger.Error(e);
                return new ValidationResult().Add(e.Message);
            }
        }

        public Retorno<Cidade> GetCidadeMaisProxima(decimal latitude, decimal longitude)
        {
            try
            {
                return new Retorno<Cidade>(true, string.Empty, _cidadeApp.GetCidadeMaisProxima(latitude, longitude));
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new Retorno<Cidade>($"{nameof(GetCidadeMaisProxima)} >> {e.Message}");
            }
        }
    }
}
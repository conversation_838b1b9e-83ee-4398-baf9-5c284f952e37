﻿using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class CampanhaRespostaRepository : Repository<CampanhaResposta>, ICampanhaRespostaRepository
    {
        public CampanhaRespostaRepository(AtsContext context) : base(context)
        {
        }

        public bool ExisteResposta(int campanhaId, int idUsuario)
        {
            return Any(c => c.IdCampanha == campanhaId && c.IdUsuario == idUsuario);
        }

        public IQueryable<CampanhaResposta> GetRespostaSemNota(int campanhaId, int idUsuario)
        {
            return Where(c =>
                c.IdCampanha == campanhaId && c.IdUsuario == idUsuario && c.Respondida == false);
        }
    }
}
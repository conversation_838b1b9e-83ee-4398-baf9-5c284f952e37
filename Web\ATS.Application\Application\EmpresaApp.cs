﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Data.Repository.External.SistemaInfo;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Empresa;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using SistemaInfo.MicroServices.Rest.Pedagio.PedagioClient;

namespace ATS.Application.Application
{
    public class EmpresaApp : AppBase, IEmpresaApp
    {
        private readonly IEmpresaService _service;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly IUserIdentity _userIdentity;
        private readonly CartoesServiceArgs _cartoesServiceArgs;

        public EmpresaApp(IEmpresaService service, IEmpresaRepository empresaRepository, IUserIdentity userIdentity, CartoesServiceArgs cartoesServiceArgs)
        {
            _service = service;
            _empresaRepository = empresaRepository;
            _userIdentity = userIdentity;
            _cartoesServiceArgs = cartoesServiceArgs;
        }
        public Empresa GetAsNoTracking(int id)
        {
            return _service.GetAsNoTracking(id);
        }

        public bool AnyById(int id)
        {
            return _service.AnyById(id);
        }

        public bool EmpresaAgrupaProtocoloMesmoEvento(int id)
        {
            var empresaService = _service;
            return empresaService.EmpresaAgrupaProtocoloMesmoEvento(id);
        }

        public int? GetDiasExpiracaoCompraPedagio(int idEmpresa)
        {
            return _service.GetDiasExpiracaoCompraPedagio(idEmpresa);
        }

        public int? GetIdProdutoCartaoFretePadrao(int idEmpresa)
        {
            return _service.GetIdProdutoCartaoFretePadrao(idEmpresa);
        }

        public string GetLogoPorId(int idEmpresa)
        {
            return _service.GetLogoPorId(idEmpresa);
        }

        public ValidationResult Add(Empresa empresa)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _service.Add(empresa);

                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public ValidationResult AddEmpresaIndicadores(EmpresaIndicadores empresaIndicadores)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _service.AddEmpresaIndicadores(empresaIndicadores);

                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public ValidationResult Update(Empresa empresa)
        {
            ValidationResult validationResult;

            using (var transaction = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions {IsolationLevel = IsolationLevel.ReadCommitted}))
            {
                validationResult = _service.Update(empresa);

                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public ValidationResult UpdateEmpresaIndicadores(EmpresaIndicadores empresaIndicadores)
        {
            ValidationResult validationResult;

            using (TransactionScope transaction = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted }))
            {
                validationResult = _service.UpdateEmpresaIndicadores(empresaIndicadores);

                if (validationResult.IsValid)
                    transaction.Complete();
            }

            return validationResult;
        }

        public Empresa Get(int id, int? idUsuarioLogOn)
        {
            return _service.Get(id, idUsuarioLogOn);
        }

        public EmpresaIndicadores GetEmpresaIndicadores(int idEmpresa)
        {
            return _empresaRepository.GetEmpresaIndicadores(idEmpresa);
        }

        public Empresa GetWithChilds(int id)
        {
            return _service.GetWithChilds(id);
        }

        public int? GetIdPorCnpj(string cnpj)
        {
            return _empresaRepository.GetIdPorCnpj(cnpj);
        }

        public string GetCnpj(int id)
        {
            return _empresaRepository.GetCnpj(id);
        }

        public bool EmpresaValidaPagamentoFrete(int id)
        {
            return _empresaRepository.EmpresaValidaPagamentoFrete(id);
        }

        public IQueryable<Empresa> Consultar(string razaoSocial, int? idUsuarioLogOn)
        {
            return _service.Consultar(razaoSocial, idUsuarioLogOn);
        }

        public List<ConsultaTodasEmpresasDto> ConsultarTodas(bool? ativo = null)
        {
            return _service.ConsultarTodas(ativo);
        }
        
        public IQueryable<Empresa> All()
        {
            return _service.All();
        }

        public Empresa Get(int idEmpresa)
        {
            return _empresaRepository.Get(idEmpresa);
        }

        public bool ValidaChaveBaixaEvento(int idEmpresa)
        {
            return _service.ValidaChaveBaixaEvento(idEmpresa);
        }

        public bool ValidaChaveMhBaixaEvento(int idEmpresa)
        {
            return _service.ValidaChaveMhBaixaEvento(idEmpresa);
        }

        public string GetLogoPorCnpj(string cnpjEmpresa)
        {
            return _service.GetLogoPorCnpj(cnpjEmpresa);
        }

        public object GetParametrosPorCnpj(string cnpjEmpresa)
        {
            return _service.GetParametrosPorCnpj(cnpjEmpresa);
        }

        public bool GetPermissaoUsuarioJuridicoEmpresa(int idEmpresa)
        {
            return _empresaRepository.GetPermissaoUsuarioJuridicoEmpresa(idEmpresa);
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, List<int> idsEstabelecimento, int? idEmpresa)
        {
            return _service.ConsultarGrid(take, page, order, filters, idsEstabelecimento, idEmpresa);
        }

        public Empresa Get(string cnpj)
        {
            return _empresaRepository.Get(cnpj);
        }

        public List<Empresa> GetTodas()
        {
            return _empresaRepository.GetTodas();
        }

        public object GetTokenAdministradora()
        {
            return new
            {
                Token = SistemaInfoConsts.TokenAdministradora
            };
        }

        public ValidationResult AlterarStatus(int idEmpresa)
        {
            return _service.AlterarStatus(idEmpresa);
        }

        public object ConsultarGridEmpresa(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters)
        {
            return _service.ConsultarGridEmpresa(idEmpresa, take, page, orderFilters, filters);
        }

        public byte[] GerarRelatorioGridEmpresas(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao)
        {
            return _service.GerarRelatorioGridEmpresas(idEmpresa, orderFilters, filters, logo, extensao);
        }

        public IQueryable<Empresa> GetQuery(int idEmpresa)
        {
            return _empresaRepository.GetQuery(idEmpresa);
        }
        
        public string GetTokenMicroServices(int id)
        {
            return _empresaRepository.GetTokenMicroServices(id);
        }
        
        public bool AnyRntrc(int idEmpresa)
        {
            return _service.AnyRntrc(idEmpresa);
        }
        
        public EmpresaParametrosViagemDto GetParametrosViagem(int idEmpresa)
        {
            var response = new EmpresaParametrosViagemDto
            {
                FornecedoresPedagio = new List<FornecedorItem>()
            };

            var empresa = _service.All()
                .Where(x => x.IdEmpresa == idEmpresa)
                .Select(x => new
                {
                    x.PedagioTag,
                    x.ParcelaViagemAberta,
                    x.TokenMicroServices
                })
                .FirstOrDefault();

            if (empresa == null)
                return response;
            
            response.Parametros = new ParametrosPedagio()
            {
                PedagioTag = empresa.PedagioTag,
                ParcelaViagemAberta = empresa.ParcelaViagemAberta
            };

            var service = new CartoesService(_cartoesServiceArgs, idEmpresa, empresa.TokenMicroServices, 
                _userIdentity.CpfCnpj, _userIdentity.Nome);
            
            var parametros = service.ConsultarEmpresaPedagio().ConfigureAwait(false).GetAwaiter().GetResult();
            var parametrosPedagio = service.ConsultarParametrosPedagio().ConfigureAwait(false).GetAwaiter().GetResult();
            
            if(parametrosPedagio?.TagExtratta?.PermiteUtilizarFornecedor ?? false)
                response.FornecedoresPedagio.Add(new FornecedorItem(FornecedorEnum.ExtrattaTag));
            
            if(!string.IsNullOrWhiteSpace(parametros?.LoginAcessoViaFacil) || (parametros?.UtilizaCredenciaisExtrattaCompraViaFacil ?? false))
                response.FornecedoresPedagio.Add(new FornecedorItem(FornecedorEnum.ViaFacil));
            
            if(!string.IsNullOrWhiteSpace(parametrosPedagio?.LoginAcessoMoveMais) || (parametrosPedagio?.UtilizaCredenciaisExtrattaMoveMais ?? false))
                response.FornecedoresPedagio.Add(new FornecedorItem(FornecedorEnum.MoveMais));
            
            if(!string.IsNullOrWhiteSpace(parametrosPedagio?.Veloe?.TenantId) || (parametrosPedagio?.Veloe?.UtilizaCredenciaisExtrattaVeloe ?? false))
                response.FornecedoresPedagio.Add(new FornecedorItem(FornecedorEnum.Veloe));

            if (!string.IsNullOrWhiteSpace(parametrosPedagio?.LoginAcessoConectCar) || (parametrosPedagio?.ConectCar.UtilizaCredenciaisExtrattaCompraConectCar ?? false))
                response.FornecedoresPedagio.Add(new FornecedorItem(FornecedorEnum.ConectCar));

            if (!string.IsNullOrWhiteSpace(parametrosPedagio?.TaggyEdenred?.LoginAcesso) || (parametrosPedagio?.TaggyEdenred?.UtilizaCredenciaisExtrattaCompraTaggyEdenred ?? false))
                response.FornecedoresPedagio.Add(new FornecedorItem(FornecedorEnum.TaggyEdenred));

            return response;
        }

        public ConsultarParametroResponse GetParametrosPedagioEmpresa(int idEmpresa,string usuarioDocAudit = null, string nomeUsuarioAudit = null)
        {   
            var service = new CartoesService(_cartoesServiceArgs, idEmpresa, GetTokenMicroServices(idEmpresa), usuarioDocAudit,  nomeUsuarioAudit);
            
           return service.ConsultarParametrosPedagio().ConfigureAwait(false).GetAwaiter().GetResult();
        }

        public EmpresaParametrosVeiculoDto GetParametroVeiculoEmpresa(int? idEmpresa)
        {
            var empresaId = _userIdentity.Perfil == (int) EPerfil.Administrador ? idEmpresa : _userIdentity.IdEmpresa;
            
            var service = new CartoesService(_cartoesServiceArgs, empresaId, GetTokenMicroServices(empresaId ?? 0), _userIdentity.CpfCnpj, _userIdentity.Nome);
            
            var parametrosPedagio = service.ConsultarParametrosPedagio().ConfigureAwait(false).GetAwaiter().GetResult();

            return new EmpresaParametrosVeiculoDto()
            {
                UtilizaExtrattaTag = parametrosPedagio.TagExtratta.PermiteUtilizarFornecedor ?? false
            };
        }
        
        public object ConsultarGridEmpresasHubPedagio(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            return _service.ConsultarGridEmpresasHubPedagio(take, page, order, filters);
        }
    }
}
﻿using NLog;
using SistemaInfo.MicroServices.Rest.Infra.ApiClient;
using System;
using System.Threading;
using System.Web;

namespace ATS.Data.Repository.External.SistemaInfo.Infra
{
    public class WebhookExternalRepository : IDisposable
    {
        private const string ServicoIndisponivelResultMessage = "Serviço de notificação indisponível";

        //todo: mover essa classe para junto da infra

        private readonly NotificacaoClient _apiClient;
        private readonly string _token;
        private readonly string _documentoUsuarioAudit;
        private readonly string _nomeUusuarioAudit;

        public void Dispose()
        {
        }

        public WebhookExternalRepository(string token, string documentoUsuarioAudit, string nomeUsuarioAudit)
        {
            _apiClient = new global::SistemaInfo.MicroServices.Rest.Infra.ApiClient.NotificacaoClient(
                new SistemaInfoMicroServiceClientParams
                {
                    LogParams = RequestLogUtils.GetLogInfo(HttpContext.Current)
                });
            _apiClient.BaseUrl = SistemaInfoConsts.InfraApiUrl;

            _token = token;
            _documentoUsuarioAudit = documentoUsuarioAudit;
            _nomeUusuarioAudit = nomeUsuarioAudit;
        }

        public void Webhook(NotificacaoWebhookApiRequest request)
        {
            const int maxRetry = 2;
            var i = 0;
            while (i < maxRetry)
            {
                try
                {
                    _apiClient.Webhook(request, _token, _documentoUsuarioAudit, _nomeUusuarioAudit);
                    return;
                }
                catch (Exception e)
                {
                    i++;
                    Thread.Sleep(TimeSpan.FromSeconds(1));
                    if (i >= 2)
                    {
                        LogManager.GetCurrentClassLogger()
                            .Error(e,
                                ServicoIndisponivelResultMessage + " Request: " +
                                request.ToJson());
                        throw;
                    }
                }
            }
        }
    }
}
using System.Collections.Generic;
using SistemaInfo.MicroServices.Rest.Cartao.ApiClient;

namespace ATS.Domain.DTO
{
    public class ConsultarContasBancariasResponseDTO
    {
        public bool Sucesso { get; set; }
        public string Mensagem
        {
            get { return _mensagem; }
            set {_mensagem = value?.Trim();}
        }
        private string _mensagem { get; set; }
        public List<PessoaContaBancariaResponseDTO> Objeto { get; set; }
    }
    public class PessoaContaBancariaResponseDTO
    {
        public string NomeConta;
        public string CodigoBacenBanco;
        public string NomeBanco;
        public string Agencia;
        public string Conta;
        public int? DigitoConta;
        public PessoaContaBancariaResponseTipoConta? TipoConta;
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Request.TipoCavalo;
using ATS.WS.Models.Webservice.Response.TipoCavalo;
using ATS.WS.Services;
using AutoMapper;

namespace ATS.WS.ControllersATS
{
    public class TipoCavaloAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdentity;
        private readonly ITipoCavaloApp _tipoCavaloApp;
        private readonly SrvTipoCavalo _srvTipoCavalo;

        public TipoCavaloAtsController(IUserIdentity userIdentity, ITipoCavaloApp tipoCavaloApp, SrvTipoCavalo srvTipoCavalo)
        {
            _userIdentity = userIdentity;
            _tipoCavaloApp = tipoCavaloApp;
            _srvTipoCavalo = srvTipoCavalo;
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Consultar()
        {
            try
            {
                var tiposCavalo = _srvTipoCavalo.Consultar()
                    ?.Objeto?.Select(x => new
                    {
                        x.Nome,
                        x.IdTipoCavalo
                    });
                return ResponderSucesso(tiposCavalo);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorEmpresa(int? idEmpresa)
        {
            try
            {
                if (_userIdentity.Perfil == (int)EPerfil.Empresa)
                    idEmpresa = _userIdentity.IdEmpresa;
                
                var tiposCavalo =
                    _tipoCavaloApp.Consultar(string.Empty, idEmpresa).Where(o => o.Ativo).OrderBy(o => o.Nome);

                return ResponderSucesso(tiposCavalo);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarCapacidadeKgPorPlaca(string placa)
        {
            try
            {
                var tipoCavalo = _tipoCavaloApp.GetCapacidadePorPlaca(placa);
                return tipoCavalo.HasValue ? ResponderSucesso(new { Capacidade = tipoCavalo }) : ResponderErro("Capacidade não configurada.");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarSemEmpresa()
        {
            try
            {
                return ResponderSucesso(_srvTipoCavalo.ConsultarSemEmpresa());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                if (_userIdentity.Perfil != (int)EPerfil.Administrador)
                    idEmpresa = _userIdentity.IdEmpresa;

                var tipoCarreta = _tipoCavaloApp.ConsultaGrid(idEmpresa, take, page, order, filters);
                return ResponderSucesso(tipoCarreta);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }
        
        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult AlterarStatus(int id)
        {
            try
            {
                var validationResult = _tipoCavaloApp.AlterarStatus(id);

                return !validationResult.IsValid ? 
                    ResponderErro(validationResult.Errors.FirstOrDefault()?.Message) : 
                    ResponderSucesso("Status alterado com sucesso!");
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [EnableLogAudit]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult Cadastrar(TipoCavaloCadastrarRequest request)
        {
            try
            {
                var validacaoRequest = request.ValidarEntrada();

                if (!validacaoRequest.IsValid)
                    return ResponderErro(validacaoRequest.Errors.FirstOrDefault()?.ErrorMessage);
                
                if(_userIdentity.Perfil != (int)EPerfil.Administrador && request.IdEmpresa != _userIdentity.IdEmpresa)
                    return ResponderErro("Usuário não autenticado.");

                if (request.IdTipoCavalo > 0)
                {
                    var responseUpdate = _tipoCavaloApp.Update(Mapper.Map<TipoCavalo>(request));
                    
                    return responseUpdate.IsValid ? 
                        ResponderSucesso("Cadastro atualizado com sucesso.") : ResponderErro(responseUpdate.Errors.FirstOrDefault()?.Message);
                }

                var response = _tipoCavaloApp.Add(Mapper.Map<TipoCavalo>(request));
                return response.IsValid ? 
                    ResponderSucesso("Cadastro realizado com sucesso.") : ResponderErro(response.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar( EPerfil.Administrador, EPerfil.Empresa)]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarParaEditar(int id)
        {
            try
            {
                var tipoCavalo = _tipoCavaloApp.Get(id);

                return tipoCavalo == null ? 
                    ResponderErro($"Tipo cavalo {id} não encontrado na base de dados") : 
                    ResponderSucesso(Mapper.Map<TipoCavaloEditarResponse>(tipoCavalo));
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }
    }
}
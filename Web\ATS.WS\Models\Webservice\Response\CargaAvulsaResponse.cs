using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Enum;

namespace ATS.WS.Models.Webservice.Response
{
    public class CargaAvulsaResponse
    {
        public string NroControleIntegracao { get; set; }
        
        public List<CargaAvulsa> CargasAvulsas { get; set; } = new List<CargaAvulsa>();
    }
    
    public class CargaAvulsa
    {
        public int? IdCargaAvulsa { get; set; }

        public string CPF { get; set; }

        public decimal Valor { get; set; }

        public EStatusPagamentoCartao StatusProcessamento { get; set; }

        public string MensagemProcessamento { get; set; }
    }
}
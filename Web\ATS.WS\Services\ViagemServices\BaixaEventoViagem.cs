using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Data.Repository.External.Extratta.Biz.Client.Interfaces;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.WS.Models.Common.Request;
using ATS.WS.Models.Viagem;
using NLog;

namespace ATS.WS.Services.ViagemServices
{
    public class BaixaEventoViagem
    {
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IViagemApp _viagemApp;
        private readonly IClienteApp _clienteApp;
        private readonly IParametrosApp _parametrosApp;
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IProprietarioApp _proprietarioApp;
        private readonly ICadastrosApp _cadastrosApp;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly SrvViagem _srvViagem;
        private readonly ITransacaoCartaoApp _transacaoCartaoApp;
        private readonly IPagamentoFreteApp _pagamentoFreteApp;
        private readonly IEmpresaApp _empresaApp;
        private readonly IViagemEventoApp _viagemEventoApp;
        private readonly IViagemEventoService _viagemEventoService;
        private readonly IUsuarioService _usuarioService;
        private readonly IParametrosEmpresaService _parametrosEmpresaService;
        private readonly IParametrosProprietarioService _parametrosProprietarioService;
        private readonly IExtrattaBizApiClient _bizApiClient;

        public BaixaEventoViagem(IVersaoAnttLazyLoadService versaoAntt, IViagemApp viagemApp, IClienteApp clienteApp, IParametrosApp parametrosApp, ICiotV2App ciotV2App, ICiotV3App ciotV3App, 
            IProprietarioApp proprietarioApp, ICadastrosApp cadastrosApp, IEmpresaRepository empresaRepository, SrvViagem srvViagem, ITransacaoCartaoApp transacaoCartaoApp,
            IPagamentoFreteApp pagamentoFreteApp, IEmpresaApp empresaApp, IViagemEventoApp viagemEventoApp, IViagemEventoService viagemEventoService, IUsuarioService usuarioService, IParametrosEmpresaService parametrosEmpresaService, IParametrosProprietarioService parametrosProprietarioService, IExtrattaBizApiClient bizApiClient)
        {
            _versaoAntt = versaoAntt;
            _viagemApp = viagemApp;
            _clienteApp = clienteApp;
            _parametrosApp = parametrosApp;
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _proprietarioApp = proprietarioApp;
            _cadastrosApp = cadastrosApp;
            _empresaRepository = empresaRepository;
            _srvViagem = srvViagem;
            _transacaoCartaoApp = transacaoCartaoApp;
            _pagamentoFreteApp = pagamentoFreteApp;
            _empresaApp = empresaApp;
            _viagemEventoApp = viagemEventoApp;
            _viagemEventoService = viagemEventoService;
            _usuarioService = usuarioService;
            _parametrosEmpresaService = parametrosEmpresaService;
            _parametrosProprietarioService = parametrosProprietarioService;
            _bizApiClient = bizApiClient;
        }

        public BaixarEventoResponseModel BaixarEvento(BaixarEventoRequestModel @params, bool isApi = false)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return BaixarEventoV2(@params, isApi);
                case EVersaoAntt.Versao3:
                    return BaixarEventoV3(@params);
                default:
                    return BaixarEventoV2(@params, isApi);
            }
        }

        public BaixarEventoSaldoResponse BaixarEventoSaldo(BaixarEventoSaldoRequest request)
        {
            switch (_versaoAntt.Value)
            {
                case EVersaoAntt.Versao2:
                    return BaixarEventoSaldoV2(request);
                case EVersaoAntt.Versao3:
                    return BaixarEventoSaldoV3(request);
                default:
                    return BaixarEventoSaldoV2(request);
            }
        }

        private BaixarEventoResponseModel BaixarEventoV2(BaixarEventoRequestModel @params, bool isApi = false)
        {
            var empresaId = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);
            if (empresaId == null)
            {
                return new BaixarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Empresa não encontrada.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };
            }
            
            var queryViagemEvento = _viagemEventoApp.Find(x => x.IdEmpresa == (empresaId ?? 0) && x.Status != EStatusViagemEvento.Cancelado);

            queryViagemEvento = @params.IdViagem.HasValue 
                ? queryViagemEvento.Where(x => x.IdViagem == @params.IdViagem && x.IdViagemEvento == @params.IdViagemEvento) 
                : queryViagemEvento.Where(x => x.Viagem.NumeroControle == @params.NumeroControleViagem && x.NumeroControle == @params.NumeroControleEvento);

            var eventoBanco = queryViagemEvento.Select(c => new
            {
                c.Status,
                c.HabilitarPagamentoCartao,
                c.HabilitarPagamentoPix,
                c.Viagem.DataAtualizacao,
                c.Viagem.CPFCNPJProprietario
            }).FirstOrDefault();
            
            if (eventoBanco == null)
            {
                return new BaixarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };
            }
            
            /*if (eventoBanco.DataAtualizacao.HasValue && @params.DataAtualizacao.HasValue && @params.DataAtualizacao < eventoBanco.DataAtualizacao)
            {
                return new BaixarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem =
                        "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };
            }*/
            
            if (eventoBanco.Status == EStatusViagemEvento.Baixado)
            {
                var mensagemCartaoViagemAtual = string.Empty;
                var statusOperacaoCartaoAtual = ERetornoOperacaoCartao.NaoHabilitado;
                
                if (eventoBanco.HabilitarPagamentoCartao)
                {
                    var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == @params.IdViagemEvento).OrderByDescending(x => x.IdTransacaoCartao);

                    var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                    mensagemCartaoViagemAtual = transacaoCartao?.MensagemProcessamentoWs;
                    
                    if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Erro;
                    else
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Sucesso;
                }
                
                return new BaixarEventoResponseModel
                {
                    Sucesso = true,
                    Mensagem = "",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = statusOperacaoCartaoAtual,
                        Mensagem = mensagemCartaoViagemAtual
                    },
                    Token = string.Empty
                };
            }

            var naoBaixarParcelasDeposito = _parametrosEmpresaService.GetNaoBaixarParcelasDeposito(empresaId.Value);
            var empresaPix = _parametrosEmpresaService.GetPermiteRealizarPagamentoPix(empresaId.Value);
            if (naoBaixarParcelasDeposito && !eventoBanco.HabilitarPagamentoCartao && eventoBanco.HabilitarPagamentoPix != true && empresaPix)
            {
                var dadosPix = _bizApiClient.GetDadosBancariosPix(@params.CNPJEmpresa, eventoBanco.CPFCNPJProprietario)?.Value;
                if (string.IsNullOrWhiteSpace(dadosPix?.ChavePix))
                {
                    return new BaixarEventoResponseModel
                    {
                        Sucesso = false,
                        Mensagem = "Empresa configurada para não baixar parcelas depósito para proprietários sem chave Pix cadastrada.",
                        IdViagem = @params.IdViagem ?? -1,
                        NumeroControle = @params.NumeroControleViagem,
                        IdViagemEvento = @params.IdViagemEvento ?? -1,
                        NumeroControleEvento = @params.NumeroControleEvento,
                        OperacaoCartao = null,
                        Token = string.Empty
                    };
                }
            }
            
            var baixaEvento = queryViagemEvento
                .Select(x => new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = @params.CNPJEmpresa,
                    CNPJAplicacao = @params.CNPJAplicacao,
                    Token = @params.Token,
                    DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                    NomeUsuarioAudit = @params.NomeUsuarioAudit,
                    IdViagem = x.IdViagem,
                    NumeroControle = x.Viagem.NumeroControle,
                    PesoSaida = x.Viagem.PesoSaida,
                    PedagioBaixado = x.Viagem.PedagioBaixado,
                    ValorPedagio = x.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = x.Viagem.HabilitarDeclaracaoCiot,
                    NaturezaCarga = x.Viagem.NaturezaCarga,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>
                    {
                        new ViagemEventoIntegrarModel
                        {
                            IdViagemEvento = x.IdViagemEvento,
                            NumeroControle = x.NumeroControle,
                            CpfUsuario = @params.DocumentoUsuarioAudit,
                            NomeUsuario = @params.NomeUsuarioAudit,
                            TipoEvento = x.TipoEventoViagem,
                            IdViagem = x.IdViagem,
                            ValorPagamento = x.ValorPagamento,
                            Status = EStatusViagemEvento.Baixado,
                            HabilitarPagamentoCartao = @params.HabilitarPagamentoCartao ?? x.HabilitarPagamentoCartao,
                            HabilitarPagamentoPix = @params.HabilitarPagamentoPix ?? (x.HabilitarPagamentoPix == true && @params.HabilitarPagamentoCartao != true)
                        }
                    },
                    //DataAtualizacao = @params.DataAtualizacao
                }).FirstOrDefault();

            if (baixaEvento == null || baixaEvento.IdViagem <= 0 || baixaEvento.ViagemEventos[0].IdViagemEvento <= 0)
                return new BaixarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = ERetornoOperacaoCartao.Erro,
                        Mensagem = string.Empty
                    },
                    Token = string.Empty
                };

            var retorno = _srvViagem.Alterar(baixaEvento, isApi);

            var statusOperacaoCartao = ERetornoOperacaoCartao.NaoHabilitado;
            var mensagemProcessamentoWs = string.Empty;
            var idViagemEvento = baixaEvento.ViagemEventos[0].IdViagemEvento.Value;

            //TODO: para não precisar ir no banco de novo para o retorno teria que ter um DTO do retorno da viagem, pois não da pra fazer select nas propriedades de objeto anônimo fora do método de declaração dele
            if (baixaEvento.ViagemEventos[0].HabilitarPagamentoCartao)
            {
                var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                    .OrderByDescending(x => x.IdTransacaoCartao);

                var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                mensagemProcessamentoWs = transacaoCartao?.MensagemProcessamentoWs;
                if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                    statusOperacaoCartao = ERetornoOperacaoCartao.Erro;
                else
                    statusOperacaoCartao = ERetornoOperacaoCartao.Sucesso;
            }

            var viagemEvento = _viagemEventoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                .Select(x => new
                {
                    x.Status,
                    x.Token
                }).First();

            return new BaixarEventoResponseModel
            {
                Sucesso = retorno.Sucesso && viagemEvento.Status == EStatusViagemEvento.Baixado && statusOperacaoCartao != ERetornoOperacaoCartao.Erro,
                Mensagem = retorno.Mensagem,
                IdViagem = baixaEvento.IdViagem ?? 0,
                NumeroControle = baixaEvento.NumeroControle,
                IdViagemEvento = baixaEvento.ViagemEventos[0].IdViagemEvento ?? 0,
                NumeroControleEvento = baixaEvento.ViagemEventos[0].NumeroControle,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = statusOperacaoCartao,
                    Mensagem = mensagemProcessamentoWs
                },
                Token = viagemEvento.Token
            };
        }

        private BaixarEventoResponseModel BaixarEventoV3(BaixarEventoRequestModel @params)
        {
            var empresaApp = _empresaApp;
            var empresaId = empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            var viagemEventoApp = _viagemEventoApp;
            var queryViagemEvento = viagemEventoApp.Find(x => x.IdEmpresa == (empresaId ?? 0) && x.Status != EStatusViagemEvento.Cancelado);

            if (@params.IdViagem.HasValue)
                queryViagemEvento = queryViagemEvento.Where(x => x.IdViagem == @params.IdViagem && x.IdViagemEvento == @params.IdViagemEvento);
            else
                queryViagemEvento = queryViagemEvento.Where(x => x.Viagem.NumeroControle == @params.NumeroControleViagem && x.NumeroControle == @params.NumeroControleEvento);

            var viagemAtual = queryViagemEvento.Select(c => new
            {
                c.Status,
                c.HabilitarPagamentoCartao,
                c.Viagem.FormaPagamento,
                c.Viagem.DataAtualizacao
            }).FirstOrDefault();
            
            if(viagemAtual?.DataAtualizacao.HasValue == true && @params.DataAtualizacao.HasValue && 
               @params.DataAtualizacao < viagemAtual.DataAtualizacao)
                return new BaixarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Os dados da viagem estão desatualizados, a mesma foi atualizada em outro processo, carregue a viagem novamente",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = null,
                    Token = string.Empty
                };
            
            if (viagemAtual?.Status == EStatusViagemEvento.Baixado)
            {
                var mensagemCartaoViagemAtual = string.Empty;
                var statusOperacaoCartaoAtual = ERetornoOperacaoCartao.NaoHabilitado;
                
                if (viagemAtual.HabilitarPagamentoCartao)
                {
                    var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == @params.IdViagemEvento).OrderByDescending(x => x.IdTransacaoCartao);

                    var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                    mensagemCartaoViagemAtual = transacaoCartao?.MensagemProcessamentoWs;
                    
                    if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Erro;
                    else
                        statusOperacaoCartaoAtual = ERetornoOperacaoCartao.Sucesso;
                }
                
                return new BaixarEventoResponseModel
                {
                    Sucesso = true,
                    Mensagem = "",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = statusOperacaoCartaoAtual,
                        Mensagem = mensagemCartaoViagemAtual
                    },
                    Token = string.Empty
                };
            }
            
            var baixaEvento = queryViagemEvento
                .Select(x => new ViagemIntegrarRequestModel
                {
                    CNPJEmpresa = @params.CNPJEmpresa,
                    CNPJAplicacao = @params.CNPJAplicacao,
                    Token = @params.Token,
                    DocumentoUsuarioAudit = @params.DocumentoUsuarioAudit,
                    NomeUsuarioAudit = @params.NomeUsuarioAudit,
                    IdViagem = x.IdViagem,
                    NumeroControle = x.Viagem.NumeroControle,
                    PesoSaida = x.Viagem.PesoSaida,
                    PedagioBaixado = x.Viagem.PedagioBaixado,
                    ValorPedagio = x.Viagem.ValorPedagio,
                    HabilitarDeclaracaoCiot = x.Viagem.HabilitarDeclaracaoCiot,
                    NaturezaCarga = x.Viagem.NaturezaCarga,
                    ViagemEventos = new List<ViagemEventoIntegrarModel>
                    {
                        new ViagemEventoIntegrarModel
                        {
                            IdViagemEvento = x.IdViagemEvento,
                            NumeroControle = x.NumeroControle,
                            CpfUsuario = @params.DocumentoUsuarioAudit,
                            NomeUsuario = @params.NomeUsuarioAudit,
                            TipoEvento = x.TipoEventoViagem,
                            IdViagem = x.IdViagem,
                            ValorPagamento = x.ValorPagamento,
                            Status = EStatusViagemEvento.Baixado,
                        }
                    },
                    DataAtualizacao = @params.DataAtualizacao
                }).FirstOrDefault();

            if (baixaEvento == null || baixaEvento.IdViagem <= 0 || baixaEvento.ViagemEventos[0].IdViagemEvento <= 0)
                return new BaixarEventoResponseModel
                {
                    Sucesso = false,
                    Mensagem = "Não foi possível encontrar um evento com os parâmetros informados, ou o evento está cancelado.",
                    IdViagem = @params.IdViagem ?? -1,
                    NumeroControle = @params.NumeroControleViagem,
                    IdViagemEvento = @params.IdViagemEvento ?? -1,
                    NumeroControleEvento = @params.NumeroControleEvento,
                    OperacaoCartao = new OperacaoCartaoBaixaEvento
                    {
                        Status = ERetornoOperacaoCartao.Erro,
                        Mensagem = string.Empty
                    },
                    Token = string.Empty
                };

            var retorno = _srvViagem.Alterar(baixaEvento);

            var statusOperacaoCartao = ERetornoOperacaoCartao.NaoHabilitado;
            var mensagemProcessamentoWs = string.Empty;
            var idViagemEvento = baixaEvento.ViagemEventos[0].IdViagemEvento.Value;

            //TODO: para não precisar ir no banco de novo para o retorno teria que ter um DTO do retorno da viagem, pois não da pra fazer select nas propriedades de objeto anônimo fora do método de declaração dele
            if (viagemAtual?.FormaPagamento == EViagemFormaPagamento.Cartao)
            {
                var query = _transacaoCartaoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                    .OrderByDescending(x => x.IdTransacaoCartao);

                var transacaoCartao = query.Select(x => new {x.MensagemProcessamentoWs, x.StatusPagamento}).FirstOrDefault();

                mensagemProcessamentoWs = transacaoCartao?.MensagemProcessamentoWs;
                if (transacaoCartao?.StatusPagamento == EStatusPagamentoCartao.Erro)
                    statusOperacaoCartao = ERetornoOperacaoCartao.Erro;
                else
                    statusOperacaoCartao = ERetornoOperacaoCartao.Sucesso;
            }

            var viagemEvento = viagemEventoApp.Find(x => x.IdViagemEvento == idViagemEvento)
                .Select(x => new
                {
                    x.Status,
                    x.Token
                }).First();

            return new BaixarEventoResponseModel
            {
                Sucesso = retorno.Sucesso && viagemEvento.Status == EStatusViagemEvento.Baixado && statusOperacaoCartao != ERetornoOperacaoCartao.Erro,
                Mensagem = retorno.Mensagem,
                IdViagem = baixaEvento.IdViagem ?? 0,
                NumeroControle = baixaEvento.NumeroControle,
                IdViagemEvento = baixaEvento.ViagemEventos[0].IdViagemEvento ?? 0,
                NumeroControleEvento = baixaEvento.ViagemEventos[0].NumeroControle,
                OperacaoCartao = new OperacaoCartaoBaixaEvento
                {
                    Status = statusOperacaoCartao,
                    Mensagem = mensagemProcessamentoWs
                },
                Token = viagemEvento.Token
            };
        }
        
        private BaixarEventoSaldoResponse BaixarEventoSaldoV2(BaixarEventoSaldoRequest request)
        {
            try
            {
                var pagamentoFreteApp = _pagamentoFreteApp;
                var viagemEventoService = _viagemEventoService;

                // Evento
                var evento = viagemEventoService.GetEventosViagemQuery(new List<int> {request.IdViagemEvento})
                    .Include(x => x.Viagem)
                    .Include(x => x.Viagem.ViagemDocumentosFiscais)
                    .Include(x => x.ViagemDocumentos)
                    .FirstOrDefault();
                
                if (evento == null || evento.IdViagem != request.IdViagem)
                    return new BaixarEventoSaldoResponse
                    {                        
                        Mensagem = $"Parcela {request.IdViagemEvento} não localizada para viagem {request.IdViagem}"
                    };

                if (evento.TipoEventoViagem != ETipoEventoViagem.Saldo)
                    return new BaixarEventoSaldoResponse
                    {
                        Mensagem = "Somente parcelas do tipo 'Saldo' são permitidas neste método. " +
                                   $"{evento.TipoEventoViagem} não autorizado."
                    };
                
                // Usuário para vincular operação - Se não existir o CPF/CNPJ cadastrado no ATS.
                // Segue em diante com o usuário genérico de integraçãao - Implemetação para go-live, verificar posteriormente a viabilidade de inserir um usuário com perfil novo de "auditoria"
                var usuarioQuitacao = _usuarioService
                    .GetByCpfQuery(request.CpfUsuarioQuitacao)
                    .AsNoTracking().FirstOrDefault();
                
                // Consultar dados do evento
                var efetuarPagamentoFreteModel = pagamentoFreteApp.ConsultarPorToken(evento.Token, usuarioQuitacao?.CPFCNPJ, usuarioQuitacao?.Nome, new List<int>());

                // Calcular valores
                var calculoValores = pagamentoFreteApp.CalcularValoresViagem(evento.Token, evento.HabilitarPagamentoCartao, request.PesoChegada, null,
                    EUnidadeMedida.Peso, false);

                // Baixar evento
                efetuarPagamentoFreteModel.Saldo = calculoValores.ValorTotalPagamento;
                efetuarPagamentoFreteModel.ValorEvento = calculoValores.ValorTotalPagamento;
                efetuarPagamentoFreteModel.PesoChegada = request.PesoChegada;
                efetuarPagamentoFreteModel.Evento.PesoChegada = request.PesoChegada;
                efetuarPagamentoFreteModel.Evento.QuebraMercadoria = calculoValores.QuebraMercadoria;
                efetuarPagamentoFreteModel.Evento.QuebraMercadoriaCalculada = calculoValores.QuebraMercadoriaCalculada;
                efetuarPagamentoFreteModel.Evento.DifFreteMotorista = calculoValores.DifFreteMotorista;
                efetuarPagamentoFreteModel.AbonoRequisitado = false;
                efetuarPagamentoFreteModel.ValorQuebra = calculoValores.QuebraMercadoria;
                efetuarPagamentoFreteModel.DataDescarga = request.DataDescarga ?? DateTime.Now;
                efetuarPagamentoFreteModel.HabilitarPagamentoCartao = evento.HabilitarPagamentoCartao;

                // TODO: Ajustar a nova versão do pagamento de frete
                var baixaResult = pagamentoFreteApp.EfetuarPagamento(efetuarPagamentoFreteModel, usuarioQuitacao, false, request.AdministradoraPlataforma, new List<int>());

                if (!baixaResult.IsValid)
                    return new BaixarEventoSaldoResponse
                    {
                        Mensagem = $"Falha ao baixar parcela de saldo: {baixaResult}"
                    };

                return new BaixarEventoSaldoResponse {Sucesso = true, Mensagem = baixaResult.ToString()};

            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e, $"Erro ao baixar parcela de saldo. IdViagem: {request.IdViagem} - IdViagemEvento: {request.IdViagemEvento}");
                return new BaixarEventoSaldoResponse
                {
                    Sucesso = false,
                    Mensagem = "Falha ao baixar parcela de saldo: " + e.Message
                };
            }
        }
        
        private BaixarEventoSaldoResponse BaixarEventoSaldoV3(BaixarEventoSaldoRequest request)
        {
            try
            {
                var pagamentoFreteApp = _pagamentoFreteApp;
                var viagemEventoService = _viagemEventoService;

                // Evento
                var evento = viagemEventoService.GetEventosViagemQuery(new List<int> {request.IdViagemEvento})
                    .Include(x => x.Viagem)
                    .Include(x => x.Viagem.ViagemDocumentosFiscais)
                    .Include(x => x.ViagemDocumentos)
                    .FirstOrDefault();

                if (evento == null || evento.IdViagem != request.IdViagem)
                    return new BaixarEventoSaldoResponse
                    {
                        Mensagem = $"Parcela {request.IdViagemEvento} não localizada para viagem {request.IdViagem}"
                    };

                if (evento.TipoEventoViagem != ETipoEventoViagem.Saldo)
                    return new BaixarEventoSaldoResponse
                    {
                        Mensagem = "Somente parcelas do tipo 'Saldo' são permitidas neste método. " +
                                   $"{evento.TipoEventoViagem} não autorizado."
                    };

                // Usuário para vincular operação - Se não existir o CPF/CNPJ cadastrado no ATS.
                // Segue em diante com o usuário genérico de integraçãao - Implemetação para go-live, verificar posteriormente a viabilidade de inserir um usuário com perfil novo de "auditoria"
                var usuarioQuitacao = _usuarioService
                    .GetByCpfQuery(request.CpfUsuarioQuitacao)
                    .AsNoTracking().FirstOrDefault();

                // Consultar dados do evento
                var efetuarPagamentoFreteModel = pagamentoFreteApp.ConsultarPorToken(evento.Token,
                    usuarioQuitacao?.CPFCNPJ, usuarioQuitacao?.Nome, new List<int>());

                // Calcular valores
                var calculoValores = pagamentoFreteApp.CalcularValoresViagem(evento.Token,
                    evento.HabilitarPagamentoCartao, request.PesoChegada, null,
                    EUnidadeMedida.Peso, false);

                // Baixar evento
                efetuarPagamentoFreteModel.Saldo = calculoValores.ValorTotalPagamento;
                efetuarPagamentoFreteModel.ValorEvento = calculoValores.ValorTotalPagamento;
                efetuarPagamentoFreteModel.PesoChegada = request.PesoChegada;
                efetuarPagamentoFreteModel.Evento.PesoChegada = request.PesoChegada;
                efetuarPagamentoFreteModel.Evento.QuebraMercadoria = calculoValores.QuebraMercadoria;
                efetuarPagamentoFreteModel.Evento.QuebraMercadoriaCalculada = calculoValores.QuebraMercadoriaCalculada;
                efetuarPagamentoFreteModel.Evento.DifFreteMotorista = calculoValores.DifFreteMotorista;
                efetuarPagamentoFreteModel.AbonoRequisitado = false;
                efetuarPagamentoFreteModel.ValorQuebra = calculoValores.QuebraMercadoria;
                efetuarPagamentoFreteModel.DataDescarga = request.DataDescarga ?? DateTime.Now;
                efetuarPagamentoFreteModel.HabilitarPagamentoCartao = evento.HabilitarPagamentoCartao;

                var baixaResult = pagamentoFreteApp.EfetuarPagamento(efetuarPagamentoFreteModel, usuarioQuitacao, false,
                    request.AdministradoraPlataforma, new List<int>());

                if (!baixaResult.IsValid)
                    return new BaixarEventoSaldoResponse
                    {
                        Mensagem = $"Falha ao baixar parcela de saldo: {baixaResult}"
                    };

                return new BaixarEventoSaldoResponse {Sucesso = true, Mensagem = baixaResult.ToString()};
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e,
                    $"Erro ao baixar parcela de saldo. IdViagem: {request.IdViagem} - IdViagemEvento: {request.IdViagemEvento}");
                return new BaixarEventoSaldoResponse
                {
                    Sucesso = false,
                    Mensagem = "Falha ao baixar parcela de saldo: " + e.Message
                };
            }
        }
    }
}
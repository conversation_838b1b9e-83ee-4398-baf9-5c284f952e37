﻿using System.Collections.Generic;
using System.Net;

namespace ATS.Data.Repository.External.Extratta.Models
{
    public class IntegracaoResult<TValue>
    {
        public readonly bool Success;
        public readonly IList<string> Messages;
        public readonly TValue Value;

        public IntegracaoResult(bool sucess, IList<string> messages)
        {
            Success = sucess;
            Messages = messages;
        }

        public IntegracaoResult(bool sucess, IList<string> messages, TValue value)
        {
            Success = sucess;
            Messages = messages;

            Value = value;
        }

        public static IntegracaoResult<TValue> Valid(TValue value)
        {
            var messages = new List<string>();

            return new IntegracaoResult<TValue>(true, messages, value);
        }

        public static IntegracaoResult<TValue> Valid(string message, TValue obj)
        {
            var messages = new List<string>();

            return new IntegracaoResult<TValue>(true, messages, obj);
        }

        public static IntegracaoResult<TValue> Error(string message)
        {
            var messages = new List<string>
            {
                message
            };

            return new IntegracaoResult<TValue>(false, messages);
        }

        public static IntegracaoResult<TValue> Error(IList<string> messages)
        {
            return new(false, messages);
        }

        public override string ToString()
        {
            return string.Join(", ", Messages);
        }
    }

    public class IntegracaoResult
    {
        public readonly bool Success;
        public readonly IList<string> Messages;

        private IntegracaoResult(bool sucess, IList<string> messages)
        {
            Success = sucess;
            Messages = messages;
        }

        public static IntegracaoResult Valid()
        {
            var messages = new List<string>();

            return new IntegracaoResult(true, messages);
        }

        public static IntegracaoResult Valid(string message)
        {
            var messages = new List<string>();

            return new IntegracaoResult(true, messages);
        }

        public static IntegracaoResult Error(string message)
        {
            var messages = new List<string>
            {
                message
            };

            return new IntegracaoResult(false, messages);
        }

        public static IntegracaoResult Error(IList<string> messages)
        {
            return new(false, messages);
        }

        public override string ToString()
        {
            return string.Join(", ", Messages);
        }
    }
}
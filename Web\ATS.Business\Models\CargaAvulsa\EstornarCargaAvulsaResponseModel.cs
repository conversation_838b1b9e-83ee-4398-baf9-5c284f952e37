using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Enum.ValidationsType.CargaAvulsa;
using Newtonsoft.Json;
using Sistema.Framework.Util.Helper;

namespace ATS.Domain.Models.CargaAvulsa
{
    public class EstornarCargaAvulsaResponseModel
    {
        public bool Sucesso { get; set; }
        public string Mensagem { get; set; }
        public EstornoCargaAvulsaModel Objeto { get; set; } = new EstornoCargaAvulsaModel();
        public EstornarCargaAvulsaResponseModel(bool sucesso, string mensagem, int? idCargaAvulsa = null, TransacaoCartao transacaoCartao = null)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
            Objeto.CodigoRetorno = sucesso ? ECodigoEstornarCargaAvulsaResponse.Sucesso : ECodigoEstornarCargaAvulsaResponse.Erro;

            if (idCargaAvulsa.HasValue)
                Objeto.IdCargaAvulsa = idCargaAvulsa;

            if (transacaoCartao == null)
                return;

            Objeto.StatusPagamento = transacaoCartao.StatusPagamento;
            Objeto.ValorMovimentado = transacaoCartao.ValorMovimentado;
            Objeto.MensagemProcessamentoWs = transacaoCartao.MensagemProcessamentoWs;
            Objeto.NumeroProtocoloWs = transacaoCartao.NumeroProtocoloWs;

            if (!sucesso && transacaoCartao.StatusPagamento.In(EStatusPagamentoCartao.Baixado, EStatusPagamentoCartao.Pendente))
                Objeto.CodigoRetorno = ECodigoEstornarCargaAvulsaResponse.CargaJaEstornada;
            else if (transacaoCartao.StatusPagamento == EStatusPagamentoCartao.Pendente)
                Objeto.CodigoRetorno = ECodigoEstornarCargaAvulsaResponse.TransacaoPendente;
        }

        public class EstornoCargaAvulsaModel
        {
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public int? IdCargaAvulsa { get; set; }
            public EStatusPagamentoCartao StatusPagamento { get; set; }
        
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public decimal? ValorMovimentado { get; set; }
        
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public string MensagemProcessamentoWs { get; set; }
        
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public long? NumeroProtocoloWs { get; set; }
        
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public ECodigoEstornarCargaAvulsaResponse CodigoRetorno { get; set; }
        }
    }
}
﻿using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Interface.Service
{
    public interface IEmpresaContaBancariaService : IService<EmpresaContaBancaria>
    {
        IQueryable<EmpresaContaBancaria> GetAll();
        EmpresaContaBancaria Inserir(EmpresaContaBancaria contaBancaria);
        void Update(EmpresaContaBancaria contaBancaria);
        EmpresaContaBancaria GetByEmpresaId(int idEmpresa);

    }
}
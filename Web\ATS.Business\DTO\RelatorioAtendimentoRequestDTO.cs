using System;
using ATS.Domain.Models.Grid.Base;

namespace ATS.Domain.DTO
{
    public class RelatorioAtendimentoRequestDTO : FiltrosGridBaseModel
    {
        public string Protocolo { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public int? Portador { get; set; }
        public int? Atendente { get; set; }
    }

    public class ConsultaRelatorioAtendimentoDTO
    {
        public string Protocolo { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public int Portador { get; set; }
        public int? Atendente { get; set; }
    }
}
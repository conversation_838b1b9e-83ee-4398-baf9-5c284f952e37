using System;
using System.Linq;
using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.WS.Models.Common.Request;
using NLog;

namespace ATS.WS.Services.ViagemServices
{
    public class WebHookViagem
    {
        private readonly IWebhookApp _webhookApp;

        public WebHookViagem(IWebhookApp webhookApp)
        {
            _webhookApp = webhookApp;
        }

        public void SetarWebhooks(ViagemIntegrarRequestModel @params, Viagem viagem)
        {
             //viagem.Empresa.TokenMicroServices, usuarioLogado?.CPFCNPJ, usuarioLogado?.Nome
            // Callback
            Webhook registredWebhook;
            if (@params.Webhook != null)
                foreach (var content in @params.Webhook)
                {
                    WebhookTipoEvento hookType;

                    if (!Enum.TryParse(content.Key, out hookType))
                    {
                        LogManager.GetCurrentClassLogger()
                            .Error("Erro ao identificar tipo da webhook de viagem: " + content.Key);
                        continue;
                    }

                    var headerToString = content.Value?.Headers != null && content.Value.Headers.Any()
                        ? string.Join(
                                Environment.NewLine,
                                content.Value.Headers.Select(k => k.Key + "=" + k.Value))
                            .Trim()
                        : string.Empty;

                    registredWebhook = _webhookApp.GetByIdRegistro(viagem.IdViagem, hookType);
                    var webhookApp = _webhookApp;

                    if (registredWebhook == null)
                    {
                        registredWebhook = new Webhook
                        {
                            IdRegistro = viagem.IdViagem,
                            Tipo = hookType,
                            Headers = headerToString,
                            Endpoint = content.Value.Endpoint,
                            Tempo = content.Value.Tempo,
                            Aplicacao = content.Value.Aplicacao
                        };
                        webhookApp.Add(registredWebhook);
                    }
                    else
                    {
                        registredWebhook.Headers = headerToString;
                        registredWebhook.Endpoint = content.Value.Endpoint;
                        registredWebhook.Tempo = content.Value.Tempo;
                        registredWebhook.Aplicacao = content.Value.Aplicacao;
                        webhookApp.Update(registredWebhook);
                    }
                }
        }
    }
}
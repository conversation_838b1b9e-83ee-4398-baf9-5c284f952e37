﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Webservice.Response;
using AutoMapper;
using JsonResult = System.Web.Mvc.JsonResult;

namespace ATS.WS.ControllersATS
{
    public class GestaoViagensAtsController : DefaultController
    {
        private readonly IUserIdentity _userIdenitty;
        private readonly IViagemApp _viagemApp;

        public GestaoViagensAtsController(IUserIdentity userIdenitty, IViagemApp viagemApp)
        {
            _userIdenitty = userIdenitty;
            _viagemApp = viagemApp;
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public  JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var viagensGestao = _viagemApp.ConsultaViagensPendentes(_userIdenitty.IdEmpresa);

                if (order?.Campo == "StatusStr") order.Campo = "Status";

                viagensGestao = string.IsNullOrWhiteSpace(order?.Campo)
                ? viagensGestao.OrderBy(x => x.DataStatus??x.DataCadastro)
                : viagensGestao.AplicarOrderByDinamicos(order);

                viagensGestao = viagensGestao.AplicarFiltrosDinamicos(filters);

                var viagensMapped = Mapper.Map<List<ViagemPendenteGestor>, List<GestaoViagensGridResponse>>(viagensGestao.Skip((page - 1) * take).Take(take)
                 .ToList());

                return ResponderSucesso(new
                {
                    totalItems = viagensGestao.Count(),
                    items = viagensMapped
                });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult AlterarViagemPendente(int idViagem, int idBloqueioGestorTipo, int status, string motivo, int idUsuario) 
        {
            try
            {
                if (!_userIdenitty.IdEmpresa.HasValue)
                    throw new Exception("Nenhuma empresa vinculada a este usuário.");

                if (!_viagemApp.PertenceAEmpresa(_userIdenitty.IdEmpresa.Value, idViagem))
                    throw new Exception("Registro não encontrado.");

                if (_userIdenitty.Perfil == (int) EPerfil.Empresa)
                {
                    idUsuario = _userIdenitty.IdUsuario;
                }

                var bloqueioGestorTipo = (EBloqueioGestorTipo) idBloqueioGestorTipo;
                var response = _viagemApp.AlterarViagemPendente(idViagem, bloqueioGestorTipo, status, idUsuario, motivo);

                return !response.IsValid ? ResponderErro(response.Errors.Select(c => c.Message).First()) : ResponderSucesso("Operação realizada com sucesso!");
            }
            catch (Exception e) 
            {
                return ResponderErro(e);
            }
        }
    }
}
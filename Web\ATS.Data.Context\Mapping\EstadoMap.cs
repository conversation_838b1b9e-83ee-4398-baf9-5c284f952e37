using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;
using ATS.Domain.Entities;

namespace ATS.Data.Context.Mapping
{
    public class EstadoMap : EntityTypeConfiguration<Estado>
    {
        public EstadoMap()
        {
            ToTable("ESTADO");

            HasKey(t => t.IdEstado);

            Property(t => t.IdEstado)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Nome)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Sigla)
                .IsRequired()
                .IsFixedLength()
                .HasMaxLength(2);

            Property(t => t.IBGE)
                .IsOptional();

            Property(t => t.DataAlteracao)
                .IsRequired()
                .HasColumnType("datetime2");

            HasRequired(a => a.Pais)
                .WithMany(b => b.Estados)
                .HasForeignKey(c => c.IdPais);
        }
    }
}
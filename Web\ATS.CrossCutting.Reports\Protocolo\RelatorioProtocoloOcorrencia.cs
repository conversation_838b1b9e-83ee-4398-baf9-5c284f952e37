﻿
using System;

namespace ATS.CrossCutting.Reports.Protocolo
{

    public class RelatorioProtocoloComOcorrencia
    {
        public int? IdProtocolo { get; set; }
        public string NomeFantasia { get; set; }
        public string Estabelecimento { get; set; }
        public DateTime? DataOcorrencia { get; set; }
        public decimal? ValorProtocolo { get; set; }
        public string UsuarioOcorrencia { get; set; }
    }

    public class RelatorioProtocoloOcorrencia {
        public int? IdProtocoloEvento { get; set; }
        public string Token { get; set; }
        public decimal? ValorTotalPagamento { get; set; }
        public string DataOcorrencia { get; set; }
        public string TipoEventoViagem { get; set; }
        public string DetalhamentoOcorrencia { get; set; }
        public string MotivoOcorrencia { get; set; }
    }

    public class RelatorioEventosVinculados
    {
        public int? IdProtocolo { get; set; }
        public decimal? Valor { get; set; }
        public string Estabelecimento { get; set; }
        public string Evento { get; set; }
    }
}

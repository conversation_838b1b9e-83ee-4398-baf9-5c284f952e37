﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ProtocoloMap : EntityTypeConfiguration<Protocolo>
    {
        public ProtocoloMap()
        {
            ToTable("PROTOCOLO");

            HasKey(t => t.IdProtocolo);

            Property(t => t.ValorProtocolo)
                .HasPrecision(10, 2);

            Property(t => t.DataRejeicao)
                .IsOptional();

            Property(t => t.TiposEventos)
                .HasMaxLength(100)
                .IsOptional();

            Property(t => t.TiposPagamentos)
                .IsRequired();

            Property(t => t.PossuiEventoReincidente)
                .IsRequired();

            HasRequired(t => t.EstabelecimentoBase)
                .WithMany(t => t.Protocolos)
                .HasForeignKey(t => t.IdEstabelecimentoBase);

            HasRequired(t => t.Empresa)
                .WithMany(t => t.Protocolos)
                .HasForeignKey(t => t.IdEmpresa);

            HasOptional(t => t.EmpresaDestinatario)
                .WithMany(t => t.ProtocolosDestinatario)
                .HasForeignKey(t => t.IdEmpresaDestinatario);

            HasOptional(t => t.EstabelecimentoDestinatario)
                .WithMany(t => t.ProtocolosDestinatario)
                .HasForeignKey(t => t.IdEstabelecimentoDestinatario);
            
            HasOptional(t => t.UsuarioAprovacao)
                .WithMany(t => t.Protocolos)
                .HasForeignKey(t => t.IdUsuarioAprovacao);
        }
    }
}
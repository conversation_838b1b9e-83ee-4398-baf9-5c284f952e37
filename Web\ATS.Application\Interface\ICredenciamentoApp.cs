﻿using System;
using System.Collections.Generic;
using ATS.CrossCutting.Reports.Credenciamento;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Grid;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ICredenciamentoApp
    {
        ValidationResult Add(Credenciamento credenciamento, int administradoraPlataforma);

        ValidationResult Add(EstabelecimentoBase estabelecimentoBase, List<int> idsEmpresa, int? idEstabelecimentoBase, int? idEstabelecimento, EStatusCredenciamento status,
            EStatusDocumentacaoCredenciamento statusDocumentacao, int administradoraPlataforma, bool emailSolicitacaoRecebida = false);
        bool AtualizarStatusDocumentacao(int idCredenciamento_, EStatusDocumentacaoCredenciamento status_, int administradoraPlataforma, string motivo = null);
        Credenciamento Get(int IdCredenciamento);
        Credenciamento Get(int idEmpresa, int idEstabelecimento);
        ValidationResult Update(Credenciamento credenciamento, int administradoraPlataforma);
        Credenciamento GetCredenciamentoPorProtocolo(int idProtocolo);

        byte[] GerarRelatorioGridCredenciamento(int? idEmpresa,
            int Take,
            int Page,
            OrderFilters Order,
            List<QueryFilters> Filters, string extensao, string logo);

        bool GetAllCredenciamentosIrregulares(List<int> idsEstabelecimentos_);
        bool VerificarEstabelecimentoBaseAssociacao(int idEstabelecimentoBase);
        bool EstabelecimentoCredenciado(int idEstabelecimento, int idEmpresa);
        List<int> GetIdsEmpresaSolicitadoCredenciamento(int idEstabelecimentoBase);
        ValidationResult AprovarCredenciamento(int idCredenciamento, string linkWebNovo, int administradoraPlataforma);


        void BloquearCredenciamento(int idCredenciamento);
        ValidationResult Cancelar(int IdCredenciamento);
        bool ChaveValida(string chaveEmail);
        DataModel<CredenciamentoModel> ConsultaGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> Filters);
        object ConsultaGrid(int? idEmpresa, string descricao, int take, int page, OrderFilters order, List<QueryFilters> filters);
        object ConsultarAnexosCredenciamento(int idEstabelecimento);
        object ConsultarCredenciamentosPorEmpresa(int idEmpresa, DateTime? dtIni, DateTime? dtFim, int administradoraPlataforma, bool aberto = false, bool aprovado = false, bool rejeitado = false, bool bloqueado = false, bool regular = true, bool irregular = true, bool aguardando = true, int? idEstabelecimento = null);
        object ConsultarGridAprovados(int IdEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters);
        object ConsultarGridPendentes(int IdEstabelecimento, int take, int page, OrderFilters order, List<QueryFilters> filters);
        object ConsultarImagemPorToken(string Token);
        void DesbloquearCredenciamento(int idCredenciamento);
        void Descredenciar(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma);
        ValidationResult EnviarEmailCredenciamentoCadastroUsuario(EstabelecimentoBase estabelecimentoBase, int idEmpresa, string chave, int administradoraPlataforma);
        List<CredenciamentoMotivo> GetAllMotivos(int idCredencimento_);
        Credenciamento GetByChaveEmail(string chaveEmail);
        object GetDetalhesRejeicaoCredenciamento(int idCredenciamento);
        bool HasDocumentacaoVencendo(int idCredenciamento);
        void ReenviarEmailCredenciamentoUsuarioCadastro(int id, string linkWebNovo, int administradoraPlataforma);
        void RejeitarCredenciamento(int idCredenciamento, int idMotivo, string detalhamento, int administradoraPlataforma);
        ValidationResult ValidarNovoUsuarioEstabelecimento(string chaveEmail, int idEstabelecimento);
    }
}
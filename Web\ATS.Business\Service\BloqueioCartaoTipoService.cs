using System.Linq;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;

namespace ATS.Domain.Service
{
    public class BloqueioCartaoTipoService : ServiceBase, IBloqueioCartaoTipoService
    {
        private readonly IBloqueioCartaoTipoRepository _bloqueioCartaoTipoRepository;

        public BloqueioCartaoTipoService(IBloqueioCartaoTipoRepository bloqueioCartaoTipoRepository)
        {
            _bloqueioCartaoTipoRepository = bloqueioCartaoTipoRepository;
        }

        public IQueryable<BloqueioCartaoTipo> GetAll()
        {
            return _bloqueioCartaoTipoRepository.GetAll();
        }
    }
}
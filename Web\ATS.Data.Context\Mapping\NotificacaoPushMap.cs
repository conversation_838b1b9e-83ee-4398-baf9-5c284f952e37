﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class NotificacaoPushMap : EntityTypeConfiguration<NotificacaoPush>
    {
        public NotificacaoPushMap()
        {
            ToTable("NOTIFICACAO_PUSH");

            HasKey(x => x.IdNotificacaoPush);

            Property(x => x.Descricao)
                .IsRequired();

            Property(x => x.DescricaoMensagem)
                .HasMaxLength(200)
                .IsRequired();

            Property(x => x.Sql)
                .HasMaxLength(5000)
                .IsRequired();

            Property(x => x.MomentoExecucao)
                .IsRequired();

            HasRequired(x => x.Empresa)
                .WithMany(x => x.NotificacoesPush)
                .HasForeignKey(x => x.IdEmpresa);

            HasOptional(x => x.Filial)
                .WithMany(x => x.NotificacoesPush)
                .HasForeignKey(x => x.IdFilial);

            HasRequired(x => x.TipoNotificacao)
                .WithMany(x => x.NotificacoesPush)
                .HasForeignKey(x => x.IdTipoNotificacao);

        }
    }
}

using System.Collections.Generic;
using ATS.Application.Interface.Common;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Models.AtendimentoPortador;
using ATS.Domain.Models.Parametro;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IParametrosApp : IBaseApp<IParametrosService>
    {
        ValidationResult SetPercentualTransferenciaFreteGenerico(PercentualTransferenciaFreteViagemParametro percentuais);

        ValidationResult SetIdTipoEstabelecimentoPadraoJSL(int idTipoEstabelecimento);

        ValidationResult SetPercentualTransferenciaFreteProprietario(PercentualTransferenciaFreteViagemParametro percentuais, string documento);

        ValidationResult SetPercentualTransferenciaFreteProprietarioMotorista(PercentualTransferenciaFreteViagemParametro percentuais, string documentoProprietario, string documentoMotorista);

        ValidationResult SetAutorizaEstabelecimentosRedeJSL(bool valor, int idEmpresa);

        ValidationResult SetParametroProprietarioTransferencia(PROPRIETARIO_DOCUMENTO parametro, decimal? valor, string documento);

        ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(AcaoSaldoResidualNovoCreditoCartaoPedagio percentuais, string documento);

        bool GetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa);

        ValidationResult SetProprietarioPermiteReceberPagamentoPix(string documento, int idEmpresa, bool valor);

        ValidationResult SetValorLimitePagamentoCheque(decimal? valor, int idEmpresa);

        ValidationResult SetSeriePadraoCheque(string valor, int idEmpresa);

        ValidationResult SetQuantidadeLimiteImpressoesCheque(decimal? valor, int idEmpresa);
        ValidationResult SetMarginTopImpressaoCheque(decimal? valor, int idEmpresa);

        ValidationResult SetMarginLeftImpressaoCheque(decimal? valor, int idEmpresa);

        ValidationResult SetEndpointIntegracaoCheque(string valor, int idEmpresa);

        ValidationResult SetHeadersIntegracaoCheque(string valor, int idEmpresa);

        ValidationResult SetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(AcaoSaldoResidualNovoCreditoCartaoPedagio valor, int idEmpresa);

        ValidationResult SetPermissaoUsuarioAlterarLimiteAlcadas(PermissaoUsuarioAlterarLimiteAlcadas valor, int idusuario);
        ValidationResult SetHorasExpiracaoCreditoPedagio(int horas, int idEmpresa);
        ValidationResult SetEmailsAlertaCiotAgregado(string emails, int idEmpresa);
        ValidationResult SetDiasCancelamentoViagem(int? dias, int idEmpresa);

        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagioProprietario(string documento);

        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagioEmpresa(int idEmpresa);

        PermissaoUsuarioAlterarLimiteAlcadas GetPermissaoUsuarioAlterarLimiteAlcadas(int idusuario);

        decimal? GetValorLimitePagamentoCheque(int idEmpresa);

        string GetSeriePadraoCheque(int idEmpresa);

        decimal? GetQuantidadeLimiteImpressoesCheque(int idEmpresa);

        decimal? GetMarginTopImpressaoCheque(int idEmpresa);

        decimal? GetMarginLeftImpressaoCheque(int idEmpresa);

        string GetEndpointIntegracaoCheque(int idEmpresa);

        string GetHeadersIntegracaoCheque(int idEmpresa);

        PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteGenerico();

        int GetIdTipoEstabelecimentoPadraoJSL();

        int GetIdEmpresaPadraoUsuarioEstabelecimentoJSL();

        PercentualTransferenciaFreteViagemParametro GetPercentualTransferenciaFreteProprietario(string documento);

        ConsultarParametroCartaoDTO GetPercentualTransferenciaCartaoProprietario(string documento);

        ConsultarParametroCartaoDTO GetPercentualTransferenciaCartaoGenerico();

        bool GetAutorizaEstabelecimentosRedeJSL(int idEmpresa);
        int? GetHorasExpiracaoCreditoPedagio(int idEmpresa);
        ValidationResult SetObrigatoriedadeArmazemEmpresa(bool? valor, int idEmpresa);

        bool? GetObrigatoriedadeArmazemEmpresa(int idEmpresa);

        ValidationResult SetObrigatoriedadeOrdemCompraEmpresa(bool? valor, int idEmpresa);

        bool? GetObrigatoriedadeOrdemCompraEmpresa(int idEmpresa);

        ValidationResult SetObrigatoriedadeFormulaEmpresa(bool? valor, int idEmpresa);

        bool? GetObrigatoriedadeFormulaEmpresa(int idEmpresa);

        ValidationResult SetObrigatoriedadePedidoEmpresa(bool? valor, int idEmpresa);

        bool? GetObrigatoriedadePedidoEmpresa(int idEmpresa);

        ValidationResult SetObrigatoriedadeProtocolo(bool? valor, int idEmpresa);

        bool? GetObrigatoriedadeProtocolo(int idEmpresa);

        ValidationResult SetObrigatoriedadeQuantidadeEmpresa(bool? valor, int idEmpresa);

        bool? GetObrigatoriedadeQuantidadeEmpresa(int idEmpresa);

        ValidationResult SetObrigatoriedadeArmazemFilial(bool? valor, int idEmpresa, int idFilial);

        bool? GetObrigatoriedadeArmazemFilial(int idEmpresa, int idFilial);

        ValidationResult SetObrigatoriedadeOrdemCompraFilial(bool? valor, int idEmpresa, int idFilial);

        bool? GetObrigatoriedadeOrdemCompraFilial(int idEmpresa, int idFilial);

        ValidationResult SetObrigatoriedadeFormulaFilial(bool? valor, int idEmpresa, int idFilial);

        bool? GetObrigatoriedadeFormulaFilial(int idEmpresa, int idFilial);

        ValidationResult SetObrigatoriedadePedidoFilial(bool? valor, int idEmpresa, int idFilial);

        bool? GetObrigatoriedadePedidoFilial(int idEmpresa, int idFilial);

        ValidationResult SetObrigatoriedadeProtocoloFilial(bool? valor, int idEmpresa, int idFilial);

        bool? GetObrigatoriedadeProtocoloFilial(int idEmpresa, int idFilial);

        ValidationResult SetObrigatoriedadeQuantidadeFilial(bool? valor, int idEmpresa, int idFilial);

        bool? GetObrigatoriedadeQuantidadeFilial(int idEmpresa, int idFilial);

        AcaoSaldoResidualNovoCreditoCartaoPedagio GetAcaoSaldoResidualNovoCreditoCartaoPedagio(string documento);

        bool GetObrigaRoteirizacaoPedagioViagemEmpresa(int idEmpresa);

        ValidationResult SetObrigaRoteirizacaoPedagioViagemEmpresa(bool valor, int idEmpresa);
        int? GetIdUsuarioGenericoWS();

        bool? GetMostrarHeaderArquivoCsv(int idEmpresa);

        ValidationResult SetMostrarHeaderArquivoCsv(bool? valor, int idEmpresa);

        string GetSeparadorArquivoCsv(int idEmpresa);

        ValidationResult SetSeparadorArquivoCsv(string valor, int idEmpresa);

        bool? HasTransferenciaFrete(IList<ETipoEventoViagem> tiposEvento, string proprietarioDocumento, int idempresa, string motoristaDocumento);

        string GetInformacoesTransferenciaBancaria(int idEmpresa);

        string GetBancoPadraoCheque(int idEmpresa);
        ValidationResult SetBancoPadraoCheque(string valor, int idEmpresa);
        ValidationResult SetTokenAdministradora(string valor, string idregistro, int idEmpresa);
        string GetTokenAdministradora(int idAdministradora);
        bool ValidaCnpjCpfProprietarioNaViagem();
        int GetIdLayoutCartao();
        int GetIdProdutoCartaoFrete();
        bool? GetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa);

        ValidationResult SetValidarDocumentosViagemComDocumentosDasIntegracoes(int idEmpresa, bool valor);
        ValidationResult SetReemiteCiotPadraoAlteracaoViagem(bool valor, int idEmpresa);
        bool? GetReemiteCiotPadraoAlteracaoViagem(int idEmpresa);
        string GetEmailsAlertaCiotAgregado(int idEmpresa);
        int GetDiasCancelamentoViagem(int idEmpresa);
        bool? GetPermiteConsultarTodasViagensEmpresa(int idEmpresa);
        int GetTipoCargaAnttDefault();
        int? GetVersaoAntt();
        PermissoesEmpresaAtendimentoPortador GetPermissoesEmpresaAtendimentoCartao(int idEmpresa);
        ValidationResult SetPermissoesEmpresaAtendimentoCartao(int idEmpresa, PermissoesEmpresaAtendimentoPortador permissoes);
        bool GetAtendimentoPermiteBloquearCartao(int idEmpresa);
        bool GetAtendimentoPermiteDesbloquearCartao(int idEmpresa);
        bool GetAtendimentoPermiteAlterarSenhaCartao(int idEmpresa);
        bool GetAtendimentoPermiteRealizarTransferenciaBancaria(int idEmpresa);
        bool GetAtendimentoPermiteRealizarTransferenciaCartoes(int idEmpresa);
        bool GetAtendimentoPermiteRealizarResgate(int idEmpresa);
        bool GetAtendimentoPermiteRealizarEstornoResgate(int idEmpresa);
        string GetTokenMicroServicoCentralAtendimento(int idEmpresa);
        ValidationResult SetTokenMicroServicoCentralAtendimento(int idEmpresa, string token);
        PermissoesUsuarioAtendimentoPortador GetNovoUsuarioPermissoesAtendimentoCartao(int idEmpresa);
        PermissoesUsuarioAtendimentoPortador GetPermissoesUsuarioAtendimentoCartao(int idUsuario, int idEmpresa);
        ValidationResult SetPermissoesUsuarioAtendimentoCartao(int idUsuario, PermissoesUsuarioAtendimentoPortador permissoes);
        bool GetOcultarListagemTerceiros(int idEmpresa);
        bool CadastraSomentePerfilEmpresa(int idEmpresa);
        string GetCaminhoEmailRecuperacaoSenhaAppPlataforma(int idAdministradoraPlataforma);
        string GetCaminhoLogo(int idAdministradoraPlataforma);
        ConfiguracaoEnvioEmail GetConfiguracaoEmailPlataforma(int idAdministradoraPlataforma);
        string GetKeyEnvioPush(int idAdministradoraPlataforma);
        int GetIdProjetoFireBase(int idusuario);
        ValidationResult SetIdProjetoFireBase(int idUsuario, int? idAdministradora);
        bool GetRegistrarValePedagio(int idEmpresa);
        ValidationResult SetRegistrarValePedagio(int idEmpresa, bool enviar);
        int GetGrupoContabilizacaoCentralAtendimento();
        ValidationResult SetRealizaTriagemEstabelecimentoInterno(int idEmpresa, bool? realizaTriagemEstabelecimentoInterno);
        bool GetRealizaTriagemEstabelecimentoInterno(int idEmpresa);
        ValidationResult SetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa, bool? parametro);
        bool? GetMantemViagemAbertaAposCancelamentoDoUltimoEvento(int idEmpresa);
        ValidationResult SetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa, bool? parametro);
        bool? GetPermiteCadastrarMotoristaComCpfFicticio(int idEmpresa);
        ValidationResult SetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa, bool? parametro);
        bool? GetPermiteCadastrarProprietarioComCpfFicticio(int idEmpresa);
        ValidationResult SetPermiteVincularCartaoComCpfFicticio(int idEmpresa, bool? parametro);
        bool? GetPermiteVincularCartaoComCpfFicticio(int idEmpresa);
        bool? GetEstabelecimentoValidarChavePagamento(int idEstabelecimento);

        ValidationResult SetEstabelecimentoValidarChavePagamento(int idEstabelecimento, bool validarChavePagamento);
        int GetMotivoPadraoBloqueioCartaoEmpresa();
        bool GetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario);
        bool GetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario);
        ValidationResult SetAplicativoPermiteRealizarTransferenciaBancaria(int idusuario, bool value);
        ValidationResult SetAplicativoPermiteRealizarTransferenciaCartoes(int idusuario, bool value);
        ValidationResult SetParametroEmpresaGridListarCnpjDespesasViagem(bool listar, int idEmmpresa);
        bool GetParametroEmpresaGridListarCnpjDespesasViagem(int idEmpresa);
        ValidationResult SetCodigoOfx(string listar, int idEmpresa);
        string GetCodigoOfx(int idEmpresa);
        
        /// <summary>
        /// EXTDEV-188: "Não pode ser menor que 0.2 (%), que é a taxa cobrada pela MM."
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        ValidationResult SetTagExtrattaTaxaVpo(int idEmpresa, decimal? value);
        decimal GetTagExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetTagExtrattaValorTag(int idEmpresa, decimal? value);
        decimal GetTagExtrattaValorTag(int idEmpresa);
        ValidationResult SetTagExtrattaValorMensalidade(int idEmpresa, decimal? value);
        decimal GetTagExtrattaValorMensalidade(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarValor(int idEmpresa, bool? value);
        bool GetTagExtrattaProvisionarValor(int idEmpresa);
        ValidationResult SetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaFaixaToleranciaNotificacaoEmail(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaProvisionarTaxaPedagioWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaProvisionarValorPedagioWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaSaldoMinimoContaFreteWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaBloquearTagLoteWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaBloquearTagLoteWebhook(int idEmpresa);
        ValidationResult SetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa, bool? parametro);
        bool GetTagExtrattaBloquearTagUnitariaWebhook(int idEmpresa);
        decimal GetTagExtrattaValorSubstituicao(int idEmpresa);
        ValidationResult SetTagExtrattaValorSubstituicao(int idEmpresa, decimal? parametro);
        decimal GetTagExtrattaValorRecargaConta(int idEmpresa);
        ValidationResult SetTagExtrattaValorRecargaConta(int idEmpresa, decimal? parametro);
        string GetTagExtrattaCnpjProvisionamento();
        ValidationResult SetTagExtrattaProvisionarTaxa(int idEmpresa, bool? value);
        bool GetTagExtrattaProvisionarTaxa(int idEmpresa);
        string GetUserWebhook();
        string GetSenhaWebhook();
        ValidationResult SetParametroUsuarioPermitirAcessoAtendimento(int idUsuario, bool value);
        bool GetParametroUsuarioPermitirAcessoAtendimento(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosEmpresa(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosFilial(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosUsuario(int idUsuario);
        bool GetPermitirEdicaoDadosAdministrativosGrupoUsuario(int idUsuario);
        bool GetPermitirAcessoExtratoDetalhadoUsuario(int idUsuario);
        ValidationResult SetPermitirAcessoExtratoDetalhadoUsuario(int idUsuario, bool value);
        decimal GetValorMinimoAlertaSaldoContaFrete(int idEmpresa);
        decimal GetValorMinimoAlertaSaldoContaPix(int idEmpresa);
        ValidationResult SetValorMinimoAlertaSaldoContaFrete(int idEmpresa, decimal? valor);
        ValidationResult SetValorMinimoAlertaSaldoContaPix(int idEmpresa, decimal? valor);
        ValidationResult SetTagExtrattaEstornarPedagio(int idEmpresa, bool? parametro);
        bool GetTagExtrattaEstornarPedagio(int idEmpresa);
        bool GetPermiteAprovarSolicitacaoAdiantamentoApp(int idUsuario);
        ValidationResult SetPermiteAprovarSolicitacaoAdiantamentoApp(int idUsuario, bool value);
        bool GetPermiteSolicitarAdiantamentoApp(int idUsuario);
        ValidationResult SetPermiteSolicitarAdiantamentoApp(int idUsuario, bool value);
        bool GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idUsuario);
        ValidationResult SetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(int idUsuario, bool value);
        ValidationResult SetMoveMaisExtrattaTaxaVpo(int idEmpresa, decimal? value);
        decimal GetMoveMaisExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetVeloeExtrattaTaxaVpo(int idEmpresa, decimal? value);
        decimal GetVeloeExtrattaTaxaVpo(int idEmpresa);
        bool GetTagExtrattaUtilizaTaxaPedagio(int idEmpresa);
        ValidationResult SetTagExtrattaUtilizaTaxaPedagio(int idEmpresa, bool? parametro);
        ValidationResult SetViaFacilExtrattaTaxaVpo(int idEmpresa, decimal? value);
        decimal GetViaFacilExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetLimiteDiarioPagamentoPixUsuario(int idUsuario, decimal? parametro);
        ValidationResult SetLimiteUnitarioPagamentoPixUsuario(int idUsuario, decimal? parametro);
        ValidationResult SetHubMoveMaisProvisionarValor(int idEmpresa, bool? value);
        bool GetHubMoveMaisProvisionarValor(int idEmpresa);
        ValidationResult SetHubMoveMaisProvisionarTaxa(int idEmpresa, bool? value);
        bool GetHubMoveMaisProvisionarTaxa(int idEmpresa);
        bool GetHubConectCarProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubConectCarProvisionarTaxa(int idEmpresa, bool? value);
        bool GetHubConectCarProvisionarValor(int idEmpresa);
        ValidationResult SetHubConectCarMaisProvisionarValor(int idEmpresa, bool? value);
        ValidationResult SetHubViaFacilProvisionarValor(int idEmpresa, bool? value);
        bool GetHubViaFacilProvisionarValor(int idEmpresa);
        ValidationResult SetHubViaFacilProvisionarTaxa(int idEmpresa, bool? value);
        bool GetHubViaFacilProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubVeloeProvisionarValor(int idEmpresa, bool? value);
        bool GetHubVeloeProvisionarValor(int idEmpresa);
        ValidationResult SetHubVeloeProvisionarTaxa(int idEmpresa, bool? value);
        bool GetHubVeloeProvisionarTaxa(int idEmpresa);
        ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa, bool? parametro);
        bool GetSolicitaAprovacaoGestorCargaAvulsaLote(int idEmpresa);
        ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa, bool? parametro);
        bool GetSolicitaAprovacaoGestorCargaAvulsaUnitario(int idEmpresa);
        ValidationResult SetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa, bool? parametro);
        bool GetSolicitaAprovacaoGestorCargaAvulsaIntegracao(int idEmpresa);
        ValidationResult SetConectCarExtrattaTaxaVpo(int idEmpresa, decimal? value);
        decimal GetConectCarExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetUtilizaRelatoriosOfx(int idEmpresa, bool? value);
        bool GetUtilizaRelatoriosOfx(int idEmpresa);
        ValidationResult SetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa, bool? value);
        bool GetMantemViagemAbertaAposBaixaDoUltimoEvento(int idEmpresa);
        bool GetDefaultIntegracaoTipoRodagemDupla(int idEmpresa);
        ValidationResult SetDefaultIntegracaoTipoRodagemDupla(int idEmpresa, bool? parametro);
        decimal GetLimitePadraoTransferenciaCartaoCNPJ();
        decimal GetLimitePadraoTransferenciaCartaoCPF();
        decimal GetLimitePadraoTransferenciaTEDCNPJ();
        decimal GetLimitePadraoTransferenciaTEDCPF();
        bool GetHubTaggyEdenredProvisionarValor(int idEmpresa);
        ValidationResult SetHubTaggyEdenredProvisionarValor(int idEmpresa, bool? parametro);
        bool GetHubTaggyEdenredProvisionarTaxa(int idEmpresa);
        ValidationResult SetHubTaggyEdenredProvisionarTaxa(int idEmpresa, bool? parametro);
        decimal GetTaggyEdenredExtrattaTaxaVpo(int idEmpresa);
        ValidationResult SetTaggyEdenredExtrattaTaxaVpo(int idEmpresa, decimal? parametro);
        bool GetBloqueiaCargaAvulsaDuplicada(int idEmpresa);
        ValidationResult SetBloqueiaCargaAvulsaDuplicada(int idEmpresa, bool? parametro);
        decimal? GetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa);
        ValidationResult SetHorasBloqueioCargaAvulsaDuplicada(int idEmpresa, decimal? parametro);
        bool GetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa);
        ValidationResult SetRodagemDuplaQuandoVem3OuMaisEixosMoveMaisVeloe(int idEmpresa, bool? parametro);
        ValidationResult SetNaoBaixarParcelasDeposito(int idEmpresa, bool? value);
        bool GetNaoBaixarParcelasDeposito(int idEmpresa);
        bool GetUtilizaRoteirizacaoPorPolyline(int idEmpresa);
        ValidationResult SetUtilizaUtilizaRoteirizacaoPorPolyline(int idEmpresa, bool? value);
    }
}

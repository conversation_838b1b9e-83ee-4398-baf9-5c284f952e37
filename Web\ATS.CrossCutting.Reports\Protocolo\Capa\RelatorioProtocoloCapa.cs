﻿using System;
using System.Collections.Generic;

namespace ATS.CrossCutting.Reports.Protocolo.Capa
{
    public class RelatorioProtocoloCapa
    {
        public byte[] GetReport(List<RelatorioProtocoloCapaDataType> listaDados, string logo)
        {
            var parametrizes = new Tuple<string, string, bool>[1];
            parametrizes[0] = new Tuple<string, string, bool>("Logo", logo, true);

            var bytes = new Base.Reports().GetReport(listaDados, parametrizes, true, "DtsRelatorioProtocoloCapa",
                "ATS.CrossCutting.Reports.Protocolo.Capa.RelatorioProtocoloCapa.rdlc",
                "pdf");

            return bytes;
        }
    }
}

﻿using ATS.Domain.Enum;
using ATS.Domain.Extensions;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Models;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Net.Mail;
using System.Net.Mime;
using System.Threading.Tasks;
using System.Web.Configuration;
using ATS.CrossCutting.Reports.Usuarios.RelatorioUsuarios;
using ATS.Domain.Models.Usuario;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO.Usuario;
using ATS.Domain.Interface.Dapper;
using Sistema.Framework.Util.Extension;
using MongoDB.Bson.Serialization.Serializers;
using ATS.Domain.DTO;
using System.Net;
using ATS.Domain.Entities;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Transactions;
using ATS.CrossCutting.IoC.Interfaces;
using NLog;

namespace ATS.Domain.Service
{
    // Importante:
    // Todos os veículos que forem enviados no objeto de usuário deverão ser sempre vinculados através da chave de IdUsuario ou IdProprietário.
    // Este vinculo visa possibilitar a leitura dos veículos vinculados a qual o usuário é 'dono'.

    public class UsuarioService : ServiceBase, IUsuarioService
    {
        #region Constantes

        /// <summary>
        /// Hash básico da senha
        /// </summary>
        private const string Md5HashPassword = "|2d331cca-f6c0-40c0-bb43-6e32989c2881";
        private readonly IUsuarioRepository _repository;
        private readonly IUsuarioDapper _dapperRepository;
        private readonly IProprietarioRepository _proprietarioRepository;
        private readonly ITipoCarretaRepository _tipoCarretaRepository;
        private readonly IUsuarioEnderecoRepository _usuarioEnderecoRepository;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IUsuarioPreferenciasRepository _usuarioPreferenciasRepository;
        private readonly ITipoCavaloRepository _tipoCavaloRepository;
        private readonly IUsuarioFilialRepository _usuarioFilialRepository;
        private readonly IUsuarioEstabelecimentoRepository _usuarioEstabelecimentoRepository;
        private readonly IGestorUsuarioRepository _gestorUsuarioRepository;
        private readonly ILayoutRepository _layoutRepository;
        private readonly IEmailService _emailService;
        private readonly IMotoristaRepository _motoristaRepository;
        private readonly IUsuarioPermissaoGestorRepository _usuarioPermissaoGestorRepository;
        private readonly IUsuarioPermissaoFinanceiroRepository _usuarioPermissaoFinanceiroRepository;
        private readonly IVeiculoRepository _veiculoRepository;
        private readonly IMenuService _menuService;
        private readonly IGrupoUsuarioService _grupoUsuarioService;
        private readonly IUsuarioDocumentoService _usuarioDocumentoService;
        private readonly IEmpresaRepository _empresaRepository;
        private readonly ILocalizacaoUsuarioPortalRepository _localizacaoUsuarioPortalRepository;
        private readonly KeycloakHelper _keycloak;
        private readonly IParametrosGenericoService _parametrosGenericoService;
        private readonly IParametrosAdministradoraPlataformaService _parametrosAdministradoraService;
        private readonly IPushService _pushService;
        private readonly IParametrosUsuarioService _parametrosUsuarioService;

        #endregion

        public UsuarioService(IUsuarioRepository repository, IUsuarioDapper dapperRepository, IProprietarioRepository proprietarioRepository,
            ITipoCarretaRepository tipoCarretaRepository, IUsuarioEnderecoRepository usuarioEnderecoRepository, IUsuarioRepository usuarioRepository,
            IUsuarioPreferenciasRepository usuarioPreferenciasRepository, ITipoCavaloRepository tipoCavaloRepository, IUsuarioFilialRepository usuarioFilialRepository,
            IUsuarioEstabelecimentoRepository usuarioEstabelecimentoRepository, ILayoutRepository layoutRepository, IEmailService emailService, IMotoristaRepository motoristaRepository,
            IUsuarioPermissaoGestorRepository usuarioPermissaoGestorRepository, IVeiculoRepository veiculoRepository, IMenuService menuService, IGrupoUsuarioService grupoUsuarioService,
            IUsuarioDocumentoService usuarioDocumentoService, IEmpresaRepository empresaRepository,IUsuarioPermissaoFinanceiroRepository usuarioPermissaoFinanceiroRepository,
            KeycloakHelper keycloak, ILocalizacaoUsuarioPortalRepository localizacaoUsuarioPortalRepository, IParametrosGenericoService parametrosGenericoService, IParametrosAdministradoraPlataformaService parametrosAdministradoraService, IPushService pushService, IGestorUsuarioRepository gestorUsuarioRepository, IParametrosUsuarioService parametrosUsuarioService)
        {
            _repository = repository;
            _dapperRepository = dapperRepository;
            _proprietarioRepository = proprietarioRepository;
            _tipoCarretaRepository = tipoCarretaRepository;
            _usuarioEnderecoRepository = usuarioEnderecoRepository;
            _usuarioRepository = usuarioRepository;
            _usuarioPreferenciasRepository = usuarioPreferenciasRepository;
            _tipoCavaloRepository = tipoCavaloRepository;
            _usuarioFilialRepository = usuarioFilialRepository;
            _usuarioEstabelecimentoRepository = usuarioEstabelecimentoRepository;
            _layoutRepository = layoutRepository;
            _emailService = emailService;
            _motoristaRepository = motoristaRepository;
            _usuarioPermissaoGestorRepository = usuarioPermissaoGestorRepository;
            _usuarioPermissaoFinanceiroRepository = usuarioPermissaoFinanceiroRepository;
            _veiculoRepository = veiculoRepository;
            _menuService = menuService;
            _grupoUsuarioService = grupoUsuarioService;
            _usuarioDocumentoService = usuarioDocumentoService;
            _empresaRepository = empresaRepository;
            _keycloak = keycloak;
            _localizacaoUsuarioPortalRepository = localizacaoUsuarioPortalRepository;
            _parametrosGenericoService = parametrosGenericoService;
            _parametrosAdministradoraService = parametrosAdministradoraService;
            _pushService = pushService;
            _gestorUsuarioRepository = gestorUsuarioRepository;
            _parametrosUsuarioService = parametrosUsuarioService;
        }

        private ValidationResult IsValidToCrud(Usuario usuario, EProcesso processo, int? idUsuario, string  cnpjEmpresa = "")
        {
            ValidationResult validationResult = new ValidationResult();

            #region Validações do Perfil Logado e Ação

            if (!idUsuario.HasValue || idUsuario.Value <= 0)
            {
                // Se não possuir o ID do usuário, será um cadastro novo feito por alguém fora do sistema.
                // Neste caso somente será possível que seja 'Motorista'
                if (usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario && usuario.Perfil != EPerfil.Estabelecimento &&
                    usuario.Perfil != EPerfil.Mesa && usuario.Perfil != EPerfil.Embarcador && usuario.Perfil != EPerfil.Empresa)
                    return new ValidationResult()
                        .Add($"Cadastro inválido. Somente é possível adicionar 'Motorista', para maiores informações entre em contato com o SistemaInfo.");
            }
            else
            {
                switch (_repository.GetPerfil(idUsuario.GetValueOrDefault()))
                {
                    case EPerfil.Motorista:
                    case EPerfil.Cliente:
                    case EPerfil.Proprietario:

                        if (processo == EProcesso.Create)
                            return validationResult.Add($"Seu perfil não permite inserir informações.");

                        if (idUsuario != usuario.IdUsuario)
                            return
                                validationResult.Add(
                                    $"Você não pode modificar informações que não sejam do seu usuário.");

                        break;

                    case EPerfil.Empresa:

                        if (processo == EProcesso.Update && usuario.Perfil != EPerfil.Motorista)
                        {
                            var idEmpresa = _repository.GetIdEmpresa(usuario.IdUsuario);
                            if (idEmpresa != null && idEmpresa != usuario.IdEmpresa)
                                return
                                    validationResult.Add(
                                        $"Usuário que se deseja modificar não esta vinculado a sua Empresa.");
                        }

                        break;
                }
            }

            #endregion

            #region Geral
            var idUsuarioToken = GetIdByTokenFirebase(usuario.TokenFirebase);
            if (!string.IsNullOrEmpty(usuario.TokenFirebase)
                && (idUsuarioToken.HasValue)
                && (idUsuarioToken.Value != usuario.IdUsuario))
                return new ValidationResult().Add($"Token informado ja está sendo utilizado por outro usuário!");

            // Sendo 'Motorista' somente poderá ser pessoa 'Física'
            if (usuario.Perfil == EPerfil.Motorista &&
                (!string.IsNullOrWhiteSpace(usuario.CPFCNPJ) && usuario.CPFCNPJ.Length > 11))
                return new ValidationResult().Add($"Não é permitido cadastrar um motorista como pessoa jurídica.");

            var permiteUsuarioJuridico = usuario.IdEmpresa.HasValue && usuario.IdEmpresa > 0
                ? _empresaRepository.GetPermissaoUsuarioJuridicoEmpresa(usuario.IdEmpresa ?? 0)
                : !string.IsNullOrEmpty(cnpjEmpresa) && _empresaRepository.GetPermissaoUsuarioJuridicoCnpjEmpresa(cnpjEmpresa);

            if (!permiteUsuarioJuridico)
            {
                validationResult.Add(usuario.TipoCobranca == ETipoCobranca.PessoaJuridica
                    ? AssertionConcern.AssertArgumentIsValidCNPJ(usuario.CPFCNPJ, "CNPJ informado é inválido.")
                    : AssertionConcern.AssertArgumentIsValidCPF(usuario.CPFCNPJ, "CPF informado é inválido."));
            }
            else
            {
                if (usuario.CPFCNPJ.Length > 11)
                    validationResult.Add(AssertionConcern.AssertArgumentIsValidCNPJ(usuario.CPFCNPJ, "CNPJ informado é inválido."));
                else
                    validationResult.Add(AssertionConcern.AssertArgumentIsValidCPF(usuario.CPFCNPJ, "CPF informado é inválido."));
            }


            validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(usuario.Nome,
                "Nome deve ser preenchido."));

            //usuario.Senha = "";
            //if (string.IsNullOrEmpty(usuario.Senha)) //Ricardo pediu para alterar por conta da outo verde
            //    usuario.Senha = GeraSenha();
            /*
            if (usuario.IdEmpresa.HasValue)
            {
                var empresaUsuario = _empresaRepository.Get(usuario.IdEmpresa.Value);
                if (empresaUsuario == null || !empresaUsuario.UtilizaAgendamento)
                {


                    validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(usuario.Senha,
                "Senha deve ser preenchida."));
                    validationResult.Add(AssertionConcern.AssertPassword(usuario.Senha,
                "Senha deve possuir no mínimo 5 caracteres."));
                }
            }
            else
            {
                validationResult.Add(AssertionConcern.AssertArgumentNotNullOrWhiteSpace(usuario.Senha,
                    "Senha deve ser preenchida."));
                validationResult.Add(AssertionConcern.AssertPassword(usuario.Senha,
                    "Senha deve possuir no mínimo 5 caracteres."));
            }*/
            // Validar Telefone
            if (!string.IsNullOrWhiteSpace(usuario.Contatos?.FirstOrDefault()?.Telefone))
                validationResult.Add(
                    AssertionConcern.AssertArgumentIsValidTelefone(usuario.Contatos?.FirstOrDefault()?.Telefone,
                        @"Telefone deve ser válido"));

            // Validar Celular
            if (!string.IsNullOrWhiteSpace(usuario.Contatos?.FirstOrDefault()?.Celular))
                validationResult.Add(
                    AssertionConcern.AssertArgumentIsValidTelefone(usuario.Contatos?.FirstOrDefault()?.Celular,
                        @"Celular deve ser válido"));

            // Validar Celular
            if (usuario.Perfil == EPerfil.Embarcador && string.IsNullOrWhiteSpace(usuario.Contatos?.FirstOrDefault()?.Celular))
                validationResult.Add(@"Celular é obrigatório");

            // Validar e-mail válido
            if (!string.IsNullOrWhiteSpace(usuario.Contatos?.FirstOrDefault()?.Email))
                validationResult.Add(
                    AssertionConcern.AssertArgumentIsValidEmail(usuario.Contatos?.FirstOrDefault()?.Email,
                        @"E-mail deve ser válido"));
            else
                validationResult.Add(@"E-mail é obrigatório");

            if (usuario.IdPonto != null)
            {
                var usuarioIdPonto = GetUsuarioByIdPonto((int)usuario.IdPonto)
                    .Where(p => p.IdUsuario != usuario.IdUsuario && p.IdEmpresa == usuario.IdEmpresa)
                    .FirstOrDefault();

                if ((usuarioIdPonto != null))
                    validationResult.Add(
                        $"Não foi possível salvar o registro! Id ponto informado já está vínculado ao usuário " +
                        usuarioIdPonto.Nome + ".");
            }
            if (usuario.Filiais != null && usuario.Filiais.Any())
            {
                if (usuario.Filiais.Any(x => x.IdFilial == 0))
                    validationResult.Add("Filial não informada na aba de Permissão de Filial.");
            }

            #endregion

            #region Unicidade do registro

            // Verificar Login único
            if (processo == EProcesso.Create)
            {
                validationResult.Add(AssertionConcern.AssertArgumentIsValidLogin(usuario.Login,
                    @"Login possui caracteres inválidos."));

                if (_repository.Find(u => u.Login == usuario.Login).Any())
                    validationResult.Add($"Já existe um usuário cadastrado com o mesmo login.");
            }
            else
            {
                string login = _repository.Get(usuario.IdUsuario).Login;
                validationResult.Add(AssertionConcern.AssertArgumentEquals(usuario.Login, login,
                    "Login não pode ser modificado."));

                if (_repository.Find(u => u.Login == usuario.Login && u.IdUsuario != usuario.IdUsuario).Any())
                    validationResult.Add($"Já existe um usuário cadastrado com o mesmo login.");
            }

            string cCPFCNPJ = usuario.CPFCNPJ.OnlyNumbers();
            if (processo == EProcesso.Create && _repository.Find(u => u.CPFCNPJ == cCPFCNPJ).Any())
                validationResult.Add("CPF/CNPJ informado já cadastrado para outro usuário.");

            if (processo == EProcesso.Update && cCPFCNPJ != _repository.GetCNPJCPF(usuario.IdUsuario))
                validationResult.Add("CPF/CNPJ não pode ser alterado");

            #endregion

            #region Atualizar

            if (processo == EProcesso.Update)
            {
                Usuario usuarioDB = _repository.Get(usuario.IdUsuario);

                if (usuarioDB.CPFCNPJ.OnlyNumbers() != usuario.CPFCNPJ.OnlyNumbers())
                    validationResult.Add($"CPF/CNPJ não pode ser alterado.");

                if (usuarioDB.Perfil != usuario.Perfil)
                    validationResult.Add($"Perfil não pode ser alterado.");
            }

            #endregion

            if (usuario.Veiculos != null && usuario.Veiculos.Any())
            {
                foreach (var item in usuario.Veiculos)
                {
                    var idTipoCarreta = item.IdTipoCarreta;
                    var idTipoCavalo = item.IdTipoCavalo;
                    if (idTipoCarreta.HasValue)
                    {
                        var tipoCarreta = _tipoCarretaRepository.Get(idTipoCarreta.Value);
                        if (tipoCarreta == null)
                            validationResult.Add("Tipo de carreta informado é inválido.");
                    }
                    if (idTipoCavalo.HasValue)
                    {
                        var tipoCavalo = _tipoCavaloRepository.Get(idTipoCavalo.Value);
                        if (tipoCavalo == null)
                            validationResult.Add("Tipo de veículo informado é inválido.");
                    }
                }
            }

            return validationResult;
        }
        /*
        public string GeraSenha()
        {
            return new Random().Next(0, 999999).ToString("D6");
        }*/

        public UsuarioDocumento GetDocumentoCNHPorUsuarioId(int idUsuario)
        {
            var tmp = _usuarioDocumentoService.GetDocumentos(idUsuario);
            if (tmp.Any())
                return tmp.FirstOrDefault(x => x.TipoDocumento.Descricao == "CNH");

            return new UsuarioDocumento()
            {
                Validade = null
            };
        }

        /// <summary>
        /// Aplicar as regras
        /// </summary>
        /// <param name="usuario">Entidade de usuário</param>
        /// <param name="processo">Processo que esta sendo validado</param>
        private void ApplyRules(Usuario usuario, EProcesso processo)
        {
            usuario.CPFCNPJ = usuario.CPFCNPJ.OnlyNumbers();

            // Verificar se o usuário não possui o CPF cadastrado como motorista para algum empresa
            // Possuindo, é automaticamente atrelado o código do Empresa ao registro

            //Retirado para comportar a seguinte regra:
            // Regra vinda diretamente da Gerência. Não foi definida por análise ou por desenvolvimento:
            // Quando vier uma integração de proprietário / motorista / usuário e já estiver
            // cadastrado para outra empresa, troca o idempresa para a que está integrando agora
//            if (usuario.Perfil == EPerfil.Motorista)
//            {
//                var idEmpresaMotorista = _motoristaService.GetIdEmpresaPorCPF(usuario.CPFCNPJ.OnlyNumbers());
//                if (idEmpresaMotorista.HasValue)
//                    usuario.IdEmpresa = idEmpresaMotorista.Value;
//            }

            // Se for motorista ou proprietário, o login do usuário será o CPF/CNPJ.
            if (string.IsNullOrWhiteSpace(usuario.Login) &&
                (usuario.Perfil == EPerfil.Motorista || usuario.Perfil == EPerfil.Proprietario))
                usuario.Login = usuario.CPFCNPJ;

            // Se não houver imagem, seta valor como null evitando qualquer informação na base
            if (usuario.Foto != null && usuario.Foto.Length <= 0)
                usuario.Foto = null;

            #region Vínculo > Usuário x Filiais

            if (usuario.Perfil != EPerfil.Empresa
                && usuario.Perfil != EPerfil.Embarcador
                && usuario.Perfil != EPerfil.Mesa)
            {
                if (usuario.Filiais != null && usuario.Filiais.Any())
                {

                    //foreach (UsuarioFilial usuarioFilial in usuario.Filiais)
                        //usuario.Filiais.Remove(usuarioFilial);

                    usuario.Filiais.Clear();
                }

            }

            #endregion

            #region Vínculo > Usuário x Veiculos

            if (usuario.Veiculos != null && usuario.Veiculos.Any(v => v.Ativo))
            {
                // Caso o perfil for diferente de um destes cenários, irá descartar qualquer informação de 'Veículo'
                if ((usuario.Perfil != EPerfil.Motorista && usuario.Perfil != EPerfil.Proprietario) ||
                    !usuario.Carreteiro)
                {
                    // Se o processo for de criar um registro, e por acaso vir configuração para veiculos é descartado
                    if (processo == EProcesso.Create)
                        usuario.Veiculos = null;
                    else
                    {
                        foreach (Veiculo veiculo in usuario.Veiculos)
                            veiculo.Ativo = false;
                    }
                }

                // Sendo:
                // - Motorista e Carreteiro
                // - Proprietário e Carreteiro
                // Irá vincular o veículo ao usuário, pois será considerado veículo 'próprio'.
                if ((usuario.Perfil == EPerfil.Motorista || usuario.Perfil == EPerfil.Proprietario) &&
                    usuario.Carreteiro)
                {

                    foreach (Veiculo veiculo in usuario.Veiculos)
                    {

                        var cnpj = veiculo.IdEmpresa.HasValue ? _empresaRepository.GetCnpj(veiculo.IdEmpresa.Value) : null;
                        if (cnpj != null && cnpj == "99999999999999")
                            continue;

                        veiculo.IdEmpresa = null;
                        veiculo.IdFilial = null;
                        veiculo.IdMotorista = null;
                        veiculo.IdProprietario = null;
                    }
                }
            }

            #endregion

            #region Formatar

            Parallel.Invoke(() =>
            {
                if (usuario.Veiculos != null && usuario.Veiculos.Any())
                {
                    foreach (Veiculo veiculo in usuario.Veiculos)
                        if (!string.IsNullOrWhiteSpace(veiculo.Placa))
                            veiculo.Placa = veiculo.Placa.RemoveSpecialCharacters().ToUpper();
                }

                if (usuario.Enderecos != null && usuario.Enderecos.Any())
                {
                    foreach (UsuarioEndereco endereco in usuario.Enderecos)
                    {
                        if (!string.IsNullOrWhiteSpace(endereco.CEP))
                            endereco.CEP = endereco.CEP.OnlyNumbers();
                    }
                }

                if (usuario.Contatos != null && usuario.Contatos.Any())
                {
                    foreach (UsuarioContato contato in usuario.Contatos)
                    {
                        if (!string.IsNullOrWhiteSpace(contato.Celular))
                            contato.Celular = contato.Celular.OnlyNumbers();

                        if (!string.IsNullOrWhiteSpace(contato.Telefone))
                            contato.Telefone = contato.Telefone.OnlyNumbers();
                    }
                }

            });

            #endregion
        }

        /// <summary>
        /// Retorna o elemento com todos os filhos carregados
        /// </summary>
        /// <param name="id">Código do registro</param>
        /// <returns></returns>
        public Usuario GetAllChilds(int id)
        {
            return _repository.GetAllChilds(id);
        }

        public string GetNome(int id)
        {
            return _repository.Find(x => x.IdUsuario == id).Select(c => c.Nome).FirstOrDefault();
        }

        /// <summary>
        /// Adicionar o usuário a base de dados
        /// </summary>
        /// <param name="usuario">Dados do usuário</param>
        /// <param name="idUsuarioLogon">Código do usuário logado no momento</param>
        /// <returns></returns>
        public ValidationResult Add(Usuario usuario, int? idUsuarioLogon, string cnpjEmpresa = "")
        {
            try
            {
                ApplyRules(usuario, EProcesso.Create);

                ValidationResult validationResult = IsValidToCrud(usuario, EProcesso.Create, idUsuarioLogon, cnpjEmpresa);
                if (!validationResult.IsValid)
                    return validationResult;

                //nao guardar mais a senha no banco
                //if (!string.IsNullOrWhiteSpace(usuario.Senha))
                //    usuario.Senha = MD5Hash.Hash($"{usuario.Senha}{Md5HashPassword}");
                //var senha = usuario.Senha;
                //usuario.Senha = "";

                usuario.DataCadastro = DateTime.Now;

                _repository.Add(usuario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public ValidationResult UpdateUsuarioCheckList(Usuario usuario)
        {
            try
            {
                _repository.Update(usuario);
            }
            catch (Exception)
            {
                return new ValidationResult().Add("Não foi possível atualizar o usuário");
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Atualizar o registro do usuário.
        /// </summary>
        /// <param name="usuario">Dados do usuário.</param>
        /// <param name="idUsuarioLogon">Código do usuário logado no momento.</param>
        /// <returns></returns>
        public ValidationResult Update(Usuario usuario, int idUsuarioLogon, string cnpjEmpresa = "")
        {
            try
            {
                ApplyRules(usuario, EProcesso.Update);

                #region Modificação de senha
                /*
                // Se no processo de atualização, a senha estiver como null ou vazia, irá jogar a antiga.
                string senhaAntiga = GetSenha(usuario.IdUsuario);

                if (string.IsNullOrWhiteSpace(usuario.Senha))
                    usuario.Senha = GetSenha(usuario.IdUsuario);
                else if (usuario.Senha != senhaAntiga)
                    usuario.Senha = MD5Hash.Hash($"{usuario.Senha}{Md5HashPassword}");
                */
                #endregion

                ValidationResult validationResult = IsValidToCrud(usuario, EProcesso.Update, idUsuarioLogon, cnpjEmpresa);
                if (!validationResult.IsValid)
                    return validationResult;

                // Irá verificar se não existe nenhum outro usuário com o IdPush vinculado, afim de evitar situação abaixo:
                // O usuário A possui seu celular e loga com o seu usuário, então o IdPush do dispositivo vai para o usuário A.
                // Ele empresta o celular para o usuário B, e o usuário B loga no celular do usuário A, e vincula o mesmo IdPush do dispositivo para o usuário B.
                // Agora o dispositivo do usuário A recebe o push dos usuários A e B.
                if (!string.IsNullOrWhiteSpace(usuario.IdPush))
                {
                    Usuario usuarioPush = _repository.FirstOrDefault(u => u.IdPush == usuario.IdPush && u.IdUsuario != usuario.IdUsuario);
                    if (usuarioPush != null)
                    {
                        usuarioPush.IdPush = null;
                        _repository.Update(usuarioPush);
                    }
                }

                #region Validações Push

                if (!string.IsNullOrWhiteSpace(usuario.IdPush) && usuario.IdPush.Length < 20)
                    usuario.IdPush = null;

                #endregion

                _repository.Update(usuario);
                //TODO: COM A REMOÇÃO DA CRIAÇÃO DE UM PRÉ USUÁRIO NO CADASTRO DE USUÁRIO, UPDATE NÃO SERÁ MAIS NECESSÁRIO NESSE MOMENTO
                //new PreUsuarioService().AtualizarPorUsuario(usuario);

            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Retorna o código do estado do primeiro endereço do usuário
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public Dictionary<int, int> GetFirstIdsEstados(List<int> idsUsuarios)
        {
            var enderecos = _usuarioEnderecoRepository
                .Find(x => idsUsuarios.Contains(x.IdUsuario))
                .Select(x => new
                {
                    x.IdUsuario,
                    x.IdEstado
                }).ToList();

            var retorno = new Dictionary<int, int>();
            foreach (var item in enderecos)
            {
                if (retorno.ContainsKey(item.IdUsuario)) continue;
                retorno.Add(item.IdUsuario, item.IdEstado);
            }

            return retorno;
        }

        /// <summary>
        /// Retorn os dados completos do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public Usuario Get(int id, bool asNoTracking)
        {
            if (!asNoTracking)
                return _repository.Get(id);
            else
                return
                    _repository
                        .AsNoTracking()
                        .Include(x => x.UsuarioEstabelecimentos)
                         .Include(x => x.UsuarioEstabelecimentos.Select(p => p.Estabelecimento))
                        .FirstOrDefault(x => x.IdUsuario == id);
        }

        public IQueryable<Usuario> Find(Expression<Func<Usuario, bool>> predicate)
        {
            return _repository.Find(predicate);
        }

        public Usuario GetComEstabelecimentos(int id)
        {
            return _repository
                .Include(x => x.UsuarioEstabelecimentos)
                .Include(x => x.Empresa)
                .Include(x => x.UsuarioEstabelecimentos.Select(p => p.Estabelecimento))
                .FirstOrDefault(x => x.IdUsuario == id);
        }

        /// <summary>
        /// Irá validar o usuário, verificando se encontra algum com o login e senha informado
        /// Login utilizado apenas pelo Portal
        /// </summary>
        /// <param name="login">Login</param>
        /// <param name="senha">Senha</param>
        /// <returns></returns>
        public UsuarioTokenDTO ValidarUsuario(string login, string senha, long? idFacebook)
        {
            //string hashSenha = MD5Hash.Hash($"{senha}{Md5HashPassword}");
            var result = new UsuarioTokenDTO();
            result.token = _keycloak.GetUserAccessToken(login, senha, "");
            if (result.token.StatusCode == HttpStatusCode.OK)
            {
                var usuario = _repository.Find(x => x.Perfil != EPerfil.Motorista && x.Perfil != EPerfil.Proprietario && x.Ativo) /* &&
                                                    (((x.Login == login && (x.Senha == hashSenha)) ||
                                                      x.IdFacebook != null && x.IdFacebook > 0 && x.IdFacebook == idFacebook)))*/
                    .AsNoTracking()
                    .Include(x => x.UsuarioEstabelecimentos)
                    .Include(x => x.Empresa)
                    .FirstOrDefault();
                result.usuario = usuario;
            }
            return result;
        }

        public UsuarioTokenDTO ValidarCodigoAcesso(string codigo, string session_state)
        {
            //string hashSenha = MD5Hash.Hash($"{senha}{Md5HashPassword}");
            var result = new UsuarioTokenDTO();
            result.token = _keycloak.GetUserAccessTokenByCode(codigo, "");
            if (result.token.StatusCode == HttpStatusCode.OK)
            {
                //extrair o IdUsuario do token
                SecurityToken validatedToken = null;
                ClaimsPrincipal tokenClaims;
                _keycloak.ValidateToken(result.token.AccessToken, out validatedToken, out tokenClaims);
                Claim idUsuarioClaim = tokenClaims.FindFirst("IdUsuario");
                int IdUsuario = int.Parse(idUsuarioClaim.Value);
                //buscar o usuario
                var usuario = _repository.Find(x => x.Perfil != EPerfil.Motorista && x.Perfil != EPerfil.Proprietario && x.Ativo && x.IdUsuario == IdUsuario)
                    /*(((x.Login == login && (x.Senha == hashSenha)) || x.IdFacebook != null && x.IdFacebook > 0 && x.IdFacebook == idFacebook)))*/
                    .AsNoTracking()
                    .Include(x => x.UsuarioEstabelecimentos)
                    .Include(x => x.Empresa)
                    .FirstOrDefault();
                if (usuario == null)
                    _keycloak.LogoutUserSession(result.token.AccessToken, result.token.RefreshToken);
                result.usuario = usuario;
            }
            return result;
        }

        public ValidationResult SalvarLocalizacao(LocalizacaoUsuarioPortal localizacao)
        {
            var localizacaoExistente = _localizacaoUsuarioPortalRepository
                .AnyByUsuarioIp(localizacao.IdUsuario, localizacao.Ip);

            if (localizacaoExistente) return new ValidationResult();

            _localizacaoUsuarioPortalRepository.Add(localizacao);

            _localizacaoUsuarioPortalRepository.SaveChanges();

            if (!_parametrosGenericoService.GetParametro<bool?>(GLOBAL.HabilitarEnvioEmailLoginNovaLocalizacao, 0) ?? false)
                return new ValidationResult();

            EnviarEmailNovaLocalizacao(localizacao);

            return new ValidationResult();
        }

        public bool ExisteLocalizacaoPorUsuarioIp(int idUsuario, string ip)
        {
            return _localizacaoUsuarioPortalRepository.AnyByUsuarioIp(idUsuario, ip);
        }

        private void EnviarEmailNovaLocalizacao(LocalizacaoUsuarioPortal localizacao)
        {
            var infos = Consultar(localizacao.IdUsuario).Select(c => new{c.Nome, c.Contatos}).FirstOrDefault();

            if (infos?.Contatos == null || !infos.Contatos.Any()) return;

            var destinatarios = infos.Contatos.Select(c => c.Email).ToList();

            var emailModel = new EmailModel()
            {
                Assunto = $"Detectamos um novo login no Portal Extratta em {localizacao.Cidade}",
                Destinatarios = destinatarios,
                NomeVisualizacao = $"Extratta – Novo login em {localizacao.Cidade}",
                Prioridade = MailPriority.High
            };

            using (var ms = new StreamReader(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Email\nova-localizacao-acesso.html"))
            {
                var logoEmail = new LinkedResource(AppDomain.CurrentDomain.BaseDirectory + @"\Content\Image\logo-extratta.png");
                logoEmail.ContentId = Guid.NewGuid().ToString();

                var html = ms.ReadToEnd();

                html = html.Replace("{0}", logoEmail.ContentId);
                html = html.Replace("{CIDADE}", localizacao.Cidade);
                html = html.Replace("{ESTADO}", localizacao.Estado);
                html = html.Replace("{LATITUDE}", localizacao.Latitude.ToString(CultureInfo.CurrentCulture));
                html = html.Replace("{LONGITUDE}", localizacao.Longitude.ToString(CultureInfo.CurrentCulture));
                html = html.Replace("{DATA}", localizacao.DataCadastro.ToString("G"));
                html = html.Replace("{IP}", localizacao.Ip);
                html = html.Replace("{USUARIO}", infos.Nome);

                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                view.LinkedResources.Add(logoEmail);
                emailModel.AlternateView = view;
            }

            _emailService.EnviarEmail(emailModel);
        }

        public List<UsuarioEstabelecimento> GetEstabelecimentos(int IdUsuario)
        {
            return _usuarioEstabelecimentoRepository.Find(x => x.IdUsuario == IdUsuario)
                .Include(x => x.Estabelecimento)
                .ToList();
        }

        /// <summary>
        /// Irá validar o usuário, verificando se encontra algum com o CPF e senha informado.
        /// </summary>
        /// <param name="nCNPJCPF">CPF</param>
        /// <param name="senha">Senha</param>
        /// <returns></returns>
        public Usuario ValidarUsuarioPorUsuario(string Usuario, string senha)
        {
            string hashSenha = MD5Hash.Hash($"{senha}{Md5HashPassword}");

            return _repository.Find(x => x.Login == Usuario && x.Senha == hashSenha)
                .Include(x => x.Veiculos)
                .Include(x => x.Enderecos)
                .Include(x => x.Enderecos.Select(c => c.Cidade))
                .Include(x => x.Filiais)
                .Include(x => x.Contatos)
                .Include(x => x.HorariosCheckIn)
                .Include(x => x.GrupoUsuario)
                .Include(x => x.Empresa)
                .Include(x => x.GrupoUsuario.Menus.Select(e => e.Menu))
                .FirstOrDefault();
        }

        public List<Tuple<int, DateTime?>> GetDocumentoCNHPorIdUsuIdTipoDoc(List<int> usuariosMotoristas, int tpDoc)
        {
            return _usuarioDocumentoService.GetDocumentoCNHPorIdUsuIdTipoDoc(usuariosMotoristas, tpDoc);
        }

        /// <summary>
        /// Created to validate usuario through Firebase. The Mobile will use this in QRA.
        /// </summary>
        /// <param name="sTokenFirebase">TokenFirebase</param>
        /// <returns></returns>
        public Usuario ValidarUsuarioPorToken(string TokenFirebase)
        {

            return _repository.Find(x => x.TokenFirebase == TokenFirebase)
                .Include(x => x.Veiculos)
                .Include(x => x.Enderecos)
                .Include(x => x.Enderecos.Select(c => c.Cidade))
                .Include(x => x.Filiais)
                .Include(x => x.Contatos)
                .Include(x => x.HorariosCheckIn)
                .Include(x => x.GrupoUsuario)
                .Include(x => x.Empresa)
                .Include(x => x.GrupoUsuario.Menus.Select(e => e.Menu))
                .FirstOrDefault();
        }
        /*
        //public Usuario ResetarSenhaUsuario(string cpf, string email)
        {
            var usuario = _repository.Include(x => x.Contatos)
                .Include(x => x.Empresa)
                .FirstOrDefault(x => x.CPFCNPJ == cpf && x.Contatos.FirstOrDefault(y => y.Email == email) != null);

            var cpfCnpj = cpf.Length == 11 ? cpf.ToCPFFormato() : cpf.ToCNPJFormato();

            if (usuario == null) throw new Exception($"Nenhum usuário encontrado para o CPF/CNPJ {cpfCnpj} e e-mail {email}! Tente novamente!");

            var novaSenha = GerarSenhaAleatoria();
            usuario.Senha = novaSenha;

            return usuario;
        }*/

        private static string CreateRandomPassword(int passwordLength)
        {
            string allowedChars = "0123456789";
            char[] chars = new char[passwordLength];
            Random rd = new Random();

            for (int i = 0; i < passwordLength; i++)
                chars[i] = allowedChars[rd.Next(0, allowedChars.Length)];

            return new string(chars);
        }

        public bool ResetarSenha(int idUsuario, int idUsuarioLogon, string senha)
        {
            try
            {
                var usuario = _usuarioRepository.Get(idUsuario);

                if (string.IsNullOrEmpty(senha)) senha = GerarSenhaAleatoria(usuario.Perfil);

                var usuarioLogon = _usuarioRepository.Get(idUsuarioLogon);
                var contato = usuario.Contatos.FirstOrDefault();
                var email = contato?.Email ?? string.Empty;
                _keycloak.CreateOrUpdateUser(usuario.Login, usuario.Nome, email, senha, usuario.Ativo, new Dictionary<string, object>()
                        {
                            { "CPFCNPJ", new List<string> { usuario.CPFCNPJ } },
                            { "Perfil", new List<string> { Convert.ToString((int) usuario.Perfil) } },
                            { "IdUsuario", new List<string> { usuario.IdUsuario.ToString() } },
                            { "IdEmpresa", new List<string> { usuario.IdEmpresa.ToString() } },
                            { "Nome", new List<string> { usuario.Nome.ToString() } },
                            { "Modified", new List<string> { "reset pw at " + DateTime.Now.ToString() + " by " + idUsuarioLogon.ToString() } },
                        }, false);

                //atualizar a senha no banco, o mobile precisa por enquanto
                usuario.Senha = MD5Hash.Hash($"{senha}{Md5HashPassword}");
                _usuarioRepository.Update(usuario);

                EnviarEmailRecuperacaoSenha(usuario, usuarioLogon, senha);

                return true;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return false;
            }
        }
        public bool Resetar2FA(int idUsuario)
        {
            try
            {
                var usuario = _usuarioRepository.Get(idUsuario);
                _keycloak.RequireUserActions(usuario.Login, new List<string> { "CONFIGURE_TOTP" });
                return true;
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return false;
            }
        }

        public void EnviarEmailRecuperacaoSenha(Usuario usuario, Usuario usuarioRequisicao, string novaSenha, string dominio = null, string templateEmail = "recuperacao")
        {
            #region E-mail de nova senha

            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var emailModel = new EmailModel();
            emailModel.Assunto = WebConfigurationManager.AppSettings["TITULO_SISTEMA"].ToUpper() + " – Recuperação de Senha";
            var em = @"\Content\Email\ATS-Recuperacao senha.html";
            if (templateEmail ==  "usuarionovo")
            {
                emailModel.Assunto ="Olá "+ usuario.Nome + ", seja bem-vindo a EXTRATTA";
                em = @"\Content\Email\ATS-SEJA_BEM_VINDO.html";
            }
            else if(templateEmail ==  "trocasenha")
            {
                em = @"\Content\Email\ATS-Recuperacao_senha_novo.html";
            }
            using (var ms = new StreamReader(caminhoAplicacao + em))
            {
                // Por default é o da sistema...
                var logoEmail = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-extratta.png");
                logoEmail.ContentId = Guid.NewGuid().ToString();
                // Se o dominio for nulo ou nao existir nenhum configuração para o mesmo no LAYOUT então botamos o da sistema msm...
                if (dominio != null && dominio.Length > 0)
                {
                    var cfgLayoutDominio = _layoutRepository.FirstOrDefault(x => x.Href == dominio);
                    if (cfgLayoutDominio != null)
                        logoEmail = new LinkedResource(caminhoAplicacao + $@"\Images\{cfgLayoutDominio.Image}");
                }
                else
                {
                    var logoEmpresaUsuaLogado = usuario.Empresa?.Logo;
                    if (logoEmpresaUsuaLogado != null)
                    {
                        //logoEmail = new LinkedResource(new MemoryStream(logoEmpresaUsuaLogado), "image/png");

                            logoEmail = new LinkedResource(caminhoAplicacao + @"\Content\Image\logo-extratta.png");

                            logoEmail.TransferEncoding = TransferEncoding.Base64;
                            logoEmail.ContentId = Guid.NewGuid().ToString();
                    }
                }
                //
                var logoFacebook = new LinkedResource(caminhoAplicacao + @"\Content\Image\facebook.png");
                logoFacebook.ContentId = Guid.NewGuid().ToString();

                var links = "";
                if (usuario.Perfil == EPerfil.Motorista || usuario.Perfil == EPerfil.Proprietario)
                {
                    links =
                        "<p style='margin: 0;font-size: 12px;line-height: 14px'>Caso ainda não tenha baixado o nosso aplicativo, segue abaixo o link disponível na loja:</p><br><p style='margin: 0;font-size: 12px;line-height: 14px'>Android</p><p style='margin: 0;font-size: 12px;line-height: 14px><a href='https://portal.extratta.com.br' target='_blank'>https://play.google.com/store/apps/details?id=br.com.extratta.app</a></p><br><p style='margin: 0;font-size: 12px;line-height: 14px'>IOS (Apple)</p><p style='margin: 0;font-size: 12px;line-height: 14px'><a href='https://apps.apple.com/br/app/extratta/id1556280567' target='_blank'>https://apps.apple.com/br/app/extratta/id1556280567</a></p>";
                }
                else
                {
                    links =
                        "<p style='margin: 0;font-size: 12px;line-height: 14px'>Será necessário alterar a senha em seu primeiro acesso ao</p><br><p style='margin: 0;font-size: 12px;line-height: 14px;><a href='https://portal.extratta.com.br' target='_blank'>https://portal.extratta.com.br</a></p>";
                }

                string senhaEscapada = HttpUtility.HtmlEncode(novaSenha);

                var html = ms.ReadToEnd();
                html = html.Replace("{0}", logoEmail.ContentId);
                html = html.Replace("{1}", logoFacebook.ContentId);
                html = html.Replace("{Usuario}", usuario.Nome);
                html = html.Replace("{SENHA}", senhaEscapada);
                html = html.Replace("{LOGIN}", usuario.Login);
                html = html.Replace("{SOLICITANTE}", usuarioRequisicao.Nome);
                html = html.Replace("{DisplayLinks}", links);
                html = html.Replace("{MostrarPortal}", usuario.Perfil == EPerfil.Empresa ? "display: block;" : "display: none;");


                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                view.LinkedResources.Add(logoEmail);
                view.LinkedResources.Add(logoFacebook);
                emailModel.AlternateView = view;

            }
            emailModel.Destinatarios = new List<string> { usuario.Contatos.FirstOrDefault().Email };
            emailModel.NomeVisualizacao = ConstantesUtils.GetNomeAdministradoraPlataforma;
            emailModel.Prioridade = MailPriority.High;

            var configuracao = _parametrosAdministradoraService.GetConfiguracaoEmail(1);

            _emailService.EnviarEmailAsync(emailModel, config : configuracao);

            #endregion
        }

        /// <summary>
        /// Irá validar o usuário, verificando se encontra algum com o CPF e senha informado.
        /// </summary>
        /// <param name="nCNPJCPF">CPF</param>
        /// <param name="senha">Senha</param>
        /// <returns></returns>
        public Usuario ValidarUsuarioPorCNPJCPF(string nCNPJCPF, string senha)
        {
            string hashSenha = MD5Hash.Hash($"{senha}{Md5HashPassword}");
            string cCNPJCPF = nCNPJCPF.OnlyNumbers();

            return _repository.Find(x => x.CPFCNPJ == cCNPJCPF && x.Senha == hashSenha)
                .Include(x => x.Veiculos)
                .Include(x => x.Enderecos)
                .Include(x => x.Enderecos.Select(c => c.Cidade))
                .Include(x => x.Filiais)
                .Include(x => x.Contatos)
                .Include(x => x.HorariosCheckIn)
                .Include(x => x.GrupoUsuario)
                .Include(x => x.Empresa)
                .Include(x => x.GrupoUsuario.Menus.Select(e => e.Menu))
                .FirstOrDefault();
        }

        /// <summary>
        /// Atualizar a senha do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <param name="senha">Senha</param>
        /// <param name="novasenha">Nova senha</param>
        /// <param name="idUsuarioLogon">Código do usuário logado</param>
        /// <returns></returns>
        public ValidationResult UpdateSenha(int id, string senha, string novasenha, int idUsuarioLogon, bool verificarSenhaAtual = true)
        {
            try
            {
                var usuario = _repository.Find(x => x.IdUsuario == id)?.FirstOrDefault();
                if (usuario == null)
                    return new ValidationResult().Add($"Usuário inexistente");

                var firstAccessDriver = false;
                if (usuario.Perfil == EPerfil.Motorista)
                {
                    var idMotorista = _motoristaRepository.GetIdMotoristaPorCpf(usuario.CPFCNPJ);
                    if (idMotorista != null)
                    {
                        var motorista = _motoristaRepository.Get(idMotorista.Value);
                        //firstAccessDriver = (motorista.Ativo && motorista.StatusGR == EStatusGR.PreCadastro && string.IsNullOrEmpty(usuario.Senha));
                    }
                }

                const string caracteresEspeciais = "!@#$%^*()_+{[}?.;,:<>&";
                string pattern = $"[{caracteresEspeciais}]+";

                if (verificarSenhaAtual && !string.IsNullOrEmpty(usuario.Senha))
                    if (usuario.Senha != MD5Hash.Hash($"{senha}{Md5HashPassword}") && !(firstAccessDriver))
                        return new ValidationResult().Add($"Senha atual informada não é válida");

                if ((!string.IsNullOrEmpty(usuario.TokenFirebase)) && usuario.Senha.Length < 6 && (!firstAccessDriver))
                    return new ValidationResult().Add($"Senha atual informada não é válida");


                if (novasenha.Length < 10 || novasenha.Length > 70)
                    return new ValidationResult().Add($"Senha nova informada tem tamanho inválido");


                if (!novasenha.Any(c => char.IsUpper(c)))
                    return new ValidationResult().Add($"Senha nova informada não contém ao menos um caractere com letra maiúscula");

                if (!novasenha.Any(c => char.IsLower(c)))
                     return new ValidationResult().Add($"Senha nova informada não contém ao menos um caractere com letra minúscula");

                if (!novasenha.Any(c => char.IsDigit(c)))
                    return new ValidationResult().Add($"Senha nova informada não contém ao menos um caractere numérico");


                if (!Regex.IsMatch(novasenha, pattern))
                    return new ValidationResult().Add($"Senha nova informada não contém ao menos um caractere especial({caracteresEspeciais})");

                usuario.Senha = MD5Hash.Hash($"{novasenha}{Md5HashPassword}");

                ApplyRules(usuario, EProcesso.Update);

                // First we need persist data in the server
                _repository.Update(usuario);
                // Then we can nofify the user. It was wrong! Be cariful...
                if (usuario != null && !String.IsNullOrEmpty(usuario.IdPush))
                    _pushService.Enviar(
                            new List<string>() { usuario.IdPush },
                            "Alteração de senha",
                            "Senha alterada com sucesso!",
                            new
                            {
                                NewPassword = novasenha,
                                OldPassword = senha,
                                TokenFirebase = usuario.TokenFirebase
                            },
                             ETipoMensagemPush.PasswordReset
                        );

                //ValidationResult validationResult = IsValidToCrud(usuario, EProcesso.Update, idUsuarioLogon);
                //if (!validationResult.IsValid)
                //return validationResult;


            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public List<dynamic> ConsultarUsuarios(int idEmpresa, bool vistoriador)
        {
            return _repository.Find(x => x.Ativo &&
                      x.IdEmpresa == idEmpresa &&
                      x.Vistoriador == vistoriador)
                .Select(x => new
                {
                    x.IdUsuario,
                    x.Nome
                }).ToList<dynamic>();
        }

        public ValidationResult UpdateSenha(int id, string novasenha)
        {
            try
            {
                Usuario usuario = _repository.Find(x => x.IdUsuario == id)?.FirstOrDefault();
                if (usuario == null)
                    return new ValidationResult().Add($"Usuário inexistente");
                string OldPassword = usuario.Senha;
                usuario.Senha = MD5Hash.Hash($"{novasenha}{Md5HashPassword}");

                if ((!string.IsNullOrEmpty(usuario.TokenFirebase)) && usuario.Senha.Length < 6)
                    return new ValidationResult().Add($"Senha atual informada não é válida");

                ApplyRules(usuario, EProcesso.Update);

                _repository.Update(usuario);

                if (usuario != null && !String.IsNullOrEmpty(usuario.IdPush))
                    _pushService.Enviar(
                            new List<string>() { usuario.IdPush },
                            "Alteração de senha",
                            "Senha alterada com sucesso!",
                            new
                            {
                                NewPassword = novasenha,
                                OldPassword = OldPassword,
                                TokenFirebase = usuario.TokenFirebase
                            },
                            ETipoMensagemPush.PasswordReset
                        );
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        /// <summary>
        /// Consultar os usuários
        /// </summary>
        /// <param name="nome">Nome do usuário (like)</param>
        /// <param name="idEmpresa">Código do Empresa</param>
        /// <param name="idUsuarioLogOn">Código do Usuário do Logon</param>
        /// <returns></returns>
        public IQueryable<Usuario> Consultar(string nome, int? idEmpresa, int idUsuarioLogOn, bool listarTerceiros = false)
        {
            var usuariosEmp = _repository.Consultar(nome ?? string.Empty, idEmpresa);
            var retConsultar = usuariosEmp;

            switch (_repository.GetPerfil(idUsuarioLogOn))
            {
                // Sendo um destes status, somente será permitido realizar a consulta do próprio registro
                case EPerfil.Motorista:
                case EPerfil.Cliente:
                case EPerfil.Proprietario:
                    retConsultar = usuariosEmp.Where(u => u.IdUsuario == idUsuarioLogOn);
                    break;

                // Sendo 'Empresa' deverá permitir visualizar de todos do empresa que se esta vinculado
                // E todos os usuários do perfil cliente
                case EPerfil.Empresa:
                    var cIdEmpresa = _repository.GetIdEmpresa(idUsuarioLogOn);
                    var usuariosClientes =
                        _repository.Where(c => c.Perfil == EPerfil.Cliente && cIdEmpresa.HasValue &&
                                            c.IdEmpresa == cIdEmpresa.Value);


                    if (cIdEmpresa.HasValue && cIdEmpresa.Value > 0)
                        usuariosEmp = usuariosEmp.Where(u => u.IdEmpresa == cIdEmpresa);
                    if (listarTerceiros)
                    {
                        var usuariosTer = _repository.Where(u => u.IdEmpresa == null && (u.Perfil == EPerfil.Motorista || u.Perfil == EPerfil.Cliente));
                        usuariosEmp = usuariosEmp.Concat(usuariosTer);
                    }
                    retConsultar = usuariosEmp.Concat(usuariosClientes);

                    if (!string.IsNullOrWhiteSpace(nome))
                        retConsultar = retConsultar.Where(x => x.Nome.Contains(nome));

                    break;

                case EPerfil.Estabelecimento:
                    var idsEstabelecimentos = _repository.Include(x => x.UsuarioEstabelecimentos)
                        .FirstOrDefault(x => x.IdUsuario == idUsuarioLogOn)
                        ?.UsuarioEstabelecimentos?.Select(x => x.IdEstabelecimento).ToList();

                    retConsultar =
                        retConsultar.Where(
                            x => x.UsuarioEstabelecimentos.Any(
                                y => idsEstabelecimentos.Any(z => z == y.IdEstabelecimento)));

                    break;

                case EPerfil.Mesa:
                    retConsultar = retConsultar
                        .Where(u => u.Perfil != EPerfil.Administrador &&
                                    u.Perfil != EPerfil.Empresa);


                    break;
            }

            return retConsultar.Distinct();
        }

        public IQueryable<Usuario> Consultar(int idUsuario)
        {
            return _usuarioRepository.Where(c => c.IdUsuario == idUsuario);
        }

        /// <summary>
        /// Retorna o código do usuário a partir do CPF
        /// </summary>
        /// <param name="nCNPJCPF">CPF do usuário</param>
        /// <returns></returns>
        public int? GetIdByTokenFirebase(string TokenFirebase)
        {

            int? retId = _repository.Find(x => x.TokenFirebase == TokenFirebase && x.Ativo)
            .Select(x => x.IdUsuario)?.FirstOrDefault();

            return retId.GetValueOrDefault() > 0 ? retId.Value : (int?)null;
        }

        /// <summary>
        /// Retorna a foto do usuário
        /// </summary>
        /// <param name="id">Código do usuário</param>
        /// <returns></returns>
        public byte[] GetFoto(int id)
        {
            return _repository.Find(x => x.IdUsuario == id)
                .Select(x => x.Foto).FirstOrDefault();
        }

        /// <summary>
        /// Inativar o usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário a ser desativado</param>
        /// <param name="idUsuarioRequisicao">Código do usuário que requisitou a desativação</param>
        /// <returns></returns>
        public ValidationResult Inativar(int idUsuario, int idUsuarioRequisicao)
        {
            try
            {
                Usuario usuario = _repository.Get(idUsuario);

                #region Validação

                var usuInativando = _repository.Find(x => x.IdUsuario == idUsuarioRequisicao && x.Ativo).FirstOrDefault();

                if (usuario.IdEmpresa == usuInativando.IdEmpresa && usuario.IdUsuario == usuInativando.IdUsuario)
                    return new ValidationResult().Add("Não é permitido o usuário inativar a si mesmo.");

                if (usuInativando.Perfil != EPerfil.Administrador
                    && usuario.IdEmpresa != usuInativando.IdEmpresa)
                    return new ValidationResult().Add($"Somente usuários administradores e empresa conseguem desativar outros usuários.");

                #endregion


                if (!usuario.Ativo)
                    return new ValidationResult().Add($"Usuário já desativado na base de dados.");

                // Inativar o registro principal
                usuario.Ativo = false;

                usuario.CpfCnpjDesabilitado = usuario.CPFCNPJ;
                usuario.CPFCNPJ = null;
                usuario.LoginDesabilitado = usuario.Login;
                usuario.Login = null;


                // Inativar todos os registros filhos
                if (usuario.Veiculos != null && usuario.Veiculos.Any(v => v.Ativo))
                {
                    foreach (Veiculo veiculo in usuario.Veiculos.Where(v => v.Ativo))
                        veiculo.Ativo = false;
                }

                _repository.Update(usuario);

                if (usuario.Perfil == EPerfil.Empresa)
                {

                    var usuarioPermissaoGestorApp = _usuarioPermissaoGestorRepository;
                    var permissoesgestor = usuarioPermissaoGestorApp.Where(g => g.IdUsuario == idUsuario);
                    if (permissoesgestor != null)
                    {
                        foreach (var permissaoGestor in permissoesgestor.ToList())
                        {
                            permissaoGestor.DesbloquearEmpresa = false;
                            permissaoGestor.DesbloquearFilial = false;
                            usuarioPermissaoGestorApp.Update(permissaoGestor);
                        }
                    }

                    var usuarioPermissaoFinanceiroApp = _usuarioPermissaoFinanceiroRepository;
                    var permissaofinanceiro = usuarioPermissaoFinanceiroApp.Where(g => g.IdUsuario == idUsuario);

                    if (permissaofinanceiro != null)
                    {
                        foreach (var permissaoFinanceiro in permissaofinanceiro.ToList())
                        {
                            permissaoFinanceiro.DesbloquearFinanceiro = false;
                            usuarioPermissaoFinanceiroApp.Update(permissaoFinanceiro);
                        }
                    }

                    /*var novaSenha = GerarSenhaAleatoria();

                    UpdateSenha(usuario.IdUsuario, novaSenha);

                    EnviarEmailRecuperacaoSenha(usuario,novaSenha);*/
                }
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public string GerarSenhaAleatoria(EPerfil perfil, int length = 10)
        {
            var listaPerfisMobile = new HashSet<EPerfil> { EPerfil.Motorista, EPerfil.Proprietario };

            length = listaPerfisMobile.Contains(perfil) ? 6 : length;

            if (length < 10 && !listaPerfisMobile.Contains(perfil))
            {
                throw new ArgumentException("O comprimento mínimo da senha é 10 caracteres.");
            }

            const string uppercaseLetters = "ABCDEFGHJKLMNOPQRSTUVWXYZ";
            const string lowercaseLetters = "abcdefghijkmnopqrstuvwxyz";
            const string numbers = "0123456789";
            const string specialCharacters = "@#$_-";
            var allCharacters = uppercaseLetters + lowercaseLetters + numbers;

            if (!listaPerfisMobile.Contains(perfil))
            {
                allCharacters += specialCharacters;
            }

            Random random = new();
            StringBuilder password = new();

            // Ao menos uma letra maiúscula
            password.Append(uppercaseLetters[random.Next(uppercaseLetters.Length)]);
            // Ao menos uma letra minúscula
            password.Append(lowercaseLetters[random.Next(lowercaseLetters.Length)]);
            // Ao menos um número
            password.Append(numbers[random.Next(numbers.Length)]);

            if (!listaPerfisMobile.Contains(perfil))
            {
                // Ao menos dois caracteres especiais
                password.Append(specialCharacters[random.Next(specialCharacters.Length)]);
                password.Append(specialCharacters[random.Next(specialCharacters.Length)]);
            }

            // Preencher os caracteres restantes
            while (password.Length < length)
            {
                password.Append(allCharacters[random.Next(allCharacters.Length)]);
            }

            // Randomiza a sequencia de caracteres
            return new string(password.ToString().OrderBy(_ => random.Next()).ToArray());
        }

        /// <summary>
        /// Reativar o usuário
        /// </summary>
        /// <param name="idUsuario">Código do usuário a ser desativado</param>
        /// <param name="idUsuarioRequisicao">Código do usuário que requisitou a desativação</param>
        /// <returns></returns>
        public ValidationResult Reativar(int idUsuario, int idUsuarioRequisicao)
        {
            try
            {
                Usuario usuario = _repository.Get(idUsuario);

                #region Validação
                var usuInativando = _repository.Find(x => x.IdUsuario == idUsuarioRequisicao && x.Ativo).FirstOrDefault();

                if (usuInativando.Perfil != EPerfil.Administrador
                    && usuario.IdEmpresa != usuInativando.IdEmpresa)
                    return new ValidationResult().Add($"Somente usuários administradores e empresa conseguem reativar outros usuários.");

                #endregion

                if (usuario.Ativo)
                    return new ValidationResult().Add($"Usuário já ativado na base de dados.");

                var existeUsuarioAtivoComCPF = _repository.ExisteUsuarioAtivoComCpf(usuario.CpfCnpjDesabilitado);
                if (existeUsuarioAtivoComCPF)
                    return new ValidationResult().Add($"Já existe um usuário ativo com o CPF/CNPJ {usuario.CpfCnpjDesabilitado}");

                // Inativar o registro principal
                usuario.Ativo = true;

                usuario.CPFCNPJ = usuario.CpfCnpjDesabilitado;

                usuario.CpfCnpjDesabilitado = null;

                usuario.Login = usuario.LoginDesabilitado;
                usuario.LoginDesabilitado = null;

                // Inativar todos os registros filhos
                if (usuario.Veiculos != null)
                {
                    foreach (Veiculo veiculo in usuario.Veiculos)
                        veiculo.Ativo = true;
                }

                _repository.Update(usuario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public IQueryable<Usuario> GetPorCnpjcpfQueryable(string nCNPJCPF)
        {
            return _repository.Where(c => c.CPFCNPJ == nCNPJCPF);
        }

        public IQueryable<Usuario> GetQueryPorCNPJCPF(string nCNPJCPF)
        {
            if (string.IsNullOrEmpty(nCNPJCPF))
                return null;

            string cCPF = nCNPJCPF?.OnlyNumbers();

            return _usuarioRepository.Where(x => x.CPFCNPJ == cCPF && x.Ativo);
        }


        public IQueryable<Usuario> GetPorCnpjcpfEmpresaQueryable(string nCNPJCPF, int idEmpresa)
        {
            return _repository.Where(c => c.CPFCNPJ == nCNPJCPF && c.IdEmpresa == idEmpresa);
        }

        public string BuscaUsuarioMasterEstabelecimento(int idEstabelecimento)
        {
            return _repository.Find(x =>
                    x.UsuarioEstabelecimentos.Any(e => e.IdEstabelecimento == idEstabelecimento))
                .Select(x => x.Nome)
                .FirstOrDefault();
        }

        /// <summary>
        /// Retorna o status do usuário
        /// </summary>
        /// <param name="nCNPJCPF">CPF do usuário a ser validado</param>
        /// <returns></returns>
        public EStatusUsuario GetStatus(string nCNPJCPF)
        {
            Usuario usuario = _repository.Find(x => x.CPFCNPJ != null && x.CPFCNPJ == nCNPJCPF)?.FirstOrDefault();
            if (usuario == null)
                return EStatusUsuario.Inexistente;

            return usuario.Ativo
                ? EStatusUsuario.Cadastrado
                : EStatusUsuario.Bloqueado;
        }

        /// <summary>
        /// Retorna o usuário vinculado a Empresa
        /// </summary>
        /// <param name="idEmpresa">ID do Empresa</param>
        /// <returns></returns>
        public Usuario GetUsuarioEmpresa(int idEmpresa)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa && x.Ativo)
                .Include(x => x.Contatos)
                .FirstOrDefault();
        }

        /// <summary>
        /// Retorna lista de usuarios por empresa
        /// </summary>
        /// <param name="idEmpresa">Id de empresa</param>
        /// <returns>IQueryable de Usuario</returns>
        public IQueryable<Usuario> GetUsuariosPorEmpresa(int idEmpresa)
        {
            return _repository.Find(x => x.IdEmpresa == idEmpresa && x.Ativo)
                .Include(x => x.Contatos);
        }

        /// <summary>
        /// Retorna o Tipo de Contrato do usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <param name="idEmpresa">ID do Empresa</param>
        /// <returns></returns>
        public ETipoContrato GetTipoContrato(int idUsuario, int? idEmpresa)
        {
            // Tipo de contrato é sempre vinculado a um Empresa.
            // Não havendo um empresa sempre será 'Terceiro'.
            if (!idEmpresa.HasValue)
                return ETipoContrato.Terceiro;

            Usuario usuario = _repository.GetWithRelationships(idUsuario);
            switch (usuario.Perfil)
            {
                /*case EPerfil.Motorista:
                    return _motoristaService.GetTipoContrato(usuario.CPFCNPJ, idEmpresa.Value);*/

                case EPerfil.Proprietario:
                    return _proprietarioRepository.GetTipoContrato(usuario.CPFCNPJ, idEmpresa.Value);

                default:
                    return ETipoContrato.Terceiro;
            }
        }

        /// <summary>
        /// Retorna a senha criptografada do usuário
        /// </summary>
        /// <param name="id">ID do usuário</param>
        /// <returns></returns>
        public string GetSenha(int id)
        {
            return _repository.Find(u => u.IdUsuario == id).Select(u => u.Senha).FirstOrDefault();
        }

        /// <summary>
        /// Retorna o último acesso do usuário no aplicativo
        /// </summary>
        /// <param name="id">ID do usuário</param>
        /// <returns></returns>
        public DateTime? GetUltimoAcessoUsuarioAplivativo(int IdUsuario)
        {
            return _repository.Find(u => u.IdUsuario == IdUsuario).Select(u => u.DataUltimoAcessoAplicativo).FirstOrDefault();
        }

        /// <summary>
        /// Retorna o último acesso do usuário no aplicativo, buscando pelo CPFCNPJ
        /// </summary>
        /// <param name="cpfcnpj">cpf ou cnpj do usuário</param>
        /// <returns></returns>
        public DateTime? GetUltimoAcessoUsuarioAplivativoByCpf(string CPFCNPJ)
        {
            return _repository.Find(u => u.CPFCNPJ == CPFCNPJ).Select(u => u.DataUltimoAcessoAplicativo).FirstOrDefault();
        }

        /// <summary>
        /// Retorna o status do usuário
        /// </summary>
        /// <param name="login">Login do usuário</param>
        /// <param name="senha">Senha do usuário</param>
        /// <returns></returns>
        public EStatusUsuario GetStatus(string login, string senha)
        {
            string hashSenha = MD5Hash.Hash($"{senha}{Md5HashPassword}");

            string cpfCNPJ = _repository.Find(x => x.Login == login && x.Senha == hashSenha)
                .Select(u => u.CPFCNPJ).FirstOrDefault();

            return GetStatus(cpfCNPJ);
        }

        /// <summary>
        /// Retorna informando se o usuário possui permissão para acessar o determinado menu.
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <param name="idMenu"></param>
        /// <returns></returns>
        public bool HasPermissaoAcessoMenu(int idUsuario, int idMenu)
        {
            EPerfil perfilUsuario = _repository.GetPerfil(idUsuario);

            // Verifica se o perfil do usuário poderá acessar o menu
            if (!_menuService.GetPerfis(idMenu).Contains(perfilUsuario))
                return false;

            // Verifica se o grupo do usuário possui permissão para acessar o menu
            int? idGrupoUsuario = _repository.GetGrupoUsuario(idUsuario);
            if (idGrupoUsuario.HasValue)
            {
                GrupoUsuario grpUsuario = _grupoUsuarioService.GetChilds(idGrupoUsuario.Value);
                if (!grpUsuario.Menus.Select(m => m.IdMenu).Contains(idMenu))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Retorna os usuários por perfil de envio de mensagem
        /// </summary>
        /// <param name="idEmpresa">Id do empresa</param>
        /// <param name="idUsuario">Id do usuário</param>
        /// <returns></returns>
        public IQueryable<Usuario> GetUsuariosPorPerfisMensagens(int? idEmpresa, int idUsuario)
        {
            var perfilUsuario = _repository.GetPerfil(idUsuario);
            var listaPerfis = new List<EPerfil> { EPerfil.Empresa };

            if (perfilUsuario == EPerfil.Empresa || perfilUsuario == EPerfil.Administrador)
            {
                listaPerfis.Add(EPerfil.Proprietario);
                listaPerfis.Add(EPerfil.Motorista);
            }

            var usuarios = _repository.Find(x => listaPerfis.Contains(x.Perfil) && x.Ativo);

            if (perfilUsuario != EPerfil.Administrador)
                usuarios = usuarios.Where(x => x.IdEmpresa == idEmpresa || x.IdEmpresa == null);

            return usuarios;
        }

        /// <summary>
        /// Atualiza a data do último acesso do usuário no sistema.
        /// </summary>
        /// <param name="idUsuario">Id do usuário</param>
        /// <param name="tipoAcesso">Tipo de acesso</param>
        /// <returns></returns>
        public ValidationResult AtualizarDataUltimoAcesso(int idUsuario, ETipoAcessoSistema tipoAcesso)
        {
            try
            {
                // Busca o usuário a ser atualizado.
                Usuario usuario = _repository.GetWithRelationships(idUsuario);

                // Atualiza a data conforme o tipo de acesso: Web ou Aplicativo.
                _repository.AtualizarDataUltimoAcesso(idUsuario, tipoAcesso);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }

            return new ValidationResult();
        }

        public Usuario GetUsuarioFromFacebook(long idFacebook)
        {
            return _repository.Where(x => x.IdFacebook == idFacebook).FirstOrDefault();
        }

        /// <summary>
        /// Retorna todos os usuários cadastrados para um determinado empresa
        /// que tenham permissão para o chat
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public IQueryable<Usuario> GetUsuariosChatByEmpresa(int idEmpresa)
        {
            return _repository.Find(x => x.PermiteResponderChat && x.IdEmpresa == idEmpresa && x.Ativo);
        }

        public List<Usuario> GetUsuarioByIdPonto(int IdPonto)
        {

            return _repository.Find(p => p.IdPonto == IdPonto)
                .ToList();

        }

        public bool ValidaSessaoUsuario(string key)
        {
            /*var solicitacao = new SolicitacaoAcessoService().GetByKey(key);

            if (!solicitacao.ToPanel) return false;
            var tempoSessao = (solicitacao.DataHoraSolicitacao - DateTime.Now);

            if (tempoSessao.Hours <= 12) return true;
            _solicitacaoAcessoRepository.Delete(solicitacao);*/

            return false;
        }

        public Usuario GetUsuarioByKey(string key)
        {
            /*var solicitacao = new SolicitacaoAcessoService().GetByKey(key);
            return this.Get(solicitacao.IdUsuario);*/
            return new Usuario();
        }

        public Usuario GetClientesByUsuario(int? idUsuario)
        {
            var result = _repository.Find(x => x.IdUsuario == idUsuario && x.Ativo)
                .Include(x => x.Clientes)
                .FirstOrDefault();

            return result;
        }

        public List<UsuarioPreferencias> GetPreferenciasUsuarioPrefixLike(int idUsuario, string prefix)
        {
            return _usuarioPreferenciasRepository.GetPreferenciasByIdUsuarioPrefixLike(idUsuario, prefix).ToList();
        }

        public List<UsuarioPreferencias> GetUsuarioPreferencias(int idUsuario, string campo = null)
        {
            return _usuarioPreferenciasRepository.GetPreferenciasByIdUsuario(idUsuario, campo).ToList();
        }

        public ValidationResult SaveUsuarioPreferencias(List<UsuarioPreferencias> usuarioPreferencias)
        {
            try
            {
                _usuarioPreferenciasRepository.SaveUsuarioPreferencias(usuarioPreferencias);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
            return new ValidationResult();
        }

        public object ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, IUserIdentity userIdentity)
        {
            var usus = _repository.GetAll()
                .Include(x => x.Empresa)
                .Where(x => x.Ativo);

            usus = string.IsNullOrWhiteSpace(order?.Campo)
                    ? usus.OrderBy(x => x.IdUsuario)
                    : usus.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            usus = usus.AplicarFiltrosDinamicos<Usuario>(filters);

            if (userIdentity.Perfil != (int)EPerfil.Administrador)
                usus = usus.Where(c => c.IdEmpresa == userIdentity.IdEmpresa);

            return new
            {
                totalItems = usus.Count(),
                items = usus.Skip((page - 1) * take).Take(take)
                .ToList().Select(x => new
                {
                    x.IdUsuario,
                    x.Nome,
                    x.Empresa?.RazaoSocial,
                    CPF = x.CPFCNPJ.ToCPFFormato()
                })
            };
        }


        public ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosLiderados(int idUsuario, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var idsSubordinados = _gestorUsuarioRepository
                .Where(c => c.IdGestor == idUsuario && c.UsuarioSubordinado.Ativo)
                .Select(c => c.IdSubordinado)
                .ToList();

            if (!idsSubordinados.Any())
                return new ConsultarGridUsuariosLideradosResponse();

            var usuariosSubordinadosQuery = _repository.Where(c => c.Ativo && idsSubordinados.Contains(c.IdUsuario));

            usuariosSubordinadosQuery = string.IsNullOrWhiteSpace(order?.Campo)
                    ? usuariosSubordinadosQuery.OrderBy(x => x.IdUsuario)
                    : usuariosSubordinadosQuery.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            var count = usuariosSubordinadosQuery.Count();

            usuariosSubordinadosQuery = usuariosSubordinadosQuery.AplicarFiltrosDinamicos(filters);

            usuariosSubordinadosQuery = usuariosSubordinadosQuery.Skip((page - 1) * take).Take(take);

            var usuariosSubordinados = usuariosSubordinadosQuery.Select(c =>
                new ConsultarGridUsuariosLideradosResponseItem
                {
                    IdUsuario = c.IdUsuario,
                    Login = c.Login,
                    Nome = c.Nome,
                    PerfilEnum = c.Perfil,
                    CpfCnpj = c.CPFCNPJ
                })
                .ToList();

            return new ConsultarGridUsuariosLideradosResponse()
            {
                items = usuariosSubordinados,
                totalItems = count
            };
        }

        public ConsultarGridUsuariosLideradosResponse ConsultarGridUsuariosDisponiveisParaSeremLiderados(int idUsuario, int? idEmpresa, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            var idsSubordinados = _gestorUsuarioRepository
                .Where(c => c.IdGestor == idUsuario && c.UsuarioSubordinado.Ativo)
                .Select(c => c.IdSubordinado)
                .ToList();

            if (idEmpresa == null)
            {
                idEmpresa = _repository.Where(c => c.IdUsuario == idUsuario).Select(c => c.IdEmpresa).FirstOrDefault();
                if (idEmpresa == null)
                    throw new InvalidOperationException("Usuários sem empresa vinculada não podem ter usuários liderados.");
            }

            var usuariosDisponiveis = _repository
                .Where(c => c.Ativo && c.IdEmpresa == idEmpresa && (c.Perfil == EPerfil.Motorista || c.Perfil == EPerfil.Proprietario)
                            && !idsSubordinados.Contains(c.IdUsuario) && c.IdUsuario != idUsuario);

            usuariosDisponiveis = string.IsNullOrWhiteSpace(order?.Campo)
                ? usuariosDisponiveis.OrderBy(x => x.IdUsuario)
                : usuariosDisponiveis.OrderBy($"{order.Campo} {order.Operador.DescriptionAttr()}");

            var count = usuariosDisponiveis.Count();

            usuariosDisponiveis = usuariosDisponiveis.AplicarFiltrosDinamicos(filters);

            usuariosDisponiveis = usuariosDisponiveis.Skip((page - 1) * take).Take(take);

            var retorno = usuariosDisponiveis.Select(c =>
                    new ConsultarGridUsuariosLideradosResponseItem
                    {
                        IdUsuario = c.IdUsuario,
                        Login = c.Login,
                        Nome = c.Nome,
                        PerfilEnum = c.Perfil,
                        CpfCnpj = c.CPFCNPJ
                    })
                .ToList();

            return new ConsultarGridUsuariosLideradosResponse()
            {
                items = retorno,
                totalItems = count
            };
        }

        public ValidationResult IncluirUsuarioLiderado(int idUsuario, int? idEmpresa, int idUsuarioParaIncluir, int idUsuarioCadastro)
        {
            var isGestor = _parametrosUsuarioService.GetPermiteSolicitarAdiantamentoApp(idUsuario) ||
                           _parametrosUsuarioService.GetPermiteEfetuarCargaSolicitacaoAdiantamentoApp(idUsuario);

            if(!isGestor)
                throw new InvalidOperationException("Usuário não tem permissões de gestão para liderar outros usuários.");

            var jaEstaSubordinado = _gestorUsuarioRepository
                .Any(c => c.IdGestor == idUsuario && c.IdSubordinado == idUsuarioParaIncluir);

            if (jaEstaSubordinado)
                throw new InvalidOperationException("Este usuário já está nessa listagem de liderança.");

            if (idEmpresa == null)
            {
                idEmpresa = _repository.Where(c => c.IdUsuario == idUsuario).Select(c => c.IdEmpresa).FirstOrDefault();
                if (idEmpresa == null)
                    throw new InvalidOperationException("Usuários sem empresa vinculada não podem ter usuários liderados.");
            }

            var usuario = _repository
                .Where(c => c.IdUsuario == idUsuarioParaIncluir && c.Ativo && c.IdEmpresa == idEmpresa
                            && (c.Perfil == EPerfil.Motorista || c.Perfil == EPerfil.Proprietario))
                .FirstOrDefault();

            if(usuario == null)
                throw new InvalidOperationException("Usuário não encontrado.");

            var gestorUsuario = new GestorUsuario()
            {
                DataCadastro = DateTime.Now,
                IdUsuarioCadastro = idUsuarioCadastro,
                IdGestor = idUsuario,
                IdSubordinado = idUsuarioParaIncluir
            };

            var retorno = _gestorUsuarioRepository.Add(gestorUsuario);

            return new ValidationResult();
        }

        public ValidationResult RemoverUsuarioLiderado(int idUsuario, int idUsuarioParaRemover)
        {
            var gestorUsuario = _gestorUsuarioRepository
                .Where(c => c.IdGestor == idUsuario && c.IdSubordinado == idUsuarioParaRemover)
                .FirstOrDefault();

            if(gestorUsuario == null)
                throw new InvalidOperationException("Registro não encontrado.");

            _gestorUsuarioRepository.Delete(gestorUsuario);

            return new ValidationResult();
        }

        public IEnumerable<UsuarioEstabelecimento> GetUsuariosEstabelecimento(int idUsuario)
        {
            return _usuarioEstabelecimentoRepository
                .Find(x => x.IdUsuario == idUsuario)
                .ToList();
        }

        public IQueryable<object> GetFiliaisAutorizadas(int idUsuario)
        {
            return _usuarioFilialRepository.Find(x => x.IdUsuario == idUsuario)
                .Select(x => new
                {
                    x.IdFilial,
                    x.Filial.CNPJ,
                    x.Filial.RazaoSocial
                });
        }

        public IQueryable<Filial> GetFiliaisAutorizadasCompleto(int idUsuario)
        {
            return _usuarioFilialRepository.Find(x => x.IdUsuario == idUsuario && x.Filial.Ativo)
                .Include(x => x.Filial)
                .Select(x => x.Filial);
        }

        public IQueryable<int> GetIdsFiliaisAutorizadasCompleto(int idUsuario)
        {
            var ids = _usuarioFilialRepository.Find(x => x.IdUsuario == idUsuario && x.Filial.Ativo)
                .Select(x => x.IdFilial);

            return ids;
        }

        public List<Usuario> GetByPerfil(EPerfil perfil, int? idFilial, int? idEmpresa)
        {
            var usuarios = _repository.Find(o => o.Ativo && o.Perfil == perfil && o.Filiais.Select(x => x.IdFilial).Contains(idFilial.Value));

            return usuarios.ToList();
        }


        public object ConsultaGrid(int? idEmpresa, int Take, int Page, OrderFilters orderFilters,
            List<QueryFilters> Filters, EPerfil prfUsuLogado, int? idUsuarioLogOn, bool listarTerceiros, bool usuariosAtivos)
        {
            var usuariosEmpresa = GetDataToGridAndReport(idEmpresa, orderFilters, Filters, prfUsuLogado, idUsuarioLogOn,
                listarTerceiros, usuariosAtivos);

            return new
            {
                totalItems = usuariosEmpresa.Count(),
                items = usuariosEmpresa.Skip((Page - 1) * Take).Take(Take)
                .ToList().Select(x => new
                {
                    x.Ativo,
                    x.Nome,
                    x.IdUsuario,
                    CPFCNPJ = x.Ativo
                        ? (x.CPFCNPJ?.Length == 11 ? x.CPFCNPJ?.ToCPFFormato() : x.CPFCNPJ?.ToCNPJFormato())
                        : (x.CpfCnpjDesabilitado.Length == 11 ? x.CpfCnpjDesabilitado?.ToCPFFormato() : x.CpfCnpjDesabilitado?.ToCNPJFormato()),
                    Login = x.Ativo ? x.Login : x.LoginDesabilitado,
                    RazaoSocialEmpresa = x.Empresa?.RazaoSocial,
                    DescricaoGrupoUsuario = x.GrupoUsuario?.Descricao,
                    Perfil = EnumHelpers.GetDescription(x.Perfil)
                })
            };
        }

        public byte[] GerarRelatorioGridUsuarios(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters,
            EPerfil perfilUsuarioLogado, int? idUsuario, bool listarTerceiros, string tipoArquivo, string logo)
        {
            var listaUsuarios = new List<RelatorioUsuariosDataType>();
            var usuarios = GetDataToGridAndReport(idEmpresa, orderFilters, filters, perfilUsuarioLogado, idUsuario,
                listarTerceiros);

            foreach (var usuario in usuarios)
            {
                listaUsuarios.Add(new RelatorioUsuariosDataType
                {
                    Empresa = usuario.Empresa?.NomeFantasia,
                    Nome = usuario.Nome,
                    Ativo = usuario.Ativo ? "Sim" : "Não",
                    Cnh = usuario.CNH,
                    CategoriaCnh = usuario.CNHCategoria,
                    ValidadeCnh = usuario.Documentos.FirstOrDefault()?.Validade?.ToShortDateString(),
                    Carreteiro = usuario.Carreteiro ? "Sim" : "Não",
                    CpfCnpj = FormatCpfOrCnpj(usuario.CPFCNPJ),
                    DataCadastro = usuario.DataCadastro.ToShortDateString(),
                    DataUltimoAcessoApp = usuario.DataUltimoAcessoAplicativo?.ToShortDateString(),
                    DataUltimoAcessoWeb = usuario.DataUltimoAcessoWeb?.ToShortDateString(),
                    Gestor = usuario.Gestor ? "Sim" : "Não",
                    IdUsuario = usuario.IdUsuario.ToString(),
                    Perfil = System.Enum.IsDefined(typeof(EPerfil), usuario.Perfil) ? usuario.Perfil.DescriptionAttr() : string.Empty,
                    RecebeNotificacao = usuario.ReceberNotificacao ? "Sim" : "Não",
                    Rntrc = usuario.RNTRC,
                    TipoCliente = usuario.TipoCliente != null && System.Enum.IsDefined(typeof(ETipoCliente), usuario.TipoCliente) ? usuario.TipoCliente?.DescriptionAttr() : string.Empty,
                    Vistoriador = usuario.Vistoriador ? "Sim" : "Não"
                });
            }

            return new RelatorioUsuarios().GetReport(listaUsuarios, tipoArquivo, logo);
        }

        private IQueryable<Usuario> GetDataToGridAndReport(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, EPerfil perfilUsuarioLogado, int? idUsuarioLogOn, bool listarTerceiros, bool usuariosAtivos = true)
        {
            var usuariosEmpresa = _repository.GetAll()
                .Include(x => x.GrupoUsuario)
                .Include(x => x.Empresa)
                .Include(x => x.Documentos);

            if (usuariosAtivos)
            {
                usuariosEmpresa = usuariosEmpresa.Where(x => x.Ativo);
            }

            var idEmpUsu = _repository.GetIdEmpresa(idUsuarioLogOn.Value);
            if (idEmpUsu.HasValue)
                usuariosEmpresa = usuariosEmpresa.Where(x => x.IdEmpresa == idEmpUsu.Value);

            if (idEmpresa.HasValue) usuariosEmpresa = usuariosEmpresa.Where(x => x.IdEmpresa == idEmpresa.Value);

            #region regra de listagem
            switch (perfilUsuarioLogado)
            {
                case EPerfil.Motorista:
                case EPerfil.Cliente:
                case EPerfil.Proprietario:
                    usuariosEmpresa = usuariosEmpresa.Where(u => u.IdUsuario == idUsuarioLogOn);
                    break;
                case EPerfil.Empresa:
                    if (listarTerceiros)
                    {
                        var usuariosTer = _repository.GetAll().Include(x => x.GrupoUsuario).Include(x => x.Empresa).Where(u => u.IdEmpresa == null && (u.Perfil == EPerfil.Motorista || u.Perfil == EPerfil.Cliente));
                        usuariosEmpresa = usuariosEmpresa.Concat(usuariosTer);
                    }
                    break;
                case EPerfil.Estabelecimento:
                    var idsEstabelecimentos = _repository.Include(x => x.UsuarioEstabelecimentos)
                        .FirstOrDefault(x => x.IdUsuario == idUsuarioLogOn)
                        ?.UsuarioEstabelecimentos?.Select(x => x.IdEstabelecimento).ToList();

                    usuariosEmpresa =
                        usuariosEmpresa.Where(
                            x => x.UsuarioEstabelecimentos.Any(
                                y => idsEstabelecimentos.Any(z => z == y.IdEstabelecimento)));

                    break;
            }


            usuariosEmpresa = usuariosEmpresa.Distinct();
            #endregion

            if(filters != null)
            {
                var customFilters = filters.Where(f => f.Campo == "CPFCNPJ" || f.Campo == "Login").ToList();

                foreach (var filter in customFilters)
                {
                    if (filter.Campo == "CPFCNPJ")
                    {
                        if (usuariosAtivos)
                        {
                            usuariosEmpresa = usuariosEmpresa.Where(x => x.CPFCNPJ == filter.Valor);
                        }
                        else
                        {
                            usuariosEmpresa = usuariosEmpresa.Where(x =>
                                (x.CPFCNPJ != null && x.CPFCNPJ == filter.Valor) ||
                                (x.CpfCnpjDesabilitado != null && x.CpfCnpjDesabilitado == filter.Valor));
                        }
                    }
                    else if (filter.Campo == "Login")
                    {
                        if (usuariosAtivos)
                        {
                            usuariosEmpresa = usuariosEmpresa.Where(x => x.Login == filter.Valor);
                        }
                        else
                        {
                            usuariosEmpresa = usuariosEmpresa.Where(x =>
                                (x.Login != null && x.Login == filter.Valor) ||
                                (x.LoginDesabilitado != null && x.LoginDesabilitado == filter.Valor));
                        }
                    }

                    filters.Remove(filter);
                }
            }

            usuariosEmpresa = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? usuariosEmpresa.OrderBy(x => x.IdUsuario)
                : usuariosEmpresa.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");

            usuariosEmpresa = usuariosEmpresa.AplicarFiltrosDinamicos<Usuario>(filters);

            return usuariosEmpresa;
        }

        private static string FormatCpfOrCnpj(string value)
        {
            switch (value?.Length)
            {
                case 14:
                    return value.ToCNPJFormato();
                case 11:
                    return value.ToCPFFormato();
            }

            return string.Empty;
        }

        public Usuario GetByCpf(string cpf, bool getDesabilitado = false)
        {
            var usuario = _repository
                .Include(x => x.ConjuntosEmpresa)
                .Include(x => x.ConjuntosEmpresa.Select(o => o.Conjunto));

            if (getDesabilitado)
                usuario = usuario.Where(o => o.CPFCNPJ == cpf || o.CpfCnpjDesabilitado == cpf);
            else
                usuario = usuario.Where(o => o.CPFCNPJ == cpf);

            return usuario.FirstOrDefault();
        }

        public Usuario GetByLogin(string login)
        {
            return _repository.Include(x => x.Contatos).FirstOrDefault(x => x.Login == login);
        }


        public IQueryable<Usuario> GetByCpfQuery(string cpf, bool getDesabilitado = false)
        {
            if (getDesabilitado)
                return _repository.Where(o => o.CPFCNPJ == cpf || o.CpfCnpjDesabilitado == cpf);
            return _repository.Where(u => u.CPFCNPJ == cpf);
        }

        public ValidationResult AtualizarContatado(string aCPF, int aStatus)
        {
            try
            {
                var lUsuario = GetByCpf(aCPF);

                if (lUsuario == null)
                    return new ValidationResult().Add("Motorista não encontrado!");
                if (aStatus != 0)
                {
                    lUsuario.DataContatado = DateTime.Now;
                    lUsuario.StatusContatado = (EStatusContatado)aStatus;
                }
                else
                {
                    lUsuario.DataContatado = null;
                    lUsuario.StatusContatado = EStatusContatado.Disponivel;
                }

                _repository.Update(lUsuario);
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
            return new ValidationResult();
        }

        public IQueryable<Usuario> GetQueryByLogin(string login)
        {
            return _repository.Where(c => c.Login == login);
        }

        public UsuarioPermissaoGestor GetConfiguracaoPermissaoGestor(int idusuario, EBloqueioGestorTipo tipo)
        {
            var repository = _usuarioPermissaoGestorRepository;

            var permissao = repository.Where(c => c.IdUsuario == idusuario && c.IdBloqueioGestorTipo == (int) tipo);

            return permissao.FirstOrDefault();
        }

        public IQueryable<GridContatosModel> GetEmailsUsuarioByListaId(int[] listaIds)
        {
            return _repository.Find(x => listaIds.Contains(x.IdUsuario))
                            .Select(x => new GridContatosModel()
                            {
                                Nome = x.Nome,
                                Email = x.Contatos.FirstOrDefault().Email,
                                Ativo = true,
                                //Origem = EOrigemContato.Embarcador
                            });
        }

        public IEnumerable<ConsultaVistoriadores> ConsultarVistoriadores(string cpfUsuario)
        {
            var possuiPermissao = _usuarioRepository
                .Any(x => x.CPFCNPJ.Contains(cpfUsuario) && x.VistoriadorMaster);

            if (!possuiPermissao)
                throw new Exception("Apenas vistoriadores master podem realizar esta consulta");


            return _usuarioRepository
                .ConsultarVistoriadores(cpfUsuario);

    }

        public bool HasEstabelecimento(int idUsuario, int? idEstabelecimentoBase)
        {
            return _usuarioRepository.Any(x =>
                x.IdUsuario == idUsuario && x.UsuarioEstabelecimentos.Any(y =>
                    y.IdEstabelecimento == idEstabelecimentoBase));
        }

        public KeyValuePair<ValidationResult, string> GerarKeyCodeTransaction(int idUsuario, string cpf)
        {
            try
            {
                var randon = new Random().Next(1000, 100000);
                var bytes = Encoding.ASCII.GetBytes(randon.ToString());
                var keyCodeTransaction = Convert.ToBase64String(bytes);

                var usuario = _repository.FirstOrDefault(o => o.IdUsuario == idUsuario && o.CPFCNPJ == cpf);

                if (usuario == null)
                    return new KeyValuePair<ValidationResult, string>(new ValidationResult().Add("Usuário não encontrado na base."), string.Empty);

                if (!string.IsNullOrEmpty(usuario.KeyCodeTransaction))
                    return new KeyValuePair<ValidationResult, string>(new ValidationResult(), usuario.KeyCodeTransaction);

                usuario.KeyCodeTransaction = keyCodeTransaction;

                _repository.Update(usuario);

                return new KeyValuePair<ValidationResult, string>(new ValidationResult(), keyCodeTransaction);
            }
            catch (Exception e)
            {
                return new KeyValuePair<ValidationResult, string>(new ValidationResult().Add(e.Message), string.Empty);
            }
        }

        public Empresa GetEmpresaPorCPFCNPJUsuario(string cpfCnpjUsuario)
        {
            if (string.IsNullOrEmpty(cpfCnpjUsuario))
                return null;

            var cpf = cpfCnpjUsuario.OnlyNumbers();

            return _usuarioRepository
                .Find(x => x.CPFCNPJ == cpfCnpjUsuario && x.Ativo)
                .Include(x => x.Empresa).FirstOrDefault().Empresa;
    }

        public Usuario GetUsuarioGestaoEntregas(string cpfCnpj)
        {
            return _usuarioRepository
                .Find(x => x.CPFCNPJ == cpfCnpj && x.Ativo)
                .Include(x => x.Filiais)
                .Include(x => x.Filiais.Select(y => y.Filial))
                .Include(x => x.Empresa)
                .FirstOrDefault();
        }

        public bool HasVeiculoCadastrado(string placa)
        {
            return _veiculoRepository
                    .Find(x => x.Placa == placa && !x.IdEmpresa.HasValue).Count() > 1;
        }

        public int? GetIdEstabelecimentoBase(int idUsuario)
        {
            var usuarioEstabelecimento = _repository.GetAll()
                .Where(x => x.IdUsuario == idUsuario)
                .Include(x => x.UsuarioEstabelecimentos)
                .Select(x => x.UsuarioEstabelecimentos)
                .FirstOrDefault();

            if(!usuarioEstabelecimento.Any())
                return null;

            return usuarioEstabelecimento.Select(x => x.IdEstabelecimento).First();
        }

        public string GetNomeById(int idUsuario)
        {
            return _repository.Find(x => x.IdUsuario == idUsuario)
                .Select(x => x.Nome).FirstOrDefault();
        }

        public object ConsultaGridPorEmpresa(int? idEmpresa, int? idFilial, int? idOperacao, int Take, int Page, OrderFilters orderFilters, List<QueryFilters> Filters, bool marcarTodos, int apertou, List<int> grupoUsuarios)
        {
            var usuariosEmpresa = GetUsuarioPorEmpresa(idEmpresa, idFilial, idOperacao, Take, Page, orderFilters, Filters, grupoUsuarios);
            bool ativo = false;

            if (apertou == 1)
            {
                ativo = marcarTodos;
            }

            return new
            {
                totalItems = usuariosEmpresa.Count(),
                items = usuariosEmpresa.Skip((Page - 1) * Take).Take(Take)
                    .ToList().Select(x => new
                    {
                        Ativo = ativo,
                        x.Nome,
                        x.IdUsuario,
                        DescricaoGrupoUsuario = x.GrupoUsuario?.Descricao
                    })
            };
        }

        private IQueryable<Usuario> GetUsuarioPorEmpresa(int? idEmpresa, int? idFilial, int? idOperacao, int Take, int Page, OrderFilters orderFilters, List<QueryFilters> filters, List<int> grupoUsuarios)
        {
            var usuariosEmpresa = _usuarioRepository
                .GetAll()
                .Include(x => x.GrupoUsuario)
                .Include(x => x.Filiais);

            if (idEmpresa.HasValue)
            {
                usuariosEmpresa = usuariosEmpresa.Where(x => x.IdEmpresa == idEmpresa && x.Ativo);
            }

            if (idFilial.HasValue)
            {
                usuariosEmpresa = usuariosEmpresa.Where(x => x.Filiais.Any(y => y.IdFilial == idFilial));
            }

            if (grupoUsuarios != null)
            {
                usuariosEmpresa = usuariosEmpresa.Where(x => grupoUsuarios.Contains(x.IdGrupoUsuario??0));
            }

            usuariosEmpresa = usuariosEmpresa.Distinct();

            usuariosEmpresa = string.IsNullOrWhiteSpace(orderFilters?.Campo)
                ? usuariosEmpresa.OrderBy(x => x.Nome)
                : usuariosEmpresa.OrderBy($"{orderFilters.Campo} {orderFilters.Operador.DescriptionAttr()}");



            usuariosEmpresa = usuariosEmpresa.AplicarFiltrosDinamicos<Usuario>(filters);

            return usuariosEmpresa;
        }

        public UsuarioMicroServicoInstanciaAppDto UsuarioMicroServicoInstanciaApp(int idUsuario)
        {
            return _repository.UsuarioMicroServicoInstanciaApp(idUsuario);
        }

        public UsuarioFotoDto GetFotoUsuario(string cpfcnpj)
        {
            var usuario = _repository.GetFotoUsuario(cpfcnpj);
            if(usuario?.FotoByte?.Any() == true)
                usuario.Foto = Convert.ToBase64String(usuario.FotoByte);

            return usuario;
        }

        public ValidationResult UpdatePush(int idUsuario, string idPush, ESistemaOperacional? sistemaOperacional)
        {
            var validationResult = new ValidationResult();
            try
            {
                _dapperRepository.UpdatePush(idUsuario, idPush, sistemaOperacional);
            }
            catch (Exception e)
            {
                validationResult.Add(e.Message);
            }

            return validationResult;
        }

        /// <summary>
        /// Retorna se o grupo ao qual o usuário pertence está ativo ou não.
        /// </summary>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public bool UserGroupIsActive(int idUsuario)
        {
            int? idGrupoUsuario = _repository.GetGrupoUsuarioInativo(idUsuario);
            if (idGrupoUsuario.HasValue)
            {
                GrupoUsuario grpUsuario = _grupoUsuarioService.Get(idGrupoUsuario.Value);
                return grpUsuario.Ativo;
            }
            return false;
        }

        public List<Usuario> GetTodos()
        {
            return _repository.All().Include(x => x.Contatos).ToList();
        }

        public bool IncrementarErroSenhaLoginMobile(int usuarioId)
        {
            var bloquearUsuarioErroSenha = _parametrosGenericoService
                .GetParametro<bool?>(GLOBAL.LoginMobileBloquearUsuarioErroSenha, 0) ?? true;

            var qtdErroSenhaBloqueio = _parametrosGenericoService
                .GetParametro<int?>(GLOBAL.LoginMobileQuantidadeErroSenhaBloqueioUsuario, 0) ?? 3;

            var usuario = _repository.FirstOrDefault(c => c.IdUsuario == usuarioId);

            if (usuario == null)
                throw new InvalidOperationException("Erro ao efetuar login. Verifique seus dados e tente novamente.");

            usuario.QuantidadeErroSenha = usuario.QuantidadeErroSenha == null ? 1 : usuario.QuantidadeErroSenha + 1;

            if(usuario.QuantidadeErroSenha >= qtdErroSenhaBloqueio && !bloquearUsuarioErroSenha)
            {
                _repository.SaveChanges();
                return false;
            }

            if (usuario.QuantidadeErroSenha < qtdErroSenhaBloqueio)
            {
                _repository.SaveChanges();
                if (!bloquearUsuarioErroSenha) return false;
                var tentativasRestantes = qtdErroSenhaBloqueio - usuario.QuantidadeErroSenha.Value;
                throw new InvalidOperationException($"Dados inválidos. Você ainda tem {tentativasRestantes} " +
                                                    $"{(tentativasRestantes == 1 ? "tentativa restante" : "tentativas restantes")} " +
                                                    $"antes de ser bloqueado.");
            }

            usuario.Ativo = false;
            usuario.CpfCnpjDesabilitado = usuario.CPFCNPJ;
            usuario.CPFCNPJ = null;
            usuario.LoginDesabilitado = usuario.Login;
            usuario.Login = null;

            // Regra da inativação
            var veiculos = _veiculoRepository.Where(c => c.IdUsuario == usuario.IdUsuario && c.Ativo).ToList();

            if (veiculos.Any())
            {
                foreach (var veiculo in veiculos)
                    veiculo.Ativo = false;
            }

            _repository.SaveChanges();

            return true;
        }

        public void ResetarErroSenhaLoginMobile(int usuarioId)
        {
            var usuario = _repository.Where(c => c.IdUsuario == usuarioId).FirstOrDefault();
            if (usuario == null) throw new InvalidOperationException("Erro ao efetuar login. Verifique seus dados e tente novamente.");
            usuario.QuantidadeErroSenha = 0;
            _repository.SaveChanges();
        }
    }
}

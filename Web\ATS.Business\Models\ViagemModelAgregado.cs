﻿namespace ATS.Domain.Models
{
    public class ViagemModelAgregado
    {
        public string MunicipioOrigem { get; set; }
        public string MunicipioDestino { get; set; }
        public string CepOrigem { get; set; }
        public string <PERSON>p<PERSON><PERSON><PERSON> { get; set; }
        public decimal? PesoSaida { get; set; }
        public decimal Quantidade { get; set; }
        public decimal ValorPedagio { get; set; } = 0;
        public decimal? ValorMercadoria { get; set; }
        public decimal? ValorImpostos { get; set; }
        public int? NaturezaCarga { get; set; }
    }
}
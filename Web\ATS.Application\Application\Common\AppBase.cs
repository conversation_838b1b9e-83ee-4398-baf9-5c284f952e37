﻿using ATS.Domain.Models;
using System;
using System.Web;

namespace ATS.Application.Application.Common
{
    public class AppBase : IDisposable
    {
        #region Propriedades

        /// <summary>
        /// Propriedades que identificam o usuário logado e que esta realizando a modificação
        /// </summary>
        private readonly IdentityUserModel IdentityUser;

        #endregion

        #region Métodos

        public AppBase()
        {
            // TODO: Este método futuramente deve ser alterado para permitir ser utilizado em múltiplos locais.
            if (HttpContext.Current != null &&
                HttpContext.Current.Session != null &&
                HttpContext.Current.Session.Count > 0 &&
                HttpContext.Current.Session["Usuario"] != null)
            {
                object @object = HttpContext.Current.Session["Usuario"];
                int sessionIdUsuario = Convert.ToInt32(@object.GetType().GetProperty("IdUsuario").GetValue(@object));

                IdentityUser = new IdentityUserModel
                {
                    IdUsuario = sessionIdUsuario
                };
            }
        }

        public void Dispose()
        {
        }

        #endregion
    }
}
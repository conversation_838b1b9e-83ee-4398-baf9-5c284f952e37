﻿using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Context;

namespace ATS.Data.Repository.EntityFramework
{
    public class TipoCombustivelRepository : Repository<TipoCombustivel>, ITipoCombustivelRepository
    {
        public TipoCombustivelRepository(AtsContext context) : base(context)
        {
        }
        
        public IQueryable<TipoCombustivel> Listar()
        {
            return from tipoCombustivel in All()
                 select tipoCombustivel;
        }
    }
}
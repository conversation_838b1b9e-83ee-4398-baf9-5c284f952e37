using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Interface.Database.Common;
using ATS.Domain.Interface.Service.Common;

namespace ATS.Domain.Service.Common
{
    public abstract class BaseService<IEntityRepository> : IBaseService<IEntityRepository> where IEntityRepository : IRepository
    {
        public readonly IEntityRepository Repository;
        public readonly IUserIdentity SessionUser;

        protected BaseService(IEntityRepository repository, IUserIdentity sessionUser)
        {
            Repository = repository;
            SessionUser = sessionUser;
        }
        
        public void Dispose()
        {
            Repository?.Dispose();
        }
    }
}
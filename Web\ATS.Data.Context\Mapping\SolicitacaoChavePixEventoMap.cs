﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class SolicitacaoChavePixEventoMap : EntityTypeConfiguration<SolicitacaoChavePixEvento>
    {
        public SolicitacaoChavePixEventoMap()
        {
            ToTable("SOLICITACAO_CHAVE_PIX_EVENTO");

            HasKey(x => x.IdSolicitacaoChavePixEvento);
            Property(x => x.IdSolicitacaoChavePixEvento)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(x => x.DocumentoUsuarioAudit).IsOptional().HasMaxLength(200);
            Property(x => x.DataCadastro).HasColumnType("datetime2");
 
            HasOptional(x => x.UsuarioCadastro)
                .WithMany()
                .HasForeignKey(x => x.IdUsuarioCadastro);

            HasRequired(x => x.SolicitacaoChavePix)
                .WithMany()
                .HasForeignKey(x => x.IdSolicitacaoChavePix);

            HasRequired(x => x.SolicitacaoChavePixStatus)
                .WithMany()
                .HasForeignKey(x => x.IdSolicitacaoChavePixStatus);
        }
    }
}

﻿using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;
using System.Linq;
using System;
using System.Collections.Generic;

namespace ATS.Domain.Service
{
    public class UsuarioDocumentoService : ServiceBase, IUsuarioDocumentoService
    {
        private readonly IUsuarioDocumentoRepository _usuarioDocumentoRepository;
        private readonly IUsuarioRepository _usuarioRepository;

        public UsuarioDocumentoService(IUsuarioDocumentoRepository usuarioDocumentoRepository, IUsuarioRepository usuarioRepository)
        {
            _usuarioDocumentoRepository = usuarioDocumentoRepository;
            _usuarioRepository = usuarioRepository;
        }

        /// <summary>
        /// Retorna todas as avaliações de um motorista
        /// </summary>
        /// <param name="idUsuario">Código do motorista</param>
        /// <returns></returns>
        public IQueryable<UsuarioDocumento> GetDocumentos(int idUsuario)
        {
            return _usuarioDocumentoRepository.GetDocumentos(idUsuario);
        }

        public UsuarioDocumento GetDocCNH(int idUsuario)
        {
            return _usuarioDocumentoRepository.GetDocCNH(idUsuario);
        }

        public UsuarioDocumento GetDocCNH(int idUsuario, int idTipoDoc)
        {
            return _usuarioDocumentoRepository.GetDocCNH(idUsuario, idTipoDoc);
        }

        public void RemoverItem(UsuarioDocumento item)
        {
            _usuarioDocumentoRepository.Delete(item);
        }

        public void AtualizarAvisoValidadeCNH(Motorista motorista)
        {
            var usuarioMotorista = _usuarioRepository.GetPorCNPJCPF(motorista.CPF);
            if (usuarioMotorista != null)
            {
                var motDocs = GetDocumentos(usuarioMotorista.IdUsuario);

                if (motDocs.Any())
                {
                    var docCNH = motDocs.FirstOrDefault(x => x.TipoDocumento.Descricao == "CNH");
                    if (docCNH != null)
                    {
                        docCNH.AvisoValidade = DateTime.Now;
                        _usuarioDocumentoRepository.Update(docCNH);
                    }
                }
            }
        }

        public List<Tuple<int, DateTime?>> GetDocumentoCNHPorIdUsuIdTipoDoc(List<int> usuariosMotoristas, int tpDoc)
        {
            return _usuarioDocumentoRepository.GetDocumentoCNHPorIdUsuIdTipoDoc(usuariosMotoristas, tpDoc);
        }

        public void UpdateusuarioDocumento(UsuarioDocumento doc)
        {
            try
            {
                _usuarioDocumentoRepository.Update(doc);
            }
            catch (Exception)
            {
            }
        }
    }
}
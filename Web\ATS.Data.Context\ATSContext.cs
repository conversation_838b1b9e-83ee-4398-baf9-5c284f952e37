﻿using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Diagnostics;
using System.Web;
using ATS.Data.Context.Config;
using ATS.Data.Context.Conventions;
using ATS.Data.Context.Mapping;
using ATS.Data.Context.Trigger;
using ATS.Domain.Entities;
using ATS.Domain.Trigger;
using Autofac;

namespace ATS.Data.Context
{
    public class AtsContext : BaseDbContext
    {
        static AtsContext()
        {
            // Se a base não existir, irá realizar a instalação da base de dados
            Database.SetInitializer<AtsContext>(null);
        }

        public AtsContext(ILifetimeScope serviceProvider) : base("csATS", serviceProvider, HttpContext.Current != null && HttpContext.Current.Session != null
            ? HttpContext.Current.Session["Usuario"] != null
                ? HttpContext.Current.Session["Usuario"].GetType().GetProperty("IdUsuario")?.GetValue(HttpContext.Current.Session["Usuario"], null)
                : null
            : null)
        {
            Configuration.LazyLoadingEnabled = false;

#if DEBUG
            Database.Log = s => Debug.Write(s);
#endif
            Database.CommandTimeout = 600;
        }

        public AtsContext() : this(null)
        {

        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            #region Regras de Tabelas

            // Remover a pluralização de tabelas
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();

            // Remover o 'delete' em cascata
            modelBuilder.Conventions.Remove<OneToManyCascadeDeleteConvention>();

            // Remover o 'delete' em casos de muitos para muitos
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();

            // Configurar para que os nomes dos campos sejam todos em caixa baixa
            modelBuilder.Conventions.Add(new LowerCaseConvention());

            #endregion

            #region Regras de campos

            // Configurar todos os campos que possuirem o nome Latitude, para um decimal de formatação 10 com precisão de 7 digitos.
            modelBuilder.Properties()
                .Where(p => p.ReflectedType != null && p.Name.ToLower() == "latitude")
                .Configure(p => p.HasPrecision(10, 7));

            // Configurar todos os campos que possuirem o nome Longitude, para um decimal de formatação 10 com precisão de 7 digitos.
            modelBuilder.Properties()
                .Where(p => p.ReflectedType != null && p.Name.ToLower() == "longitude")
                .Configure(p => p.HasPrecision(10, 7));

            // Configura para que todas as propriedades definidas como "string" devem ser configuradas como "varchar" e não mais "nvarchar"
            modelBuilder.Properties<string>()
                .Where(p => p.ReflectedType != null && p.Name.ToLower() != "requisicao" && p.Name.ToLower() != "mensagemerro")
                .Configure(p => p.HasColumnType("varchar"));

            // Configurar para que as propriedades de de string quando não configuradas, deverão possuir como configuração de varchar de 100
            modelBuilder.Properties<string>()
                .Configure(p => p.HasMaxLength(100));

            #endregion

            modelBuilder.Configurations.Add(new CidadeMap());
            modelBuilder.Configurations.Add(new ClienteMap());
            modelBuilder.Configurations.Add(new ClienteAcessoMap());
            modelBuilder.Configurations.Add(new CategoriaMap());
            modelBuilder.Configurations.Add(new MensagemMap());
            modelBuilder.Configurations.Add(new MensagemGrupoUsuarioMap());
            modelBuilder.Configurations.Add(new GruposUsuarioMap());
            modelBuilder.Configurations.Add(new MensagemDestinatarioMap());
            modelBuilder.Configurations.Add(new MensagemGrupoDestinatarioMap());
            modelBuilder.Configurations.Add(new NotificacaoMap());
            modelBuilder.Configurations.Add(new EmpresaMap());
            modelBuilder.Configurations.Add(new EmpresaIndicadoresMap());
            modelBuilder.Configurations.Add(new EmpresaLayoutMap());
            //modelBuilder.Configurations.Add(new EmpresaParametroMap());
            modelBuilder.Configurations.Add(new PlanoEmpresaMap());
            modelBuilder.Configurations.Add(new PlanoMap());
            modelBuilder.Configurations.Add(new EstadoMap());
            modelBuilder.Configurations.Add(new FilialMap());
            modelBuilder.Configurations.Add(new GrupoUsuarioMap());
            modelBuilder.Configurations.Add(new GrupoUsuarioMenuMap());
            modelBuilder.Configurations.Add(new MenuMap());
            modelBuilder.Configurations.Add(new ModuloMap());
            modelBuilder.Configurations.Add(new EmpresaModuloMap());
            modelBuilder.Configurations.Add(new MotoristaMap());
            modelBuilder.Configurations.Add(new MotoristaMovelMap());
            modelBuilder.Configurations.Add(new RotaModeloMap());
            modelBuilder.Configurations.Add(new PontosRotaModeloMap());
            modelBuilder.Configurations.Add(new PracasRotaModeloMap());
            modelBuilder.Configurations.Add(new PaisMap());
            modelBuilder.Configurations.Add(new TipoCombustivelMap());
            modelBuilder.Configurations.Add(new UsuarioMap());
            modelBuilder.Configurations.Add(new UsuarioFilialMap());
            modelBuilder.Configurations.Add(new UsuarioEnderecoMap());
            modelBuilder.Configurations.Add(new UsuarioContatoMap());
            modelBuilder.Configurations.Add(new UsuarioClienteMap());
            modelBuilder.Configurations.Add(new UsuarioHorarioCheckInMap());
            modelBuilder.Configurations.Add(new VeiculoMap());
            modelBuilder.Configurations.Add(new VeiculoDigitosMercosulMap());
            modelBuilder.Configurations.Add(new ViagemMap());
            modelBuilder.Configurations.Add(new ViagemCheckMap());
            modelBuilder.Configurations.Add(new ViagemCargaMap());
            modelBuilder.Configurations.Add(new ViagemRotaMap());
            modelBuilder.Configurations.Add(new ViagemRotaPontoMap());
            modelBuilder.Configurations.Add(new FornecedorCnpjPedagioMap());
            modelBuilder.Configurations.Add(new ProprietarioMap());
            modelBuilder.Configurations.Add(new ProprietarioContatoMap());
            modelBuilder.Configurations.Add(new ProprietarioEnderecoMap());
            modelBuilder.Configurations.Add(new CheckInMap());
            modelBuilder.Configurations.Add(new TipoCavaloMap());
            modelBuilder.Configurations.Add(new TipoCarretaMap());
            modelBuilder.Configurations.Add(new EspecieMap());
            modelBuilder.Configurations.Add(new VeiculoTipoCombustivelMap());
            modelBuilder.Configurations.Add(new AutenticacaoAplicacaoMap());
            modelBuilder.Configurations.Add(new AutorizacaoEmpresaMap());
            modelBuilder.Configurations.Add(new ModuloMenuMap());
            modelBuilder.Configurations.Add(new UsuarioPreferenciasMap());
            modelBuilder.Configurations.Add(new AuthSessionMap());
            modelBuilder.Configurations.Add(new VeiculosHistoricoEmpresaMap());
            modelBuilder.Configurations.Add(new ViagemVirtualMap());
            modelBuilder.Configurations.Add(new TipoNotificacaoMap());
            modelBuilder.Configurations.Add(new NotificacaoPushMap());
            modelBuilder.Configurations.Add(new NotificacaoPushItemMap());
            modelBuilder.Configurations.Add(new NotificacaoPushGrupoUsuarioMap());
            modelBuilder.Configurations.Add(new IconeMap());
            modelBuilder.Configurations.Add(new TipoEstabelecimentoMap());
            modelBuilder.Configurations.Add(new UsoTipoEstabelecimentoMap());
            modelBuilder.Configurations.Add(new EstabelecimentoMap());
            modelBuilder.Configurations.Add(new EstabelecimentoProdutoMap());
            modelBuilder.Configurations.Add(new EstabelecimentoBaseProdutoMap());
            modelBuilder.Configurations.Add(new ViagemCarretaMap());
            modelBuilder.Configurations.Add(new DocumentoMap());
            modelBuilder.Configurations.Add(new MotivoMap());
            modelBuilder.Configurations.Add(new TipoMotivoMap());
            modelBuilder.Configurations.Add(new EstabelecimentoBaseMap());
            modelBuilder.Configurations.Add(new PagamentoConfiguracaoMap());
            modelBuilder.Configurations.Add(new PagamentoConfiguracaoProcessoMap());
            modelBuilder.Configurations.Add(new CredenciamentoMap());
            modelBuilder.Configurations.Add(new CredenciamentoAnexoMap());
            modelBuilder.Configurations.Add(new UsuarioEstabelecimentoMap());
            modelBuilder.Configurations.Add(new ViagemRegraMap());
            modelBuilder.Configurations.Add(new ViagemEstabelecimentoMap());
            modelBuilder.Configurations.Add(new ViagemEventoMap());
            modelBuilder.Configurations.Add(new ViagemDocumentoMap());
            modelBuilder.Configurations.Add(new ViagemValorAdicionalMap());
            modelBuilder.Configurations.Add(new ProtocoloMap());
            modelBuilder.Configurations.Add(new ProtocoloEventoMap());
            modelBuilder.Configurations.Add(new ProtocoloAnexoMap());
            modelBuilder.Configurations.Add(new ProtocoloAntecipacaoMap());
            modelBuilder.Configurations.Add(new LayoutMap());
            modelBuilder.Configurations.Add(new EstabelecimentoAssociacaoMap());
            modelBuilder.Configurations.Add(new RotaMap());
            modelBuilder.Configurations.Add(new RotaTrajetoMap());
            modelBuilder.Configurations.Add(new RotaEstabelecimentoMap());
            modelBuilder.Configurations.Add(new ViagemSolicitacaoAbonoMap());
            modelBuilder.Configurations.Add(new TipoDocumentoMap());
            modelBuilder.Configurations.Add(new ProdutoMap());
            modelBuilder.Configurations.Add(new ConjuntoMap());
            modelBuilder.Configurations.Add(new ConjuntoCarretaMap());
            modelBuilder.Configurations.Add(new FilialContatosMap());
            modelBuilder.Configurations.Add(new PINMap());
            modelBuilder.Configurations.Add(new ConjuntoEmpresaMap());
            modelBuilder.Configurations.Add(new ConjuntoCarretaEmpresaMap());
            modelBuilder.Configurations.Add(new UsuarioDocumentoMap());
            modelBuilder.Configurations.Add(new LayoutCartaoMap());
            modelBuilder.Configurations.Add(new TipoCavaloClienteMap());
            modelBuilder.Configurations.Add(new LayoutCartaoItemMap());
            modelBuilder.Configurations.Add(new TransacaoCartaoMap());
            modelBuilder.Configurations.Add(new ContratoMap());
            modelBuilder.Configurations.Add(new DeclaracaoCiotMap());
            modelBuilder.Configurations.Add(new LogSmsMap());
            modelBuilder.Configurations.Add(new ClienteProdutoEspecieMap());
            modelBuilder.Configurations.Add(new ClienteEnderecoMap());
            modelBuilder.Configurations.Add(new BloqueioFinanceiroTipoMap());
            modelBuilder.Configurations.Add(new BloqueioGestorTipoMap());
            modelBuilder.Configurations.Add(new BloqueioGestorValorMap());
            modelBuilder.Configurations.Add(new UsuarioPermissaoFinanceiroMap());
            modelBuilder.Configurations.Add(new UsuarioPermissaoGestorMap());
            modelBuilder.Configurations.Add(new EmpresaContaBancariaMap());
            modelBuilder.Configurations.Add(new EstabelecimentoBaseAssociacaoMap());
            modelBuilder.Configurations.Add(new CredenciamentoMotivoMap());
            modelBuilder.Configurations.Add(new CargaAvulsaMap());
            modelBuilder.Configurations.Add(new ContratoCiotAgregadoMap());
            modelBuilder.Configurations.Add(new ContratoCiotAgregadoVeiculoMap());
            modelBuilder.Configurations.Add(new ViagemPendenteGestorMap());
            modelBuilder.Configurations.Add(new ViagemDocumentoFiscalMap());
            modelBuilder.Configurations.Add(new AdministradoraPlataformaMapping());
            modelBuilder.Configurations.Add(new CteMap());
            modelBuilder.Configurations.Add(new CombustivelJSLMap());
            modelBuilder.Configurations.Add(new CombustivelJSLEstabelecimentoBaseMap());
            modelBuilder.Configurations.Add(new AtendimentoPortadorMap());
            modelBuilder.Configurations.Add(new AtendimentoPortadorTramiteMap());
            modelBuilder.Configurations.Add(new ParametrosMap());
            modelBuilder.Configurations.Add(new PedagioRotaMap());
            modelBuilder.Configurations.Add(new PedagioRotaPontoMap());
            modelBuilder.Configurations.Add(new EstabelecimentoContaBancariaMap());
            modelBuilder.Configurations.Add(new EstabelecimentoBaseContaBancariaMap());
            modelBuilder.Configurations.Add(new EstabelecimentoBaseDocumentoMap());
            modelBuilder.Configurations.Add(new WebHookMap());
            modelBuilder.Configurations.Add(new ProdutoDadosCargaMap());
            modelBuilder.Configurations.Add(new ConsumoServicoExternoMap());
            modelBuilder.Configurations.Add(new CheckinResumoMap());
            modelBuilder.Configurations.Add(new UsuarioPermissoesConcedidasMobileMap());

            modelBuilder.Configurations.Add(new ViagemEventoProtocoloAnexoMap());
            modelBuilder.Configurations.Add(new ResgateCartaoAtendimentoMap());

            #region Register triggers

            TriggerManager.Register(typeof(Protocolo), typeof(ProtocoloTrigger));
            TriggerManager.Register(typeof(EstabelecimentoBase), typeof(EstabelecimentoBaseTrigger));

            //TriggerManager.Register(typeof(Viagem), typeof(ViagemTrigger));
            TriggerManager.Register(typeof(ViagemEvento), typeof(ViagemEventoTrigger));
            #endregion

            modelBuilder.Configurations.Add(new ViagemPagamentoContaMap());
            modelBuilder.Configurations.Add(new ProjetoFirebaseMapping());
            modelBuilder.Configurations.Add(new WhiteListIPMap());
            modelBuilder.Configurations.Add(new LocalizacaoUsuarioMap());
            modelBuilder.Configurations.Add(new DespesaUsuarioMap());
            modelBuilder.Configurations.Add(new LimiteTransacaoPortadorMap());
            modelBuilder.Configurations.Add(new BloqueioOrigemTipoMap());
            modelBuilder.Configurations.Add(new BlacklistIpMap());
            modelBuilder.Configurations.Add(new CampanhaMap());
            modelBuilder.Configurations.Add(new CampanhaRespostaMap());
            modelBuilder.Configurations.Add(new LocalizacaoUsuarioPortalMap());
            modelBuilder.Configurations.Add(new TagMap());
            modelBuilder.Configurations.Add(new UsuarioPermissaoCartaoMap());
            modelBuilder.Configurations.Add(new BloqueioCartaoTipoMap());
            modelBuilder.Configurations.Add(new BannerMap());
            modelBuilder.Configurations.Add(new BannerUsuarioMap());
            modelBuilder.Configurations.Add(new GestorUsuarioMap());
            modelBuilder.Configurations.Add(new PrestacaoContasMap());
            modelBuilder.Configurations.Add(new PrestacaoContasEventoMap());
            modelBuilder.Configurations.Add(new TransacaoPixMap());
            modelBuilder.Configurations.Add(new TransacaoPixStatusMap());
            modelBuilder.Configurations.Add(new AvaliacaoPlanilhaGestorCargaAvulsaMap());
            modelBuilder.Configurations.Add(new SolicitacaoChavePixStatusMap());
            modelBuilder.Configurations.Add(new SolicitacaoChavePixMap());
            modelBuilder.Configurations.Add(new SolicitacaoChavePixEventoMap());
            modelBuilder.Configurations.Add(new SerproCacheResultadoMap());
            modelBuilder.Configurations.Add(new SerproCacheMap());

        }

        #region Entidades
        public DbSet<Tag> Tag { get; set; }
        public DbSet<Cidade> Cidade { get; set; }
        public DbSet<Cliente> Cliente { get; set; }
        public DbSet<ClienteAcesso> ClienteAcesso { get; set; }
        public DbSet<Categoria> Categorias { get; set; }
        public DbSet<Mensagem> Mensagem { get; set; }
        public DbSet<Notificacao> Notificacao { get; set; }
        public DbSet<MensagemDestinatario> MensagemDestinatario { get; set; }
        public DbSet<MensagemGrupoDestinatario> MensagemGrupoDestinatario { get; set; }
        public DbSet<MensagemGrupoUsuario> MensagemGrupoUsuario { get; set; }
        public DbSet<GruposUsuarios> GruposUsuarios { get; set; }
        public DbSet<Empresa> Empresa { get; set; }
        public DbSet<EmpresaIndicadores> EmpresaIndicadores { get; set; }
        public DbSet<EmpresaLayout> EmpresaLayout { get; set; }
        //public DbSet<EmpresaParametro> EmpresaParametro { get; set; }
        public DbSet<PlanoEmpresa> PlanoEmpresa { get; set; }
        public DbSet<Plano> Plano { get; set; }
        public DbSet<Estado> Estado { get; set; }
        public DbSet<Filial> Filial { get; set; }
        public DbSet<GrupoUsuario> GrupoUsuario { get; set; }
        public DbSet<GrupoUsuarioMenu> GrupoUsuarioMenu { get; set; }
        public DbSet<Menu> Menu { get; set; }
        public DbSet<Modulo> Modulo { get; set; }
        public DbSet<EmpresaModulo> EmpresaModulo { get; set; }
        public DbSet<Motorista> Motorista { get; set; }
        public DbSet<MotoristaMovel> MotoristaMovel { get; set; }
        public DbSet<PontosRotaModelo> PontosRotaModelo { get; set; }
        public DbSet<RotaModelo> RotaModelo { get; set; }
        public DbSet<PracasRotaModelo> PracasRotaModelo { get; set; }
        public DbSet<Pais> Pais { get; set; }
        public DbSet<TipoCombustivel> TipoCombustivel { get; set; }
        public DbSet<Usuario> Usuario { get; set; }
        public DbSet<UsuarioFilial> UsuarioFilial { get; set; }
        public DbSet<UsuarioEndereco> UsuarioEndereco { get; set; }
        public DbSet<UsuarioContato> UsuarioContato { get; set; }
        public DbSet<UsuarioCliente> UsuarioCliente { get; set; }
        public DbSet<UsuarioHorarioCheckIn> UsuarioCheckIn { get; set; }
        public DbSet<Veiculo> Veiculo { get; set; }
        public DbSet<VeiculoDigitosMercosul> VeiculoDigitosMercosul { get; set; }
        public DbSet<Viagem> Viagem { get; set; }
        public DbSet<ViagemCheck> Check { get; set; }
        public DbSet<ViagemCarga> ViagemCarga { get; set; }
        public DbSet<Proprietario> Proprietario { get; set; }
        public DbSet<ProprietarioContato> ProprietarioContato { get; set; }
        public DbSet<ProprietarioEndereco> ProprietarioEndereco { get; set; }
        public DbSet<CheckIn> CheckIn { get; set; }
        public DbSet<TipoCavalo> TipoCavalo { get; set; }
        public DbSet<TipoCarreta> TipoCarreta { get; set; }
        public DbSet<Especie> Especie { get; set; }
        public DbSet<VeiculoTipoCombustivel> VeiculoTipoCombustivel { get; set; }
        public DbSet<AutenticacaoAplicacao> AutenticacaoEmpresa { get; set; }
        public DbSet<AutorizacaoEmpresa> AutorizacaoEmpresa { get; set; }
        public DbSet<ModuloMenu> ModuloMenu { get; set; }
        public DbSet<UsuarioPreferencias> UsuarioPreferencias { get; set; }
        public DbSet<AuthSession> AuthSession { get; set; }
        public DbSet<TipoNotificacao> TipoNotificacao { get; set; }
        public DbSet<NotificacaoPush> NotificacaoPush { get; set; }
        public DbSet<NotificacaoPushItem> NotificacaoPushItem { get; set; }
        public DbSet<NotificacaoPushGrupoUsuario> NotificacaoPushGrupoUsuario { get; set; }
        public DbSet<Icone> Icone { get; set; }
        public DbSet<TipoEstabelecimento> TipoEstabelecimento { get; set; }
        public DbSet<UsoTipoEstabelecimento> UsoTipoEstabelecimento { get; set; }
        public DbSet<Estabelecimento> Estabelecimento { get; set; }
        public DbSet<EstabelecimentoProduto> EstabelecimentoProduto { get; set; }
        public DbSet<EstabelecimentoBaseProduto> EstabelecimentoBaseProduto { get; set; }
        public DbSet<ViagemCarreta> ViagemCarreta { get; set; }
        public DbSet<Documento> Documento { get; set; }
        public DbSet<Motivo> Motivo { get; set; }
        public DbSet<TipoMotivo> TipoMotivo { get; set; }
        public DbSet<EstabelecimentoBase> EstabelecimentoBase { get; set; }
        public DbSet<PagamentoConfiguracao> PagamentoConfiguracao { get; set; }
        public DbSet<PagamentoConfiguracaoProcesso> PagamentoConfiguracaoProcesso { get; set; }
        public DbSet<Credenciamento> Credenciamento { get; set; }
        public DbSet<CredenciamentoAnexo> CredenciamentoAnexo { get; set; }
        public DbSet<UsuarioEstabelecimento> UsuarioEstabelecimento { get; set; }
        public DbSet<ViagemRegra> ViagemRegra { get; set; }
        public DbSet<ViagemEstabelecimento> ViagemEstabelecimento { get; set; }
        public DbSet<ViagemEvento> ViagemEvento { get; set; }
        public DbSet<ViagemDocumento> ViagemDocumento { get; set; }
        public DbSet<ViagemValorAdicional> ViagemValorAdicional { get; set; }
        public DbSet<Protocolo> Protocolo { get; set; }
        public DbSet<ProtocoloEvento> ProtocoloEvento { get; set; }
        public DbSet<ProtocoloAnexo> ProtocoloAnexo { get; set; }
        public DbSet<ProtocoloAntecipacao> ProtocoloAntecipacao { get; set; }
        public DbSet<Layout> Layout { get; set; }
        public DbSet<EstabelecimentoAssociacao> EstabelecimentoAssociacao { get; set; }
        public DbSet<Rota> Rota { get; set; }
        public DbSet<RotaTrajeto> RotaTrajeto { get; set; }
        public DbSet<RotaEstabelecimento> RotaEstabelecimento { get; set; }
        public DbSet<TipoDocumento> TipoDocumento { get; set; }
        public DbSet<UsuarioDocumento> UsuarioDocumento { get; set; }
        public DbSet<VeiculosHistoricoEmpresa> VeiculosHistoricoEmpresa { get; set; }
        public DbSet<ViagemVirtual> ViagemVirtual { get; set; }

        public DbSet<Produto> Produtos { get; set; }
        public DbSet<Conjunto> Conjunto { get; set; }
        public DbSet<ConjuntoCarreta> ConjuntoCarreta { get; set; }
        public DbSet<FilialContatos> FilialContatos { get; set; }
        public DbSet<PIN> PIN { get; set; }
        public DbSet<ConjuntoEmpresa> ConjuntoEmpresa { get; set; }
        public DbSet<ConjuntoCarretaEmpresa> ConjuntoCarretaEmpresa { get; set; }

        public DbSet<TipoCavaloCliente> TipoCavaloCliente { get; set; }
        public DbSet<Contrato> Contrato { get; set; }
        public DbSet<LayoutCartao> LayoutCartao { get; set; }
        public DbSet<LayoutCartaoItem> LayoutCartaoItem { get; set; }

        public DbSet<LogSms> LogSms { get; set; }
        public DbSet<EstabelecimentoBaseAssociacao> EstabelecimentoBaseAssociacao { get; set; }
        public DbSet<CredenciamentoMotivo> CredenciamentoMotivo { get; set; }
        public DbSet<ClienteProdutoEspecie> ClienteProdutoEspecie { get; set; }
        public DbSet<ClienteEndereco> ClienteEndereco { get; set; }
        public DbSet<DeclaracaoCiot> DeclaracaoCiot { get; set; }
        public DbSet<Webhook> Webhook { get; set; }
        public DbSet<BloqueioGestorTipo> BloqueioGestorTipo { get; set; }
        public DbSet<BloqueioGestorValor> BloqueioGestorValor { get; set; }
        public DbSet<UsuarioPermissaoGestor> UsuarioPermissaoGestor { get; set; }
        public DbSet<ViagemPendenteGestor> ViagemPendenteGestor { get; set; }
        public DbSet<EmpresaContaBancaria> EmpresaContaBancaria { get; set; }
        public DbSet<CargaAvulsa> CargaAvulsa { get; set; }
        public DbSet<ViagemDocumentoFiscal> ViagemDocumentoFiscal { get; set; }

        public DbSet<Parametros> Parametros { get; set; }
        public DbSet<EstabelecimentoContaBancaria> EstabelecimentoContaBancaria { get; set; }
        public DbSet<EstabelecimentoBaseContaBancaria> EstabelecimentoBaseContaBancaria { get; set; }
        public DbSet<EstabelecimentoBaseDocumento> EstabelecimentoBaseDocumento { get; set; }
        public DbSet<ProdutoDadosCarga> ProdutoDadosCarga { get; set; }
        public DbSet<PedagioRota> PedagioRota { get; set; }
        public DbSet<PedagioRotaPonto> PedagioRotaPonto { get; set; }


        public DbSet<ViagemEventoProtocoloAnexo> ViagemEventoProtocoloAnexo { get; set; }

        public DbSet<AtendimentoPortador> AtendimentoPortador { get; set; }
		public DbSet<AtendimentoPortadorTramite> AtendimentoPortadorTramite { get; set; }
        public DbSet<ConsumoServicoExterno> ConsumoServicoExterno { get; set; }

        public DbSet<CheckinResumo> CheckinResumo { get; set; }
        public DbSet<UsuarioPermissoesConcedidasMobile> UsuarioPermissoesConcedidasMobile { get; set; }
        public DbSet<ResgateCartaoAtendimento> ResgatarCartao { get; set; }

        public DbSet<ViagemPagamentoConta> ViagemPagamentoConta { get; set; }
        public DbSet<ProjetoFirebase> ProjetoFirebase { get; set; }
        public DbSet<WhiteListIP> WhiteListIps { get; set; }
        public DbSet<DespesaUsuario> DespesaUsuarios { get; set; }
        public DbSet<LocalizacaoUsuario> LocalizacaoUsuarios { get; set; }
        public DbSet<BloqueioOrigemTipo> BloqueioOrigemTipo { get; set; }
        
        public DbSet<BlacklistIp> BlacklistIp { get; set; }
        public DbSet<Campanha> Campanha { get; set; }
        public DbSet<CampanhaResposta> CampanhaResposta { get; set; }
        public DbSet<LocalizacaoUsuarioPortal> LocalizacaoUsuarioPortal { get; set; }
        public DbSet<BloqueioCartaoTipo> BloqueioCartaoTipo { get; set; }
        public DbSet<UsuarioPermissaoCartao> UsuarioPermissaoCartao { get; set; }
        public DbSet<Banner> Banner { get; set; }
        public DbSet<BannerUsuario> BannerUsuario { get; set; }
        public DbSet<GestorUsuario> GestorUsuario { get; set; }
        public DbSet<PrestacaoContas> PrestacaoContas { get; set; }
        public DbSet<PrestacaoContasEvento> PrestacaoContasEvento { get; set; }
        public DbSet<TransacaoPix> TransacaoPix { get; set; }
        public DbSet<TransacaoPixStatus> TransacaoPixStatus { get; set; }
        public DbSet<AvaliacaoPlanilhaGestorCargaAvulsa> AvaliacaoPlanilhaGestorCargaAvulsa { get; set; }
        public DbSet<SolicitacaoChavePixStatus> SolicitacaoChavePixStatus { get; set; }
        public DbSet<SolicitacaoChavePix> SolicitacaoChavePix { get; set; }
        public DbSet<SolicitacaoChavePixEvento> SolicitacaoChavePixEvento { get; set; }
        public DbSet<SerproCache> SerproCache { get; set; }
        public DbSet<SerproCacheResultado> SerproCacheResultado { get; set; }

        #endregion
    }
}

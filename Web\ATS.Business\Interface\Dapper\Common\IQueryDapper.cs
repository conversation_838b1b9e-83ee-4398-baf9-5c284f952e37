﻿using System;
using System.Collections.Generic;
using System.Data;
using Dapper;

namespace ATS.Domain.Interface.Dapper.Common
{
    public interface IQueryDapper<T> : IDisposable
    {
        IDbConnection GetConnection();
        IEnumerable<T> RunSelect(string select);
        IEnumerable<T> RunSelect(string @select, object param);
        IEnumerable<UserDTO> RunSelect<UserDTO>(string select);

        IEnumerable<T> RunSelect(string @select, DynamicParameters @params);
    }
}
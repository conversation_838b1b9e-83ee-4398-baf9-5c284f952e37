﻿using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;
using System.Collections.Generic;
using System.Linq;
using ATS.Domain.DTO;
using ATS.Domain.DTO.Veiculo;
using ATS.Domain.Grid;

namespace ATS.Application.Interface
{
    public interface IVeiculoApp : IAppBase<Veiculo>
    {
        Veiculo GetWithAllChilds(int idVeiculo);
        ValidationResult Add(Veiculo veiculo);
        ValidationResult Update(Veiculo veiculo);
        IQueryable<Veiculo> GetVeiculosPorNumerosFrotas(List<long> numeroFrota);
        Veiculo GetVeiculoPorPlaca(string placa, int? idEmpresa);
        ConsultaPlacaDTO PlacaExistente(string placa, int? idEmpresa);
        object ConsultarGrid(int? idEmpresa, bool comTracao, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultaDadosVeiculoResponseDTO ConsultaDadosVeiculo(int idVeiculo);
        IQueryable<Veiculo> QueryById(int idVeiculo);
        IQueryable<Veiculo> Query(string placa, int? idEmpresa = null, bool? ativo = true, bool somenteTerceiros = false);
        IQueryable<Veiculo> GetVeiculosByListIdVeiculos(List<int> lIdVeiculo, int? idEmpresa = null);
        bool VeiculoValidoIntegracao(string placa, int? idEmpresa = null);
        IEnumerable<Veiculo> GetTodosVeiculosPorPlaca(string placa, int idEmpresa);
        Veiculo GetVeiculoPorEmpresaMotorista(int idEmpresa, int idMotorista);
        IQueryable<Veiculo> GetVeiculosPorPlaca(List<string> placas);
        List<string> GetPlacasPorEmpresa(int idEmpresa, int? idOperacao);
        Veiculo GetVeiculoTerceiro(string placa);
        ValidationResult AlterarStatus(int idVeiculo);
        VeiculoGridResponse ConsultarGrid(int? idEmpresa, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        byte[] GerarRelatorioGridVeiculos(int? idEmpresa, OrderFilters orderFilters, List<QueryFilters> filters, string logo, string extensao);
        object ConsultarGridVeiculoEmpresa(int? idEmpresa, int? idFilial, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool marcarTodos, int apertou);
        bool PertenceAEmpresa(int idempresa, int idVeiculo);
    }
}
﻿using ATS.Domain.Entities;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class EstabelecimentoMap : EntityTypeConfiguration<Estabelecimento>
    {
        public EstabelecimentoMap()
        {
            ToTable("ESTABELECIMENTO");

            HasKey(x => x.IdEstabelecimento);

            Property(x => x.DataUltimaAtualizacao)
                .IsOptional();

            Property(x => x.CEP)
                .HasMaxLength(20);

            Property(x => x.Telefone)
                .HasMaxLength(16);

            HasRequired(x => x.Empresa)
                .WithMany(x => x.Estabelecimentos)
                .HasForeignKey(x => x.IdEmpresa);

            HasRequired(x => x.TipoEstabelecimento)
                .WithMany(x => x.Estabelecimentos)
                .HasForeignKey(x => x.IdTipoEstabelecimento);

            HasRequired(x => x.Pais)
                .WithMany(x => x.Estabelecimentos)
                .HasForeignKey(x => x.IdPais);

            HasRequired(x => x.Estado)
                .WithMany(x => x.Estabelecimentos)
                .HasForeignKey(x => x.IdEstado);

            HasRequired(x => x.Cidade)
                .WithMany(x => x.Estabelecimentos)
                .HasForeignKey(x => x.IdCidade);

            HasOptional(x => x.EstabelecimentoBase)
                .WithMany(x => x.Estabelecimento)
                .HasForeignKey(x => x.IdEstabelecimentoBase);

            Property(o => o.HoraInicialSemValidarChave)
                .IsOptional();

            Property(o => o.HoraFinalSemValidarChave)
                .IsOptional();

            Property(o => o.RealizaPagamentoComCheque)
                .IsRequired();
        }
    }
}

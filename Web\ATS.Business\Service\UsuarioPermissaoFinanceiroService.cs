using System;
using System.Data.Entity;
using System.Linq;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class UsuarioPermissaoFinanceiroService : ServiceBase, IUsuarioPermissaoFinanceiroService
    {
        private readonly IUsuarioPermissaoFinanceiroRepository _usuarioPermissaoFinanceiroRepository;

        public UsuarioPermissaoFinanceiroService(IUsuarioPermissaoFinanceiroRepository usuarioPermissaoFinanceiroRepository)
        {
            _usuarioPermissaoFinanceiroRepository = usuarioPermissaoFinanceiroRepository;
        }

        public ValidationResult Integrar(int idUsuario, EBloqueioFinanceiroTipo idBloqueioGestorTipo, bool bloqueioFinanceiro)
        {
            try
            {
                if (idBloqueioGestorTipo == EBloqueioFinanceiroTipo.exigeAutenticação2FA && bloqueioFinanceiro == false)
                    return new ValidationResult();
                
                var repository = _usuarioPermissaoFinanceiroRepository;
                var permissionUpdate = repository.GetAll().FirstOrDefault(g => g.IdUsuario == idUsuario && g.IdBloqueioGestorTipo == (int) idBloqueioGestorTipo);
                if (permissionUpdate != null)
                {
                    permissionUpdate.DesbloquearFinanceiro = bloqueioFinanceiro;
                    repository.Update(permissionUpdate);
                    return new ValidationResult();
                }

                var newPermission = new UsuarioPermissaoFinanceiro()
                {
                    IdUsuario = idUsuario,
                    IdBloqueioGestorTipo = (int) idBloqueioGestorTipo,
                    DesbloquearFinanceiro = bloqueioFinanceiro
                };
            
                repository.Add(newPermission);
            
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }

        public UsuarioPermissaoFinanceiro GetParametroPermissaoFinanceiro(int idUsuario, EBloqueioFinanceiroTipo idBloqueioFinanceiroTipo)
        {
            var repository = _usuarioPermissaoFinanceiroRepository;
            var permissionUpdate = repository.GetAll().FirstOrDefault(g => g.IdUsuario == idUsuario && g.IdBloqueioGestorTipo == (int) idBloqueioFinanceiroTipo);
            return permissionUpdate;
        }

        public bool PossuiPermissao(int idUsuario, EBloqueioFinanceiroTipo bloqueioFinanceiroTipo)
        {
            var permissao =
                _usuarioPermissaoFinanceiroRepository
                    .GetAll()
                    .FirstOrDefault(g =>
                        g.IdUsuario == idUsuario && g.IdBloqueioGestorTipo == (int) bloqueioFinanceiroTipo);

            if (permissao == null)
                return false;

            return permissao.DesbloquearFinanceiro;
        }

        public IQueryable<UsuarioPermissaoFinanceiro> GetByEmpresa(int idEmpresa)
        {
            return _usuarioPermissaoFinanceiroRepository
                    .GetAll()
                    .Include(x => x.Usuario)
                    .Where(x => x.Usuario != null && x.Usuario.IdEmpresa == idEmpresa);
        }
    }
}
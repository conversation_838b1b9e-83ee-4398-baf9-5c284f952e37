using ATS.Application.Application;
using ATS.Application.Interface;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Validation;
using ATS.WS.Models.Mobile.Common;
using ATS.WS.Models.ViagemV2.Response;
using AutoMapper;

namespace ATS.WS.Services.ViagemV2Services
{
    public class ConsultasViagemV2
    {
        private readonly ICiotV2App _ciotV2App;
        private readonly ICiotV3App _ciotV3App;
        private readonly IViagemApp _viagemApp;
        private readonly IVersaoAnttLazyLoadService _versaoAntt;
        private readonly IEmpresaApp _empresaApp;

        public ConsultasViagemV2(ICiotV2App ciotV2App, ICiotV3App ciotV3App, IViagemApp viagemApp, IVersaoAnttLazyLoadService versaoAntt, IEmpresaApp empresaApp)
        {
            _ciotV2App = ciotV2App;
            _ciotV3App = ciotV3App;
            _viagemApp = viagemApp;
            _versaoAntt = versaoAntt;
            _empresaApp = empresaApp;
        }

        public Retorno<ViagemV2ConsultaResponseModel> Consultar(int? viagemId, string cnpj)
        {
            if (!viagemId.HasValue || viagemId.Value == 0)
                return new Retorno<ViagemV2ConsultaResponseModel>(new ValidationResult().Add("Viagem Id não informado.", EFaultType.Error), null);
            
            var empresaId = _empresaApp.GetIdPorCnpj(cnpj);

            var viagem = _viagemApp.ObterViagemPorEmpresa(viagemId.Value, empresaId ?? 0);
            
            if (viagem == null)
                return new Retorno<ViagemV2ConsultaResponseModel>(false, $"Viagem de código {viagemId} não encontrada.", null);
            
            var viagemModel =  Mapper.Map<ViagemV2ConsultaResponseModel>(viagem);

            viagemModel.Ciot = _versaoAntt.Value == EVersaoAntt.Versao2
                ? _ciotV2App.GetCiotResult(viagemId.Value, empresaId ?? 0)
                : _ciotV3App.GetCiotResult(viagemId.Value, empresaId ?? 0);
            
            viagemModel.Pedagio = _viagemApp.GetStatusPedagio(viagem);
            
            return new Retorno<ViagemV2ConsultaResponseModel>(true, string.Empty, viagemModel);
        }
    }
}
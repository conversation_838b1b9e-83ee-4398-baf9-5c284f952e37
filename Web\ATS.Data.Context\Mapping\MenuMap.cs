using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Infrastructure.Annotations;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class MenuMap : EntityTypeConfiguration<Menu>
    {
        public MenuMap()
        {
            ToTable("MENU");

            HasKey(t => t.IdMenu);

            Property(t => t.IdMenu)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.Descricao)
                .IsRequired()
                .HasMaxLength(100);

            Property(t => t.Link)
                .HasMaxLength(100);

            Property(t => t.Perfis)
                .HasMaxLength(20)
                .IsRequired();
                   
            Ignore(x => x.GrupoUsuarioLista);
        }
    }
}
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Domain.Models;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class Filial
    {
        /// <summary>
        /// Código da Filial
        /// </summary>
        public int IdFilial { get; set; }

        /// <summary>
        /// CNPJ
        /// </summary>
        public string CNPJ { get; set; }

        /// <summary>
        /// Código da filial do sistema externo
        /// </summary>
        public string CodigoFilial { get; set; }
        
        /// <summary>
        /// Razão Social
        /// </summary>
        public string RazaoSocial { get; set; }

        /// <summary>
        /// Nome da Fantasia
        /// </summary>
        public string NomeFantasia { get; set; }

        /// <summary>
        /// Sigla da Filial
        /// </summary>
        public string Sigla { get; set; }

        /// <summary>
        /// CEP
        /// </summary>
        public string CEP { get; set; }

        /// <summary>
        /// Endereço
        /// </summary>
        public string Endereco { get; set; }

        /// <summary>
        /// Complemento do endereço
        /// </summary>
        public string Complemento { get; set; }

        /// <summary>
        /// Número do edifício, ou afim
        /// </summary>
        public int? Numero { get; set; }

        /// <summary>
        /// Bairro
        /// </summary>
        public string Bairro { get; set; }

        /// <summary>
        /// Telefone 
        /// </summary>
        public string Telefone { get; set; }

        /// <summary>
        /// E-mail para contato
        /// </summary
        public string Email { get; set; }

        /// <summary>
        /// Código do Empresa
        /// </summary>
        public int IdEmpresa { get; set; }

        /// <summary>
        /// Código da Cidade
        /// </summary>
        public int IdCidade { get; set; }

        /// <summary>
        /// Código do Estado
        /// </summary>
        public int IdEstado { get; set; }

        /// <summary>
        /// Código do País
        /// </summary>
        public int IdPais { get; set; }

        /// <summary>
        /// Informa se o registro esta deletado ou não
        /// </summary>
        public bool Ativo { get; set; } = true;

        /// <summary>
        /// Latitude da filial (localização)
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// Longitude da filial (localização)
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Inscrição estadual
        /// </summary>
        public int? Ie { get; set; }

        /// <summary>
        /// Data e hora em que o registro foi atualizado pela ultima vez
        /// </summary>
        [SkipTracking]
        public DateTime? DataHoraUltimaAtualizacao { get; set; }

        /// <summary>
        /// Identifica se a filial é um ponto de apoio
        /// </summary>
        public bool PontoApoio { get; set; } = false;

        public int? IdFilialMae { get; set; }

        public string IdSistemaExterno { get; set; }

        public bool? PontoReferencia { get; set; } = false;

        #region Parâmetros de perimetro (Localização)
        /// <summary>
        /// Indica quando está fora de uma área (Ex.: Cerca virtual)
        /// </summary>
        public bool AlertaArea { get; set; }

        /// <summary>
        /// Indica a precisão de uma localização
        /// </summary>
        public decimal Acuracia { get; set; }
        #endregion

        #region Parâmetros de servidor de e-mail

        /// <summary>
        /// Nome da conta
        /// </summary>
        public string EmailNome { get; set; }

        /// <summary>
        /// Endereço da conta
        /// </summary>
        public string EmailEndereco { get; set; }

        /// <summary>
        /// Porta do servidor
        /// </summary>
        public decimal? EmailPorta { get; set; }

        /// <summary>
        /// Servidor de saída
        /// </summary>
        public string EmailServidor { get; set; }

        /// <summary>
        /// SSL seguro
        /// </summary>
        public bool EmailSsl { get; set; }

        /// <summary>
        /// Usuário da conta
        /// </summary>
        public string EmailUsuario { get; set; }

        /// <summary>
        /// Senha da conta
        /// </summary>
        public string EmailSenha { get; set; }
        #endregion

        #region Referências

        /// <summary>
        /// Cidade
        /// </summary>
        public virtual Cidade Cidade { get; set; }

        /// <summary>
        /// Estado
        /// </summary>
        public virtual Estado Estado { get; set; }

        /// <summary>
        /// País
        /// </summary>
        public virtual Pais Pais { get; set; }

        /// <summary>
        /// Empresa
        /// </summary>
        public virtual Empresa Empresa { get; set; }

        /// <summary>
        /// Filial contatos
        /// </summary>
        public virtual ICollection<FilialContatos> FilialContatos { get; set; }

        #endregion

        #region Navegação Inversa
        
        public virtual ICollection<Veiculo> Veiculos { get; set; }
        /// <summary>
        /// Cargas da Filial
        /// </summary>
        public virtual ICollection<TipoNotificacao> TiposNotificacao { get; set; }
        public virtual ICollection<NotificacaoPush> NotificacoesPush { get; set; }
        public virtual ICollection<Documento> Documentos { get; set; }
        public virtual ICollection<Motivo> MotivosCredenciamento { get; set; }
        public virtual ICollection<PagamentoConfiguracao>  PagamentoConfiguracoes { get; set; }
        public virtual ICollection<Viagem> Viagens { get; set; }
        public virtual ICollection<Contrato> Contrato { get; set; }
        public virtual ICollection<UsuarioFilial> UsuarioFilial { get; set; }

        
        #endregion

        #region Parâmetro Validade Ordem Carregamento, AtivaValidacaoFilialGr, MostraFilialOc

        public int? ValidadeOC { get; set; } //<-- Parâmetro Validade Ordem Carregamento dia 06-11-2018

        public bool? AtivaValidacaoFilialGr { get; set; } //<-- criado dia 19-11-2018 para atribuir o valor booleano para ativar a validação do statusGr da empresa

        public bool? MostraFilialOc { get; set; } = true; //<-- criado dia 21-11-2018 para atribuir o valor booleano para ativar a visualização da filial na Ordem de Carregamento

        public string InscricaoEstadual { get; set; } //<!-- criado dia 26-11-2018 para atribuir o valor string para a inscrição estadual da filial para imprimir na Ordem de Carregamento

        #endregion
    }
}

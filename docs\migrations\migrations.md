# Migrations

Toda manipulação de banco de dados deve ser realizada através de migrations, deste a sincronização da estrutura de dados das classes até os comandos customizados que um processo pode demandar.

**NUNCA** habilite o **AutomaticMigrations** e **AutomaticMigrationDataLoss**. Tudo deve estar explicito com add migrations. 

## Base de dados para migration

O Entity Framework 6 é fortemente acoplado ao banco de dados físico, diferentemente do EF Core que funciona totalmente baseado no código snapshot gerado após cada add migration.
No EF 6 o snapshot é armazenado na tabela `__MigrationHistory` e por isso ocorrem problemas ao compartilhar o banco para manipular migrations.

Cada desenvolvedor/feature deve ter um database **representando a versão base de produção** para ser apontado no Web.config. Para isso basta apenas apontar o server/database_name no arquivo e realizar o processo no Rider de **ATS.WS -> Entity Framework -> Update migration.**.

## Adicionar migration

> Certifique-se de ter a [base de dados para migration](#base-de-dados-para-migration) adequada para realizar o procedimento.

Para adicionar o migrations no Rider, clique em **ATS.WS -> Entity Framework -> Add migration.**

![migrations-add](migrations-add.png)

Posteriormente informe o nome do seu migration, seleciona o web.config para startup da aplicação e clique em OK.

![migrations-add-2](migrations-add-2.png)

Confira se o arquivo adicionado em `Web\ATS.Data.Context\Migrations` possui **exatamente e somente** suas alterações conhecidas.

Caso haver algo não relacionado a sua alteração, busque no histórico do Git o resposánvel e avise-o para que o mesmo gere o migration dele.

## Personalizar migration

Quando necessário comandos personalizados numa atualização, estes também devem ser versionados através de migrations,
execute o procedimento [Adicionar migration](#adicionar-migration) e inclua o comando como sql no local adequado.

Também é possível adicionar instruções adicionais não disponíveis nativamente no mapeamento `EntityTypeConfiguration`. 
No exemplo abaixo foi adicionado a clásula **defaultValueSql** para gerar um campo not null recurso de default pelo banco dados.
Com isso mantendo o dominio da aplicação rico e o banco de dados devidamente compatibilizado.

![migrations-add-3.png](migrations-add-3.png)

## Atualização de base de dados

**O PROCESSO DE ATUALIZAÇÃO DE BASE DE DADOS É REALIZADO AUTOMATICAMENTE**.

No [StartUp](../../Web/ATS.WS/StartUp.cs) da aplicação é executado a atualização através da classe [AtsContextMigrator](../../Web/ATS.Data.Context/AtsContextMigrator.cs),
onde são aplicados todos os migrations pendentes na base de dados.

Os logs de migrations são gerados no arquivo ***./Logs/Migrations.log***.

### Simulando atualização manual

Apenas para caracter de documentação, seguem passos para realizar uma atualização de base de dados manualmente.

1. Primeiro é necessário identificar o último migration aplicado no ambiente através da query:

```sql
select top 1 * from [__MigrationHistory] mh order by mh.MigrationId desc
```

2. Acesso o menu de atualização de base de dados (Entity Framework -> Update Database)

![migrations-script.png](migrations-script.png)

3. Informe no campo ***Source migration*** o ponto que se encontra o banco de dados (resultado da query no passo 1) e 
no campo **Target migration** o ultimo migration versionado no código fonte para geração dos scripts.
Também selecione a opção ***Script***.

![migrations-script-2.png](migrations-script-2.png)

4. Pressione OK, será realizado o build do projeto e logo em seguida será aberta uma página com o script de atualização (Funcionalidade do Rider, em outras IDEs poderá ficar apenas no console o script).
Execute o script inteiro no banco de dados (Principalmente a ultima parte para inserir na migration history!).

## Solução de problemas

> Unable to generate an explicit migration because the following explicit migrations are pending: [202302100124581_AlterProprietario_AddFormaPagamentoFrete]. 
> Apply the pending explicit migrations before attempting to generate a new explicit migration.

O banco de dados não está sincronizado com o código fonte, aplique o migration pendente.

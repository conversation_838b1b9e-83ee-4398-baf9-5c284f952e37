﻿using System.Net.Http;

// ReSharper disable once CheckNamespace
namespace SistemaInfo.MicroServices.Rest.Ciot.ApiClient
{
    public partial class AnttClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }

    public partial class ConsultasClient
    {
        partial void PrepareRequest(HttpClient client, HttpRequestMessage request, string url)
        {
            LogPublishRequest(client, request, url, BaseUrl);
        }

        partial void ProcessResponse(HttpClient client, HttpResponseMessage response)
        {
            LogPublishResponse(client, response);
        }
    }
}

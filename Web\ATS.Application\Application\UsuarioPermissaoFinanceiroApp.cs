﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Enum;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service;
using ATS.Domain.Validation;

namespace ATS.Application.Application
{
    public class UsuarioPermissaoFinanceiroApp : AppBase, IUsuarioPermissaoFinanceiroApp
    {
        private readonly IUsuarioPermissaoFinanceiroService _usuarioPermissaoFinanceiroService;

        public UsuarioPermissaoFinanceiroApp(IUsuarioPermissaoFinanceiroService usuarioPermissaoFinanceiroService)
        {
            _usuarioPermissaoFinanceiroService = usuarioPermissaoFinanceiroService;
        }

        public ValidationResult Integrar(int idUsuario, EBloqueioFinanceiroTipo idBloqueioGestorTipo,bool bloqueioFinanceiro)
        {
            return _usuarioPermissaoFinanceiroService.Integrar(idUsuario, idBloqueioGestorTipo, bloqueioFinanceiro);
        }

        public UsuarioPermissaoFinanceiro GetParametroPermissaoFinanceiro(int idUsuario, EBloqueioFinanceiroTipo idBloqueioGestorTipo)
        {
            return _usuarioPermissaoFinanceiroService.GetParametroPermissaoFinanceiro(idUsuario, idBloqueioGestorTipo);
        }

        public bool PossuiPermissao(int idUsuario, EBloqueioFinanceiroTipo bloqueioFinanceiroTipo)
        {
            return _usuarioPermissaoFinanceiroService.PossuiPermissao(idUsuario, bloqueioFinanceiroTipo);
        }
    }
}
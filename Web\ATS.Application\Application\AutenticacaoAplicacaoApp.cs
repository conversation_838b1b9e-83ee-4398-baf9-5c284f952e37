﻿using ATS.Application.Application.Common;
using ATS.Application.Interface;
using ATS.Domain.Entities;
using ATS.Domain.Service;
using System.Linq;
using ATS.Domain.Interface.Service;

namespace ATS.Application.Application
{
    public class AutenticacaoAplicacaoApp : AppBase, IAutenticacaoAplicacaoApp
    {
        private readonly IAutenticacaoAplicacaoService _autenticacaoAplicacaoService;

        public AutenticacaoAplicacaoApp(IAutenticacaoAplicacaoService autenticacaoAplicacaoService)
        {
            _autenticacaoAplicacaoService = autenticacaoAplicacaoService;
        }

        public bool AcessoConcedido(string cnpjAplicacao, string token)
        {
            return _autenticacaoAplicacaoService.AcessoConcedido(cnpjAplicacao, token);
        }

        public IQueryable<AutenticacaoAplicacao> GetAutenticacaoAplicacaoPorCnpjAplicacao(string cnpjAplicacao, string token)
        {
            return _autenticacaoAplicacaoService.GetAutenticacaoAplicacaoPorCNPJAplicacao(cnpjAplicacao, token);
        }

        public AutenticacaoAplicacao Get(string cnpjAplicacao)
        {
            return _autenticacaoAplicacaoService.Get(cnpjAplicacao);
        }

        public IQueryable<AutenticacaoAplicacao> GetPorIdEmpresa(int idEmpresa)
        {
            return _autenticacaoAplicacaoService.GetPorIdEmpresa(idEmpresa);
        }
    }
}

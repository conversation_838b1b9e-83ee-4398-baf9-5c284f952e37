using System;
using System.Collections.Generic;

namespace ATS.Domain.DTO
{
    public class BloqueioEventosExpiradosResponseDTO
    {
        public List<BloqueioEventosExpiradoDTO> Eventos { get; set; } = new List<BloqueioEventosExpiradoDTO>();
    }
    
    public class BloqueioEventosExpiradoDTO
    {
        public int IdEmpresa { get; set; }
        public int IdViagemEvento { get; set; }
        public string TipoEvento { get; set; }
        public DateTime? DataEmissaoViagem { get; set; }
        public decimal ValorPagamento { get; set; }
    }
}
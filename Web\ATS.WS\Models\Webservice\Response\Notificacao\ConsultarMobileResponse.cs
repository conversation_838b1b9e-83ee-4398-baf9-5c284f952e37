﻿using System;

namespace ATS.WS.Models.Webservice.Response.Notificacao
{
    public class ConsultarMobileResponse
    {
        public int IdNotificacao { get; set; }
        public int IdUsuario { get; set; }
        public int Tipo { get; set; }
        public string Conteudo { get; set; }
        public DateTime DataHoraEnvio { get; set; }
        public bool Recebida { get; set; } = false;
        public bool? Lida { get; set; } = false;
        public string Link { get; set; }
        public int IdTipoNotificacao { get; set; }
    }
}
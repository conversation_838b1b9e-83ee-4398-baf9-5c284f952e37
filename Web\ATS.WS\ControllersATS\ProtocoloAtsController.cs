﻿using ATS.Application.Application;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.CrossCutting.IoC.Utils;
using ATS.Domain.DTO;
using ATS.Domain.Entities;
using ATS.Domain.Grid;
using ATS.Domain.Helpers;
using ATS.Domain.Models;
using ATS.Domain.Service;
using ATS.WS.Attributes;
using ATS.WS.ControllersATS.Default;
using ATS.WS.Models.Common;
using ATS.WS.Models.Common.Request;
using ATS.WS.Services;
using AutoMapper;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Enum;

namespace ATS.WS.ControllersATS
{
    public class ProtocoloAtsController : DefaultController
    {
        public readonly IProtocoloApp _appLayer;
        private readonly IUserIdentity _userIdentity;
        private readonly IViagemApp _viagemApp;
        private readonly IEstabelecimentoBaseService _estabelecimentoBaseService;
        private readonly IPagamentoFreteApp _pagamentoFreteApp;
        private readonly IUsuarioApp _usuarioApp;
        private readonly IProtocoloApp _protocoloApp;
        private readonly IEstabelecimentoApp _estabelecimentoApp;
        private readonly IPagamentoFreteService _pagamentoFreteService;
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly SrvProtocolo _srvProtocolo;

        public ProtocoloAtsController(IUserIdentity userIdentity, IViagemApp viagemApp, IEstabelecimentoBaseService estabelecimentoBaseService, IPagamentoFreteApp pagamentoFreteApp,
            IUsuarioApp usuarioApp, IProtocoloApp appLayer, IProtocoloApp protocoloApp, IEstabelecimentoApp estabelecimentoApp, IPagamentoFreteService pagamentoFreteService,
            IUsuarioRepository usuarioRepository, SrvProtocolo srvProtocolo)
        {
            _userIdentity = userIdentity;
            _viagemApp = viagemApp;
            _estabelecimentoBaseService = estabelecimentoBaseService;
            _pagamentoFreteApp = pagamentoFreteApp;
            _usuarioApp = usuarioApp;
            _appLayer = appLayer;
            _protocoloApp = protocoloApp;
            _estabelecimentoApp = estabelecimentoApp;
            _pagamentoFreteService = pagamentoFreteService;
            _usuarioRepository = usuarioRepository;
            _srvProtocolo = srvProtocolo;
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult CalcularValores(string token, decimal? pesoChegada, int? numeroSacas)
        {
            try
            {
                if (pesoChegada.HasValue && pesoChegada < 0)
                    throw new Exception("Não é possível informar valor negativo para o peso de chegada.");
                if (numeroSacas.HasValue && numeroSacas < 0)
                    throw new Exception("Não é possível informar um valor negativo para a quantidade de sacas.");

                if (pesoChegada == null && numeroSacas == null)
                    throw new Exception("Informe um peso ou número de sacas válido para continuar!");

                if (!pesoChegada.HasValue && !numeroSacas.HasValue)
                    return ResponderSucesso(new PagamentoFreteEventoModel());
                return ResponderSucesso(_pagamentoFreteApp.CalcularValoresProtocolo(token, pesoChegada, numeroSacas));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPorToken(string token, int? idProtocolo)
        {
            try
            {
                return ResponderSucesso(_protocoloApp.ConsultarPorToken(token, (Usuario) _userIdentity, idProtocolo));
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarPagamentosPorProtocolo(int IdProtocolo)
        {
            try
            {

                var dados = _protocoloApp.ConsultarPagamentosPorProtocolo(IdProtocolo);

                return ResponderSucesso(dados);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }


        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object ConsultarEventosOriginalGrid(int IdProtocolo, int take, int page, OrderFilters order, List<QueryFilters> filters)
        {
            try
            {
                var eventos = _protocoloApp.ConsultarEventosOriginal(IdProtocolo, take, page, order, filters);


                return ResponderSucesso(eventos);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGrid(int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, int? idEstabelecimento = 0)
        {
            try
            {
                // Return an empty value when the company comes null,
                //It happens when the users open the screen for the first time.
                if (!idEmpresa.HasValue && idEmpresa == null)
                    return ResponderSucesso(new { });

                if (!idEstabelecimento.HasValue || idEstabelecimento < 1 &&
                    _userIdentity.Perfil == (int)Domain.Enum.EPerfil.Estabelecimento)
                {

                    idEstabelecimento = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario)?.FirstOrDefault()
                        ?.IdEstabelecimento;
                }

                var dataGrid = _appLayer.ConsultarGrid(idEstabelecimento, idEmpresa, take, page, order, filters);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Fatal(e, "Erro ao consultar protocolos");
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGrid(string json)
        {
            var request = JsonConvert.DeserializeObject<RelatorioProtocoloGridDTO>(json, new JsonSerializerSettings {DateTimeZoneHandling = DateTimeZoneHandling.Local});
            var report = _srvProtocolo.GerarRelatorioGrid(request.IdEstabelecimento, request.IdEmpresa, request.Order, request.Filters, request.Extensao);
            
            var mimeType = string.Empty;

            switch (request.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de protocolo.{request.Extensao}");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioGridRecebimentoProtocolo(string json)
        {
            var request = JsonConvert.DeserializeObject<RelatorioRecebimentoProtocoloDTO>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            var report = _srvProtocolo.GerarRelatorioGridRecebimentoProtocolo(request.IdEmpresa, request.Order, request.Filters, request.Extensao);

            var mimeType = string.Empty;

            switch (request.Extensao)
            {
                case "pdf":
                    mimeType = ConstantesUtils.PdfMimeType;
                    break;
                case "xlsx":
                    mimeType = ConstantesUtils.ExcelMimeType;
                    break;
            }

            return File(report, mimeType, $"Relatório de recebimento de protocolo.{request.Extensao}");
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult EstabelecimentoHasAssociacoes(int idEstabelecimento)
        {
            try
            {
                if (idEstabelecimento < 1)
                    throw new Exception("É necessário informar um estabelecimento válido!");

                if (_userIdentity.Perfil == (int)Domain.Enum.EPerfil.Estabelecimento)
                {
                    // retorna se o id estabelecimento informado tem alguma associação, e não é associação!
                    var idEstab = _estabelecimentoApp.GetByBase(idEstabelecimento);
                    if (!idEstab.HasValue)
                        throw new Exception("Nenhum estabelecimento encontrado para o usuário conectado!");

                    var estab = _estabelecimentoApp.Get(idEstab.Value);
                    if (estab == null)
                        throw new Exception("Nenhum estabelecimento encontrado para o usuário conectado!");

                    return ResponderSucesso(new
                    {
                        estabNaoTemAssocENaoEAssoc = estab.Associacao == false && !estab.EstabelecimentoAssociacoesEstabelecimento.Any()
                    });
                }

                throw new Exception("Método disponível apenas para usuários do tipo estabelecimento!");
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object AdicionarProtocoloAssociacao(List<int> Eventos, int IdEstabelecimento)
        {
            try
            {
                var resposta = _srvProtocolo.ProcessarAdicaoProtocoloAssociacao(Eventos, IdEstabelecimento);

                return ResponderSucesso("Protocolo adicionado com sucesso!", resposta);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object IsAssociacao()
        {
            try
            {
                var idEstabelecimentoUsuario = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario)?.FirstOrDefault()
                    ?.IdEstabelecimento;
                var estabelecimento = new EstabelecimentoBaseApp(_estabelecimentoBaseService).Get(idEstabelecimentoUsuario ?? 0);


                return ResponderSucesso(new { estabelecimento.Associacao });
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }


        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object AdicionarProtocolo(ProtocoloModel model)
        {
            try
            {
                var ret = _srvProtocolo.Add(model, _userIdentity.IdUsuario);

                if (!ret.Key.IsValid)
                    throw new Exception(ret.Key.Errors.FirstOrDefault()?.Message);

                return ResponderSucesso("Protocolo adicionado com sucesso!", ret.Value);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object AtualizarProtocolo(ProtocoloModel model)
        {
            try
            {
                var ret = _srvProtocolo.Update(model);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public object ConsultarDocumentosProtocolo(int IdEmpresa, int? IdProtocolo)
        {
            try
            {
                var ret = _protocoloApp.ConsultarDocumentoProtocolo(IdEmpresa, IdProtocolo);

                return ResponderSucesso(ret);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult GerarRecibo(string token, int idViagemEvento, string usuario)
        {
            try
            {
                Response.AddHeader("Content-Disposition", $"inline; filename=Recibo-{token}.pdf");
                return File(_pagamentoFreteService.GerarReciboProtocolo(token, idViagemEvento, usuario), "application/pdf");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpNotFoundResult(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public ActionResult RelatorioAgrupados()
        {
            try
            {
                Response.AddHeader("Content-Disposition", $"inline; filename=Protocolos-agrupados.pdf");
                return File(_pagamentoFreteService.GerarReciboProtocolo("", 1, ""), "application/pdf");
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return new HttpNotFoundResult(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult RealizarDesconto(int idProtocoloEvento, decimal? valorDesconto, decimal? pesoChegada, int? idMotivoDesconto, string observacaoDesconto, string tokenAnexoDesconto)
        {
            try
            {
                if (!valorDesconto.HasValue)
                    throw new Exception("Informe o valor do desconto");

                var validationResult = _protocoloApp.RealizarDesconto(idProtocoloEvento, valorDesconto, pesoChegada, idMotivoDesconto, observacaoDesconto, tokenAnexoDesconto);
                return validationResult.IsValid ? ResponderSucesso("Desconto realizado com sucesso!") : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult RemoverDesconto(int idProtocoloEvento)
        {
            try
            {
                if (idProtocoloEvento == 0)
                    return ResponderErro("Protocolo evento deve ser informado");

                var resultado = _protocoloApp.RemoverDesconto(idProtocoloEvento);

                return resultado.IsValid
                    ? ResponderSucesso("Desconto removido com sucesso.")
                    : ResponderErro(resultado.Errors.FirstOrDefault()?.Message);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetQRCodeProtocolo(int idEmpresa)
        {
            try
            {
                var cpfUsuario = _userIdentity.CpfCnpj;
                var tokenHorario = new DateTimeHelper().ConvertToUnix(DateTime.UtcNow).ToString(CultureInfo.InvariantCulture).Replace(",", "").Replace(".", "");

                object qrCode = _srvProtocolo.GetQRCodeProtocolo(idEmpresa, cpfUsuario, tokenHorario);

                return ResponderSucesso(qrCode);
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult VincularPagamentoProtocolo(int idProtocolo, string token)
        {
            try
            {
                var validationResult = _srvProtocolo.VincularPagamentoProtocolo(idProtocolo, token, _userIdentity.IdUsuario);

                return validationResult.IsValid
                    ? ResponderSucesso("Pagamento vinculado com sucesso")
                    : ResponderErro(validationResult.ToString());
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GerarProtocoloPorViagemEvento(string token, int? idEstabelecimento)
        {
            try
            {
                if (!idEstabelecimento.HasValue)
                    return ResponderErro("É necessário informar um estabelecimento para gerar um novo protocolo");

                var idProtocolo = _protocoloApp.GerarProtocoloPorViagemEvento(token, idEstabelecimento.Value);

                return ResponderSucesso($"Protocolo gerado com sucesso, redirecionando para o protocolo {idProtocolo}", new { idProtocolo });
            }
            catch (Exception e)
            {
                return ResponderErro(e);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult GetViagensByCte(string numero)
        {
            try
            {
                var viagens = _viagemApp.GetByCte(numero);
                var viagemsModel = Mapper.Map<List<Viagem>, List<ViagemModel>>(viagens);

                return ResponderSucesso(viagemsModel);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarGridAssociacao(int take, int page, OrderFilters order, List<QueryFilters> filters, int? idEmpresa, int? idEstabelecimento = 0, int? idAssociacao = 0)
        {
            try
            {
                if (!idEmpresa.HasValue)
                    return ResponderAtencao(true, "Por favor, informe uma empresa para consultar.");
                
                var idEstabelecimentoUsuario = _usuarioApp.GetEstabelecimentos(_userIdentity.IdUsuario)?.FirstOrDefault()
                    ?.IdEstabelecimento;
                var dataGrid = _appLayer.ConsultarGridAssociacao(idEstabelecimento, take, page, order, filters, idEmpresa, idEstabelecimentoUsuario);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult ConsultarProtocoloEvento(int IdProtocolo)
        {
            try
            {
                var dataGrid = _appLayer.ConsultarProtocoloEvento(IdProtocolo);

                return ResponderSucesso(dataGrid);
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpGet]
        [Autorizar]
        [Expor(EApi.Portal)]
        public JsonResult VerificarPagamentosSemProtocolo(int idEstabelecimentoBase)
        {
            try
            {
                var response = _appLayer.VerificarPagamentosSemProtocolo(idEstabelecimentoBase);
                return ResponderSucesso(string.Empty, new {existePagamentosSemProtocolo = response});
            }
            catch (Exception e)
            {
                return ResponderErro(e.Message);
            }
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioEtiquetas(string json, int? idEmpresaLogada, int? idUsuarioLogado)
        {
            var dados = JsonConvert.DeserializeObject<List<int>>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });
            int idEmpresa = 0;
            if(idEmpresaLogada == null)
            {
                var idEmpresaUsuarioLogado = _usuarioRepository.GetIdEmpresa(idUsuarioLogado ?? 0);
                if (idEmpresaUsuarioLogado.HasValue && idEmpresaUsuarioLogado > 0)
                    idEmpresa = idEmpresaUsuarioLogado ?? 0;
            }
            else
                idEmpresa = idEmpresaLogada ?? 0;


            var report = _protocoloApp.GerarRelatorioEtiquetas(dados, GetLogo(idEmpresa));

            Response.AddHeader("Content-Disposition", "inline; filename=Etiquetas protocolo.pdf");
            return File(report, "application/pdf");
        }

        [HttpPost]
        [Autorizar]
        [Expor(EApi.Portal)]
        //[IgnoreAuthSessionValidation]
        public FileResult GerarRelatorioCapa(string json)
        {
            var dados = JsonConvert.DeserializeObject<RelatorioProtocoloCapaModel>(json, new JsonSerializerSettings { DateTimeZoneHandling = DateTimeZoneHandling.Local });

            var report = _protocoloApp.GerarRelatorioCapa(dados.IdsProtocolos, GetLogo(dados.IdEmpresa ?? 0));

            Response.AddHeader("Content-Disposition", "inline; filename=Capas protocolo.pdf");
            return File(report, "application/pdf");
    }
}
}

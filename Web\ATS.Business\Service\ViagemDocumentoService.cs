﻿using System;
using ATS.CrossCutting.IoC;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;
using ATS.Domain.Interface.Service;
using ATS.Domain.Service.Common;
using ATS.Domain.Validation;

namespace ATS.Domain.Service
{
    public class ViagemDocumentoService : ServiceBase, IViagemDocumentoService
    {
        private readonly IViagemDocumentoRepository _viagemDocumentoRepository;

        public ViagemDocumentoService(IViagemDocumentoRepository viagemDocumentoRepository)
        {
            _viagemDocumentoRepository = viagemDocumentoRepository;
        }

        public ViagemDocumento Get(int id)
        {
            return _viagemDocumentoRepository.Get(id);
        }

        public ValidationResult Update(ViagemDocumento viagemDocumento)
        {
            try
            {
                _viagemDocumentoRepository.Update(viagemDocumento);
                return new ValidationResult();
            }
            catch (Exception e)
            {
                return new ValidationResult().Add(e.Message);
            }
        }
    }
}

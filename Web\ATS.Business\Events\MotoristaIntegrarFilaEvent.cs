﻿using System;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Extratta.ImportacaoDadosConsumer.Events
{
    public class MotoristaIntegrarFilaEvent
    {
        public string Nome { get; set; }
        public string Rg { get; set; }
        public string RgOrgaoExpedidor { get; set; }
        public string Cpf { get; set; }
        public string Sexo { get; set; }
        public string Cnh { get; set; }
        public string CnhCategoria { get; set; }
        public DateTime? ValidadeCnh { get; set; }
        public string Celular { get; set; }
        public ETipoContratoFilaEvent TipoContrato { get; set; }
        public string Email { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string Cep { get; set; }
        public string Endereco { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public int IbgeCidade { get; set; }
        public int IbgeEstado { get; set; }
        public string CNPJEmpresa { get; set; }
        public string Token { get; set; }
    }

    public enum ETipoContratoFilaEvent
    {
        [EnumMember, Description("Frota")]
        Frota = 1,

        [EnumMember, Description("Cooperado")]
        Cooperado = 2,

        [EnumMember, Description("Agregado")]
        Agregado = 3,

        [EnumMember, Description("Terceiro")]
        Terceiro = 4
    }
}
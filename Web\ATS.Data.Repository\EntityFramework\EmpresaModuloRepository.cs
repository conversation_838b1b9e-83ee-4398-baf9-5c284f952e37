﻿using System.Data.Entity;
using System.Linq;
using ATS.Data.Context;
using ATS.Data.Repository.EntityFramework.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Database;

namespace ATS.Data.Repository.EntityFramework
{
    public class EmpresaModuloRepository : Repository<EmpresaModulo>, IEmpresaModuloRepository
    {
        public EmpresaModuloRepository(AtsContext context) : base(context)
        {
        }
        
        /// <summary>
        /// Método utilizado por listar ModuloEmpresa
        /// </summary>
        /// <param name="idEmpresa">id de Empresa</param>
        /// <returns>IQueryable de ModuloEmpresa</returns>
        public IQueryable<EmpresaModulo> ListarModuloPorIdEmpresa(int? idEmpresa)
        {
            return from moduloTransp in All()
                   .Include(x => x.Modulo)
                   .Include(x => x.Empresa)
                   .Include(x => x.Modulo.Menus)
                   .Where(x => x.Modulo.Ativo)

                   where moduloTransp.IdEmpresa == idEmpresa
                   select moduloTransp;
        }
    }
}
﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ContratoMap : EntityTypeConfiguration<Contrato>
    {
        public ContratoMap()
        {
            ToTable("CONTRATO");

            HasKey(t => t.IdContrato);

            Property(t => t.IdContrato)
               .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.NumContratoErp)
                .IsRequired()
                .HasMaxLength(10);

            HasOptional(t => t.Filial)
                .WithMany(t => t.Contrato)
                .HasForeignKey(d => d.IdFilial);

            HasOptional(t => t.Cliente)
                .WithMany(t => t.Contrato)
                .HasForeignKey(d => d.IdClientePagador);
        }
    }
}
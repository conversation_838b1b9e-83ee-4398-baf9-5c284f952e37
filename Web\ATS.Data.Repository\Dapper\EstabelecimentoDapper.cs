using System;
using System.Collections.Generic;
using System.Linq;
using ATS.Data.Repository.Dapper.Common;
using ATS.Domain.Entities;
using ATS.Domain.Interface.Dapper;
using ATS.Domain.Models;
using Dapper;
using NLog;

namespace ATS.Data.Repository.Dapper
{
    public class EstabelecimentoDapper : DapperFactory<ConsultaEstabelecimentoModelResponse>, IEstabelecimentoDapper
    {
        public IEnumerable<ConsultaEstabelecimentoModelResponse> ConsultarEstabelecimentos(int idEmpresa, string cnpjEstabelecimento)
        {
            try
            {
                var clausulaWhere = string.Empty;

                if (!string.IsNullOrEmpty(cnpjEstabelecimento))
                    clausulaWhere = $@" AND E.cnpjestabelecimento = '{cnpjEstabelecimento}'";

                using (var connection = new DapperContext().GetConnection)
                {
                    var queryEstabelecimento = $@"SELECT E.IDESTABELECIMENTO as IdEstabelecimento,
                                           TE.descricao as TipoEstabelecimento,
                                           TE.idicone as Icone,
                                           IC.TOKENICONE as TokenIcone,
                                           E.descricao as Estabelecimento,
                                           P.nome as Pais,
                                           P.bacen as CodigoBACENPais,
                                           UF.nome as Estado,
                                           UF.ibge as CodigoIBGEEstado,
                                           C.nome as Cidade,
                                           C.ibge as CodigoIBGECidade,
                                           E.bairro as Bairro,
                                           E.logradouro as Logradouro,
                                           E.cep as CEP,
                                           E.numero as Numero,
                                           E.complemento as Complemento,
                                           E.latitude as Latitude,
                                           E.longitude as Longitude,
                                           E.ativo as Ativo,
                                           E.cnpjestabelecimento as CNPJEstabelecimento,
                                           E.telefone as Telefone,
                                           E.email as Email
                                FROM ESTABELECIMENTO E
                                       INNER JOIN TIPO_ESTABELECIMENTO TE ON TE.idtipoestabelecimento = E.idtipoestabelecimento
                                       INNER JOIN CIDADE C ON C.idcidade = E.idcidade
                                       INNER JOIN ESTADO UF ON UF.idestado = C.idestado
                                       INNER JOIN PAIS P ON P.idpais = UF.idpais
                                       LEFT JOIN ICONE IC ON IC.IDICONE = TE.IDICONE
                                WHERE E.idempresa = {idEmpresa} {clausulaWhere}";
                    
                    var retornoQueryEstabelecimento = connection.Query<ConsultaEstabelecimentoModelResponse>(queryEstabelecimento).ToList();
                    var ids = retornoQueryEstabelecimento.Select(o => o.IdEstabelecimento).ToList();
                    
                    if(!ids.Any())
                        return retornoQueryEstabelecimento;

                    var queryProdutos = $@"SELECT EP.idproduto,
                                                  EP.descricao, 
                                                  EP.unidademedida, 
                                                  EP.precopromocional, 
                                                  EP.precounitario,
                                                  EP.idestabelecimento
                                           FROM ESTABELECIMENTO_PRODUTO EP WHERE idestabelecimento IN ({string.Join(", ", ids)})";

                    var retornoQueryProdutos =
                        connection.Query<EstabelecimentoProduto>(queryProdutos).AsQueryable();

                    foreach (var estabelecimento in retornoQueryEstabelecimento)
                    {
                        estabelecimento.Produtos = new List<EstabelecimentoProdutoModelResponse>();    
                        var produtos = retornoQueryProdutos.Where(o =>
                            o.IdEstabelecimento == estabelecimento.IdEstabelecimento);

                        foreach (var produto in produtos)
                            estabelecimento.Produtos.Add(new EstabelecimentoProdutoModelResponse {Produto = produto.Descricao, IdProduto = produto.IdProduto, PrecoPromocional = produto.PrecoPromocional, PrecoUnitario = produto.PrecoUnitario, UnidadeMedida = produto.UnidadeMedida});
                    }
                    
                    return retornoQueryEstabelecimento;
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, e.Message);
                return null;
            }
                
        }
    }
}
﻿using ATS.Application.Application;
using ATS.Domain.Entities;
using ATS.WS.Models.Common;
using ATS.WS.Models.Mobile.Common;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ATS.Application.Interface;
using ATS.WS.Helpers;
using ATS.Domain.Validation;
using Sistema.Framework.Util.Helper;
using ATS.Domain.Enum;
using ATS.WS.Models.Webservice.Request.TipoCarreta;

namespace ATS.WS.Services
{
    public class SrvTipoCarreta : SrvBase
    {
        private readonly ITipoCarretaApp _tipoCarretaApp;
        private readonly IEmpresaApp _empresaApp;

        public SrvTipoCarreta(ITipoCarretaApp tipoCarretaApp, IEmpresaApp empresaApp)
        {
            _tipoCarretaApp = tipoCarretaApp;
            _empresaApp = empresaApp;
        }

        public TipoCarreta Get(int idTipoCavalo)
        {
            try
            {
                return _tipoCarretaApp.Get(idTipoCavalo);
            }
            catch (Exception e)
            {
                _logger.Error(e);
                throw new Exception($"{nameof(Consultar)} >> {e.Message}");
            }
        }

        public Retorno<List<TipoCarretaModel>> Consultar()
        {
            try
            {
                return new Retorno<List<TipoCarretaModel>>(true, string.Empty,
                    Mapper.Map<List<TipoCarreta>, List<TipoCarretaModel>>(
                        _tipoCarretaApp.GetTodos().ToList()));
            }
            catch (Exception e)
            {
                throw new Exception($"{nameof(Consultar)} >> {e.Message}");
            }
        }

        public Retorno<object> Integrar(TipoCarretaModel @params)
        {
            var idEmpresaInformada = _empresaApp.GetIdPorCnpj(@params.CNPJEmpresa);

            if (string.IsNullOrWhiteSpace(@params.Nome))
                throw new Exception("Campo Nome é obrigatório para integração de um tipo de carreta.");

            if (@params.Categoria <= 0)
                throw new Exception("Campo Categoria é obrigatório.");

            if (!idEmpresaInformada.HasValue)
                throw new Exception("Não foi possível identificar a empresa. ");

            var tipoCarretaExistente = _tipoCarretaApp.GetPorDescricao(@params.Nome, idEmpresaInformada.Value);
            if (tipoCarretaExistente != null)
                return new Retorno<object>(true, tipoCarretaExistente.IdTipoCarreta);

            var tipoCarreta = new TipoCarreta
            {
                IdEmpresa = idEmpresaInformada,
                Categoria = @params.Categoria,
                DataHoraUltimaAtualizacao = DateTime.Now,
                Capacidade = @params.Capacidade,
                Nome = @params.Nome,
            };

            var result = _tipoCarretaApp.Add(tipoCarreta);
            if (!result.IsValid)
                throw new Exception(result.ToFormatedMessage());

            return new Retorno<object>(true, tipoCarreta.IdTipoCarreta);
        }

        public ValidationResult Editar(CadastroTipoCarretaRequestModel @params, Usuario usuarioLogado)
        {
            try
            {
                var validationResult = new ValidationResult();

                if (usuarioLogado.Perfil != EPerfil.Administrador)
                    validationResult.Add("Usuário não possui permissão para editar este registro.");

                var tipoCarreta = _tipoCarretaApp.Get(@params.IdTipoCarreta);
                if (tipoCarreta == null)
                    validationResult.Add("Não foi possível localizar o tipo de carreta.");

                if (!validationResult.IsValid)
                    return validationResult;

                ReflectionHelper.CopyProperties(@params, tipoCarreta);
                validationResult = _tipoCarretaApp.Update(tipoCarreta);

                return validationResult;
            }
            catch (Exception e)
            {
                _logger.Error(e);
                return new ValidationResult().Add(e.Message);
            }
        }

        internal object ConsultarSemEmpresa()
        {
            return _tipoCarretaApp.ConsultarSemEmpresa();
        }
    }
}
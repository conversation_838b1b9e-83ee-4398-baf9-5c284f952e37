﻿using System;
using System.Web;
using System.Web.Mvc;
using ATS.Application.Interface;
using ATS.CrossCutting.IoC.Interfaces;
using ATS.Domain.Enum;
using ATS.WS.Controllers.Base;
using ATS.WS.Services;

namespace ATS.WS.Attributes
{
    [AttributeUsage(AttributeTargets.Method)]
    public class AutorizarMoveMaisWebhook : ActionFilterAttribute
    {
        public IUserIdentity UserIdentity { get; set; }
        private EValidacaoToken Validation { get; set; } = EValidacaoToken.Todos;
        public AutorizarMoveMaisWebhook()
        {
        }
        
        public AutorizarMoveMaisWebhook(EValidacaoToken validation)
        {
            Validation = validation;
        }
        
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            BaseController controller = (BaseController)filterContext.Controller;
            SrvIP srvIP = DependencyResolver.Current.GetService<SrvIP>();

            if (Validation == EValidacaoToken.Todos || Validation == EValidacaoToken.ApenasIP)
            {
                var ip = controller.GetRealIp();
                if (!srvIP.AnalisarIP(null,ip,EOrigemRequisicao.Parceiro))
                {
                    filterContext.Result = new HttpUnauthorizedResult("IP não autorizado!");
                    return;
                }
            }

            if (Validation == EValidacaoToken.Todos || Validation == EValidacaoToken.ApenasToken)
            {
                ITagExtrattaApp extrattaApp = DependencyResolver.Current.GetService<ITagExtrattaApp>();
                var token = filterContext.HttpContext.Request.Headers["Authorization"];
            
                if (token != extrattaApp.GetWebhookToken())
                    filterContext.Result = new HttpUnauthorizedResult();
            }

            UserIdentity.Perfil = 3;
        }
    }
}
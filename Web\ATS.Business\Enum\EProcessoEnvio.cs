﻿using System.ComponentModel;
using System.Runtime.Serialization;

namespace ATS.Domain.Enum
{
    public enum EProcessoEnvio
    {
        [EnumMember, Description("Solicitação de código de segurança")]
        SolicitacaoCodigoSeguranca = 1,
        [EnumMember, Description("Indicação TMov")]
        IndicacaoTmov = 2,
        [EnumMember, Description("Indicação WEB")]
        IndicacaoWeb = 3,
        [EnumMember, Description("Serviço de inatividade de uso do TMov")]
        ServicoInatividadeTmov = 4,
        [EnumMember, Description("Geração da Ordem de Carregamento")]
        GeracaoOrdemCarregamento = 5,
        [EnumMember, Description("Cancelamento da Ordem de Carregamento")]
        CancelamentoOrdemCarregamento = 6,
        [EnumMember, Description("Atualização de status GR")]
        AtualizacaoStatusGr = 7,
        [EnumMember, Description("Pré-Ordem de Carregamento")]
        PreOrdemCarregamento = 8,
        [EnumMember, Description("Local não mapeado")]
        LocalNaoMapeado = 9,
        [EnumMember, Description("Usuários que nunca usaram App")]
        UsuarioNuncaUsaramApp = 10,
        [EnumMember, Description("Validação de Token de pagamento de frete")]
        ValidarTokenPagamento = 11
    }
}

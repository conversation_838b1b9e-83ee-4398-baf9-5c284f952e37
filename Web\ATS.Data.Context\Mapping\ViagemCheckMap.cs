﻿using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class ViagemCheckMap : EntityTypeConfiguration<ViagemCheck>
    {
        public ViagemCheckMap()
        {
            ToTable("VIAGEM_CHECK");

            HasKey(t => new {t.IdViagem, IdEmpresa = t.IdEmpresa, t.IdCheck});

            Property(t => t.IdViagem)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdEmpresa)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.None);

            Property(t => t.IdCheck)
                .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);

            Property(t => t.IdEmpresa)
                .IsRequired();

            Property(t => t.IdUsuario)
                .IsRequired();

            Property(t => t.DataHora)
                .IsRequired();

            Property(t => t.Status)
                .IsRequired();

            HasRequired(a => a.<PERSON>ua<PERSON>)
                .WithMany(b => b.ViagemChe<PERSON>)
                .HasForeignKey(c => c.IdUsuario);

            HasRequired(a => a.Viagem)
                .WithMany(b => b.ViagemChecks)
                .HasForeignKey(c => new { c.IdViagem, IdEmpresa = c.IdEmpresa});
        }
    }
}
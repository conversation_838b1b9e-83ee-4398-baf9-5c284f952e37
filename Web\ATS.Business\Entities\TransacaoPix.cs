﻿using ATS.Domain.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ATS.Data.Repository.External.Extratta.Biz.Models;
using Sistema.Framework.Util.Extension;

namespace ATS.Domain.Entities
{
    [TrackChanges]
    public class TransacaoPix
    {
        public int IdTransacaoPix { get; set; }

        public int IdViagemEvento { get; set; }

        public ETransacaoPixStatus IdTransacaoPixStatus { get; set; }

        public int IdEmpresa { get; set; }

        public decimal Valor { get; set; }

        public string DocumentoDestino { get; set; }

        /// <summary>
        /// Integracao, Portal
        /// </summary>
        public string OrigemPagamento { get; set; }

        /// <summary>
        /// Id da transação referente a essa, salva na tabela do MS Cartao
        /// </summary>
        public int? IdTransacaoMsCartao { get; set; }

        public int? IdUsuarioCadastro { get; set; }

        public int? IdUsuarioAtualizacao { get; set; }
        public string DocumentoUsuarioAuditCriacao { get; set; }
        public string DocumentoUsuarioAuditAtualizacao { get; set; }
        [SkipTracking]
        public string MensagemRetorno { get; set; }

        public DateTime DataCadastro { get; set; }

        public DateTime? DataAtualizacao { get; set; }

        public DateTime? DataConfirmacao { get; set; }

        #region Navegacao

        public virtual TransacaoPixStatus TransacaoPixStatus { get; set; }

        public virtual ViagemEvento ViagemEvento { get; set; }

        public virtual Usuario UsuarioCadastro { get; set; }

        public virtual Usuario UsuarioAtualizacao { get; set; }
        
        public virtual Empresa Empresa { get; set; }

        #endregion
    }
}
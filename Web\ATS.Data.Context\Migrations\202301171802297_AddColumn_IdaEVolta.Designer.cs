﻿// <auto-generated />
namespace ATS.Data.Context.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.2.0-61023")]
    public sealed partial class AddColumn_IdaEVolta : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddColumn_IdaEVolta));
        
        string IMigrationMetadata.Id
        {
            get { return "202301171802297_AddColumn_IdaEVolta"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}

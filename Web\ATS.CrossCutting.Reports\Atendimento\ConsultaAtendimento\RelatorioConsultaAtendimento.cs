using System.Collections.Generic;
using ATS.CrossCutting.IoC.Utils;
using Microsoft.Reporting.WebForms;

namespace ATS.CrossCutting.Reports.Atendimento.ConsultaAtendimento
{
    public class RelatorioConsultaAtendimento
    {
        public byte[] GetReport(string tipo, RelatorioConsultaAtendimentoDataType dadosRelatorio, string logo)
        {
            var localReport = new LocalReport();
            try
            {
                var tipoRelatorio = string.Empty;

                if (tipo == "pdf")
                    tipoRelatorio = ConstantesUtils.FormatoPdf;

                if (tipo == "xlsx")
                    tipoRelatorio = ConstantesUtils.FormatoExcelOpenXml;

                localReport.DataSources.Add(new ReportDataSource
                {
                    Value = dadosRelatorio.items,
                    Name = "DataSetAtendimento"
                });

                var path = ReportUtils.CreateLogo(logo);

                var parametros = new List<ReportParameter>();
                parametros.Add(new ReportParameter("Logo", "file:///" + path));

                localReport.EnableExternalImages = true;
                localReport.ReportEmbeddedResource = "ATS.CrossCutting.Reports.Atendimento.ConsultaAtendimento.RelatorioConsultaAtendimento.rdlc";
                localReport.SetParameters(parametros);
                localReport.Refresh();

                return localReport.Render(tipoRelatorio);
            }
            finally
            {
                localReport.ReleaseSandboxAppDomain();
                localReport = null;
            }
        }
    }
}
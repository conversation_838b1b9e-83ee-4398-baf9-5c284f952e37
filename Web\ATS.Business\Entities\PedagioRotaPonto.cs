using System;
using ATS.Domain.Entities.Base;

namespace ATS.Domain.Entities
{
    public class PedagioRotaPonto : Entidade
    {
        public int IdPedagioRota { get; set; }
        public int Sequencia { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public int? IdCidade { get; set; }
        
        public virtual PedagioRota PedagioRota { get; set; }
        public virtual Cidade Cidade { get; set; }
    }
}
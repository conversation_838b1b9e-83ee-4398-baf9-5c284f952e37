using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IEstabelecimentoBaseApp: IAppBase<EstabelecimentoBase>
    {
        ValidationResult Add(EstabelecimentoBase estabelecimento);
        IQueryable<EstabelecimentoBase> GetQueryByCnpj(string cnpj);
        IQueryable<EstabelecimentoBase> GetQueryById(int id);
        IQueryable<EstabelecimentoBase> GetEstabelecimentosJSL();
        Task<ValidationResult> AutorizarEstabelecimentoJSLEmpresas(int idEstabelecimento, int administradoraPlataforma);
        ValidationResult AutorizarEstabelecimentoJSLParaEmpresa(int idEstabelecimento, int idempresa, int administradoraPlataforma);
        ValidationResult IntegrarEstabelecimentoJSL(EstabelecimentoBase estabelecimentoBase);
        ValidationResult RemoverCombustiveisJSL(IList<int> idprodutobase);
    }
}
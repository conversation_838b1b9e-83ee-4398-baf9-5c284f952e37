﻿using System.Collections.Generic;
using ATS.Domain.Entities;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface IConjuntoApp
    {
        Conjunto Get(int id);
        List<Conjunto> GetByPlaca(string placa);
        string GetByCPFPlacaConjuntoMontado(string CPF);
        Conjunto GetConjuntoByPlacaCPF(string CPF, string placa);
        ValidationResult Add(Conjunto entity);
        ValidationResult MontarConjunto(string CPF, int IdConjunto);
        ValidationResult Update(Conjunto entity);
        ValidationResult AddGestorFrota(int IdConjunto, int IdGestorFrota);
        ValidationResult RemoveGestorFrota(List<int> IdsConjunto, int IdGestorFrota);
        ValidationResult AtulizarStatusConjunto(int IdConjunto, string CPF);
        ValidationResult AlterarTipoVeiculoConjunto(int IdConjunto, string placa, bool ehCavalo, int? tipoVeiculo);
        List<Conjunto> GetByIdGestor(int IdGestorFrota);
        List<Conjunto> GetByCPFCNPJGestor(List<string> CPFCNPJs);
        ConjuntoEmpresa GetConjuntoEmpresa( int IdConjunto);
        ValidationResult AtualizarTipos(int IdConjunto, Conjunto conjunto);
    }
}
﻿using System;
using ATS.Application.Interface.Common;
using ATS.Domain.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using ATS.Domain.Validation;

namespace ATS.Application.Interface
{
    public interface ITransacaoCartaoApp : IAppBase<TransacaoCartao>
    {
        TransacaoCartao Add(TransacaoCartao transacaoCartao);
        void AdicionarByViagemEvento(ViagemEvento viagemEvento);
        IList<TransacaoCartao> GetAllByIdEvento(int idevento);
        IList<TransacaoCartao> GetAllByIdViagem(int idViagem);
        IQueryable<TransacaoCartao> Find(Expression<Func<TransacaoCartao, bool>> predicate, bool @readonly = false);

        ValidationResult AtualizarStatusTransacaoPendenteReprocessada(int protocoloRequisicao,
            long protocoloProcessamento, DateTime dataTransacao, string mensagemStatus);

        ValidationResult AtualizarStatusTransacaoFalhaReprocessada(int protocoloRequisicao,
            long protocoloProcessamento, DateTime dataTransacao, string mensagemStatus);

        List<TransacaoCartao> BuscarEventosBaixados(int idViagem, List<int?> idsEventos);

        decimal GetValorTotalTransferenciasTEDUltimas24horas(string documentoPortador);
        decimal GetValorTotalTransferenciasCartaoUltimas24horas(string documentoPortador, bool applicativo = true);
    }
}

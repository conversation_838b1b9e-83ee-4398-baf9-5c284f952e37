﻿using ATS.Domain.Enum;
using ATS.Domain.Validation;

namespace ATS.Domain.DTO
{
    public class ReciboTransferenciaDto 
    {
        public string NomeOrigem { get; set; }
        public string CpfCnpjOrigem { get; set; }
        public string CartaoOrigem { get; set; }
        public string NomeDest<PERSON> { get; set; }
        public string CpfCnpjDestino { get; set; }
        public string CartaoDestino { get; set; }
        public string DataTransacao { get; set; }
        public string Descricao { get; set; }
        public string Informacoes { get; set; }
        public string ValorFormatado { get; set; }
        public string Instituicao { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string Dv { get; set; }
        
        public ValidationResult ValidarTransacao()
        {
            if (!(Descricao == "Transferência para Conta Bancária" || Descricao == "Transferência saldo entre contas"))
                return new ValidationResult().Add("Descrição de transação inválida", EFaultType.Error);
            
            if (string.IsNullOrWhiteSpace(CpfCnpjOrigem) || string.IsNullOrWhiteSpace(CartaoOrigem))
                return new ValidationResult().Add("Informações de origem inválidas", EFaultType.Error);

            return new ValidationResult();
        } 
    }
}
using ATS.Domain.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.ModelConfiguration;

namespace ATS.Data.Context.Mapping
{
    public class CampanhaRespostaMap : EntityTypeConfiguration<CampanhaResposta>
    {
        public CampanhaRespostaMap()
        {
            ToTable("CAMPANHA_RESPOSTA");

            HasKey(t => t.Id);

            Property(t => t.Id).HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity);
            Property(t => t.Observacao).IsOptional().HasMaxLength(500);
            Property(t => t.DataCadastro).HasColumnType("datetime2");
            Property(t => t.DataResposta).IsOptional().HasColumnType("datetime2");

            HasIndex(t => t.IdUsuario);
            HasIndex(t => t.IdEmpresa);

            HasRequired(c => c.<PERSON>)
                .WithMany(c => c.Respostas)
                .HasForeignKey(c => c.IdCampanha);

            HasRequired(c => c.<PERSON><PERSON>)
                .WithMany(c => c.<PERSON>an<PERSON>es<PERSON>as)
                .HasForeignKey(c => c.IdUsuario);

            HasOptional(c => c.Empresa)
                .WithMany()
                .HasForeignKey(c => c.IdEmpresa);
        }
    }
}